using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.Outlook.Communications;

public class OutlookEmailMetadata : EmailMetadata
{
    [JsonProperty("outlook_message_id")]
    public string? OutlookMessageId { get; set; }

    [JsonProperty("importance")]
    public string? Importance { get; set; }

    [JsonProperty("outlook_conversation_id")]
    public string? OutlookConversationId { get; set; }

    [JsonProperty("email_status")]
    public string? EmailStatus { get; set; }

    [JsonProperty("is_read")]
    public bool? IsRead { get; set; }

    [JsonProperty("internal_message_id")]
    public string? InternalMessageId { get; set; }

    [JsonProperty("has_attachments")]
    public bool? HasAttachments { get; set; }

    [JsonConstructor]
    public OutlookEmailMetadata(
        string? outlookMessageId = null,
        string? importance = null,
        string? outlookConversationId = null,
        DateTimeOffset? sentDate = null,
        string? emailStatus = null,
        bool? isRead = null,
        string? internalMessageId = null,
        bool? hasAttachments = null)
        : base(ProviderNames.Outlook, ProviderNames.Outlook, sentDate ?? DateTimeOffset.UtcNow)
    {
        OutlookMessageId = outlookMessageId;
        Importance = importance;
        OutlookConversationId = outlookConversationId;
        EmailStatus = emailStatus;
        IsRead = isRead;
        InternalMessageId = internalMessageId;
        HasAttachments = hasAttachments;
    }
}