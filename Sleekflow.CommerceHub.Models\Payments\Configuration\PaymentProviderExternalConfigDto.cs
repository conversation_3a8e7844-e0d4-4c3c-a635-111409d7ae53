using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Payments.Configuration;

public class PaymentProviderExternalConfigDto
{
    [Required]
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [Required]
    [JsonProperty("provider_id")]
    public string ProviderId { get; set; }

    [JsonConstructor]
    public PaymentProviderExternalConfigDto(
        string providerName,
        string providerId)
    {
        ProviderName = providerName;
        ProviderId = providerId;
    }

    public PaymentProviderExternalConfigDto(
        PaymentProviderExternalConfig paymentProviderExternalConfig)
        : this(
            paymentProviderExternalConfig.ProviderName,
            paymentProviderExternalConfig.ProviderId)
    {
    }
}