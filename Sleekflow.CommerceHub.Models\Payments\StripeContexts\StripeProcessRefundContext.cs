using Newtonsoft.Json;
using Stripe;

namespace Sleekflow.CommerceHub.Models.Payments.StripeContexts;

public class StripeProcessRefundContext : ProcessRefundContext
{
    [JsonProperty("refund")]
    public Refund Refund { get; set; }

    [JsonConstructor]
    public StripeProcessRefundContext(
        string providerRefundId,
        string status,
        decimal? amount,
        Refund refund)
        : base("Stripe", providerRefundId, status, amount)
    {
        Refund = refund;
    }
}