using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.Tests.Utils;

[TestFixture]
public class WhatsAppMarkdownConverterTests
{
    [SetUp]
    public void Setup()
    {
        // Setup code if needed
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithBoldTags_ReturnsBoldMarkdown()
    {
        var input = "Hello <b>World</b>!";
        var expected = "Hello *World* !";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithItalicTags_ReturnsItalicMarkdown()
    {
        var input = "Hello <i>World</i>!";
        var expected = "Hello _World_ !";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithStrikethroughTags_ReturnsStrikethroughMarkdown()
    {
        var input = "Hello <s>World</s>!";
        var expected = "Hello ~World~ !";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithQuoteTags_ReturnsQuoteMarkdown()
    {
        var input = "Hello <q>World</q>!";
        var expected = "Hello \n> World\n!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithMultilineQuoteTags_ReturnsQuoteWithNewlinesAsSpaces()
    {
        var input = "Hello <q>World\nHow are you?</q>!";
        var expected = "Hello \n> World How are you?\n!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithMultipleFormattingTags_ReturnsCorrectMarkdown()
    {
        var input = "Hello <b>Bold</b> and <i>Italic</i> and <s>Strike</s> and <q>Quote</q>!";
        var expected = "Hello *Bold* and _Italic_ and ~Strike~ and \n> Quote\n!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithEmptyInput_ReturnsEmptyString()
    {
        var input = string.Empty;
        var expected = string.Empty;

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithNoTags_ReturnsSameString()
    {
        var input = "Hello World!";
        var expected = "Hello World!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithNullInput_ReturnsNull()
    {
        string? input = null;

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.IsNull(result);
    }

    [Test]
    public void ApplyWhatsAppMarkdownToResponse_WithTagsInResponse_ConvertsResponseField()
    {
        var inputObject = new JObject
        {
            ["agent_name"] = "ResponseCrafterAgent",
            ["structured_response"] = "Detailed structured response with all information",
            ["natural_response_reasoning"] = "Making it more natural and conversational",
            ["response"] = "Hello <b>Bold</b> and <i>Italic</i>!"
        };

        var expected = "Hello *Bold* and _Italic_ !";

        var resultObject = WhatsAppMarkdownConverter.ApplyWhatsAppMarkdownToResponse(inputObject);

        Assert.AreEqual(expected, resultObject["response"].ToString());
        Assert.AreEqual("ResponseCrafterAgent", resultObject["agent_name"].ToString());
    }

    [Test]
    public void ApplyWhatsAppMarkdownToResponse_WithNoResponseField_ReturnsUnchangedObject()
    {
        var inputObject = new JObject
        {
            ["agent_name"] = "ResponseCrafterAgent",
            ["some_other_field"] = "Some value"
        };

        var resultObject = WhatsAppMarkdownConverter.ApplyWhatsAppMarkdownToResponse(inputObject);

        Assert.That(resultObject.ToString(), Is.EqualTo(inputObject.ToString()));
    }

    [Test]
    public void ApplyWhatsAppMarkdownToResponse_WithNullObject_ReturnsNull()
    {
        JObject inputObject = null;

        var resultObject = WhatsAppMarkdownConverter.ApplyWhatsAppMarkdownToResponse(inputObject);

        Assert.That(resultObject, Is.Null);
    }

    [TestCase("<b>Bold</b>", "*Bold*")]
    [TestCase("<i>Italic</i>", "_Italic_")]
    [TestCase("<s>Strike</s>", "~Strike~")]
    [TestCase("<q>Quote</q>", "> Quote")]
    public void ConvertToWhatsAppMarkdown_WithDifferentTags_ReturnsCorrectMarkdown(string input, string expected)
    {
        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [TestCase("<b></b>", "**")]
    [TestCase("<i></i>", "__")]
    [TestCase("<s></s>", "~~")]
    [TestCase("<q></q>", "> ")]
    public void ConvertToWhatsAppMarkdown_WithEmptyTags_ReturnsEmptyMarkdown(string input, string expected)
    {
        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithInvalidOpeningTags_LeavesTagsUnchanged()
    {
        var input = "Hello <invalid>World</invalid>!";
        var expected = "Hello <invalid>World</invalid>!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithMismatchedTags_OnlyProcessesMatchedTags()
    {
        var input = "Hello <b>Bold but no closing tag and <i>Italic</i> here";
        var expected = "Hello <b>Bold but no closing tag and _Italic_ here";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithSpecialCharactersInContent_HandlesThemCorrectly()
    {
        var input = "Hello <b>*Bold with asterisks*</b>!";
        var expected = "Hello **Bold with asterisks** !";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    [Category("Integration")]
    public void ConvertToWhatsAppMarkdown_WithComplexFormatting_ProcessesCorrectly()
    {
        // Test with a more complex real-world example
        var input = @"<b>Product Summary:</b>
<i>SleekFlow Business Plan</i>
- Includes <b>unlimited contacts</b>
- <s>$39/month</s> Now only $29/month
- <q>Perfect for small businesses looking to grow their customer base</q>

Let me know if you have any questions!";

        var expected = @"*Product Summary:*
_SleekFlow Business Plan_
- Includes *unlimited contacts*
- ~$39/month~ Now only $29/month
-
> Perfect for small businesses looking to grow their customer base

Let me know if you have any questions!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithMultilineContent_HandlesAllTagsCorrectly()
    {
        var input = "<b>Bold\nText</b> and <i>Italic\nText</i> and <s>Strike\nthrough</s>";
        var expected = "*Bold\nText* and _Italic\nText_ and ~Strike\nthrough~";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithNoSpaceBeforeTag_AddsPropperSpaceForFormatting()
    {
        var input = "Hello<b>Bold</b> and text<i>Italic</i> and more<s>Strike</s>";
        var expected = "Hello *Bold* and text _Italic_ and more ~Strike~";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithNoSpaceAfterTag_AddsProperSpaceAfterFormatting()
    {
        var input = "<b>Bold</b>and <i>Italic</i>text and <s>Strike</s>more";
        var expected = "*Bold* and _Italic_ text and ~Strike~ more";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithChineseText_HandlesFormattingCorrectly()
    {
        var input = "你好<b>粗体</b>文本 和 <i>斜体</i>文本";
        var expected = "你好 *粗体* 文本 和 _斜体_ 文本";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithChineseTextNoSpaceAfter_AddsSpaceAfterFormatting()
    {
        var input = "<b>粗体</b>文本 和 <i>斜体</i>更多文本";
        var expected = "*粗体* 文本 和 _斜体_ 更多文本";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithMixedLanguage_HandlesFormattingCorrectly()
    {
        var input = "Hello <b>World</b>你好<i>世界</i>!";
        var expected = "Hello *World* 你好 _世界_ !";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithChineseQuotes_HandlesQuotesCorrectly()
    {
        var input = "消息开始<q>这是引用的中文内容</q>消息结束";
        var expected = "消息开始\n> 这是引用的中文内容\n消息结束";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithComplexChineseFormatting_ProcessesCorrectly()
    {
        var input = "<b>产品摘要:</b>\n<i>SleekFlow商业计划</i>\n- 包括<b>无限联系人</b>\n- <s>每月¥399</s> 现在只需¥299每月\n<q>非常适合希望扩大客户群的小型企业</q>\n\n如有任何问题，请告诉我!";
        var expected = "*产品摘要:*\n_SleekFlow商业计划_\n- 包括 *无限联系人*\n- ~每月¥399~ 现在只需¥299每月\n> 非常适合希望扩大客户群的小型企业\n\n如有任何问题，请告诉我!";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithQuoteInMiddleOfText_AddsNewLineBefore()
    {
        var input = "Start of message<q>This is a quote</q>End of message";
        var expected = "Start of message\n> This is a quote\nEnd of message";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithQuoteAlreadyAfterNewLine_DoesNotAddExtraNewLine()
    {
        var input = "Start of message\n<q>This is a quote</q>";
        var expected = "Start of message\n> This is a quote";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithQuoteFollowedByNewLine_DoesNotAddExtraNewLine()
    {
        var input = "Start of message\n<q>This is a quote</q>\nNext line";
        var expected = "Start of message\n> This is a quote\nNext line";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithMultipleQuotes_HandlesEachQuoteCorrectly()
    {
        var input = "First <q>Quote one</q> then <q>Quote two</q> and done";
        var expected = "First \n> Quote one\n then \n> Quote two\n and done";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void ConvertToWhatsAppMarkdown_WithHyperlinkTags_ConvertsToWhatsAppFormat()
    {
        var input = "當然可以！您可以通過以下連結直接預約：<l>https://www.google.com</l>。我們的清水灣診所星期六的營業時間為上午9:00至中午12:30";
        var expected = "當然可以！您可以通過以下連結直接預約：[https://www.google.com] 。我們的清水灣診所星期六的營業時間為上午9:00至中午12:30";

        var result = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(input);

        Assert.That(result, Is.EqualTo(expected));
    }
}