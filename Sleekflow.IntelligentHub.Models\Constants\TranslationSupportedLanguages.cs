﻿using System.Collections.Immutable;

namespace Sleekflow.IntelligentHub.Models.Constants;

public static class TranslationSupportedLanguages
{
    public const string English = "en";
    public const string Cantonese = "yue";
    public const string TraditionalChineseTaiwan = "zh-TW";
    public const string SimplifiedChinese = "zh-CN";
    public const string Bahasa = "ms";
    public const string Portuguese = "pt";
    public const string Spanish = "es";
    public const string Indonesian = "id";
    public const string Arabic = "ar";

    public static readonly ImmutableList<string> AllSupportedLanguages =
        new List<string>()
            {
                English,
                Cantonese,
                TraditionalChineseTaiwan,
                SimplifiedChinese,
                Bahasa,
                Portuguese,
                Spanish,
                Indonesian,
                Arabic
            }
            .ToImmutableList();
}