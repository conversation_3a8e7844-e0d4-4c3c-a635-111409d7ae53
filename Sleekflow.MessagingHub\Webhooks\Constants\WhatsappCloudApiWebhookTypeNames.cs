namespace Sleekflow.MessagingHub.Webhooks.Constants;

public static class WhatsappCloudApiWebhookTypeNames
{
    public const string AccountAlerts = "account_alerts";
    public const string AccountReviewUpdate = "account_review_update";
    public const string AccountUpdate = "account_update";
    public const string BusinessCapabilityUpdate = "business_capability_update";
    public const string BusinessStatusUpdate = "business_status_update";
    public const string CampaignStatusUpdate = "campaign_status_update";
    public const string Flows = "flows";
    public const string MessageEchoes = "message_echoes";
    public const string TemplateCategoryUpdate = "template_category_update";
    public const string Messages = "messages";
    public const string MessageTemplateQualityUpdate = "message_template_quality_update";
    public const string MessageTemplateStatusUpdate = "message_template_status_update";
    public const string MessagingHandovers = "messaging_handovers";
    public const string PhoneNumberNameUpdate = "phone_number_name_update";
    public const string PhoneNumberQualityUpdate = "phone_number_quality_update";
    public const string Security = "security";
    public const string DetectedOutcomes = "detected_outcomes";
}