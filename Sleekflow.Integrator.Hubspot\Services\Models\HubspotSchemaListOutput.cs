﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Services.Models;

public class HubspotSchemaListOutput
{
    [JsonProperty("results")]
    public List<Schema> Results { get; set; }

    [JsonConstructor]
    public HubspotSchemaListOutput(List<Schema> results)
    {
        Results = results;
    }
}

public class Schema
{
    [JsonProperty("associations")]
    public List<Association>? Associations { get; set; }

    [JsonProperty("secondaryDisplayProperties")]
    public List<string>? SecondaryDisplayProperties { get; set; }

    [JsonProperty("createdByUserId")]
    public int? CreatedByUserId { get; set; }

    [JsonProperty("objectTypeId")]
    public string? ObjectTypeId { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("updatedByUserId")]
    public int? UpdatedByUserId { get; set; }

    [JsonProperty("fullyQualifiedName")]
    public string? FullyQualifiedName { get; set; }

    [JsonProperty("labels")]
    public Labels? Labels { get; set; }

    [JsonProperty("archived")]
    public bool? Archived { get; set; }

    [JsonProperty("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("requiredProperties")]
    public List<string>? RequiredProperties { get; set; }

    [JsonProperty("searchableProperties")]
    public List<string>? SearchableProperties { get; set; }

    [JsonProperty("primaryDisplayProperty")]
    public string? PrimaryDisplayProperty { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("properties")]
    public List<Property>? Properties { get; set; }

    [JsonProperty("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [JsonConstructor]
    public Schema(
        List<Association>? associations,
        List<string>? secondaryDisplayProperties,
        int? createdByUserId,
        string? objectTypeId,
        string? description,
        int? updatedByUserId,
        string? fullyQualifiedName,
        Labels? labels,
        bool? archived,
        DateTime? createdAt,
        List<string>? requiredProperties,
        List<string>? searchableProperties,
        string? primaryDisplayProperty,
        string? name,
        string? id,
        List<Property>? properties,
        DateTime? updatedAt)
    {
        Associations = associations;
        SecondaryDisplayProperties = secondaryDisplayProperties;
        CreatedByUserId = createdByUserId;
        ObjectTypeId = objectTypeId;
        Description = description;
        UpdatedByUserId = updatedByUserId;
        FullyQualifiedName = fullyQualifiedName;
        Labels = labels;
        Archived = archived;
        CreatedAt = createdAt;
        RequiredProperties = requiredProperties;
        SearchableProperties = searchableProperties;
        PrimaryDisplayProperty = primaryDisplayProperty;
        Name = name;
        Id = id;
        Properties = properties;
        UpdatedAt = updatedAt;
    }
}

public class Association
{
    [JsonProperty("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("fromObjectTypeId")]
    public string? FromObjectTypeId { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("toObjectTypeId")]
    public string? ToObjectTypeId { get; set; }

    [JsonProperty("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [JsonConstructor]
    public Association(
        DateTime? createdAt,
        string? fromObjectTypeId,
        string? name,
        string? id,
        string? toObjectTypeId,
        DateTime? updatedAt)
    {
        CreatedAt = createdAt;
        FromObjectTypeId = fromObjectTypeId;
        Name = name;
        Id = id;
        ToObjectTypeId = toObjectTypeId;
        UpdatedAt = updatedAt;
    }
}

public class Labels
{
    [JsonProperty("plural")]
    public string? Plural { get; set; }

    [JsonProperty("singular")]
    public string? Singular { get; set; }

    [JsonConstructor]
    public Labels(string? plural, string? singular)
    {
        Plural = plural;
        Singular = singular;
    }
}

public class Property
{
    [JsonProperty("hidden")]
    public bool? Hidden { get; set; }

    [JsonProperty("displayOrder")]
    public int? DisplayOrder { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("showCurrencySymbol")]
    public bool? ShowCurrencySymbol { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("hubspotDefined")]
    public bool? HubspotDefined { get; set; }

    [JsonProperty("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("archived")]
    public bool? Archived { get; set; }

    [JsonProperty("options")]
    public List<Option>? Options { get; set; }

    [JsonProperty("hasUniqueValue")]
    public bool? HasUniqueValue { get; set; }

    [JsonProperty("calculated")]
    public bool? Calculated { get; set; }

    [JsonProperty("externalOptions")]
    public bool? ExternalOptions { get; set; }

    [JsonProperty("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("createdUserId")]
    public string? CreatedUserId { get; set; }

    [JsonProperty("modificationMetadata")]
    public ModificationMetadata? ModificationMetadata { get; set; }

    [JsonProperty("sensitiveDataCategories")]
    public List<string>? SensitiveDataCategories { get; set; }

    [JsonProperty("label")]
    public string? Label { get; set; }

    [JsonProperty("formField")]
    public bool? FormField { get; set; }

    [JsonProperty("dataSensitivity")]
    public string? DataSensitivity { get; set; }

    [JsonProperty("archivedAt")]
    public DateTime? ArchivedAt { get; set; }

    [JsonProperty("groupName")]
    public string? GroupName { get; set; }

    [JsonProperty("referencedObjectType")]
    public string? ReferencedObjectType { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("calculationFormula")]
    public string? CalculationFormula { get; set; }

    [JsonProperty("fieldType")]
    public string? FieldType { get; set; }

    [JsonProperty("updatedUserId")]
    public string? UpdatedUserId { get; set; }

    [JsonConstructor]
    public Property(
        bool? hidden,
        int? displayOrder,
        string? description,
        bool? showCurrencySymbol,
        string? type,
        bool? hubspotDefined,
        DateTime? createdAt,
        bool? archived,
        List<Option>? options,
        bool? hasUniqueValue,
        bool? calculated,
        bool? externalOptions,
        DateTime? updatedAt,
        string? createdUserId,
        ModificationMetadata? modificationMetadata,
        List<string>? sensitiveDataCategories,
        string? label,
        bool? formField,
        string? dataSensitivity,
        DateTime? archivedAt,
        string? groupName,
        string? referencedObjectType,
        string? name,
        string? calculationFormula,
        string? fieldType,
        string? updatedUserId)
    {
        Hidden = hidden;
        DisplayOrder = displayOrder;
        Description = description;
        ShowCurrencySymbol = showCurrencySymbol;
        Type = type;
        HubspotDefined = hubspotDefined;
        CreatedAt = createdAt;
        Archived = archived;
        Options = options;
        HasUniqueValue = hasUniqueValue;
        Calculated = calculated;
        ExternalOptions = externalOptions;
        UpdatedAt = updatedAt;
        CreatedUserId = createdUserId;
        ModificationMetadata = modificationMetadata;
        SensitiveDataCategories = sensitiveDataCategories;
        Label = label;
        FormField = formField;
        DataSensitivity = dataSensitivity;
        ArchivedAt = archivedAt;
        GroupName = groupName;
        ReferencedObjectType = referencedObjectType;
        Name = name;
        CalculationFormula = calculationFormula;
        FieldType = fieldType;
        UpdatedUserId = updatedUserId;
    }
}