﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.OpenTelemetry.FlowHub;
using Sleekflow.OpenTelemetry.FlowHub.MeterNames;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowExecutionStartedEventConsumerDefinition : ConsumerDefinition<OnWorkflowExecutionStartedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowExecutionStartedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowExecutionStartedEventConsumer : IConsumer<OnWorkflowExecutionStartedEvent>
{
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IFlowHubMeters _flowHubMeters;

    public OnWorkflowExecutionStartedEventConsumer(
        IWorkflowExecutionService workflowExecutionService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IFlowHubMeters flowHubMeters)
    {
        _workflowExecutionService = workflowExecutionService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _flowHubMeters = flowHubMeters;
    }

    public async Task Consume(ConsumeContext<OnWorkflowExecutionStartedEvent> context)
    {
        var @event = context.Message;

        var stateId = @event.StateId;
        var stateIdentity = @event.StateIdentity;
        var workflowExecutionReasonCode = @event.WorkflowExecutionReasonCode;
        var workflowType = @event.WorkflowType;
        var startedBy = @event.StartedBy;
        var startedAt = @event.StartedAt;
        var subWorkflowType = @event.SubWorkflowType;

        _flowHubMeters.IncrementCounter(subWorkflowType, FlowHubMeterOptions.WorkflowStart);

        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.FlowHubWorkflowExecutionStarted,
            new Dictionary<string, string>()
            {
                {
                    "sub_workflow_type", subWorkflowType
                },
            });

        await _workflowExecutionService.CreateWorkflowExecutionAsync(
            stateId,
            stateIdentity,
            WorkflowExecutionStatuses.Started,
            workflowExecutionReasonCode,
            0,
            workflowType,
            startedBy,
            startedAt);
    }
}