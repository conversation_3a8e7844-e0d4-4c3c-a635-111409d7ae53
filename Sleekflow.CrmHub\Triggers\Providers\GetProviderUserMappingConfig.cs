﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderUserMappingConfig : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderUserMappingConfig(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [JsonConstructor]
        public GetProviderUserMappingConfigInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
        }
    }

    public class GetProviderUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public GetProviderUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<GetProviderUserMappingConfigOutput> F(
        GetProviderUserMappingConfigInput getProviderUserMappingConfigInput)
    {
        var providerService = _providerSelector.GetProviderService(
            getProviderUserMappingConfigInput.ProviderName);

        var output = await providerService.GetProviderUserMappingConfigAsync(
            getProviderUserMappingConfigInput.SleekflowCompanyId,
            getProviderUserMappingConfigInput.ProviderConnectionId);

        return new GetProviderUserMappingConfigOutput(output.UserMappingConfig);
    }
}