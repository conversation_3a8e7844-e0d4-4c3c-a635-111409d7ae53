using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionDto : EntityDto, IHasCreatedAt, IHasSleekflowCompanyId, IHasUpdatedAt
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    public StateIdentityDto StateIdentity { get; set; }

    [JsonProperty("workflow_execution_status")]
    public string WorkflowExecutionStatus { get; set; }

    [JsonProperty("workflow_execution_reason_code")]
    public string? WorkflowExecutionReasonCode { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("remarks")]
    public List<WorkflowExecutionDtoRemark> Remarks { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(AuditEntity.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonConstructor]
    public WorkflowExecutionDto(
        string id,
        string sleekflowCompanyId,
        string stateId,
        StateIdentityDto stateIdentity,
        string workflowExecutionStatus,
        string? workflowExecutionReasonCode,
        DateTimeOffset createdAt,
        List<WorkflowExecutionDtoRemark> remarks,
        DateTimeOffset updatedAt,
        AuditEntity.SleekflowStaff? updatedBy)
        : base(id)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        StateIdentity = stateIdentity;
        WorkflowExecutionStatus = workflowExecutionStatus;
        WorkflowExecutionReasonCode = workflowExecutionReasonCode;
        CreatedAt = createdAt;
        Remarks = remarks;
        UpdatedAt = updatedAt;
        UpdatedBy = updatedBy;
    }

    public WorkflowExecutionDto(
        WorkflowExecution earliestWorkflowExecution,
        WorkflowExecution latestWorkflowExecution,
        List<WorkflowExecutionDtoRemark> remarks)
        : this(
            earliestWorkflowExecution.Id,
            earliestWorkflowExecution.SleekflowCompanyId,
            earliestWorkflowExecution.StateId,
            new StateIdentityDto(earliestWorkflowExecution.StateIdentity),
            latestWorkflowExecution.WorkflowExecutionStatus,
            earliestWorkflowExecution.WorkflowExecutionReasonCode,
            earliestWorkflowExecution.CreatedAt,
            remarks,
            latestWorkflowExecution.CreatedAt,
            latestWorkflowExecution.CreatedBy)
    {
    }
}