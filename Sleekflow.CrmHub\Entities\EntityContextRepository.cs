﻿using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Entities;

public interface IEntityContextRepository : IRepository<CrmHubEntityContext>
{
}

public class EntityContextRepository : BaseRepository<CrmHubEntityContext>, IEntityContextRepository, ISingletonService
{
    public EntityContextRepository(
        ILogger<BaseRepository<CrmHubEntityContext>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}