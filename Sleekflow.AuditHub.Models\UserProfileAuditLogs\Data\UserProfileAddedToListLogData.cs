using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileAddedToListLogData
{
    [JsonProperty("user_profile_added_to_list")]
    public UserProfileListLogData UserProfileAddedToList { get; set; }

    [JsonConstructor]
    public UserProfileAddedToListLogData(UserProfileListLogData userProfileAddedToList)
    {
        UserProfileAddedToList = userProfileAddedToList;
    }
}

public class UserProfileListLogData
{
    [JsonConstructor]
    public UserProfileListLogData(long listId, string name)
    {
        ListId = listId;
        Name = name;
    }

    [JsonProperty("list_id")]
    public long ListId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }
}