using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.Models.Blobs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.EmailHub.Triggers.Blobs;

[TriggerGroup(ControllerNames.Blobs)]
public class CreateBlobUploadSasUrls
    : ITrigger<
        CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsInput,
        CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsOutput>
{
    private readonly IRequestClient<CreateBlobUploadSasUrlsRequest> _createBlobUploadSasUrlsRequestClient;

    public CreateBlobUploadSasUrls(
        IRequestClient<CreateBlobUploadSasUrlsRequest> createBlobUploadSasUrlsRequestClient)
    {
        _createBlobUploadSasUrlsRequestClient = createBlobUploadSasUrlsRequestClient;
    }

    public class CreateBlobUploadSasUrlsInput
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [Range(1, 16)]
        [JsonProperty("number_of_blobs")]
        [Required]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        [Required]
        public string BlobType { get; set; }

        [JsonConstructor]
        public CreateBlobUploadSasUrlsInput(
            string sleekflowCompanyId,
            int numberOfBlobs,
            string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            NumberOfBlobs = numberOfBlobs;
            BlobType = blobType;
        }
    }

    public class CreateBlobUploadSasUrlsOutput
    {
        [JsonProperty("upload_blobs")]
        public List<PublicBlob> UploadBlobs { get; set; }

        [JsonConstructor]
        public CreateBlobUploadSasUrlsOutput(
            List<PublicBlob> uploadBlobs)
        {
            UploadBlobs = uploadBlobs;
        }
    }

    public async Task<CreateBlobUploadSasUrlsOutput> F(CreateBlobUploadSasUrlsInput createBlobUploadSasUrlsInput)
    {
        var createBlobUploadSasUrlsReplyResponse =
            await _createBlobUploadSasUrlsRequestClient.GetResponse<CreateBlobUploadSasUrlsReply>(
                new CreateBlobUploadSasUrlsRequest(
                    createBlobUploadSasUrlsInput.SleekflowCompanyId,
                    createBlobUploadSasUrlsInput.NumberOfBlobs,
                    createBlobUploadSasUrlsInput.BlobType));
        var createBlobUploadSasUrlsReply = createBlobUploadSasUrlsReplyResponse.Message;

        return new CreateBlobUploadSasUrlsOutput(
            createBlobUploadSasUrlsReply.UploadBlobs);
    }
}