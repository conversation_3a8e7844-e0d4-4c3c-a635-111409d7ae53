﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Internals;

[TriggerGroup(TriggerGroups.Internals)]
public class LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch
    : ITrigger<
        LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch.LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput,
        LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch.LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput>
{
    private const short BatchSize = 100;

    private Schema Schema { get; set; } = null!;

    private string WorkFlowId { get; set; } = null!;

    private string WorkFlowVersionedId { get; set; } = null!;

    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;
    private readonly ILogger<LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch> _logger;
    private readonly IBus _bus;

    public LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch(
        ISchemaService schemaService,
        ISchemafulObjectService schemafulObjectService,
        ILogger<LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch> logger,
        IBus bus)
    {
        _schemaService = schemaService;
        _schemafulObjectService = schemafulObjectService;
        _logger = logger;
        _bus = bus;
    }

    public class LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        public string SchemaId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput(
            string sleekflowCompanyId,
            string schemaId,
            string? continuationToken,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SchemaId = schemaId;
            ContinuationToken = continuationToken;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput
    {
        [Required]
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput(long count, string? nextContinuationToken)
        {
            Count = count;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput> F(
        LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput input)
    {
        Schema = await _schemaService.GetAsync(input.SchemaId, input.SleekflowCompanyId);
        WorkFlowId = input.FlowHubWorkflowId;
        WorkFlowVersionedId = input.FlowHubWorkflowVersionedId;

        var sorts = new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>
        {
            new (
                AuditEntity.PropertyNameCreatedBy,
                "DESC",
                false)
        };

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>(),
            sorts,
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            input.SleekflowCompanyId,
            input.SchemaId,
            true);

        var (schemafulObjects, nextContinuationToken) =
            await _schemafulObjectService.GetContinuationTokenizedSchemafulObjectsAsync(
                queryDefinition,
                input.ContinuationToken,
                BatchSize);

        await PublishFlowHubEnrollmentRequestEvents(schemafulObjects);

        return new LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput(schemafulObjects.Count, nextContinuationToken);
    }

    private async Task PublishFlowHubEnrollmentRequestEvents(List<SchemafulObject> schemafulObjects)
    {
        var events = schemafulObjects
            .Select(
                ConstructOnSchemafulObjectEnrollmentToFlowHubRequestedEvent)
            .ToList();

        var eventChunks = events.Chunk(100).ToList();
        var totalChunks = eventChunks.Count;

        for (var i = 0; i < totalChunks; i++)
        {
            await _bus.PublishBatch(
                eventChunks[i],
                context => { context.ConversationId = Guid.Parse(Schema.SleekflowCompanyId); });

            if (i < totalChunks - 1)
            {
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
        }
    }

    private OnSchemafulObjectEnrollmentToFlowHubRequestedEvent
        ConstructOnSchemafulObjectEnrollmentToFlowHubRequestedEvent(SchemafulObject schemafulObject)
    {
        var fullPropertyValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
            Schema.Properties,
            schemafulObject.PropertyValues,
            _logger);
        fullPropertyValues.Add(Schema.PrimaryProperty.Id, schemafulObject.PrimaryPropertyValue);
        fullPropertyValues.Add(Schema.PrimaryProperty.UniqueName, schemafulObject.PrimaryPropertyValue);

        return new OnSchemafulObjectEnrollmentToFlowHubRequestedEvent(
            Schema.SleekflowCompanyId,
            schemafulObject.Id,
            Schema.Id,
            schemafulObject.PrimaryPropertyValue,
            schemafulObject.SleekflowUserProfileId,
            fullPropertyValues,
            schemafulObject.CreatedAt,
            WorkFlowId,
            WorkFlowVersionedId);
    }
}