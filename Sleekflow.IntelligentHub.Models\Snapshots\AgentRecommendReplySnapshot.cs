using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class AgentRecommendReplySnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public string ConversationContext { get; set; }

    [JsonProperty("knowledge_base_entries")]
    public string KnowledgeBaseEntries { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonProperty("token_counts")]
    public Dictionary<string, int> TokenCounts { get; set; }

    [JsonProperty("collaboration_mode")]
    public string CollaborationMode { get; set; }

    [JsonConstructor]
    public AgentRecommendReplySnapshot(
        string conversationContext,
        string knowledgeBaseEntries,
        string outputMessage,
        Dictionary<string, int> tokenCounts,
        string collaborationMode)
    {
        ConversationContext = conversationContext;
        KnowledgeBaseEntries = knowledgeBaseEntries;
        OutputMessage = outputMessage;
        TokenCounts = tokenCounts;
        CollaborationMode = collaborationMode;
    }
}