using Alba;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Searches;
using Sleekflow.CommerceHub.Triggers.Categories;
using Sleekflow.CommerceHub.Triggers.Products;
using Sleekflow.CommerceHub.Triggers.Stores;
using Sleekflow.Outputs;

namespace Sleekflow.CommerceHub.Tests;

public class SearchTests
{
    private CreateStore.CreateStoreOutput? _createStoreOutput;
    private CreateCategory.CreateCategoryOutput? _createCategoryOutput;
    private CreateProduct.CreateProductOutput? _createProductOutput;

    [SetUp]
    public async Task Setup()
    {
        var createStoreOutputOutput = await Mocks.CreateTestStoreAsync();
        var createCategoryOutputOutput = await Mocks.CreateTestCategoryAsync(createStoreOutputOutput!.Data);
        var createProductOutputOutput = await Mocks.CreateTestProductAsync(
            createStoreOutputOutput.Data,
            createCategoryOutputOutput!.Data);

        _createStoreOutput = createStoreOutputOutput.Data;
        _createCategoryOutput = createCategoryOutputOutput.Data;
        _createProductOutput = createProductOutputOutput.Data;
    }

    [TearDown]
    public async Task TearDown()
    {
        await Mocks.DeleteTestProductAsync(_createStoreOutput!, _createProductOutput!);
        await Mocks.DeleteTestCategoryAsync(_createStoreOutput!, _createCategoryOutput!);
        await Mocks.DeleteTestStoreAsync(_createStoreOutput!);
    }

    [Test]
    public async Task HealthzTest()
    {
        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/Public/healthz");
                _.ContentShouldBe("HEALTH");
                _.StatusCodeShouldBeOk();
            });
    }

    [Test]
    public async Task SearchProductsTest()
    {
        // /Products/CreateDefaultProduct
        var createDefaultProductInputs = new List<CreateDefaultProduct.CreateDefaultProductInput>()
        {
            new CreateDefaultProduct.CreateDefaultProductInput(
                new List<string>
                {
                    _createCategoryOutput!.Category.Id,
                },
                null,
                null,
                new List<Multilingual>
                {
                    new ("en", "LV Bag"), new ("zh-Hant", "LV 袋"),
                },
                new List<Description>
                {
                    new (
                        DescriptionTypes.Text,
                        new Multilingual(
                            "en",
                            "To fit today's lifestyles, Louis Vuitton designs an extensive selection of bags for men, from the Christopher line to the new Soft Trunk models."),
                        null,
                        null),
                    new (
                        DescriptionTypes.Text,
                        new Multilingual(
                            "zh-Hant",
                            "為適應當今的生活方式，路易威登設計了種類繁多的男士手袋，從 Christopher 系列到新款 Soft Trunk 款式。"),
                        null,
                        null)
                },
                new List<ImageDto>(),
                new Dictionary<string, object?>(),
                Mocks.SleekflowCompanyId,
                _createStoreOutput!.Store.Id,
                new List<Price>()
                {
                    new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
                },
                new List<ProductVariant.ProductVariantAttribute>
                {
                    new ProductVariant.ProductVariantAttribute("color", "red"),
                    new ProductVariant.ProductVariantAttribute("size", "M"),
                },
                Mocks.SleekflowStaffId,
                null),
            new CreateDefaultProduct.CreateDefaultProductInput(
                new List<string>
                {
                    _createCategoryOutput!.Category.Id,
                },
                null,
                null,
                new List<Multilingual>
                {
                    new ("en", "Samsung Galaxy Z Fold4"), new ("zh-Hant", "三星 Galaxy Z Fold4"),
                },
                new List<Description>
                {
                    new (
                        DescriptionTypes.Text,
                        new Multilingual(
                            "en",
                            "Meet the new Galaxy Z Fold4, the tough foldable that brings PC-like power to your pocket. Unfold your world with an immersive display and Nightography."),
                        null,
                        null),
                    new (
                        DescriptionTypes.Text,
                        new Multilingual(
                            "zh-Hant",
                            "展開的Galaxy Z Fold4上，主屏幕正顯示賽車遊戲畫面。一. 現在你可真正投入其中。盡窄邊框，加上震撼7.6\"無邊際開展式屏幕下、藏得更深的屏下鏡頭，帶來更大屏幕空間，再無 ..."),
                        null,
                        null)
                },
                new List<ImageDto>(),
                new Dictionary<string, object?>(),
                Mocks.SleekflowCompanyId,
                _createStoreOutput!.Store.Id,
                new List<Price>()
                {
                    new Price("HKD", 14999), new Price("GBP", 1999), new Price("USD", 1999),
                },
                new List<ProductVariant.ProductVariantAttribute>
                {
                    new ProductVariant.ProductVariantAttribute("color", "grey"),
                    new ProductVariant.ProductVariantAttribute("brand", "samsung"),
                },
                Mocks.SleekflowStaffId,
                null)
        };

        var createdProductIds = new List<string>();
        foreach (var createDefaultProductInput in createDefaultProductInputs)
        {
            var createDefaultProductScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.Post.Json(createDefaultProductInput).ToUrl("/Products/CreateDefaultProduct");
                });

            var createDefaultProductOutputOutput =
                await createDefaultProductScenarioResult
                    .ReadAsJsonAsync<Output<CreateDefaultProduct.CreateDefaultProductOutput>>();

            Assert.That(createDefaultProductOutputOutput, Is.Not.Null);
            Assert.That(createDefaultProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            createdProductIds.Add(createDefaultProductOutputOutput.Data.Product.Id);
        }

        await Task.Delay(3000);

        // /Products/SearchProducts
        await TestSearchProductsAll();
        await TestSearchProductsSamsung();

        // /Products/DeleteProduct
        foreach (var createdProductId in createdProductIds)
        {
            var deleteProductInput = new DeleteProduct.DeleteProductInput(
                createdProductId,
                Mocks.SleekflowCompanyId,
                _createStoreOutput.Store.Id,
                Mocks.SleekflowStaffId,
                null);
            var deleteProductScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.Post.Json(deleteProductInput).ToUrl("/Products/DeleteProduct");
                });
            var deleteProductOutputOutput =
                await deleteProductScenarioResult.ReadAsJsonAsync<Output<DeleteProduct.DeleteProductOutput>>();

            Assert.That(deleteProductOutputOutput, Is.Not.Null);
            Assert.That(deleteProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        }
    }

    private async Task TestSearchProductsAll()
    {
        // /Products/SearchProducts
        var searchProductsInput = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>(),
            null,
            100,
            "*");
        var searchProductsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput =
            await searchProductsScenarioResult
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput = searchProductsOutputOutput!.Data;

        Assert.That(searchProductsOutput, Is.Not.Null);
        Assert.That(searchProductsOutput.Products.Count, Is.EqualTo(3));
    }

    private async Task TestSearchProductsSamsung()
    {
        // /Products/SearchProducts - name
        var searchProductsInput1 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("name", "contains", "Samsung")
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult1 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput1).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput1 =
            await searchProductsScenarioResult1
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput1 = searchProductsOutputOutput1!.Data;

        Assert.That(searchProductsOutput1, Is.Not.Null);
        Assert.That(searchProductsOutput1.Products.Count, Is.EqualTo(1));

        // /Products/SearchProducts - name
        var searchProductsInput2 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("name", "contains", "Fold4")
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput2).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput2 =
            await searchProductsScenarioResult2
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput2 = searchProductsOutputOutput2!.Data;

        Assert.That(searchProductsOutput2, Is.Not.Null);
        Assert.That(searchProductsOutput2.Products.Count, Is.EqualTo(1));

        // /Products/SearchProducts - description
        var searchProductsInput3 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("description", "contains", "Nightography")
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult3 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput3).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput3 =
            await searchProductsScenarioResult3
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput3 = searchProductsOutputOutput3!.Data;

        Assert.That(searchProductsOutput3, Is.Not.Null);
        Assert.That(searchProductsOutput3.Products.Count, Is.EqualTo(1));

        // /Products/SearchProducts - price
        var searchProductsInput4 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("product_variant_price", ">=", new Price("GBP", 1000))
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult4 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput4).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput4 =
            await searchProductsScenarioResult4
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput4 = searchProductsOutputOutput4!.Data;

        Assert.That(searchProductsOutput4, Is.Not.Null);
        Assert.That(searchProductsOutput4.Products.Count, Is.EqualTo(1));

        // /Products/SearchProducts - search_text
        var searchProductsInput5 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>(),
            null,
            100,
            "Samsung");
        var searchProductsScenarioResult5 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput5).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput5 =
            await searchProductsScenarioResult5
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput5 = searchProductsOutputOutput5!.Data;

        Assert.That(searchProductsOutput5, Is.Not.Null);
        Assert.That(searchProductsOutput5.Products.Count, Is.EqualTo(1));
        Assert.That(
            searchProductsOutput5.Products[0].Names.First(n => n.LanguageIsoCode == "en").Value,
            Is.EqualTo("Samsung Galaxy Z Fold4"));

        // /Products/SearchProducts - name
        var searchProductsInput6 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("name", "contains", "三星")
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult6 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput6).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput6 =
            await searchProductsScenarioResult6
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput6 = searchProductsOutputOutput6!.Data;

        Assert.That(searchProductsOutput6, Is.Not.Null);
        Assert.That(searchProductsOutput6.Products.Count, Is.EqualTo(1));

        // /Products/SearchProducts - is_view_enabled == true
        var searchProductsInput7 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("is_view_enabled", "=", true)
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult7 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput7).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput7 =
            await searchProductsScenarioResult7
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput7 = searchProductsOutputOutput7!.Data;

        Assert.That(searchProductsOutput7, Is.Not.Null);
        Assert.That(searchProductsOutput7.Products.Count, Is.EqualTo(0));

        // /Products/SearchProducts - is_view_enabled == true
        var searchProductsInput8 = new SearchProducts.SearchProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<SearchFilterGroup>()
            {
                new SearchFilterGroup(
                    new List<SearchFilter>()
                    {
                        new SearchFilter("is_view_enabled", "=", false)
                    })
            },
            null,
            100,
            "*");
        var searchProductsScenarioResult8 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(searchProductsInput8).ToUrl("/Products/SearchProducts");
            });
        var searchProductsOutputOutput8 =
            await searchProductsScenarioResult8
                .ReadAsJsonAsync<Output<SearchProducts.SearchProductsOutput>>();

        var searchProductsOutput8 = searchProductsOutputOutput8!.Data;

        Assert.That(searchProductsOutput8, Is.Not.Null);
        Assert.That(searchProductsOutput8.Products.Count, Is.EqualTo(3));
    }
}