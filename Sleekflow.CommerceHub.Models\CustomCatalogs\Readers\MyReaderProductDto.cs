using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;

#pragma warning disable SF1001
public class MyReaderProductDto
#pragma warning restore SF1001
{
    [JsonProperty("category_names")]
    public Dictionary<string, string> CategoryNames { get; set; }

    [JsonProperty("sku")]
    public string? Sku { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("names")]
    public Dictionary<string, string> Names { get; set; }

    [JsonProperty("descriptions")]
    public List<MyReaderProductDescriptionDto> Descriptions { get; set; }

    [JsonProperty("prices")]
    public Dictionary<string, string> Prices { get; set; }

    [JsonProperty("image_urls")]
    public List<string> ImageUrls { get; set; }

    [JsonProperty("image_blob_ids")]
    public List<string> ImageBlobIds { get; set; }

    [JsonProperty("attributes")]
    public Dictionary<string, Dictionary<string, string>> Attributes { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, string> Metadata { get; set; }

    [JsonConstructor]
    public MyReaderProductDto(
        Dictionary<string, string> categoryNames,
        string? sku,
        string? url,
        Dictionary<string, string> names,
        List<MyReaderProductDescriptionDto> descriptions,
        Dictionary<string, string> prices,
        List<string> imageUrls,
        List<string> imageBlobIdIds,
        Dictionary<string, Dictionary<string, string>> attributes,
        Dictionary<string, string> metadata)
    {
        CategoryNames = categoryNames;
        Sku = sku;
        Url = url;
        Names = names;
        Descriptions = descriptions;
        Prices = prices;
        ImageUrls = imageUrls;
        ImageBlobIds = imageBlobIdIds;
        Attributes = attributes;
        Metadata = metadata;
    }
}

public class MyReaderProductDescriptionDto
{
    [JsonProperty("idx")]
    public int Idx { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("language_code")]
    public string LanguageCode { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }

    [JsonConstructor]
    public MyReaderProductDescriptionDto(
        int idx,
        string type,
        string languageCode,
        string value)
    {
        Idx = idx;
        Type = type;
        LanguageCode = languageCode;
        Value = value;
    }
}