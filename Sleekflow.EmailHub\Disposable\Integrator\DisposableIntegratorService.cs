using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Disposable.Authentications;
using Sleekflow.EmailHub.Disposable.Communications;
using Sleekflow.EmailHub.Disposable.Subscriptions;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;

namespace Sleekflow.EmailHub.Disposable.Integrator;

public interface IDisposableIntegratorService : IEmailService
{
}

public class DisposableIntegratorService : EmailService, IDisposableIntegratorService, IScopedService
{
    public const string EmailProviderName = ProviderNames.Disposable;

    public DisposableIntegratorService(
        IDisposableAuthenticationService disposableAuthenticationService,
        IDisposableSubscriptionService disposableSubscriptionService,
        IDisposableCommunicationService disposableCommunicationService,
        IEmailRepository emailRepository,
        IBus bus,
        IProviderConfigService providerConfigService)
        : base(
            EmailProviderName,
            disposableAuthenticationService,
            disposableSubscriptionService,
            disposableCommunicationService,
            emailRepository,
            bus,
            providerConfigService)
    {
    }
}