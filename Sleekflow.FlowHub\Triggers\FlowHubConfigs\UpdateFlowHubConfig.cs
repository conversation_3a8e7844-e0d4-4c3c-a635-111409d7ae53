using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class UpdateFlowHubConfig : ITrigger
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public UpdateFlowHubConfig(
        IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    public class UpdateFlowHubConfigInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("usage_limit")]
        [Validations.ValidateObject]
        [Required]
        public UsageLimit UsageLimit { get; set; }

        [JsonProperty("usage_limit_offset")]
        [Validations.ValidateObject]
        public UsageLimitOffset? UsageLimitOffset { get; set; }

        [JsonProperty("origin")]
        public string? Origin { get; set; }

        [JsonConstructor]
        public UpdateFlowHubConfigInput(
            string sleekflowCompanyId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            UsageLimit usageLimit,
            UsageLimitOffset? usageLimitOffset,
            string? origin)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            UsageLimit = usageLimit;
            UsageLimitOffset = usageLimitOffset;
            Origin = origin;
        }
    }

    public class UpdateFlowHubConfigOutput
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; }

        [JsonConstructor]
        public UpdateFlowHubConfigOutput(
            FlowHubConfig flowHubConfig)
        {
            FlowHubConfig = flowHubConfig;
        }
    }

    public async Task<UpdateFlowHubConfigOutput> F(UpdateFlowHubConfigInput updateFlowHubConfigInput)
    {
        var flowHubConfig = await _flowHubConfigService.UpdateFlowHubConfigAsync(
            updateFlowHubConfigInput.SleekflowCompanyId,
            updateFlowHubConfigInput.UsageLimit,
            updateFlowHubConfigInput.UsageLimitOffset,
            updateFlowHubConfigInput.Origin,
            updateFlowHubConfigInput.SleekflowStaffId is not null
                ? new AuditEntity.SleekflowStaff(
                    updateFlowHubConfigInput.SleekflowStaffId,
                    updateFlowHubConfigInput.SleekflowStaffTeamIds)
                : null);

        return new UpdateFlowHubConfigOutput(flowHubConfig);
    }
}