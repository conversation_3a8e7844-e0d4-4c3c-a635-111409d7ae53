namespace Sleekflow.FlowHub.Silos
{
    public class SiloShutdownObserver : ILifecycleParticipant<ISiloLifecycle>
    {
        private readonly ILogger<SiloShutdownObserver> _logger;
        private readonly IHostEnvironment _hostEnvironment;

        public SiloShutdownObserver(
            ILogger<SiloShutdownObserver> logger,
            IHostEnvironment hostEnvironment)
        {
            _logger = logger;
            _hostEnvironment = hostEnvironment;
        }

        public void Participate(ISiloLifecycle lifecycle)
        {
            // Stages are listed here in an order that their OnStop handlers will be invoked
            // during shutdown (descending numerical stage value).
            Func<CancellationToken, Task> nopOnStart = (ct) => Task.CompletedTask;

            // Stage 1: Graceful shutdown - Orleans and ASP.NET Core handle request completion automatically
            lifecycle.Subscribe<SiloShutdownObserver>(
                stage: ServiceLifecycleStage.Active, // <PERSON>lo is no longer active in the cluster
                onStart: nopOnStart,
                onStop: ct => OnStageStopGracefulShutdownAsync(ServiceLifecycleStage.Active, "Active", ct));

            // Stage 2: Application services stopping
            lifecycle.Subscribe<SiloShutdownObserver>(
                stage: ServiceLifecycleStage.ApplicationServices, // Application layer services stopping
                onStart: nopOnStart,
                onStop: ct => OnStageStop(ServiceLifecycleStage.ApplicationServices, "ApplicationServices", ct));

            // Stage 3: Grain services stopping
            lifecycle.Subscribe<SiloShutdownObserver>(
                stage: ServiceLifecycleStage.RuntimeGrainServices, // Grain services (directory, etc.) stopping
                onStart: nopOnStart,
                onStop: ct => OnStageStop(ServiceLifecycleStage.RuntimeGrainServices, "RuntimeGrainServices", ct));

            // Stage 4: Core runtime services stopping
            lifecycle.Subscribe<SiloShutdownObserver>(
                stage: ServiceLifecycleStage.RuntimeServices, // Core runtime services (networking, agents) stopping
                onStart: nopOnStart,
                onStop: ct => OnStageStop(ServiceLifecycleStage.RuntimeServices, "RuntimeServices", ct));
        }

        /// <summary>
        /// Stage 1: Graceful shutdown initiation - Orleans and ASP.NET Core handle request completion automatically.
        /// </summary>
        private async Task OnStageStopGracefulShutdownAsync(int stageCode, string stageName, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation(
                "SiloShutdownObserver: Graceful shutdown initiated for stage {StageName} ({StageCode}).",
                stageName,
                stageCode);

            try
            {
                // Calculate jittered delay based on silo identity for more distribution
                var siloIdentityHash = Environment.MachineName.GetHashCode();
                var jitterSeed = Math.Abs(siloIdentityHash) % 60;
                var totalDelaySeconds = _hostEnvironment.IsDevelopment() ? 0 : jitterSeed;

                var jitteredDelay = TimeSpan.FromSeconds(totalDelaySeconds);

                await Task.Delay(jitteredDelay, cancellationToken);

                _logger.LogInformation("SiloShutdownObserver: Graceful shutdown stage manual delay completed. ");
            }
            catch (OperationCanceledException ex)
            {
                _logger.LogWarning(
                    ex,
                    "SiloShutdownObserver: Graceful shutdown stage {StageName} was cancelled",
                    stageName);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "SiloShutdownObserver: Error during graceful shutdown initiation for stage {StageName}",
                    stageName);
                throw;
            }

            _logger.LogInformation(
                "SiloShutdownObserver: OnStop for stage {StageName} ({StageCode}) completed.",
                stageName,
                stageCode);
        }

        /// <summary>
        /// Standard shutdown stage handler for remaining stages.
        /// </summary>
        private Task OnStageStop(int stageCode, string stageName, CancellationToken cancellationToken = default)
        {
            // The CancellationToken 'ct' here is tied to the HostOptions.ShutdownTimeout.
            // If operations here take too long, the silo shutdown might be aborted.
            _logger.LogInformation(
                "SiloShutdownObserver: OnStop for stage {StageName} ({StageCode}) called.",
                stageName,
                stageCode);

            try
            {
                // Orleans handles the remaining shutdown stages automatically
                _logger.LogInformation(
                    "SiloShutdownObserver: OnStop for stage {StageName} ({StageCode}) completed.",
                    stageName,
                    stageCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "SiloShutdownObserver: Error during shutdown stage {StageName} ({StageCode})",
                    stageName,
                    stageCode);

                // Monitor for Orleans-specific exceptions during shutdown
                MonitorOrleansSpecificExceptions(ex);

                // Don't re-throw to avoid blocking shutdown
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Monitor and log Orleans-specific exceptions that may occur during shutdown.
        /// </summary>
        private void MonitorOrleansSpecificExceptions(Exception ex)
        {
            // Check for Orleans-specific exceptions that were problematic in historical shutdown issues
            var exceptionType = ex.GetType().Name;
            var exceptionMessage = ex.Message;

            switch (exceptionType)
            {
                case "NonExistentActivationException":
                    _logger.LogWarning(
                        "SiloShutdownObserver: Detected NonExistentActivationException during shutdown - " +
                        "this may indicate grains were deactivated before requests completed. Message: {Message}",
                        exceptionMessage);
                    break;

                case "OrleansMessageRejectionException":
                    _logger.LogWarning(
                        "SiloShutdownObserver: Detected OrleansMessageRejectionException during shutdown - " +
                        "this may indicate message forwarding issues. Message: {Message}",
                        exceptionMessage);
                    break;

                case "SiloUnavailableException":
                    _logger.LogWarning(
                        "SiloShutdownObserver: Detected SiloUnavailableException during shutdown - " +
                        "this may indicate premature gateway closure. Message: {Message}",
                        exceptionMessage);
                    break;

                case "TimeoutException":
                    _logger.LogWarning(
                        "SiloShutdownObserver: Detected TimeoutException during shutdown - " +
                        "this may indicate slow request processing or network issues. Message: {Message}",
                        exceptionMessage);
                    break;

                case "ObjectDisposedException":
                    _logger.LogWarning(
                        "SiloShutdownObserver: Detected ObjectDisposedException during shutdown - " +
                        "this may indicate resources were disposed while still in use. Message: {Message}",
                        exceptionMessage);
                    break;

                case "OrleansException":
                    // Check for specific grain directory issues
                    if (exceptionMessage.Contains("is not owner") || exceptionMessage.Contains("hop limit is reached"))
                    {
                        _logger.LogWarning(
                            "SiloShutdownObserver: Detected grain directory ownership/forwarding issue during shutdown - " +
                            "this indicates grain directory inconsistency during scale-in. Message: {Message}",
                            exceptionMessage);
                    }
                    else
                    {
                        _logger.LogWarning(
                            "SiloShutdownObserver: Detected OrleansException during shutdown - {Message}",
                            exceptionMessage);
                    }

                    break;

                default:
                    // Log any other exceptions for analysis
                    _logger.LogWarning(
                        "SiloShutdownObserver: Detected exception during shutdown: {ExceptionType} - {Message}",
                        exceptionType,
                        exceptionMessage);
                    break;
            }

            // Check for nested Orleans exceptions
            if (ex.InnerException != null)
            {
                MonitorOrleansSpecificExceptions(ex.InnerException);
            }
        }
    }
}