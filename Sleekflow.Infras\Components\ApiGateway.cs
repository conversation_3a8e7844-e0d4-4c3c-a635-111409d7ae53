using Pulumi;
using Pulumi.AzureNative.ContainerRegistry;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Auth0s;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using ConfigurationResponse = Pulumi.AzureNative.App.V20240301.Outputs.ConfigurationResponse;
using Docker = Pulumi.Docker;
using SecretArgs = Pulumi.AzureNative.App.V20240301.Inputs.SecretArgs;

namespace Sleekflow.Infras.Components;

public class ApiGateway
{
    private readonly Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;

    public ApiGateway(
        Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        MyConfig myConfig,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
    }

    public List<App.ContainerApp> InitApiGateway()
    {
        var auth0TenantHubConfig = new Auth0TenantHubConfig();

        var apigwImage = "sleekflow-apigw";
        var globalApigwImage = "sleekflow-gapigw";

        var krakenDImage = GetKrakenDImage(apigwImage);
        var globalKrakenDImage = GetGlobalKrakenDImage(globalApigwImage);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var image = managedEnvAndAppsTuple is FilteredManagedEnvAndAppsTuple ? globalKrakenDImage : krakenDImage;
            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName("apigw");

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = true,
                            TargetPort = 8080,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            }
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 4 : 1,
                            MaxReplicas = 80,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "300"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-apigw-app",
                                Image = image.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 0.5, Memory = "1.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/__health", Port = 8080, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 12,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/__health", Port = 8080, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 12,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/__health", Port = 8080, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 48,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FC_ENABLE", Value = "1"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SALESFORCE_INTEGRATOR_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.SalesforceIntegrator,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SALESFORCE_INTEGRATOR_KEY",
                                            Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "HUBSPOT_INTEGRATOR_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.HubspotIntegrator,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "HUBSPOT_INTEGRATOR_KEY", Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DYNAMICS365_INTEGRATOR_HOST",
                                            Value = GetContainerAppsValues(
                                                "Dynamics365Integrator",
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DYNAMICS365_INTEGRATOR_KEY",
                                            Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_INTEGRATOR_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.GoogleSheetsIntegrator,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_INTEGRATOR_KEY",
                                            Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ZOHO_INTEGRATOR_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.ZohoIntegrator,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ZOHO_INTEGRATOR_KEY",
                                            Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CRM_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.CrmHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CRM_HUB_KEY", Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EMAIL_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.EmailHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EMAIL_HUB_KEY", Value = "k18da7c7j5d03cadf5b1ee6u1d3f6dcb"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGING_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.MessagingHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGING_HUB_KEY", Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COMMERCE_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.CommerceHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COMMERCE_HUB_KEY",
                                            Value =
                                                "w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WEBHOOK_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.WebhookHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SHARE_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.ShareHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SHARE_HUB_KEY", Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "PUBLIC_API_GATEWAY_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.PublicApiGateway,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "PUBLIC_API_GATEWAY_KEY", Value = "a12da7c775d00cada5b1ee611d3f6dca"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUDIT_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.AuditHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUDIT_HUB_KEY",
                                            Value = "3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FLOW_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.FlowHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FLOW_HUB_KEY",
                                            Value = "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FLOW_HUB_EXECUTOR_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.FlowHubExecutor,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FLOW_HUB_EXECUTOR_KEY",
                                            Value = "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FLOW_HUB_INTEGRATOR_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.FlowHubIntegrator,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FLOW_HUB_INTEGRATOR_KEY",
                                            Value = "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TENANT_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.TenantHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TENANT_HUB_KEY",
                                            Value = "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUTH_0_AUDIENCE",
                                            Value = auth0TenantHubConfig.ActionAudience[
                                                ..
                                                    auth0TenantHubConfig.ActionAudience.LastIndexOf(
                                                        "/",
                                                        StringComparison.Ordinal)]
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUTH_0_JWK_URL", Value = auth0TenantHubConfig.JwkUrl
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS",
                                            Value = "/Internals/GetUserAuthenticationDetails"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "INTELLIGENT_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.IntelligentHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "INTELLIGENT_HUB_KEY",
                                            Value = "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "USER_EVENT_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.UserEventHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "USER_EVENT_HUB_KEY",
                                            Value = "wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SUPPORT_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.SupportHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SUPPORT_HUB_KEY",
                                            Value = "wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TICKETING_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.TicketingHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TICKETING_HUB_KEY",
                                            Value = "wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa"
                                        },

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OPA_ENDPOINT",
                                            Value = _myConfig.Name != "production"
                                                ? GetContainerAppsValues(
                                                    ServiceNames.OpenPolicyAgent,
                                                    containerApps,
                                                    res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                                : string.Empty
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "INTERNAL_INTEGRATION_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.InternalIntegrationHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "INTERNAL_INTEGRATION_HUB_KEY",
                                            Value = "LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "INTERNAL_INTEGRATION_HUB_NETSUITE_KEY",
                                            Value = "8q8MGQbNPR09e2amxjiJJAiWYKmmKJAhAjdXkaMYPVuzWtpC2cyzefxKkweLjXKn"
                                        }
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.ApiGateway, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }

    private Docker.Image GetKrakenDImage(string apigwImage)
    {
        return ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            apigwImage,
            _myConfig.BuildTime);
    }

    private Docker.Image GetGlobalKrakenDImage(string globalApigwImage)
    {
        return ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            globalApigwImage,
            _myConfig.BuildTime);
    }

    private Output<string> GetContainerAppsValues(
        string name,
        Dictionary<string, App.ContainerApp> containerApps,
        Func<Output<ConfigurationResponse?>, Output<string>> func)
    {
        return containerApps.TryGetValue(name, out var app)
            ? func(app.Configuration)
            : Output.Create(string.Empty);
    }
}
