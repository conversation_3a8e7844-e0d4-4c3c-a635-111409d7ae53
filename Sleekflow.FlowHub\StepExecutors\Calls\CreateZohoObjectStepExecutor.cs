﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateZohoObjectStepExecutor : IStepExecutor
{
}

public class CreateZohoObjectStepExecutor
    : GeneralStepExecutor<CallStep<CreateZohoObjectStepArgs>>,
        ICreateZohoObjectStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public CreateZohoObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            const string sleekflowContactOwnerFieldName = "ContactOwner";

            var createZohoObjectInput = await GetArgs(callStep, state);

            string? sleekflowUserIdForMapping = null;
            var sleekflowContactOwnerValue = await state.UsrVarDict.GetInnerDictionary().GetAsync(sleekflowContactOwnerFieldName);
            if (sleekflowContactOwnerValue != null && !string.IsNullOrWhiteSpace((string)sleekflowContactOwnerValue))
            {
                sleekflowUserIdForMapping = (string)sleekflowContactOwnerValue;
            }

            await _serviceBusManager.PublishAsync(
                new CreateZohoObjectRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    createZohoObjectInput.StateIdentity.SleekflowCompanyId,
                    createZohoObjectInput.ConnectionId,
                    createZohoObjectInput.EntityTypeName,
                    createZohoObjectInput.FieldsDict,
                    sleekflowUserIdForMapping));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnZohoFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Zoho create object operation timed out after 5 minutes")),
                typeof(OnZohoFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class CreateZohoObjectInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("fields_dict")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> FieldsDict { get; set; }

        [JsonConstructor]
        public CreateZohoObjectInput(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string entityTypeName,
            Dictionary<string, object?> fieldsDict)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            FieldsDict = fieldsDict;
        }
    }

    private async Task<CreateZohoObjectInput> GetArgs(
        CallStep<CreateZohoObjectStepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var entityTypeName = callStep.Args.EntityTypeName;

        var fieldsKeyExprDict = callStep.Args.FieldsNameExprSet
            .GroupBy(x => x.FieldName)
            .ToDictionary(
                x => x.Key,
                g => g.Last().FieldValueExpr);

        var fieldsDict = new Dictionary<string, object?>();

        foreach (var entry in fieldsKeyExprDict)
        {
            fieldsDict[entry.Key] = string.IsNullOrWhiteSpace(entry.Value)
                ? null
                : await _stateEvaluator.EvaluateExpressionAsync(state, entry.Value);
        }

        return new CreateZohoObjectInput(
            state.Id,
            state.Identity,
            connectionId,
            entityTypeName,
            fieldsDict);
    }
}