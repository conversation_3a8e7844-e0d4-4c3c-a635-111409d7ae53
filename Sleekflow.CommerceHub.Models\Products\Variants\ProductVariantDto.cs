using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Products.Variants;

public class ProductVariantDto
{
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(ProductVariant.PropertyNameProductId)]
    public string ProductId { get; set; }

    [JsonProperty(ProductVariant.PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(ProductVariant.PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(ProductVariant.PropertyNamePrices)]
    public List<Price> Prices { get; set; }

    [JsonProperty(ProductVariant.PropertyNamePosition)]
    public int Position { get; set; }

    [JsonProperty(ProductVariant.PropertyNameIsDefaultVariantProduct)]
    public bool IsDefaultVariantProduct { get; set; }

    [JsonProperty(ProductVariant.PropertyNameAttributes)]
    public List<ProductVariant.ProductVariantAttribute> Attributes { get; set; }

    [JsonProperty(ProductVariant.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(ProductVariant.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(ProductVariant.PropertyNameImages)]
    public List<ImageDto> Images { get; set; }

    [JsonConstructor]
    public ProductVariantDto(
        string id,
        string storeId,
        string productId,
        string? sku,
        string? url,
        List<Price> prices,
        int position,
        bool isDefaultVariantProduct,
        List<ProductVariant.ProductVariantAttribute> attributes,
        List<Multilingual> names,
        List<Description> descriptions,
        List<ImageDto> images)
    {
        Id = id;
        StoreId = storeId;
        ProductId = productId;
        Sku = sku;
        Url = url;
        Prices = prices;
        Position = position;
        IsDefaultVariantProduct = isDefaultVariantProduct;
        Attributes = attributes;
        Names = names;
        Descriptions = descriptions;
        Images = images;
    }

    public ProductVariantDto(
        ProductVariant productVariant)
        : this(
            productVariant.Id,
            productVariant.StoreId,
            productVariant.ProductId,
            productVariant.Sku,
            productVariant.Url,
            productVariant.Prices,
            productVariant.Position,
            productVariant.IsDefaultVariantProduct,
            productVariant.Attributes,
            productVariant.Names,
            productVariant.Descriptions,
            productVariant.Images.Select(x => new ImageDto(x)).ToList())
    {
    }

    public ProductVariantDto(
        ProductVariant productVariant,
        Product product)
        : this(
            productVariant.Id,
            productVariant.StoreId,
            productVariant.ProductId,
            productVariant.Sku,
            productVariant.Url,
            productVariant.Prices,
            productVariant.Position,
            productVariant.IsDefaultVariantProduct,
            productVariant.Attributes,
            productVariant.Names,
            productVariant.Descriptions,
            productVariant.Images.Select(x => new ImageDto(x))
                .Concat(product.Images.Select(x => new ImageDto(x)).ToList())
                .ToList())
    {
    }
}