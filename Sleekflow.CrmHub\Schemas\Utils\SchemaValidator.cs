﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Schemas.Utils;

public interface ISchemaValidator
{
    Task ValidateSchemaUniqueNameAsync(string uniqueName, string sleekflowCompanyId);

    void ValidateSchemaMetadata(string displayName, string relationType, PrimaryProperty primaryProperty);

    void ValidateAndSortProperties(List<Property> properties);

    void ValidateIsExceedMaximumPropertyNumPerSchema(int propertyCount, CrmHubConfig crmHubConfig);

    Task ValidateIsReachedMaximumSchemaNumAsync(
        string sleekflowCompanyId,
        CrmHubConfig crmHubConfig,
        SchemaAccessibilitySettings schemaAccessibilitySettings);
}

public partial class SchemaValidator : ISchemaValidator, ISingletonService
{
    private readonly ISchemaRepository _schemaRepository;

    public SchemaValidator(ISchemaRepository schemaRepository)
    {
        _schemaRepository = schemaRepository;
    }

    public async Task ValidateSchemaUniqueNameAsync(string uniqueName, string sleekflowCompanyId)
    {
        ValidateUniqueNamePattern(uniqueName);

        var isSchemaUniqueNameExists = await _schemaRepository.IsSchemaUniqueNameExistsAsync(sleekflowCompanyId, uniqueName);
        if (isSchemaUniqueNameExists)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult("Schema unique name already exists.")
                });
        }
    }

    public void ValidateSchemaMetadata(string displayName, string relationType, PrimaryProperty primaryProperty)
    {
        if (!SchemaRelationshipTypes.SupportedRelationships.Contains(relationType))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult(
                        "Invalid relationship type.",
                        new[]
                        {
                            "RelationshipType"
                        })
                });
        }

        if (primaryProperty.DataType.Name != SchemaPropertyDataTypes.SingleLineText)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult(
                        "Primary property data type should be single line text.",
                        new[]
                        {
                            "PrimaryProperty"
                        })
                });
        }
    }

    public void ValidateAndSortProperties(List<Property> properties)
    {
        properties = properties.OrderBy(p => p.DisplayOrder).ToList();

        var imagePropertyCount = 0;
        for (var i = 0; i < properties.Count; i++)
        {
            var property = properties[i];

            if (property is { IsSearchable: false, IsPinned: true })
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new ValidationResult($"[{property.UniqueName}] Unsearchable property cannot be pinned.")
                    });
            }

            ValidateUniqueNamePattern(property.UniqueName);

            property.DisplayOrder = i + 1;

            if (property.DataType is ImageDataType && ++imagePropertyCount > 1)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new ValidationResult($"Only 1 image property is allowed in each schema.")
                    });
            }
        }

        if (properties.GroupBy(p => p.UniqueName).Any(g => g.Count() > 1))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult("Duplicated property name detected.")
                });
        }
    }

    public void ValidateIsExceedMaximumPropertyNumPerSchema(int propertyCount, CrmHubConfig crmHubConfig)
    {
        if (propertyCount > crmHubConfig.GetOffsetAppliedUsageLimit().CustomObjectMaximumPropertyNumPerSchema)
        {
            throw new SfCrmHubExceedUsageException(UsageLimit.PropertyNameCustomObjectMaximumPropertyNumPerSchema);
        }
    }

    public async Task ValidateIsReachedMaximumSchemaNumAsync(
        string sleekflowCompanyId,
        CrmHubConfig crmHubConfig,
        SchemaAccessibilitySettings schemaAccessibilitySettings)
    {
        if (schemaAccessibilitySettings.Category != SchemaCategories.Custom)
        {
            return;
        }

        var schemaCount = await _schemaRepository.GetUsageLimitCountableSchemasCountAsync(sleekflowCompanyId);
        if (schemaCount >= crmHubConfig.GetOffsetAppliedUsageLimit().CustomObjectMaximumSchemaNum)
        {
            throw new SfCrmHubExceedUsageException(UsageLimit.PropertyNameCustomObjectMaximumSchemaNum);
        }
    }

    /// <summary>
    /// Legal chars: lowercase letters, numbers, underscore.
    /// </summary>
    /// <param name="uniqueName">Display name.</param>
    private static void ValidateUniqueNamePattern(string uniqueName)
    {
        if (string.IsNullOrEmpty(uniqueName) ||
            MyUniqueNameRegex().IsMatch(uniqueName))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult(
                        "Invalid chars in unique name.",
                        new[]
                        {
                            "UniqueName"
                        })
                });
        }
    }

    [GeneratedRegex(@"[^a-z0-9_]")]
    private static partial Regex MyUniqueNameRegex();
}