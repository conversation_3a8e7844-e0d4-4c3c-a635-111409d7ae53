﻿namespace Sleekflow.Exceptions.IntelligentHub;

public class SfKnowledgeBaseSourceTypeNotSupportedException : ErrorCodeException
{
    public string SourceType { get; }

    public SfKnowledgeBaseSourceTypeNotSupportedException(string sourceType)
        : base(
            ErrorCodeConstant.SfKnowledgeBaseSourceTypeNotSupportedException,
            $"This source type is not supported. sourceType=[{sourceType}]",
            new Dictionary<string, object?>
            {
                {
                    "sourceType", sourceType
                }
            })
    {
        SourceType = sourceType;
    }
}