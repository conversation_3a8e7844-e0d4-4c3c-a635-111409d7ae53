﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class CreateProviderUserMappingConfigOutput
{
    [JsonProperty("user_mapping_config")]
    public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

    [JsonConstructor]
    public CreateProviderUserMappingConfigOutput(
        ProviderUserMappingConfigDto userMappingConfig)
    {
        UserMappingConfig = userMappingConfig;
    }
}