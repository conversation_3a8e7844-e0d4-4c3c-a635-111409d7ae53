﻿using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.Locks;

namespace Sleekflow.IntelligentHub.KnowledgeBaseEntries.Indexing;

public interface IKnowledgeBaseEntryIndexingService
{
    SearchIndexClient CreateIndexClient(string sleekflowCompanyId);

    SearchClient CreateSearchClient(string sleekflowCompanyId);

    Task CreateSearchIndexAsync(string sleekflowCompanyId);

    Task IndexEntriesAsync(Dictionary<string, List<KnowledgeBaseEntry>> entryMapByCompanyId);

    Task DeleteEntryIndexesAsync(Dictionary<string, List<KnowledgeBaseEntry>> entryMapByCompanyId);
}

public class KnowledgeBaseEntryIndexingService : ISingletonService, IKnowledgeBaseEntryIndexingService
{
    private readonly ILogger<KnowledgeBaseEntryIndexingService> _logger;
    private readonly IAzureCognitiveSearchConfig _searchConfig;
    private readonly ILockService _lockService;

    public KnowledgeBaseEntryIndexingService(
        ILogger<KnowledgeBaseEntryIndexingService> logger,
        IAzureCognitiveSearchConfig searchConfig,
        ILockService lockService)
    {
        _logger = logger;
        _searchConfig = searchConfig;
        _lockService = lockService;
    }

    private readonly List<string> _prodSleekflowCompanyIds = new List<string>
    {
        "030c9165-e7f3-4f9a-9430-1ba67dc728b3",
        "05966bf7-6c8e-4213-a0f9-f9a5e9103b5c",
        "0bed0da6-2a69-4217-adbd-5e8fa036d69d",
        "0e779f07-e591-4ed4-ab95-a09436430708",
        "0f3ca3d0-3f8c-442c-ade5-082e765fc364",
        "124e4759-69d8-44ba-be11-8fa8d2682767",
        "13b31475-1f5e-49d4-b671-ad7d140c658f",
        "163ff3c4-378e-477f-bb36-71e5a5e42fdc",
        "192bef39-2496-4a70-a209-d32b086258f2",
        "1f4f88e5-bc11-49f1-88e6-9bb5d8c6cef7",
        "21da92b8-53aa-42dd-b4a6-1d24a87979fb",
        "2a9d2d05-c139-4e73-8467-7695be999866",
        "3319d08e-c601-46df-9dfb-2900bcbcffe3",
        "35f3bad9-1f17-4a2e-8ef6-fab115119abe",
        "4432909f-3ded-4290-95f3-7a82b752113b",
        "471a6289-b9b7-43c3-b6ad-395a1992baea",
        "51c82d9a-b49b-4214-9060-7ef3ef66ea22",
        "52745941-20d8-452e-9e38-f11bfe0cddc8",
        "56e4b388-ab93-463b-bb47-33f844393bd3",
        "5a24c178-c3a6-42ec-9aa7-4c7ca0103fa1",
        "5c674c53-2aec-45cd-90c0-784b72a0edcc",
        "600bc941-7300-44a4-a9d1-98efc4a0a8fe",
        "63886738-30f6-47ec-b7c3-2d303a893494",
        "64e9cbfe-89e5-45ce-b388-88e0c1ea1989",
        "6925631d-6e26-4d95-8206-a216d243dc25",
        "6b9c4632-2edb-4405-90e3-ded27f5ad5ea",
        "704ede85-2f4e-4500-842c-74cc45aa0564",
        "7836b1fe-d68d-4cc0-88b8-f71f01b53b0b",
        "871a03ab-b89b-4c6d-96db-614ce569bca9",
        "876ba870-fc86-4e5d-a223-e0a443b29c75",
        "8b8a1ae3-87cf-4ae5-87cc-37aeab0d79b0",
        "8e1bcaf5-13f5-48f0-827e-6bb543800f4a",
        "90e800e0-b6d3-4786-82ab-99338f7456b0",
        "90fbb684-0bdf-47a2-8506-5552dc302255",
        "92dbc0d6-e70e-431d-9a5e-c9a40e7fad4d",
        "942dcd1f-18d9-48a4-9170-4ae254ba1981",
        "98cbb74f-e3a4-481e-b4b6-793b700bcc9d",
        "9c24fb57-b846-40a3-865c-de05e07ff204",
        "a5d6cb54-0c1f-4e3e-b36a-8263fe05f565",
        "ad9328d3-a5bf-41fc-8c58-97b6baf11e92",
        "b62ab9b8-013d-4c57-8c8c-f99110a410dc",
        "bf5d31f9-8506-45fb-aa8c-4f67aa549a37",
        "c118b160-0af9-4c8e-9bf5-2c794bb13d15",
        "ca63373a-06fb-4fec-a7f7-be0cba01cb3a",
        "cbf6050c-5836-41b9-ae0c-35ae8a7183ad",
        "da50589d-2dc2-4352-9e29-4c033d3dd9da",
        "e80448f9-afe8-4871-aaa8-645786ceedef",
        "e9fbb41b-bfd5-46de-b219-f9bdf97ec5bf",
        "ed43bd11-90e2-45aa-8cd3-49405acf2226",
        "f023aa38-eb2f-4e89-abc2-fc51cb602610",
    };

    private readonly List<string> _prodSleekflowCompanyIds_2 = new List<string>
    {
        "00633656-50d2-49ae-bc4d-3961c9514956",
        "006acd11-d746-4d80-a4f2-a523a84ae5b2",
        "01234bc3-9eac-4f24-a362-5c65170474fc",
        "015e5421-ff3b-47e1-a5c4-157b107d958d",
        "022954f1-87b7-4382-a806-086ead1d2a39",
        "02ca1f00-030a-4421-934a-5572f427315e",
        "03a9d450-7e65-412c-80d3-45cc914c7ae3",
        "043f7d8b-f63f-4737-a679-f791892d990a",
        "079c58b1-c26c-4b04-af76-6353df0ab275",
        "0c93fb42-1cdf-440e-858b-592eb89350c3",
        "0fa1687c-1fdb-47f0-8c21-c8a428c76754",
        "10038dd3-5071-4b7f-a982-d69cc1bf6d85",
        "10f5ec9c-5ec9-43d3-bb92-fca81d0245e9",
        "1377c401-3f2a-4f93-9434-78bb4deb60d7",
        "13c148f8-1584-4d62-811f-67315ff0d0db",
        "13ffb480-2f0b-45f3-ae68-0ab13e20ed35",
        "15e45ce8-b390-44e7-8938-5e90becae20a",
        "16e2700e-c2d9-432e-a26f-0b6e04f02f45",
        "1768555e-50db-4b05-84f5-fc2cd30a0ad2",
        "19052af6-03a4-485e-b15f-686c3391dd22",
        "1c1ac2ba-abcc-40e5-bd4d-33a34203996e",
        "21da963c-a525-478d-8ec7-d473791a482e",
        "221afa5b-8dba-4f5f-920a-a5fe5fd28bdf",
        "25d0e172-e974-4dcf-b217-52ab12f25a1d",
        "26015900-9f41-4664-a575-36fa69f4686e",
        "2754c7dc-0b4f-46b3-9d69-76a9bedf515f",
        "288f4e9a-8352-4ec5-8bae-cd2e63995b56",
        "28d10a5f-6b13-40a8-9057-b4084732d4dd",
        "2a2bcc9d-6cdd-48dc-8449-4644a5aaeea8",
        "2abe14dd-d58a-4bfa-b154-98bd3aac75ad",
        "2c92fd01-f9ad-4187-811e-bd9f86456679",
        "2d79b7b1-b8fe-4267-8050-cbac69e8fff4",
        "30a3bd8e-9914-4090-9f52-82089356dcef",
        "316f9493-e0df-4fe7-a37b-ed5580d85843",
        "318840ef-c6c8-4d36-9094-655cefbbc987",
        "34433d31-e17d-4451-9472-59112113bc56",
        "3525bd7b-6f8c-47b9-94ea-f59816570d79",
        "38be0185-fb51-4891-aa4e-0d15aeb388f2",
        "38ffe154-c209-41ea-b64a-90fc30d72302",
        "3960c530-4f6a-4c2b-9e0b-9dcb0d8471b6",
        "39afd035-3080-4546-a6ec-99302c94bf2d",
        "3bc7a9a4-d99b-4442-ab9e-1e6441de5070",
        "3bdec202-cfe5-49dd-ac13-2c70b09641f9",
        "3c517e46-097b-450c-8399-622b64073634",
        "3e017da5-839b-47be-9bec-a48f0985b10e",
        "3e5c8b59-a4e6-40fc-a00f-1a5f45beb1f1",
        "41b3ec8e-1e79-499f-9b14-6a3e8f2d82f7",
        "426333ad-7cdb-44da-99e6-968cff27bb88",
        "42bd1c5b-b5f2-46e3-8c8d-16fb5bca7509",
        "45d65627-af92-426e-a61f-fad4109f67d4",
        "467e6b0e-af25-43ac-92f9-a09a6ce3967d",
        "47bbfa27-29b8-4d5b-b953-5bebaa3fba28",
        "4c8b77f1-a531-410a-9e39-0aca882c9d4d",
        "4d94c28f-2948-494b-bafe-dc33e62d9201",
        "4e9f3660-d561-423a-875c-83f2a8b672f4",
        "50f4fcef-cccc-4afe-9573-984a98ff1f41",
        "52040a17-9578-4c18-8f4e-8691e1779835",
        "520ff000-3b6d-491c-b19f-6e29eea841c8",
        "53f4d6a0-94d4-4529-9797-5f36700fdbec",
        "553d5637-e470-4388-8b32-89265bd45375",
        "559b0201-1c6b-4dc0-8273-71bc9185af8c",
        "55cfa958-2f2e-4100-b661-0d59d9138e48",
        "577ede89-7160-4611-a587-62cbe7a3351a",
        "597e873d-7178-4781-8d5f-a44b728357be",
        "59ad3653-77c0-4665-bdd4-c6f9ebb988f4",
        "5b4db8a8-d99b-4063-9641-7ace87f9df41",
        "5b953649-1a78-421e-b588-38b9805354ad",
        "5d8893f3-32c8-4f77-b7ec-5d165930903c",
        "5fb12654-4672-4365-b620-cfb4062f93ac",
        "622aadcd-47a1-4974-8409-ba7390310ade",
        "62667d3f-c55b-41c8-be79-c61deebac387",
        "62ea24d2-adcb-48a2-a013-9b2a10d2ebcf",
        "6333ca04-f257-4c2c-a682-598615646b34",
        "64248aec-1778-4e7b-b643-12acd98e55a3",
        "66315e00-d3f9-4f90-b212-1a8cce7d9cd2",
        "673422a2-731e-400f-8a44-e55b936d216b",
        "6a12d533-3954-4953-9c1d-525275bd7669",
        "6ae6da96-3f95-4b20-bff8-547400e4a2fb",
        "6d869da9-ab74-43b4-b069-050fb1c22fbc",
        "6f41aee1-91a1-4cce-a3fc-7c3e616e6912",
        "70275552-1d7b-4b70-be21-36be22b2efad",
        "71749479-36d7-4e3f-878f-7e6189727d00",
        "73a6a954-b04e-400c-a0cf-bcbeee8b437d",
        "758622b2-b7f3-42a9-8959-d04a2a71743f",
        "7671f1c4-7477-4f0f-9330-24d76bceb207",
        "775816e4-4cfe-4b64-872c-7692ccbbd136",
        "77bc7665-cfcb-40df-8b5b-d044eb7bba13",
        "78b16687-67f1-4e1b-9333-64770d569b8e",
        "7bc83952-3b0e-4e51-8e3e-1da36fc626a0",
        "7e3c1399-9ab6-4c74-9c87-0fdf6aba4cef",
        "7ecaf76b-7202-4c4c-9813-1b26618b86d5",
        "7ee31440-7264-44f4-a96e-ad45aed35e7c",
        "7f72fc16-6567-4751-9e99-c7ac38ffb877",
        "7fdc1d28-581e-4f6b-b560-bb9651bc08c5",
        "804fed15-a603-44aa-97d4-55945f3654df",
        "8233a2b8-3c2c-4808-8007-82f7d7f475af",
        "846c0582-6eff-4e0e-9da0-542a9ce0cdf2",
        "8493a992-7a38-4a1a-8393-90e2de6f3ebc",
        "87e143ee-29c9-4918-a1ac-ea535fd0ede0",
        "87edf8ee-9aeb-4c85-b9b6-bda4349f335f",
        "87eecf80-216e-4639-a82a-a66ac91501d3",
        "8a4751d5-2a3f-4a2c-8d7d-46c1d9b81a95",
        "8aa6cf66-e501-4426-8380-4eb332c34958",
        "8c0ff4af-14e5-4252-b786-c92376b6396a",
        "8c8f77af-b14f-49f6-b58d-6b9a20ae2b85",
        "8ce05671-3eda-4653-aa52-9fad8fe0150c",
        "8dc97bdf-76eb-4b1d-87e4-5ae1edb61532",
        "8fbf28ae-885d-440d-a3f5-40498faa535a",
        "90c2ac24-fee1-42e4-b2e9-0fe172649732",
        "90d48ea1-d997-42b3-a707-45bf748d8572",
        "90fc5aca-a687-4ded-ada4-29b0cfc4c89d",
        "9209bdc9-d56a-42a1-8b91-7d205e0b159c",
        "92533d3b-ef46-4eb6-8797-05f447322ada",
        "93027edd-fdc5-4dcb-b854-129d97d39b5e",
        "93986340-6cb8-4379-b6d4-81d32c2d3570",
        "94182151-437c-4b25-9428-a7edeae63ba0",
        "94d32a9f-70fc-41e6-8fa8-f4c99ef0a58e",
        "963886c6-0307-42b5-83cb-edc188127e4b",
        "97910170-13aa-43d8-ba3e-8a8da492e643",
        "9953353c-151f-4e10-8306-09386808dc61",
        "9a246659-fbd9-4686-ae59-3e5046392cb0",
        "9c4630ab-8fbb-42d3-9084-0bb7a59dfd2c",
        "9d81bef2-efe5-44ec-957f-6ed656e0ad52",
        "a16eeec3-e124-4690-b5bc-612b29ef585a",
        "a4a1ead4-a6e7-4594-bfdd-1b5295ebad8a",
        "a4a2e83b-7cfd-468d-989d-81163524af33",
        "a5d30644-8138-4c9c-91f2-9ff271f7cdc5",
        "a5f39020-751f-48eb-937a-f16ac71fc29d",
        "a6b05daf-af5f-4591-a620-848461b5aaf3",
        "a6db6579-a1f1-4763-99bb-4324fe1dad94",
        "a8220e32-4d26-43d1-a1f1-fc43a90198fe",
        "aa7fc756-1b47-43e7-b495-08e21fbcceae",
        "aac8165e-5d38-42ee-934f-64604f03607e",
        "ab5c7e41-401f-4381-abbd-6eb5427539e2",
        "ae55dc4d-e74d-440d-a220-70454ee51577",
        "b1485235-dcbe-47e7-b17c-4f22aba36e4d",
        "b16eef1f-674c-4876-96b5-55512a362e7d",
        "b182dab1-5175-4886-98fe-02dfaffd0fd0",
        "b19920a4-03a2-41dd-ad7f-affaa01e7d7c",
        "b2ab2e31-d1ca-4e53-9c9f-5984c2fa0635",
        "b4a3de84-7b7a-44d2-9012-90991b422083",
        "b54fa532-286e-48fc-9c28-7ec6bc725cb5",
        "b5e51ad2-98fd-45ee-bf15-68f3c4bcfa74",
        "b6bc4677-8f36-4632-9c15-542accf23d9c",
        "b7160c93-ea27-49f7-92c0-65e5bf8602f4",
        "b74927b8-a3c7-44eb-b3c8-29f880e263b4",
        "b97b8b91-76ed-42fe-a689-14dbfdca01f0",
        "b9db6fb6-95be-4c5d-aa17-94c41df2e520",
        "ba038227-1616-45b2-a5b4-d845485753e4",
        "bb0ff85b-0082-4f72-90d1-a69b7597e08b",
        "bb28854e-7663-4c13-9b24-dc2776a4520a",
        "bcad7412-a1a4-4390-8f5a-4ef10f9f6214",
        "bec2b17a-62ed-456f-86c2-7127d0d72ac7",
        "c0f71e6a-fbc8-43f1-b88d-51d553bc5664",
        "c4b13647-4547-41f9-9d10-0c04913bc861",
        "c4ec5a68-746e-4e34-a273-b746de1e1325",
        "c69671c1-db30-47da-9408-724ca8010eb5",
        "c6a97855-03c2-4aaa-97ed-77183f3d4ffe",
        "c89be436-a94d-4144-bc14-32e6ff3f2812",
        "c9b89056-df14-4a09-990f-0b49ac48d298",
        "ca6c775d-3c4d-4ae8-af32-cb1dd7ad7486",
        "cd38f570-586b-4e33-b67d-ece854b96b92",
        "cd4aafb2-580a-4e86-becb-43d7c4ff52ec",
        "cd826f05-b9f5-49c7-9e97-de29a1d52ebc",
        "cf36d5c8-b05a-4dee-81ad-0cfe0aea3402",
        "d2618a4a-068c-48a9-8389-9851c597b6c7",
        "d34c20ad-5f85-435b-b094-0b1eb9ec7474",
        "d3e6fa74-6920-4c71-9364-0cd4306503d5",
        "d561f5ce-5fb7-49a4-a989-388e6c64ac32",
        "d5e4ce89-2b13-4278-95fe-b1bc3622fb90",
        "d6f6c344-6304-4e3d-a9a5-4a0ef26d580e",
        "d8f55586-fc15-413b-b200-99a25edeb1e7",
        "d9a36e04-00ea-44e9-8f1f-0b337f5a0162",
        "db70b6da-3d3c-4d0f-9e2d-8d15982836bd",
        "dd829b34-5997-4196-b797-7eb50a3c9c8c",
        "de564f24-95ed-4806-be79-172d4f9577b6",
        "e072a1f0-bffa-4ea7-b849-9ed892347076",
        "e149687b-9815-44ee-b294-cd54c55080f5",
        "e1f49bce-5935-4301-87ce-7c425ec62eb3",
        "e3c75c3c-b8ba-49b4-a26a-ef7b38f09a17",
        "e560180a-b798-4c8d-8bbd-16a63f39e43d",
        "e83f2b29-a8a0-4001-bd71-4bac62b7750a",
        "e9aa21af-3c54-44f9-b591-274d62702ebb",
        "ea6ee77a-28b2-4066-94d2-3c78d3a67586",
        "ed8823a0-356f-4503-b692-552f3f6f575e",
        "ee6edc77-cb64-408b-95d9-d2aa2057d49d",
        "eea7a273-8d80-4958-9326-f0114db0f00a",
        "eff1f5f4-bf6a-48c8-a78f-8b8005a6ef23",
        "f0bd1cad-0cb6-49fb-81fe-c566a57a6c97",
        "f15e65ea-193e-45b6-a709-efb5dff76b47",
        "f1cf7e23-d5c6-40d6-b921-3803482f1158",
        "f37971b3-f675-4694-a5ef-ac4291644e6d",
        "f37fe31f-c9a3-4872-99cf-5e6015660e74",
        "f64136e9-9590-472a-9dc5-3edd4f30e541",
        "f6dd88a1-1b43-4653-b7d1-1d7d373a9cb0",
        "f7464831-d08a-4a81-81cc-b35cfb14004a",
        "f826e1cb-9801-4e66-9124-2ae24d60249f",
        "fb68458e-5d79-4009-88c0-1ed8dbb968b8",
        "fb98fd22-0ab9-4b7f-b541-8b308af4afe6",
        "fda9a4c9-10d2-4ab0-9b06-94d3d5506220"
    };

    private readonly List<string> _stagingSleekflowCompanyIds = new List<string>
    {
        "39ee5f12-7997-45fa-a960-e4feecba425c",
    };

    private string DetermineSuffix(string companyId)
    {
        if (_prodSleekflowCompanyIds.Contains(companyId))
        {
            return string.Empty;
        }

        if (_stagingSleekflowCompanyIds.Contains(companyId))
        {
            return string.Empty;
        }

        if (_prodSleekflowCompanyIds_2.Contains(companyId))
        {
            return "_2";
        }

        return "_3";
    }

    public SearchIndexClient CreateIndexClient(string sleekflowCompanyId)
    {
        string suffix = DetermineSuffix(sleekflowCompanyId);
        return new SearchIndexClient(
            new Uri(_searchConfig.GetEndpoint(suffix)),
            new AzureKeyCredential(_searchConfig.GetKey(suffix)));
    }

    public SearchClient CreateSearchClient(string sleekflowCompanyId)
    {
        string suffix = DetermineSuffix(sleekflowCompanyId);
        return new SearchClient(
            new Uri(_searchConfig.GetEndpoint(suffix)),
            sleekflowCompanyId,
            new AzureKeyCredential(_searchConfig.GetKey(suffix)),
            new SearchClientOptions
            {
                Serializer = new NewtonsoftJsonObjectSerializer(
                    NewtonsoftJsonObjectSerializer.CreateJsonSerializerSettings())
            });
    }

    public async Task CreateSearchIndexAsync(string sleekflowCompanyId)
    {
        var indexClient = CreateIndexClient(sleekflowCompanyId);

        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(CreateSearchIndexAsync),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(3),
            TimeSpan.FromSeconds(30));

        try
        {
            var indexNames = indexClient.GetIndexNamesAsync();
            await foreach (var page in indexNames.AsPages())
            {
                if (page.Values.Any(indexName => indexName == sleekflowCompanyId))
                {
                    return;
                }
            }

            var index = new SearchIndex(sleekflowCompanyId)
            {
                Fields =
                {
                    new SimpleField("id", SearchFieldDataType.String)
                    {
                        IsKey = true
                    },
                    new SearchableField("content"),
                    new SearchableField("content_en")
                    {
                        AnalyzerName = "en.microsoft"
                    },
                    new SimpleField("chunk_id", SearchFieldDataType.String)
                    {
                        IsFacetable = true, IsFilterable = true
                    },
                    new SimpleField("sourceType", SearchFieldDataType.String)
                    {
                        IsFacetable = true, IsFilterable = true
                    },
                    new SimpleField("sourceId", SearchFieldDataType.String)
                    {
                        IsFacetable = true, IsFilterable = true
                    },
                    new SimpleField("category", SearchFieldDataType.String)
                    {
                        IsFacetable = true, IsFilterable = true
                    },
                    new SimpleField("sleekflow_company_id", SearchFieldDataType.String)
                    {
                        IsFacetable = true, IsFilterable = true
                    }
                },
                SemanticSearch = new SemanticSearch
                {
                    Configurations =
                    {
                        new SemanticConfiguration(
                            "default",
                            new SemanticPrioritizedFields
                            {
                                ContentFields =
                                {
                                    new SemanticField("content")
                                }
                            })
                    }
                }
            };

            await indexClient.CreateIndexAsync(index);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task IndexEntriesAsync(
        Dictionary<string, List<KnowledgeBaseEntry>> entryMapByCompanyId)
    {
        foreach (var sleekflowCompanyId in entryMapByCompanyId.Keys)
        {
            // Access the corresponding value using the key
            var knowledgeBaseEntries = entryMapByCompanyId[sleekflowCompanyId];

            var searchClient = CreateSearchClient(sleekflowCompanyId);

            var iteration = 0;
            var batch = new IndexDocumentsBatch<SearchDocument>();
            foreach (var entry in knowledgeBaseEntries)
            {
                _logger.LogInformation($"Indexing entry {entry.Id}.");
                var strCategories = JsonConvert.SerializeObject(entry.Categories);

                batch.Actions.Add(
                    new IndexDocumentsAction<SearchDocument>(
                        IndexActionType.MergeOrUpload,
                        new SearchDocument
                        {
                            ["id"] = entry.Id,
                            ["sleekflow_company_id"] = sleekflowCompanyId,
                            ["content"] = entry.Content,
                            ["content_en"] = entry.ContentEn,
                            ["chunk_id"] = entry.ChunkId,
                            ["sourceType"] = entry.KnowledgeBaseEntrySource.SourceType,
                            ["sourceId"] = entry.KnowledgeBaseEntrySource.SourceId,
                            ["category"] = strCategories
                        }));

                iteration++;
                if (iteration % 1_000 is 0)
                {
                    // Every one thousand documents, batch create.
                    IndexDocumentsResult result = await searchClient.IndexDocumentsAsync(batch);
                    var succeeded = result.Results.Count(r => r.Succeeded);

                    batch = new ();
                }

                _logger.LogInformation($"Indexed document {entry.Id}.");
            }

            if (batch is { Actions.Count: > 0 })
            {
                // Any remaining documents, batch create.
                SearchIndex? index = new SearchIndex($"index-{batch.Actions.Count}");
                IndexDocumentsResult result = await searchClient.IndexDocumentsAsync(batch);
                var succeeded = result.Results.Count(r => r.Succeeded);
            }
        }
    }

    public async Task DeleteEntryIndexesAsync(
        Dictionary<string, List<KnowledgeBaseEntry>> entryMapByCompanyId)
    {
        foreach (var sleekflowCompanyId in entryMapByCompanyId.Keys)
        {
            var searchClient = CreateSearchClient(sleekflowCompanyId);

            var documentsToDelete = new List<dynamic>();
            var entries = entryMapByCompanyId[sleekflowCompanyId];
            foreach (var e in entries)
            {
                documentsToDelete.Add(
                    new
                    {
                        id = e.Id
                    });
            }

            IndexDocumentsResult result = await searchClient.DeleteDocumentsAsync(documentsToDelete);
            var succeeded = result.Results.Count(r => r.Succeeded);
        }
    }
}