using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Triggers.TransactionLogs;

[TriggerGroup(ControllerNames.TransactionLogs)]
public class EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronization
    : ITrigger<
        EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronization.
        EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput,
        EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronization.
        EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutput>
{
    private readonly IBus _bus;

    public EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronization(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput
    {
        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [Required]
        [JsonProperty("last_conversation_usage_insert_timestamp")]
        public long LastConversationUsageInsertTimestamp { get; set; }

        [Required]
        [JsonProperty("latest_conversation_usage_insert_timestamp")]
        public long LatestConversationUsageInsertTimestamp { get; set; }

        [JsonConstructor]
        public EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput(
            string facebookWabaId,
            string facebookBusinessId,
            long lastConversationUsageInsertTimestamp,
            long latestConversationUsageInsertTimestamp)
        {
            FacebookWabaId = facebookWabaId;
            FacebookBusinessId = facebookBusinessId;
            LastConversationUsageInsertTimestamp = lastConversationUsageInsertTimestamp;
            LatestConversationUsageInsertTimestamp = latestConversationUsageInsertTimestamp;
        }
    }

    public class EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutput
    {
    }

    public async Task<EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutput> F(
        EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput
            enqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput)
    {
        var lastConversationUsageInsertTimestamp =
            enqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput
                .LastConversationUsageInsertTimestamp;
        var latestConversationUsageInsertTimestamp =
            enqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput
                .LatestConversationUsageInsertTimestamp;

        if (DateTimeOffset.FromUnixTimeSeconds(lastConversationUsageInsertTimestamp) >
            DateTimeOffset.FromUnixTimeSeconds(latestConversationUsageInsertTimestamp))
        {
            throw new SfInternalErrorException("Unsupported date ranges");
        }

        await _bus.Publish(
            new OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent(
                enqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput.FacebookWabaId,
                enqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput.FacebookBusinessId,
                lastConversationUsageInsertTimestamp,
                latestConversationUsageInsertTimestamp));

        return new EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutput();
    }
}