using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.Plugins;

public interface IInformationGatheringPlugin
{
    [KernelFunction("extract_fields")]
    [Description("Extracts field information from conversation context based on required fields")]
    [return: Description("A JSON string containing the extracted field information")]
    Task<InformationGatheringPlugin.ExtractFieldsOutput> ExtractFieldsAsync(
        Kernel kernel,
        [Description("The conversation context to extract information from")]
        string conversationContext,
        List<RequiredField>? requiredFields = null);
}

public class InformationGatheringPlugin : IInformationGatheringPlugin, IScopedService
{
    private readonly ILogger<InformationGatheringPlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public InformationGatheringPlugin(
        ILogger<InformationGatheringPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("extract_fields")]
    [Description("Extracts field information from conversation context based on required fields")]
    [return: Description("A JSON string containing the extracted field information")]
    public async Task<ExtractFieldsOutput> ExtractFieldsAsync(
        Kernel kernel,
        [Description("The conversation context to extract information from")]
        string conversationContext,
        [Description("The list of required fields to extract information for")]
        List<RequiredField>? requiredFields = null)
    {
        _logger.LogInformation(
            "Extracting fields from conversation context. ConversationContext: {ConversationContext}",
            conversationContext);

        try
        {
            // Dynamically generate Field Descriptions based on LeadNurturingTools
            var leadNurturingTools =
                (kernel.Data.TryGetValue(KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG, out var obj)
                    ? obj
                    : null) as LeadNurturingTools;

            List<RequiredField> myRequiredFields;
            if (requiredFields is not null)
            {
                myRequiredFields = requiredFields;
            }
            else if (leadNurturingTools?.DemoTool == null || leadNurturingTools.DemoTool.RequiredFields.Count == 0)
            {
                myRequiredFields = new List<RequiredField>()
                {
                    new RequiredField("first_name", "The customer's first name", true),
                    new RequiredField("last_name", "The customer's last name", true),
                    new RequiredField("email", "The customer's email address", true)
                };
            }
            else
            {
                myRequiredFields = leadNurturingTools.DemoTool.RequiredFields;
            }

            var gatheredFields = await ExtractFieldsAsync(kernel, myRequiredFields, conversationContext);

            return gatheredFields;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting fields from conversation context");
            throw;
        }
    }

    private async Task<ExtractFieldsOutput> ExtractFieldsAsync(
        Kernel kernel,
        List<RequiredField> requiredFields,
        string conversationContext)
    {
        var executionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_FLASH,
            true);

        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            executionSettings,
            [
                new PromptExecutionSettingsUtils.Property(
                    "extracted_fields",
                    "array",
                    false,
                    null,
                    new PromptExecutionSettingsUtils.Property(
                        string.Empty,
                        "object",
                        false,
                        [
                            new PromptExecutionSettingsUtils.Property("field_name", "string"),
                            new PromptExecutionSettingsUtils.Property("is_required", "boolean"),
                            new PromptExecutionSettingsUtils.Property("extraction_reasoning", "string"),
                            new PromptExecutionSettingsUtils.Property("field_value", "string", true),
                            new PromptExecutionSettingsUtils.Property("validation_reasoning", "string"),
                            new PromptExecutionSettingsUtils.Property("is_valid_field_value", "boolean"),
                        ])),
            ]);

        var requiredFieldsInstructions =
            $"{string.Join("\n", requiredFields.Select(f => $"- {f.Name}: {f.Description}{(f.IsRequired ? " (Required)" : " (Optional)")}").ToArray())}";

        var prompt =
            $$"""
              You are a Information Extractor.

              ### Instructions

              Follow these steps carefully:

              **Step 1: Extract Information from the Conversation**

              - **What to Do**: Review the entire conversation history to find values for each field listed in the "Field Descriptions."
              - **How to Do It**:
                - Look for 4 types of mentions
                  1. **direct mentions**: e.g. "My name is John"
                  2. **indirect mentions**: e.g. "Call me John"
                  3. **guesses**: All guesses or possible interpretations (e.g., Customer said 'My company is HK based', we could guess the region code is '+852'.)
                  4. **contact properties**: The information provided in the contact properties may be outdated.
                - Handling each type of mention:
                  - Direct Mentions & Indirect Mentions: `extraction_reasoning` should include the direct and indirect mentions. `field_value` should include the only confirmed value.
                  - Guesses: `extraction_reasoning` should include the guesses. `field_value` should be null. You should prompt the consumer in `extraction_reasoning` to confirm the guesses with the customer.
                  - Contact Properties: `extraction_reasoning` should include the contact properties. `field_value` should be null. You should prompt the consumer in `extraction_reasoning` to confirm the information with the customer.
                - If no mention is found, `extraction_reasoning` should state no mention is found. `field_value` should be null.
                - If multiple mentions exist:
                  - `extraction_reasoning` should include 1. all the mentions 2. evaluation the most suitable combination strategy 3. if enough partial mentions are found, combine them.
                    - Combine partial mentions if they clearly fit together (e.g., "+1" and "************" for a phone number).
                    - Use the most recent complete mention if there are multiple complete values.
                  - `field_value` should be the confirmed value from the selected combination strategy.
                - If the description has multiple options, you should choose the most suitable one based on the confirmed value. The matching should be covering the most obvious and common cases. Otherwise, you should prompt the consumer in `extraction_reasoning` to confirm the information with the customer.

              **Step 2: Validate the Extracted Information**

              - **What to Do**: Check if each extracted `field_value` meets the requirements in the "Field Descriptions."
              - **How to Do It**:
                - Explain in `validation_reasoning` why the value is valid or invalid.
                - Set `is_valid_field_value` to `true` if valid, `false` otherwise.
                - **Examples**:
                  - For emails: Ensure it includes "@" (e.g., "<EMAIL>" is valid).
                  - For phone numbers: Ensure it has a country code and complete number (e.g., "******-987-6543" is valid).

              ### Output Structure

              **Example Output (Missing/Invalid Fields)**:
              ```json
              {
                "extracted_fields": [
                  {
                    "field_name": "first_name",
                    "is_required": true,
                    "extraction_reasoning": "1. Direct Mention: Customer said 'My name is John'",
                    "field_value": "John",
                    "validation_reasoning": "'John' is a valid first name",
                    "is_valid_field_value": true
                  },
                  {
                    "field_name": "phone_number",
                    "is_required": true,
                    "extraction_reasoning": "1. Direct Mention: Customer said 'My phone number is 6109 6000'.\n2. Note: The phone number is incomplete as it lacks the country code.\n3. Guess: Based on 'My company is HK based', the region code might be '+852'.\n4. Note: Confirmation is needed for the combined phone number.",
                    "field_value": "6109 6000",
                    "validation_reasoning": "Invalid: Missing country code",
                    "is_valid_field_value": false
                  },
                  {
                    "field_name": "email",
                    "is_required": true,
                    "extraction_reasoning": "1. Contact Property: Customer's email is '<EMAIL>'.\n2. Note: Customer confirmation is needed.",
                    "field_value": null,
                    "validation_reasoning": "Invalid: Contact property is unconfirmed.",
                    "is_valid_field_value": false
                    }
                ]
              }
              ```

              **Example Output (All Required Fields Valid)**:
              ```json
              {
                "extracted_fields": [
                  {
                    "field_name": "first_name",
                    "is_required": true,
                    "extraction_reasoning": "1. Direct Mention: Customer said 'My name is Jane'",
                    "field_value": "Jane",
                    "validation_reasoning": "'Jane' is a valid first name",
                    "is_valid_field_value": true
                  },
                  {
                    "field_name": "phone_number",
                    "is_required": true,
                    "extraction_reasoning": "1. Direct Mention: Customer said '+1'.\n2. Direct Mention: Customer said '************'.\n3. Combination Strategy: Combine the country code '+1' with the phone number '************' to form '******-987-6543'.",
                    "field_value": "******-987-6543",
                    "validation_reasoning": "Valid: Includes country code and complete number",
                    "is_valid_field_value": true
                  }
                ]
              }
              ```

              ### Field Descriptions

              {{requiredFieldsInstructions}}

              ### Conversation Context
              {{conversationContext}}

              ### Notes

              - Analyze **all fields** (required and optional) and include them in `extracted_fields`.
              - Set `field_value` to `null` only if no value is found; otherwise, provide the extracted and confirmed value, even if invalid.
              - Guessing is different from assuming. You should not assume anything. You should guess in the `extraction_reasoning`, but not in the field_value. Guessing is encouraged.
              - Information provided in the contact properties may be outdated. You should put it in `extraction_reasoning`, but not in the field_value. Confirming is necessary.
              """;

        var functionResult = await kernel.InvokePromptAsync(
            prompt,
            arguments: new KernelArguments(executionSettings));

        try
        {
            return JsonConvert.DeserializeObject<ExtractFieldsOutput>(
                functionResult.GetValue<string>()!)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deserializing the extracted fields");
            throw new InvalidOperationException("Failed to deserialize the extracted fields", ex);
        }
    }

    public class ExtractFieldsOutput
    {
        [JsonProperty("extracted_fields")]
        public List<ExtractedField> GatheredFields { get; set; }

        [method: JsonConstructor]
        public ExtractFieldsOutput(
            List<ExtractedField> gatheredFields)
        {
            GatheredFields = gatheredFields;
        }
    }

    [method: JsonConstructor]
    public class ExtractedField(
        string fieldName,
        string extractionReasoning,
        string? fieldValue,
        string validationReasoning,
        bool isValidFieldValue)
    {
        [JsonProperty("field_name")]
        public string FieldName { get; set; } = fieldName;

        [JsonProperty("extraction_reasoning")]
        public string ExtractionReasoning { get; set; } = extractionReasoning;

        [JsonProperty("field_value")]
        public string? FieldValue { get; set; } = fieldValue;

        [JsonProperty("validation_reasoning")]
        public string ValidationReasoning { get; set; } = validationReasoning;

        [JsonProperty("is_valid_field_value")]
        public bool IsValidFieldValue { get; set; } = isValidFieldValue;
    }
}