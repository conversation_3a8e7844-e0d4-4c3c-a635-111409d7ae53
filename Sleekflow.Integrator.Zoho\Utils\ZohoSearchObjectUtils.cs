﻿using System.Text;
using Sleekflow.CrmHub.Models.InflowActions;

namespace Sleekflow.Integrator.Zoho.Utils;

public static class ZohoSearchObjectUtils
{
    public static string BuildCriteria(List<SearchObjectCondition> conditions)
    {
        var criteriaBuilder = new StringBuilder();
        foreach (var condition in conditions)
        {
            if (criteriaBuilder.Length > 0)
            {
                criteriaBuilder.Append(" and ");
            }

            criteriaBuilder.Append('(');
            criteriaBuilder.Append(condition.FieldName);
            criteriaBuilder.Append(':');

            switch (condition.Operator)
            {
                case SearchObjectConditionOperators.IsEqualTo:
                    criteriaBuilder.Append("equals:");
                    break;
                case SearchObjectConditionOperators.IsNotEqualTo:
                    criteriaBuilder.Append("not_equals:");
                    break;
                case SearchObjectConditionOperators.IsGreaterThan:
                case SearchObjectConditionOperators.IsAfter:
                    criteriaBuilder.Append("greater_than:");
                    break;
                case SearchObjectConditionOperators.IsLessThan:
                case SearchObjectConditionOperators.IsBefore:
                    criteriaBuilder.Append("less_than:");
                    break;
                case SearchObjectConditionOperators.StartsWith:
                    criteriaBuilder.Append("starts_with:");
                    break;
                case SearchObjectConditionOperators.In:
                    criteriaBuilder.Append("in:");
                    break;
                case SearchObjectConditionOperators.IsGreaterThanOrEqualTo:
                    criteriaBuilder.Append("greater_equal:");
                    break;
                case SearchObjectConditionOperators.IsLessThanOrEqualTo:
                    criteriaBuilder.Append("less_equal:");
                    break;
            }

            criteriaBuilder.Append(condition.Value);
            criteriaBuilder.Append(')');
        }

        return criteriaBuilder.ToString();
    }
}