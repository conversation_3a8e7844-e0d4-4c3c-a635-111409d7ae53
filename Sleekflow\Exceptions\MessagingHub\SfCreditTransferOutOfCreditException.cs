﻿using Newtonsoft.Json;

namespace Sleekflow.Exceptions.MessagingHub;

public class SfCreditTransferOutOfCreditException : ErrorCodeException
{
    public string CreditTransferTargetFromEntity { get; }

    public string CreditTransferTargetFromEntityId { get; }

    public string CreditTransferTargetToEntity { get; }

    public object CreditTransferTargetToEntityIds { get; }

    public string CurrentTargetFromEntityCredit { get; }

    public string CreditTransferAmount { get; }

    public SfCreditTransferOutOfCreditException(
        string creditTransferTargetFromEntity,
        string creditTransferTargetFromEntityId,
        string creditTransferTargetToEntity,
        object creditTransferTargetToEntityIds,
        string currentTargetFromEntityCredit,
        string creditTransferAmount)
        : base(
            ErrorCodeConstant.SfCreditTransferOutOfCreditException,
            $"Credit transfer from {creditTransferTargetFromEntity} id {creditTransferTargetFromEntityId} to {creditTransferTargetToEntity} ids {JsonConvert.SerializeObject(creditTransferTargetToEntityIds)} is out of credit because the current credit is {currentTargetFromEntityCredit} and the transfer amount is {creditTransferAmount}.",
            new Dictionary<string, object?>
            {
                {
                    "creditTransferTargetFromEntity", creditTransferTargetFromEntity
                },
                {
                    "creditTransferTargetFromEntityId", creditTransferTargetFromEntityId
                },
                {
                    "creditTransferTargetToEntity", creditTransferTargetToEntity
                },
                {
                    "creditTransferTargetToEntityIds", JsonConvert.SerializeObject(creditTransferTargetToEntityIds)
                },
                {
                    "currentTargetFromEntityCredit", currentTargetFromEntityCredit
                },
                {
                    "creditTransferAmount", creditTransferAmount
                }
            })
    {
        CreditTransferTargetFromEntity = creditTransferTargetFromEntity;
        CreditTransferTargetFromEntityId = creditTransferTargetFromEntityId;
        CreditTransferTargetToEntity = creditTransferTargetToEntity;
        CreditTransferTargetToEntityIds = creditTransferTargetToEntityIds;
        CurrentTargetFromEntityCredit = currentTargetFromEntityCredit;
        CreditTransferAmount = creditTransferAmount;
    }
}