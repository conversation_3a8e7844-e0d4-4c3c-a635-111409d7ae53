﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class UnassociateObject : ITrigger
{
    private readonly IBus _bus;

    public UnassociateObject(
        IBus bus)
    {
        _bus = bus;
    }

    public class UnassociateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        [StringLength(128)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        [StringLength(64)]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        [StringLength(128)]
        public string ObjectId { get; set; }

        [JsonProperty("provider_object_id")]
        [Required]
        [StringLength(128)]
        public string ProviderObjectId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        [StringLength(128)]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public UnassociateObjectInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string objectId,
            string providerObjectId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
            ProviderObjectId = providerObjectId;
            ProviderName = providerName;
        }
    }

    public class UnassociateObjectOutput
    {
    }

    public async Task<UnassociateObjectOutput> F(
        UnassociateObjectInput unassociateObjectInput)
    {
        if (unassociateObjectInput.ProviderName == "sleekflow" && unassociateObjectInput.EntityTypeName == "User")
        {
            var onObjectOperationEvent = new OnObjectOperationEvent(
                new Dictionary<string, object?>
                {
                    {
                        "Id", unassociateObjectInput.ProviderObjectId
                    }
                },
                OnObjectOperationEvent.OperationUnassociateObject,
                unassociateObjectInput.ProviderName,
                unassociateObjectInput.SleekflowCompanyId,
                unassociateObjectInput.ProviderObjectId,
                unassociateObjectInput.EntityTypeName,
                unassociateObjectInput.ObjectId);

            await _bus.Publish(
                onObjectOperationEvent,
                context => { context.ConversationId = Guid.Parse(unassociateObjectInput.SleekflowCompanyId); });

            return new UnassociateObjectOutput();
        }

        throw new NotImplementedException();
    }
}