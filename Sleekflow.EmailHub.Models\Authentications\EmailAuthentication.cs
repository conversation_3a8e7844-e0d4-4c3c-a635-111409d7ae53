using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.EmailHubDb;

namespace Sleekflow.EmailHub.Models.Authentications;

[Resolver(typeof(IEmailHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.EmailAuthentication)]
public class EmailAuthentication : Entity
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; private set; }

    [Required]
    [JsonProperty("email_address")]
    public string EmailAddress { get; private set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; private set; }

    [Required]
    [JsonProperty("is_authenticated")]
    public bool IsAuthenticated { get; set; }

    [JsonProperty("email_authentication_metadata", TypeNameHandling = TypeNameHandling.Objects)]
    public EmailAuthenticationMetadata EmailAuthenticationMetadata { get; set; }

    [JsonConstructor]
    public EmailAuthentication(
        string id,
        string sleekflowCompanyId,
        string emailAddress,
        bool isAuthenticated,
        EmailAuthenticationMetadata emailAuthenticationMetadata,
        string providerName)
        : base(id, ContainerNames.EmailAuthentication)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EmailAddress = emailAddress;
        IsAuthenticated = isAuthenticated;
        EmailAuthenticationMetadata = emailAuthenticationMetadata;
        ProviderName = providerName;
    }
}