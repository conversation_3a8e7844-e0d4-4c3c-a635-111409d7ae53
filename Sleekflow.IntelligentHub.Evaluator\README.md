# Sleekflow.IntelligentHub.Evaluator

## Overview
This project is a testing and evaluation framework for Sleekflow's intelligent hub services, particularly focused on evaluating chat and lead scoring capabilities. It enables automated testing of AI-powered chat responses, RAG (Retrieval-Augmented Generation) systems, and lead scoring functionalities.

## Features
- **Chat Evaluation**: Test and score AI chat responses against model answers
- **RAG System Evaluation**: Measure the effectiveness of Retrieval-Augmented Generation
- **Lead Scoring Evaluation**: Validate lead scoring algorithms
- **Diagnostic Reports**: Generate detailed diagnostics and performance summaries
- **Test Case Management**: Organize test cases by client/scenario
- **CSV Export**: Export test results to CSV format for further analysis

## Technical Architecture

### Core Components
- **ChatEvalFixture**: Orchestrates test execution and collects evaluation metrics
- **ChatEvalQuestion**: Represents test cases with question context, expected answers, and configuration
- **ChatEvalResult**: Stores evaluation results, scores, and diagnostics
- **OverallScore**: Aggregates results and generates comprehensive reports
- **ChatEvalScoreCalculator**: Implements scoring algorithms for different evaluation aspects
- **DiagnosticsSummaryPlugin**: Analyzes diagnostic information to identify improvement areas

### Evaluation Metrics
- **AnswerScoringScore**: Measures how well responses match expected answers (0.0-5.0)
- **RagOutputScoringScore**: Evaluates the quality of retrieved information (0.0-5.0)
- **ElapsedMilliseconds**: Performance tracking for response generation
- **Qualitative Analysis**: Detailed feedback on response quality and relevance

### Test Execution Flow
1. Test cases are loaded from `TestCases` directory
2. `ChatEvalFixture` executes each test against multiple configurations
3. Evaluators score responses and generate diagnostics
4. Results are collected and aggregated by `OverallScore`
5. Reports are generated in console output and CSV format

## Project Structure
- **ChatEvals/**: Core chat evaluation components and scoring algorithms
- **Evaluators/**: Evaluation logic for different aspects (RAG, answers, etc.)
- **TestCases/**: Test case definitions organized by client/scenario
- **Methods/**: Utility methods and shared functionality
- **Plugins/**: Extension plugins for additional evaluation capabilities
- **Constants/**: Shared constants and configuration values
- **Utils/**: General utility functions
- **KnowledgeSources/**: Knowledge base sources for testing

## Integration with Sleekflow Systems

### Intelligent Hub Integration
The evaluator connects to the Sleekflow.IntelligentHub service through Alba test hosting:
```csharp
Host = await AlbaHost.For<Program>(
    webHostBuilder =>
    {
        webHostBuilder.ConfigureServices(
            services =>
            {
                services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                services.AddScoped<IAgentCollaborationDefinitionService, TestAgentCollaborationDefinitionService>();
            });
    },
    Array.Empty<IAlbaExtension>());
```

### Test Repository Context
The evaluator uses a test repository context to control environment settings:
```csharp
public class TestRepositoryContext : IDynamicFiltersRepositoryContext
{
    public bool IsSoftDeleteEnabled { get; set; } = true;
    public string? SleekflowCompanyId { get; set; } = null;
}
```

### Knowledge Base Testing
Evaluates integration with external knowledge sources:
- Retrieval effectiveness
- Content relevance
- Response accuracy based on retrieved content

## Test Case Definition

### Creating Chat Evaluation Tests
Test cases are defined using the `ChatEvalQuestion` class with the following properties:
```csharp
public class ChatEvalQuestion
{
    public required string Scenario { get; init; }
    public required ChatMessageContent[] QuestionContexts { get; init; }
    public required string ModelAnswer { get; init; }
    public string[]? SourceFilenames { get; init; }
    public bool? ShouldAssignLead { get; init; }
    public ChatEvalConfig ChatEvalConfig { get; init; } = new();
}
```

### Test Configuration Options
- **ChatEvalConfig**: Controls test behavior and environment settings
- **ActionDefinitionsMock**: Mocks for testing action definitions
- **LeadNurturingTools**: Configuration for lead nurturing functionality

## Scoring Methodology

### Answer Scoring
Uses AI evaluation models to assess response quality:
- **Correctness**: Accuracy of information
- **Completeness**: Coverage of expected information
- **Relevance**: Focus on addressing the specific query
- **Clarity**: Readability and understandability

### RAG Scoring
Evaluates retrieval performance:
- **Retrieval Precision**: Accuracy of retrieved documents
- **Information Usage**: How well retrieved information is incorporated
- **Citation Accuracy**: Proper attribution of information sources

## Getting Started

### Prerequisites
- .NET 8.0
- NUnit testing framework
- Access to Sleekflow development environment

### Running Tests
```bash
# Run all tests
dotnet test

# Run specific test categories
dotnet test --filter "Category=ChatEvaluation"
```

### Adding New Test Cases
1. Create a new class in the `TestCases` directory
2. Implement test cases following the pattern in existing files
3. Add appropriate attributes for categorization

## Testing Methodology
The framework evaluates several aspects of the intelligent systems:
- Answer quality (compared to model answers)
- Relevance of retrieved information
- Performance metrics (response time, token usage)
- Lead nurturing functionality
- Assignment actions

## Reporting
Test results are output in multiple formats:
- Console output for immediate feedback
- Detailed CSV export for further analysis
- Aggregated scoring and diagnostics summary

### Sample Report Output
```
Test Id: [GUID]
Scenario: Customer inquiry about product features
----------------------- Scores ------------------------
NoRAG (2.3s); Primary Score: 3.75; RAG Score: 0.00
LightRag_Medium (3.1s); Primary Score: 4.25; RAG Score: 4.50
----------------------- Diagnostics ------------------------
NoRAG diagnostics:
Primary Diagnostic: {"Strengths":"...","Weaknesses":"...","Areas for Improvement":"..."}
LightRag_Medium diagnostics:
Primary Diagnostic: {"Strengths":"...","Weaknesses":"...","Areas for Improvement":"..."}
RAG Diagnostic: {"RetrievalQuality":"...","SourceUtilization":"...","Accuracy":"..."}
```

## Dependencies
- Microsoft.Extensions.AI libraries
- Semantic Kernel
- OpenAI integration
- Alba testing framework
- NUnit testing framework

## Contributing
Follow existing patterns when adding new test cases or evaluation mechanisms. Ensure all tests are properly categorized and documented.

## License
Proprietary - Sleekflow Internal Use Only 