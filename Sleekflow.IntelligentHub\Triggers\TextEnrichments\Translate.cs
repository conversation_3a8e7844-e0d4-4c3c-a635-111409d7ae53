﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TextEnrichments;

[TriggerGroup(ControllerNames.TextEnrichments)]
public class Translate : ITrigger<Translate.TranslateInput, Translate.TranslateOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly ITextTranslationService _textTranslationService;

    public Translate(
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        ITextTranslationService textTranslationService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _textTranslationService = textTranslationService;
    }

    public class TranslateInput : IValidatableObject, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("message")]
        public string Message { get; set; }

        [Required]
        [JsonProperty("target_language_code")]
        public string TargetLanguageCode { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [Validations.ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
        [Validations.ValidateObject]
        public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public TranslateInput(
            string message,
            string targetLanguageCode,
            string sleekflowCompanyId,
            IntelligentHubUsageFilter? intelligentHubUsageFilter,
            AuditEntity.SleekflowStaff? createdBy)
        {
            Message = message;
            TargetLanguageCode = targetLanguageCode;
            SleekflowCompanyId = sleekflowCompanyId;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
            CreatedBy = createdBy;
        }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var validationResults = new List<ValidationResult>();

            if (!TranslationSupportedLanguages.AllSupportedLanguages.Contains(TargetLanguageCode))
            {
                validationResults.Add(
                    new ValidationResult(
                        "The LanguageIsoCode code is not supported.",
                        new List<string>
                        {
                            nameof(TargetLanguageCode)
                        }));
            }

            return validationResults;
        }
    }

    public class TranslateOutput
    {
        [JsonProperty("output_message")]
        public string OutputMessage { get; set; }

        [JsonConstructor]
        public TranslateOutput(string outputMessage)
        {
            OutputMessage = outputMessage;
        }
    }

    public async Task<TranslateOutput> F(TranslateInput translateInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(translateInput.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null || await _intelligentHubUsageService.IsUsageLimitExceeded(
            translateInput.SleekflowCompanyId,
            new Dictionary<string, int>
            {
                {
                    PriceableFeatures.AiFeaturesTotalUsage,
                    _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                        intelligentHubConfig,
                        PriceableFeatures.AiFeaturesTotalUsage)
                }
            },
            translateInput.IntelligentHubUsageFilter);

        if (isUsageLimitExceeded)
        {
            throw new SfUserFriendlyException("Cannot find IntelligentHubConfig or exceed max usage limit.");
        }

        var translateResult = await _textTranslationService.TranslateByLlmAsync(translateInput.TargetLanguageCode, translateInput.Message);

        await _intelligentHubUsageService.RecordUsageAsync(
            translateInput.SleekflowCompanyId,
            PriceableFeatures.Translate,
            translateInput.CreatedBy,
            new TranslateSnapshot(
                translateInput.Message,
                translateInput.TargetLanguageCode,
                translateResult));

        return new TranslateOutput(translateResult);
    }
}