using System.Web;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Integrator.Salesforce.Triggers.Public;
using Sleekflow.Integrator.Salesforce.UserMappingConfigs;
using Sleekflow.Mvc.Func;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Salesforce.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private readonly ILogger<PublicController> _logger;
    private readonly IFuncService _funcService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly IBus _bus;
    private readonly ApexCallback _apexCallback;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceUserMappingConfigService _salesforceUserMappingConfigService;

    public PublicController(
        ILogger<PublicController> logger,
        IFuncService funcService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        IBus bus,
        ApexCallback apexCallback,
        ISalesforceObjectService salesforceObjectService,
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceUserMappingConfigService salesforceUserMappingConfigService)
    {
        _logger = logger;
        _funcService = funcService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _bus = bus;
        _apexCallback = apexCallback;
        _salesforceObjectService = salesforceObjectService;
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceUserMappingConfigService = salesforceUserMappingConfigService;
    }

    [Route("healthz")]
    [HttpGet]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }

    [SwaggerQuery(
        new[]
        {
            "code",
            "state"
        })]
    [Route("AuthenticateCallback")]
    [HttpGet]
    public async Task<IActionResult> AuthenticateCallback()
    {
        var code = HttpContext.Request.Query["code"];
        var encryptedState = HttpContext.Request.Query["state"];

        var (authentication, isV2Authentication, returnToUrl, successUrl, failureUrl, isSandbox) =
            await _salesforceAuthenticationService.HandleAuthenticateCallbackAndStoreAsync(
                code!,
                encryptedState!);

        try
        {
            if (authentication == null)
            {
                throw new Exception("Unable to handle the authentication callback");
            }

            await _bus.Publish(
                new OnProviderInitializedEvent(
                    authentication.SleekflowCompanyId,
                    "salesforce-integrator"),
                context => { context.ConversationId = Guid.Parse(authentication.SleekflowCompanyId); });

            if (isV2Authentication)
            {
                var connection = await _salesforceConnectionService.GetByAuthenticationIdAsync(
                    authentication.SleekflowCompanyId,
                    authentication.Id);

                var organizationName = await _salesforceObjectService.GetOrganizationNameAsync(authentication);

                if (connection is null)
                {
                    connection = await _salesforceConnectionService.CreateAndGetAsync(
                        authentication.SleekflowCompanyId,
                        authentication.InstanceUrl.Replace("https://", string.Empty),
                        authentication.Id,
                        organizationName,
                        isSandbox is true ? "sandbox" : "production",
                        true);

                    await _salesforceUserMappingConfigService.CreateAndGetAsync(
                        authentication.SleekflowCompanyId,
                        connection.Id,
                        new List<UserMapping>());
                }
                else
                {
                    await _salesforceConnectionService.PatchAsync(
                        connection.Id,
                        authentication.SleekflowCompanyId,
                        organizationName,
                        true);
                }

                string encodedSuccessUrl;

                var successUri = new Uri(successUrl!);

                var existingQueryStrings = HttpUtility.ParseQueryString(successUri.Query);
                if (existingQueryStrings.Count == 0)
                {
                    encodedSuccessUrl = successUrl + $"?connection_id={HttpUtility.HtmlEncode(connection.Id)}";
                }
                else
                {
                    var queryStrings = existingQueryStrings;
                    queryStrings["connection_id"] = HttpUtility.HtmlEncode(connection.Id);

                    encodedSuccessUrl = successUri.GetLeftPart(UriPartial.Path) + "?" + queryStrings;
                }

                return new ContentResult
                {
                    ContentType = "text/html",
                    Content = $"<meta http-equiv=\"refresh\" content=\"0;URL='{encodedSuccessUrl}'\" />"
                };
            }

            return new ContentResult
            {
                ContentType = "text/html",
                Content =
                    $"Authenticated. You will be redirected back to our main app shortly. Thank you. <meta http-equiv=\"refresh\" content=\"5;URL='{returnToUrl}'\" />"
            };
        }
        catch (Exception exception)
        {
            var gen = RandomStringUtils.Gen(10);

            _logger.LogError(exception, "Caught an exception. requestId {RequestId}", gen);

            if (isV2Authentication)
            {
                return new ContentResult
                {
                    ContentType = "text/html",
                    Content = "<meta http-equiv=\"refresh\" content=\"0;URL='" + failureUrl + "'\" />"
                };
            }

            return new ContentResult
            {
                ContentType = "text/html",
                Content = $"Still Unauthenticated. Please contact our support team. Thank you. RequestId = {gen}"
            };
        }
    }

    [Route("ApexCallback")]
    [HttpPost]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<Output<ApexCallback.ApexCallbackOutput>>> ApexCallback(
        [FromBody]
        ApexCallback.ApexCallbackInput apexCallbackInput)
    {
        return await _funcService.Run1Async(
            (HttpContext.Request, apexCallbackInput),
            _apexCallback.F,
            HttpContext.Request.Path);
    }
}