﻿using System.ComponentModel.DataAnnotations;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events.InternalActionEvents;

public class CreateInternalWorkflowRequest : IHasSleekflowCompanyId, IHasMetadata, IValidatableObject
{
    public string SleekflowCompanyId { get; set; }

    public string WorkflowType { get; set; }

    public WorkflowTriggers Triggers { get; set; }

    public List<Step> Steps { get; set; }

    public string Name { get; set; }

    public Dictionary<string, object?> Metadata { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        if (WorkflowType == Constants.WorkflowType.Normal)
        {
            results.Add(new ValidationResult("Normal type workflow is not accepted here"));
        }

        return results;
    }

    public CreateInternalWorkflowRequest(
        string sleekflowCompanyId,
        string workflowType,
        WorkflowTriggers triggers,
        List<Step> steps,
        string name,
        Dictionary<string, object?> metadata)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowType = workflowType;
        Triggers = triggers;
        Steps = steps;
        Name = name;
        Metadata = metadata;
    }
}