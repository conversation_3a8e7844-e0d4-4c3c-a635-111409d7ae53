﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Documents.Chunks;

public class EditingChunkDto : IHasMetadata
{
    [JsonProperty("chunk_id")]
    public string ChunkId { get; set; }

    [JsonProperty("content")]
    public string Content { get; set; }

    [JsonProperty("categories")]
    public List<Category> Categories { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public EditingChunkDto(
        string chunkId,
        string content,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        ChunkId = chunkId;
        Content = content;
        Categories = categories;
        Metadata = metadata;
    }
}