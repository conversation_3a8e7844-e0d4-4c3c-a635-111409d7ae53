using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Wabas;

[TriggerGroup(ControllerNames.Wabas)]
public class GetConnectedWhatsappCloudApiWabas
    : ITrigger<
        GetConnectedWhatsappCloudApiWabas.GetConnectedWhatsappCloudApiWabasInput,
        GetConnectedWhatsappCloudApiWabas.GetConnectedWhatsappCloudApiWabasOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetConnectedWhatsappCloudApiWabas> _logger;

    public GetConnectedWhatsappCloudApiWabas(
        IWabaService wabaService,
        ILogger<GetConnectedWhatsappCloudApiWabas> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
    }

    public class GetConnectedWhatsappCloudApiWabasInput : IHasSleekflowStaff
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("should_refresh")]
        public bool ShouldRefresh { get; set; }

        [JsonProperty("user_access_token")]
        public string? UserAccessToken { get; set; }

        [JsonProperty("facebook_authorization_code")]
        public string? FacebookAuthorizationCode { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public GetConnectedWhatsappCloudApiWabasInput(
            string? sleekflowCompanyId,
            bool shouldRefresh,
            string? userAccessToken,
            string? facebookAuthorizationCode,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ShouldRefresh = shouldRefresh;
            UserAccessToken = userAccessToken;
            FacebookAuthorizationCode = facebookAuthorizationCode;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class GetConnectedWhatsappCloudApiWabasOutput
    {
        [JsonProperty("connected_wabas")]
        public List<WabaDto> ConnectedConnectedWabas { get; set; }

        [JsonProperty("business_integration_system_user_access_token")]
        public string? BusinessIntegrationSystemUserAccessToken { get; set; }

        [JsonConstructor]
        public GetConnectedWhatsappCloudApiWabasOutput(List<WabaDto> connectedWabas, string? businessIntegrationSystemUserAccessToken)
        {
            ConnectedConnectedWabas = connectedWabas;
            BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken;
        }
    }

    public async Task<GetConnectedWhatsappCloudApiWabasOutput> F(
        GetConnectedWhatsappCloudApiWabasInput input)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        List<Waba>? wabas;
        string? businessIntegrationSystemUserAccessToken = null;
        if (input.UserAccessToken is not null)
        {
            wabas = await _wabaService.GetConnectedWabaWithUserAccessTokenAsync(
                input.UserAccessToken);
        }
        else if (input.FacebookAuthorizationCode is not null)
        {
            (wabas, businessIntegrationSystemUserAccessToken) = await _wabaService.GetConnectedWabaWithBISUTokenFromFacebookAuthorizationCodeAsync(
                input.FacebookAuthorizationCode);
        }
        else if (input.SleekflowCompanyId is not null)
        {
            wabas = await _wabaService.GetConnectedWabaAsync(
                input.SleekflowCompanyId,
                input.ShouldRefresh,
                sleekflowStaff);
        }
        else
        {
            throw new SfUserFriendlyException("Missing required input value.");
        }

        return new GetConnectedWhatsappCloudApiWabasOutput(
            wabas.Select(
                w =>
                {
                    var dto = new WabaDto(w);

                    // Filtering out other company connected phone numbers
                    dto.WabaPhoneNumbers =
                        dto.WabaPhoneNumbers.Where(p =>
                            p.SleekflowCompanyId == input.SleekflowCompanyId || p.SleekflowCompanyId == null).ToList();

                    if (input.UserAccessToken is not null || input.FacebookAuthorizationCode is not null)
                    {
                        dto.SleekflowCompanyIds = w.SleekflowCompanyIds;
                    }

                    return dto;
                }).ToList(),
            businessIntegrationSystemUserAccessToken);
    }
}