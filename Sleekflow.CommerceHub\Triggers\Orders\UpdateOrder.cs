using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Orders;

[TriggerGroup(ControllerNames.Orders)]
public class UpdateOrder
    : ITrigger<
        UpdateOrder.UpdateOrderInput,
        UpdateOrder.UpdateOrderOutput>
{
    private readonly IOrderService _orderService;
    private readonly IProductVariantService _productVariantService;

    public UpdateOrder(
        IOrderService orderService,
        IProductVariantService productVariantService)
    {
        _orderService = orderService;
        _productVariantService = productVariantService;
    }

    public class UpdateOrderInput : OrderInput, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateOrderInput(
            string id,
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            UserProfile userProfile,
            string storeId,
            List<OrderLineItemInputDto> lineItems,
            Discount? discount,
            string countryIsoCode,
            string languageIsoCode,
            string currencyIsoCode,
            Dictionary<string, object?> metadata,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                sleekflowCompanyId,
                sleekflowUserProfileId,
                userProfile,
                storeId,
                lineItems,
                discount,
                countryIsoCode,
                languageIsoCode,
                currencyIsoCode,
                metadata)
        {
            Id = id;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateOrderOutput
    {
        [JsonProperty("order")]
        public OrderDto Order { get; set; }

        [JsonConstructor]
        public UpdateOrderOutput(OrderDto order)
        {
            Order = order;
        }
    }

    public async Task<UpdateOrderOutput> F(
        UpdateOrderInput updateOrderInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateOrderInput.SleekflowStaffId,
            updateOrderInput.SleekflowStaffTeamIds);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            updateOrderInput.LineItems.Select(x => x.ProductVariantId).ToList(),
            updateOrderInput.SleekflowCompanyId,
            updateOrderInput.StoreId);
        var productVariantIdToProductVariant = productVariants.ToDictionary(pv => pv.Id, pv => pv);

        var lineItems = updateOrderInput.LineItems
            .Select(
                li =>
                {
                    var productVariantSnapshot = productVariantIdToProductVariant[li.ProductVariantId];

                    return new OrderLineItem(
                        li.ProductVariantId,
                        li.ProductId,
                        null,
                        li.Quantity,
                        li.LineItemDiscount,
                        new Dictionary<string, object?>(),
                        productVariantSnapshot);
                })
            .ToList();

        var order = await _orderService.GetAsync(
            updateOrderInput.Id,
            updateOrderInput.SleekflowCompanyId);

        await _orderService.PatchAndGetOrderAsync(
            updateOrderInput.Id,
            updateOrderInput.SleekflowCompanyId,
            updateOrderInput.StoreId,
            updateOrderInput.SleekflowUserProfileId,
            lineItems,
            updateOrderInput.Discount,
            sleekflowStaff);

        // TODO Patch more attributes
        return new UpdateOrderOutput(new OrderDto(order));
    }
}