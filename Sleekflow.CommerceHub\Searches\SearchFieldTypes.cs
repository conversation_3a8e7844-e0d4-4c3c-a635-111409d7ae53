namespace Sleekflow.CommerceHub.Searches;

public static class SearchFieldTypes
{
    public const string String = "Edm.String";
    public const string Boolean = "Edm.Boolean";
    public const string Int32 = "Edm.Int32";
    public const string Int64 = "Edm.Int64";
    public const string Double = "Edm.Double";
    public const string DateTimeOffset = "Edm.DateTimeOffset";
    public const string GeographyPoint = "Edm.GeographyPoint";
    public const string ComplexType = "Edm.ComplexType";

    // Collection
    public const string CollectionString = "Collection(Edm.String)";
    public const string CollectionBoolean = "Collection(Edm.Boolean)";
    public const string CollectionInt32 = "Collection(Edm.Int32)";
    public const string CollectionInt64 = "Collection(Edm.Int64)";
    public const string CollectionDouble = "Collection(Edm.Double)";
    public const string CollectionDateTimeOffset = "Collection(Edm.DateTimeOffset)";
    public const string CollectionGeographyPoint = "Collection(Edm.GeographyPoint)";
    public const string CollectionComplexType = "Collection(Edm.ComplexType)";

    // Custom
    public const string CollectionPrice = "Collection(Sleekflow.Price)";
}