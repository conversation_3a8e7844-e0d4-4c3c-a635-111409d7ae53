using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface IFacebookMessageBodyCreator : IMessageBodyCreator
{
}

public class FacebookMessageBodyCreator : BaseMessageBodyCreator, IFacebookMessageBodyCreator
{
    public FacebookMessageBodyCreator()
        : base(ChannelTypes.Facebook)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                facebookMessengerMessage: new FacebookMessengerMessageObject(
                    new FacebookPageMessengerMessageObject(
                        null,
                        messageStr),
                    messagingType: "MESSAGE_TAG",
                    args.FacebookMessageParameters!.MessageTag)),
            "text"));
    }
}