using Newtonsoft.Json;

namespace Sleekflow.Infras.Components.Auth0s;

public class CustomDomainConfig
{
    [JsonProperty("is_enabled")]
    public bool IsEnabled { get; set; }

    [JsonProperty("domain")]
    public string Domain { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonConstructor]
    public CustomDomainConfig(bool isEnabled, string domain, string type)
    {
        IsEnabled = isEnabled;
        Domain = domain;
        Type = type;
    }
}