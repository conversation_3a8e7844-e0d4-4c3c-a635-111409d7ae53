using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Polly.Retry;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;

namespace Sleekflow;

public interface ICommonRetryPolicyService
{
    public AsyncRetryPolicy GetAsyncRetryPolicy(
        Func<Exception, bool>? exceptionPredicate = null,
        int totalRetryCount = 3,
        Func<int, Exception, Context, TimeSpan>? sleepDurationProvider = null);
}

public class CommonRetryPolicyService : ICommonRetryPolicyService, ISingletonService
{
    private readonly ILogger<CommonRetryPolicyService> _logger;

    public CommonRetryPolicyService(
        ILogger<CommonRetryPolicyService> logger)
    {
        _logger = logger;
    }

    public AsyncRetryPolicy GetAsyncRetryPolicy(
        Func<Exception, bool>? exceptionPredicate = null,
        int totalRetryCount = 3,
        Func<int, Exception, Context, TimeSpan>? sleepDurationProvider = null)
    {
        exceptionPredicate ??= _ => true;
        return Policy
            .Handle<Exception>(exceptionPredicate)
            .WaitAndRetryAsync(
                totalRetryCount,
                sleepDurationProvider: sleepDurationProvider ?? ((retryCount, exception, _) => TimeSpan.FromSeconds(1.37) * retryCount),
                onRetryAsync: (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogInformation(
                        "Exception during retry policy action {Exception} / {TimeSpan} / {RetryCount} / {Context}",
                        JsonConvert.SerializeObject(exception, JsonConfig.DefaultLoggingJsonSerializerSettings),
                        timeSpan,
                        retryCount,
                        JsonConvert.SerializeObject(context, JsonConfig.DefaultLoggingJsonSerializerSettings));

                    return Task.CompletedTask;
                });
    }
}