﻿using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Tests.Workflows.Enrollments;

public class WorkflowEnrollmentFilterTests
{
    private readonly IWorkflowEnrollmentFilter _workflowEnrollmentFilter = new WorkflowEnrollmentFilter();

    [Test]
    public void Filter_Given_WorkflowsWithDefaultEnrollmentSettings_Should_ReturnNonRunningWorkflowsOnly()
    {
        // Arrange
        var matchedWorkflows = new List<CompactWorkflow>()
        {
            CreateCompactWorkflow("match-1", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-2", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-3", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-4", WorkflowEnrollmentSettings.Default())
        };

        // Act
        var result = _workflowEnrollmentFilter.Filter(
            matchedWorkflows,
            runningWorkflowIds: new List<string>()
            {
                "match-3"
            },
            completeExecutionWorkflowIds: new List<string>());

        // Act
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnce, Is.Empty);
                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure, Is.Empty);
                Assert.That(result.WorkflowsIncludedDueToCanEnrollInParallel, Is.Empty);

                Assert.That(result.WorkflowsToEnroll, Has.Count.EqualTo(3));
                Assert.That(
                    result.WorkflowsToEnroll.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new[]
                        {
                            "match-1",
                            "match-2",
                            "match-4"
                        }));
            });
    }

    [Test]
    public void Filter_Given_WorkflowsCanEnrollOnlyOnce_Should_ReturnWorkflowsThatHasNotBeenEnrolledBefore()
    {
        // Arrange
        var matchedWorkflows = new List<CompactWorkflow>()
        {
            CreateCompactWorkflow("match-1", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-2", CanEnrollOnlyOnceSettings),
            CreateCompactWorkflow("match-3", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-4", CanEnrollOnlyOnceSettings),
            CreateCompactWorkflow("match-5", CanEnrollOnlyOnceSettings),
            CreateCompactWorkflow("match-6", WorkflowEnrollmentSettings.Default()),
        };

        // Act
        var result = _workflowEnrollmentFilter.Filter(
            matchedWorkflows,
            runningWorkflowIds: new List<string>()
            {
                "match-2"
            },
            completeExecutionWorkflowIds: new List<string>()
            {
                "match-4",
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure, Is.Empty);
                Assert.That(result.WorkflowsIncludedDueToCanEnrollInParallel, Is.Empty);

                Assert.That(result.WorkflowsToEnroll, Has.Count.EqualTo(4));
                Assert.That(
                    result.WorkflowsToEnroll.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new[]
                        {
                            "match-1",
                            "match-3",
                            "match-5",
                            "match-6"
                        }));


                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnce, Has.Count.EqualTo(1));
                Assert.That(
                    result.WorkflowsSkippedDueToCanEnrollOnlyOnce.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new[]
                        {
                            "match-4"
                        }));
            });
    }

    [Test]
    public void Filter_Given_WorkflowsCanEnrollAgainOnFailureOnly_Should_ReturnWorkflowsThatHasNotBeenEnrolledOrRanToCompletionBefore()
    {
        // Arrange
        var matchedWorkflows = new List<CompactWorkflow>()
        {
            CreateCompactWorkflow("match-1", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-2", CanEnrollAgainOnFailureOnlySettings),
            CreateCompactWorkflow("match-3", CanEnrollAgainOnFailureOnlySettings),
            CreateCompactWorkflow("match-4", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-5", CanEnrollAgainOnFailureOnlySettings),
            CreateCompactWorkflow("match-6", WorkflowEnrollmentSettings.Default()),
        };

        // Act
        var result = _workflowEnrollmentFilter.Filter(
            matchedWorkflows,
            runningWorkflowIds: new List<string>()
            {
                "match-5"
            },
            completeExecutionWorkflowIds: new List<string>()
            {
                "match-3"
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnce, Is.Empty);
                Assert.That(result.WorkflowsIncludedDueToCanEnrollInParallel, Is.Empty);

                Assert.That(result.WorkflowsToEnroll, Has.Count.EqualTo(4));
                Assert.That(
                    result.WorkflowsToEnroll.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-1",
                            "match-2",
                            "match-4",
                            "match-6"
                        }));

                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure, Has.Count.EqualTo(1));
                Assert.That(
                    result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-3"
                        }));
            });
    }

    [Test]
    public void Filter_Given_WorkflowsCanEnrollInParallel_Should_ReturnAllWorkflows()
    {
        // Arrange
        var matchedWorkflows = new List<CompactWorkflow>()
        {
            CreateCompactWorkflow("match-1", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-2", CanEnrollInParallelSettings),
            CreateCompactWorkflow("match-3", CanEnrollInParallelSettings),
            CreateCompactWorkflow("match-4", CanEnrollInParallelSettings),
            CreateCompactWorkflow("match-5", WorkflowEnrollmentSettings.Default()),
        };

        // Act
        var result = _workflowEnrollmentFilter.Filter(
            matchedWorkflows,
            runningWorkflowIds: new List<string>()
            {
                "match-2",
                "match-3"
            },
            completeExecutionWorkflowIds: new List<string>()
            {
                "match-4"
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnce, Is.Empty);
                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure, Is.Empty);

                Assert.That(result.WorkflowsToEnroll, Has.Count.EqualTo(5));
                Assert.That(
                    result.WorkflowsToEnroll.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-1",
                            "match-2",
                            "match-3",
                            "match-4",
                            "match-5"
                        }));

                Assert.That(result.WorkflowsIncludedDueToCanEnrollInParallel, Has.Count.EqualTo(2));
                Assert.That(
                    result.WorkflowsIncludedDueToCanEnrollInParallel.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-2",
                            "match-3"
                        }));
            });
    }

    [Test]
    public void Filter_Given_WorkflowOfDifferentSettings_Should_ReturnOnlyWorkflowsThatCanBeEnrolled()
    {
        // Arrange
        var matchedWorkflows = new List<CompactWorkflow>()
        {
            CreateCompactWorkflow("match-1", CanEnrollOnlyOnceSettings),
            CreateCompactWorkflow("match-2", CanEnrollAgainOnFailureOnlySettings),
            CreateCompactWorkflow("match-3", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-4", CanEnrollInParallelSettings),
            CreateCompactWorkflow("match-5", CanEnrollOnlyOnceSettings),
            CreateCompactWorkflow("match-6", CanEnrollInParallelSettings),
            CreateCompactWorkflow("match-7", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-8", CanEnrollAgainOnFailureOnlySettings),
            CreateCompactWorkflow("match-9", CanEnrollAgainOnFailureOnlySettings),
            CreateCompactWorkflow("match-10", WorkflowEnrollmentSettings.Default()),
            CreateCompactWorkflow("match-11", CanEnrollOnlyOnceSettings)
        };

        // Act
        var result = _workflowEnrollmentFilter.Filter(
            matchedWorkflows,
            new List<string>()
            {
                "match-1", // only once
                "match-3", // default
                "match-4", // parallel
                "match-8", // on failure only
            },
            new List<string>()
            {
                "match-5", // only once
                "match-7", // default
                "match-9", // on failure only
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);

                Assert.That(result.WorkflowsToEnroll, Has.Count.EqualTo(6));
                Assert.That(
                    result.WorkflowsToEnroll.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-2", // on failure only
                            "match-4", // parallel
                            "match-6", // parallel
                            "match-7", // default
                            "match-10", // default
                            "match-11" // only once
                        }));

                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnce, Has.Count.EqualTo(1));
                Assert.That(
                    result.WorkflowsSkippedDueToCanEnrollOnlyOnce.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-5"
                        }));

                Assert.That(result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure, Has.Count.EqualTo(1));
                Assert.That(
                    result.WorkflowsSkippedDueToCanEnrollOnlyOnFailure.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-9"
                        }));

                Assert.That(result.WorkflowsIncludedDueToCanEnrollInParallel, Has.Count.EqualTo(1));
                Assert.That(
                    result.WorkflowsIncludedDueToCanEnrollInParallel.Select(x => x.WorkflowId),
                    Is.EquivalentTo(
                        new List<string>()
                        {
                            "match-4"
                        }));
            });
    }

    private static CompactWorkflow CreateCompactWorkflow(
        string workflowId,
        WorkflowEnrollmentSettings workflowEnrollmentSettings)
    {
        var versionedId = Guid.NewGuid().ToString();

        return new CompactWorkflow(
            "mock-company-id",
            workflowId,
            versionedId,
            new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            workflowEnrollmentSettings,
            WorkflowScheduleSettings.Default(),
            DateTimeOffset.Now);
    }

    private static WorkflowEnrollmentSettings CanEnrollOnlyOnceSettings => new WorkflowEnrollmentSettings(
        canEnrollOnlyOnce: true,
        canEnrollAgainOnFailureOnly: false,
        canEnrollInParallel: false);

    private static WorkflowEnrollmentSettings CanEnrollAgainOnFailureOnlySettings => new WorkflowEnrollmentSettings(
        canEnrollOnlyOnce: false,
        canEnrollAgainOnFailureOnly: true,
        canEnrollInParallel: false);

    private static WorkflowEnrollmentSettings CanEnrollInParallelSettings => new WorkflowEnrollmentSettings(
        canEnrollOnlyOnce: false,
        canEnrollAgainOnFailureOnly: false,
        canEnrollInParallel: true);
}