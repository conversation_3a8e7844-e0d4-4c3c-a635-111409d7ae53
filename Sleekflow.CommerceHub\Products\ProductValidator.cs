using System.ComponentModel.DataAnnotations;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Products;

public interface IProductValidator
{
    Task AssertValidProductPropertiesAsync(
        string sleekflowCompanyId,
        string storeId,
        List<Multilingual> names,
        List<Description> descriptions,
        List<string> categoryIds,
        object? input = null);
}

public class ProductValidator : IProductValidator, IScopedService
{
    private readonly ICategoryRepository _categoryRepository;
    private readonly IStoreService _storeService;

    public ProductValidator(
        ICategoryRepository categoryRepository,
        IStoreService storeService)
    {
        _categoryRepository = categoryRepository;
        _storeService = storeService;
    }

    public async Task AssertValidProductPropertiesAsync(
        string sleekflowCompanyId,
        string storeId,
        List<Multilingual> names,
        List<Description> descriptions,
        List<string> categoryIds,
        object? input = null)
    {
        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);
        if (store is null)
        {
            throw new SfNotFoundObjectException(storeId, sleekflowCompanyId);
        }

        // TODO: Validate product properties
        var storeCategories = await _categoryRepository.GetCategoriesAsync(sleekflowCompanyId, store.Id);
        var storeCategoryIds = storeCategories.Select(c => c.Id).ToHashSet();

        if (categoryIds.Any(categoryId => !storeCategoryIds.Contains(categoryId)))
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult("The category is not found in the store")
                });
        }

        // if (names is null && descriptions is null && categoryIds is null)
        // {
        //     return;
        // }
        //
        // if (names is not null && !MultilingualUtils.AreValidMultilinguals(names, store))
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new (
        //             $"Unsupported language found in the name {JsonConvert.SerializeObject(names)}")
        //     });
        // }
        //
        // // TODO: Validate descriptions
        // if (descriptions is not null
        //     && !MultilingualUtils.AreValidMultilinguals(descriptions.Select(d => d.Text).ToList(), store))
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new (
        //             $"Unsupported language found in the description {JsonConvert.SerializeObject(descriptions)}")
        //     });
        // }

        // if (productVariants is null)
        // {
        //     return;
        // }
        //
        // if (!MultilingualUtils.AreValidMultilinguals(productVariants.SelectMany(v => v.Names).ToList(), store))
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new (
        //             $"Unsupported language found in product variants names {JsonConvert.SerializeObject(input)}")
        //     });
        // }
        //
        // if (!MultilingualUtils.AreValidMultilinguals(productVariants.SelectMany(v => v.Descriptions).ToList(), store))
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new (
        //             $"Unsupported language found in product variants description {JsonConvert.SerializeObject(input)}")
        //     });
        // }
        //
        // if (categories.Select(c => c.Id).Except(productVariants.SelectMany(v => v.CategoryIds)).Any())
        // {
        //     throw new SfNotFoundObjectException(
        //         $"Unable to locate category information in product variants {JsonConvert.SerializeObject(input)}");
        // }
    }
}