﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Workers;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Workers;

namespace Sleekflow.CrmHub.Workers;

public interface ICrmHubWorkerService
{
    Task<DurablePayload> StartLoopThroughAndEnrollSchemafulObjectsToFlowHubAsync(
        string sleekflowCompanyId,
        string schemaId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId);
}

public class CrmHubWorkerService : ICrmHubWorkerService, IScopedService
{
    private readonly IWorkerService _workerService;
    private readonly HttpClient _httpClient;
    private readonly IWorkerConfig _workerConfig;

    public CrmHubWorkerService(
        IWorkerService workerService,
        HttpClient httpClient,
        IWorkerConfig workerConfig)
    {
        _workerService = workerService;
        _httpClient = httpClient;
        _workerConfig = workerConfig;
    }

    public async Task<DurablePayload> StartLoopThroughAndEnrollSchemafulObjectsToFlowHubAsync(
        string sleekflowCompanyId,
        string schemaId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        var (_, _, output) = await _workerService.PostAsync<DurablePayload>(
            _httpClient,
            JsonConvert.SerializeObject(
                new LoopThroughAndEnrollSchemafulObjectsToFlowHubInput(
                    sleekflowCompanyId,
                    schemaId,
                    flowHubWorkflowId,
                    flowHubWorkflowVersionedId)),
            _workerConfig.WorkerHostname + "/api/LoopThroughAndEnrollSchemafulObjectsToFlowHub");

        return output.Data!;
    }
}