﻿# https://developer.salesforce.com/forums/?id=906F0000000ApQfIAK
# https://github.com/jamesward/salesforce-webhook-creator/blob/master/app/utils/ForceUtil.scala

### Select Apex Triggers

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+from+ApexTrigger+LIMIT+200 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Select Apex Classes

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+from+ApexClass+LIMIT+200 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Create Apex Class

POST {{instance_url}}/services/data/v54.0/sobjects/ApexClass HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1
Content-Type: application/json

{
  "NamespacePrefix": null,
  "Name": "Webhook",
  "Status": "Active",
  "IsValid": true,
  "Body": "public class Webhook implements HttpCalloutMock {\n\n    public static HttpRequest request;\n    public static HttpResponse response;\n\n    public HTTPResponse respond(HTTPRequest req) {\n        request = req;\n        response = new HttpResponse();\n        response.setStatusCode(200);\n        return response;\n    }\n\n    public static String jsonContent(List<Object> triggerNew, List<Object> triggerOld) {\n        String newObjects = '[]';\n        if (triggerNew != null) {\n            newObjects = JSON.serialize(triggerNew);\n        }\n\n        String oldObjects = '[]';\n        if (triggerOld != null) {\n            oldObjects = JSON.serialize(triggerOld);\n        }\n\n        String userId = JSON.serialize(UserInfo.getUserId());\n\n        String content = '{\"new\": ' + newObjects + ', \"old\": ' + oldObjects + ', \"userId\": ' + userId + '}';\n        return content;\n    }\n\n    @future(callout=true)\n    public static void callout(String url, String content) {\n\n        if (Test.isRunningTest()) {\n            Test.setMock(HttpCalloutMock.class, new Webhook());\n        }\n\n        Http h = new Http();\n\n        HttpRequest req = new HttpRequest();\n        req.setEndpoint(url);\n        req.setMethod('POST');\n        req.setHeader('Content-Type', 'application/json');\n        req.setBody(content);\n\n        h.send(req);\n    }\n\n}"
}

### Delete Apex Class

DELETE {{instance_url}}/services/data/v54.0/sobjects/ApexClass/01p5i000004h0VfAAI HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1
Content-Type: application/json

### Delete Apex Class

DELETE {{instance_url}}/services/data/v54.0/sobjects/ApexClass/01p5i000004h0VaAAI HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1
Content-Type: application/json

### Delete Apex Trigger

DELETE {{instance_url}}/services/data/v54.0/sobjects/ApexTrigger/01q5i000000wywyAAA HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1
Content-Type: application/json

### Metadata

POST {{instance_url}}/services/Soap/m/54.0/ HTTP/1.1
SOAPAction: RemoteSiteSetting
Content-Type: text/xml

<env:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <env:Header>
        <urn:SessionHeader xmlns:urn="http://soap.sforce.com/2006/04/metadata">
            <urn:sessionId>{{access_token}}</urn:sessionId>
        </urn:SessionHeader>
    </env:Header>
    <env:Body>
        <createMetadata xmlns="http://soap.sforce.com/2006/04/metadata">
            <metadata xsi:type="RemoteSiteSetting">
                <fullName>webhook_site</fullName>
                <isActive>true</isActive>
                <url>https://webhook.site/696a5fcb-3597-474e-8b62-50bb83adfcef</url>
            </metadata>
        </createMetadata>
    </env:Body>
</env:Envelope>
