﻿using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos;

/// <summary>
/// Data structure of a Vtex Order Hook.
/// <br/><br/>Documentation see https://developers.vtex.com/docs/guides/orders-feed#hook-notifications
/// </summary>
public record VtexOrderHookDto
{
    [JsonProperty("Domain")]
    public string Domain { get; }

    [JsonProperty("OrderId")]
    public string OrderId { get; }

    /// <summary>
    /// Candidates see <see cref="VtexOrderStatusCodes"/>
    /// </summary>
    [JsonProperty("State")]
    public string State { get; }

    [JsonProperty("LastState")]
    public string LastState { get; }

    [JsonProperty("LastChange")]
    public DateTimeOffset LastChange { get; }

    [JsonProperty("CurrentChange")]
    public DateTimeOffset CurrentChange { get; }

    [JsonProperty("Origin")]
    public OrderHookOriginDto Origin { get; }

    [JsonConstructor]
    public VtexOrderHookDto(string domain, string orderId, string state, string lastState, DateTimeOffset lastChange, DateTimeOffset currentChange, OrderHookOriginDto origin)
    {
        Domain = domain;
        OrderId = orderId;
        State = state;
        LastState = lastState;
        LastChange = lastChange;
        CurrentChange = currentChange;
        Origin = origin;
    }
}

public record OrderHookOriginDto
{
    [JsonProperty("Account")]
    public string Account { get; }

    [JsonProperty("Key")]
    public string Key { get; }

    [JsonConstructor]
    public OrderHookOriginDto(string account, string key)
    {
        Account = account;
        Key = key;
    }
}