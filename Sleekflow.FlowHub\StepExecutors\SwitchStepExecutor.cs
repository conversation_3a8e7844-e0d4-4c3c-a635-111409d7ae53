using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors;

public interface ISwitchStepExecutor : IStepExecutor
{
}

public class SwitchStepExecutor : ISwitchStepExecutor, IScopedService
{
    private readonly IWorkflowStepLocator _workflowStepLocator;
    private readonly IStateEvaluator _stateEvaluator;

    public SwitchStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IStateEvaluator stateEvaluator)
    {
        _workflowStepLocator = workflowStepLocator;
        _stateEvaluator = stateEvaluator;
    }

    public bool IsMatched(Step step)
    {
        return step is SwitchStep;
    }

    public async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        await onActivatedAsync(state, StepExecutionStatuses.Complete);
    }

    public async Task<Step?> OnStepCompleteAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries)
    {
        var switchStep = (SwitchStep) step;

        foreach (var switchStepSwitch in switchStep.SwitchCases)
        {
            var evaluatedCondition = await _stateEvaluator.EvaluateExpressionAsync(
                state,
                switchStepSwitch.Condition);
            if (evaluatedCondition is true or "true")
            {
                var nextStep = _workflowStepLocator.GetStep(workflow, switchStepSwitch.NextStepId);

                return nextStep;
            }
        }

        throw new SfFlowHubUserFriendlyException(
            UserFriendlyErrorCodes.UnmatchedCondition,
            "No matched condition");
    }
}