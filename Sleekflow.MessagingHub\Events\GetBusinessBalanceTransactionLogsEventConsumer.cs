using MassTransit;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Filters;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.Models.WhatsappCloudApi;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Events;

public class GetBusinessBalanceTransactionLogsEventConsumerDefinition
    : ConsumerDefinition<GetBusinessBalanceTransactionLogsEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetBusinessBalanceTransactionLogsEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetBusinessBalanceTransactionLogsEventConsumer
    : IConsumer<GetBusinessBalanceTransactionLogsRequest>
{
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;

    public GetBusinessBalanceTransactionLogsEventConsumer(
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public async Task Consume(ConsumeContext<GetBusinessBalanceTransactionLogsRequest> context)
    {
        var request = context.Message;
        var filter = new BusinessBalanceTransactionLogFilter(
                    request.TransactionType,
                    request.FacebookBusinessId,
                    null,
                    null,
                    IHasCreatedAt.PropertyNameCreatedAt,
                    "ASC");

        var (transactionLogs, continuationToken) = await _businessBalanceTransactionLogService
            .GetFilteredConversationUsageTransactionLogsAsync(filter, 1000, request.ContinuationToken);
        var formattedLogs = transactionLogs
                    .Where(x => x.WabaTopUp != null)
                    .Select(
                        x => new BusinessBalanceTransactionLog(
                            x.UniqueId,
                            x.FacebookBusinessId,
                            new Money(x.Credit.CurrencyIsoCode, x.Credit.Amount),
                            x.TransactionType,
                            new WabaTopUpTransactionItem(
                                new Money(x.WabaTopUp!.PayAmount.CurrencyIsoCode, x.WabaTopUp.PayAmount.Amount),
                                x.WabaTopUp.PaymentMethod),
                            x.CreatedAt,
                            x.UpdatedAt)).ToList();

        await context.RespondAsync(
            new GetBusinessBalanceTransactionLogsReply(formattedLogs, continuationToken));
    }
}