using Newtonsoft.Json;

namespace Sleekflow.Models.Blobs;

public class PublicImage : PublicBlob
{
    [JsonProperty("width")]
    public int Width { get; set; }

    [JsonProperty("height")]
    public int Height { get; set; }

    [JsonConstructor]
    public PublicImage(
        string? containerName,
        string? blobName,
        string? blobId,
        string url,
        DateTimeOffset expiresOn,
        string contentType,
        int width,
        int height)
        : base(containerName, blobName, blobId, url, expiresOn, contentType)
    {
        Width = width;
        Height = height;
    }
}