using System.Globalization;
using System.Text;
using ShopifySharp;
using ShopifySharp.Filters;
using Sleekflow.CommerceHub.Models.Shopify;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Shopify;

public interface IShopifyClientService
{
    Task<Customer> GetOrCreateShopifyCustomerAsync(
        string shopifyUrl,
        string shopAccessToken,
        string phone,
        string email);

    Task<DraftOrder> CreateDraftOrderAsync(
        string shopifyUrl,
        string shopAccessToken,
        string customerPhone,
        string customerEmail,
        string? orderNote,
        string? orderTags,
        List<ShopifyDraftOrderLineItem> lineItems);
}

public class ShopifyClientService : IShopifyClientService, IScopedService
{
    public async Task<Customer> GetOrCreateShopifyCustomerAsync(
        string shopifyUrl,
        string shopAccessToken,
        string phone,
        string email)
    {
        await ShopifyClientValidator.AssertValidShopifyCredentialAsync(shopifyUrl, shopAccessToken);

        var customerService = new CustomerService(shopifyUrl, shopAccessToken);

        var customers = await customerService.SearchAsync(
            new CustomerSearchListFilter
            {
                Query = GetShopifyFieldPhraseSearchOrQuery(new List<(string Name, string Value)>
                {
                    ("phone", phone),
                    ("email", email)
                })
            });

        if (customers?.Items?.Any() is true)
        {
            return customers.Items.First();
        }

        return await customerService.CreateAsync(
            new Customer
            {
                Email = email,
                Phone = phone
            });
    }

    public async Task<DraftOrder> CreateDraftOrderAsync(
        string shopifyUrl,
        string shopAccessToken,
        string customerPhone,
        string customerEmail,
        string? orderNote,
        string? orderTags,
        List<ShopifyDraftOrderLineItem> lineItems)
    {
        await ShopifyClientValidator.AssertValidShopifyCredentialAsync(shopifyUrl, shopAccessToken);

        var draftLineItems = new List<DraftLineItem>();

        foreach (var orderLineItem in lineItems)
        {
            var draftLineItem = new DraftLineItem
            {
                VariantId = orderLineItem.ShopifyProductVariantId,
                Quantity = orderLineItem.Quantity
            };

            if (orderLineItem.Discount is not null)
            {
                draftLineItem.AppliedDiscount = new AppliedDiscount()
                {
                    Title = orderLineItem.Discount.Title,
                    Value = orderLineItem.Discount.Value.ToString(CultureInfo.InvariantCulture),
                    ValueType = orderLineItem.Discount.Type,
                    Amount = orderLineItem.Discount.MaxAmount
                };
            }

            draftLineItems.Add(draftLineItem);
        }

        var draftOrderService = new DraftOrderService(
            shopifyUrl,
            shopAccessToken);

        var draftOrder = new DraftOrder
        {
            Email = customerEmail,
            LineItems = draftLineItems
        };

        if (orderNote is not null)
        {
            draftOrder.Note = orderNote;
        }

        if (orderTags is not null)
        {
            draftOrder.Tags = orderTags;
        }

        draftOrder.Customer = await GetOrCreateShopifyCustomerAsync(
            shopifyUrl,
            shopAccessToken,
            customerPhone[0] == '+' ? customerPhone : "+" + customerPhone,
            customerEmail);

        return await draftOrderService.CreateAsync(draftOrder);
    }

    private static string GetShopifyFieldPhraseSearchOrQuery(
        List<(string Name, string Value)> filters)
    {
        var queryBuilder = new StringBuilder();

        foreach (var filter in filters)
        {
            if (string.IsNullOrEmpty(filter.Value))
            {
                continue;
            }

            if (queryBuilder.Length > 0)
            {
                queryBuilder.Append(" OR ");
            }

            queryBuilder.Append($"{filter.Name}:\"{filter.Value}\"");
        }

        return queryBuilder.ToString();
    }
}