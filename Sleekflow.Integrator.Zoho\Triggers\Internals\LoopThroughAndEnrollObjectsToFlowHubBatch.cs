﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Triggers.Internals;

[TriggerGroup("Internals")]
public class LoopThroughAndEnrollObjectsToFlowHubBatch : ITrigger
{
    private readonly ILogger<LoopThroughAndEnrollObjectsToFlowHubBatch> _logger;
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IBus _bus;

    public LoopThroughAndEnrollObjectsToFlowHubBatch(
        ILogger<LoopThroughAndEnrollObjectsToFlowHubBatch> logger,
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService,
        IBus bus)
    {
        _logger = logger;
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
        _bus = bus;
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("next_page_size")]
        public int? NextPageSize { get; set; }

        [JsonProperty("next_page")]
        public int? NextPage { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            int? nextPageSize,
            int? nextPage,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            NextPageSize = nextPageSize;
            NextPage = nextPage;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_page_size")]
        public int? NextPageSize { get; }

        [JsonProperty("next_page")]
        public int? NextPage { get; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchOutput(
            long count,
            int? nextPageSize,
            int? nextPage)
        {
            Count = count;
            NextPageSize = nextPageSize;
            NextPage = nextPage;
        }
    }

    public async Task<LoopThroughAndEnrollObjectsToFlowHubBatchOutput> F(
        LoopThroughAndEnrollObjectsToFlowHubBatchInput loopThroughAndEnrollObjectsToFlowHubBatchInput)
    {
        var sleekflowCompanyId = loopThroughAndEnrollObjectsToFlowHubBatchInput.SleekflowCompanyId;
        var connectionId = loopThroughAndEnrollObjectsToFlowHubBatchInput.ConnectionId;
        var entityTypeName = loopThroughAndEnrollObjectsToFlowHubBatchInput.EntityTypeName;
        var flowHubWorkflowId = loopThroughAndEnrollObjectsToFlowHubBatchInput.FlowHubWorkflowId;
        var flowHubWorkflowVersionedId = loopThroughAndEnrollObjectsToFlowHubBatchInput.FlowHubWorkflowVersionedId;

        var connection = await _zohoConnectionService.GetByIdAsync(connectionId, sleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(connection.AuthenticationId, sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Start LoopThroughAndEnrollObjectsToFlowHubBatch: " +
            "sleekflowCompanyId {SleekflowCompanyId}, connectionId {ConnectionId} entityTypeName {EntityTypeName}, nextPageSize {NextPageSize}, nextPage {NextPage}",
            sleekflowCompanyId,
            connectionId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPageSize,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPage);

        var (objects, nextPageSize, nextPage) = await _zohoObjectService.GetObjectsAsync(
            authentication,
            entityTypeName,
            new List<SyncConfigFilterGroup>(),
            null,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPageSize,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPage);

        _logger.LogInformation(
            "End LoopThroughAndEnrollObjectsToFlowHubBatch: sleekflowCompanyId {SleekflowCompanyId}, " +
            "entityTypeName {EntityTypeName}, " +
            "prevNextPageSize {PrevNextPageSize}, " +
            "prevNextPage {PrevNextPage}, " +
            "nextPageSize {NextPageSize}, " +
            "nextPage {NextPage}, " +
            "count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPageSize,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPage,
            nextPageSize,
            nextPage,
            objects.Count);

        var events = objects
            .Select(
                dict =>
                {
                    var onZohoObjectEnrollmentToFlowHubRequestedEvent = new OnZohoObjectEnrollmentToFlowHubRequestedEvent(
                        DateTimeOffset.UtcNow,
                        sleekflowCompanyId,
                        connectionId,
                        (string) dict["Id"]!,
                        entityTypeName,
                        dict,
                        flowHubWorkflowId,
                        flowHubWorkflowVersionedId);

                    return onZohoObjectEnrollmentToFlowHubRequestedEvent;
                })
            .ToList();

        var eventChunks = events.Chunk(100).ToList();
        var totalChunks = eventChunks.Count;

        for (var i = 0; i < totalChunks; i++)
        {
            await _bus.PublishBatch(
                eventChunks[i],
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });

            if (i < totalChunks - 1)
            {
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
        }

        _logger.LogInformation(
            "Flushed LoopThroughAndEnrollObjectsToFlowHubBatch: sleekflowCompanyId {SleekflowCompanyId}, " +
            "entityTypeName {EntityTypeName}, " +
            "prevNextPageSize {PrevNextPageSize}, " +
            "prevNextPage {PrevNextPage}, " +
            "nextPageSize {NextPageSize}, " +
            "nextPage {NextPage}, " +
            "count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPageSize,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextPage,
            nextPageSize,
            nextPage,
            objects.Count);

        return new LoopThroughAndEnrollObjectsToFlowHubBatchOutput(objects.Count, nextPageSize, nextPage);
    }
}