﻿using System.Net;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CommerceHub;

namespace Sleekflow.CommerceHub.Vtex.Helpers;

public interface IVtexOrderHookRegister
{
    Task RegisterAsync(
        VtexCredential credential,
        string vtexAuthenticationId,
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default);

    Task<bool> ValidateRegistrationAsync(
        VtexCredential credential,
        CancellationToken cancellationToken = default);

    Task RemoveRegistrationAsync(
        VtexCredential credential,
        CancellationToken cancellationToken = default);
}

public class VtexOrderHookRegister : IVtexOrderHookRegister, IScopedService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<VtexOrderHookRegister> _logger;
    private readonly IAppConfig _appConfig;

    public VtexOrderHookRegister(HttpClient httpClient, ILogger<VtexOrderHookRegister> logger, IAppConfig appConfig)
    {
        _httpClient = httpClient;
        _logger = logger;
        _appConfig = appConfig;
    }

    #region private DTO definitions

    [method: JsonConstructor]
    public record Filter(
        List<string> Status,
        string Type)
    {
        [JsonProperty("status")]
        public List<string> Status { get; set; } = Status;

        [JsonProperty("type")]
        public string Type { get; set; } = Type;
    }

    [method: JsonConstructor]
    public record Headers(
        string VtexAuthenticationId,
        string SleekflowCompanyId)
    {
        [JsonProperty(VtexHttpHeaderNames.SfVtexAuthenticationId)]
        public string VtexAuthenticationId { get; set; } = VtexAuthenticationId;

        [JsonProperty(VtexHttpHeaderNames.SfCompanyId)]
        public string SleekflowCompanyId { get; set; } = SleekflowCompanyId;
    }

    [method: JsonConstructor]
    public record Hook(
        string Url,
        Headers Headers)
    {
        [JsonProperty("url")]
        public string Url { get; set; } = Url;

        [JsonProperty("headers")]
        public Headers Headers { get; set; } = Headers;
    }

    [method: JsonConstructor]
    private sealed record OrderHookRegistrationRequestBody(
        Filter Filter,
        Hook Hook)
    {
        [JsonProperty("filter")]
        public Filter Filter { get; set; } = Filter;

        [JsonProperty("hook")]
        public Hook Hook { get; set; } = Hook;
    }

    #endregion

    public async Task RegisterAsync(
        VtexCredential credential,
        string vtexAuthenticationId,
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default)
    {
        var webhookUrl = $"{_appConfig.CommerceHubEndpoint}/VtexWebhook/order";

        // build http request
        var request = new HttpRequestMessage(
            HttpMethod.Post,
            $"{credential.Domain}/api/orders/hook/config");

        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppKey, credential.AppKey);
        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppToken, credential.AppToken);

        // build request body
        var requestBody = new OrderHookRegistrationRequestBody(
            new Filter(
                VtexOrderStatusCodes.Captured.ToList(),
                "FromWorkflow"),
            new Hook(
                webhookUrl,
                new Headers(
                    vtexAuthenticationId,
                    sleekflowCompanyId)));

        request.Content = new StringContent(
            JsonConvert.SerializeObject(requestBody),
            Encoding.UTF8,
            "application/json");

        var response = await _httpClient.SendAsync(request, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            return;
        }

        if (response.StatusCode == HttpStatusCode.Unauthorized)
        {
            throw new SfVtexInvalidCredentialException();
        }
        else
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            _logger.LogError(
                "Failed to register VTEX order hook with status code: {StatusCode}, content: {Content}. {VtexCredential} {SleekflowCompanyId}",
                response.StatusCode,
                content,
                credential,
                sleekflowCompanyId);

            throw new SfVtexHttpRequestException(
                request,
                $"Failed to register VTEX order hook: {content}");
        }
    }

    public async Task<bool> ValidateRegistrationAsync(VtexCredential credential, CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new HttpRequestMessage(
                HttpMethod.Get,
                $"{credential.Domain}/api/orders/hook/config");

            request.Headers.Add(VtexHttpHeaderNames.VtexApiAppKey, credential.AppKey);
            request.Headers.Add(VtexHttpHeaderNames.VtexApiAppToken, credential.AppToken);

            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                return false;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var registration = JsonConvert.DeserializeObject<OrderHookRegistrationRequestBody>(content);

            return registration != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error when validate VTEX order hook registration. {VtexCredential}",
                credential);

            return false;
        }
    }

    public async Task RemoveRegistrationAsync(VtexCredential credential, CancellationToken cancellationToken = default)
    {
        var request = new HttpRequestMessage(
            HttpMethod.Delete,
            $"{credential.Domain}/api/orders/hook/config");

        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppKey, credential.AppKey);
        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppToken, credential.AppToken);

        var response = await _httpClient.SendAsync(request, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            _logger.LogInformation(
                "Removed VTEX order hook. {VtexCredential}",
                credential);

            return;
        }

        if (response.StatusCode == HttpStatusCode.Unauthorized)
        {
            throw new SfVtexInvalidCredentialException();
        }
        else
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            _logger.LogError(
                "Failed to remove VTEX order hook with status code: {StatusCode}, content: {Content}. {VtexCredential}",
                response.StatusCode,
                content,
                credential);

            throw new SfVtexHttpRequestException(
                request,
                $"Failed to remove VTEX order hook: {content}");
        }
    }
}