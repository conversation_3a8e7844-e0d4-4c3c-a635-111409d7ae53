using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows.Settings;

public class CustomObjectDateTimeSettings : BaseDateTimeSettings
{
    [JsonConstructor]
    public CustomObjectDateTimeSettings(
        string? schemaId,
        string? schemafulObjectPropertyId,
        string? triggerDateType,
        string[] triggerDateDuration,
        string? triggerTimeType,
        string? triggerCustomTime)
        : base(triggerDateType, triggerDateDuration, triggerTimeType, triggerCustomTime)
    {
        SchemaId = schemaId;
        SchemafulObjectPropertyId = schemafulObjectPropertyId;
    }

    [JsonProperty("schema_id")]
    public string? SchemaId { get; set; }

    [JsonProperty("schemaful_object_property_id")]
    public string? SchemafulObjectPropertyId { get; set; }
}