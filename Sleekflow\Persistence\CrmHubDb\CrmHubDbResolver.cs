using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.CrmHubDb;

public interface ICrmHubDbResolver : IContainerResolver
{
}

public class CrmHubDbResolver : ICrmHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public CrmHubDbResolver(ICrmHubDbConfig crmHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            crmHubDbConfig.Endpoint,
            crmHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}