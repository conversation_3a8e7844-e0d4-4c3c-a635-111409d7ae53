﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReInitConnection : ITrigger
{
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;

    public ReInitConnection(
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsConnectionService googleSheetsConnectionService)
    {
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
    }

    public class ReInitConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        [ValidateObject]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public ReInitConnectionInput(
            string sleekflowCompanyId,
            string connectionId,
            string successUrl,
            string failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class ReInitConnectionOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("is_re_authentication_required")]
        public bool IsReAuthenticationRequired { get; set; }

        [JsonProperty("google_sheets_authentication_url")]
        public string? GoogleSheetsAuthenticationUrl { get; set; }

        [JsonConstructor]
        public ReInitConnectionOutput(
            string providerName,
            bool isReAuthenticationRequired,
            string? googleSheetsAuthenticationUrl)
        {
            ProviderName = providerName;
            IsReAuthenticationRequired = isReAuthenticationRequired;
            GoogleSheetsAuthenticationUrl = googleSheetsAuthenticationUrl;
        }
    }

    public async Task<ReInitConnectionOutput> F(
        ReInitConnectionInput reInitConnectionInput)
    {
        var connection = await _googleSheetsConnectionService.GetByIdAsync(
            reInitConnectionInput.ConnectionId,
            reInitConnectionInput.SleekflowCompanyId);
        if (connection.IsActive)
        {
            return new ReInitConnectionOutput(
                "google-sheets-integrator",
                false,
                null);
        }

        try
        {
            await _googleSheetsAuthenticationService.ReAuthenticateAndStoreAsync(
                connection.AuthenticationId,
                connection.SleekflowCompanyId);

            await _googleSheetsConnectionService.PatchAsync(
                connection.Id,
                connection.SleekflowCompanyId,
                connection.Name,
                true);

            return new ReInitConnectionOutput(
                "google-sheets-integrator",
                false,
                null);
        }
        catch (Exception)
        {
            return new ReInitConnectionOutput(
                "google-sheets-integrator",
                true,
                _googleSheetsAuthenticationService.GetAuthenticationUrl(
                    reInitConnectionInput.SleekflowCompanyId,
                    reInitConnectionInput.SuccessUrl,
                    reInitConnectionInput.FailureUrl,
                    reInitConnectionInput.AdditionalDetails));
        }
    }
}