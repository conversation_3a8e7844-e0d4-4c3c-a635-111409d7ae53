using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetWorkflowExecutions : ITrigger
{
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public GetWorkflowExecutions(
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
    }

    public class GetWorkflowExecutionsInput : IHasSleekflowCompanyId
    {
        [Required]
        [Json<PERSON>roperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty("limit")]
        [Range(1, 1000)]
        public int Limit { get; set; }

        [Required]
        [JsonProperty("filters")]
        [ValidateObject]
        public GetWorkflowExecutionsInputFilters Filters { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionsInput(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            GetWorkflowExecutionsInputFilters filters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            Limit = limit;
            Filters = filters;
        }
    }

    public class GetWorkflowExecutionsInputFilters : WorkflowExecutionFilters
    {
        public GetWorkflowExecutionsInputFilters(
            string? workflowId,
            string? workflowExecutionStatus,
            DateTimeOffset? fromDateTime,
            DateTimeOffset? toDateTime)
            : base(workflowId, workflowExecutionStatus, fromDateTime, toDateTime)
        {
        }
    }

    public class GetWorkflowExecutionsOutput
    {
        [JsonProperty("workflow_executions")]
        public List<WorkflowExecutionListDto> WorkflowExecutions { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionsOutput(
            List<WorkflowExecutionListDto> workflowExecutions,
            string? nextContinuationToken)
        {
            WorkflowExecutions = workflowExecutions;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetWorkflowExecutionsOutput> F(GetWorkflowExecutionsInput getWorkflowExecutionsInput)
    {
        var (workflowExecutions, nextContinuationToken) = await _workflowExecutionService.GetWorkflowExecutionsAsync(
            getWorkflowExecutionsInput.SleekflowCompanyId,
            getWorkflowExecutionsInput.ContinuationToken,
            getWorkflowExecutionsInput.Limit,
            getWorkflowExecutionsInput.Filters);

        return new GetWorkflowExecutionsOutput(
            workflowExecutions.Select(we => new WorkflowExecutionListDto(we)).ToList(),
            nextContinuationToken);
    }
}