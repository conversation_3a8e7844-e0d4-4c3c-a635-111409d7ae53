#if SWAGGERGEN
using Microsoft.OpenApi.Models;
using Sleekflow.DistributedInvocations;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Sleekflow.Mvc.SwaggerConfiguration.OperationFilters;

public class DistributedInvocationContextHeaderOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters ??= new List<OpenApiParameter>();
        operation.Parameters.Add(
            new OpenApiParameter
        {
            Name = IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
            In = ParameterLocation.Header,
            Required = false,
            Schema = new OpenApiSchema
            {
                Type = "string"
            }
        });
    }
}
#endif