﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowGroups;
using Sleekflow.FlowHub.WorkflowGroups;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.WorkflowGroups;

[TriggerGroup(ControllerNames.WorkflowGroups)]
public class GetWorkflowGroups
    : ITrigger<
        GetWorkflowGroups.GetWorkflowGroupsInput,
        GetWorkflowGroups.GetWorkflowGroupsOutput>
{
    private readonly IWorkflowGroupService _workflowGroupService;

    public GetWorkflowGroups(IWorkflowGroupService  workflowGroupService)
    {
        _workflowGroupService = workflowGroupService;
    }

    public class GetWorkflowGroupsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWorkflowGroupsInput(
            string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWorkflowGroupsOutput
    {
        [JsonProperty("workflow_groups")]
        public List<WorkflowGroupDto> WorkflowGroups { get; set; }

        [JsonConstructor]
        public GetWorkflowGroupsOutput(
            List<WorkflowGroupDto> workflowGroups)
        {
            WorkflowGroups = workflowGroups;
        }
    }

    public async Task<GetWorkflowGroupsOutput> F(GetWorkflowGroupsInput input)
    {
        var workflowGroups = await _workflowGroupService.GetWorkflowGroupsAsync(
            input.SleekflowCompanyId);

        return new GetWorkflowGroupsOutput(
            workflowGroups
                .Select(group => new WorkflowGroupDto(group))
                .ToList());
    }
}