using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.Links;

public class GetUrlQrCodeRequest
{
    [JsonProperty("url")]
    [Required]
    public string Url { get; set; }

    [JsonProperty("qr_code_config")]
    [Required]
    public QrCodeConfig QrCodeConfig { get; set; }

    [JsonConstructor]
    public GetUrlQrCodeRequest(
        string url,
        QrCodeConfig qrCodeConfig)
    {
        Url = url;
        QrCodeConfig = qrCodeConfig;
    }
}

public class QrCodeConfig
{
    [JsonProperty("size")]
    public int Size { get; set; }

    [JsonConstructor]
    public QrCodeConfig(
        int size)
    {
        Size = size;
    }
}