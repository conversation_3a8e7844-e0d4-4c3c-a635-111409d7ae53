﻿namespace Sleekflow.Exceptions.FlowHub;

public class SfWorkflowStateNotFoundException : ErrorCodeException
{
    public string? StateId { get; }

    public string? SleekflowCompanyId { get; }

    public SfWorkflowStateNotFoundException(
        string? sleekflowCompanyId,
        string? stateId)
        : base(
            ErrorCodeConstant.SfWorkflowStateNotFoundException,
            $"State {stateId} not found in company {sleekflowCompanyId}")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
    }
}