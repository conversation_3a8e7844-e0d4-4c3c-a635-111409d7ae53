using System.ComponentModel;
using MassTransit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Steps.Common;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.IntelligentHub.FaqAgents.Chats;

namespace Sleekflow.IntelligentHub.Plugins;

using Microsoft.SemanticKernel;

public class ContactProperty
{
    public string PropertyId { get; set; } = string.Empty;

    public string PropertyValue { get; set; } = string.Empty;
}

public interface ISleekflowToolsPlugin
{
    [KernelFunction("add_lead_score_custom_object")]
    [Description("Adds the lead score custom object to the lead.")]
    Task<string> AddLeadScoreCustomObject(
        Kernel kernel,
        [Description("The classification of the lead type.")]
        string classification,
        [Description("The reasong for the classification and the lead score.")]
        string reasoning,
        [Description("The latest lead score of the lead.")]
        string score);

    [KernelFunction("assign_to_team")]
    [Description("Assigns the lead to the specified team.")]
    Task<string> AssignToTeam(
        Kernel kernel,
        [Description("The name of the team to which the lead will be assigned.")]
        string teamName,
        [Description("The reason for triggering assignment.")]
        string assignReason);

    [KernelFunction("add_hands_off_custom_object")]
    [Description("Adds the hands off custom object to the lead.")]
    Task<string> AddHandsOffCustomObject(
        Kernel kernel,
        [Description("The reason for triggering assignment.")]
        string assignmentReason,
        [Description("The latest lead score of the lead.")]
        int leadScore,
        [Description("A comprehensive conversation summary between the lead and our company.")]
        string shortSummary);

    [KernelFunction("update_contact_properties")]
    [Description("Updates the contact properties with the specified values.")]
    Task<string> UpdateContactProperties(
        Kernel kernel,
        [Description("List of contact properties to update, each with PropertyId and PropertyValue.")]
        List<ContactProperty> properties);

    [KernelFunction("add_contact_labels")]
    [Description("Adds labels to a contact without removing existing labels.")]
    Task<string> AddContactLabels(
        Kernel kernel,
        [Description("The list of labels to add to the contact.")]
        List<string> labels);

    [KernelFunction("remove_contact_labels")]
    [Description("Removes specific labels from a contact.")]
    Task<string> RemoveContactLabels(
        Kernel kernel,
        [Description("The list of labels to remove from the contact.")]
        List<string> labels);

    [KernelFunction("set_contact_labels")]
    [Description("Removes all existing labels and sets the contact labels to the provided list. Use an empty list to remove all labels.")]
    Task<string> SetContactLabels(
        Kernel kernel,
        [Description("The list of labels to set for the contact, replacing all existing labels.")]
        List<string> labels);

    [KernelFunction("send_message")]
    [Description("Sends a message asynchronously to a recipient.")]
    Task<string> SendMessage(
        Kernel kernel,
        [Description("The message to send.")]
        string message);

    [KernelFunction("add_internal_note")]
    [Description("Adds an internal note to a contact.")]
    Task<string> AddInternalNote(
        Kernel kernel,
        [Description("The content of the internal note to add to the contact.")]
        string content);
}

public class SleekflowToolsPlugin : ISleekflowToolsPlugin, IScopedService
{
    private readonly ILogger<SleekflowToolsPlugin> _logger;
    private readonly IBus _bus;
    private readonly IUserInteractionTrackingService _userInteractionTracker;

    public SleekflowToolsPlugin(
        ILogger<SleekflowToolsPlugin> logger,
        IBus bus,
        IUserInteractionTrackingService userInteractionTracker)
    {
        _logger = logger;
        _bus = bus;
        _userInteractionTracker = userInteractionTracker;
    }

    [KernelFunction("add_lead_score_custom_object")]
    [Description("Adds the lead score custom object to the lead.")]
    public async Task<string> AddLeadScoreCustomObject(
        Kernel kernel,
        [Description("The classification of the lead type.")]
        string classification,
        [Description("The reasong for the classification and the lead score.")]
        string reasoning,
        [Description("The latest lead score of the lead.")]
        string score)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;
        var tools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;
        var customObjectLeadScoreTool = tools?.LeadScoreCustomObjectTool;

        if (stateId == null || contactId == null || tools == null || customObjectLeadScoreTool == null)
        {
            Console.WriteLine("Simulating the lead score custom object creation...");
            Console.WriteLine($"Action - Added Lead Score Custom Object to Lead ({contactId})");
            Console.WriteLine($"Classification - {classification}");
            Console.WriteLine($"Reasoning - {reasoning}");
            Console.WriteLine($"Score - {score}");
            Console.WriteLine();

            _logger.LogInformation("Simulating the lead score custom object creation...");
            _logger.LogInformation(
                "Action - Added Lead Score Custom Object to Lead ({ContactId})",
                contactId);
            _logger.LogInformation("Classification - {Classification}", classification);
            _logger.LogInformation("Reasoning - {Reasoning}", reasoning);
            _logger.LogInformation("Score - {Score}", score);

            return "Added Lead Score Custom Object";
        }

        var id = $"cn_{CreateSchemafulObjectStepArgs.CallName}_id_{Guid.NewGuid()}";
        var name = "Add Lead Score Custom Object";

        var callStep = new CallStep<CreateSchemafulObjectStepArgs>(
            id,
            name,
            null,
            null,
            CreateSchemafulObjectStepArgs.CallName,
            new CreateSchemafulObjectStepArgs(
                customObjectLeadScoreTool.SchemaId,
                null,
                new Dictionary<string, string?>
                {
                    {
                        customObjectLeadScoreTool.ClassificationFieldId, classification
                    },
                    {
                        customObjectLeadScoreTool.ReasoningFieldId, reasoning
                    },
                    {
                        customObjectLeadScoreTool.ScoreFieldId, score
                    },
                },
                contactId));

        await _bus.Publish(
            new OnExternalStepSubmittedEvent(
                stateId,
                JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

        return "Added Lead Score Custom Object";
    }

    [KernelFunction("assign_to_team")]
    [Description("Assigns the lead to the specified team.")]
    public async Task<string> AssignToTeam(
        Kernel kernel,
        [Description("The name of the team to which the lead will be assigned.")]
        string teamName,
        [Description("The reason for triggering assignment.")]
        string assignReason)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;
        var tools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;
        var assignmentTool = tools?.AssignmentTool;

        if (stateId == null || contactId == null || tools == null || assignmentTool == null
            || assignmentTool.Assignments.Exists(a => a.TeamId == null))
        {
            Console.WriteLine("Simulating the assignment...");
            Console.WriteLine($"Action - Assigned Lead ({contactId}) to Team ({teamName})");
            Console.WriteLine($"Reason - {assignReason}");
            Console.WriteLine();

            _logger.LogInformation("Simulating the assignment...");
            _logger.LogInformation(
                "Action - Assigned Lead ({ContactId}) to Team ({TeamName})",
                contactId,
                teamName);
            _logger.LogInformation("Reason - {AssignReason}", assignReason);

            return "Assigned Team";
        }

        var id = $"cn_{UpdateContactOwnerRelationshipsStepArgs.CallName}_id_{Guid.NewGuid()}";
        var name = "Team Assignment";

        var assignmentPairs = assignmentTool.Assignments;

        var matchedAssignmentPair = assignmentPairs.Find(pair => pair.TeamName == teamName);
        if (matchedAssignmentPair == null)
        {
            return
                $"Unable to assign due to team `{teamName}` not found. Available teams are: {string.Join(", ", assignmentPairs.Select(pair => $"`{pair.TeamName}`"))}";
        }

        var callStep = new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
            id,
            name,
            null,
            null,
            UpdateContactOwnerRelationshipsStepArgs.CallName,
            new UpdateContactOwnerRelationshipsStepArgs(
                contactId,
                null,
                $$$"""{{ [{{{matchedAssignmentPair.TeamId}}}] }}""",
                UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team));

        await _bus.Publish(
            new OnExternalStepSubmittedEvent(
                stateId,
                JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

        return "Assigned Team";
    }

    [KernelFunction("add_hands_off_custom_object")]
    [Description("Adds the hands off custom object to the lead.")]
    public async Task<string> AddHandsOffCustomObject(
        Kernel kernel,
        [Description("The reason for triggering assignment.")]
        string assignmentReason,
        [Description("The latest lead score of the lead.")]
        int leadScore,
        [Description("A comprehensive conversation summary between the lead and our company.")]
        string shortSummary)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;
        var tools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;
        var customObjectLeadScoreTool = tools?.HandsOffCustomObjectTool;

        if (stateId == null || contactId == null || tools == null || customObjectLeadScoreTool == null)
        {
            Console.WriteLine("Simulating the hands off custom object creation...");
            Console.WriteLine($"Action - Added Hands Off Custom Object to Lead ({contactId})");
            Console.WriteLine($"Assignment Reason - {assignmentReason}");
            Console.WriteLine($"Lead Score - {leadScore}");
            Console.WriteLine($"Short Summary - {shortSummary}");
            Console.WriteLine();

            _logger.LogInformation("Simulating the hands off custom object creation...");
            _logger.LogInformation(
                "Action - Added Hands Off Custom Object to Lead ({ContactId})",
                contactId);
            _logger.LogInformation("Assignment Reason - {AssignmentReason}", assignmentReason);
            _logger.LogInformation("Lead Score - {LeadScore}", leadScore);
            _logger.LogInformation("Short Summary - {ShortSummary}", shortSummary);

            return "Added Hands Off Custom Object";
        }

        var id = $"cn_{CreateSchemafulObjectStepArgs.CallName}_id_{Guid.NewGuid()}";
        var name = "Add Hands Off Custom Object";

        var callStep = new CallStep<CreateSchemafulObjectStepArgs>(
            id,
            name,
            null,
            null,
            CreateSchemafulObjectStepArgs.CallName,
            new CreateSchemafulObjectStepArgs(
                customObjectLeadScoreTool.SchemaId,
                null,
                new Dictionary<string, string?>
                {
                    {
                        customObjectLeadScoreTool.AssignmentReasonFieldId, assignmentReason
                    },
                    {
                        customObjectLeadScoreTool.LeadScoreFieldId, $"{leadScore}"
                    },
                    {
                        customObjectLeadScoreTool.ShortSummaryFieldId, shortSummary
                    },
                },
                contactId));

        await _bus.Publish(
            new OnExternalStepSubmittedEvent(
                stateId,
                JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

        return "Added Hands Off Custom Object";
    }

    [KernelFunction("update_contact_properties")]
    [Description("Updates the contact properties with the specified values.")]
    public async Task<string> UpdateContactProperties(
        Kernel kernel,
        [Description("List of contact properties to update, each with PropertyId and PropertyValue.")]
        List<ContactProperty> properties)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;

        if (stateId == null || contactId == null)
        {
            Console.WriteLine("Simulating contact properties update...");
            Console.WriteLine($"Action - Updated Contact Properties for Contact ({contactId})");

            foreach (var property in properties)
            {
                Console.WriteLine($"Property {property.PropertyId} = {property.PropertyValue}");
            }

            Console.WriteLine();

            _logger.LogInformation("Simulating contact properties update...");
            _logger.LogInformation(
                "Action - Updated Contact Properties for Contact ({ContactId})",
                contactId);

            foreach (var property in properties)
            {
                _logger.LogInformation("Property {PropertyId} = {PropertyValue}", property.PropertyId, property.PropertyValue);
            }

            return "Updated Contact Properties";
        }

        try
        {
            // Create a set of ContactPropertyIdValuePair objects
            var propertySet = new HashSet<ContactPropertyIdValuePair>();

            foreach (var property in properties)
            {
                propertySet.Add(new ContactPropertyIdValuePair(
                    property.PropertyId,
                    property.PropertyValue
                ));
            }

            // Create a unique ID for this step
            var id = $"cn_{UpdateContactPropertiesV2StepArgs.CallName}_id_{Guid.NewGuid()}";
            var name = "Update Contact Properties";

            // Create the call step with UpdateContactPropertiesV2StepArgs
            var callStep = new CallStep<UpdateContactPropertiesV2StepArgs>(
                id,
                name,
                null,
                null,
                UpdateContactPropertiesV2StepArgs.CallName,
                new UpdateContactPropertiesV2StepArgs(propertySet));

            // Publish the event
            await _bus.Publish(
                new OnExternalStepSubmittedEvent(
                    stateId,
                    JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

            return "Updated Contact Properties";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update contact properties");
            return $"Failed to update contact properties: {ex.Message}";
        }
    }

    [KernelFunction("add_contact_labels")]
    [Description("Adds labels to a contact without removing existing labels.")]
    public async Task<string> AddContactLabels(
        Kernel kernel,
        [Description("The list of labels to add to the contact.")]
        List<string> labels)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;

        if (stateId == null || contactId == null)
        {
            Console.WriteLine("Simulating adding labels to contact...");
            Console.WriteLine($"Action - Added labels to Contact ({contactId})");
            Console.WriteLine($"Labels: {string.Join(", ", labels)}");
            Console.WriteLine();

            _logger.LogInformation("Simulating adding labels to contact...");
            _logger.LogInformation(
                "Action - Added labels to Contact ({ContactId})",
                contactId);
            _logger.LogInformation("Labels: {Labels}", string.Join(", ", labels));

            return "Added Contact Labels";
        }

        try
        {
            // Create a unique ID for this step
            var id = $"cn_{UpdateContactLabelRelationshipsV2StepArgs.CallName}_id_{Guid.NewGuid()}";
            var name = "Add Contact Labels";

            // Create the call step with UpdateContactLabelRelationshipsV2StepArgs
            var callStep = new CallStep<UpdateContactLabelRelationshipsV2StepArgs>(
                id,
                name,
                null,
                null,
                UpdateContactLabelRelationshipsV2StepArgs.CallName,
                new UpdateContactLabelRelationshipsV2StepArgs(
                    labels,
                    null)); // null means add labels

            // Publish the event
            await _bus.Publish(
                new OnExternalStepSubmittedEvent(
                    stateId,
                    JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

            return "Added Contact Labels";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add contact labels");
            return $"Failed to add contact labels: {ex.Message}";
        }
    }

    [KernelFunction("remove_contact_labels")]
    [Description("Removes specific labels from a contact.")]
    public async Task<string> RemoveContactLabels(
        Kernel kernel,
        [Description("The list of labels to remove from the contact.")]
        List<string> labels)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;

        if (stateId == null || contactId == null)
        {
            Console.WriteLine("Simulating removing labels from contact...");
            Console.WriteLine($"Action - Removed specific labels from Contact ({contactId})");
            Console.WriteLine($"Labels to remove: {string.Join(", ", labels)}");
            Console.WriteLine();

            _logger.LogInformation("Simulating removing labels from contact...");
            _logger.LogInformation(
                "Action - Removed specific labels from Contact ({ContactId})",
                contactId);
            _logger.LogInformation("Labels to remove: {Labels}", string.Join(", ", labels));

            return "Removed Contact Labels";
        }

        try
        {
            // Create a unique ID for this step
            var id = $"cn_{UpdateContactLabelRelationshipsV2StepArgs.CallName}_id_{Guid.NewGuid()}";
            var name = "Remove Contact Labels";

            // Create the call step with UpdateContactLabelRelationshipsV2StepArgs
            var callStep = new CallStep<UpdateContactLabelRelationshipsV2StepArgs>(
                id,
                name,
                null,
                null,
                UpdateContactLabelRelationshipsV2StepArgs.CallName,
                new UpdateContactLabelRelationshipsV2StepArgs(
                    labels,
                    "specific_label")); // "specific_label" means remove specific labels

            // Publish the event
            await _bus.Publish(
                new OnExternalStepSubmittedEvent(
                    stateId,
                    JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

            return "Removed Contact Labels";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove contact labels");
            return $"Failed to remove contact labels: {ex.Message}";
        }
    }

    [KernelFunction("set_contact_labels")]
    [Description("Removes all existing labels and sets the contact labels to the provided list. Use an empty list to remove all labels.")]
    public async Task<string> SetContactLabels(
        Kernel kernel,
        [Description("The list of labels to set for the contact, replacing all existing labels.")]
        List<string> labels)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;

        if (stateId == null || contactId == null)
        {
            Console.WriteLine("Simulating setting contact labels...");
            Console.WriteLine($"Action - Set labels for Contact ({contactId})");
            Console.WriteLine($"New labels: {string.Join(", ", labels)}");
            Console.WriteLine();

            _logger.LogInformation("Simulating setting contact labels...");
            _logger.LogInformation(
                "Action - Set labels for Contact ({ContactId})",
                contactId);
            _logger.LogInformation("New labels: {Labels}", string.Join(", ", labels));

            return labels.Count > 0 ? "Set Contact Labels" : "Removed All Contact Labels";
        }

        try
        {
            // Create a unique ID for this step
            var id = $"cn_{UpdateContactLabelRelationshipsV2StepArgs.CallName}_id_{Guid.NewGuid()}";
            var name = "Set Contact Labels";

            // Create the call step with UpdateContactLabelRelationshipsV2StepArgs
            var callStep = new CallStep<UpdateContactLabelRelationshipsV2StepArgs>(
                id,
                name,
                null,
                null,
                UpdateContactLabelRelationshipsV2StepArgs.CallName,
                new UpdateContactLabelRelationshipsV2StepArgs(
                    labels,
                    "all_labels")); // "all_labels" means remove all labels and set to the provided list

            // Publish the event
            await _bus.Publish(
                new OnExternalStepSubmittedEvent(
                    stateId,
                    JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

            return labels.Count > 0 ? "Set Contact Labels" : "Removed All Contact Labels";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set contact labels");
            return $"Failed to set contact labels: {ex.Message}";
        }
    }

    [KernelFunction("send_message")]
    [Description("Sends a message asynchronously to a recipient.")]
    public async Task<string> SendMessage(
        Kernel kernel,
        [Description("The message to send.")]
        string message)
    {
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;

        _logger.LogInformation("Attempting to send message via SleekflowToolsPlugin. Message: {Message}, ContactId: {ContactId}", message, contactId);

        var toolsConfig = kernel.Data[KernelDataKeys.TOOLS_CONFIG] as ToolsConfig;
        var sendMessageTool = toolsConfig?.SleekflowSendMessageTool;

        if (string.IsNullOrEmpty(stateId) || sendMessageTool == null)
        {
            _logger.LogWarning("SendMessageAsync: stateId is missing from Kernel data. Simulating send.");

            // Simulate if stateId is not available (e.g., when called directly without full FlowHub context)
            await Task.Delay(50); // Simulate work
            return "MessageSent_Simulated_NoStateId";
        }

        var sendMessageArgs = new SendMessageV2StepArgs(
            sendMessageTool.ChannelExpr,
            sendMessageTool.ChannelIdentityIdExpr,
            message,
            null,
            new WhatsAppCloudApiMessageParameters()
            {
                MessageType = "manual_message",
                MessageInteractiveType = "none",
            },
            null,
            deliveryTypeExpr: "{{ 6 }}",
            staffIdExpr: null);

        var id = $"cn_{SendMessageStepArgs.CallName}_id_{Guid.NewGuid()}";
        var name = "Send Follow-up Message via Intelligent Hub";

        var callStep = new CallStep<SendMessageV2StepArgs>(
            id,
            name,
            null,
            null,
            SendMessageV2StepArgs.CallName,
            sendMessageArgs);

        try
        {
            await _bus.Publish(
                new OnExternalStepSubmittedEvent(
                    stateId,
                    JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

            // No need to track system messages in simplified approach

            _logger.LogInformation("SendMessageStepArgs submitted to bus for Recipient: {ContactId}", contactId);
            return "MessageSent_EventPublished";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish SendMessageStepArgs for Recipient: {ContactId}", contactId);
            return "FailedToSend_PublishError";
        }
    }

    [KernelFunction("add_internal_note")]
    [Description("Adds an internal note to a contact.")]
    public async Task<string> AddInternalNote(
        Kernel kernel,
        [Description("The content of the internal note to add to the contact.")]
        string content)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;

        if (stateId == null || contactId == null)
        {
            Console.WriteLine("Simulating adding internal note to contact...");
            Console.WriteLine($"Action - Added Internal Note to Contact ({contactId})");
            Console.WriteLine($"Content - {content}");
            Console.WriteLine();

            _logger.LogInformation("Simulating adding internal note to contact...");
            _logger.LogInformation(
                "Action - Added Internal Note to Contact ({ContactId})",
                contactId);
            _logger.LogInformation("Content - {Content}", content);

            return "Added Internal Note";
        }

        try
        {
            // Create a unique ID for this step
            var id = $"cn_{AddInternalNoteToContactV2StepArgs.CallName}_id_{Guid.NewGuid()}";
            var name = "Add Internal Note to Contact";

            // Create the call step with AddInternalNoteToContactV2StepArgs
            var callStep = new CallStep<AddInternalNoteToContactV2StepArgs>(
                id,
                name,
                null,
                null,
                AddInternalNoteToContactV2StepArgs.CallName,
                new AddInternalNoteToContactV2StepArgs(content));

            // Publish the event
            await _bus.Publish(
                new OnExternalStepSubmittedEvent(
                    stateId,
                    JsonConvert.DeserializeObject<JObject>(JsonConvert.SerializeObject(callStep))!));

            _logger.LogInformation("Added internal note to contact {ContactId}: {Content}", contactId, content);
            return "Added Internal Note";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add internal note to contact");
            return $"Failed to add internal note: {ex.Message}";
        }
    }
}