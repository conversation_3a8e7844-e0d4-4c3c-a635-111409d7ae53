using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class LocationMessageObject : BaseMessageObject
{
    [JsonProperty("latitude")]
    public double? Latitude { get; set; }

    [JsonProperty("longitude")]
    public double? Longitude { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("address")]
    public string Address { get; set; }

    [JsonConstructor]
    public LocationMessageObject(double? latitude, double? longitude, string name, string address)
    {
        Latitude = latitude;
        Longitude = longitude;
        Name = name;
        Address = address;
    }
}