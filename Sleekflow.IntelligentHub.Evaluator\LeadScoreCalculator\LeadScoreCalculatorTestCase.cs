﻿using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator;

public record LeadScoreCalculatorTestCase(
    string Scenario,
    ChatMessageContent[] ChatMessageContents,
    List<LeadScoreCriterion> Criteria,
    EvaluatedScore ExpectedEvaluatedScore);