﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

[ContainerId(ContainerNames.IntelligentHubUsage)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IIntelligentHubDbResolver))]
public class IntelligentHubUsage : AuditEntity
{
    public const string PropertyNameFeatureName = "feature_name";
    public const string PropertyNameIntelligentHubUsageSnapshot = "intelligent_hub_usage_snapshot";

    [JsonProperty(PropertyNameFeatureName)]
    public string FeatureName { get; set; }

    [JsonProperty(PropertyNameIntelligentHubUsageSnapshot, TypeNameHandling = TypeNameHandling.Objects)]
    public IntelligentHubUsageSnapshot? IntelligentHubUsageSnapshot { get; set; }

    [JsonConstructor]
    public IntelligentHubUsage(
        string featureName,
        IntelligentHubUsageSnapshot? intelligentHubUsageSnapshot,
        string id,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy = null)
        : base(id, sysTypeName: SysTypeNames.IntelligentHubUsage, createdAt, updatedAt, sleekflowCompanyId, createdBy)
    {
        IntelligentHubUsageSnapshot = intelligentHubUsageSnapshot;
        FeatureName = featureName;
    }
}