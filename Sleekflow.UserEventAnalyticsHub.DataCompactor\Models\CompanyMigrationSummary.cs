namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;

public class CompanyMigrationSummary
{
    public string SleekflowCompanyId { get; set; } = string.Empty;
    public int TotalFiles { get; set; }
    public int ProcessedFiles { get; set; }
    public int FailedFiles { get; set; }
    public long TotalRecords { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
    public List<string> Errors { get; set; } = new();

    public int PendingFiles => TotalFiles - ProcessedFiles - FailedFiles;
    public double ProgressPercentage => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;

    public override string ToString()
    {
        return $"CompanyMigrationSummary [Company={SleekflowCompanyId}, Progress={ProcessedFiles}/{TotalFiles}, Duration={ProcessingDuration}, Errors={Errors.Count}]";
    }
}