extern alias <PERSON><PERSON><PERSON><PERSON><PERSON>;
extern alias FlowHubExecutor;
using System.Xml;
using Alba;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Orleans.Serialization;
using Orleans.TestingHost;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.Tests.Grains;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.JsonConfigs;
using Sleekflow.Mvc.Tests;
using Sleekflow.Persistence.Abstractions;
using Azure.Data.Tables;

namespace Sleekflow.FlowHub.Tests;

[SetUpFixture]
public class Application
{
    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }

    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.FlowHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        await SetUpHost();
        await SetupOrleans();
    }

    private static async Task SetupOrleans()
    {
        var builder = new TestClusterBuilder();
        builder.AddSiloBuilderConfigurator<TestSiloConfigurator>();
        builder.AddClientBuilderConfigurator<TestClientConfigurator>();

        Cluster = builder.Build();
        await Cluster.DeployAsync();
    }

    private static async Task SetUpHost()
    {
        Host = await AlbaHost.For<FlowHubApi::Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(services =>
                {
                    services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                    services.AddScoped<IStepOrchestrationService, TestStepOrchestrationService>();
                    services.AddScoped<IStepRequester, TestStepRequester>();
                });
            },
            Array.Empty<IAlbaExtension>());

        Host.AfterEachAsync(async context =>
        {
            await BaseTestHost.InterceptAfterEachAsync(context);
        });

        InMemoryBusHost = await AlbaHost.For<FlowHubApi::Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(services =>
                {
                    services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                    services.AddScoped<IStepOrchestrationService, TestStepOrchestrationService>();
                    services.AddScoped<IStepRequester, TestStepRequester>();
                    services.AddMassTransitTestHarness(r =>
                    {
                        r.AddDelayedMessageScheduler();
                        r.UsingInMemory((context, cfg) =>
                        {
                            cfg.UseNewtonsoftJsonSerializer();
                            cfg.UseNewtonsoftJsonDeserializer();
                            cfg.ConfigureNewtonsoftJsonSerializer(
                                JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                            cfg.ConfigureNewtonsoftJsonDeserializer(
                                JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                            cfg.UseDelayedMessageScheduler();
                            cfg.ConfigureEndpoints(context);
                        });
                    });
                });
            },
            Array.Empty<IAlbaExtension>());

        InMemoryBusHost.AfterEachAsync(async context =>
        {
            await BaseTestHost.InterceptAfterEachAsync(context);
        });
    }

    public class TestRepositoryContext : IDynamicFiltersRepositoryContext
    {
        public bool IsSoftDeleteEnabled { get; set; } = false;

        public string? SleekflowCompanyId { get; set; } = null;
    }

    public class TestSiloConfigurator : ISiloConfigurator
    {
        public void Configure(ISiloBuilder siloBuilder)
        {
            // Use Azure Table Storage (Azurite) instead of memory storage
            var connectionString = Environment.GetEnvironmentVariable("ORLEANS_STORAGE_CONN_STR")
                                   ?? "UseDevelopmentStorage=true";

            // Configure clustering to use Azure Table Storage
            siloBuilder.UseAzureStorageClustering(options =>
            {
                options.TableServiceClient = new TableServiceClient(connectionString);
            });

            // Configure grain storage to use Azure Table Storage
            siloBuilder.AddAzureTableGrainStorage(
                "dictStore",
                options =>
                {
                    options.TableServiceClient = new TableServiceClient(connectionString);
                });

            siloBuilder.AddAzureTableGrainStorage(
                "workflowStore",
                options =>
                {
                    options.TableServiceClient = new TableServiceClient(connectionString);
                });

            siloBuilder.Services.AddSerializer(serializerBuilder =>
            {
                serializerBuilder.AddNewtonsoftJsonSerializer(
                    isSupported: type => type.Namespace == typeof(TestExecutionDetails).Namespace
                );
            });
        }
    }

    public class TestClientConfigurator : IClientBuilderConfigurator
    {
        public void Configure(IConfiguration configuration, IClientBuilder clientBuilder)
        {
            // Use Azure Table Storage (Azurite) for client clustering
            var connectionString = Environment.GetEnvironmentVariable("ORLEANS_STORAGE_CONN_STR")
                                   ?? "UseDevelopmentStorage=true";

            clientBuilder.UseAzureStorageClustering(options =>
            {
                options.TableServiceClient = new TableServiceClient(connectionString);
            });

            clientBuilder.Services.AddSerializer(serializerBuilder =>
            {
                serializerBuilder.AddNewtonsoftJsonSerializer(
                    isSupported: type => type.Namespace == typeof(TestExecutionDetails).Namespace);
            });
        }
    }

    public class TestStepOrchestrationService : IStepOrchestrationService
    {
        private readonly IStateService _stateService;
        private readonly IStepExecutionService _stepExecutionService;
        private readonly IStepRequester _stepRequester;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStateAggregator _stateAggregator;
        private readonly IWorkflowService _workflowService;
        private readonly IWorkflowStepNodeLocator _workflowStepNodeLocator;
        private readonly IWorkflowStepLocator _workflowStepLocator;

        public TestStepOrchestrationService(
            IStateService stateService,
            IStepExecutionService stepExecutionService,
            IStepRequester stepRequester,
            IServiceProvider serviceProvider,
            IStateAggregator stateAggregator,
            IWorkflowService workflowService,
            IWorkflowStepNodeLocator workflowStepNodeLocator,
            IWorkflowStepLocator workflowStepLocator)
        {
            _stateService = stateService;
            _stepExecutionService = stepExecutionService;
            _stepRequester = stepRequester;
            _serviceProvider = serviceProvider;
            _stateAggregator = stateAggregator;
            _workflowService = workflowService;
            _workflowStepNodeLocator = workflowStepNodeLocator;
            _workflowStepLocator = workflowStepLocator;
        }

        public async Task ExecuteStepAsync(string stateId, string stepId, Stack<StackEntry> stackEntries)
        {
            await _stepRequester.RequestAsync(stateId, stepId, stackEntries, null);
        }

        public async Task ExecuteSleepSleep(
            string stateId,
            string stepId,
            TimeSpan timeSpan,
            Stack<StackEntry> stackEntries)
        {
            await Task.Delay(timeSpan);
            await ExecuteStepAsync(stateId, stepId, stackEntries);
        }

        // Override Sleekflow.FlowHub.Workers.Triggers.Orchestrators.ExecuteTryCatchStepOrchestrator.RunAsync
        public async Task ExecuteTryCatchStepAsync(
            string stateId,
            string stepId,
            string tryStepId,
            string catchStepId,
            Stack<StackEntry> stackEntries)
        {
            try
            {
                await ExecuteTryCatchStepInternalAsync(stateId, stepId, tryStepId, catchStepId, stackEntries);
            }
            catch (Exception e)
            {
                await _serviceProvider.GetRequiredService<IStepExecutorActivator>().CompleteStepAsync(
                    stateId,
                    stepId,
                    stackEntries,
                    StepExecutionStatuses.Failed);
            }
        }

        private async Task ExecuteTryCatchStepInternalAsync(
            string stateId,
            string stepId,
            string tryStepId,
            string catchStepId,
            Stack<StackEntry> stackEntries)
        {
            try
            {
                await ExecuteStepAsync(stateId, tryStepId, stackEntries);
            }
            catch (SfFlowHubUserFriendlyException e)
            {
                var proxyState = await _stateService.GetProxyStateAsync(stateId);
                var tryCatchStep = (TryCatchStep) _workflowStepLocator.GetStep(
                    proxyState.WorkflowContext.SnapshottedWorkflow,
                    stepId);
                await _stateAggregator.AggregateStateError(
                    proxyState,
                    tryCatchStep.Catch.As,
                    new UserFriendlyError(e.UserFriendlyErrorCode, e.UserFriendlyErrorMessage));

                await ExecuteStepAsync(stateId, catchStepId, stackEntries);

                await _stateAggregator.AggregateStateError(
                    proxyState,
                    tryCatchStep.Catch.As,
                    null);
            }
            catch (Exception)
            {
                await ExecuteStepAsync(stateId, catchStepId, stackEntries);
            }

            await _serviceProvider.GetRequiredService<IStepExecutorActivator>().CompleteStepAsync(
                stateId,
                stepId,
                stackEntries,
                StepExecutionStatuses.Complete);
        }

        public async Task ExecuteParallelStepAsync(
            string stateId,
            string stepId,
            List<string> parallelStepIds,
            Stack<StackEntry> stackEntries)
        {
            var tasks = parallelStepIds.Select(id => ExecuteStepAsync(stateId, id, stackEntries)).ToList();
            await Task.WhenAll(tasks);

            await _serviceProvider.GetRequiredService<IStepExecutorActivator>().CompleteStepAsync(
                stateId,
                stepId,
                stackEntries,
                StepExecutionStatuses.Complete);
        }

        public async Task NotifyStatusAsync(
            string stateId,
            string stepId,
            string? workerInstanceId,
            string stepExecutionStatus,
            Exception? exception = null)
        {
            var stateIdentity = await _stateService.GetStateIdentityAsync(stateId);

            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                stateIdentity.SleekflowCompanyId,
                stateIdentity.WorkflowVersionedId);

            var stepNodeId = _workflowStepNodeLocator.GetStepNodeId(
                workflow,
                stepId);

            UserFriendlyError? userFriendlyError = null;

            if (exception is SfFlowHubUserFriendlyException sfFlowHubUserFriendlyException)
            {
                userFriendlyError = new UserFriendlyError(
                    sfFlowHubUserFriendlyException.UserFriendlyErrorCode,
                    sfFlowHubUserFriendlyException.UserFriendlyErrorMessage);
            }
            else if (exception is not null)
            {
                userFriendlyError = new UserFriendlyError(
                    UserFriendlyErrorCodes.InternalError,
                    string.Empty);
            }

            await _stepExecutionService.CreateStepExecutionAsync(
                stateId,
                stateIdentity,
                stepId,
                stepNodeId,
                stepExecutionStatus,
                workerInstanceId,
                DateTimeOffset.UtcNow,
                userFriendlyError);
        }
    }

    public class TestStepRequester : IStepRequester
    {
        private readonly IServiceProvider _serviceProvider;

        public TestStepRequester(
            IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task RequestAsync(
            string stateId,
            string stepId,
            Stack<StackEntry> stackEntries,
            string? workerInstanceId)
        {
            var stepExecutorActivator = _serviceProvider.GetRequiredService<IStepExecutorActivator>();

            await stepExecutorActivator.RequestStepAsync(
                stateId,
                stepId,
                stackEntries,
                null);
        }
    }

    public static IAlbaHost Host { get; private set; }

    public static IAlbaHost InMemoryBusHost { get; private set; }

    public static TestCluster Cluster { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
        InMemoryBusHost.Dispose();
        Cluster.StopAllSilos();
        Cluster.Dispose();
    }
}