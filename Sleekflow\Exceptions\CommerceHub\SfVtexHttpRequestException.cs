﻿namespace Sleekflow.Exceptions.CommerceHub;

public class SfVtexHttpRequestException : ErrorCodeException
{
    public HttpRequestMessage? HttpRequestMessage { get; }

    public SfVtexHttpRequestException(
        HttpRequestMessage? httpRequestMessage,
        string message)
        : base(ErrorCodeConstant.SfVtexHttpRequestException, message)
    {
        HttpRequestMessage = httpRequestMessage;
    }

    public SfVtexHttpRequestException(string message)
        : base(ErrorCodeConstant.SfVtexHttpRequestException, message)
    {
    }
}