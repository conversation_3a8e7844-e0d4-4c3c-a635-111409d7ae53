using System.ComponentModel.DataAnnotations;
using Azure.Search.Documents.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class AutocompleteCategories
    : ITrigger<
        AutocompleteCategories.AutocompleteCategoriesInput,
        AutocompleteCategories.AutocompleteCategoriesOutput>
{
    private readonly ICategorySearchService _categorySearchService;

    public AutocompleteCategories(ICategorySearchService categorySearchService)
    {
        _categorySearchService = categorySearchService;
    }

    public class AutocompleteCategoriesInput
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(256, MinimumLength = 1)]
        [JsonProperty("search_text")]
        public string SearchText { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public AutocompleteCategoriesInput(
            string sleekflowCompanyId,
            string storeId,
            string searchText,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            SearchText = searchText;
            Limit = limit;
        }
    }

    public class AutocompleteCategoriesOutput
    {
        [JsonProperty("autocomplete_items")]
        public List<AutocompleteItem> AutocompleteItems { get; set; }

        [JsonConstructor]
        public AutocompleteCategoriesOutput(
            List<AutocompleteItem> autocompleteItems)
        {
            AutocompleteItems = autocompleteItems;
        }
    }

    public async Task<AutocompleteCategoriesOutput> F(AutocompleteCategoriesInput autocompleteCategoriesInput)
    {
        var autocompleteItems =
            await _categorySearchService.AutocompleteCategoriesAsync(
                autocompleteCategoriesInput.SleekflowCompanyId,
                autocompleteCategoriesInput.StoreId,
                autocompleteCategoriesInput.SearchText,
                autocompleteCategoriesInput.Limit);

        return new AutocompleteCategoriesOutput(autocompleteItems);
    }
}