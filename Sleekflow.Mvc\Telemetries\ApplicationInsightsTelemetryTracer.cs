using System.Diagnostics;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.Extensions.Logging;
using Sleekflow.Configs;

namespace Sleekflow.Mvc.Telemetries;

public interface IApplicationInsightsTelemetryTracer
{
    ValueTask InvokeAndTraceDependencyCallAsync(
        string dependencyTypeName,
        string target,
        string dependencyName,
        string? data,
        Func<ValueTask> onPerformAsync);

    void TraceDependency(
        string dependencyTypeName,
        string target,
        string dependencyName,
        string data,
        DateTimeOffset startTime,
        TimeSpan duration,
        string? resultCode = null,
        bool success = true);

    void TraceEvent(
        string eventName,
        IDictionary<string, string>? properties = null,
        IDictionary<string, double>? metrics = null);
}

public class ApplicationInsightsTelemetryTracer : IApplicationInsightsTelemetryTracer
{
    private readonly ILogger<ApplicationInsightsTelemetryTracer> _logger;
    private readonly TelemetryClient? _telemetryClient;
    private readonly bool _isEnabled = false;

    public ApplicationInsightsTelemetryTracer(
        ILogger<ApplicationInsightsTelemetryTracer> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;

        // Avoid TelemetryClient is not injected with service.AddApplicationInsightsTelemetry() for dev / local environment
        var applicationInsightTelemetryConfig = new ApplicationInsightsTelemetryConfig();
        if (applicationInsightTelemetryConfig.IsTelemetryTracerEnabled == "TRUE")
        {
            _telemetryClient = serviceProvider.GetService(typeof(TelemetryClient)) as TelemetryClient;

            _isEnabled = _telemetryClient != null;
        }
        else
        {
            _isEnabled = false;
        }
    }

    public async ValueTask InvokeAndTraceDependencyCallAsync(
        string dependencyTypeName,
        string target,
        string dependencyName,
        string? data,
        Func<ValueTask> onPerformAsync)
    {
        if (!_isEnabled)
        {
            await onPerformAsync.Invoke();
            return;
        }

        var startTime = DateTimeOffset.UtcNow;

        var stopwatch = Stopwatch.StartNew();
        try
        {
            await onPerformAsync.Invoke();

            stopwatch.Stop();

            TraceDependency(
                dependencyTypeName,
                target,
                dependencyName,
                data,
                startTime,
                TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds));
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to track dependency");
            _telemetryClient?.TrackDependency(
                new DependencyTelemetry(
                    dependencyTypeName,
                    target,
                    dependencyName,
                    data,
                    startTime,
                    TimeSpan.FromMilliseconds(stopwatch.ElapsedMilliseconds),
                    null,
                    false));
            throw;
        }
    }

    public void TraceDependency(
        string dependencyTypeName,
        string target,
        string dependencyName,
        string data,
        DateTimeOffset startTime,
        TimeSpan duration,
        string? resultCode = null,
        bool success = true)
    {
        if (!_isEnabled)
        {
            return;
        }

        _telemetryClient?.TrackDependency(
            new DependencyTelemetry(
                dependencyTypeName,
                target,
                dependencyName,
                data,
                startTime,
                duration,
                null,
                true));
    }

    public void TraceEvent(
        string eventName,
        IDictionary<string, string>? properties = null,
        IDictionary<string, double>? metrics = null)
    {
        if (!_isEnabled)
        {
            return;
        }

        _telemetryClient?.TrackEvent(eventName, properties, metrics);
    }
}