﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnAgentCompleteStepActivationEventConsumerDefinition
    : ConsumerDefinition<OnAgentCompleteStepActivationEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnAgentCompleteStepActivationEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnAgentCompleteStepActivationEventConsumer : IConsumer<OnAgentCompleteStepActivationEvent>
{
    private readonly ILogger _logger;
    private readonly IStateService _stateService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IStepExecutorActivator _stepExecutorActivator;

    public OnAgentCompleteStepActivationEventConsumer(
        ILogger<OnAgentCompleteStepActivationEventConsumer> logger,
        IStateService stateService,
        IStateAggregator stateAggregator,
        IStepExecutorActivator stepExecutorActivator)
    {
        _logger = logger;
        _stateService = stateService;
        _stateAggregator = stateAggregator;
        _stepExecutorActivator = stepExecutorActivator;
    }

    public async Task Consume(ConsumeContext<OnAgentCompleteStepActivationEvent> context)
    {
        var message = context.Message;
        var stepExecutionStatus = message.StepExecutionStatus ?? StepExecutionStatuses.Complete;

        _logger.LogInformation(
            "OnAgentCompleteStepActivationEventConsumer: Consume: ProxyStateId: {ProxyStateId}, AggregateStepId: {AggregateStepId}",
            message.ProxyStateId,
            message.AggregateStepId);
        var state = await _stateService.GetProxyStateAsync(context.Message.ProxyStateId);

        if (!string.IsNullOrEmpty(message.AggregateStateContext))
        {
            var deserializeAggregateStateContext =
                JsonConvert.DeserializeObject<GetAgentRecommendedReplyEvent.Response>(
                    message.AggregateStateContext);

            if (deserializeAggregateStateContext is { ConfidenceScore: < 0 })
            {
                _logger.LogError(
                    "AgentFlow exited due to insufficient credit, Consume: ProxyStateId: {ProxyStateId}, AggregateStepId: {AggregateStepId}",
                    message.ProxyStateId,
                    message.AggregateStepId);

                await _stepExecutorActivator.CompleteStepAsync(
                    message.ProxyStateId,
                    message.AggregateStepId,
                    message.StackEntries,
                    StepExecutionStatuses.Failed);

                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.InternalError,
                    "AgentFlow exited due to insufficient credit");
            }

            await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                message.AggregateStepId,
                message.AggregateStateContext);
        }

        await _stepExecutorActivator.CompleteStepAsync(
            message.ProxyStateId,
            message.AggregateStepId,
            message.StackEntries,
            stepExecutionStatus);
    }
}