using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Workers.Services;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class ProcessContactEligibilityCheckByBatch
{
    private readonly IContactBatchService _contactBatchService;
    private readonly IContactEligibilityService _contactEligibilityService;
    private readonly IContactEnrollmentService _contactEnrollmentService;
    private readonly ILogger<ProcessContactEligibilityCheckByBatch> _logger;

    public ProcessContactEligibilityCheckByBatch(
        IContactBatchService contactBatchService,
        IContactEligibilityService contactEligibilityService,
        IContactEnrollmentService contactEnrollmentService,
        ILogger<ProcessContactEligibilityCheckByBatch> logger)
    {
        _contactBatchService = contactBatchService;
        _contactEligibilityService = contactEligibilityService;
        _contactEnrollmentService = contactEnrollmentService;
        _logger = logger;
    }

    public class ProcessContactEligibilityCheckByBatchInput
    {
        [Required]
        [JsonProperty("origin")]
        public string Origin { get; set; }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("last_contact_created_at")]
        [Validations.ValidateObject]
        public DateTimeOffset? LastContactCreatedAt { get; set; }

        [JsonProperty("last_contact_id")]
        public string? LastContactId { get; set; }

        [JsonProperty("batch_size")]
        [Validations.ValidateObject]
        public int? BatchSize { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("workflow_name")]
        [Required]
        public string WorkflowName { get; set; }

        [JsonProperty("condition")]
        [Required]
        public string Condition { get; set; }

        [JsonConstructor]
        public ProcessContactEligibilityCheckByBatchInput(
            string origin,
            string sleekflowCompanyId,
            DateTimeOffset? lastContactCreatedAt,
            string? lastContactId,
            int? batchSize,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string condition)
        {
            Origin = origin;
            SleekflowCompanyId = sleekflowCompanyId;
            LastContactCreatedAt = lastContactCreatedAt;
            LastContactId = lastContactId;
            BatchSize = batchSize;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            WorkflowName = workflowName;
            Condition = condition;
        }
    }

    public class ProcessContactEligibilityCheckByBatchOutput
    {
        [JsonProperty("next_batch_last_contact_created_at")]
        public DateTimeOffset? NextBatchLastContactCreatedAt { get; set; }

        [JsonProperty("next_batch_last_contact_id")]
        public string? NextBatchLastContactId { get; set; }

        [JsonProperty("contacts_in_fetched_batch")]
        public int ContactsInFetchedBatch { get; set; }

        [JsonProperty("contacts_enrolled")]
        public int ContactsEnrolled { get; set; }

        [JsonConstructor]
        public ProcessContactEligibilityCheckByBatchOutput(
            DateTimeOffset? nextBatchLastContactCreatedAt,
            string? nextBatchLastContactId,
            int contactsInFetchedBatch,
            int contactsEnrolled)
        {
            NextBatchLastContactCreatedAt = nextBatchLastContactCreatedAt;
            NextBatchLastContactId = nextBatchLastContactId;
            ContactsInFetchedBatch = contactsInFetchedBatch;
            ContactsEnrolled = contactsEnrolled;
        }
    }

    [Function("ProcessContactEligibilityCheckByBatch")]
    public async Task<ProcessContactEligibilityCheckByBatchOutput> RunAsync(
        [ActivityTrigger]
        ProcessContactEligibilityCheckByBatchInput input)
    {
        _logger.LogInformation(
            "Starting ProcessContactsBatchActivity for Company {CompanyId}, Workflow {WorkflowVersionedId}, BatchSize {BatchSize}, LastContactCreatedAt {LastContactCreatedAt}, LastContactId {LastContactId}",
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            input.BatchSize,
            input.LastContactCreatedAt,
            input.LastContactId);

        // Step 1: Get contacts by batch
        var contactsBatchResult = await _contactBatchService.GetContactsByBatchAsync(
            input.Origin,
            input.SleekflowCompanyId,
            input.LastContactCreatedAt,
            input.LastContactId,
            input.BatchSize,
            input.WorkflowVersionedId);

        var contactsInFetchedBatch = contactsBatchResult.Contacts?.Count ?? 0;
        _logger.LogInformation("Fetched {ContactsCount} contacts for batch processing.", contactsInFetchedBatch);

        // Step 2 & 3: Check eligibility and enroll contacts
        var contactsEnrolled = 0;

        if (contactsBatchResult.Contacts != null && contactsBatchResult.Contacts.Count > 0)
        {
            contactsEnrolled = await _contactEligibilityService.ProcessAndEnrollEligibleContactsAsync(
                contactsBatchResult.Contacts,
                input.WorkflowId,
                input.WorkflowVersionedId,
                input.SleekflowCompanyId,
                input.Condition,
                input.WorkflowName,
                _contactEnrollmentService);
        }

        _logger.LogInformation(
            "Processed batch for Company {CompanyId}, Workflow {WorkflowVersionedId}. Fetched: {FetchedCount}, Enrolled: {EnrolledCount}. Next cursor: CreatedAt={NextCreatedAt}, Id={NextId}",
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            contactsInFetchedBatch,
            contactsEnrolled,
            contactsBatchResult.NextBatch?.LastContactCreatedAt,
            contactsBatchResult.NextBatch?.LastContactId);

        return new ProcessContactEligibilityCheckByBatchOutput(
            contactsBatchResult.NextBatch?.LastContactCreatedAt,
            contactsBatchResult.NextBatch?.LastContactId,
            contactsInFetchedBatch,
            contactsEnrolled);
    }
}