using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISendMetaCapiEventStepExecutor : IStepExecutor
{
}

public class SendMetaCapiEventStepExecutor
    : GeneralStepExecutor<CallStep<SendMetaCapiEventStepArgs>>,
        ISendMetaCapiEventStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IRequestClient<SendMetaCapiEventRequest> _sendMetaCapiEventRequestClient;

    public SendMetaCapiEventStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IRequestClient<SendMetaCapiEventRequest> sendMetaCapiEventRequestClient)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _sendMetaCapiEventRequestClient = sendMetaCapiEventRequestClient;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var sendMetaCapiEventInput = await GetArgs(callStep, state);

            var conversionApiEvent = new SendMetaCapiEventObjectRequest(
                sendMetaCapiEventInput.Data.Select(d => new SendMetaCapiEventData(
                    d.EventName,
                    d.EventTime,
                    d.ActionSource,
                    d.MessagingChannel,
                    new SendMetaCapiEventUserData(
                        d.UserData.WhatsappBusinessAccountId,
                        d.UserData.CtwaClid),
                    new SendMetaCapiEventCustomData(
                        d.CustomData.Currency,
                        d.CustomData.Value)
                )).ToArray())
            {
                PartnerAgent = sendMetaCapiEventInput.PartnerAgent
            };

            await _sendMetaCapiEventRequestClient.GetResponse<SendMetaCapiEventReply>(
                new SendMetaCapiEventRequest(
                    sendMetaCapiEventInput.StateIdentity.SleekflowCompanyId,
                    sendMetaCapiEventInput.Data[0].UserData.WhatsappBusinessAccountId,
                    conversionApiEvent), default, RequestTimeout.After(m: 10));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class SendMetaCapiEventInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("data")]
        [Required]
        [Validations.ValidateObject]
        public CapiData[] Data { get; set; }

        [JsonProperty("partner_agent")]
        [Required]
        public string PartnerAgent { get; set; } = "sleekflow";

        [JsonConstructor]
        public SendMetaCapiEventInput(
            string stateId,
            StateIdentity stateIdentity,
            CapiData[] data)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            Data = data;
        }
    }

    public class CapiData
    {
        [JsonProperty("event_name")]
        [Required]
        public string EventName { get; set; }

        [JsonProperty("event_time")]
        [Required]
        public string EventTime { get; set; }

        [JsonProperty("action_source")]
        [Required]
        public string ActionSource { get; set; }

        [JsonProperty("messaging_channel")]
        [Required]
        public string MessagingChannel { get; set; }

        [JsonProperty("user_data")]
        [Required]
        [Validations.ValidateObject]
        public CapiUserData UserData { get; set; }

        [JsonProperty("custom_data")]
        [Required]
        [Validations.ValidateObject]
        public CapiCustomData CustomData { get; set; }

        [JsonConstructor]
        public CapiData(
            string eventName,
            string eventTime,
            string actionSource,
            string messagingChannel,
            CapiUserData userData,
            CapiCustomData customData)
        {
            EventName = eventName;
            EventTime = eventTime;
            ActionSource = actionSource;
            MessagingChannel = messagingChannel;
            UserData = userData;
            CustomData = customData;
        }
    }

    public class CapiUserData
    {
        [JsonProperty("whatsapp_business_account_id")]
        [Required]
        public string WhatsappBusinessAccountId { get; set; }

        [JsonProperty("ctwa_clid")]
        [Required]
        public string CtwaClid { get; set; }

        [JsonConstructor]
        public CapiUserData(
            string whatsappBusinessAccountId,
            string ctwaClid)
        {
            WhatsappBusinessAccountId = whatsappBusinessAccountId;
            CtwaClid = ctwaClid;
        }
    }

    public class CapiCustomData
    {
        [JsonProperty("currency")]
        [Required]
        public string Currency { get; set; }

        [JsonProperty("value")]
        [Required]
        public string Value { get; set; }

        [JsonConstructor]
        public CapiCustomData(
            string currency,
            string value)
        {
            Currency = currency;
            Value = value;
        }
    }

    private async Task<SendMetaCapiEventInput> GetArgs(
        CallStep<SendMetaCapiEventStepArgs> callStep,
        ProxyState state)
    {
        var whatsappBusinessAccountId = callStep.Args.WhatsappBusinessAccountId;
        var eventName = callStep.Args.EventName;
        var actionSource = callStep.Args.ActionSource;
        var messagingChannel = callStep.Args.MessagingChannel;

        var ctwaClidExpr = (string?) await _stateEvaluator.EvaluateExpressionAsync(
            state,
            callStep.Args.CtwaClidExpr ?? string.Empty) ?? callStep.Args.CtwaClidExpr;

        var customDataCurrencyExpr = (string?) await _stateEvaluator.EvaluateExpressionAsync(
            state,
            callStep.Args.CustomDataCurrencyExpr ?? string.Empty) ?? callStep.Args.CustomDataCurrencyExpr;

        var customDataValueExpr = (string?) await _stateEvaluator.EvaluateExpressionAsync(
            state,
            callStep.Args.CustomDataValueExpr ?? string.Empty) ?? callStep.Args.CustomDataValueExpr;

        var eventTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        var userData = new CapiUserData(
            whatsappBusinessAccountId,
            ctwaClidExpr);

        var customData = new CapiCustomData(
            customDataCurrencyExpr,
            customDataValueExpr);

        return new SendMetaCapiEventInput(
            state.Id,
            state.Identity,
            new[]
            {
                new CapiData(
                    eventName,
                    eventTime.ToString(),
                    actionSource,
                    messagingChannel,
                    userData,
                    customData)
            });
    }
}