using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;

namespace Sleekflow.CommerceHub.Models.Carts;

public class CartLineItemDto : CartLineItem
{
    [JsonProperty("product_variant_snapshot")]
    public ProductVariantDto ProductVariantSnapshot { get; set; }

    [JsonProperty("product_snapshot")]
    public ProductDto? ProductSnapshot { get; set; }

    [JsonConstructor]
    public CartLineItemDto(
        string productVariantId,
        string productId,
        string? description,
        int quantity,
        Discount? lineItemDiscount,
        Dictionary<string, object?> metadata,
        ProductVariantDto productVariantSnapshot,
        ProductDto? productSnapshot = null)
        : base(
            productVariantId,
            productId,
            description,
            quantity,
            lineItemDiscount,
            metadata)
    {
        ProductSnapshot = productSnapshot;
        ProductVariantSnapshot = productVariantSnapshot;
    }

    public CartLineItemDto(
        CartLineItem cartLineItem,
        ProductVariantDto productVariantDto,
        ProductDto? productDto = null)
        : this(
            cartLineItem.ProductVariantId,
            cartLineItem.ProductId,
            cartLineItem.Description,
            cartLineItem.Quantity,
            cartLineItem.LineItemDiscount,
            cartLineItem.Metadata,
            productVariantDto,
            productDto)
    {
    }
}