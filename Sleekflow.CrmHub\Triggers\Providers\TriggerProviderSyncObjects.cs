﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class TriggerProviderSyncObjects : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;
    private readonly IProviderSelector _providerSelector;

    public TriggerProviderSyncObjects(
        IProviderConfigService providerConfigService,
        IProviderSelector providerSelector)
    {
        _providerConfigService = providerConfigService;
        _providerSelector = providerSelector;
    }

    public class TriggerProviderSyncObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public TriggerProviderSyncObjectsInput(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
        }
    }

    public class TriggerProviderSyncObjectsOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("provider_state_id")]
        public string ProviderStateId { get; set; }

        [JsonConstructor]
        public TriggerProviderSyncObjectsOutput(long count, string providerStateId)
        {
            Count = count;
            ProviderStateId = providerStateId;
        }
    }

    public async Task<TriggerProviderSyncObjectsOutput> F(
        TriggerProviderSyncObjectsInput triggerProviderSyncObjectsInput)
    {
        var sleekflowCompanyId = triggerProviderSyncObjectsInput.SleekflowCompanyId;
        var providerName = triggerProviderSyncObjectsInput.ProviderName;
        var entityTypeName = triggerProviderSyncObjectsInput.EntityTypeName;

        var providerService = _providerSelector.GetProviderService(providerName);

        var providerConfig = await _providerConfigService.GetProviderConfigOrDefaultAsync(
            sleekflowCompanyId,
            providerName);
        if (providerConfig == null
            || providerConfig.EntityTypeNameToSyncConfigDict.ContainsKey(entityTypeName) == false)
        {
            throw new SfNotInitializedException(
                providerName,
                entityTypeName);
        }

        var syncConfig = providerConfig.EntityTypeNameToSyncConfigDict[entityTypeName];

        var getObjectsCountOutput = await providerService.GetObjectsCountAsync(
            sleekflowCompanyId,
            entityTypeName,
            syncConfig.FilterGroups);

        var syncObjectsOutput = await providerService.SyncObjectsAsync(
            sleekflowCompanyId,
            entityTypeName,
            syncConfig.FilterGroups,
            syncConfig.FieldFilters);

        return new TriggerProviderSyncObjectsOutput(
            getObjectsCountOutput.Count,
            syncObjectsOutput.ProviderStateId);
    }
}