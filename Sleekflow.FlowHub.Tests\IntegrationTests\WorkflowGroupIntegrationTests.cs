﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Triggers.WorkflowGroups;
using Sleekflow.FlowHub.Triggers.Workflows;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class WorkflowGroupIntegrationTests
{
    [Test]
    [TestCase(0)]
    [TestCase(1)]
    [TestCase(5)]
    public async Task GetWorkflowGroups_GivenANumberOfWorkflowGroupsInCompany_ShouldReturnAllTheWorkflowGroups(int count)
    {
        // Arrange
        var mockCompanyId = $"{nameof(GetWorkflowGroups)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupIds = new List<string>();

        for (var i = 0; i < count; ++i)
        {
            var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
                mockCompanyId,
                $"test-work-group-{i+1}",
                mockStaffId);

            workflowGroupIds.Add(createWorkflowGroupOutput!.Data.WorkflowGroup.Id);
        }

        // Act
        var getWorkflowGroupsInput = new GetWorkflowGroups.GetWorkflowGroupsInput(
            mockCompanyId);

        var getWorkflowGroupsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getWorkflowGroupsInput).ToUrl("/WorkflowGroups/GetWorkflowGroups");
            });

        var getWorkflowGroupsOutput = await getWorkflowGroupsScenarioResult
            .ReadAsJsonAsync<Output<GetWorkflowGroups.GetWorkflowGroupsOutput>>();

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(getWorkflowGroupsOutput, Is.Not.Null);
                Assert.That(getWorkflowGroupsOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(getWorkflowGroupsOutput.Data, Is.Not.Null);
                Assert.That(getWorkflowGroupsOutput.Data.WorkflowGroups, Has.Count.EqualTo(count));
            });

        // Clean up
        foreach (var workflowGroupId in workflowGroupIds)
        {
            await DeleteWorkflowGroupAsync(
                workflowGroupId,
                mockCompanyId,
                mockStaffId);
        }
    }

    [Test]
    public async Task CreateWorkflowGroup_GivenUniqueName_ShouldSucceed()
    {
        // Arrange
        var mockCompanyId = $"{nameof(CreateWorkflowGroup)}--{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-workflow-group";

        // Act
        var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(createWorkflowGroupOutput, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(createWorkflowGroupOutput.Data, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput.Data.WorkflowGroup, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput.Data.WorkflowGroup.Name, Is.EqualTo(workflowGroupName));
                Assert.That(createWorkflowGroupOutput.Data.WorkflowGroup.SleekflowCompanyId, Is.EqualTo(mockCompanyId));
            });

        // Clean up
        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput!.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task CreateWorkflowGroup_GivenDuplicateNameInSameCompany_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"{nameof(CreateWorkflowGroup)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-workflow-group";

        // Act
        var createWorkflowGroupOutput1 = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        var createWorkflowGroupOutput2 = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(createWorkflowGroupOutput1, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput1!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(createWorkflowGroupOutput1.Data, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput1.Data.WorkflowGroup, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput1.Data.WorkflowGroup.Name, Is.EqualTo(workflowGroupName));
                Assert.That(createWorkflowGroupOutput1.Data.WorkflowGroup.SleekflowCompanyId, Is.EqualTo(mockCompanyId));

                Assert.That(createWorkflowGroupOutput2, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput2!.HttpStatusCode, Is.EqualTo(StatusCodes.Status500InternalServerError));
                Assert.That(
                    createWorkflowGroupOutput2.Message,
                    Is.EqualTo($"The name '{createWorkflowGroupOutput1.Data.WorkflowGroup.Name}' is already in use by workflow group id '{createWorkflowGroupOutput1.Data.WorkflowGroup.Id}'"));
            });

        // Clean up
        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput1!.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task CreateWorkflowGroup_GivenDuplicateNameInDifferentCompanies_ShouldSucceed()
    {
        // Arrange
        var mockCompanyId1 = $"{nameof(CreateWorkflowGroup)}1-{Guid.NewGuid()}";
        var mockCompanyId2 = $"{nameof(CreateWorkflowGroup)}2-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-workflow-group";

        // Act
        var createWorkflowGroupOutput1 = await CreateWorkflowGroupAsync(
            mockCompanyId1,
            workflowGroupName,
            mockStaffId);

        var createWorkflowGroupOutput2 = await CreateWorkflowGroupAsync(
            mockCompanyId2,
            workflowGroupName,
            mockStaffId);

        Assert.Multiple(
            () =>
            {
                Assert.That(createWorkflowGroupOutput1, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput1!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(createWorkflowGroupOutput1.Data, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput1.Data.WorkflowGroup, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput1.Data.WorkflowGroup.Name, Is.EqualTo(workflowGroupName));
                Assert.That(createWorkflowGroupOutput1.Data.WorkflowGroup.SleekflowCompanyId, Is.EqualTo(mockCompanyId1));

                Assert.That(createWorkflowGroupOutput2, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput2!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(createWorkflowGroupOutput2.Data, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput2.Data.WorkflowGroup, Is.Not.Null);
                Assert.That(createWorkflowGroupOutput2.Data.WorkflowGroup.Name, Is.EqualTo(workflowGroupName));
                Assert.That(createWorkflowGroupOutput2.Data.WorkflowGroup.SleekflowCompanyId, Is.EqualTo(mockCompanyId2));
            });

        // Clean up
        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput1!.Data.WorkflowGroup.Id,
            mockCompanyId1,
            mockStaffId);

        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput2!.Data.WorkflowGroup.Id,
            mockCompanyId2,
            mockStaffId);
    }

    [Test]
    public async Task UpdateWorkflowGroup_GivenUniqueName_ShouldSucceed()
    {
        // Arrange
        var mockCompanyId = $"{nameof(UpdateWorkflowGroup)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var initialWorkflowGroupName = "test-create-workflow-group";
        var updatedWorkflowGroupName = "test-update-workflow-group";

        var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
            mockCompanyId,
            initialWorkflowGroupName,
            mockStaffId);

        // Act
        var updateWorkflowGroupOutput = await UpdateWorkflowGroupAsync(
            mockCompanyId,
            createWorkflowGroupOutput!.Data.WorkflowGroup.Id,
            updatedWorkflowGroupName,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(updateWorkflowGroupOutput, Is.Not.Null);
                Assert.That(updateWorkflowGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(updateWorkflowGroupOutput.Data, Is.Not.Null);
                Assert.That(updateWorkflowGroupOutput.Data.WorkflowGroup, Is.Not.Null);
                Assert.That(updateWorkflowGroupOutput.Data.WorkflowGroup.Id, Is.EqualTo(createWorkflowGroupOutput.Data.WorkflowGroup.Id));
                Assert.That(updateWorkflowGroupOutput.Data.WorkflowGroup.Name, Is.EqualTo(updatedWorkflowGroupName));
            });

        // Clean up
        await DeleteWorkflowGroupAsync(
            updateWorkflowGroupOutput!.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task UpdateWorkflowGroup_GivenDuplicateNameInSameCompany_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"{nameof(UpdateWorkflowGroup)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName1 = "test-create-workflow-group";
        var workflowGroupName2 = "test-create-workflow-group-2";

        var createWorkflowGroupOutput1 = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName1,
            mockStaffId);

        var createWorkflowGroupOutput2 = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName2,
            mockStaffId);

        // Act
        var updateWorkflowGroupOutput = await UpdateWorkflowGroupAsync(
            mockCompanyId,
            createWorkflowGroupOutput2!.Data.WorkflowGroup.Id,
            createWorkflowGroupOutput1!.Data.WorkflowGroup.Name,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(updateWorkflowGroupOutput, Is.Not.Null);
                Assert.That(updateWorkflowGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status500InternalServerError));
                Assert.That(
                    updateWorkflowGroupOutput.Message,
                    Is.EqualTo(
                        $"The name '{createWorkflowGroupOutput1.Data.WorkflowGroup.Name}' is already in use by workflow group id '{createWorkflowGroupOutput1.Data.WorkflowGroup.Id}'"));
            });

        // Clean up
        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput1.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);

        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput2.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task UpdateWorkflowGroup_GivenGroupNotExist_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"{nameof(UpdateWorkflowGroup)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var mockWorkflowGroupId = Guid.NewGuid().ToString();

        // Act
        var updateWorkflowGroupOutput = await UpdateWorkflowGroupAsync(
            mockCompanyId,
            mockWorkflowGroupId,
            "test-update-workflow-group",
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(updateWorkflowGroupOutput, Is.Not.Null);
                Assert.That(updateWorkflowGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status500InternalServerError));
                Assert.That(
                    updateWorkflowGroupOutput.Message,
                    Is.EqualTo($"The object doesn't exist. objectId {mockWorkflowGroupId} partitionKey {mockCompanyId}"));
            });
    }

    [Test]
    public async Task DeleteWorkflowGroup_GivenGroupNotExist_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"{nameof(DeleteWorkflowGroupAsync)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var mockWorkflowGroupId = Guid.NewGuid().ToString();

        // Act
        var deleteWorkflowGroupOutput = await DeleteWorkflowGroupAsync(
            mockWorkflowGroupId,
            mockCompanyId,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(deleteWorkflowGroupOutput, Is.Not.Null);
                Assert.That(deleteWorkflowGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status500InternalServerError));
                Assert.That(
                    deleteWorkflowGroupOutput.Message,
                    Is.EqualTo($"The object doesn't exist. objectId {mockWorkflowGroupId} partitionKey {mockCompanyId}"));
            });
    }

    [Test]
    public async Task DeleteWorkflowGroup_GivenGroupExist_ShouldSucceed_And_SetWorkflowsThatAreUnderTheDeletedGroupToNull()
    {
        // Arrange
        var mockCompanyId = $"{nameof(DeleteWorkflowGroupAsync)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-group";

        var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Create workflow with group",
            mockStaffId,
            workflowGroupId: createWorkflowGroupOutput!.Data.WorkflowGroup.Id);

        // Act
        var deleteWorkflowGroupOutput = await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(deleteWorkflowGroupOutput, Is.Not.Null);
                Assert.That(deleteWorkflowGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
            });

        await Task.Delay(TimeSpan.FromSeconds(10));

        var getWorkflowOutput = await GetWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId);

        Assert.Multiple(
            () =>
            {
                Assert.That(getWorkflowOutput, Is.Not.Null);
                Assert.That(getWorkflowOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(getWorkflowOutput.Data, Is.Not.Null);
                Assert.That(
                    () => getWorkflowOutput.Data.VersionedWorkflows
                        .First(x => x.Id == createWorkflowOutput.Data.Workflow.Id)
                        .WorkflowGroupId,
                    Is.Null);
            });

        // Clean up
        await DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput.Data.Workflow.WorkflowId,
            mockStaffId);
    }

    [Test]
    public async Task CreateWorkflowWithGroup_GivenGroupExist_ShouldSucceed()
    {
        // Arrange
        var mockCompanyId = $"CreateWorkflowWithGroup-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-group";

        var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        // Act
        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Create workflow with group",
            mockStaffId,
            workflowGroupId: createWorkflowGroupOutput!.Data.WorkflowGroup.Id);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(createWorkflowOutput, Is.Not.Null);
                Assert.That(createWorkflowOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(
                    createWorkflowOutput.Data.Workflow.WorkflowGroupId,
                    Is.EqualTo(createWorkflowGroupOutput.Data.WorkflowGroup.Id));
            });

        // Clean up
        await DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            mockStaffId);

        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task CreateWorkflowWithGroup_GivenGroupNotExist_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"CreateWorkflowWithGroup-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var mockWorkflowGroupId = Guid.NewGuid().ToString();

        // Act
        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Create workflow with group",
            mockStaffId,
            workflowGroupId: mockWorkflowGroupId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(createWorkflowOutput, Is.Not.Null);
                Assert.That(createWorkflowOutput!.HttpStatusCode, Is.EqualTo(500));
                Assert.That(
                    createWorkflowOutput.Message,
                    Is.EqualTo($"The object doesn't exist. objectId {mockWorkflowGroupId} partitionKey {mockCompanyId}"));
            });
    }

    [Test]
    public async Task AssignWorkflowToGroup_GivenGroupExist_ShouldSucceed()
    {
        // Arrange
        var mockCompanyId = $"AssignWorkflowToGroup-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-group";

        var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Assign workflow to group",
            mockStaffId);

        // Act
        var assignWorkflowToGroupOutput = await AssignWorkflowToGroupAsync(
            mockCompanyId,
            createWorkflowOutput!.Data.Workflow.Id,
            createWorkflowGroupOutput!.Data.WorkflowGroup.Id,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(assignWorkflowToGroupOutput, Is.Not.Null);
                Assert.That(assignWorkflowToGroupOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(
                    assignWorkflowToGroupOutput.Data.Workflow.WorkflowGroupId,
                    Is.EqualTo(createWorkflowGroupOutput.Data.WorkflowGroup.Id));
            });

        // Clean up
        await DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task AssignWorkflowToGroup_GivenGroupNotExist_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"AssignWorkflowToGroup-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var mockWorkflowGroupId = Guid.NewGuid().ToString();

        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Assign workflow to group",
            mockStaffId);

        // Act
        var assignWorkflowToGroupOutput = await AssignWorkflowToGroupAsync(
            mockCompanyId,
            createWorkflowOutput!.Data.Workflow.Id,
            mockWorkflowGroupId,
            mockStaffId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(assignWorkflowToGroupOutput, Is.Not.Null);
                Assert.That(assignWorkflowToGroupOutput!.HttpStatusCode, Is.EqualTo(500));
                Assert.That(
                    assignWorkflowToGroupOutput.Message,
                    Is.EqualTo($"The object doesn't exist. objectId {mockWorkflowGroupId} partitionKey {mockCompanyId}"));
            });

        // Clean up
        await DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput.Data.Workflow.WorkflowId,
            mockStaffId);
    }

    [Test]
    public async Task UpdateWorkflowWithGroup_GivenGroupExist_ShouldSucceed()
    {
        // Arrange
        var mockCompanyId = $"UpdateWorkflowWithGroup-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var workflowGroupName = "test-create-group";

        var createWorkflowGroupOutput = await CreateWorkflowGroupAsync(
            mockCompanyId,
            workflowGroupName,
            mockStaffId);

        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Update workflow with group",
            mockStaffId);

        // Act
        var updateWorkflowOutput = await UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            createWorkflowOutput.Data.Workflow.Name,
            mockStaffId,
            workflowTriggers: createWorkflowOutput.Data.Workflow.Triggers,
            steps: createWorkflowOutput.Data.Workflow.Steps
                .Select(x => x.ToObject<Step>())
                .ToList()!,
            workflowGroupId: createWorkflowGroupOutput!.Data.WorkflowGroup.Id);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(updateWorkflowOutput, Is.Not.Null);
                Assert.That(updateWorkflowOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(updateWorkflowOutput.Data, Is.Not.Null);
                Assert.That(
                    updateWorkflowOutput.Data.Workflow.WorkflowGroupId,
                    Is.EqualTo(createWorkflowGroupOutput.Data.WorkflowGroup.Id));
            });

        // Clean up
        await DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        await DeleteWorkflowGroupAsync(
            createWorkflowGroupOutput.Data.WorkflowGroup.Id,
            mockCompanyId,
            mockStaffId);
    }

    [Test]
    public async Task UpdateWorkflowWithGroup_GivenGroupNotExist_ShouldThrowError()
    {
        // Arrange
        var mockCompanyId = $"UpdateWorkflowWithGroup-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";
        var mockWorkflowGroupId = Guid.NewGuid().ToString();

        var createWorkflowOutput = await CreateWorkflowAsync(
            mockCompanyId,
            "Update workflow with group",
            mockStaffId);

        // Act
        var updateWorkflowOutput = await UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            createWorkflowOutput.Data.Workflow.Name,
            mockStaffId,
            workflowTriggers: createWorkflowOutput.Data.Workflow.Triggers,
            steps: createWorkflowOutput.Data.Workflow.Steps
                .Select(x => x.ToObject<Step>())
                .ToList()!,
            workflowGroupId: mockWorkflowGroupId);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(updateWorkflowOutput, Is.Not.Null);
                Assert.That(updateWorkflowOutput!.HttpStatusCode, Is.EqualTo(StatusCodes.Status500InternalServerError));
                Assert.That(
                    updateWorkflowOutput.Message,
                    Is.EqualTo($"The object doesn't exist. objectId {mockWorkflowGroupId} partitionKey {mockCompanyId}"));
            });

        // Clean up
        await DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutput.Data.Workflow.WorkflowId,
            mockStaffId);
    }

    #region Helper methods

    private static async Task<Output<CreateWorkflow.CreateWorkflowOutput>?> CreateWorkflowAsync(
        string sleekflowCompanyId,
        string workflowName,
        string staffId,
        List<string>? staffTeamIds = null,
        WorkflowTriggers? workflowTriggers = null,
        List<Step>? steps = null,
        string? workflowGroupId = null)
    {
        workflowTriggers ??= new WorkflowTriggers(
            null,
            new WorkflowTrigger("{{ true }}"),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);

        steps ??= new List<Step>()
        {
            new LogStep("log", "Log", null, null, "Hello", "Information")
        };

        var createWorkflowInput = new CreateWorkflow.CreateWorkflowInput(
            sleekflowCompanyId,
            workflowTriggers,
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            steps
                .Select(JObject.FromObject)
                .ToList(),
            workflowName,
            WorkflowType.Normal,
            workflowGroupId,
            new Dictionary<string, object?>(),
            "v1",
            staffId,
            staffTeamIds,
            null);

        var createWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(createWorkflowInput).ToUrl("/Workflows/CreateWorkflow");
            });

        var createWorkflowOutput = await createWorkflowScenarioResult
            .ReadAsJsonAsync<Output<CreateWorkflow.CreateWorkflowOutput>>();

        return createWorkflowOutput;
    }

    private static async Task<Output<UpdateWorkflow.UpdateWorkflowOutput>?> UpdateWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId,
        string workflowName,
        string staffId,
        List<string>? staffTeamIds = null,
        WorkflowTriggers? workflowTriggers = null,
        List<Step>? steps = null,
        string? workflowGroupId = null)
    {
        workflowTriggers ??= new WorkflowTriggers(
            null,
            new WorkflowTrigger("{{ true }}"),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);

        steps ??= new List<Step>()
        {
            new LogStep("log", "Log", null, null, "Hello", "Information")
        };

        var updateWorkflowInput = new UpdateWorkflow.UpdateWorkflowInput(
            sleekflowCompanyId,
            workflowId,
            workflowTriggers,
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            steps
                .Select(JObject.FromObject)
                .ToList(),
            workflowName,
            workflowGroupId,
            staffId,
            staffTeamIds,
            new Dictionary<string, object?>(),
            null);

        var updateWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(updateWorkflowInput).ToUrl("/Workflows/UpdateWorkflow");
            });

        var updateWorkflowOutput = await updateWorkflowScenarioResult
            .ReadAsJsonAsync<Output<UpdateWorkflow.UpdateWorkflowOutput>>();

        return updateWorkflowOutput;
    }

    private static async Task<Output<GetWorkflow.GetWorkflowOutput>?> GetWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        var getWorkflowInput = new GetWorkflow.GetWorkflowInput(
            sleekflowCompanyId,
            workflowId);

        var getWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getWorkflowInput).ToUrl("/Workflows/GetWorkflow");
            });

        var getWorkflowOutput = await getWorkflowScenarioResult
            .ReadAsJsonAsync<Output<GetWorkflow.GetWorkflowOutput>>();

        return getWorkflowOutput;
    }

    private static async Task DeleteWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var deleteWorkflowInput = new DeleteWorkflow.DeleteWorkflowInput(
            sleekflowCompanyId,
            workflowId,
            staffId,
            staffTeamIds);

        await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(deleteWorkflowInput).ToUrl("/Workflows/DeleteWorkflow");
            });
    }

    private static async Task<Output<AssignWorkflowToGroup.AssignWorkflowToGroupOutput>?> AssignWorkflowToGroupAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string workflowGroupId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var assignWorkflowToGroupInput = new AssignWorkflowToGroup.AssignWorkflowToGroupInput(
            workflowVersionedId,
            workflowGroupId,
            sleekflowCompanyId,
            staffId,
            staffTeamIds);

        var assignWorkflowToGroupScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(assignWorkflowToGroupInput).ToUrl("/Workflows/AssignWorkflowToGroup");
            });

        var assignWorkflowToGroupOutput = await assignWorkflowToGroupScenarioResult
            .ReadAsJsonAsync<Output<AssignWorkflowToGroup.AssignWorkflowToGroupOutput>>();

        return assignWorkflowToGroupOutput;
    }

    private static async Task<Output<CreateWorkflowGroup.CreateWorkflowGroupOutput>?> CreateWorkflowGroupAsync(
        string sleekflowCompanyId,
        string workflowGroupName,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var createWorkflowGroupInput = new CreateWorkflowGroup.CreateWorkflowGroupInput(
            workflowGroupName,
            sleekflowCompanyId,
            staffId,
            staffTeamIds);

        var createWorkflowGroupScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(createWorkflowGroupInput).ToUrl("/WorkflowGroups/CreateWorkflowGroup");
            });

        var createWorkflowGroupOutput = await createWorkflowGroupScenarioResult
            .ReadAsJsonAsync<Output<CreateWorkflowGroup.CreateWorkflowGroupOutput>>();

        return createWorkflowGroupOutput;
    }

    private static async Task<Output<UpdateWorkflowGroup.UpdateWorkflowGroupOutput>?> UpdateWorkflowGroupAsync(
        string sleekflowCompanyId,
        string workflowGroupId,
        string workflowGroupName,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var updateWorkflowGroupInput = new UpdateWorkflowGroup.UpdateWorkflowGroupInput(
            workflowGroupId,
            workflowGroupName,
            sleekflowCompanyId,
            staffId,
            staffTeamIds);

        var updateWorkflowGroupScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(updateWorkflowGroupInput).ToUrl("/WorkflowGroups/UpdateWorkflowGroup");
            });

        var updateWorkflowGroupOutput = await updateWorkflowGroupScenarioResult
            .ReadAsJsonAsync<Output<UpdateWorkflowGroup.UpdateWorkflowGroupOutput>>();

        return updateWorkflowGroupOutput;
    }

    private static async Task<Output<DeleteWorkflowGroup.DeleteWorkflowGroupOutput>?> DeleteWorkflowGroupAsync(
        string workflowGroupId,
        string sleekflowCompanyId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var deleteWorkflowGroupInput = new DeleteWorkflowGroup.DeleteWorkflowGroupInput(
            workflowGroupId,
            sleekflowCompanyId,
            staffId,
            staffTeamIds);

        var deleteWorkflowGroupScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(deleteWorkflowGroupInput).ToUrl("/WorkflowGroups/DeleteWorkflowGroup");
            });

        var deleteWorkflowGroupOutput = await deleteWorkflowGroupScenarioResult
            .ReadAsJsonAsync<Output<DeleteWorkflowGroup.DeleteWorkflowGroupOutput>>();

        return deleteWorkflowGroupOutput;
    }

    #endregion Helper methods
}