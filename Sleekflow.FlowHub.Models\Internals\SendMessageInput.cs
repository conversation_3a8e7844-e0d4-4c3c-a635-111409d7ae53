using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.JsonConfigs;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.States;
using Swashbuckle.AspNetCore.Annotations;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class SendMessageInput
{
    [JsonProperty("state_id")]
    [Required]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    [Required]
    [Validations.ValidateObject]
    public StateIdentity StateIdentity { get; set; }

    [JsonProperty("channel")]
    [Required]
    public string Channel { get; set; }

    [JsonProperty("from_to")]
    [Required]
    [Validations.ValidateObject]
    [JsonConverter(typeof(SendMessageInputFromToConverter))]
    public SendMessageInputFromTo FromTo { get; set; }

    [JsonProperty("message_type")]
    [Required]
    public string MessageType { get; set; }

    [JsonProperty("message_body")]
    [Required]
    [Validations.ValidateObject]
    public MessageBody MessageBody { get; set; }

    [JsonProperty("delivery_type")]
    public int? DeliveryType { get; set; }

    [JsonProperty("staff_id")]
    public string? StaffId { get; set; }

    [JsonConstructor]
    public SendMessageInput(
        string stateId,
        StateIdentity stateIdentity,
        string channel,
        SendMessageInputFromTo fromTo,
        string messageType,
        MessageBody messageBody,
        int? deliveryType,
        string? staffId)
    {
        StateId = stateId;
        StateIdentity = stateIdentity;
        Channel = channel;
        FromTo = fromTo;
        MessageType = messageType;
        MessageBody = messageBody;
        DeliveryType = deliveryType;
        StaffId = staffId;
    }
}

[SwaggerInclude]
[SwaggerDiscriminator("from_to_type")] // We have some tweaks in the Sleekflow.Api project to make this work
[SwaggerSubType(
    typeof(WhatsappCloudApiSendMessageInputFromTo),
    DiscriminatorValue = "WhatsappCloudApiSendMessageInputFromTo")]
[SwaggerSubType(
    typeof(FacebookPageMessengerSendMessageInputFromTo),
    DiscriminatorValue = "FacebookPageMessengerSendMessageInputFromTo")]
[SwaggerSubType(
    typeof(InstagramPageMessengerSendMessageInputFromTo),
    DiscriminatorValue = "InstagramPageMessengerSendMessageInputFromTo")]
public abstract class SendMessageInputFromTo
{
    protected SendMessageInputFromTo(string fromToType)
    {
        FromToType = fromToType;
    }

    // We have some tweaks in the Sleekflow.Api project to make this work
    [JsonProperty("from_to_type")]
    public string FromToType { get; set; }
}

[SwaggerInclude]
public class WhatsappCloudApiSendMessageInputFromTo : SendMessageInputFromTo
{
    [JsonProperty("from_phone_number")]
    public string? FromPhoneNumber { get; set; }

    [JsonProperty("to_phone_number")]
    public string? ToPhoneNumber { get; set; }

    [JsonProperty("to_contact_id")]
    public string? ToContactId { get; set; }

    [JsonProperty("from_channel_id")]
    public string? FromChannelId { get; set; }

    public WhatsappCloudApiSendMessageInputFromTo(
        string? fromPhoneNumber,
        string? toPhoneNumber,
        string? toContactId,
        string? fromChannelId)
        : base("WhatsappCloudApiSendMessageInputFromTo")
    {
        ToContactId = toContactId;
        FromPhoneNumber = fromPhoneNumber;
        ToPhoneNumber = toPhoneNumber;
        ToContactId = toContactId;
        FromChannelId = fromChannelId;
    }
}

[SwaggerInclude]
public class FacebookPageMessengerSendMessageInputFromTo : SendMessageInputFromTo
{
    [JsonProperty("from_facebook_page_id")]
    public string? FromFacebookPageId { get; set; }

    [JsonProperty("to_facebook_id")]
    public string? ToFacebookId { get; set; }

    [JsonProperty("facebook_post_comment_details")]
    public FacebookPostCommentDetailsObject? FacebookPostCommentDetails { get; set; }

    public FacebookPageMessengerSendMessageInputFromTo(
        string? fromFacebookPageId,
        string? toFacebookId,
        FacebookPostCommentDetailsObject facebookPostCommentDetails)
        : base("FacebookPageMessengerSendMessageInputFromTo")
    {
        FromFacebookPageId = fromFacebookPageId;
        ToFacebookId = toFacebookId;
        FacebookPostCommentDetails = facebookPostCommentDetails;
    }
}

[SwaggerInclude]
public class FacebookPostCommentDetailsObject
{
    [JsonProperty("from_facebook_post_id")]
    public string? FromFacebookPostId { get; set; }

    [JsonProperty("to_facebook_comment_id")]
    public string? ToFacebookCommentId { get; set; }

    [JsonConstructor]
    public FacebookPostCommentDetailsObject(string? fromFacebookPostId, string? toFacebookCommentId)
    {
        FromFacebookPostId = fromFacebookPostId;
        ToFacebookCommentId = toFacebookCommentId;
    }
}

[SwaggerInclude]
public class InstagramPageMessengerSendMessageInputFromTo : SendMessageInputFromTo
{
    [JsonProperty("from_instagram_page_id")]
    public string? FromInstagramPageId { get; set; }

    [JsonProperty("to_instagram_id")]
    public string? ToInstagramId { get; set; }

    [JsonProperty("instagram_media_comment_details")]
    public InstagramMediaCommentDetailsObject? InstagramMediaCommentDetails { get; set; }

    public InstagramPageMessengerSendMessageInputFromTo(
        string? fromInstagramPageId,
        string? toInstagramId,
        InstagramMediaCommentDetailsObject? instagramMediaCommentDetails)
        : base("InstagramPageMessengerSendMessageInputFromTo")
    {
        FromInstagramPageId = fromInstagramPageId;
        ToInstagramId = toInstagramId;
        InstagramMediaCommentDetails = instagramMediaCommentDetails;
    }
}

[SwaggerInclude]
public class InstagramMediaCommentDetailsObject
{
    [JsonProperty("from_instagram_media_id")]
    public string? FromInstagramMediaId { get; set; }

    [JsonProperty("to_instagram_comment_id")]
    public string? ToInstagramCommentId { get; set; }

    [JsonConstructor]
    public InstagramMediaCommentDetailsObject(string? fromInstagramMediaId, string? toInstagramCommentId)
    {
        FromInstagramMediaId = fromInstagramMediaId;
        ToInstagramCommentId = toInstagramCommentId;
    }
}