using Microsoft.Extensions.Logging;
using Npgsql;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public class FileTrackingService : IFileTrackingService
{
    private readonly ILogger<FileTrackingService> _logger;
    private readonly IPostgreSqlConfig _config;

    public FileTrackingService(ILogger<FileTrackingService> logger, IPostgreSqlConfig config)
    {
        _logger = logger;
        _config = config;
    }

    public async Task<bool> IsFileMigratedAsync(string sleekflowCompanyId, string blobPath)
    {
        try
        {
            var tableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            var sql = $@"
                SELECT COUNT(*)
                FROM {tableName}
                WHERE blob_path = @blobPath AND migration_status = 'completed'";

            await using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@blobPath", blobPath);

            var count = await command.ExecuteScalarAsync() as long? ?? 0;
            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking migration status for blob: {BlobPath}", blobPath);
            return false;
        }
    }

    public async Task<List<BlobFileInfo>> GetUnmigratedFilesAsync(string sleekflowCompanyId, List<BlobFileInfo> allFiles)
    {
        try
        {
            var tableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            // Get all migrated file paths
            var sql = $@"
                SELECT blob_path
                FROM {tableName}
                WHERE migration_status = 'completed'";

            await using var command = new NpgsqlCommand(sql, connection);
            await using var reader = await command.ExecuteReaderAsync();

            var migratedPaths = new HashSet<string>();
            while (await reader.ReadAsync())
            {
                migratedPaths.Add(reader.GetString(0));
            }

            // Filter out migrated files
            var unmigratedFiles = allFiles.Where(f => !migratedPaths.Contains(f.BlobPath)).ToList();

            _logger.LogInformation("Found {UnmigratedCount} unmigrated files out of {TotalCount} for company {CompanyId}",
                unmigratedFiles.Count, allFiles.Count, sleekflowCompanyId);

            return unmigratedFiles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unmigrated files for company: {CompanyId}", sleekflowCompanyId);
            // Return all files if we can't determine migration status
            return allFiles;
        }
    }

    public async Task MarkFileAsStartedAsync(string sleekflowCompanyId, BlobFileInfo file)
    {
        try
        {
            var tableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            var sql = $@"
                INSERT INTO {tableName} AS t (blob_path, blob_last_modified, blob_size_bytes, records_count, migration_started_at, migration_status)
                VALUES (@blobPath, @lastModified, @sizeBytes, 0, @startedAt, 'pending')
                ON CONFLICT (blob_path)
                DO UPDATE SET
                    migration_started_at = @startedAt,
                    migration_status = 'pending',
                    retry_count = COALESCE(t.retry_count, 0) + 1";

            await using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@blobPath", file.BlobPath);
            command.Parameters.AddWithValue("@lastModified", file.LastModified);
            command.Parameters.AddWithValue("@sizeBytes", file.SizeBytes);
            command.Parameters.AddWithValue("@startedAt", DateTime.UtcNow);

            await command.ExecuteNonQueryAsync();

            _logger.LogDebug("Marked file as started: {BlobPath}", file.BlobPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking file as started: {BlobPath}", file.BlobPath);
            throw;
        }
    }

    public async Task MarkFileAsCompletedAsync(string sleekflowCompanyId, BlobFileInfo file, int recordsCount)
    {
        try
        {
            var tableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            var sql = $@"
                UPDATE {tableName}
                SET migration_completed_at = @completedAt,
                    migration_status = 'completed',
                    records_count = @recordsCount
                WHERE blob_path = @blobPath";

            await using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@blobPath", file.BlobPath);
            command.Parameters.AddWithValue("@completedAt", DateTime.UtcNow);
            command.Parameters.AddWithValue("@recordsCount", recordsCount);

            await command.ExecuteNonQueryAsync();

            _logger.LogDebug("Marked file as completed: {BlobPath} with {RecordsCount} records", file.BlobPath, recordsCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking file as completed: {BlobPath}", file.BlobPath);
            throw;
        }
    }

    public async Task MarkFileAsFailedAsync(string sleekflowCompanyId, BlobFileInfo file, string errorMessage)
    {
        try
        {
            var tableName = PostgreSqlTableNameHelper.GetMigrationHistoryTableName(sleekflowCompanyId);

            await using var connection = new NpgsqlConnection(_config.ConnectionString);
            await connection.OpenAsync();

            var sql = $@"
                UPDATE {tableName}
                SET migration_status = 'failed',
                    error_message = @errorMessage,
                    retry_count = COALESCE(retry_count, 0) + 1
                WHERE blob_path = @blobPath";

            await using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@blobPath", file.BlobPath);
            command.Parameters.AddWithValue("@errorMessage", errorMessage);

            await command.ExecuteNonQueryAsync();

            _logger.LogWarning("Marked file as failed: {BlobPath} - {ErrorMessage}", file.BlobPath, errorMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking file as failed: {BlobPath}", file.BlobPath);
            throw;
        }
    }
}