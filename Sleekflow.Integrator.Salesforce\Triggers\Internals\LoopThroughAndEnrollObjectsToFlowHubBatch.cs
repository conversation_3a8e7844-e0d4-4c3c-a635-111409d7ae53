﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Triggers.Internals;

[TriggerGroup("Internals")]
public class LoopThroughAndEnrollObjectsToFlowHubBatch : ITrigger
{
    private readonly ILogger<LoopThroughAndEnrollObjectsToFlowHubBatch> _logger;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly IBus _bus;

    public LoopThroughAndEnrollObjectsToFlowHubBatch(
        ILogger<LoopThroughAndEnrollObjectsToFlowHubBatch> logger,
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService,
        IBus bus)
    {
        _logger = logger;
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
        _bus = bus;
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            bool isCustomObject,
            string? nextRecordsUrl,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            IsCustomObject = isCustomObject;
            NextRecordsUrl = nextRecordsUrl;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchOutput(long count, string? nextRecordsUrl)
        {
            Count = count;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public async Task<LoopThroughAndEnrollObjectsToFlowHubBatchOutput> F(
        LoopThroughAndEnrollObjectsToFlowHubBatchInput loopThroughAndEnrollObjectsToFlowHubBatchInput)
    {
        var sleekflowCompanyId = loopThroughAndEnrollObjectsToFlowHubBatchInput.SleekflowCompanyId;
        var connectionId = loopThroughAndEnrollObjectsToFlowHubBatchInput.ConnectionId;
        var entityTypeName = loopThroughAndEnrollObjectsToFlowHubBatchInput.EntityTypeName;
        var isCustomObject = loopThroughAndEnrollObjectsToFlowHubBatchInput.IsCustomObject;
        var flowHubWorkflowId = loopThroughAndEnrollObjectsToFlowHubBatchInput.FlowHubWorkflowId;
        var flowHubWorkflowVersionedId = loopThroughAndEnrollObjectsToFlowHubBatchInput.FlowHubWorkflowVersionedId;

        var connection = await _salesforceConnectionService.GetByIdAsync(connectionId, sleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(connection.AuthenticationId, sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Start LoopThroughAndEnrollObjectsToFlowHubBatch: " +
            "sleekflowCompanyId {SleekflowCompanyId}, connectionId {ConnectionId} entityTypeName {EntityTypeName}, nextRecordsUrl {NextRecordsUrls}",
            sleekflowCompanyId,
            connectionId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextRecordsUrl);

        var filterGroup = new SyncConfigFilterGroup(new List<SyncConfigFilter>());
        var filterGroups = new List<SyncConfigFilterGroup> { filterGroup };

        var (objects, nextRecordsUrl) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            entityTypeName,
            filterGroups,
            null,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextRecordsUrl);

        _logger.LogInformation(
            "End LoopThroughAndEnrollObjectsToFlowHubBatch: sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, prevNextRecordsUrl {PrevNextRecordsUrls}, nextRecordsUrl {NextRecordsUrls}, count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextRecordsUrl,
            nextRecordsUrl,
            objects.Count);

        var events = objects
            .Select(
                dict =>
                {
                    var onSalesforceObjectEnrollmentToFlowHubRequestedEvent = new OnSalesforceObjectEnrollmentToFlowHubRequestedEvent(
                        DateTimeOffset.UtcNow,
                        sleekflowCompanyId,
                        connectionId,
                        (string) dict["Id"]!,
                        entityTypeName,
                        isCustomObject,
                        dict,
                        flowHubWorkflowId,
                        flowHubWorkflowVersionedId);

                    return onSalesforceObjectEnrollmentToFlowHubRequestedEvent;
                })
            .ToList();

        var eventChunks = events.Chunk(100).ToList();
        var totalChunks = eventChunks.Count;

        for (var i = 0; i < totalChunks; i++)
        {
            await _bus.PublishBatch(
                eventChunks[i],
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });

            if (i < totalChunks - 1)
            {
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
        }

        _logger.LogInformation(
            "Flushed LoopThroughAndEnrollObjectsToFlowHubBatch: sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, prevNextRecordsUrl {PrevNextRecordsUrls}, nextRecordsUrl {NextRecordsUrls}, count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.NextRecordsUrl,
            nextRecordsUrl,
            objects.Count);

        return new LoopThroughAndEnrollObjectsToFlowHubBatchOutput(objects.Count, nextRecordsUrl);
    }
}