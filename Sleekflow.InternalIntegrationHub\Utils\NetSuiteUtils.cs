using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

namespace Sleekflow.InternalIntegrationHub.Utils;

public static class NetSuiteUtils
{
    public static string LocationConverter(string location, List<SubsidiaryDetailsResponse> netSuite)
    {
        // no direct mapping between OmniHr location and NetSuite subsidiary
        // hardcoding the mapping for now
        Dictionary<string, string> locationMap = new ()
        {
            {
                "Hong Kong SAR", "HK"
            },
            {
                "Singapore", "SG"
            },
            {
                "Malaysia", "MY"
            },
            {
                "Brazil", "BR"
            },
            {
                "United Arab Emirates", "UAE"
            },
            {
                "UAE", "UAE"
            },
            {
                "Indonesia", "ID"
            }
        };
        var locationCode = locationMap.FirstOrDefault(l => string.Equals(l.Key, location));
        if (locationCode.Value != null)
        {
            var subsidiary = netSuite.Find(s => string.Equals(s.Name, locationCode.Value));
            if (subsidiary != null)
            {
                return subsidiary.Id;
            }
        }

        // default to Hong Kong
        return "2";
    }

    public static string CurrencyConverter(string location, List<CurrencyDetailsResponse> netSuite)
    {
        // no direct mapping between OmniHr location and NetSuite currency
        // hardcoding the mapping for now
        Dictionary<string, string> currencyMap = new ()
        {
            {
                "Hong Kong SAR", "HKD"
            },
            {
                "Singapore", "SGD"
            },
            {
                "Malaysia", "MYR"
            },
            {
                "Brazil", "BRL"
            },
            {
                "United Arab Emirates", "AED"
            },
            {
                "UAE", "AED"
            },
            {
                "Indonesia", "IDR"
            }
        };
        var currencyCode = currencyMap.FirstOrDefault(l => string.Equals(l.Key, location));
        if (currencyCode.Value != null)
        {
            var currency = netSuite.Find(s => string.Equals(s.Name, currencyCode.Value));
            if (currency != null)
            {
                return currency.Id;
            }
        }

        // default to USD
        return "1";
    }
}