﻿using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.NeedConfigs;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
[SuppressMessage("Design", "SF1102:Non-nullable properties in Input classes should have [Required] attribute")]
[SuppressMessage("Design", "SF1104:Object properties in Input/Output classes should have [ValidateObject] attribute")]
[SuppressMessage("Design", "SF1103:Collection properties in Input classes should have [ValidateArray] attribute")]
public class SaveWorkflowDraft : ITrigger<
    SaveWorkflowDraft.SaveWorkflowDraftInput,
    SaveWorkflowDraft.SaveWorkflowDraftOutput>
{
    private readonly IWorkflowService _workflowService;
    private readonly INeedConfigService _needConfigService;

    public SaveWorkflowDraft(
        IWorkflowService workflowService,
        INeedConfigService needConfigService)
    {
        _workflowService = workflowService;
        _needConfigService = needConfigService;
    }

    public class SaveWorkflowDraftInput : IHasSleekflowStaff, IHasMetadata, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings? WorkflowEnrollmentSettings { get; set; }

        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings? WorkflowScheduleSettings { get; set; }

        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

        [JsonIgnore]
#pragma warning disable JA1001
#pragma warning disable S2365
        public List<Step> InternalSteps
        {
            get
            {
                return Steps
                    .Select(step => step.ToObject<Step>()!)
                    .ToList();
            }
        }
#pragma warning restore S2365
#pragma warning restore JA1001

        [Required]
        [MaxLength(100)]
        [MinLength(1)]
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_group_id")]
        public string? WorkflowGroupId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [Required]
        [JsonProperty("metadata")]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonConstructor]
        public SaveWorkflowDraftInput(
            string sleekflowCompanyId,
            string workflowId,
            WorkflowTriggers triggers,
            WorkflowEnrollmentSettings? workflowEnrollmentSettings,
            WorkflowScheduleSettings? workflowScheduleSettings,
            List<JObject> steps,
            string name,
            string? workflowGroupId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            Dictionary<string, object?> metadata)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            Triggers = triggers;
            WorkflowEnrollmentSettings = workflowEnrollmentSettings;
            WorkflowScheduleSettings = workflowScheduleSettings;
            Steps = steps;
            Name = name;
            WorkflowGroupId = workflowGroupId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            Metadata = metadata;
        }
    }

    public class SaveWorkflowDraftOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public SaveWorkflowDraftOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<SaveWorkflowDraftOutput> F(SaveWorkflowDraftInput saveWorkflowDraftInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            saveWorkflowDraftInput.SleekflowStaffId,
            saveWorkflowDraftInput.SleekflowStaffTeamIds);

        string? manualEnrollmentSource = null;

        var eventMetadata = WorkflowMetadataUtils.TryExtractEventMetadataFromWorkflow(saveWorkflowDraftInput.Metadata);
        if (eventMetadata != null)
        {
            var postEnrollmentConfigs = await _needConfigService.GetPostWorkflowPublishedEnrollmentConfigsAsync(
                null,
                eventMetadata.Id);

            if (postEnrollmentConfigs != null && postEnrollmentConfigs.Any())
            {
                manualEnrollmentSource = "external_integration_loop_through";
            }
        }

        var workflow = await _workflowService.UpdateWorkflowAsync(
            saveWorkflowDraftInput.WorkflowId,
            saveWorkflowDraftInput.SleekflowCompanyId,
            saveWorkflowDraftInput.Triggers,
            saveWorkflowDraftInput.WorkflowEnrollmentSettings,
            saveWorkflowDraftInput.WorkflowScheduleSettings,
            saveWorkflowDraftInput.InternalSteps,
            saveWorkflowDraftInput.Name,
            saveWorkflowDraftInput.WorkflowGroupId,
            sleekflowStaff,
            saveWorkflowDraftInput.Metadata,
            manualEnrollmentSource);

        return new SaveWorkflowDraftOutput(new WorkflowDto(workflow));
    }
}