using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models;

public class SentMessage : IHasCreatedAt
{
    [JsonProperty("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonProperty("message_id")]
    public string? MessageId { get; set; }

    [JsonProperty("error_status_message")]
    public string? ErrorStatusMessage { get; set; }

    [JsonProperty("is_success")]
    public bool IsSuccess { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public SentMessage(
        string phoneNumber,
        string? messageId,
        string? errorStatusMessage,
        bool isSuccess,
        DateTimeOffset createdAt)
    {
        PhoneNumber = phoneNumber;
        MessageId = messageId;
        ErrorStatusMessage = errorStatusMessage;
        IsSuccess = isSuccess;
        CreatedAt = createdAt;
    }
}