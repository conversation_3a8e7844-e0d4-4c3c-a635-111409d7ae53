namespace Sleekflow.FlowHub.Silos;
public class WaitForSiloReadyHostedService : IHostedService
{
    private readonly ISiloReadinessIndicator _siloReadinessIndicator;
    private readonly ILogger<WaitForSiloReadyHostedService> _logger;

    public WaitForSiloReadyHostedService(
        ISiloReadinessIndicator siloReadinessIndicator,
        ILogger<WaitForSiloReadyHostedService> logger)
    {
        _siloReadinessIndicator = siloReadinessIndicator;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Waiting for Orleans Silo readiness signal before completing host startup...");
        // This await will block the completion of this StartAsync until the signal is received
        await _siloReadinessIndicator.WaitUntilReadyAsync(cancellationToken);
        _logger.LogInformation("Orleans Silo readiness signal received. Host startup can proceed.");
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        // No specific action needed on stop for this service
        return Task.CompletedTask;
    }
}