﻿using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.Authentications;

public interface IGoogleSheetsAuthenticationRepository : IRepository<GoogleSheetsAuthentication>
{
}

public class GoogleSheetsAuthenticationRepository
    : BaseRepository<GoogleSheetsAuthentication>,
        IGoogleSheetsAuthenticationRepository,
        ISingletonService
{
    public GoogleSheetsAuthenticationRepository(
        ILogger<BaseRepository<GoogleSheetsAuthentication>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}