using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.LineItems;
using Sleekflow.CommerceHub.Models.Products.Variants;

namespace Sleekflow.CommerceHub.Models.Orders;

public class OrderLineItem : SnapshottedLineItem
{
    [JsonConstructor]
    public OrderLineItem(
        string productVariantId,
        string productId,
        string? description,
        int quantity,
        Discount? lineItemDiscount,
        Dictionary<string, object?> metadata,
        ProductVariant productVariantSnapshot)
        : base(productVariantId, productId, description, quantity, lineItemDiscount, metadata, productVariantSnapshot)
    {
    }
}