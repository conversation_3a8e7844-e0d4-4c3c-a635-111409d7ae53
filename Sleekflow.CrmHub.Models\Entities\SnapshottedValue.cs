﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.JsonConfigs;

namespace Sleekflow.CrmHub.Models.Entities;

public class SnapshottedValue
{
    private DateTimeOffset _snapshotTime;

    public const string PropertyNameSnapshotTime = "t";
    public const string PropertyNameValue = "v";
    public const string PropertyNameValueCi = "i";

    [JsonConverter(typeof(MillisecondEpochConverter))]
    [JsonProperty(PropertyNameSnapshotTime)]
    public DateTimeOffset SnapshotTime
    {
        get => _snapshotTime;
        set => _snapshotTime = value.ToUniversalTime();
    }

    [JsonProperty(PropertyNameValue)]
    public object? Value { get; set; }

    [JsonProperty(PropertyNameValueCi)]
    public object? ValueCi
    {
        get
        {
            if (Value is string strValue)
            {
                return strValue.ToLowerInvariant();
            }
            else if (Value is JObject)
            {
                return new JObject();
            }

            return Value;
        }
    }

    [JsonProperty("snapshot_time")]
    public DateTimeOffset SnapshotTimeAlias
    {
        set => SnapshotTime = value;
    }

    [JsonProperty("value")]
    public object? ValueAlias
    {
        set => Value = value;
    }

    public static bool IsSnapshottedValue(object? o)
    {
        return
            o is JObject jObject
            && (jObject.ContainsKey(PropertyNameSnapshotTime) || jObject.ContainsKey("snapshot_time"))
            && (jObject.ContainsKey(PropertyNameValue) || jObject.ContainsKey("value"));
    }

    [JsonConstructor]
    public SnapshottedValue(DateTimeOffset snapshotTime, object? value)
    {
        SnapshotTime = snapshotTime;
        Value = value;
    }

    public static SnapshottedValue? FromObject(object? o)
    {
        return o switch
        {
            null => null,
            SnapshottedValue sv => sv,
            JObject jObject => jObject.ToObject<SnapshottedValue>(),
            _ => null
        };
    }
}