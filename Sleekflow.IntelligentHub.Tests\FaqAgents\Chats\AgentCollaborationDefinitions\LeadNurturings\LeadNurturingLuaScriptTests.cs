using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.Tests.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

/// <summary>
/// Tests specifically targeting the Lua script functionality in LeadNurturingAgentSessionManager
/// </summary>
[TestFixture]
public class LeadNurturingLuaScriptTests
{
    private IConnectionMultiplexer _connectionMultiplexer;
    private IDatabase _database;
    private TestCacheConfig _cacheConfig;
    private string _testPrefix;

    // Extract the Lua scripts from the original class for testing
    // Note: Ideally these would be extracted to constants in the original class for sharing
    private const string MarkActionsPerformedScript = @"
        local key = KEYS[1]
        local sessionJson = redis.call('GET', key)
        if not sessionJson then
            return 0
        end

        local updatedJson = string.gsub(sessionJson, '""HasPerformedActions"":false', '""HasPerformedActions"":true')
        redis.call('SET', key, updatedJson, 'EX', ARGV[1])
        return 1
    ";

    private const string CancelPreviousSessionsScript = @"
        local agentSessionIdsKey = KEYS[1]
        local currentAgentSessionId = ARGV[1]
        local expireSeconds = ARGV[2]
        local sessionPrefix = ARGV[3]
        local canceledCount = 0

        local agentSessionIds = redis.call('SMEMBERS', agentSessionIdsKey)
        for i, agentSessionId in ipairs(agentSessionIds) do
            if agentSessionId ~= currentAgentSessionId then
                local sessionKey = sessionPrefix .. agentSessionId
                local sessionJson = redis.call('GET', sessionKey)

                if sessionJson then
                    if not string.match(sessionJson, '""HasPerformedActions"":true') then
                        local updatedJson = string.gsub(sessionJson, '""ShouldCancel"":false', '""ShouldCancel"":true')
                        redis.call('SET', sessionKey, updatedJson, 'EX', expireSeconds)
                        canceledCount = canceledCount + 1
                    end
                end
            end
        end
        return canceledCount
    ";

    [SetUp]
    public async Task Setup()
    {
        // Use a unique prefix for each test run to avoid test interference
        _testPrefix = $"TEST-LUA-SCRIPTS-{Guid.NewGuid():N}";
        _cacheConfig = new TestCacheConfig(_testPrefix);
        _connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(_cacheConfig.RedisConnStr);
        _database = _connectionMultiplexer.GetDatabase();
    }

    [TearDown]
    public async Task TearDown()
    {
        // Clean up Redis keys after each test
        var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

        // Get and delete all keys with our test prefix
        var keys = server.Keys(pattern: $"{_testPrefix}*").ToArray();
        if (keys.Any())
        {
            await _database.KeyDeleteAsync(keys);
        }

        await _connectionMultiplexer.CloseAsync();
    }

    [Test]
    public async Task MarkActionsPerformedScript_WhenSessionExists_ShouldUpdateHasPerformedActions()
    {
        // Arrange
        var sessionId = Guid.NewGuid().ToString();
        var sessionKey = $"{_cacheConfig.CachePrefix}:agent-session:{sessionId}";

        var initialState = new AgentSessionState(
            "testCompany",
            "testObject",
            sessionId,
            hasPerformedActions: false,
            shouldCancel: false);

        var json = JsonConvert.SerializeObject(initialState);
        await _database.StringSetAsync(sessionKey, json, TimeSpan.FromMinutes(30));

        // Act
        var result = await _database.ScriptEvaluateAsync(
            MarkActionsPerformedScript,
            new RedisKey[]
            {
                sessionKey
            },
            new RedisValue[]
            {
                1800
            } // 30 minutes in seconds
        );

        // Assert
        Assert.That((long) result, Is.EqualTo(1));

        // Verify the session was updated
        var updatedJson = await _database.StringGetAsync(sessionKey);
        var updatedState = JsonConvert.DeserializeObject<AgentSessionState>(updatedJson);

        Assert.That(updatedState.HasPerformedActions, Is.True);
    }

    [Test]
    public async Task MarkActionsPerformedScript_WhenSessionDoesNotExist_ShouldReturnZero()
    {
        // Arrange
        var nonExistentSessionKey = $"{_cacheConfig.CachePrefix}:agent-session:nonexistent";

        // Act
        var result = await _database.ScriptEvaluateAsync(
            MarkActionsPerformedScript,
            new RedisKey[]
            {
                nonExistentSessionKey
            },
            new RedisValue[]
            {
                1800
            } // 30 minutes in seconds
        );

        // Assert
        Assert.That((long) result, Is.EqualTo(0));
    }

    [Test]
    public async Task CancelPreviousSessionsScript_ShouldOnlyCancelSessionsWithoutActions()
    {
        // Arrange
        var objectId = Guid.NewGuid().ToString();
        var agentSessionIdsKey = $"{_cacheConfig.CachePrefix}:all-agent-session-ids:{objectId}";

        // Create three sessions:
        // 1. Session without actions
        // 2. Session with actions
        // 3. Current session (should not be canceled)
        var session1Id = "session1";
        var session2Id = "session2";
        var session3Id = "session3";

        var session1Key = $"{_cacheConfig.CachePrefix}:agent-session:{session1Id}";
        var session2Key = $"{_cacheConfig.CachePrefix}:agent-session:{session2Id}";
        var session3Key = $"{_cacheConfig.CachePrefix}:agent-session:{session3Id}";

        var session1State = new AgentSessionState("company", objectId, session1Id, false, false);
        var session2State = new AgentSessionState("company", objectId, session2Id, true, false);
        var session3State = new AgentSessionState("company", objectId, session3Id, false, false);

        await _database.StringSetAsync(
            session1Key,
            JsonConvert.SerializeObject(session1State),
            TimeSpan.FromMinutes(30));
        await _database.StringSetAsync(
            session2Key,
            JsonConvert.SerializeObject(session2State),
            TimeSpan.FromMinutes(30));
        await _database.StringSetAsync(
            session3Key,
            JsonConvert.SerializeObject(session3State),
            TimeSpan.FromMinutes(30));

        // Add all sessions to the set
        await _database.SetAddAsync(
            agentSessionIdsKey,
            new RedisValue[]
            {
                session1Id,
                session2Id,
                session3Id
            });

        // Act - Run the script from the perspective of session3
        var result = await _database.ScriptEvaluateAsync(
            CancelPreviousSessionsScript,
            new RedisKey[]
            {
                agentSessionIdsKey
            },
            new RedisValue[]
            {
                session3Id, // Current session ID
                1800, // 30 minutes in seconds
                $"{_cacheConfig.CachePrefix}:agent-session:" // Session key prefix
            }
        );

        // Assert
        // Only session1 should be canceled (it has no actions)
        Assert.That((long) result, Is.EqualTo(1), "Should have canceled exactly 1 session");

        // Check the state of each session
        var session1Updated = JsonConvert.DeserializeObject<AgentSessionState>(
            await _database.StringGetAsync(session1Key));
        var session2Updated = JsonConvert.DeserializeObject<AgentSessionState>(
            await _database.StringGetAsync(session2Key));
        var session3Updated = JsonConvert.DeserializeObject<AgentSessionState>(
            await _database.StringGetAsync(session3Key));

        Assert.That(session1Updated.ShouldCancel, Is.True, "Session 1 should be canceled");
        Assert.That(session2Updated.ShouldCancel, Is.False, "Session 2 should not be canceled (has actions)");
        Assert.That(session3Updated.ShouldCancel, Is.False, "Session 3 should not be canceled (current session)");
    }

    [Test]
    public async Task CancelPreviousSessionsScript_WithNoSessions_ShouldReturnZero()
    {
        // Arrange
        var objectId = Guid.NewGuid().ToString();
        var agentSessionIdsKey = $"{_cacheConfig.CachePrefix}:all-agent-session-ids:{objectId}";
        var currentSessionId = "current-session";

        // Create an empty set
        await _database.KeyDeleteAsync(agentSessionIdsKey);
        await _database.SetAddAsync(agentSessionIdsKey, currentSessionId);

        // Act
        var result = await _database.ScriptEvaluateAsync(
            CancelPreviousSessionsScript,
            new RedisKey[]
            {
                agentSessionIdsKey
            },
            new RedisValue[]
            {
                currentSessionId,
                1800,
                $"{_cacheConfig.CachePrefix}:agent-session:"
            }
        );

        // Assert
        Assert.That((long) result, Is.EqualTo(0), "Should not have canceled any sessions");
    }

    [Test]
    public async Task CancelPreviousSessionsScript_WithMissingSessionData_ShouldHandleGracefully()
    {
        // Arrange
        var objectId = Guid.NewGuid().ToString();
        var agentSessionIdsKey = $"{_cacheConfig.CachePrefix}:all-agent-session-ids:{objectId}";

        // Add non-existent session IDs to the set
        var nonExistentSession = "non-existent";
        var currentSessionId = "current-session";

        await _database.SetAddAsync(
            agentSessionIdsKey,
            new RedisValue[]
            {
                nonExistentSession,
                currentSessionId
            });

        // Act
        var result = await _database.ScriptEvaluateAsync(
            CancelPreviousSessionsScript,
            new RedisKey[]
            {
                agentSessionIdsKey
            },
            new RedisValue[]
            {
                currentSessionId,
                1800,
                $"{_cacheConfig.CachePrefix}:agent-session:"
            }
        );

        // Assert - should handle gracefully and not cancel anything
        Assert.That((long) result, Is.EqualTo(0), "Should not have canceled any sessions");
    }

    // Test implementation of ICacheConfig
    private class TestCacheConfig : ICacheConfig
    {
        private readonly string _prefix;

        public TestCacheConfig(string prefix)
        {
            _prefix = prefix;
        }

        public string CachePrefix => _prefix;

        public string RedisConnStr =>
            "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False";
    }
}