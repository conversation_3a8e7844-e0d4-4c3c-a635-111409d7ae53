﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.CrmHub.Sequences;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Ids;
using Sleekflow.Locks;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.SchemafulObjects;

public interface ISchemafulObjectService
{
    Task<SchemafulObject> GetSchemafulObjectAsync(string id, string schemaId, string sleekflowCompanyId);

    Task<(List<SchemafulObject> SchemafulObjects, string? NextContinuationToken)> GetContinuationTokenizedSchemafulObjectsAsync(
        QueryDefinition queryDefinition,
        string? continuationToken,
        int limit = 10000);

    Task<List<Dictionary<string, object?>>> GetFilteredRawRecordsAsync(
        QueryDefinition queryDefinition,
        int limit = 10000);

    Task<SchemafulObject> CreateAndGetSchemafulObjectAsync(
        Schema schema,
        string sleekflowCompanyId,
        string primaryPropertyValue,
        Dictionary<string, object?> propertyValues,
        string sleekflowUserProfileId,
        string createdVia,
        AuditEntity.SleekflowStaff? createdBy = null);

    Task<SchemafulObject> PatchAndGetSchemafulObjectAsync(
        string id,
        Schema schema,
        string sleekflowCompanyId,
        Dictionary<string, object?> propertyValues,
        string sleekflowUserProfileId,
        string updatedVia,
        AuditEntity.SleekflowStaff? updatedBy = null);

    Task<SchemafulObject> PartialUpdatePropertyValuesAndGetSchemafulObjectAsync(
        string id,
        Schema schema,
        string sleekflowCompanyId,
        Dictionary<string, object?> receivedPropertyValues,
        string updatedVia,
        AuditEntity.SleekflowStaff? updatedBy = null);

    Task<bool> DeleteSchemafulObjectAsync(string id, string schemaId, string sleekflowCompanyId);

    Task<int> DeleteSchemafulObjectsAsync(List<string> ids, string schemaId, string sleekflowCompanyId);

    Task<bool> ProcessIndexPropertiesAsync(
        string id,
        string schemaId,
        string sleekflowCompanyId,
        List<string> indexedPropertyIds);

    Task<bool> ProcessDeleteSchemaPropertiesAsync(
        string id,
        string schemaId,
        string sleekflowCompanyId,
        List<string> propertyIds);

    Task<bool> ProcessDeleteSchemaPropertyOptionsAsync(
        string id,
        string schemaId,
        string sleekflowCompanyId,
        Dictionary<string, (string DataType, List<string> ToBeDeletedOptionId)> toBeDeletedOptionIdDictionary);

    Task<SchemafulObject> GetSchemafulObjectByPrimaryPropertyValueAsync(
        string schemaId,
        string sleekflowCompanyId,
        string primaryPropertyValue);

    Task<SchemafulObject> GetLatestSchemafulObjectByUserProfileIdAsync(
        string schemaId,
        string sleekflowCompanyId,
        string sleekflowUserProfileId);
}

public class SchemafulObjectService : ISchemafulObjectService, IScopedService
{
    private readonly ILogger<SchemafulObjectService> _logger;
    private readonly ISchemafulObjectRepository _schemafulObjectRepository;
    private readonly ILockService _lockService;
    private readonly IIdService _idService;
    private readonly ISequenceService _sequenceService;
    private readonly IBus _bus;
    private readonly ISchemafulObjectValidator _schemafulObjectValidator;
    private readonly IPropertyValueParser _propertyValueParser;
    private readonly ICrmHubConfigService _crmHubConfigService;
    private readonly IPropertyValueValidator _propertyValueValidator;

    public SchemafulObjectService(
        ILogger<SchemafulObjectService> logger,
        ISchemafulObjectRepository schemafulObjectRepository,
        ILockService lockService,
        IIdService idService,
        ISequenceService sequenceService,
        IBus bus,
        ISchemafulObjectValidator schemafulObjectValidator,
        IPropertyValueParser propertyValueParser,
        ICrmHubConfigService crmHubConfigService,
        IPropertyValueValidator propertyValueValidator)
    {
        _logger = logger;
        _schemafulObjectRepository = schemafulObjectRepository;
        _lockService = lockService;
        _idService = idService;
        _sequenceService = sequenceService;
        _bus = bus;
        _schemafulObjectValidator = schemafulObjectValidator;
        _propertyValueParser = propertyValueParser;
        _crmHubConfigService = crmHubConfigService;
        _propertyValueValidator = propertyValueValidator;
    }

    public async Task<SchemafulObject> GetSchemafulObjectAsync(string id, string schemaId, string sleekflowCompanyId)
    {
        return await _schemafulObjectRepository.GetAsync(
            id,
            GetPartitionKey(id, schemaId, sleekflowCompanyId));
    }

    public async Task<(List<SchemafulObject> SchemafulObjects, string? NextContinuationToken)> GetContinuationTokenizedSchemafulObjectsAsync(
        QueryDefinition queryDefinition,
        string? continuationToken,
        int limit = 10000)
    {
        return await _schemafulObjectRepository.GetContinuationTokenizedObjectsAsync(
            queryDefinition,
            continuationToken,
            limit);
    }

    public async Task<List<Dictionary<string, object?>>> GetFilteredRawRecordsAsync(
        QueryDefinition queryDefinition,
        int limit = 10000)
    {
        return await _schemafulObjectRepository.GetObjectsAsync<Dictionary<string, object?>>(
            queryDefinition,
            limit);
    }

    public async Task<SchemafulObject> CreateAndGetSchemafulObjectAsync(
        Schema schema,
        string sleekflowCompanyId,
        string primaryPropertyValue,
        Dictionary<string, object?> propertyValues,
        string sleekflowUserProfileId,
        string createdVia,
        AuditEntity.SleekflowStaff? createdBy = null)
    {
        // preprocess property values before validation
        propertyValues = _propertyValueParser.Parse(schema.Properties, propertyValues);

        // validations
        var crmHubConfig = await _crmHubConfigService.GetCrmHubConfigAsync(sleekflowCompanyId);
        _propertyValueValidator.Validate(schema.Properties, propertyValues, schema.Id, crmHubConfig);

        await _schemafulObjectValidator.ValidateIsExceedMaximumSchemafulObjectNumPerCompanyAsync(crmHubConfig, sleekflowCompanyId);

        await _schemafulObjectValidator.ValidatePrimaryPropertyValueAsync(
            schema,
            sleekflowCompanyId,
            primaryPropertyValue);

        await _schemafulObjectValidator.ValidateSleekflowUserProfileIdOccupancyAsync(
            schema,
            sleekflowCompanyId,
            sleekflowUserProfileId);

        // index properties that marked as searchable
        var indexedPropertyValues = propertyValues
            .Where(kvp =>
                schema.Properties.Exists(p =>
                    p.Id == kvp.Key && p.IsSearchable))
            .ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value);

        var id = _idService.GetId(SysTypeNames.SchemafulObject);

        // Generate primary property value
        if (schema.PrimaryProperty.PrimaryPropertyConfig.IsAutoGenerated)
        {
            primaryPropertyValue = schema.PrimaryProperty.PrimaryPropertyConfig.Sequential is null
                ? id
                : await GetNextPrimaryPropertyValueAsync(
                    schema.PrimaryProperty.PrimaryPropertyConfig.Sequential.SequencePrefix,
                    schema.Id);
        }

        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(CreateAndGetSchemafulObjectAsync),
                sleekflowCompanyId,
                schema.Id,
                primaryPropertyValue
            },
            TimeSpan.FromSeconds(20),
            TimeSpan.Zero);

        try
        {
            var schemafulObject = await _schemafulObjectRepository.CreateAndGetAsync(
                new SchemafulObject(
                    id,
                    schema.Id,
                    primaryPropertyValue,
                    sleekflowCompanyId,
                    sleekflowUserProfileId,
                    propertyValues,
                    indexedPropertyValues,
                    null,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    createdVia,
                    null,
                    createdBy),
                GetPartitionKey(id, schema.Id, sleekflowCompanyId));

            await ProcessPostSchemafulObjectCreatedOperationsAsync(schema, schemafulObject);

            return schemafulObject;
        }
        catch (SfValidationException)
        {
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when create schemaful object {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            throw;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<SchemafulObject> PatchAndGetSchemafulObjectAsync(
        string id,
        Schema schema,
        string sleekflowCompanyId,
        Dictionary<string, object?> propertyValues,
        string sleekflowUserProfileId,
        string updatedVia,
        AuditEntity.SleekflowStaff? updatedBy = null)
    {
        var @lock = await GetUpdateSchemafulObjectLockAsync(sleekflowCompanyId, schema.Id, id);

        try
        {
            var schemafulObject = await GetSchemafulObjectAsync(
                id,
                schema.Id,
                sleekflowCompanyId);

            await _schemafulObjectValidator.ValidateSleekflowUserProfileIdOccupancyAsync(
                schema,
                sleekflowCompanyId,
                sleekflowUserProfileId,
                id);

            // preprocess property values before validation
            propertyValues = _propertyValueParser.Parse(schema.Properties, propertyValues);

            // validate property values and process indexing
            var crmHubConfig = await _crmHubConfigService.GetCrmHubConfigAsync(sleekflowCompanyId);
            _propertyValueValidator.Validate(schema.Properties, propertyValues, schema.Id, crmHubConfig);

            var indexedPropertyValues = propertyValues
                .Where(kvp =>
                    schema.Properties.Exists(p =>
                        p.Id == kvp.Key && p.IsSearchable))
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value);

            var updatedSchemafulObject = await _schemafulObjectRepository.PatchAndGetSchemafulObjectAsync(
                id,
                GetPartitionKey(id, schema.Id, sleekflowCompanyId),
                propertyValues,
                indexedPropertyValues,
                sleekflowUserProfileId,
                updatedVia,
                updatedBy,
                schemafulObject.ETag);

            await ProcessPostSchemafulObjectUpdatedOperationsAsync(schema, schemafulObject, updatedSchemafulObject);

            return updatedSchemafulObject;
        }
        catch (SfValidationException)
        {
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when patching schemaful object {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            throw;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<SchemafulObject> PartialUpdatePropertyValuesAndGetSchemafulObjectAsync(
        string id,
        Schema schema,
        string sleekflowCompanyId,
        Dictionary<string, object?> receivedPropertyValues,
        string updatedVia,
        AuditEntity.SleekflowStaff? updatedBy = null)
    {
        var @lock = await GetUpdateSchemafulObjectLockAsync(sleekflowCompanyId, schema.Id, id);

        try
        {
            var schemafulObject = await GetSchemafulObjectAsync(
                id,
                schema.Id,
                sleekflowCompanyId);

            var sleekflowUserProfileId = schemafulObject.SleekflowUserProfileId;

            var propertyValuesForUpdate = new Dictionary<string, object?>(schemafulObject.PropertyValues);

            receivedPropertyValues = _propertyValueParser.Parse(schema.Properties, receivedPropertyValues);

            // override current property values
            foreach (var receivedPropertyValue in receivedPropertyValues)
            {
                propertyValuesForUpdate[receivedPropertyValue.Key] = receivedPropertyValue.Value;
            }

            // validate property values and process indexing
            var crmHubConfig = await _crmHubConfigService.GetCrmHubConfigAsync(sleekflowCompanyId);
            _propertyValueValidator.Validate(schema.Properties, propertyValuesForUpdate, schema.Id, crmHubConfig);

            var indexedPropertyValues = propertyValuesForUpdate
                .Where(kvp =>
                    schema.Properties.Exists(p =>
                        p.Id == kvp.Key && p.IsSearchable))
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value);

            var updatedSchemafulObject = await _schemafulObjectRepository.PatchAndGetSchemafulObjectAsync(
                id,
                GetPartitionKey(id, schema.Id, sleekflowCompanyId),
                propertyValuesForUpdate,
                indexedPropertyValues,
                sleekflowUserProfileId,
                updatedVia,
                updatedBy,
                schemafulObject.ETag);

            await ProcessPostSchemafulObjectUpdatedOperationsAsync(schema, schemafulObject, updatedSchemafulObject);

            return updatedSchemafulObject;
        }
        catch (SfValidationException)
        {
            throw;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when partial update schemaful object {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            throw;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<bool> DeleteSchemafulObjectAsync(string id, string schemaId, string sleekflowCompanyId)
    {
        try
        {
            _logger.LogInformation("Start deleting schemaful object {Id}", id);

            var deleteResult = await _schemafulObjectRepository.DeleteAsync(
                id,
                GetPartitionKey(id, schemaId, sleekflowCompanyId));

            if (deleteResult != 1)
            {
                throw new SfNotFoundObjectException(id);
            }

            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when deleting schemaful object {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            return false;
        }
    }

    public async Task<int> DeleteSchemafulObjectsAsync(List<string> ids, string schemaId, string sleekflowCompanyId)
    {
        var deletedCount = 0;

        foreach (var id in ids)
        {
            if (await DeleteSchemafulObjectAsync(id, schemaId, sleekflowCompanyId))
            {
                deletedCount++;
            }
        }

        return deletedCount;
    }

    public async Task<bool> ProcessIndexPropertiesAsync(
        string id,
        string schemaId,
        string sleekflowCompanyId,
        List<string> indexedPropertyIds)
    {
        try
        {
            var @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    sleekflowCompanyId,
                    schemaId,
                    id
                },
                TimeSpan.FromSeconds(20),
                TimeSpan.FromSeconds(20));

            try
            {
                var schemafulObject = await GetSchemafulObjectAsync(id, schemaId, sleekflowCompanyId);

                _logger.LogInformation(
                    "Start processing index schemaful object properties {Id}, [{PropertyIds}]",
                    id,
                    string.Join(",", indexedPropertyIds));

                var indexedPropertyValues = schemafulObject.PropertyValues
                    .Select(pv => indexedPropertyIds.Contains(pv.Key))
                    .ToList();

                var patchResult = await _schemafulObjectRepository.PatchAsync(
                    id,
                    GetPartitionKey(schemafulObject),
                    new List<PatchOperation>
                    {
                        PatchVariable.Replace(SchemafulObject.PropertyNameIndexedPropertyValues, indexedPropertyValues)
                    },
                    eTag: schemafulObject.ETag);

                if (patchResult != 1)
                {
                    throw new Exception("Unable to patch.");
                }
            }
            finally
            {
                await _lockService.ReleaseAsync(@lock);
            }

            return true;
        }
        catch (Exception e)
        {
            _logger.LogWarning(
                e,
                "Errors when indexing schemaful object {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            return false;
        }
    }

    public async Task<bool> ProcessDeleteSchemaPropertiesAsync(
        string id,
        string schemaId,
        string sleekflowCompanyId,
        List<string> propertyIds)
    {
        try
        {
            var @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    sleekflowCompanyId,
                    schemaId,
                    id
                },
                TimeSpan.FromSeconds(20),
                TimeSpan.FromSeconds(20));

            try
            {
                var schemafulObject = await GetSchemafulObjectAsync(id, schemaId, sleekflowCompanyId);

                _logger.LogInformation(
                    "Start processing delete schemaful object properties {Id}, [{PropertyIds}]",
                    id,
                    string.Join(",", propertyIds));

                var patchOperations = new List<PatchOperation>();
                propertyIds.ForEach(
                    pid => patchOperations.AddRange(
                        new List<PatchOperation>
                        {
                            PatchOperation.Remove($"/{SchemafulObject.PropertyNamePropertyValues}/{pid}"),
                            PatchOperation.Remove($"/{SchemafulObject.PropertyNameIndexedPropertyValues}/{pid}")
                        }));

                var patchResult = await _schemafulObjectRepository.PatchAsync(
                    id,
                    GetPartitionKey(id, schemaId, sleekflowCompanyId),
                    patchOperations,
                    eTag: schemafulObject.ETag);

                if (patchResult != 1)
                {
                    throw new Exception("Unable to patch.");
                }
            }
            finally
            {
                await _lockService.ReleaseAsync(@lock);
            }

            return true;
        }
        catch (Exception e)
        {
            _logger.LogWarning(
                e,
                "Errors when processing delete schemaful object properties {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            return false;
        }
    }

    public async Task<bool> ProcessDeleteSchemaPropertyOptionsAsync(
        string id,
        string schemaId,
        string sleekflowCompanyId,
        Dictionary<string, (string DataType, List<string> ToBeDeletedOptionId)> toBeDeletedOptionIdDictionary)
    {
        try
        {
            var @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    sleekflowCompanyId,
                    schemaId,
                    id
                },
                TimeSpan.FromSeconds(20),
                TimeSpan.FromSeconds(20));

            try
            {
                var schemafulObject = await GetSchemafulObjectAsync(id, schemaId, sleekflowCompanyId);

                _logger.LogInformation(
                    "Start processing delete schemaful object property options {Id}, [{PropertyOptionIds}]",
                    id,
                    JsonConvert.SerializeObject(toBeDeletedOptionIdDictionary));

                var patchOperations = new List<PatchOperation>();

                foreach (var kvp in toBeDeletedOptionIdDictionary)
                {
                    if (kvp.Value.DataType == SchemaPropertyDataTypes.SingleChoice &&
                        kvp.Value.ToBeDeletedOptionId.Contains(schemafulObject.PropertyValues[kvp.Key]))
                    {
                        patchOperations.AddRange(
                            new List<PatchOperation>
                            {
                                PatchOperation.Replace<string?>(
                                    $"/{SchemafulObject.PropertyNamePropertyValues}/{kvp.Key}",
                                    null),
                                PatchOperation.Replace<string?>(
                                    $"/{SchemafulObject.PropertyNameIndexedPropertyValues}/{kvp.Key}",
                                    null)
                            });
                    }
                    else if (kvp.Value.DataType == SchemaPropertyDataTypes.MultipleChoice &&
                             schemafulObject.PropertyValues[kvp.Key] != null)
                    {
                        var propertyValue =
                            JsonConvert.DeserializeObject<List<string>>(
                                schemafulObject.PropertyValues[kvp.Key]!.ToString()!);

                        if (propertyValue == null)
                        {
                            continue;
                        }

                        propertyValue = propertyValue.Except(kvp.Value.ToBeDeletedOptionId).ToList();
                        kvp.Value.ToBeDeletedOptionId.ForEach(
                            _ => patchOperations.AddRange(
                                new List<PatchOperation>
                                {
                                    PatchOperation.Replace(
                                        $"/{SchemafulObject.PropertyNamePropertyValues}/{kvp.Key}",
                                        propertyValue.Any() ? propertyValue : null),
                                    PatchOperation.Replace(
                                        $"/{SchemafulObject.PropertyNameIndexedPropertyValues}/{kvp.Key}",
                                        propertyValue.Any() ? propertyValue : null)
                                }));
                    }
                }

                if (patchOperations.Any())
                {
                    var patchResult = await _schemafulObjectRepository.PatchAsync(
                        id,
                        GetPartitionKey(id, schemaId, sleekflowCompanyId),
                        patchOperations,
                        eTag: schemafulObject.ETag);

                    if (patchResult != 1)
                    {
                        throw new SfNotFoundObjectException(id);
                    }
                }
            }
            finally
            {
                await _lockService.ReleaseAsync(@lock);
            }

            return true;
        }
        catch (Exception e)
        {
            _logger.LogWarning(
                e,
                "Errors when processing delete schemaful object property options {Id}. Company: {SleekflowCompanyId}, error message: {Message}",
                id,
                sleekflowCompanyId,
                e.Message);

            return false;
        }
    }

    public async Task<SchemafulObject> GetSchemafulObjectByPrimaryPropertyValueAsync(
        string schemaId,
        string sleekflowCompanyId,
        string primaryPropertyValue)
    {
        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            SchemafulObject.PropertyNamePrimaryPropertyValue,
                            "=",
                            primaryPropertyValue,
                            false)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            sleekflowCompanyId,
            schemaId,
            true);

        var filterResult = await GetContinuationTokenizedSchemafulObjectsAsync(queryDefinition, null, 2);

        return filterResult.SchemafulObjects.Single();
    }

    public async Task<SchemafulObject> GetLatestSchemafulObjectByUserProfileIdAsync(
        string schemaId,
        string sleekflowCompanyId,
        string sleekflowUserProfileId)
    {
        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                            "=",
                            sleekflowUserProfileId,
                            false)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>
            {
                new SchemafulObjectQueryBuilder.SchemafulObjectSort(IHasCreatedAt.PropertyNameCreatedAt, "desc", false)
            },
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            sleekflowCompanyId,
            schemaId,
            true);

        var filterResult = await GetContinuationTokenizedSchemafulObjectsAsync(queryDefinition, null, 2);

        return filterResult.SchemafulObjects[0];
    }

    private static PartitionKey GetPartitionKey(SchemafulObject schemafulObject)
    {
        return new PartitionKeyBuilder()
            .Add(schemafulObject.SleekflowCompanyId)
            .Add(schemafulObject.SchemaId)
            .Add(schemafulObject.Id)
            .Build();
    }

    private static PartitionKey GetPartitionKey(string id, string schemaId, string sleekflowCompanyId)
    {
        return new PartitionKeyBuilder()
            .Add(sleekflowCompanyId)
            .Add(schemaId)
            .Add(id)
            .Build();
    }

    private async Task<string> GetNextPrimaryPropertyValueAsync(string prefix, string schemaId)
    {
        var nextSequenceNum = (await _sequenceService.GetNextValueAsync(schemaId)).ToString();

        // nextSequenceNum < 10M: padding zeros to 7 digits
        // e.g. [0000011]
        //
        // nextSequenceNum > 10M: no paddings
        // e.g. [12345678]
        var paddedSequenceNum = nextSequenceNum.Length > 7
            ? nextSequenceNum
            : nextSequenceNum.PadLeft(7, '0');

        return prefix + paddedSequenceNum;
    }

    private async Task ProcessPostSchemafulObjectCreatedOperationsAsync(Schema schema, SchemafulObject schemafulObject)
    {
        // to include primary property value
        // DEVS-6786 requested to send Option.Value
        // DEVS-7044 requested to include Property.UniqueName into property values
        var fullPropertyValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
            schema.Properties,
            schemafulObject.PropertyValues,
            _logger);
        fullPropertyValues.Add(schema.PrimaryProperty.Id, schemafulObject.PrimaryPropertyValue);
        fullPropertyValues.Add(schema.PrimaryProperty.UniqueName, schemafulObject.PrimaryPropertyValue);

        await _bus.Publish(
            new SchemafulObjectCreatedEventRequest(
                schemafulObject.SleekflowCompanyId,
                schemafulObject.Id,
                schemafulObject.SchemaId,
                schemafulObject.PrimaryPropertyValue,
                schemafulObject.SleekflowUserProfileId,
                fullPropertyValues,
                schemafulObject.CreatedAt,
                schemafulObject.CreatedBy?.SleekflowStaffId,
                schemafulObject.CreatedBy?.SleekflowStaffTeamIds));
    }

    private async Task ProcessPostSchemafulObjectUpdatedOperationsAsync(
        Schema schema,
        SchemafulObject preUpdatedSchemafulObject,
        SchemafulObject postUpdatedSchemafulObject)
    {
        // to include primary property value
        // DEVS-6786 requested to send Option.Value
        // DEVS-7044 requested to include Property.UniqueName into property values
        var primaryPropertyValue = preUpdatedSchemafulObject.PrimaryPropertyValue;

        var preUpdatedFullPropertyValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
            schema.Properties,
            preUpdatedSchemafulObject.PropertyValues,
            _logger);
        preUpdatedFullPropertyValues.Add(schema.PrimaryProperty.Id, primaryPropertyValue);
        preUpdatedFullPropertyValues.Add(schema.PrimaryProperty.UniqueName, primaryPropertyValue);

        var postUpdatedFullPropertyValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
            schema.Properties,
            postUpdatedSchemafulObject.PropertyValues,
            _logger);
        postUpdatedFullPropertyValues.Add(schema.PrimaryProperty.Id, primaryPropertyValue);
        postUpdatedFullPropertyValues.Add(schema.PrimaryProperty.UniqueName, primaryPropertyValue);

        await _bus.Publish(
            new SchemafulObjectUpdatedEventRequest(
                preUpdatedSchemafulObject.SleekflowCompanyId,
                preUpdatedSchemafulObject.Id,
                preUpdatedSchemafulObject.SchemaId,
                primaryPropertyValue,
                preUpdatedSchemafulObject.SleekflowUserProfileId,
                postUpdatedSchemafulObject.SleekflowUserProfileId,
                preUpdatedFullPropertyValues,
                postUpdatedFullPropertyValues,
                postUpdatedSchemafulObject.UpdatedAt,
                postUpdatedSchemafulObject.UpdatedBy?.SleekflowStaffId,
                postUpdatedSchemafulObject.UpdatedBy?.SleekflowStaffTeamIds));
    }

    private async Task<Lock> GetUpdateSchemafulObjectLockAsync(string sleekflowCompanyId, string schemaId, string schemafulObjectId)
    {
        var @lock = await _lockService.LockAsync(
            new[]
            {
                sleekflowCompanyId,
                schemaId,
                schemafulObjectId
            },
            TimeSpan.FromSeconds(20));

        if (@lock is null)
        {
            throw new SfCustomObjectConcurrentCallException();
        }

        return @lock;
    }
}