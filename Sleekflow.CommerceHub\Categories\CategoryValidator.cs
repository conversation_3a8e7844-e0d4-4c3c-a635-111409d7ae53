using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Descriptions;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.Utils;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Categories;

public interface ICategoryValidator
{
    Task AssertValidCategoryPropertiesAsync(
        string sleekflowCompanyId,
        string storeId,
        IReadOnlyCollection<Multilingual> names,
        IReadOnlyCollection<Description> descriptions);
}

public class CategoryValidator : ICategoryValidator, IScopedService
{
    private readonly IDescriptionValidator _descriptionValidator;
    private readonly IStoreService _storeService;

    public CategoryValidator(
        IDescriptionValidator descriptionValidator,
        IStoreService storeService)
    {
        _descriptionValidator = descriptionValidator;
        _storeService = storeService;
    }

    public async Task AssertValidCategoryPropertiesAsync(
        string sleekflowCompanyId,
        string storeId,
        IReadOnlyCollection<Multilingual> names,
        IReadOnlyCollection<Description> descriptions)
    {
        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);
        if (store is null)
        {
            throw new SfNotFoundObjectException(storeId, sleekflowCompanyId);
        }

        if (!MultilingualUtils.AreValidMultilinguals(names, store))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        $"Unsupported language found in names {JsonConvert.SerializeObject(names)}")
                });
        }

        await _descriptionValidator.AssertValidDescriptionsAsync(
            descriptions,
            store.Languages.Select(l => l.LanguageIsoCode).ToHashSet());
    }
}