/**
 * Handler that will be called during the execution of a PostChangePassword flow.
 *
 * @param {Event} event - Details about the user and the context in which the change password is happening.
 * @param {PostChangePasswordAPI} api - Methods and utilities to help change the behavior after a user changes their password.
 */
exports.onExecutePostChangePassword = async (event, api) => {
  const domain = event.secrets.domain;
  const clientId = event.secrets.client_id;
  const clientSecret = event.secrets.client_secret;
  const postChangePasswordWebhookUrl = event.secrets.post_change_password_webhook;
  const issuer = event.secrets.issuer;
  const audience = event.secrets.audience;

  const axios = require("axios").default;

  const sendPostChangePasswordWebhook = async (user) => {
    const jwt = require('jwt-encode');

    const data = {
      iss: issuer,
      aud: audience,
      domain: domain,
      client_id: clientId
    }

    const tokenSecret = issuer + audience + "+wsadz4gI_3DUXI8P";
    const token = jwt(data, tokenSecret);

    const options = {
      method: 'POST',
      url: postChangePasswordWebhookUrl,
      params: {connection: event.connection.strategy, token: token},
      headers: {'content-type': 'application/json'},
      data: user
    };

    const response = await axios.request(options);

    return response;
  };

  const res = await sendPostChangePasswordWebhook(event.user);
  if (res.status !== 200) {
    throw res
  }
};
