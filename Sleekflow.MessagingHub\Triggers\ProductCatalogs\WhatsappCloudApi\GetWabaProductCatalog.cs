using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class GetWabaProductCatalog
    : ITrigger<GetWabaProductCatalog.GetWabaProductCatalogInput, GetWabaProductCatalog.GetWabaProductCatalogOutput>
{
    private readonly ILogger<GetWabaProductCatalog> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public GetWabaProductCatalog(ILogger<GetWabaProductCatalog> logger, IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class GetWabaProductCatalogInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [JsonConstructor]
        public GetWabaProductCatalogInput(string sleekflowCompanyId, string wabaId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
        }
    }

    public class GetWabaProductCatalogOutput
    {
        [JsonProperty("waba_product_catalog")]
        public WabaProductCatalogDto WabaProductCatalog { get; set; }

        [JsonConstructor]
        public GetWabaProductCatalogOutput(WabaProductCatalogDto wabaProductCatalog)
        {
            WabaProductCatalog = wabaProductCatalog;
        }
    }

    public async Task<GetWabaProductCatalogOutput> F(GetWabaProductCatalogInput getWabaProductCatalogInput)
    {
        _logger.LogInformation("getting waba product catalog for waba id {WabaId}", getWabaProductCatalogInput.WabaId);

        var wabaProductCatalog = await _productCatalogService.GetWabaProductCatalogAsync(
            getWabaProductCatalogInput.SleekflowCompanyId,
            getWabaProductCatalogInput.WabaId);

        if (wabaProductCatalog == null)
        {
            _logger.LogWarning("unable to get any product catalog for {WabaId}", getWabaProductCatalogInput.WabaId);

            throw new SfInternalErrorException($"unable to get waba id {getWabaProductCatalogInput.WabaId} connected product catalog");
        }

        return new GetWabaProductCatalogOutput(new WabaProductCatalogDto(wabaProductCatalog));
    }
}