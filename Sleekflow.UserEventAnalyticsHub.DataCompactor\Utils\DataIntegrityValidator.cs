using Microsoft.Extensions.Logging;
using Npgsql;
using DuckDB.NET.Data;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;

public class DataIntegrityValidator
{
    private readonly ILogger<DataIntegrityValidator> _logger;

    public DataIntegrityValidator(ILogger<DataIntegrityValidator> logger)
    {
        _logger = logger;
    }

    public async Task<bool> ValidateRowCountAsync(string postgresConnectionString, string tableName, List<string> parquetFilePaths)
    {
        try
        {
            var postgresCount = await GetPostgresRowCountAsync(postgresConnectionString, tableName);
            var parquetCount = await GetParquetRowCountAsync(parquetFilePaths);

            var isValid = postgresCount == parquetCount;

            if (isValid)
            {
                _logger.LogInformation("Data integrity validation passed. PostgreSQL: {PostgresCount}, Parquet: {ParquetCount}",
                    postgresCount, parquetCount);
            }
            else
            {
                _logger.LogWarning("Data integrity validation failed. PostgreSQL: {PostgresCount}, Parquet: {ParquetCount}",
                    postgresCount, parquetCount);
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during data integrity validation for table {TableName}", tableName);
            return false;
        }
    }

    public async Task<bool> ValidateSampleDataAsync(string postgresConnectionString, string tableName, List<string> parquetFilePaths, int sampleSize = 100)
    {
        try
        {
            var postgresRecords = await GetPostgresSampleAsync(postgresConnectionString, tableName, sampleSize);
            var parquetRecords = await GetParquetSampleAsync(parquetFilePaths, sampleSize);

            // Simple validation - check if we have overlapping records
            var commonIds = postgresRecords.Intersect(parquetRecords).Count();
            var validationThreshold = Math.Min(postgresRecords.Count, parquetRecords.Count) * 0.8; // 80% overlap expected

            var isValid = commonIds >= validationThreshold;

            if (isValid)
            {
                _logger.LogInformation("Sample data validation passed. Common IDs: {CommonIds}, Threshold: {Threshold}",
                    commonIds, validationThreshold);
            }
            else
            {
                _logger.LogWarning("Sample data validation failed. Common IDs: {CommonIds}, Threshold: {Threshold}",
                    commonIds, validationThreshold);
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during sample data validation for table {TableName}", tableName);
            return false;
        }
    }

    private async Task<long> GetPostgresRowCountAsync(string connectionString, string tableName)
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();

        using var command = new NpgsqlCommand($"SELECT COUNT(*) FROM {tableName}", connection);
        var result = await command.ExecuteScalarAsync();
        return Convert.ToInt64(result);
    }

    private async Task<long> GetParquetRowCountAsync(List<string> parquetFilePaths)
    {
        await using var connection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
        await connection.OpenAsync();

        var totalCount = 0L;

        foreach (var filePath in parquetFilePaths)
        {
            using var command = new DuckDBCommand($"SELECT COUNT(*) FROM parquet_scan('{filePath}')", connection);
            var result = await command.ExecuteScalarAsync();
            totalCount += Convert.ToInt64(result);
        }

        return totalCount;
    }

    private async Task<List<string>> GetPostgresSampleAsync(string connectionString, string tableName, int sampleSize)
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();

        using var command = new NpgsqlCommand($"SELECT id FROM {tableName} ORDER BY RANDOM() LIMIT {sampleSize}", connection);
        using var reader = await command.ExecuteReaderAsync();

        var ids = new List<string>();
        while (await reader.ReadAsync())
        {
            ids.Add(reader.GetString(0));
        }

        return ids;
    }

    private async Task<List<string>> GetParquetSampleAsync(List<string> parquetFilePaths, int sampleSize)
    {
        await using var connection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
        await connection.OpenAsync();

        var unionQuery = string.Join(" UNION ALL ", parquetFilePaths.Select(path => $"SELECT id FROM parquet_scan('{path}')"));
        var query = $"SELECT id FROM ({unionQuery}) ORDER BY RANDOM() LIMIT {sampleSize}";

        using var command = new DuckDBCommand(query, connection);
        using var reader = await command.ExecuteReaderAsync();

        var ids = new List<string>();
        while (await reader.ReadAsync())
        {
            ids.Add(reader.GetString(0));
        }

        return ids;
    }
}