﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class MigrateUserProfileAuditLogToAuditHub : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;

    public MigrateUserProfileAuditLogToAuditHub(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
    }

    public class MigrateUserProfileAuditLogToAuditHubInput
    {
        [JsonConstructor]
        public MigrateUserProfileAuditLogToAuditHubInput(List<MigrateLog> migrateLogs)
        {
            MigrateLogs = migrateLogs;
        }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("migrate_logs")]
        public List<MigrateLog> MigrateLogs { get; set; }
    }

    public class MigrateLog : IHasSleekflowCompanyId, IHasSleekflowUserProfileId, IHasCreatedAt
    {
        [JsonConstructor]
        public MigrateLog(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            Dictionary<string, object?> data,
            DateTimeOffset createdAt,
            string type)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
            CreatedAt = createdAt;
            Type = type;
        }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [JsonProperty("data")]
        public Dictionary<string, object?> Data { get; set; }

        [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
        public DateTimeOffset CreatedAt { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }
    }

    public class MigrateUserProfileAuditLogToAuditHubOutput
    {
    }

    public async Task<MigrateUserProfileAuditLogToAuditHubOutput> F(
        MigrateUserProfileAuditLogToAuditHubInput migrateUserProfileAuditLogToAuditHubInput)
    {
        await Parallel.ForEachAsync(
            migrateUserProfileAuditLogToAuditHubInput.MigrateLogs,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (migrateLog, cancellationToken) =>
            {
                await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
                    new UserProfileAuditLog(
                        _idService.GetId("UserProfileAuditLog"),
                        migrateLog.SleekflowCompanyId,
                        migrateLog.SleekflowStaffId,
                        migrateLog.SleekflowUserProfileId,
                        migrateLog.Type,
                        migrateLog.AuditLogText,
                        migrateLog.Data,
                        migrateLog.CreatedAt,
                        null),
                    cancellationToken);
            });

        return new MigrateUserProfileAuditLogToAuditHubOutput();
    }
}