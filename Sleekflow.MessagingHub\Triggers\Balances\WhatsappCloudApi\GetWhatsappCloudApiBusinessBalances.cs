using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetWhatsappCloudApiBusinessBalances
    : ITrigger<
        GetWhatsappCloudApiBusinessBalances.GetWhatsappCloudApiBusinessBalancesInput,
        GetWhatsappCloudApiBusinessBalances.GetWhatsappCloudApiBusinessBalancesOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetWhatsappCloudApiBusinessBalances> _logger;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;

    public GetWhatsappCloudApiBusinessBalances(
        IWabaService wabaService,
        ILogger<GetWhatsappCloudApiBusinessBalances> logger,
        IBusinessBalanceService businessBalanceService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
    }

    public class GetWhatsappCloudApiBusinessBalancesInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalancesInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWhatsappCloudApiBusinessBalancesOutput
    {
        [JsonProperty("business_balances")]
        public List<BusinessBalanceDto> BusinessBalances { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalancesOutput(List<BusinessBalanceDto> businessBalances)
        {
            BusinessBalances = businessBalances;
        }
    }

    public async Task<GetWhatsappCloudApiBusinessBalancesOutput> F(
        GetWhatsappCloudApiBusinessBalancesInput getWhatsappCloudApiBusinessBalancesInput)
    {
        var sleekflowCompanyId = getWhatsappCloudApiBusinessBalancesInput.SleekflowCompanyId;

        var wabas = await _wabaService.GetWabasAsync(sleekflowCompanyId);

        if (wabas.Count == 0)
        {
            return new GetWhatsappCloudApiBusinessBalancesOutput(
                new List<BusinessBalanceDto>());
        }

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w => w.FacebookBusinessId != null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        var businessBalances = new List<BusinessBalanceDto>();
        foreach (var (facebookBusinessId, relatedWabas) in facebookBusinessIdToWabasDict)
        {
            var businessBalance =
                await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

            if (businessBalance is not null)
            {
                var unCalculatedCreditTransferTransactionLogs =
                    await _wabaLevelCreditManagementService.GetUnCalculatedCreditTransferTransactionLogsAsync(
                        businessBalance);

                businessBalances.Add(
                    new BusinessBalanceDto(businessBalance, relatedWabas, unCalculatedCreditTransferTransactionLogs));
            }
            else
            {
                _logger.LogWarning(
                    "Unable to locate BusinessBalance with {FacebookBusinessId}. {RelatedWabas}",
                    facebookBusinessId,
                    JsonConvert.SerializeObject(relatedWabas));
            }
        }

        return new GetWhatsappCloudApiBusinessBalancesOutput(
            businessBalances);
    }
}