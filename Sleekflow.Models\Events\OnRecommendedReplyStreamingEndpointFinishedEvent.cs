using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

public class OnRecommendedReplyStreamingEndpointFinishedEvent
{
    [JsonProperty("correlation_id")]
    public Guid CorrelationId { get; set; }

    [JsonProperty("session_id")]
    public string SessionId { get; set; }

    [JsonProperty("final_sequence_number")]
    public int FinalSequenceNumber { get; set; }

    [JsonProperty("client_request_id")]
    public string ClientRequestId { get; set; }

    [JsonConstructor]
    public OnRecommendedReplyStreamingEndpointFinishedEvent(
        Guid correlationId,
        string sessionId,
        int finalSequenceNumber,
        string clientRequestId)
    {
        CorrelationId = correlationId;
        SessionId = sessionId;
        FinalSequenceNumber = finalSequenceNumber;
        ClientRequestId = clientRequestId;
    }
}