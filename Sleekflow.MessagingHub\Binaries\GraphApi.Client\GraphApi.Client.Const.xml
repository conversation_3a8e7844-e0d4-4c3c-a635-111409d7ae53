<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GraphApi.Client.Const</name>
    </assembly>
    <members>
        <member name="T:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst">
            <summary>
            Data localization region constants
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Australia">
            <summary>
            Australia
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Indonesia">
            <summary>
            Indonesia
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.India">
            <summary>
            India
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Japan">
            <summary>
            Japan
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Singapore">
            <summary>
            Singapore
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.SouthKorea">
            <summary>
            South Korea
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Germany">
            <summary>
            EU (Germany)
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Switzerland">
            <summary>
            Switzerland
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.UnitedKingdom">
            <summary>
            United Kingdom
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Brazil">
            <summary>
            Brazil
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Bahrain">
            <summary>
            Bahrain
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.SouthAfrica">
            <summary>
            South Africa
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.UnitedArabEmirates">
            <summary>
            United Arab Emirates
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.DataLocalizationRegionConst.Canada">
            <summary>
            Canada
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.Flows.FlowComponentImageScaleType.Cover">
            <summary>
            Image is clipped to fit the image container.
            If there is no height value (which is the default), the image will be displayed to its full width with its original aspect ratio.
            If the height value is set, the image is cropped within the fixed height. Depending on the image whether it is portrait or landscape, image is clipped vertically or horizontally.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.Flows.FlowComponentImageScaleType.Contain">
            <summary>
            Image is contained within the image container with the original aspect ratio.
            If there is no height value (which is the default), the image will be displayed to its full width with its original aspect ratio.
            If the height value is set, the image is contained in the image container with the fixed height and the original aspect ratio.
            Refer to https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson/components#img for more info.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.Flows.FlowComponentPhotoSource.CameraGallery">
            <summary>
            User can select from gallery or take a photo
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.Flows.FlowComponentPhotoSource.Gallery">
            <summary>
            User can select only from gallery
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.Flows.FlowComponentPhotoSource.Camera">
            <summary>
            User can only take a photo
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.Flows.FlowPayloadFieldSchemaDataType.DatePicker">
            <summary>
            Before Json Version 5.0: long timestamp in milliseconds, as long data type, for setting and retrieving date values.
            On Or After Json Version 5.0: formatted date string in the format "YYYY-MM-DD", such as "2024-10-21", for setting and retrieving date values.
            </summary>
        </member>
        <member name="T:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiComponentTypeConst">
            <summary>
            must be one of {BODY, BUTTON, HEADER, CAROUSEL}
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.delivered">
            <summary>
            A message sent by your business was delivered to the user's device.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.read">
            <summary>
            A message sent by your business was read by the user. read notifications are only available for users that have read receipts enabled. For users that do not have it enabled, you only receive the delivered notification.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.sent">
            <summary>
            A message sent by your business is in transit within our systems.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.failed">
            <summary>
            A message sent by your business failed to send. A reason for the failure will be included in the callback.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.deleted">
            <summary>
            A message send by the user was deleted by the user. Upon receiving this notification, you should ensure that the message is deleted from your system if it was downloaded from the server.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.warning">
            <summary>
            A message your business sent contains an item in a catalog that is not available or does not exist.
            </summary>
        </member>
    </members>
</doc>
