using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using Newtonsoft.Json;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IReplyFacebookInstagramPostCommentStepExecutor : IStepExecutor
{
}

public class ReplyFacebookInstagramPostCommentStepExecutor
    : GeneralStepExecutor<CallStep<ReplyFacebookInstagramPostCommentStepArgs>>,
        IReplyFacebookInstagramPostCommentStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly HttpClient _httpClient;
    private readonly IStateAggregator _stateAggregator;
    private readonly ICoreCommander _coreCommander;

    public ReplyFacebookInstagramPostCommentStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IHttpClientFactory httpClientFactory,
        IStateAggregator stateAggregator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _stateAggregator = stateAggregator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var pageId =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(
                              state,
                              "{{ trigger_event_body.page_id }}")
                          ?? throw new InvalidOperationException("No page id found"));

            var commentId =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(
                              state,
                              "{{ trigger_event_body.comment_id }}")
                          ?? throw new InvalidOperationException("No comment id found"));

            var channel =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(
                              state,
                              "{{ trigger_event_body.channel }}")
                          ?? throw new InvalidOperationException("No channel found"));

            var commentBody =
                (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.CommentBodyExpr) ?? callStep.Args.CommentBodyExpr);

            // var shouldLikeComment =
            //     (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ShouldLikeCommentExpr)
            //               ?? callStep.Args.ShouldLikeCommentExpr);

            var pageAccessToken = string.Empty;

            if (channel == ChannelTypes.Facebook)
            {
                var getFacebookPageAccessToken = JsonConvert.DeserializeObject<GetFacebookPageAccessTokenOutput>(
                    await _coreCommander.ExecuteAsync(
                        state.Origin,
                        "GetFacebookPageAccessToken",
                        GetFacebookPageAccessTokenArgs(pageId, state)));

                if (getFacebookPageAccessToken == null || string.IsNullOrEmpty(getFacebookPageAccessToken.FacebookPageAccessToken))
                {
                    throw new SfFlowHubUserFriendlyException(
                        UserFriendlyErrorCodes.InternalError,
                        $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id} because of unable to get page {pageId} access token");
                }

                pageAccessToken = getFacebookPageAccessToken.FacebookPageAccessToken;
            }
            else if (channel == ChannelTypes.Instagram)
            {
                var getInstagramPageAccessToken = JsonConvert.DeserializeObject<GetInstagramPageAccessTokenOutput>(
                    await _coreCommander.ExecuteAsync(
                        state.Origin,
                        "GetInstagramPageAccessToken",
                        GetInstagramPageAccessTokenArgs(pageId, state)));

                if (getInstagramPageAccessToken == null || string.IsNullOrEmpty(getInstagramPageAccessToken.InstagramPageAccessToken))
                {
                    throw new SfFlowHubUserFriendlyException(
                        UserFriendlyErrorCodes.InternalError,
                        $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id} because of unable to get page {pageId} access token");
                }

                pageAccessToken = getInstagramPageAccessToken.InstagramPageAccessToken;
            }

            var pageCommentClient = new FacebookInstagramPageCommentClient(pageAccessToken, _httpClient);

            var replyToCommentResponse = await pageCommentClient.PublishCommentAsync(commentId, commentBody, channel);

            // if (bool.TryParse(shouldLikeComment, out var shouldLikeCommentBool) && shouldLikeCommentBool)
            // {
            //     await pageCommentClient.LikeObjectAsync(commentId);
            // }

            var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                JsonConvert.SerializeObject(replyToCommentResponse));

            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.MetaGraphApiClientError,
                    $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                    g);
            }

            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private GetFacebookPageAccessTokenInput GetFacebookPageAccessTokenArgs(
        string facebookPageId,
        ProxyState state)
    {
        return new GetFacebookPageAccessTokenInput(state.Identity.SleekflowCompanyId, facebookPageId);
    }

    private GetInstagramPageAccessTokenInput GetInstagramPageAccessTokenArgs(
        string instagramPageId,
        ProxyState state)
    {
        return new GetInstagramPageAccessTokenInput(state.Identity.SleekflowCompanyId, instagramPageId);
    }
}