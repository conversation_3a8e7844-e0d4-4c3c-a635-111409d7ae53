using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;
using Sleekflow.MessagingHub.WhatsappCloudApis.Managements;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class DisassociateFacebookBusinessAccountFromCompany  : ITrigger<
    DisassociateFacebookBusinessAccountFromCompany.DisassociateFacebookBusinessAccountFromCompanyInput,
    DisassociateFacebookBusinessAccountFromCompany.DisassociateFacebookBusinessAccountFromCompanyOutput>
{
    private readonly IFacebookBusinessAccountManagementService _facebookBusinessAccountManagementService;
    private readonly ILogger<DisassociateFacebookBusinessAccountFromCompany> _logger;

    public class DisassociateFacebookBusinessAccountFromCompanyInput : IHasSleekflowCompanyId
    {
        [JsonConstructor]
        public DisassociateFacebookBusinessAccountFromCompanyInput(
            string sleekflowCompanyId,
            string facebookBusinessId,
            string sleekflowStaffIdentityId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookBusinessId = facebookBusinessId;
            SleekflowStaffIdentityId = sleekflowStaffIdentityId;
        }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffIdentityId)]
        [Required]
        public string SleekflowStaffIdentityId { get; set; }
    }

    public class DisassociateFacebookBusinessAccountFromCompanyOutput
    {
        [JsonProperty("business_balances")]
        public List<ManagementBusinessBalanceDto> BusinessBalances { get; set; }

        [JsonConstructor]
        public DisassociateFacebookBusinessAccountFromCompanyOutput(List<ManagementBusinessBalanceDto> businessBalances)
        {
            BusinessBalances = businessBalances;
        }
    }

    public DisassociateFacebookBusinessAccountFromCompany(
        ILogger<DisassociateFacebookBusinessAccountFromCompany> logger,
        IFacebookBusinessAccountManagementService facebookBusinessAccountManagementService)
    {
        _logger = logger;
        _facebookBusinessAccountManagementService = facebookBusinessAccountManagementService;
    }

    public async Task<DisassociateFacebookBusinessAccountFromCompanyOutput> F(DisassociateFacebookBusinessAccountFromCompanyInput disassociateWhatsappCloudApiBusinessInput)
    {
        _logger.LogInformation("Disassociating company Id: {companyId} from waba for facebook business Id: {facebookBusinessId} by Staff Identity Id: {sleekflowStaffIdentityId}",
            disassociateWhatsappCloudApiBusinessInput.SleekflowCompanyId,
            disassociateWhatsappCloudApiBusinessInput.FacebookBusinessId,
            disassociateWhatsappCloudApiBusinessInput.SleekflowStaffIdentityId);

        var managementBusinessBalanceDtos = await _facebookBusinessAccountManagementService.DisassociateFacebookBusinessAccountFromCompanyAsync(
            disassociateWhatsappCloudApiBusinessInput.FacebookBusinessId,
            disassociateWhatsappCloudApiBusinessInput.SleekflowCompanyId,
            disassociateWhatsappCloudApiBusinessInput.SleekflowStaffIdentityId!);

        return new DisassociateFacebookBusinessAccountFromCompanyOutput(managementBusinessBalanceDtos);
    }
}