using Sleekflow.DependencyInjection;

namespace Sleekflow.Persistence.Abstractions;

public interface IDynamicFiltersRepositoryContext
{
    public bool IsSoftDeleteEnabled { get; set; }

    public string? SleekflowCompanyId { get; set; }
}

public class DynamicFiltersRepositoryContext : IDynamicFiltersRepositoryContext, IScopedService
{
    public bool IsSoftDeleteEnabled { get; set; } = true;

    public string? SleekflowCompanyId { get; set; } = null;
}