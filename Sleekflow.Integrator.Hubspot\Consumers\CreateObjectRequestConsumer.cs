﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Integrator.Hubspot.UserMappingConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.Hubspot.Consumers;

public class CreateHubspotObjectRequestConsumerDefinition : ConsumerDefinition<CreateHubspotObjectRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<CreateHubspotObjectRequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class CreateHubspotObjectRequestConsumer : IConsumer<CreateHubspotObjectRequest>
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IHubspotUserMappingConfigService _hubspotUserMappingConfigService;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly ILogger<CreateHubspotObjectRequestConsumer> _logger;

    public CreateHubspotObjectRequestConsumer(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService,
        IHubspotUserMappingConfigService hubspotUserMappingConfigService,
        IBus bus,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        ILogger<CreateHubspotObjectRequestConsumer> logger)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
        _hubspotUserMappingConfigService = hubspotUserMappingConfigService;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<CreateHubspotObjectRequest> context)
    {
        const string hubspotOwnerFieldName = "hubspot_owner_id";

        var request = context.Message;

        var consumeId = Guid.NewGuid().ToString();

        try
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotCreateObjectRequestReceived,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            var connection = await _hubspotConnectionService.GetByIdAsync(
                request.ConnectionId,
                request.SleekflowCompanyId);

            var authentication =
                await _hubspotAuthenticationService.GetAsync(
                    connection.AuthenticationId,
                    request.SleekflowCompanyId);
            if (authentication == null)
            {
                throw new SfUnauthorizedException();
            }

            var getFieldsOutput =
                await _hubspotObjectService.GetFieldsAsync(authentication, request.EntityTypeName);
            var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();
            var updatableBooleanFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "boolean")
                .Select(f => f.Name)
                .ToList();
            var updatableDateFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "date")
                .Select(f => f.Name)
                .ToList();
            var updatableDateTimeFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "datetime")
                .Select(f => f.Name)
                .ToList();

            var dict = request.ObjectProperties
                .Where(e => updatableFieldNames.Contains(e.Key))
                .ToDictionary(
                    e => e.Key,
                    e =>
                    {
                        if (updatableBooleanFieldNames.Contains(e.Key))
                        {
                            if (e.Value is string value && !string.IsNullOrEmpty(value))
                            {
                                return value.ToLower();
                            }

                            return null;
                        }

                        if (updatableDateFieldNames.Contains(e.Key))
                        {
                            if (e.Value is DateTimeOffset dateValue)
                            {
                                return dateValue.ToString("yyyy-MM-dd");
                            }

                            if (e.Value is string dateString && DateTimeOffset.TryParse(
                                    dateString,
                                    out var parsedDateValue))
                            {
                                return parsedDateValue.ToString("yyyy-MM-dd");
                            }

                            return null;
                        }

                        if (updatableDateTimeFieldNames.Contains(e.Key))
                        {
                            if (e.Value is DateTimeOffset dateTimeValue)
                            {
                                return dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                            }

                            if (e.Value is string dateTimeString && DateTimeOffset.TryParse(
                                    dateTimeString,
                                    out var parsedDateTimeValue))
                            {
                                return parsedDateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                            }

                            return null;
                        }

                        return e.Value;
                    });

            if (request.SleekflowUserIdForMapping is not null)
            {
                var userMappingConfig = await _hubspotUserMappingConfigService.GetAsync(
                    request.SleekflowCompanyId,
                    request.ConnectionId);

                var userMapping = userMappingConfig.UserMappings?
                    .Find(m => m.SleekflowUserId == request.SleekflowUserIdForMapping);
                if (userMapping is not null)
                {
                    dict[hubspotOwnerFieldName] = userMapping.ProviderUserId;
                }
            }

            await _hubspotObjectService.CreateAsync(
                authentication,
                dict,
                request.EntityTypeName);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotCreateObjectRequestHandled,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            await _bus.Publish(
                new OnHubspotCompleteStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null));
        }
        catch (Exception ex)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotCreateObjectRequestFailed,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            _logger.LogError(
                ex,
                "CreateHubspotObjectRequest failed,"
                + " ConsumeId: {ConsumeId},"
                + " SleekflowCompanyId: {SleekflowCompanyId},"
                + " ConnectionId: {ConnectionId},"
                + " EntityTypeName: {EntityTypeName},"
                + " ObjectProperties: {ObjectProperties},"
                + " AggregateStepId: {AggregateStepId},"
                + " ProxyStateId: {ProxyStateId},"
                + " StackEntries: {StackEntries},",
                consumeId,
                request.SleekflowCompanyId,
                request.ConnectionId,
                request.EntityTypeName,
                JsonConvert.SerializeObject(request.ObjectProperties),
                request.AggregateStepId,
                request.ProxyStateId,
                request.StackEntries);

            await _bus.Publish(
                new OnHubspotFailStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null,
                    ex));
        }
    }
}