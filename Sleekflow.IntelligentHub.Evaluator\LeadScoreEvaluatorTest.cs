using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using CsvHelper;
using CsvHelper.Configuration.Attributes;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.LeadScores;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator;

[TestFixture]
[Parallelizable(ParallelScope.Children)]
public partial class LeadScoreEvaluatorTest
{
    private static readonly List<(string TestId, string Scenario, LeadScoreTestCase TestCase, LeadScoreEvaluatorResult[] Results)> AllTestResults = [];

    [TestCaseSource(nameof(GetTestCases))]
    [Parallelizable(ParallelScope.All)]
    public async Task EvaluateLeadScoreTest(LeadScoreTestCase testCase)
    {
        var testId = Guid.NewGuid();
        var fixture = new LeadScoreEvaluatorFixture();
        var evalResults = await fixture.EvaluateAsync(testCase, CancellationToken.None);

        AllTestResults.Add((testId.ToString(), testCase.Scenario, testCase, evalResults));

        Console.WriteLine("Chat messages scenarios:");
        foreach (var chatMessageContent in testCase.ChatMessageContents)
        {
            Console.WriteLine($"{chatMessageContent.Role}: {chatMessageContent.Content}");
        }

        Console.WriteLine("----------------------- Best Agent ---------------------");

        var bestOutput = evalResults.OrderByDescending(r => r.AnswerScoringScore).First();
        Console.WriteLine($"Best Scoring Agent: {bestOutput.AgentName}");

        Console.WriteLine("----------------------- Scores ------------------------");

        foreach (var evalResult in evalResults.OrderByDescending(r => r.AnswerScoringScore))
        {
            Console.WriteLine($"Agent Name: {evalResult.AgentName}");
            Console.WriteLine($"Elapsed Time: {evalResult.ElapsedMilliseconds} ms");

            Console.WriteLine("-------------------------------------------------------");

            Console.WriteLine(
                $"Expected Answer: {JsonConvert.SerializeObject(testCase.ExpectedEvaluatedScore, Formatting.Indented)}");
            Console.WriteLine(
                $"Generated Answer Scoring Score: {JsonConvert.SerializeObject(evalResult.Answer, Formatting.Indented)}");
            Console.WriteLine($"Matching Score: {evalResult.AnswerScoringScore}");

            foreach (var value in evalResult.EvaluationResult.Metrics.Values.SelectMany(v => v.Diagnostics))
            {
                Console.WriteLine($"Diagnostic: {value.Message} \n");
            }

            Console.WriteLine("-------------------------------------------------------");
        }
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        // Generate CSV output
        var csvPath = Path.Combine(
            GetRootPath(),
            $"lead_score_eval_test_results_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

        await using var writer = new StreamWriter(csvPath);
        await using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // Write test results
        var records = AllTestResults.Select(result =>
        {
            var (testId, scenario, testCase, results) = result;
            var resultDict = results.ToDictionary(r => r.AgentName, r => r);

            // var defaultAgentResult = resultDict.GetValueOrDefault("Default");
            var newAgentResult = resultDict.GetValueOrDefault("New");

            return new LeadScoreTestResult
            {
                TestId = testId,
                Scenario = scenario,
                Question = string.Join(" ", testCase.ChatMessageContents.Select(q => $"{q.Role}: {q.Content}")),
                ExpectedCategory = testCase.ExpectedEvaluatedScore.Category,
                ExpectedScore = testCase.ExpectedEvaluatedScore.Score,
                NewAgentCategory = FormatCategory(newAgentResult),
                NewAgentScore = FormatScore(newAgentResult),
                NewAgentExplanation = FormatExplanation(newAgentResult)
            };

            string FormatCategory(LeadScoreEvaluatorResult? r) =>
                r?.Answer?.Category ?? string.Empty;
                
            string FormatScore(LeadScoreEvaluatorResult? r) =>
                r?.Answer?.Score.ToString() ?? string.Empty;
                
            string FormatExplanation(LeadScoreEvaluatorResult? r) =>
                r?.Answer?.Reason.Replace("\r\n", " ").Replace("\n", " ") ?? string.Empty;  
        });

        // Sort by Scenario
        records = records.OrderBy(r => r.Scenario).ToList();
        // Calculate the number of matching and mismatching NewAgentCategory and ExpectedCategory for each Scenario
        var scenarioStats = records
            .GroupBy(r => r.Scenario)
            .Select(g => new 
            {
                Scenario = g.Key,
                TotalCount = g.Count(),
                MatchCount = g.Count(r => r.NewAgentCategory == r.ExpectedCategory),
                MismatchCount = g.Count(r => r.NewAgentCategory != r.ExpectedCategory),
                MatchPercentage = (double)g.Count(r => r.NewAgentCategory == r.ExpectedCategory) / g.Count(),
                MismatchPercentage = (double)g.Count(r => r.NewAgentCategory != r.ExpectedCategory) / g.Count()
            })
            .ToList();

        // Calculate the total number of matches and mismatches across all cases
        int totalMatchCount = scenarioStats.Sum(s => s.MatchCount);
        int totalMismatchCount = scenarioStats.Sum(s => s.MismatchCount);
        int totalCount = records.Count();

        // Add statistics to records
        var statsRecord = new LeadScoreTestResult
        {
            TestId = "Statistics",
            Scenario = "Total",
            Question = "",
            ExpectedCategory = "",
            ExpectedScore = 0,
            NewAgentCategory = $"Match: {totalMatchCount}/{totalCount} ({(double)totalMatchCount/totalCount:P2})",
            NewAgentScore = $"Mismatch: {totalMismatchCount}/{totalCount} ({(double)totalMismatchCount/totalCount:P2})",
            NewAgentExplanation = ""
        };

        // Add statistics for each scenario
        var scenarioStatsRecords = scenarioStats.Select(s => new LeadScoreTestResult
        {
            TestId = "Statistics",
            Scenario = s.Scenario,
            Question = "",
            ExpectedCategory = "",
            ExpectedScore = 0,
            NewAgentCategory = $"Match: {s.MatchCount}/{s.TotalCount} ({s.MatchPercentage:P2})",
            NewAgentScore = $"Mismatch: {s.MismatchCount}/{s.TotalCount} ({s.MismatchPercentage:P2})",
            NewAgentExplanation = ""
        });

        // Merge original records with statistics records
        records = records.Concat(new[] { statsRecord }).Concat(scenarioStatsRecords).ToList();
        await csv.WriteRecordsAsync(records);
        Console.WriteLine($"\nTest results exported to: {csvPath}");
    }

    private string GetRootPath()
    {
        var exePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var appPathMatcher = new Regex(@"(?<!file)[A-Za-z]:\\+[\S\s]*?(?=\\+bin)");
        return appPathMatcher.Match(exePath).Value;
    }
}

public class LeadScoreTestResult
{
    [Name("test_id")]
    public string TestId { get; init; } = string.Empty;

    [Name("scenario")]
    public string Scenario { get; init; } = string.Empty;

    [Name("question")]
    public string Question { get; init; } = string.Empty;

    [Name("expected_category")]
    public string ExpectedCategory { get; init; } = string.Empty;

    [Name("expected_score")]
    public int ExpectedScore { get; init; }

    [Name("expected_explanation")]
    public string ExpectedExplanation { get; init; } = string.Empty;

    [Name("default_agent_category")]
    public string DefaultAgentCategory { get; init; } = string.Empty;

    [Name("default_agent_score")]
    public string DefaultAgentScore { get; init; } = string.Empty;

    [Name("default_agent_explanation")]
    public string DefaultAgentExplanation { get; init; } = string.Empty;

    [Name("new_agent_category")]
    public string NewAgentCategory { get; init; } = string.Empty;

    [Name("new_agent_score")]
    public string NewAgentScore { get; init; } = string.Empty;

    [Name("new_agent_explanation")]
    public string NewAgentExplanation { get; init; } = string.Empty;
}