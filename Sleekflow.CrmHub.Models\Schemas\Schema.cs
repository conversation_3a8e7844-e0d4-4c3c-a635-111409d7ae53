﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.Schemas;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId(ContainerNames.Schema)]
public class Schema : Entity, IHasSleekflowCompanyId, IHasUpdatedAt, IHasCreatedAt
{
    public const string PropertyNameDisplayName = "display_name";
    public const string PropertyNameUniqueName = "unique_name";
    public const string PropertyNameRelationshipType = "relationship_type";
    public const string PropertyNameIsEnabled = "is_enabled";
    public const string PropertyNameIsDeleted = "is_deleted";
    public const string PropertyNameSortingWeight = "sorting_weight";
    public const string PropertyNamePrimaryProperty = "primary_property";
    public const string PropertyNameProperties = "properties";
    public const string PropertyNameSchemaAccessibilitySettings = "schema_accessibility_settings";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameDisplayName)]
    public string DisplayName { get; set; }

    [JsonProperty(PropertyNameUniqueName)]
    public string UniqueName { get; set; }

    [JsonProperty(PropertyNameRelationshipType)]
    public string RelationshipType { get; }

    [JsonProperty(PropertyNameIsEnabled)]
    public bool IsEnabled { get; set; }

    [JsonProperty(PropertyNameIsDeleted)]
    public bool IsDeleted { get; set; }

    [JsonProperty(PropertyNameSortingWeight)]
    public ushort SortingWeight { get; set; }

    [JsonProperty(PropertyNamePrimaryProperty)]
    public PrimaryProperty PrimaryProperty { get; set; }

    [JsonProperty(PropertyNameProperties)]
    public List<Property> Properties { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(PropertyNameSchemaAccessibilitySettings)]
    public SchemaAccessibilitySettings? SchemaAccessibilitySettings { get; set; }

    [JsonConstructor]
    public Schema(
        string id,
        string sleekflowCompanyId,
        string displayName,
        string uniqueName,
        string relationshipType,
        bool isEnabled,
        bool isDeleted,
        ushort sortingWeight,
        List<Property> properties,
        PrimaryProperty primaryProperty,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SchemaAccessibilitySettings? schemaAccessibilitySettings)
        : base(id, SysTypeNames.Schema)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        DisplayName = displayName;
        UniqueName = uniqueName;
        RelationshipType = relationshipType;
        IsEnabled = isEnabled;
        IsDeleted = isDeleted;
        PrimaryProperty = primaryProperty;
        Properties = properties;
        SortingWeight = sortingWeight;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        SchemaAccessibilitySettings = schemaAccessibilitySettings ?? SchemaAccessibilitySettings.Default();
    }
}