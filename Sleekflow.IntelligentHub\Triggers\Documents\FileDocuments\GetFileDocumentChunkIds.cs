using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class GetFileDocumentChunkIds
    : ITrigger<GetFileDocumentChunkIds.GetFileDocumentChunkIdsInput, GetFileDocumentChunkIds.GetFileDocumentChunkIdsOutput>
{
    private readonly IFileDocumentChunkService _fileDocumentChunkService;

    public GetFileDocumentChunkIds(
        IFileDocumentChunkService fileDocumentChunkService)
    {
        _fileDocumentChunkService = fileDocumentChunkService;
    }

    public class GetFileDocumentChunkIdsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunkIdsInput(string sleekflowCompanyId, string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class GetFileDocumentChunkIdsOutput
    {
        [JsonProperty("chunk_ids")]
        public List<string> ChunkIds { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunkIdsOutput(List<string> chunkIds)
        {
            ChunkIds = chunkIds;
        }
    }

    public async Task<GetFileDocumentChunkIdsOutput> F(GetFileDocumentChunkIdsInput getFileDocumentChunkIdsInput)
    {
        var chunkIds =
            await _fileDocumentChunkService.GetFileDocumentChunkIdsAsync(
                getFileDocumentChunkIdsInput.SleekflowCompanyId,
                getFileDocumentChunkIdsInput.DocumentId);

        return new GetFileDocumentChunkIdsOutput(chunkIds);
    }
}