using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class UpdateAiFeatureSetting
    : ITrigger<UpdateAiFeatureSetting.UpdateAiFeatureSettingInput, UpdateAiFeatureSetting.UpdateAiFeatureSettingOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public UpdateAiFeatureSetting(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class UpdateAiFeatureSettingInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IntelligentHubConfig.PropertyNameEnableWritingAssistant)]
        public bool EnableWritingAssistant { get; set; }

        [Required]
        [JsonProperty(IntelligentHubConfig.PropertyNameEnableSmartReply)]
        public bool EnableSmartReply { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateAiFeatureSettingInput(
            string sleekflowCompanyId,
            bool enableWritingAssistant,
            bool enableSmartReply,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EnableWritingAssistant = enableWritingAssistant;
            EnableSmartReply = enableSmartReply;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateAiFeatureSettingOutput
    {
        [JsonProperty(IntelligentHubConfig.PropertyNameEnableWritingAssistant)]
        public bool EnableWritingAssistant { get; set; }

        [JsonProperty(IntelligentHubConfig.PropertyNameEnableSmartReply)]
        public bool EnableSmartReply { get; set; }

        [JsonConstructor]
        public UpdateAiFeatureSettingOutput(bool enableWritingAssistant, bool enableSmartReply)
        {
            EnableWritingAssistant = enableWritingAssistant;
            EnableSmartReply = enableSmartReply;
        }
    }

    public async Task<UpdateAiFeatureSettingOutput> F(UpdateAiFeatureSettingInput updateAiFeatureSettingInput)
    {
        var updatedBy = string.IsNullOrWhiteSpace(updateAiFeatureSettingInput.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                updateAiFeatureSettingInput.SleekflowStaffId,
                updateAiFeatureSettingInput.SleekflowStaffTeamIds);

        var intelligentHubConfig = await _intelligentHubConfigService.UpdateAiFeatureSettingAsync(
            updateAiFeatureSettingInput.SleekflowCompanyId,
            updateAiFeatureSettingInput.EnableWritingAssistant,
            updateAiFeatureSettingInput.EnableSmartReply,
            updatedBy);

        return new UpdateAiFeatureSettingOutput(
            intelligentHubConfig.EnableWritingAssistant,
            intelligentHubConfig.EnableSmartReply);
    }
}