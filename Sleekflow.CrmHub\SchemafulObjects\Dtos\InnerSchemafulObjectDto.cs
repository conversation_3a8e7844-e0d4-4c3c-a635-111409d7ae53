﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Attributes;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.SchemafulObjects.Dtos;

[SwaggerInclude]
public class InnerSchemafulObjectDto : IHasCreatedAt
{
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public InnerSchemafulObjectDto(
        string id,
        Dictionary<string, object?> propertyValues,
        DateTimeOffset createdAt)
    {
        Id = id;
        PropertyValues = propertyValues;
        CreatedAt = createdAt;
    }

    public static bool TryParse(object? value, out List<InnerSchemafulObjectDto> innerSchemafulObjects)
    {
        var res = false;
        try
        {
            innerSchemafulObjects = Parse(value);
            res = true;
        }
        catch
        {
            innerSchemafulObjects = new List<InnerSchemafulObjectDto>();
        }

        return res;
    }

    public static List<InnerSchemafulObjectDto> Parse(object? value)
    {
        return value switch
        {
            null => new List<InnerSchemafulObjectDto>(),
            List<InnerSchemafulObjectDto> innerSchemafulObjects => innerSchemafulObjects,
            JArray jArray => jArray.ToObject<List<InnerSchemafulObjectDto>>()!,
            _ => throw new InvalidOperationException()
        };
    }
}