﻿using Microsoft.Extensions.AI.Evaluation;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores;

public class LeadScoreEvaluatorResult(
    string agentName,
    EvaluatedScore? answer,
    EvaluationResult evaluationResult,
    long elapsedMilliseconds)
{
    public string AgentName { get; init; } = agentName;

    public EvaluatedScore? Answer { get; init; } = answer;

    public EvaluationResult EvaluationResult { get; init; } = evaluationResult;

    public long ElapsedMilliseconds { get; init; } = elapsedMilliseconds;

    // all scores are numeric from 1 to 5, where 5 means exceptional and 1 means fail
    public double AnswerScoringScore { get; private set; } =
        (evaluationResult.Metrics[LeadScoreEvaluator.ScoreMetricName] as NumericMetric)?.Value ??
        throw new Exception("Answer scoring score is missing.");
}