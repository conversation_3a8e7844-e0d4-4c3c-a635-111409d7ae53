﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class ScheduledTriggerConditionsCheckStepArgs : TypedCallStepArgs
{
    public const string CallName = "sys.scheduled-trigger-conditions-check";

    [Required]
    [JsonProperty("conditions__expr")]
    public string ConditionsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => string.Empty;

    [JsonConstructor]
    public ScheduledTriggerConditionsCheckStepArgs(string conditionsExpr)
    {
        ConditionsExpr = conditionsExpr;
    }
}