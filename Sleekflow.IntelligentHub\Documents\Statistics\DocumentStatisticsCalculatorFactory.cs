using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.IntelligentHub;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Constants;

namespace Sleekflow.IntelligentHub.Documents.Statistics;

public interface IDocumentStatisticsCalculatorFactory
{
    IDocumentStatisticsCalculator CreateDocumentStatisticsCalculator(string documentType);

    IDocumentStatisticsCalculator CreateDocumentStatisticsCalculatorByMimeType(string mimeType);
}

public class DocumentStatisticsCalculatorFactory : IDocumentStatisticsCalculatorFactory, ISingletonService
{
    private readonly IDocumentCounterService _documentCounterService;

    public DocumentStatisticsCalculatorFactory(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public IDocumentStatisticsCalculator CreateDocumentStatisticsCalculator(string documentType)
    {
        switch (documentType.ToLower())
        {
            case DocumentTypes.Pdf:
                return new PdfStatisticsCalculator(_documentCounterService);
            case DocumentTypes.Text:
                return new TxtStatisticsCalculator(_documentCounterService);
            case DocumentTypes.Csv:
                return new CsvStatisticsCalculator(_documentCounterService);
            case DocumentTypes.MicrosoftExcel:
                return new ExcelStatisticsCalculator(_documentCounterService);
            case DocumentTypes.MicrosoftWord:
                return new WordStatisticsCalculator(_documentCounterService);
            case DocumentTypes.Jpg:
            case DocumentTypes.Jpeg:
                return new JpgStatisticsCalculator(_documentCounterService);
            case DocumentTypes.Mp4:
                return new Mp4StatisticsCalculator(_documentCounterService);
            default:
                throw new SfKnowledgeBaseDocumentTypeNotSupportedException(documentType.ToLower());
        }
    }

    public IDocumentStatisticsCalculator CreateDocumentStatisticsCalculatorByMimeType(string mimeType)
    {
        var documentType = mimeType.ToLower() switch
        {
            "application/pdf" => DocumentTypes.Pdf,
            "text/plain" => DocumentTypes.Text,
            "text/csv" => DocumentTypes.Csv,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => DocumentTypes.MicrosoftExcel,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => DocumentTypes.MicrosoftWord,
            "image/jpeg" => DocumentTypes.Jpeg,
            "image/jpg" => DocumentTypes.Jpg,
            "video/mp4" => DocumentTypes.Mp4,
            _ => throw new SfKnowledgeBaseDocumentTypeNotSupportedException(mimeType)
        };

        return CreateDocumentStatisticsCalculator(documentType);
    }
}