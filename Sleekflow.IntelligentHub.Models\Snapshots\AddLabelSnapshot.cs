using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class AddLabelSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("add_label")]
    public AddLabelResult? AddLabel { get; set; }

    [JsonConstructor]
    public AddLabelSnapshot(List<SfChatEntry> conversationContext, AddLabelResult? addLabel)
    {
        ConversationContext = conversationContext;
        AddLabel = addLabel;
    }
}