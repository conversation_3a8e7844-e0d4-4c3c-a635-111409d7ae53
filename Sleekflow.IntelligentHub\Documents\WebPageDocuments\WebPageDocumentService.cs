﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;

namespace Sleekflow.IntelligentHub.Documents.WebPageDocuments;

public interface IWebPageDocumentService
{
    Task<WebPageDocument> GetDocumentByBlobIdAsync(string sleekflowCompanyId, string blobId);

    Task<WebPageDocument> GetDocumentAsync(string sleekflowCompanyId, string documentId);

    Task<WebPageDocument> CreateOrGetDocumentAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        string pageUri,
        string language,
        DocumentStatistics documentStatistics);
}

public class WebPageDocumentService : IWebPageDocumentService, IScopedService
{
    private readonly IWebPageDocumentRepository _webPageDocumentRepository;
    private readonly IIdService _idService;

    public WebPageDocumentService(IWebPageDocumentRepository webPageDocumentRepository, IIdService idService)
    {
        _webPageDocumentRepository = webPageDocumentRepository;
        _idService = idService;
    }

    public async Task<WebPageDocument> GetDocumentByBlobIdAsync(string sleekflowCompanyId, string blobId)
    {
        var document = (await _webPageDocumentRepository.GetDocumentsAsync(
            sleekflowCompanyId,
            blobId))[0];
        return document;
    }

    public async Task<WebPageDocument> GetDocumentAsync(string sleekflowCompanyId, string documentId)
    {
        var document = await _webPageDocumentRepository.GetAsync(documentId, sleekflowCompanyId);
        if (document == null)
        {
            throw new SfNotFoundObjectException(documentId, sleekflowCompanyId);
        }

        return document;
    }

    public async Task<WebPageDocument> CreateOrGetDocumentAsync(
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        string pageUri,
        string language,
        DocumentStatistics documentStatistics)
    {
        var documents = await _webPageDocumentRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.BlobId == blobId && c.BlobType == blobType);
        if (documents.Any())
        {
            return documents.First();
        }

        var createdDocument = await _webPageDocumentRepository.CreateAndGetAsync(
            new WebPageDocument(
                _idService.GetId(SysTypeNames.WebPageDocument),
                sleekflowCompanyId,
                blobId,
                blobType,
                pageUri,
                language,
                documentStatistics,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowCompanyId);

        return createdDocument;
    }
}