using MassTransit;
using Sleekflow.IntelligentHub.Models.Events;
using Sleekflow.IntelligentHub.Services; // For IFollowUpMessagingService
using Microsoft.SemanticKernel; // For Kernel

namespace Sleekflow.IntelligentHub.Consumers;

// Definition class for MassTransit configuration (similar to AgentDeletedEventConsumerDefinition)
public class ScheduleFollowUpMessageConsumerDefinition : ConsumerDefinition<ScheduleFollowUpMessageConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<ScheduleFollowUpMessageConsumer> consumerConfigurator)
    {
        // Add any specific configurations for this consumer, mirroring other consumers if applicable
        // Example from AgentDeletedEventConsumerDefinition:
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusConfig)
        {
            serviceBusConfig.EnablePartitioning = false; // Example setting
        }
        // consumerConfigurator.UseMessageRetry(r => r.Interval(5, TimeSpan.FromSeconds(30))); // Example retry policy
    }
}

public class ScheduleFollowUpMessageConsumer : IConsumer<ScheduleFollowUpMessage>
{
    private readonly ILogger<ScheduleFollowUpMessageConsumer> _logger;
    private readonly IFollowUpMessagingService _followUpMessagingService;
    private readonly Kernel _kernel; // Kernel might be needed by FollowUpMessagingService for SleekflowToolsPlugin

    public ScheduleFollowUpMessageConsumer(
        ILogger<ScheduleFollowUpMessageConsumer> logger,
        IFollowUpMessagingService followUpMessagingService,
        Kernel kernel) // Inject Kernel if needed by the service chain
    {
        _logger = logger;
        _followUpMessagingService = followUpMessagingService;
        _kernel = kernel;
    }

    public async Task Consume(ConsumeContext<ScheduleFollowUpMessage> context)
    {
        var message = context.Message;
        _logger.LogInformation("Received scheduled follow-up for GroupChatId: {GroupChatId} at {ScheduledTimeUtc}", message.GroupChatId, message.ScheduledTimeUtc);

        try
        {
            // Pass the kernel instance and all context data to the service method
            await _followUpMessagingService.SendFollowUpIfAppropriateAsync(
                _kernel,
                message.GroupChatId,
                message.FollowUpMessage,
                message.CompanyId,
                message.ObjectId,
                message.StateId,
                message.ContactId,
                message.AgentId,
                message.ToolsConfigJson,
                message.LeadNurturingToolsConfigJson,
                message.OriginalUserMessage);
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error processing scheduled follow-up for GroupChatId: {GroupChatId}", message.GroupChatId);
            // Consider re-throwing or specific error handling based on your MassTransit error strategy
            throw;
        }
    }
}