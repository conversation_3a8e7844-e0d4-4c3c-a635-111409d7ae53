using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Subscriptions;
using Sleekflow.Integrator.Salesforce.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeleteConnection : ITrigger
{
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceSubscriptionService _salesforceSubscriptionService;
    private readonly ISalesforceUserMappingConfigService _salesforceUserMappingConfigService;

    public DeleteConnection(
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceSubscriptionService salesforceSubscriptionService,
        ISalesforceUserMappingConfigService salesforceUserMappingConfigService)
    {
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceSubscriptionService = salesforceSubscriptionService;
        _salesforceUserMappingConfigService = salesforceUserMappingConfigService;
    }

    public class DeleteConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public DeleteConnectionInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class DeleteConnectionOutput
    {
    }

    public async Task<DeleteConnectionOutput> F(DeleteConnectionInput deleteConnectionInput)
    {
        var connection = await _salesforceConnectionService.GetByIdAsync(
            deleteConnectionInput.ConnectionId,
            deleteConnectionInput.SleekflowCompanyId);

        if (connection is null)
        {
            throw new SfNotFoundObjectException(
                deleteConnectionInput.ConnectionId,
                deleteConnectionInput.SleekflowCompanyId);
        }

        await _salesforceSubscriptionService.ClearByConnectionIdAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _salesforceUserMappingConfigService.ClearAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _salesforceAuthenticationService.DeleteAsync(
            connection.AuthenticationId);

        await _salesforceConnectionService.DeleteAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        return new DeleteConnectionOutput();
    }
}