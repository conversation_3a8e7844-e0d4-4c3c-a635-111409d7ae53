using Microsoft.SemanticKernel.ChatCompletion;

namespace Sleekflow.IntelligentHub.Tokens;

public class PromptComposer
{
    private readonly int _maxNumOfTokens;
    private readonly ITokenService _tokenService;
    private readonly List<Prompt> _prompts = new List<Prompt>();

    public PromptComposer(int maxNumOfTokens, ITokenService tokenService)
    {
        _maxNumOfTokens = maxNumOfTokens;
        _tokenService = tokenService;
    }

    public void AddPrompt(Prompt prompt)
    {
        _prompts.Add(prompt);
    }

    public (ChatHistory ChatHistory, int ConumedNumOfTokens) Compose(ChatHistory chatHistory)
    {
        var totalConsumedNumOfTokens = 0;

        foreach (var prompt in _prompts)
        {
            var (chatEntries, consumedNumOfTokens) = prompt.GetChatEntries(_maxNumOfTokens, _tokenService);

            foreach (var chatEntry in chatEntries)
            {
                if (chatEntry.Sys != null)
                {
                    chatHistory.AddSystemMessage(chatEntry.Sys);
                }

                if (chatEntry.User != null)
                {
                    chatHistory.AddUserMessage(chatEntry.User);
                }

                if (chatEntry.Bot != null)
                {
                    chatHistory.AddAssistantMessage(chatEntry.Bot);
                }
            }

            totalConsumedNumOfTokens += consumedNumOfTokens;
        }

        return (chatHistory, totalConsumedNumOfTokens);
    }
}