﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Currencies;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class UpdateStore
    : ITrigger<
        UpdateStore.UpdateStoreInput,
        UpdateStore.UpdateStoreOutput>
{
    private readonly IStoreService _storeService;
    private readonly ICurrencyService _currencyService;

    public UpdateStore(
        IStoreService storeService,
        ICurrencyService currencyService)
    {
        _storeService = storeService;
        _currencyService = currencyService;
    }

    public class UpdateStoreInput : StoreInput, IHasSleekflowStaff, IHasMetadata
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateStoreInput(
            List<Multilingual> names,
            List<Description> descriptions,
            bool isViewEnabled,
            bool isPaymentEnabled,
            List<LanguageInputDto> languages,
            List<CurrencyInputDto> currencies,
            StoreTemplateDict templateDict,
            Dictionary<string, object?> metadata,
            StoreIntegrationExternalConfigInput? storeIntegrationExternalConfigInput,
            StoreSubscriptionStatus? subscriptionStatus,
            string id,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                names,
                descriptions,
                isViewEnabled,
                isPaymentEnabled,
                languages,
                currencies,
                templateDict,
                metadata,
                storeIntegrationExternalConfigInput,
                subscriptionStatus)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateStoreOutput
    {
        [JsonProperty("store")]
        public StoreDto Store { get; set; }

        [JsonConstructor]
        public UpdateStoreOutput(StoreDto store)
        {
            Store = store;
        }
    }

    public async Task<UpdateStoreOutput> F(UpdateStoreInput updateStoreInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateStoreInput.SleekflowStaffId,
            updateStoreInput.SleekflowStaffTeamIds);

        var languages = updateStoreInput.Languages
            .Select(l => (CultureUtils.GetCultureInfoByLanguageIsoCode(l.LanguageIsoCode)!, l.IsDefault))
            .Select(t => new Language(t.Item1.Name, t.Item1.EnglishName, t.Item1.NativeName, t.IsDefault))
            .ToList();

        var currencies = _currencyService.GetCurrencies()
            .Where(
                c => updateStoreInput.Currencies.Any(
                    cc => cc.CurrencyIsoCode == c.CurrencyIsoCode))
            .ToList();

        var store = await _storeService.PatchAndGetStoreAsync(
            updateStoreInput.Id,
            updateStoreInput.SleekflowCompanyId,
            updateStoreInput.Names,
            updateStoreInput.Descriptions,
            updateStoreInput.IsViewEnabled,
            updateStoreInput.IsPaymentEnabled,
            languages,
            currencies,
            updateStoreInput.TemplateDict,
            updateStoreInput.Metadata,
            updateStoreInput.StoreIntegrationExternalConfigInput?.GetConfig(),
            updateStoreInput.SubscriptionStatus,
            sleekflowStaff);

        return new UpdateStoreOutput(new StoreDto(store));
    }
}