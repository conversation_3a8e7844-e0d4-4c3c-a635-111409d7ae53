using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.UserEventHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class UserEventHubModules
{
    public static void BuildUserEventHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON><PERSON><PERSON><IUserEventHubDbConfig>(new Mock<IUserEventHubDbConfig>().Object);
        b.<PERSON><PERSON><IUserEventHubDbResolver>(new Mock<IUserEventHubDbResolver>().Object);

#else
        var userEventHubDbConfig = new UserEventHubDbConfig();

        b.<PERSON>d<PERSON><IUserEventHubDbConfig>(userEventHubDbConfig);
        b.<PERSON>d<PERSON><PERSON><IUserEventHubDbResolver, UserEventHubDbResolver>();
#endif
    }
}