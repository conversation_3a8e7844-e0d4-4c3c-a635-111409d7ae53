using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class UpdateWhatsappCloudApiBusinessBalanceMarkupProfile
    : ITrigger<
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfile.UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput,
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfile.UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput>
{
    private const int LockDuration = 5;
    private const int MaxAutoRenewDuration = 15;

    private readonly IBus _bus;
    private readonly ILockService _lockService;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<UpdateWhatsappCloudApiBusinessBalanceMarkupProfile> _logger;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;

    public UpdateWhatsappCloudApiBusinessBalanceMarkupProfile(
        IBus bus,
        ILockService lockService,
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        ILogger<UpdateWhatsappCloudApiBusinessBalanceMarkupProfile> logger,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _bus = bus;
        _logger = logger;
        _lockService = lockService;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public class UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("business_balance_id")]
        public string BusinessBalanceId { get; set; }

        [Required]
        [JsonProperty("markup_profile")]
        public MarkupProfile MarkupProfile { get; set; }

        [Required]
        [JsonProperty("should_recalculate")]
        public bool ShouldRecalculate { get; set; }

        [JsonProperty("last_recalculate_business_balance_transaction_log")]
        public DateTimeOffset? LastRecalculateBusinessBalanceTransactionLog { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput(
            string businessBalanceId,
            MarkupProfile markupProfile,
            bool shouldRecalculate,
            DateTimeOffset? lastRecalculateBusinessBalanceTransactionLog,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            BusinessBalanceId = businessBalanceId;
            MarkupProfile = markupProfile;
            ShouldRecalculate = shouldRecalculate;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            LastRecalculateBusinessBalanceTransactionLog = lastRecalculateBusinessBalanceTransactionLog;
        }
    }

    public class UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput
    {
    }

    public async Task<UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput> F(
        UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput updateWhatsappCloudApiBusinessBalanceMarkupProfileInput)
    {
        var id = updateWhatsappCloudApiBusinessBalanceMarkupProfileInput.BusinessBalanceId;
        var markupProfile = updateWhatsappCloudApiBusinessBalanceMarkupProfileInput.MarkupProfile;
        var shouldRecalculate = updateWhatsappCloudApiBusinessBalanceMarkupProfileInput.ShouldRecalculate;
        var sleekflowStaffId = updateWhatsappCloudApiBusinessBalanceMarkupProfileInput.SleekflowStaffId;
        var sleekflowStaffTeamIds = updateWhatsappCloudApiBusinessBalanceMarkupProfileInput.SleekflowStaffTeamIds;
        var lastRecalculateBusinessBalanceTransactionLog =
            updateWhatsappCloudApiBusinessBalanceMarkupProfileInput.LastRecalculateBusinessBalanceTransactionLog;

        var businessBalance = await _businessBalanceService.GetWithIdAsync(id);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException($"Unable to locate business balance object with {id}");
        }

        var conversationUsageTransactionLock = await _lockService.LockAsync(
            new[]
            {
                businessBalance.FacebookBusinessId,
                "ConversationUsageTransactionLock"
            },
            TimeSpan.FromSeconds(60 * (LockDuration + MaxAutoRenewDuration)));
        if (conversationUsageTransactionLock is null)
        {
            throw new SfInternalErrorException("Unable to lock business balance for conversation usage transaction");
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                businessBalance.FacebookBusinessId
            },
            TimeSpan.FromSeconds(60 * (LockDuration + MaxAutoRenewDuration)));
        if (@lock is null)
        {
            await _lockService.ReleaseAsync(conversationUsageTransactionLock);
            throw new SfInternalErrorException("Unable to lock business balance");
        }

        try
        {
            businessBalance = await _businessBalanceService.UpsertBusinessBalanceMarkupProfileAsync(
                businessBalance,
                markupProfile,
                sleekflowStaffId,
                sleekflowStaffTeamIds);

            await _lockService.ReleaseAsync(conversationUsageTransactionLock);
            if (!shouldRecalculate)
            {
                await _lockService.ReleaseAsync(@lock);
                return new UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput();
            }

            if (lastRecalculateBusinessBalanceTransactionLog is null)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new ValidationResult("LastRecalculateBusinessBalanceTransactionLog value not found")
                    });
            }

            await _businessBalanceTransactionLogService.UpsertMarkupProfileSnapshotStateAsync(
                businessBalance.FacebookBusinessId,
                markupProfile,
                lastRecalculateBusinessBalanceTransactionLog.Value,
                sleekflowStaffId,
                sleekflowStaffTeamIds);

            await _lockService.ReleaseAsync(@lock);

            // Trigger resynchronization event to recalculate conversation usage transaction with patched markup profile
            var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(businessBalance.FacebookBusinessId);
            foreach (var waba in wabas)
            {
                await _bus.Publish(
                    new OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent(
                        waba.FacebookWabaId,
                        businessBalance.FacebookBusinessId,
                        lastRecalculateBusinessBalanceTransactionLog.Value.ToUnixTimeSeconds(),
                        DateTimeOffset.UtcNow.ToUnixTimeSeconds()));
            }

            return new UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput();
        }
        catch (Exception exception)
        {
            await _lockService.ReleaseAsync(@lock);
            _logger.LogError(
                "Exception occur in UpdateWhatsappCloudApiBusinessBalanceMarkupProfile {Exception}",
                JsonConvert.SerializeObject(exception));
            throw;
        }
    }
}