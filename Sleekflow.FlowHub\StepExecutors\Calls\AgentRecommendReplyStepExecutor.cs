using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Internals.ChatHistory;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Contacts;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IAgentRecommendReplyStepExecutor : IStepExecutor
{
}

public class AgentRecommendReplyStepExecutor
    : GeneralStepExecutor<CallStep<AgentRecommendReplyStepArgs>>, IAgentRecommendReplyStepExecutor, IScopedService
{
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ILogger _logger;
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IAgentConfigService _agentConfigService;
    private readonly IStateService _stateService;
    private readonly IChatHistoryService _chatHistoryService;

    public AgentRecommendReplyStepExecutor(
        IServiceBusManager serviceBusManager,
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator,
        IServiceProvider serviceProvider,
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        ILogger<AgentRecommendReplyStepExecutor> logger,
        IAgentConfigService agentConfigService,
        IStateService stateService,
        IChatHistoryService chatHistoryService)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _serviceBusManager = serviceBusManager;
        _logger = logger;
        _coreCommander = coreCommander;
        _stateEvaluator = stateEvaluator;
        _agentConfigService = agentConfigService;
        _stateService = stateService;
        _chatHistoryService = chatHistoryService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        try
        {
            var callStep = ToConcreteStep(step);
            var contactId = await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.ContactIdExpr);

            // Start independent operations in parallel
            var configTask = _agentConfigService.GetAsync(state, callStep.Args.CompanyAgentConfigIdExpr);
            var inputTask = GetInputAsync(state, callStep);
            var intelligentHubUsageFilterTask = GetIntelligentHubUsageFilterAsync(state);

            // Wait for config to be available for dependent operations
            var config = await configTask;
            var runningStates = await _stateService.GetRunningStatesAsync(
                                       state.Identity.ObjectId,
                                       state.Identity.ObjectType,
                                       state.Identity.SleekflowCompanyId,
                                       state.TriggerEventBody);
            var channelId = StateUtils
                    .GetChannelIdFromParentState(runningStates, state.WorkflowContext.SnapshottedWorkflow.WorkflowId, _logger);

            // Start config-dependent operations
            var conversationMessagesTask = GetConversationMessagesAsync(state, contactId, config, callStep, channelId);
            var contactPropertiesTask = GetContactPropertiesAsync(state, contactId, config);

            // Wait for all operations to complete
            await Task.WhenAll(
                conversationMessagesTask,
                contactPropertiesTask,
                inputTask,
                intelligentHubUsageFilterTask);

            // Get the results
            var conversationMessages = await conversationMessagesTask;
            var contactProperties = await contactPropertiesTask;
            var input = await inputTask;
            var intelligentHubUsageFilter = await intelligentHubUsageFilterTask;

            // Check if the company has the AI POC plan
            var isCompanyHasAiPocPlan = await GetIsCompanyHasAiPocPlanAsync(state);

            // Delay the completion until the agent has generated the response - refer to the OnAgentStepCompleteEventConsumer
            await _serviceBusManager.PublishAsync(
                new GetAgentRecommendedReplyEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    state.Identity.SleekflowCompanyId,
                    config.Id,
                    conversationMessages,
                    contactProperties,
                    contactId,
                    intelligentHubUsageFilter?.FromDateTime,
                    intelligentHubUsageFilter?.ToDateTime,
                    input,
                    isCompanyHasAiPocPlan));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<List<SfChatEntry>> GetConversationMessagesAsync(
        ProxyState state,
        string? contactId,
        AgentConfig config,
        CallStep<AgentRecommendReplyStepArgs> callStep,
        string? channelId = null)
    {
        var retrievalWindowTimestamp = await GetRetrievalWindowTimestamp(state, callStep);
        var finalChannelId = channelId ?? config.ChannelId;

        var response = await _coreCommander.ExecuteAsync<GetConversationLastMessagesOutput>(
            state.Origin,
            "GetConversionLastMessages",
            new GetConversationLastMessagesInput(
                state.Identity,
                contactId!,
                [ChannelTypes.WhatsAppCloudApi],
                finalChannelId,
                0,
                config.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
                retrievalWindowTimestamp));

        return response?.ConversationMessages
            .Select(c =>
            {
                var chatEntry = new SfChatEntry();
                if (c.IsSentFromSleekflow)
                {
                    chatEntry.Bot = string.IsNullOrEmpty(c.MessageContent) ? string.Empty : c.MessageContent;
                }
                else
                {
                    chatEntry.User = string.IsNullOrEmpty(c.MessageContent) ? string.Empty : c.MessageContent;
                }

                chatEntry.CreatedAt = c.CreatedAt;

                if (c.Files != null)
                {
                    chatEntry.Files = c.Files.Select(f => new SfChatEntryFile(f.Url, f.MimeType, f.FileSize)).ToList();
                }

                return chatEntry;
            }).ToList() ?? [];
    }

    private async Task<Dictionary<string, string>?> GetContactPropertiesAsync(
        ProxyState state,
        string? contactId,
        AgentConfig config)
    {
        return config.IsContactPropertiesEnabledAsContext == true
            ? (await _coreCommander.ExecuteAsync<ContactProperties.GetContactPropertiesOutput>(
                state.Origin,
                CommandNames.GetContactProperties,
                new ContactProperties.GetContactPropertiesInput(state.Identity, contactId!)))
            ?.ContactProperties
            : null;
    }

    private async Task<DateTimeOffset?> GetRetrievalWindowTimestamp(
        ProxyState state,
        CallStep<AgentRecommendReplyStepArgs> callStep)
    {
        if (callStep.Args.RetrievalWindowTimestampExpr == null)
        {
            return null;
        }

        var retrievalWindowTimestamp = await _stateEvaluator.EvaluateExpressionAsync<string>(
            state,
            callStep.Args.RetrievalWindowTimestampExpr);

        return DateTimeOffset.TryParse(retrievalWindowTimestamp, out var result) ? result : null;
    }

    private async Task<GetIntelligentHubUsageFilterOutput.IntelligentHubUsageFilterOutput?>
        GetIntelligentHubUsageFilterAsync(ProxyState state)
    {
        var response = await _coreCommander.ExecuteAsync<GetIntelligentHubUsageFilterOutput>(
            state.Origin,
            "GetIntelligentHubUsageFilter",
            new GetIntelligentHubUsageFilterInput(state.Identity.SleekflowCompanyId));

        return response?.IntelligentHubUsageFilter;
    }

    private async Task<string?> GetInputAsync(
        ProxyState state,
        CallStep<AgentRecommendReplyStepArgs> callStep)
    {
        if (callStep.Args.InputExpr == null)
        {
            return null;
        }

        return await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.InputExpr);
    }

    private async Task<bool> GetIsCompanyHasAiPocPlanAsync(ProxyState state)
    {
        try
        {
            var response = await _coreCommander.ExecuteAsync<GetIsCompanyHasAiPocPlanOutput>(
                state.Origin,
                "GetIsCompanyHasAiPocPlan",
                new GetIsCompanyHasAiPocPlanInput(state.Identity.SleekflowCompanyId));

            return response?.IsCompanyHasAiPocPlan ?? false;
        }
        catch (Exception)
        {
            return false;
        }
    }
}