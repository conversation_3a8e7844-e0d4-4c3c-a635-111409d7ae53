using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Blobs;
using Sleekflow.MessagingHub.Models.Blobs;
using Sleekflow.MessagingHub.Models.Constants;

namespace Sleekflow.MessagingHub.Triggers.Medias;

[TriggerGroup(ControllerNames.Medias)]
public class GetUploadMediaLink
    : ITrigger<
        GetUploadMediaLink.GetUploadMediaLinkInput,
        GetUploadMediaLink.GetUploadMediaLinkOutput>
{
    private readonly IBlobService _blobService;

    public GetUploadMediaLink(IBlobService blobService)
    {
        _blobService = blobService;
    }

    public class GetUploadMediaLinkInput
    {
    }

    public class GetUploadMediaLinkOutput
    {
        [JsonProperty("blob_sas_url")]
        public IBlobService.GetBlobSasUrlOutput BlobSasUrl { get; set; }

        [JsonConstructor]
        public GetUploadMediaLinkOutput(IBlobService.GetBlobSasUrlOutput blobSasUrl)
        {
            BlobSasUrl = blobSasUrl;
        }
    }

    public Task<GetUploadMediaLinkOutput> F(GetUploadMediaLinkInput getUploadMediaLinkInput)
    {
        return Task.FromResult(new GetUploadMediaLinkOutput(_blobService.CreateBlobSasUrl(BlobStorageType.External)));
    }
}