﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnContactManuallyEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactManuallyEnrolled; }
    }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnContactManuallyEnrolledEventBody(
        DateTimeOffset createdAt,
        string contactId,
        Dictionary<string, object?> contact,
        string workflowId,
        string workflowVersionedId)
        : base(createdAt)
    {
        ContactId = contactId;
        Contact = contact;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}