using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Workers.Triggers.CloudApis;

public class OnCloudApiConversationAnalyticsTrigger
{
    private readonly IBus _bus;

    public OnCloudApiConversationAnalyticsTrigger(
        IBus bus)
    {
        _bus = bus;
    }

    [Function("OnCloudApiConversationAnalyticsTrigger")]
    public async Task RunAsync(
        [TimerTrigger("0 */30 * * * *")]
        TimerInfo timerInfo)
    {
        await _bus.Publish(
            new OnCloudApiConversationAnalyticsTriggerEvent(DateTimeOffset.UtcNow));
    }
}