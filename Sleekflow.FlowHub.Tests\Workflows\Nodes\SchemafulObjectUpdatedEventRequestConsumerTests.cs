﻿using System.Reflection;
using Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

namespace Sleekflow.FlowHub.Tests.Workflows.Nodes;

public class SchemafulObjectUpdatedEventRequestConsumerTests
{
    private static bool MyEquals(object? value1, object? value2)
    {
        var method = typeof(SchemafulObjectUpdatedEventRequestConsumer)
            .GetMethod(
                "Equals",
                BindingFlags.NonPublic | BindingFlags.Static);

        var result = method!.Invoke(
            null,
            [
                value1,
                value2
            ]);

        return (bool) result!;
    }

    [Test]
    public void EqualsTest_HandelNullValue_WithBothNull_ShouldReturnTrue()
    {
        var equals = MyEquals(null, null);

        Assert.That(equals, Is.True);
    }

    [Test]
    public void EqualsTestHandelNullValue_WithSingleNull_ShouldReturnFalse()
    {
        var v1 = "s1";
        var v2 = "s2";

        var equals1 = MyEquals(v1, v2);
        var equals2 = MyEquals(null, v2);
        var equals3 = MyEquals(v1, null);

        Assert.That(equals1, Is.False);
        Assert.That(equals2, Is.False);
        Assert.That(equals3, Is.False);
    }

    [Test]
    public void EqualsTest_DateTimeOffset_WithOnlyMilliSecondsDifferent_ShouldReturnTrue()
    {
        var d1 = DateTimeOffset.Parse("2000-01-01T12:34:56.123Z");
        var d2 = DateTimeOffset.Parse("2000-01-01T12:34:56.456Z");

        var equals = MyEquals(d1, d2);

        Assert.That(equals, Is.True);
    }

    [Test]
    public void EqualsTest_ArrayOfObject_WithSameValue_ShouldReturnTrue()
    {
        var arr1 = new[]
        {
            new { a = "a_1", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var arr2 = new[]
        {
            new { a = "a_1", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var equals = MyEquals(arr1, arr2);

        Assert.That(equals, Is.True);
    }

    [Test]
    public void EqualsTest_ArrayOfObject_WithDifferentKey_ShouldReturnFalse()
    {
        var arr1 = new[]
        {
            new { a = "a_1", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var arr2 = new[]
        {
            new { aa = "a_1", b = new { bb = "bb_1"} },
            new { aa = "a_2", b = new { bb = "bb_2"} },
        };

        var equals = MyEquals(arr1, arr2);

        Assert.That(equals, Is.False);
    }

    [Test]
    public void EqualsTest_ArrayOfObject_WithDifferentValue_ShouldReturnFalse()
    {
        var arr1 = new[]
        {
            new { a = "a_1", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var arr2 = new[]
        {
            new { a = "aaa", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var equals = MyEquals(arr1, arr2);

        Assert.That(equals, Is.False);
    }

    [Test]
    public void EqualsTest_ArrayOfObject_WithMissingKey_ShouldReturnFalse()
    {
        var arr1 = new List<dynamic>
        {
            new { a = "a_1", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var arr2 = new List<dynamic>
        {
            new { a = "a_1" },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var equals = MyEquals(arr1, arr2);

        Assert.That(equals, Is.False);
    }

    [Test]
    public void EqualsTest_ArrayOfObject_WithDifferentOrder_ShouldReturnFalse()
    {
        var arr1 = new []
        {
            new { a = "a_1", b = new { bb = "bb_1"} },
            new { a = "a_2", b = new { bb = "bb_2"} },
        };

        var arr2 = new []
        {
            new { a = "a_2", b = new { bb = "bb_2"} },
            new { a = "a_1", b = new { bb = "bb_1"} },
        };

        var equals = MyEquals(arr1, arr2);

        Assert.That(equals, Is.False);
    }
}