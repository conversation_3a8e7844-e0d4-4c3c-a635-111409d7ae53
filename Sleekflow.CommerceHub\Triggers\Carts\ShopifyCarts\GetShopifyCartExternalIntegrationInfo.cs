using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts.ShopifyCarts;

[TriggerGroup(ControllerNames.ShopifyCarts)]
public class GetShopifyCartExternalIntegrationInfo
    : ITrigger<
        GetShopifyCartExternalIntegrationInfo.GetShopifyCartExternalIntegrationInfoInput,
        GetShopifyCartExternalIntegrationInfo.GetShopifyCartExternalIntegrationInfoOutput>
{
    private readonly IShopifyCartService _shopifyCartService;

    public GetShopifyCartExternalIntegrationInfo(
        IShopifyCartService shopifyCartService)
    {
        _shopifyCartService = shopifyCartService;
    }

    public class GetShopifyCartExternalIntegrationInfoInput :
        IHasSleekflowCompanyId, IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("cart_status")]
        public string CartStatus { get; set; }

        [Required]
        [JsonProperty("shopify_cart_token")]
        public string ShopifyCartToken { get; set; }

        [JsonConstructor]
        public GetShopifyCartExternalIntegrationInfoInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            string cartStatus,
            string shopifyCartToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            CartStatus = cartStatus;
            ShopifyCartToken = shopifyCartToken;
        }
    }

    public class GetShopifyCartExternalIntegrationInfoOutput
    {
        [JsonProperty("shopify_cart_external_integration_info")]
        public ShopifyCartExternalIntegrationInfo? ShopifyCartExternalIntegrationInfo { get; set; }

        [JsonConstructor]
        public GetShopifyCartExternalIntegrationInfoOutput(
            ShopifyCartExternalIntegrationInfo? shopifyCartExternalIntegrationInfo)
        {
            ShopifyCartExternalIntegrationInfo = shopifyCartExternalIntegrationInfo;
        }
    }

    public async Task<GetShopifyCartExternalIntegrationInfoOutput> F(
        GetShopifyCartExternalIntegrationInfoInput getShopifyCartExternalIntegrationInfoInput)
    {
        var shopifyCart = await _shopifyCartService.GetShopifyCartAsync(
            getShopifyCartExternalIntegrationInfoInput.SleekflowCompanyId,
            getShopifyCartExternalIntegrationInfoInput.SleekflowUserProfileId,
            getShopifyCartExternalIntegrationInfoInput.StoreId,
            getShopifyCartExternalIntegrationInfoInput.CartStatus,
            getShopifyCartExternalIntegrationInfoInput.ShopifyCartToken);

        if (shopifyCart?.CartExternalIntegrationInfo is null)
        {
            return new GetShopifyCartExternalIntegrationInfoOutput(null);
        }

        return new GetShopifyCartExternalIntegrationInfoOutput(
            (ShopifyCartExternalIntegrationInfo)shopifyCart.CartExternalIntegrationInfo);
    }
}