using System.ComponentModel.DataAnnotations;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors;

public abstract class GeneralStepExecutor<T>
    : IStepExecutor
    where T : Step
{
    private readonly IWorkflowStepLocator _workflowStepLocator;
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IServiceProvider _serviceProvider;

    protected GeneralStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider)
    {
        _workflowStepLocator = workflowStepLocator;
        _workflowRuntimeService = workflowRuntimeService;
        _serviceProvider = serviceProvider;
    }

    public virtual bool IsMatched(Step step)
    {
        return step is T;
    }

    public virtual T ToConcreteStep(Step step)
    {
        var concreteStep = step as T ?? throw new InvalidOperationException();

        var validationResults = new List<ValidationResult>();

        var isValid = Validator.TryValidateObject(
            concreteStep,
            new ValidationContext(concreteStep, _serviceProvider, null),
            validationResults,
            validateAllProperties: true);

        if (isValid == false)
        {
            throw new SfValidationException(validationResults);
        }

        return concreteStep;
    }

    public abstract Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync);

    public virtual async Task<Step?> OnStepCompleteAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries)
    {
        var nextStep = _workflowStepLocator.GetNextStep(workflow, step, stackEntries);

        if (nextStep == null
            && _workflowStepLocator.IsRootStep(workflow, step))
        {
            await _workflowRuntimeService.CompleteWorkflowAsync(state);
        }
        else if (nextStep == null)
        {
            // This is intended empty
        }
        else
        {
            return nextStep;
        }

        return null;
    }
}