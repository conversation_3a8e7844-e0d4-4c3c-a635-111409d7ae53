﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.CrmHubConfigs;

[TriggerGroup(TriggerGroups.CrmHubConfigs)]
public class GetCrmHubConfig : ITrigger<GetCrmHubConfig.GetCrmHubConfigInput, GetCrmHubConfig.GetCrmHubConfigOutput>
{
    private readonly ICrmHubConfigService _crmHubConfigService;

    public GetCrmHubConfig(ICrmHubConfigService crmHubConfigService)
    {
        _crmHubConfigService = crmHubConfigService;
    }

    public class GetCrmHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetCrmHubConfigInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetCrmHubConfigOutput
    {
        [JsonProperty("crm_hub_config")]
        public CrmHubConfig CrmHubConfig { get; set; }

        [JsonConstructor]
        public GetCrmHubConfigOutput(CrmHubConfig crmHubConfig)
        {
            CrmHubConfig = crmHubConfig;
        }
    }

    public async Task<GetCrmHubConfigOutput> F(GetCrmHubConfigInput getCrmHubConfigInput)
    {
        var crmHubConfig = await _crmHubConfigService.GetCrmHubConfigAsync(getCrmHubConfigInput.SleekflowCompanyId);

        return new GetCrmHubConfigOutput(crmHubConfig);
    }
}