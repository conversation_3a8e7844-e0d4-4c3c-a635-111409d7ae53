using Sleekflow.EmailHub.Models.Authentications;

namespace Sleekflow.EmailHub.Services;

public interface IEmailOAuthService
{
    Task<EmailAuthentication> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default);

    Task HandleAuthCallbackAndStoreAsync(
        string code,
        string nonce,
        CancellationToken cancellationToken = default);
}