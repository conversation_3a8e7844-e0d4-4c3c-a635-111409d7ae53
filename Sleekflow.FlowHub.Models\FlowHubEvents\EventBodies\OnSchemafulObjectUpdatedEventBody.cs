﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnSchemafulObjectUpdatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnSchemafulObjecUpdated; }
    }

    [Required]
    [JsonProperty("schemaful_object_id")]
    public string SchemafulObjectId { get; set; }

    [Required]
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [Required]
    [JsonProperty("primary_property_value")]
    public string PrimaryPropertyValue { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; } // Not named as [SleekflowUserProfileId] to align with other event body


    [Required]
    [JsonProperty("pre_updated_contact_id")]
    public string PreUpdatedContactId { get; set; }

    [Required]
    [JsonProperty("post_updated_contact_id")]
    public string PostUpdatedContactId { get; set; }

    [Required]
    [JsonProperty("property_values")]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [Required]
    [JsonProperty("pre_updated_property_values")]
    public Dictionary<string, object?> PreUpdatedPropertyValues { get; set; }

    [Required]
    [JsonProperty("post_updated_property_values")]
    public Dictionary<string, object?> PostUpdatedPropertyValues { get; set; }

    [Required]
    [JsonProperty("change_entries")]
    public List<OnSchemafulObjectUpdatedEventBodyChangeEntry> ChangeEntries { get; set; }

    [Required]
    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonConstructor]
    public OnSchemafulObjectUpdatedEventBody(
        DateTimeOffset createdAt,
        string schemafulObjectId,
        string schemaId,
        string primaryPropertyValue,
        string preUpdatedContactId,
        string postUpdatedContactId,
        Dictionary<string, object?> preUpdatedPropertyValues,
        Dictionary<string, object?> postUpdatedPropertyValues,
        List<OnSchemafulObjectUpdatedEventBodyChangeEntry> changeEntries,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
        : base(createdAt)
    {
        SchemafulObjectId = schemafulObjectId;
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        ContactId = postUpdatedContactId;
        PreUpdatedContactId = preUpdatedContactId;
        PostUpdatedContactId = postUpdatedContactId;
        PropertyValues = postUpdatedPropertyValues;
        PreUpdatedPropertyValues = preUpdatedPropertyValues;
        PostUpdatedPropertyValues = postUpdatedPropertyValues;
        ChangeEntries = changeEntries;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
    }
}

public class OnSchemafulObjectUpdatedEventBodyChangeEntry
{
    [JsonProperty("property_name")]
    public string PropertyName { get; set; }

    [JsonProperty("property_id")]
    public string PropertyId { get; set; }

    [JsonProperty("from_value")]
    public object? FromValue { get; set; }

    [JsonProperty("to_value")]
    public object? ToValue { get; set; }

    [JsonConstructor]
    public OnSchemafulObjectUpdatedEventBodyChangeEntry(
        string propertyName,
        string propertyId,
        object? fromValue,
        object? toValue)
    {
        PropertyName = propertyName;
        PropertyId = propertyId;
        FromValue = fromValue;
        ToValue = toValue;
    }
}