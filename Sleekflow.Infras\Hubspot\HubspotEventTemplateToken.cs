﻿using Newtonsoft.Json;

namespace Sleekflow.Infras.Hubspot;

public class HubspotEventTemplateToken
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("objectPropertyName")]
    public string? ObjectPropertyName { get; set; }

    [JsonProperty("options")]
    public List<HubspotOption> Options { get; set; }

    [JsonConstructor]
    public HubspotEventTemplateToken(
        string name,
        string type,
        DateTime? createdAt,
        DateTime? updatedAt,
        string label,
        string? objectPropertyName,
        List<HubspotOption> options)
    {
        Name = name;
        Type = type;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        Label = label;
        ObjectPropertyName = objectPropertyName;
        Options = options;
    }

    public HubspotEventTemplateToken(
        string name,
        string type,
        string label,
        List<HubspotOption> options)
    {
        Name = name;
        Type = type;
        Label = label;
        Options = options;
    }
}