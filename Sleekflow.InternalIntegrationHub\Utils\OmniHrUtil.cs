using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

namespace Sleekflow.InternalIntegrationHub.Utils;

public static class OmniHrUtil
{
    // no direct mapping between OmniHr location and NetSuite subsidiary
    // hardcoding the mapping for now
    public static Dictionary<string, string> GetLocationMappings()
    {
        return new Dictionary<string, string>
        {
            {
                "Hong Kong", "HK"
            },
            {
                "Singapore", "SG"
            },
            {
                "Malaysia", "MY"
            },
            {
                "Brazil", "BR"
            },
            {
                "United Arab Emirates", "UAE"
            },
            {
                "UAE", "UAE"
            },
            {
                "Indonesia", "ID"
            },
            {
                "United Kingdom", "UK"
            },

            // special case
            {
                "Taiwan", "HK"
            },
            {
                "Canada", "HK"
            }
        };
    }

    public static string LocationConverter(string location, List<SubsidiaryDetailsResponse> netSuite)
    {
        var locationMap = GetLocationMappings();
        var locationCode = locationMap.FirstOrDefault(l => string.Equals(l.Key, location));
        if (locationCode.Value != null)
        {
            var subsidiary = netSuite.Find(s => string.Equals(s.Name, locationCode.Value));
            if (subsidiary != null)
            {
                return subsidiary.Id;
            }
        }

        // default to Hong Kong
        var defaultSubsidiary = netSuite.Find(s => string.Equals(s.Name, "HK"));
        return defaultSubsidiary!.Id;
    }

    public static string CurrencyConverter(CompensationCurrency? omni, List<CurrencyDetailsResponse> netSuite)
    {
        if (omni == null)
        {
            return "1";
        }

        var currency = netSuite.Find(c => string.Equals(c.Symbol, omni.Value));
        if (currency != null)
        {
            return currency.Id;
        }

        // default to USD
        return "1";
    }
}