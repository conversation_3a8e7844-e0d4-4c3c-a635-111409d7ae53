﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class GetSyncObjectsProgressOutput
{
    [JsonProperty("count")]
    public int Count { get; set; }

    [JsonProperty("last_update_time")]
    public DateTime LastUpdateTime { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonConstructor]
    public GetSyncObjectsProgressOutput(
        int count,
        DateTime lastUpdateTime,
        string status)
    {
        Count = count;
        LastUpdateTime = lastUpdateTime;
        Status = status;
    }
}