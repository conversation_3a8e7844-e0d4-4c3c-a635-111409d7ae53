using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

public class ProviderLimit
{
    [JsonProperty("is_read_only_integration")]
    public bool IsReadOnlyIntegration { get; set; } = false;

    [JsonProperty("object_count_limit_dict")]
    public Dictionary<string, int> ObjectCountLimitDict { get; set; }

    [JsonConstructor]
    public ProviderLimit(bool isReadOnlyIntegration, Dictionary<string, int> objectCountLimitDict)
    {
        IsReadOnlyIntegration = isReadOnlyIntegration;
        ObjectCountLimitDict = objectCountLimitDict;
    }
}