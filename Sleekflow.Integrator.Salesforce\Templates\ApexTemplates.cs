namespace Sleekflow.Integrator.Salesforce.Templates;

// https://github.com/jamesward/salesforce-webhook-creator/blob/master/app/apextemplates/
public static class ApexTemplates
{
    private const string WebhookClassTemplate = @"
public class SleekflowWebhookUtility implements HttpCalloutMock {

    public static HttpRequest request;
    public static HttpResponse response;

    public HTTPResponse respond(HTTPRequest req) {
        request = req;
        response = new HttpResponse();
        response.setStatusCode(200);
        return response;
    }

    public static String jsonContent(List<Object> triggerNew, List<Object> triggerOld, String sleekflowCompanyId, String type, String key) {
        String newObjects = '[]';
        if (triggerNew != null) {
            newObjects = JSON.serialize(triggerNew);
        }

        String oldObjects = '[]';
        if (triggerOld != null) {
            oldObjects = JSON.serialize(triggerOld);
        }

        String userId = JSON.serialize(UserInfo.getUserId());
        String content = '{""new"": ' + newObjects + ', ""old"": ' + oldObjects + ', ""user_id"": ' + userId + ', ""sleekflow_company_id"": ""' + sleekflowCompanyId + '"", ""type"": ""' + type + '"", ""key"": ""' + key + '""}';
        
        return content;
    }

    @future(callout=true)
    public static void callout(String url, String content) {

        if (Test.isRunningTest()) {
            Test.setMock(HttpCalloutMock.class, new SleekflowWebhookUtility());
        }

        Http h = new Http();

        HttpRequest req = new HttpRequest();
        req.setEndpoint(url);
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        req.setBody(content);

        h.send(req);
    }

}
";

    private const string TriggerTestTemplate = @"
@isTest
public class {{PARAM_NAME}} {

    static SObject mock(String sobjectName) {
        SObjectType t = Schema.getGlobalDescribe().get(sobjectName);

        SObject o = t.newSobject();

        Map<String, Schema.SObjectField> m = t.getDescribe().fields.getMap();

        for (String fieldName : m.keySet()) {
            DescribeFieldResult f = m.get(fieldName).getDescribe();
            if (!f.isNillable() && f.isCreateable() && !f.isDefaultedOnCreate()) {
                if (f.getType() == DisplayType.Boolean) {
                    o.put(f.getName(), false);
                }
                else if (f.getType() == DisplayType.Currency) {
                    o.put(f.getName(), 0);
                }
                else if (f.getType() == DisplayType.Date) {
                    o.put(f.getName(), Date.today());
                }
                else if (f.getType() == DisplayType.DateTime) {
                    o.put(f.getName(), System.now());
                }
                else if (f.getType() == DisplayType.Double) {
                    o.put(f.getName(), 0.0);
                }
                else if (f.getType() == DisplayType.Email) {
                    o.put(f.getName(), '<EMAIL>');
                }
                else if (f.getType() == DisplayType.Integer) {
                    o.put(f.getName(), 0);
                }
                else if (f.getType() == DisplayType.Percent) {
                    o.put(f.getName(), 0);
                }
                else if (f.getType() == DisplayType.Phone) {
                    o.put(f.getName(), '************');
                }
                else if (f.getType() == DisplayType.String) {
                    o.put(f.getName(), 'TEST');
                }
                else if (f.getType() == DisplayType.TextArea) {
                    o.put(f.getName(), 'TEST');
                }
                else if (f.getType() == DisplayType.Time) {
                    o.put(f.getName(), System.now().time());
                }
                else if (f.getType() == DisplayType.URL) {
                    o.put(f.getName(), 'http://foo.com');
                }
                else if (f.getType() == DisplayType.PickList) {
                    o.put(f.getName(), f.getPicklistValues()[0].getValue());
                }
            }
        }
        return o;
    }

    @isTest static void testTrigger() {
        SObject o = mock('Contact');

        Test.startTest();
        insert o;
        update o;
        delete o;
        Test.stopTest();

        System.assertEquals(200, SleekflowWebhookUtility.response.getStatusCode());
        System.assertEquals('{{PARAM_WEBHOOK_URL}}', SleekflowWebhookUtility.request.getEndpoint());

        if (SleekflowWebhookUtility.request != null) {
            Map<String, Object> jsonResponse = (Map<String, Object>) JSON.deserializeUntyped(SleekflowWebhookUtility.request.getBody());
            System.assertNotEquals(null, jsonResponse);
        }
    }

}
";

    private const string TriggerTemplate = @"
trigger {{PARAM_NAME}} on {{PARAM_SOBJECT}} ({{PARAM_ON_EVENTS}}) {
    String url = '{{PARAM_WEBHOOK_URL}}';
    String content = SleekflowWebhookUtility.jsonContent(Trigger.new, Trigger.old, '{{PARAM_SLEEKFLOW_COMPANY_ID}}', '{{PARAM_SOBJECT}}', '{{PARAM_KEY}}');

    SleekflowWebhookUtility.callout(url, content);
}
";

    public static string GetWebhookClassTemplate()
    {
        return WebhookClassTemplate;
    }

    public static string GetTriggerTestTemplate(
        string name,
        string webhookUrl)
    {
        return TriggerTestTemplate
            .Replace("{{PARAM_NAME}}", name)
            .Replace("{{PARAM_WEBHOOK_URL}}", webhookUrl);
    }

    public static string GetTriggerTemplate(
        string name,
        string sobject,
        string onEvents,
        string webhookUrl,
        string sleekflowCompanyId,
        string key)
    {
        return TriggerTemplate
            .Replace("{{PARAM_NAME}}", name)
            .Replace("{{PARAM_SOBJECT}}", sobject)
            .Replace("{{PARAM_ON_EVENTS}}", onEvents)
            .Replace("{{PARAM_WEBHOOK_URL}}", webhookUrl)
            .Replace("{{PARAM_SLEEKFLOW_COMPANY_ID}}", sleekflowCompanyId)
            .Replace("{{PARAM_KEY}}", key);
    }
}