using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Summary;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.Plugins;

public interface ISummaryPlugin
{
    Task<string> SummarizeTextAsync(Kernel kernel, string inputText);

    Task<string> TransformCsvToText(Kernel kernel, string inputText);

    Task<string> TransformHtmlTableToText(Kernel kernel, string inputText);

    Task<string> SummarizeConversationAsync(
        Kernel kernel,
        List<string> chatHistory);

    Task<ConversationSummary?> SummarizeConversationForHandoverAsync(
        Kernel kernel,
        string chatHistory,
        string handoverReason,
        string tone,
        string responseLevel,
        string languageIsoCode,
        Dictionary<string, string> contactProperties);
}

public class SummaryPlugin : ISummaryPlugin, IScopedService
{
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public SummaryPlugin(
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("SummarizeText")]
    [Description("Summarizes the original text.")]
    [return: Description("The summarized text.")]
    public async Task<string> SummarizeTextAsync(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var summarizeTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SummarizeText",
                Description =
                    "Summarizes the original text into a concise summary of 50 words or fewer.",
                Template =
                    """
                    <message role="system">
                    Please provide a concise summary of the following content within 50 words or fewer. Your summary should be clear, accurate, and include all key information. Please also include all the quoted information e.g. [abc.pdf] at the end of the summary as they are the referenced citations.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The summarized text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await summarizeTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Summarize failed";
    }

    [KernelFunction("TransformCsvToText")]
    [Description("Transforms the CSV table into human-readable paragraphs.")]
    [return: Description("The transformed text.")]
    public async Task<string> TransformCsvToText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var transformCsvTableToTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "TransformCsvTableToText",
                Description =
                    "Transforms the CSV table into human-readable paragraphs.",
                Template =
                    """"
                    <message role="system">
                    We need to transform the following CSV table into human-readable paragraphs to optimize indexing and searching in ElasticSearch. Please adhere to the following instructions:

                    Language Determination: Produce the output in the same language as the provided table.
                    Paragraph Structure: Convert the CSV data into well-organized and coherent paragraphs. Each paragraph should follow a similar structure for consistency.
                    Data Coverage: Ensure that all specific data points and the structure of the original table are fully represented in the paragraphs.
                    Clarity and Engagement: Write in a clear and engaging manner that is easy to understand. Avoid using bullet points or lists; instead, present the information seamlessly within the text.
                    No Summary Needed: Do not include a summary paragraph. Focus solely on transforming the table data into paragraphs.
                    Purpose Alignment: The transformed paragraphs should clearly explain the table's information to facilitate optimal indexing and searching in ElasticSearch.
                    """
                    </message>

                    <message role="user">
                    大廈名稱,樓層,單位,每個住宅物業的非結構的預製外牆的總面積 【平方米】
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,Al,0.929
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,A2,0.545
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,B,0.814
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,C,1.074
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,D,0.632
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,E,0.755
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,F,0.776
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,G,0.626
                    Charlot Tower 1B Charlot 第 1B 座,29/F-33/F 29 樓至 33 樓,H,0.779
                    Charlot Tower 1B Charlot 第 1B 座,Penthouse Floor (35/F) 頂層 (35 樓),AT,0.539
                    Charlot Tower 1B Charlot 第 1B 座,Penthouse Floor (35/F) 頂層 (35 樓),A2,0.000
                    Charlot Tower 1B Charlot 第 1B 座,Penthouse Floor (35/F) 頂層 (35 樓),A3,0.000
                    </message>

                    <message role="assistant">
                    在 Charlot Tower 1B Charlot 第 1B 座大廈中，29/F-33/F 29 樓至 33 樓的各個單位的非結構的預製外牆的總面積如下：單位 A1 的面積為 0.929 平方米；單位 A2 的面積為 0.545 平方米；單位 B 的面積為 0.814 平方米；單位 C 的面積為 1.074 平方米；單位 D 的面積為 0.632 平方米；單位 E 的面積為 0.755 平方米；單位 F 的面積為 0.776 平方米；單位 G 的面積為 0.626 平方米；單位 H 的面積為 0.779 平方米。

                    此外，在 Charlot Tower 1B Charlot 第 1B 座大廈的 Penthouse Floor (35/F) 頂層 (35 樓)，非結構的預製外牆的總面積如下：單位 AT 的面積為 0.539 平方米；單位 A2 的面積為 0.000 平方米；單位 A3 的面積為 0.000 平方米。
                    </message>

                    <message role="user">
                    CAI,Description,Material Group,DOT 2023,DOT 2024, List Price 2024 AED ,Rim Size
                    CTM772786,175/65R14 82H TL EXM2+ MI,CTMPCR001,4+,4+,351,14
                    CTM498361,175/70R14 88T XLTL EXM2+ MI,CTMPCR001,1,4+,344,14
                    CTM086627,185/65R14 86H TL EXM2+ MI,CTMPCR002, -   ,4+,365,14
                    CTM920422,185/70 R 14 88H TL EXM2+ MI,CTMPCR002,4,4+,386,14
                    CTM053272,165/70R14 81T TL ENERGY XM2 + MI,CTMPCR001, -   ,4+,338,14
                    </message>

                    <message role="assistant">
                    The CTM772786 is a Michelin Energy XM2+ tire with specifications 175/65R14 82H TL. This tubeless tire has a width of 175mm, aspect ratio of 65%, and fits 14-inch rims. Its load index of 82 means it can carry up to 475 kg, while the H speed rating allows for speeds up to 210 km/h. The tire maintained a strong DOT rating of 4+ from 2023 to 2024 and is priced at 351 AED in the UAE market. This model falls under the material group CTMPCR001.
                    The CTM498361 is a Michelin Energy XM2+ tire with specifications 175/70R14 88T XL TL. This tubeless Extra Load tire features a 175mm width, 70% aspect ratio, and fits 14-inch rims. With a load index of 88, it can support up to 560 kg, and its T speed rating allows for speeds up to 190 km/h. The tire's DOT rating improved significantly from 1 in 2023 to 4+ in 2024, and it's priced at 344 AED in the UAE market. This model belongs to material group CTMPCR001.
                    The CTM086627 is a Michelin Energy XM2+ tire with specifications 185/65R14 86H TL. This tubeless tire has a width of 185mm, aspect ratio of 65%, and fits 14-inch rims. Its load index of 86 indicates a load capacity of 530 kg, and the H speed rating permits speeds up to 210 km/h. While it had no DOT rating in 2023, it achieved a 4+ rating in 2024. The tire is priced at 365 AED in the UAE market and falls under material group CTMPCR002.
                    The CTM920422 is a Michelin Energy XM2+ tire with specifications 185/70R14 88H TL. This tubeless tire combines a 185mm width with a 70% aspect ratio for 14-inch rims. Its load index of 88 provides a maximum load capacity of 560 kg, and the H speed rating allows for speeds up to 210 km/h. The tire's DOT rating improved from 4 in 2023 to 4+ in 2024, and it's priced at 386 AED in the UAE market. This model belongs to material group CTMPCR002.
                    The CTM053272 is a Michelin Energy XM2+ tire with specifications 165/70R14 81T TL. This tubeless tire features the narrowest width at 165mm, with a 70% aspect ratio for 14-inch rims. Its load index of 81 supports up to 462 kg, and the T speed rating allows for speeds up to 190 km/h. While it had no DOT rating in 2023, it achieved a 4+ rating in 2024. The tire is priced at 338 AED in the UAE market and belongs to material group CTMPCR001.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """",
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The transformed text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await transformCsvTableToTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Transforming CSV table to text failed";
    }

    [KernelFunction("TransformHtmlTableToText")]
    [Description("Transforms the HTML table into human-readable paragraphs.")]
    [return: Description("The transformed text.")]
    public async Task<string> TransformHtmlTableToText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var transformHtmlTableToTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "TransformHtmlTableToText",
                Description =
                    "Transforms the HTML table into human-readable paragraphs.",
                Template =
                    """"
                    <message role="system">
                    Transform the HTML table containing colspan and rowspan into human-readable paragraphs. Ensure the paragraphs are well-organized, engaging, and easy to understand without using bullet points or lists.

                    Additional Instructions:"""
                    - Determine the result language based on the language of given table.
                    - Make sure the result covering the given table's structure and specific data points.
                    - Each paragraph should clearly explain the table's information for optimal indexing and searching in ElasticSearch.
                    """
                    </message>

                    <message role="user">
                    <table><tr><th>大廈名稱</th><th>樓層</th><th>單位</th><th>每個住宅物業的非結構的預製外牆的總面積 【平方米】</th></tr><tr><td rowspan="12">Charlot Tower 1B Charlot 第 1B 座</td><td rowspan="9">29/F-33/F 29 樓至 33 樓</td><td>Al</td><td>0.929</td></tr><tr><td>A2</td><td>0.545</td></tr><tr><td>B</td><td>0.814</td></tr><tr><td>C</td><td>1.074</td></tr><tr><td>D</td><td>0.632</td></tr><tr><td>E</td><td>0.755</td></tr><tr><td>F</td><td>0.776</td></tr><tr><td>G</td><td>0.626</td></tr><tr><td>H</td><td>0.779</td></tr><tr><td rowspan="3">Penthouse Floor (35/F) 頂層 (35 樓)</td><td>AT</td><td>0.539</td></tr><tr><td>A2</td><td>0.000</td></tr><tr><td>A3</td><td>0.000</td></tr></table>
                    </message>
                    <message role="assistant">
                    在 Charlot Tower 1B Charlot 第 1B 座大廈中，29/F-33/F 29 樓至 33 樓的各個單位的非結構的預製外牆的總面積如下：單位 A1 的面積為 0.929 平方米；單位 A2 的面積為 0.545 平方米；單位 B 的面積為 0.814 平方米；單位 C 的面積為 1.074 平方米；單位 D 的面積為 0.632 平方米；單位 E 的面積為 0.755 平方米；單位 F 的面積為 0.776 平方米；單位 G 的面積為 0.626 平方米；單位 H 的面積為 0.779 平方米。

                    此外，在 Charlot Tower 1B Charlot 第 1B 座大廈的 Penthouse Floor (35/F) 頂層 (35 樓)，非結構的預製外牆的總面積如下：單位 AT 的面積為 0.539 平方米；單位 A2 的面積為 0.000 平方米；單位 A3 的面積為 0.000 平方米。
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """",
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The transformed text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await transformHtmlTableToTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Transforming HTML table to text failed";
    }

    [KernelFunction("SummarizeConversation")]
    [Description("Summarizes the conversation.")]
    [return: Description("The summarized text.")]
    public async Task<string> SummarizeConversationAsync(
        Kernel kernel,
        List<string> chatHistory)
    {
        // Ensure more than 10 messages
        if (chatHistory.Count <= 10)
        {
            return string.Join("--\n", chatHistory);
        }

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        var summarizeConversationFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SummarizeConversation",
                Description =
                    "Summarizes the conversation to be more focused.",
                Template =
                    """
                    <message role="system">
                    **1. GOAL**
                    Create a concise summary of the summary between our company and a customer.

                    **2. RETURN FORMAT**
                    Produce the summary using this structure:

                    Background: [Necessary context to understand customer needs]
                    Important Points:
                    - [Key customer preferences]
                    - [Key context details]
                    - [Other key points from the conversation]

                    **3. WARNINGS**
                    - **Do Not** include excessive greetings or politeness that aren't part of the original conversation.
                    - **Do Not** include irrelevant historical data.
                    - **Do Not** lose track of the chronological order, ensuring the latest customer message is clearly the most recent.
                    - **Do Not** mix up the order or details of the conversation threads.
                    </message>

                    <message role="user">
                    {{$chat_history}}
                    </message>
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "chat_history"
                    }
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "The conversation summary"
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        // Exclude last 8 messages
        var excludeLast8Messages = chatHistory
            .Take(chatHistory.Count - 8)
            .ToList();

        // Last 8 messages
        var last8Messages = chatHistory
            .TakeLast(8)
            .ToList();

        var chatMessageContent = await summarizeConversationFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "chat_history", string.Join("--\n", excludeLast8Messages)
                },
            });

        var content =
            $"""
             ======Past conversation summary======
             {chatMessageContent?.Content ?? string.Empty}
             ======Past conversation summary======

             ======Latest 8 messages======
             {string.Join("--\n", last8Messages)}
             ======Latest 8 messages======
             """;

        return content;
    }

    [KernelFunction("SummarizeConversationForHandover")]
    [Description("Summarizes the conversation.")]
    [return: Description("The summarized text.")]
    public async Task<ConversationSummary?> SummarizeConversationForHandoverAsync(
        Kernel kernel,
        string chatHistory,
        string handoverReason,
        string tone = TargetToneTypes.Professional,
        string responseLevel = "",
        string languageIsoCode = "en",
        Dictionary<string, string>? contactProperties = null)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1, true);

        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            promptExecutionSettings,
            [
                new PromptExecutionSettingsUtils.Property("context", "string"),
                new PromptExecutionSettingsUtils.Property("handover_reason", "string"),
            ]);

        var summarizeTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SummarizeText",
                Description =
                    "Summarizes the original text into a concise summary of 50 words or fewer.",
                Template =
                    """
                    <message role="system">
                    Generate a handover summary for a human agent to help him/her understand the context of a conversation and the reason for this handover.
                    The summary should includes two key components: **"Context"** and **"Reason for Handover"**.
                    Follow the guidelines below to ensure clarity, professionalism, and usefulness.

                    ---

                    ### **Context**
                    Create a **concise, bullet-point summary** of the conversation, emphasizing the most relevant details and insights. The summary should:

                    1. Highlight the key points, decisions, and outcomes discussed.
                    2. Accurately reflect the conversation content, ensuring all crucial aspects are included.
                    3. Be clear, easy to understand, and professionally written.
                    4. Consider any provided user details (e.g., name, preferences) to personalize the interaction.
                    5. Adhere to any specified response length, ensuring it does not exceed the maximum word count set by the user.
                    6. Use a polite, professional, and supportive tone unless the user specifies a different tone preference.

                    ### **Reason for Handover**
                    Generate **two specific bullet points** explaining the reason for the handover:

                    **Handover Reason:** Clearly state why the conversation is being transferred to a human agent.
                       - Examples: "Handover from AI to human agent was triggered due to user request for specialized assistance," "Handover from AI to human agent was triggered due to complex issue requiring human intervention," or "Handover from AI to human agent was triggered due to escalation due to system limitation."

                    Language Preference
                    - Respond in the language represented by **ISO-639**

                    ### Response Format
                    The response should be in JSON format with the following properties:
                    - **context**: The summary of the conversation.
                    - **handover_reason**: The reason for the handover.
                    </message>

                    <message role="user">
                    ###Tone:```
                    Professional
                    ```

                    ###Response Length:```
                    100
                    ```

                    ###Language ISO Code:```
                    en_US
                    ```

                    ###Contact Properties:
                    ```
                    {"user_name": "John Doe", "email": "<EMAIL>", "phone": "+852 1234 5678", "company": "Sleekflow", "position": "Marketing Manager"}
                    ```
                    ### Chat History:
                    ```
                    User: What are the price for the plans?
                    Bot: The prices for the plan are as follows: Basic - $10/month, Standard - $20/month, Premium - $30/month. Each plan offers different features and support levels to cater to various business needs.
                    User: Great! What are the features of each plan?
                    Bot: The basic plan includes essential features like WhatsApp messaging and notifications. The standard plan offers additional features like templated messages and quick replies. The premium plan provides advanced functionalities such as automation setup and team collaboration tools.
                    User: How do I sign up for a plan I'm interested in?
                    Bot: We will guide you through the human-assisted onboarding process to help you choose the right plan and set up your account. Our team will assist you in selecting the best plan based on your needs and preferences.
                    ```
                    ### Handover Reason:```
                    The user expressed interest in a specific plan and inquired about promotional offers, indicating high intent and readiness to purchase.
                    ```
                    </message>

                    <message role="assistant">
                    {
                        "context": "- The user inquired about pricing and features for available plans. \n- The bot explained that the Basic plan costs $10/month and includes WhatsApp messaging and notifications, the Standard plan costs $20/month and adds templated messages and quick replies, while the Premium plan is $30/month and offers advanced features like automation setup and team collaboration tools.\n- For sign-up, the bot mentioned a human-assisted onboarding process to help users choose and set up the most suitable plan.",
                        "handover_reason" : "- Handover from AI to human agent was triggered due to the user expressed interest in a specific plan and inquired about promotional offers, indicating high intent and readiness to purchase."
                    }

                    <message role="user">
                    ###Tone:```
                    Professional
                    ```

                    ###Response Length:```
                    100
                    ```

                    ###Language ISO Code:```
                    zh-HK
                    ```

                    ###Contact Properties:```
                    {"user_name": "John Doe", "email": "<EMAIL>", "phone": "+852 1234 5678", "company": "Sleekflow", "position": "Marketing Manager"}
                    ```
                    ### Chat History:
                    ```
                    User: What are the price for the plans?
                    Bot: The prices for the plan are as follows: Basic - $10/month, Standard - $20/month, Premium - $30/month. Each plan offers different features and support levels to cater to various business needs.
                    User: Great! What are the features of each plan?
                    Bot: The basic plan includes essential features like WhatsApp messaging and notifications. The standard plan offers additional features like templated messages and quick replies. The premium plan provides advanced functionalities such as automation setup and team collaboration tools.
                    User: How do I sign up for a plan I'm interested in?
                    Bot: We will guide you through the human-assisted onboarding process to help you choose the right plan and set up your account. Our team will assist you in selecting the best plan based on your needs and preferences.
                    ```
                    ### Handover Reason:```
                    The user expressed interest in a specific plan and inquired about promotional offers, indicating high intent and readiness to purchase.
                    ```
                    </message>

                    <message role="assistant">
                    {
                        "context": "- 用戶查詢咗可用計劃嘅定價同功能。\n- 機械人解釋話 Basic 計劃每個月收費 $10，包含 WhatsApp 訊息同通知；Standard 計劃每個月 $20，加入咗範本訊息同快速回覆；而 Premium 計劃每個月 $30，提供更進階功能，好似自動化設定同團隊協作工具。\n- 喺註冊方面，機械人提到有人工協助上手流程，幫用戶揀同設定最合適嘅計劃。",
                        "handover_reason" : "- 由 AI 轉交比真人客服，因為用戶對某個計劃表示興趣，並且問到有冇推廣優惠，顯示用戶意向高同準備購買。"
                    }

                    <message role="user">
                    ###Tone:```
                    {{$tone}}
                    ```
                    ###Response Length:```
                    {{$response_length}}
                    ```
                    ###Language ISO Code:
                    ```
                    {{$language_iso_code}}
                    ```
                    ###Contact Properties:
                    ```
                    {{$contact_properties}}
                    ```
                    ### Chat History:
                    ```
                    {{$chat_history}}
                    ```
                    ### Handover Reason:
                    ```
                    {{$handover_reason}}
                    ```
                    </message>
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "tone"
                    },
                    new InputVariable
                    {
                        Name = "response_length"
                    },
                    new InputVariable()
                    {
                        Name = "language_iso_code"
                    },
                    new InputVariable
                    {
                        Name = "contact_properties",
                    },
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
                    new InputVariable()
                    {
                        Name = "handover_reason"
                    }
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "The summary in JSON format",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await summarizeTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "tone", tone
                },
                {
                    "response_length", AgentUtils.GetResponseLength(responseLevel)
                },
                {
                    "contact_properties",
                    JsonConvert.SerializeObject(contactProperties ?? new Dictionary<string, string>())
                },
                {
                    "chat_history", chatHistory
                },
                {
                    "language_iso_code", languageIsoCode
                },
                {
                    "handover_reason", handoverReason
                }
            });

        var content = chatMessageContent?.Content ?? "{}";

        return JsonConvert.DeserializeObject<ConversationSummary>(content);
    }
}