﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderCustomObjectTypes : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderCustomObjectTypes(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderCustomObjectTypesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [JsonConstructor]
        public GetProviderCustomObjectTypesInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
        }
    }

    public class GetProviderCustomObjectTypesOutput
    {
        [JsonProperty("custom_object_types")]
        public List<CustomObjectType> CustomObjectTypes { get; set; }

        [JsonConstructor]
        public GetProviderCustomObjectTypesOutput(
            List<CustomObjectType> customObjectTypes)
        {
            CustomObjectTypes = customObjectTypes;
        }
    }

    public async Task<GetProviderCustomObjectTypesOutput> F(
        GetProviderCustomObjectTypesInput getProviderCustomObjectTypesInput)
    {
        var providerService = _providerSelector.GetProviderService(
            getProviderCustomObjectTypesInput.ProviderName);

        var output = await providerService.GetCustomObjectTypesAsync(
            getProviderCustomObjectTypesInput.SleekflowCompanyId,
            getProviderCustomObjectTypesInput.ProviderConnectionId);

        return new GetProviderCustomObjectTypesOutput(output.CustomObjectTypes);
    }
}