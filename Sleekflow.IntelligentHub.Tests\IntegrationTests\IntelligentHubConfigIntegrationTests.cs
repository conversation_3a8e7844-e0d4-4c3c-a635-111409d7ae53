﻿using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class IntelligentHubConfigIntegrationTests
{
    private const string MockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
    private readonly IntelligentHubUsageFilter _intelligentHubUsageFilter;
    private readonly AuditEntity.SleekflowStaff _sleekflowStaff;
    private readonly Dictionary<string, int> _usageLimits;
    private readonly Dictionary<string, int> _usageLimitOffsets;

    public IntelligentHubConfigIntegrationTests()
    {
        var utcNow = DateTimeOffset.UtcNow;
        _intelligentHubUsageFilter = new IntelligentHubUsageFilter(
            new DateTimeOffset(utcNow.Year, utcNow.Month, 1, 0, 0, 0, TimeSpan.Zero),
            new DateTimeOffset(
                utcNow.Year,
                utcNow.Month,
                DateTime.DaysInMonth(utcNow.Year, utcNow.Month),
                23,
                59,
                59,
                TimeSpan.Zero));
        _sleekflowStaff = new AuditEntity.SleekflowStaff(
            "3880",
            new List<string>
            {
                "233", "282"
            });
        _usageLimits = new Dictionary<string, int>
        {
            {
                PriceableFeatures.AiFeaturesTotalUsage, 1000
            }
        };
        _usageLimitOffsets = new Dictionary<string, int>
        {
            {
                PriceableFeatures.AiFeaturesTotalUsage, 100
            }
        };
    }

    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }
    }

    [Test]
    public async Task IntelligentHubConfigTest()
    {
        // /IntelligentHubConfigs/InitializeIntelligentHubConfig
        var enrollIntelligentHubInput =
            new InitializeIntelligentHubConfig.InitializeIntelligentHubConfigInput(
                MockCompanyId,
                _usageLimits);

        var enrollIntelligentHubScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(enrollIntelligentHubInput).ToUrl("/IntelligentHubConfigs/InitializeIntelligentHubConfig");
            });

        var initializeIntelligentHubConfigOutput =
            await enrollIntelligentHubScenarioResult.ReadAsJsonAsync<
                Output<UpdateIntelligentHubConfig.UpdateIntelligentHubConfigOutput>>();

        Assert.That(initializeIntelligentHubConfigOutput, Is.Not.Null);
        Assert.That(initializeIntelligentHubConfigOutput!.HttpStatusCode, Is.EqualTo(500));
        Assert.That(initializeIntelligentHubConfigOutput.ErrorCode, Is.EqualTo(1005));

        // /IntelligentHubConfigs/UpdateIntelligentHubConfig
        var updateIntelligentHubConfigInput =
            new UpdateIntelligentHubConfig.UpdateIntelligentHubConfigInput(
                MockCompanyId,
                _usageLimits,
                _sleekflowStaff.SleekflowStaffId,
                _sleekflowStaff.SleekflowStaffTeamIds);

        var updateIntelligentHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateIntelligentHubConfigInput).ToUrl("/IntelligentHubConfigs/UpdateIntelligentHubConfig");
            });

        var updateIntelligentHubConfigOutput =
            await updateIntelligentHubConfigScenarioResult.ReadAsJsonAsync<
                Output<UpdateIntelligentHubConfig.UpdateIntelligentHubConfigOutput>>();

        Assert.That(updateIntelligentHubConfigOutput, Is.Not.Null);
        Assert.That(updateIntelligentHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));

        // /IntelligentHubConfigs/UpdateIntelligentHubConfigUsageLimitOffsets
        var updateIntelligentHubConfigUsageLimitOffsetsInput =
            new UpdateIntelligentHubConfigUsageLimitOffsets.UpdateIntelligentHubConfigUsageLimitOffsetsInput(
                MockCompanyId,
                _usageLimitOffsets,
                _sleekflowStaff.SleekflowStaffId,
                _sleekflowStaff.SleekflowStaffTeamIds);

        var updateIntelligentHubConfigUsageLimitOffsetsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateIntelligentHubConfigUsageLimitOffsetsInput).ToUrl(
                    "/IntelligentHubConfigs/UpdateIntelligentHubConfigUsageLimitOffsets");
            });

        var updateIntelligentHubConfigUsageLimitOffsetsOutput =
            await updateIntelligentHubConfigUsageLimitOffsetsScenarioResult.ReadAsJsonAsync<
                Output<UpdateIntelligentHubConfigUsageLimitOffsets.
                    UpdateIntelligentHubConfigUsageLimitOffsetsOutput>>();

        Assert.That(updateIntelligentHubConfigUsageLimitOffsetsOutput, Is.Not.Null);
        Assert.That(updateIntelligentHubConfigUsageLimitOffsetsOutput!.HttpStatusCode, Is.EqualTo(200));

        // /IntelligentHubConfigs/GetIntelligentHubConfig
        var getIntelligentHubConfigInput = new GetIntelligentHubConfig.GetIntelligentHubConfigInput(MockCompanyId);

        var getIntelligentHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getIntelligentHubConfigInput).ToUrl("/IntelligentHubConfigs/GetIntelligentHubConfig");
            });

        var getIntelligentHubConfigOutput =
            await getIntelligentHubConfigScenarioResult.ReadAsJsonAsync<
                Output<UpdateIntelligentHubConfig.UpdateIntelligentHubConfigOutput>>();

        Assert.That(getIntelligentHubConfigOutput, Is.Not.Null);
        Assert.That(getIntelligentHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));

        // /IntelligentHubConfigs/GetIntelligentHubConfigs
        var getIntelligentHubConfigsInput1 = new GetIntelligentHubConfigs.GetIntelligentHubConfigsInput(null, 100);

        var getIntelligentHubConfigsScenarioResult1 = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getIntelligentHubConfigsInput1).ToUrl("/IntelligentHubConfigs/GetIntelligentHubConfigs");
            });

        var getIntelligentHubConfigsOutput1 =
            await getIntelligentHubConfigsScenarioResult1.ReadAsJsonAsync<
                Output<GetIntelligentHubConfigs.GetIntelligentHubConfigsOutput>>();

        Assert.That(getIntelligentHubConfigsOutput1, Is.Not.Null);
        Assert.That(getIntelligentHubConfigsOutput1.HttpStatusCode, Is.EqualTo(200));

        var getIntelligentHubConfigsInput2 = new GetIntelligentHubConfigs.GetIntelligentHubConfigsInput(
            getIntelligentHubConfigsOutput1.Data.ContinuationToken,
            100);

        var getIntelligentHubConfigsScenarioResult2 = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getIntelligentHubConfigsInput2).ToUrl("/IntelligentHubConfigs/GetIntelligentHubConfigs");
            });

        var getIntelligentHubConfigsOutput2 =
            await getIntelligentHubConfigsScenarioResult2.ReadAsJsonAsync<
                Output<GetIntelligentHubConfigs.GetIntelligentHubConfigsOutput>>();

        Assert.That(getIntelligentHubConfigsOutput2, Is.Not.Null);
        Assert.That(getIntelligentHubConfigsOutput2.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task AiFeatureSettingTest()
    {
        // /IntelligentHubConfigs/UpdateAiFeatureSetting
        var updateAiFeatureSettingInput = new
            UpdateAiFeatureSetting.UpdateAiFeatureSettingInput(
                MockCompanyId,
                true,
                true,
                _sleekflowStaff.SleekflowStaffId,
                _sleekflowStaff.SleekflowStaffTeamIds);

        var updateAiFeatureSettingScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateAiFeatureSettingInput).ToUrl("/IntelligentHubConfigs/UpdateAiFeatureSetting");
            });

        var updateAiFeatureSettingOutput =
            await updateAiFeatureSettingScenarioResult.ReadAsJsonAsync<
                Output<UpdateAiFeatureSetting.UpdateAiFeatureSettingOutput>>();

        Assert.That(updateAiFeatureSettingOutput, Is.Not.Null);
        Assert.That(updateAiFeatureSettingOutput!.HttpStatusCode, Is.EqualTo(200));

        // /IntelligentHubConfigs/GetAiFeatureSetting
        var getAiFeatureSettingInput = new GetAiFeatureSetting.GetAiFeatureSettingInput(MockCompanyId);

        var getAiFeatureSettingScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getAiFeatureSettingInput).ToUrl("/IntelligentHubConfigs/GetAiFeatureSetting");
            });

        var getAiFeatureSettingOutput =
            await getAiFeatureSettingScenarioResult.ReadAsJsonAsync<
                Output<GetAiFeatureSetting.GetAiFeatureSettingOutput>>();

        Assert.That(getAiFeatureSettingOutput, Is.Not.Null);
        Assert.That(getAiFeatureSettingOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task CountIntelligentHubUsageTest()
    {
        // /IntelligentHubConfigs/GetFeatureUsages
        var getFeatureUsagesInput = new
            GetFeatureUsages.GetFeatureUsagesInput(
                MockCompanyId,
                new HashSet<string>
                {
                    PriceableFeatures.AiFeaturesTotalUsage,
                    PriceableFeatures.Translate,
                    PriceableFeatures.Rephrase
                },
                _intelligentHubUsageFilter);

        var getFeatureUsagesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getFeatureUsagesInput).ToUrl("/IntelligentHubConfigs/GetFeatureUsages");
            });

        var getFeatureUsagesOutput =
            await getFeatureUsagesScenarioResult.ReadAsJsonAsync<
                Output<GetFeatureUsages.GetFeatureUsagesOutput>>();

        Assert.That(getFeatureUsagesOutput, Is.Not.Null);
        Assert.That(getFeatureUsagesOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            getFeatureUsagesOutput.Data.FeatureUsages.ContainsKey(PriceableFeatures.AiFeaturesTotalUsage),
            Is.True);
        Assert.That(getFeatureUsagesOutput.Data.FeatureUsages.ContainsKey(PriceableFeatures.Translate), Is.True);
        Assert.That(getFeatureUsagesOutput.Data.FeatureUsages.ContainsKey(PriceableFeatures.Rephrase), Is.True);
    }
}