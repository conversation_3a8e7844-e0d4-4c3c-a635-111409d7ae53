﻿using System.Data;
using Microsoft.Data.SqlClient;
using Sleekflow.Auth0.BulkImporter.Models;

namespace Sleekflow.Auth0.BulkImporter.Services;

public class SleekflowUserService
{
    private readonly string _connectionString;
    private readonly SqlConnection _sqlConnection;

    public SleekflowUserService(string connectionString)
    {
        _connectionString = connectionString;
        _sqlConnection = new SqlConnection(_connectionString);
    }

    public async Task<List<ImportUser>> FetchAndConvertUsers(int top = 0, IProgress<float>? progress = null)
    {
        var topStr = top > 0
            ? "top " + top
            : string.Empty;
        var sql =
            $@"select " + topStr +
            $@" u.Id, u.UserName, u.PasswordHash, u.Email, u.EmailConfirmed, u.FirstName, u.LastName,
                    u.DisplayName, u.PhoneNumber
               from AspNetUsers u
               order by u.UserName";

        await _sqlConnection.OpenAsync();
        await using var cmd = new SqlCommand(sql, _sqlConnection);
        await using var reader = await cmd.ExecuteReaderAsync();

        var users = new List<ImportUser>();

        if (!reader.HasRows)
        {
            return users;
        }

        var dt = new DataTable();
        dt.Load(reader);

        float maxProgress = dt.Rows.Count;
        for (var index = 0; index < maxProgress; index++)
        {
            var row = dt.Rows[index];

            var userRoles = await FetchUserRoles(row["Id"].ToString()!);
            var userName = EmailUtils.IsValidEmail(row["UserName"].ToString()!)
                ? null
                : row["UserName"].ToString();

            users.Add(
                new ImportUser
                {
                    UserId = row["Id"].ToString(),
                    Email = row["Email"].ToString() == string.Empty
                        ? $"{row["Id"].ToString()}@id.sleekflow.io" // EmailUtils.CreateTmpEmail()
                        : row["Email"].ToString(),
                    EmailVerified = (bool) row["EmailConfirmed"],
                    UserName = userName,
                    DisplayName = row["DisplayName"].ToString() == string.Empty
                        ? null
                        : row["DisplayName"].ToString(),
                    FirstName = row["FirstName"].ToString() == string.Empty
                        ? null
                        : row["FirstName"].ToString(),
                    LastName = row["LastName"].ToString() == string.Empty
                        ? null
                        : row["LastName"].ToString(),
                    CustomPasswordHash = string.IsNullOrEmpty(row["PasswordHash"].ToString())
                        ? null
                        : new CustomPasswordHash()
                        {
                            Algorithm = "pbkdf2",
                            Hash = new Hash()
                            {
                                Value = PasswordUtils.ToAuth0PasswordHash(row["PasswordHash"].ToString()!),
                                Encoding = "utf8"
                            }
                        },
                    AppMetadata = new MetaData()
                    {
                        SleekflowId = row["Id"].ToString(),
                        Roles = userRoles,
                        PhoneNumber = row["PhoneNumber"].ToString() == string.Empty
                            ? null
                            : row["PhoneNumber"].ToString(),
                    },
                });

            progress?.Report(index / maxProgress);
        }

        // Show 100% in console log
        progress?.Report(1.0f);
        return users;
    }

    public async Task<List<string?>> FetchUserRoles(string userId)
    {
        var roles = new List<string?>();

        var sql = @$"select role.Name from AspNetUserRoles ur
                     left join AspNetRoles role on ur.RoleId = role.Id
                     where ur.UserId = '{userId}'";
        await using var tmpConn = new SqlConnection(_connectionString);
        await tmpConn.OpenAsync();
        await using var cmd = new SqlCommand(sql, _sqlConnection);
        await using var reader = await cmd.ExecuteReaderAsync();

        if (!reader.HasRows)
        {
            return roles;
        }

        var dt = new DataTable();

        dt.Load(reader);
        foreach (DataRow row in dt.Rows)
        {
            roles.Add(row["Name"].ToString());
        }

        return roles;
    }
}