using MassTransit;
using Sleekflow.EmailHub.Gmail.Communications;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.Gmail.Consumers;

public class OnGmailPartialSyncTriggeredEventConsumerDefinition : ConsumerDefinition<OnGmailPartialSyncTriggeredEventConsumer>
{
    public const int LockDuration = 10;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnGmailPartialSyncTriggeredEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 40;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 40;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnGmailPartialSyncTriggeredEventConsumer : IConsumer<OnGmailPartialSyncTriggeredEvent>
{
    private readonly IBus _bus;
    private readonly IGmailCommunicationService _gmailCommunicationService;
    private readonly ILogger<OnGmailPartialSyncTriggeredEventConsumer> _logger;
    private readonly ILockService _lockService;

    public OnGmailPartialSyncTriggeredEventConsumer(
        IBus bus,
        IGmailCommunicationService gmailCommunicationService,
        ILogger<OnGmailPartialSyncTriggeredEventConsumer> logger,
        ILockService lockService)
    {
        _bus = bus;
        _gmailCommunicationService = gmailCommunicationService;
        _logger = logger;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<OnGmailPartialSyncTriggeredEvent> context)
    {
        var gmailPartialSyncEvent = context.Message;
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            new[]
            {
                gmailPartialSyncEvent.EmailAddress,
                "GmailFullSync"
            },
            TimeSpan.FromSeconds(
                60 * OnGmailPartialSyncTriggeredEventConsumerDefinition.LockDuration),
            cancellationToken);
        if (@lock is null)
        {
            return;
        }

        try
        {
            _logger.LogInformation(
                "[OnGmailPartialSyncTriggeredEventConsumer]: Starting Gmail Partial Sync: emailAddress {emailAddress}",
                gmailPartialSyncEvent.EmailAddress);

            await _gmailCommunicationService.TriggerPartialSyncEmailsAsync(
                gmailPartialSyncEvent.EmailAddress,
                cancellationToken);

            await _lockService.ReleaseAsync(@lock, cancellationToken);
            _logger.LogInformation(
                "[OnGmailPartialSyncTriggeredEventConsumer]: Gmail Partial Sync successes: emailAddress {emailAddress}",
                gmailPartialSyncEvent.EmailAddress);
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Partial Sync fails, starting Full Sync: emailAddress {emailAddress}: Exception {e}",
                gmailPartialSyncEvent.EmailAddress,
                e);
            await _lockService.ReleaseAsync(@lock, cancellationToken);

            await _bus.Publish(
                new OnGmailSyncAllEmailsTriggeredEvent(gmailPartialSyncEvent.EmailAddress),
                cancellationToken);
        }
    }
}