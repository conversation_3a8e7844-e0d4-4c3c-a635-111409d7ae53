name: "Pull Request Title Check"

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
      - reopened
    branches:
      - dev
      - staging

permissions:
  pull-requests:
    write

jobs:
  main:
    name: <PERSON>ida<PERSON> Pull Request Title
    runs-on: ubuntu-latest
    if: github.event.pull_request.head.ref != 'master' && github.event.pull_request.head.ref != 'staging'
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        id:  lint_pr_title
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            chore
            revert
          scopes: |
            DEVS-\d+
            INT-\d+
          headerPattern: '^(\w*)(?:\(([\w$.\-*/ ]*)\))?: (.*)$'

      - uses: marocchino/sticky-pull-request-comment@v2
        if: always() && (steps.lint_pr_title.outputs.error_message != null)
        with:
          header: pr-title-lint-error
          message: |
            Hey there and thank you for opening this pull request! 👋🏼

            We require pull request titles to follow `type(scope): description` and it looks like your proposed title needs to be adjusted.

            Details:

            ```
            ${{ steps.lint_pr_title.outputs.error_message }}
            ```

      - if: ${{ steps.lint_pr_title.outputs.error_message == null }}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: pr-title-lint-error
          delete: true