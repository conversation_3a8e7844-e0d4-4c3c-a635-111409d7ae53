using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Models.ObjectIdResolvers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Salesforce;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Models.Errors;
using Sleekflow.Integrator.Salesforce.Services.Models;
using Sleekflow.Integrator.Salesforce.Utils;
using Sleekflow.JsonConfigs;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using IMapper = AutoMapper.IMapper;

namespace Sleekflow.Integrator.Salesforce.Services;

public interface ISalesforceObjectService
{
    string GetSObjectCodeName(string entityTypeName);

    Task<SalesforceObjectService.GetFieldsOutput> GetFieldsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName);

    Task<Dictionary<string, object?>> GetObjectAsync(
        SalesforceAuthentication authentication,
        string objectId,
        string entityTypeName);

    Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> GetObjectsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters = null,
        string? nextRecordsUrl = null);

    Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> GetRecentlyUpdatedObjectsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        DateTimeOffset lastModificationTime,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters = null,
        string? nextRecordsUrl = null);

    Task<long> GetObjectsCountAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups);

    Task DeleteAsync(
        SalesforceAuthentication authentication,
        string objectId,
        string entityTypeName);

    Task CreateAsync(
        SalesforceAuthentication authentication,
        Dictionary<string, object?> dict,
        string entityTypeName);

    Task UpdateAsync(
        SalesforceAuthentication authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string? entityTypeName = null);

    string ResolveObjectId(Dictionary<string, object?> dict);

    Task<string> GetObjectDirectRefUrlAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        string objectId);

    Task<List<CustomObjectType>> GetCustomObjectTypes(SalesforceAuthentication authentication);

    Task<string> GetOrganizationNameAsync(SalesforceAuthentication authentication);

    Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> SearchObjectsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        List<SearchObjectCondition> conditions,
        string? nextRecordsUrl = null);
}

public class SalesforceObjectService : ISingletonService, ISalesforceObjectService
{
    private readonly IMapper _mapper;
    private readonly ICacheService _cacheService;
    private readonly ISalesforceObjectIdResolver _salesforceObjectIdResolver;
    private readonly HttpClient _httpClient;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public SalesforceObjectService(
        IHttpClientFactory httpClientFactory,
        IMapper mapper,
        ICacheService cacheService,
        ISalesforceObjectIdResolver salesforceObjectIdResolver,
        ISalesforceConnectionService salesforceConnectionService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _mapper = mapper;
        _cacheService = cacheService;
        _salesforceObjectIdResolver = salesforceObjectIdResolver;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _salesforceConnectionService = salesforceConnectionService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public class SalesforceGetOutput<TR>
    {
        [JsonProperty("totalSize")]
        public long? TotalSize { get; set; }

        [JsonProperty("done")]
        public bool? Done { get; set; }

        [JsonProperty("records")]
        public List<TR>? Records { get; set; }

        [JsonProperty("nextRecordsUrl")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public SalesforceGetOutput(
            long? totalSize,
            bool? done,
            List<TR>? records,
            string? nextRecordsUrl)
        {
            this.TotalSize = totalSize;
            this.Done = done;
            this.Records = records;
            this.NextRecordsUrl = nextRecordsUrl;
        }
    }

    public string GetSObjectCodeName(string entityTypeName)
    {
        switch (entityTypeName)
        {
            case "ApexClass":
                return "ApexClass";
            case "ApexTrigger":
                return "ApexTrigger";

            case "Activity":
                return "Event";
            case "Company":
                return "Account";
            case "Contact":
                return "Contact";
            case "Lead":
                return "Lead";
            case "Note":
                return "ContentNote";
            case "Opportunity":
                return "Opportunity";
            case "User":
                return "User";
            case "Campaign":
                return "Campaign";
            case "CampaignMember":
                return "CampaignMember";
            case "Account":
                return "Account";
            default:
                return entityTypeName;
        }
    }

    public class GetFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            this.UpdatableFields = updatableFields;
            this.CreatableFields = creatableFields;
            this.ViewableFields = viewableFields;
        }
    }

    public async Task<GetFieldsOutput> GetFieldsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName)
    {
        return await _cacheService.CacheAsync(
            $"{nameof(SalesforceObjectService)}-{authentication.Id}-{entityTypeName}",
            async () =>
            {
                var url =
                    $"{authentication.InstanceUrl}/services/data/v55.0/sobjects/{GetSObjectCodeName(entityTypeName)}/describe";

                var (_, salesforceGetFieldsOutput) =
                    await SalesforceGetAsync<SalesforceGetFieldsOutput>(authentication, entityTypeName, url);

                var updatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var creatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var viewableFields = new List<GetTypeFieldsOutputFieldDto>();
                var skippedFields = new List<GetTypeFieldsOutputFieldDto>();

                var allowedTypes = new HashSet<string>
                {
                    "id",
                    "boolean",
                    "string",
                    "picklist",
                    "textarea",
                    "double",
                    "phone",
                    "email",
                    "date",
                    "datetime",
                    "url",
                    "currency",

                    // "reference",
                    // "address",
                };

                if (salesforceGetFieldsOutput.Fields == null)
                {
                    return new GetFieldsOutput(updatableFields, creatableFields, viewableFields);
                }

                foreach (var field in salesforceGetFieldsOutput.Fields)
                {
                    if (field is { Calculated: true, Filterable: false } || field.Encrypted == true)
                    {
                        continue;
                    }

                    if (field.Updateable == true)
                    {
                        var updatableField = _mapper.Map<GetTypeFieldsOutputFieldDto>(field);
                        updatableField.Mandatory = IsMandatoryField(field);

                        updatableFields.Add(updatableField);
                    }
                    else if (field.Createable == true)
                    {
                        creatableFields.Add(_mapper.Map<GetTypeFieldsOutputFieldDto>(field));
                    }
                    else if (field.Type != null && allowedTypes.Contains(field.Type))
                    {
                        viewableFields.Add(_mapper.Map<GetTypeFieldsOutputFieldDto>(field));
                    }
                    else
                    {
                        skippedFields.Add(_mapper.Map<GetTypeFieldsOutputFieldDto>(field));
                    }
                }

                return new GetFieldsOutput(updatableFields, creatableFields, viewableFields);
            });
    }

    public async Task<Dictionary<string, object?>> GetObjectAsync(
        SalesforceAuthentication authentication,
        string objectId,
        string entityTypeName)
    {
        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();

        var url = authentication.InstanceUrl
                  + $"/services/data/v55.0/query?q="
                  + $"SELECT+{string.Join(",", fields.Select(f => f.Name))}+"
                  + $"FROM+{GetSObjectCodeName(entityTypeName)}+"
                  + $"WHERE+Id+=+'{objectId}'+"
                  + $"LIMIT+1";

        var (str, salesforceGet) =
            await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object>>>(
                authentication,
                entityTypeName,
                url);
        if (salesforceGet.Done == null || salesforceGet.TotalSize == null || salesforceGet.Records == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
        }

        return (salesforceGet.Records.Count == 0
            ? default
            : salesforceGet.Records[0])!;
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> GetObjectsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters = null,
        string? nextRecordsUrl = null)
    {
        if (nextRecordsUrl != null)
        {
            var url = authentication.InstanceUrl + nextRecordsUrl;

            var (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    entityTypeName,
                    url);
            if (salesforceGetOutput.Done == null
                || salesforceGetOutput.TotalSize == null
                || salesforceGetOutput.Records == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
            }

            return (salesforceGetOutput.Records, salesforceGetOutput.NextRecordsUrl);
        }
        else
        {
            var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
            var creatableFields = getFieldsOutput.CreatableFields;
            var updatableFields = getFieldsOutput.UpdatableFields;
            var viewableFields = getFieldsOutput.ViewableFields;

            var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
            var fieldNames = fields.Select(f => f.Name).ToHashSet();

            EnsureCorrectFieldNames(filterGroups.SelectMany(f => f.Filters).Select(f => f.FieldName), fieldNames);
            if (fieldFilters != null)
            {
                EnsureCorrectFieldNames(fieldFilters.Select(f => f.Name), fieldNames);
            }

            var selectedFieldNames = fieldFilters == null
                ? fields
                    .Select(f => f.Name)
                    .ToList()
                : fields
                    .Select(f => f.Name)
                    .Where(f => fieldFilters.Any(ff => ff.Name == f))
                    .ToList();

            var where = filterGroups.SelectMany(fg => fg.Filters).Any()
                ? "WHERE" + string.Join(
                              "+AND+",
                              filterGroups.Select(
                                  fg =>
                                      "(" + string.Join(
                                          "+OR+",
                                          fg.Filters.Select(f => f.FieldName + (f.Operator ?? "=") + f.Value)) +
                                      ")"))
                          + "+"
                : string.Empty;

            var url = authentication.InstanceUrl
                      + $"/services/data/v55.0/query?q="
                      + $"SELECT+{string.Join(",", selectedFieldNames)}+"
                      + $"FROM+{GetSObjectCodeName(entityTypeName)}+"
                      + where
                      + $"ORDER+BY+CreatedDate+ASC+";

            var (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    entityTypeName,
                    url);
            if (salesforceGetOutput.Done == null
                || salesforceGetOutput.TotalSize == null
                || salesforceGetOutput.Records == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
            }

            return (salesforceGetOutput.Records, salesforceGetOutput.NextRecordsUrl);
        }
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)>
        GetRecentlyUpdatedObjectsAsync(
            SalesforceAuthentication authentication,
            string entityTypeName,
            DateTimeOffset lastModificationTime,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters = null,
            string? nextRecordsUrl = null)
    {
        if (nextRecordsUrl != null)
        {
            var url = authentication.InstanceUrl + nextRecordsUrl;

            var (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    entityTypeName,
                    url);
            if (salesforceGetOutput.Done == null
                || salesforceGetOutput.TotalSize == null
                || salesforceGetOutput.Records == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
            }

            return (salesforceGetOutput.Records, salesforceGetOutput.NextRecordsUrl);
        }
        else
        {
            var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
            var creatableFields = getFieldsOutput.CreatableFields;
            var updatableFields = getFieldsOutput.UpdatableFields;
            var viewableFields = getFieldsOutput.ViewableFields;

            var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
            var fieldNames = fields.Select(f => f.Name).ToHashSet();

            EnsureCorrectFieldNames(filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName), fieldNames);
            if (fieldFilters != null)
            {
                EnsureCorrectFieldNames(fieldFilters.Select(f => f.Name), fieldNames);
            }

            var selectedFieldNames = fieldFilters == null
                ? fields
                    .Select(f => f.Name)
                    .ToList()
                : fields
                    .Select(f => f.Name)
                    .Where(f => fieldFilters.Any(ff => ff.Name == f))
                    .ToList();

            var lastModificationTimeStr =
                JsonConvert.SerializeObject(lastModificationTime, JsonConfig.DefaultJsonSerializerSettings);
            var where = filterGroups.SelectMany(fg => fg.Filters).Any()
                ? "WHERE+" + string.Join(
                               "+AND+",
                               filterGroups.Select(
                                   fg =>
                                       "(" + string.Join(
                                           "+OR+",
                                           fg.Filters.Select(f => f.FieldName + (f.Operator ?? "=") + f.Value)) +
                                       ")"))
                           + "+AND+SystemModstamp+>=+" + lastModificationTimeStr.Replace("\"", string.Empty)
                           + "+"
                : "WHERE+" + "SystemModstamp+>=+" + lastModificationTimeStr.Replace("\"", string.Empty) + "+";

            var url = authentication.InstanceUrl
                      + $"/services/data/v55.0/query?q="
                      + $"SELECT+{string.Join(",", selectedFieldNames)}+"
                      + $"FROM+{GetSObjectCodeName(entityTypeName)}+"
                      + where
                      + $"ORDER+BY+CreatedDate+ASC";

            var (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    entityTypeName,
                    url);
            if (salesforceGetOutput.Done == null
                || salesforceGetOutput.TotalSize == null
                || salesforceGetOutput.Records == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
            }

            return (salesforceGetOutput.Records, salesforceGetOutput.NextRecordsUrl);
        }
    }

    public async Task<long> GetObjectsCountAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups)
    {
        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
        var fieldNames = fields.Select(f => f.Name).ToHashSet();

        EnsureCorrectFieldNames(filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName), fieldNames);

        var where = filterGroups.SelectMany(fg => fg.Filters).Any()
            ? "WHERE" + string.Join(
                          "+AND+",
                          filterGroups.Select(
                              fg =>
                                  "(" + string.Join(
                                      "+OR+",
                                      fg.Filters.Select(f => f.FieldName + (f.Operator ?? "=") + f.Value)) +
                                  ")"))
                      + "+"
            : string.Empty;

        var url = authentication.InstanceUrl +
                  "/services/data/v55.0/query?q="
                  + "SELECT+COUNT(Id)+"
                  + $"FROM+{GetSObjectCodeName(entityTypeName)}+"
                  + where;

        var (str, salesforceGetOutput) =
            await SalesforceGetAsync<SalesforceGetOutput<dynamic>>(authentication, entityTypeName, url);
        if (salesforceGetOutput.Done == null || salesforceGetOutput.TotalSize == null ||
            salesforceGetOutput.Records == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
        }

        return salesforceGetOutput.Records?[0]["expr0"] ?? 0;
    }

    public async Task DeleteAsync(
        SalesforceAuthentication authentication,
        string objectId,
        string entityTypeName)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Delete,
            RequestUri = new Uri(
                authentication.InstanceUrl +
                $"/services/data/v55.0/sobjects/{GetSObjectCodeName(entityTypeName)}/{objectId}"),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                "The httpResponseMsg {httpResponseMsg}, str {str}, sobject {GetSObjectCodeName(entityTypeName)} is not working");
        }
    }

    public async Task CreateAsync(
        SalesforceAuthentication authentication,
        Dictionary<string, object?> dict,
        string entityTypeName)
    {
        var objectDict = new Dictionary<string, object?>(dict);

        if (await _salesforceConnectionService.GetByAuthenticationIdAsync(
                authentication.SleekflowCompanyId,
                authentication.Id) == null
            && entityTypeName == "Lead" && (objectDict.ContainsKey("Company") == false
                                            || string.IsNullOrWhiteSpace(objectDict["Company"] as string)))
        {
            objectDict["Company"] = "Sleekflow";
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                JsonConvert.SerializeObject(objectDict),
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri(
                authentication.InstanceUrl +
                $"/services/data/v55.0/sobjects/{GetSObjectCodeName(entityTypeName)}")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectOperationException(str, GetSObjectCodeName(entityTypeName), httpResponseMsg);
        }
    }

    public async Task UpdateAsync(
        SalesforceAuthentication authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string? entityTypeName = null)
    {
        var objStr = JsonConvert.SerializeObject(
            dict,
            JsonConfig.EnhanceWithDefaultJsonSerializerSettings(
                settings =>
                {
                    settings.NullValueHandling = NullValueHandling.Ignore;

                    return settings;
                }));

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Patch,
            Content = new StringContent(
                objStr,
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri(
                authentication.InstanceUrl +
                $"/services/data/v55.0/sobjects/{GetSObjectCodeName(entityTypeName)}/{objectId}")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectOperationException(str, GetSObjectCodeName(entityTypeName), httpResponseMsg);
        }
    }

    public string ResolveObjectId(Dictionary<string, object?> dict)
    {
        return _salesforceObjectIdResolver.ResolveObjectId(dict);
    }

    public Task<string> GetObjectDirectRefUrlAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        string objectId)
    {
        if (entityTypeName == "Activity"
            || entityTypeName == "Company"
            || entityTypeName == "Contact"
            || entityTypeName == "Lead"
            || entityTypeName == "Note"
            || entityTypeName == "Opportunity"
            || entityTypeName == "User"
            || entityTypeName == "Campaign"
            || entityTypeName == "CampaignMember"
            || entityTypeName == "Account")
        {
            return Task.FromResult(
                $"{authentication.InstanceUrl}/lightning/r/{GetSObjectCodeName(entityTypeName)}/{objectId}/view");
        }

        throw new SfUnrecognizedSObjectTypeNameException(entityTypeName);
    }

    public async Task<List<CustomObjectType>> GetCustomObjectTypes(SalesforceAuthentication authentication)
    {
        var url = authentication.InstanceUrl
                  + "/services/data/v55.0/tooling/query?q="
                  + "SELECT+DeveloperName+"
                  + "FROM+CustomObject+"
                  + "WHERE+ManageableState+=+'unmanaged'";

        string str;
        SalesforceGetOutput<Dictionary<string, object?>> salesforceGetOutput;

        try
        {
            (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    "CustomObject",
                    url);
        }
        catch (SfSInvalidTypeException)
        {
            return new List<CustomObjectType>();
        }

        if (salesforceGetOutput.Done == null
            || salesforceGetOutput.TotalSize == null
            || salesforceGetOutput.Records == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectQueryOperationException(str, "CustomObject");
        }

        var customObjectTypes = new List<CustomObjectType>();
        foreach (var record in salesforceGetOutput.Records)
        {
            try
            {
                var developerNameObject = record["DeveloperName"];

                if (developerNameObject is null)
                {
                    continue;
                }

                var developerName = (string) developerNameObject;
                var apiName = GetCustomObjectTypeApiName(developerName);
                var displayName = await GetCustomObjectTypeDisplayName(authentication, apiName) ?? developerName;

                customObjectTypes.Add(new CustomObjectType(apiName, displayName));
            }
            catch (Exception e)
            {
                if (e is SfSNotFoundException)
                {
                    continue;
                }

                throw;
            }
        }

        return customObjectTypes;
    }

    public async Task<string> GetOrganizationNameAsync(SalesforceAuthentication authentication)
    {
        var url = authentication.InstanceUrl
                  + "/services/data/v55.0/query?q=SELECT+Name+FROM+Organization";

        var (str, salesforceGetOutput) =
            await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                authentication,
                "Organization",
                url);

        if (salesforceGetOutput.Done == null
            || salesforceGetOutput.TotalSize == null
            || salesforceGetOutput.Records == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectQueryOperationException(str, "Organization");
        }

        return salesforceGetOutput.Records[0]["Name"] as string ?? string.Empty;
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> SearchObjectsAsync(
        SalesforceAuthentication authentication,
        string entityTypeName,
        List<SearchObjectCondition> conditions,
        string? nextRecordsUrl = null)
    {
        if (nextRecordsUrl != null)
        {
            var url = authentication.InstanceUrl + nextRecordsUrl;

            var (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    entityTypeName,
                    url);
            if (salesforceGetOutput.Done == null
                || salesforceGetOutput.TotalSize == null
                || salesforceGetOutput.Records == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
            }

            return (salesforceGetOutput.Records, salesforceGetOutput.NextRecordsUrl);
        }
        else
        {
            var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);
            var creatableFields = getFieldsOutput.CreatableFields;
            var updatableFields = getFieldsOutput.UpdatableFields;
            var viewableFields = getFieldsOutput.ViewableFields;

            var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
            var fieldNames = fields.Select(f => f.Name).ToHashSet();

            var searchFieldNames = conditions.Select(c => c.FieldName).ToList();

            EnsureCorrectFieldNames(searchFieldNames, fieldNames);

            var selectedFieldNames = fields
                .Select(f => f.Name)
                .ToList();

            var whereClause = SalesforceSearchObjectUtils.GenerateSoqlWhereClauseFromSearchObjectConditions(conditions);

            var url = authentication.InstanceUrl
                      + $"/services/data/v55.0/query?q="
                      + $"SELECT+{string.Join(",", selectedFieldNames)}+"
                      + $"FROM+{GetSObjectCodeName(entityTypeName)}+"
                      + whereClause + "+"
                      + $"ORDER+BY+CreatedDate+ASC+";

            var (str, salesforceGetOutput) =
                await SalesforceGetAsync<SalesforceGetOutput<Dictionary<string, object?>>>(
                    authentication,
                    entityTypeName,
                    url);
            if (salesforceGetOutput.Done == null
                || salesforceGetOutput.TotalSize == null
                || salesforceGetOutput.Records == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new SfSObjectQueryOperationException(str, GetSObjectCodeName(entityTypeName));
            }

            return (salesforceGetOutput.Records, salesforceGetOutput.NextRecordsUrl);
        }
    }

    private async Task<(string Str, T Obj)> SalesforceGetAsync<T>(
        SalesforceAuthentication authentication,
        string typeName,
        string url)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(url),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (httpResponseMsg.IsSuccessStatusCode == false)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            var errorResponses =
                JsonConvert.DeserializeObject<List<SalesforceApiErrorResponse>>(str);
            if (errorResponses is not null)
            {
                if (errorResponses.Exists(r =>
                            r.ErrorCode == SalesforceApiErrorCodes.RequestLimitExceededErrorCode))
                {
                    var connection = await _salesforceConnectionService.GetByAuthenticationIdAsync(
                        authentication.SleekflowCompanyId,
                        authentication.Id);

                    if (connection is not null)
                    {
                        throw new SfSApiRequestLimitExceededException(connection.Id);
                    }
                }

                if (errorResponses.Exists(r =>
                        r.ErrorCode == SalesforceApiErrorCodes.NotFoundErrorCode))
                {
                    throw new SfSNotFoundException();
                }

                if (errorResponses.Exists(r =>
                        r.ErrorCode == SalesforceApiErrorCodes.InvalidTypeErrorCode))
                {
                    throw new SfSInvalidTypeException(typeName);
                }
            }

            throw new SfSObjectOperationException(str, GetSObjectCodeName(typeName), httpResponseMsg);
        }

        T? obj;
        try
        {
            obj = JsonConvert.DeserializeObject<T>(str);
        }
        catch (Exception e)
        {
            throw new SfSObjectOperationException(e, str, GetSObjectCodeName(typeName), httpResponseMsg);
        }

        if (obj == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new SfSObjectOperationException(str, GetSObjectCodeName(typeName), httpResponseMsg);
        }

        return (str, obj);
    }

    private static void EnsureCorrectFieldNames(IEnumerable<string> fieldNames, IReadOnlySet<string> allFieldNames)
    {
        var invalidFieldNamesInFilters = fieldNames
            .Where(fn => allFieldNames.Contains(fn) == false)
            .ToList();
        if (invalidFieldNamesInFilters.Any())
        {
            throw new SfUserFriendlyException(
                $"Some filters are invalid. invalidFieldNames {invalidFieldNamesInFilters}");
        }
    }

    private static string GetCustomObjectTypeApiName(string developerName)
    {
        return developerName + "__c";
    }

    private async Task<string?> GetCustomObjectTypeDisplayName(
        SalesforceAuthentication authentication,
        string apiName)
    {
        var url =
            $"{authentication.InstanceUrl}/services/data/v55.0/sobjects/{apiName}/describe";

        var (_, salesforceGetOutput) =
            await SalesforceGetAsync<SalesforceGetFieldsOutput>(
                authentication,
                apiName,
                url);

        return salesforceGetOutput.Label;
    }

    private static bool IsMandatoryField(Field field)
    {
        // except for bool fields and OwnerId, non-nillable fields are mandatory
        return field.Nillable is false && field.Type != "boolean" && field.Name != "OwnerId";
    }
}