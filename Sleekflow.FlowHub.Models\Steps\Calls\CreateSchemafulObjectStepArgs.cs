﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateSchemafulObjectStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-schemaful-object";

    [Required]
    [JsonProperty("schema_id__expr")]
    public string SchemaIdExpr { get; set; }

    [JsonProperty("primary_property_value__expr")]
    public string? PrimaryPropertyValueExpr { get; set; }

    [JsonProperty("property_values__key_expr_dict")]
    public Dictionary<string, string?>? PropertyValuesKeyExprDict { get; set; }

    [JsonProperty("contact_id__expr")]
    public string? ContactIdExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.CustomObject;

    [JsonConstructor]
    public CreateSchemafulObjectStepArgs(
        string schemaIdExpr,
        string? primaryPropertyValueExpr,
        Dictionary<string, string?>? propertyValuesKeyExprDict,
        string? contactIdExpr)
    {
        SchemaIdExpr = schemaIdExpr;
        PrimaryPropertyValueExpr = primaryPropertyValueExpr;
        PropertyValuesKeyExprDict = propertyValuesKeyExprDict;
        ContactIdExpr = contactIdExpr;
    }
}