using System.Net.Http.Headers;
using System.Text;
using MassTransit;
using Microsoft.Graph.Models;
using Microsoft.IdentityModel.Tokens;
using MimeKit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Attachments;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Events;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Models.Outlook.Authentications;
using Sleekflow.EmailHub.Models.Outlook.Communications;
using Sleekflow.EmailHub.Models.Outlook.Subscriptions;
using Sleekflow.EmailHub.Outlook.Authentications;
using Sleekflow.EmailHub.Outlook.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using DeltaResponse = Sleekflow.EmailHub.Models.Outlook.Communications.OutlookInboxDeltaResponse;

namespace Sleekflow.EmailHub.Outlook.Communications;

public interface IOutlookCommunicationService : IEmailCommunicationService
{
}

public class OutlookCommunicationService : IScopedService, IOutlookCommunicationService
{
    private readonly HttpClient _httpClient;
    private readonly IBus _bus;
    private readonly IAttachmentService _attachmentService;
    private readonly IOutlookAuthenticationService _outlookAuthenticationService;
    private readonly IOutlookSubscriptionService _outlookSubscriptionService;
    private readonly ILogger<OutlookCommunicationService> _logger;
    private readonly IEmailRepository _emailRepository;
    private readonly IIdService _idService;
    private readonly IProviderConfigService _providerConfigService;

    public OutlookCommunicationService(
        IHttpClientFactory httpClientFactory,
        IBus bus,
        IAttachmentService attachmentService,
        IOutlookAuthenticationService outlookAuthenticationService,
        IOutlookSubscriptionService outlookSubscriptionService,
        ILogger<OutlookCommunicationService> logger,
        IEmailRepository emailRepository,
        IIdService idService,
        IProviderConfigService providerConfigService)
    {
        _bus = bus;
        _attachmentService = attachmentService;
        _outlookAuthenticationService = outlookAuthenticationService;
        _outlookSubscriptionService = outlookSubscriptionService;
        _logger = logger;
        _emailRepository = emailRepository;
        _idService = idService;
        _providerConfigService = providerConfigService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task OnReceiveEmailAsync(
        List<string> sleekflowCompanyIds,
        string emailAddress,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        if (!emailMetadata!.ContainsKey("changeType") || !emailMetadata.ContainsKey("msgId"))
        {
            throw new ArgumentException(
                $"[OnReceiveEmailAsync] changeType or msgId is not provided: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyIds.First()}");
        }

        var changeType = emailMetadata["changeType"];
        var msgId = emailMetadata["msgId"];

        switch (changeType)
        {
            case "created":
                await HandleOutlookEmailCreatedAsync(
                    emailAddress,
                    sleekflowCompanyIds,
                    msgId,
                    cancellationToken);

                break;
            case "updated":
                await HandleOutlookEmailUpdatedAsync(
                    emailAddress,
                    sleekflowCompanyIds,
                    msgId,
                    cancellationToken);

                break;
            case "deleted":
                await HandleOutlookEmailDeletedAsync(
                    msgId,
                    cancellationToken);

                break;
            default:
                throw new NotImplementedException();
        }
    }

    public async Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        var authentication =
            await _outlookAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                sender.EmailAddress,
                cancellationToken: cancellationToken);

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new NullReferenceException(
                $"Cannot parse outlookAuthenticationMetaData: emailAddress {sender.EmailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var mimeMessage = new MimeMessage();

        mimeMessage.From.Add(new MailboxAddress(sender.Name, sender.EmailAddress));

        var toAddresses = to.ToList();

        foreach (var recipient in toAddresses)
        {
            mimeMessage.To.Add(new MailboxAddress(recipient.Name, recipient.EmailAddress));
        }

        var ccAddresses = cc.ToList();

        foreach (var ccAddress in ccAddresses)
        {
            mimeMessage.Cc.Add(new MailboxAddress(ccAddress.Name, ccAddress.EmailAddress));
        }

        var bccAddresses = bcc.ToList();

        foreach (var bccAddress in bccAddresses)
        {
            mimeMessage.Bcc.Add(new MailboxAddress(bccAddress.Name, bccAddress.EmailAddress));
        }

        mimeMessage.Subject = subject;

        var replyToAddresses = replyTo.ToList();

        foreach (var replyToAddress in replyToAddresses)
        {
            mimeMessage.ReplyTo.Add(new MailboxAddress(replyToAddress.Name, replyToAddress.EmailAddress));
        }

        var builder = new BodyBuilder
        {
            TextBody = textBody, HtmlBody = htmlBody
        };

        foreach (var emailAttachment in emailAttachments)
        {
            await _attachmentService.ProcessOutboundAttachment(emailAttachment, builder, cancellationToken);
        }

        mimeMessage.Body = builder.ToMessageBody();

        var raw = string.Empty;

        using (var memory = new MemoryStream())
        {
            await mimeMessage.WriteToAsync(memory, cancellationToken);

            var blob = memory.ToArray();
            raw = Convert.ToBase64String(blob);
        }

        var sendEmailRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"https://graph.microsoft.com/v1.0/me/sendMail"),
            Content = new StringContent(raw, Encoding.UTF8, "text/plain"),
        };

        sendEmailRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            outlookAuthenticationMetadata.TokenType ?? "Bearer",
            outlookAuthenticationMetadata.AccessToken);

        var sendEmailResponse = await _httpClient.SendAsync(sendEmailRequestMessage, cancellationToken);

        var sendEmailResponseContent = await sendEmailResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!sendEmailResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(sendEmailResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} SendEmailAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                sendEmailResponse.StatusCode,
                errorMsg,
                sender,
                sleekflowCompanyId);

            throw new SfInternalErrorException($"HTTP[{sendEmailResponse.StatusCode}] {errorMsg}");
        }

        var emailId = _idService.GetId("Email");

        await _emailRepository.UpsertAsync(
            new Email(
                emailId,
                sleekflowCompanyId,
                sender,
                new List<EmailContact>
                {
                    sender
                },
                to,
                cc,
                bcc,
                replyTo,
                subject,
                htmlBody,
                textBody,
                true,
                emailAttachments.ToList(),
                new OutlookEmailMetadata()),
            emailId,
            cancellationToken: cancellationToken);
    }

    public async Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _bus.Publish(
            new OnSendEmailTriggeredEvent(
                sleekflowCompanyId,
                ProviderNames.Outlook,
                sender,
                subject,
                to,
                cc,
                bcc,
                replyTo,
                htmlBody,
                textBody,
                emailAttachments,
                emailMetadata),
            cancellationToken);
    }

    public async Task SyncAllEmailsAsync(string emailAddress, CancellationToken cancellationToken = default)
    {
        var sleekflowCompanyIds = await _outlookSubscriptionService.FilterSubscribedCompanies(
            await _providerConfigService.GetCompanyIdsByEmailAddressAsync(emailAddress, cancellationToken),
            emailAddress,
            cancellationToken);

        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            var authentication =
                await _outlookAuthenticationService.GetAuthenticationAsync(
                    sleekflowCompanyId,
                    emailAddress,
                    cancellationToken: cancellationToken);

            var outlookAuthenticationMetadata =
                authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
                throw new NullReferenceException(
                    $"Cannot parse outlookAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyIds[0]}");

            var subscription = await _outlookSubscriptionService.GetSubscriptionAsync(
                sleekflowCompanyIds[0],
                emailAddress,
                cancellationToken);

            var outlookSubscriptionMetadata = subscription.EmailSubscriptionMetadata as OutlookSubscriptionMetadata ??
                                              throw new SfInternalErrorException(
                                                  $"Cannot parse outlook subscription metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyIds[0]}");
            DeltaResponse? deltaResponseModel = null;

            do
            {
                var deltaRequestMessage = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri = new Uri(
                        deltaResponseModel == null
                            ? $"https://graph.microsoft.com/v1.0/me/mailfolders/{outlookSubscriptionMetadata.InboxFolderId}/messages/delta"
                            : deltaResponseModel.ODateNextLink!),
                };

                deltaRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
                    outlookAuthenticationMetadata.TokenType ?? "Bearer",
                    outlookAuthenticationMetadata.AccessToken);
                var deltaResponse = await _httpClient.SendAsync(deltaRequestMessage, cancellationToken);
                var deltaResponseContent = await deltaResponse.Content.ReadAsStringAsync(cancellationToken);

                if (!deltaResponse.IsSuccessStatusCode)
                {
                    var errorObject = JObject.Parse(deltaResponseContent);
                    var errorMsg = errorObject["error"]?.ToString();

                    _logger.LogError(
                        "HTTP[{statusCode}] {errorMsg} Outlook FullSyncOutlookInboxAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                        deltaResponse.StatusCode,
                        errorMsg,
                        emailAddress,
                        sleekflowCompanyId);

                    throw new SfInternalErrorException(
                        $"Outlook FullSyncOutlookInboxAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
                }

                deltaResponseModel = JsonConvert.DeserializeObject<DeltaResponse>(deltaResponseContent)!;

                foreach (var message in deltaResponseModel.Value)
                {
                    try
                    {
                        await UpsertOutlookMessage(
                            sleekflowCompanyId,
                            emailAddress,
                            message.Id!,
                            cancellationToken: cancellationToken);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            "Error while receiving outlook emails via full sync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}: Exception :{e}",
                            emailAddress,
                            sleekflowCompanyId,
                            e);
                    }
                }

                if (string.IsNullOrEmpty(deltaResponseModel.ODateNextLink))
                {
                    await _outlookSubscriptionService.UpdateDeltaLink(
                        sleekflowCompanyIds[0],
                        emailAddress,
                        deltaResponseModel.ODataDeltaLink!,
                        cancellationToken);

                    break;
                }
            }
            while (!string.IsNullOrEmpty(deltaResponseModel.ODateNextLink));

            _logger.LogInformation(
                "Outlook full sync done: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId} : time {time}",
                emailAddress,
                sleekflowCompanyIds[0],
                DateTime.UtcNow);
        }
    }

    private async Task UpsertOutlookMessage(
        string sleekflowCompanyId,
        string emailAddress,
        string outlookMessageId,
        bool isNewMessage = true,
        Email? emailToBeUpdated = null,
        CancellationToken cancellationToken = default)
    {
        EmailAuthentication authentication;

        try
        {
            authentication = await _outlookAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId: sleekflowCompanyId,
                emailAddress: emailAddress,
                cancellationToken: cancellationToken);
        }
        catch
        {
            _logger.LogError(
                "Outlook GetMessageFromMessageIdAsync fails due to not authenticated: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"Outlook GetMessageFromMessageIdAsync fails due to not authenticated: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new SfInternalErrorException(
                $"Cannot parse outlook authentication metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var messagesRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = new Uri($"https://graph.microsoft.com/v1.0/me/messages/{outlookMessageId}"),
        };

        messagesRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            outlookAuthenticationMetadata.TokenType ?? "Bearer",
            outlookAuthenticationMetadata.AccessToken);

        var messagesResponse = await _httpClient.SendAsync(messagesRequestMessage, cancellationToken);
        var messagesResponseContent = await messagesResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!messagesResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(messagesResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} Outlook GetMessageFromMessageId Async fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                messagesResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"{errorMsg} Outlook GetMessageFromMessageIdAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var message = JsonConvert.DeserializeObject<Message>(messagesResponseContent)!;
        var emailId = isNewMessage ? _idService.GetId("Email") : emailToBeUpdated!.Id;

        List<EmailAttachment> attachments = new ();

        if (isNewMessage)
        {
            await HandleOutlookMessageAttachment(
                sleekflowCompanyId,
                emailAddress,
                outlookMessageId,
                cancellationToken,
                authentication,
                emailId,
                attachments);
        }

#pragma warning disable
        var email = new Email(
            emailId,
            sleekflowCompanyId,
            new EmailContact(message.Sender.EmailAddress.Address, message.Sender.EmailAddress.Name),
            new List<EmailContact>
            {
                new (message.From.EmailAddress.Address, message.From.EmailAddress.Name)
            },
            message.ToRecipients?.Select(x => new EmailContact(x.EmailAddress.Address, x.EmailAddress.Name)).ToList(),
            message.CcRecipients?.Select(x => new EmailContact(x.EmailAddress.Address, x.EmailAddress.Name)).ToList(),
            message.BccRecipients?.Select(x => new EmailContact(x.EmailAddress.Address, x.EmailAddress.Name)).ToList(),
            message.ReplyTo.Select(x => new EmailContact(x.EmailAddress.Address, x.EmailAddress.Name)).ToList(),
            message.Subject,
            message.Body.ContentType.ToString() == "html" ? message.Body.Content : null,
            message.BodyPreview,
            false,
            attachments,
            new OutlookEmailMetadata(
                message.Id,
                message.Importance.ToString(),
                message.ConversationId,
                message.SentDateTime,
                nameof(message.Flag.FlagStatus),
                message.IsRead,
                message.InternetMessageId,
                message.HasAttachments));
#pragma warning restore
        await _emailRepository.UpsertAsync(email, emailId, cancellationToken: cancellationToken);
    }

    private async Task HandleOutlookMessageAttachment(
        string sleekflowCompanyId,
        string emailAddress,
        string outlookMessageId,
        CancellationToken cancellationToken,
        EmailAuthentication authentication,
        string emailId,
        List<EmailAttachment> attachments)
    {

        var attachmentRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = new Uri($"https://graph.microsoft.com/v1.0/me/messages/{outlookMessageId}/attachments"),
        };

        attachmentRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            "Bearer",
            (authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
             throw new NullReferenceException(
                 $"Cannot parse outlookAuthenticationMetadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}"))
            .AccessToken);

        var attachmentResponse = await _httpClient.SendAsync(attachmentRequestMessage, cancellationToken);
        var attachmentResponseContent = await attachmentResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!attachmentResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(attachmentResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} Outlook GetAttachmentsFromOutlookMessage fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                attachmentResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"{errorMsg} Outlook GetAttachmentsFromOutlookMessage fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var attachmentResponseModel =
            JsonConvert.DeserializeObject<OutlookFileAttachmentResponse>(attachmentResponseContent)!;

        if (attachmentResponseModel.Value.Any())
        {
            foreach (var attachment in attachmentResponseModel.Value)
            {
                if (attachment.ContentBytes == null)
                {
                    continue;
                }

                await _attachmentService.ProcessInboundAttachment(
                    sleekflowCompanyId,
                    emailId,
                    new MailboxAddress(emailAddress, emailAddress),
                    attachment.Name ?? string.Empty,
                    attachment.ContentBytes,
                    attachments,
                    cancellationToken);
            }
        }
    }

    private async Task HandleOutlookEmailCreatedAsync(
        string emailAddress,
        List<string> sleekflowCompanyIds,
        string outlookMessageId,
        CancellationToken cancellationToken = default)
    {
        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            await UpsertOutlookMessage(
                sleekflowCompanyId,
                emailAddress,
                outlookMessageId,
                cancellationToken: cancellationToken);
        }
    }

    private async Task HandleOutlookEmailUpdatedAsync(
        string emailAddress,
        List<string> sleekflowCompanyIds,
        string outlookMessageId,
        CancellationToken cancellationToken = default)
    {
        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            _logger.LogInformation(
                "[Outlook webhook updated event]: start processing {msgId}: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                outlookMessageId,
                emailAddress,
                sleekflowCompanyId);
            var (isNewMessage, email) = await IsNewMessageAsync(outlookMessageId, cancellationToken);

            if (isNewMessage)
            {
                return;
            }

            await UpsertOutlookMessage(
                sleekflowCompanyId,
                emailAddress,
                outlookMessageId,
                false,
                email,
                cancellationToken);
        }
    }

    private async Task HandleOutlookEmailDeletedAsync(
        string outlookMessageId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[Outlook webhook deleted event]: start processing {msgId}",
            outlookMessageId);
        var (isNewMessage, email) = await IsNewMessageAsync(outlookMessageId, cancellationToken);

        if (isNewMessage)
        {
            return;
        }

        await _emailRepository.DeleteAsync(email!.Id, email.Id, cancellationToken: cancellationToken);
    }

    private async Task<(bool IsNewMessage, Email? Message)> IsNewMessageAsync(
        string outlookMessageId,
        CancellationToken cancellationToken = default)
    {
        var emails = await _emailRepository.GetObjectsAsync(
            x =>
                x.EmailMetadata.ProviderName == ProviderNames.Outlook
                && ((OutlookEmailMetadata) x.EmailMetadata).OutlookMessageId == outlookMessageId,
            cancellationToken: cancellationToken);

        if (emails is null || emails.Count == 0)
        {
            return (true, null);
        }

        return (false, emails[0]);
    }
}