﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.WorkflowGroups;

[ContainerId(ContainerNames.WorkflowGroup)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class WorkflowGroup : AuditEntity
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonConstructor]
    public WorkflowGroup(
        string name,
        string id,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(
            id,
            SysTypeNames.WorkflowGroup,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        Name = name;
    }
}