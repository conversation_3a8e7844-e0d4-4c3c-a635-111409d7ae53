﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Queries;

namespace Sleekflow.CommerceHub.Orders;

public interface IOrderService
{
    Task<Order> GetAsync(
        string id,
        string sleekflowCompanyId);

    Task<List<Order>> GetUserOrdersAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId);

    Task<Order> CreateAndGetOrderAsync(
        Order order,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<Order> PatchAndGetOrderAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        List<OrderLineItem> orderLineItems,
        Discount? orderDiscount,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<Order> PatchAndGetOrderAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        string orderStatus,
        string paymentStatus,
        Dictionary<string, object?> metadata,
        DateTimeOffset? paymentLinkSentAt,
        DateTimeOffset? completedAt,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<Order> PatchAndGetOrderAsync(
        string id,
        string sleekflowCompanyId,
        string orderStatus,
        string paymentStatus);

    Task DeleteOrderAsync(
        string id,
        string sleekflowCompanyId);

    Task<(List<Order> Orders, string? NextContinuationToken)> GetOrdersAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        List<QueryBuilder.Sort> sorts);
}

public class OrderService : IOrderService, IScopedService
{
    private readonly ILogger<OrderService> _logger;
    private readonly IOrderRepository _orderRepository;
    private readonly IBus _bus;
    private readonly IOrderValidator _orderValidator;
    private readonly IOrderPriceCalculator _orderPriceCalculator;

    public OrderService(
        ILogger<OrderService> logger,
        IOrderRepository orderRepository,
        IBus bus,
        IOrderValidator orderValidator,
        IOrderPriceCalculator orderPriceCalculator)
    {
        _logger = logger;
        _orderRepository = orderRepository;
        _bus = bus;
        _orderValidator = orderValidator;
        _orderPriceCalculator = orderPriceCalculator;
    }

    public async Task<Order> GetAsync(string id, string sleekflowCompanyId)
    {
        var orders = await _orderRepository.GetObjectsAsync(
            e =>
                e.Id == id &&
                e.SleekflowCompanyId == sleekflowCompanyId);

        if (orders.Count != 1)
        {
            throw new SfInternalErrorException("The object does not exist");
        }

        return orders[0];
    }

    public async Task<List<Order>> GetUserOrdersAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId)
    {
        return await _orderRepository.GetUserOrdersAsync(
            sleekflowCompanyId,
            sleekflowUserProfileId);
    }

    public async Task<Order> CreateAndGetOrderAsync(
        Order order,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var (subtotalPrice, totalPrice) = await _orderPriceCalculator.CalculateAsync(
            order.SleekflowCompanyId,
            order.StoreId,
            order.LineItems,
            order.OrderDiscount,
            new IOrderPriceCalculator.CalculatorContext(
                order.CurrencyIsoCode,
                order.LanguageIsoCode));

        order.TotalPrice = totalPrice;
        order.SubtotalPrice = subtotalPrice;

        await _orderValidator.AssertValidOrderAsync(order);

        var persistedOrder = await _orderRepository.CreateAndGetAsync(
            order,
            order.SleekflowCompanyId);

        if (persistedOrder.OrderStatus == OrderStatuses.Draft)
        {
            var onDraftOrderCreatedEvent = new OnDraftOrderCreatedEvent(
                persistedOrder.Id,
                persistedOrder.SleekflowCompanyId);

            await _bus.Publish(
                onDraftOrderCreatedEvent,
                context => { context.Delay = TimeSpan.FromMinutes(15); });
        }

        return order;
    }

    public async Task<Order> PatchAndGetOrderAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        List<OrderLineItem> orderLineItems,
        Discount? orderDiscount,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var order = await _orderRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (order is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var (subtotalPrice, totalPrice) = await _orderPriceCalculator.CalculateAsync(
            sleekflowCompanyId,
            storeId,
            orderLineItems,
            orderDiscount,
            new IOrderPriceCalculator.CalculatorContext(
                order.CurrencyIsoCode,
                order.LanguageIsoCode));

        order.LineItems = orderLineItems;
        order.OrderDiscount = orderDiscount;
        order.SubtotalPrice = subtotalPrice;
        order.TotalPrice = totalPrice;
        order.UpdatedAt = DateTimeOffset.UtcNow;
        order.UpdatedBy = sleekflowStaff;

        await _orderValidator.AssertValidOrderAsync(order);

        var isPatched = await _orderRepository.UpsertAsync(
            order,
            order.SleekflowCompanyId);
        if (isPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the order {JsonConvert.SerializeObject(order, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        }

        return order;
    }

    public async Task<Order> PatchAndGetOrderAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        string orderStatus,
        string paymentStatus,
        Dictionary<string, object?> metadata,
        DateTimeOffset? paymentLinkSentAt,
        DateTimeOffset? completedAt,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var order = await _orderRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (order is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        order.OrderStatus = orderStatus;
        order.PaymentStatus = paymentStatus;
        order.Metadata = metadata;
        order.PaymentLinkSentAt = paymentLinkSentAt;
        order.CompletedAt = completedAt;
        order.UpdatedAt = DateTimeOffset.UtcNow;
        order.UpdatedBy = sleekflowStaff;

        await _orderValidator.AssertValidOrderAsync(order);

        var isPatched = await _orderRepository.UpsertAsync(
            order,
            order.SleekflowCompanyId);
        if (isPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the order {JsonConvert.SerializeObject(order, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        }

        return order;
    }

    public async Task<Order> PatchAndGetOrderAsync(
        string id,
        string sleekflowCompanyId,
        string orderStatus,
        string paymentStatus)
    {
        var order = await _orderRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (order is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        order.OrderStatus = orderStatus;
        order.PaymentStatus = paymentStatus;
        order.UpdatedAt = DateTimeOffset.UtcNow;

        await _orderValidator.AssertValidOrderAsync(order);

        var isPatched = await _orderRepository.UpsertAsync(
            order,
            order.SleekflowCompanyId);
        if (isPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the order {JsonConvert.SerializeObject(order, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        }

        return order;
    }

    public async Task DeleteOrderAsync(
        string id,
        string sleekflowCompanyId)
    {
        var order = await GetAsync(
            id,
            sleekflowCompanyId);

        var deleteAsync = await _orderRepository.DeleteAsync(
            order.Id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the order with id {id} and sleekflowCompanyId {sleekflowCompanyId}");
        }
    }

    public async Task<(List<Order> Orders, string? NextContinuationToken)> GetOrdersAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        List<QueryBuilder.Sort> sorts)
    {
        var fgs = new List<QueryBuilder.FilterGroup>
        {
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                        "=",
                        sleekflowCompanyId),
                }),
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        Entity.PropertyNameSysTypeName,
                        "=",
                        SysTypeNames.Order)
                }),
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        IHasRecordStatuses.PropertyNameRecordStatuses,
                        "array_contains",
                        RecordStatuses.Active)
                }),
        };

        var queryDefinition =
            QueryBuilder.BuildQueryDef(
                new List<QueryBuilder.ISelect>(),
                fgs,
                sorts);

        _logger.LogInformation(
            "Executing queryText {QueryText}, queryParameters {QueryParameters}",
            queryDefinition.QueryText,
            queryDefinition.GetQueryParameters());

        var (records, nextContinuationToken) =
            await _orderRepository.GetContinuationTokenizedObjectsAsync(
                queryDefinition,
                continuationToken,
                limit);

        return (records, nextContinuationToken);
    }
}