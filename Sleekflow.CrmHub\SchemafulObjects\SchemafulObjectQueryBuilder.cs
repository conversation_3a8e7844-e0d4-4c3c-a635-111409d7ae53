﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.SchemafulObjects;

public class SchemafulObjectQueryBuilder
{
    public class FilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<ISchemafulObjectFilter> Filters { get; set; }

        [JsonConstructor]
        public FilterGroup(
            List<ISchemafulObjectFilter> filters)
        {
            Filters = filters;
        }
    }

    public interface ISchemafulObjectFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }

        public bool IsPropertyValue { get; set; }
    }

    public class SchemafulObjectFilter : ISchemafulObjectFilter
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                SchemafulObject.PropertyNameSchemaId,
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(=|>|<|>=|<=|!=|contains|arrayContains|in|startsWith|isDefined|notContains|notArrayContains|notIn)$")]
        [JsonProperty("operator")]
        public string Operator { get; set; }

        [JsonProperty("value")]
        public object? Value { get; set; }

        [Required]
        [JsonProperty("is_property_value")]
        public bool IsPropertyValue { get; set; }

        [JsonConstructor]
        public SchemafulObjectFilter(string fieldName, string @operator, object? value, bool isPropertyValue)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
            IsPropertyValue = isPropertyValue;
        }
    }

    public class PlainSchemafulObjectFilter : ISchemafulObjectFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }

        public bool IsPropertyValue { get; set; }

        public PlainSchemafulObjectFilter(string fieldName, string @operator, object? value, bool isPropertyValue)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
            IsPropertyValue = isPropertyValue;
        }
    }

    public class SchemafulObjectSort
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(asc|desc|ASC|DESC)$")]
        [JsonProperty("direction")]
        public string Direction { get; set; }

        [Required]
        [JsonProperty("is_property_value")]
        public bool IsPropertyValue { get; set; }

        [JsonConstructor]
        public SchemafulObjectSort(string fieldName, string direction, bool isPropertyValue)
        {
            FieldName = fieldName;
            Direction = direction;
            IsPropertyValue = isPropertyValue;
        }
    }

    public class GroupBy
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                SchemafulObject.PropertyNameSchemaId
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [JsonProperty("is_property_value")]
        public bool IsPropertyValue { get; set; }

        [JsonConstructor]
        public GroupBy(string fieldName, bool isPropertyValue)
        {
            FieldName = fieldName;
            IsPropertyValue = isPropertyValue;
        }
    }

    private sealed class Param
    {
        public string Name { get; set; }

        public object? Value { get; set; }

        public Param(string name, object? value)
        {
            Name = name;
            Value = value;
        }
    }

    public interface ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }

        public bool IsPropertyValue { get; set; }
    }

    public class Select : ISelect
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                SchemafulObject.PropertyNameSchemaId
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonProperty("as")]
        public string? As { get; set; }

        [Required]
        [JsonProperty("is_property_value")]
        public bool IsPropertyValue { get; set; }

        [JsonConstructor]
        public Select(string fieldName, string? @as, bool isPropertyValue)
        {
            FieldName = fieldName;
            As = @as;
            IsPropertyValue = isPropertyValue;
        }
    }

    public class PlainSelect : ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }

        public bool IsPropertyValue { get; set; }

        public PlainSelect(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public class ArrayExist
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                SchemafulObject.PropertyNameSchemaId
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [JsonProperty("is_property_value")]
        public bool IsPropertyValue { get; set; }

        [Required]
        [JsonProperty("filters")]
        public List<SchemafulObjectFilter> Filters { get; set; }

        [JsonConstructor]
        public ArrayExist(string fieldName, bool isPropertyValue, List<SchemafulObjectFilter> filters)
        {
            FieldName = fieldName;
            IsPropertyValue = isPropertyValue;
            Filters = filters;
        }
    }

    public static QueryDefinition BuildQueryDef(
        List<ISelect> selects,
        List<FilterGroup> filterGroups,
        List<SchemafulObjectSort> sorts,
        List<GroupBy> groupBys,
        string sleekflowCompanyId,
        string schemaId,
        bool isSearchIndexedPropertyValues,
        List<ArrayExist>? arrayExists = null)
    {
        var fieldNamePropertyValues = isSearchIndexedPropertyValues
            ? SchemafulObject.PropertyNameIndexedPropertyValues
            : SchemafulObject.PropertyNamePropertyValues;

        var selectExpressions = new List<string>()
            {
                Capacity = selects.Count + groupBys.Count
            }
            .Concat(
                selects.Select(
                    f =>
                    {
                        if (f is PlainSelect)
                        {
                            return $"{f.FieldName} {f.As}";
                        }

                        return f.IsPropertyValue
                            ? $"m[\"{fieldNamePropertyValues}\"][\"{f.FieldName}\"] {f.As}"
                            : $"m[\"{f.FieldName}\"] {f.As}";
                    }))
            .Concat(
                groupBys.Select(
                    f =>
                    {
                        return f.IsPropertyValue
                        ? $"m[\"{fieldNamePropertyValues}\"][\"{f.FieldName}\"] as \"{f.FieldName}\""
                        : $"m[\"{f.FieldName}\"] as \"{f.FieldName}\"";
                    }))
            .ToList();

        var selectClause = selectExpressions.Any()
            ? "SELECT "
              + string.Join(", ", selectExpressions)
            : "SELECT *";

        var defaultFilterGroups = new List<FilterGroup>
        {
            new FilterGroup(
                new List<ISchemafulObjectFilter>
                {
                    new PlainSchemafulObjectFilter(
                        IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                        "=",
                        sleekflowCompanyId,
                        false)
                }),
            new FilterGroup(
                new List<ISchemafulObjectFilter>
                {
                    new PlainSchemafulObjectFilter(
                        SchemafulObject.PropertyNameSchemaId,
                        "=",
                        schemaId,
                        false)
                })
        };

        var (@params, whereClause) = GetWhereClause(
            defaultFilterGroups
                .Concat(filterGroups)
                .ToList(),
            fieldNamePropertyValues);

        var (arrayExistParams, arrayExistClause) = GetArrayExistClause(arrayExists, fieldNamePropertyValues);
        @params.AddRange(arrayExistParams);

        var sortClause = sorts.Count == 0
            ? string.Empty
            : "ORDER BY "
              + string.Join(
                  ", ",
                  sorts.Select(
                      f => f.IsPropertyValue
                          ? $"m[\"{fieldNamePropertyValues}\"][\"{f.FieldName}\"] {f.Direction}"
                          : $"m[\"{f.FieldName}\"] {f.Direction}"));

        var groupByClause = groupBys.Count == 0
            ? string.Empty
            : "GROUP BY "
              + string.Join(
                  ", ",
                  groupBys.Select(
                      f => f.IsPropertyValue
                          ? $"m[{fieldNamePropertyValues}][\"{f.FieldName}\"]"
                          : $"m[\"{f.FieldName}\"]"));

        var clauses = new List<string>
            {
                selectClause,
                $"FROM schema_{schemaId} m",
                whereClause,
                arrayExistClause,
                sortClause,
                groupByClause
            }
            .Where(l => !string.IsNullOrWhiteSpace(l))
            .ToList();

        var queryDefinition = @params
            .Aggregate(
                new QueryDefinition(string.Join("\n", clauses)),
                (qd, param) =>
                {
                    if (param.Value is JArray jArray)
                    {
                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<string>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<long>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<double>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        throw new SfValidationException(
                            new List<ValidationResult>()
                            {
                                new ValidationResult(
                                    "Invalid parameter value",
                                    new[]
                                    {
                                        param.Name
                                    })
                            });
                    }

                    return qd.WithParameter(param.Name, param.Value);
                });

        return queryDefinition;
    }

    private static (List<Param> Params, string Where) GetWhereClause(
        IReadOnlyCollection<FilterGroup> filterGroups,
        string fieldNamePropertyValues,
        string alias = "m")
    {
        var @params = new List<Param>();

        if (filterGroups.Count <= 0)
        {
            return (@params, string.Empty);
        }

        var stringBuilder = new StringBuilder();
        var i = 0;

        stringBuilder.Append("WHERE ");
        stringBuilder.AppendJoin(
            " AND ",
            filterGroups
                .Where(fg => fg.Filters.Any())
                .Select(
                    fg =>
                    {
                        var filterClauseStrs = fg.Filters.Select(
                            f =>
                            {
                                var fieldName = GetFieldNameClause(f.FieldName, f.IsPropertyValue, fieldNamePropertyValues, alias);

                                var match = Regex.Match(f.Operator, @"^(not)?(.+)$");   // match the prefix [not]

                                var notOperator = match.Groups[1].Value;
                                var plainOperator = match.Groups[2].Value.ToLower();

                                var notClause = string.IsNullOrEmpty(notOperator) ? string.Empty : "NOT ";

                                return notClause + plainOperator switch
                                {
                                    "contains" =>
                                        $"CONTAINS({fieldName}, {GetSanitizedParamName(f.FieldName, i++, alias)}, true)",
                                    "arraycontains" =>
                                        $"ARRAY_CONTAINS({fieldName}, {GetSanitizedParamName(f.FieldName, i++, alias)}, false)",
                                    "startswith" =>
                                        $"STARTSWITH({fieldName}, {GetSanitizedParamName(f.FieldName, i++, alias)}, false)",
                                    "in" =>
                                        $"ARRAY_CONTAINS({GetSanitizedParamName(f.FieldName, i++, alias)}, {fieldName}, false)",
                                    "isdefined" =>
                                        $"IS_DEFINED({fieldName}) = {GetSanitizedParamName(f.FieldName, i++, alias)}",
                                    _ =>
                                        $"{fieldName} {f.Operator} {GetSanitizedParamName(f.FieldName, i++, alias)}"
                                };
                            });

                        var sb = new StringBuilder();
                        sb.Append('(');
                        sb.AppendJoin(" OR ", filterClauseStrs);
                        sb.Append(')');

                        return sb.ToString();
                    }));

        i = 0;

        @params.AddRange(
            filterGroups
                .SelectMany(fg => fg.Filters)
                .Select(f => new Param(GetSanitizedParamName(f.FieldName, i++, alias), f.Value)));

        return (@params, stringBuilder.ToString());
    }

    private static (List<Param> Params, string Join) GetArrayExistClause(
        IReadOnlyCollection<ArrayExist>? arrayExists,
        string fieldNamePropertyValues)
    {
        var @params = new List<Param>();

        if (arrayExists is not { Count: > 0 })
        {
            return (@params, string.Empty);
        }

        var stringBuilder = new StringBuilder();
        var i = 0;

        foreach (var arrayExist in arrayExists)
        {
            var alias = $"e{i++}";
            var fieldName = GetFieldNameClause(arrayExist.FieldName, arrayExist.IsPropertyValue, fieldNamePropertyValues, "m");
            var filterGroups = new List<FilterGroup>
            {
                new (arrayExist.Filters.Select(f => f).Cast<ISchemafulObjectFilter>().ToList())
            };
            var (arrayExistsParams, arrayExistsWhere) = GetWhereClause(filterGroups, SchemafulObject.PropertyNamePropertyValues, alias);

            stringBuilder.Append($" AND EXISTS ( SELECT 1 FROM {alias} ");
            stringBuilder.Append($"IN {fieldName} ");
            stringBuilder.Append(arrayExistsWhere);
            stringBuilder.Append(')');

            @params.AddRange(arrayExistsParams);
        }

        return (@params, stringBuilder.ToString());
    }

    private static string GetSanitizedParamName(string name, int index, string alias)
    {
        return "@param_"
               + name
                   .Replace("[", "_")
                   .Replace("]", "_")
                   .Replace(".", "_")
                   .Replace(":", "_")
               + "_" + alias
               + "_" + index;
    }

    private static string GetFieldNameClause(string fieldName, bool isPropertyValue, string fieldNamePropertyValues, string alias)
    {
        if (isPropertyValue)
        {
            return $"{alias}[\"{fieldNamePropertyValues}\"][\"{fieldName}\"]";
        }

        return fieldName switch
        {
            IHasCreatedBy.PropertyNameCreatedBy => $"{alias}[\"{IHasCreatedBy.PropertyNameCreatedBy}\"][\"{IHasSleekflowStaff.PropertyNameSleekflowStaffId}\"]",
            IHasUpdatedBy.PropertyNameUpdatedBy => $"{alias}[\"{IHasUpdatedBy.PropertyNameUpdatedBy}\"][\"{IHasSleekflowStaff.PropertyNameSleekflowStaffId}\"]",
            _ => $"{alias}[\"{fieldName}\"]"
        };
    }
}