# This is a markdown for the website at https://help.sleekflow.io/en_US/troubleshooting-whatsapp
# Troubleshooting

This article provides solutions for SleekFlow WhatsApp channel errors.

SleekFlow platform will return an error code when you encounter an issue with messaging. For example,

The image shows an example of an error message with an error code.

Error code is a numeric or alphanumeric code that indicates the nature of an error and, when possible, why it occurred. It helps you to identify the cause of a problem, a failing component, and the service actions that might be needed to solve the problem.

Here is a list of error codes with their possible causes and suggested solutions.

## SleekFlow Cloud API

Here is a list of SleekFlow Cloud API error codes, their possible causes, and suggested solutions:

| Error code | Description | Possible cause and suggested solution |
|---|---|---|
| 1 | API Unknown | Invalid request or server error: Check WhatsApp Business Platform Status page for API status, or check your endpoint request status. |
| 2 | API Service | API service is temporarily suspended due to downtime or service is overloaded. Click [here](https://metastatus.com/whatsapp-business-api) to check WhatsApp Business Platform status before retrying. |
| 4 | N/A | This channel has reached its API call rate limit. |
| 33 | Parameter value is not valid | The business phone number has been deleted. Please check if the phone number you have entered is correct and try again. |
| 100 | Invalid parameter | The request includes unsupported or misspelled parameters. Click [here](https://developers.facebook.com/docs/whatsapp/cloud-api/reference?locale=en_US) to view parameter requirements and review your request before trying again. |
| 368 | N/A | The WhatsApp Business Account associated with the app has been restricted or disabled for violating a platform policy. |
| 1001 | Validation exception | Empty file found. Check and try again, or contact us for support |
| 1003 | Not found object exception | This could be caused by being unable to locate the phone number information provided by the client. The cause could be like phone number being removed on the Meta side, or the phone number function is limited. Try again, or contact us for support. |
| 1006 | Internal exceptions | This could be caused by being unable to re-sync the WABA information. Try again, or contact us for support. |
| 4002 | Not support operation | The operation type is not supported. This could be due to the media in the message not being found, insufficient balance in your account, or our platform having temporarily suspended the feature. Please try again later, or contact us for support.  |
| 4002 | Not supported operation | Unsupported operation. This could be because SleekFlow has restricted its functions as your WABA is out of balance, unable to locate WABA or phone number, Meta restrictions on WABA or phone number, or removal of WABA or phone number on Meta's side. Check your WABA balance and top up your account to try reconnecting to Cloud API again, or contact us for support. |
| 6001 | Graph API error | Unable to create message template. Please try again later, or contact us for support. |
| 6002 | Message function restricted | Your WABA's message function is limited. Our platform has restricted its functions as your WABA is out of balance. Check your WABA balance and top up your account to try again, or contact us for support. |
| 8007 | N/A | The WhatsApp Business Account has reached its rate limit. |
| 130429 | Graph API error | You have reached the API's throughput limit. See Throughput. Please try again later, or reduce the frequency of messages sent by the app. |
| 130472 | User's number is part of an experiment | The customer's phone number is selected as part of a WhatsApp user experience experiment and will not be able to receive any marketing template message. Contact the customer via other non-WhatsApp means to request a message for resending within the service window in order to initiate a conversation. |
| 131000 | Something went wrong | Unable to send message due to an unknown error. Please try again later. If this issue persists, please contact us for support. |
| 131005 | Access denied | Permission is not granted or has been removed. Verify app permissions with an access token debugger. Please review and try again, or contact us for support. |
| 131008 | Required parameter is missing | The request is missing a required parameter. Click [here](https://developers.facebook.com/docs/whatsapp/cloud-api/reference) to view parameter requirements and review your request before trying again. |
| 131009 | Parameter value is not valid | The request includes invalid information. Review your request and try again. |
| 131016 | Service unavailable | Service is temporarily unavailable. Click [here](https://l.facebook.com/l.php?u=https%3A%2F%2Fmetastatus.com%2Fwhatsapp-business-api&h=AT31euiE-SzI3btdgsddisMJ3MWw0QrMX40kO08W3sZ-7WFGO31X87BVaGIVeWCX6bKYY1SVEwGxPbhOMUd8lc_tYkOWeMcIp3lAXYxoSrurFF3njOQuWV20Fy1bLIA-aiNvwEH1OeL1CM86znV9mgdzF314wwm9As4ANGhb) to view WhatsApp Business platform status and API status before retrying. |
| 131021 | Recipient cannot be sender | Sender and recipient phone numbers are the same. Please review the phone numbers and try again. |
| 131026 | Message undeliverable | Unable to deliver your message. The recipient may not have accepted our new Terms of Service and Privacy Policy or may be using an old version of WhatsApp. |
| 131031 | Business Account locked | Your WhatsApp Business account has been locked. Please go to WhatsApp account page to review your account status. |
| 131042 | Business eligibility payment issue | Unable to send message due to an error occurring for your WhatsApp Business account payment method. Review your account payment method again, or contact us for support. |
| 131045 | Incorrect certificate | Unable to send the message due to a phone number registration error. Please register your phone number before retrying. |
| 131047 | Re-engagement message | Unable to send message. The 24-hour conversation window has passed without a reply. Use an approved message template to send messages. |
| 131048 | Spam rate limit hit | Spam Rate limit reached. Unable to send this message because there are restrictions on the number of messages allowed from your phone number. This could be due to previous messages being blocked or flagged as spam. |
| 131049 | Meta chose not to deliver the message | Message not delivered to ensure a healthy ecosystem. Please retry later, gradually increasing the time between attempts. See [Per-User Marketing Template Message Limits](https://developers.facebook.com/micro_site/url/?click_from_context_menu=true&country=TW&destination=https%3A%2F%2Fdevelopers.facebook.com%2Fdocs%2Fwhatsapp%2Fcloud-api%2Fguides%2Fsend-message-templates%23per-user-marketing-template-message-limits&event_type=click&last_nav_impression_id=0Q9Bo0PyoyVau5PUp&max_percent_page_viewed=69&max_viewport_height_px=1156&max_viewport_width_px=1865&orig_http_referrer=https%3A%2F%2Fdevelopers.facebook.com%2Fdocs%2Fwhatsapp%2Fcloud-api%2Fsupport%2Ferror-codes%3Flocale%3Den_US&orig_request_uri=https%3A%2F%2Fdevelopers.facebook.com%2Fajax%2Fdocs%2Fnav%2F%3Fpath1%3Dwhatsapp%26path2%3Dcloud-api%26path3%3Dsupport%26path4%3Derror-codes&region=apac&scrolled=true&session_id=1A28E225CfN1JlRwB&site=developers) for additional information. |
| 131051 | Unsupported message type | Unable to send message as this is an unsupported message type. Review message content and try again. |
| 131052 | Media download error | Unable to download media. This could be an unsupported media type or the media exceeds the size limit. |
| 131053 | Media upload error | Invalid image format. Please check the image properties (8-bit image, RGB or RGBD, JPEG/PNG) and upload again. |
| 131056 | Business eligibility payment issue | Unable to send message. There are some errors with your payment method. Check your account payment method and status to send messages, or contact us for support. |
| 132000 | Template param count mismatch | The number of parameters does not match the requirement. Please check and try again. |
| 132001 | Template does not exist | Template does not exist in the specified language, or the template has not been approved. Please make sure your template has been approved, and the template name and language locale are correct. Click [here](https://developers.facebook.com/docs/whatsapp/message-templates/guidelines/) to view message template guidelines |
| 132005 | Template Hydrated Text Too Long | The translated text in your message template exceeds the character limit of 1024 characters. Please edit your template and try again. |
| 132007 | Template format character policy violated | Template content violates WhatsApp's policy. Please review your message template again, or click [here](https://developers.facebook.com/docs/whatsapp/message-templates/guidelines/#rejection-reasons) to view WhatsApp's policy. |
| 132021 | Template parameter format mismatch | The variable parameter values included in the request are not using the format specified in the template. Click [here](https://developers.facebook.com/docs/whatsapp/message-templates/guidelines/) to view message template guidelines and review your request before retrying. |
| 132015 | Template is paused | Your template is paused due to low quality. Edit your content to improve template quality and submit for approval. Click [here](https://developers.facebook.com/docs/whatsapp/business-management-api/message-templates#edit-a-message-template) to view message template guidelines. |
| 132016 | Template is disabled | Your template has been disabled permanently due to being paused too many times for low quality. Create a new template with different content to try again. Click [here](https://developers.facebook.com/docs/whatsapp/business-management-api/message-templates#edit-a-message-template) to view message template guidelines. |
| 133000 | Incomplete Deregistration | Your previous account cancellation was not completed. If you wish to cancel your account, please return to the previous pages to retry. |
| 133004 | Service temporarily unavailable | Server is temporarily unavailable. Click [here](https://metastatus.com/whatsapp-business-api) to view WhatsApp API status and check the response details before retrying. |
| 133005 | N/A | Incorrect two-step verification PIN. Please check and try again. |
| 133006 | N/A | You are required to verify your phone number before registering it. Please verify your phone number to continue. |
| 133008 | N/A | Too many verification attempts for this phone number. Please check and try again. |
| 133009 | N/A | Two-step verification PIN was not loading properly. Please try again. |
| 133010 | N/A | This phone number is not registered on the WhatsApp Business Platform. Check your phone number or go to WhatsApp Business Platform to register your phone number. |
| 133015 | N/A | The phone number you are attempting to register was recently deleted and has not been deleted completely. |
| 135000 | Generic user error | Unable to send your message because of an unknown error with your request parameters. Check your e ndpoint's reference to determine if you are querying the endpoint using the correct syntax. If the error persists, contact us for support. |

## 360dialog

Here is a list of 360dialog error codes, their possible causes, and suggested solutions.

```markdown
| Error code | Description | Possible cause and suggested solution |
|---|---|---|
| 400 | You have exceeded the limit of 1 request every * seconds. Try again later. | You have exceeded the endpoint call limit of 1 request per second. You can view the call limit [here](https://docs.360dialog.com/docs/account-management/capacity-quality-rating-and-messaging-limits). |
| 403 | URL is blocked. | URL API path is blocked. Please contact us for support to let us know the details of the issue and describe your use case. |
| 404 | Invalid api_key or token expired | Invalid API key or the token has expired. Generate another API key to try again. |
| 408 | Message is not valid | Unable to send message because it was pending for more than 1 day. Draft your message again to resend to customers. |
| 470 | Re-engagement message | Unable to send message. The 24-hour conversation window has passed without a reply. Use an approved message template to send messages |
| 500 | Generic error | The WhatsApp consumer client could not display the highly structured message (HSM) due to a format issue. Please check the template format and ensure it meets the required standards. |
| 500 | Internal server error | There has been an internal server error. Restarting will take up to 10 minutes. If you continue to receive this error after 10 minutes, contact us for support. |
| 502 | WhatsApp for Business API not available | There has been an error with 360dialog's WhatsApp for Business API has the WABA account does not exist in the system, or the integration is not running properly. Please check the account status to ensure it's active. |
| 1000 | Generic error | The image file format (audio/mpeg) is not supported. Please ensure the message type matches the MIME-type of the file. For example, use "audio" instead of "image" for audio files. Additionally, check if the video file has an audio track. Contact us for support if the error persists. |
| 1000 | Generic error | Unable to generate the processed file path. Please try again later, or contact us for support. |
| 1005 | Access denied | An error occurred when accessing this WhatsApp number. This number has not been approved for an enterprise. To resolve the issue, please reinstall WhatsApp on the device where the number was previously registered and ensure that it is completely deleted. Contact us for support if the error persists. |
| 1005 | Access denied | An unknown error occurred when trying to access the WhatsApp number. To resolve the issue, please reinstall WhatsApp on the device where the number was previously registered and ensure that it is completely deleted. |
| 1005 | Access denied | Error occurred during phone number registration. Please wait until the timeout phase ends and try again. |
| 1006 | Resource not found | Unable to find related files or resources. This could be because you were not using the v1/contact endpoint to get. valid "WA_id" before sending out the message. Please use the v1/contact endpoint to try again. |
| 1009 | Parameter value is not valid | The value you entered for a parameter is of the wrong type. For sending a list message, please check and see if each row's ID is unique. If you are sending a single product message, head to Commerce Manager to check if you have sent out the correct "catalog_id". If you are sending out a multi-product message, please check if your "product_retailer_id" is unique per section, and make sure you have entered the header and body objects, and they both have an assigned value. |
| 1014 | Internal error | The provided URL is either double URL-encoded or does not exist. The image upload has failed due to incorrect uploading or an endpoint that cannot be found. Additionally, the hash provided does not match the user's latest hash. Please check the URL, ensure proper encoding, and verify that the file exists. If the issue persists, please contact our support team for assistance. |
| 1026 | Receiver incapable | Delivery rates may not reach 100% due to factors like users being offline, network issues, or phone inactivity. Avoid resending messages to the same recipient as it can lead to complaints or being blocked; monitor message status via callbacks instead. |
| 2001 | Template missing | The template you are trying to send is not active, or the template does not exist for a language and locale. Contact us for support for setting up your template. |
```

## Twilio

Here is a list of Twilio error codes, their possible causes, and suggested solutions.

| **Error code** | **Description** | **Possible cause and suggested solution** |
|---|---|---|
| 63001 | Channel could not authenticate the request. Please see Channel specific error message for more information | The channel encountered an authentication error while processing the request. To resolve this issue, address the specific authentication error mentioned in the channel-specific message. If the error persists, contact us for support. |
| 63003 | Channel could not find to-address | Unable to locate file or resource. Please check and try again, or contact us for support. |
| 63003 | Channel could not find to-address | The message is failed to send because the channel could not find the recipient's address.  To resolve this, please send the message to a phone number that is not your own. |
| 63005 | Channel did not accept given content. Plese see Channel specific error message for more information | The provided content was not accepted by the channel. Please try again later, or contact us for support. |
| 63010 | Twilio's platform encountered an internal error processing this message | There was an internal error processing the message on Twilio's platform. This issue is likely due to a missing or misconfigured Webhook URL.  Please ensure that you have properly configured the REST API Webhook format. Double-check the settings to ensure they meet the required specifications. |
| 63012 | Invalid request | Invalid request. Please check and try again, or contact us for support. |
| 63012 | Channel provider returned an internal service error | There are some issues with Twilio at the moment. Please try again later, or contact us for support. |
| 63012 | Channel provider returned an internal service error | There has been an error with Twilio's service. Please try again later, or contact us for support. |
| 63012 | Channel provider returned an internal service error | Unable to send message because an unknown error occurred on Twilio. Try again later or contact us for support. |
| 63013 | Channel policy violation | Invalid parameter value or missing required parameter. If you are sending a list message, please check if you have missed the title part of the section object or IDs for your rows.  If you are sending a single-product message or a multi-product message, please check if you have missed the "catalog_id" or the "product_retailer_id". |
| 63013 | Channel policy violation | The message could not be sent due to a missing required parameters.  If you are sending a List Message, ensure that you include the title for the section object and IDs for the rows.  For Single Product messages or Multi-Product messages, make sure to include the "catalog_id" and "product_retailer_id" as they are required.  To resolve this, review your message parameters and include the necessary required parameters. |
| 63016 | Failed to send freeform message because you are outside the allowed window. If you are using WhatsApp, please use a Message Template. | Unable to send message. The 24-hour conversation window has passed without a reply. Use an approved message template to send messages. |
| 63018 | Invalid IP route | Invalid IP route. For outgoing messages, your message is unable to be sent out because there were too many messages sent from this phone number in a short period of time. Try resending the failed messages later.  For incoming messages, there has been an error when downloading the media file due to rate-limiting. Please ask the sender to resend the message. |
| 63018 | Invalid IP route | The message failed to send because the phone number has reached the limit for sending messages from previous messages being blocked or flagged as spam.  To resolve this, check the quality status in the WhatsApp Manager and refer to the Quality-Based Rate Limits documentation for more information. |
| 63018 | Rate limit exceeded for channel | You have reached your limit for this channel. Please review your account balance or contact us for support. |
| 63019 | Media failed to download | Unable to download media file. Please try again later or contact us for support. |
| 63020 | Twilio encountered a Business Manager account error | The message failed to send due to payment-related issues. Please check your payment setup in WhatsApp Manager and resolve any of the following issues:  *   Payment account not attached to a WhatsApp Account *   Credit line over the limit *   Inactive or unset credit line (Payment account) *   Deleted WhatsApp Business Account *   Suspended account *   Timezone or currency not set *   Pending or declined MessagingFor request (On Behalf Of) *   Exceeded conversation free tier threshold without valid payment method (effective from February 1, 2022).  Once the payment setup is corrected, try sending the message again. Contact us for support if the error persists. |
| 63021 | Channel invalid content error | Unable to send this message during its Time to Live (TTL) duration. Please try again later, or contact us for support. |
| 63021 | Channel invalid content error | Your message contains invalid content. Please review your mesage to try again, or contact us for support. |
| 63021 | Channel invalid content error | Your message content cannot exceed the character limit of 4096 characters. Please edit your message to try again. |
| 63022 | Invalid vname certificate | The certificate signature is invalid, indicating that the business client's certificate was not properly signed using the client's identity keys. This typically occurs when the client re-registers with new identity keys but skips the full certificate creation process.  To resolve this issue, please ensure that the client completes the entire certificate creation flow after re-registering with new identity keys. |
| 63022 | Invalid vname certificate | The message failed to send due to this certificate issue. To resolve the problem, please download a new certificate from the WhatsApp Manager and register it again. |
| 63022 | Invalid vname certificate | Unable to send message because there was an error related to your vname certifcate. To resolve the problem, please download a new certificate from the WhatsApp Manager and register it again. |
| 63024 | Invalid message recipient | Invalid message recipient. Please review your message and try sending again. |
| 63024 | Invalid message recipient | Message cannot be delivered due to invalid recipients selected. Please check and try again. |
| 63025 | Media already exists | Media already exists. Please review and try again. |
| 63027 | Template does not exist for a language and locale | {language} content cannot be added while the existing {language} content is being deleted. Try again in 4 weeks or create a new message template. |
| 63027 | Template does not exist for a language and locale | Your template is currently inactive, or the template does not exist for a language or locale. Please check and try again. |
| 63028 | Number of parameters provided does not match the expected number of parameters | The number of parameters provided does not match with the expected amount. Please check and try again, or contact us for support. |
| 63029 | The receiver failed to download the template | Unable to fetch and download message template. Please try again later, or contact us for support. |
| 63030 | Unsupported parameter for type of channels message | The message could not be processed due to an unsupported parameter for the channel's message type. The provided parameter is not required. Review the message parameters and remove any unnecessary or unsupported ones. |
| 63032 | We cannot send this message to this user because of a WhatsApp limitation | The customer's phone number is selected as part of a WhatsApp user experience experiment and will not be able to receive any marketing template message. Contact the customer via other non-WhatsApp means to request a message for resending within the service window in order to initiate a conversation. |
| 63033 | Recipient blocked to receive message | The message failed to send because the recipient blocked message reception.  To resolve this, please contact the recipient and request them to unblock or add your number to their allowed list. |