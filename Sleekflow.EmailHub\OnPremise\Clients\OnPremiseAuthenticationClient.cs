using MailKit;
using MailKit.Security;
using Serilog;

namespace Sleekflow.EmailHub.OnPremise.Clients;

public class OnPremiseAuthenticationClient : OnPremiseClient
{
    private readonly ILogger<OnPremiseAuthenticationClient> _logger;

    public OnPremiseAuthenticationClient(
        string serverType,
        string hostName,
        string username,
        string password,
        int portNumber,
        SecureSocketOptions sslOptions,
        FetchRequest? fetchRequest = null)
        : base(
            serverType,
            hostName,
            username,
            password,
            portNumber,
            fetchRequest ?? new FetchRequest(MessageSummaryItems.UniqueId), sslOptions)
    {
        _logger = new LoggerFactory().AddSerilog().CreateLogger<OnPremiseAuthenticationClient>();
    }

    public override async Task RunAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await ReconnectAsync(cancellationToken);
        }
        catch (OperationCanceledException)
        {
            // ignored
        }

        _logger.LogInformation(
            "[OnPremiseAuthenticationClient]: Finish RunAsync, " +
                               "serverName: {hostName}", HostName);
    }
}