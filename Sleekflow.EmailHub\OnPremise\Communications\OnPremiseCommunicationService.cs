using MailKit.Security;
using MassTransit;
using MimeKit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Attachments;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Models.OnPremise.Authentications;
using Sleekflow.EmailHub.Models.OnPremise.Communications;
using Sleekflow.EmailHub.Models.OnPremise.Events;
using Sleekflow.EmailHub.Models.OnPremise.Subscriptions;
using Sleekflow.EmailHub.OnPremise.Authentications;
using Sleekflow.EmailHub.OnPremise.Clients;
using Sleekflow.EmailHub.OnPremise.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.EmailHub.OnPremise.Communications;

public interface IOnPremiseCommunicationService : IEmailCommunicationService
{
}

public class OnPremiseCommunicationService : IScopedService, IOnPremiseCommunicationService
{
    private readonly IEmailRepository _emailRepository;
    private readonly IOnPremiseAuthenticationService _onPremiseAuthenticationService;
    private readonly IOnPremiseSubscriptionService _onPremiseSubscriptionService;
    private readonly ILogger<OnPremiseCommunicationService> _logger;
    private readonly IIdService _idService;
    private readonly IBus _bus;
    private readonly IAttachmentService _attachmentService;
    private readonly IProviderConfigService _providerConfigService;

    public OnPremiseCommunicationService(
        IEmailRepository emailRepository,
        IOnPremiseAuthenticationService onPremiseAuthenticationService,
        IOnPremiseSubscriptionService onPremiseSubscriptionService,
        ILogger<OnPremiseCommunicationService> logger,
        IIdService idService,
        IBus bus,
        IAttachmentService attachmentService,
        IProviderConfigService providerConfigService)
    {
        _emailRepository = emailRepository;
        _onPremiseAuthenticationService = onPremiseAuthenticationService;
        _onPremiseSubscriptionService = onPremiseSubscriptionService;
        _logger = logger;
        _idService = idService;
        _bus = bus;
        _attachmentService = attachmentService;
        _providerConfigService = providerConfigService;
    }

    public async Task OnReceiveEmailAsync(
        List<string> sleekflowCompanyIds,
        string emailAddress,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _bus.Publish(
            new OnOnPremiseSyncAllEmailsTriggeredEvent(
                emailAddress),
            cancellationToken);
    }

    public async Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        var authentication =
            await _onPremiseAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                sender.EmailAddress,
                "smtp",
                cancellationToken);

        var onPremiseAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OnPremiseAuthenticationMetadata ??
            throw new NullReferenceException(
                $"Cannot parse gmailAuthenticationMetaData: emailAddress {sender.EmailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        _ = await _onPremiseSubscriptionService.GetSubscriptionAsync(
            sleekflowCompanyId,
            sender.EmailAddress,
            cancellationToken);

        var mimeMessage = new MimeMessage();

        mimeMessage.From.Add(new MailboxAddress(sender.Name, sender.EmailAddress));

        var toAddresses = to.ToList();

        foreach (var recipient in toAddresses)
        {
            mimeMessage.To.Add(new MailboxAddress(recipient.Name, recipient.EmailAddress));
        }

        var ccAddresses = cc.ToList();

        foreach (var ccAddress in ccAddresses)
        {
            mimeMessage.Cc.Add(new MailboxAddress(ccAddress.Name, ccAddress.EmailAddress));
        }

        var bccAddresses = bcc.ToList();

        foreach (var bccAddress in bccAddresses)
        {
            mimeMessage.Bcc.Add(new MailboxAddress(bccAddress.Name, bccAddress.EmailAddress));
        }

        mimeMessage.Subject = subject;

        var replyToAddresses = replyTo.ToList();

        foreach (var replyToAddress in replyToAddresses)
        {
            mimeMessage.ReplyTo.Add(new MailboxAddress(replyToAddress.Name, replyToAddress.EmailAddress));
        }

        var builder = new BodyBuilder
        {
            HtmlBody = htmlBody ?? string.Empty, TextBody = textBody ?? string.Empty
        };

        foreach (var emailAttachment in emailAttachments)
        {
            await _attachmentService.ProcessOutboundAttachment(
                emailAttachment,
                builder,
                cancellationToken);
        }

        mimeMessage.Body = builder.ToMessageBody();

        var emailId = _idService.GetId("Email");

        try
        {
            using var client = new OnPremiseSendEmailClient(
                mimeMessage,
                ProtocolTypes.Smtp,
                onPremiseAuthenticationMetadata.ServerName,
                onPremiseAuthenticationMetadata.Username,
                onPremiseAuthenticationMetadata.Password,
                onPremiseAuthenticationMetadata.PortNumber,
                SecureSocketOptions.Auto);
            await client.RunAsync(cancellationToken);
            _logger.LogInformation("[OnPremise]: email with Id {id} sent at {time}", emailId, DateTime.UtcNow);
            client.Exit();
        }
        catch (Exception)
        {
            throw new SfInternalErrorException(
                $"Error on sending message: emailAddress {sender.EmailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        await _emailRepository.UpsertAsync(
            new Email(
                emailId,
                sleekflowCompanyId,
                sender,
                new List<EmailContact>
                {
                    sender
                },
                toAddresses,
                ccAddresses,
                bccAddresses,
                replyToAddresses,
                subject,
                null,
                textBody,
                true,
                emailAttachments.ToList(),
                new OnPremiseEmailMetadata(
                    mimeMessage.MessageId,
                    mimeMessage.Priority,
                    mimeMessage.Importance,
                    mimeMessage.XPriority,
                    mimeMessage.Date)),
            emailId,
            cancellationToken: cancellationToken);
    }

    public async Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _bus.Publish(
            new OnSendEmailTriggeredEvent(
                sleekflowCompanyId,
                ProviderNames.OnPremise,
                sender,
                subject,
                to,
                cc,
                bcc,
                replyTo,
                htmlBody,
                textBody,
                emailAttachments,
                emailMetadata),
            cancellationToken);
    }

    public async Task SyncAllEmailsAsync(string emailAddress, CancellationToken cancellationToken = default)
    {
        var sleekflowCompanyIds = await _onPremiseSubscriptionService.FilterSubscribedCompanies(
            await _providerConfigService.GetCompanyIdsByEmailAddressAsync(emailAddress, cancellationToken),
            emailAddress,
            cancellationToken);

        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            var authentication =
                await _onPremiseAuthenticationService.GetAuthenticationAsync(
                    sleekflowCompanyId,
                    emailAddress,
                    ProtocolTypes.Imap,
                    cancellationToken);

            var authenticationMetadata =
                authentication.EmailAuthenticationMetadata as OnPremiseAuthenticationMetadata ??
                throw new SfInternalErrorException(
                    $"cannot parse onPremiseAuthenticationMetadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

            var subscription =
                await _onPremiseSubscriptionService.GetSubscriptionAsync(
                    sleekflowCompanyId,
                    emailAddress,
                    cancellationToken);

            var subscriptionMetadata = subscription.EmailSubscriptionMetadata as OnPremiseSubscriptionMetadata ??
                                       throw new SfInternalErrorException(
                                           $"cannot parse onPremiseSubscriptionMetadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

            await SyncOnPremiseMailAsync(
                sleekflowCompanyId,
                emailAddress,
                subscriptionMetadata.StartIndex,
                authenticationMetadata,
                cancellationToken);
        }
    }

    private async Task SyncOnPremiseMailAsync(
        string sleekflowCompanyId,
        string emailAddress,
        int startIndex,
        OnPremiseAuthenticationMetadata authenticationMetadata,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            $"OnPremise starts sync: emailAddress {emailAddress} of sleekflowCompanyIds {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        using (var client = new OnPremisePeriodicSyncClient(
                   ProtocolTypes.Imap,
                   authenticationMetadata.ServerName,
                   authenticationMetadata.Username,
                   authenticationMetadata.Password,
                   authenticationMetadata.PortNumber,
                   SecureSocketOptions.Auto,
                   startIndex))
        {
            await client.RunAsync();
            var messageSummaries = client.MessageSummaries;
            startIndex += messageSummaries.Count + 1;
            var downloadedMessages = client.Messages;

            foreach (var message in downloadedMessages)
            {
                try
                {
                    var emailId = _idService.GetId("Email");
                    List<EmailAttachment> attachments = new ();

                    foreach (var attachment in message.Attachments)
                    {
                        await _attachmentService.ProcessInboundAttachment(
                            sleekflowCompanyId,
                            emailId,
                            (message.To.FirstOrDefault() ?? throw new SfNotFoundObjectException(string.Empty)) as
                            MailboxAddress ??
                            throw new SfInternalErrorException(string.Empty),
                            attachment,
                            attachments,
                            cancellationToken);
                    }

                    await _emailRepository.UpsertAsync(
                        new Email(
                            emailId,
                            sleekflowCompanyId,
                            message.From.Mailboxes.Select(x => new EmailContact(x.Address, x.Name)).First(),
                            message.From.Mailboxes.Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                            message.To.Mailboxes.Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                            message.Cc.Mailboxes.Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                            message.Bcc.Mailboxes.Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                            message.ReplyTo.Mailboxes.Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                            message.Subject,
                            message.HtmlBody,
                            message.TextBody,
                            false,
                            attachments,
                            new OnPremiseEmailMetadata(
                                message.MessageId,
                                message.Priority,
                                message.Importance,
                                message.XPriority,
                                message.Date)),
                        emailId,
                        cancellationToken: cancellationToken);
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        $"OnPremise provider Sync error: emailAddress {emailAddress} of sleekflowCompanyIds {sleekflowCompanyId}, ex: {e}",
                        emailAddress,
                        e);
                }
            }

            client.Exit();
        }

        await _onPremiseSubscriptionService.UpdateStartIndex(
            sleekflowCompanyId,
            emailAddress,
            startIndex,
            cancellationToken);
    }
}