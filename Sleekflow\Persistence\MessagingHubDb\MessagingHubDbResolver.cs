using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.MessagingHubDb;

public interface IMessagingHubDbResolver : IContainerResolver
{
}

public class MessagingHubDbResolver : IMessagingHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public MessagingHubDbResolver(IMessagingHubDbConfig messagingHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            messagingHubDbConfig.Endpoint,
            messagingHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}