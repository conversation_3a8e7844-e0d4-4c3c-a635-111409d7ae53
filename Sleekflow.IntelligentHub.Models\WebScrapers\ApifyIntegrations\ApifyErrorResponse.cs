﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyErrorResponse
{
    [JsonProperty(PropertyName = "error")]
    public ApifyError Error { get; set; }

    [JsonConstructor]
    public ApifyErrorResponse(
        ApifyError error)
    {
        Error = error;
    }
}

public sealed class ApifyError
{
    [JsonProperty(PropertyName = "type")]
    public string Type { get; set; }

    [JsonProperty(PropertyName = "message")]
    public string Message { get; set; }

    [JsonConstructor]
    public ApifyError(
        string type,
        string message)
    {
        Type = type;
        Message = message;
    }
}