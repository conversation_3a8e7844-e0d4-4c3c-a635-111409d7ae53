using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class ScheduleDeleteWorkflows : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public ScheduleDeleteWorkflows(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class ScheduleDeleteWorkflowsInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_ids")]
        [Required]
        [Validations.ValidateArray]
        public List<string> WorkflowIds { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ScheduleDeleteWorkflowsInput(
            string sleekflowCompanyId,
            List<string> workflowIds,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowIds = workflowIds;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ScheduleDeleteWorkflowsOutput
    {
    }

    public async Task<ScheduleDeleteWorkflowsOutput> F(ScheduleDeleteWorkflowsInput scheduleDeleteWorkflowsInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            scheduleDeleteWorkflowsInput.SleekflowStaffId,
            scheduleDeleteWorkflowsInput.SleekflowStaffTeamIds);

        await _workflowService.ScheduleDeleteWorkflowsAsync(
            scheduleDeleteWorkflowsInput.WorkflowIds,
            scheduleDeleteWorkflowsInput.SleekflowCompanyId,
            sleekflowStaff);

        return new ScheduleDeleteWorkflowsOutput();
    }
}