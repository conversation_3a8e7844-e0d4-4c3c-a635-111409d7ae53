using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Models.MessageObjects;
using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Messages;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Messages.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class SendWhatsappCloudApiMessage
    : ITrigger<
        SendWhatsappCloudApiMessage.SendWhatsappCloudApiMessageInput,
        SendWhatsappCloudApiMessage.SendWhatsappCloudApiMessageOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IMessageService _messageService;
    private readonly ILogger<SendWhatsappCloudApiMessage> _logger;

    public SendWhatsappCloudApiMessage(
        IWabaService wabaService,
        IMessageService messageService,
        ILogger<SendWhatsappCloudApiMessage> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _messageService = messageService;
    }

    public class SendWhatsappCloudApiMessageInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("message_object")]
        public WhatsappCloudApiMessageObject MessageObject { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("is_mm_lite")]
        public bool IsMMLite { get; set; }

        [JsonConstructor]
        public SendWhatsappCloudApiMessageInput(
            string wabaPhoneNumberId,
            string sleekflowCompanyId,
            WhatsappCloudApiMessageObject messageObject,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            bool isMMLite = false)
        {
            WabaPhoneNumberId = wabaPhoneNumberId;
            SleekflowCompanyId = sleekflowCompanyId;
            MessageObject = messageObject;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            IsMMLite = isMMLite;
        }
    }

    public class SendWhatsappCloudApiMessageOutput
    {
        [JsonProperty("message_response")]
        public SendMessageResponse MessageResponse { get; set; }

        [JsonConstructor]
        public SendWhatsappCloudApiMessageOutput(SendMessageResponse messageResponse)
        {
            MessageResponse = messageResponse;
        }
    }

    public async Task<SendWhatsappCloudApiMessageOutput> F(
        SendWhatsappCloudApiMessageInput sendWhatsappCloudApiMessageInput)
    {
        var sleekflowCompanyId = sendWhatsappCloudApiMessageInput.SleekflowCompanyId;
        var wabaPhoneNumberId = sendWhatsappCloudApiMessageInput.WabaPhoneNumberId;
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            sendWhatsappCloudApiMessageInput.SleekflowStaffId,
            sendWhatsappCloudApiMessageInput.SleekflowStaffTeamIds);

        var isMMLite = sendWhatsappCloudApiMessageInput.IsMMLite;

        var waba = await _wabaService.GetWabaWithWabaPhoneNumberIdAsync(sleekflowCompanyId, wabaPhoneNumberId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba, true))
        {
            throw new SfMessageFunctionRestrictedFunctionException("Message Function is Limited");
        }

        return new SendWhatsappCloudApiMessageOutput(
            await _messageService.SendWhatsappCloudApiMessageAsync(
                waba,
                sleekflowCompanyId,
                wabaPhoneNumberId,
                sendWhatsappCloudApiMessageInput.MessageObject,
                sleekflowStaff,
                isMMLite));
    }
}