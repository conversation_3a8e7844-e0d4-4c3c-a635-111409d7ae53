﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class OnZohoObjectEnrollmentToFlowHubRequestedEvent
{
    public DateTimeOffset CreatedAt { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string ZohoConnectionId { get; set; }

    public string ObjectId { get; set; }

    public string ObjectType { get; set; }

    public Dictionary<string, object?> ObjectDict { get; set; }

    public string FlowHubWorkflowId { get; set; }

    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnZohoObjectEnrollmentToFlowHubRequestedEvent(
        DateTimeOffset createdAt,
        string sleekflowCompanyId,
        string zohoConnectionId,
        string objectId,
        string objectType,
        Dictionary<string, object?> objectDict,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        CreatedAt = createdAt;
        SleekflowCompanyId = sleekflowCompanyId;
        ZohoConnectionId = zohoConnectionId;
        ObjectId = objectId;
        ObjectType = objectType;
        ObjectDict = objectDict;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}