<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GraphApi.Client.Const</name>
    </assembly>
    <members>
        <member name="T:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiComponentTypeConst">
            <summary>
            must be one of {BODY, BUTTON, HEADER}
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.delivered">
            <summary>
            A message sent by your business was delivered to the user's device.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.read">
            <summary>
            A message sent by your business was read by the user. read notifications are only available for users that have read receipts enabled. For users that do not have it enabled, you only receive the delivered notification.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.sent">
            <summary>
            A message sent by your business is in transit within our systems.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.failed">
            <summary>
            A message sent by your business failed to send. A reason for the failure will be included in the callback.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.deleted">
            <summary>
            A message send by the user was deleted by the user. Upon receiving this notification, you should ensure that the message is deleted from your system if it was downloaded from the server.
            </summary>
        </member>
        <member name="F:GraphApi.Client.Const.WhatsappCloudApi.WhatsappCloudApiMessageStatusConst.warning">
            <summary>
            A message your business sent contains an item in a catalog that is not available or does not exist.
            </summary>
        </member>
    </members>
</doc>
