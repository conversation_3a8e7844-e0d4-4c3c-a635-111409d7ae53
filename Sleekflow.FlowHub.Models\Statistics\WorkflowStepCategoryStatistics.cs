﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.Statistics;

[ContainerId(ContainerNames.WorkflowStepCategoryStatistics)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class WorkflowStepCategoryStatistics : Entity, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("workflow_type")]
    public string WorkflowType { get; set; }

    [JsonProperty("workflow_trigger_step_category")]
    public string WorkflowTriggerStepCategory { get; set; }

    [JsonProperty("step_category_count_dict")]
    public Dictionary<string, int> StepCategoryCountDict { get; set; }

    [JsonProperty("valid_from")]
    public DateTimeOffset ValidFrom { get; set; }

    [JsonProperty("valid_to")]
    public DateTimeOffset ValidTo { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public WorkflowStepCategoryStatistics(
        string id,
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        string workflowType,
        string workflowTriggerStepCategory,
        Dictionary<string, int> stepCategoryCountDict,
        DateTimeOffset validFrom,
        DateTimeOffset validTo,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string? eTag)
        : base(
            id,
            SysTypeNames.WorkflowStepCategoryStatistics)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        WorkflowType = workflowType;
        WorkflowTriggerStepCategory = workflowTriggerStepCategory;
        StepCategoryCountDict = stepCategoryCountDict;
        ValidFrom = validFrom;
        ValidTo = validTo;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        ETag = eTag;
    }
}