using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.States;

public interface IStateRepository : IRepository<State>
{
}

public class StateRepository : BaseRepository<State>, IStateRepository, IScopedService
{
    public StateRepository(
        ILogger<StateRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}