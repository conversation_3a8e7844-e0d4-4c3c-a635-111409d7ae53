/**
 * Handler that will be called during the execution of a PreUserRegistration flow.
 *
 * @param {Event} event - Details about the context and user that is attempting to register.
 * @param {PreUserRegistrationAPI} api - Interface whose methods can be used to change the behavior of the signup.
 */
const axios = require("axios");
exports.onExecutePreUserRegistration = async (event, api) => {
  const disabledApps = ["sleekflow-reseller-portal-app", "sleekflow-client-powerflow-app", "sleekflow-client-mobile-app"];
    try {
        const emailDisposedRequest = await axios.request({
            method: 'GET',
            timeout: 5000,
            url: `https://api.usercheck.com/email/${event.user.email}`,
            headers: {'content-type': 'application/json', 'Authorization': `Bearer ${event.secrets.user_email_check_secret_key}`},
        });

        if (emailDisposedRequest.data.disposable) {
            api.access.deny(
                "User registration disabled for " + event.client?.name + ` invalid email ${event.user.email}`,
                "User registration is not allowed in this Application" + ` invalid email ${event.user.email}`);
        }
    } catch (e) {

    }
  if (event.client && disabledApps.includes(event.client.name)) {
    api.access.deny(
      "User registration disabled for " + event.client.name,
      "User registration is not allowed in this Application");
  }

  if (event.user.username && !(/^[0-9a-zA-Z_\\.\\-]+$/.test(event.user.username))) {
    if (event.user.username.startsWith("invite.")) {
      return;
    }

    const LOCALIZED_MESSAGES = {
      en: 'Your username must only contain alphanumeric values',
    };

    const userMessage = LOCALIZED_MESSAGES[event.request.language] || LOCALIZED_MESSAGES['en'];
    api.access.deny('no_signups_for_non_alphanumeric_values', userMessage);
  }
};
