﻿using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Integrator.FlowHubs;
using Sleekflow.FlowHub.Integrator.Integrations;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Constants.FlowHubIntegrators;
using Sleekflow.FlowHub.Models.Integrations;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.Integrator.Integrators.ZapierTriggerIntegrator;

public abstract class ZapierTriggerIntegratorBase
{
    /// <inheritdoc cref="ZapierTriggerNames"/>
    /// <summary>
    /// Trigger name.
    /// </summary>
    protected abstract string TriggerName { get; }

    /// <summary>
    /// Json string of the FlowBuilder Trigger(s) that will be used by this Zapier Trigger.
    /// </summary>
    protected abstract string WorkflowTriggers { get; set; }

    /// <summary>
    /// Json string of the FlowBuilder Step(s) that will be used by this Zapier Trigger.
    /// </summary>
    protected abstract string WorkflowSteps { get; set; }

    /// <summary>
    /// Json string of the workflow Metadata.
    /// Could be Null if no UI required.
    /// </summary>
    protected abstract string? WorkflowMetadata { get; set; }

    private readonly ILogger<ZapierTriggerIntegratorBase> _logger;
    private readonly IInternalWorkflowService _internalWorkflowService;
    private readonly IZapierTriggerIntegrationService _zapierTriggerIntegrationService;
    private readonly IIdService _idService;

    protected ZapierTriggerIntegratorBase(
        ILogger<ZapierTriggerIntegratorBase> logger,
        IInternalWorkflowService internalWorkflowService,
        IZapierTriggerIntegrationService zapierTriggerIntegrationService,
        IIdService idService)
    {
        _logger = logger;
        _internalWorkflowService = internalWorkflowService;
        _zapierTriggerIntegrationService = zapierTriggerIntegrationService;
        _idService = idService;
    }

    protected async Task IntegrateToFlowBuilderAsync(
        string sleekflowCompanyId,
        string zapId,
        string hookUrl,
        string? sleekflowLocation)
    {
        _logger.LogInformation(
            "Create Zapier Trigger Integration. {SleekflowCompanyId} {ZapId} {HookUrl} {SleekflowLocation}",
            sleekflowCompanyId,
            zapId,
            hookUrl,
            sleekflowLocation);

        if (await IsZapAlreadyIntegratedAsync(sleekflowCompanyId, zapId))
        {
            throw new SfUserFriendlyException($"Zapier trigger integration with Zap Id [{zapId}] already exists.");
        }

        var workflowName = $"Zapier Trigger - {TriggerName} - {zapId}";

        var workflow = await _internalWorkflowService.CreateAndEnableInternalWorkflowAsync(
            sleekflowCompanyId,
            WorkflowType.InternalForZapierIntegration,
            WorkflowTriggers,
            WorkflowSteps,
            WorkflowMetadata,
            workflowName);

        var zapierTriggerIntegration = new ZapierTriggerIntegration(
            _idService.GetId(SysTypeNames.ZapierTriggerIntegration),
            sleekflowCompanyId,
            zapId,
            hookUrl,
            TriggerName,
            sleekflowLocation,
            workflow.WorkflowId,
            workflow.WorkflowVersionedId,
            DateTimeOffset.Now,
            DateTimeOffset.Now);

        await _zapierTriggerIntegrationService.CreateIntegrationAsync(zapierTriggerIntegration, sleekflowCompanyId);
    }

    protected async Task SegregateWithFlowBuilderAsync(string sleekflowCompanyId, string zapId)
    {
        _logger.LogInformation(
            "Start delete Zapier Trigger Integration. {SleekflowCompanyId} {TriggerName} {ZapId}",
            sleekflowCompanyId,
            TriggerName,
            zapId);

        try
        {
            var zapierTriggerIntegration = (await _zapierTriggerIntegrationService.GetIntegrationsAsync(
                x =>
                    x.SleekflowCompanyId == sleekflowCompanyId &&
                    x.ZapId == zapId,
                2))
                .Single();

            await _internalWorkflowService.DeleteInternalWorkflowAsync(
                sleekflowCompanyId,
                zapierTriggerIntegration.WorkflowId);

            await _zapierTriggerIntegrationService.DeleteIntegrationAsync(
                sleekflowCompanyId,
                zapierTriggerIntegration.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Errors when deleting Zapier Trigger Integration. {SleekflowCompanyId} {ZapId}",
                sleekflowCompanyId,
                zapId);

            throw new SfUserFriendlyException($"Errors when deleting Zapier Trigger Integration: {ex.Message}");
        }
    }

    private async Task<bool> IsZapAlreadyIntegratedAsync(string sleekflowCompanyId, string zapId)
    {
        return (await _zapierTriggerIntegrationService.GetIntegrationsAsync(
                x =>
                    x.SleekflowCompanyId == sleekflowCompanyId &&
                    x.ZapId == zapId))
            .Any();
    }
}