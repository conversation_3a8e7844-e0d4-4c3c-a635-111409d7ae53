using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Cores;

[SwaggerInclude]
public class CreateUserProfileInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("name")]
    public string Name { get; set; }

    [Required]
    [JsonProperty("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonConstructor]
    public CreateUserProfileInput(string sleekflowCompanyId, string name, string phoneNumber)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Name = name;
        PhoneNumber = phoneNumber;
    }
}

[SwaggerInclude]
public class CreateUserProfileOutput : UserProfile
{
    [JsonConstructor]
    public CreateUserProfileOutput(string id, string sleekflowCompanyId, string phoneNumber)
        : base(id, sleekflowCompanyId, phoneNumber)
    {
    }
}