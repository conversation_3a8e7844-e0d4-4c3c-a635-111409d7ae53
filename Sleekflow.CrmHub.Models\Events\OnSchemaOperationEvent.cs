﻿using Sleekflow.Events;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Events;

public class OnSchemaOperationEvent : IEvent, IHasSleekflowCompanyId
{
    public const string OperationDeleteSchema = "DeleteSchema";
    public const string OperationDeleteSchemaProperties = "DeleteSchemaProperties";
    public const string OperationDeleteSchemaPropertyOptions = "DeleteSchemaPropertyOptions";
    public const string OperationReindexSchemaPropertyValues = "ReindexSchemaPropertyValues";

    public string SleekflowCompanyId { get; set; }

    public string SchemaId { get; set; }

    public string Operation { get; set; }

    public List<string>? IndexedPropertyIds { get; set; }

    public List<string>? ToBeDeletedPropertyIds { get; set; }

    public Dictionary<string, (string DataType, List<string> ToBeDeletedOptionId)>? ToBeDeletedOptionIdDictionary { get; set; }

    public OnSchemaOperationEvent(
        string sleekflowCompanyId,
        string schemaId,
        string operation,
        List<string>? indexedPropertyIds = null,
        List<string>? toBeDeletedPropertyIds = null,
        Dictionary<string, (string DataType, List<string> ToBeDeletedOptionId)>? toBeDeletedOptionIdDictionary = null)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemaId = schemaId;
        Operation = operation;
        IndexedPropertyIds = indexedPropertyIds;
        ToBeDeletedPropertyIds = toBeDeletedPropertyIds;
        ToBeDeletedOptionIdDictionary = toBeDeletedOptionIdDictionary;
    }
}