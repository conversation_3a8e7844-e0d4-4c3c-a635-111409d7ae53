﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Orders;

public class OrderDto : AuditEntityDto, IHasMetadata, IHasSleekflowUserProfileId
{
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty("line_items")]
    public List<OrderLineItem> LineItems { get; set; }

    [Required]
    [StringLength(3, MinimumLength = 3)]
    [JsonProperty("currency_iso_code")]
    public string CurrencyIsoCode { get; set; }

    [JsonProperty("total_price")]
    public decimal TotalPrice { get; set; }

    [JsonProperty("subtotal_price")]
    public decimal SubtotalPrice { get; set; }

    [JsonProperty("order_discount")]
    public Discount? OrderDiscount { get; set; }

    [JsonProperty("order_status")]
    public string OrderStatus { get; set; }

    [JsonProperty("payment_status")]
    public string PaymentStatus { get; set; }

    [JsonProperty("payment_link_sent_at")]
    public DateTimeOffset? PaymentLinkSentAt { get; set; }

    [JsonProperty("completed_at")]
    public DateTimeOffset? CompletedAt { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public OrderDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        string sleekflowUserProfileId,
        List<OrderLineItem> lineItems,
        string currencyIsoCode,
        decimal totalPrice,
        decimal subtotalPrice,
        Discount? orderDiscount,
        string orderStatus,
        string paymentStatus,
        DateTimeOffset? paymentLinkSentAt,
        DateTimeOffset? completedAt,
        Dictionary<string, object?> metadata)
        : base(id, sleekflowCompanyId, createdBy, updatedBy, createdAt, updatedAt)
    {
        StoreId = storeId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        LineItems = lineItems;
        CurrencyIsoCode = currencyIsoCode;
        TotalPrice = totalPrice;
        SubtotalPrice = subtotalPrice;
        OrderDiscount = orderDiscount;
        OrderStatus = orderStatus;
        PaymentStatus = paymentStatus;
        PaymentLinkSentAt = paymentLinkSentAt;
        CompletedAt = completedAt;
        Metadata = metadata;
    }

    public OrderDto(Order order)
        : this(
            order.Id,
            order.SleekflowCompanyId,
            order.CreatedBy,
            order.UpdatedBy,
            order.CreatedAt,
            order.UpdatedAt,
            order.StoreId,
            order.SleekflowUserProfileId,
            order.LineItems,
            order.CurrencyIsoCode,
            order.TotalPrice,
            order.SubtotalPrice,
            order.OrderDiscount,
            order.OrderStatus,
            order.PaymentStatus,
            order.PaymentLinkSentAt,
            order.CompletedAt,
            order.Metadata)
    {
    }
}