using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Outputs;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class KnowledgeBaseIntegrationTests
{
    // The mocked company requires to have a Knowledge Base Entry already created
    private const string MockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";

    [Test]
    public async Task HttpHeadTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        var clientFactory = scope.ServiceProvider.GetService<IHttpClientFactory>();
        var httpClient = clientFactory.CreateClient("default-handler");

        var request = new HttpRequestMessage(
            HttpMethod.Get,
            "https://tanstack.com/");
        request.Headers.Add(
            "User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

        var response = await httpClient.SendAsync(request);

        var mimeType = response.Content.Headers.ContentType?.MediaType;

        Assert.That(mimeType, Is.Not.Null);
    }

    [Test]
    public async Task WebsiteCrawlingTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        var clientFactory = scope.ServiceProvider.GetService<IHttpClientFactory>();

        var webCrawlingService = new WebCrawlingService(
            new Mock<ILogger<WebCrawlingService>>().Object,
            clientFactory,
            new Mock<IDocumentStatisticsCalculatorFactory>().Object);

        var testUrl = "https://eshop.sinomax.com/en";
        var results = await webCrawlingService!.CrawlBatchAsync(testUrl, [testUrl], [], 50);

        Assert.That(results.NewCrawlingResults.Count, Is.EqualTo(1));
        Assert.That(results.UpdatedUrlsToProcess.Count, Is.GreaterThan(1));
    }

    // [Test]
    // public async Task PatchSelectedUrlsTest()
    // {
    //     using var scope = Application.Host.Services.CreateScope();
    //
    //     Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;
    //
    //     // /KnowledgeBases/CreateWebsiteDocument
    //     var createWebsiteDocumentInput =
    //         new CreateWebsiteDocument.CreateWebsiteDocumentInput(
    //             "https://example.com",
    //             "Example Website",
    //             [
    //                 "https://example.com/page1",
    //                 "https://example.com/page2",
    //                 "https://example.com/page3"
    //             ],
    //             [
    //                 "XbbTR1lopPvx3GRP",
    //                 "Smart Reply"
    //             ]);
    //     var createWebsiteDocumentScenarioResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(createWebsiteDocumentInput)
    //                 .ToUrl("/authorized/KnowledgeBases/CreateWebsiteDocument");
    //         });
    //
    //     var createWebsiteDocumentOutput =
    //         await createWebsiteDocumentScenarioResult.ReadAsJsonAsync<
    //             Output<CreateWebsiteDocument.CreateWebsiteDocumentOutput>>();
    //
    //     Assert.That(createWebsiteDocumentOutput, Is.Not.Null);
    //     Assert.That(createWebsiteDocumentOutput.HttpStatusCode, Is.EqualTo(200));
    //
    //     var websiteDocumentRepository = scope.ServiceProvider.GetService<IWebsiteDocumentRepository>();
    //     await websiteDocumentRepository.PatchSelectedUrlsAsync(
    //         MockCompanyId,
    //         createWebsiteDocumentOutput.Data.Id,
    //         []);
    // }

    [Test]
    public async Task GetKnowledgeBaseSearchResultsTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /KnowledgeBases/GetKnowledgeBaseSearchResults
        var getKnowledgeBaseSearchResultsInput =
            new GetKnowledgeBaseSearchResults.GetKnowledgeBaseSearchResultsInput("test", null);
        var getKnowledgeBaseSearchResultsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getKnowledgeBaseSearchResultsInput)
                    .ToUrl("/authorized/KnowledgeBases/GetKnowledgeBaseSearchResults");
            });

        var getKnowledgeBaseSearchResultsOutput =
            await getKnowledgeBaseSearchResultsScenarioResult.ReadAsJsonAsync<
                Output<GetKnowledgeBaseSearchResults.GetKnowledgeBaseSearchResultsOutput>>();

        Assert.That(getKnowledgeBaseSearchResultsOutput, Is.Not.Null);
        Assert.That(getKnowledgeBaseSearchResultsOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetFileDocumentListTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /Documents/GetFileDocumentList
        var input =
            new GetFileDocumentList.GetFileDocumentListInput(
                null,
                null,
                null,
                null,
                null,
                null);
        var getKnowledgeBaseSearchResultsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(input)
                    .ToUrl("/authorized/KnowledgeBases/GetFileDocumentList");
            });

        var output = await getKnowledgeBaseSearchResultsScenarioResult.ReadAsTextAsync();
        var jObject = JObject.Parse(output);

        Assert.That(jObject["data"], Is.Not.Null);
        Assert.That((int) jObject["http_status_code"], Is.EqualTo(200));

        var getFileDocumentListOutput = jObject["data"];
        Assert.That(getFileDocumentListOutput["kb_documents"], Is.Not.Empty);

        // Make sure agent assignment is not null
        foreach (var kbDocument in getFileDocumentListOutput["kb_documents"])
        {
            Assert.That(kbDocument["agent_assignments"], Is.Not.Null);
        }
    }

    [Test]
    public async Task GetFileDocumentListFilterExcludeTrainingStatusTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /Documents/GetFileDocumentList
        var input =
            new GetFileDocumentList.GetFileDocumentListInput(
                null,
                [ProcessFileDocumentStatuses.FailedToConvert],
                null,
                "PGGI1kKDNmyy02B6",
                null,
                null);
        var getKnowledgeBaseSearchResultsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(input)
                    .ToUrl("/authorized/KnowledgeBases/GetFileDocumentList");
            });

        var output = await getKnowledgeBaseSearchResultsScenarioResult.ReadAsTextAsync();
        var jObject = JObject.Parse(output);

        Assert.That(jObject["data"], Is.Not.Null);
        Assert.That((int) jObject["http_status_code"], Is.EqualTo(200));

        var getFileDocumentListOutput = jObject["data"];
        Assert.That(getFileDocumentListOutput["kb_documents"], Is.Not.Empty);

        // Make sure agent assignment is not null
        foreach (var kbDocument in getFileDocumentListOutput["kb_documents"])
        {
            Assert.That(
                (string) kbDocument["file_document_process_status"],
                Is.Not.EqualTo(ProcessFileDocumentStatuses.FailedToConvert));
            Assert.That(
                (string) kbDocument["file_document_process_status"],
                Is.Not.EqualTo(ProcessFileDocumentStatuses.Failed));
        }
    }

    [Test]
    public async Task UpdateDocumentAgentAssignmentsForAgentTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /KnowledgeBases/UpdateDocumentAgentAssignmentsForAgent
        var updateAssignmentInput =
            new UpdateDocumentAgentAssignmentsForAgent.UpdateDocumentAgentAssignmentsForAgentInput(
                "XbbTR1lopPvx3GRP",
                [
                    "GA2IM46eVoaRV4Ry",
                    "aQVIdEJMXoGbMqdy",
                    "vQxIE0BBq0ZGElBq",
                ]);
        var updateAssignmentResults = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateAssignmentInput)
                    .ToUrl("/authorized/KnowledgeBases/UpdateDocumentAgentAssignmentsForAgent");
            });

        var updateAssignmentOutput =
            await updateAssignmentResults.ReadAsJsonAsync<
                Output<UpdateDocumentAgentAssignmentsForAgent.UpdateDocumentAgentAssignmentsForAgentOutput>>();

        Assert.That(updateAssignmentOutput, Is.Not.Null);
        Assert.That(updateAssignmentOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateDocumentAgentAssignmentsForDocumentTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /KnowledgeBases/UpdateDocumentAgentAssignmentsForDocument
        var updateAssignmentInput =
            new UpdateDocumentAgentAssignmentsForDocument.UpdateDocumentAgentAssignmentsForDocumentInput(
                "GA2IM46eVoaRV4Ry",
                [
                    "XbbTR1lopPvx3GRP",
                    "Smart Reply"
                ]);
        var updateAssignmentResults = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateAssignmentInput)
                    .ToUrl("/authorized/KnowledgeBases/UpdateDocumentAgentAssignmentsForDocument");
            });

        var updateAssignmentOutput =
            await updateAssignmentResults.ReadAsJsonAsync<
                Output<UpdateDocumentAgentAssignmentsForDocument.UpdateDocumentAgentAssignmentsForDocumentOutput>>();

        Assert.That(updateAssignmentOutput, Is.Not.Null);
        Assert.That(updateAssignmentOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Documents/GetFileDocumentList
        var input =
            new GetFileDocumentList.GetFileDocumentListInput(
                null,
                null,
                "PGGI1kKDNmyy02B6",
                null,
                null,
                null);
        var getKnowledgeBaseSearchResultsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(input)
                    .ToUrl("/authorized/KnowledgeBases/GetFileDocumentList");
            });

        var output = await getKnowledgeBaseSearchResultsScenarioResult.ReadAsTextAsync();
        var jObject = JObject.Parse(output);

        Assert.That(jObject["data"], Is.Not.Null);
        Assert.That((int) jObject["http_status_code"], Is.EqualTo(200));

        var getFileDocumentListOutput = jObject["data"];
        Assert.That(getFileDocumentListOutput["kb_documents"], Is.Not.Empty);
    }
    //
    // [Test]
    // public async Task CreateWebsiteDocumentTest()
    // {
    //     using var scope = Application.Host.Services.CreateScope();
    //
    //     Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;
    //
    //     // /KnowledgeBases/CreateWebsiteDocument
    //     var createWebsiteDocumentInput =
    //         new CreateWebsiteDocument.CreateWebsiteDocumentInput(
    //             "https://example.com",
    //             "Example Website",
    //             [
    //                 "https://example.com/page1",
    //                 "https://example.com/page2",
    //                 "https://example.com/page3"
    //             ],
    //             [
    //                 "XbbTR1lopPvx3GRP",
    //                 "Smart Reply"
    //             ]);
    //     var createWebsiteDocumentScenarioResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(createWebsiteDocumentInput)
    //                 .ToUrl("/authorized/KnowledgeBases/CreateWebsiteDocument");
    //         });
    //
    //     var createWebsiteDocumentOutput =
    //         await createWebsiteDocumentScenarioResult.ReadAsJsonAsync<
    //             Output<CreateWebsiteDocument.CreateWebsiteDocumentOutput>>();
    //
    //     Assert.That(createWebsiteDocumentOutput, Is.Not.Null);
    //     Assert.That(createWebsiteDocumentOutput.HttpStatusCode, Is.EqualTo(200));
    //
    //     // Call GetFileDocumentList and make sure the created website document is there with the correct attributes
    //     var getFileDocumentListInput =
    //         new GetFileDocumentList.GetFileDocumentListInput(
    //             null,
    //             null,
    //             null,
    //             null,
    //             null,
    //             null);
    //     var getFileDocumentListScenarioResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getFileDocumentListInput)
    //                 .ToUrl("/authorized/KnowledgeBases/GetFileDocumentList");
    //         });
    //
    //     var getFileDocumentListOutput = await getFileDocumentListScenarioResult.ReadAsTextAsync();
    //     var jObject = JObject.Parse(getFileDocumentListOutput);
    //
    //     Assert.That(jObject["data"], Is.Not.Null);
    //     Assert.That((int) jObject["http_status_code"], Is.EqualTo(200));
    //
    //     var getFileDocumentListData = jObject["data"];
    //     Assert.That(getFileDocumentListData["kb_documents"], Is.Not.Empty);
    //
    //     // Find the created website document
    //     var createdWebsiteDocument = getFileDocumentListData["kb_documents"]
    //         .FirstOrDefault(doc => (string) doc["id"] == createWebsiteDocumentOutput.Data.Id);
    //
    //     Assert.That(
    //         createdWebsiteDocument,
    //         Is.Not.Null,
    //         "Created website document should be found in the document list");
    //     Assert.That((string) createdWebsiteDocument["base_url_title"], Is.EqualTo("Example Website"));
    //     Assert.That((string) createdWebsiteDocument["base_url"], Is.EqualTo("https://example.com"));
    //     Assert.That(createdWebsiteDocument["agent_assignments"], Is.Not.Null);
    //
    //     // Verify agent assignments contain the expected agents
    //     var agentAssignments = createdWebsiteDocument["agent_assignments"].ToArray();
    //     Assert.That(
    //         agentAssignments.Any(
    //             assignment => (string) assignment[AgentAssignment.PropertyNameAgentId] == "XbbTR1lopPvx3GRP"),
    //         Is.True,
    //         "Should contain agent XbbTR1lopPvx3GRP");
    //     Assert.That(
    //         agentAssignments.Any(
    //             assignment => (string) assignment[AgentAssignment.PropertyNameAgentId] == "Smart Reply"),
    //         Is.True,
    //         "Should contain Smart Reply agent");
    // }
    //
    // [Test]
    // public async Task CreateWebsiteDocumentWithoutAgentsAndUpdateAssignmentsTest()
    // {
    //     using var scope = Application.Host.Services.CreateScope();
    //
    //     Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;
    //
    //     // /KnowledgeBases/CreateWebsiteDocument - Create without any agent assignments
    //     var createWebsiteDocumentInput =
    //         new CreateWebsiteDocument.CreateWebsiteDocumentInput(
    //             "https://test-example.com",
    //             "Test Example Website",
    //             [
    //                 "https://test-example.com/page1",
    //                 "https://test-example.com/page2"
    //             ],
    //             []); // No initial agent assignments
    //
    //     var createWebsiteDocumentScenarioResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(createWebsiteDocumentInput)
    //                 .ToUrl("/authorized/KnowledgeBases/CreateWebsiteDocument");
    //         });
    //
    //     var createWebsiteDocumentOutput =
    //         await createWebsiteDocumentScenarioResult.ReadAsJsonAsync<
    //             Output<CreateWebsiteDocument.CreateWebsiteDocumentOutput>>();
    //
    //     Assert.That(createWebsiteDocumentOutput, Is.Not.Null);
    //     Assert.That(createWebsiteDocumentOutput.HttpStatusCode, Is.EqualTo(200));
    //
    //     // Verify the document was created without agent assignments
    //     var getFileDocumentListInput =
    //         new GetFileDocumentList.GetFileDocumentListInput(
    //             null,
    //             null,
    //             null,
    //             null,
    //             null,
    //             null);
    //     var getFileDocumentListScenarioResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getFileDocumentListInput)
    //                 .ToUrl("/authorized/KnowledgeBases/GetFileDocumentList");
    //         });
    //
    //     var getFileDocumentListOutput = await getFileDocumentListScenarioResult.ReadAsTextAsync();
    //     var jObject = JObject.Parse(getFileDocumentListOutput);
    //
    //     Assert.That(jObject["data"], Is.Not.Null);
    //     Assert.That((int) jObject["http_status_code"], Is.EqualTo(200));
    //
    //     var getFileDocumentListData = jObject["data"];
    //     Assert.That(getFileDocumentListData["kb_documents"], Is.Not.Empty);
    //
    //     // Find the created website document
    //     var createdWebsiteDocument = getFileDocumentListData["kb_documents"]
    //         .FirstOrDefault(doc => (string) doc["id"] == createWebsiteDocumentOutput.Data.Id);
    //
    //     Assert.That(
    //         createdWebsiteDocument,
    //         Is.Not.Null,
    //         "Created website document should be found in the document list");
    //     Assert.That((string) createdWebsiteDocument["base_url_title"], Is.EqualTo("Test Example Website"));
    //     Assert.That((string) createdWebsiteDocument["base_url"], Is.EqualTo("https://test-example.com"));
    //     Assert.That(createdWebsiteDocument["agent_assignments"], Is.Not.Null);
    //
    //     // Verify no initial agent assignments
    //     var initialAgentAssignments = createdWebsiteDocument["agent_assignments"].ToArray();
    //     Assert.That(initialAgentAssignments.Length, Is.EqualTo(0), "Should have no initial agent assignments");
    //
    //     // Get the document ID for updating assignments
    //     var documentId = (string) createdWebsiteDocument["id"];
    //     Assert.That(documentId, Is.Not.Null, "Document ID should not be null");
    //
    //     // /KnowledgeBases/UpdateDocumentAgentAssignmentsForDocument - Update agent assignments
    //     var updateAssignmentInput =
    //         new UpdateDocumentAgentAssignmentsForDocument.UpdateDocumentAgentAssignmentsForDocumentInput(
    //             documentId,
    //             [
    //                 "XbbTR1lopPvx3GRP",
    //                 "Smart Reply"
    //             ]);
    //     var updateAssignmentResults = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(updateAssignmentInput)
    //                 .ToUrl("/authorized/KnowledgeBases/UpdateDocumentAgentAssignmentsForDocument");
    //         });
    //
    //     var updateAssignmentOutput =
    //         await updateAssignmentResults.ReadAsJsonAsync<
    //             Output<UpdateDocumentAgentAssignmentsForDocument.UpdateDocumentAgentAssignmentsForDocumentOutput>>();
    //
    //     Assert.That(updateAssignmentOutput, Is.Not.Null);
    //     Assert.That(updateAssignmentOutput.HttpStatusCode, Is.EqualTo(200));
    //
    //     // Verify the agent assignments were updated
    //     var getUpdatedDocumentListScenarioResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getFileDocumentListInput)
    //                 .ToUrl("/authorized/KnowledgeBases/GetFileDocumentList");
    //         });
    //
    //     var getUpdatedDocumentListOutput = await getUpdatedDocumentListScenarioResult.ReadAsTextAsync();
    //     var updatedJObject = JObject.Parse(getUpdatedDocumentListOutput);
    //
    //     Assert.That(updatedJObject["data"], Is.Not.Null);
    //     Assert.That((int) updatedJObject["http_status_code"], Is.EqualTo(200));
    //
    //     var updatedDocumentListData = updatedJObject["data"];
    //     var updatedWebsiteDocument = updatedDocumentListData["kb_documents"]
    //         .FirstOrDefault(doc => (string) doc["id"] == createWebsiteDocumentOutput.Data.Id);
    //
    //     Assert.That(
    //         updatedWebsiteDocument,
    //         Is.Not.Null,
    //         "Updated website document should be found in the document list");
    //     Assert.That(updatedWebsiteDocument["agent_assignments"], Is.Not.Null);
    //
    //     // Verify agent assignments contain the expected agents after update
    //     var updatedAgentAssignments = updatedWebsiteDocument["agent_assignments"].ToArray();
    //     Assert.That(updatedAgentAssignments.Length, Is.EqualTo(2), "Should have 2 agent assignments after update");
    //     Assert.That(
    //         updatedAgentAssignments.Any(
    //             assignment => (string) assignment[AgentAssignment.PropertyNameAgentId] == "XbbTR1lopPvx3GRP"),
    //         Is.True,
    //         "Should contain agent XbbTR1lopPvx3GRP");
    //     Assert.That(
    //         updatedAgentAssignments.Any(
    //             assignment => (string) assignment[AgentAssignment.PropertyNameAgentId] == "Smart Reply"),
    //         Is.True,
    //         "Should contain Smart Reply agent");
    // }

    [Test]
    public async Task StartWebCrawlingSessionTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /KnowledgeBases/StartWebCrawlingSession
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", true);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput, Is.Not.Null);
        Assert.That(startWebCrawlingSessionOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Null);
        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Empty);
    }

    [Test]
    public async Task GetWebCrawlingSessionStatusTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // First start a web crawling session
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", true);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Null);

        // /KnowledgeBases/GetWebCrawlingSessionStatus
        var getWebCrawlingSessionStatusInput =
            new GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusInput(
                startWebCrawlingSessionOutput.Data.WebCrawlingSessionId);
        var getWebCrawlingSessionStatusScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getWebCrawlingSessionStatusInput)
                    .ToUrl("/authorized/KnowledgeBases/GetWebCrawlingSessionStatus");
            });

        var getWebCrawlingSessionStatusOutput =
            await getWebCrawlingSessionStatusScenarioResult.ReadAsJsonAsync<
                Output<GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusOutput>>();

        Assert.That(getWebCrawlingSessionStatusOutput, Is.Not.Null);
        Assert.That(getWebCrawlingSessionStatusOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWebCrawlingSessionStatusOutput.Data.Status, Is.Not.Null);
        Assert.That(getWebCrawlingSessionStatusOutput.Data.BaseUrl, Is.EqualTo("https://example.com"));
        Assert.That(getWebCrawlingSessionStatusOutput.Data.CrawlingResults, Is.Not.Null);
    }

    [Test]
    public async Task PauseWebCrawlingSessionTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // First start a web crawling session
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", true);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput?.Data?.WebCrawlingSessionId, Is.Not.Null);

        // /KnowledgeBases/PauseWebCrawlingSession
        var pauseWebCrawlingSessionInput =
            new PauseWebCrawlingSession.PauseWebCrawlingSessionInput(
                startWebCrawlingSessionOutput.Data.WebCrawlingSessionId);
        var pauseWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(pauseWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/PauseWebCrawlingSession");
            });

        var pauseOutput =
            await pauseWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<PauseWebCrawlingSession.PauseWebCrawlingSessionOutput>>();

        Assert.That(pauseOutput, Is.Not.Null);
        Assert.That(pauseOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(pauseOutput.Data.Status, Is.EqualTo(WebCrawlingSessionStatuses.Paused));
        Assert.That(pauseOutput.Data.BaseUrl, Is.EqualTo("https://example.com"));
        Assert.That(pauseOutput.Data.CrawlingResults, Is.Not.Null);
    }

    [Test]
    public async Task ResumeWebCrawlingSessionTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // First start a web crawling session
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", true);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Null);

        // Pause the session first
        var pauseWebCrawlingSessionInput =
            new PauseWebCrawlingSession.PauseWebCrawlingSessionInput(
                startWebCrawlingSessionOutput.Data.WebCrawlingSessionId);
        await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(pauseWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/PauseWebCrawlingSession");
            });

        // /KnowledgeBases/ResumeWebCrawlingSession
        var resumeWebCrawlingSessionInput =
            new ResumeWebCrawlingSession.ResumeWebCrawlingSessionInput(
                startWebCrawlingSessionOutput.Data.WebCrawlingSessionId);
        var resumeWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(resumeWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/ResumeWebCrawlingSession");
            });

        var resumeWebCrawlingSessionOutput =
            await resumeWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<ResumeWebCrawlingSession.ResumeWebCrawlingSessionOutput>>();

        Assert.That(resumeWebCrawlingSessionOutput, Is.Not.Null);
        Assert.That(resumeWebCrawlingSessionOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(resumeWebCrawlingSessionOutput.Data.Status, Is.EqualTo(WebCrawlingSessionStatuses.InProgress));
        Assert.That(resumeWebCrawlingSessionOutput.Data.BaseUrl, Is.EqualTo("https://example.com"));
        Assert.That(resumeWebCrawlingSessionOutput.Data.CrawlingResults, Is.Not.Null);
    }

    [Test]
    public async Task WebCrawlingSessionWorkflowTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // Step 1: Start a web crawling session
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", true);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Null);
        var sessionId = startWebCrawlingSessionOutput.Data.WebCrawlingSessionId;

        // Step 2: Get initial status
        var getStatusInput = new GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusInput(sessionId);
        var getStatusResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStatusInput)
                    .ToUrl("/authorized/KnowledgeBases/GetWebCrawlingSessionStatus");
            });

        var getStatusOutput =
            await getStatusResult
                .ReadAsJsonAsync<Output<GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusOutput>>();
        Assert.That(getStatusOutput.Data.Status, Is.EqualTo(WebCrawlingSessionStatuses.InProgress));

        // Step 3: Pause the session
        var pauseInput = new PauseWebCrawlingSession.PauseWebCrawlingSessionInput(sessionId);
        var pauseResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(pauseInput)
                    .ToUrl("/authorized/KnowledgeBases/PauseWebCrawlingSession");
            });

        var pauseOutput =
            await pauseResult.ReadAsJsonAsync<Output<PauseWebCrawlingSession.PauseWebCrawlingSessionOutput>>();
        Assert.That(pauseOutput.Data.Status, Is.EqualTo(WebCrawlingSessionStatuses.Paused));

        // Step 4: Resume the session
        var resumeInput = new ResumeWebCrawlingSession.ResumeWebCrawlingSessionInput(sessionId);
        var resumeResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(resumeInput)
                    .ToUrl("/authorized/KnowledgeBases/ResumeWebCrawlingSession");
            });

        var resumeOutput =
            await resumeResult.ReadAsJsonAsync<Output<ResumeWebCrawlingSession.ResumeWebCrawlingSessionOutput>>();
        Assert.That(resumeOutput, Is.Not.Null);
        Assert.That(resumeOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(resumeOutput.Data.Status, Is.EqualTo(WebCrawlingSessionStatuses.InProgress));
        Assert.That(resumeOutput.Data.BaseUrl, Is.EqualTo("https://example.com"));
    }

    [Test]
    public async Task StartWebCrawlingSessionWithCrawlingDisabledTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // /KnowledgeBases/StartWebCrawlingSession with isCrawlingEnabled = false
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", false);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput, Is.Not.Null);
        Assert.That(startWebCrawlingSessionOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Null);
        Assert.That(startWebCrawlingSessionOutput.Data.WebCrawlingSessionId, Is.Not.Empty);
    }

    [Test]
    public async Task GetWebCrawlingSessionStatusWithCrawlingDisabledTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // First start a web crawling session with crawling disabled
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", false);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput!.Data.WebCrawlingSessionId, Is.Not.Null);

        // /KnowledgeBases/GetWebCrawlingSessionStatus
        var getWebCrawlingSessionStatusInput =
            new GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusInput(
                startWebCrawlingSessionOutput.Data.WebCrawlingSessionId);
        var getWebCrawlingSessionStatusScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getWebCrawlingSessionStatusInput)
                    .ToUrl("/authorized/KnowledgeBases/GetWebCrawlingSessionStatus");
            });

        var getWebCrawlingSessionStatusOutput =
            await getWebCrawlingSessionStatusScenarioResult.ReadAsJsonAsync<
                Output<GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusOutput>>();

        Assert.That(getWebCrawlingSessionStatusOutput, Is.Not.Null);
        Assert.That(getWebCrawlingSessionStatusOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWebCrawlingSessionStatusOutput.Data.Status, Is.Not.Null);
        Assert.That(getWebCrawlingSessionStatusOutput.Data.BaseUrl, Is.EqualTo("https://example.com"));
        Assert.That(getWebCrawlingSessionStatusOutput.Data.CrawlingResults, Is.Not.Null);

        // When crawling is disabled, we expect fewer or just the single URL in crawling results
        // The exact behavior depends on implementation, but the session should still be valid
    }

    [Test]
    public async Task PauseWebCrawlingSessionWithCrawlingDisabledTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // First start a web crawling session with crawling disabled
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", false);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput?.Data?.WebCrawlingSessionId, Is.Not.Null);

        // /KnowledgeBases/PauseWebCrawlingSession
        var pauseWebCrawlingSessionInput =
            new PauseWebCrawlingSession.PauseWebCrawlingSessionInput(
                startWebCrawlingSessionOutput!.Data.WebCrawlingSessionId);
        var pauseWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(pauseWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/PauseWebCrawlingSession");
            });

        var pauseOutput =
            await pauseWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<PauseWebCrawlingSession.PauseWebCrawlingSessionOutput>>();

        Assert.That(pauseOutput, Is.Not.Null);
        Assert.That(pauseOutput!.HttpStatusCode, Is.EqualTo(500));
    }

    [Test]
    public async Task ResumeWebCrawlingSessionWithCrawlingDisabledTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = MockCompanyId;

        // First start a web crawling session with crawling disabled
        var startWebCrawlingSessionInput =
            new StartWebCrawlingSession.StartWebCrawlingSessionInput("https://example.com", false);
        var startWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(startWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/StartWebCrawlingSession");
            });

        var startWebCrawlingSessionOutput =
            await startWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<StartWebCrawlingSession.StartWebCrawlingSessionOutput>>();

        Assert.That(startWebCrawlingSessionOutput!.Data.WebCrawlingSessionId, Is.Not.Null);

        // /KnowledgeBases/ResumeWebCrawlingSession
        var resumeWebCrawlingSessionInput =
            new ResumeWebCrawlingSession.ResumeWebCrawlingSessionInput(
                startWebCrawlingSessionOutput.Data.WebCrawlingSessionId);
        var resumeWebCrawlingSessionScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(resumeWebCrawlingSessionInput)
                    .ToUrl("/authorized/KnowledgeBases/ResumeWebCrawlingSession");
            });

        var resumeWebCrawlingSessionOutput =
            await resumeWebCrawlingSessionScenarioResult.ReadAsJsonAsync<
                Output<ResumeWebCrawlingSession.ResumeWebCrawlingSessionOutput>>();

        Assert.That(resumeWebCrawlingSessionOutput, Is.Not.Null);
        Assert.That(resumeWebCrawlingSessionOutput!.HttpStatusCode, Is.EqualTo(500));
    }
}