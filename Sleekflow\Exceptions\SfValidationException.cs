﻿using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Exceptions;

public class SfValidationException : ErrorCodeException
{
    public List<ValidationResult> ValidationResults { get; }

    public SfValidationException(List<ValidationResult> validationResults)
        : base(
            ErrorCodeConstant.SfValidationException)
    {
        ValidationResults = validationResults;
    }
}