using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Stores;

public class StoreSubscriptionStatus
{
    [JsonProperty("payment_status")]
    public string PaymentStatus { get; set; }

    [JsonProperty("sleekflow_bill_record_id")]
    public string? SleekflowBillRecordId { get; set; }

    [JsonConstructor]
    public StoreSubscriptionStatus(
        string paymentStatus,
        string? sleekflowBillRecordId)
    {
        PaymentStatus = paymentStatus;
        SleekflowBillRecordId = sleekflowBillRecordId;
    }
}