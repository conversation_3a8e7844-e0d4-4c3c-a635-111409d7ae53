﻿using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.SupportHub;

public class SupportHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;

    public SupportHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
    }

    public class SupportHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public SupportHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public SupportHubDbOutput InitSupportHubDb()
    {
        const string cosmosDbId = "supporthubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var containerParams = new ContainerParam[]
        {
            // TODO: DB container structure parameters
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new SupportHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}