namespace Sleekflow.Models.Subscriptions;

public class GetCompanyActiveFeaturesRequest
{
    public string SleekflowCompanyId { get; set; }

    public List<string> FeatureNames { get; set; }

    public DateTimeOffset RequestedAt { get; set; }

    public GetCompanyActiveFeaturesRequest(string sleekflowCompanyId, List<string> featureNames)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FeatureNames = featureNames;
        RequestedAt = DateTimeOffset.UtcNow;
    }
}