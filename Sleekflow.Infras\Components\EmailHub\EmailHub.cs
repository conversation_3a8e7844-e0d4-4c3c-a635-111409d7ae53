using Pulumi;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Storage;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.EmailHub;

public class EmailHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly EmailHubDb.EmailHubDbOutput _emailHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;

    public EmailHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        EmailHubDb.EmailHubDbOutput emailHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _emailHubDbOutput = emailHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
    }

    public List<App.ContainerApp> InitEmailHub()
    {
        var imageRandomId = new Random.RandomId(
            "sleekflow-emailh-image-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "email hub image storage"
                    }
                },
            });
        var imageStorageAccount = new Storage.StorageAccount(
            "sleekflow-emailh-image-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-email-hub-image-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = imageRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var imageStorageAccountBlobServiceProperties = new Storage.BlobServiceProperties(
            "sleekflow-emailh-image-storage-account-table-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = imageStorageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*",
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.GET),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.HEAD),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.POST),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.OPTIONS),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.PUT)
                            },
                            AllowedOrigins = new[]
                            {
                                "*",
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                    },
                },
                BlobServicesName = "default",
            });
        var _ = new Storage.BlobContainer(
            "sleekflow-emailh-image-container",
            new Storage.BlobContainerArgs
            {
                AccountName = imageStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "image-container"
            },
            new CustomResourceOptions
            {
                Parent = imageStorageAccount
            });
        var fileRandomId = new Random.RandomId(
            "sleekflow-emailh-file-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "email hub file storage"
                    }
                },
            });

        var fileStorageAccount = new Storage.StorageAccount(
            "sleekflow-emailh-file-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-email-hub-file-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = fileRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var fileStorageAccountBlobServiceProperties = new Storage.BlobServiceProperties(
            "sleekflow-emailh-file-storage-account-table-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = fileStorageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*"
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.GET),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.HEAD),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.POST),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.OPTIONS),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.PUT)
                            },
                            AllowedOrigins = new[]
                            {
                                "*",
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                    },
                },
                BlobServicesName = "default",
            });

        var __ = new Storage.BlobContainer(
            "sleekflow-emailh-file-container",
            new Storage.BlobContainerArgs
            {
                AccountName = fileStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "file-container"
            },
            new CustomResourceOptions
            {
                Parent = fileStorageAccount
            });

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.EmailHub),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.EmailHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var worker = managedEnvAndAppsTuple.GetWorkerApp(ServiceNames.EmailHub);
            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.EmailHub));

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var listWorkerHostKeysResult = Output
                .Tuple(worker!.Name, _resourceGroup.Name, worker.Id)
                .Apply(
                    items => Web.ListWebAppHostKeys.Invoke(
                        new Web.ListWebAppHostKeysInvokeArgs
                        {
                            Name = items.Item1, ResourceGroupName = items.Item2,
                        }));


            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));
            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = 1,
                            MaxReplicas = 20,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "50"
                                            }
                                        }
                                    }
                                }
                            },
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-eh-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 0.5, Memory = "1Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>
                                {
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },

                                        #region AuditHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_EMAIL_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_emailHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_EMAIL_HUB_DB_KEY", Value = _emailHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_EMAIL_HUB_DB_DATABASE_ID",
                                            Value = _emailHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.EmailHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "IMAGE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                imageStorageAccount.Name),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FILE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                fileStorageAccount.Name),
                                        },

                                        #endregion

                                        #region EmailHub

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GMAIL_CLIENT_ID",
                                            Value =
                                                "************-pc8q82kjfma91jgf9n4nl1rvbmmb44ef.apps.googleusercontent.com",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GMAIL_CLIENT_SECRET", Value = "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",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GMAIL_REDIRECT_URI",
                                            Value =
                                                "https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/email-hub/auth/callback",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GMAIL_PROJECT_ID", Value = "emailgetstarted",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GMAIL_TOPIC_ID", Value = "MyTopic",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GMAIL_SUBSCRIPTION_ID", Value = "MyPushSub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OUTLOOK_CLIENT_ID", Value = "DUMMY",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OUTLOOK_CLIENT_SECRET", Value = "DUMMY",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OUTLOOK_REDIRECT_URI", Value = "DUMMY",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OUTLOOK_TENANT", Value = "DUMMY",
                                        },

                                        #endregion

                                        #region WorkerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_HOSTNAME",
                                            Value = worker.DefaultHostName.Apply(hn => "https://" + hn),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_FUNCTIONS_KEY",
                                            Value = listWorkerHostKeysResult
                                                .Apply(l => l.FunctionKeys!["default"]),
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.EmailHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }
}
