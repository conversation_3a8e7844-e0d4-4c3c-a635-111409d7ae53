using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.TenantHub;

public class TenantHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public TenantHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class TenantHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public TenantHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public TenantHubDbOutput InitTenantHubDb()
    {
        const string cosmosDbId = "tenanthubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 10000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.TenantHubDb.ITenantHubDbService
        var containerParams = new ContainerParam[]
        {
            new (
                "company",
                "company",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "user",
                "user",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "feature",
                "feature",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "enabled_feature",
                "enabled_feature",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "role",
                "role",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "plan",
                "plan",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "plan_definition",
                "plan_definition",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "payment",
                "payment",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ip_whitelist_settings",
                "ip_whitelist_settings",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "experimental_feature",
                "experimental_feature",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new TenantHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}