using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Blobs;

public interface IBlobUploadHistoryRepository : IDynamicFiltersRepository<BlobUploadHistory>
{
}

public class BlobUploadHistoryRepository
    : DynamicFiltersBaseRepository<BlobUploadHistory>, IBlobUploadHistoryRepository, IScopedService
{
    public BlobUploadHistoryRepository(
        ILogger<BlobUploadHistoryRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}