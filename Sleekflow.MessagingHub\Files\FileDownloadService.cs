using System.Net.Http.Headers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Files;

public interface IFileDownloadService
{
    Task<DownloadFileFromLinkOutput> DownloadFileFromUrlAsync(string url);

    public record struct DownloadFileFromLinkOutput(string? FileName, string ContentType, byte[] FileContent);
}

public class FileDownloadService : IFileDownloadService, ISingletonService
{
    public async Task<IFileDownloadService.DownloadFileFromLinkOutput> DownloadFileFromUrlAsync(string url)
    {
        using var httpClient = new HttpClient();

        var response = await httpClient.GetAsync(url);

        if (response.IsSuccessStatusCode)
        {
            // Get the content type and file name from the response headers
            var contentType = response.Content.Headers.ContentType?.ToString() ??
                              throw new SfInternalErrorException("ContentType is null");

            var fileName = GetFileNameFromContentDisposition(response.Content.Headers.ContentDisposition);

            var fileContent = await response.Content.ReadAsByteArrayAsync();

            return new IFileDownloadService.DownloadFileFromLinkOutput(fileName, contentType, fileContent);
        }

        throw new SfInternalErrorException($"Error downloading file: {response.StatusCode}");
    }

    private static string? GetFileNameFromContentDisposition(ContentDispositionHeaderValue? contentDisposition)
    {
        if (contentDisposition != null && !string.IsNullOrEmpty(contentDisposition.FileName))
        {
            return contentDisposition.FileName.Trim('"');
        }

        return null;
    }
}