﻿using Microsoft.Extensions.Logging;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Webhooks;

public interface IWebhookEventRepository : IRepository<WebhookEvent>
{
}

public class WebhookEventRepository : BaseRepository<WebhookEvent>, IWebhookEventRepository
{
    public WebhookEventRepository(
        ILogger<BaseRepository<WebhookEvent>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}