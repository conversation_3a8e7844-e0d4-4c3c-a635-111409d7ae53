﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class DeleteSchema : ITrigger<DeleteSchema.DeleteSchemaInput, DeleteSchema.DeleteSchemaOutput>
{
    private readonly ISchemaService _schemaService;

    public DeleteSchema(
        ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public class DeleteSchemaInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [JsonConstructor]
        public DeleteSchemaInput(
            string sleekflowCompanyId,
            string id)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Id = id;
        }
    }

    public class DeleteSchemaOutput
    {
    }

    public async Task<DeleteSchemaOutput> F(DeleteSchemaInput deleteSchemaInput)
    {
        await _schemaService.DeleteAsync(deleteSchemaInput.Id, deleteSchemaInput.SleekflowCompanyId);

        return new DeleteSchemaOutput();
    }
}