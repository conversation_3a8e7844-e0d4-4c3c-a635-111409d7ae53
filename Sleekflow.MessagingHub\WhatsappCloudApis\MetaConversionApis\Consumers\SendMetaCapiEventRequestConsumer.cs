using MassTransit;
using Newtonsoft.Json;
using GraphApi.Client.Payloads.Models.ConversionApi;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.MetaConversionApis.Consumers;

public class SendMetaCapiEventRequestConsumerDefinition
    : ConsumerDefinition<SendMetaCapiEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SendMetaCapiEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SendMetaCapiEventRequestConsumer : IConsumer<SendMetaCapiEventRequest>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<SendMetaCapiEventRequestConsumer> _logger;
    private readonly IWabaAssetsManager _wabaAssetsManager;
    private readonly IMetaConversionApiService _metaConversionApiService;

    public SendMetaCapiEventRequestConsumer(
        IWabaService wabaService,
        ILogger<SendMetaCapiEventRequestConsumer> logger,
        IWabaAssetsManager wabaAssetsManager,
        IMetaConversionApiService metaConversionApiService)
    {
        _wabaService = wabaService;
        _logger = logger;
        _wabaAssetsManager = wabaAssetsManager;
        _metaConversionApiService = metaConversionApiService;
    }

    public async Task Consume(ConsumeContext<SendMetaCapiEventRequest> context)
    {
        try
        {
            var message = context.Message;
            var facebookWabaId = message.WhatsappBusinessAccountId;
            var sendConversionApiEvent = message.ConversionApiEvent;

            var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);
            var wabaDataset = _wabaAssetsManager.GetExistingWabaDataset(waba);

            if (wabaDataset is null)
            {
                _logger.LogError(
                    "Waba {FacebookWabaId} dataset not found",
                    facebookWabaId);

                await context.RespondAsync(new SendMetaCapiEventReply(
                    0,
                    new List<string> { $"Waba {facebookWabaId} dataset not found" },
                    null));

                return;
            }

            var (_, tokenDto) = _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

            var conversionApiEvent = new SendConversionApiEventObject
            {
                PartnerAgent = sendConversionApiEvent.PartnerAgent,
                Data = sendConversionApiEvent.Data.Select(d => new ConversionApiEventData
                {
                    EventName = d.EventName,
                    EventTime = long.Parse(d.EventTime),
                    ActionSource = d.ActionSource,
                    MessagingChannel = d.MessagingChannel,
                    UserData = new UserData
                    {
                        WhatsappBusinessAccountId = d.UserData.WhatsappBusinessAccountId,
                        CtwaClid = d.UserData.CtwaClid
                    },
                    CustomData = new CustomData
                    {
                        Currency = d.CustomData.Currency,
                        Value = int.Parse(d.CustomData.Value)
                    }
                }).ToList()
            };

            var sendConversionApiEventResponse = await _metaConversionApiService.SendConversionApiEventAsync(
                wabaDataset.FacebookDatasetId,
                conversionApiEvent,
                tokenDto?.DecryptedToken);

            _logger.LogInformation(
                "Conversion API signal event sent for Waba {FacebookWabaId} {FacebookDatasetId} with {RequestPayload} and {Response}",
                facebookWabaId,
                wabaDataset.FacebookDatasetId,
                JsonConvert.SerializeObject(sendConversionApiEvent),
                JsonConvert.SerializeObject(sendConversionApiEventResponse));

            var reply = new SendMetaCapiEventReply(
                sendConversionApiEventResponse.EventsReceived,
                sendConversionApiEventResponse.Messages?.ToList() ?? new List<string>(),
                sendConversionApiEventResponse.FbtraceId);

            await context.RespondAsync(reply);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[MessagingHub] Error when processing Conversion API signal event");

            await context.RespondAsync(new SendMetaCapiEventReply(
                0,
                new List<string> { $"Error processing Conversion API signal event: {ex.Message}" },
                null));

            throw;
        }
    }
}