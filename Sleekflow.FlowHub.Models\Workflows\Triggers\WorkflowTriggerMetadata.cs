using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows.Triggers;

public class TriggerMetadata
{
    [JsonProperty("event")]
    public EventMetadata Event { get; set; }

    [JsonProperty("setup")]
    public List<SetupItem> Setup { get; set; }

    [JsonProperty("filters")]
    public List<object> Filters { get; set; }

    [JsonProperty("isFilterByCondition")]
    public bool IsFilterByCondition { get; set; }

    [JsonConstructor]
    public TriggerMetadata(
        EventMetadata @event,
        List<SetupItem> setup,
        List<object> filters,
        bool isFilterByCondition)
    {
        Event = @event;
        Setup = setup;
        Filters = filters;
        IsFilterByCondition = isFilterByCondition;
    }
}

public class EventMetadata
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("trigger_name")]
    public string TriggerName { get; set; }

    [JsonProperty("trigger_group")]
    public string TriggerGroup { get; set; }

    [Json<PERSON>roperty("trigger_description")]
    public string TriggerDescription { get; set; }

    [JsonProperty("is_external_integration")]
    public bool IsExternalIntegration { get; set; }

    [JsonProperty("condition_arg_name")]
    public string ConditionArgName { get; set; }

    [JsonConstructor]
    public EventMetadata(
        string id,
        string triggerName,
        string triggerGroup,
        string triggerDescription,
        bool isExternalIntegration,
        string conditionArgName)
    {
        Id = id;
        TriggerName = triggerName;
        TriggerGroup = triggerGroup;
        TriggerDescription = triggerDescription;
        IsExternalIntegration = isExternalIntegration;
        ConditionArgName = conditionArgName;
    }
}

public class SetupItem
{
    [JsonProperty("arg_name")]
    public string ArgName { get; set; }

    [JsonProperty("input_type")]
    public string InputType { get; set; }

    [JsonProperty("value")]
    public SetupValue Value { get; set; }

    [JsonConstructor]
    public SetupItem(
        string argName,
        string inputType,
        SetupValue value)
    {
        ArgName = argName;
        InputType = inputType;
        Value = value;
    }
}

public class SetupValue
{
    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [JsonProperty("contact_payload_key")]
    public string ContactPayloadKey { get; set; }

    [JsonProperty("new_customer_entrollment")]
    public bool? NewCustomerEntrollment { get; set; }

    [JsonConstructor]
    public SetupValue(
        string objectType,
        string contactPayloadKey,
        bool? newCustomerEntrollment = null)
    {
        ObjectType = objectType;
        ContactPayloadKey = contactPayloadKey;
        NewCustomerEntrollment = newCustomerEntrollment;
    }
}