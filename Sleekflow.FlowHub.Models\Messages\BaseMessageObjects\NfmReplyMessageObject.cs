using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class NfmReplyMessageObject : BaseMessageObject
{
    /// <summary>
    /// Always is "flow"
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// Always is "Sent"
    /// </summary>
    [JsonProperty("body")]
    public string Body { get; set; }

    /// <summary>
    /// Flow-specific data.
    /// The structure is either defined in flow JSON,
    /// or if flow is using an endpoint, controlled by endpoint
    /// </summary>
    [JsonProperty("response_json")]
    public string ResponseJson { get; set; }

    [JsonConstructor]
    public NfmReplyMessageObject(
        string name,
        string body,
        string responseJson)
    {
        Name = name;
        Body = body;
        ResponseJson = responseJson;
    }
}