using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Subscriptions;

public abstract class EmailSubscriptionMetadata
{
    [Required]
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [Required]
    [JsonProperty("provider_id")]
    public string ProviderId { get; set; }

    [JsonConstructor]
    protected EmailSubscriptionMetadata(
        string providerName,
        string providerId)
    {
        ProviderName = providerName;
        ProviderId = providerId;
    }
}