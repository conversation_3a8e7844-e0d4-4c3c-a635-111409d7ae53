﻿using Sleekflow.Events;

namespace Sleekflow.CrmHub.Models.Events;

public class OnObjectOperationEvent : IEvent
{
    public const string OperationUnassociateObject = "UnassociateObject";
    public const string OperationAssociateObject = "AssociateObject";
    public const string OperationCreateOrUpdateObject = "CreateOrUpdateObject";
    public const string OperationCreateObject = "CreateObject";
    public const string OperationUpdateObject = "UpdateObject";
    public const string OperationDeleteObject = "DeleteObject";

    /// <summary>
    /// The property stores the latest snapshot of an object.
    /// </summary>
    public Dictionary<string, object?> Object { get; set; }

    /// <summary>
    /// The property stores the operation triggering this event.
    /// </summary>
    public string Operation { get; set; }

    /// <summary>
    /// The property stores who publishes this event.
    /// </summary>
    public string ProviderName { get; set; }

    /// <summary>
    /// The property stores the event belongs to which sleekflow company.
    /// </summary>
    public string SleekflowCompanyId { get; set; }

    public string ProviderObjectId { get; set; }

    public string EntityTypeName { get; set; }

    public string? TargetObjectId { get; set; }

    public OnObjectOperationEvent(
        Dictionary<string, object?> @object,
        string operation,
        string providerName,
        string sleekflowCompanyId,
        string providerObjectId,
        string entityTypeName,
        string? targetObjectId)
    {
        Object = @object;
        Operation = operation;
        ProviderName = providerName;
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderObjectId = providerObjectId;
        EntityTypeName = entityTypeName;
        TargetObjectId = targetObjectId;
    }
}