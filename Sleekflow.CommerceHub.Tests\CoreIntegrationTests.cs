using Alba;
using Azure.Storage.Blobs.Specialized;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Triggers.Blobs;
using Sleekflow.CommerceHub.Triggers.Carts;
using Sleekflow.CommerceHub.Triggers.Categories;
using Sleekflow.CommerceHub.Triggers.Currencies;
using Sleekflow.CommerceHub.Triggers.Languages;
using Sleekflow.CommerceHub.Triggers.Products;
using Sleekflow.CommerceHub.Triggers.ProductVariants;
using Sleekflow.CommerceHub.Triggers.Stores;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;
using Sleekflow.Queries;

namespace Sleekflow.CommerceHub.Tests;

public class CoreIntegrationTests
{
    private CreateStore.CreateStoreOutput? _createStoreOutput;
    private CreateCategory.CreateCategoryOutput? _createCategoryOutput;
    private CreateProduct.CreateProductOutput? _createProductOutput;

    [SetUp]
    public async Task Setup()
    {
        var createStoreOutputOutput = await Mocks.CreateTestStoreAsync();
        var createCategoryOutputOutput = await Mocks.CreateTestCategoryAsync(createStoreOutputOutput!.Data);
        var createProductOutputOutput = await Mocks.CreateTestProductAsync(
            createStoreOutputOutput.Data,
            createCategoryOutputOutput!.Data);

        _createStoreOutput = createStoreOutputOutput.Data;
        _createCategoryOutput = createCategoryOutputOutput.Data;
        _createProductOutput = createProductOutputOutput.Data;
    }

    [TearDown]
    public async Task TearDown()
    {
        await Mocks.DeleteTestProductAsync(_createStoreOutput!, _createProductOutput!);
        await Mocks.DeleteTestCategoryAsync(_createStoreOutput!, _createCategoryOutput!);
        await Mocks.DeleteTestStoreAsync(_createStoreOutput!);
    }

    [Test]
    public async Task HealthzTest()
    {
        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/Public/healthz");
                _.ContentShouldBe("HEALTH");
                _.StatusCodeShouldBeOk();
            });

        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/healthz/liveness");
                _.StatusCodeShouldBeOk();
            });

        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/healthz/readiness");
                _.StatusCodeShouldBeOk();
            });

        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/healthz/startup");
                _.StatusCodeShouldBeOk();
            });
    }

    [Test]
    public async Task CartsTest()
    {
        // /Carts/GetCart
        var getCartInput = new GetCart.GetCartInput(
            Mocks.SleekflowCompanyId,
            Mocks.SleekflowUserProfileId,
            _createStoreOutput!.Store.Id);
        var getCartScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCartInput).ToUrl("/Carts/GetCart");
            });
        var getCartOutputOutput =
            await getCartScenarioResult.ReadAsJsonAsync<Output<GetCart.GetCartOutput>>();

        Assert.That(getCartOutputOutput, Is.Not.Null);
        Assert.That(getCartOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createdCartId = getCartOutputOutput.Data.Cart.Id;

        // /Carts/UpdateCartItem
        var updateCartItemInput = new UpdateCartItem.UpdateCartItemInput(
            Mocks.SleekflowCompanyId,
            Mocks.SleekflowUserProfileId,
            _createStoreOutput!.Store.Id,
            _createProductOutput!.Product.ProductVariants[0].Id,
            _createProductOutput.Product.Id,
            111);
        var updateCartItemScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateCartItemInput).ToUrl("/Carts/UpdateCartItem");
            });
        var updateCartItemOutputOutput =
            await updateCartItemScenarioResult.ReadAsJsonAsync<Output<UpdateCartItem.UpdateCartItemOutput>>();

        Assert.That(updateCartItemOutputOutput, Is.Not.Null);
        Assert.That(updateCartItemOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Carts/UpdateCart
        var updateCartInput = new UpdateCart.UpdateCartInput(
            Mocks.SleekflowCompanyId,
            Mocks.SleekflowUserProfileId,
            _createStoreOutput!.Store.Id,
            new List<CartLineItem>
            {
                new CartLineItem(
                    _createProductOutput!.Product.ProductVariants[0].Id,
                    _createProductOutput.Product.Id,
                    null,
                    222,
                    null,
                    new Dictionary<string, object?>())
            },
            new Discount(
                null,
                null,
                10m,
                DiscountTypes.AbsoluteOff,
                new Dictionary<string, object?>()));
        var updateCartScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateCartInput).ToUrl("/Carts/UpdateCart");
            });
        var updateCartOutputOutput =
            await updateCartScenarioResult.ReadAsJsonAsync<Output<UpdateCart.UpdateCartOutput>>();

        Assert.That(updateCartOutputOutput, Is.Not.Null);
        Assert.That(updateCartOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Carts/GetUserCarts
        var getUserCartsInput = new GetUserCarts.GetUserCartsInput(
            Mocks.SleekflowCompanyId,
            Mocks.SleekflowUserProfileId,
            CartStatuses.Active);
        var getUserCartsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getUserCartsInput).ToUrl("/Carts/GetUserCarts");
            });
        var getUserCartsOutputOutput =
            await getUserCartsScenarioResult.ReadAsJsonAsync<Output<GetUserCarts.GetUserCartsOutput>>();

        Assert.That(getUserCartsOutputOutput, Is.Not.Null);
        Assert.That(getUserCartsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getUserCartsOutputOutput.Data, Is.Not.Null);
        Assert.That(
            getUserCartsOutputOutput.Data.Carts.Select(s => s.Id).ToList(),
            Does.Contain(createdCartId));

        // /Carts/GetCalculatedCart
        var getCalculatedCartInput = new GetCalculatedCart.GetCalculatedCartInput(
            Mocks.SleekflowCompanyId,
            Mocks.SleekflowUserProfileId,
            _createStoreOutput!.Store.Id,
            "HKD",
            "en");
        var getCalculatedCartScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCalculatedCartInput).ToUrl("/Carts/GetCalculatedCart");
            });
        var getCalculatedCartOutputOutput =
            await getCalculatedCartScenarioResult
                .ReadAsJsonAsync<Output<GetCalculatedCart.GetCalculatedCartOutput>>();

        Assert.That(getCalculatedCartOutputOutput, Is.Not.Null);
        Assert.That(getCalculatedCartOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Carts/ClearCart
        var clearCartInput = new ClearCart.ClearCartInput(
            Mocks.SleekflowCompanyId,
            Mocks.SleekflowUserProfileId,
            _createStoreOutput.Store.Id,
            Mocks.SleekflowStaffId,
            null);
        var clearCartScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(clearCartInput).ToUrl("/Carts/ClearCart");
            });
        var clearCartOutputOutput =
            await clearCartScenarioResult.ReadAsJsonAsync<Output<ClearCart.ClearCartOutput>>();

        Assert.That(clearCartOutputOutput, Is.Not.Null);
        Assert.That(clearCartOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task CategoriesTest()
    {
        // /Categories/CreateCategory
        var createCategoryInput = new CreateCategory.CreateCategoryInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<Multilingual>
            {
                new ("en", nameof(CategoriesTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test category."),
                    null,
                    null)
            },
            null,
            new Dictionary<string, object?>(),
            Mocks.SleekflowStaffId,
            null);
        var createCategoryScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createCategoryInput).ToUrl("/Categories/CreateCategory");
            });
        var createCategoryOutputOutput =
            await createCategoryScenarioResult.ReadAsJsonAsync<Output<CreateCategory.CreateCategoryOutput>>();

        Assert.That(createCategoryOutputOutput, Is.Not.Null);
        Assert.That(createCategoryOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createdCategoryId = createCategoryOutputOutput.Data.Category.Id;

        // /Categories/GetCategories
        var getCategoriesInput = new GetCategories.GetCategoriesInput(
            null,
            Mocks.SleekflowCompanyId,
            _createStoreOutput.Store.Id,
            200);
        var getCategoriesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCategoriesInput).ToUrl("/Categories/GetCategories");
            });
        var getCategoriesOutputOutput =
            await getCategoriesScenarioResult.ReadAsJsonAsync<Output<GetCategories.GetCategoriesOutput>>();

        Assert.That(getCategoriesOutputOutput, Is.Not.Null);
        Assert.That(getCategoriesOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCategoriesOutputOutput.Data, Is.Not.Null);
        Assert.That(
            getCategoriesOutputOutput.Data.Categories.Select(s => s.Id).ToList(),
            Does.Contain(createdCategoryId));

        // /Categories/DeleteCategory
        var deleteCategoryInput = new DeleteCategory.DeleteCategoryInput(
            createCategoryOutputOutput.Data.Category.Id,
            Mocks.SleekflowCompanyId,
            _createStoreOutput.Store.Id,
            Mocks.SleekflowStaffId,
            null);
        var deleteCategoryScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteCategoryInput).ToUrl("/Categories/DeleteCategory");
            });
        var deleteCategoryOutputOutput =
            await deleteCategoryScenarioResult.ReadAsJsonAsync<Output<DeleteCategory.DeleteCategoryOutput>>();

        Assert.That(deleteCategoryOutputOutput, Is.Not.Null);
        Assert.That(deleteCategoryOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Stores/GetCategories after delete
        var getCategoriesInput2 = new GetCategories.GetCategoriesInput(
            null,
            Mocks.SleekflowCompanyId,
            _createStoreOutput.Store.Id,
            200);
        var getCategoriesScenarioResult2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCategoriesInput2).ToUrl("/Categories/GetCategories");
            });
        var getCategoriesOutputOutput2 =
            await getCategoriesScenarioResult2.ReadAsJsonAsync<Output<GetCategories.GetCategoriesOutput>>();

        Assert.That(getCategoriesOutputOutput2, Is.Not.Null);
        Assert.That(getCategoriesOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCategoriesOutputOutput2.Data, Is.Not.Null);
        Assert.That(
            getCategoriesOutputOutput2.Data.Categories.Select(s => s.Id).ToList(),
            Does.Not.Contain(createdCategoryId));
    }

    [Test]
    public async Task CurrenciesTest()
    {
        // /Currencies/GetCurrencies
        var getCurrenciesInput = new GetCurrencies.GetCurrenciesInput();
        var getCurrenciesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCurrenciesInput).ToUrl("/Currencies/GetCurrencies");
            });
        var getCurrenciesOutputOutput =
            await getCurrenciesScenarioResult
                .ReadAsJsonAsync<Output<GetCurrencies.GetCurrenciesOutput>>();

        var gbpCurrency = getCurrenciesOutputOutput!.Data.Currencies.Find(l => l.CurrencyIsoCode == "GBP");
        var hkdCurrency = getCurrenciesOutputOutput.Data.Currencies.Find(l => l.CurrencyIsoCode == "HKD");

        Assert.That(gbpCurrency, Is.Not.Null);
        Assert.That(hkdCurrency, Is.Not.Null);

        // /Currencies/GetSupportedCurrencies
        var getSupportedCurrenciesInput = new GetSupportedCurrencies.GetSupportedCurrenciesInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id);
        var getSupportedCurrenciesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSupportedCurrenciesInput).ToUrl("/Currencies/GetSupportedCurrencies");
            });
        var getSupportedCurrenciesOutputOutput =
            await getSupportedCurrenciesScenarioResult
                .ReadAsJsonAsync<Output<GetSupportedCurrencies.GetSupportedCurrenciesOutput>>();

        gbpCurrency =
            getSupportedCurrenciesOutputOutput!.Data.Currencies.Find(l => l.CurrencyIsoCode == "GBP");
        hkdCurrency =
            getSupportedCurrenciesOutputOutput.Data.Currencies.Find(l => l.CurrencyIsoCode == "HKD");

        Assert.That(gbpCurrency, Is.Not.Null);
        Assert.That(hkdCurrency, Is.Not.Null);
    }

    [Test]
    public async Task LanguagesTest()
    {
        // /Languages/GetLanguages
        var getLanguagesInput = new GetLanguages.GetLanguagesInput();
        var getLanguagesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getLanguagesInput).ToUrl("/Languages/GetLanguages");
            });
        var getLanguagesOutputOutput =
            await getLanguagesScenarioResult.ReadAsJsonAsync<Output<GetLanguages.GetLanguagesOutput>>();

        var enLanguage = getLanguagesOutputOutput!.Data.Languages.Find(l => l.LanguageIsoCode == "en");
        var zhLanguage = getLanguagesOutputOutput.Data.Languages.Find(l => l.LanguageIsoCode == "zh");

        Assert.That(enLanguage, Is.Not.Null);
        Assert.That(zhLanguage, Is.Not.Null);
    }

    [Test]
    public async Task ProductsTest()
    {
        // /Products/CreateProduct
        var createProductInput = new CreateProduct.CreateProductInput(
            new List<string>
            {
                _createCategoryOutput!.Category.Id,
            },
            null,
            null,
            new List<Multilingual>
            {
                new ("en", nameof(ProductsTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test product."),
                    null,
                    null)
            },
            new List<ImageDto>(),
            new Dictionary<string, object?>(),
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<CreateProduct.CreateProductInputProductVariant>
            {
                new CreateProduct.CreateProductInputProductVariant(
                    null,
                    null,
                    new List<Price>()
                    {
                        new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
                    },
                    0,
                    new List<ProductVariant.ProductVariantAttribute>
                    {
                        new ProductVariant.ProductVariantAttribute("color", "red")
                    },
                    new List<Multilingual>
                    {
                        new Multilingual("en", nameof(ProductsTest) + " - Red")
                    },
                    new List<Description>
                    {
                        new (
                            DescriptionTypes.Text,
                            new Multilingual("en", "This is a test product - red version."),
                            null,
                            null)
                    },
                    new List<ImageDto>(),
                    null),
            },
            null,
            false,
            Mocks.SleekflowStaffId,
            null);
        var createProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createProductInput).ToUrl("/Products/CreateProduct");
            });
        var createProductOutputOutput =
            await createProductScenarioResult.ReadAsJsonAsync<Output<CreateProduct.CreateProductOutput>>();

        Assert.That(createProductOutputOutput, Is.Not.Null);
        Assert.That(createProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/GetProduct
        var getProductInput = new GetProduct.GetProductInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            createProductOutputOutput.Data.Product.Id);
        var getProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getProductInput).ToUrl("/Products/GetProduct");
            });
        var getProductOutputOutput =
            await getProductScenarioResult.ReadAsJsonAsync<Output<GetProduct.GetProductOutput>>();

        Assert.That(getProductOutputOutput, Is.Not.Null);
        Assert.That(getProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/UpdateProduct
        var updateProductInput = new UpdateProduct.UpdateProductInput(
            new List<string>
            {
                _createCategoryOutput!.Category.Id,
            },
            null,
            null,
            new List<Multilingual>
            {
                new ("en", nameof(ProductsTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test product."),
                    null,
                    null)
            },
            new List<ImageDto>(),
            new Dictionary<string, object?>(),
            createProductOutputOutput.Data.Product.Id,
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            false,
            Mocks.SleekflowStaffId,
            null);
        var updateProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateProductInput).ToUrl("/Products/UpdateProduct");
            });
        var updateProductOutputOutput =
            await updateProductScenarioResult.ReadAsJsonAsync<Output<UpdateProduct.UpdateProductOutput>>();

        Assert.That(updateProductOutputOutput, Is.Not.Null);
        Assert.That(updateProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/GetProducts
        var getProductsInput = new GetProducts.GetProductsInput(
            null,
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            200,
            new List<GetProducts.GetProductsInputFilterGroup>(),
            new List<QueryBuilder.Sort>());
        var getProductsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getProductsInput).ToUrl("/Products/GetProducts");
            });
        var getProductsOutputOutput =
            await getProductsScenarioResult.ReadAsJsonAsync<Output<GetProducts.GetProductsOutput>>();

        Assert.That(getProductsOutputOutput, Is.Not.Null);
        Assert.That(getProductsOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/DuplicateProducts
        var duplicateProductsInput = new DuplicateProducts.DuplicateProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<string>
            {
                createProductOutputOutput.Data.Product.Id, createProductOutputOutput.Data.Product.Id,
            },
            Mocks.SleekflowStaffId,
            null);
        var duplicateProductsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(duplicateProductsInput).ToUrl("/Products/DuplicateProducts");
            });
        var duplicateProductsOutputOutput =
            await duplicateProductsScenarioResult.ReadAsJsonAsync<Output<DuplicateProducts.DuplicateProductsOutput>>();

        Assert.That(duplicateProductsOutputOutput, Is.Not.Null);
        Assert.That(duplicateProductsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(duplicateProductsOutputOutput.Data.Products, Has.Count.EqualTo(1));
        duplicateProductsOutputOutput.Data.Products.ForEach(
            p =>
            {
                Assert.That(p.Id, Is.Not.Null);
                Assert.That(p.Id, Is.Not.EqualTo(createProductOutputOutput.Data.Product.Id));

                p.Names.ForEach(m => { StringAssert.StartsWith("DUPLICATE - ", m.Value); });
            });

        // /Products/DeleteProduct
        var deleteProductInput = new DeleteProduct.DeleteProductInput(
            createProductOutputOutput.Data.Product.Id,
            Mocks.SleekflowCompanyId,
            _createStoreOutput.Store.Id,
            Mocks.SleekflowStaffId,
            null);
        var deleteProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteProductInput).ToUrl("/Products/DeleteProduct");
            });
        var deleteProductOutputOutput =
            await deleteProductScenarioResult.ReadAsJsonAsync<Output<DeleteProduct.DeleteProductOutput>>();

        Assert.That(deleteProductOutputOutput, Is.Not.Null);
        Assert.That(deleteProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/DeleteProducts
        var deleteProductsInput = new DeleteProducts.DeleteProductsInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput.Store.Id,
            duplicateProductsOutputOutput.Data.Products.Select(p => p.Id).ToList(),
            Mocks.SleekflowStaffId,
            null);
        var deleteProductsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteProductsInput).ToUrl("/Products/DeleteProducts");
            });
        var deleteProductsOutputOutput =
            await deleteProductsScenarioResult.ReadAsJsonAsync<Output<DeleteProducts.DeleteProductsOutput>>();

        Assert.That(deleteProductsOutputOutput, Is.Not.Null);
        Assert.That(deleteProductsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task DefaultProductsTest()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }

        // /Blobs/CreateUploadBlobSasUrls
        var createUploadBlobSasUrlsInput =
            new CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsInput(
                Mocks.SleekflowCompanyId,
                1,
                _createStoreOutput!.Store.Id,
                BlobTypes.Image);
        var createUploadBlobSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createUploadBlobSasUrlsInput).ToUrl("/Blobs/CreateUploadBlobSasUrls");
            });
        var createUploadBlobSasUrlsOutput =
            await createUploadBlobSasUrlsScenarioResult.ReadAsJsonAsync<
                Output<CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsOutput>>();

        Assert.That(createUploadBlobSasUrlsOutput, Is.Not.Null);
        Assert.That(createUploadBlobSasUrlsOutput!.HttpStatusCode, Is.EqualTo(200));

        var uploadBlobs = createUploadBlobSasUrlsOutput.Data.UploadBlobs;

        Assert.That(uploadBlobs, Is.Not.Null);
        Assert.That(uploadBlobs.Count, Is.EqualTo(1));

        var targetUploadBlob = uploadBlobs[0];

        var uri = new Uri(targetUploadBlob.BlobUrl);
        var blockBlobClient = new BlockBlobClient(uri);

        await using var fileStream = File.OpenRead("sleekflow-logo.jfif");
        await blockBlobClient.UploadAsync(fileStream);

        // /Products/CreateDefaultProduct
        var createDefaultProductInput = new CreateDefaultProduct.CreateDefaultProductInput(
            new List<string>
            {
                _createCategoryOutput!.Category.Id,
            },
            null,
            null,
            new List<Multilingual>
            {
                new ("en", nameof(DefaultProductsTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test product."),
                    null,
                    null)
            },
            new List<ImageDto>()
            {
                new ImageDto(null, targetUploadBlob.BlobName),
                new ImageDto("https://images.media-outreach.com/256811/DSC01571.jpg", null),
            },
            new Dictionary<string, object?>(),
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            new List<Price>()
            {
                new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
            },
            new List<ProductVariant.ProductVariantAttribute>
            {
                new ProductVariant.ProductVariantAttribute("color", "red")
            },
            Mocks.SleekflowStaffId,
            null);
        var createDefaultProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createDefaultProductInput).ToUrl("/Products/CreateDefaultProduct");
            });
        var createDefaultProductOutputOutput =
            await createDefaultProductScenarioResult
                .ReadAsJsonAsync<Output<CreateDefaultProduct.CreateDefaultProductOutput>>();

        Assert.That(createDefaultProductOutputOutput, Is.Not.Null);
        Assert.That(createDefaultProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createdProductId = createDefaultProductOutputOutput.Data.Product.Id;

        // /Products/GetProductMessagePreview
        var getProductMessagePreviewInput = new GetProductMessagePreview.GetProductMessagePreviewInput(
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            createDefaultProductOutputOutput.Data.Product.DefaultProductVariant!.Id,
            createDefaultProductOutputOutput.Data.Product.Id,
            "HKD",
            "en");
        var getProductMessagePreviewScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getProductMessagePreviewInput).ToUrl("/Products/GetProductMessagePreview");
            });
        var getProductMessagePreviewOutputOutput =
            await getProductMessagePreviewScenarioResult
                .ReadAsJsonAsync<Output<GetProductMessagePreview.GetProductMessagePreviewOutput>>();

        Assert.That(getProductMessagePreviewOutputOutput, Is.Not.Null);
        Assert.That(getProductMessagePreviewOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/UpdateDefaultProduct
        var updateDefaultProductInput = new UpdateDefaultProduct.UpdateDefaultProductInput(
            new List<string>
            {
                _createCategoryOutput!.Category.Id,
            },
            null,
            null,
            new List<Multilingual>
            {
                new ("en", nameof(DefaultProductsTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test product."),
                    null,
                    null)
            },
            createDefaultProductOutputOutput.Data.Product.Images,
            new Dictionary<string, object?>(),
            createdProductId,
            Mocks.SleekflowCompanyId,
            _createStoreOutput!.Store.Id,
            false,
            new List<Price>()
            {
                new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
            },
            new List<ProductVariant.ProductVariantAttribute>
            {
                new ProductVariant.ProductVariantAttribute("color", "red")
            },
            Mocks.SleekflowStaffId,
            null);
        var updateDefaultProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateDefaultProductInput).ToUrl("/Products/UpdateDefaultProduct");
            });
        var updateDefaultProductOutputOutput =
            await updateDefaultProductScenarioResult
                .ReadAsJsonAsync<Output<UpdateDefaultProduct.UpdateDefaultProductOutput>>();

        Assert.That(updateDefaultProductOutputOutput, Is.Not.Null);
        Assert.That(updateDefaultProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Products/DeleteProduct
        var deleteProductInput = new DeleteProduct.DeleteProductInput(
            createDefaultProductOutputOutput.Data.Product.Id,
            Mocks.SleekflowCompanyId,
            _createStoreOutput.Store.Id,
            Mocks.SleekflowStaffId,
            null);
        var deleteProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteProductInput).ToUrl("/Products/DeleteProduct");
            });
        var deleteProductOutputOutput =
            await deleteProductScenarioResult.ReadAsJsonAsync<Output<DeleteProduct.DeleteProductOutput>>();

        Assert.That(deleteProductOutputOutput, Is.Not.Null);
        Assert.That(deleteProductOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task ProductVariantsTest()
    {
        // /ProductVariants/CreateProductVariant
        var createProductVariantInput = new CreateProductVariant.CreateProductVariantInput(
            null,
            null,
            new List<Price>()
            {
                new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
            },
            0,
            new List<ProductVariant.ProductVariantAttribute>
            {
                new ProductVariant.ProductVariantAttribute(
                    "color",
                    "yellow")
            },
            new List<Multilingual>
            {
                new ("en", nameof(ProductVariantsTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test productVariant."),
                    null,
                    null)
            },
            new List<ImageDto>(),
            null,
            Mocks.SleekflowCompanyId,
            new List<string>
            {
                _createCategoryOutput!.Category.Id,
            },
            _createStoreOutput!.Store.Id,
            _createProductOutput!.Product.Id,
            Mocks.SleekflowStaffId,
            null);
        var createProductVariantScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createProductVariantInput).ToUrl("/ProductVariants/CreateProductVariant");
            });
        var createProductVariantOutputOutput =
            await createProductVariantScenarioResult
                .ReadAsJsonAsync<Output<CreateProductVariant.CreateProductVariantOutput>>();

        Assert.That(createProductVariantOutputOutput, Is.Not.Null);
        Assert.That(createProductVariantOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /ProductVariants/DeleteProductVariant
        var deleteProductVariantInput = new DeleteProductVariant.DeleteProductVariantInput(
            createProductVariantOutputOutput.Data.ProductVariant.Id,
            _createStoreOutput.Store.Id,
            Mocks.SleekflowCompanyId,
            createProductVariantOutputOutput.Data.ProductVariant.ProductId,
            Mocks.SleekflowStaffId,
            null);
        var deleteProductVariantScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteProductVariantInput).ToUrl("/ProductVariants/DeleteProductVariant");
            });
        var deleteProductVariantOutputOutput =
            await deleteProductVariantScenarioResult
                .ReadAsJsonAsync<Output<DeleteProductVariant.DeleteProductVariantOutput>>();

        Assert.That(deleteProductVariantOutputOutput, Is.Not.Null);
        Assert.That(deleteProductVariantOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task StoresTest()
    {
        // /Languages/GetLanguages
        var getLanguagesInput = new GetLanguages.GetLanguagesInput();
        var getLanguagesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getLanguagesInput).ToUrl("/Languages/GetLanguages");
            });
        var getLanguagesOutputOutput =
            await getLanguagesScenarioResult.ReadAsJsonAsync<Output<GetLanguages.GetLanguagesOutput>>();

        var enLanguage = getLanguagesOutputOutput!.Data.Languages.First(l => l.LanguageIsoCode == "en");
        var zhLanguage = getLanguagesOutputOutput.Data.Languages.First(l => l.LanguageIsoCode == "zh");

        // /Stores/CreateStore
        var createStoreInput = new CreateStore.CreateStoreInput(
            new List<Multilingual>
            {
                new (enLanguage.LanguageIsoCode, nameof(StoresTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual(enLanguage.LanguageIsoCode, "This is a test store."),
                    null,
                    null)
            },
            true,
            true,
            new List<LanguageInputDto>
            {
                new (enLanguage.LanguageIsoCode, true)
            },
            new List<CurrencyInputDto>
            {
                new ("GBP"), new ("HKD")
            },
            new StoreTemplateDict(
                new List<Multilingual>()
                {
                    new Multilingual(
                        enLanguage.LanguageIsoCode,
                        """
                        Id {{ product_variant.id }}

                        Store Id {{ product_variant.store.id }}
                        Store Url {{ product_variant.store.url }}
                        Store Name {{ product_variant.store.name }}
                        Store Description {% for description in product_variant.store.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
                        Store Languages {% for language in product_variant.store.languages -%} {{ language.language_iso_code }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}

                        Product Id {{ product_variant.product.id }}
                        Product Categories {% for category in product_variant.product.categories -%} {{ category.name }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
                        Product Sku {{ product_variant.product.sku }}
                        Product Name {{ product_variant.product.name }}
                        Product Description {% for description in product_variant.product.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
                        Product Images {% for image in product_variant.product.images -%} {{ image.image_url }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}

                        Product Variant Sku {{ product_variant.sku }}
                        Product Variant Prices {% for price in product_variant.prices -%} {{ price.currency_iso_code }} {{ price.amount }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
                        Product Variant Name {{ product_variant.name }}
                        Product Variant Descriptions {% for description in product_variant.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
                        Product Images {% for image in product_variant.images -%} {{ image.image_url }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
                        """)
                }),
            new Dictionary<string, object?>(),
            null,
            null,
            Mocks.SleekflowCompanyId,
            "MyStaffId",
            null);
        var createStoreScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createStoreInput).ToUrl("/Stores/CreateStore");
            });
        var createStoreOutputOutput =
            await createStoreScenarioResult.ReadAsJsonAsync<Output<CreateStore.CreateStoreOutput>>();

        Assert.That(createStoreOutputOutput, Is.Not.Null);
        Assert.That(createStoreOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createdStoreId = createStoreOutputOutput.Data.Store.Id;

        // /Stores/PreviewStoreTemplates
        var previewStoreTemplatesInput = new PreviewStoreTemplates.PreviewStoreTemplatesInput(
            createdStoreId,
            Mocks.SleekflowCompanyId);
        var previewStoreTemplatesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(previewStoreTemplatesInput).ToUrl("/Stores/PreviewStoreTemplates");
            });
        var previewStoreTemplatesOutputOutput =
            await previewStoreTemplatesScenarioResult
                .ReadAsJsonAsync<Output<PreviewStoreTemplates.PreviewStoreTemplatesOutput>>();

        Assert.That(previewStoreTemplatesOutputOutput, Is.Not.Null);
        Assert.That(previewStoreTemplatesOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(previewStoreTemplatesOutputOutput.Data, Is.Not.Null);
        Assert.That(previewStoreTemplatesOutputOutput.Data.RenderedTemplateDict, Is.Not.Null);

        // /Stores/PreviewTemplate
        var previewTemplateInput = new PreviewTemplate.PreviewTemplateInput(
            createdStoreId,
            Mocks.SleekflowCompanyId,
            "MessagePreview",
            new List<Multilingual>()
            {
                new Multilingual(
                    enLanguage.LanguageIsoCode,
                    "Id {{ product_variant.id }}")
            });
        var previewTemplateScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(previewTemplateInput).ToUrl("/Stores/PreviewTemplate");
            });
        var previewTemplateOutputOutput =
            await previewTemplateScenarioResult
                .ReadAsJsonAsync<Output<PreviewTemplate.PreviewTemplateOutput>>();

        Assert.That(previewTemplateOutputOutput, Is.Not.Null);
        Assert.That(previewTemplateOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(previewTemplateOutputOutput.Data, Is.Not.Null);
        Assert.That(previewTemplateOutputOutput.Data.RenderedTemplateDict, Is.Not.Null);

        // /Stores/GetStore
        var getStoreInput = new GetStore.GetStoreInput(
            createdStoreId,
            Mocks.SleekflowCompanyId);
        var getStoreScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStoreInput).ToUrl("/Stores/GetStore");
            });
        var getStoreOutputOutput =
            await getStoreScenarioResult.ReadAsJsonAsync<Output<GetStore.GetStoreOutput>>();

        Assert.That(getStoreOutputOutput, Is.Not.Null);
        Assert.That(getStoreOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getStoreOutputOutput.Data, Is.Not.Null);
        Assert.That(getStoreOutputOutput.Data.Store.Id, Is.EqualTo(createdStoreId));

        // /Stores/GetStores
        var getStoresInput = new GetStores.GetStoresInput(
            null,
            Mocks.SleekflowCompanyId,
            null,
            null,
            null,
            200);
        var getStoresScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStoresInput).ToUrl("/Stores/GetStores");
            });
        var getStoresOutputOutput =
            await getStoresScenarioResult.ReadAsJsonAsync<Output<GetStores.GetStoresOutput>>();

        Assert.That(getStoresOutputOutput, Is.Not.Null);
        Assert.That(getStoreOutputOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getStoresOutputOutput!.Data, Is.Not.Null);
        Assert.That(
            getStoresOutputOutput.Data.Stores.Select(s => s.Id).ToList(),
            Does.Contain(createdStoreId));

        // /Stores/UpdateStore
        var updateStoreInput = new UpdateStore.UpdateStoreInput(
            new List<Multilingual>
            {
                new (enLanguage.LanguageIsoCode, nameof(StoresTest)),
                new (zhLanguage.LanguageIsoCode, nameof(StoresTest)),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual(enLanguage.LanguageIsoCode, "This is a test store."),
                    null,
                    null),
                new (
                    DescriptionTypes.Text,
                    new Multilingual(zhLanguage.LanguageIsoCode, "這是一個測試商店。"),
                    null,
                    null)
            },
            false,
            false,
            new List<LanguageInputDto>
            {
                new (enLanguage.LanguageIsoCode, true), new (zhLanguage.LanguageIsoCode, false),
            },
            new List<CurrencyInputDto>
            {
                new ("GBP"), new ("HKD")
            },
            new StoreTemplateDict(
                new List<Multilingual>()),
            new Dictionary<string, object?>(),
            null,
            null,
            createdStoreId,
            Mocks.SleekflowCompanyId,
            "MyStaffId",
            null);
        var updateStoreScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateStoreInput).ToUrl("/Stores/UpdateStore");
            });
        var updateStoreOutputOutput =
            await updateStoreScenarioResult.ReadAsJsonAsync<Output<UpdateStore.UpdateStoreOutput>>();

        Assert.That(updateStoreOutputOutput, Is.Not.Null);
        Assert.That(updateStoreOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Stores/DeleteStore
        var deleteStoreInput = new DeleteStore.DeleteStoreInput(
            createdStoreId,
            Mocks.SleekflowCompanyId,
            "MyStaffId",
            null);
        await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteStoreInput).ToUrl("/Stores/DeleteStore");
            });

        // /Stores/GetStores after delete
        var getStoresInput2 = new GetStores.GetStoresInput(
            null,
            Mocks.SleekflowCompanyId,
            null,
            null,
            null,
            200);
        var getStoresScenarioResult2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStoresInput2).ToUrl("/Stores/GetStores");
            });
        var getStoresOutputOutput2 =
            await getStoresScenarioResult2.ReadAsJsonAsync<Output<GetStores.GetStoresOutput>>();

        Assert.That(getStoresOutputOutput2, Is.Not.Null);
        Assert.That(getStoresOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getStoresOutputOutput2.Data, Is.Not.Null);
        Assert.That(
            getStoresOutputOutput2.Data.Stores.Select(s => s.Id).ToList(),
            Does.Not.Contain(createdStoreId));
    }
}