using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Orders.ShopifyOrders;

public class ShopifyOrderStatistics
{
    [JsonProperty("count")]
    public long Count { get; set; }

    [JsonProperty("total_price")]
    public decimal TotalPrice { get; set; }

    [JsonConstructor]
    public ShopifyOrderStatistics(
        long count,
        decimal totalPrice)
    {
        Count = count;
        TotalPrice = totalPrice;
    }
}