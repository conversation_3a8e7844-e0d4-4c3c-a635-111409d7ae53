using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Crm;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Workers.Services
{
    public class GetPropertyValueService : IGetPropertyValueService
    {
        private readonly ILogger<GetPropertyValueService> _logger;
        private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
        private readonly ISleekflowCoreConfig _sleekflowCoreConfig;
        private readonly HttpClient _httpClient;
        private readonly IAppConfig _appConfig;

        public GetPropertyValueService(
            ILogger<GetPropertyValueService> logger,
            IAsyncPolicy<HttpResponseMessage> retryPolicy,
            ISleekflowCoreConfig sleekflowCoreConfig,
            IHttpClientFactory httpClientFactory,
            IAppConfig appConfig)
        {
            _logger = logger;
            _retryPolicy = retryPolicy;
            _sleekflowCoreConfig = sleekflowCoreConfig;
            _appConfig = appConfig;
            _httpClient = httpClientFactory.CreateClient("default-handler");
        }

        public async Task<GetContactPropertyValueByContactIdsOutput> GetContactPropertyValuesByContactIds(
            string companyId,
            string propertyId,
            Dictionary<string, ContactDetail>? contacts,
            string origin)
        {
            var getContactPropertyValueByContactIdsInput = new GetContactPropertyValueByContactIdsInput(
                companyId,
                contacts!.Keys.ToList(),
                propertyId
            );

            var getContactsInputJsonStr = JsonConvert.SerializeObject(getContactPropertyValueByContactIdsInput);
            var getContactsTargetUri = new Uri(origin + "/FlowHub/Internals/Commands/GetContactPropertyValueByContactIds");

            try
            {
                var pollyContext = new Context();
                pollyContext["logger"] = _logger;

                var getContactsResMsg = await _retryPolicy.ExecuteAsync(
                    async (context) =>
                    {
                        var reqMsg = new HttpRequestMessage
                        {
                            Method = HttpMethod.Post,
                            Content = new StringContent(getContactsInputJsonStr, Encoding.UTF8, "application/json"),
                            RequestUri = getContactsTargetUri,
                            Headers =
                            {
                                {
                                    "X-Sleekflow-Flow-Hub-Authorization",
                                    InternalsTokenUtils.CreateJwt(_sleekflowCoreConfig.CoreInternalsKey)
                                }
                            }
                        };
                        _logger.LogInformation("Attempting to get contacts batch from {Uri}", getContactsTargetUri);

                        return await _httpClient.SendAsync(reqMsg);
                    },
                    pollyContext);
                _logger.LogInformation("[GetContactPropertyValue] Result of query GetContactPropertyValue: {GetContactPropertyValue}", getContactsResMsg);
                getContactsResMsg.EnsureSuccessStatusCode();
                var getContactsResStr = await getContactsResMsg.Content.ReadAsStringAsync();
                var deserializeObject = JsonConvert.DeserializeObject<GetContactPropertyValueByContactIdsOutput>(getContactsResStr)!;
                return deserializeObject;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to get or deserialize contacts batch for Company {CompanyId}. Input: {InputJson}",
                    companyId,
                    getContactsInputJsonStr);

                throw;
            }
        }

        public async Task<GetPropertyValuesByContactIdsOutput> GetCustomObjectPropertyValueByContactsId(
            string sleekflowCompanyId,
            string schemaId,
            string schemafulObjectPropertyId,
            Dictionary<string, ContactDetail>? contacts,
            string origin)
        {
            var getContactPropertyValueByContactIdsInput = new GetPropertyValuesByContactIdsInput(
                sleekflowCompanyId,
                schemaId,
                contacts!.Keys.ToList(),
                schemafulObjectPropertyId
            );

            var getContactsInputJsonStr = JsonConvert.SerializeObject(getContactPropertyValueByContactIdsInput);
            var getContactsTargetUri = new Uri(_appConfig.CrmHubInternalsEndpoint + "/GetPropertyValuesByContactIds");
            _logger.LogInformation("[CheckEligibility] Begin to GetCustomObjectPropertyByContactsId. params: {GetContactPropertyValueByContactIdsInput}", JsonConvert.SerializeObject(getContactPropertyValueByContactIdsInput));

            try
            {
                var pollyContext = new Context();
                pollyContext["logger"] = _logger;

                var getContactsResMsg = await _retryPolicy.ExecuteAsync(
                    async (context) =>
                    {
                        var reqMsg = new HttpRequestMessage
                        {
                            Method = HttpMethod.Post,
                            Content = new StringContent(getContactsInputJsonStr, Encoding.UTF8, "application/json"),
                            RequestUri = getContactsTargetUri,
                            Headers =
                            {
                                {

                                    "X-Sleekflow-Key", _appConfig.HubspotIntegratorKey
                                }
                            }
                        };
                        _logger.LogInformation("Attempting to get custom object property value batch from {Uri}", getContactsTargetUri);
                        return await _httpClient.SendAsync(reqMsg);
                    },
                    pollyContext);

                getContactsResMsg.EnsureSuccessStatusCode();
                var getContactsResStr = await getContactsResMsg.Content.ReadAsStringAsync();

                var deserializeObject = JsonConvert.DeserializeObject<Output<GetPropertyValuesByContactIdsOutput>>(getContactsResStr)!;
                return deserializeObject.Data;
            }
            catch (Exception ex)
            {
                // return new GetPropertyValuesByContactIdsOutput(new Dictionary<string, List<object?>>()
                // {
                //     {
                //         "b95858f1-dd79-4b64-9c0a-ce1833ff6a7c", ["2025-06-01T09:21:00.000Z"]
                //     }
                // });
                _logger.LogError(
                    ex,
                    "Failed to get or deserialize property value request or response for Company {CompanyId}. Input: {InputJson}",
                    sleekflowCompanyId,
                    getContactsInputJsonStr);

                throw;
            }
        }
    }
}