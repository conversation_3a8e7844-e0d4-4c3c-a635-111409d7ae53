using NUnit.Framework;
using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

namespace Sleekflow.FlowHub.Tests.StepExecutors.Calls.MessageBodyCreators;

[TestFixture]
public class InstagramMessageBodyCreatorTests
{
    private InstagramMessageBodyCreator _creator;
    private SendMessageV2StepArgs _args;

    [SetUp]
    public void Setup()
    {
        _creator = new InstagramMessageBodyCreator();
        _args = new SendMessageV2StepArgs(
            channelType: ChannelTypes.Instagram,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: null,
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);
    }

    [Test]
    public void CanHandle_GivenInstagramChannel_ShouldReturnTrue()
    {
        // Act
        var result = _creator.CanHandle(ChannelTypes.Instagram);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public void CanHandle_GivenNonInstagramChannel_ShouldReturnFalse()
    {
        // Act
        var result = _creator.CanHandle(ChannelTypes.Facebook);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CreateMessageBodyAsync_GivenTextMessage_ShouldCreateCorrectMessageBody()
    {
        // Arrange
        const string messageText = "Hello, Instagram!";

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync(messageText, _args);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.InstagramMessengerMessage, Is.Not.Null);
        Assert.That(body.InstagramMessengerMessage!.MessagingType, Is.EqualTo("MESSAGE_TAG"));
        Assert.That(body.InstagramMessengerMessage.Message.Text, Is.EqualTo(messageText));
        Assert.That(body.InstagramMessengerMessage.Tag, Is.EqualTo("HUMAN_AGENT"));
        Assert.That(type, Is.EqualTo("text"));
    }
}