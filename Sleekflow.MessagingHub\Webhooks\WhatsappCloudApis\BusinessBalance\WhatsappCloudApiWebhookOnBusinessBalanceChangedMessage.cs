using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;

[SwaggerInclude]
public class WhatsappCloudApiWebhookOnBusinessBalanceChangedMessage
{
    public WhatsappCloudApiWebhookOnBusinessBalanceChangedMessage(BusinessBalanceDto businessBalance)
    {
        BusinessBalance = businessBalance;
    }

    [JsonProperty("business_balance")]
    public BusinessBalanceDto BusinessBalance { get; set; }
}