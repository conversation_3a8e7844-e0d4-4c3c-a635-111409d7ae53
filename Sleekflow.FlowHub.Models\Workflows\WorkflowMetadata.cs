﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows;

public class WorkflowMetadata
{
    [JsonProperty("nodes")]
    public List<WorkflowMetadataNode> Nodes { get; set; }

    [JsonProperty("edges")]
    public List<WorkflowMetadataEdge> Edges { get; set; }

    [JsonConstructor]
    public WorkflowMetadata(
        List<WorkflowMetadataNode>? nodes,
        List<WorkflowMetadataEdge>? edges)
    {
        Nodes = nodes ?? new();
        Edges = edges ?? new();
    }
}