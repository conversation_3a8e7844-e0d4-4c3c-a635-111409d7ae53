﻿using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.States.LoopThroughObjects;

[Resolver(typeof(ICommerceHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.SysStateLoopThroughObjectsProgress)]
public class LoopThroughObjectsProgressState : Entity, IHasSleekflowCompanyId
{
    public const string PropertyNameStateObj = "state_obj";

    [JsonProperty("state_name")]
    public string StateName { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("flow_hub_workflow_id")]
    public string FlowHubWorkflowId { get; set; }

    [JsonProperty("flow_hub_workflow_versioned_id")]
    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_obj")]
    public LoopThroughObjectsProgressStateObj StateObj { get; set; }

    [JsonConstructor]
    public LoopThroughObjectsProgressState(
        string id,
        string stateName,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId,
        LoopThroughObjectsProgressStateObj stateObj)
        : base(id, "LoopThroughObjectsProgressState")
    {
        StateName = stateName;
        ProviderName = providerName;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        SleekflowCompanyId = sleekflowCompanyId;
        StateObj = stateObj;
    }
}