using Pulumi;
using Pulumi.AzureNative.App.V20240301.Inputs;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Storage;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Models;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using ContainerArgs = Pulumi.AzureNative.App.V20240301.Inputs.ContainerArgs;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using Insights = Pulumi.AzureNative.Insights;
using Random = Pulumi.Random;
using SecretArgs = Pulumi.AzureNative.App.V20240301.Inputs.SecretArgs;
using Storage = Pulumi.AzureNative.Storage;
using VolumeArgs = Pulumi.AzureNative.App.V20240301.Inputs.VolumeArgs;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.IntelligentHub;

public class IntelligentHubLightRag
{
    private readonly Docker.Image _image;
    private readonly MyConfig _myConfig;
    private readonly Output<string> _imageName;
    private readonly ResourceGroup _resourceGroup;
    private readonly ResourceGroup? _appServiceResourceGroup;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ContainerRegistry.Registry _registry;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;

    public IntelligentHubLightRag(
        Output<string> imageName,
        Docker.Image image,
        MyConfig myConfig,
        ResourceGroup resourceGroup,
        ResourceGroup? appServiceResourceGroup,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ContainerRegistry.Registry registry,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples)
    {
        _image = image;
        _myConfig = myConfig;
        _imageName = imageName;
        _resourceGroup = resourceGroup;
        _appServiceResourceGroup = appServiceResourceGroup;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _registry = registry;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
    }

    public List<App.ContainerApp> Initialize()
    {
        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.IntelligentHubLightRag))
            {
                continue;
            }

            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var webApps = managedEnvAndAppsTuple.WebApps;

            var storageRandomId = new Random.RandomId(
                "sleekflow-ihlr-storage-account-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "IHLR", "LIGHT-RAG"
                        }
                    },
                });
            var storageAccount = new Storage.StorageAccount(
                "sleekflow-ihlr-storage-account",
                new Storage.StorageAccountArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new Storage.Inputs.SkuArgs
                    {
                        Name = Storage.SkuName.Standard_LRS,
                    },
                    Tags = new InputMap<string>
                    {
                        {
                            "Environment", _myConfig.Name
                        },
                        {
                            "StorageAccountName", $"sleekflow-intelligent-hub-light-rag-storage-{_myConfig.Name}"
                        }
                    },
                    Kind = Storage.Kind.StorageV2,
                    AccountName = storageRandomId.Hex.Apply(h => "s" + h)
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            // Create a File Share
            var fileShare = new Storage.FileShare(
                "sleekflow-ihlr-light-rag-file-share",
                new FileShareArgs
                {
                    AccountName = storageAccount.Name,
                    ShareQuota = 100, // Quota in GB
                    ResourceGroupName = _resourceGroup.Name,
                });

            var storageAccountKeys = ListStorageAccountKeys.Invoke(
                new ListStorageAccountKeysInvokeArgs
                {
                    ResourceGroupName = _resourceGroup.Name, AccountName = storageAccount.Name
                });

            var managedEnvironmentsStorage = new App.ManagedEnvironmentsStorage(
                "sleekflow-ihlr-light-rag-managed-environments-storage",
                new App.ManagedEnvironmentsStorageArgs
                {
                    EnvironmentName = managedEnvironment.Name,
                    Properties = new ManagedEnvironmentStoragePropertiesArgs
                    {
                        AzureFile = new AzureFilePropertiesArgs
                        {
                            AccessMode = App.AccessMode.ReadWrite,
                            AccountKey = storageAccountKeys.Apply(keys => keys.Keys[0].Value),
                            AccountName = storageAccount.Name,
                            ShareName = fileShare.Name,
                        },
                    },
                    ResourceGroupName = _resourceGroup.Name,
                    StorageName = storageAccount.Name,
                });

            var (webApp, webAppName) = InitWebApp(
                "sleekflow-ihlr-light-rag",
                fileShare,
                _resourceGroup,
                GetSku(_myConfig.Name),
                storageAccount,
                managedEnvAndAppsTuple,
                storageAccountKeys);
            var appServiceConfiguration = new AppServiceConfiguration(webAppName, webApp);

            if (_myConfig.Name is "staging" or "production" && _appServiceResourceGroup is not null)
            {
                var (app, appName) = InitWebApp(
                    "sleekflow-light-rag",
                    fileShare,
                    _appServiceResourceGroup,
                    new Web.Inputs.SkuDescriptionArgs
                    {
                        Name = "P4mv3", Tier = "PremiumV3",
                    },
                    storageAccount,
                    managedEnvAndAppsTuple,
                    storageAccountKeys);
                // if (_myConfig.Name is "staging")
                // {
                appServiceConfiguration = new AppServiceConfiguration(appName, app);
                // }
            }

            webApps.Add(ServiceNames.IntelligentHubLightRag, appServiceConfiguration);
        }

        return apps;
    }

    private (App.ContainerApp App, string Name) InitContainerApp(
        StorageAccount storageAccount,
        App.ManagedEnvironment managedEnvironment,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple)
    {
        var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
            ServiceNames.GetShortName(ServiceNames.IntelligentHubLightRag));

        var containerApp = new App.ContainerApp(
            containerAppName,
            new App.ContainerAppArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ManagedEnvironmentId = managedEnvironment.Id,
                ContainerAppName = containerAppName,
                Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                Configuration = new ConfigurationArgs
                {
                    Ingress = new IngressArgs
                    {
                        External = true,
                        TargetPort = 8020,
                        Traffic = new InputList<TrafficWeightArgs>
                        {
                            new TrafficWeightArgs
                            {
                                LatestRevision = true, Weight = 100
                            }
                        },
                    },
                    Registries =
                    {
                        new RegistryCredentialsArgs
                        {
                            Server = _registry.LoginServer,
                            Username = _registryUsername,
                            PasswordSecretRef = "registry-password-secret",
                        }
                    },
                    Secrets =
                    {
                        new SecretArgs
                        {
                            Name = "registry-password-secret", Value = _registryPassword
                        },
                    },
                    ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                },
                Template = new TemplateArgs
                {
                    Scale = new ScaleArgs
                    {
                        MinReplicas = 1,
                        MaxReplicas = 1,
                        Rules =
                        [
                            new ScaleRuleArgs
                            {
                                Name = "http",
                                Http = new HttpScaleRuleArgs
                                {
                                    Metadata = new InputMap<string>
                                    {
                                        {
                                            "concurrentRequests", "160"
                                        }
                                    }
                                }
                            }
                        ]
                    },
                    Volumes = new VolumeArgs
                    {
                        Name = "index-volume",
                        StorageType = App.StorageType.AzureFile,
                        StorageName = storageAccount.Name,
                    },
                    Containers =
                    {
                        new ContainerArgs
                        {
                            Name = "sleekflow-ihlr-app",
                            Image = _image.BaseImageName,
                            VolumeMounts = new VolumeMountArgs
                            {
                                MountPath = "/app/index", VolumeName = "index-volume"
                            },
                            Resources = new ContainerResourcesArgs
                            {
                                Cpu = 1.0, Memory = "2.0Gi"
                            },
                            Probes = new List<ContainerAppProbeArgs>()
                            {
                                new ()
                                {
                                    Type = "liveness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                    {
                                        Path = "/health",
                                        Port = 8020,
                                        Scheme = App.Scheme.HTTP,
                                        HttpHeaders = new List<App.Inputs.ContainerAppProbeHttpHeadersArgs>()
                                        {
                                            new ()
                                            {
                                                Name = "X-API-Key", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                                            },
                                        },
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 8,
                                    PeriodSeconds = 2,
                                },
                                new ()
                                {
                                    Type = "readiness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                    {
                                        Path = "/health",
                                        Port = 8020,
                                        Scheme = App.Scheme.HTTP,
                                        HttpHeaders = new List<App.Inputs.ContainerAppProbeHttpHeadersArgs>()
                                        {
                                            new ()
                                            {
                                                Name = "X-API-Key", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                                            },
                                        },
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 8,
                                    PeriodSeconds = 2,
                                },
                                new ()
                                {
                                    Type = "startup",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                    {
                                        Path = "/health",
                                        Port = 8020,
                                        Scheme = App.Scheme.HTTP,
                                        HttpHeaders = new List<App.Inputs.ContainerAppProbeHttpHeadersArgs>()
                                        {
                                            new ()
                                            {
                                                Name = "X-API-Key", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                                            },
                                        },
                                    },
                                    InitialDelaySeconds = 12,
                                    TimeoutSeconds = 8,
                                }
                            },
                            Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                            [
                                new EnvironmentVarArgs
                                {
                                    Name = "HOST", Value = "0.0.0.0"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "PORT", Value = "8020"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "MAX_ASYNC", Value = "4"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "MAX_TOKENS", Value = "32768"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "EMBEDDING_DIM", Value = "3072"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "MAX_EMBED_TOKENS", Value = "8192"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "LIGHTRAG_API_KEY", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "LOG_LEVEL", Value = "INFO"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "VOLUME_DIR", Value = "./index"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "LLM_BINDING", Value = "azure_openai"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "LLM_BINDING_HOST", Value = "https://sleekflow-openai-eus2.openai.azure.com"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "LLM_MODEL", Value = "gpt-4.1"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "LLM_BINDING_API_KEY", Value = "********************************"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "EMBEDDING_BINDING", Value = "azure_openai"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "EMBEDDING_MODEL", Value = "embedding"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_OPENAI_API_VERSION", Value = "2024-10-21"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_OPENAI_DEPLOYMENT", Value = "gpt-4.1"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_OPENAI_API_KEY", Value = "********************************"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_OPENAI_ENDPOINT",
                                    Value = "https://sleekflow-openai-eus2.openai.azure.com"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_EMBEDDING_DEPLOYMENT", Value = "embedding"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_EMBEDDING_API_VERSION", Value = "2024-06-01"
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "AZURE_TRANSLATOR_KEY", Value = "d2275a86c8c84137b7a1ff1d6e2f2dae"
                                },
                            ])
                        }
                    },
                }
            },
            new CustomResourceOptions
            {
                Parent = managedEnvironment
            });
        return (containerApp, containerAppName);
    }

    private (Web.WebApp App, string Name) InitWebApp(
        string prefix,
        Storage.FileShare fileShare,
        ResourceGroup resourceGroup,
        Web.Inputs.SkuDescriptionArgs sku,
        Storage.StorageAccount storageAccount,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Output<Storage.ListStorageAccountKeysResult> storageAccountKeys)
    {
        var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
        var plan = new Web.AppServicePlan(
            $"{prefix}-plan",
            new Web.AppServicePlanArgs
            {
                Kind = "Linux", ResourceGroupName = resourceGroup.Name, Sku = sku, Reserved = true,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var appInsights = new Insights.Component(
            $"{prefix}-app-insights",
            new Insights.ComponentArgs
            {
                ResourceGroupName = resourceGroup.Name,
                ApplicationType = Insights.ApplicationType.Web,
                FlowType = "Redfield",
                RequestSource = "IbizaWebAppExtensionCreate",
                Kind = "Web",
                WorkspaceResourceId = logAnalyticsWorkspace.Id
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var webAppName = $"{prefix}-webapp";
        webAppName = _myConfig.Name == "production"
            ? webAppName
            : $"{webAppName}-{_myConfig.Name}";

        var app = new Web.WebApp(
            webAppName,
            new Web.WebAppArgs
            {
                ServerFarmId = plan.Id,
                Name = webAppName,
                ResourceGroupName = resourceGroup.Name,
                SiteConfig = new Web.Inputs.SiteConfigArgs
                {
                    AlwaysOn = true, NumberOfWorkers = 1
                },
                Kind = "app,linux,container",
                Reserved = true,
                PublicNetworkAccess = "Enabled",
                HttpsOnly = true,

                // The following settings are swapped in the standby slot
                // ClientAffinityEnabled = false,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var slot = new Web.WebAppSlot(
            $"{webAppName}-slot-standby",
            new Web.WebAppSlotArgs
            {
                ServerFarmId = plan.Id,
                Name = webAppName,
                Slot = "standby",
                ResourceGroupName = resourceGroup.Name,
                SiteConfig = new Web.Inputs.SiteConfigArgs
                {
                    AlwaysOn = true,
                    NumberOfWorkers = 1,
                    AzureStorageAccounts =
                    {
                        {
                            "INDEX_VOLUME", new Web.Inputs.AzureStorageInfoValueArgs
                            {
                                AccountName = storageAccount.Name,
                                AccessKey = storageAccountKeys.Apply(keys => keys.Keys[0].Value),
                                MountPath = "/home/<USER>",
                                ShareName = fileShare.Name,
                                Type = Web.AzureStorageType.AzureFiles
                            }
                        },
                    },
                    AppSettings = new List<Web.Inputs.NameValuePairArgs>
                    {
                        new ()
                        {
                            Name = "WEBSITES_PORT", Value = "80"
                        },
                        new ()
                        {
                            Name = "DOCKER_CUSTOM_IMAGE_NAME", Value = _image.ImageName.Apply(i => i)
                        },
                        new ()
                        {
                            Name = "DOCKER_REGISTRY_SERVER_URL",
                            Value = _registry.LoginServer.Apply(s => $"https://{s}")
                        },
                        new ()
                        {
                            Name = "DOCKER_REGISTRY_SERVER_USERNAME", Value = _registryUsername
                        },
                        new ()
                        {
                            Name = "DOCKER_REGISTRY_SERVER_PASSWORD", Value = _registryPassword
                        },
                        new ()
                        {
                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING", Value = appInsights.ConnectionString
                        },
                        new ()
                        {
                            Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
                        },
                        new ()
                        {
                            Name = "HOST", Value = "0.0.0.0"
                        },
                        new ()
                        {
                            Name = "PORT", Value = "80"
                        },
                        new ()
                        {
                            Name = "MAX_ASYNC", Value = "4"
                        },
                        new ()
                        {
                            Name = "MAX_TOKENS", Value = "32768"
                        },
                        new ()
                        {
                            Name = "EMBEDDING_DIM", Value = "3072"
                        },
                        new ()
                        {
                            Name = "MAX_EMBED_TOKENS", Value = "8192"
                        },
                        new ()
                        {
                            Name = "LIGHTRAG_API_KEY", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                        },
                        new ()
                        {
                            Name = "LOG_LEVEL", Value = "INFO"
                        },
                        new ()
                        {
                            Name = "VOLUME_DIR", Value = "/home/<USER>"
                        },
                        new ()
                        {
                            Name = "LLM_BINDING", Value = "azure_openai"
                        },
                        new ()
                        {
                            Name = "LLM_BINDING_HOST", Value = "https://sleekflow-ih-openai216b80d4.openai.azure.com"
                        },
                        new ()
                        {
                            Name = "LLM_MODEL", Value = "chat"
                        },
                        new ()
                        {
                            Name = "LLM_BINDING_API_KEY", Value = "********************************"
                        },
                        new ()
                        {
                            Name = "EMBEDDING_BINDING", Value = "azure_openai"
                        },
                        new ()
                        {
                            Name = "EMBEDDING_MODEL", Value = "embedding"
                        },
                        new ()
                        {
                            Name = "AZURE_OPENAI_API_VERSION", Value = "2024-10-21"
                        },
                        new ()
                        {
                            Name = "AZURE_OPENAI_DEPLOYMENT", Value = "chat"
                        },
                        new ()
                        {
                            Name = "AZURE_OPENAI_API_KEY", Value = "********************************"
                        },
                        new ()
                        {
                            Name = "AZURE_OPENAI_ENDPOINT",
                            Value = "https://sleekflow-ih-openai216b80d4.openai.azure.com"
                        },
                        new ()
                        {
                            Name = "AZURE_EMBEDDING_DEPLOYMENT", Value = "embedding"
                        },
                        new ()
                        {
                            Name = "AZURE_EMBEDDING_API_VERSION", Value = "2024-06-01"
                        },
                        new ()
                        {
                            Name = "AZURE_TRANSLATOR_KEY", Value = "d2275a86c8c84137b7a1ff1d6e2f2dae"
                        },
                        new ()
                        {
                            Name = "RAG_CACHE_MAX_SIZE", Value = GetMaxCacheSize(_myConfig.Name)
                        },
                    },
                    HealthCheckPath = "/health",
                    LinuxFxVersion = _imageName.Apply(n => $"DOCKER|{n}"),
                },
                Kind = "app,linux,container",
                Reserved = true,
                PublicNetworkAccess = "Enabled",
                HttpsOnly = true,
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
        return (app, webAppName);
    }

    private static Web.Inputs.SkuDescriptionArgs GetSku(string env)
    {
        return env switch
        {
            "production" => new Web.Inputs.SkuDescriptionArgs
            {
                Name = "P3V3", Tier = "PremiumV3",
            },
            _ => new Web.Inputs.SkuDescriptionArgs
            {
                Name = "P1v3", Tier = "PremiumV3",
            }
        };
    }

    private static string GetMaxCacheSize(string env)
    {
        return env switch
        {
            "production" => "600",
            _ => "20"
        };
    }
}