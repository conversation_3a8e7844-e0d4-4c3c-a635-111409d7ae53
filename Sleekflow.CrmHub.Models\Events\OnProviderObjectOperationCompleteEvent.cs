﻿using Sleekflow.Events;

namespace Sleekflow.CrmHub.Models.Events;

public class OnProviderObjectOperationCompleteEvent : IEvent
{
    public string SleekflowCompanyId { get; set; }

    public string ObjectOperation { get; set; }

    public string CrmHubObjectId { get; set; }

    public string EntityTypeName { get; set; }

    public Dictionary<string, object?> Dict { get; set; }

    public string ProviderName { get; set; }

    public OnProviderObjectOperationCompleteEvent(
        string sleekflowCompanyId,
        string objectOperation,
        string crmHubObjectId,
        string entityTypeName,
        Dictionary<string, object?> dict,
        string providerName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ObjectOperation = objectOperation;
        CrmHubObjectId = crmHubObjectId;
        EntityTypeName = entityTypeName;
        Dict = dict;
        ProviderName = providerName;
    }
}