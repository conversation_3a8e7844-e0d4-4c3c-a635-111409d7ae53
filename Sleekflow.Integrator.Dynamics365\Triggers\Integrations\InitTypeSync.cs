using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Subscriptions;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitTypeSync : ITrigger
{
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;
    private readonly IDynamics365SubscriptionRepository _dynamics365SubscriptionRepository;

    public InitTypeSync(
        IDynamics365AuthenticationService dynamics365AuthenticationService,
        IDynamics365SubscriptionRepository dynamics365SubscriptionRepository)
    {
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
        _dynamics365SubscriptionRepository = dynamics365SubscriptionRepository;
    }

    public class InitTypeSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_config")]
        public SyncConfig? SyncConfig { get; set; }

        [JsonConstructor]
        public InitTypeSyncInput(string sleekflowCompanyId, string entityTypeName, SyncConfig? syncConfig)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            SyncConfig = syncConfig;
        }
    }

    public class InitTypeSyncOutput
    {
    }

    public async Task<InitTypeSyncOutput> F(
        InitTypeSyncInput initTypeSyncInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(initTypeSyncInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        if (initTypeSyncInput.SyncConfig == null)
        {
            await ClearSyncConfigsAsync(initTypeSyncInput.EntityTypeName, initTypeSyncInput.SleekflowCompanyId);

            return new InitTypeSyncOutput();
        }

        if (initTypeSyncInput.SyncConfig.FieldFilters is { Count: 0 })
        {
            throw new SfMissingNecessaryFieldFiltersException(new List<string>());
        }

        var objects = await _dynamics365SubscriptionRepository.GetObjectsAsync(
            s =>
                s.EntityTypeName == initTypeSyncInput.EntityTypeName
                && s.SysTypeName == Dynamics365Subscription.SysTypeNameValue
                && s.SleekflowCompanyId == initTypeSyncInput.SleekflowCompanyId);
        if (objects.Any())
        {
            var @object = objects[0];

            @object.Interval = initTypeSyncInput.SyncConfig.Interval;

            await _dynamics365SubscriptionRepository.ReplaceAsync(@object.Id, @object.SleekflowCompanyId, @object);

            return new InitTypeSyncOutput();
        }

        var createCount = await _dynamics365SubscriptionRepository.CreateAsync(
            new Dynamics365Subscription(
                Guid.NewGuid().ToString(),
                initTypeSyncInput.SleekflowCompanyId,
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SyncConfig.Interval,
                DateTimeOffset.UtcNow,
                null,
                null),
            initTypeSyncInput.SleekflowCompanyId);
        if (createCount == 0)
        {
            // TODO Specify Exception
            throw new SfUserFriendlyException("Unable to init the type");
        }

        return new InitTypeSyncOutput();
    }

    private async Task ClearSyncConfigsAsync(string entityTypeName, string sleekflowCompanyId)
    {
        var objectEnumerableAsync = _dynamics365SubscriptionRepository.GetObjectEnumerableAsync(
            s =>
                s.EntityTypeName == entityTypeName
                && s.SysTypeName == Dynamics365Subscription.SysTypeNameValue
                && s.SleekflowCompanyId == sleekflowCompanyId);
        await foreach (var subscription in objectEnumerableAsync)
        {
            await _dynamics365SubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }
}