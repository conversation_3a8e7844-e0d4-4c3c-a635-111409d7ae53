using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnSalesforceObjectCreatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnSalesforceObjectCreated; }
    }

    [Required]
    [JsonProperty("salesforce_connection_id")]
    public string SalesforceConnectionId { get; set; }

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [Required]
    [JsonProperty("is_custom_object")]
    public bool IsCustomObject { get; set; }

    [Required]
    [JsonProperty("object_dict")]
    public Dictionary<string, object?> ObjectDict { get; set; }

    [JsonConstructor]
    public OnSalesforceObjectCreatedEventBody(
        DateTimeOffset createdAt,
        string salesforceConnectionId,
        string connectionId,
        string objectType,
        string entityTypeName,
        bool isCustomObject,
        Dictionary<string, object?> objectDict)
        : base(createdAt)
    {
        SalesforceConnectionId = salesforceConnectionId;
        ConnectionId = connectionId;
        ObjectType = objectType;
        EntityTypeName = entityTypeName;
        IsCustomObject = isCustomObject;
        ObjectDict = objectDict;
    }
}