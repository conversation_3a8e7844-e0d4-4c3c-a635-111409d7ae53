﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class SearchZohoObjectRequest
{
    [JsonProperty("aggregate_step_id")]
    [Required]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    [Required]
    public string ProxyStateId { get; set; }

    [JsonProperty("stack_entries")]
    [Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    [Required]
    public string ConnectionId { get; set; }

    [JsonProperty("entity_type_name")]
    [Required]
    public string EntityTypeName { get; set; }

    [JsonProperty("conditions")]
    [Required]
    public List<SearchObjectCondition> Conditions { get; set; }

    [JsonConstructor]
    public SearchZohoObjectRequest(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName,
        List<SearchObjectCondition> conditions)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        Conditions = conditions;
    }
}