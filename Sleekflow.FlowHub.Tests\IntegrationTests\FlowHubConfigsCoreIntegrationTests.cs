using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Tests.TestClients;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class FlowHubConfigsCoreIntegrationTests
{
    [Test]
    public async Task EnrollFlowHubTest()
    {
        var mockCompanyId = nameof(EnrollFlowHubTest);
        var mockStaffId = "my-staff-id";

        // /FlowHubConfigs/EnrollFlowHub
        var enrollFlowHubOutput = await FlowHubConfigTestClient.EnrollAsync(
            mockCompanyId,
            mockStaffId);

        Assert.That(enrollFlowHubOutput, Is.Not.Null);
        Assert.That(enrollFlowHubOutput!.HttpStatusCode, Is.EqualTo(200));

        // /FlowHubConfigs/GetFlowHubConfig
        var getFlowHubConfigOutput = await FlowHubConfigTestClient.GetAsync(mockCompanyId);

        Assert.That(getFlowHubConfigOutput, Is.Not.Null);
        Assert.That(getFlowHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getFlowHubConfigOutput!.Data.FlowHubConfig.IsEnrolled, Is.EqualTo(true));
        Assert.That(getFlowHubConfigOutput.Data.FlowHubConfig.UsageLimit, Is.Null);
        Assert.That(getFlowHubConfigOutput.Data.FlowHubConfig.UsageLimitOffset, Is.Null);
    }

    [Test]
    public async Task GetFlowHubConfigTest()
    {
        var mockCompanyId = nameof(GetFlowHubConfigTest);
        var mockStaffId = "my-staff-id";

        // /FlowHubConfigs/GetFlowHubConfig
        var getFlowHubConfigOutput = await FlowHubConfigTestClient.GetAsync(mockCompanyId);

        Assert.That(getFlowHubConfigOutput, Is.Not.Null);
        Assert.That(getFlowHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getFlowHubConfigOutput!.Data.FlowHubConfig.IsEnrolled, Is.EqualTo(false));
    }

    [Test]
    public async Task UnenrollFlowHubTest()
    {
        var mockCompanyId = nameof(UnenrollFlowHubTest);
        var mockStaffId = "my-staff-id";

        // /FlowHubConfigs/EnrollFlowHub
        var enrollFlowHubOutput = await FlowHubConfigTestClient.EnrollAsync(
                mockCompanyId,
                mockStaffId);

        // /FlowHubConfigs/UnenrollFlowHub
        var unenrollFlowHubOutput = await FlowHubConfigTestClient.UnenrollAsync(
                mockCompanyId,
                mockStaffId);

        Assert.That(unenrollFlowHubOutput, Is.Not.Null);
        Assert.That(unenrollFlowHubOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(unenrollFlowHubOutput.Data.FlowHubConfig.UsageLimit, Is.Null);

        // /FlowHubConfigs/GetFlowHubConfig
        var getFlowHubConfigOutput = await FlowHubConfigTestClient.GetAsync(mockCompanyId);

        Assert.That(getFlowHubConfigOutput, Is.Not.Null);
        Assert.That(getFlowHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getFlowHubConfigOutput!.Data.FlowHubConfig.IsEnrolled, Is.EqualTo(false));
    }

    [Test]
    public async Task UpdateFlowHubConfigTest()
    {
        var mockCompanyId = $"{nameof(UpdateFlowHubConfigTest)}-{Guid.NewGuid()}";
        var mockStaffId = "my-staff-id";

        // /FlowHubConfigs/EnrollFlowHub
        var enrollFlowHubOutput = await FlowHubConfigTestClient.EnrollAsync(
            mockCompanyId,
            mockStaffId);

        Assert.That(enrollFlowHubOutput, Is.Not.Null);
        Assert.That(enrollFlowHubOutput!.HttpStatusCode, Is.EqualTo(200));

        // /FlowHubConfigs/UpdateFlowHub
        var updateFlowHubConfigOutput2 = await FlowHubConfigTestClient.UpdateFlowHubConfigAsync(
            mockCompanyId,
            mockStaffId,
            usageLimit: new UsageLimit(
                maximumNumOfWorkflows: 100,
                maximumNumOfActiveWorkflows: 10,
                maximumNumOfNodesPerWorkflow: 200,
                maximumNumOfMonthlyWorkflowExecutions: 10000));

        Assert.That(updateFlowHubConfigOutput2, Is.Not.Null);
        Assert.That(updateFlowHubConfigOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateFlowHubConfigOutput2.Data.FlowHubConfig.UsageLimit, Is.Not.Null);
        Assert.That(
            updateFlowHubConfigOutput2!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfWorkflows,
            Is.EqualTo(100));
        Assert.That(
            updateFlowHubConfigOutput2!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfActiveWorkflows,
            Is.EqualTo(10));
        Assert.That(
            updateFlowHubConfigOutput2!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfNodesPerWorkflow,
            Is.EqualTo(200));
        Assert.That(
            updateFlowHubConfigOutput2!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfMonthlyWorkflowExecutions,
            Is.EqualTo(10000));
        Assert.That(updateFlowHubConfigOutput2!.Data.FlowHubConfig.UsageLimitOffset, Is.Null);

        // /FlowHubConfigs/UpdateFlowHub
        var updateFlowHubConfigOutput3 = await FlowHubConfigTestClient.UpdateFlowHubConfigAsync(
            mockCompanyId,
            mockStaffId,
            usageLimit: new UsageLimit(
                maximumNumOfWorkflows: 100,
                maximumNumOfActiveWorkflows: 10,
                maximumNumOfNodesPerWorkflow: 200,
                maximumNumOfMonthlyWorkflowExecutions: 10000),
            usageLimitOffset: new UsageLimitOffset(
                maximumNumOfActiveWorkflowsOffset: 12,
                maximumNumOfNodesPerWorkflowOffset: -3,
                maximumNumOfMonthlyWorkflowExecutionsOffset: 100));

        Assert.That(updateFlowHubConfigOutput3, Is.Not.Null);
        Assert.That(updateFlowHubConfigOutput3!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateFlowHubConfigOutput3.Data.FlowHubConfig.UsageLimit, Is.Not.Null);
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfWorkflows,
            Is.EqualTo(100));
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfActiveWorkflows,
            Is.EqualTo(10));
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfNodesPerWorkflow,
            Is.EqualTo(200));
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfMonthlyWorkflowExecutions,
            Is.EqualTo(10000));
        Assert.That(updateFlowHubConfigOutput3.Data.FlowHubConfig.UsageLimitOffset, Is.Not.Null);
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimitOffset!.MaximumNumOfActiveWorkflowsOffset,
            Is.EqualTo(12));
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimitOffset!.MaximumNumOfNodesPerWorkflowOffset,
            Is.EqualTo(-3));
        Assert.That(
            updateFlowHubConfigOutput3!.Data.FlowHubConfig.UsageLimitOffset.MaximumNumOfMonthlyWorkflowExecutionsOffset,
            Is.EqualTo(100));
    }

    [Test]
    public async Task UpdateFlowHubConfigWithoutEnrollTest()
    {
        var mockNotExistedCompanyId = $"{nameof(UpdateFlowHubConfigWithoutEnrollTest)}-{Guid.NewGuid()}";
        var mockStaffId = "my-staff-id";

        // FlowHubConfigs/UpdateFlowHubConfig
        var updateFlowHubConfigOutput = await FlowHubConfigTestClient.UpdateFlowHubConfigAsync(
            mockNotExistedCompanyId,
            mockStaffId,
            usageLimit: new UsageLimit(
                maximumNumOfWorkflows: 100,
                maximumNumOfActiveWorkflows: 10,
                maximumNumOfNodesPerWorkflow: 200,
                maximumNumOfMonthlyWorkflowExecutions: 10000));

        Assert.That(updateFlowHubConfigOutput, Is.Not.Null);
        Assert.That(updateFlowHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateFlowHubConfigOutput.Data.FlowHubConfig.UsageLimit, Is.Not.Null);
        Assert.That(updateFlowHubConfigOutput.Data.FlowHubConfig.IsEnrolled, Is.EqualTo(true));
        Assert.That(updateFlowHubConfigOutput.Data.FlowHubConfig.UsageLimitOffset, Is.Null);
        Assert.That(
            updateFlowHubConfigOutput.Data.FlowHubConfig.CreatedBy!.SleekflowStaffId,
            Is.EqualTo(
                updateFlowHubConfigOutput.Data.FlowHubConfig.UpdatedBy!.SleekflowStaffId));
        Assert.That(
            updateFlowHubConfigOutput!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfWorkflows,
            Is.EqualTo(100));
        Assert.That(
            updateFlowHubConfigOutput!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfActiveWorkflows,
            Is.EqualTo(10));
        Assert.That(
            updateFlowHubConfigOutput!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfNodesPerWorkflow,
            Is.EqualTo(200));
        Assert.That(
            updateFlowHubConfigOutput!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfMonthlyWorkflowExecutions,
            Is.EqualTo(10000));
    }

    [Test]
    public async Task GetEnrolledFlowHubConfigsTest()
    {
        // /FlowHubConfigs/GetEnrolledFlowHubConfigs
        var getEnrolledFlowHubConfigsOutput1 =
            await FlowHubConfigTestClient.GetEnrolledFlowHubConfigsAsync(
                null,
                10);

        Assert.That(getEnrolledFlowHubConfigsOutput1, Is.Not.Null);
        Assert.That(getEnrolledFlowHubConfigsOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /FlowHubConfigs/GetEnrolledFlowHubConfigs
        var getEnrolledFlowHubConfigsOutput2 =
            await FlowHubConfigTestClient.GetEnrolledFlowHubConfigsAsync(
                getEnrolledFlowHubConfigsOutput1!.Data.ContinuationToken,
                10);

        Assert.That(getEnrolledFlowHubConfigsOutput2, Is.Not.Null);
        Assert.That(getEnrolledFlowHubConfigsOutput2!.HttpStatusCode, Is.EqualTo(200));
    }
}