using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json.Linq;
using Sleekflow.Constants;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Providers.Resolvers;

namespace Sleekflow.CrmHub.Tests;

public class ProviderObjectIdentityResolverServiceTests
{
    [SetUp]
    public void Setup()
    {
        // Method intentionally left empty.
    }

    [Test]
    public void Test1()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "61096623"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "HK",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("85261096623"));
    }

    [Test]
    public void Test2()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "+61096623"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "HK",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("6196623"));
    }

    [Test]
    public void Test3()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "(*************"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "HK",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("8526202416200"));
    }

    [Test]
    public void Test4()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "+1(*************"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "HK",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("16202416200"));
    }

    [Test]
    public void Test5()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "(*************"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("*********"));
    }

    [Test]
    public void Test6()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "+" + "(*************"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("*********"));
    }

    [Test]
    public void Test7()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "83074740"
                },
                {
                    "salesforce-integrator:CountryCode", "SG"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("**********"));
    }

    [Test]
    public void Test8()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "68402002"
                },
                {
                    "salesforce-integrator:CountryCode", "HK"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("85268402002"));
    }

    [Test]
    public void Test9()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "012-3272 494"
                },
                {
                    "salesforce-integrator:DefaultCurrencyIsoCode", "MYR"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("60123272494"));
    }

    [Test]
    public void Test10()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "(*************"
                },
                {
                    "salesforce-integrator:Country", "USA"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("19523463500"));
    }

    [Test]
    public void Test11()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "886-2-25474189"
                },
                {
                    "salesforce-integrator:Country", "taiwan"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("886225474189"));
    }

    [Test]
    public void Test12()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "60194531114"
                },
                {
                    "salesforce-integrator:Country", "Malaysia"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("60194531114"));
    }

    [Test]
    public void Test13()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "************"
                },
                {
                    "salesforce-integrator:Country", "India"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("************"));
    }

    [Test]
    public void Test14()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "61096623"
                },
                {
                    "salesforce-integrator:Country", "Hong KONg"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("85261096623"));
    }

    [Test]
    public void Test15()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "d365:mobilephone", "61096623"
                },
                {
                    "d365:new_APMCountryId", new JObject
                    {
                        {
                            "new_code", "HK"
                        },
                        {
                            "new_apmcountryid", "9d5be191-8c91-e911-a99e-000d3aa306f0"
                        }
                    }
                }
            },
            "Contact",
            "d365",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "d365",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("85261096623"));
    }

    [Test]
    public void Test16()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "d365:mobilephone", "81632997"
                },
                {
                    "d365:new_APMCountryId", new JObject
                    {
                        {
                            "new_code", "SG"
                        },
                        {
                            "new_apmcountryid", "9d5be191-8c91-e911-a99e-000d3aa306f0"
                        }
                    }
                }
            },
            "Contact",
            "d365",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "d365",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("**********"));
    }

    [Test]
    public void Test17()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "*********"
                },
                {
                    "salesforce-integrator:MailingCountry", "Indonesia"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("62*********"));
    }

    [Test]
    public void Test18()
    {
        var providerObjectIdentityResolverService = new ProviderObjectIdentityResolverService(
            NullLogger<ProviderObjectIdentityResolverService>.Instance);

        var providerObjectIdentity = providerObjectIdentityResolverService.Resolve(
            new Dictionary<string, object?>
            {
                {
                    "salesforce-integrator:MobilePhone", "91397244"
                },
                {
                    "salesforce-integrator:CurrencyIsoCode", "SGD"
                }
            },
            "Contact",
            "salesforce-integrator",
            new ProviderConfig(
                "id",
                "sleekflowCompanyId",
                "key",
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null));

        Assert.That(providerObjectIdentity.PhoneNumber, Is.EqualTo("**********"));
    }
}