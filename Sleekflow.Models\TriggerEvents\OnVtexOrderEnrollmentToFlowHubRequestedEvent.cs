﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class OnVtexOrderEnrollmentToFlowHubRequestedEvent
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("vtex_authentication_id")]
    public string VtexAuthenticationId { get; set; }

    [JsonProperty("status_code")]
    public string StatusCode { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("order")]
    public VtexOrderOverview Order { get; set; }

    [JsonConstructor]
    public OnVtexOrderEnrollmentToFlowHubRequestedEvent(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        string vtexAuthenticationId,
        string statusCode,
        string orderId,
        VtexOrderOverview order)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        VtexAuthenticationId = vtexAuthenticationId;
        StatusCode = statusCode;
        OrderId = orderId;
        Order = order;
    }
}