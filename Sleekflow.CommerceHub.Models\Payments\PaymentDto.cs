﻿using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Payments;

public class PaymentDto
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("platform_specific_payment_id")]
    public string? PlatformSpecificPaymentId { get; set; }

    [JsonProperty("payment_record")]
    public PaymentRecord PaymentRecord { get; set; }

    [JsonConstructor]
    public PaymentDto(
        string sleekflowCompanyId,
        string orderId,
        string? platformSpecificPaymentId,
        PaymentRecord paymentRecord)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        OrderId = orderId;
        PlatformSpecificPaymentId = platformSpecificPaymentId;
        PaymentRecord = paymentRecord;
    }
}