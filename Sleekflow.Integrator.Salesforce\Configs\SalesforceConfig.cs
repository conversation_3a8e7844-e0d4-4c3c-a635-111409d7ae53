using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Integrator.Salesforce.Configs;

public interface ISalesforceConfig
{
    string SalesforceClientId { get; }

    string SalesforceClientSecret { get; }

    string SalesforceOauthCallbackUrl { get; }

    string SalesforceWebhookCallbackUrl { get; }

    string SalesforceOauthStateEncryptionKey { get; }
}

public class SalesforceConfig : IConfig, ISalesforceConfig
{
    public string SalesforceClientId { get; private set; }

    public string SalesforceClientSecret { get; private set; }

    public string SalesforceOauthCallbackUrl { get; private set; }

    public string SalesforceWebhookCallbackUrl { get; private set; }

    public string SalesforceOauthStateEncryptionKey { get; private set; }

    public SalesforceConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        SalesforceClientId =
            Environment.GetEnvironmentVariable("SALESFORCE_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("SALESFORCE_CLIENT_ID");
        SalesforceClientSecret =
            Environment.GetEnvironmentVariable("SALESFORCE_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("SALESFORCE_CLIENT_SECRET");
        SalesforceOauthCallbackUrl =
            Environment.GetEnvironmentVariable("SALESFORCE_OAUTH_CALLBACK_URL", target)
            ?? configuration["SALESFORCE_OAUTH_CALLBACK_URL"]!;
        SalesforceWebhookCallbackUrl =
            Environment.GetEnvironmentVariable("SALESFORCE_WEBHOOK_CALLBACK_URL", target)
            ?? configuration["SALESFORCE_WEBHOOK_CALLBACK_URL"]!;
        SalesforceOauthStateEncryptionKey =
            Environment.GetEnvironmentVariable("SALESFORCE_OAUTH_STATE_ENCRYPTION_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("SALESFORCE_OAUTH_STATE_ENCRYPTION_KEY");
    }
}