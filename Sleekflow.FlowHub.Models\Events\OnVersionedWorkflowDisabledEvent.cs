﻿using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events;

public class OnVersionedWorkflowDisabledEvent : IHasSleekflowCompanyId
{
    public string WorkflowId { get; set; }

    public string WorkflowVersionedId { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string DisableReasonCode { get; set; }

    public AuditEntity.SleekflowStaff? DisabledBy { get; set; }

    public DateTimeOffset DisabledAt { get; } = DateTimeOffset.UtcNow;

    public OnVersionedWorkflowDisabledEvent(
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string disableReasonCode,
        AuditEntity.SleekflowStaff? disabledBy)
    {
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        SleekflowCompanyId = sleekflowCompanyId;
        DisableReasonCode = disableReasonCode;
        DisabledBy = disabledBy;
    }
}