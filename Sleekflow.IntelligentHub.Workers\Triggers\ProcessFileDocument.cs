﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class ProcessFileDocument : ITrigger
{
    private readonly IFileIngestionService _fileIngestionService;

    public ProcessFileDocument(IFileIngestionService fileIngestionService)
    {
        _fileIngestionService = fileIngestionService;
    }

    public class ProcessFileDocumentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [Required]
        [JsonProperty("blob_url")]
        public string BlobUrl { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("file_ingestion_progress")]
        public object? FileIngestionProgress { get; set; }

        [JsonConstructor]
        public ProcessFileDocumentInput(
            string sleekflowCompanyId,
            string documentId,
            string blobUrl,
            object? fileIngestionProgress)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            BlobUrl = blobUrl;
            FileIngestionProgress = fileIngestionProgress;
        }
    }

    public class ProcessFileDocumentOutput
    {
        [JsonProperty("is_completed")]
        public bool IsCompleted { get; set; }

        [JsonProperty("progress_percentage")]
        public double ProgressPercentage { get; set; }

        [JsonProperty("file_ingestion_progress")]
        public object FileIngestionProgress { get; set; }

        [JsonConstructor]
        public ProcessFileDocumentOutput(bool isCompleted, double progressPercentage, object fileIngestionProgress)
        {
            IsCompleted = isCompleted;
            ProgressPercentage = progressPercentage;
            FileIngestionProgress = fileIngestionProgress;
        }
    }

    [Function(nameof(ProcessFileDocument))]
    public async Task<ProcessFileDocumentOutput> Process(
        [ActivityTrigger]
        ProcessFileDocumentInput processFileDocumentInput)
    {
        var fileIngestionProgress = await _fileIngestionService.ProcessFileDocument(
            processFileDocumentInput.SleekflowCompanyId,
            processFileDocumentInput.DocumentId,
            processFileDocumentInput.BlobUrl,
            processFileDocumentInput.FileIngestionProgress);

        return new ProcessFileDocumentOutput(
            fileIngestionProgress.IsCompleted(),
            fileIngestionProgress.GetProgressPercentage(),
            fileIngestionProgress)!;
    }
}