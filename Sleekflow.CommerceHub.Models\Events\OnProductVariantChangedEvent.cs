using Newtonsoft.Json;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Events;

public class OnProductVariantChangedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StoreId { get; set; }

    public string ProductId { get; set; }

    public string ProductVariantId { get; set; }

    public AuditEntity.SleekflowStaff SleekflowStaff { get; set; }

    [JsonConstructor]
    public OnProductVariantChangedEvent(
        string sleekflowCompanyId,
        string storeId,
        string productId,
        string productVariantId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StoreId = storeId;
        ProductId = productId;
        ProductVariantId = productVariantId;
        SleekflowStaff = sleekflowStaff;
    }
}