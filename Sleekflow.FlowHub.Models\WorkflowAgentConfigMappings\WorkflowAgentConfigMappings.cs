using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.WorkflowAgentConfigMappings;

[ContainerId(ContainerNames.WorkflowAgentConfigMapping)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class WorkflowAgentConfigMapping : AuditEntity
{
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("agent_config_id")]
    public string AgentConfigId { get; set; }

    [JsonConstructor]
    public WorkflowAgentConfigMapping(
        string workflowId,
        string agentConfigId,
        string id,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
    : base(id, SysTypeNames.WorkflowAgentConfigMapping, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        WorkflowId = workflowId;
        AgentConfigId = agentConfigId;
    }
}