namespace Sleekflow.DependencyInjection;

/// <summary>
/// Marker interface for services with a scoped lifetime in the dependency injection container.
/// </summary>
/// <remarks>
/// <para>
/// Services implementing this interface are automatically registered with a scoped lifetime.
/// A new instance is created per request or per scope, making it suitable for services that
/// handle request-specific data or maintain request-specific state.
/// </para>
/// <para>
/// Use this interface for services that:
/// <list type="bullet">
///   <li>Require access to request-scoped dependencies</li>
///   <li>Maintain state that should be isolated to the current request</li>
///   <li>Should be created once per request and then disposed at the end of the request</li>
/// </list>
/// </para>
/// <para>
/// The registration is automatically handled through assembly scanning in the Modules class.
/// </para>
/// </remarks>
public interface IScopedService
{
}