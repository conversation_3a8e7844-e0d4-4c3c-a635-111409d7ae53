using System.Text;
using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

public interface IAgenticKnowledgePluginStorageService
{
    void StoreToolResult(string response);

    string GetAllToolResults();
}

public class AgenticKnowledgePluginStorageService : IAgenticKnowledgePluginStorageService, IScopedService
{
    private readonly StringBuilder _sb = new ();

    // Just append into a string builder
    public void StoreToolResult(string response)
    {
        _sb.AppendLine(response);
    }

    public string GetAllToolResults()
    {
        return _sb.ToString();
    }
}