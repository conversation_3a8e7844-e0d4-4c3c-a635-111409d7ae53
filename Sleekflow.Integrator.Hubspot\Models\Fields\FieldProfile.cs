﻿using AutoMapper;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Integrator.Hubspot.Services.Models;

namespace Sleekflow.Integrator.Hubspot.Models.Fields;

public class FieldProfile : Profile
{
    [JsonConstructor]
    public FieldProfile()
    {
        // TODO soaptype
        CreateMap<HubspotPropertiesResult, GetTypeFieldsOutputFieldDto>()
            .ForMember(f => f.Calculated, e => e.MapFrom(p => p.Calculated))
            .ForMember(f => f.CompoundFieldName, e => e.MapFrom(p => p.GroupName))
            .ForMember(f => f.Createable, e => e.MapFrom(p => !p.Calculated))
            .ForMember(f => f.Custom, e => e.MapFrom(p => !p.HubspotDefined))
            .ForMember(f => f.Encrypted, e => e.MapFrom(p => false))
            .ForMember(f => f.Label, e => e.MapFrom(p => p.Label))
            .ForMember(f => f.Length, e => e.MapFrom(p => 1024))
            .ForMember(f => f.Name, e => e.MapFrom(p => p.Name))
            .ForMember(
                f => f.PicklistValues,
                e => e.MapFrom(
                    p => p.Options
                        .Select(o => new GetTypeFieldsOutputPicklistValue(o.Label, o.Value))
                        .ToList()))
            .ForMember(f => f.SoapType, e => e.MapFrom(p => p.Type))
            .ForMember(f => f.Type, e => e.MapFrom(p => p.Type))
            .ForMember(f => f.Unique, e => e.MapFrom(p => p.HasUniqueValue))
            .ForMember(f => f.Updateable, e => e.MapFrom(p => !p.ModificationMetadata.ReadOnlyValue))
            .ForCtorParam("calculated", e => e.MapFrom(p => p.Calculated))
            .ForCtorParam("compoundFieldName", e => e.MapFrom(p => p.GroupName))
            .ForCtorParam("createable", e => e.MapFrom(p => !p.Calculated))
            .ForCtorParam("custom", e => e.MapFrom(p => !p.HubspotDefined))
            .ForCtorParam("encrypted", e => e.MapFrom(p => false))
            .ForCtorParam("label", e => e.MapFrom(p => p.Label))
            .ForCtorParam("length", e => e.MapFrom(p => 1024))
            .ForCtorParam("name", e => e.MapFrom(p => p.Name))
            .ForCtorParam(
                "picklistValues",
                e => e.MapFrom(
                    p => p.Options
                        .Select(o => new GetTypeFieldsOutputPicklistValue(o.Label, o.Value))
                        .ToList()))
            .ForCtorParam("soapType", e => e.MapFrom(p => p.Type))
            .ForCtorParam("type", e => e.MapFrom(p => p.Type))
            .ForCtorParam("unique", e => e.MapFrom(p => p.HasUniqueValue))
            .ForCtorParam("updateable", e => e.MapFrom(p => !p.ModificationMetadata.ReadOnlyValue));
    }
}