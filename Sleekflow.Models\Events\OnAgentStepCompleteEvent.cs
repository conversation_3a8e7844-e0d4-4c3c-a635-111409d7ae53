﻿using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.Events;

public class OnAgentCompleteStepActivationEvent
{
    public string AggregateStepId { get; set; }

    public string ProxyStateId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string? AggregateStateContext { get; set; }

    public string? StepExecutionStatus { get; set; }

    public OnAgentCompleteStepActivationEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string? aggregateStateContext,
        string? stepExecutionStatus = null)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        AggregateStateContext = aggregateStateContext;
        StepExecutionStatus = stepExecutionStatus;
    }
}