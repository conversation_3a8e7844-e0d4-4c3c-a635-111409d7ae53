﻿namespace Sleekflow.Exceptions.CrmHub;

public class SfNotInitializedException : ErrorCodeException
{
    public SfNotInitializedException(string providerName, string entityTypeName)
        : base(
            ErrorCodeConstant.SfNotInitializedException,
            $"The providerName {providerName} and entityTypeName {entityTypeName} is not initialized")
    {
    }

    public SfNotInitializedException(string providerName)
        : base(
            ErrorCodeConstant.SfNotInitializedException,
            $"The providerName {providerName} is not initialized")
    {
    }
}