﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

/// <summary>
/// Manually update Run status and retrieve output from Apify Database.
/// For internal use only.
/// </summary>
[TriggerGroup(ControllerNames.WebScraperRuns)]
public class SyncWebScraperRun : ITrigger<SyncWebScraperRun.SyncWebScraperRunInput, SyncWebScraperRun.SyncWebScraperRunOutput>
{
    private readonly IWebScraperService _webScraperService;

    public SyncWebScraperRun(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class SyncWebScraperRunInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperRun.PropertyNameApifyRunId)]
        public string ApifyRunId { get; set; }

        [Required]
        [JsonProperty("is_sync_database")]
        public bool IsSyncDatabase { get; set; }

        [JsonConstructor]
        public SyncWebScraperRunInput(string sleekflowCompanyId, string apifyRunId, bool isSyncDatabase)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyRunId = apifyRunId;
            IsSyncDatabase = isSyncDatabase;
        }
    }

    public class SyncWebScraperRunOutput
    {
        [JsonProperty(WebScraperRun.PropertyNameWebScraperRun)]
        public WebScraperRun WebScraperRun { get; set; }

        [JsonConstructor]
        public SyncWebScraperRunOutput(WebScraperRun webScraperRun)
        {
            WebScraperRun = webScraperRun;
        }
    }

    public async Task<SyncWebScraperRunOutput> F(SyncWebScraperRunInput syncWebScraperRunInput)
    {
        var run = await _webScraperService.UpdateRunStatusAsync(
            syncWebScraperRunInput.SleekflowCompanyId,
            syncWebScraperRunInput.ApifyRunId);

        if (syncWebScraperRunInput.IsSyncDatabase)
        {
            run = await _webScraperService.StoreWebPageContentAsync(run.ApifyRunId);
        }

        return new SyncWebScraperRunOutput(run);
    }
}