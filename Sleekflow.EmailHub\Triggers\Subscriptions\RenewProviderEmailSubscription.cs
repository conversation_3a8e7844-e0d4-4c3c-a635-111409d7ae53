using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Events;

namespace Sleekflow.EmailHub.Triggers.Subscriptions;

[TriggerGroup(ControllerNames.Subscriptions)]
public class RenewProviderEmailSubscription : ITrigger
{
    private readonly IBus _bus;

    public RenewProviderEmailSubscription(IBus bus)
    {
        _bus = bus;
    }

    public class RenewProviderEmailSubscriptionInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public RenewProviderEmailSubscriptionInput(string providerName)
        {
            ProviderName = providerName;
        }
    }

    public class RenewProviderEmailSubscriptionOutput
    {
    }

    public async Task<RenewProviderEmailSubscriptionOutput> F(
        RenewProviderEmailSubscriptionInput subscriptionToProviderEmailAddressInput)
    {
        await _bus.Publish(new OnEmailSubscriptionRenewTriggeredEvent(subscriptionToProviderEmailAddressInput.ProviderName));
        return new RenewProviderEmailSubscriptionOutput();
    }
}