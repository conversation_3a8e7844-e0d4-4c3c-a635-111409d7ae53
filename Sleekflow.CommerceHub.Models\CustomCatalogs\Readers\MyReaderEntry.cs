using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;

public class MyReaderEntry
{
    [JsonProperty("error_message")]
    public string? ErrorMessage { get; set; }

    [JsonProperty("product")]
    public MyReaderProductDto? Product { get; set; }

    [JsonProperty("row")]
    public int Row { get; }

    [JsonConstructor]
    public MyReaderEntry(
        string? errorMessage,
        MyReaderProductDto? product,
        int row)
    {
        ErrorMessage = errorMessage;
        Product = product;
        Row = row;
    }
}