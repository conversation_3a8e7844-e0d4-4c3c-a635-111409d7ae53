using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.InternalIntegrationHub.Configs.NetSuite;

public interface INetSuiteConfig
{
    string ConsumerKey { get; }

    string ConsumerSecret { get; }

    string AccessToken { get; }

    string AccessTokenSecret { get; }

    string AccountId { get; }

    string Realm { get; }

    string BaseUrl { get; }
}

class NetSuiteConfig : IConfig, INetSuiteConfig
{
    public string ConsumerKey { get; }

    public string ConsumerSecret { get; }

    public string AccessToken { get; }

    public string AccessTokenSecret { get; }

    public string AccountId { get; }

    public string Realm { get; }

    public string BaseUrl { get; }

    public NetSuiteConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ConsumerKey =
            Environment.GetEnvironmentVariable("NETSUITE_CONSUMER_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_CONSUMER_KEY");

        ConsumerSecret =
            Environment.GetEnvironmentVariable("NETSUITE_CONSUMER_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_CONSUMER_SECRET");

        AccessToken =
            Environment.GetEnvironmentVariable("NETSUITE_ACCESS_TOKEN", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_ACCESS_TOKEN");

        AccessTokenSecret =
            Environment.GetEnvironmentVariable("NETSUITE_ACCESS_TOKEN_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_ACCESS_TOKEN_SECRET");

        AccountId =
            Environment.GetEnvironmentVariable("NETSUITE_ACCOUNT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_ACCOUNT_ID");

        Realm =
            Environment.GetEnvironmentVariable("NETSUITE_REALM", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_REALM");

        BaseUrl =
            Environment.GetEnvironmentVariable("NETSUITE_BASE_URL", target)
            ?? throw new SfMissingEnvironmentVariableException("NETSUITE_BASE_URL");
    }
}