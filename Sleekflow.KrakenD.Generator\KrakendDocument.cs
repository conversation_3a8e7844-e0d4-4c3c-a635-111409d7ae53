// <auto-generated />
//
// To parse this JSON data, add NuGet 'Newtonsoft.Json' then do:
//
//    using Sleekflow.KrakenD.Generator;
//
//    var krakendDocument = KrakendDocument.FromJson(jsonString);

namespace Sleekflow.KrakenD.Generator
{
    using System;
    using System.Collections.Generic;

    using System.Globalization;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Converters;

    public partial class KrakendDocument
    {
        /// <summary>
        /// By default, KrakenD verifies every SSL connection. This option allows you to connect to
        /// backends considered insecure, for instance when you are using **self-signed certificates**
        /// </summary>
        [JsonProperty("allow_insecure_connections", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AllowInsecureConnections { get; set; }

        [JsonProperty("async_agent", NullValueHandling = NullValueHandling.Ignore)]
        public AsyncAgentElement[] AsyncAgent { get; set; }

        /// <summary>
        /// Sets a default `Cache-Control: public, max-age=%d` header to all endpoints where `%d` is
        /// the conversion to seconds of any duration you write, indicating for how long the client
        /// (or CDN) can cache the content of the request. You can override this value per endpoint.
        /// Notice that KrakenD does not cache the content with this parameter, but tells the client
        /// how to do it. Defaults to `0s` (no cache). **For KrakenD cache, see [backend
        /// caching](/docs/backends/caching/)**.
        /// </summary>
        [JsonProperty("cache_ttl", NullValueHandling = NullValueHandling.Ignore)]
        public string CacheTtl { get; set; }

        /// <summary>
        /// Enables the `/__debug/` endpoint for this configuration. You can safely enable it in
        /// production.
        /// </summary>
        [JsonProperty("debug_endpoint", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DebugEndpoint { get; set; }

        /// <summary>
        /// Specifies the length of time to wait before spawning a RFC 6555 Fast Fallback connection.
        /// If zero, a default delay of 300ms is used.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("dialer_fallback_delay", NullValueHandling = NullValueHandling.Ignore)]
        public string DialerFallbackDelay { get; set; }

        /// <summary>
        /// The interval between keep-alive probes for an active network connection. If zero,
        /// keep-alive probes are sent with a default value (currently 15 seconds), if supported by
        /// the protocol and operating system. Network protocols or operating systems that do not
        /// support keep-alives ignore this field. If negative, keep-alive probes are disabled.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("dialer_keep_alive", NullValueHandling = NullValueHandling.Ignore)]
        public string DialerKeepAlive { get; set; }

        /// <summary>
        /// The timeout of the dial function for creating connections.The default is no timeout. With
        /// or without a timeout, the operating system may impose its own earlier timeout.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("dialer_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string DialerTimeout { get; set; }

        /// <summary>
        /// When true prevents requesting compression with an `Accept-Encoding: gzip` request header
        /// when the Request contains no existing Accept-Encoding value. If the Transport requests
        /// gzip on its own and gets a gzipped response, it's transparently decoded. However, if the
        /// user explicitly requested gzip it is not automatically uncompressed.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("disable_compression", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableCompression { get; set; }

        /// <summary>
        /// When true it disables HTTP keep-alives and will only use the connection to the server for
        /// a single HTTP request.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("disable_keep_alives", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableKeepAlives { get; set; }

        /// <summary>
        /// Only RESTful URL patterns are valid to access backends. Set to true if your backends
        /// aren't RESTful, e.g.: `/url.{some_variable}.json`
        /// </summary>
        [JsonProperty("disable_rest", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableRest { get; set; }

        /// <summary>
        /// Your API contract, a.k.a the list of all endpoints recognized by this gateway. The
        /// `/__health/`, `/__debug/`, and `/__stats/` endpoints are not controlled with this list.
        ///
        /// See: https://www.krakend.io/docs/endpoints/
        /// </summary>
        [JsonProperty("endpoints", NullValueHandling = NullValueHandling.Ignore)]
        public Endpoint[] Endpoints { get; set; }

        /// <summary>
        /// If non-zero, specifies the amount of time to wait for a server's first response headers
        /// after fully writing the request headers if the request has an `Expect: 100-continue`
        /// header. Zero means no timeout and causes the body to be sent immediately, without waiting
        /// for the server to approve. This time does not include the time to send the request
        /// header.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("expect_continue_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string ExpectContinueTimeout { get; set; }

        /// <summary>
        /// The optional configuration that extends the core functionality of the gateway is
        /// specified here. The `extra_config` at this level enables service components, meaning that
        /// they apply globally to all endpoints or activity.
        /// </summary>
        [JsonProperty("extra_config", NullValueHandling = NullValueHandling.Ignore)]
        public KrakendDocumentExtraConfig ExtraConfig { get; set; }

        /// <summary>
        /// The default host list for all backends if they specify none.
        /// </summary>
        [JsonProperty("host", NullValueHandling = NullValueHandling.Ignore)]
        public string[] Host { get; set; }

        /// <summary>
        /// The maximum number of idle (keep-alive) connections across all hosts. Zero means no
        /// limit.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("idle_connection_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string IdleConnectionTimeout { get; set; }

        /// <summary>
        /// The maximum amount of time to wait for the next request when keep-alives are enabled. If
        /// `idle_timeout` is zero, the value of `read_timeout` is used. If both are zero, there is
        /// no timeout.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("idle_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string IdleTimeout { get; set; }

        /// <summary>
        /// The maximum number of idle (keep-alive) connections across all hosts. Zero means no
        /// limit.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("max_idle_connections", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxIdleConnections { get; set; }

        /// <summary>
        /// If non-zero, controls the maximum idle (keep-alive) connections to keep per-host. If
        /// zero, `250` is used instead.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("max_idle_connections_per_host", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxIdleConnectionsPerHost { get; set; }

        /// <summary>
        /// Used in telemetry. A friendly name, title, date, version or any other short description
        /// that helps you recognize the configuration.
        /// </summary>
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string Name { get; set; }

        /// <summary>
        /// The encoding used to display the content to the end-user.
        ///
        /// See: https://www.krakend.io/docs/endpoints/content-types/
        /// </summary>
        [JsonProperty("output_encoding", NullValueHandling = NullValueHandling.Ignore)]
        public OutputEncoding? OutputEncoding { get; set; }

        /// <summary>
        /// Enables external plugins that are copied in a specific folder
        /// </summary>
        [JsonProperty("plugin", NullValueHandling = NullValueHandling.Ignore)]
        public Plugin Plugin { get; set; }

        /// <summary>
        /// The TCP port where KrakenD is listening to. Recommended value is in the range 1024-65535
        /// to run as an unpriviliged user
        /// </summary>
        [JsonProperty("port", NullValueHandling = NullValueHandling.Ignore)]
        public long? Port { get; set; }

        /// <summary>
        /// The amount of time allowed to read request headers. The connection's read deadline is
        /// reset after reading the headers and the Handler can decide what is considered too slow
        /// for the body.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("read_header_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string ReadHeaderTimeout { get; set; }

        /// <summary>
        /// Is the maximum duration for reading the entire request, including the body. Because
        /// `read_timeout` does not let Handlers make per-request decisions on each request body's
        /// acceptable deadline or upload rate, most users will prefer to use `read_header_timeout`.
        /// It is valid to use them both.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("read_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string ReadTimeout { get; set; }

        /// <summary>
        /// If non-zero, specifies the amount of time to wait for a server's response headers after
        /// fully writing the request (including its body, if any). This time does not include the
        /// time to read the response body.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/
        /// </summary>
        [JsonProperty("response_header_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseHeaderTimeout { get; set; }

        /// <summary>
        /// A sequential start registers all async agents in order, allowing you to have the starting
        /// logs in sequential order. A non-sequential start is much faster, but logs are harder to
        /// follow.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("sequential_start", NullValueHandling = NullValueHandling.Ignore)]
        public bool? SequentialStart { get; set; }

        /// <summary>
        /// Defines a default timeout for all endpoints. Can be overriden per endpoint.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-transport-settings/#global-timeout
        /// </summary>
        [JsonProperty("timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string Timeout { get; set; }

        [JsonProperty("tls", NullValueHandling = NullValueHandling.Ignore)]
        public TlsSsl Tls { get; set; }

        /// <summary>
        /// The syntax version tells KrakenD how to read this configuration. This is not the KrakenD
        /// version. Each KrakenD version is linked to a syntax version, and since KrakenD v2.0 the
        /// version must be `3`
        /// </summary>
        [JsonProperty("version")]
        public long Version { get; set; }

        /// <summary>
        /// Maximum duration before timing out writes of the response.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("write_timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string WriteTimeout { get; set; }
    }

    public partial class AsyncAgentClass
    {
        /// <summary>
        /// The [backend definition](/docs/backends/) (as you might have in any endpoint) indicating
        /// where the event data is sent. It is a full backend object definition, with all its
        /// possible options, transformations, filters, validations, etc.
        /// </summary>
        [JsonProperty("backend")]
        public Backends[] Backend { get; set; }

        /// <summary>
        /// A key defining all the connection settings between the agent and your messaging system.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("connection", NullValueHandling = NullValueHandling.Ignore)]
        public Connection Connection { get; set; }

        /// <summary>
        /// Defines all the settings for each agent consuming messages.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("consumer")]
        public Consumer Consumer { get; set; }

        /// <summary>
        /// Informs KrakenD how to parse the responses of your services.
        ///
        /// See: https://www.krakend.io/docs/backends/supported-encodings/
        /// </summary>
        [JsonProperty("encoding", NullValueHandling = NullValueHandling.Ignore)]
        public AsyncAgentEncoding? Encoding { get; set; }

        /// <summary>
        /// Defines the driver that connects to your queue or PubSub system. In addition, you can
        /// place other middlewares to modify the request (message) or the response, apply logic or
        /// any other endpoint middleware, but adding the driver is mandatory.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("extra_config")]
        public ExtraConfigUnion ExtraConfig { get; set; }

        /// <summary>
        /// A unique name for this agent. KrakenD shows it in the health endpoint and logs and
        /// metrics. KrakenD does not check collision names, so make sure each agent has a different
        /// name.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }
    }

    /// <summary>
    /// A backend object is an array of all the services that an endpoint connects to. It defines
    /// the list of hostnames that connects to and the URL to send or receive the data.
    /// </summary>
    public partial class Backends
    {
        /// <summary>
        /// **Only return the fields in the list**. Only the matching fields (case-sensitive) are
        /// returned in the final response. Use a dot `.` separator to define nested attributes,
        /// e.g.: `a.b` removes `{"a":{"b": true}}`
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("allow", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Allow { get; set; }

        /// <summary>
        /// **Don't return the fields in the list**. All matching fields (case-sensitive) defined in
        /// the list, are removed from the response. Use a dot `.` separator to definr nested
        /// attributes, e.g.: `a.b` removes `{"a":{"b": true}}`.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("deny", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Deny { get; set; }

        /// <summary>
        /// Set it to `true` when the host doesn't need to be checked for an HTTP protocol. This is
        /// the case of `sd=dns` or when using other protocols like `amqp://`, `nats://`, `kafka://`,
        /// etc. When set to true, and the protocol is not HTTP, KrakenD fails with an `invalid host`
        /// error.
        /// </summary>
        [JsonProperty("disable_host_sanitize", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableHostSanitize { get; set; }

        /// <summary>
        /// Defines your [needed encoding](/docs/backends/supported-encodings/) to set how to parse
        /// the response. Defaults to the value of its endpoint's `encoding`, or to `json` if not
        /// defined anywhere else.
        ///
        /// See: https://www.krakend.io/docs/backends/supported-encodings/
        /// </summary>
        [JsonProperty("encoding", NullValueHandling = NullValueHandling.Ignore)]
        public BackendEncoding? Encoding { get; set; }

        /// <summary>
        /// When there is additional configuration related to a specific component or middleware
        /// (like a circuit breaker, rate limit, etc.), it is declared under this section.
        /// </summary>
        [JsonProperty("extra_config", NullValueHandling = NullValueHandling.Ignore)]
        public BackendExtraConfiguration ExtraConfig { get; set; }

        /// <summary>
        /// Instead of placing all the response attributes in the root of the response, create a new
        /// key and encapsulate the response inside.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("group", NullValueHandling = NullValueHandling.Ignore)]
        public string Group { get; set; }

        /// <summary>
        /// An array with all the available hosts to **load balance** requests, including the schema
        /// (when possible) `schema://host:port`. E.g.: ` https://my.users-ms.com`. If you are in a
        /// platform where hosts or services are balanced (e.g., a K8S service), write a single entry
        /// in the array with the service name/balancer address. Defaults to the `host` declaration
        /// at the configuration's root level, and the service fails starting when there is none.
        /// </summary>
        [JsonProperty("host", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Host { get; set; }

        /// <summary>
        /// Set to true when your API does not return an object {} but a collection []
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("is_collection", NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsCollection { get; set; }

        /// <summary>
        /// Mapping, or also known as renaming, let you change the name of the fields of the
        /// generated responses, so your composed response would be as close to your use case as
        /// possible without changing a line on any backend.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("mapping", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Mapping { get; set; }

        /// <summary>
        /// The method sent to this backend in **uppercase**. The method does not need to match the
        /// endpoint's method. When the value is omitted, it uses the same endpoint's method.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("method", NullValueHandling = NullValueHandling.Ignore)]
        public Method? Method { get; set; }

        /// <summary>
        /// The [Service Discovery](/docs/backends/service-discovery/) system to resolve your backend
        /// services. Defaults to `static` (no external Service Discovery). Use `dns` to use DNS SRV
        /// records.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("sd", NullValueHandling = NullValueHandling.Ignore)]
        public ServiceDiscovery? Sd { get; set; }

        /// <summary>
        /// Removes the matching object from the reponse and returns only its contents.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("target", NullValueHandling = NullValueHandling.Ignore)]
        public string Target { get; set; }

        /// <summary>
        /// The path inside the service (no protocol, no host, no method). E.g: `/users`. Some
        /// functionalities under `extra_config` might drop the requirement of declaring a valid
        /// `url_pattern`, but they are exceptions. The URL must be RESTful, if it is not (e.g.:
        /// `/url.{some_variable}.json`), then see how to [disable RESTful
        /// checking](#disable-restful-checking).
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("url_pattern")]
        public string UrlPattern { get; set; }
    }

    /// <summary>
    /// When there is additional configuration related to a specific component or middleware
    /// (like a circuit breaker, rate limit, etc.), it is declared under this section.
    /// </summary>
    public partial class BackendExtraConfiguration
    {
        [JsonProperty("auth/client-credentials", NullValueHandling = NullValueHandling.Ignore)]
        public OAuth2ClientCredentials AuthClientCredentials { get; set; }

        [JsonProperty("backend/amqp/consumer", NullValueHandling = NullValueHandling.Ignore)]
        public AmqpConsumer BackendAmqpConsumer { get; set; }

        [JsonProperty("backend/amqp/producer", NullValueHandling = NullValueHandling.Ignore)]
        public AmqpProducer BackendAmqpProducer { get; set; }

        [JsonProperty("backend/graphql", NullValueHandling = NullValueHandling.Ignore)]
        public RestToGraphQl BackendGraphql { get; set; }

        [JsonProperty("backend/http", NullValueHandling = NullValueHandling.Ignore)]
        public BackendHttpUnion? BackendHttp { get; set; }

        [JsonProperty("backend/lambda", NullValueHandling = NullValueHandling.Ignore)]
        public AwsLambdaFunctions BackendLambda { get; set; }

        [JsonProperty("backend/pubsub/publisher", NullValueHandling = NullValueHandling.Ignore)]
        public BackendPubsubPublisherClass BackendPubsubPublisher { get; set; }

        [JsonProperty("backend/pubsub/subscriber", NullValueHandling = NullValueHandling.Ignore)]
        public BackendPubsubSubscriberClass BackendPubsubSubscriber { get; set; }

        [JsonProperty("modifier/lua-backend", NullValueHandling = NullValueHandling.Ignore)]
        public ModifierLuaEndpointClass ModifierLuaBackend { get; set; }

        /// <summary>
        /// Transform requests and responses through a simple DSL definition in the configuration
        /// file.
        ///
        /// See: https://www.krakend.io/docs/backends/martian/
        /// </summary>
        [JsonProperty("modifier/martian", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> ModifierMartian { get; set; }

        [JsonProperty("plugin/http-client", NullValueHandling = NullValueHandling.Ignore)]
        public HttpClientPluginsSeeHttpsWwwKrakendIoDocsExtendingInjectingPlugins PluginHttpClient { get; set; }

        [JsonProperty("plugin/req-resp-modifier", NullValueHandling = NullValueHandling.Ignore)]
        public RequestResponseModifierPlugins PluginReqRespModifier { get; set; }

        [JsonProperty("proxy", NullValueHandling = NullValueHandling.Ignore)]
        public ProxyOption Proxy { get; set; }

        [JsonProperty("qos/circuit-breaker", NullValueHandling = NullValueHandling.Ignore)]
        public CircuitBreaker QosCircuitBreaker { get; set; }

        /// <summary>
        /// Enable in-memory caching for backend responses for as long as its Cache-Control header
        /// permits.
        ///
        /// See: https://www.krakend.io/docs/backends/caching/
        /// </summary>
        [JsonProperty("qos/http-cache", NullValueHandling = NullValueHandling.Ignore)]
        public BackendCache QosHttpCache { get; set; }

        [JsonProperty("qos/ratelimit/proxy", NullValueHandling = NullValueHandling.Ignore)]
        public ProxyRatelimit QosRatelimitProxy { get; set; }

        [JsonProperty("validation/cel", NullValueHandling = NullValueHandling.Ignore)]
        public ValidationCelElement[] ValidationCel { get; set; }
    }

    /// <summary>
    /// 2-legged OAuth2 flow: Request to your authorization server an access token to reach
    /// protected resources.
    ///
    /// See: https://www.krakend.io/docs/authorization/client-credentials/
    /// </summary>
    public partial class OAuth2ClientCredentials
    {
        /// <summary>
        /// The Client ID provided to the Auth server
        ///
        /// See: https://www.krakend.io/docs/authorization/client-credentials/
        /// </summary>
        [JsonProperty("client_id")]
        public string ClientId { get; set; }

        /// <summary>
        /// The secret string provided to the Auth server.
        ///
        /// See: https://www.krakend.io/docs/authorization/client-credentials/
        /// </summary>
        [JsonProperty("client_secret")]
        public string ClientSecret { get; set; }

        /// <summary>
        /// Any additional parameters you want to include **in the payload** when requesting the
        /// token. For instance, adding the `audience` request parameter may denote the target API
        /// for which the token should be issued.
        ///
        /// See: https://www.krakend.io/docs/authorization/client-credentials/
        /// </summary>
        [JsonProperty("endpoint_params", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> EndpointParams { get; set; }

        /// <summary>
        /// A comma-separated list of scopes needed, e.g.: `scopeA,scopeB`
        ///
        /// See: https://www.krakend.io/docs/authorization/client-credentials/
        /// </summary>
        [JsonProperty("scopes", NullValueHandling = NullValueHandling.Ignore)]
        public string Scopes { get; set; }

        /// <summary>
        /// The endpoint URL where the negotiation of the token happens
        ///
        /// See: https://www.krakend.io/docs/authorization/client-credentials/
        /// </summary>
        [JsonProperty("token_url")]
        public string TokenUrl { get; set; }
    }

    /// <summary>
    /// The AMQP component allows to send and receive messages to and from a queue through the
    /// API Gateway.
    ///
    /// See: https://www.krakend.io/docs/backends/amqp-consumer/
    /// </summary>
    public partial class AmqpConsumer
    {
        /// <summary>
        /// When KrakenD retrieves the messages, regardless of the success or failure of the
        /// operation, it marks them as `ACK`nowledge.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("auto_ack", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AutoAck { get; set; }

        /// <summary>
        /// Setting to `false` is recommended to avoid deletions when the consumer is disconnected.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("delete")]
        public bool Delete { get; set; }

        /// <summary>
        /// Durable queues will survive server restarts and remain when there are no remaining
        /// consumers or bindings. `true` is recommended, but depends on the use case.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("durable")]
        public bool Durable { get; set; }

        /// <summary>
        /// The exchange name (must have a **topic** type if already exists).
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("exchange")]
        public string Exchange { get; set; }

        /// <summary>
        /// Queue name.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// The no_local flag is not supported by RabbitMQ.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("no_local", NullValueHandling = NullValueHandling.Ignore)]
        public bool? NoLocal { get; set; }

        /// <summary>
        /// When true, do not wait for the server to confirm the request and immediately begin
        /// deliveries. If it is not possible to consume, a channel exception will be raised and the
        /// channel will be closed.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("no_wait")]
        public bool NoWait { get; set; }

        /// <summary>
        /// The number of messages you want to prefetch prior to consume them.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("prefetch_count", NullValueHandling = NullValueHandling.Ignore)]
        public long? PrefetchCount { get; set; }

        /// <summary>
        /// The number of bytes you want to use to prefetch messages.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("prefetch_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? PrefetchSize { get; set; }

        /// <summary>
        /// The list of routing keys you will use to consume messages.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("routing_key")]
        public object[] RoutingKey { get; set; }
    }

    /// <summary>
    /// Send messages to a queue through the API Gateway.
    ///
    /// See: https://www.krakend.io/docs/backends/amqp-producer/
    /// </summary>
    public partial class AmqpProducer
    {
        /// <summary>
        /// false is recommended to avoid deletions when the consumer is disconnected.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("delete")]
        public bool Delete { get; set; }

        /// <summary>
        /// true is recommended, but depends on the use case. Durable queues will survive server
        /// restarts and remain when there are no remaining consumers or bindings.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("durable")]
        public bool Durable { get; set; }

        /// <summary>
        /// The exchange name (must have a topic type if already exists).
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("exchange")]
        public string Exchange { get; set; }

        /// <summary>
        /// A consumer must be connected to the queue when true. Defaults to false.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("immediate", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Immediate { get; set; }

        /// <summary>
        /// The exchange must have at least one queue bound when true. Defaults to false.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("mandatory", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Mandatory { get; set; }

        /// <summary>
        /// Queue name.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// The no_local flag is not supported by RabbitMQ.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-consumer/
        /// </summary>
        [JsonProperty("no_local", NullValueHandling = NullValueHandling.Ignore)]
        public bool? NoLocal { get; set; }

        /// <summary>
        /// When true, do not wait for the server to confirm the request and immediately begin
        /// deliveries. If it is not possible to consume, a channel exception will be raised and the
        /// channel will be closed.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("no_wait")]
        public bool NoWait { get; set; }

        /// <summary>
        /// The number of messages you want to prefetch prior to consume them.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("prefetch_count", NullValueHandling = NullValueHandling.Ignore)]
        public long? PrefetchCount { get; set; }

        /// <summary>
        /// The number of bytes you want to use to prefetch messages.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("prefetch_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? PrefetchSize { get; set; }

        /// <summary>
        /// The routing key you will use to send messages.
        ///
        /// See: https://www.krakend.io/docs/backends/amqp-producer/
        /// </summary>
        [JsonProperty("routing_key")]
        public string RoutingKey { get; set; }
    }

    public partial class RestToGraphQl
    {
        /// <summary>
        /// A meaningful and explicit name for your operation, required in multi-operation documents
        /// and for helpful debugging and server-side logging.
        ///
        /// See: https://www.krakend.io/docs/backends/graphql/
        /// </summary>
        [JsonProperty("operationName", NullValueHandling = NullValueHandling.Ignore)]
        public string OperationName { get; set; }

        /// <summary>
        /// An inline GraphQL query you want to send to the server. Use this attribute for simple and
        /// inline queries, use query_path instead for larger queries. Use escaping when needed.
        ///
        /// See: https://www.krakend.io/docs/backends/graphql/
        /// </summary>
        [JsonProperty("query", NullValueHandling = NullValueHandling.Ignore)]
        public string Query { get; set; }

        /// <summary>
        /// Path to the file containing the query. This file is loaded during startup and never
        /// checked again, if it changes KrakenD will be unaware of it.
        ///
        /// See: https://www.krakend.io/docs/backends/graphql/
        /// </summary>
        [JsonProperty("query_path", NullValueHandling = NullValueHandling.Ignore)]
        public string QueryPath { get; set; }

        /// <summary>
        /// The type of query you are declaring.
        ///
        /// See: https://www.krakend.io/docs/backends/graphql/
        /// </summary>
        [JsonProperty("type")]
        public QueryType Type { get; set; }

        /// <summary>
        /// A dictionary defining all the variables sent to the GraphQL server. You can use
        /// {placeholders} to inject parameters from the endpoint URL.
        ///
        /// See: https://www.krakend.io/docs/backends/graphql/
        /// </summary>
        [JsonProperty("variables", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Variables { get; set; }
    }

    public partial class BackendHttpClass
    {
        /// <summary>
        /// Returns to the client details of a failing request.
        ///
        /// See: https://www.krakend.io/docs/backends/detailed-errors/
        /// </summary>
        [JsonProperty("return_error_details")]
        public string ReturnErrorDetails { get; set; }
    }

    /// <summary>
    /// Invoke Amazon Lambda functions on a KrakenD endpoint call.
    ///
    /// See: https://www.krakend.io/docs/backends/lambda/
    /// </summary>
    public partial class AwsLambdaFunctions
    {
        /// <summary>
        /// An optional parameter to customize the Lambda endpoint to call. Useful when Localstack is
        /// used for testing instead of direct AWS usage.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("endpoint", NullValueHandling = NullValueHandling.Ignore)]
        public string Endpoint { get; set; }

        /// <summary>
        /// Name of the lambda function as saved in the AWS service. You have to choose between
        /// function_name and function_param_name but not both.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("function_name", NullValueHandling = NullValueHandling.Ignore)]
        public string FunctionName { get; set; }

        /// <summary>
        /// The endpoint {placeholder} that sets the function name. You have to choose between
        /// function_name and function_param_name but not both.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("function_param_name", NullValueHandling = NullValueHandling.Ignore)]
        public string FunctionParamName { get; set; }

        /// <summary>
        /// Maximum times you want to execute the function until you have a successful response.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("max_retries", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxRetries { get; set; }

        /// <summary>
        /// The AWS identifier region (e.g.: us-east-1, eu-west-2, etc.)
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("region", NullValueHandling = NullValueHandling.Ignore)]
        public string Region { get; set; }
    }

    /// <summary>
    /// Publishes to a topic using the desired driver.
    ///
    /// See: https://www.krakend.io/docs/backends/pubsub/
    /// </summary>
    public partial class BackendPubsubPublisherClass
    {
        /// <summary>
        /// Topic URL according to the selected driver
        ///
        /// See: https://www.krakend.io/docs/backends/pubsub/
        /// </summary>
        [JsonProperty("topic_url")]
        public string TopicUrl { get; set; }
    }

    /// <summary>
    /// Publishes to a topic using the desired driver.
    ///
    /// See: https://www.krakend.io/docs/backends/pubsub/
    /// </summary>
    public partial class BackendPubsubSubscriberClass
    {
        /// <summary>
        /// Topic URL according to the selected driver
        ///
        /// See: https://www.krakend.io/docs/backends/pubsub/
        /// </summary>
        [JsonProperty("topic_url")]
        public string TopicUrl { get; set; }
    }

    /// <summary>
    /// Scripting with Lua is an additional choice to extend your business logic, and is
    /// compatible with the rest of options such as CEL, Martian, or other Go plugins and
    /// middlewares.
    ///
    /// See: https://www.krakend.io/docs/endpoints/lua/
    /// </summary>
    public partial class ModifierLuaEndpointClass
    {
        /// <summary>
        /// As an efficiency point the Lua component does not load the standard libraries by default.
        /// If you need to import Lua libraries (e.g, the I/O, String, etc.), then you must set this
        /// flag to true.
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("allow_open_libs", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AllowOpenLibs { get; set; }

        /// <summary>
        /// For security and efficiency, the Lua script is loaded once into memory and not reloaded
        /// even if the file contents change. Set this flag to `true` if you want to modify the Lua
        /// script while KrakenD is running and apply the changes live (mostly during development to
        /// avoid the snippet being cached).
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("live", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Live { get; set; }

        /// <summary>
        /// The md5sum is an extra security feature to make sure that once you have coded the Lua
        /// script, the MD5 of what is loaded into memory matches what you expect and has not been
        /// tampered by a malicious 3rd party.
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("md5", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Md5 { get; set; }

        /// <summary>
        /// The Lua code that is executed **after** performing the request. Available when used in
        /// the `backend` section. You can write all the Lua code inline (e.g., `print('Hi');
        /// print('there!')` but you can also call functions that live inside one of the files under
        /// `sources` (e.g., `my_function()`).
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("post", NullValueHandling = NullValueHandling.Ignore)]
        public string Post { get; set; }

        /// <summary>
        /// The Lua code that is executed **before** performing the request. Unlike `post`, it's
        /// available in all sections. You can write all the Lua code inline (e.g., `print('Hi');
        /// print('there!')` but you can also call functions that live inside one of the files under
        /// `sources` (e.g., `my_function()`).
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("pre", NullValueHandling = NullValueHandling.Ignore)]
        public string Pre { get; set; }

        /// <summary>
        /// Available on the `backend` section only. Instead of connecting to next backend in the
        /// pipe, returns an empty response and executes the `post` lua function.
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("skip_next", NullValueHandling = NullValueHandling.Ignore)]
        public bool? SkipNext { get; set; }

        /// <summary>
        /// An array with all the Lua files that will be processed. If no path is provided (e.g.,
        /// `myfile.lua`) the file loads from the working directory (same place you loaded the
        /// configuration, usually `/etc/krakend`)
        ///
        /// See: https://www.krakend.io/docs/endpoints/lua/
        /// </summary>
        [JsonProperty("sources", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Sources { get; set; }
    }

    public partial class HttpClientPluginsSeeHttpsWwwKrakendIoDocsExtendingInjectingPlugins
    {
        /// <summary>
        /// The name of the plugin to load. Only one plugin is supported per backend.
        ///
        /// See: https://www.krakend.io/docs/extending/injecting-plugins/
        /// </summary>
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string Name { get; set; }
    }

    public partial class RequestResponseModifierPlugins
    {
        [JsonProperty("content-replacer", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, EnterpriseOnlyTheContentReplacerPluginAllowsYouToModifyTheResponseOfYourServicesByDoingLiteralReplacementsOrMoreSophisticatedReplacementsWithRegularExpressionsSeeSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsContentReplacer> ContentReplacer { get; set; }

        [JsonProperty("ip-filter", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheIpFilteringPluginAllowsYouToRestrictTheTrafficToYourApiGatewayBasedOnTheIpAddressItWorksInTwoDifferentModesAllowOrDenyWhereYouDefineTheListOfIPsCidrBlocksThatAreAuthorizedToUseTheApiOrThatAreDeniedFromUsingTheApiSeeHttpsWwwKrakendIoDocsEnterpriseThrottlingIpfilter IpFilter { get; set; }

        /// <summary>
        /// An array with the names of plugins to load. The names are defined inside your plugin.
        ///
        /// See: https://www.krakend.io/docs/extending/plugin-modifiers/
        /// </summary>
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Name { get; set; }

        [JsonProperty("response-schema-validator", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheResponseSchemaValidatorPluginAddsASchemaValidationBeforeTheGatewayReturnsTheResponseToTheEndUserOrBeforeItSMergedInTheEndpointWithTheRestOfTheBackendsSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsResponseSchemaValidator ResponseSchemaValidator { get; set; }
    }

    public partial class EnterpriseOnlyTheContentReplacerPluginAllowsYouToModifyTheResponseOfYourServicesByDoingLiteralReplacementsOrMoreSophisticatedReplacementsWithRegularExpressionsSeeSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsContentReplacer
    {
        /// <summary>
        /// The find expression or literal you want to use.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/content-replacer/
        /// </summary>
        [JsonProperty("find")]
        public string Find { get; set; }

        /// <summary>
        /// When you are passing regular expressions instead of literal values, set it to true.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/content-replacer/
        /// </summary>
        [JsonProperty("regexp", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Regexp { get; set; }

        /// <summary>
        /// The literal string or expression you want to use as a replacement.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/content-replacer/
        /// </summary>
        [JsonProperty("replace")]
        public string Replace { get; set; }
    }

    public partial class EnterpriseOnlyTheIpFilteringPluginAllowsYouToRestrictTheTrafficToYourApiGatewayBasedOnTheIpAddressItWorksInTwoDifferentModesAllowOrDenyWhereYouDefineTheListOfIPsCidrBlocksThatAreAuthorizedToUseTheApiOrThatAreDeniedFromUsingTheApiSeeHttpsWwwKrakendIoDocsEnterpriseThrottlingIpfilter
    {
        /// <summary>
        /// When true, only the matching IPs are able to access the content. When false, all matching
        /// IPs are discarded.
        ///
        /// See: https://www.krakend.io/docs/enterprise/throttling/ipfilter/
        /// </summary>
        [JsonProperty("allow")]
        public bool Allow { get; set; }

        /// <summary>
        /// The CIDR blocks (list of IPs) you want to allow or deny.
        ///
        /// See: https://www.krakend.io/docs/enterprise/throttling/ipfilter/
        /// </summary>
        [JsonProperty("CIDR")]
        public object[] Cidr { get; set; }

        /// <summary>
        /// A custom list of all headers that might contain the real IP of the client. The first
        /// matching IP in the list will be used. Default headers are (in order of checking):
        /// X-Forwarded-For, X-Real-IP, and X-Appengine-Remote-Addr.
        ///
        /// See: https://www.krakend.io/docs/enterprise/throttling/ipfilter/
        /// </summary>
        [JsonProperty("client_ip_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] ClientIpHeaders { get; set; }

        /// <summary>
        /// A custom list of all the recognized machines/balancers that proxy the client to your
        /// application. This list is used to avoid spoofing when trying to get the real IP of the
        /// client.
        ///
        /// See: https://www.krakend.io/docs/enterprise/throttling/ipfilter/
        /// </summary>
        [JsonProperty("trusted_proxies", NullValueHandling = NullValueHandling.Ignore)]
        public object[] TrustedProxies { get; set; }
    }

    public partial class EnterpriseOnlyTheResponseSchemaValidatorPluginAddsASchemaValidationBeforeTheGatewayReturnsTheResponseToTheEndUserOrBeforeItSMergedInTheEndpointWithTheRestOfTheBackendsSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsResponseSchemaValidator
    {
        /// <summary>
        /// In case the validation fails, the error definition containing body and status.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/response-schema-validator/
        /// </summary>
        [JsonProperty("error", NullValueHandling = NullValueHandling.Ignore)]
        public ErrorDefinition Error { get; set; }

        /// <summary>
        /// Write your JSON schema directly in this field, with any number of fields or validations
        /// you need.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/response-schema-validator/
        /// </summary>
        [JsonProperty("schema")]
        public Dictionary<string, object> Schema { get; set; }
    }

    /// <summary>
    /// In case the validation fails, the error definition containing body and status.
    ///
    /// See: https://www.krakend.io/docs/enterprise/endpoints/response-schema-validator/
    /// </summary>
    public partial class ErrorDefinition
    {
        /// <summary>
        /// The error message you want to show when the validation fails. Set it to an empty string
        /// `""` to show the JSON-schema validation error.
        /// </summary>
        [JsonProperty("body", NullValueHandling = NullValueHandling.Ignore)]
        public string Body { get; set; }

        /// <summary>
        /// The HTTP status code you want to set back in the response.
        /// </summary>
        [JsonProperty("status", NullValueHandling = NullValueHandling.Ignore)]
        public long? Status { get; set; }
    }

    public partial class ProxyOption
    {
        /// <summary>
        /// The list of operations to **execute sequentially** (top down). Every operation is defined
        /// with an object containing two properties:
        ///
        /// See: https://www.krakend.io/docs/backends/flatmap/
        /// </summary>
        [JsonProperty("flatmap_filter", NullValueHandling = NullValueHandling.Ignore)]
        public FlatmapOperation[] FlatmapFilter { get; set; }

        /// <summary>
        /// Mark this backend as a shadow backend. Sending copies of the traffic but ignore its
        /// responses.
        ///
        /// See: https://www.krakend.io/docs/backends/shadow-backends/
        /// </summary>
        [JsonProperty("shadow", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Shadow { get; set; }
    }

    /// <summary>
    /// The flatmap middleware allows you to manipulate collections (or arrays, or lists, you
    /// name it) from the backend response. While the basic manipulation operations allow you to
    /// work directly with objects, the collections require a different approach: the flatmap
    /// component.
    ///
    /// See: https://www.krakend.io/docs/backend/flatmap/
    /// </summary>
    public partial class FlatmapOperation
    {
        /// <summary>
        /// The arguments passed to the operation.
        ///
        /// See: https://www.krakend.io/docs/backends/flatmap/
        /// </summary>
        [JsonProperty("args")]
        public object[] Args { get; set; }

        /// <summary>
        /// The types of operations are defined as follows.
        ///
        /// See: https://www.krakend.io/docs/backends/flatmap/
        /// </summary>
        [JsonProperty("type")]
        public TypeEnum Type { get; set; }
    }

    /// <summary>
    /// The circuit breaker prevents sending more traffic to a failing backend.
    ///
    /// See: https://www.krakend.io/docs/backends/circuit-breaker/
    /// </summary>
    public partial class CircuitBreaker
    {
        /// <summary>
        /// Time window where the errors count, in seconds.
        ///
        /// See: https://www.krakend.io/docs/backends/circuit-breaker/
        /// </summary>
        [JsonProperty("interval")]
        public long Interval { get; set; }

        /// <summary>
        /// Whether to log the changes of state of this circuit breaker or not.
        ///
        /// See: https://www.krakend.io/docs/backends/circuit-breaker/
        /// </summary>
        [JsonProperty("log_status_change", NullValueHandling = NullValueHandling.Ignore)]
        public bool? LogStatusChange { get; set; }

        /// <summary>
        /// The consecutive number of errors within the `interval` window to consider the backend
        /// unhealthy. An error is any response without a success (20x) status code or no response.
        ///
        /// See: https://www.krakend.io/docs/backends/circuit-breaker/
        /// </summary>
        [JsonProperty("max_errors")]
        public long MaxErrors { get; set; }

        /// <summary>
        /// A friendly name to follow this circuit breaker's activity in the logs.
        ///
        /// See: https://www.krakend.io/docs/backends/circuit-breaker/
        /// </summary>
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string Name { get; set; }

        /// <summary>
        /// For how many seconds the circuit breaker will wait before testing again if the backend is
        /// healthy.
        ///
        /// See: https://www.krakend.io/docs/backends/circuit-breaker/
        /// </summary>
        [JsonProperty("timeout")]
        public long Timeout { get; set; }
    }

    /// <summary>
    /// Enable in-memory caching for backend responses for as long as its Cache-Control header
    /// permits.
    ///
    /// See: https://www.krakend.io/docs/backends/caching/
    /// </summary>
    public partial class BackendCache
    {
        /// <summary>
        /// The `shared` cache makes that different backend definitions with this flag enabled can
        /// reuse the cache. When the `shared` flag is missing or set to false, the backend uses its
        /// own cache private context.
        ///
        /// See: https://www.krakend.io/docs/backends/detailed-errors/
        /// </summary>
        [JsonProperty("shared", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Shared { get; set; }
    }

    /// <summary>
    /// Restrict the rate of requests KrakenD makes to your backends.
    ///
    /// See: https://www.krakend.io/docs/backends/rate-limit/
    /// </summary>
    public partial class ProxyRatelimit
    {
        /// <summary>
        /// The capacity according to the [token bucket algorithm](/docs/throttling/token-bucket/)
        /// with a `bucket capacity == tokens added per second` so KrakenD is able to allow some
        /// bursting on the request rates. Recommended value is `capacity == max_rate`.
        ///
        /// See: https://www.krakend.io/docs/backends/rate-limit/
        /// </summary>
        [JsonProperty("capacity")]
        public long Capacity { get; set; }

        /// <summary>
        /// Maximum requests per second you want to accept in this backend.
        ///
        /// See: https://www.krakend.io/docs/backends/rate-limit/
        /// </summary>
        [JsonProperty("max_rate")]
        public double MaxRate { get; set; }
    }

    /// <summary>
    /// The Common Expression Language (CEL) middleware enables expression evaluation, when an
    /// expression returns false, KrakenD does not return the content as the condition has
    /// failed. Otherwise, if all expressions returned true, the content is served.
    ///
    /// See: https://www.krakend.io/docs/endpoints/common-expression-language-cel/
    /// </summary>
    public partial class ValidationCelElement
    {
        /// <summary>
        /// The expression that evaluates as a boolean, you can write here any conditional. If the
        /// result of the expression is `true`, the execution continues. See in the docs how to use
        /// additional variables to retrieve data from requests, responses, and tokens.
        ///
        /// See: https://www.krakend.io/docs/endpoints/common-expression-language-cel/
        /// </summary>
        [JsonProperty("check_expr")]
        public string CheckExpr { get; set; }
    }

    /// <summary>
    /// A key defining all the connection settings between the agent and your messaging system.
    ///
    /// See: https://www.krakend.io/docs/async/
    /// </summary>
    public partial class Connection
    {
        /// <summary>
        /// When the connection to your event source gets interrupted for whatever reason, KrakenD
        /// keeps trying to reconnect until it succeeds or until it reaches the `max_retries`. The
        /// backoff strategy defines the delay in seconds in between consecutive failed retries.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("backoff_strategy", NullValueHandling = NullValueHandling.Ignore)]
        public BackoffStrategy? BackoffStrategy { get; set; }

        /// <summary>
        /// The time between pings checking that the agent is connected to the queue and alive.
        /// Regardless of the health interval, if an agent fails, KrakenD will restart it again
        /// immediately as defined by `max_retries`and `backoff_strategy`.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("health_interval", NullValueHandling = NullValueHandling.Ignore)]
        public string HealthInterval { get; set; }

        /// <summary>
        /// The maximum number of times you will allow KrakenD to retry reconnecting to a broken
        /// messaging system. Use 0 for unlimited retries.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("max_retries", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxRetries { get; set; }
    }

    public partial class ConsumerClass
    {
        /// <summary>
        /// The maximum number of messages you allow each worker to consume per second. Use any of
        /// `0` or `-1` for unlimited speed.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("max_rate", NullValueHandling = NullValueHandling.Ignore)]
        public double? MaxRate { get; set; }

        /// <summary>
        /// The maximum time the agent will wait to process an event sent to the backend. If the
        /// backend fails to process it, the message is reinserted for later consumption. Defaults to
        /// the timeout in the root level, or to `2s` if no value is declared.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string Timeout { get; set; }

        /// <summary>
        /// The topic name you want to consume. The syntax depends on the driver. Examples for AMQP:
        /// `*`, `mytopic`, `lazy.#`, `*`, `foo.*`.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("topic")]
        public string Topic { get; set; }

        /// <summary>
        /// The number of workers (consuming processes) you want to start simultaneously for this
        /// agent.
        ///
        /// See: https://www.krakend.io/docs/async/
        /// </summary>
        [JsonProperty("workers", NullValueHandling = NullValueHandling.Ignore)]
        public long? Workers { get; set; }
    }

    public partial class ExtraConfigClass
    {
        /// <summary>
        /// See the configuration for async/amqp
        /// </summary>
        [JsonProperty("async/amqp")]
        public AsyncAmqpDriver AsyncAmqp { get; set; }
    }

    /// <summary>
    /// See the configuration for async/amqp
    ///
    /// The Async AMQP component enables the AMQP driver for the Async functionality.
    ///
    /// See: https://www.krakend.io/docs/async/amqp/
    /// </summary>
    public partial class AsyncAmqpDriver
    {
        /// <summary>
        /// When KrakenD retrieves the messages, regardless of the success or failure of the
        /// operation, it marks them as ACK. Defaults to false.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("auto_ack", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AutoAck { get; set; }

        /// <summary>
        /// false is recommended to avoid deletions when the consumer is disconnected.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("delete", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Delete { get; set; }

        /// <summary>
        /// true is recommended, but depends on the use case. Durable queues will survive server
        /// restarts and remain when there are no remaining consumers or bindings.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("durable", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Durable { get; set; }

        /// <summary>
        /// The exchange name (must have a topic type if already exists).
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("exchange")]
        public string Exchange { get; set; }

        /// <summary>
        /// true if only this consumer can access the queue.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("exclusive", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Exclusive { get; set; }

        /// <summary>
        /// The connection string, ends in slash.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("host")]
        public string Host { get; set; }

        /// <summary>
        /// Queue name.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// The no_local flag is not supported by RabbitMQ.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("no_local", NullValueHandling = NullValueHandling.Ignore)]
        public bool? NoLocal { get; set; }

        /// <summary>
        /// When true, do not wait for the server to confirm the request and immediately begin
        /// deliveries. If it is not possible to consume, a channel exception will be raised and the
        /// channel will be closed.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("no_wait", NullValueHandling = NullValueHandling.Ignore)]
        public bool? NoWait { get; set; }

        /// <summary>
        /// The number of messages you want to prefetch prior to consume them.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("prefetch_count", NullValueHandling = NullValueHandling.Ignore)]
        public long? PrefetchCount { get; set; }

        /// <summary>
        /// The number of bytes you want to use to prefetch messages.
        ///
        /// See: https://www.krakend.io/docs/async/amqp/
        /// </summary>
        [JsonProperty("prefetch_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? PrefetchSize { get; set; }
    }

    public partial class Endpoint
    {
        /// <summary>
        /// The exact string resource URL you want to expose. You can use `{placeholders}` to use
        /// variables when needed. URLs do not support colons `:` in their definition. Endpoints
        /// should start with slash `/`. Example: `/foo/{var}`. If `{vars}` are placed in the middle
        /// words, like in `/var{iable}` you must set in the root level `disable_rest` strict
        /// checking.
        ///
        /// See: https://www.krakend.io/docs/endpoints/
        /// </summary>
        [JsonProperty("endpoint")]
        public string EndpointEndpoint { get; set; }

        /// <summary>
        /// List of all the [backend objects](/docs/backends/) queried for this endpoint
        /// </summary>
        [JsonProperty("backend")]
        public Backend[] Backend { get; set; }

        /// <summary>
        /// Sets or overrides the cache headers to inform for how long the client or CDN can cache
        /// the request to this endpoint. Related: [caching backend
        /// responses](/docs/backends/caching/).
        /// </summary>
        [JsonProperty("cache_ttl", NullValueHandling = NullValueHandling.Ignore)]
        public string CacheTtl { get; set; }

        /// <summary>
        /// The concurrent requests are an excellent technique to improve the response times and
        /// decrease error rates by requesting in parallel the same information multiple times. Yes,
        /// you make the same request to several backends instead of asking to just one. When the
        /// first backend returns the information, the remaining requests are canceled.
        ///
        /// See: https://www.krakend.io/docs/endpoints/concurrent-requests/
        /// </summary>
        [JsonProperty("concurrent_calls", NullValueHandling = NullValueHandling.Ignore)]
        public long? ConcurrentCalls { get; set; }

        /// <summary>
        /// Configuration entries for additional components that are executed within this endpoint,
        /// during the request, response or merge operations.
        /// </summary>
        [JsonProperty("extra_config", NullValueHandling = NullValueHandling.Ignore)]
        public EndpointExtraConfig ExtraConfig { get; set; }

        /// <summary>
        /// Defines the list of all headers allowed to reach the backend when passed.
        /// By default, KrakenD won't pass any header from the client to the backend. See [headers
        /// forwarding](/docs/endpoints/parameter-forwarding/#headers-forwarding)
        /// </summary>
        [JsonProperty("input_headers", NullValueHandling = NullValueHandling.Ignore)]
        public string[] InputHeaders { get; set; }

        /// <summary>
        /// Defines the exact list of quey strings parameters that are allowed to reach the backend.
        /// By default, KrakenD won't pass any query string to the backend.
        ///
        /// See: https://www.krakend.io/docs/endpoints/parameter-forwarding/
        /// </summary>
        [JsonProperty("input_query_strings", NullValueHandling = NullValueHandling.Ignore)]
        public string[] InputQueryStrings { get; set; }

        /// <summary>
        /// The method supported by this endpoint. Create multiple endpoint entries if you need
        /// different methods.
        ///
        /// See: https://www.krakend.io/docs/endpoints/
        /// </summary>
        [JsonProperty("method", NullValueHandling = NullValueHandling.Ignore)]
        public Method? Method { get; set; }

        /// <summary>
        /// The gateway can work with several content types, even allowing your clients to choose how
        /// to consume the content. See the [supported encodings](/docs/endpoints/content-types/)
        /// </summary>
        [JsonProperty("output_encoding", NullValueHandling = NullValueHandling.Ignore)]
        public OutputEncoding? OutputEncoding { get; set; }

        /// <summary>
        /// The duration you write in the timeout represents the **whole duration of the pipe**, so
        /// it counts the time all your backends take to respond and the processing of all the
        /// components involved in the endpoint (the request, fetching data, manipulation, etc.).
        /// Usually specified in seconds (`s`) or milliseconds (`ms`. e.g.: `2000ms` or `2s`). If you
        /// don't set any timeout, the timeout is taken from the entry in the service level, or to
        /// the system's default
        /// </summary>
        [JsonProperty("timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string Timeout { get; set; }
    }

    /// <summary>
    /// A backend object is an array of all the services that an endpoint connects to. It defines
    /// the list of hostnames that connects to and the URL to send or receive the data.
    /// </summary>
    public partial class Backend
    {
        /// <summary>
        /// The path inside the service (no protocol, no host, no method). E.g: `/users`. Some
        /// functionalities under `extra_config` might drop the requirement of declaring a valid
        /// `url_pattern`, but they are exceptions. The URL must be RESTful, if it is not (e.g.:
        /// `/url.{some_variable}.json`), then see how to [disable RESTful
        /// checking](#disable-restful-checking).
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("url_pattern")]
        public string UrlPattern { get; set; }

        /// <summary>
        /// An array with all the available hosts to **load balance** requests, including the schema
        /// (when possible) `schema://host:port`. E.g.: ` https://my.users-ms.com`. If you are in a
        /// platform where hosts or services are balanced (e.g., a K8S service), write a single entry
        /// in the array with the service name/balancer address. Defaults to the `host` declaration
        /// at the configuration's root level, and the service fails starting when there is none.
        /// </summary>
        [JsonProperty("host", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Host { get; set; }

        /// <summary>
        /// **Only return the fields in the list**. Only the matching fields (case-sensitive) are
        /// returned in the final response. Use a dot `.` separator to define nested attributes,
        /// e.g.: `a.b` removes `{"a":{"b": true}}`
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("allow", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Allow { get; set; }

        /// <summary>
        /// **Don't return the fields in the list**. All matching fields (case-sensitive) defined in
        /// the list, are removed from the response. Use a dot `.` separator to definr nested
        /// attributes, e.g.: `a.b` removes `{"a":{"b": true}}`.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("deny", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Deny { get; set; }

        /// <summary>
        /// Set it to `true` when the host doesn't need to be checked for an HTTP protocol. This is
        /// the case of `sd=dns` or when using other protocols like `amqp://`, `nats://`, `kafka://`,
        /// etc. When set to true, and the protocol is not HTTP, KrakenD fails with an `invalid host`
        /// error.
        /// </summary>
        [JsonProperty("disable_host_sanitize", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableHostSanitize { get; set; }

        /// <summary>
        /// Defines your [needed encoding](/docs/backends/supported-encodings/) to set how to parse
        /// the response. Defaults to the value of its endpoint's `encoding`, or to `json` if not
        /// defined anywhere else.
        ///
        /// See: https://www.krakend.io/docs/backends/supported-encodings/
        /// </summary>
        [JsonProperty("encoding", NullValueHandling = NullValueHandling.Ignore)]
        public BackendEncoding? Encoding { get; set; }

        /// <summary>
        /// When there is additional configuration related to a specific component or middleware
        /// (like a circuit breaker, rate limit, etc.), it is declared under this section.
        /// </summary>
        [JsonProperty("extra_config", NullValueHandling = NullValueHandling.Ignore)]
        public BackendExtraConfiguration ExtraConfig { get; set; }

        /// <summary>
        /// Instead of placing all the response attributes in the root of the response, create a new
        /// key and encapsulate the response inside.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("group", NullValueHandling = NullValueHandling.Ignore)]
        public string Group { get; set; }

        /// <summary>
        /// Set to true when your API does not return an object {} but a collection []
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("is_collection", NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsCollection { get; set; }

        /// <summary>
        /// Mapping, or also known as renaming, let you change the name of the fields of the
        /// generated responses, so your composed response would be as close to your use case as
        /// possible without changing a line on any backend.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("mapping", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Mapping { get; set; }

        /// <summary>
        /// The method sent to this backend in **uppercase**. The method does not need to match the
        /// endpoint's method. When the value is omitted, it uses the same endpoint's method.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("method", NullValueHandling = NullValueHandling.Ignore)]
        public Method? Method { get; set; }

        /// <summary>
        /// The [Service Discovery](/docs/backends/service-discovery/) system to resolve your backend
        /// services. Defaults to `static` (no external Service Discovery). Use `dns` to use DNS SRV
        /// records.
        ///
        /// See: https://www.krakend.io/docs/backends/
        /// </summary>
        [JsonProperty("sd", NullValueHandling = NullValueHandling.Ignore)]
        public ServiceDiscovery? Sd { get; set; }

        /// <summary>
        /// Removes the matching object from the reponse and returns only its contents.
        ///
        /// See: https://www.krakend.io/docs/backends/data-manipulation/
        /// </summary>
        [JsonProperty("target", NullValueHandling = NullValueHandling.Ignore)]
        public string Target { get; set; }
    }

    /// <summary>
    /// Configuration entries for additional components that are executed within this endpoint,
    /// during the request, response or merge operations.
    /// </summary>
    public partial class EndpointExtraConfig
    {
        /// <summary>
        /// Enterprise only. Validates that users of this endpoint pass a valid API-key containing
        /// one of the declared roles.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("auth/api-keys", NullValueHandling = NullValueHandling.Ignore)]
        public ApiKeyValidation AuthApiKeys { get; set; }

        [JsonProperty("auth/signer", NullValueHandling = NullValueHandling.Ignore)]
        public JwtSigner AuthSigner { get; set; }

        [JsonProperty("auth/validator", NullValueHandling = NullValueHandling.Ignore)]
        public JwtValidator AuthValidator { get; set; }

        [JsonProperty("documentation/openapi", NullValueHandling = NullValueHandling.Ignore)]
        public GenerateDocumentationUsingOpenApi DocumentationOpenapi { get; set; }

        /// <summary>
        /// The JMESPath query language allows you to select, slice, filter, map, project, flatten,
        /// sort, and all sorts of operations on data.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/jmespath/
        /// </summary>
        [JsonProperty("modifier/jmespath", NullValueHandling = NullValueHandling.Ignore)]
        public JmesPathResponseManipulationWithQueryLanguage ModifierJmespath { get; set; }

        [JsonProperty("modifier/lua-endpoint", NullValueHandling = NullValueHandling.Ignore)]
        public ModifierLuaEndpointClass ModifierLuaEndpoint { get; set; }

        [JsonProperty("modifier/lua-proxy", NullValueHandling = NullValueHandling.Ignore)]
        public ModifierLuaEndpointClass ModifierLuaProxy { get; set; }

        [JsonProperty("plugin/req-resp-modifier", NullValueHandling = NullValueHandling.Ignore)]
        public RequestResponseModifierPlugins PluginReqRespModifier { get; set; }

        [JsonProperty("proxy", NullValueHandling = NullValueHandling.Ignore)]
        public Proxy Proxy { get; set; }

        /// <summary>
        /// The router rate limit feature allows you to set a number of maximum requests per second a
        /// KrakenD endpoint will accept.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("qos/ratelimit/router", NullValueHandling = NullValueHandling.Ignore)]
        public RateLimiting QosRatelimitRouter { get; set; }

        [JsonProperty("security/bot-detector", NullValueHandling = NullValueHandling.Ignore)]
        public BotDetector SecurityBotDetector { get; set; }

        [JsonProperty("security/cors", NullValueHandling = NullValueHandling.Ignore)]
        public SecurityCorsClass SecurityCors { get; set; }

        [JsonProperty("security/http", NullValueHandling = NullValueHandling.Ignore)]
        public SecurityHttpClass SecurityHttp { get; set; }

        [JsonProperty("validation/cel", NullValueHandling = NullValueHandling.Ignore)]
        public ValidationCelElement[] ValidationCel { get; set; }

        /// <summary>
        /// apply automatic validations using the JSON Schema vocabulary before the content passes to
        /// the backends. The json schema component allows you to define validation rules on the
        /// body, type definition, or even validate the fields' values.
        ///
        /// See: https://www.krakend.io/docs/endpoints/json-schema/
        /// </summary>
        [JsonProperty("validation/json-schema", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> ValidationJsonSchema { get; set; }

        [JsonProperty("websocket", NullValueHandling = NullValueHandling.Ignore)]
        public SchemaDefinitionForWebsockets Websocket { get; set; }
    }

    /// <summary>
    /// Enterprise only. Validates that users of this endpoint pass a valid API-key containing
    /// one of the declared roles.
    ///
    /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
    /// </summary>
    public partial class ApiKeyValidation
    {
        /// <summary>
        /// If you want to limit the endpoint usage to this specific user at a number of requests per
        /// second. Exceeding the number of requests per second will give the client a `429 Too Many
        /// Requests` HTTP status code.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("client_max_rate", NullValueHandling = NullValueHandling.Ignore)]
        public double? ClientMaxRate { get; set; }

        /// <summary>
        /// The list of roles allowed to access the endpoint. Values must match (case sensitive)
        /// definitions in the `keys` section at the service level of `auth/api-keys`. API Keys not
        /// having the right role, or unauthenticated requests, will receive a `401 Unauthorized`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("roles", NullValueHandling = NullValueHandling.Ignore)]
        public string[] Roles { get; set; }
    }

    /// <summary>
    /// creates a wrapper for your login endpoint that signs with your secret key the selected
    /// fields of the backend payload right before returning the content to the end-user.
    ///
    /// See: https://www.krakend.io/docs/authorization/jwt-signing/
    /// </summary>
    public partial class JwtSigner
    {
        /// <summary>
        /// The hashing algorithm used by the issuer. Usually `RS256`. The algorithm you choose
        /// directly affects the CPU consumption.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("alg")]
        public Algorithm Alg { get; set; }

        /// <summary>
        /// Override the default cipher suites (see [JWT
        /// validation](/docs/authorization/jwt-validation/)). Unless you have a legacy JWK, **you
        /// don't need to set this value**.
        /// </summary>
        [JsonProperty("cipher_suites", NullValueHandling = NullValueHandling.Ignore)]
        public long[] CipherSuites { get; set; }

        /// <summary>
        /// The cyphering key.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("cypher_key", NullValueHandling = NullValueHandling.Ignore)]
        public string CypherKey { get; set; }

        /// <summary>
        /// Disables HTTP security of the JWK client and allows insecure connections (plain HTTP) to
        /// download the keys. The flag should be `false` when you use HTTPS, and `true` when using
        /// plain HTTP or loading the key from a local file.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("disable_jwk_security", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableJwkSecurity { get; set; }

        /// <summary>
        /// Use JSON format instead of the compact form JWT provides.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("full", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Full { get; set; }

        /// <summary>
        /// A list of fingerprints (the unique identifier of the certificate) for certificate pinning
        /// and avoid man in the middle attacks. Add fingerprints in base64 format.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-signing/
        /// </summary>
        [JsonProperty("jwk_fingerprints", NullValueHandling = NullValueHandling.Ignore)]
        public object[] JwkFingerprints { get; set; }

        /// <summary>
        /// Path to the CA’s certificate verifying a secure connection when downloading the JWK. Use
        /// when not recognized by the system (e.g., self-signed certificates).
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("jwk_local_ca", NullValueHandling = NullValueHandling.Ignore)]
        public string JwkLocalCa { get; set; }

        /// <summary>
        /// Local path to the JWK public keys, has preference over `jwk_url`. Instead of pointing to
        /// an external URL (with `jwk_url`), public keys are kept locally, in a plain JWK file
        /// (security alert!), or encrypted. When encrypted, also add `secret_url` and `cypher_key`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("jwk_local_path", NullValueHandling = NullValueHandling.Ignore)]
        public string JwkLocalPath { get; set; }

        /// <summary>
        /// The URL to the JWK endpoint with the private keys used to sign the token.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-signing/
        /// </summary>
        [JsonProperty("jwk_url", NullValueHandling = NullValueHandling.Ignore)]
        public string JwkUrl { get; set; }

        /// <summary>
        /// List of all the specific keys that need signing (e.g., `refresh_token` and
        /// `access_token`).
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-signing/
        /// </summary>
        [JsonProperty("keys_to_sign")]
        public object[] KeysToSign { get; set; }

        /// <summary>
        /// The key ID purpose is to match a specific key, as the jwk_url might contain several
        /// keys.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("kid")]
        public string Kid { get; set; }

        /// <summary>
        /// An URL with a custom scheme using one of the supported providers (e.g.: `awskms://keyID`)
        /// ([see
        /// providers](/docs/authorization/jwt-validation/#accepted-providers-for-encrypting-payloads)).
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("secret_url", NullValueHandling = NullValueHandling.Ignore)]
        public string SecretUrl { get; set; }
    }

    /// <summary>
    /// Protect endpoints from public usage by validating JWT tokens generated by any
    /// industry-standard OpenID Connect (OIDC) integration.
    ///
    /// See: https://www.krakend.io/docs/authorization/jwt-validation/
    /// </summary>
    public partial class JwtValidator
    {
        /// <summary>
        /// The hashing algorithm used by the token issuer.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("alg")]
        public Algorithm Alg { get; set; }

        /// <summary>
        /// Reject tokens that do not contain ALL audiences declared in the list.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("audience", NullValueHandling = NullValueHandling.Ignore)]
        public string[] Audience { get; set; }

        /// <summary>
        /// Set this value to `true` (recommended) to stop downloading keys on every request and
        /// store them in memory for the next `cache_duration` period and avoid hammering the key
        /// server, as recommended for performance. Do not use this flag when using `jwk_local_ca`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("cache", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Cache { get; set; }

        /// <summary>
        /// The cache duration when the `cache` is enabled. Value in seconds, defaults to 15
        /// minutes.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("cache_duration", NullValueHandling = NullValueHandling.Ignore)]
        public long? CacheDuration { get; set; }

        /// <summary>
        /// Override the default cipher suites. Use it if you want to enforce an even higher security
        /// standard.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("cipher_suites", NullValueHandling = NullValueHandling.Ignore)]
        public long[] CipherSuites { get; set; }

        /// <summary>
        /// Add the key name of the cookie containing the token when it is not passed in the headers
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("cookie_key", NullValueHandling = NullValueHandling.Ignore)]
        public string CookieKey { get; set; }

        /// <summary>
        /// The cyphering key.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("cypher_key", NullValueHandling = NullValueHandling.Ignore)]
        public string CypherKey { get; set; }

        /// <summary>
        /// When true, disables security of the JWK client and allows insecure connections (plain
        /// HTTP) to download the keys. Useful for development environments.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("disable_jwk_security", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableJwkSecurity { get; set; }

        /// <summary>
        /// When set, tokens not matching the issuer are rejected.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("issuer", NullValueHandling = NullValueHandling.Ignore)]
        public string Issuer { get; set; }

        /// <summary>
        /// A list of fingerprints (the certificate's unique identifier) for certificate pinning and
        /// avoid man-in-the-middle attacks. Add fingerprints in base64 format.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("jwk_fingerprints", NullValueHandling = NullValueHandling.Ignore)]
        public string[] JwkFingerprints { get; set; }

        /// <summary>
        /// Path to the CA's certificate verifying a secure connection when downloading the JWK. Use
        /// when not recognized by the system (e.g., self-signed certificates).
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("jwk_local_ca", NullValueHandling = NullValueHandling.Ignore)]
        public string JwkLocalCa { get; set; }

        /// <summary>
        /// Local path to the JWK public keys, has preference over `jwk_url`. Instead of pointing to
        /// an external URL (with `jwk_url`), public keys are kept locally, in a plain JWK file
        /// (security alert!), or encrypted. When encrypted, also add `secret_url` and `cypher_key`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("jwk_local_path", NullValueHandling = NullValueHandling.Ignore)]
        public string JwkLocalPath { get; set; }

        /// <summary>
        /// The URL to the JWK endpoint with the public keys used to verify the token's authenticity
        /// and integrity.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("jwk_url", NullValueHandling = NullValueHandling.Ignore)]
        public string JwkUrl { get; set; }

        /// <summary>
        /// Allows strategies other than `kid` to load keys.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("key_identify_strategy", NullValueHandling = NullValueHandling.Ignore)]
        public KeyIdentifyStrategy? KeyIdentifyStrategy { get; set; }

        /// <summary>
        /// When `true`, any JWT **validation operation** gets printed in the log with a level
        /// `ERROR`. You will see if a client does not have sufficient roles, the allowed claims,
        /// scopes, and other useful information.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("operation_debug", NullValueHandling = NullValueHandling.Ignore)]
        public bool? OperationDebug { get; set; }

        /// <summary>
        /// Enables passing claims in the backend's request header. You can pass nested claims using
        /// the dot `.` operator. E.g.: `realm_access.roles`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("propagate_claims", NullValueHandling = NullValueHandling.Ignore)]
        public string[][] PropagateClaims { get; set; }

        /// <summary>
        /// When set, the JWT token not having at least one of the listed roles is rejected.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("roles", NullValueHandling = NullValueHandling.Ignore)]
        public string[] Roles { get; set; }

        /// <summary>
        /// When validating users through roles, provide the key name inside the JWT payload that
        /// lists their roles. If this key is nested inside another object, add `roles_key_is_nested`
        /// and use the dot notation `.` to traverse each level. E.g.:
        /// `resource_access.myclient.roles` represents the payload `{resource_access: { myclient: {
        /// roles: ["myrole"] } }`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("roles_key", NullValueHandling = NullValueHandling.Ignore)]
        public string RolesKey { get; set; }

        /// <summary>
        /// If the roles key uses a nested object using the `.` dot notation, you must set it to
        /// `true` to traverse the object.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("roles_key_is_nested", NullValueHandling = NullValueHandling.Ignore)]
        public bool? RolesKeyIsNested { get; set; }

        /// <summary>
        /// A list of scopes to validate. Make sure to use a list `[]` in the config, but when
        /// passing the token, the scopes should be separated by spaces, e.g.: `"my_scopes":
        /// "resource1:action1 resource3:action7"`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("scopes", NullValueHandling = NullValueHandling.Ignore)]
        public string[] Scopes { get; set; }

        /// <summary>
        /// The key name where KrakenD can find the scopes. The key can be a nested object using the
        /// `.` dot notation, e.g.: `data.access.my_scopes`.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("scopes_key", NullValueHandling = NullValueHandling.Ignore)]
        public string ScopesKey { get; set; }

        /// <summary>
        /// Defines if the user needs to have in its token at least one of the listed claims (`any`),
        /// or `all` of them.
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("scopes_matcher", NullValueHandling = NullValueHandling.Ignore)]
        public ScopesMatcher? ScopesMatcher { get; set; }

        /// <summary>
        /// An URL with a custom scheme using one of the supported providers (e.g.: `awskms://keyID`)
        /// (see providers).
        ///
        /// See: https://www.krakend.io/docs/authorization/jwt-validation/
        /// </summary>
        [JsonProperty("secret_url", NullValueHandling = NullValueHandling.Ignore)]
        public string SecretUrl { get; set; }
    }

    /// <summary>
    /// Enterprise only. Generates OpenAPI documentation automatically through `krakend generate
    /// openapi` command.
    ///
    /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
    /// </summary>
    public partial class GenerateDocumentationUsingOpenApi
    {
        /// <summary>
        /// The list of audiences that will consume this endpoint. These values **do not define the
        /// gateway logic** in any way. They are a way to group endpoints and filter them out when
        /// generating the OpenAPI documentation.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("audience", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Audience { get; set; }

        /// <summary>
        /// A starting path that is appended to any endpoint.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("base_path", NullValueHandling = NullValueHandling.Ignore)]
        public string BasePath { get; set; }

        /// <summary>
        /// Email where users of your API can write to.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("contact_email", NullValueHandling = NullValueHandling.Ignore)]
        public string ContactEmail { get; set; }

        /// <summary>
        /// Contact name.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("contact_name", NullValueHandling = NullValueHandling.Ignore)]
        public string ContactName { get; set; }

        /// <summary>
        /// Contact URL that users of your API can read.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("contact_url", NullValueHandling = NullValueHandling.Ignore)]
        public string ContactUrl { get; set; }

        /// <summary>
        /// Description in [CommonMark](http://commonmark.org/help/) or HTML.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("description", NullValueHandling = NullValueHandling.Ignore)]
        public string Description { get; set; }

        /// <summary>
        /// A free form JSON object or a string you would like to show as a sample response of the
        /// endpoint. The examples assume they are JSON content types except when using the
        /// `output_encoding=string`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("example", NullValueHandling = NullValueHandling.Ignore)]
        public Example? Example { get; set; }

        /// <summary>
        /// The hostname where you will publish your API.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("host", NullValueHandling = NullValueHandling.Ignore)]
        public string Host { get; set; }

        /// <summary>
        /// The license name (e.g.: Apache License)
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("license_name", NullValueHandling = NullValueHandling.Ignore)]
        public string LicenseName { get; set; }

        /// <summary>
        /// The URL where the license is hosted
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("license_url", NullValueHandling = NullValueHandling.Ignore)]
        public string LicenseUrl { get; set; }

        /// <summary>
        /// The list of schemes supported by the API, e.g. `http` or `https`
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("schemes", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Schemes { get; set; }

        /// <summary>
        /// A short summary for the endpoint.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("summary", NullValueHandling = NullValueHandling.Ignore)]
        public string Summary { get; set; }

        /// <summary>
        /// You can assign a list of tags to each API operation. Tagged operations may be handled
        /// differently by tools and libraries. For example, Swagger UI uses tags to group the
        /// displayed operations.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("tags", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Tags { get; set; }

        /// <summary>
        /// The URL to the terms of service for using this API.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("terms_of_service", NullValueHandling = NullValueHandling.Ignore)]
        public string TermsOfService { get; set; }

        /// <summary>
        /// The version numbering you want to apply to this release of API., e.g.: `1.0`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
        /// </summary>
        [JsonProperty("version", NullValueHandling = NullValueHandling.Ignore)]
        public string Version { get; set; }
    }

    /// <summary>
    /// The JMESPath query language allows you to select, slice, filter, map, project, flatten,
    /// sort, and all sorts of operations on data.
    ///
    /// See: https://www.krakend.io/docs/enterprise/endpoints/jmespath/
    /// </summary>
    public partial class JmesPathResponseManipulationWithQueryLanguage
    {
        /// <summary>
        /// The JMESPath expression you want to apply to this endpoint.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/jmespath/
        /// </summary>
        [JsonProperty("expr")]
        public string Expr { get; set; }
    }

    public partial class Proxy
    {
        /// <summary>
        /// For custom builds of KrakenD only
        /// </summary>
        [JsonProperty("combiner", NullValueHandling = NullValueHandling.Ignore)]
        public string Combiner { get; set; }

        /// <summary>
        /// The list of operations to **execute sequentially** (top down). Every operation is defined
        /// with an object containing two properties:
        ///
        /// See: https://www.krakend.io/docs/backends/flatmap/
        /// </summary>
        [JsonProperty("flatmap_filter", NullValueHandling = NullValueHandling.Ignore)]
        public FlatmapOperation[] FlatmapFilter { get; set; }

        /// <summary>
        /// The sequential proxy allows you to chain backend requests, making calls dependent one of
        /// each other.
        ///
        /// See: https://www.krakend.io/docs/endpoints/sequential-proxy/
        /// </summary>
        [JsonProperty("sequential", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Sequential { get; set; }

        /// <summary>
        /// The static proxy injects static data in the final response when the selected strategy
        /// matches.
        ///
        /// See: https://www.krakend.io/docs/endpoints/static-proxy/
        /// </summary>
        [JsonProperty("static", NullValueHandling = NullValueHandling.Ignore)]
        public StaticResponse Static { get; set; }
    }

    /// <summary>
    /// The static proxy injects static data in the final response when the selected strategy
    /// matches.
    ///
    /// See: https://www.krakend.io/docs/endpoints/static-proxy/
    /// </summary>
    public partial class StaticResponse
    {
        /// <summary>
        /// The static data (as a JSON object) that you will return.
        ///
        /// See: https://www.krakend.io/docs/endpoints/static-proxy/
        /// </summary>
        [JsonProperty("data")]
        public Dictionary<string, object> Data { get; set; }

        /// <summary>
        /// One of the supported strategies
        ///
        /// See: https://www.krakend.io/docs/endpoints/static-proxy/
        /// </summary>
        [JsonProperty("strategy")]
        public StaticStrategy Strategy { get; set; }
    }

    /// <summary>
    /// The router rate limit feature allows you to set a number of maximum requests per second a
    /// KrakenD endpoint will accept.
    ///
    /// See: https://www.krakend.io/docs/endpoints/rate-limit/
    ///
    /// Enterprise Only. The service rate limit feature allows you to set the maximum requests
    /// per second a user or group of users can do to KrakenD and works analogously to the
    /// endpoint rate limit.
    ///
    /// See: https://www.krakend.io/docs/enterprise/service-settings/service-rate-limit/
    /// </summary>
    public partial class RateLimiting
    {
        /// <summary>
        /// Number of tokens you can store in the Token Bucket. Traduces into maximum concurrent
        /// requests this endpoint will accept for all users.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("capacity", NullValueHandling = NullValueHandling.Ignore)]
        public long? Capacity { get; set; }

        /// <summary>
        /// Number of tokens you can store in the Token Bucket for each individual user. Traduces
        /// into maximum concurrent requests this endpoint will accept for the connected user. The
        /// client is defined by the `strategy` field. The `client_max_rate` keeps a counter for
        /// every client and endpoint.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("client_capacity", NullValueHandling = NullValueHandling.Ignore)]
        public long? ClientCapacity { get; set; }

        /// <summary>
        /// Number of tokens added per second to the [Token Bucket](/docs/throttling/token-bucket/)
        /// for each individual user (*user quota*). The remaining tokens for a user are the requests
        /// per second a specific user can do. The client is defined by `strategy`. Instead of
        /// counting all the connections to the endpoint as the option above, the `client_max_rate`
        /// keeps a counter for every client and endpoint. Keep in mind that every KrakenD instance
        /// keeps its counters in memory for **every single client**.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("client_max_rate", NullValueHandling = NullValueHandling.Ignore)]
        public double? ClientMaxRate { get; set; }

        [JsonProperty("every", NullValueHandling = NullValueHandling.Ignore)]
        public string Every { get; set; }

        /// <summary>
        /// Available when using `client_max_rate`. Sets the header containing the user
        /// identification (e.g., `Authorization`) or IP (e.g.,`X-Original-Forwarded-For`). When the
        /// header contains a list of space-separated IPs, it will take the IP from the client that
        /// hit the first trusted proxy.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("key", NullValueHandling = NullValueHandling.Ignore)]
        public string Key { get; set; }

        /// <summary>
        /// Sets the number of tokens added per second to the Token Bucket. The remaining tokens in
        /// the bucket are the **maximum requests the endpoint can handle per second** at once or in.
        /// The absence of `max_rate` in the configuration or `0` is the equivalent to no
        /// limitation.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("max_rate", NullValueHandling = NullValueHandling.Ignore)]
        public double? MaxRate { get; set; }

        /// <summary>
        /// Available when using `client_max_rate`. Sets the strategy you will use to set client
        /// counters. Choose `ip` when the restrictions apply to the client's IP address, or set it
        /// to `header` when there is a header that identifies a user uniquely. That header must be
        /// defined with the `key` entry.
        ///
        /// See: https://www.krakend.io/docs/endpoints/rate-limit/
        /// </summary>
        [JsonProperty("strategy", NullValueHandling = NullValueHandling.Ignore)]
        public QosRatelimitServiceStrategy? Strategy { get; set; }
    }

    /// <summary>
    /// The bot detector module checks incoming connections to the gateway to determine if a bot
    /// made them, helping you detect and reject bots carrying out scraping, content theft, and
    /// form spam.
    ///
    /// See: https://www.krakend.io/docs/throttling/botdetector/
    /// </summary>
    public partial class BotDetector
    {
        /// <summary>
        /// An array with EXACT MATCHES of trusted user agents that can connect.
        ///
        /// See: https://www.krakend.io/docs/throttling/botdetector/
        /// </summary>
        [JsonProperty("allow", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Allow { get; set; }

        /// <summary>
        /// Size of the LRU cache that helps speed the bot detection. The size is the mumber of users
        /// agents that you want to keep in memory.
        ///
        /// See: https://www.krakend.io/docs/throttling/botdetector/
        /// </summary>
        [JsonProperty("cache_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? CacheSize { get; set; }

        /// <summary>
        /// An array with EXACT MATCHES of undesired bots, to reject immediately.
        ///
        /// See: https://www.krakend.io/docs/throttling/botdetector/
        /// </summary>
        [JsonProperty("deny", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Deny { get; set; }

        /// <summary>
        /// An array with all the regular expressions that define bots. Matching bots are rejected.
        ///
        /// See: https://www.krakend.io/docs/throttling/botdetector/
        /// </summary>
        [JsonProperty("patterns", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Patterns { get; set; }
    }

    /// <summary>
    /// Security through HTTP headers, including HSTS, HPKP, MIME-Sniffing prevention,
    /// Clickjacking protection, and others.
    ///
    /// See: https://www.krakend.io/docs/service-settings/security/
    /// </summary>
    public partial class SecurityCorsClass
    {
        /// <summary>
        /// When requests can include user credentials like cookies, HTTP authentication or client
        /// side SSL certificates.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_credentials", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AllowCredentials { get; set; }

        /// <summary>
        /// An array with the headers allowed, but `Origin`is always appended to the list. Requests
        /// with headers not in this list are rejected.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] AllowHeaders { get; set; }

        /// <summary>
        /// An array with all the HTTP methods allowed, in uppercase. Possible values are `GET`,
        /// `HEAD`,`POST`,`PUT`,`PATCH`,`DELETE`, or `OPTIONS`
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_methods", NullValueHandling = NullValueHandling.Ignore)]
        public AllowMethodElement[] AllowMethods { get; set; }

        /// <summary>
        /// An array with all the origins allowed, examples of values are `https://example.com`, or
        /// `*` (any origin).
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_origins", NullValueHandling = NullValueHandling.Ignore)]
        public object[] AllowOrigins { get; set; }

        /// <summary>
        /// Show debugging information in the logger, **use it only during development**.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("debug", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Debug { get; set; }

        /// <summary>
        /// List of headers that are safe to expose to the API of a CORS API specification.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("expose_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] ExposeHeaders { get; set; }

        /// <summary>
        /// For how long the response can be cached. For zero values the `Access-Control-Max-Age`
        /// header is not set.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("max_age", NullValueHandling = NullValueHandling.Ignore)]
        public string MaxAge { get; set; }
    }

    /// <summary>
    /// Security through HTTP headers, including HSTS, HPKP, MIME-Sniffing prevention,
    /// Clickjacking protection, and others.
    ///
    /// See: https://www.krakend.io/docs/service-settings/security/
    /// </summary>
    public partial class SecurityHttpClass
    {
        /// <summary>
        /// When a request hits KrakenD, it will confirm if the value of the Host HTTP header is in
        /// the list. If so, it will further process the request. If the host is not in the allowed
        /// hosts list, KrakenD will simply reject the request.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("allowed_hosts", NullValueHandling = NullValueHandling.Ignore)]
        public object[] AllowedHosts { get; set; }

        [JsonProperty("browser_xss_filter", NullValueHandling = NullValueHandling.Ignore)]
        public bool? BrowserXssFilter { get; set; }

        /// <summary>
        /// The HTTP Content-Security-Policy (CSP) default-src directive serves as a fallback for the
        /// other CSP fetch directives.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("content_security_policy", NullValueHandling = NullValueHandling.Ignore)]
        public string ContentSecurityPolicy { get; set; }

        /// <summary>
        /// Enabling this feature will prevent the user's browser from interpreting files as
        /// something else than declared by the content type in the HTTP headers.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("content_type_nosniff", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ContentTypeNosniff { get; set; }

        /// <summary>
        /// You can add an X-Frame-Options header using custom_frame_options_value with the value of
        /// DENY (default behavior) or even set your custom value.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("custom_frame_options_value", NullValueHandling = NullValueHandling.Ignore)]
        public string CustomFrameOptionsValue { get; set; }

        /// <summary>
        /// Set to true to enable clickjacking protection, together with
        /// `custom_frame_options_value`.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("frame_deny", NullValueHandling = NullValueHandling.Ignore)]
        public bool? FrameDeny { get; set; }

        /// <summary>
        /// A set of header keys that may hold a proxied hostname value for the request.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("host_proxy_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] HostProxyHeaders { get; set; }

        /// <summary>
        /// HTTP Public Key Pinning (HPKP) is a security mechanism which allows HTTPS websites to
        /// resist impersonation by attackers using mis-issued or otherwise fraudulent certificates.
        /// (For example, sometimes attackers can compromise certificate authorities, and then can
        /// mis-issue certificates for a web origin.).
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("hpkp_public_key", NullValueHandling = NullValueHandling.Ignore)]
        public string HpkpPublicKey { get; set; }

        /// <summary>
        /// This will cause the AllowedHosts, SSLRedirect, and STSSeconds/STSIncludeSubdomains
        /// options to be ignored during development. When deploying to production, be sure to set
        /// this to false.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("is_development", NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsDevelopment { get; set; }

        /// <summary>
        /// Allows the Referrer-Policy header with the value to be set with a custom value.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("referrer_policy", NullValueHandling = NullValueHandling.Ignore)]
        public string ReferrerPolicy { get; set; }

        /// <summary>
        /// When the SSL redirect is true, the host where the request is redirected to.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("ssl_host", NullValueHandling = NullValueHandling.Ignore)]
        public string SslHost { get; set; }

        /// <summary>
        /// Header keys with associated values that would indicate a valid https request. Useful when
        /// using Nginx, e.g: `"X-Forwarded-Proto": "https"`
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("ssl_proxy_headers", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> SslProxyHeaders { get; set; }

        /// <summary>
        /// Redirect any request that is not using HTTPS
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("ssl_redirect", NullValueHandling = NullValueHandling.Ignore)]
        public bool? SslRedirect { get; set; }

        /// <summary>
        /// Set to true when you want the `includeSubdomains` be appended to the
        /// Strict-Transport-Security header.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("sts_include_subdomains", NullValueHandling = NullValueHandling.Ignore)]
        public bool? StsIncludeSubdomains { get; set; }

        /// <summary>
        /// Enable this policy by setting the `max-age` of the `Strict-Transport-Security` header.
        /// Setting to `0` disables HSTS.
        ///
        /// See: https://www.krakend.io/docs/service-settings/security/
        /// </summary>
        [JsonProperty("sts_seconds", NullValueHandling = NullValueHandling.Ignore)]
        public long? StsSeconds { get; set; }
    }

    /// <summary>
    /// Enterprise only. Enables websocket communication.
    ///
    /// See: https://www.krakend.io/docs/enterprise/websockets/
    /// </summary>
    public partial class SchemaDefinitionForWebsockets
    {
        /// <summary>
        /// When the connection to your event source gets interrupted for whatever reason, KrakenD
        /// keeps trying to reconnect until it succeeds or until it reaches the max_retries. The
        /// backoff strategy defines the delay in seconds in between consecutive failed retries.
        /// Defaults to 'fallback'
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("backoff_strategy", NullValueHandling = NullValueHandling.Ignore)]
        public BackoffStrategy? BackoffStrategy { get; set; }

        /// <summary>
        /// Notifies in the log when there is the client connect event.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("connect_event", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ConnectEvent { get; set; }

        /// <summary>
        /// Notifies in the log when there is a client disconnect event.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("disconnect_event", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisconnectEvent { get; set; }

        /// <summary>
        /// Defines which input headers are allowed to pass to the backend. Notice that you need to
        /// declare the `input_headers` at the endpoint level too.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("input_headers", NullValueHandling = NullValueHandling.Ignore)]
        public string[] InputHeaders { get; set; }

        /// <summary>
        /// Sets the maximum size of client messages (in bytes).
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("max_message_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxMessageSize { get; set; }

        /// <summary>
        /// The maximum number of times you will allow KrakenD to retry reconnecting to a broken
        /// messaging system. Use a value `<= 0` for unlimited retries.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("max_retries", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxRetries { get; set; }

        /// <summary>
        /// Sets the maximum buffer size for messages (in bytes).
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("message_buffer_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? MessageBufferSize { get; set; }

        /// <summary>
        /// Sets the time between pings checking the health of the system.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("ping_period", NullValueHandling = NullValueHandling.Ignore)]
        public string PingPeriod { get; set; }

        /// <summary>
        /// Sets the maximum time KrakenD will until the pong times out.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("pong_wait", NullValueHandling = NullValueHandling.Ignore)]
        public string PongWait { get; set; }

        /// <summary>
        /// Connections buffer network input and output to reduce the number of system calls when
        /// reading messages. You can set the maximum buffer size for reading in bytes.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("read_buffer_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? ReadBufferSize { get; set; }

        /// <summary>
        /// Provides an error `{'error':'reason here'}` to the client when KrakenD was unable to send
        /// the message to the backend.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("return_error_details", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ReturnErrorDetails { get; set; }

        /// <summary>
        /// Connections buffer network input and output to reduce the number of system calls when
        /// writing messages. You can set the maximum buffer size for writing in bytes.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("write_buffer_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? WriteBufferSize { get; set; }

        /// <summary>
        /// Sets the maximum time KrakenD will wait until the write times out.
        ///
        /// See: https://www.krakend.io/docs/enterprise/websockets/
        /// </summary>
        [JsonProperty("write_wait", NullValueHandling = NullValueHandling.Ignore)]
        public string WriteWait { get; set; }
    }

    /// <summary>
    /// The optional configuration that extends the core functionality of the gateway is
    /// specified here. The `extra_config` at this level enables service components, meaning that
    /// they apply globally to all endpoints or activity.
    /// </summary>
    public partial class KrakendDocumentExtraConfig
    {
        [JsonProperty("auth/api-keys", NullValueHandling = NullValueHandling.Ignore)]
        public ApiKeyAuthentication AuthApiKeys { get; set; }

        [JsonProperty("auth/revoker", NullValueHandling = NullValueHandling.Ignore)]
        public TheApiGatewayAuthorizesUsersThatProvideValidTokensAccordingToYourCriteriaButAtSomePointYouMightWantToChangeYourMindAndDecideToRevokeJwtTokensThatAreStillValid AuthRevoker { get; set; }

        [JsonProperty("documentation/openapi", NullValueHandling = NullValueHandling.Ignore)]
        public GenerateDocumentationUsingOpenApi DocumentationOpenapi { get; set; }

        [JsonProperty("plugin/http-server", NullValueHandling = NullValueHandling.Ignore)]
        public HttpServerPluginsSeeHttpsWwwKrakendIoDocsExtendingHttpServerPlugins PluginHttpServer { get; set; }

        /// <summary>
        /// Enterprise Only. The service rate limit feature allows you to set the maximum requests
        /// per second a user or group of users can do to KrakenD and works analogously to the
        /// endpoint rate limit.
        ///
        /// See: https://www.krakend.io/docs/enterprise/service-settings/service-rate-limit/
        /// </summary>
        [JsonProperty("qos/ratelimit/service", NullValueHandling = NullValueHandling.Ignore)]
        public RateLimiting QosRatelimitService { get; set; }

        [JsonProperty("router", NullValueHandling = NullValueHandling.Ignore)]
        public RouterFlags Router { get; set; }

        [JsonProperty("security/bot-detector", NullValueHandling = NullValueHandling.Ignore)]
        public BotDetector SecurityBotDetector { get; set; }

        /// <summary>
        /// When KrakenD endpoints are consumed from a browser, you might need to enable the
        /// Cross-Origin Resource Sharing (CORS) module as browsers restrict cross-origin HTTP
        /// requests initiated from scripts.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("security/cors", NullValueHandling = NullValueHandling.Ignore)]
        public CrossOriginResourceSharing SecurityCors { get; set; }

        [JsonProperty("security/http", NullValueHandling = NullValueHandling.Ignore)]
        public SecurityHttpClass SecurityHttp { get; set; }

        [JsonProperty("telemetry/ganalytics", NullValueHandling = NullValueHandling.Ignore)]
        public TelemetryGanalytics TelemetryGanalytics { get; set; }

        [JsonProperty("telemetry/gelf", NullValueHandling = NullValueHandling.Ignore)]
        public Gelf TelemetryGelf { get; set; }

        [JsonProperty("telemetry/influx", NullValueHandling = NullValueHandling.Ignore)]
        public TelemetryViaInflux TelemetryInflux { get; set; }

        [JsonProperty("telemetry/instana", NullValueHandling = NullValueHandling.Ignore)]
        public TelemetryInstanaClass TelemetryInstana { get; set; }

        [JsonProperty("telemetry/logging", NullValueHandling = NullValueHandling.Ignore)]
        public ImprovedLogging TelemetryLogging { get; set; }

        [JsonProperty("telemetry/logstash", NullValueHandling = NullValueHandling.Ignore)]
        public Logstash TelemetryLogstash { get; set; }

        [JsonProperty("telemetry/metrics", NullValueHandling = NullValueHandling.Ignore)]
        public ExtendedMetrics TelemetryMetrics { get; set; }

        [JsonProperty("telemetry/newrelic", NullValueHandling = NullValueHandling.Ignore)]
        public NewRelicExporter TelemetryNewrelic { get; set; }

        [JsonProperty("telemetry/opencensus", NullValueHandling = NullValueHandling.Ignore)]
        public TelemetryOpencensusClass TelemetryOpencensus { get; set; }
    }

    /// <summary>
    /// Enterprise only. Enables a Role-Based Access Control (RBAC) mechanism by reading the
    /// `Authorization` header of incoming requests.
    ///
    /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
    /// </summary>
    public partial class ApiKeyAuthentication
    {
        /// <summary>
        /// The header name or the query string name that contains the API key. Defaults to `key`
        /// when using the `query_string` strategy and to Authorization when using `header`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("identifier", NullValueHandling = NullValueHandling.Ignore)]
        public string Identifier { get; set; }

        /// <summary>
        /// A list of objects defining each API Key.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("keys")]
        public ApiKey[] Keys { get; set; }

        /// <summary>
        /// One of header or query_string. Specifies where to expect the user API key, whether inside
        /// a header or as part of the query string. Defaults to header.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("strategy", NullValueHandling = NullValueHandling.Ignore)]
        public AuthApiKeysStrategy? Strategy { get; set; }
    }

    public partial class ApiKey
    {
        /// <summary>
        /// The secret key used by the client to access the resources. Don't have a key? Execute in a
        /// terminal `uuidgen` to generate a random one.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("key", NullValueHandling = NullValueHandling.Ignore)]
        public string Key { get; set; }

        /// <summary>
        /// All the roles this user has. See roles as all the identifying labels that belong to this
        /// client.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
        /// </summary>
        [JsonProperty("roles", NullValueHandling = NullValueHandling.Ignore)]
        public string[] Roles { get; set; }
    }

    public partial class TheApiGatewayAuthorizesUsersThatProvideValidTokensAccordingToYourCriteriaButAtSomePointYouMightWantToChangeYourMindAndDecideToRevokeJwtTokensThatAreStillValid
    {
        /// <summary>
        /// Either `optimal` (recommended) or `default`. The `optimal` consumes less CPU but has less
        /// entropy when generating the hash, although the loss is negligible.
        ///
        /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
        /// </summary>
        [JsonProperty("hash_name")]
        public HashFunctionName HashName { get; set; }

        /// <summary>
        /// The maximum `N`umber of elements you want to keep in the bloom filter. Tens of millions
        /// work fine on machines with low resources.
        ///
        /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
        /// </summary>
        [JsonProperty("N")]
        public long N { get; set; }

        /// <summary>
        /// The `P`robability of returning a false positive. E.g.,`1e-7` for one false positive every
        /// 10 million different tokens. The values `N` and `P` determine the size of the resulting
        /// bloom filter to fulfill your expectations. E.g: 0.0000001
        ///
        /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
        /// </summary>
        [JsonProperty("P")]
        public double P { get; set; }

        /// <summary>
        /// The port number exposed on each KrakenD instance for the RPC service to interact with the
        /// bloomfilter. This port is allocated only to the clients (running KrakenDs).
        ///
        /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
        /// </summary>
        [JsonProperty("port")]
        public long Port { get; set; }

        /// <summary>
        /// A string used as an exchange API key to secure the communication between the Revoke
        /// Server and the KrakenD instances and to consume the REST API of the Revoker Server as
        /// well. E.g., a string generated with `uuidgen`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/revoke-server/
        /// </summary>
        [JsonProperty("revoke_server_api_key", NullValueHandling = NullValueHandling.Ignore)]
        public string RevokeServerApiKey { get; set; }

        /// <summary>
        /// Maximum number of retries after a connection fails. When the value is less than zero it
        /// is changed automatically to zero.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/revoke-server/
        /// </summary>
        [JsonProperty("revoke_server_max_retries", NullValueHandling = NullValueHandling.Ignore)]
        public long? RevokeServerMaxRetries { get; set; }

        /// <summary>
        /// How many workers are used concurrently to execute an action (e.g., push a token) to all
        /// registered instances, allowing you to limit the amount of memory consumed by the server.
        /// For example, if you have 100 KrakenD servers and need to push 5MB of data each, you need
        /// to send 500MB in total. A max_workers=5 will consume a maximum of `5MB x 5 workers =
        /// 25MB` of memory in a given instant. Defaults to the same number of CPUs available.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/revoke-server/
        /// </summary>
        [JsonProperty("revoke_server_max_workers", NullValueHandling = NullValueHandling.Ignore)]
        public long? RevokeServerMaxWorkers { get; set; }

        /// <summary>
        /// Time the server and the client wait to verify they are alive with each other (health
        /// check). Defaults to `30s`. Do not lower this value a lot; otherwise, you will have a lot
        /// of internal traffic.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/revoke-server/
        /// </summary>
        [JsonProperty("revoke_server_ping_interval", NullValueHandling = NullValueHandling.Ignore)]
        public string RevokeServerPingInterval { get; set; }

        /// <summary>
        /// The address to the `/instances` endpoint in the Revoke Server.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/revoke-server/
        /// </summary>
        [JsonProperty("revoke_server_ping_url", NullValueHandling = NullValueHandling.Ignore)]
        public string RevokeServerPingUrl { get; set; }

        /// <summary>
        /// The list with all the claims in your JWT payload that need watching. These fields
        /// establish the criteria to revoke accesses in the future. The Revoker does not use this
        /// value, only the clients.
        ///
        /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
        /// </summary>
        [JsonProperty("token_keys")]
        public object[] TokenKeys { get; set; }

        /// <summary>
        /// The lifespan of the JWT you are generating in seconds. The value must match the
        /// expiration you are setting in the identity provider when creating the tokens.
        ///
        /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
        /// </summary>
        [JsonProperty("TTL")]
        public long Ttl { get; set; }
    }

    public partial class HttpServerPluginsSeeHttpsWwwKrakendIoDocsExtendingHttpServerPlugins
    {
        [JsonProperty("basic-auth", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheBasicAuthenticationPluginProtectsTheAccessToSelectedEndpointsUsingBasicUsernameAndPasswordCredentialsSeeHttpsWwwKrakendIoDocsEnterpriseAuthenticationBasicAuthentication BasicAuth { get; set; }

        [JsonProperty("geoip", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheGeoIpIntegrationAllowsYouLoadMaxmindSGeoIp2CityDatabasePaymentAndFreeVersionsAndEnrichAllKrakenDCallsToYourBackendsWithGeoDataSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsGeoip Geoip { get; set; }

        [JsonProperty("ip-filter", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheIpFilteringPluginAllowsYouToRestrictTheTrafficToYourApiGatewayBasedOnTheIpAddressItWorksInTwoDifferentModesAllowOrDenyWhereYouDefineTheListOfIPsCidrBlocksThatAreAuthorizedToUseTheApiOrThatAreDeniedFromUsingTheApiSeeHttpsWwwKrakendIoDocsEnterpriseThrottlingIpfilter IpFilter { get; set; }

        [JsonProperty("jwk-aggregator", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheJwkAggregatorPluginAllowsKrakenDToValidateTokensIssuedByMultipleIdentityProvidersSeeHttpsWwwKrakendIoDocsEnterpriseAuthenticationMultipleIdentityProviders JwkAggregator { get; set; }

        /// <summary>
        /// An array with the names of plugins to load. The names are defined inside your plugin.
        ///
        /// See: https://www.krakend.io/docs/extending/http-server-plugins/
        /// </summary>
        [JsonProperty("name")]
        public object[] Name { get; set; }

        [JsonProperty("redis-ratelimit", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheGlobalRateLimitFunctionalityEnablesARedisDatabaseStoreToCentralizeAllKrakenDNodeCountersInsteadOfHavingEachKrakenDNodeCountItsHitsTheCountersAreGlobalAndStoredInTheDatabaseSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsGlobalRateLimit RedisRatelimit { get; set; }

        [JsonProperty("static-filesystem", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyAllowsYouToFetchAndServeStaticContentInTwoDifferentUseCasesWhenThePluginIsUsedAsAnHttpServerHandlerTheStaticContentIsForYourEndUsersGivingThemCssJsImagesOrJsonFilesToNameAFewExamplesOnTheOtherSideWhenThePluginIsUsedAsAnHttpClientExecutorTheKrakenDEndpointsUseStaticContentAsIfItWereABackendSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsServeStaticContent StaticFilesystem { get; set; }

        [JsonProperty("url-rewrite", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyAllowsYouToDeclareAdditionalUrLsOtherThanTheOnesDefinedUnderTheEndpointsConfigurationUsedAsAliasesOfExistingEndpointsSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsUrlRewrite UrlRewrite { get; set; }

        [JsonProperty("virtualhost", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyTheVirtualHostPluginAllowsYouToRunDifferentConfigurationsOfKrakenDEndpointsBasedOnTheHostAccessingTheServerSeeHttpsWwwKrakendIoDocsEnterpriseServiceSettingsVirtualHosts Virtualhost { get; set; }

        [JsonProperty("wildcard", NullValueHandling = NullValueHandling.Ignore)]
        public EnterpriseOnlyEnablesWildcardProcessingOfRequestsWithoutDeclaringAllEndpointSubresroucesSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsWildcard Wildcard { get; set; }
    }

    public partial class EnterpriseOnlyTheBasicAuthenticationPluginProtectsTheAccessToSelectedEndpointsUsingBasicUsernameAndPasswordCredentialsSeeHttpsWwwKrakendIoDocsEnterpriseAuthenticationBasicAuthentication
    {
        /// <summary>
        /// An array to restrict which endpoints are protected with basic authentication. The
        /// `endpoints` values must match with the declaration of endpoints in your configuration,
        /// including any `{placeholders}` in the path. Use `["*"]` or delete this property to
        /// protect all endpoints.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/basic-authentication/
        /// </summary>
        [JsonProperty("endpoints")]
        public object[] Endpoints { get; set; }

        /// <summary>
        /// Absolute Path to the `htpasswd` filename (recommended) or relative `./` to the workdir
        /// (less secure).
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/basic-authentication/
        /// </summary>
        [JsonProperty("htpasswd_path")]
        public string HtpasswdPath { get; set; }

        /// <summary>
        /// **Additional** users to the `htpasswd` file can be declared directly inside the
        /// configuration. The content of both places will be merged (and this list will overwrite
        /// users already defined in the htpasswd file).
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/basic-authentication/
        /// </summary>
        [JsonProperty("users", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Users { get; set; }
    }

    public partial class EnterpriseOnlyTheGeoIpIntegrationAllowsYouLoadMaxmindSGeoIp2CityDatabasePaymentAndFreeVersionsAndEnrichAllKrakenDCallsToYourBackendsWithGeoDataSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsGeoip
    {
        /// <summary>
        /// The path in the filesystem containing the database in GeoIP2 Binary (`.mmdb`) format.
        /// Relative to the working dir or absolute path.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/geoip/
        /// </summary>
        [JsonProperty("citydb_path")]
        public string CitydbPath { get; set; }
    }

    public partial class EnterpriseOnlyTheJwkAggregatorPluginAllowsKrakenDToValidateTokensIssuedByMultipleIdentityProvidersSeeHttpsWwwKrakendIoDocsEnterpriseAuthenticationMultipleIdentityProviders
    {
        /// <summary>
        /// The list of all JWK URLs recognized as valid Identity Providers by the gateway.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/multiple-identity-providers/
        /// </summary>
        [JsonProperty("origins")]
        public object[] Origins { get; set; }

        /// <summary>
        /// The port of the local server doing the aggregation. The port is only accessible within
        /// the gateway machine using localhost, and it's never exposed to the external network.
        /// Choose any port that is free in the system.
        ///
        /// See: https://www.krakend.io/docs/enterprise/authentication/multiple-identity-providers/
        /// </summary>
        [JsonProperty("port")]
        public long Port { get; set; }
    }

    public partial class EnterpriseOnlyTheGlobalRateLimitFunctionalityEnablesARedisDatabaseStoreToCentralizeAllKrakenDNodeCountersInsteadOfHavingEachKrakenDNodeCountItsHitsTheCountersAreGlobalAndStoredInTheDatabaseSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsGlobalRateLimit
    {
        /// <summary>
        /// How many requests a client can make above the rate specified during a peak.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
        /// </summary>
        [JsonProperty("Burst")]
        public long Burst { get; set; }

        /// <summary>
        /// The URL to the Redis instance that stores the counters using the format `host:port`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
        /// </summary>
        [JsonProperty("Host")]
        public string Host { get; set; }

        /// <summary>
        /// How many requests a client can make above the rate specified during a peak. Usually in
        /// seconds, minutes, or hours. E.g., use `120s` or `2m` for two minutes
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
        /// </summary>
        [JsonProperty("Period")]
        public string Period { get; set; }

        /// <summary>
        /// Number of allowed requests during the observed period.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
        /// </summary>
        [JsonProperty("Rate")]
        public long Rate { get; set; }

        /// <summary>
        /// One of the preselected strategies to rate-limit users.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
        /// </summary>
        [JsonProperty("Tokenizer")]
        public Tokenizer Tokenizer { get; set; }

        /// <summary>
        /// The field used to set a custom field for the tokenizer (e.g., extracting the token from a
        /// custom header other than Authorization or using a claim from a JWT other than the jti).
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
        /// </summary>
        [JsonProperty("TokenizerField", NullValueHandling = NullValueHandling.Ignore)]
        public string TokenizerField { get; set; }
    }

    public partial class EnterpriseOnlyAllowsYouToFetchAndServeStaticContentInTwoDifferentUseCasesWhenThePluginIsUsedAsAnHttpServerHandlerTheStaticContentIsForYourEndUsersGivingThemCssJsImagesOrJsonFilesToNameAFewExamplesOnTheOtherSideWhenThePluginIsUsedAsAnHttpClientExecutorTheKrakenDEndpointsUseStaticContentAsIfItWereABackendSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsServeStaticContent
    {
        /// <summary>
        /// The folder in the filesystem containing the static files. Relative to the working dir
        /// where KrakenD config is (e.g.: `./assets`) or absolute (e.g.: `/var/www/assets`).
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/serve-static-content/
        /// </summary>
        [JsonProperty("path")]
        public string Path { get; set; }

        /// <summary>
        /// This is the beginning (prefix) of all URLs that are resolved using this plugin. All
        /// matching URLs won't be passed to the router, meaning that they are not considered
        /// endpoints. Make sure you are not overwriting valid endpoints. When the `prefix` is `/`,
        /// then **all traffic is served as static** and you must declare a prefix under `skip`
        /// (e.g.: `/api`) to match endpoints.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/serve-static-content/
        /// </summary>
        [JsonProperty("prefix")]
        public string Prefix { get; set; }

        /// <summary>
        /// An array with all the prefix URLs that despite they could match with the `prefix`, you
        /// don't want to treat them as static content and pass them to the router.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/serve-static-content/
        /// </summary>
        [JsonProperty("skip", NullValueHandling = NullValueHandling.Ignore)]
        public object[] Skip { get; set; }
    }

    public partial class EnterpriseOnlyAllowsYouToDeclareAdditionalUrLsOtherThanTheOnesDefinedUnderTheEndpointsConfigurationUsedAsAliasesOfExistingEndpointsSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsUrlRewrite
    {
        /// <summary>
        /// A map with the exact desired url and its mapping to an endpoint. If the endpoint has
        /// `{placeholders}` you need to write them, but the literal value `{placeholders}` is
        /// passed.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/url-rewrite/
        /// </summary>
        [JsonProperty("literal", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Literal { get; set; }

        /// <summary>
        /// A list of lists, containing the regular expression that defines the URL to be rewritten,
        /// and its endpoint destination. You can use the capturing groups with the syntax `${1}`,
        /// `${2}`, etc.
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/url-rewrite/
        /// </summary>
        [JsonProperty("regexp", NullValueHandling = NullValueHandling.Ignore)]
        public object[][] Regexp { get; set; }
    }

    public partial class EnterpriseOnlyTheVirtualHostPluginAllowsYouToRunDifferentConfigurationsOfKrakenDEndpointsBasedOnTheHostAccessingTheServerSeeHttpsWwwKrakendIoDocsEnterpriseServiceSettingsVirtualHosts
    {
        /// <summary>
        /// All recognized virtual hosts by KrakenD must be listed here. The values declared here
        /// must match the content of the `Host` header when passed by the client.
        ///
        /// See: https://www.krakend.io/docs/enterprise/service-settings/virtual-hosts/
        /// </summary>
        [JsonProperty("hosts")]
        public string[] Hosts { get; set; }
    }

    public partial class EnterpriseOnlyEnablesWildcardProcessingOfRequestsWithoutDeclaringAllEndpointSubresroucesSeeHttpsWwwKrakendIoDocsEnterpriseEndpointsWildcard
    {
        /// <summary>
        /// The key of the map is the KrakenD endpoint that receives all the wildcard traffic. The
        /// value is an array with all the user paths that match this wildcard (you don't need to
        /// declare the subresources).
        ///
        /// See: https://www.krakend.io/docs/enterprise/endpoints/wildcard/
        /// </summary>
        [JsonProperty("endpoints")]
        public Dictionary<string, object> Endpoints { get; set; }
    }

    /// <summary>
    /// The optional router configuration allows you to set global flags that change the way
    /// KrakenD processes the requests at the router layer.
    ///
    /// See: https://www.krakend.io/docs/service-settings/router-options/
    /// </summary>
    public partial class RouterFlags
    {
        /// <summary>
        /// Whether to automatically trust headers starting with `X-AppEngine...` or not, for better
        /// integration with Google App Engine.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("app_engine", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AppEngine { get; set; }

        /// <summary>
        /// When true, enables the autogenerated `OPTIONS` endpoint for all the registered paths.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("auto_options", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AutoOptions { get; set; }

        /// <summary>
        /// Stops registering access requests to KrakenD and leaving any logs out from the output.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("disable_access_log", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableAccessLog { get; set; }

        /// <summary>
        /// Whether to checks if another method is allowed for the current route, if the current
        /// request can not be routed. If this is the case, the request is answered with `Method Not
        /// Allowed` and HTTP status code `405`. If no other Method is allowed, the request is a
        /// `404`.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("disable_handle_method_not_allowed", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableHandleMethodNotAllowed { get; set; }

        /// <summary>
        /// When true you don't have any exposed health endpoint. You can still use a TCP checker or
        /// build an endpoint yourself.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("disable_health", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableHealth { get; set; }

        /// <summary>
        /// Disables automatic validation of the url params looking for url encoded ones. The default
        /// behavior (`false`) is to avoid double URL encoding of parameters that could lead to
        /// backend exploration. The server rejects with a `400` when the user is trying to make an
        /// injection with double (or more) URL-encoded parameters.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("disable_path_decoding", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisablePathDecoding { get; set; }

        /// <summary>
        /// If enabled, the router tries to fix the current request path, if no handle is registered
        /// for it. First superfluous path elements like `../` or `//` are removed. Afterwards the
        /// router does a case-insensitive lookup of the cleaned path. If a handle can be found for
        /// this route, the router makes a redirection to the corrected path with status code `301`
        /// for GET requests and `307` for all other request methods. For example `/FOO` and
        /// `/..//Foo` could be redirected to `/foo`. The flag `disable_redirect_trailing_slash` is
        /// independent of this option.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("disable_redirect_fixed_path", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableRedirectFixedPath { get; set; }

        /// <summary>
        /// Disables automatic redirection if the current route can't be matched but a handler for
        /// the path with (without) the trailing slash exists. For example if `/foo/` is requested
        /// but a route only exists for `/foo`, the client is redirected to `/foo` with http status
        /// code `301` for GET requests and `307` for all other request methods.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("disable_redirect_trailing_slash", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableRedirectTrailingSlash { get; set; }

        /// <summary>
        /// Sets custom error bodies for 404 and 405 errors.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("error_body", NullValueHandling = NullValueHandling.Ignore)]
        public CustomErrorBody ErrorBody { get; set; }

        /// <summary>
        /// When set to true, client IP will be parsed from the request's headers. If no IP can be
        /// fetched, it falls back to the IP obtained from the request's remote address.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("forwarded_by_client_ip", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ForwardedByClientIp { get; set; }

        /// <summary>
        /// The path where you'd like to expose the health endpoint.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("health_path", NullValueHandling = NullValueHandling.Ignore)]
        public string HealthPath { get; set; }

        /// <summary>
        /// Removes the version of KrakenD used in the X-KrakenD-version headers.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("hide_version_header", NullValueHandling = NullValueHandling.Ignore)]
        public bool? HideVersionHeader { get; set; }

        /// <summary>
        /// Defines the set of paths that are removed from the logging.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("logger_skip_paths", NullValueHandling = NullValueHandling.Ignore)]
        public string[] LoggerSkipPaths { get; set; }

        /// <summary>
        /// Sets the maxMemory param that is given to http.Request's Multipart Form method call.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("max_multipart_memory", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxMultipartMemory { get; set; }

        /// <summary>
        /// List of headers used to obtain the client IP when `forwarded_by_client_ip` is set to
        /// `true` and the remote address is matched by at least one of the network origins of
        /// `trusted_proxies`.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("remote_ip_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] RemoteIpHeaders { get; set; }

        /// <summary>
        /// A parameter can be parsed from the URL even with extra slashes.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("remove_extra_slash", NullValueHandling = NullValueHandling.Ignore)]
        public bool? RemoveExtraSlash { get; set; }

        /// <summary>
        /// When there is an error in the gateway (such as a timeout, a non-200 status code, etc.) it
        /// returns to the client the reason for the failure. The error is written in the body as
        /// is.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("return_error_msg", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ReturnErrorMsg { get; set; }

        /// <summary>
        /// List of network origins (IPv4 addresses, IPv4 CIDRs, IPv6 addresses or IPv6 CIDRs) from
        /// which to trust request's headers that contain alternative client IP when
        /// `forwarded_by_client_ip` is `true`.
        ///
        /// See: https://www.krakend.io/docs/service-settings/router-options/
        /// </summary>
        [JsonProperty("trusted_proxies", NullValueHandling = NullValueHandling.Ignore)]
        public object[] TrustedProxies { get; set; }
    }

    /// <summary>
    /// Sets custom error bodies for 404 and 405 errors.
    ///
    /// See: https://www.krakend.io/docs/service-settings/router-options/
    /// </summary>
    public partial class CustomErrorBody
    {
        /// <summary>
        /// Write any JSON object structure you would like to return to users when they request an
        /// endpoint not known by KrakenD. 404 Not Found errors.
        /// </summary>
        [JsonProperty("404", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> The404 { get; set; }

        /// <summary>
        /// Write any JSON object structure you would like to return to users
        /// </summary>
        [JsonProperty("405", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> The405 { get; set; }
    }

    /// <summary>
    /// When KrakenD endpoints are consumed from a browser, you might need to enable the
    /// Cross-Origin Resource Sharing (CORS) module as browsers restrict cross-origin HTTP
    /// requests initiated from scripts.
    ///
    /// See: https://www.krakend.io/docs/service-settings/cors/
    /// </summary>
    public partial class CrossOriginResourceSharing
    {
        /// <summary>
        /// When requests can include user credentials like cookies, HTTP authentication or client
        /// side SSL certificates
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_credentials", NullValueHandling = NullValueHandling.Ignore)]
        public bool? AllowCredentials { get; set; }

        [JsonProperty("allow_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] AllowHeaders { get; set; }

        /// <summary>
        /// The array of all HTTP methods accepted, in uppercase.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_methods", NullValueHandling = NullValueHandling.Ignore)]
        public AllowMethodElement[] AllowMethods { get; set; }

        /// <summary>
        /// An array with all the origins allowed, examples of values are https://example.com, or *
        /// (any origin).
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("allow_origins")]
        public string[] AllowOrigins { get; set; }

        /// <summary>
        /// Show debugging information in the logger, to be used only during development.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("debug", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Debug { get; set; }

        /// <summary>
        /// Headers that are safe to expose to the API of a CORS API specification-
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("expose_headers", NullValueHandling = NullValueHandling.Ignore)]
        public object[] ExposeHeaders { get; set; }

        /// <summary>
        /// For how long the response can be cached.
        ///
        /// See: https://www.krakend.io/docs/service-settings/cors/
        /// </summary>
        [JsonProperty("max_age", NullValueHandling = NullValueHandling.Ignore)]
        public string MaxAge { get; set; }
    }

    /// <summary>
    /// Enterprise only. Generate API Analytics from your API activity, using Google’s Analytics
    /// platform as the storage backend.
    ///
    /// See: https://www.krakend.io/docs/enterprise/telemetry/google-analytics/
    /// </summary>
    public partial class TelemetryGanalytics
    {
        /// <summary>
        /// The size of the buffer determines how much data can be stored in memory. The default
        /// value is twice the number of workers. It must be at least twice the number of workers,
        /// otherwise it is automatically changed to the double.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/google-analytics/
        /// </summary>
        [JsonProperty("buffer_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? BufferSize { get; set; }

        /// <summary>
        /// An object with key-values describing the tags you want to include when sending reports
        /// and their values. These tags are static.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/google-analytics/
        /// </summary>
        [JsonProperty("tags", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Tags { get; set; }

        /// <summary>
        /// The time you will wait for the internal buffer to be avaiable before discarding a report.
        /// </summary>
        [JsonProperty("timeout", NullValueHandling = NullValueHandling.Ignore)]
        public string Timeout { get; set; }

        /// <summary>
        /// The tracking ID as provided by Google. It is recommended to create a new property in
        /// Google Analytics to track the activity. It usually looks like **********.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/google-analytics/
        /// </summary>
        [JsonProperty("track_id")]
        public string TrackId { get; set; }

        /// <summary>
        /// The reporting URL where KrakenD sends the batches. Unless you need a custom backend
        /// endpoint, use `https://www.google-analytics.com/batch`.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/google-analytics/
        /// </summary>
        [JsonProperty("url")]
        public string Url { get; set; }

        /// <summary>
        /// The number of internal workers that send the reports in batch.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/google-analytics/
        /// </summary>
        [JsonProperty("workers", NullValueHandling = NullValueHandling.Ignore)]
        public long? Workers { get; set; }
    }

    /// <summary>
    /// Send structured events in GELF format to your Graylog Cluster.
    ///
    /// See: https://www.krakend.io/docs/logging/graylog-gelf/
    /// </summary>
    public partial class Gelf
    {
        /// <summary>
        /// The address (including the port) of your Graylog cluster (or any other service that
        /// receives GELF inputs). E.g., `myGraylogInstance:12201`
        ///
        /// See: https://www.krakend.io/docs/logging/graylog-gelf/
        /// </summary>
        [JsonProperty("address")]
        public string Address { get; set; }

        /// <summary>
        /// Set to false (recommended) to use UDP, or true to use TCP. TCP performance is worst than
        /// UDP under heavy load.
        ///
        /// See: https://www.krakend.io/docs/logging/graylog-gelf/
        /// </summary>
        [JsonProperty("enable_tcp")]
        public bool EnableTcp { get; set; }
    }

    /// <summary>
    /// Enables the extended logging capabilities.
    ///
    /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
    /// </summary>
    public partial class TelemetryViaInflux
    {
        /// <summary>
        /// The complete url of the influxdb including the port if different from defaults in
        /// http/https.
        ///
        /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
        /// </summary>
        [JsonProperty("address")]
        public string Address { get; set; }

        /// <summary>
        /// How many points you want to store in a memory buffer before pushing them to InfluxDB. Use
        /// `0` to send events immediately or set the number of points that should be sent together.
        ///
        /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
        /// </summary>
        [JsonProperty("buffer_size", NullValueHandling = NullValueHandling.Ignore)]
        public long? BufferSize { get; set; }

        /// <summary>
        /// Name of the InfluxDB database.
        ///
        /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
        /// </summary>
        [JsonProperty("db", NullValueHandling = NullValueHandling.Ignore)]
        public string Db { get; set; }

        /// <summary>
        /// Password to authenticate to InfluxDB.
        ///
        /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
        /// </summary>
        [JsonProperty("password", NullValueHandling = NullValueHandling.Ignore)]
        public string Password { get; set; }

        /// <summary>
        /// TTL against Influx.
        ///
        /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
        /// </summary>
        [JsonProperty("ttl")]
        public string Ttl { get; set; }

        /// <summary>
        /// Username to authenticate to InfluxDB.
        ///
        /// See: https://www.krakend.io/docs/telemetry/influxdb-native/
        /// </summary>
        [JsonProperty("username", NullValueHandling = NullValueHandling.Ignore)]
        public string Username { get; set; }
    }

    /// <summary>
    /// Enterprise only. Send metrics and traces to your Instana dashboard.
    ///
    /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
    /// </summary>
    public partial class TelemetryInstanaClass
    {
        /// <summary>
        /// By default Instana uses localhost.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("AgentHost")]
        public string AgentHost { get; set; }

        /// <summary>
        /// By default instana uses 46999.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("AgentPort")]
        public long AgentPort { get; set; }

        /// <summary>
        /// Enables automatic continuous process profiling when true.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("EnableAutoProfile", NullValueHandling = NullValueHandling.Ignore)]
        public bool? EnableAutoProfile { get; set; }

        /// <summary>
        /// The number of spans to collect before flushing the buffer to the agent.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("ForceTransmissionStartingAt", NullValueHandling = NullValueHandling.Ignore)]
        public long? ForceTransmissionStartingAt { get; set; }

        /// <summary>
        /// Whether to include profiler calls into the profile or not.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("IncludeProfilerFrames", NullValueHandling = NullValueHandling.Ignore)]
        public bool? IncludeProfilerFrames { get; set; }

        /// <summary>
        /// One of Error 0, Warn 1, Info 2 or Debug 3.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("LogLevel", NullValueHandling = NullValueHandling.Ignore)]
        public long? LogLevel { get; set; }

        /// <summary>
        /// The maximum number of profiles to buffer.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("MaxBufferedProfiles", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxBufferedProfiles { get; set; }

        /// <summary>
        /// The maximum number of spans to buffer.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("MaxBufferedSpans", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxBufferedSpans { get; set; }

        /// <summary>
        /// The global service name that will be used to identify the program in the Instana backend.
        /// The service name is set to the name of current executable by default.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("Service", NullValueHandling = NullValueHandling.Ignore)]
        public string Service { get; set; }

        /// <summary>
        /// Tracer-specific configuration used by all tracers
        /// </summary>
        [JsonProperty("Tracer", NullValueHandling = NullValueHandling.Ignore)]
        public TelemetryInstanaTracer Tracer { get; set; }
    }

    /// <summary>
    /// Tracer-specific configuration used by all tracers
    /// </summary>
    public partial class TelemetryInstanaTracer
    {
        /// <summary>
        /// Case-insensitive list of [HTTP headers to be
        /// collected](https://www.instana.com/docs/setup_and_manage/host_agent/configuration/#capture-custom-http-headers)
        /// from HTTP requests and sent to the agent.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("CollectableHTTPHeaders", NullValueHandling = NullValueHandling.Ignore)]
        public object[] CollectableHttpHeaders { get; set; }

        /// <summary>
        /// Turns log events on all spans into no-ops.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("DropAllLogs", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DropAllLogs { get; set; }

        /// <summary>
        /// MaxLogsPerSpan limits the number of log records in a span (if set to a non-zero value).
        /// If a span has more logs than this value, logs are dropped as necessary.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("MaxLogsPerSpan", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxLogsPerSpan { get; set; }

        /// <summary>
        /// A secrets matcher used to filter out sensitive data from HTTP requests, database
        /// connection strings, etc. By default tracer does not filter any values. [More info on
        /// secrets](https://www.instana.com/docs/setup_and_manage/host_agent/configuration/#secrets)
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("Secrets", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Secrets { get; set; }
    }

    /// <summary>
    /// Enables the extended logging capabilities.
    ///
    /// See: https://www.krakend.io/docs/logging/
    /// </summary>
    public partial class ImprovedLogging
    {
        /// <summary>
        /// Lets you write a custom logging pattern using variables, e.g: `%{message}`.
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("custom_format", NullValueHandling = NullValueHandling.Ignore)]
        public string CustomFormat { get; set; }

        /// <summary>
        /// Specify the format of the logs: default, logstash, or custom.
        /// The custom format needs an additional key "custom_format".
        /// The "logstash" format needs the "telemetry/logstash" component added too.
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("format", NullValueHandling = NullValueHandling.Ignore)]
        public string Format { get; set; }

        /// <summary>
        /// What type of **reporting level** do you expect from the application? Use the `DEBUG`
        /// level in the development stages but not in production. Possible values are from more
        /// verbose to least.
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("level")]
        public LogLevel Level { get; set; }

        /// <summary>
        /// Adds the defined string at the beginning of every logged line, so you can quickly filter
        /// messages with external tools later on. It's recommended to always add a prefix `[INSIDE
        /// BRACKETS]` to make use of predefined dashboards.
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("prefix", NullValueHandling = NullValueHandling.Ignore)]
        public string Prefix { get; set; }

        /// <summary>
        /// Set to true to send logs to stdout.
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("stdout", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Stdout { get; set; }

        /// <summary>
        /// Set to true to send logs to syslog.
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("syslog", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Syslog { get; set; }

        /// <summary>
        /// When using syslog, the facility tells KrakenD where to send the messages as set by the
        /// locals of the [syslog standard](https://www.rfc-editor.org/rfc/rfc5424.html).
        ///
        /// See: https://www.krakend.io/docs/logging/
        /// </summary>
        [JsonProperty("syslog_facility", NullValueHandling = NullValueHandling.Ignore)]
        public SyslogFacility? SyslogFacility { get; set; }
    }

    /// <summary>
    /// Enables logstash when the extra_config "telemetry/logging" is also present.
    ///
    /// See: https://www.krakend.io/docs/logging/logstash/
    /// </summary>
    public partial class Logstash
    {
        [JsonProperty("enabled")]
        public bool Enabled { get; set; }
    }

    /// <summary>
    /// Collects extended metrics to push to InfluxDB or expose them in the /__stats/ endpoint.
    ///
    /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
    /// </summary>
    public partial class ExtendedMetrics
    {
        /// <summary>
        /// Skip any metrics happening in the backend layer.
        ///
        /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
        /// </summary>
        [JsonProperty("backend_disabled", NullValueHandling = NullValueHandling.Ignore)]
        public bool? BackendDisabled { get; set; }

        /// <summary>
        /// The time window to collect metrics
        ///
        /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
        /// </summary>
        [JsonProperty("collection_time", NullValueHandling = NullValueHandling.Ignore)]
        public string CollectionTime { get; set; }

        /// <summary>
        /// When true do not publish the /__stats/ endpoint. Metrics won't be accessible via the
        /// endpoint but still collected.
        ///
        /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
        /// </summary>
        [JsonProperty("endpoint_disabled", NullValueHandling = NullValueHandling.Ignore)]
        public bool? EndpointDisabled { get; set; }

        /// <summary>
        /// Change the listening address where the metrics endpoint is exposed.
        ///
        /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
        /// </summary>
        [JsonProperty("listen_address")]
        public string ListenAddress { get; set; }

        /// <summary>
        /// Skip any metrics happening in the proxy layer (traffic against your backends).
        ///
        /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
        /// </summary>
        [JsonProperty("proxy_disabled", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ProxyDisabled { get; set; }

        /// <summary>
        /// Skip any metrics happening in the router layer (activity in KrakenD endpoints).
        ///
        /// See: https://www.krakend.io/docs/telemetry/extended-metrics/
        /// </summary>
        [JsonProperty("router_disabled", NullValueHandling = NullValueHandling.Ignore)]
        public bool? RouterDisabled { get; set; }
    }

    /// <summary>
    /// The New Relic integration lets you push KrakenD metrics and distributed traces to your
    /// New Relic dashboard. It uses internally the official New Relic SDK and brings its
    /// features to your APM dashboard.
    ///
    /// See: https://www.krakend.io/docs/enterprise/telemetry/newrelic/
    /// </summary>
    public partial class NewRelicExporter
    {
        /// <summary>
        /// Set to true when configuring New Relic for the first time while in development, to see
        /// the activity in the logs. Set to false in production.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/newrelic/
        /// </summary>
        [JsonProperty("debug", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Debug { get; set; }

        /// <summary>
        /// The API key provided by New Relic to push data into your account.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/newrelic/
        /// </summary>
        [JsonProperty("license")]
        public string License { get; set; }
    }

    /// <summary>
    /// Enterprise only. Send metrics and traces to your Instana dashboard.
    ///
    /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
    /// </summary>
    public partial class TelemetryOpencensusClass
    {
        /// <summary>
        /// By default Instana uses localhost.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("AgentHost")]
        public string AgentHost { get; set; }

        /// <summary>
        /// By default instana uses 46999.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("AgentPort")]
        public long AgentPort { get; set; }

        /// <summary>
        /// Enables automatic continuous process profiling when true.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("EnableAutoProfile", NullValueHandling = NullValueHandling.Ignore)]
        public bool? EnableAutoProfile { get; set; }

        /// <summary>
        /// The number of spans to collect before flushing the buffer to the agent.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("ForceTransmissionStartingAt", NullValueHandling = NullValueHandling.Ignore)]
        public long? ForceTransmissionStartingAt { get; set; }

        /// <summary>
        /// Whether to include profiler calls into the profile or not.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("IncludeProfilerFrames", NullValueHandling = NullValueHandling.Ignore)]
        public bool? IncludeProfilerFrames { get; set; }

        /// <summary>
        /// One of Error 0, Warn 1, Info 2 or Debug 3.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("LogLevel", NullValueHandling = NullValueHandling.Ignore)]
        public long? LogLevel { get; set; }

        /// <summary>
        /// The maximum number of profiles to buffer.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("MaxBufferedProfiles", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxBufferedProfiles { get; set; }

        /// <summary>
        /// The maximum number of spans to buffer.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("MaxBufferedSpans", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxBufferedSpans { get; set; }

        /// <summary>
        /// The global service name that will be used to identify the program in the Instana backend.
        /// The service name is set to the name of current executable by default.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("Service", NullValueHandling = NullValueHandling.Ignore)]
        public string Service { get; set; }

        /// <summary>
        /// Tracer-specific configuration used by all tracers
        /// </summary>
        [JsonProperty("Tracer", NullValueHandling = NullValueHandling.Ignore)]
        public TelemetryOpencensusTracer Tracer { get; set; }
    }

    /// <summary>
    /// Tracer-specific configuration used by all tracers
    /// </summary>
    public partial class TelemetryOpencensusTracer
    {
        /// <summary>
        /// Case-insensitive list of [HTTP headers to be
        /// collected](https://www.instana.com/docs/setup_and_manage/host_agent/configuration/#capture-custom-http-headers)
        /// from HTTP requests and sent to the agent.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("CollectableHTTPHeaders", NullValueHandling = NullValueHandling.Ignore)]
        public object[] CollectableHttpHeaders { get; set; }

        /// <summary>
        /// Turns log events on all spans into no-ops.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("DropAllLogs", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DropAllLogs { get; set; }

        /// <summary>
        /// MaxLogsPerSpan limits the number of log records in a span (if set to a non-zero value).
        /// If a span has more logs than this value, logs are dropped as necessary.
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("MaxLogsPerSpan", NullValueHandling = NullValueHandling.Ignore)]
        public long? MaxLogsPerSpan { get; set; }

        /// <summary>
        /// A secrets matcher used to filter out sensitive data from HTTP requests, database
        /// connection strings, etc. By default tracer does not filter any values. [More info on
        /// secrets](https://www.instana.com/docs/setup_and_manage/host_agent/configuration/#secrets)
        ///
        /// See: https://www.krakend.io/docs/enterprise/telemetry/instana/
        /// </summary>
        [JsonProperty("Secrets", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, object> Secrets { get; set; }
    }

    /// <summary>
    /// Enables external plugins that are copied in a specific folder
    /// </summary>
    public partial class Plugin
    {
        /// <summary>
        /// The path in the filesystem where all the plugins you want to load are. MUST END IN SLASH.
        /// The folder can be a relative or absolute path. KrakenD Enterprise uses
        /// /opt/krakend/plugins/ for all plugins.
        /// </summary>
        [JsonProperty("folder")]
        public string Folder { get; set; }

        /// <summary>
        /// The pattern narrows down the contents of the folder. It represents the substring that
        /// must be present in the plugin name to load.
        /// </summary>
        [JsonProperty("pattern")]
        public string Pattern { get; set; }
    }

    /// <summary>
    /// Enabling TLS for HTTPS and HTTP/2.
    ///
    /// See: https://www.krakend.io/docs/service-settings/tls/
    /// </summary>
    public partial class TlsSsl
    {
        /// <summary>
        /// An array with all the CA certificates you would like to load to KrakenD **when using
        /// mTLS**, in addition to the certificates present in the system's CA.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("ca_certs", NullValueHandling = NullValueHandling.Ignore)]
        public object[] CaCerts { get; set; }

        /// <summary>
        /// The list of cipher suites
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("cipher_suites", NullValueHandling = NullValueHandling.Ignore)]
        public object[] CipherSuites { get; set; }

        /// <summary>
        /// The list of all the identifiers for the curve preferences. Use `23` for CurveP256, `24`
        /// for CurveP384 or `25` for CurveP521.
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("curve_preferences", NullValueHandling = NullValueHandling.Ignore)]
        public CurveIdentifier[] CurvePreferences { get; set; }

        /// <summary>
        /// Make that any certificate in the system's CA is not recognized by KrakenD. The only
        /// certificates loaded will be the ones in the `ca_certs` list when true.
        ///
        /// See: https://www.krakend.io/docs/service-settings/http-server-settings/
        /// </summary>
        [JsonProperty("disable_system_ca_pool", NullValueHandling = NullValueHandling.Ignore)]
        public bool? DisableSystemCaPool { get; set; }

        /// <summary>
        /// A flag to disable TLS (useful while in development).
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("disabled", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Disabled { get; set; }

        /// <summary>
        /// Whether to enable or not Mutual Authentication. When mTLS is enabled, **all KrakenD
        /// endpoints** require clients to provide a known client-side X.509 authentication
        /// certificate. KrakenD relies on the system’s CA to validate certificates.
        ///
        /// See: https://www.krakend.io/docs/authorization/mutual-authentication/
        /// </summary>
        [JsonProperty("enable_mtls", NullValueHandling = NullValueHandling.Ignore)]
        public bool? EnableMtls { get; set; }

        /// <summary>
        /// Maximum TLS version supported.
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("max_version", NullValueHandling = NullValueHandling.Ignore)]
        public ImumTlsVersion? MaxVersion { get; set; }

        /// <summary>
        /// Minimum TLS version supported.
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("min_version", NullValueHandling = NullValueHandling.Ignore)]
        public ImumTlsVersion? MinVersion { get; set; }

        /// <summary>
        /// Enforces the use of one of the cipher suites offered by the server, instead of going with
        /// the suite proposed by the client.
        ///
        /// See: https://www.krakend.io/docs/authorization/mutual-authentication/
        /// </summary>
        [JsonProperty("prefer_server_cipher_suites", NullValueHandling = NullValueHandling.Ignore)]
        public bool? PreferServerCipherSuites { get; set; }

        /// <summary>
        /// Absolute path to the private key, or relative to the current working directory.
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("private_key")]
        public string PrivateKey { get; set; }

        /// <summary>
        /// Absolute path to the public key, or relative to the current working directory.
        ///
        /// See: https://www.krakend.io/docs/service-settings/tls/
        /// </summary>
        [JsonProperty("public_key")]
        public string PublicKey { get; set; }
    }

    /// <summary>
    /// Defines your [needed encoding](/docs/backends/supported-encodings/) to set how to parse
    /// the response. Defaults to the value of its endpoint's `encoding`, or to `json` if not
    /// defined anywhere else.
    ///
    /// See: https://www.krakend.io/docs/backends/supported-encodings/
    /// </summary>
    public enum BackendEncoding { FastJson, Json, NoOp, Rss, Safejson, String, Xml };

    /// <summary>
    /// The type of query you are declaring.
    ///
    /// See: https://www.krakend.io/docs/backends/graphql/
    /// </summary>
    public enum QueryType { Mutation, Query };

    /// <summary>
    /// The types of operations are defined as follows.
    ///
    /// See: https://www.krakend.io/docs/backends/flatmap/
    /// </summary>
    public enum TypeEnum { Append, Del, Move };

    /// <summary>
    /// The method sent to this backend in **uppercase**. The method does not need to match the
    /// endpoint's method. When the value is omitted, it uses the same endpoint's method.
    ///
    /// See: https://www.krakend.io/docs/backends/
    ///
    /// The method supported by this endpoint. Create multiple endpoint entries if you need
    /// different methods.
    ///
    /// See: https://www.krakend.io/docs/endpoints/
    /// </summary>
    public enum Method { Delete, Get, Patch, Post, Put };

    /// <summary>
    /// The [Service Discovery](/docs/backends/service-discovery/) system to resolve your backend
    /// services. Defaults to `static` (no external Service Discovery). Use `dns` to use DNS SRV
    /// records.
    ///
    /// See: https://www.krakend.io/docs/backends/
    /// </summary>
    public enum ServiceDiscovery { Dns, Static };

    /// <summary>
    /// When the connection to your event source gets interrupted for whatever reason, KrakenD
    /// keeps trying to reconnect until it succeeds or until it reaches the `max_retries`. The
    /// backoff strategy defines the delay in seconds in between consecutive failed retries.
    ///
    /// See: https://www.krakend.io/docs/async/
    ///
    /// When the connection to your event source gets interrupted for whatever reason, KrakenD
    /// keeps trying to reconnect until it succeeds or until it reaches the max_retries. The
    /// backoff strategy defines the delay in seconds in between consecutive failed retries.
    /// Defaults to 'fallback'
    ///
    /// See: https://www.krakend.io/docs/enterprise/websockets/
    /// </summary>
    public enum BackoffStrategy { Exponential, ExponentialJitter, Fallback, Linear, LinearJitter };

    /// <summary>
    /// Informs KrakenD how to parse the responses of your services.
    ///
    /// See: https://www.krakend.io/docs/backends/supported-encodings/
    /// </summary>
    public enum AsyncAgentEncoding { Json, NoOp, Rss, Safejson, String, Xml };

    /// <summary>
    /// The hashing algorithm used by the issuer. Usually `RS256`. The algorithm you choose
    /// directly affects the CPU consumption.
    ///
    /// See: https://www.krakend.io/docs/authorization/jwt-validation/
    ///
    /// The hashing algorithm used by the token issuer.
    ///
    /// See: https://www.krakend.io/docs/authorization/jwt-validation/
    /// </summary>
    public enum Algorithm { EdDsa, Es256, Es384, Es512, Hs256, Hs384, Hs512, Ps256, Ps384, Ps512, Rs256, Rs384, Rs512 };

    /// <summary>
    /// Allows strategies other than `kid` to load keys.
    ///
    /// See: https://www.krakend.io/docs/authorization/jwt-validation/
    /// </summary>
    public enum KeyIdentifyStrategy { Kid, KidX5T, X5T };

    /// <summary>
    /// Defines if the user needs to have in its token at least one of the listed claims (`any`),
    /// or `all` of them.
    ///
    /// See: https://www.krakend.io/docs/authorization/jwt-validation/
    /// </summary>
    public enum ScopesMatcher { All, Any };

    /// <summary>
    /// One of the supported strategies
    ///
    /// See: https://www.krakend.io/docs/endpoints/static-proxy/
    /// </summary>
    public enum StaticStrategy { Always, Complete, Errored, Incomplete, Success };

    /// <summary>
    /// Available when using `client_max_rate`. Sets the strategy you will use to set client
    /// counters. Choose `ip` when the restrictions apply to the client's IP address, or set it
    /// to `header` when there is a header that identifies a user uniquely. That header must be
    /// defined with the `key` entry.
    ///
    /// See: https://www.krakend.io/docs/endpoints/rate-limit/
    /// </summary>
    public enum QosRatelimitServiceStrategy { Header, Ip };

    /// <summary>
    /// See: https://www.krakend.io
    /// </summary>
    public enum AllowMethodElement { Delete, Get, Head, Options, Patch, Post, Put };

    /// <summary>
    /// The gateway can work with several content types, even allowing your clients to choose how
    /// to consume the content. See the [supported encodings](/docs/endpoints/content-types/)
    ///
    /// The encoding used to display the content to the end-user.
    ///
    /// See: https://www.krakend.io/docs/endpoints/content-types/
    /// </summary>
    public enum OutputEncoding { FastJson, Json, JsonCollection, Negotiate, NoOp, String, Xml };

    /// <summary>
    /// One of header or query_string. Specifies where to expect the user API key, whether inside
    /// a header or as part of the query string. Defaults to header.
    ///
    /// See: https://www.krakend.io/docs/enterprise/authentication/api-keys/
    /// </summary>
    public enum AuthApiKeysStrategy { Header, QueryString };

    /// <summary>
    /// Either `optimal` (recommended) or `default`. The `optimal` consumes less CPU but has less
    /// entropy when generating the hash, although the loss is negligible.
    ///
    /// See: https://www.krakend.io/docs/authorization/revoking-tokens/
    /// </summary>
    public enum HashFunctionName { Default, Optimal };

    /// <summary>
    /// One of the preselected strategies to rate-limit users.
    ///
    /// See: https://www.krakend.io/docs/enterprise/endpoints/global-rate-limit/
    /// </summary>
    public enum Tokenizer { Cookie, Header, Ip, Jwt, Param, Path, Url };

    /// <summary>
    /// What type of **reporting level** do you expect from the application? Use the `DEBUG`
    /// level in the development stages but not in production. Possible values are from more
    /// verbose to least.
    ///
    /// See: https://www.krakend.io/docs/logging/
    /// </summary>
    public enum LogLevel { Critical, Debug, Error, Info, Warning };

    /// <summary>
    /// When using syslog, the facility tells KrakenD where to send the messages as set by the
    /// locals of the [syslog standard](https://www.rfc-editor.org/rfc/rfc5424.html).
    ///
    /// See: https://www.krakend.io/docs/logging/
    /// </summary>
    public enum SyslogFacility { Local0, Local1, Local2, Local3, Local4, Local5, Local6, Local7 };

    /// <summary>
    /// Maximum TLS version supported.
    ///
    /// See: https://www.krakend.io/docs/service-settings/tls/
    ///
    /// Minimum TLS version supported.
    ///
    /// See: https://www.krakend.io/docs/service-settings/tls/
    /// </summary>
    public enum ImumTlsVersion { Ssl30, Tls10, Tls11, Tls12, Tls13 };

    public partial struct BackendHttpUnion
    {
        public object[] AnythingArray;
        public BackendHttpClass BackendHttpClass;
        public bool? Bool;
        public double? Double;
        public long? Integer;
        public string String;

        public static implicit operator BackendHttpUnion(object[] AnythingArray) => new BackendHttpUnion { AnythingArray = AnythingArray };
        public static implicit operator BackendHttpUnion(BackendHttpClass BackendHttpClass) => new BackendHttpUnion { BackendHttpClass = BackendHttpClass };
        public static implicit operator BackendHttpUnion(bool Bool) => new BackendHttpUnion { Bool = Bool };
        public static implicit operator BackendHttpUnion(double Double) => new BackendHttpUnion { Double = Double };
        public static implicit operator BackendHttpUnion(long Integer) => new BackendHttpUnion { Integer = Integer };
        public static implicit operator BackendHttpUnion(string String) => new BackendHttpUnion { String = String };
        public bool IsNull => AnythingArray == null && Bool == null && BackendHttpClass == null && Double == null && Integer == null && String == null;
    }

    /// <summary>
    /// Defines all the settings for each agent consuming messages.
    ///
    /// See: https://www.krakend.io/docs/async/
    /// </summary>
    public partial struct Consumer
    {
        public object[] AnythingArray;
        public bool? Bool;
        public ConsumerClass ConsumerClass;
        public double? Double;
        public long? Integer;
        public string String;

        public static implicit operator Consumer(object[] AnythingArray) => new Consumer { AnythingArray = AnythingArray };
        public static implicit operator Consumer(bool Bool) => new Consumer { Bool = Bool };
        public static implicit operator Consumer(ConsumerClass ConsumerClass) => new Consumer { ConsumerClass = ConsumerClass };
        public static implicit operator Consumer(double Double) => new Consumer { Double = Double };
        public static implicit operator Consumer(long Integer) => new Consumer { Integer = Integer };
        public static implicit operator Consumer(string String) => new Consumer { String = String };
        public bool IsNull => AnythingArray == null && Bool == null && ConsumerClass == null && Double == null && Integer == null && String == null;
    }

    /// <summary>
    /// Defines the driver that connects to your queue or PubSub system. In addition, you can
    /// place other middlewares to modify the request (message) or the response, apply logic or
    /// any other endpoint middleware, but adding the driver is mandatory.
    ///
    /// See: https://www.krakend.io/docs/async/
    /// </summary>
    public partial struct ExtraConfigUnion
    {
        public object[] AnythingArray;
        public bool? Bool;
        public double? Double;
        public ExtraConfigClass ExtraConfigClass;
        public long? Integer;
        public string String;

        public static implicit operator ExtraConfigUnion(object[] AnythingArray) => new ExtraConfigUnion { AnythingArray = AnythingArray };
        public static implicit operator ExtraConfigUnion(bool Bool) => new ExtraConfigUnion { Bool = Bool };
        public static implicit operator ExtraConfigUnion(double Double) => new ExtraConfigUnion { Double = Double };
        public static implicit operator ExtraConfigUnion(ExtraConfigClass ExtraConfigClass) => new ExtraConfigUnion { ExtraConfigClass = ExtraConfigClass };
        public static implicit operator ExtraConfigUnion(long Integer) => new ExtraConfigUnion { Integer = Integer };
        public static implicit operator ExtraConfigUnion(string String) => new ExtraConfigUnion { String = String };
        public bool IsNull => AnythingArray == null && Bool == null && ExtraConfigClass == null && Double == null && Integer == null && String == null;
    }

    /// <summary>
    /// Async agents are routines listening to queues or PubSub systems that react to new events
    /// and push data to your backends. Through async agents, you can start a lot of consumers to
    /// process your events autonomously.
    ///
    /// See: https://www.krakend.io/docs/async/
    /// </summary>
    public partial struct AsyncAgentElement
    {
        public object[] AnythingArray;
        public AsyncAgentClass AsyncAgentClass;
        public bool? Bool;
        public double? Double;
        public long? Integer;
        public string String;

        public static implicit operator AsyncAgentElement(object[] AnythingArray) => new AsyncAgentElement { AnythingArray = AnythingArray };
        public static implicit operator AsyncAgentElement(AsyncAgentClass AsyncAgentClass) => new AsyncAgentElement { AsyncAgentClass = AsyncAgentClass };
        public static implicit operator AsyncAgentElement(bool Bool) => new AsyncAgentElement { Bool = Bool };
        public static implicit operator AsyncAgentElement(double Double) => new AsyncAgentElement { Double = Double };
        public static implicit operator AsyncAgentElement(long Integer) => new AsyncAgentElement { Integer = Integer };
        public static implicit operator AsyncAgentElement(string String) => new AsyncAgentElement { String = String };
        public bool IsNull => AnythingArray == null && Bool == null && AsyncAgentClass == null && Double == null && Integer == null && String == null;
    }

    /// <summary>
    /// A free form JSON object or a string you would like to show as a sample response of the
    /// endpoint. The examples assume they are JSON content types except when using the
    /// `output_encoding=string`.
    ///
    /// See: https://www.krakend.io/docs/enterprise/developer/openapi/
    /// </summary>
    public partial struct Example
    {
        public Dictionary<string, object> AnythingMap;
        public string String;

        public static implicit operator Example(Dictionary<string, object> AnythingMap) => new Example { AnythingMap = AnythingMap };
        public static implicit operator Example(string String) => new Example { String = String };
    }

    public partial struct CurveIdentifier
    {
        public double? Double;
        public long? Integer;

        public static implicit operator CurveIdentifier(double Double) => new CurveIdentifier { Double = Double };
        public static implicit operator CurveIdentifier(long Integer) => new CurveIdentifier { Integer = Integer };
    }

    public partial class KrakendDocument
    {
        public static KrakendDocument FromJson(string json) => JsonConvert.DeserializeObject<KrakendDocument>(json, Sleekflow.KrakenD.Generator.Converter.Settings);
    }

    public static class Serialize
    {
        public static string ToJson(this KrakendDocument self) => JsonConvert.SerializeObject(self, Sleekflow.KrakenD.Generator.Converter.Settings);
    }

    internal static class Converter
    {
        public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
        {
            MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
            DateParseHandling = DateParseHandling.None,
            Converters =
            {
                AsyncAgentElementConverter.Singleton,
                BackendEncodingConverter.Singleton,
                QueryTypeConverter.Singleton,
                BackendHttpUnionConverter.Singleton,
                TypeEnumConverter.Singleton,
                MethodConverter.Singleton,
                ServiceDiscoveryConverter.Singleton,
                BackoffStrategyConverter.Singleton,
                ConsumerConverter.Singleton,
                AsyncAgentEncodingConverter.Singleton,
                ExtraConfigUnionConverter.Singleton,
                AlgorithmConverter.Singleton,
                KeyIdentifyStrategyConverter.Singleton,
                ScopesMatcherConverter.Singleton,
                ExampleConverter.Singleton,
                StaticStrategyConverter.Singleton,
                QosRatelimitServiceStrategyConverter.Singleton,
                AllowMethodElementConverter.Singleton,
                OutputEncodingConverter.Singleton,
                AuthApiKeysStrategyConverter.Singleton,
                HashFunctionNameConverter.Singleton,
                TokenizerConverter.Singleton,
                LogLevelConverter.Singleton,
                SyslogFacilityConverter.Singleton,
                CurveIdentifierConverter.Singleton,
                ImumTlsVersionConverter.Singleton,
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
            Formatting = Formatting.Indented
        };
    }

    internal class AsyncAgentElementConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(AsyncAgentElement) || t == typeof(AsyncAgentElement?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.Null:
                    return new AsyncAgentElement { };
                case JsonToken.Integer:
                    var integerValue = serializer.Deserialize<long>(reader);
                    return new AsyncAgentElement { Integer = integerValue };
                case JsonToken.Float:
                    var doubleValue = serializer.Deserialize<double>(reader);
                    return new AsyncAgentElement { Double = doubleValue };
                case JsonToken.Boolean:
                    var boolValue = serializer.Deserialize<bool>(reader);
                    return new AsyncAgentElement { Bool = boolValue };
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new AsyncAgentElement { String = stringValue };
                case JsonToken.StartObject:
                    var objectValue = serializer.Deserialize<AsyncAgentClass>(reader);
                    return new AsyncAgentElement { AsyncAgentClass = objectValue };
                case JsonToken.StartArray:
                    var arrayValue = serializer.Deserialize<object[]>(reader);
                    return new AsyncAgentElement { AnythingArray = arrayValue };
            }
            throw new Exception("Cannot unmarshal type AsyncAgentElement");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (AsyncAgentElement)untypedValue;
            if (value.IsNull)
            {
                serializer.Serialize(writer, null);
                return;
            }
            if (value.Integer != null)
            {
                serializer.Serialize(writer, value.Integer.Value);
                return;
            }
            if (value.Double != null)
            {
                serializer.Serialize(writer, value.Double.Value);
                return;
            }
            if (value.Bool != null)
            {
                serializer.Serialize(writer, value.Bool.Value);
                return;
            }
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            if (value.AnythingArray != null)
            {
                serializer.Serialize(writer, value.AnythingArray);
                return;
            }
            if (value.AsyncAgentClass != null)
            {
                serializer.Serialize(writer, value.AsyncAgentClass);
                return;
            }
            throw new Exception("Cannot marshal type AsyncAgentElement");
        }

        public static readonly AsyncAgentElementConverter Singleton = new AsyncAgentElementConverter();
    }

    internal class BackendEncodingConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(BackendEncoding) || t == typeof(BackendEncoding?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "fast-json":
                    return BackendEncoding.FastJson;
                case "json":
                    return BackendEncoding.Json;
                case "no-op":
                    return BackendEncoding.NoOp;
                case "rss":
                    return BackendEncoding.Rss;
                case "safejson":
                    return BackendEncoding.Safejson;
                case "string":
                    return BackendEncoding.String;
                case "xml":
                    return BackendEncoding.Xml;
            }
            throw new Exception("Cannot unmarshal type BackendEncoding");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (BackendEncoding)untypedValue;
            switch (value)
            {
                case BackendEncoding.FastJson:
                    serializer.Serialize(writer, "fast-json");
                    return;
                case BackendEncoding.Json:
                    serializer.Serialize(writer, "json");
                    return;
                case BackendEncoding.NoOp:
                    serializer.Serialize(writer, "no-op");
                    return;
                case BackendEncoding.Rss:
                    serializer.Serialize(writer, "rss");
                    return;
                case BackendEncoding.Safejson:
                    serializer.Serialize(writer, "safejson");
                    return;
                case BackendEncoding.String:
                    serializer.Serialize(writer, "string");
                    return;
                case BackendEncoding.Xml:
                    serializer.Serialize(writer, "xml");
                    return;
            }
            throw new Exception("Cannot marshal type BackendEncoding");
        }

        public static readonly BackendEncodingConverter Singleton = new BackendEncodingConverter();
    }

    internal class QueryTypeConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(QueryType) || t == typeof(QueryType?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "mutation":
                    return QueryType.Mutation;
                case "query":
                    return QueryType.Query;
            }
            throw new Exception("Cannot unmarshal type QueryType");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (QueryType)untypedValue;
            switch (value)
            {
                case QueryType.Mutation:
                    serializer.Serialize(writer, "mutation");
                    return;
                case QueryType.Query:
                    serializer.Serialize(writer, "query");
                    return;
            }
            throw new Exception("Cannot marshal type QueryType");
        }

        public static readonly QueryTypeConverter Singleton = new QueryTypeConverter();
    }

    internal class BackendHttpUnionConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(BackendHttpUnion) || t == typeof(BackendHttpUnion?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.Null:
                    return new BackendHttpUnion { };
                case JsonToken.Integer:
                    var integerValue = serializer.Deserialize<long>(reader);
                    return new BackendHttpUnion { Integer = integerValue };
                case JsonToken.Float:
                    var doubleValue = serializer.Deserialize<double>(reader);
                    return new BackendHttpUnion { Double = doubleValue };
                case JsonToken.Boolean:
                    var boolValue = serializer.Deserialize<bool>(reader);
                    return new BackendHttpUnion { Bool = boolValue };
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new BackendHttpUnion { String = stringValue };
                case JsonToken.StartObject:
                    var objectValue = serializer.Deserialize<BackendHttpClass>(reader);
                    return new BackendHttpUnion { BackendHttpClass = objectValue };
                case JsonToken.StartArray:
                    var arrayValue = serializer.Deserialize<object[]>(reader);
                    return new BackendHttpUnion { AnythingArray = arrayValue };
            }
            throw new Exception("Cannot unmarshal type BackendHttpUnion");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (BackendHttpUnion)untypedValue;
            if (value.IsNull)
            {
                serializer.Serialize(writer, null);
                return;
            }
            if (value.Integer != null)
            {
                serializer.Serialize(writer, value.Integer.Value);
                return;
            }
            if (value.Double != null)
            {
                serializer.Serialize(writer, value.Double.Value);
                return;
            }
            if (value.Bool != null)
            {
                serializer.Serialize(writer, value.Bool.Value);
                return;
            }
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            if (value.AnythingArray != null)
            {
                serializer.Serialize(writer, value.AnythingArray);
                return;
            }
            if (value.BackendHttpClass != null)
            {
                serializer.Serialize(writer, value.BackendHttpClass);
                return;
            }
            throw new Exception("Cannot marshal type BackendHttpUnion");
        }

        public static readonly BackendHttpUnionConverter Singleton = new BackendHttpUnionConverter();
    }

    internal class TypeEnumConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(TypeEnum) || t == typeof(TypeEnum?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "append":
                    return TypeEnum.Append;
                case "del":
                    return TypeEnum.Del;
                case "move":
                    return TypeEnum.Move;
            }
            throw new Exception("Cannot unmarshal type TypeEnum");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (TypeEnum)untypedValue;
            switch (value)
            {
                case TypeEnum.Append:
                    serializer.Serialize(writer, "append");
                    return;
                case TypeEnum.Del:
                    serializer.Serialize(writer, "del");
                    return;
                case TypeEnum.Move:
                    serializer.Serialize(writer, "move");
                    return;
            }
            throw new Exception("Cannot marshal type TypeEnum");
        }

        public static readonly TypeEnumConverter Singleton = new TypeEnumConverter();
    }

    internal class MethodConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Method) || t == typeof(Method?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "DELETE":
                    return Method.Delete;
                case "GET":
                    return Method.Get;
                case "PATCH":
                    return Method.Patch;
                case "POST":
                    return Method.Post;
                case "PUT":
                    return Method.Put;
            }
            throw new Exception("Cannot unmarshal type Method");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (Method)untypedValue;
            switch (value)
            {
                case Method.Delete:
                    serializer.Serialize(writer, "DELETE");
                    return;
                case Method.Get:
                    serializer.Serialize(writer, "GET");
                    return;
                case Method.Patch:
                    serializer.Serialize(writer, "PATCH");
                    return;
                case Method.Post:
                    serializer.Serialize(writer, "POST");
                    return;
                case Method.Put:
                    serializer.Serialize(writer, "PUT");
                    return;
            }
            throw new Exception("Cannot marshal type Method");
        }

        public static readonly MethodConverter Singleton = new MethodConverter();
    }

    internal class ServiceDiscoveryConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(ServiceDiscovery) || t == typeof(ServiceDiscovery?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "dns":
                    return ServiceDiscovery.Dns;
                case "static":
                    return ServiceDiscovery.Static;
            }
            throw new Exception("Cannot unmarshal type ServiceDiscovery");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (ServiceDiscovery)untypedValue;
            switch (value)
            {
                case ServiceDiscovery.Dns:
                    serializer.Serialize(writer, "dns");
                    return;
                case ServiceDiscovery.Static:
                    serializer.Serialize(writer, "static");
                    return;
            }
            throw new Exception("Cannot marshal type ServiceDiscovery");
        }

        public static readonly ServiceDiscoveryConverter Singleton = new ServiceDiscoveryConverter();
    }

    internal class BackoffStrategyConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(BackoffStrategy) || t == typeof(BackoffStrategy?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "exponential":
                    return BackoffStrategy.Exponential;
                case "exponential-jitter":
                    return BackoffStrategy.ExponentialJitter;
                case "fallback":
                    return BackoffStrategy.Fallback;
                case "linear":
                    return BackoffStrategy.Linear;
                case "linear-jitter":
                    return BackoffStrategy.LinearJitter;
            }
            throw new Exception("Cannot unmarshal type BackoffStrategy");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (BackoffStrategy)untypedValue;
            switch (value)
            {
                case BackoffStrategy.Exponential:
                    serializer.Serialize(writer, "exponential");
                    return;
                case BackoffStrategy.ExponentialJitter:
                    serializer.Serialize(writer, "exponential-jitter");
                    return;
                case BackoffStrategy.Fallback:
                    serializer.Serialize(writer, "fallback");
                    return;
                case BackoffStrategy.Linear:
                    serializer.Serialize(writer, "linear");
                    return;
                case BackoffStrategy.LinearJitter:
                    serializer.Serialize(writer, "linear-jitter");
                    return;
            }
            throw new Exception("Cannot marshal type BackoffStrategy");
        }

        public static readonly BackoffStrategyConverter Singleton = new BackoffStrategyConverter();
    }

    internal class ConsumerConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Consumer) || t == typeof(Consumer?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.Null:
                    return new Consumer { };
                case JsonToken.Integer:
                    var integerValue = serializer.Deserialize<long>(reader);
                    return new Consumer { Integer = integerValue };
                case JsonToken.Float:
                    var doubleValue = serializer.Deserialize<double>(reader);
                    return new Consumer { Double = doubleValue };
                case JsonToken.Boolean:
                    var boolValue = serializer.Deserialize<bool>(reader);
                    return new Consumer { Bool = boolValue };
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new Consumer { String = stringValue };
                case JsonToken.StartObject:
                    var objectValue = serializer.Deserialize<ConsumerClass>(reader);
                    return new Consumer { ConsumerClass = objectValue };
                case JsonToken.StartArray:
                    var arrayValue = serializer.Deserialize<object[]>(reader);
                    return new Consumer { AnythingArray = arrayValue };
            }
            throw new Exception("Cannot unmarshal type Consumer");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (Consumer)untypedValue;
            if (value.IsNull)
            {
                serializer.Serialize(writer, null);
                return;
            }
            if (value.Integer != null)
            {
                serializer.Serialize(writer, value.Integer.Value);
                return;
            }
            if (value.Double != null)
            {
                serializer.Serialize(writer, value.Double.Value);
                return;
            }
            if (value.Bool != null)
            {
                serializer.Serialize(writer, value.Bool.Value);
                return;
            }
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            if (value.AnythingArray != null)
            {
                serializer.Serialize(writer, value.AnythingArray);
                return;
            }
            if (value.ConsumerClass != null)
            {
                serializer.Serialize(writer, value.ConsumerClass);
                return;
            }
            throw new Exception("Cannot marshal type Consumer");
        }

        public static readonly ConsumerConverter Singleton = new ConsumerConverter();
    }

    internal class AsyncAgentEncodingConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(AsyncAgentEncoding) || t == typeof(AsyncAgentEncoding?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "json":
                    return AsyncAgentEncoding.Json;
                case "no-op":
                    return AsyncAgentEncoding.NoOp;
                case "rss":
                    return AsyncAgentEncoding.Rss;
                case "safejson":
                    return AsyncAgentEncoding.Safejson;
                case "string":
                    return AsyncAgentEncoding.String;
                case "xml":
                    return AsyncAgentEncoding.Xml;
            }
            throw new Exception("Cannot unmarshal type AsyncAgentEncoding");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (AsyncAgentEncoding)untypedValue;
            switch (value)
            {
                case AsyncAgentEncoding.Json:
                    serializer.Serialize(writer, "json");
                    return;
                case AsyncAgentEncoding.NoOp:
                    serializer.Serialize(writer, "no-op");
                    return;
                case AsyncAgentEncoding.Rss:
                    serializer.Serialize(writer, "rss");
                    return;
                case AsyncAgentEncoding.Safejson:
                    serializer.Serialize(writer, "safejson");
                    return;
                case AsyncAgentEncoding.String:
                    serializer.Serialize(writer, "string");
                    return;
                case AsyncAgentEncoding.Xml:
                    serializer.Serialize(writer, "xml");
                    return;
            }
            throw new Exception("Cannot marshal type AsyncAgentEncoding");
        }

        public static readonly AsyncAgentEncodingConverter Singleton = new AsyncAgentEncodingConverter();
    }

    internal class ExtraConfigUnionConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(ExtraConfigUnion) || t == typeof(ExtraConfigUnion?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.Null:
                    return new ExtraConfigUnion { };
                case JsonToken.Integer:
                    var integerValue = serializer.Deserialize<long>(reader);
                    return new ExtraConfigUnion { Integer = integerValue };
                case JsonToken.Float:
                    var doubleValue = serializer.Deserialize<double>(reader);
                    return new ExtraConfigUnion { Double = doubleValue };
                case JsonToken.Boolean:
                    var boolValue = serializer.Deserialize<bool>(reader);
                    return new ExtraConfigUnion { Bool = boolValue };
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new ExtraConfigUnion { String = stringValue };
                case JsonToken.StartObject:
                    var objectValue = serializer.Deserialize<ExtraConfigClass>(reader);
                    return new ExtraConfigUnion { ExtraConfigClass = objectValue };
                case JsonToken.StartArray:
                    var arrayValue = serializer.Deserialize<object[]>(reader);
                    return new ExtraConfigUnion { AnythingArray = arrayValue };
            }
            throw new Exception("Cannot unmarshal type ExtraConfigUnion");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (ExtraConfigUnion)untypedValue;
            if (value.IsNull)
            {
                serializer.Serialize(writer, null);
                return;
            }
            if (value.Integer != null)
            {
                serializer.Serialize(writer, value.Integer.Value);
                return;
            }
            if (value.Double != null)
            {
                serializer.Serialize(writer, value.Double.Value);
                return;
            }
            if (value.Bool != null)
            {
                serializer.Serialize(writer, value.Bool.Value);
                return;
            }
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            if (value.AnythingArray != null)
            {
                serializer.Serialize(writer, value.AnythingArray);
                return;
            }
            if (value.ExtraConfigClass != null)
            {
                serializer.Serialize(writer, value.ExtraConfigClass);
                return;
            }
            throw new Exception("Cannot marshal type ExtraConfigUnion");
        }

        public static readonly ExtraConfigUnionConverter Singleton = new ExtraConfigUnionConverter();
    }

    internal class AlgorithmConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Algorithm) || t == typeof(Algorithm?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "ES256":
                    return Algorithm.Es256;
                case "ES384":
                    return Algorithm.Es384;
                case "ES512":
                    return Algorithm.Es512;
                case "EdDSA":
                    return Algorithm.EdDsa;
                case "HS256":
                    return Algorithm.Hs256;
                case "HS384":
                    return Algorithm.Hs384;
                case "HS512":
                    return Algorithm.Hs512;
                case "PS256":
                    return Algorithm.Ps256;
                case "PS384":
                    return Algorithm.Ps384;
                case "PS512":
                    return Algorithm.Ps512;
                case "RS256":
                    return Algorithm.Rs256;
                case "RS384":
                    return Algorithm.Rs384;
                case "RS512":
                    return Algorithm.Rs512;
            }
            throw new Exception("Cannot unmarshal type Algorithm");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (Algorithm)untypedValue;
            switch (value)
            {
                case Algorithm.Es256:
                    serializer.Serialize(writer, "ES256");
                    return;
                case Algorithm.Es384:
                    serializer.Serialize(writer, "ES384");
                    return;
                case Algorithm.Es512:
                    serializer.Serialize(writer, "ES512");
                    return;
                case Algorithm.EdDsa:
                    serializer.Serialize(writer, "EdDSA");
                    return;
                case Algorithm.Hs256:
                    serializer.Serialize(writer, "HS256");
                    return;
                case Algorithm.Hs384:
                    serializer.Serialize(writer, "HS384");
                    return;
                case Algorithm.Hs512:
                    serializer.Serialize(writer, "HS512");
                    return;
                case Algorithm.Ps256:
                    serializer.Serialize(writer, "PS256");
                    return;
                case Algorithm.Ps384:
                    serializer.Serialize(writer, "PS384");
                    return;
                case Algorithm.Ps512:
                    serializer.Serialize(writer, "PS512");
                    return;
                case Algorithm.Rs256:
                    serializer.Serialize(writer, "RS256");
                    return;
                case Algorithm.Rs384:
                    serializer.Serialize(writer, "RS384");
                    return;
                case Algorithm.Rs512:
                    serializer.Serialize(writer, "RS512");
                    return;
            }
            throw new Exception("Cannot marshal type Algorithm");
        }

        public static readonly AlgorithmConverter Singleton = new AlgorithmConverter();
    }

    internal class KeyIdentifyStrategyConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(KeyIdentifyStrategy) || t == typeof(KeyIdentifyStrategy?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "kid":
                    return KeyIdentifyStrategy.Kid;
                case "kid_x5t":
                    return KeyIdentifyStrategy.KidX5T;
                case "x5t":
                    return KeyIdentifyStrategy.X5T;
            }
            throw new Exception("Cannot unmarshal type KeyIdentifyStrategy");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (KeyIdentifyStrategy)untypedValue;
            switch (value)
            {
                case KeyIdentifyStrategy.Kid:
                    serializer.Serialize(writer, "kid");
                    return;
                case KeyIdentifyStrategy.KidX5T:
                    serializer.Serialize(writer, "kid_x5t");
                    return;
                case KeyIdentifyStrategy.X5T:
                    serializer.Serialize(writer, "x5t");
                    return;
            }
            throw new Exception("Cannot marshal type KeyIdentifyStrategy");
        }

        public static readonly KeyIdentifyStrategyConverter Singleton = new KeyIdentifyStrategyConverter();
    }

    internal class ScopesMatcherConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(ScopesMatcher) || t == typeof(ScopesMatcher?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "all":
                    return ScopesMatcher.All;
                case "any":
                    return ScopesMatcher.Any;
            }
            throw new Exception("Cannot unmarshal type ScopesMatcher");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (ScopesMatcher)untypedValue;
            switch (value)
            {
                case ScopesMatcher.All:
                    serializer.Serialize(writer, "all");
                    return;
                case ScopesMatcher.Any:
                    serializer.Serialize(writer, "any");
                    return;
            }
            throw new Exception("Cannot marshal type ScopesMatcher");
        }

        public static readonly ScopesMatcherConverter Singleton = new ScopesMatcherConverter();
    }

    internal class ExampleConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Example) || t == typeof(Example?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new Example { String = stringValue };
                case JsonToken.StartObject:
                    var objectValue = serializer.Deserialize<Dictionary<string, object>>(reader);
                    return new Example { AnythingMap = objectValue };
            }
            throw new Exception("Cannot unmarshal type Example");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (Example)untypedValue;
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            if (value.AnythingMap != null)
            {
                serializer.Serialize(writer, value.AnythingMap);
                return;
            }
            throw new Exception("Cannot marshal type Example");
        }

        public static readonly ExampleConverter Singleton = new ExampleConverter();
    }

    internal class StaticStrategyConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(StaticStrategy) || t == typeof(StaticStrategy?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "always":
                    return StaticStrategy.Always;
                case "complete":
                    return StaticStrategy.Complete;
                case "errored":
                    return StaticStrategy.Errored;
                case "incomplete":
                    return StaticStrategy.Incomplete;
                case "success":
                    return StaticStrategy.Success;
            }
            throw new Exception("Cannot unmarshal type StaticStrategy");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (StaticStrategy)untypedValue;
            switch (value)
            {
                case StaticStrategy.Always:
                    serializer.Serialize(writer, "always");
                    return;
                case StaticStrategy.Complete:
                    serializer.Serialize(writer, "complete");
                    return;
                case StaticStrategy.Errored:
                    serializer.Serialize(writer, "errored");
                    return;
                case StaticStrategy.Incomplete:
                    serializer.Serialize(writer, "incomplete");
                    return;
                case StaticStrategy.Success:
                    serializer.Serialize(writer, "success");
                    return;
            }
            throw new Exception("Cannot marshal type StaticStrategy");
        }

        public static readonly StaticStrategyConverter Singleton = new StaticStrategyConverter();
    }

    internal class QosRatelimitServiceStrategyConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(QosRatelimitServiceStrategy) || t == typeof(QosRatelimitServiceStrategy?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "header":
                    return QosRatelimitServiceStrategy.Header;
                case "ip":
                    return QosRatelimitServiceStrategy.Ip;
            }
            throw new Exception("Cannot unmarshal type QosRatelimitServiceStrategy");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (QosRatelimitServiceStrategy)untypedValue;
            switch (value)
            {
                case QosRatelimitServiceStrategy.Header:
                    serializer.Serialize(writer, "header");
                    return;
                case QosRatelimitServiceStrategy.Ip:
                    serializer.Serialize(writer, "ip");
                    return;
            }
            throw new Exception("Cannot marshal type QosRatelimitServiceStrategy");
        }

        public static readonly QosRatelimitServiceStrategyConverter Singleton = new QosRatelimitServiceStrategyConverter();
    }

    internal class AllowMethodElementConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(AllowMethodElement) || t == typeof(AllowMethodElement?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "DELETE":
                    return AllowMethodElement.Delete;
                case "GET":
                    return AllowMethodElement.Get;
                case "HEAD":
                    return AllowMethodElement.Head;
                case "OPTIONS":
                    return AllowMethodElement.Options;
                case "PATCH":
                    return AllowMethodElement.Patch;
                case "POST":
                    return AllowMethodElement.Post;
                case "PUT":
                    return AllowMethodElement.Put;
            }
            throw new Exception("Cannot unmarshal type AllowMethodElement");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (AllowMethodElement)untypedValue;
            switch (value)
            {
                case AllowMethodElement.Delete:
                    serializer.Serialize(writer, "DELETE");
                    return;
                case AllowMethodElement.Get:
                    serializer.Serialize(writer, "GET");
                    return;
                case AllowMethodElement.Head:
                    serializer.Serialize(writer, "HEAD");
                    return;
                case AllowMethodElement.Options:
                    serializer.Serialize(writer, "OPTIONS");
                    return;
                case AllowMethodElement.Patch:
                    serializer.Serialize(writer, "PATCH");
                    return;
                case AllowMethodElement.Post:
                    serializer.Serialize(writer, "POST");
                    return;
                case AllowMethodElement.Put:
                    serializer.Serialize(writer, "PUT");
                    return;
            }
            throw new Exception("Cannot marshal type AllowMethodElement");
        }

        public static readonly AllowMethodElementConverter Singleton = new AllowMethodElementConverter();
    }

    internal class OutputEncodingConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(OutputEncoding) || t == typeof(OutputEncoding?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "fast-json":
                    return OutputEncoding.FastJson;
                case "json":
                    return OutputEncoding.Json;
                case "json-collection":
                    return OutputEncoding.JsonCollection;
                case "negotiate":
                    return OutputEncoding.Negotiate;
                case "no-op":
                    return OutputEncoding.NoOp;
                case "string":
                    return OutputEncoding.String;
                case "xml":
                    return OutputEncoding.Xml;
            }
            throw new Exception("Cannot unmarshal type OutputEncoding");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (OutputEncoding)untypedValue;
            switch (value)
            {
                case OutputEncoding.FastJson:
                    serializer.Serialize(writer, "fast-json");
                    return;
                case OutputEncoding.Json:
                    serializer.Serialize(writer, "json");
                    return;
                case OutputEncoding.JsonCollection:
                    serializer.Serialize(writer, "json-collection");
                    return;
                case OutputEncoding.Negotiate:
                    serializer.Serialize(writer, "negotiate");
                    return;
                case OutputEncoding.NoOp:
                    serializer.Serialize(writer, "no-op");
                    return;
                case OutputEncoding.String:
                    serializer.Serialize(writer, "string");
                    return;
                case OutputEncoding.Xml:
                    serializer.Serialize(writer, "xml");
                    return;
            }
            throw new Exception("Cannot marshal type OutputEncoding");
        }

        public static readonly OutputEncodingConverter Singleton = new OutputEncodingConverter();
    }

    internal class AuthApiKeysStrategyConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(AuthApiKeysStrategy) || t == typeof(AuthApiKeysStrategy?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "header":
                    return AuthApiKeysStrategy.Header;
                case "query_string":
                    return AuthApiKeysStrategy.QueryString;
            }
            throw new Exception("Cannot unmarshal type AuthApiKeysStrategy");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (AuthApiKeysStrategy)untypedValue;
            switch (value)
            {
                case AuthApiKeysStrategy.Header:
                    serializer.Serialize(writer, "header");
                    return;
                case AuthApiKeysStrategy.QueryString:
                    serializer.Serialize(writer, "query_string");
                    return;
            }
            throw new Exception("Cannot marshal type AuthApiKeysStrategy");
        }

        public static readonly AuthApiKeysStrategyConverter Singleton = new AuthApiKeysStrategyConverter();
    }

    internal class HashFunctionNameConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(HashFunctionName) || t == typeof(HashFunctionName?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "default":
                    return HashFunctionName.Default;
                case "optimal":
                    return HashFunctionName.Optimal;
            }
            throw new Exception("Cannot unmarshal type HashFunctionName");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (HashFunctionName)untypedValue;
            switch (value)
            {
                case HashFunctionName.Default:
                    serializer.Serialize(writer, "default");
                    return;
                case HashFunctionName.Optimal:
                    serializer.Serialize(writer, "optimal");
                    return;
            }
            throw new Exception("Cannot marshal type HashFunctionName");
        }

        public static readonly HashFunctionNameConverter Singleton = new HashFunctionNameConverter();
    }

    internal class TokenizerConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Tokenizer) || t == typeof(Tokenizer?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "cookie":
                    return Tokenizer.Cookie;
                case "header":
                    return Tokenizer.Header;
                case "ip":
                    return Tokenizer.Ip;
                case "jwt":
                    return Tokenizer.Jwt;
                case "param":
                    return Tokenizer.Param;
                case "path":
                    return Tokenizer.Path;
                case "url":
                    return Tokenizer.Url;
            }
            throw new Exception("Cannot unmarshal type Tokenizer");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (Tokenizer)untypedValue;
            switch (value)
            {
                case Tokenizer.Cookie:
                    serializer.Serialize(writer, "cookie");
                    return;
                case Tokenizer.Header:
                    serializer.Serialize(writer, "header");
                    return;
                case Tokenizer.Ip:
                    serializer.Serialize(writer, "ip");
                    return;
                case Tokenizer.Jwt:
                    serializer.Serialize(writer, "jwt");
                    return;
                case Tokenizer.Param:
                    serializer.Serialize(writer, "param");
                    return;
                case Tokenizer.Path:
                    serializer.Serialize(writer, "path");
                    return;
                case Tokenizer.Url:
                    serializer.Serialize(writer, "url");
                    return;
            }
            throw new Exception("Cannot marshal type Tokenizer");
        }

        public static readonly TokenizerConverter Singleton = new TokenizerConverter();
    }

    internal class LogLevelConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(LogLevel) || t == typeof(LogLevel?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "CRITICAL":
                    return LogLevel.Critical;
                case "DEBUG":
                    return LogLevel.Debug;
                case "ERROR":
                    return LogLevel.Error;
                case "INFO":
                    return LogLevel.Info;
                case "WARNING":
                    return LogLevel.Warning;
            }
            throw new Exception("Cannot unmarshal type LogLevel");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (LogLevel)untypedValue;
            switch (value)
            {
                case LogLevel.Critical:
                    serializer.Serialize(writer, "CRITICAL");
                    return;
                case LogLevel.Debug:
                    serializer.Serialize(writer, "DEBUG");
                    return;
                case LogLevel.Error:
                    serializer.Serialize(writer, "ERROR");
                    return;
                case LogLevel.Info:
                    serializer.Serialize(writer, "INFO");
                    return;
                case LogLevel.Warning:
                    serializer.Serialize(writer, "WARNING");
                    return;
            }
            throw new Exception("Cannot marshal type LogLevel");
        }

        public static readonly LogLevelConverter Singleton = new LogLevelConverter();
    }

    internal class SyslogFacilityConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(SyslogFacility) || t == typeof(SyslogFacility?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "local0":
                    return SyslogFacility.Local0;
                case "local1":
                    return SyslogFacility.Local1;
                case "local2":
                    return SyslogFacility.Local2;
                case "local3":
                    return SyslogFacility.Local3;
                case "local4":
                    return SyslogFacility.Local4;
                case "local5":
                    return SyslogFacility.Local5;
                case "local6":
                    return SyslogFacility.Local6;
                case "local7":
                    return SyslogFacility.Local7;
            }
            throw new Exception("Cannot unmarshal type SyslogFacility");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (SyslogFacility)untypedValue;
            switch (value)
            {
                case SyslogFacility.Local0:
                    serializer.Serialize(writer, "local0");
                    return;
                case SyslogFacility.Local1:
                    serializer.Serialize(writer, "local1");
                    return;
                case SyslogFacility.Local2:
                    serializer.Serialize(writer, "local2");
                    return;
                case SyslogFacility.Local3:
                    serializer.Serialize(writer, "local3");
                    return;
                case SyslogFacility.Local4:
                    serializer.Serialize(writer, "local4");
                    return;
                case SyslogFacility.Local5:
                    serializer.Serialize(writer, "local5");
                    return;
                case SyslogFacility.Local6:
                    serializer.Serialize(writer, "local6");
                    return;
                case SyslogFacility.Local7:
                    serializer.Serialize(writer, "local7");
                    return;
            }
            throw new Exception("Cannot marshal type SyslogFacility");
        }

        public static readonly SyslogFacilityConverter Singleton = new SyslogFacilityConverter();
    }

    internal class CurveIdentifierConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(CurveIdentifier) || t == typeof(CurveIdentifier?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.Integer:
                    var integerValue = serializer.Deserialize<long>(reader);
                    return new CurveIdentifier { Integer = integerValue };
                case JsonToken.Float:
                    var doubleValue = serializer.Deserialize<double>(reader);
                    return new CurveIdentifier { Double = doubleValue };
            }
            throw new Exception("Cannot unmarshal type CurveIdentifier");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (CurveIdentifier)untypedValue;
            if (value.Integer != null)
            {
                serializer.Serialize(writer, value.Integer.Value);
                return;
            }
            if (value.Double != null)
            {
                serializer.Serialize(writer, value.Double.Value);
                return;
            }
            throw new Exception("Cannot marshal type CurveIdentifier");
        }

        public static readonly CurveIdentifierConverter Singleton = new CurveIdentifierConverter();
    }

    internal class ImumTlsVersionConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(ImumTlsVersion) || t == typeof(ImumTlsVersion?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "SSL3.0":
                    return ImumTlsVersion.Ssl30;
                case "TLS10":
                    return ImumTlsVersion.Tls10;
                case "TLS11":
                    return ImumTlsVersion.Tls11;
                case "TLS12":
                    return ImumTlsVersion.Tls12;
                case "TLS13":
                    return ImumTlsVersion.Tls13;
            }
            throw new Exception("Cannot unmarshal type ImumTlsVersion");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (ImumTlsVersion)untypedValue;
            switch (value)
            {
                case ImumTlsVersion.Ssl30:
                    serializer.Serialize(writer, "SSL3.0");
                    return;
                case ImumTlsVersion.Tls10:
                    serializer.Serialize(writer, "TLS10");
                    return;
                case ImumTlsVersion.Tls11:
                    serializer.Serialize(writer, "TLS11");
                    return;
                case ImumTlsVersion.Tls12:
                    serializer.Serialize(writer, "TLS12");
                    return;
                case ImumTlsVersion.Tls13:
                    serializer.Serialize(writer, "TLS13");
                    return;
            }
            throw new Exception("Cannot marshal type ImumTlsVersion");
        }

        public static readonly ImumTlsVersionConverter Singleton = new ImumTlsVersionConverter();
    }
}
