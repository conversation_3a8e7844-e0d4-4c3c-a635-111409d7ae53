﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateConversationReadLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateConversationReadLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateConversationReadLogInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public ConversationReadLogData Data { get; set; }

        [JsonConstructor]
        public CreateConversationReadLogInput(
            string? sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            ConversationReadLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateConversationReadLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateConversationReadLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateConversationReadLogOutput> F(
        CreateConversationReadLogInput createConversationReadLogInput)
    {
        var dataStr = JsonConvert.SerializeObject(createConversationReadLogInput.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
            new UserProfileAuditLog(
                id,
                (distributedInvocationContext?.SleekflowCompanyId
                 ?? createConversationReadLogInput.SleekflowCompanyId)
                ?? throw new InvalidOperationException(),
                distributedInvocationContext?.SleekflowStaffId
                ?? createConversationReadLogInput.SleekflowStaffId,
                createConversationReadLogInput.SleekflowUserProfileId,
                UserProfileAuditLogTypes.ConversationRead,
                createConversationReadLogInput.AuditLogText,
                data,
                DateTimeOffset.UtcNow,
                null));

        return new CreateConversationReadLogOutput(id);
    }
}