﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.IntegrationObjects;
using Sleekflow.Integrator.GoogleSheets.Services;
using Sleekflow.Integrator.GoogleSheets.Subscriptions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitTypeSyncV2 : ITrigger
{
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsSubscriptionService _googleSheetsSubscriptionService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly IIntegrationObjectService _integrationObjectService;

    public InitTypeSyncV2(
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsSubscriptionService googleSheetsSubscriptionService,
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        IGoogleSheetsObjectService googleSheetsObjectService,
        IIntegrationObjectService integrationObjectService)
    {
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsSubscriptionService = googleSheetsSubscriptionService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _googleSheetsObjectService = googleSheetsObjectService;
        _integrationObjectService = integrationObjectService;
    }

    public class InitTypeSyncV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("typed_ids")]
        [Required]
        [ValidateArray]
        public List<TypedId> TypedIds { get; set; }

        [JsonProperty("sync_interval")]
        public int? SyncInterval { get; set; }

        [JsonProperty("is_flows_based")]
        public bool? IsFlowsBased { get; set; }

        [JsonConstructor]
        public InitTypeSyncV2Input(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<TypedId> typedIds,
            int? syncInterval,
            bool? isFlowsBased)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            TypedIds = typedIds;
            SyncInterval = syncInterval;
            IsFlowsBased = isFlowsBased;
        }
    }

    public class InitTypeSyncV2Output
    {
    }

    public async Task<InitTypeSyncV2Output> F(
        InitTypeSyncV2Input initTypeSyncV2Input)
    {
        var connection = await _googleSheetsConnectionService.GetByIdAsync(
            initTypeSyncV2Input.ConnectionId,
            initTypeSyncV2Input.SleekflowCompanyId);

        var authentication =
            await _googleSheetsAuthenticationService.GetAsync(
                connection.AuthenticationId,
                initTypeSyncV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var existingSubscription = await _googleSheetsSubscriptionService.GetAsync(
            initTypeSyncV2Input.ConnectionId,
            initTypeSyncV2Input.SleekflowCompanyId,
            initTypeSyncV2Input.TypedIds,
            initTypeSyncV2Input.EntityTypeName,
            initTypeSyncV2Input.IsFlowsBased ?? true);
        if (existingSubscription != null)
        {
            return new InitTypeSyncV2Output();
        }

        // initial sync and persistence: special handling for Google Sheets
        var latestObject = await _googleSheetsObjectService.GetObjectAsync(
            authentication,
            initTypeSyncV2Input.TypedIds,
            initTypeSyncV2Input.EntityTypeName);
        await _integrationObjectService.CreateAndGetAsync(
            initTypeSyncV2Input.SleekflowCompanyId,
            "google-sheets-integrator",
            initTypeSyncV2Input.EntityTypeName,
            latestObject,
            initTypeSyncV2Input.TypedIds,
            DateTimeOffset.UtcNow);

        await _googleSheetsSubscriptionService.UpsertAsync(
            initTypeSyncV2Input.SleekflowCompanyId,
            initTypeSyncV2Input.ConnectionId,
            initTypeSyncV2Input.TypedIds,
            initTypeSyncV2Input.EntityTypeName,
            initTypeSyncV2Input.SyncInterval ?? 60,
            initTypeSyncV2Input.IsFlowsBased ?? true,
            DateTimeOffset.UtcNow);

        return new InitTypeSyncV2Output();
    }
}