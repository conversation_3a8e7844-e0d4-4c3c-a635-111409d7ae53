﻿using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.ApexTriggerAuthentications;
using Sleekflow.Integrator.Salesforce.Configs;
using Sleekflow.Integrator.Salesforce.Templates;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Salesforce.Services;

public interface ISalesforceApexService
{
    Task InitWebhookApexClassAsync(
        SalesforceAuthentication authentication,
        bool isReplace);

    Task InitWebhookTriggerAsync(
        string sObjectTypeName,
        SalesforceAuthentication authentication,
        string sleekflowCompanyId,
        bool isReplace);

    Task InitWebhookTriggerTestAsync(
        SalesforceAuthentication authentication,
        string sObjectTypeName,
        bool isReplace);

    Task RemoveWebhookApexClassAsync(
        SalesforceAuthentication authentication);

    Task RemoveWebhookTriggerAsync(
        string sObjectTypeName,
        SalesforceAuthentication authentication);

    Task RemoveWebhookTriggerTestAsync(
        SalesforceAuthentication authentication,
        string sObjectTypeName);
}

public class SalesforceApexService : ISingletonService, ISalesforceApexService
{
    private readonly ILogger<SalesforceApexService> _logger;
    private readonly ISalesforceConfig _salesforceConfig;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly IApexTriggerAuthenticationService _apexTriggerAuthenticationService;

    public SalesforceApexService(
        ILogger<SalesforceApexService> logger,
        ISalesforceConfig salesforceConfig,
        ISalesforceObjectService salesforceObjectService,
        IApexTriggerAuthenticationService apexTriggerAuthenticationService)
    {
        _salesforceConfig = salesforceConfig;
        _salesforceObjectService = salesforceObjectService;
        _apexTriggerAuthenticationService = apexTriggerAuthenticationService;
        _logger = logger;
    }

    public async Task InitWebhookApexClassAsync(
        SalesforceAuthentication authentication,
        bool isReplace)
    {
        var sleekflowWebhookUtilityApexClassName = "SleekflowWebhookUtility";

        var (apexClasses, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            "ApexClass",
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(
                    new List<SyncConfigFilter>()
                    {
                        new SyncConfigFilter("Name", $"'{sleekflowWebhookUtilityApexClassName}'", null)
                    })
            });
        if (apexClasses.Count == 200)
        {
            _logger.LogWarning($"Only fewer than 200 is handled");
        }

        var sleekflowWebhookUtilityApexClass =
            apexClasses.Find(ac => (string) ac["Name"]! == sleekflowWebhookUtilityApexClassName);

        if (isReplace)
        {
            if (sleekflowWebhookUtilityApexClass != null)
            {
                await _salesforceObjectService.DeleteAsync(
                    authentication,
                    _salesforceObjectService.ResolveObjectId(sleekflowWebhookUtilityApexClass),
                    "ApexClass");
            }
        }
        else
        {
            if (sleekflowWebhookUtilityApexClass != null)
            {
                _logger.LogInformation(
                    "ApexClass {Name} was already initialized",
                    sleekflowWebhookUtilityApexClassName);

                return;
            }
        }

        await _salesforceObjectService.CreateAsync(
            authentication,
            new Dictionary<string, object?>
            {
                {
                    "Name", sleekflowWebhookUtilityApexClassName
                },
                {
                    "Status", "Active"
                },
                {
                    "Body", ApexTemplates.GetWebhookClassTemplate()
                },
                {
                    "IsValid", true
                }
            },
            "ApexClass");

        _logger.LogInformation($"ApexClass SleekflowWebhookUtility is initialized");
    }

    public async Task InitWebhookTriggerAsync(
        string sObjectTypeName,
        SalesforceAuthentication authentication,
        string sleekflowCompanyId,
        bool isReplace)
    {
        var apexWebhookTriggerName = $"Sleekflow_{sObjectTypeName}WebhookTrigger";

        var (apexTriggers, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            "ApexTrigger",
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(
                    new List<SyncConfigFilter>()
                    {
                        new SyncConfigFilter("Name", $"'{apexWebhookTriggerName}'", null)
                    })
            });
        var apexWebhookTrigger = apexTriggers.FirstOrDefault(ac => (string) ac["Name"]! == apexWebhookTriggerName);

        if (isReplace)
        {
            if (apexWebhookTrigger != null)
            {
                await _salesforceObjectService.DeleteAsync(
                    authentication,
                    _salesforceObjectService.ResolveObjectId(apexWebhookTrigger),
                    "ApexTrigger");
            }
        }
        else
        {
            if (apexWebhookTrigger != null)
            {
                _logger.LogInformation($"ApexTrigger {apexWebhookTriggerName} was already initialized");

                return;
            }
        }

        try
        {
            await _apexTriggerAuthenticationService.ClearAsync(sleekflowCompanyId, sObjectTypeName);
        }
        catch (Exception e)
        {
            _logger.LogWarning(
                e,
                "Unable to clear previous ApexTriggerAuthentications for SleekflowCompanyId {SleekflowCompanyId} SObjectTypeName {SObjectTypeName}",
                sleekflowCompanyId,
                sObjectTypeName);
        }

        var key = RandomStringUtils.Gen(12);

        await _apexTriggerAuthenticationService.UpsertAsync(
            new SalesforceApexTriggerAuthentication(
                authentication.SleekflowCompanyId + "_" + sObjectTypeName + "_" + key,
                authentication.SleekflowCompanyId,
                key,
                sObjectTypeName));

        await _salesforceObjectService.CreateAsync(
            authentication,
            new Dictionary<string, object?>
            {
                {
                    "name", apexWebhookTriggerName
                },
                {
                    "tableEnumOrId", sObjectTypeName
                },
                {
                    "usageBeforeInsert", false
                },
                {
                    "usageAfterInsert", true
                },
                {
                    "usageBeforeUpdate", false
                },
                {
                    "usageAfterUpdate", true
                },
                {
                    "usageBeforeDelete", false
                },
                {
                    "usageAfterDelete", true
                },
                {
                    "usageIsBulk", false
                },
                {
                    "usageAfterUndelete", true
                },
                {
                    "status", "Active"
                },
                {
                    "isValid", true
                },
                {
                    "body", ApexTemplates.GetTriggerTemplate(
                        apexWebhookTriggerName,
                        sObjectTypeName,
                        "after undelete,after delete,after update,after insert",
                        _salesforceConfig.SalesforceWebhookCallbackUrl,
                        sleekflowCompanyId,
                        key)
                }
            },
            "ApexTrigger");

        _logger.LogInformation("ApexTrigger {ApexWebhookTriggerName} is initialized", apexWebhookTriggerName);
    }

    public async Task InitWebhookTriggerTestAsync(
        SalesforceAuthentication authentication,
        string sObjectTypeName,
        bool isReplace)
    {
        var apexWebhookTriggerTestName = $"Sleekflow_{sObjectTypeName}WebhookTriggerTest";

        var (apexClasses, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            "ApexClass",
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(
                    new List<SyncConfigFilter>()
                    {
                        new SyncConfigFilter("Name", $"'{apexWebhookTriggerTestName}'", null)
                    })
            });
        var apexWebhookTriggerTest =
            apexClasses.FirstOrDefault(ac => (string) ac["Name"]! == apexWebhookTriggerTestName);

        if (isReplace)
        {
            if (apexWebhookTriggerTest != null)
            {
                await _salesforceObjectService.DeleteAsync(
                    authentication,
                    _salesforceObjectService.ResolveObjectId(apexWebhookTriggerTest),
                    "ApexClass");
            }
        }
        else
        {
            if (apexWebhookTriggerTest != null)
            {
                _logger.LogInformation(
                    "ApexClass {ApexWebhookTriggerTestName} was already initialized.",
                    apexWebhookTriggerTestName);

                return;
            }
        }

        await _salesforceObjectService.CreateAsync(
            authentication,
            new Dictionary<string, object?>
            {
                {
                    "Name", apexWebhookTriggerTestName
                },
                {
                    "Status", "Active"
                },
                {
                    "Body", ApexTemplates.GetTriggerTestTemplate(
                        apexWebhookTriggerTestName,
                        _salesforceConfig.SalesforceWebhookCallbackUrl)
                },
                {
                    "IsValid", true
                }
            },
            "ApexClass");

        _logger.LogInformation("ApexClass {ApexWebhookTriggerTestName} is initialized.", apexWebhookTriggerTestName);
    }

    public async Task RemoveWebhookApexClassAsync(
        SalesforceAuthentication authentication)
    {
        var apexClassName = "SleekflowWebhookUtility";

        var (apexClasses, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            "ApexClass",
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(
                    new List<SyncConfigFilter>()
                    {
                        new SyncConfigFilter("Name", $"'{apexClassName}'", null)
                    })
            });
        var webhookUtilityApexClass =
            apexClasses.FirstOrDefault(ac => (string) ac["Name"]! == apexClassName);

        if (webhookUtilityApexClass != null)
        {
            await _salesforceObjectService.DeleteAsync(
                authentication,
                _salesforceObjectService.ResolveObjectId(webhookUtilityApexClass),
                "ApexClass");
        }
    }

    public async Task RemoveWebhookTriggerAsync(
        string sObjectTypeName,
        SalesforceAuthentication authentication)
    {
        var apexTriggerName = $"Sleekflow_{sObjectTypeName}WebhookTrigger";

        var (apexTriggers, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            "ApexTrigger",
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(
                    new List<SyncConfigFilter>()
                    {
                        new SyncConfigFilter("Name", $"'{apexTriggerName}'", null)
                    })
            });
        var apexWebhookTrigger =
            apexTriggers.FirstOrDefault(ac => (string) ac["Name"]! == apexTriggerName);

        if (apexWebhookTrigger != null)
        {
            await _salesforceObjectService.DeleteAsync(
                authentication,
                _salesforceObjectService.ResolveObjectId(apexWebhookTrigger),
                "ApexTrigger");
        }
    }

    public async Task RemoveWebhookTriggerTestAsync(
        SalesforceAuthentication authentication,
        string sObjectTypeName)
    {
        var apexClassName = $"Sleekflow_{sObjectTypeName}WebhookTriggerTest";

        var (apexClasses, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            "ApexClass",
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(
                    new List<SyncConfigFilter>()
                    {
                        new SyncConfigFilter("Name", $"'{apexClassName}'", null)
                    })
            });
        var apexWebhookTriggerTest =
            apexClasses.FirstOrDefault(ac => (string) ac["Name"]! == apexClassName);

        if (apexWebhookTriggerTest != null)
        {
            await _salesforceObjectService.DeleteAsync(
                authentication,
                _salesforceObjectService.ResolveObjectId(apexWebhookTriggerTest),
                "ApexClass");
        }
    }
}