﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;
using Entity = Sleekflow.Persistence.Entity;

namespace Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.KnowledgeBaseEntry)]
public class KnowledgeBaseEntry : Entity, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt, IHasRecordStatuses
{
    public const string PropertyNameChunkId = "chunk_id";
    public const string PropertyNameContent = "content";
    public const string PropertyNameContentEn = "content_en";
    public const string PropertyNameCategories = "categories";
    public const string PropertyNameKnowledgeBaseEntrySource = "knowledge_base_entry_source";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameChunkId)]
    public string ChunkId { get; set; }

    [JsonProperty(PropertyNameContent)]
    public string Content { get; set; }

    [JsonProperty(PropertyNameContentEn)]
    public string ContentEn { get; set; }

    [JsonProperty(PropertyNameKnowledgeBaseEntrySource)]
    public KnowledgeBaseEntrySource KnowledgeBaseEntrySource { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(PropertyNameCategories)]
    public List<Category> Categories { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonConstructor]
    public KnowledgeBaseEntry(
        string id,
        string sleekflowCompanyId,
        string chunkId,
        string content,
        string contentEn,
        KnowledgeBaseEntrySource knowledgeBaseEntrySource,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<Category> categories,
        List<string> recordStatuses)
        : base(id, SysTypeNames.KnowledgeBaseEntry)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ChunkId = chunkId;
        Content = content;
        ContentEn = contentEn;
        KnowledgeBaseEntrySource = knowledgeBaseEntrySource;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        Categories = categories;
        RecordStatuses = recordStatuses;
    }
}