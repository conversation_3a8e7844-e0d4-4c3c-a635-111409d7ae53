﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class GetScheduledWorkflowEnrolmentCondition
    : ITrigger<GetScheduledWorkflowEnrolmentCondition.GetScheduledWorkflowEnrolmentConditionInput,
        GetScheduledWorkflowEnrolmentCondition.GetScheduledWorkflowEnrolmentConditionOutput>
{
    private readonly IWorkflowService _workflowService;

    public GetScheduledWorkflowEnrolmentCondition(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class GetScheduledWorkflowEnrolmentConditionInput
        : Sleekflow.FlowHub.Models.Internals.GetScheduledWorkflowEnrolmentConditionInput
    {
        [JsonConstructor]
        public GetScheduledWorkflowEnrolmentConditionInput(string sleekflowCompanyId, string workflowVersionedId)
            : base(sleekflowCompanyId, workflowVersionedId)
        {
        }
    }

    public class GetScheduledWorkflowEnrolmentConditionOutput
        : Sleekflow.FlowHub.Models.Internals.GetScheduledWorkflowEnrolmentConditionOutput
    {
        [JsonConstructor]
        public GetScheduledWorkflowEnrolmentConditionOutput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string condition,
            string workflowName)
            : base(sleekflowCompanyId, workflowVersionedId, condition, workflowName)
        {
        }
    }

    public async Task<GetScheduledWorkflowEnrolmentConditionOutput> F(
        GetScheduledWorkflowEnrolmentConditionInput input)
    {
        var condition = string.Empty;

        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId);

        var scheduledTriggerStep = proxyWorkflow.Steps.OfType<CallStep<ScheduledTriggerConditionsCheckStepArgs>>().FirstOrDefault();

        if (proxyWorkflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema == true)
        {
            condition = proxyWorkflow.Triggers.ScheduledWorkflowContactEnrolled?.Condition;
        }
        else if (scheduledTriggerStep != null)
        {
            condition = scheduledTriggerStep.Args.ConditionsExpr;
        }

        return new GetScheduledWorkflowEnrolmentConditionOutput(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            condition!,
            proxyWorkflow.Name);
    }
}