using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.States;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.States;

[TriggerGroup(ControllerNames.States)]
public class GetStates : ITrigger
{
    private readonly IStateService _stateService;

    public GetStates(
        IStateService stateService)
    {
        _stateService = stateService;
    }

    public class GetStatesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("filters")]
        public GetStatesInputFilters Filters { get; set; }

        [JsonConstructor]
        public GetStatesInput(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            GetStatesInputFilters filters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            Limit = limit;
            Filters = filters;
        }
    }

    public class GetStatesInputFilters : StateFilters
    {
        [JsonConstructor]
        public GetStatesInputFilters(
            string? workflowId,
            string? stateStatus,
            DateTimeOffset? fromDateTime,
            DateTimeOffset? toDateTime)
            : base(workflowId, stateStatus, fromDateTime, toDateTime)
        {
        }
    }

    public class GetStatesOutput
    {
        [JsonProperty("states")]
        public List<ProxyStateDto> States { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetStatesOutput(List<ProxyStateDto> states, string? nextContinuationToken)
        {
            States = states;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetStatesOutput> F(GetStatesInput getStatesInput)
    {
        var (proxyStates, nextContinuationToken) = await _stateService.GetProxyStatesAsync(
            getStatesInput.SleekflowCompanyId,
            getStatesInput.ContinuationToken,
            getStatesInput.Limit,
            getStatesInput.Filters);

        return new GetStatesOutput(
            proxyStates.Select(ps => new ProxyStateDto(ps)).ToList(),
            nextContinuationToken);
    }
}