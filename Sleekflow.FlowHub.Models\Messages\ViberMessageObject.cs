using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages;

/// <summary>
/// Viber text message object following Viber's API format.
/// Reference: https://developers.viber.com/docs/api/rest-bot-api/#text-message
/// </summary>
public class ViberMessageObject
{
    /// <summary>
    /// Message type. For text messages, this should be "text".
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// The text of the message. Max: 7000 characters.
    /// </summary>
    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonConstructor]
    public ViberMessageObject(string type, string text)
    {
        Type = type;
        Text = text;
    }
}