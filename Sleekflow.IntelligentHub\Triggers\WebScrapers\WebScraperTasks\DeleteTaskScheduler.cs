﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class DeleteTaskScheduler : ITrigger<DeleteTaskScheduler.DeleteTaskSchedulerInput, DeleteTaskScheduler.DeleteTaskSchedulerOutput>
{
    private readonly IWebScraperService _webScraperService;

    public DeleteTaskScheduler(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class DeleteTaskSchedulerInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [JsonConstructor]
        public DeleteTaskSchedulerInput(string sleekflowCompanyId, string apifyTaskId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyTaskId = apifyTaskId;
        }
    }

    public class DeleteTaskSchedulerOutput
    {
        [JsonConstructor]
        public DeleteTaskSchedulerOutput(WebScraperTask webScraperTask)
        {
            WebScraperTask = webScraperTask;
        }

        [JsonProperty(WebScraperTask.PropertyNameWebScraperTask)]
        public WebScraperTask WebScraperTask { get; set; }
    }

    public async Task<DeleteTaskSchedulerOutput> F(DeleteTaskSchedulerInput deleteTaskSchedulerInput)
    {
        var webScraperTask = await _webScraperService.DeleteScheduleForWebScraperTaskAsync(
            deleteTaskSchedulerInput.SleekflowCompanyId,
            deleteTaskSchedulerInput.ApifyTaskId);
        return new DeleteTaskSchedulerOutput(webScraperTask);
    }
}