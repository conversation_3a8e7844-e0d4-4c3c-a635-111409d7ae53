using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowLogger
{
    Task LogAsync(ProxyWorkflow workflow, ProxyState proxyState, string logLevel, string logMessage);
}

public class WorkflowLogger : IWorkflowLogger, IScopedService
{
    private readonly ILogger<WorkflowLogger> _logger;

    public WorkflowLogger(
        ILogger<WorkflowLogger> logger)
    {
        _logger = logger;
    }

    public Task LogAsync(ProxyWorkflow workflow, ProxyState proxyState, string logLevel, string logMessage)
    {
        if (logLevel == "Information")
        {
            _logger.LogInformation(logMessage);
        }
        else if (logLevel == "Warning")
        {
            _logger.LogWarning(logMessage);
        }
        else if (logLevel == "Error")
        {
            _logger.LogError(logMessage);
        }
        else if (logLevel == "Critical")
        {
            _logger.LogCritical(logMessage);
        }
        else if (logLevel == "Debug")
        {
            _logger.LogDebug(logMessage);
        }
        else if (logLevel == "Trace")
        {
            _logger.LogTrace(logMessage);
        }
        else
        {
            _logger.LogInformation(logMessage);
        }

        return Task.CompletedTask;
    }
}