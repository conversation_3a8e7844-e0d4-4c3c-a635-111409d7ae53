using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps;

public class SwitchStep : Step
{
    [Required]
    [JsonProperty("switch")]
    public List<SwitchStepCase> SwitchCases { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public SwitchStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        List<SwitchStepCase> switchCases)
        : base(id, name, assign, nextStepId)
    {
        SwitchCases = switchCases;
    }
}

public class SwitchStepCase
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [Required]
    [JsonProperty("condition")]
    public string Condition { get; set; }

    [JsonProperty("condition_criteria")]
    public List<ConditionCriterion>? ConditionCriteria { get; set; }

    [Required]
    [JsonProperty("next_step_id")]
    public string NextStepId { get; set; }

    [JsonConstructor]
    public SwitchStepCase(
        string? id,
        string? name,
        string condition,
        List<ConditionCriterion>? conditionCriteria,
        string nextStepId)
    {
        Id = id;
        Name = name;
        Condition = condition;
        ConditionCriteria = conditionCriteria;
        NextStepId = nextStepId;
    }
}