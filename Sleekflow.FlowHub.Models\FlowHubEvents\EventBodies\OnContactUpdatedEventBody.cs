using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnContactUpdatedEventBody : EventBody, IHasSleekflowStaff
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactUpdated; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [Required]
    [JsonProperty("post_updated_contact")]
    public Dictionary<string, object?> PostUpdatedContact { get; set; }

    [Required]
    [JsonProperty("pre_updated_contact")]
    public Dictionary<string, object?> PreUpdatedContact { get; set; }

    [Required]
    [JsonProperty("change_entries")]
    public List<OnContactUpdatedEventBodyChangeEntry> ChangeEntries { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [JsonConstructor]
    public OnContactUpdatedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        Dictionary<string, object?> postUpdatedContact,
        Dictionary<string, object?> preUpdatedContact,
        List<OnContactUpdatedEventBodyChangeEntry> changeEntries,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        PostUpdatedContact = postUpdatedContact;
        PreUpdatedContact = preUpdatedContact;
        ChangeEntries = changeEntries;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
    }
}

public class OnContactUpdatedEventBodyChangeEntry
{
    [JsonProperty("property_name")]
    public string PropertyName { get; set; }

    [JsonProperty("property_id")]
    public string PropertyId { get; set; }

    [JsonProperty("from_value")]
    public object? FromValue { get; set; }

    [JsonProperty("to_value")]
    public object? ToValue { get; set; }

    [JsonConstructor]
    public OnContactUpdatedEventBodyChangeEntry(
        string propertyName,
        string propertyId,
        object? fromValue,
        object? toValue)
    {
        PropertyName = propertyName;
        PropertyId = propertyId;
        FromValue = fromValue;
        ToValue = toValue;
    }
}