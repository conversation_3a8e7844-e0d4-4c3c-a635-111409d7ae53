﻿using Azure.Messaging.EventHubs;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json;
using Sleekflow.JsonConfigs;

namespace Sleekflow.Utils;

public static class EventDataUtils
{
    public static EventData GetEventData<T>(T @event)
    {
        var eventBody = new BinaryData(
            JsonConvert.SerializeObject(
                @event,
                JsonConfig.DefaultJsonSerializerSettings));
        var eventData = new EventData(eventBody);

        return eventData;
    }

    public static ServiceBusMessage GetServiceBusMessage<T>(
        T @event,
        string partitionId,
        string envId)
    {
        var eventBody = new BinaryData(
            JsonConvert.SerializeObject(
                @event,
                JsonConfig.DefaultJsonSerializerSettings));
        var serviceBusMessage = new ServiceBusMessage(eventBody)
        {
            ApplicationProperties =
            {
                ["EnvId"] = envId
            },
            SessionId = partitionId,
        };

        return serviceBusMessage;
    }
}