using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.TemplateRenderers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class PreviewTemplate
    : ITrigger<
        PreviewTemplate.PreviewTemplateInput,
        PreviewTemplate.PreviewTemplateOutput>
{
    private readonly IStoreService _storeService;
    private readonly ITemplateRenderer _templateRenderer;

    public PreviewTemplate(
        IStoreService storeService,
        ITemplateRenderer templateRenderer)
    {
        _storeService = storeService;
        _templateRenderer = templateRenderer;
    }

    public class PreviewTemplateInput
    {
        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("template_name")]
        public string TemplateName { get; set; }

        [JsonProperty("templates")]
        [Required]
        public List<Multilingual> Templates { get; set; }

        [JsonConstructor]
        public PreviewTemplateInput(
            string storeId,
            string sleekflowCompanyId,
            string templateName,
            List<Multilingual> templates)
        {
            StoreId = storeId;
            SleekflowCompanyId = sleekflowCompanyId;
            TemplateName = templateName;
            Templates = templates;
        }
    }

    public class PreviewTemplateOutput
    {
        [JsonProperty("rendered_template_dict")]
        public Dictionary<string, RenderedTemplate> RenderedTemplateDict { get; set; }

        [JsonConstructor]
        public PreviewTemplateOutput(Dictionary<string, RenderedTemplate> renderedTemplateDict)
        {
            RenderedTemplateDict = renderedTemplateDict;
        }
    }

    public async Task<PreviewTemplateOutput> F(PreviewTemplateInput previewTemplateInput)
    {
        var store = await _storeService.GetStoreAsync(
            previewTemplateInput.StoreId,
            previewTemplateInput.SleekflowCompanyId);

        if (previewTemplateInput.TemplateName != "MessagePreview")
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult(
                        "Unrecognized template name",
                        new List<string>
                        {
                            "TemplateName"
                        })
                });
        }

        foreach (var languageIsoCode in previewTemplateInput.Templates.Select(ts => ts.LanguageIsoCode))
        {
            if (store.Languages.All(l => l.LanguageIsoCode != languageIsoCode))
            {
                throw new SfValidationException(
                    new List<ValidationResult>()
                    {
                        new ValidationResult(
                            "Unrecognized language iso code",
                            new List<string>
                            {
                                "TemplateStrings", "LanguageIsoCode"
                            })
                    });
            }
        }

        var renderedTemplateDict = new Dictionary<string, RenderedTemplate>();

        foreach (var templateString in previewTemplateInput.Templates)
        {
            var renderedTemplate = await _templateRenderer.PreviewProductVariantTemplateAsync(
                templateString.Value,
                templateString.LanguageIsoCode);

            renderedTemplateDict.Add(
                $"MessagePreview_{templateString.LanguageIsoCode}",
                renderedTemplate);
        }

        return new PreviewTemplateOutput(renderedTemplateDict);
    }
}