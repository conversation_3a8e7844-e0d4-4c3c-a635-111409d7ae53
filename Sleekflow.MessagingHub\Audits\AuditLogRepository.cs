using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.MessagingHub.Audits;

public interface IAuditLogRepository : IRepository<AuditLog>
{
    Task<bool> PatchCloudApiAuditAsync(
        AuditLog auditLog,
        Dictionary<string, object?>? request = default,
        Dictionary<string, object?>? response = default,
        Dictionary<string, object?>? error = default);
}

public class AuditLogRepository : BaseRepository<AuditLog>, IAuditLogRepository, ISingletonService
{
    public AuditLogRepository(
        ILogger<AuditLogRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<bool> PatchCloudApiAuditAsync(
        AuditLog auditLog,
        Dictionary<string, object?>? request = default,
        Dictionary<string, object?>? response = default,
        Dictionary<string, object?>? error = default)
    {
        return await PatchAsync(
            auditLog.Id,
            auditLog.AuditingPartitionId,
            error == default
                ? new List<PatchOperation>
                {
                    Replace(AuditLog.CloudApiRequestAudit.RequestPath, request),
                    Replace(AuditLog.CloudApiRequestAudit.ResponsePath, response),
                    Replace(AuditLog.CloudApiRequestAudit.ResponseAtPath, DateTimeOffset.UtcNow)
                }
                : new List<PatchOperation>
                {
                    Replace(AuditLog.CloudApiRequestAudit.RequestPath, request),
                    Replace(AuditLog.CloudApiRequestAudit.ExceptionPath, error),
                    Replace(AuditLog.CloudApiRequestAudit.ResponseAtPath, DateTimeOffset.UtcNow)
                }) == 1;
    }
}