﻿using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Orders;

[ContainerId(ContainerNames.Order)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class Order : AuditEntity, IHasSleekflowUserProfileId, IHasRecordStatuses, IHasMetadata
{
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty("user_profile")]
    public UserProfile UserProfile { get; set; }

    [JsonProperty("shipping")]
    public Shipping? Shipping { get; set; }

    [JsonProperty("line_items")]
    public List<OrderLineItem> LineItems { get; set; }

    [JsonProperty("currency_iso_code")]
    public string CurrencyIsoCode { get; set; }

    [JsonProperty("language_iso_code")]
    public string LanguageIsoCode { get; set; }

    /// <summary>
    /// The sum of all the prices of all the items in the order, including taxes and discounts.
    /// </summary>
    [JsonProperty("total_price")]
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// The price of the order before shipping and taxes.
    /// </summary>
    [JsonProperty("subtotal_price")]
    public decimal SubtotalPrice { get; set; }

    [JsonProperty("order_discount")]
    public Discount? OrderDiscount { get; set; }

    [JsonProperty("country_iso_code")]
    public string? CountryIsoCode { get; set; }

    [JsonProperty("order_status")]
    public string OrderStatus { get; set; }

    [JsonProperty("payment_status")]
    public string PaymentStatus { get; set; }

    [JsonProperty("payment_link_expired_at")]
    public DateTimeOffset? PaymentLinkExpiredAt { get; set; }

    [JsonProperty("payment_link_sent_at")]
    public DateTimeOffset? PaymentLinkSentAt { get; set; }

    [JsonProperty("completed_at")]
    public DateTimeOffset? CompletedAt { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonProperty("order_external_integration_info", TypeNameHandling = TypeNameHandling.Objects)]
    public OrderExternalIntegrationInfo? OrderExternalIntegrationInfo { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonConstructor]
    public Order(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        UserProfile userProfile,
        Shipping? shipping,
        List<OrderLineItem> lineItems,
        string currencyIsoCode,
        string languageIsoCode,
        decimal totalPrice,
        decimal subtotalPrice,
        Discount? orderDiscount,
        string? countryIsoCode,
        string orderStatus,
        string paymentStatus,
        DateTimeOffset? paymentLinkSentAt,
        DateTimeOffset? completedAt,
        Dictionary<string, object?> metadata,
        OrderExternalIntegrationInfo? orderExternalIntegrationInfo,
        List<string> recordStatuses,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(id, SysTypeNames.Order, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        StoreId = storeId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        UserProfile = userProfile;
        Shipping = shipping;
        LineItems = lineItems;
        CurrencyIsoCode = currencyIsoCode;
        LanguageIsoCode = languageIsoCode;
        TotalPrice = totalPrice;
        SubtotalPrice = subtotalPrice;
        OrderDiscount = orderDiscount;
        CountryIsoCode = countryIsoCode;
        OrderStatus = orderStatus;
        PaymentStatus = paymentStatus;
        PaymentLinkSentAt = paymentLinkSentAt;
        CompletedAt = completedAt;
        Metadata = metadata;
        OrderExternalIntegrationInfo = orderExternalIntegrationInfo;
        RecordStatuses = recordStatuses;
    }
}

public class Shipping
{
    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("phone")]
    public string Phone { get; set; }

    [JsonProperty("address")]
    public Address? Address { get; set; }

    [JsonConstructor]
    public Shipping(
        string firstName,
        string lastName,
        string phone,
        Address? address)
    {
        FirstName = firstName;
        LastName = lastName;
        Phone = phone;
        Address = address;
    }
}

public class Address
{
    [JsonProperty("line1")]
    public string Line1 { get; set; }

    [JsonProperty("line2")]
    public string? Line2 { get; set; }

    [JsonProperty("city")]
    public string? City { get; set; }

    [JsonProperty("province")]
    public string? Province { get; set; }

    [JsonProperty("postal_code")]
    public string? PostalCode { get; set; }

    [JsonProperty("country_iso_code")]
    public string? CountryIsoCode { get; set; }

    [JsonConstructor]
    public Address(
        string line1,
        string? line2,
        string? city,
        string? province,
        string? postalCode,
        string? countryIsoCode)
    {
        Line1 = line1;
        Line2 = line2;
        City = city;
        Province = province;
        PostalCode = postalCode;
        CountryIsoCode = countryIsoCode;
    }
}

public class UserProfile
{
    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("phone")]
    public string? Phone { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonConstructor]
    public UserProfile(
        string firstName,
        string lastName,
        string? phone,
        string? email)
    {
        FirstName = firstName;
        LastName = lastName;
        Phone = phone;
        Email = email;
    }
}