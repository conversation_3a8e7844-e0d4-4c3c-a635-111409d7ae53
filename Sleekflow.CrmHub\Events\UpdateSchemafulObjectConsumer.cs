﻿using MassTransit;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.Models.ActionEvents.CrmHub;
using Sleekflow.Persistence;

namespace Sleekflow.CrmHub.Events;

public class UpdateSchemafulObjectConsumerDefinition
    : ConsumerDefinition<UpdateSchemafulObjectConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<UpdateSchemafulObjectConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class UpdateSchemafulObjectConsumer : IConsumer<UpdateSchemafulObjectRequest>
{
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public UpdateSchemafulObjectConsumer(
        ISchemaService schemaService,
        ISchemafulObjectService schemafulObjectService)
    {
        _schemaService = schemaService;
        _schemafulObjectService = schemafulObjectService;
    }

    public async Task Consume(ConsumeContext<UpdateSchemafulObjectRequest> context)
    {
        var updateSchemafulObjectRequest = context.Message;

        var schema = await _schemaService.GetAsync(
            updateSchemafulObjectRequest.SchemaId,
            updateSchemafulObjectRequest.SleekflowCompanyId);

        var schemafulObject = string.IsNullOrEmpty(updateSchemafulObjectRequest.SleekflowUserProfileIdForRecordLocator)
            ? await _schemafulObjectService.GetSchemafulObjectByPrimaryPropertyValueAsync(
                updateSchemafulObjectRequest.SchemaId,
                updateSchemafulObjectRequest.SleekflowCompanyId,
                updateSchemafulObjectRequest.PrimaryPropertyValue)
            : await _schemafulObjectService.GetLatestSchemafulObjectByUserProfileIdAsync(
                updateSchemafulObjectRequest.SchemaId,
                updateSchemafulObjectRequest.SleekflowCompanyId,
                updateSchemafulObjectRequest.SleekflowUserProfileIdForRecordLocator);

        if (!string.IsNullOrWhiteSpace(updateSchemafulObjectRequest.SleekflowUserProfileId))
        {
            Guid.Parse(updateSchemafulObjectRequest.SleekflowUserProfileId);
            schemafulObject.SleekflowUserProfileId = updateSchemafulObjectRequest.SleekflowUserProfileId;
        }

        foreach (var propertyValue in updateSchemafulObjectRequest.PropertyValues)
        {
            schemafulObject.PropertyValues[propertyValue.Key] = propertyValue.Value;
        }

        await _schemafulObjectService.PatchAndGetSchemafulObjectAsync(
            schemafulObject.Id,
            schema,
            updateSchemafulObjectRequest.SleekflowCompanyId,
            schemafulObject.PropertyValues,
            schemafulObject.SleekflowUserProfileId,
            updateSchemafulObjectRequest.UpdatedVia,
            new AuditEntity.SleekflowStaff(
                string.Empty,
                null));

        await context.RespondAsync(new UpdateSchemafulObjectReply());
    }
}