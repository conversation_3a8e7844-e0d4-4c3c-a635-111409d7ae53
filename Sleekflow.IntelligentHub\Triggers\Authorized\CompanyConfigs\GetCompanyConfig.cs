using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyConfigs;

[TriggerGroup(
    ControllerNames.CompanyConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetCompanyConfig
    : ITrigger<GetCompanyConfig.GetCompanyConfigInput, GetCompanyConfig.GetCompanyConfigOutput>
{
    private readonly ICompanyConfigService _companyConfigService;
    private readonly ISleekflowAuthorizationContext _sleekflowAuthorizationContext;

    public GetCompanyConfig(
        ICompanyConfigService companyConfigService,
        ISleekflowAuthorizationContext sleekflowAuthorizationContext)
    {
        _companyConfigService = companyConfigService;
        _sleekflowAuthorizationContext = sleekflowAuthorizationContext;
    }

    public class GetCompanyConfigInput
    {
    }

    public class GetCompanyConfigOutput
    {
        [JsonProperty("company_config")]
        public CompanyConfigDto CompanyConfig { get; set; }

        [JsonConstructor]
        public GetCompanyConfigOutput(CompanyConfigDto companyConfig)
        {
            CompanyConfig = companyConfig;
        }
    }

    public async Task<GetCompanyConfigOutput> F(GetCompanyConfigInput input)
    {
        var companyConfig =
            await _companyConfigService.GetConfigAsync(_sleekflowAuthorizationContext.SleekflowCompanyId!);

        return new GetCompanyConfigOutput(new CompanyConfigDto(companyConfig));
    }
}