﻿using Sleekflow.CrmHub.Models.IntegrationObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.IntegrationObjects;

public interface IIntegrationObjectRepository : IDynamicFiltersRepository<IntegrationObject>
{
}

public class IntegrationObjectRepository
    : DynamicFiltersBaseRepository<IntegrationObject>, IIntegrationObjectRepository, IScopedService
{
    public IntegrationObjectRepository(
        ILogger<DynamicFiltersBaseRepository<IntegrationObject>> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}