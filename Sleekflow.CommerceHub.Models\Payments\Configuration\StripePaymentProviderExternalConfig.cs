using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Stripe;

namespace Sleekflow.CommerceHub.Models.Payments.Configuration;

public class StripePaymentProviderExternalConfig : PaymentProviderExternalConfig
{
    [Required]
    [JsonProperty("stripe_api_key")]
    public string StripeApiKey { get; set; }

    [Required]
    [JsonProperty("stripe_connect_webhook_secret")]
    public string StripeConnectWebhookSecret { get; set; }

    [Required]
    [JsonProperty("stripe_payment_webhook_secret")]
    public string StripePaymentWebhookSecret { get; set; }

    [Required]
    [JsonProperty("stripe_account")]
    public Account StripeAccount { get; set; }

    [Required]
    [JsonProperty("is_connected_account")]
    public bool IsConnectedAccount { get; set; }

    [Required]
    [JsonProperty("application_fee_rate")]
    public decimal ApplicationFeeRate { get; set; }

    [Required]
    [JsonProperty("is_shipping_enabled")]
    public bool IsShippingEnabled { get; set; }

    [JsonProperty("shipping_allowed_country_iso_codes")]
    public List<string>? ShippingAllowedCountryIsoCodes { get; set; }

    [Required]
    [JsonProperty("is_inventory_enabled")]
    public bool IsInventoryEnabled { get; set; }

    [JsonConstructor]
    public StripePaymentProviderExternalConfig(
        string providerName,
        string providerId,
        string stripeApiKey,
        string stripeConnectWebhookSecret,
        string stripePaymentWebhookSecret,
        Account stripeAccount,
        bool isConnectedAccount,
        decimal applicationFeeRate,
        bool isShippingEnabled,
        List<string>? shippingAllowedCountryIsoCodes,
        bool isInventoryEnabled)
        : base(providerName, providerId)
    {
        StripeApiKey = stripeApiKey;
        StripeConnectWebhookSecret = stripeConnectWebhookSecret;
        StripePaymentWebhookSecret = stripePaymentWebhookSecret;
        StripeAccount = stripeAccount;
        IsConnectedAccount = isConnectedAccount;
        ApplicationFeeRate = applicationFeeRate;
        IsShippingEnabled = isShippingEnabled;
        ShippingAllowedCountryIsoCodes = shippingAllowedCountryIsoCodes;
        IsInventoryEnabled = isInventoryEnabled;
    }
}