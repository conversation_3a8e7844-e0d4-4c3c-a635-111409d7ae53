﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class SyncObjectsOrchestrator : ITrigger
{
    public class SyncObjectsOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public SyncObjectsOrchestratorCustomStatusOutput(long count, DateTime lastUpdateTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class SyncObjectsOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SyncObjectsOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Salesforce_SyncObjects_Orchestrator")]
    public async Task<SyncObjectsOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var syncObjectsInput = context.GetInput<SyncObjects.SyncObjectsInput>();

        context.SetCustomStatus(new SyncObjectsOrchestratorCustomStatusOutput(0, context.CurrentUtcDateTime));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;
        var nextRecordsUrl = null as string;
        while (true)
        {
            var syncObjectsBatchOutput = await context
                .CallActivityAsync<SyncObjectsBatch.SyncObjectsBatchOutput>(
                    "Salesforce_SyncObjects_Batch",
                    new SyncObjectsBatch.SyncObjectsBatchInput(
                        syncObjectsInput.SleekflowCompanyId,
                        syncObjectsInput.EntityTypeName,
                        syncObjectsInput.FilterGroups,
                        syncObjectsInput.FieldFilters,
                        nextRecordsUrl),
                    taskOptions);

            totalCount += syncObjectsBatchOutput.Count;
            nextRecordsUrl = syncObjectsBatchOutput.NextRecordsUrl;

            context.SetCustomStatus(
                new SyncObjectsOrchestratorCustomStatusOutput(totalCount, context.CurrentUtcDateTime));

            if (nextRecordsUrl == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        return new SyncObjectsOrchestratorOutput(totalCount);
    }
}