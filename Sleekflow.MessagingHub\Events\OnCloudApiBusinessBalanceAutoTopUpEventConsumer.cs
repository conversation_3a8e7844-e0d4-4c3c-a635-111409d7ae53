using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceAutoTopUpCharge;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUp;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiBusinessBalanceAutoTopUpEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiBusinessBalanceAutoTopUpEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiBusinessBalanceAutoTopUpEventConsumer> configurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiBusinessBalanceAutoTopUpEventConsumer
    : IConsumer<
        OnCloudApiBusinessBalanceAutoTopUpEvent>
{
    private readonly IBusinessBalanceAutoTopUpService _businessBalanceAutoTopUpService;
    private readonly IBusinessBalanceAutoTopUpProfileService _businessBalanceAutoTopUpProfileService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiBusinessBalanceAutoTopUpEventConsumer> _logger;
    private readonly ILockService _lockService;
    private readonly IBalanceAutoTopUpChargeService _balanceAutoTopUpChargeService;

    public OnCloudApiBusinessBalanceAutoTopUpEventConsumer(
        IBusinessBalanceAutoTopUpService businessBalanceAutoTopUpService,
        IBusinessBalanceAutoTopUpProfileService businessBalanceAutoTopUpProfileService,
        IBusinessBalanceService businessBalanceService,
        ILockService lockService,
        ILogger<OnCloudApiBusinessBalanceAutoTopUpEventConsumer> logger,
        IBalanceAutoTopUpChargeService balanceAutoTopUpChargeService)
    {
        _businessBalanceAutoTopUpService = businessBalanceAutoTopUpService;
        _businessBalanceAutoTopUpProfileService = businessBalanceAutoTopUpProfileService;
        _businessBalanceService = businessBalanceService;
        _lockService = lockService;
        _logger = logger;
        _balanceAutoTopUpChargeService = balanceAutoTopUpChargeService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiBusinessBalanceAutoTopUpEvent> context)
    {
        var onCloudApiBusinessBalanceAutoTopUpEvent = context.Message;
        var retryCount = context.GetRedeliveryCount();

        if (retryCount > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiBusinessBalanceAutoTopUpEvent}",
                JsonConvert.SerializeObject(onCloudApiBusinessBalanceAutoTopUpEvent));

            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var cancellationToken = context.CancellationToken;

        var facebookBusinessId = onCloudApiBusinessBalanceAutoTopUpEvent.FacebookBusinessId;

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId,
                "BusinessBalanceAutoTopUpLock"
            },
            TimeSpan.FromSeconds(
                10),
            cancellationToken);

        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));

            return;
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            _logger.LogWarning(
                "Unable to locate business account balance object with {FacebookBusinessId}",
                facebookBusinessId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);

            return;
        }

        var businessBalanceAutoTopProfile =
            await _businessBalanceAutoTopUpProfileService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalanceAutoTopProfile is null)
        {
            _logger.LogWarning(
                "Unable to locate business account balance object with {FacebookBusinessId}",
                facebookBusinessId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);

            return;
        }

        var performAutoTopUp = _businessBalanceAutoTopUpService.ShouldPerformAutoTopUp(
            facebookBusinessId,
            businessBalance,
            businessBalanceAutoTopProfile);

        if (performAutoTopUp)
        {
            await _balanceAutoTopUpChargeService.ChargeAutoTopUpFee(onCloudApiBusinessBalanceAutoTopUpEvent);
        }

        await _lockService.ReleaseAsync(@lock, cancellationToken);
    }
}