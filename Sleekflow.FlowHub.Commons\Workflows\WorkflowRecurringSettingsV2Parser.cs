using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows.Settings;

namespace Sleekflow.FlowHub.Commons.Workflows;

public class WorkflowRecurringSettingsV2Parser
{
    private readonly ILogger<WorkflowRecurringSettingsV2Parser> _logger;

    public WorkflowRecurringSettingsV2Parser(ILogger<WorkflowRecurringSettingsV2Parser> logger)
    {
        _logger = logger;
    }

    public DateTimeOffset GetNextOccurrence(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings recurringSettings,
        int offset = 0,
        bool futureOccurrenceOnly = true)
    {
        throw new NotImplementedException();
    }

    public DateTimeOffset GetNextOccurrenceAfter(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings recurringSettings,
        DateTimeOffset currentTimeUtc)
    {
        if (recurringSettings == null)
        {
            if (firstOccurrence < currentTimeUtc)
            {
                return DateTimeOffset.MaxValue;
            }

            return firstOccurrence;
        }

        const int maxAttempts = 1000; // Only used for monthly/yearly rules
        return recurringSettings.RecurringType switch
        {
            WorkflowRecurringTypes.Hourly => CalculateHourly(firstOccurrence, recurringSettings, currentTimeUtc),
            WorkflowRecurringTypes.Minutely => CalculateMinutely(firstOccurrence, recurringSettings, currentTimeUtc),
            WorkflowRecurringTypes.Daily => CalculateDaily(firstOccurrence, recurringSettings, currentTimeUtc),
            WorkflowRecurringTypes.Weekly => CalculateWeekly(firstOccurrence, recurringSettings, currentTimeUtc),
            WorkflowRecurringTypes.Monthly => CalculateMonthlySafe(
                firstOccurrence,
                recurringSettings,
                currentTimeUtc,
                maxAttempts),
            WorkflowRecurringTypes.Yearly => CalculateYearlySafe(
                firstOccurrence,
                recurringSettings,
                currentTimeUtc,
                maxAttempts),
            _ => throw new ArgumentException($"Unsupported recurring type: {recurringSettings.RecurringType}")
        };
    }

    private DateTimeOffset CalculateDaily(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings settings,
        DateTimeOffset current)
    {
        var interval = settings.Day ?? 1;
        var elapsedDays = (current - firstOccurrence).TotalDays;
        var intervals = (int) Math.Ceiling(elapsedDays / interval);
        return firstOccurrence.AddDays(interval * intervals);
    }

    private DateTimeOffset CalculateWeekly(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings settings,
        DateTimeOffset current)
    {
        var intervalWeeks = settings.Week ?? 1;
        var daysInInterval = intervalWeeks * 7;

        if (settings.RepeatOnWeekly == "default")
        {
            return firstOccurrence.DayOfWeek == current.DayOfWeek
                ? firstOccurrence.AddDays(
                    daysInInterval * (int) Math.Ceiling((current - firstOccurrence).TotalDays / daysInInterval))
                : FindNextDay(
                    firstOccurrence,
                    current,
                    new[]
                    {
                        firstOccurrence.DayOfWeek
                    },
                    intervalWeeks);
        }

        if (settings.RepeatOnWeekly == "specific" && settings.RepeatOnWeeklySpecificDay?.Any() == true)
        {
            var targetDays = settings.RepeatOnWeeklySpecificDay
                .Select(d => Enum.Parse<DayOfWeek>(d, true))
                .OrderBy(d => d)
                .ToArray();

            return FindNextDay(firstOccurrence, current, targetDays, intervalWeeks);
        }

        throw new ArgumentException("Invalid weekly configuration");
    }

    private DateTimeOffset CalculateMonthlySafe(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings settings,
        DateTimeOffset current,
        int maxAttempts)
    {
        var intervalMonths = settings.Month ?? 1;
        var targetDay = settings.RepeatOnMonthly ?? firstOccurrence.Day;
        int attempts = 0;

        // Calculate complete months from first occurrence to current time
        var totalMonths = ((current.Year - firstOccurrence.Year) * 12) + current.Month - firstOccurrence.Month;
        var fullIntervals = (int) Math.Ceiling(totalMonths / (double) intervalMonths);
        var candidate = firstOccurrence.AddMonths(fullIntervals * intervalMonths);

        while (attempts++ < maxAttempts)
        {
            try
            {
                // Try to create target date (forcing target day)
                var nextCandidate = new DateTimeOffset(
                    candidate.Year,
                    candidate.Month,
                    targetDay,
                    candidate.Hour,
                    candidate.Minute,
                    candidate.Second,
                    candidate.Offset
                );

                // Check if conditions are met
                if (nextCandidate > current)
                {
                    return nextCandidate;
                }

                // Move to next interval
                candidate = candidate.AddMonths(intervalMonths);
            }
            catch (ArgumentOutOfRangeException)
            {
                // Skip invalid date (e.g. Feb 30th)
                candidate = candidate.AddMonths(intervalMonths);
            }
        }

        return DateTimeOffset.MaxValue;
    }

    private DateTimeOffset CalculateYearlySafe(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings settings,
        DateTimeOffset current,
        int maxAttempts)
    {
        var intervalYears = settings.Year ?? 1;
        int attempts = 0;

        // Calculate complete years from first occurrence to current time
        var totalYears = current.Year - firstOccurrence.Year;
        var fullIntervals = (int) Math.Ceiling(totalYears / (double) intervalYears);
        var candidate = firstOccurrence.AddYears(fullIntervals * intervalYears);

        while (attempts++ < maxAttempts)
        {
            try
            {
                // Try to create target date (keeping original month/day)
                var nextCandidate = new DateTimeOffset(
                    candidate.Year,
                    firstOccurrence.Month,
                    firstOccurrence.Day,
                    candidate.Hour,
                    candidate.Minute,
                    candidate.Second,
                    candidate.Offset
                );

                // Check if conditions are met
                if (nextCandidate > current)
                {
                    return nextCandidate;
                }

                // Move to next interval
                candidate = candidate.AddYears(intervalYears);
            }
            catch (ArgumentOutOfRangeException)
            {
                // Skip invalid date (e.g. Feb 29th in non-leap year)
                candidate = candidate.AddYears(intervalYears);
                continue;
            }
        }

        return DateTimeOffset.MaxValue;
    }

    private DateTimeOffset FindNextDay(
        DateTimeOffset firstOccurrence,
        DateTimeOffset current,
        DayOfWeek[] targetDays,
        int weeksPerPeroid
    )
    {
        var currentDate = current.Date;
        var startDate = firstOccurrence.Date;
        var dayInterval = weeksPerPeroid * 7;

        // Calculate complete week intervals
        var totalDays = (currentDate - startDate).TotalDays;
        var periods = (int) (totalDays / dayInterval);

        // Calculate base date for current interval
        var baseDate = startDate.AddDays(periods * dayInterval);

        // Search within current interval
        for (int i = 0; i < dayInterval; i++)
        {
            var candidate = baseDate.AddDays(i);
            if (candidate < currentDate)
                continue;

            if (targetDays.Contains(candidate.DayOfWeek))
            {
                bool rightWeek = ((int) (candidate - startDate).TotalDays / 7) % weeksPerPeroid == 0;
                if (!rightWeek)
                {
                    _logger.LogInformation(
                        "Candidate: {Candidate}, StartDate: {StartDate}, weeksPerPeroid: {weeksPerPeroid}, current: {Current}",
                        candidate,
                        startDate,
                        weeksPerPeroid,
                        current);
                    continue;
                }

                return new DateTimeOffset(
                    candidate.Year,
                    candidate.Month,
                    candidate.Day,
                    firstOccurrence.Hour,
                    firstOccurrence.Minute,
                    firstOccurrence.Second,
                    firstOccurrence.Offset
                );
            }
        }

        // Move to next interval and find nearest target day
        var daysToAdd = targetDays
            .Select(d => (d - baseDate.DayOfWeek + 7) % 7)
            .Min();

        var nextIntervalDate = baseDate.AddDays(dayInterval + daysToAdd);
        return new DateTimeOffset(
            nextIntervalDate.Year,
            nextIntervalDate.Month,
            nextIntervalDate.Day,
            firstOccurrence.Hour,
            firstOccurrence.Minute,
            firstOccurrence.Second,
            firstOccurrence.Offset
        );
    }

    private DateTimeOffset CalculateHourly(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings settings,
        DateTimeOffset current)
    {
        var interval = settings.Hour ?? 1;
        var elapsedHours = (current - firstOccurrence).TotalHours;
        var intervals = (int) Math.Ceiling(elapsedHours / interval);
        return firstOccurrence.AddHours(interval * intervals);
    }

    private DateTimeOffset CalculateMinutely(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings settings,
        DateTimeOffset current)
    {
        var interval = settings.Minute ?? 1;
        var elapsedMinutes = (current - firstOccurrence).TotalMinutes;
        var intervals = (int) Math.Ceiling(elapsedMinutes / interval);
        return firstOccurrence.AddMinutes(interval * intervals);
    }
}