name: Cleanup Old Container Images

on:
  schedule:
    # Run at 00:00 UTC every Sunday
    - cron: '0 0 * * 0'
  # Allow manual trigger
  workflow_dispatch:

env:
  REGISTRY: ghcr.io

jobs:
  cleanup:
    name: Cleanup Old Images
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Get Repository Owner
        id: owner
        run: echo "owner=$(echo ${{ github.repository_owner }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

      - name: Delete Old Images
        env:
          GH_TOKEN: ${{ secrets.CONTAINER_IMAGES_REMOVAL_WORKFLOW_TOKEN }}
        run: |
          # Function to delete old versions of an image
          get_packages() {
            gh api \
              -H "Accept: application/vnd.github+json" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              "/orgs/SleekFlow/packages?package_type=container" \
              --paginate | jq -r '.[] | select(.name | startswith("sleekflow")) | .name'
          }

          delete_old_versions() {
            local image="$1"

            # Get all versions of the image
            res=$(gh api \
              -H "Accept: application/vnd.github+json" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              "/orgs/SleekFlow/packages/container/$image/versions" \
              --paginate | jq '[.[]] | unique_by(.id)')

            # Check if the response is empty
            if [[ -z "$res" ]]; then
              echo "No data received from GitHub API"
              exit 1
            fi

            # Validate the response is in valid JSON format
            if ! echo "$res" | jq empty 2>/dev/null; then
              echo "Invalid JSON received from GitHub API"
              exit 1
            fi

            versions=$(echo "$res" | jq -c '.[] | select(.metadata.container.tags[] | contains("buildcache") | not) | {id: .id, created_at: .created_at, tags: .metadata.container.tags}')

            # Current timestamp and 30 days ago in seconds
            now=$(date +%s)
            thirty_days_ago=$((now - 2592000))

            echo "$versions" | while IFS= read -r version; do
              # Skip if empty
              [ -z "$version" ] && continue

              created_at=$(echo "$version" | jq -r '.created_at')
              created_timestamp=$(date -d "$created_at" +%s)
              id=$(echo "$version" | jq -r '.id')
              tags=$(echo "$version" | jq -r '.tags[]')

              # If older than 30 days and not tagged as latest
              if [ $created_timestamp -lt $thirty_days_ago ] && ! echo "$tags" | grep -q "latest"; then
                if gh api \
                    --method DELETE \
                    -H "Accept: application/vnd.github+json" \
                    -H "X-GitHub-Api-Version: 2022-11-28" \
                    "/orgs/SleekFlow/packages/container/$image/versions/$id" >/dev/null 2>&1; then  
                    echo "Successfully deleted version $id from $image"  
                fi  
              fi
            done
          }

          # Process each package
          packages=$(get_packages)
          for package in $packages; do
            echo "Processing image: $package"
            delete_old_versions "$package"
          done
