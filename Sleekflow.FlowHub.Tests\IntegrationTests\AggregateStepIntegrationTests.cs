using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.FlowHub.Executor.Saga;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

internal class AggregateStepIntegrationTestsFixture
{
    public Guid CorrelationId { get; set; }

    public OnAggregateStepEvent AggregateEvent1 { get; set; }

    public OnAggregateStepEvent AggregateEvent2 { get; set; }

    public AggregateStepIntegrationTestsFixture()
    {
        var stepId = Guid.NewGuid().ToString();

        AggregateEvent1 =
            new OnAggregateStepEvent(
                stepId,
                "state-0",
                "contact-0",
                1,
                []);

        AggregateEvent2 =
            new OnAggregateStepEvent(
                stepId,
                "state-1",
                "contact-0",
                1,
                []);

        CorrelationId = AggregateEvent1.GetCorrelationId();
    }
}

public class AggregateStepIntegrationTests
{
    [Test]
    public async Task AggregateStateMachineCreateTest()
    {
        var fixture = new AggregateStepIntegrationTestsFixture();
        var provider = Application.InMemoryBusHost.Server.Services;
        var harness = provider.GetRequiredService<ITestHarness>();

        await harness.Bus.Publish(fixture.AggregateEvent1);
        Assert.IsTrue(
            await harness.Consumed.Any<OnAggregateStepEvent>(
                ev => ev.Context.Message.GetCorrelationId() == fixture.CorrelationId));

        var sagaHarness = harness
            .GetSagaStateMachineHarness<
                AggregateStateMachine,
                AggregateState>();

        Assert.That(
            await sagaHarness.Consumed.Any<OnAggregateStepEvent>(
                ev => ev.Context.Message.GetCorrelationId() == fixture.CorrelationId));
        Assert.That(await sagaHarness.Created.Any(x => x.CorrelationId == fixture.CorrelationId));
    }

    [Test]
    public async Task AggregateStateMachineAggregateTest()
    {
        var fixture = new AggregateStepIntegrationTestsFixture();
        var provider = Application.InMemoryBusHost.Server.Services;
        var harness = provider.GetRequiredService<ITestHarness>();

        await harness.Bus.Publish(fixture.AggregateEvent1);

        var sagaHarness = harness
            .GetSagaStateMachineHarness<
                AggregateStateMachine,
                AggregateState>();

        Assert.That(
            await sagaHarness.Consumed.Any<OnAggregateStepEvent>(
                ev => ev.Context.Message.GetCorrelationId() == fixture.CorrelationId));
        Assert.That(await sagaHarness.Created.Any(x => x.CorrelationId == fixture.CorrelationId));

        await harness.Bus.Publish(fixture.AggregateEvent2);
        Assert.That(
            await sagaHarness.Consumed.Any<OnAggregateStepEvent>(
                ev => ev.Context.Message.ProxyStateId == fixture.AggregateEvent2.ProxyStateId));
        Assert.That(await sagaHarness.Sagas.Any(x => x.CurrentState == 3));
        Assert.IsTrue(
            await harness.Published.Any<OnAggregateStepAggregatedEvent>(
                ev => ev.Context.Message.ProxyStateId == fixture.AggregateEvent2.ProxyStateId));
    }

    [Test]
    public async Task AggregateStateMachineDurationFinishTest()
    {
        var fixture = new AggregateStepIntegrationTestsFixture();
        var provider = Application.InMemoryBusHost.Server.Services;
        var harness = provider.GetRequiredService<ITestHarness>();

        await harness.Bus.Publish(fixture.AggregateEvent1);
        Assert.IsTrue(
            await harness.Consumed.Any<OnAggregateStepEvent>(
                ev => ev.Context.Message.GetCorrelationId() == fixture.CorrelationId));

        var sagaHarness = harness
            .GetSagaStateMachineHarness<
                AggregateStateMachine,
                AggregateState>();

        Assert.That(
            await sagaHarness.Consumed.Any<OnAggregateStepEvent>(
                ev => ev.Context.Message.GetCorrelationId() == fixture.CorrelationId));
        Assert.That(await sagaHarness.Created.Any(x => x.CorrelationId == fixture.CorrelationId));

        Assert.IsTrue(
            await harness.Published.Any<OnAggregateStepAggregatedEvent>(
                ev => ev.Context.Message.ProxyStateId == fixture.AggregateEvent1.ProxyStateId));
    }
}