using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class UploadFileDocumentToAllAssignedAgentsOrchestrator : ITrigger
{
    private readonly ILogger<UploadFileDocumentToAllAssignedAgentsOrchestrator> _logger;

    public UploadFileDocumentToAllAssignedAgentsOrchestrator(
        ILogger<UploadFileDocumentToAllAssignedAgentsOrchestrator> logger)
    {
        _logger = logger;
    }

    public class UploadFileDocumentToAllAssignedAgentsOrchestratorOutput
    {
        [JsonConstructor]
        public UploadFileDocumentToAllAssignedAgentsOrchestratorOutput()
        {
        }
    }

    [Function(nameof(UploadFileDocumentToAllAssignedAgentsOrchestrator))]
    public async Task<UploadFileDocumentToAllAssignedAgentsOrchestratorOutput> Process(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var input = context.GetInput<StartUploadToAgentKnowledgeBasesEvent>();
        _logger.LogInformation(
            "UploadFileDocumentToAllAssignedAgentsOrchestrator document {DocumentId}",
            input!.DocumentId);

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(60), 1)));

        var getKbDocumentOutput = await context
            .CallActivityAsync<GetKbDocument.GetKbDocumentOutput>(
                nameof(GetKbDocument),
                new GetKbDocument.GetKbDocumentInput(
                    input.SleekflowCompanyId,
                    input.DocumentId),
                taskOptions);

        var kbDocumentJObject = JObject.FromObject(getKbDocumentOutput.KbDocument);
        var sysTypeName = (string) kbDocumentJObject[Entity.PropertyNameSysTypeName]!;
        KbDocument kbDocument = sysTypeName switch
        {
            SysTypeNames.FileDocument => kbDocumentJObject.ToObject<FileDocument>()!,
            SysTypeNames.WebsiteDocument => kbDocumentJObject.ToObject<WebsiteDocument>()!,
            _ => throw new Exception($"Unknown system type: {sysTypeName}")
        };

        var chunkIds = getKbDocumentOutput.ChunkIds;
        var agentAssignments = kbDocument.AgentAssignments ?? [];

        if (kbDocument.FileDocumentProcessStatus != ProcessFileDocumentStatuses.ReadyToAssign &&
            kbDocument.FileDocumentProcessStatus != ProcessFileDocumentStatuses.Completed)
        {
            throw new Exception(
                "UploadFileDocumentToAllAssignedAgentsOrchestratorOutput fileDocument is not ready to assign.");
        }

        foreach (var agentAssignment in agentAssignments)
        {
            switch (agentAssignment.RagStatus)
            {
                case RagStatus.Pending:
                    try
                    {
                        _logger.LogInformation(
                            "Uploading file document {DocumentId} to assigned agent {AgentId}",
                            input.DocumentId,
                            agentAssignment.AgentId);

                        await context
                            .CallActivityAsync<PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                                nameof(PatchAgentAssignmentRagStatus),
                                new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                                    input.SleekflowCompanyId,
                                    input.DocumentId,
                                    agentAssignment.AgentId,
                                    RagStatus.InProgress,
                                    0.0),
                                taskOptions);

                        if (agentAssignment.AgentId == CompanyAgentTypes.SmartReply)
                        {
                            // an exceptional case - Smart Reply is still using the old key-value search
                            await context
                                .CallActivityAsync<
                                    LoadFileDocumentChunksToKnowledgeBase.LoadFileDocumentChunksToKnowledgeBaseOutput>(
                                    nameof(LoadFileDocumentChunksToKnowledgeBase),
                                    new LoadFileDocumentChunksToKnowledgeBase.
                                        LoadFileDocumentChunksToKnowledgeBaseInput(
                                            input.SleekflowCompanyId,
                                            input.DocumentId),
                                    taskOptions);
                        }
                        else
                        {
                            // parallel upload to LightRAG has issues, so just upload 1 chunk at a time
                            for (var i = 0; i < chunkIds.Count; ++i)
                            {
                                await context
                                    .CallActivityAsync<UploadFileDocumentChunkToAgentKnowledgeBase.
                                        UploadFileDocumentChunkToAgentKnowledgeBaseOutput>(
                                        nameof(UploadFileDocumentChunkToAgentKnowledgeBase),
                                        new UploadFileDocumentChunkToAgentKnowledgeBase.
                                            UploadFileDocumentChunkToAgentKnowledgeBaseInput(
                                                input.SleekflowCompanyId,
                                                input.DocumentId,
                                                chunkIds[i],
                                                agentAssignment.AgentId),
                                        taskOptions);

                                await context
                                    .CallActivityAsync<
                                        PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                                        nameof(PatchAgentAssignmentRagStatus),
                                        new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                                            input.SleekflowCompanyId,
                                            input.DocumentId,
                                            agentAssignment.AgentId,
                                            RagStatus.InProgress,
                                            (i + 1) * 100.0 / chunkIds.Count),
                                        taskOptions);
                            }
                        }

                        await context
                            .CallActivityAsync<PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                                nameof(PatchAgentAssignmentRagStatus),
                                new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                                    input.SleekflowCompanyId,
                                    input.DocumentId,
                                    agentAssignment.AgentId,
                                    RagStatus.Completed,
                                    100.0),
                                taskOptions);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("UploadFileDocumentToAgentKnowledgeBase failed: {Message}", ex.Message);

                        await context
                            .CallActivityAsync<PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusOutput>(
                                nameof(PatchAgentAssignmentRagStatus),
                                new PatchAgentAssignmentRagStatus.PatchAgentAssignmentRagStatusInput(
                                    input.SleekflowCompanyId,
                                    input.DocumentId,
                                    agentAssignment.AgentId,
                                    RagStatus.Failed,
                                    0.0),
                                taskOptions);
                    }

                    break;

                case RagStatus.InProgress:
                case RagStatus.Completed:
                case RagStatus.Failed:
                    _logger.LogInformation(
                        "{DocumentId} skipping upload to assigned agent {AgentId}, rag status: {RagStatus}",
                        input.DocumentId,
                        agentAssignment.AgentId,
                        agentAssignment.RagStatus);
                    break;
            }
        }

        return new UploadFileDocumentToAllAssignedAgentsOrchestratorOutput();
    }
}