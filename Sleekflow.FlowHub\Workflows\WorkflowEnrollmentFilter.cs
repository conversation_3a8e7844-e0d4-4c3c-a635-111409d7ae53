﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowEnrollmentFilter
{
    WorkflowEnrollmentFilterResult Filter(
        IReadOnlyList<CompactWorkflow> matchedWorkflows,
        IReadOnlyList<string> runningWorkflowIds,
        IReadOnlyList<string> completeExecutionWorkflowIds);
}

public class WorkflowEnrollmentFilter : IWorkflowEnrollmentFilter, IScopedService
{
    public WorkflowEnrollmentFilterResult Filter(
        IReadOnlyList<CompactWorkflow> matchedWorkflows,
        IReadOnlyList<string> runningWorkflowIds,
        IReadOnlyList<string> completeExecutionWorkflowIds)
    {
        // Workflows with default settings
        var defaultSettingWorkflows = matchedWorkflows
            .Where(IsWorkflowWithDefaultSettings)
            .ToHashSet();

        var defaultWorkflowsToEnroll = defaultSettingWorkflows
            .Where(workflow => !runningWorkflowIds.Contains(workflow.WorkflowId))
            .ToHashSet();

        // Workflows that can enroll only once
        var workflowsThatCanEnrollOnlyOnce = matchedWorkflows
            .Where(IsWorkflowThatCanEnrollOnlyOnce)
            .ToHashSet();

        var workflowsThatCanEnrollOnlyOnceToEnroll = workflowsThatCanEnrollOnlyOnce
            .Where(workflow =>
                !runningWorkflowIds.Contains(workflow.WorkflowId)
                && !completeExecutionWorkflowIds.Contains(workflow.WorkflowId))
            .ToHashSet();

        var workflowsSkippedDueToCanEnrollOnlyOnce = workflowsThatCanEnrollOnlyOnce
            .Except(workflowsThatCanEnrollOnlyOnceToEnroll)
            .Where(workflow => !runningWorkflowIds.Contains(workflow.WorkflowId))
            .ToHashSet();

        // Workflows that can enroll again only on failure
        var workflowsThatCanEnrollAgainOnFailureOnly = matchedWorkflows
            .Where(IsWorkflowThatCanEnrollAgainOnlyFailure)
            .ToHashSet();

        var workflowsThatCanEnrollAgainOnFailureOnlyToEnroll = workflowsThatCanEnrollAgainOnFailureOnly
            .Where(workflow =>
                !runningWorkflowIds.Contains(workflow.WorkflowId)
                && !completeExecutionWorkflowIds.Contains(workflow.WorkflowId))
            .ToHashSet();

        var workflowsSkippedDueToCanEnrollAgainOnFailureOnly = workflowsThatCanEnrollAgainOnFailureOnly
            .Except(workflowsThatCanEnrollAgainOnFailureOnlyToEnroll)
            .Where(workflow => !runningWorkflowIds.Contains(workflow.WorkflowId))
            .ToHashSet();

        // Workflows that can enroll in parallel
        var workflowsThatCanEnrollInParallel = matchedWorkflows
            .Where(IsWorkflowThatCanEnrollInParallel)
            .ToHashSet();

        var workflowsIncludedDueToCanEnrollInParallel = workflowsThatCanEnrollInParallel
            .Where(workflow => runningWorkflowIds.Contains(workflow.WorkflowId))
            .ToHashSet();

        // Finalize
        var workflowsToEnroll = defaultWorkflowsToEnroll
            .Union(workflowsThatCanEnrollOnlyOnceToEnroll)
            .Union(workflowsThatCanEnrollAgainOnFailureOnlyToEnroll)
            .Union(workflowsThatCanEnrollInParallel)
            .ToHashSet();

        return new WorkflowEnrollmentFilterResult(
            workflowsToEnroll,
            workflowsSkippedDueToCanEnrollOnlyOnce,
            workflowsSkippedDueToCanEnrollAgainOnFailureOnly,
            workflowsIncludedDueToCanEnrollInParallel);
    }

    private static bool IsWorkflowWithDefaultSettings(CompactWorkflow workflow)
        => workflow.WorkflowEnrollmentSettings is
        {
            CanEnrollOnlyOnce: false,
            CanEnrollAgainOnFailureOnly: false,
            CanEnrollInParallel: false,
        };

    private static bool IsWorkflowThatCanEnrollOnlyOnce(
        CompactWorkflow workflow)
        => workflow.WorkflowEnrollmentSettings is
           {
               CanEnrollOnlyOnce: true,
               CanEnrollAgainOnFailureOnly: false,
               CanEnrollInParallel: false,
           };

    private static bool IsWorkflowThatCanEnrollAgainOnlyFailure(
        CompactWorkflow workflow)
        => workflow.WorkflowEnrollmentSettings is
           {
               CanEnrollOnlyOnce: false,
               CanEnrollAgainOnFailureOnly: true,
               CanEnrollInParallel: false,
           };

    private static bool IsWorkflowThatCanEnrollInParallel(
        CompactWorkflow workflow)
        => workflow.WorkflowEnrollmentSettings is
        {
            CanEnrollOnlyOnce: false,
            CanEnrollAgainOnFailureOnly: false,
            CanEnrollInParallel: true,
        };
}

public record WorkflowEnrollmentFilterResult(
    HashSet<CompactWorkflow> WorkflowsToEnroll,
    HashSet<CompactWorkflow> WorkflowsSkippedDueToCanEnrollOnlyOnce,
    HashSet<CompactWorkflow> WorkflowsSkippedDueToCanEnrollOnlyOnFailure,
    HashSet<CompactWorkflow> WorkflowsIncludedDueToCanEnrollInParallel);