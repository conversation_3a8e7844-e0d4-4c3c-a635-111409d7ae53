using Sleekflow.Outputs;
using Sleekflow.UserEventHub.Tests.Constants;
using Sleekflow.UserEventHub.Triggers.Authorized.Notifications;

namespace Sleekflow.UserEventHub.Tests.Notifications;

[Order(1)]
public class EnableAndDisableMobilePushNotificationTest
{
    [Test]
    [Order(1)]
    public async Task TestEnableMobilePushNotification()
    {
        var enableMobilePushNotificationInput = new RegisterAndEnableMobilePushNotification.RegisterAndEnableMobilePushNotificationInput(
            Mocks.NotificationToken,
            new[]
            {
                Mocks.FakeUserId,
                Mocks.FakeCompanyId
            },
            Mocks.FakeDeviceId,
            Mocks.FakePlatform,
            Mocks.FakeHubName);
        var enableMobilePushNotificationResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader("X-Sleekflow-Company-Id", Mocks.FakeCompanyId);
                _.WithRequestHeader("X-Sleekflow-User-Id", Mocks.FakeUserId);
                _.WithRequestHeader("X-Sleekflow-Staff-Id", Mocks.FakeStaffId);
                _.Post.Json(enableMobilePushNotificationInput).ToUrl(Endpoints.RegisterAndEnableMobilePushNotification);
            });
        var enableMobilePushNotificationOutput = await enableMobilePushNotificationResult
            .ReadAsJsonAsync<Output<RegisterAndEnableMobilePushNotification.RegisterAndEnableMobilePushNotificationOutput>>();
        Assert.That(enableMobilePushNotificationOutput, Is.Not.Null);
        Assert.That(enableMobilePushNotificationOutput.Success, Is.True);
    }

    [Test]
    [Order(2)]
    public async Task TestDisableMobilePushNotification()
    {
        var disableMobilePushNotificationInput = new UnregisterMobilePushNotification.UnregisterMobilePushNotificationInput();
        var disableMobilePushNotificationResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader("X-Sleekflow-Company-Id", Mocks.FakeCompanyId);
                _.WithRequestHeader("X-Sleekflow-User-Id", Mocks.FakeUserId);
                _.WithRequestHeader("X-Sleekflow-Staff-Id", Mocks.FakeStaffId);
                _.Post.Json(disableMobilePushNotificationInput).ToUrl(Endpoints.UnregisterMobilePushNotification);
            });
        var disableMobilePushNotificationOutput = await disableMobilePushNotificationResult
            .ReadAsJsonAsync<Output<UnregisterMobilePushNotification.UnregisterMobilePushNotificationOutput>>();
        Assert.That(disableMobilePushNotificationOutput, Is.Not.Null);
        Assert.That(disableMobilePushNotificationOutput.Success, Is.True);
    }
}