﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.HubspotQueues;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class CreateObject : ITrigger
{
    private readonly IIdService _idService;
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotQueueService _hubspotQueueService;

    public CreateObject(
        IIdService idService,
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotQueueService hubspotQueueService)
    {
        _idService = idService;
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotQueueService = hubspotQueueService;
    }

    public class CreateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("crm_hub_object_id")]
        [Required]
        public string CrmHubObjectId { get; set; }

        [JsonConstructor]
        public CreateObjectInput(
            string sleekflowCompanyId,
            Dictionary<string, object?> dict,
            string entityTypeName,
            string crmHubObjectId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Dict = dict;
            EntityTypeName = entityTypeName;
            CrmHubObjectId = crmHubObjectId;
        }
    }

    public class CreateObjectOutput
    {
        [JsonConstructor]
        public CreateObjectOutput(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }

        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }
    }

    public async Task<CreateObjectOutput> F(CreateObjectInput createObjectInput)
    {
        var authentication =
            await _hubspotAuthenticationService.GetAsync(createObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsOutput =
            await _hubspotObjectService.GetFieldsAsync(authentication, createObjectInput.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();

        var dict = createObjectInput.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e => e.Value);

        /*
            Special handling for HubSpot date fields.
            HubSpot requires date fields to be in midnight UTC time, or only the date part.
        */
        var dateFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "date")
            .Select(f => f.Name)
            .ToList();

        foreach (var dateFieldName in dateFieldNames)
        {
            if (dict.TryGetValue(dateFieldName, out var value)
                && value is DateTimeOffset offset)
            {
                dict[dateFieldName] = offset.Date.ToString("yyyy-MM-dd");
            }
        }

        await _hubspotQueueService.EnqueueItemAsync(
            createObjectInput.SleekflowCompanyId,
            new HubspotQueueItem(
                _idService.GetId("HubspotQueueItem"),
                null,
                createObjectInput.SleekflowCompanyId,
                createObjectInput.EntityTypeName,
                createObjectInput.CrmHubObjectId,
                null,
                dict,
                ObjectOperations.CreateObjectOperation,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow));

        return new CreateObjectOutput(true);
    }
}