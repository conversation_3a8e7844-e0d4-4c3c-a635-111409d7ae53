﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Dynamics365.Objects;

public class Dynamics365GetObjectsOutput
{
    [JsonConstructor]
    public Dynamics365GetObjectsOutput(
        string odataContext,
        string odataNextLink,
        List<Dictionary<string, object?>> value)
    {
        OdataContext = odataContext;
        OdataNextLink = odataNextLink;
        Value = value;
    }

    [JsonProperty("@odata.context")]
    public string OdataContext { get; set; }

    [JsonProperty("@odata.nextLink")]
    public string OdataNextLink { get; set; }

    [JsonProperty("value")]
    public List<Dictionary<string, object?>> Value { get; set; }
}