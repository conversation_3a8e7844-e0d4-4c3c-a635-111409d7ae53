using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.Crm;

public class GetPropertyValuesByContactIdsOutput
{
    [Required]
    [JsonProperty("property_values")]
    public Dictionary<string, List<object?>> PropertyValues { get; set; }

    [JsonConstructor]
    public GetPropertyValuesByContactIdsOutput(Dictionary<string, List<object?>> propertyValues)
    {
        PropertyValues = propertyValues;
    }
}