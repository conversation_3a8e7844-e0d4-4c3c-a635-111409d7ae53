using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateTicketStepExecutor : IStepExecutor;

public class CreateTicketStepExecutor
    : GeneralStepExecutor<CallStep<CreateTicketStepArgs>>,
        ICreateTicketStepExecutor,
        IScopedService
{
    private readonly ILogger _logger;
    private readonly IStateAggregator _stateAggregator;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public CreateTicketStepExecutor(
        ILogger<CreateTicketStepExecutor> logger,
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateAggregator stateAggregator,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _logger = logger;
        _stateAggregator = stateAggregator;
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var response = await _coreCommander.ExecuteAsync<CreateTicketOutput>(
                state.Origin,
                "CreateTicket",
                await GetArgs(callStep, state));

            var ticketExportData = await _coreCommander.ExecuteAsync<GetTicketExportDataOutput>(
                state.Origin,
                "GetTicketExportData",
                new GetTicketExportDataInput(
                    state.Id,
                    state.Identity,
                    response?.Id));
            state = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                JsonConvert.SerializeObject(ticketExportData));
            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class CreateTicketInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [Required]
        [JsonProperty("message_origin_channel")]
        public string MessageOriginChannel { get; set; }

        [Required]
        [JsonProperty("channel_type")]
        public string ChannelType { get; set; }

        [Required]
        [JsonProperty("channel_identity_id")]
        public string ChannelIdentityId { get; set; }

        [Required]
        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("due_date")]
        public DateTimeOffset? DueDate { get; set; }

        [JsonProperty("type_id")]
        public string? TypeId { get; set; }

        [Required]
        [JsonProperty("priority_id")]
        public string PriorityId { get; set; }

        [JsonProperty("description")]
        public string? Description { get; set; }

        [JsonProperty("is_unassigned")]
        [Required]
        public bool IsUnassigned { get; set; }

        [JsonProperty("assignment_strategy")]
        [Required]
        public string AssignmentStrategy { get; set; }

        [JsonProperty("team_id")]
        public string? TeamId { get; set; }

        [JsonProperty("staff_id")]
        public string? StaffId { get; set; }

        [JsonProperty("assignment_counter")]
        [Validations.ValidateObject]
        public long? AssignmentCounter { get; set; }

        [JsonConstructor]
        public CreateTicketInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactId,
            string messageOriginChannel,
            string channelType,
            string channelIdentityId,
            string title,
            DateTimeOffset? dueDate,
            string? typeId,
            string priorityId,
            bool isUnassigned,
            string? teamId,
            string? staffId,
            long? assignmentCounter,
            string assignmentStrategy,
            string? description)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactId = contactId;
            MessageOriginChannel = messageOriginChannel;
            ChannelType = channelType;
            ChannelIdentityId = channelIdentityId;
            Title = title;
            DueDate = dueDate;
            TypeId = typeId;
            PriorityId = priorityId;
            IsUnassigned = isUnassigned;
            TeamId = teamId;
            StaffId = staffId;
            AssignmentCounter = assignmentCounter;
            AssignmentStrategy = assignmentStrategy;
            Description = description;
        }
    }

    [SwaggerInclude]
    public class CreateTicketOutput
    {
        [JsonProperty("id")]
        [Required]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateTicketOutput(string id)
        {
            Id = id;
        }
    }

    private async Task<CreateTicketInput> GetArgs(
        CallStep<CreateTicketStepArgs> callStep,
        ProxyState state)
    {
        var contactId = (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                      state,
                                      "{{ (trigger_event_body.contact_id | string.whitespace) ? usr_var_dict.contact.id : trigger_event_body.contact_id }}")
                                  ?? throw new InvalidOperationException("No contact id found"));

        var title = (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.TitleExpr)
                              ?? callStep.Args.TitleExpr);

        var messageOriginChannel = callStep.Args.MessageOriginChannel;
        var channelType = callStep.Args.ChannelType;
        var channelIdentityId = callStep.Args.ChannelIdentityId;

        DateTimeOffset? dueDate = null;
        var dueDateSetting = callStep.Args.DueDateSetting;

        if(dueDateSetting == "after_ticket_creation_date")
        {
            var dueDateValueUnit = callStep.Args.DueDateValueUnit;
            var dueDatevalue = callStep.Args.DueDateValue;

            dueDate = dueDateValueUnit switch
            {
                "day" => DateTimeOffset.UtcNow.AddDays(dueDatevalue),
                "hour" => DateTimeOffset.UtcNow.AddHours(dueDatevalue),
                _ => dueDate
            };
        }

        var typeId = callStep.Args.TypeId;
        var priorityId = callStep.Args.PriorityId;
        var assignmentStrategy = callStep.Args.Strategy;
        var description = (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.DescriptionExpr)
                                    ?? callStep.Args.DescriptionExpr);

        switch (assignmentStrategy)
        {
            case CreateTicketStepArgs.CreateTicketV2StepArgsStrategy.Specific_Staff:
            {
                var staffId = callStep.Args.StaffId!;

                return new CreateTicketInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    messageOriginChannel,
                    channelType,
                    channelIdentityId,
                    title,
                    dueDate,
                    typeId,
                    priorityId,
                    false,
                    null,
                    staffId,
                    null,
                    assignmentStrategy,
                    description);
            }
            case CreateTicketStepArgs.CreateTicketV2StepArgsStrategy.Company_RoundRobbin_All_Staffs:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(CreateTicketStepExecutor) + "_company_staffs");

                return new CreateTicketInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    messageOriginChannel,
                    channelType,
                    channelIdentityId,
                    title,
                    dueDate,
                    typeId,
                    priorityId,
                    false,
                    null,
                    null,
                    increment,
                    assignmentStrategy,
                    description);
            }

            case CreateTicketStepArgs.CreateTicketV2StepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team:
            {
                var teamId = callStep.Args.TeamId!;

                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(CreateTicketStepExecutor) + "_team_staff_id");

                return new CreateTicketInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    messageOriginChannel,
                    channelType,
                    channelIdentityId,
                    title,
                    dueDate,
                    typeId,
                    priorityId,
                    false,
                    teamId,
                    null,
                    increment,
                    assignmentStrategy,
                    description);
            }
        }

#pragma warning disable S3928
        throw new ArgumentOutOfRangeException(nameof(callStep.Args.Strategy));
#pragma warning restore S3928
    }
}