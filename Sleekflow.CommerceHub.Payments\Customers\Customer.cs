namespace Sleekflow.CommerceHub.Payments.Customers;

public class Customer
{
    public string Id { get; set; }

    public string Email { get; set; }

    public string Phone { get; set; }

    public string Name { get; set; }

    public string[] ExternalIds { get; set; }

    public Customer(
        string id,
        string email,
        string phone,
        string name,
        string[] externalIds)
    {
        Id = id;
        Email = email;
        Phone = phone;
        Name = name;
        ExternalIds = externalIds;
    }
}