using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IPlanningPlugin
{
    [KernelFunction("plan_lead_assignment")]
    [Description(
        "Plans lead assignment using data from data pane and stores assignment plan with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with lead assignment planning details.")]
    Task<string> PlanLeadAssignmentWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where decision results are stored in the data pane.")]
        string decisionKey,
        [Description("Data key where classification results are stored in the data pane.")]
        string classificationKey);

    [KernelFunction("plan_demo_scheduling")]
    [Description(
        "Plans demo scheduling using data from data pane and stores demo plan with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with demo scheduling planning details.")]
    Task<string> PlanDemoSchedulingWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where decision results are stored in the data pane.")]
        string decisionKey,
        [Description("Data key where classification results are stored in the data pane.")]
        string classificationKey);
}

public class PlanningPlugin : BaseLeadNurturingPlugin, IPlanningPlugin
{
    public PlanningPlugin(
        ILogger<PlanningPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
    }

    private async Task<string> PlanLeadAssignmentAsync(
        Kernel kernel,
        string conversationContext,
        string decisionInfo,
        string classificationInfo)
    {
        var leadAssignmentPlanningAgent = _agentDefinitions.GetLeadAssignmentPlanningAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true));

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, classificationInfo);
        AddContextToThread(agentThread, decisionInfo);

        return await ExecuteAgentWithTelemetryAsync(
            leadAssignmentPlanningAgent,
            agentThread,
            "PlanningPlugin-LeadAssignment");
    }

    private async Task<string> PlanDemoSchedulingAsync(
        Kernel kernel,
        string conversationContext,
        string decisionInfo,
        string classificationInfo)
    {
        var demoSchedulingPlanningAgent = _agentDefinitions.GetDemoSchedulingPlanningAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true));

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, classificationInfo);
        AddContextToThread(agentThread, decisionInfo);

        return await ExecuteAgentWithTelemetryAsync(
            demoSchedulingPlanningAgent,
            agentThread,
            "PlanningPlugin-DemoScheduling");
    }

    [KernelFunction("plan_lead_assignment")]
    [Description(
        "Plans lead assignment using data from data pane and stores assignment plan with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with lead assignment planning details.")]
    public async Task<string> PlanLeadAssignmentWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where decision results are stored in the data pane.")]
        string decisionKey,
        [Description("Data key where classification results are stored in the data pane.")]
        string classificationKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.LeadAssignmentPlanningAgentResponse>(
            sessionKey,
            "PlanningLeadAssignment",
            dataRetrievalFunc: async () => await RetrieveMultipleDataAsync(
                (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
                (decisionKey, AgentOutputKeys.DecisionComplete, "decisionInfo"),
                (classificationKey, AgentOutputKeys.LeadClassifierComplete, "classificationInfo")
            ),
            agentExecutionFunc: async (data) => await PlanLeadAssignmentAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["decisionInfo"],
                data["classificationInfo"]),
            storageKey: _keyManager.GetPlanningKey(sessionKey, "lead_assignment"),
            storageDataType: AgentOutputKeys.PlanningAssignmentComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreLeadAssignmentPlanningComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    [KernelFunction("plan_demo_scheduling")]
    [Description(
        "Plans demo scheduling using data from data pane and stores demo plan with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with demo scheduling planning details.")]
    public async Task<string> PlanDemoSchedulingWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where decision results are stored in the data pane.")]
        string decisionKey,
        [Description("Data key where classification results are stored in the data pane.")]
        string classificationKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.DemoSchedulingPlanningAgentResponse>(
            sessionKey,
            "PlanningDemoScheduling",
            dataRetrievalFunc: async () => await RetrieveMultipleDataAsync(
                (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
                (decisionKey, AgentOutputKeys.DecisionComplete, "decisionInfo"),
                (classificationKey, AgentOutputKeys.LeadClassifierComplete, "classificationInfo")
            ),
            agentExecutionFunc: async (data) => await PlanDemoSchedulingAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["decisionInfo"],
                data["classificationInfo"]),
            storageKey: _keyManager.GetPlanningKey(sessionKey, "demo_scheduling"),
            storageDataType: AgentOutputKeys.PlanningDemoComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreDemoSchedulingPlanningComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task StoreLeadAssignmentPlanningComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.LeadAssignmentPlanningAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "LeadAssignmentPlanningAgent", "action_type"),
                    "action_type",
                    parsedResponse.ActionType ?? "");

                _logger.LogInformation(
                    "Lead assignment planning completed for session {SessionKey}: {ActionType}",
                    sessionKey,
                    parsedResponse.ActionType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to store lead assignment planning components for session {SessionKey}",
                sessionKey);
        }
    }

    private async Task StoreDemoSchedulingPlanningComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.DemoSchedulingPlanningAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "DemoSchedulingPlanningAgent", "action_type"),
                    "action_type",
                    parsedResponse.ActionType ?? "");

                _logger.LogInformation(
                    "Demo scheduling planning completed for session {SessionKey}: {ActionType}",
                    sessionKey,
                    parsedResponse.ActionType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to store demo scheduling planning components for session {SessionKey}",
                sessionKey);
        }
    }
}