using System.Net;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Plugins.LeadNurturings;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using IReviewerPlugin = Sleekflow.IntelligentHub.Plugins.LeadNurturings.IReviewerPlugin;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ManagerLeadNurturings;

public class ExecutedTool
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("tool_name")]
    public string ToolName { get; set; } = string.Empty;

    [JsonProperty("outcome")]
    public string Outcome { get; set; } = string.Empty;
}

public class ReportPhase
{
    [JsonProperty("workflow_summary")]
    public string WorkflowSummary { get; set; } = string.Empty;

    [JsonProperty("lead_classification")]
    public string LeadClassification { get; set; } = string.Empty;

    [JsonProperty("decision_made")]
    public string DecisionMade { get; set; } = string.Empty;

    [JsonProperty("actions_taken")]
    public string ActionsTaken { get; set; } = string.Empty;

    [JsonProperty("executed_tools")]
    public List<ExecutedTool> ExecutedTools { get; set; } = new ();
}

public class ManagerLeadNurturingResponse
{
    [JsonProperty("agent_name")]
    public string AgentName { get; set; } = string.Empty;

    [JsonProperty("session_id")]
    public string SessionId { get; set; } = string.Empty;

    [JsonProperty("phase")]
    public string Phase { get; set; } = string.Empty;

    [JsonProperty("initial_phase")]
    public string? InitialPhase { get; set; }

    [JsonProperty("observation_phase")]
    public string? ObservationPhase { get; set; }

    [JsonProperty("report_phase")]
    public ReportPhase? ReportPhase { get; set; }

    [JsonProperty("next_phase")]
    public string? NextPhase { get; set; }

    [JsonProperty("result")]
    public string Result { get; set; } = string.Empty;
}

public interface IManagerLeadNurturingCollaborationDefinition : IAgentCollaborationDefinition
{
}

public class ManagerLeadNurturingCollaborationDefinition
    : BaseAgentCollaborationDefinition, IManagerLeadNurturingCollaborationDefinition, IScopedService
{
    private readonly ILogger<ManagerLeadNurturingCollaborationDefinition> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ILeadNurturingDataPane _dataPane;
    private readonly IDataPaneKeyManager _keyManager;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly ILeadClassifierPlugin _leadClassifierPlugin;
    private readonly IDecisionPlugin _decisionPlugin;
    private readonly IStrategyPlugin _strategyPlugin;
    private readonly IKeyBasedKnowledgePlugin _knowledgePlugin;
    private readonly IResponseCrafterPlugin _responseCrafterPlugin;
    private readonly IResponseGenerationPlugin _responseGenerationPlugin;
    private readonly IReviewerPlugin _reviewerPlugin;
    private readonly IPlanningPlugin _planningPlugin;
    private readonly IConfirmationPlugin _confirmationPlugin;
    private readonly IActionPlugin _actionPlugin;

    public ManagerLeadNurturingCollaborationDefinition(
        ILogger<ManagerLeadNurturingCollaborationDefinition> logger,
        ILoggerFactory loggerFactory,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IChatHistoryEnricherFactory enricherFactory,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        ILeadClassifierPlugin leadClassifierPlugin,
        IDecisionPlugin decisionPlugin,
        IStrategyPlugin strategyPlugin,
        IKeyBasedKnowledgePlugin knowledgePlugin,
        IResponseCrafterPlugin responseCrafterPlugin,
        IResponseGenerationPlugin responseGenerationPlugin,
        IReviewerPlugin reviewerPlugin,
        IPlanningPlugin planningPlugin,
        IConfirmationPlugin confirmationPlugin,
        IActionPlugin actionPlugin,
        IFileContentExtractionPlugin fileContentExtractionPlugin)
        : base(
            logger,
            kernel,
            summaryPlugin,
            languagePlugin,
            agentDurationTracker,
            companyConfigService,
            enricherFactory,
            fileContentExtractionPlugin)
    {
        _logger = logger;
        _loggerFactory = loggerFactory;
        _dataPane = dataPane;
        _keyManager = keyManager;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _leadClassifierPlugin = leadClassifierPlugin;
        _decisionPlugin = decisionPlugin;
        _strategyPlugin = strategyPlugin;
        _knowledgePlugin = knowledgePlugin;
        _responseCrafterPlugin = responseCrafterPlugin;
        _responseGenerationPlugin = responseGenerationPlugin;
        _reviewerPlugin = reviewerPlugin;
        _planningPlugin = planningPlugin;
        _confirmationPlugin = confirmationPlugin;
        _actionPlugin = actionPlugin;
    }

    public override Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig)
    {
        var settings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_GPT_4_1);

        var managerAgent = ManagerLeadNurturingAgent.Create(
            kernel,
            settings,
            _loggerFactory.CreateLogger<ManagerLeadNurturingAgent>(),
            _dataPane,
            _keyManager,
            _leadClassifierPlugin,
            _decisionPlugin,
            _strategyPlugin,
            _knowledgePlugin,
            _responseCrafterPlugin,
            _responseGenerationPlugin,
            _reviewerPlugin,
            _planningPlugin,
            _confirmationPlugin,
            _actionPlugin);

        return Task.FromResult(
            new List<Agent>
            {
                managerAgent
            });
    }

    public override async Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? companyAgentConfig)
    {
        // Get base initialization
        var (chatHistoryStr, context) = await base.InitializeChatHistoryAsync(
            null,
            groupChatIdStr,
            chatEntries,
            replyGenerationContext,
            agentCollaborationConfig,
            companyAgentConfig);

        agentGroupChat?.AddChatMessage(new ChatMessageContent(AuthorRole.User, "Group Chat Id: " + groupChatIdStr));

        // Store conversation and configuration in data pane for manager agent
        try
        {
            var sessionKey = groupChatIdStr;
            var conversationContextKey = _keyManager.GetConversationContextKey(sessionKey);

            // Store conversation context
            await _dataPane.StoreData(conversationContextKey, AgentOutputKeys.Conversation, chatHistoryStr);

            // Store configuration values for key-based retrieval
            await _dataPane.StoreData(
                _keyManager.GetAgentOutputKey(sessionKey, "Configuration", AgentOutputKeys.ResponseLanguage),
                AgentOutputKeys.ResponseLanguage,
                agentCollaborationConfig.DetectedResponseLanguage ?? "English");

            await _dataPane.StoreData(
                _keyManager.GetAgentOutputKey(
                    sessionKey,
                    "Configuration",
                    AgentOutputKeys.AdditionalInstructionResponse),
                AgentOutputKeys.AdditionalInstructionResponse,
                agentCollaborationConfig.AdditionalInstructionResponse ?? "");

            await _dataPane.StoreData(
                _keyManager.GetAgentOutputKey(
                    sessionKey,
                    "Configuration",
                    AgentOutputKeys.AdditionalInstructionStrategy),
                AgentOutputKeys.AdditionalInstructionStrategy,
                agentCollaborationConfig.AdditionalInstructionStrategy ?? "");

            _logger.LogInformation(
                "Stored conversation and configuration for session {SessionKey} in data pane",
                sessionKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store conversation in data pane for session {GroupChatId}", groupChatIdStr);

            throw ex;
        }

        return (chatHistoryStr, context);
    }

    public override SelectionStrategy CreateSelectionStrategy(Kernel kernel)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

        return new KernelFunctionSelectionStrategy(
            KernelFunctionFactory.CreateFromMethod(
#pragma warning disable SA1114

                // The parameter name should not be discarded, as it is being used in the reflection
                (string history) => ManagerLeadNurturingAgent.GetAgentName(),
#pragma warning restore SA1114
                "ManagerLeadNurturingCoordinatingFunction"),
            kernel)
        {
            HistoryReducer = new ChatHistoryTruncationReducer(1),
            HistoryVariableName = "history",
            ResultParser = result => result.GetValue<string>() ?? throw new NotImplementedException(),
            Arguments = new KernelArguments(promptExecutionSettings),
            UseInitialAgentAsFallback = true
        };
    }

    public override RegexTerminationStrategy CreateTerminationStrategy(List<Agent> agents)
    {
        // Terminate when TaskExecutionAgent produces a message with "phase": "report"
        return new RegexTerminationStrategy(
            expressions:
            [
                "\"phase\":\\s*\"report\""
            ])
        {
            MaximumIterations = 15,
            AutomaticReset = true,
            Agents = agents.Where(a => a.Name == ManagerLeadNurturingAgent.GetAgentName()).ToList()
        };
    }

    public override string GetFinalReplyTag() => string.Empty;

    public override async Task<string> GetFinalReplyAsync(ChatHistory chatHistory)
    {
        try
        {
            // Extract session key from the final report phase message
            var finalChatMessage = chatHistory
                .LastOrDefault(x =>
                    x.Content != null
                    && JsonUtils.TryParseJson<ManagerLeadNurturingResponse>(x.Content, out var response)
                    && response != null
                    && response.Phase == "report")
                ?.Content;

            if (finalChatMessage == null ||
                !JsonUtils.TryParseJson<ManagerLeadNurturingResponse>(finalChatMessage, out var finalResponse) ||
                string.IsNullOrEmpty(finalResponse?.SessionId))
            {
                return string.Empty;
            }

            var sessionKey = finalResponse.SessionId;

            // Try to retrieve natural response text from different possible sources in data pane
            // Priority order: ResponseCrafterAgent → TransitioningResponseCrafterAgent → InformationGatheringResponseCrafterAgent

            // 1. Try ResponseCrafterAgent (standard response)
            var responseText = await _dataPane.GetData(
                _keyManager.GetAgentOutputKey(sessionKey, "ResponseCrafterAgent", AgentOutputKeys.ResponseNatural),
                AgentOutputKeys.ResponseNatural);

            // 2. Try TransitioningResponseCrafterAgent (transition response)
            if (string.IsNullOrEmpty(responseText))
            {
                responseText = await _dataPane.GetData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "TransitioningResponseCrafterAgent",
                        AgentOutputKeys.ResponseNatural),
                    AgentOutputKeys.ResponseNatural);
            }

            // 3. Try InformationGatheringResponseCrafterAgent (info gathering response)
            if (string.IsNullOrEmpty(responseText))
            {
                responseText = await _dataPane.GetData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "InformationGatheringResponseCrafterAgent",
                        AgentOutputKeys.ResponseNatural),
                    AgentOutputKeys.ResponseNatural);
            }

            if (string.IsNullOrEmpty(responseText))
            {
                _logger.LogWarning("No response text found in data pane for session {SessionKey}", sessionKey);
                return string.Empty;
            }

            // Apply the same formatting as before
            var finalReplyToCustomer = responseText.Trim();

            // We encountered a scenario where the final reply to the customer was not decoded properly.
            // The emoji is displayed as a string instead of the actual emoji.
            // e.g. "👍" is displayed as "&#128077;". We need to decode the HTML entities.
            finalReplyToCustomer = WebUtility.HtmlDecode(finalReplyToCustomer);

            // Convert HTML-style formatting tags to WhatsApp markdown format
            finalReplyToCustomer = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(finalReplyToCustomer);

            _logger.LogInformation("Retrieved final response from data pane for session {SessionKey}", sessionKey);
            return finalReplyToCustomer;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving final reply from data pane");
            return string.Empty;
        }
    }

    public override string GetSourceTag() => "CONFIRMED_KNOWLEDGE";

    public override async Task<string> GetSourceAsync(ChatHistory chatHistory, string groupChatIdStr)
    {
        try
        {
            var sessionKey = groupChatIdStr;

            // Retrieve the complete knowledge result that was used during the lead nurturing process
            var knowledgeResult = await _dataPane.GetData(
                _keyManager.GetKnowledgeKey(sessionKey),
                AgentOutputKeys.KnowledgeComplete);

            return knowledgeResult ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving knowledge source for session {GroupChatId}", groupChatIdStr);
            return string.Empty;
        }
    }
}