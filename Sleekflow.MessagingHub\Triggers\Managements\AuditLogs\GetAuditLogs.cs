using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.MessagingHub.Models.Constants;

namespace Sleekflow.MessagingHub.Triggers.Managements.AuditLogs;

[TriggerGroup(ControllerNames.Managements)]
public class GetAuditLogs
    : ITrigger<
        GetAuditLogs.GetAuditLogsInput,
        GetAuditLogs.GetAuditLogsOutput>
{
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<GetAuditLogs> _logger;

    public GetAuditLogs(
        IAuditLogService auditLogService,
        ILogger<GetAuditLogs> logger)
    {
        _auditLogService = auditLogService;
        _logger = logger;
    }

    public class GetAuditLogsInput
    {
        [Required]
        [JsonProperty("auditing_operations")]
        [Validations.ValidateArray]
        public List<string> AuditingOperations { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [JsonProperty("has_cloud_api_audits_exception")]
        [Validations.ValidateObject]
        public bool? HasCloudApiAuditsException { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetAuditLogsInput(
            List<string> auditingOperations,
            string? sleekflowCompanyId,
            string? continuationToken,
            bool? hasCloudApiAuditsException,
            int limit)
        {
            AuditingOperations = auditingOperations;
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            HasCloudApiAuditsException = hasCloudApiAuditsException;
            Limit = limit;
        }
    }

    public class GetAuditLogsOutput
    {
        [JsonProperty("audit_logs")]
        public List<AuditLog> AuditLogs { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetAuditLogsOutput(List<AuditLog> auditLogs, string? continuationToken)
        {
            AuditLogs = auditLogs;
            ContinuationToken = continuationToken;
        }
    }

    public async Task<GetAuditLogsOutput> F(GetAuditLogsInput input)
    {
        var (auditLogs, nextContinuationToken) = await _auditLogService.GetWhatsappCloudApiConnectionAuditLogsAsync(
            input.AuditingOperations,
            input.SleekflowCompanyId,
            input.HasCloudApiAuditsException,
            input.ContinuationToken,
            input.Limit);

        return new GetAuditLogsOutput(auditLogs, nextContinuationToken);
    }
}