using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class CreateCategory
    : ITrigger<
        CreateCategory.CreateCategoryInput,
        CreateCategory.CreateCategoryOutput>
{
    private readonly ICategoryService _categoryService;
    private readonly IIdService _idService;

    public CreateCategory(
        ICategoryService categoryService,
        IIdService idService)
    {
        _categoryService = categoryService;
        _idService = idService;
    }

    public class CreateCategoryInput : IHasSleekflowStaff, IHasMetadata
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId, NullValueHandling = NullValueHandling.Ignore)]
        public string StoreId { get; set; }

        [Required]
        [ValidateArray]
        [MinLength(1)]
        [MaxLength(32)]
        [JsonProperty(Category.PropertyNameNames, NullValueHandling = NullValueHandling.Ignore)]
        public List<Multilingual> Names { get; set; }

        [Required]
        [ValidateArray]
        [MaxLength(32)]
        [JsonProperty(Category.PropertyNameDescriptions, NullValueHandling = NullValueHandling.Ignore)]
        public List<Description> Descriptions { get; set; }

        [ValidateObject]
        [JsonProperty(Category.PropertyNamePlatformData, NullValueHandling = NullValueHandling.Ignore)]
        public PlatformData? PlatformData { get; set; }

        [Required]
        [JsonProperty(IHasMetadata.PropertyNameMetadata)]
        public Dictionary<string, object?> Metadata { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateCategoryInput(
            string sleekflowCompanyId,
            string storeId,
            List<Multilingual> names,
            List<Description> descriptions,
            PlatformData? platformData,
            Dictionary<string, object?> metadata,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            Names = names;
            Descriptions = descriptions;
            PlatformData = platformData;
            Metadata = metadata;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateCategoryOutput
    {
        [JsonProperty("category")]
        public CategoryDto Category { get; set; }

        [JsonConstructor]
        public CreateCategoryOutput(CategoryDto category)
        {
            Category = category;
        }
    }

    public async Task<CreateCategoryOutput> F(CreateCategoryInput createCategoryInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createCategoryInput.SleekflowStaffId,
            createCategoryInput.SleekflowStaffTeamIds);

        var category = await _categoryService.CreateAndGetCategoryAsync(
            new Category(
                _idService.GetId(SysTypeNames.Category),
                createCategoryInput.SleekflowCompanyId,
                createCategoryInput.Names,
                createCategoryInput.Descriptions,
                createCategoryInput.StoreId,
                createCategoryInput.PlatformData ?? PlatformData.CustomCatalog(),
                new List<string>
                {
                    RecordStatuses.Active
                },
                createCategoryInput.Metadata,
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowStaff);

        return new CreateCategoryOutput(new CategoryDto(category));
    }
}