using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class EnableWorkflowDraft : ITrigger<
    EnableWorkflowDraft.EnableWorkflowDraftInput,
    EnableWorkflowDraft.EnableWorkflowDraftOutput>
{
    private readonly IWorkflowService _workflowService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IWorkflowStepValidator _workflowStepValidator;

    public EnableWorkflowDraft(
        IWorkflowService workflowService,
        IFlowHubConfigService flowHubConfigService,
        IServiceProvider serviceProvider,
        IWorkflowStepValidator workflowStepValidator)
    {
        _workflowService = workflowService;
        _flowHubConfigService = flowHubConfigService;
        _serviceProvider = serviceProvider;
        _workflowStepValidator = workflowStepValidator;
    }

    public class EnableWorkflowDraftInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public EnableWorkflowDraftInput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowVersionedId = workflowVersionedId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class EnableWorkflowDraftOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public EnableWorkflowDraftOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<EnableWorkflowDraftOutput> F(EnableWorkflowDraftInput input)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var numOfActiveWorkflows = await _workflowService.CountWorkflowsAsync(
            input.SleekflowCompanyId,
            activationStatus: WorkflowActivationStatuses.Active,
            workflowType: WorkflowType.Normal);

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(input.SleekflowCompanyId);

        var usageLimit = flowHubConfig.UsageLimit;
        var usageLimitOffset = flowHubConfig.UsageLimitOffset;

        var workflowType = (await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId))?.WorkflowType;

        if ((string.IsNullOrEmpty(workflowType) || workflowType == WorkflowType.Normal) && usageLimit != null)
        {
            var maximumNumOfActiveWorkflows = usageLimit.MaximumNumOfActiveWorkflows ?? 0;
            var maximumNumOfActiveWorkflowsOffset = usageLimitOffset?.MaximumNumOfActiveWorkflowsOffset ?? 0;

            if (numOfActiveWorkflows >= maximumNumOfActiveWorkflows + maximumNumOfActiveWorkflowsOffset)
            {
                throw new SfFlowHubExceedUsageException(UsageLimitFieldNames.PropertyNameMaximumNumOfActiveWorkflows);
            }
        }

        await ValidateWorkflowDraftAsync(input);

        var workflow = await _workflowService.EnableWorkflowAsync(
            input.WorkflowVersionedId,
            input.SleekflowCompanyId,
            sleekflowStaff);

        return new EnableWorkflowDraftOutput(new WorkflowDto(workflow));
    }

    private async Task ValidateWorkflowDraftAsync(EnableWorkflowDraftInput input)
    {
        var targetWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId);

        if (targetWorkflow is null)
        {
            throw new SfNotFoundObjectException(input.WorkflowVersionedId);
        }

        if (targetWorkflow is { ActivationStatus: WorkflowActivationStatuses.Deleted })
        {
            throw new SfWorkflowDeletedException(targetWorkflow.WorkflowId);
        }

        var validationResults = new List<ValidationResult>();

        var isValid = Validator.TryValidateObject(
            targetWorkflow,
            new ValidationContext(targetWorkflow, _serviceProvider, null),
            validationResults,
            validateAllProperties: true);

        if (!isValid)
        {
            throw new SfValidationException(validationResults);
        }

        await _workflowStepValidator.AssertAllStepsAreValidAsync(
            targetWorkflow.Steps,
            targetWorkflow.WorkflowScheduleSettings.ScheduleType,
            targetWorkflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema);
    }
}