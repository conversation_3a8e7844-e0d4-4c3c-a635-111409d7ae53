using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.CommerceHub.Payments;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Payments;

[TriggerGroup(ControllerNames.Payments)]
public class RefundOrderPayment
    : ITrigger<
        RefundOrderPayment.RefundOrderPaymentInput,
        RefundOrderPayment.RefundOrderPaymentOutput>
{
    private readonly IOrderService _orderService;
    private readonly IPaymentService _paymentService;

    public RefundOrderPayment(
        IOrderService orderService,
        IPaymentService paymentService)
    {
        _orderService = orderService;
        _paymentService = paymentService;
    }

    public class RefundOrderPaymentInput : IHasSleekflowStaff, IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty("order_id")]
        public string OrderId { get; set; }

        [Required]
        [JsonProperty("payment_provider_name")]
        [RegularExpression("^(Stripe)$")]
        public string PaymentProviderName { get; set; }

        [Required]
        [JsonProperty("amount")]
        public decimal Amount { get; set; }

        [Required]
        [JsonProperty("reason")]
        public string Reason { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public RefundOrderPaymentInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string orderId,
            string paymentProviderName,
            decimal amount,
            string reason,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            OrderId = orderId;
            PaymentProviderName = paymentProviderName;
            Amount = amount;
            Reason = reason;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class RefundOrderPaymentOutput
    {
        [JsonProperty("refund_id")]
        public string RefundId { get; set; }

        [JsonConstructor]
        public RefundOrderPaymentOutput(
            string refundId)
        {
            RefundId = refundId;
        }
    }

    public async Task<RefundOrderPaymentOutput> F(
        RefundOrderPaymentInput refundOrderPaymentInput)
    {
        var order = await _orderService.GetAsync(
            refundOrderPaymentInput.OrderId,
            refundOrderPaymentInput.SleekflowCompanyId);

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            refundOrderPaymentInput.SleekflowStaffId,
            refundOrderPaymentInput.SleekflowStaffTeamIds);

        var refundOutput = await _paymentService.RefundOrderPaymentAsync(
            order,
            refundOrderPaymentInput.PaymentProviderName,
            refundOrderPaymentInput.Amount,
            refundOrderPaymentInput.Reason,
            sleekflowStaff);

        await _orderService.PatchAndGetOrderAsync(
            order.Id,
            order.SleekflowCompanyId,
            order.StoreId,
            order.SleekflowUserProfileId,
            OrderStatuses.PendingPayment,
            PaymentStatuses.GeneratedPaymentLink,
            order.Metadata,
            DateTimeOffset.UtcNow,
            null,
            sleekflowStaff);

        return new RefundOrderPaymentOutput(refundOutput.RefundId);
    }
}