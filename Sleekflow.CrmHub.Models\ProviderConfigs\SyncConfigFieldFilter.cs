﻿using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

public sealed class SyncConfigFieldFilter : IEquatable<SyncConfigFieldFilter>
{
    public static readonly Regex NameRegex = new Regex("^[a-zA-Z0-9_\\-]+$");

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonConstructor]
    public SyncConfigFieldFilter(string name)
    {
        Name = name;
    }

    public bool Equals(SyncConfigFieldFilter? other)
    {
        if (other == null)
        {
            return false;
        }

        return Name == other.Name;
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj))
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (obj.GetType() != this.GetType())
        {
            return false;
        }

        return Equals((SyncConfigFieldFilter) obj);
    }

    public override int GetHashCode()
    {
        return Name.GetHashCode();
    }
}