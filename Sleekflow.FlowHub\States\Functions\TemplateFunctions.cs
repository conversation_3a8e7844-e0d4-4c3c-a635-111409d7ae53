using System.Text.RegularExpressions;
using <PERSON><PERSON><PERSON>;
using Scriban.Runtime;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.Extensions;

namespace Sleekflow.FlowHub.States.Functions;

public class TemplateFunctions : ScriptObject
{
    public static string Eval(TemplateContext context, string? text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return string.Empty;
        }

        var expressions = ExtractExpressions(text);
        var evaluations = expressions
            .Select(x => EvaluateExpression(x, context))
            .ToList();
        for (var i = 0; i < expressions.Count; i++)
        {
            var currentExpression = expressions[i];
            var currentEvaluation = evaluations[i];

            var isExpressionButNotEvaluable = currentEvaluation == "<<FunctionNotFound>>";
            if (isExpressionButNotEvaluable)
            {
                text = RemoveBracesForExpression(text, currentExpression);
                continue;
            }

            text = ReplaceExpressionWithEvaluation(text, currentExpression, currentEvaluation);
        }

        return text;
    }

    private static List<string> ExtractExpressions(string input)
    {
        var pattern = @"{{([^{}]*)}}";
        var matches = Regex.Matches(input, pattern);
        return matches.Select(x => x.Groups[1].Value).ToList();
    }

    private static string EvaluateExpression(string expression, TemplateContext context)
    {
        object? res;
        try
        {
            res = Template.Evaluate(expression, context);
        }
        catch (Exception e)
        {
            var eStr = e.ToString();
            if (eStr.Contains("The function") && eStr.Contains("was not found"))
            {
                // meaning nothing to be evaluated anymore
                return "<<FunctionNotFound>>";
            }

            throw new SfScriptingException(e);
        }

        var isNotAnExpression = res is null || (res is string && string.IsNullOrEmpty(res.ToString()));
        if (isNotAnExpression)
        {
            return string.Empty;
        }

        var resultStr = res!.ToString()!;

        return resultStr.IsValidJson()
            ? resultStr
            : resultStr
                .Replace("{{", string.Empty)
                .Replace("}}", string.Empty);
    }

    private static string RemoveBracesForExpression(string text, string expression)
    {
        return text.Replace("{{" + expression + "}}", expression);
    }

    private static string ReplaceExpressionWithEvaluation(string text, string expression, string evaluation)
    {
        return text.Replace("{{" + expression + "}}", evaluation);
    }
}