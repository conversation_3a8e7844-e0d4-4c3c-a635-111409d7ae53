using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetWorkflows : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public GetWorkflows(
        IWorkflowService workflowService,
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowService = workflowService;
        _workflowExecutionService = workflowExecutionService;
    }

    public class GetWorkflowsInput : Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 50)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("search_name")]
        public string? SearchName { get; set; }

        [JsonProperty("workflow_filters")]
        [Validations.ValidateObject]
        public WorkflowFilters? WorkflowFilters { get; }

        [Validations.ValidateObject]
        [JsonProperty("statistics_filters")]
        public WorkflowExecutionStatisticsFilters? StatisticsFilters { get; }

        [JsonConstructor]
        public GetWorkflowsInput(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string? searchName,
            WorkflowFilters? workflowFilters,
            WorkflowExecutionStatisticsFilters? statisticsFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            Limit = limit;
            SearchName = searchName;
            WorkflowFilters = workflowFilters;
            StatisticsFilters = statisticsFilters;
        }
    }

    public class GetWorkflowsOutput
    {
        [JsonProperty("workflows")]
        public List<WorkflowListDto> Workflows { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetWorkflowsOutput(
            List<WorkflowListDto> workflows,
            string? nextContinuationToken)
        {
            Workflows = workflows;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetWorkflowsOutput> F(GetWorkflowsInput getWorkflowsInput)
    {
        var (workflows, nextContinuationToken) = await _workflowService.GetAllLatestWorkflowAndStatusTuplesAsync(
            getWorkflowsInput.SleekflowCompanyId,
            getWorkflowsInput.ContinuationToken,
            getWorkflowsInput.Limit,
            getWorkflowsInput.SearchName,
            getWorkflowsInput.WorkflowFilters);

        return new GetWorkflowsOutput(
            workflows
                .Select(
                    w => new WorkflowListDto(w))
                .ToList(),
            nextContinuationToken);
    }
}