﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Events;

public class OnVtexOrderForFlowHubEnrollmentFetchedEvent
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("vtex_authentication_id")]
    public string VtexAuthenticationId { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("flow_hub_workflow_id")]
    public string FlowHubWorkflowId { get; set; }

    [JsonProperty("flow_hub_workflow_versioned_id")]
    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnVtexOrderForFlowHubEnrollmentFetchedEvent(
        string sleekflowCompanyId,
        string vtexAuthenticationId,
        string orderId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        VtexAuthenticationId = vtexAuthenticationId;
        OrderId = orderId;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}