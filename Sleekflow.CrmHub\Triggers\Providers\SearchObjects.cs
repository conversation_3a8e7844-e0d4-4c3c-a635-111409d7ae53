﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class SearchObjects : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public SearchObjects(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class SearchObjectsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("conditions")]
        [ValidateArray]
        public List<SearchObjectCondition>? Conditions { get; set; }

        [JsonProperty("typed_ids")]
        [ValidateArray]
        public List<TypedId>? TypedIds { get; set; }

        [JsonConstructor]
        public SearchObjectsInput(
            string sleekflowCompanyId,
            string providerConnectionId,
            string providerName,
            string entityTypeName,
            List<SearchObjectCondition>? conditions,
            List<TypedId>? typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderConnectionId = providerConnectionId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
            TypedIds = typedIds;
        }
    }

    public class SearchObjectsOutput
    {
        [JsonProperty("records")]
        public List<Dictionary<string, object?>> Records { get; set; }

        [JsonConstructor]
        public SearchObjectsOutput(
            List<Dictionary<string, object?>> records)
        {
            Records = records;
        }
    }

    public async Task<SearchObjectsOutput> F(
        SearchObjectsInput searchObjectsInput)
    {
        var sleekflowCompanyId = searchObjectsInput.SleekflowCompanyId;
        var providerConnectionId = searchObjectsInput.ProviderConnectionId;
        var providerName = searchObjectsInput.ProviderName;
        var entityTypeName = searchObjectsInput.EntityTypeName;
        var conditions = searchObjectsInput.Conditions;
        var typedIds = searchObjectsInput.TypedIds;

        var providerService = _providerSelector.GetProviderService(providerName);

        var searchObjectsOutput = await providerService.SearchObjectsAsync(
            sleekflowCompanyId,
            providerConnectionId,
            entityTypeName,
            conditions,
            typedIds);

        return new SearchObjectsOutput(
            searchObjectsOutput.Objects);
    }
}