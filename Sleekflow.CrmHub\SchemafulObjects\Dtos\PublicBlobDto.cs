﻿using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.CrmHub.SchemafulObjects.Dtos;

[SwaggerInclude]
public class PublicBlobDto
{
    [JsonProperty("blob_type")]
    public string BlobType { get; set; }

    [Json<PERSON>roperty("blob_name")]
    public string BlobName { get; set; }

    [JsonProperty("blob_id")]
    public string? BlobId { get; set; }

    [JsonProperty("file_name")]
    public string? FileName { get; set; }

    [JsonProperty("content_type")]
    public string? ContentType { get; set; }

    [JsonConstructor]
    public PublicBlobDto(
        string blobType,
        string blobName,
        string? blobId,
        string? fileName,
        string? contentType)
    {
        BlobType = blobType;
        BlobName = blobName;
        BlobId = blobId;
        FileName = fileName;
        ContentType = contentType;
    }
}