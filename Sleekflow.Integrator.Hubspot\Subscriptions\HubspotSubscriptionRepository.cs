﻿using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Subscriptions;

public interface IHubspotSubscriptionRepository : IRepository<HubspotSubscription>
{
}

public class HubspotSubscriptionRepository
    : BaseRepository<HubspotSubscription>,
        IHubspotSubscriptionRepository,
        ISingletonService
{
    public HubspotSubscriptionRepository(
        ILogger<BaseRepository<HubspotSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}