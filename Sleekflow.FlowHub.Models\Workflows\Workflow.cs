using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.Workflows;

[ContainerId(ContainerNames.Workflow)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class Workflow : AuditEntity, IHasMetadata, IHasETag
{
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("workflow_type")]
    public string WorkflowType { get; set; }

    [JsonProperty("workflow_group_id")]
    public string? WorkflowGroupId { get; set; }

    [JsonProperty("triggers")]
    public WorkflowTriggers Triggers { get; set; }

    [JsonProperty("workflow_enrollment_settings")]
    public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

    [JsonProperty("workflow_schedule_settings")]
    public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

    [JsonProperty("steps")]
    public List<Step> Steps { get; set; }

    [JsonProperty("activation_status")]
    public string ActivationStatus { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonProperty("version")]
    public string Version { get; set; }

    [JsonProperty("dependency_workflow_id")]
    public string? DependencyWorkflowId { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty("manual_enrollment_source")]
    public string? ManualEnrollmentSource { get; set; }
    [JsonProperty("sub_workflow_type")]
    public string? SubWorkflowType { get; set; }

    [JsonConstructor]
    public Workflow(
        string workflowId,
        string workflowVersionedId,
        string name,
        string? workflowType,
        string? workflowGroupId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings? workflowEnrollmentSettings,
        WorkflowScheduleSettings? workflowScheduleSettings,
        List<Step> steps,
        string activationStatus,
        string id,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        Dictionary<string, object?> metadata,
        string? version,
        string? dependencyWorkflowId = null,
        string? eTag = null,
        string? manualEnrollmentSource = null,
        string? subWorkflowType = null)
        : base(id, SysTypeNames.Workflow, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        Name = name;
        WorkflowType = workflowType ?? Constants.WorkflowType.Normal;
        WorkflowGroupId = workflowGroupId;
        Triggers = triggers;
        WorkflowEnrollmentSettings = workflowEnrollmentSettings ?? WorkflowEnrollmentSettings.Default();
        WorkflowScheduleSettings = workflowScheduleSettings ?? WorkflowScheduleSettings.Default();
        Steps = steps;
        ActivationStatus = activationStatus;
        Metadata = metadata;
        Version = version ?? "v1";
        DependencyWorkflowId = dependencyWorkflowId;
        ETag = eTag;
        ManualEnrollmentSource = manualEnrollmentSource;
        SubWorkflowType = subWorkflowType;
    }

    public bool ShouldSerializeSteps()
        => false;

    public bool ShouldSerializeMetadata()
        => false;
}