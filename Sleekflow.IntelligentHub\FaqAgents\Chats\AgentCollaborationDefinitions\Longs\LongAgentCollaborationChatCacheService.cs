using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

public interface ILongAgentCollaborationChatCacheService
{
    public Task<string> GetCustomerInquiryAsync(string groupChatIdStr);

    public Task<string> GetStrategyAsync(string groupChatIdStr);

    public Task<List<string>> GetAllConfirmedKnowledgesAsync(string groupChatIdStr);

    public Task<string?> GetLatestProposedReplyToCustomerAsync(string groupChatIdStr);

    public Task SetCustomerInquiryAsync(string groupChatIdStr, string customerInquiry);

    public Task SetStrategyAsync(string groupChatIdStr, string strategy);

    public Task AppendConfirmedKnowledgeAsync(string groupChatIdStr, string confirmedKnowledge);

    public Task AppendProposedReplyToCustomerAsync(string groupChatIdStr, string proposedReplyToCustomer);
}

public class LongAgentCollaborationChatCacheService : ILongAgentCollaborationChatCacheService, IScopedService
{
    private readonly ILogger<LongAgentCollaborationChatCacheService> _logger;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;

    private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(30);

    public LongAgentCollaborationChatCacheService(
        ILogger<LongAgentCollaborationChatCacheService> logger,
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig)
    {
        _logger = logger;
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
    }

    public Task<string> GetCustomerInquiryAsync(string groupChatIdStr)
    {
        var database = _connectionMultiplexer.GetDatabase();

        return database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    return groupChatCacheRecord!.CustomerInquiryRecord;
                }

                return string.Empty;
            });
    }

    public Task<string> GetStrategyAsync(string groupChatIdStr)
    {
        var database = _connectionMultiplexer.GetDatabase();

        return database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    return groupChatCacheRecord!.StrategyRecord;
                }

                return string.Empty;
            });
    }

    public Task<List<string>> GetAllConfirmedKnowledgesAsync(string groupChatIdStr)
    {
        var database = _connectionMultiplexer.GetDatabase();

        return database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    return groupChatCacheRecord!.AllConfirmedKnowledgeRecords.ToList();
                }

                return new List<string>();
            });
    }

    public Task<string?> GetLatestProposedReplyToCustomerAsync(string groupChatIdStr)
    {
        var database = _connectionMultiplexer.GetDatabase();

        return database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    return groupChatCacheRecord!.AllProposedReplyToCustomerRecords.LastOrDefault();
                }

                return string.Empty;
            });
    }

    public async Task SetCustomerInquiryAsync(string groupChatIdStr, string customerInquiry)
    {
        if (string.IsNullOrWhiteSpace(customerInquiry))
        {
            // Some cases the customer inquiry is empty, we don't need to cache it
            return;
        }

        var database = _connectionMultiplexer.GetDatabase();

        await database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    groupChatCacheRecord!.CustomerInquiryRecord = customerInquiry;

                    return database.StringSetAsync(
                        GetCacheKey(groupChatIdStr),
                        JsonConvert.SerializeObject(groupChatCacheRecord, JsonConfig.DefaultJsonSerializerSettings),
                        CacheExpiration);
                }

                return database.StringSetAsync(
                    GetCacheKey(groupChatIdStr),
                    JsonConvert.SerializeObject(
                        new LongAgentCollaborationChatCacheRecord(
                            groupChatIdStr,
                            customerInquiry,
                            string.Empty,
                            [],
                            []),
                        JsonConfig.DefaultJsonSerializerSettings),
                    CacheExpiration);
            });
    }

    public async Task SetStrategyAsync(string groupChatIdStr, string strategy)
    {
        if (string.IsNullOrWhiteSpace(strategy))
        {
            // Some cases the strategy is empty, we don't need to cache it
            return;
        }

        var database = _connectionMultiplexer.GetDatabase();

        await database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    groupChatCacheRecord!.StrategyRecord = strategy;

                    database.StringSetAsync(
                        GetCacheKey(groupChatIdStr),
                        JsonConvert.SerializeObject(groupChatCacheRecord, JsonConfig.DefaultJsonSerializerSettings),
                        CacheExpiration);
                }
                else
                {
                    throw new InvalidOperationException("Group chat cache record not found");
                }
            });
    }

    public async Task AppendConfirmedKnowledgeAsync(string groupChatIdStr, string confirmedKnowledge)
    {
        if (string.IsNullOrWhiteSpace(confirmedKnowledge))
        {
            // Some cases the confirmed knowledge is empty, we don't need to cache it
            return;
        }

        var database = _connectionMultiplexer.GetDatabase();

        await database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    var allConfirmedKnowledgeRecords =
                        groupChatCacheRecord!.AllConfirmedKnowledgeRecords.ToList();
                    allConfirmedKnowledgeRecords.Add(confirmedKnowledge);

                    groupChatCacheRecord.AllConfirmedKnowledgeRecords =
                        allConfirmedKnowledgeRecords.ToArray();

                    database.StringSetAsync(
                        GetCacheKey(groupChatIdStr),
                        JsonConvert.SerializeObject(groupChatCacheRecord, JsonConfig.DefaultJsonSerializerSettings),
                        CacheExpiration);
                }

                // Skip the case that the group chat cache record not found
            });
    }

    public async Task AppendProposedReplyToCustomerAsync(string groupChatIdStr, string proposedReplyToCustomer)
    {
        if (string.IsNullOrWhiteSpace(proposedReplyToCustomer))
        {
            // Some cases the proposed reply to customer is empty, we don't need to cache it
            return;
        }

        var database = _connectionMultiplexer.GetDatabase();

        await database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord = JsonConvert.DeserializeObject<LongAgentCollaborationChatCacheRecord>(
                        cachedValue!,
                        JsonConfig.DefaultJsonSerializerSettings);

                    var allProposedReplyToCustomerRecords =
                        groupChatCacheRecord!.AllProposedReplyToCustomerRecords.ToList();
                    allProposedReplyToCustomerRecords.Add(proposedReplyToCustomer);

                    groupChatCacheRecord.AllProposedReplyToCustomerRecords =
                        allProposedReplyToCustomerRecords.ToArray();

                    database.StringSetAsync(
                        GetCacheKey(groupChatIdStr),
                        JsonConvert.SerializeObject(groupChatCacheRecord, JsonConfig.DefaultJsonSerializerSettings),
                        CacheExpiration);
                }
                else
                {
                    throw new InvalidOperationException("Group chat cache record not found");
                }
            });
    }

    private string GetCacheKey(string groupChatIdStr)
    {
        return $"{_cacheConfig.CachePrefix}:group-chat:{groupChatIdStr}";
    }
}