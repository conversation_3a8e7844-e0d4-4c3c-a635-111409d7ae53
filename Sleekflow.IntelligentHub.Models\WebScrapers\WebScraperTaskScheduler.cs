﻿using System.ComponentModel;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers;

public interface IWebScraperTaskScheduler
{
    public string Name { get; set; }

    public string CronExpression { get; set; }

    public bool IsEnabled { get; set; }
}

public class WebScraperTaskScheduler : IWebScraperTaskScheduler
{
    public const string PropertyNameWebScraperTaskScheduler = "web_scraper_task_scheduler";
    public const string PropertyNameCronExpression = "cron_expression";
    public const string PropertyNameIsEnabled = "is_enabled";

    [JsonProperty(PropertyName = "name")]
    [Description("WebScraperCode + TaskId. Mapping to the name of Apify Schedule")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "apify_scheduler_id")]
    [Description("Schedule Id, generated by Apify")]
    public string ApifySchedulerId { get; set; }

    [JsonProperty(PropertyName = PropertyNameCronExpression)]
    public string CronExpression { get; set; }

    [JsonProperty(PropertyName = PropertyNameIsEnabled)]
    public bool IsEnabled { get; set; }

    [JsonConstructor]
    public WebScraperTaskScheduler(
        string name,
        string apifySchedulerId,
        string cronExpression,
        bool isEnabled)
    {
        Name = name;
        ApifySchedulerId = apifySchedulerId;
        CronExpression = cronExpression;
        IsEnabled = isEnabled;
    }
}