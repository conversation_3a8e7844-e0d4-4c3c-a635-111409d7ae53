using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

public class WabaPhoneNumberDto : IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string? Id { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string? SleekflowCompanyId { get; set; }

    [JsonProperty("facebook_phone_number_id")]
    public string FacebookPhoneNumberId { get; set; }

    [JsonProperty("facebook_phone_number")]
    public string? FacebookPhoneNumber { get; set; }

    [Json<PERSON>roperty("facebook_phone_number_verified_name")]
    public string? FacebookPhoneNumberVerifiedName { get; set; }

    [JsonProperty("webhook_url")]
    public string? WebhookUrl { get; set; }

    [JsonProperty("facebook_phone_number_quality_rating")]
    public string? FacebookPhoneNumberQualityRating { get; set; }

    [JsonProperty("facebook_phone_number_name_status")]
    public string? FacebookPhoneNumberNameStatus { get; set; }

    [JsonProperty("facebook_phone_number_new_name_status")]
    public string? FacebookPhoneNumberNewNameStatus { get; set; }

    [JsonProperty("facebook_phone_number_account_mode")]
    public string? FacebookPhoneNumberAccountMode { get; set; }

    [JsonProperty("facebook_phone_number_code_verification_status")]
    public string? FacebookPhoneNumberCodeVerificationStatus { get; set; }

    [JsonProperty("facebook_phone_number_is_pin_enabled")]
    public bool? FacebookPhoneNumberIsPinEnabled { get; set; }

    [JsonProperty("facebook_phone_number_quality_score")]
    public WabaDtoPhoneNumberQualityScore? FacebookPhoneNumberQualityScore { get; set; }

    [JsonProperty("facebook_phone_number_status")]
    public string? FacebookPhoneNumberStatus { get; set; }

    [JsonProperty("facebook_phone_number_is_official_business_account")]
    public string? FacebookPhoneNumberIsOfficialBusinessAccount { get; set; }

    [JsonProperty("facebook_phone_number_messaging_limit_tier")]
    public string? FacebookPhoneNumberMessagingLimitTier { get; set; }

    [JsonProperty("facebook_is_catalog_visible")]
    public bool? FacebookIsCatalogVisible { get; set; }

    [JsonProperty("facebook_is_cart_enabled")]
    public bool? FacebookIsCartEnabled { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaPhoneNumberDto(
        string? id,
        string? sleekflowCompanyId,
        string facebookPhoneNumberId,
        string? facebookPhoneNumber,
        string? facebookPhoneNumberVerifiedName,
        string? webhookUrl,
        string? facebookPhoneNumberQualityRating,
        string? facebookPhoneNumberNameStatus,
        string? facebookPhoneNumberNewNameStatus,
        string? facebookPhoneNumberAccountMode,
        string? facebookPhoneNumberCodeVerificationStatus,
        bool? facebookPhoneNumberIsPinEnabled,
        WabaDtoPhoneNumberQualityScore? facebookPhoneNumberQualityScore,
        string? facebookPhoneNumberStatus,
        string? facebookPhoneNumberIsOfficialBusinessAccount,
        string? facebookPhoneNumberMessagingLimitTier,
        bool? facebookIsCatalogVisible,
        bool? facebookIsCartEnabled,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        FacebookPhoneNumberId = facebookPhoneNumberId;
        FacebookPhoneNumber = facebookPhoneNumber;
        FacebookPhoneNumberVerifiedName = facebookPhoneNumberVerifiedName;
        WebhookUrl = webhookUrl;
        FacebookPhoneNumberQualityRating = facebookPhoneNumberQualityRating;
        FacebookPhoneNumberNameStatus = facebookPhoneNumberNameStatus;
        FacebookPhoneNumberNewNameStatus = facebookPhoneNumberNewNameStatus;
        FacebookPhoneNumberAccountMode = facebookPhoneNumberAccountMode;
        FacebookPhoneNumberCodeVerificationStatus = facebookPhoneNumberCodeVerificationStatus;
        FacebookPhoneNumberIsPinEnabled = facebookPhoneNumberIsPinEnabled;
        FacebookPhoneNumberQualityScore = facebookPhoneNumberQualityScore;
        FacebookPhoneNumberStatus = facebookPhoneNumberStatus;
        FacebookPhoneNumberIsOfficialBusinessAccount = facebookPhoneNumberIsOfficialBusinessAccount;
        FacebookPhoneNumberMessagingLimitTier = facebookPhoneNumberMessagingLimitTier;
        FacebookIsCatalogVisible = facebookIsCatalogVisible;
        FacebookIsCartEnabled = facebookIsCartEnabled;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public WabaPhoneNumberDto(
        WabaPhoneNumber wabaPhoneNumber)
        : this(
            wabaPhoneNumber.Id,
            wabaPhoneNumber.SleekflowCompanyId,
            wabaPhoneNumber.FacebookPhoneNumberId,
            wabaPhoneNumber.FacebookPhoneNumberDetail.DisplayPhoneNumber,
            wabaPhoneNumber.FacebookPhoneNumberDetail.VerifiedName,
            wabaPhoneNumber.WebhookUrl,
            wabaPhoneNumber.FacebookPhoneNumberDetail.QualityRating,
            wabaPhoneNumber.FacebookPhoneNumberDetail.NameStatus,
            wabaPhoneNumber.FacebookPhoneNumberDetail?.NewNameStatus,
            wabaPhoneNumber.FacebookPhoneNumberDetail?.AccountMode,
            wabaPhoneNumber.FacebookPhoneNumberDetail?.CodeVerificationStatus,
            wabaPhoneNumber.FacebookPhoneNumberDetail?.IsPinEnabled,
            JsonConvert.DeserializeObject<WabaDtoPhoneNumberQualityScore>(
                JsonConvert.SerializeObject(
                    wabaPhoneNumber.FacebookPhoneNumberDetail?.QualityScore)),
            wabaPhoneNumber.FacebookPhoneNumberDetail?.Status,
            wabaPhoneNumber.FacebookPhoneNumberDetail?.IsOfficialBusinessAccount?.ToString(),
            wabaPhoneNumber.FacebookPhoneNumberDetail?.MessagingLimitTier,
            wabaPhoneNumber.WhatsappCommerceSetting?.IsCatalogVisible,
            wabaPhoneNumber.WhatsappCommerceSetting?.IsCartEnabled,
            wabaPhoneNumber.CreatedAt,
            wabaPhoneNumber.UpdatedAt)
    {
    }

    public class WabaDtoPhoneNumberQualityScore
    {
        [JsonProperty("reasons")]
        public List<string>? Reasons { get; set; }

        [JsonProperty("score")]
        public string? Score { get; set; }

        [JsonProperty("date")]
        public string? Date { get; set; }

        [JsonConstructor]
        public WabaDtoPhoneNumberQualityScore(
            List<string>? reasons,
            string? score,
            string? date)
        {
            Reasons = reasons;
            Score = score;
            Date = date;
        }
    }
}