﻿using System.Net;
using System.Text;
using Newtonsoft.Json.Linq;
using Polly;
using Sleekflow.Exceptions;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.Workers;

public interface IWorkerService
{
    Task<(HttpResponseMessage ResMsg, string ResStr, Output<T?> Output)> PostAsync<T>(
        HttpClient httpClient,
        string inputJsonStr,
        string uriStr);
}

public class WorkerService : IWorkerService
{
    private readonly IWorkerConfig _workerConfig;

    private static readonly IAsyncPolicy<HttpResponseMessage> HttpRetryPolicy
        = Policy<HttpResponseMessage>
            .HandleResult(
                res =>
                    res.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: currentRetryCount => TimeSpan.FromSeconds(5 * currentRetryCount));

    public WorkerService(
        IWorkerConfig workerConfig)
    {
        _workerConfig = workerConfig;
    }

    public async Task<(HttpResponseMessage ResMsg, string ResStr, Output<T?> Output)> PostAsync<T>(
        HttpClient httpClient,
        string inputJsonStr,
        string uriStr)
    {
        var resMsg = await HttpRetryPolicy.ExecuteAsync(() =>
        {
            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
                RequestUri = new Uri(uriStr),
                Headers =
                {
                    {
                        "X-Functions-Key", _workerConfig.WorkerFunctionsKey
                    }
                }
            };

            return httpClient.SendAsync(requestMessage);
        });

        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<JObject>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.ContainsKey("success") == false || output.GetValue("success")!.Value<bool>() == false)
        {
            throw new ErrorCodeException(output.ToObject<Output<dynamic>>());
        }

        try
        {
            return (resMsg, resStr, output.ToObject<Output<T?>>()!);
        }
        catch (Exception e)
        {
            throw new SfInternalErrorException(
                e,
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }
    }
}