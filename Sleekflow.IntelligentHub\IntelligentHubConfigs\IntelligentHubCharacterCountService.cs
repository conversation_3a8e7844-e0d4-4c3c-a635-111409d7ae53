using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubCharacterCountService
{
    Task<int> GetCharacterCountForAgent(string companyId, string agentId);

    Task<int> GetCharacterCountLimit(string companyId);
}

public class IntelligentHubCharacterCountService : IIntelligentHubCharacterCountService, IScopedService
{
    private readonly IKbDocumentRepository _kbDocumentRepository;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly ILogger<IntelligentHubCharacterCountService> _logger;

    public IntelligentHubCharacterCountService(
        IKbDocumentRepository kbDocumentRepository,
        IIntelligentHubConfigService intelligentHubConfigService,
        ILogger<IntelligentHubCharacterCountService> logger)
    {
        _kbDocumentRepository = kbDocumentRepository;
        _intelligentHubConfigService = intelligentHubConfigService;
        _logger = logger;
    }

    public async Task<int> GetCharacterCountForAgent(string companyId, string agentId)
    {
        _logger.LogInformation("Getting character count for agent {AgentId}", agentId);

        var documents = await _kbDocumentRepository.GetObjectsAsync(
            d =>
                d.SleekflowCompanyId == companyId &&
                d.AgentAssignments != null &&
                d.AgentAssignments.Any(a => a.AgentId == agentId));

        var characterCount = documents
            .Where(
                document => document.FileDocumentProcessStatus != ProcessFileDocumentStatuses.Failed &&
                            document.FileDocumentProcessStatus != ProcessFileDocumentStatuses.FailedToConvert)
            .Sum(document => document.GetCharacterCount());

        _logger.LogInformation(
            "Found {CharacterCount} characters for agent {AgentId} across {DocumentCount} documents",
            characterCount,
            agentId,
            documents.Count);

        return characterCount;
    }

    public async Task<int> GetCharacterCountLimit(string companyId)
    {
        _logger.LogInformation("Getting character count limit for company {CompanyId}", companyId);

        var config = await _intelligentHubConfigService.GetIntelligentHubConfigAsync(companyId);

        if (config == null)
        {
            throw new Exception($"No IntelligentHub config found for company {companyId}");
        }

        var limit = config.UsageLimits.TryGetValue(
            PriceableFeatures.AiAgentsKnowledgeBaseTotalCharacterCount,
            out var limitValue)
            ? limitValue
            : 0;

        _logger.LogInformation("Character count limit for company {CompanyId} is {Limit}", companyId, limit);
        return limit;
    }
}