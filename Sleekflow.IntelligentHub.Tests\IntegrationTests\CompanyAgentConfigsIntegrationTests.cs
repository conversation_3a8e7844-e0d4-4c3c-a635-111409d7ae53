﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;
using Sleekflow.Models.Constants;
using Sleekflow.Outputs;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class CompanyAgentConfigsIntegrationTests
{
    [Test]
    public async Task CompanyAgentConfigsTest()
    {
        var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

        // Create test data for new fields
        var leadNurturingTools = new LeadNurturingTools(
            leadScoreCustomObjectTool: null,
            assignmentTool: new AssignmentTool(
                new List<AssignmentPair>
                {
                    new AssignmentPair("test_rule", "test_team", "team_123")
                }),
            demoTool: null,
            additionalHandsOffClassificationRules: null);

        var toolsConfig = new ToolsConfig(
            sleekflowSendMessageTool: null,
            hubspotTool: new HubspotTool("test_api_key"));

        var enricherConfigs = new List<EnricherConfig>
        {
            new EnricherConfig
            {
                Type = "contact_properties", IsEnabled = true
            },
            new EnricherConfig
            {
                Type = "company_data", IsEnabled = false
            }
        };

        var knowledgeRetrievalConfig = new KnowledgeRetrievalConfig(
            webSearch: new WebSearchConfig(
                new List<TriggerSiteMapping>
                {
                    new TriggerSiteMapping("pricing", "site:example.com/pricing", "pricing.*")
                }),
            staticSearch: new StaticSearchConfig(
                new List<StaticPageEntry>
                {
                    new StaticPageEntry
                    {
                        Page = "Product Features",
                        Url = "https://example.com/features",
                        Description = "Comprehensive overview of our product features and capabilities"
                    }
                }));

        // /CompanyAgentConfigs/CreateCompanyAgentConfig
        var createCompanyAgentConfigInput =
            new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
                "TestCompanyAgentConfig",
                CompanyAgentTypes.Sales,
                true,
                true,
                10,
                null,
                null,
                AgentCollaborationModes.Default,
                leadNurturingTools,
                toolsConfig,
                enricherConfigs,
                knowledgeRetrievalConfig,
                "Simple config description");

        var createCompanyAgentConfigScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(createCompanyAgentConfigInput)
                .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
        });

        var createCompanyAgentConfigOutput =
            await createCompanyAgentConfigScenarioResult.ReadAsJsonAsync<
                Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

        Assert.That(createCompanyAgentConfigOutput, Is.Not.Null);
        Assert.That(createCompanyAgentConfigOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(createCompanyAgentConfigOutput.Data.CompanyAgentConfig.Name, Is.EqualTo("TestCompanyAgentConfig"));
        Assert.That(createCompanyAgentConfigOutput.Data.CompanyAgentConfig.Type, Is.EqualTo(CompanyAgentTypes.Sales));
        Assert.That(
            createCompanyAgentConfigOutput.Data.CompanyAgentConfig
                .NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            Is.EqualTo(10));
        Assert.That(
            createCompanyAgentConfigOutput.Data.CompanyAgentConfig.Description,
            Is.EqualTo("Simple config description"));

        // /CompanyAgentConfigs/GetCompanyAgentConfig
        var getCompanyAgentConfigInput =
            new GetCompanyAgentConfig.GetCompanyAgentConfigInput(
                createCompanyAgentConfigOutput.Data.CompanyAgentConfig.Id);

        var getCompanyAgentConfigScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getCompanyAgentConfigInput)
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfig");
        });

        var getCompanyAgentConfigOutput =
            await getCompanyAgentConfigScenarioResult.ReadAsJsonAsync<
                Output<GetCompanyAgentConfig.GetCompanyAgentConfigOutput>>();

        Assert.That(getCompanyAgentConfigOutput, Is.Not.Null);
        Assert.That(getCompanyAgentConfigOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyAgentConfigOutput.Data.CompanyAgentConfig.Name, Is.EqualTo("TestCompanyAgentConfig"));
        Assert.That(getCompanyAgentConfigOutput.Data.CompanyAgentConfig.Type, Is.EqualTo(CompanyAgentTypes.Sales));
        Assert.That(
            getCompanyAgentConfigOutput.Data.CompanyAgentConfig
                .NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            Is.EqualTo(10));
        Assert.That(
            getCompanyAgentConfigOutput.Data.CompanyAgentConfig.Description,
            Is.EqualTo("Simple config description"));

        // /CompanyAgentConfigs/GetCompanyAgentConfigs
        var getCompanyAgentConfigsInput =
            new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();

        var getCompanyAgentConfigsScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getCompanyAgentConfigsInput)
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfigs");
        });

        var getCompanyAgentConfigsOutput =
            await getCompanyAgentConfigsScenarioResult.ReadAsJsonAsync<
                Output<GetCompanyAgentConfigs.GetCompanyAgentConfigsOutput>>();

        Assert.That(getCompanyAgentConfigsOutput, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyAgentConfigsOutput!.Data.CompanyAgentConfigs, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput!.Data.CompanyAgentConfigs.Count, Is.EqualTo(1));

        var config = getCompanyAgentConfigsOutput!.Data.CompanyAgentConfigs.Single();
        Assert.That(config.SleekflowCompanyId, Is.EqualTo(mockCompanyId));
        Assert.That(config.IsChatHistoryEnabledAsContext, Is.EqualTo(true));
        Assert.That(config.IsContactPropertiesEnabledAsContext, Is.EqualTo(true));
        Assert.That(config.NumberOfPreviousMessagesInChatHistoryAvailableAsContext, Is.EqualTo(10));

        // Verify new fields are properly stored
        Assert.That(config.LeadNurturingTools, Is.Not.Null);
        Assert.That(config.LeadNurturingTools!.AssignmentTool, Is.Not.Null);
        Assert.That(config.LeadNurturingTools!.AssignmentTool!.Assignments.Count, Is.EqualTo(1));
        Assert.That(config.LeadNurturingTools!.AssignmentTool!.Assignments[0].Rule, Is.EqualTo("test_rule"));
        Assert.That(config.LeadNurturingTools!.AssignmentTool!.Assignments[0].TeamName, Is.EqualTo("test_team"));

        Assert.That(config.ToolsConfig, Is.Not.Null);
        Assert.That(config.ToolsConfig!.HubspotTool, Is.Not.Null);
        Assert.That(config.ToolsConfig!.HubspotTool!.ApiKey, Is.EqualTo("test_api_key"));

        Assert.That(config.EnricherConfigs, Is.Not.Null);
        Assert.That(config.EnricherConfigs.Count, Is.EqualTo(2));
        Assert.That(config.EnricherConfigs.Any(e => e.Type == "contact_properties" && e.IsEnabled), Is.True);
        Assert.That(config.EnricherConfigs.Any(e => e.Type == "company_data" && !e.IsEnabled), Is.True);

        Assert.That(config.KnowledgeRetrievalConfig, Is.Not.Null);
        Assert.That(config.KnowledgeRetrievalConfig!.WebSearch, Is.Not.Null);
        Assert.That(config.KnowledgeRetrievalConfig!.WebSearch!.TriggerSiteMappings.Count, Is.EqualTo(1));
        Assert.That(config.KnowledgeRetrievalConfig!.WebSearch!.TriggerSiteMappings[0].Trigger, Is.EqualTo("pricing"));

        // Create updated test data for patch operation
        var updatedLeadNurturingTools = new LeadNurturingTools(
            leadScoreCustomObjectTool: new LeadScoreCustomObjectTool(
                "test_schema",
                "class_field",
                "reason_field",
                "score_field"),
            assignmentTool: new AssignmentTool(
                new List<AssignmentPair>
                {
                    new AssignmentPair("updated_rule", "updated_team", "team_456")
                }),
            demoTool: new DemoTool(
                new List<RequiredField>
                {
                    new RequiredField("name", "Customer name", true)
                }),
            additionalHandsOffClassificationRules: null);

        var updatedToolsConfig = new ToolsConfig(
            sleekflowSendMessageTool: new SleekflowSendMessageTool("channel_id_expr", "channel_expr", null),
            hubspotTool: null);

        var updatedEnricherConfigs = new List<EnricherConfig>
        {
            new EnricherConfig
            {
                Type = "contact_properties", IsEnabled = false
            },
            new EnricherConfig
            {
                Type = "sales_data", IsEnabled = true
            }
        };

        var updatedKnowledgeRetrievalConfig = new KnowledgeRetrievalConfig(
            webSearch: new WebSearchConfig(
                new List<TriggerSiteMapping>
                {
                    new TriggerSiteMapping("support", "site:example.com/support", "support.*"),
                    new TriggerSiteMapping("docs", "site:example.com/docs", "documentation.*")
                }),
            staticSearch: new StaticSearchConfig());
        Assert.That(getCompanyAgentConfigsOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Count, Is.EqualTo(1));
        Assert.That(
            getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single().SleekflowCompanyId,
            Is.EqualTo(mockCompanyId));
        Assert.That(
            getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single().IsChatHistoryEnabledAsContext,
            Is.EqualTo(true));
        Assert.That(
            getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single().IsContactPropertiesEnabledAsContext,
            Is.EqualTo(true));
        Assert.That(
            getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single()
                .NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            Is.EqualTo(10));
        Assert.That(
            getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single().Description,
            Is.EqualTo("Simple config description"));
        Assert.That(
            getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single().Type,
            Is.EqualTo(CompanyAgentTypes.Sales));

        // Define sample ActionsDto for patch
        var actionsToPatch = new CompanyAgentConfigActionsDto(
            new SendMessageActionDto(true, "intelligent", "#Tone: Friendly"),
            new CalculateLeadScoreActionDto(
                true,
                new List<LeadScoreCriterionDto>
                {
                    new LeadScoreCriterionDto(50, "#Intent: High")
                }),
            new ExitConversationActionDto(
                true,
                new List<ExitConditionDto>
                {
                    new ExitConditionDto("High Lead Score", "custom", "Exit if score > 80")
                }),
            new AddLabelActionDto(
                true,
                new List<LabelDto>
                {
                    new LabelDto("test_label", "test_label_value", "test_label_type")
                },
                "test_instructions"
            )
        );

        // /CompanyAgentConfigs/PatchCompanyAgentConfig
        var patchCompanyAgentConfigInput =
            new PatchCompanyAgentConfig.PatchCompanyAgentConfigInput(
                config.Id,
                config.ETag!,
                "TestCompanyAgentConfig",
                null,
                new PromptInstructionDto(
                    string.Empty,
                    TargetToneTypes.Casual,
                    DiscloseLevels.Always,
                    ResponseLevels.Short,
                    RestrictivenessLevels.Normal,
                    "Hello",
                    null,
                    null,
                    null,
                    null,
                    null),
                false,
                false,
                20,
                null,
                null,
                null,
                getCompanyAgentConfigsOutput.Data.CompanyAgentConfigs.Single().Type,
                updatedLeadNurturingTools,
                updatedToolsConfig,
                updatedEnricherConfigs,
                updatedKnowledgeRetrievalConfig,
                actionsToPatch);

        var patchCompanyAgentConfigScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(patchCompanyAgentConfigInput)
                .ToUrl("/authorized/CompanyAgentConfigs/PatchCompanyAgentConfig");
        });

        var patchCompanyAgentConfigOutput =
            await patchCompanyAgentConfigScenarioResult.ReadAsJsonAsync<
                Output<PatchCompanyAgentConfig.PatchCompanyAgentConfigOutput>>();

        Assert.That(patchCompanyAgentConfigOutput, Is.Not.Null);
        Assert.That(patchCompanyAgentConfigOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.Actions, Is.Not.Null);
        Assert.That(patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.Actions?.SendMessage, Is.Not.Null);
        Assert.That(patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.Actions?.SendMessage?.Enabled, Is.True);
        Assert.That(
            patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.Actions?.SendMessage?.ResponseType,
            Is.EqualTo("intelligent"));
        Assert.That(patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.Actions?.CalculateLeadScore, Is.Not.Null);
        Assert.That(
            patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.Actions?.CalculateLeadScore?.Criteria.Count,
            Is.EqualTo(1));
        Assert.That(patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.PromptInstruction, Is.Not.Null);
        Assert.That(
            patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.PromptInstruction.Tone,
            Is.EqualTo(TargetToneTypes.Casual));
        Assert.That(
            patchCompanyAgentConfigOutput.Data.CompanyAgentConfig.PromptInstruction.GreetingMessage,
            Is.EqualTo("Hello"));

        // /CompanyAgentConfigs/GetCompanyAgentConfigs - Verify patch worked
        var getCompanyAgentConfigsScenarioResult2 = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getCompanyAgentConfigsInput)
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfigs");
        });

        var getCompanyAgentConfigsOutput2 =
            await getCompanyAgentConfigsScenarioResult2.ReadAsJsonAsync<
                Output<GetCompanyAgentConfigs.GetCompanyAgentConfigsOutput>>();

        Assert.That(getCompanyAgentConfigsOutput2, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyAgentConfigsOutput2!.Data.CompanyAgentConfigs, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput2!.Data.CompanyAgentConfigs.Count, Is.EqualTo(1));

        var updatedConfig = getCompanyAgentConfigsOutput2!.Data.CompanyAgentConfigs.Single();
        Assert.That(updatedConfig.SleekflowCompanyId, Is.EqualTo(mockCompanyId));
        Assert.That(updatedConfig.IsChatHistoryEnabledAsContext, Is.EqualTo(false));
        Assert.That(updatedConfig.IsContactPropertiesEnabledAsContext, Is.EqualTo(false));
        Assert.That(updatedConfig.NumberOfPreviousMessagesInChatHistoryAvailableAsContext, Is.EqualTo(20));

        // Verify updated new fields
        Assert.That(updatedConfig.LeadNurturingTools, Is.Not.Null);
        Assert.That(updatedConfig.LeadNurturingTools!.LeadScoreCustomObjectTool, Is.Not.Null);
        Assert.That(updatedConfig.LeadNurturingTools!.LeadScoreCustomObjectTool!.SchemaId, Is.EqualTo("test_schema"));
        Assert.That(updatedConfig.LeadNurturingTools!.AssignmentTool, Is.Not.Null);
        Assert.That(updatedConfig.LeadNurturingTools!.AssignmentTool!.Assignments.Count, Is.EqualTo(1));
        Assert.That(updatedConfig.LeadNurturingTools!.AssignmentTool!.Assignments[0].Rule, Is.EqualTo("updated_rule"));
        Assert.That(
            updatedConfig.LeadNurturingTools!.AssignmentTool!.Assignments[0].TeamName,
            Is.EqualTo("updated_team"));
        Assert.That(updatedConfig.LeadNurturingTools!.DemoTool, Is.Not.Null);
        Assert.That(updatedConfig.LeadNurturingTools!.DemoTool!.RequiredFields.Count, Is.EqualTo(1));
        Assert.That(updatedConfig.LeadNurturingTools!.DemoTool!.RequiredFields[0].Name, Is.EqualTo("name"));

        Assert.That(updatedConfig.ToolsConfig, Is.Not.Null);
        Assert.That(updatedConfig.ToolsConfig!.HubspotTool, Is.Null);
        Assert.That(updatedConfig.ToolsConfig!.SleekflowSendMessageTool, Is.Not.Null);
        Assert.That(
            updatedConfig.ToolsConfig!.SleekflowSendMessageTool!.ChannelIdentityIdExpr,
            Is.EqualTo("channel_id_expr"));

        Assert.That(updatedConfig.EnricherConfigs, Is.Not.Null);
        Assert.That(updatedConfig.EnricherConfigs.Count, Is.EqualTo(2));
        Assert.That(updatedConfig.EnricherConfigs.Any(e => e.Type == "contact_properties" && !e.IsEnabled), Is.True);
        Assert.That(updatedConfig.EnricherConfigs.Any(e => e.Type == "sales_data" && e.IsEnabled), Is.True);

        Assert.That(updatedConfig.KnowledgeRetrievalConfig, Is.Not.Null);
        Assert.That(updatedConfig.KnowledgeRetrievalConfig!.WebSearch, Is.Not.Null);
        Assert.That(updatedConfig.KnowledgeRetrievalConfig!.WebSearch!.TriggerSiteMappings.Count, Is.EqualTo(2));
        Assert.That(
            updatedConfig.KnowledgeRetrievalConfig!.WebSearch!.TriggerSiteMappings.Any(t => t.Trigger == "support"),
            Is.True);
        Assert.That(
            updatedConfig.KnowledgeRetrievalConfig!.WebSearch!.TriggerSiteMappings.Any(t => t.Trigger == "docs"),
            Is.True);
        Assert.That(getCompanyAgentConfigsOutput2.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Count, Is.EqualTo(1));
        Assert.That(
            getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().SleekflowCompanyId,
            Is.EqualTo(mockCompanyId));
        Assert.That(
            getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().IsChatHistoryEnabledAsContext,
            Is.EqualTo(false));
        Assert.That(
            getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().IsContactPropertiesEnabledAsContext,
            Is.EqualTo(false));
        Assert.That(
            getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single()
                .NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            Is.EqualTo(20));
        Assert.That(getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().Actions, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().Actions.SendMessage, Is.Not.Null);

        // /CompanyAgentConfigs/DeleteCompanyAgentConfig
        var deleteCompanyAgentConfigInput =
            new DeleteCompanyAgentConfig.DeleteCompanyAgentConfigInput(
                getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().Id,
                getCompanyAgentConfigsOutput2.Data.CompanyAgentConfigs.Single().ETag!);

        var deleteCompanyAgentConfigScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(deleteCompanyAgentConfigInput)
                .ToUrl("/authorized/CompanyAgentConfigs/DeleteCompanyAgentConfig");
        });

        var deleteCompanyAgentConfigOutput =
            await deleteCompanyAgentConfigScenarioResult.ReadAsJsonAsync<
                Output<DeleteCompanyAgentConfig.DeleteCompanyAgentConfigOutput>>();

        Assert.That(deleteCompanyAgentConfigOutput, Is.Not.Null);
        Assert.That(deleteCompanyAgentConfigOutput.HttpStatusCode, Is.EqualTo(200));

        // /CompanyAgentConfigs/GetCompanyAgentConfigs
        var getCompanyAgentConfigsScenarioResult4 = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getCompanyAgentConfigsInput)
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfigs");
        });

        var getCompanyAgentConfigsOutput4 =
            await getCompanyAgentConfigsScenarioResult4.ReadAsJsonAsync<
                Output<GetCompanyAgentConfigs.GetCompanyAgentConfigsOutput>>();

        Assert.That(getCompanyAgentConfigsOutput4, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput4.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyAgentConfigsOutput4.Data.CompanyAgentConfigs, Is.Not.Null);
        Assert.That(getCompanyAgentConfigsOutput4.Data.CompanyAgentConfigs.Count, Is.EqualTo(0));
    }

    [Test]
    public async Task CompanyAgentConfigs_CreateWithOptionalFields_Test()
    {
        var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
        using var scope = Application.Host.Services.CreateScope();
        var getInput =
            new GetCompanyAgentConfig.GetCompanyAgentConfigInput(string.Empty); // Placeholder, will be updated

        Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

        // Define sample ActionsDto and PromptInstructionDto for create
        var actionsToCreate = new CompanyAgentConfigActionsDto(
            new SendMessageActionDto(true, "canned", "Hello from canned response!"),
            new CalculateLeadScoreActionDto(
                false,
                new List<LeadScoreCriterionDto>()), // Corrected: empty list for criteria when disabled
            null, // No ExitConversationAction initially
            null // No AddLabelAction initially
        );

        var promptInstructionToCreate = new PromptInstructionDto(
            string.Empty,
            TargetToneTypes.Professional,
            DiscloseLevels.WhenAsked,
            ResponseLevels.Normal,
            RestrictivenessLevels.Relaxed,
            "Welcome!",
            "Core instruction for new agent.",
            "Strategy for new agent.",
            "Response style for new agent.",
            "Knowledge retrieval for new agent.",
            null);

        // /CompanyAgentConfigs/CreateCompanyAgentConfig with optional fields
        var createInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
            "TestConfigWithOptionalFields",
            CompanyAgentTypes.Sales,
            false,
            false,
            5,
            null,
            null,
            AgentCollaborationModes.Default,
            null,
            null,
            null,
            null,
            "Config created with actions and prompt instruction",
            actionsToCreate,
            promptInstructionToCreate
        );

        var createScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(createInput)
                .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
        });

        var createOutput = await createScenarioResult
            .ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

        Assert.That(createOutput, Is.Not.Null);
        Assert.That(createOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(createOutput.Data, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig.Name, Is.EqualTo("TestConfigWithOptionalFields"));
        Assert.That(createOutput.Data.CompanyAgentConfig.Type, Is.EqualTo(CompanyAgentTypes.Sales));
        Assert.That(
            createOutput.Data.CompanyAgentConfig.Description,
            Is.EqualTo("Config created with actions and prompt instruction"));
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.SendMessage, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.SendMessage.Enabled, Is.True);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.SendMessage.ResponseType, Is.EqualTo("canned"));
        Assert.That(
            createOutput.Data.CompanyAgentConfig.Actions.SendMessage.Instructions,
            Is.EqualTo("Hello from canned response!")); // Corrected from DefaultMessage
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.CalculateLeadScore, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.CalculateLeadScore.Enabled, Is.False);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.CalculateLeadScore.Criteria, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.CalculateLeadScore.Criteria.Count, Is.EqualTo(0));
        Assert.That(createOutput.Data.CompanyAgentConfig.PromptInstruction, Is.Not.Null);
        Assert.That(
            createOutput.Data.CompanyAgentConfig.PromptInstruction.Tone,
            Is.EqualTo(TargetToneTypes.Professional));
        Assert.That(createOutput.Data.CompanyAgentConfig.PromptInstruction.GreetingMessage, Is.EqualTo("Welcome!"));
        Assert.That(
            createOutput.Data.CompanyAgentConfig.PromptInstruction.AdditionalInstructionCore,
            Is.EqualTo("Core instruction for new agent."));

        // Update getInput for subsequent calls
        getInput = new GetCompanyAgentConfig.GetCompanyAgentConfigInput(createOutput.Data.CompanyAgentConfig.Id);

        // /CompanyAgentConfigs/GetCompanyAgentConfig
        var getScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getInput)
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfig");
        });
        var getOutput =
            await getScenarioResult.ReadAsJsonAsync<Output<GetCompanyAgentConfig.GetCompanyAgentConfigOutput>>();
        Assert.That(getOutput, Is.Not.Null);
        Assert.That(getOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            getOutput.Data.CompanyAgentConfig.Name,
            Is.EqualTo("TestConfigWithOptionalFields")); // Ensure all fields are present

        // /CompanyAgentConfigs/DeleteCompanyAgentConfig
        var deleteInput = new DeleteCompanyAgentConfig.DeleteCompanyAgentConfigInput(
            createOutput.Data.CompanyAgentConfig.Id,
            createOutput.Data.CompanyAgentConfig.ETag!
        );
        var deleteScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(deleteInput)
                .ToUrl("/authorized/CompanyAgentConfigs/DeleteCompanyAgentConfig");
        });
        var deleteOutput = await deleteScenarioResult
            .ReadAsJsonAsync<Output<DeleteCompanyAgentConfig.DeleteCompanyAgentConfigOutput>>();
        Assert.That(deleteOutput, Is.Not.Null);
        Assert.That(deleteOutput.HttpStatusCode, Is.EqualTo(200));

        // Verify deletion by trying to get it again
        var getAfterDeleteScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getInput) // getInput is defined earlier in this test method
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfig");
        });
        var getAfterDeleteOutput = await getAfterDeleteScenarioResult
            .ReadAsJsonAsync<Output<GetCompanyAgentConfig.GetCompanyAgentConfigOutput>>();
        Assert.That(getAfterDeleteOutput, Is.Not.Null);
        Assert.That(
            getAfterDeleteOutput.HttpStatusCode,
            Is.EqualTo(500));
    }

    [Test]
    public async Task CompanyAgentConfigs_ExitConditionsWithOperatorAndValues_Test()
    {
        var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

        // Create a config with exit conditions that have operator and values
        var createInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
            "TestConfigWithExitConditions",
            CompanyAgentTypes.Sales,
            false,
            false,
            5,
            null,
            null,
            AgentCollaborationModes.Default,
            null,
            null,
            null,
            null,
            "Config with exit conditions that have operator and values",
            new CompanyAgentConfigActionsDto(
                null,
                null,
                new ExitConversationActionDto(
                    true,
                    new List<ExitConditionDto>
                    {
                        new ExitConditionDto(
                            "Lead Score High",
                            "lead_score",
                            "Exit when lead score is high",
                            "larger_than",
                            new List<object>
                            {
                                80
                            }),
                        new ExitConditionDto(
                            "Lead Score Low",
                            "lead_score",
                            "Exit when lead score is low",
                            "smaller_than",
                            new List<object>
                            {
                                20
                            }),
                        new ExitConditionDto(
                            "Lead Score Range",
                            "lead_score",
                            "Exit when lead score is in range",
                            "is_between",
                            new List<object>
                            {
                                30, 50
                            }),
                        new ExitConditionDto(
                            "Low Confidence",
                            "low_confidence",
                            "Exit when confidence is low",
                            "smaller_than",
                            new List<object>
                            {
                                25
                            })
                    }),
                null
            ),
            null);

        var createScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(createInput)
                .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
        });

        var createOutput = await createScenarioResult
            .ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

        Assert.That(createOutput, Is.Not.Null);
        Assert.That(createOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions?.ExitConversation, Is.Not.Null);
        Assert.That(createOutput.Data.CompanyAgentConfig.Actions.ExitConversation.Conditions.Count, Is.EqualTo(4));

        // Verify the exit conditions with operator and values
        var conditions = createOutput.Data.CompanyAgentConfig.Actions.ExitConversation.Conditions;

        var leadScoreHighCondition = conditions.First(c => c.Title == "Lead Score High");
        Assert.That(leadScoreHighCondition.Type, Is.EqualTo("lead_score"));
        Assert.That(leadScoreHighCondition.Operator, Is.EqualTo("larger_than"));
        Assert.That(leadScoreHighCondition.Values, Is.Not.Null);
        Assert.That(leadScoreHighCondition.Values.Count, Is.EqualTo(1));
        Assert.That(leadScoreHighCondition.Values[0], Is.EqualTo(80));

        var leadScoreLowCondition = conditions.First(c => c.Title == "Lead Score Low");
        Assert.That(leadScoreLowCondition.Type, Is.EqualTo("lead_score"));
        Assert.That(leadScoreLowCondition.Operator, Is.EqualTo("smaller_than"));
        Assert.That(leadScoreLowCondition.Values, Is.Not.Null);
        Assert.That(leadScoreLowCondition.Values.Count, Is.EqualTo(1));
        Assert.That(leadScoreLowCondition.Values[0], Is.EqualTo(20));

        var leadScoreRangeCondition = conditions.First(c => c.Title == "Lead Score Range");
        Assert.That(leadScoreRangeCondition.Type, Is.EqualTo("lead_score"));
        Assert.That(leadScoreRangeCondition.Operator, Is.EqualTo("is_between"));
        Assert.That(leadScoreRangeCondition.Values, Is.Not.Null);
        Assert.That(leadScoreRangeCondition.Values.Count, Is.EqualTo(2));
        Assert.That(leadScoreRangeCondition.Values[0], Is.EqualTo(30));
        Assert.That(leadScoreRangeCondition.Values[1], Is.EqualTo(50));

        var lowConfidenceCondition = conditions.First(c => c.Title == "Low Confidence");
        Assert.That(lowConfidenceCondition.Type, Is.EqualTo("low_confidence"));
        Assert.That(lowConfidenceCondition.Operator, Is.EqualTo("smaller_than"));
        Assert.That(lowConfidenceCondition.Values, Is.Not.Null);
        Assert.That(lowConfidenceCondition.Values.Count, Is.EqualTo(1));
        Assert.That(lowConfidenceCondition.Values[0], Is.EqualTo(25));

        // Test GetCompanyAgentConfig to ensure the fields are persisted correctly
        var getInput = new GetCompanyAgentConfig.GetCompanyAgentConfigInput(createOutput.Data.CompanyAgentConfig.Id);
        var getScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(getInput)
                .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfig");
        });

        var getOutput =
            await getScenarioResult.ReadAsJsonAsync<Output<GetCompanyAgentConfig.GetCompanyAgentConfigOutput>>();

        Assert.That(getOutput, Is.Not.Null);
        Assert.That(getOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getOutput.Data.CompanyAgentConfig.Actions?.ExitConversation, Is.Not.Null);
        Assert.That(getOutput.Data.CompanyAgentConfig.Actions.ExitConversation.Conditions.Count, Is.EqualTo(4));

        // Verify the retrieved conditions still have the correct operator and values
        var retrievedConditions = getOutput.Data.CompanyAgentConfig.Actions.ExitConversation.Conditions;
        var retrievedHighCondition = retrievedConditions.First(c => c.Title == "Lead Score High");
        Assert.That(retrievedHighCondition.Operator, Is.EqualTo("larger_than"));
        Assert.That(retrievedHighCondition.Values, Is.Not.Null);
        Assert.That(retrievedHighCondition.Values[0], Is.EqualTo(80));

        // Test PatchCompanyAgentConfig to ensure the fields can be updated
        var patchInput = new PatchCompanyAgentConfig.PatchCompanyAgentConfigInput(
            createOutput.Data.CompanyAgentConfig.Id,
            createOutput.Data.CompanyAgentConfig.ETag!,
            "TestConfigWithExitConditions", // name parameter
            null, // description
            new PromptInstructionDto(
                string.Empty,
                TargetToneTypes.Professional,
                DiscloseLevels.Always,
                ResponseLevels.Normal,
                RestrictivenessLevels.Normal,
                "Hello there!",
                null,
                null,
                null,
                null,
                null), // promptInstruction - provide a valid PromptInstructionDto since it's required
            null, // isChatHistoryEnabledAsContext
            null, // isContactPropertiesEnabledAsContext
            null, // numberOfPreviousMessages
            null, // channelType
            null, // channelId
            null, // collaborationMode
            null, // type
            null, // leadNurturingTools
            null, // toolsConfig
            null, // enricherConfigs
            null, // knowledgeRetrievalConfig
            new CompanyAgentConfigActionsDto(
                null,
                null,
                new ExitConversationActionDto(
                    true,
                    new List<ExitConditionDto>
                    {
                        new ExitConditionDto(
                            "Updated Lead Score",
                            "lead_score",
                            "Updated exit condition",
                            "larger_than",
                            new List<object>
                            {
                                90
                            })
                    }),
                null
            ));

        var patchScenarioResult = await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(patchInput)
                .ToUrl("/authorized/CompanyAgentConfigs/PatchCompanyAgentConfig");
        });

        var patchOutput = await patchScenarioResult
            .ReadAsJsonAsync<Output<PatchCompanyAgentConfig.PatchCompanyAgentConfigOutput>>();

        Assert.That(patchOutput, Is.Not.Null);
        Assert.That(patchOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(patchOutput.Data.CompanyAgentConfig.Actions?.ExitConversation, Is.Not.Null);
        Assert.That(patchOutput.Data.CompanyAgentConfig.Actions.ExitConversation.Conditions.Count, Is.EqualTo(1));

        var updatedCondition = patchOutput.Data.CompanyAgentConfig.Actions.ExitConversation.Conditions.First();
        Assert.That(updatedCondition.Title, Is.EqualTo("Updated Lead Score"));
        Assert.That(updatedCondition.Type, Is.EqualTo("lead_score"));
        Assert.That(updatedCondition.Operator, Is.EqualTo("larger_than"));
        Assert.That(updatedCondition.Values, Is.Not.Null);
        Assert.That(updatedCondition.Values[0], Is.EqualTo(90));

        // Clean up
        var deleteInput = new DeleteCompanyAgentConfig.DeleteCompanyAgentConfigInput(
            patchOutput.Data.CompanyAgentConfig.Id,
            patchOutput.Data.CompanyAgentConfig.ETag!);

        await Application.Host.Scenario(_ =>
        {
            _.WithRequestHeader("X-Sleekflow-Record", "true");
            _.Post.Json(deleteInput)
                .ToUrl("/authorized/CompanyAgentConfigs/DeleteCompanyAgentConfig");
        });
    }

    [Test]
    public async Task CompanyAgentConfigs_DemoPropertyValidation_Test()
    {
        var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

        // Keep track of all successfully created configs for cleanup
        var createdConfigIds = new List<string>();

        try
        {
            // First, create 11 agent configs to exceed the limit
            for (int i = 1; i <= 11; i++)
            {
                var createInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
                    $"TestConfig{i}",
                    CompanyAgentTypes.Sales,
                    true,
                    true,
                    10,
                    null,
                    null,
                    AgentCollaborationModes.Default,
                    null,
                    null,
                    null,
                    null,
                    $"Test config {i}");

                var createResult = await Application.Host.Scenario(_ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createInput)
                        .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
                });

                var createOutput = await createResult.ReadAsJsonAsync<
                    Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

                Assert.That(createOutput, Is.Not.Null);
                Assert.That(createOutput.HttpStatusCode, Is.EqualTo(200));
                createdConfigIds.Add(createOutput.Data.CompanyAgentConfig.Id);
            }

            // Test 1: Attempt to create a 12th config without demo flag - should fail
            var regularConfigInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
                "RegularConfig12",
                CompanyAgentTypes.Sales,
                true,
                true,
                10,
                null,
                null,
                AgentCollaborationModes.Default,
                null,
                null,
                null,
                null,
                "Regular config that should fail");

            var regularConfigResult = await Application.Host.Scenario(_ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(regularConfigInput)
                    .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
            });

            var regularConfigOutput = await regularConfigResult.ReadAsJsonAsync<
                Output<object>>();

            Assert.That(regularConfigOutput, Is.Not.Null);
            Assert.That(regularConfigOutput.HttpStatusCode, Is.EqualTo(400));
            Assert.That(regularConfigOutput.Message, Is.EqualTo("The input is incorrect."));

            // Check that the validation error message contains the expected text
            var dataJson = JsonConvert.SerializeObject(regularConfigOutput.Data);
            Assert.That(dataJson, Does.Contain("Only ten company agent configs are allowed"));

            // Test 2: Attempt to create a 12th config with demo = false - should also fail
            var demoFalseConfigInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
                "DemoFalseConfig12",
                CompanyAgentTypes.Sales,
                true,
                true,
                10,
                null,
                null,
                AgentCollaborationModes.Default,
                null,
                null,
                null,
                null,
                "Demo false config that should fail",
                null,
                null,
                false);

            var demoFalseConfigResult = await Application.Host.Scenario(_ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(demoFalseConfigInput)
                    .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
            });

            var demoFalseConfigOutput = await demoFalseConfigResult.ReadAsJsonAsync<
                Output<object>>();

            Assert.That(demoFalseConfigOutput, Is.Not.Null);
            Assert.That(demoFalseConfigOutput.HttpStatusCode, Is.EqualTo(400));
            Assert.That(demoFalseConfigOutput.Message, Is.EqualTo("The input is incorrect."));

            // Check that the validation error message contains the expected text
            var demoFalseDataJson = JsonConvert.SerializeObject(demoFalseConfigOutput.Data);
            Assert.That(demoFalseDataJson, Does.Contain("Only ten company agent configs are allowed"));

            // Test 3: Create a 12th config with demo = true - should succeed
            var demoTrueConfigInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
                "DemoTrueConfig12",
                CompanyAgentTypes.Sales,
                true,
                true,
                10,
                null,
                null,
                AgentCollaborationModes.Default,
                null,
                null,
                null,
                null,
                "Demo true config that should succeed",
                null,
                null,
                true);

            var demoTrueConfigResult = await Application.Host.Scenario(_ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(demoTrueConfigInput)
                    .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
            });

            var demoTrueConfigOutput = await demoTrueConfigResult.ReadAsJsonAsync<
                Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

            Assert.That(demoTrueConfigOutput, Is.Not.Null);
            Assert.That(demoTrueConfigOutput.HttpStatusCode, Is.EqualTo(200));
            Assert.That(demoTrueConfigOutput.Data.CompanyAgentConfig.Name, Is.EqualTo("DemoTrueConfig12"));
            Assert.That(
                demoTrueConfigOutput.Data.CompanyAgentConfig.Description,
                Is.EqualTo("Demo true config that should succeed"));

            // Add the successfully created demo config to cleanup list
            createdConfigIds.Add(demoTrueConfigOutput.Data.CompanyAgentConfig.Id);

            // Test 4: Verify we can create multiple demo configs beyond the limit
            var demoTrueConfigInput2 = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
                "DemoTrueConfig13",
                CompanyAgentTypes.Sales,
                true,
                true,
                10,
                null,
                null,
                AgentCollaborationModes.Default,
                null,
                null,
                null,
                null,
                "Another demo config",
                null,
                null,
                true);

            var demoTrueConfigResult2 = await Application.Host.Scenario(_ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(demoTrueConfigInput2)
                    .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
            });

            var demoTrueConfigOutput2 = await demoTrueConfigResult2.ReadAsJsonAsync<
                Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

            Assert.That(demoTrueConfigOutput2, Is.Not.Null);
            Assert.That(demoTrueConfigOutput2.HttpStatusCode, Is.EqualTo(200));
            Assert.That(demoTrueConfigOutput2.Data.CompanyAgentConfig.Name, Is.EqualTo("DemoTrueConfig13"));

            // Add the successfully created demo config to cleanup list
            createdConfigIds.Add(demoTrueConfigOutput2.Data.CompanyAgentConfig.Id);

            // Verify all configs exist (11 regular + 2 demo = 13 total)
            var getConfigsInput = new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();
            var getConfigsResult = await Application.Host.Scenario(_ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getConfigsInput)
                    .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfigs");
            });

            var getConfigsOutput = await getConfigsResult.ReadAsJsonAsync<
                Output<GetCompanyAgentConfigs.GetCompanyAgentConfigsOutput>>();

            Assert.That(getConfigsOutput, Is.Not.Null);
            Assert.That(getConfigsOutput.HttpStatusCode, Is.EqualTo(200));
            Assert.That(getConfigsOutput.Data.CompanyAgentConfigs.Count, Is.EqualTo(13)); // 11 regular + 2 demo configs
        }
        finally
        {
            // Cleanup: Delete all created configs
            // First, get the current state of all configs to get their ETags
            var getConfigsForCleanupInput = new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();
            var getConfigsForCleanupResult = await Application.Host.Scenario(_ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getConfigsForCleanupInput)
                    .ToUrl("/authorized/CompanyAgentConfigs/GetCompanyAgentConfigs");
            });

            var getConfigsForCleanupOutput = await getConfigsForCleanupResult.ReadAsJsonAsync<
                Output<GetCompanyAgentConfigs.GetCompanyAgentConfigsOutput>>();

            if (getConfigsForCleanupOutput?.HttpStatusCode == 200 &&
                getConfigsForCleanupOutput.Data?.CompanyAgentConfigs != null)
            {
                // Delete each config that was created in this test
                foreach (var configId in createdConfigIds)
                {
                    var configToDelete = getConfigsForCleanupOutput.Data.CompanyAgentConfigs
                        .FirstOrDefault(c => c.Id == configId);

                    if (configToDelete?.ETag != null)
                    {
                        var deleteInput = new DeleteCompanyAgentConfig.DeleteCompanyAgentConfigInput(
                            configId,
                            configToDelete.ETag);

                        try
                        {
                            await Application.Host.Scenario(_ =>
                            {
                                _.WithRequestHeader("X-Sleekflow-Record", "true");
                                _.Post.Json(deleteInput)
                                    .ToUrl("/authorized/CompanyAgentConfigs/DeleteCompanyAgentConfig");
                            });
                        }
                        catch
                        {
                            // Ignore cleanup errors to not mask test failures
                        }
                    }
                }
            }
        }
    }
}