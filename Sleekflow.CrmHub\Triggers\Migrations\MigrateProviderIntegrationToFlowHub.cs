﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Migrations;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.CrmHubToFlowHubMigrations;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Migrations;

[TriggerGroup(TriggerGroups.Migrations)]
public class MigrateProviderIntegrationToFlowHub : ITrigger
{
    private static readonly List<string> EntityTypesToMigrate = new()
    {
        "Contact",
        "Lead",
        "Opportunity",
        "Campaign"
    };

    private static readonly List<string> EntityTypesToMigrateWithWorkflows = new()
    {
        "Contact",
    };

    private readonly ICrmHubToFlowHubMigrationService _crmHubToFlowHubMigrationService;
    private readonly IProviderConfigService _providerConfigService;
    private readonly IProviderSelector _providerSelector;
    private readonly IBus _bus;

    public MigrateProviderIntegrationToFlowHub(
        ICrmHubToFlowHubMigrationService crmHubToFlowHubMigrationService,
        IProviderConfigService providerConfigService,
        IProviderSelector providerSelector,
        IBus bus)
    {
        _crmHubToFlowHubMigrationService = crmHubToFlowHubMigrationService;
        _providerConfigService = providerConfigService;
        _providerSelector = providerSelector;
        _bus = bus;
    }

    public class MigrateProviderIntegrationToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("sleekflow_fields")]
        [Required]
        [ValidateArray]
        public List<string> SleekflowFields { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public MigrateProviderIntegrationToFlowHubInput(
            string sleekflowCompanyId,
            string providerName,
            List<string> sleekflowFields,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            SleekflowFields = sleekflowFields;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class MigrateProviderIntegrationToFlowHubOutput
    {
    }

    public async Task<MigrateProviderIntegrationToFlowHubOutput> F(
        MigrateProviderIntegrationToFlowHubInput migrationProviderIntegrationToFlowHubInput)
    {
        var sleekflowCompanyId = migrationProviderIntegrationToFlowHubInput.SleekflowCompanyId;
        var providerName = migrationProviderIntegrationToFlowHubInput.ProviderName;
        var sleekflowFields = migrationProviderIntegrationToFlowHubInput.SleekflowFields;
        var sleekflowStaffId = migrationProviderIntegrationToFlowHubInput.SleekflowStaffId;
        var sleekflowStaffTeamIds = migrationProviderIntegrationToFlowHubInput.SleekflowStaffTeamIds;

        var providerService = _providerSelector.GetProviderService(providerName);

        var prepareMigrationToFlowHubOutput = await providerService.PrepareMigrationToFlowHubAsync(
            sleekflowCompanyId,
            EntityTypesToMigrate);
        var providerConnectionId = prepareMigrationToFlowHubOutput.CreatedConnectionId;

        var providerConfig = await _providerConfigService.GetProviderConfigAsync(
            sleekflowCompanyId,
            providerName);

        var entityTypeNameToSyncConfigDict =
            providerConfig.EntityTypeNameToSyncConfigDict
                .Where(d => EntityTypesToMigrate.Contains(d.Key));

        var entityTypeNameToMigrateCrmHubSyncConfigDtoDict = new Dictionary<string, MigrateCrmHubSyncConfigDto>();
        var entityTypeNameToFieldMappingsDict = new Dictionary<string, List<SleekflowFieldToCrmProviderFieldMapping>>();

        foreach (var (entityTypeName, syncConfig) in entityTypeNameToSyncConfigDict)
        {
            if (EntityTypesToMigrateWithWorkflows.Contains(entityTypeName))
            {
                var migrateCrmHubSyncConfigFilterGroupDtos = syncConfig.FilterGroups
                    .Select(filterGroup => new MigrateCrmHubSyncConfigFilterGroupDto(
                        filterGroup.Filters.Select(filter => new MigrateCrmHubSyncConfigFilterDto(
                            filter.FieldName,
                            filter.Operator == null || filter.Operator == "=" ? "string" : "datetime",
                            filter.Value,
                            filter.Operator ?? "=")).ToList())).ToList();
                var migrateCrmHubSyncConfigDto = new MigrateCrmHubSyncConfigDto(
                    migrateCrmHubSyncConfigFilterGroupDtos,
                    syncConfig.Interval,
                    syncConfig.SyncMode);
                entityTypeNameToMigrateCrmHubSyncConfigDtoDict.Add(entityTypeName, migrateCrmHubSyncConfigDto);
            }

            var fieldMappings = (await _crmHubToFlowHubMigrationService.GetSleekflowFieldToProviderFieldMappings(
                    sleekflowCompanyId,
                    entityTypeName,
                    providerName))
                .Where(m => sleekflowFields.Contains(m.SleekflowField))
                .ToList();
            entityTypeNameToFieldMappingsDict.Add(entityTypeName, fieldMappings);
        }

        await _bus.Publish(
            new MigrateCrmHubIntegrationToFlowHubEventRequest(
                sleekflowCompanyId,
                providerName,
                providerConnectionId,
                entityTypeNameToMigrateCrmHubSyncConfigDtoDict,
                entityTypeNameToFieldMappingsDict,
                sleekflowStaffId,
                sleekflowStaffTeamIds),
            context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });

        return new MigrateProviderIntegrationToFlowHubOutput();
    }
}