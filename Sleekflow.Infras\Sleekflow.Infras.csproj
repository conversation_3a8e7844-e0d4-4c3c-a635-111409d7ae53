<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Exe</OutputType>
        <_EnableMacOSCodeSign>false</_EnableMacOSCodeSign>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Search.Documents" Version="11.6.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Pulumi" Version="3.70.0" />
        <PackageReference Include="Pulumi.Auth0" Version="3.5.0" />
        <PackageReference Include="Pulumi.Azure" Version="6.7.0" />
        <PackageReference Include="Pulumi.AzureNative" Version="2.74.0" />
        <PackageReference Include="Pulumi.Docker" Version="4.5.5" />
        <PackageReference Include="Pulumi.Random" Version="4.16.3" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Resources/*" CopyToOutputDirectory="PreserveNewest" />
        <Content Include="Resources\**\*">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <None Update="Resources\ic_state\get-id-stored-procedure.js">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Resources\upsert-metadata-stored-procedure.js">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.CommerceHub.Models\Sleekflow.CommerceHub.Models.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Resources\opa\data\" />
      <Folder Include="Resources\opa\policies\" />
    </ItemGroup>

</Project>
