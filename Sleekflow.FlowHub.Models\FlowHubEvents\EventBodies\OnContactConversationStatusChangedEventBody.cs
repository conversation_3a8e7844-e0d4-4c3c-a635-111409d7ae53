using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnContactConversationStatusChangedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactConversationStatusChanged; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [Required]
    [JsonProperty("original_status")]
    public string OriginalStatus { get; set; }

    [Required]
    [JsonProperty("new_status")]
    public string NewStatus { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [JsonConstructor]
    public OnContactConversationStatusChangedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        string originalStatus,
        string newStatus,
        string contactId,
        string conversationId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        OriginalStatus = originalStatus;
        NewStatus = newStatus;
        ContactId = contactId;
        ConversationId = conversationId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
    }
}