using Newtonsoft.Json;

namespace Sleekflow.Models.WhatsappCloudApi;

public class BusinessBalanceTransactionLog
{
    [JsonProperty("unique_id")]
    public string UniqueId { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("credit")]
    public Money Credit { get; set; }

    [JsonProperty("transaction_type")]
    public string TransactionType { get; set; }

    [JsonProperty("waba_top_up")]
    public WabaTopUpTransactionItem? WabaTopUp { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public BusinessBalanceTransactionLog(
        string uniqueId,
        string facebookBusinessId,
        Money credit,
        string transactionType,
        WabaTopUpTransactionItem? wabaTopUp,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    {
        UniqueId = uniqueId;
        FacebookBusinessId = facebookBusinessId;
        Credit = credit;
        TransactionType = transactionType;
        WabaTopUp = wabaTopUp;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}

public class WabaTopUpTransactionItem
{
    [JsonProperty("pay_amount")]
    public Money PayAmount { get; set; }

    [JsonProperty("payment_method")]
    public string PaymentMethod { get; set; }

    [JsonConstructor]
    public WabaTopUpTransactionItem(
        Money payAmount,
        string topUpPaymentMethod)
    {
        PayAmount = payAmount;
        PaymentMethod = topUpPaymentMethod;
    }
}

public class Money
{
    [JsonProperty("currency_iso_code")]
    public string CurrencyIsoCode { get; set; }

    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    [JsonConstructor]
    public Money(string currencyIsoCode, decimal amount)
    {
        CurrencyIsoCode = currencyIsoCode;
        Amount = amount;
    }
}