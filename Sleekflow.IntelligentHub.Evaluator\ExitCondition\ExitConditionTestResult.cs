using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator.ExitCondition;

public class ExitConditionTestResult(
    string methodName,
    ExitConditionResult? answer,
    long elapsedMilliseconds)
{
    public string MethodName { get; init; } = methodName;

    public ExitConditionResult? Answer { get; init; } = answer;

    public long ElapsedMilliseconds { get; init; } = elapsedMilliseconds;

}