using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetWorkflowExecutionStatistics : ITrigger
{
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public GetWorkflowExecutionStatistics(
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
    }

    public class GetWorkflowExecutionStatisticsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("filters")]
        [Required]
        [ValidateObject]
        public GetWorkflowExecutionStatisticsInputFilters Filters { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionStatisticsInput(
            string sleekflowCompanyId,
            GetWorkflowExecutionStatisticsInputFilters filters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Filters = filters;
        }
    }

    public class GetWorkflowExecutionStatisticsInputFilters : WorkflowExecutionStatisticsFilters
    {
        [JsonProperty("workflow_id")]
        public string? WorkflowId { get; set; }

        public GetWorkflowExecutionStatisticsInputFilters(
            DateTimeOffset? fromDateTime,
            DateTimeOffset? toDateTime,
            string? workflowType,
            string? workflowId)
            : base(fromDateTime, toDateTime, workflowType)
        {
            WorkflowId = workflowId;
        }
    }

    public class GetWorkflowExecutionStatisticsOutput : WorkflowStatistics
    {
        [JsonConstructor]
        public GetWorkflowExecutionStatisticsOutput(
            long numOfStartedWorkflows,
            long numOfCompleteWorkflows,
            long numOfCancelledWorkflows,
            long numOfBlockedWorkflows,
            long numOfFailedWorkflows,
            long numOfRestrictedWorkflows)
            : base(
                numOfStartedWorkflows,
                numOfCompleteWorkflows,
                numOfCancelledWorkflows,
                numOfBlockedWorkflows,
                numOfFailedWorkflows,
                numOfRestrictedWorkflows)
        {
        }
    }

    public async Task<GetWorkflowExecutionStatisticsOutput> F(
        GetWorkflowExecutionStatisticsInput getWorkflowExecutionStatisticsInput)
    {
        var workflowStatistics =
            await _workflowExecutionService.GetWorkflowExecutionStatisticsAsync(
                getWorkflowExecutionStatisticsInput.SleekflowCompanyId,
                getWorkflowExecutionStatisticsInput.Filters.WorkflowId,
                getWorkflowExecutionStatisticsInput.Filters);

        return new GetWorkflowExecutionStatisticsOutput(
            workflowStatistics.NumOfStartedWorkflows,
            workflowStatistics.NumOfCompleteWorkflows,
            workflowStatistics.NumOfCancelledWorkflows,
            workflowStatistics.NumOfBlockedWorkflows,
            workflowStatistics.NumOfFailedWorkflows,
            workflowStatistics.NumOfRestrictedWorkflows);
    }
}