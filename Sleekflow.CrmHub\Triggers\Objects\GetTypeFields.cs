﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Metadatas;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetTypeFields : ITrigger
{
    private readonly IMetadataRepository _metadataRepository;

    public GetTypeFields(
        IMetadataRepository metadataRepository)
    {
        _metadataRepository = metadataRepository;
    }

    public class GetTypeFieldsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public GetTypeFieldsInput(
            string sleekflowCompanyId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
        }
    }

    public class FieldDto
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("types")]
        public List<dynamic> Types { get; set; }

        public FieldDto(
            string name,
            List<dynamic> types)
        {
            Name = name;
            Types = types;
        }
    }

    public class GetTypeFieldsOutput
    {
        [JsonProperty("fields")]
        public List<FieldDto> Fields { get; set; }

        [JsonConstructor]
        public GetTypeFieldsOutput(
            List<FieldDto> fields)
        {
            Fields = fields;
        }
    }

    public async Task<GetTypeFieldsOutput> F(
        GetTypeFieldsInput getTypeFieldsInput)
    {
        var metadata = await _metadataRepository.GetOrDefaultAsync(
            getTypeFieldsInput.EntityTypeName,
            getTypeFieldsInput.SleekflowCompanyId);
        if (metadata == null)
        {
            return new GetTypeFieldsOutput(new List<FieldDto>());
        }

        var systemKeys = new HashSet<string>
        {
            "_rid",
            "_self",
            "_etag",
            "_attachments",
            "sys_type_name",
            CrmHubEntity.PropertyNameSysResolvedPhoneNumber,
            CrmHubEntity.PropertyNameSysResolvedEmail,
            CrmHubEntity.PropertyNameSysTags,
            CrmHubEntity.PropertyNameSysVersion,
        };

        return new GetTypeFieldsOutput(
            metadata
                .Fields
                .Where(f => !systemKeys.Contains(f.Key))
                .Select(f => new FieldDto(f.Value.Name, f.Value.Types))
                .ToList());
    }
}