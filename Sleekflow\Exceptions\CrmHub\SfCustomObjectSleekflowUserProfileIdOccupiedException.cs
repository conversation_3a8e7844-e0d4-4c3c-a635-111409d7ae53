﻿namespace Sleekflow.Exceptions.CrmHub;

public class SfCustomObjectSleekflowUserProfileIdOccupiedException : ErrorCodeException
{
    public string SleekflowUserProfileId { get; set; }

    public SfCustomObjectSleekflowUserProfileIdOccupiedException(string sleekflowUserProfileId)
        : base(
            ErrorCodeConstant.SfCustomObjectSleekflowUserProfileIdOccupiedException,
            "SleekflowUserProfileId is occupied by other schemaful object.")
    {
        SleekflowUserProfileId = sleekflowUserProfileId;
    }
}