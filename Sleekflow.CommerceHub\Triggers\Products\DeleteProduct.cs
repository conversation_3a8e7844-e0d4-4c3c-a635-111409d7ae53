using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class DeleteProduct
    : ITrigger<
        DeleteProduct.DeleteProductInput,
        DeleteProduct.DeleteProductOutput>
{
    private readonly IProductService _productService;

    public DeleteProduct(
        IProductService productService)
    {
        _productService = productService;
    }

    public class DeleteProductInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteProductInput(
            string id,
            string sleekflowCompanyId,
            string storeId,
            string sleekflowStaffId,
            List<string>? sleekflowTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowTeamIds;
        }
    }

    public class DeleteProductOutput
    {
    }

    public async Task<DeleteProductOutput> F(DeleteProductInput deleteProductInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteProductInput.SleekflowStaffId,
            deleteProductInput.SleekflowStaffTeamIds);

        await _productService.DeleteProductAsync(
            deleteProductInput.Id,
            deleteProductInput.SleekflowCompanyId,
            deleteProductInput.StoreId,
            sleekflowStaff);

        return new DeleteProductOutput();
    }
}