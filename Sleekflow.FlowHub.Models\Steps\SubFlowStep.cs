using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps;

public class SubFlowStep : Step
{
    [Required]
    [JsonProperty("substeps")]
    public List<Step> Substeps { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public SubFlowStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        List<Step> substeps)
        : base(id, name, assign, nextStepId)
    {
        Substeps = substeps;
    }
}