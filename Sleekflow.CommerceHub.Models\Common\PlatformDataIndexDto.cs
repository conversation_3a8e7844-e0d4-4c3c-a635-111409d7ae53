using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Common;

public class PlatformDataIndexDto
{
    [JsonConstructor]
    public PlatformDataIndexDto(string? id, string type)
    {
        Id = id;
        Type = type;
    }

    public PlatformDataIndexDto(PlatformData platformData)
    {
        Id = platformData.Id;
        Type = platformData.Type;
    }

    [SearchableField(IsFilterable = true, IsFacetable = false, IsSortable = false)]
    [JsonProperty("id")]
    public string? Id { get; set; }

    [SearchableField(IsFilterable = true, IsFacetable = true, IsSortable = true)]
    [JsonProperty("type")]
    public string Type { get; set; }
}