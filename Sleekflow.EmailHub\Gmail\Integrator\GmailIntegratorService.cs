using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Gmail.Authentications;
using Sleekflow.EmailHub.Gmail.Communications;
using Sleekflow.EmailHub.Gmail.Subscriptions;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;

namespace Sleekflow.EmailHub.Gmail.Integrator;

public interface IGmailIntegratorService : IEmailService
{
}

public class GmailIntegratorService : EmailService, IGmailIntegratorService, IScopedService
{
    public const string EmailProviderName = ProviderNames.Gmail;

    public GmailIntegratorService(
        IGmailAuthenticationService gmailAuthenticationService,
        IGmailSubscriptionService gmailSubscriptionService,
        IGmailCommunicationService gmailCommunicationService,
        IEmailRepository emailRepository,
        IBus bus,
        IProviderConfigService providerConfigService)
        : base(
            EmailProviderName,
            gmailAuthenticationService,
            gmailSubscriptionService,
            gmailCommunicationService,
            emailRepository,
            bus,
            providerConfigService)
    {
    }
}