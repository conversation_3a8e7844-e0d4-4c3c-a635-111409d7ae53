﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SendMessageV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.send-message";

    [Required]
    [JsonProperty("channel_type")]
    public string ChannelType { get; set; }

    [Required]
    [JsonProperty("channel_identity_id")]
    public string ChannelIdentityId { get; set; }

    [JsonProperty("message__expr")]
    public string? MessageExpr { get; set; }

    [JsonProperty("media_parameters")]
    public MediaParameters? MediaParameters { get; set; }

    [JsonProperty("whatsappcloudapi_message_parameters")]
    public WhatsAppCloudApiMessageParameters? WhatsAppCloudApiMessageParameters { get; set; }

    [JsonProperty("facebook_message_parameters")]
    public FacebookMessageParameters? FacebookMessageParameters { get; set; }

    /// <summary>
    /// Specifies the delivery type for the message
    /// </summary>
    [JsonProperty("delivery_type__expr")]
    public string? DeliveryTypeExpr { get; set; }

    /// <summary>
    /// Impersonate which staff to send message
    /// </summary>
    [JsonProperty("staff_id__expr")]
    public string? StaffIdExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Messaging;

    [JsonConstructor]
    public SendMessageV2StepArgs(
        string channelType,
        string channelIdentityId,
        string? messageExpr,
        MediaParameters? mediaParameters,
        WhatsAppCloudApiMessageParameters? whatsAppCloudApiMessageParameters,
        FacebookMessageParameters? facebookMessageParameters,
        string? deliveryTypeExpr,
        string? staffIdExpr)
    {
        ChannelType = channelType;
        ChannelIdentityId = channelIdentityId;
        MessageExpr = messageExpr;
        MediaParameters = mediaParameters;
        WhatsAppCloudApiMessageParameters = whatsAppCloudApiMessageParameters;
        FacebookMessageParameters = facebookMessageParameters;
        DeliveryTypeExpr = deliveryTypeExpr;
        StaffIdExpr = staffIdExpr;
    }
}

public class InteractiveOption
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [Required]
    [JsonProperty("option")]
    public string Option { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonConstructor]
    public InteractiveOption(
        string? id,
        string option,
        string? description)
    {
        Id = string.IsNullOrWhiteSpace(id) ? Guid.NewGuid().ToString() : id;
        Option = option;
        Description = description;
    }
}

public class WhatsAppCloudApiMessageParameters
{
    [JsonProperty("message_type")]
    public string? MessageType { get; set; }

    [JsonProperty("message_interactive_type")]
    public string? MessageInteractiveType { get; set; }

    [JsonProperty("interactive_title")]
    public string? InteractiveTitle { get; set; }

    [JsonProperty("interactive_options")]
    public List<InteractiveOption>? InteractiveOptions { get; set; }
}

public class FacebookMessageParameters
{
    [JsonProperty("send_window")]
    public string? SendWindow { get; set; }

    [JsonProperty("message_tag")]
    public string? MessageTag { get; set; }
}

public class MediaParameters
{
    [JsonProperty("media_name__expr")]
    public string? MediaNameExpr { get; set; }

    [JsonProperty("media_url__expr")]
    public string? MediaUrlExpr { get; set; }
}