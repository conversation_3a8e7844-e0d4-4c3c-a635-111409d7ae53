﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Contacts;

[TriggerGroup(TriggerGroups.InflowActions)]
public class EnrollContactsToFlowHub : ITrigger
{
    private readonly IBus _bus;

    public EnrollContactsToFlowHub(
        IBus bus)
    {
        _bus = bus;
    }

    public class EnrollContactsToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("contacts")]
        public List<Dictionary<string, object?>> Contacts { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public EnrollContactsToFlowHubInput(
            string sleekflowCompanyId,
            List<Dictionary<string, object?>> contacts,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Contacts = contacts;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class EnrollContactsToFlowHubOutput
    {
    }

    public async Task<EnrollContactsToFlowHubOutput> F(
        EnrollContactsToFlowHubInput enrollContactsToFlowHubInput)
    {
        var sleekflowCompanyId = enrollContactsToFlowHubInput.SleekflowCompanyId;
        var contacts = enrollContactsToFlowHubInput.Contacts;
        var flowHubWorkflowId = enrollContactsToFlowHubInput.FlowHubWorkflowId;
        var flowHubWorkflowVersionedId = enrollContactsToFlowHubInput.FlowHubWorkflowVersionedId;

        var events = contacts
            .Select(
                contact => new OnSleekflowContactEnrollmentToFlowHubRequestedEvent(
                    DateTimeOffset.UtcNow,
                    sleekflowCompanyId,
                    (string) contact["Id"]!,
                    contact,
                    flowHubWorkflowId,
                    flowHubWorkflowVersionedId))
            .ToList();

        await _bus.PublishBatch(
            events,
            context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });

        return new EnrollContactsToFlowHubOutput();
    }
}