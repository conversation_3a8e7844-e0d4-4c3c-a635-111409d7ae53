﻿using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Payments;

[ContainerId(ContainerNames.Order)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class Payment : AuditEntity, IHasSleekflowUserProfileId, IHasRecordStatuses, IHasMetadata
{
    public const string PropertyNamePaymentRecords = "payment_records";
    private const string PropertyNamePaymentStatus = "payment_status";

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty(PropertyNamePaymentStatus)]
    public string PaymentStatus { get; set; }

    [JsonProperty("process_payment_context", TypeNameHandling = TypeNameHandling.Objects)]
    public ProcessPaymentContext? ProcessPaymentContext { get; set; }

    [JsonProperty("process_refund_contexts", TypeNameHandling = TypeNameHandling.Objects)]
    public List<ProcessRefundContext>? ProcessRefundContexts { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonConstructor]
    public Payment(
        string id,
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string orderId,
        string paymentStatus,
        ProcessPaymentContext? processPaymentContext,
        List<ProcessRefundContext>? processRefundContexts,
        Dictionary<string, object?> metadata,
        List<string> recordStatuses,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(id, SysTypeNames.Payment, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        SleekflowUserProfileId = sleekflowUserProfileId;
        OrderId = orderId;
        PaymentStatus = paymentStatus;
        ProcessPaymentContext = processPaymentContext;
        ProcessRefundContexts = processRefundContexts;
        Metadata = metadata;
        RecordStatuses = recordStatuses;
    }
}