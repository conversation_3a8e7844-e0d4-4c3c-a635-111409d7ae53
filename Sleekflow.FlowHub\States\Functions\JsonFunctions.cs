using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Sleekflow.FlowHub.Jsons;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.States.Functions;

public class JsonFunctions : ScriptObject
{
    public static dynamic? Deserialize(string? text)
    {
        if (text == null)
        {
            return null;
        }

        var o = JsonConvert.DeserializeObject(text, JsonConfig.DefaultJsonSerializerSettings);

        return o switch
        {
            JValue jValue => jValue.Value,
            JObject jObject => jObject.ToNativeDictionary(),
            _ => o
        };
    }

    public static string Serialize(object? obj)
    {
        return JsonConvert.SerializeObject(obj, JsonConfig.DefaultJsonSerializerSettings);
    }
}