﻿using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;

namespace Sleekflow.MessagingHub.Models.Events;

public class OnCloudApiHandleWebhookStatusUpdateEvent
{
    public OnCloudApiHandleWebhookStatusUpdateEvent(string wabaId, WhatsappCloudApiWebhookMessages.WhatsappCloudApiEntry entry, WhatsappCloudApiWebhookMessages.WhatsappCloudApiChange change)
    {
        WabaId = wabaId;
        Entry = entry;
        Change = change;
    }

    public string WabaId { get; set; }

    public WhatsappCloudApiWebhookMessages.WhatsappCloudApiEntry Entry { get; set; }

    public WhatsappCloudApiWebhookMessages.WhatsappCloudApiChange Change { get; set; }
}