using Newtonsoft.Json;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.States;
using Sleekflow.JsonConfigs;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.Tests.States;

public class StateAggregatorTests
{
#pragma warning disable CS8618
    private StateAggregator _stateAggregator;
    private StateEvaluator _stateEvaluator;
#pragma warning restore CS8618

    [SetUp]
    public void Setup()
    {
        _stateEvaluator = new StateEvaluator();
        _stateAggregator = new StateAggregator(
            _stateEvaluator,
            new InMemoryLockService());
    }

    [Test]
    public async Task AggregateStateWithBodyStr()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        await usrDict.SetAsync("hello", "world");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, string.Empty, string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            null,
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var step1BodyStr =
            """{"success":true,"data":{"currencies":[{"currency_iso_code":"GBP","currency_name":"British Pound","currency_symbol":"£"},{"currency_iso_code":"HKD","currency_name":"Hong Kong Dollar","currency_symbol":"HK$"},{"currency_iso_code":"MYR","currency_name":"Malaysian Ringgit","currency_symbol":"RM"},{"currency_iso_code":"SGD","currency_name":"Singapore Dollar","currency_symbol":"$"}]},"date_time":"2023-02-22T16:29:08.845Z","http_status_code":200,"request_id":"73TKXnkkYpXvnY"}""";

        var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
            state,
            "step1",
            step1BodyStr);

        Assert.That(updatedState.SysVarDict["step1"], Is.EqualTo(step1BodyStr));

        updatedState = await _stateAggregator.AggregateStateAssignAsync(
            state,
            new Assign
            {
                {
                    "hello", "{{ (sys_var_dict.step1 | json.deserialize).success }}"
                }
            });

        Assert.That(updatedState.UsrVarDict["hello"], Is.EqualTo(true));

        updatedState = await _stateAggregator.AggregateStateAssignAsync(
            state,
            new Assign
            {
                {
                    "hello2", "{{ (sys_var_dict.step1 | json.deserialize).data.currencies[0] | json.serialize }}"
                }
            });

        Assert.That(
            updatedState.UsrVarDict["hello2"],
            Is.EqualTo("""{"currency_iso_code":"GBP","currency_name":"British Pound","currency_symbol":"£"}"""));

        updatedState = await _stateAggregator.AggregateStateAssignAsync(
            state,
            new Assign
            {
                {
                    "hello3", "{{ (sys_var_dict.step1 | json.deserialize).data.currencies[0].currency_iso_code }}"
                }
            });

        Assert.That(updatedState.UsrVarDict["hello3"], Is.EqualTo("GBP"));

        updatedState = await _stateAggregator.AggregateStateAssignAsync(
            state,
            new Assign
            {
                {
                    "hello4",
                    """{{ { "curr": (sys_var_dict.step1 | json.deserialize).data.currencies[0].currency_iso_code, "amount": 123.123 } }}"""
                }
            });

        var hello4 = updatedState.UsrVarDict["hello4"];

        Assert.That(
            ((Dictionary<string, object?>) hello4!)["curr"]!,
            Is.EqualTo("GBP"));
        Assert.That(
            ((Dictionary<string, object?>) hello4)["amount"],
            Is.EqualTo(123.123));

        var serializeObject = JsonConvert.SerializeObject(updatedState, JsonConfig.DefaultJsonSerializerSettings);
        Assert.That(
            serializeObject,
            Is.EqualTo(
                $$$$"""{"id":"","identity":{"sleekflow_company_id":"","object_id":"","workflow_id":"","workflow_versioned_id":"","object_type":"Contact"},"workflow_context":{"snapshotted_workflow":{"id":"my-workflow-1-1","sleekflow_company_id":"my-company-id-1","workflow_id":"my-workflow-1","workflow_versioned_id":"my-workflow-1-1","name":"My Workflow 1","workflow_type":"normal","workflow_group_id":null,"triggers":{"conversation_status_changed":null,"contact_created":null,"contact_label_relationships_changed":null,"contact_list_relationships_changed":null,"contact_updated":null,"message_received":null,"message_sent":null,"webhook":null,"facebook_post_comment_received":null,"instagram_media_comment_received":null,"salesforce_account_updated":null,"salesforce_account_created":null,"salesforce_contact_updated":null,"salesforce_contact_created":null,"salesforce_lead_updated":null,"salesforce_lead_created":null,"salesforce_opportunity_updated":null,"salesforce_opportunity_created":null,"salesforce_campaign_updated":null,"salesforce_campaign_created":null,"salesforce_custom_object_created":null,"salesforce_custom_object_updated":null,"click_to_whatsapp_ads_message_received":null,"schemaful_object_created":null,"schemaful_object_updated":null,"salesforce_account_enrolled":null,"salesforce_contact_enrolled":null,"salesforce_lead_enrolled":null,"salesforce_opportunity_enrolled":null,"salesforce_campaign_enrolled":null,"salesforce_custom_object_enrolled":null,"contact_enrolled":null,"contact_manually_enrolled":null,"schemaful_object_enrolled":null,"whatsapp_flow_submission_message_received":null},"workflow_enrollment_settings":{"can_enroll_only_once":false,"can_enroll_again_on_failure_only":false,"can_enroll_in_parallel":false},"workflow_schedule_settings":{"scheduled_at":null,"contact_property_id":null,"schemaful_object_property_id":null,"schedule_type":"none","recurring_settings":null},"activation_status":"Active","created_by":null,"updated_by":null,"created_at":"2023-01-01T01:01:01.000Z","updated_at":"2023-01-01T01:01:01.000Z","steps":[],"metadata":{}}},"trigger_event_body":null,"origin":"https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net","usr_var_dict":{"hello":true,"hello2":"{\"currency_iso_code\":\"GBP\",\"currency_name\":\"British Pound\",\"currency_symbol\":\"£\"}","hello3":"GBP","hello4":{"curr":"GBP","amount":123.123}},"sys_var_dict":{"step1":"{\"success\":true,\"data\":{\"currencies\":[{\"currency_iso_code\":\"GBP\",\"currency_name\":\"British Pound\",\"currency_symbol\":\"£\"},{\"currency_iso_code\":\"HKD\",\"currency_name\":\"Hong Kong Dollar\",\"currency_symbol\":\"HK$\"},{\"currency_iso_code\":\"MYR\",\"currency_name\":\"Malaysian Ringgit\",\"currency_symbol\":\"RM\"},{\"currency_iso_code\":\"SGD\",\"currency_name\":\"Singapore Dollar\",\"currency_symbol\":\"$\"}]},\"date_time\":\"2023-02-22T16:29:08.845Z\",\"http_status_code\":200,\"request_id\":\"73TKXnkkYpXvnY\"}"},"sys_company_var_dict":{},"_etag":null,"state_status":"Running","state_reason_code":null,"created_at":"{{{{updatedState.CreatedAt:yyyy-MM-ddTHH:mm:ss.fffZ}}}}"}"""));
    }

    [Test]
    public async Task AggregateStateConcurrently()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, string.Empty, string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            null,
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var updatedState = await _stateAggregator.AggregateStateAssignAsync(
            state,
            new Assign
            {
                {
                    "hello", "{{ 1 }}"
                }
            });

        Assert.That(updatedState.UsrVarDict["hello"], Is.EqualTo(1));

        var ints = new List<int>();
        for (int i = 0; i < 10000; i++)
        {
            ints.Add(i);
        }

        await Parallel.ForEachAsync(
            ints,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 50
            },
            async (i, loopState) =>
            {
                await _stateAggregator.AggregateStateAssignAsync(
                    state,
                    new Assign
                    {
                        {
                            "hello", "{{ usr_var_dict.hello + 1 }}"
                        }
                    });
            });

        Assert.That(updatedState.UsrVarDict["hello"], Is.EqualTo(10001));
    }

    [Test]
    public async Task IncrementConcurrently()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, string.Empty, string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            null,
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        await _stateAggregator.IncrementSysCompanyVarAsync(
            state,
            "hello");

        Assert.That(state.SysCompanyVarDict["hello"], Is.EqualTo(1));

        var ints = new List<int>();
        for (int i = 0; i < 10000; i++)
        {
            ints.Add(i);
        }

        await Parallel.ForEachAsync(
            ints,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 50
            },
            async (i, loopState) =>
            {
                await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    "hello");
            });

        Assert.That(state.SysCompanyVarDict["hello"], Is.EqualTo(10001));
    }
}