using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.CompanyConfig)]
[Resolver(typeof(IIntelligentHubDbResolver))]
public class CompanyConfig : AuditEntity, IHasETag
{
    public const string PropertyNameName = "name";
    public const string PropertyNamePreferredLanguage = "preferred_language";
    public const string PropertyNameBackgroundInformation = "background_information";

    [JsonProperty(PropertyNameName)]
    public string Name { get; set; }

    [JsonProperty(PropertyNameBackgroundInformation)]
    public string BackgroundInformation { get; set; }

    [JsonProperty(PropertyNamePreferredLanguage)]
    public string PreferredLanguage { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public CompanyConfig(
        string id,
        string sleekflowCompanyId,
        string name,
        string backgroundInformation,
        string preferredLanguage,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null,
        string? eTag = null)
        : base(id, SysTypeNames.CompanyConfig, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        Name = name;
        BackgroundInformation = backgroundInformation;
        PreferredLanguage = preferredLanguage;
        ETag = eTag;
    }
}