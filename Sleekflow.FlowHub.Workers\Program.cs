using Microsoft.DurableTask.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.FlowHub.Workers.Extensions;
using Sleekflow.FlowHub.Workers.Services;
using Sleekflow.Mvc;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.TimeProviders;
using TimeProvider = Sleekflow.TimeProviders.TimeProvider;

const string name = "SleekflowFlowHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildDbServices(services);
        services.AddDurableTaskClient(
            _ =>
            {
            });

        services.AddSingleton<IAppConfig, AppConfig>();
        services.AddSingleton<ISleekflowCoreConfig, SleekflowCoreConfig>();
        services.AddSingleton<ITimeProvider, TimeProvider>();
        services.AddScoped<IWorkflowRecurringSettingsParser, WorkflowRecurringSettingsParser>();
        services.AddScoped<WorkflowRecurringSettingsV2Parser, WorkflowRecurringSettingsV2Parser>();
        services.AddScoped<DateTimePropertySettingsParser>();
        services.AddScoped<IDynamicFiltersRepositoryContext, DynamicFiltersRepositoryContext>();
        services.AddScoped<IGetPropertyValueService, GetPropertyValueService>();
        services.AddHttpContextAccessor();

        Modules.BuildServiceBusServices(services);
        Modules.BuildHighTrafficServiceBusServices(services);
        Modules.BuildServiceBusManager(services);
        Modules.BuildHttpRequestPollyRetry(services);
        services.AddContactProcessingServices();
    });

hostBuilder.Build().Run();