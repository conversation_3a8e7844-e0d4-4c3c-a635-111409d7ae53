﻿using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using AutoMapper;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Models.ObjectIdResolvers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.Integrator.Hubspot.Configs;
using Sleekflow.Integrator.Hubspot.Services.Models;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Hubspot.Services;

public interface IHubspotObjectService
{
    Task<HubspotObjectService.GetFieldsOutput> GetFieldsAsync(
        HubspotAuthentication authentication,
        string entityTypeName);

    Task<Dictionary<string, object?>> GetObjectAsync(
        HubspotAuthentication authentication,
        string objectId,
        string entityTypeName);

    Task<(List<Dictionary<string, object?>> Objects, string? After)> GetObjectsAsync(
        HubspotAuthentication authentication,
        long limit,
        string? after,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters);

    Task<(List<Dictionary<string, object?>> Objects, string? After)> GetRecentlyUpdatedObjectsAsync(
        HubspotAuthentication authentication,
        DateTimeOffset dateTimeOffset,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        string? after);

    Task<long> GetObjectsCountAsync(
        HubspotAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups);

    Task DeleteAsync(
        HubspotAuthentication authentication,
        string objectId,
        string entityTypeName);

    Task CreateAsync(
        HubspotAuthentication authentication,
        Dictionary<string, object?> dict,
        string entityTypeName);

    Task UpdateAsync(
        HubspotAuthentication authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string entityTypeName);

    Task<List<Dictionary<string, object?>>> BatchCreateAsync(
        HubspotAuthentication authentication,
        List<Dictionary<string, object?>> dicts,
        string entityTypeName);

    Task BatchUpdateAsync(
        HubspotAuthentication authentication,
        List<HubspotObjectServiceUpdateItem> updateItems,
        string entityTypeName);

    string ResolveObjectId(Dictionary<string, object?> dict);

    Task<List<CustomObjectType>> GetCustomObjectTypesAsync(HubspotAuthentication authentication);

    Task<List<Dictionary<string, object?>>> SearchObjectsAsync(
        HubspotAuthentication authentication,
        string entityTypeName,
        List<SearchObjectCondition> conditions);
}

public class HubspotObjectService : ISingletonService, IHubspotObjectService
{
    private static readonly List<SyncConfigFieldFilter> DefaultIntegratedFields = new()
    {
        new SyncConfigFieldFilter("hs_object_id"),
        new SyncConfigFieldFilter("hs_whatsapp_phone_number"),
        new SyncConfigFieldFilter("createdate"),
        new SyncConfigFieldFilter("id"),
        new SyncConfigFieldFilter("firstName"),
        new SyncConfigFieldFilter("lastName"),
        new SyncConfigFieldFilter("email"),
        new SyncConfigFieldFilter("createdAt"),
        new SyncConfigFieldFilter("updatedAt"),
        new SyncConfigFieldFilter("phone"),
        new SyncConfigFieldFilter("mobilephone"),
    };

    private static readonly Dictionary<string, string> SearchObjectConditionOperatorToHubspotOperatorMappings = new()
    {
        { SearchObjectConditionOperators.IsEqualTo, "EQ" },
        { SearchObjectConditionOperators.Exists, "HAS_PROPERTY" },
        { SearchObjectConditionOperators.DoesNotExist, "NOT_HAS_PROPERTY" },
        { SearchObjectConditionOperators.IsGreaterThan, "GT" },
        { SearchObjectConditionOperators.IsLessThan, "LT" },
        { SearchObjectConditionOperators.Contains, "CONTAINS_TOKEN" },
        { SearchObjectConditionOperators.DoesNotContain, "NOT_CONTAINS_TOKEN" },
    };

    private readonly IMapper _mapper;
    private readonly ICacheService _cacheService;
    private readonly IHubspotConfig _config;
    private readonly IHubspotObjectIdResolver _hubspotObjectIdResolver;
    private readonly HttpClient _httpClient;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public HubspotObjectService(
        IHttpClientFactory httpClientFactory,
        IMapper mapper,
        ICacheService cacheService,
        IHubspotConfig config,
        IHubspotObjectIdResolver hubspotObjectIdResolver,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _mapper = mapper;
        _cacheService = cacheService;
        _config = config;
        _hubspotObjectIdResolver = hubspotObjectIdResolver;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    private static string GetObjectCodeName(string entityTypeName)
    {
        switch (entityTypeName)
        {
            case "Contact":
                return "contacts";
            default:
                break;
        }

        throw new Exception($"Unexpected entityTypeName {entityTypeName}");
    }

    public class GetFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            this.UpdatableFields = updatableFields;
            this.CreatableFields = creatableFields;
            this.ViewableFields = viewableFields;
        }
    }

    public async Task<GetFieldsOutput> GetFieldsAsync(
        HubspotAuthentication authentication,
        string entityTypeName)
    {
        if (entityTypeName == "User")
        {
            var getFieldsOutput = new GetFieldsOutput(
                new List<GetTypeFieldsOutputFieldDto>(),
                new List<GetTypeFieldsOutputFieldDto>(),
                new List<GetTypeFieldsOutputFieldDto>()
                {
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "Id",
                        length: 1024,
                        name: "id",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "enumeration",
                        type: "enumeration",
                        unique: false,
                        updateable: false),
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "Email",
                        length: 1024,
                        name: "email",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "string",
                        type: "string",
                        unique: false,
                        updateable: false),
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "First Name",
                        length: 1024,
                        name: "firstName",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "string",
                        type: "string",
                        unique: false,
                        updateable: false),
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "Last Name",
                        length: 1024,
                        name: "lastName",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "string",
                        type: "string",
                        unique: false,
                        updateable: false),
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "User ID",
                        length: 1024,
                        name: "userId",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "number",
                        type: "number",
                        unique: false,
                        updateable: false),
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "createdAt",
                        length: 1024,
                        name: "userId",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "datetime",
                        type: "datetime",
                        unique: false,
                        updateable: false),
                    new GetTypeFieldsOutputFieldDto(
                        calculated: false,
                        compoundFieldName: string.Empty,
                        createable: false,
                        custom: false,
                        encrypted: false,
                        label: "updatedAt",
                        length: 1024,
                        name: "updatedAt",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        soapType: "datetime",
                        type: "datetime",
                        unique: false,
                        updateable: false),
                });

            return getFieldsOutput;
        }

        return await _cacheService.CacheAsync(
            $"{nameof(HubspotObjectService)}-{authentication.SleekflowCompanyId}-{entityTypeName}",
            func: async () =>
            {
                var requestMessage = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri = new Uri($"{_config.HubspotApiUrl}/properties/{entityTypeName}"),
                };
                requestMessage.Headers.Authorization =
                    new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

                var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
                var str = await httpResponseMsg.Content.ReadAsStringAsync();
                if (!httpResponseMsg.IsSuccessStatusCode)
                {
                    _applicationInsightsTelemetryTracer.TraceEvent(
                        TraceEventNames.HubspotDataApiErrorResponseReceived,
                        new Dictionary<string, string>
                        {
                            { "status_code", httpResponseMsg.StatusCode.ToString() },
                            { "sleekflow_company_id", authentication.SleekflowCompanyId },
                            { "authentication_id", authentication.Id },
                        });

                    throw new Exception(
                        $"The httpResponseMsg {httpResponseMsg}, str {str}, objectCodeName {GetObjectCodeName(entityTypeName)} is not working");
                }

                var hubspotPropertiesOutput = str.ToObject<HubspotPropertiesOutput>();
                if (hubspotPropertiesOutput == null)
                {
                    _applicationInsightsTelemetryTracer.TraceEvent(
                        TraceEventNames.HubspotDataApiInvalidOutputReceived,
                        new Dictionary<string, string>
                        {
                            { "sleekflow_company_id", authentication.SleekflowCompanyId },
                            { "authentication_id", authentication.Id },
                        });

                    throw new Exception(
                        $"The httpResponseMsg {httpResponseMsg}, str {str}, objectCodeName {GetObjectCodeName(entityTypeName)} is not working");
                }

                var updatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var creatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var viewableFields = new List<GetTypeFieldsOutputFieldDto>();

                foreach (var field in hubspotPropertiesOutput.Results)
                {
                    if (field.ModificationMetadata.ReadOnlyValue)
                    {
                        viewableFields.Add(_mapper.Map<GetTypeFieldsOutputFieldDto>(field));
                    }
                    else
                    {
                        updatableFields.Add(_mapper.Map<GetTypeFieldsOutputFieldDto>(field));
                    }
                }

                return new GetFieldsOutput(updatableFields, creatableFields, viewableFields);
            },
            CancellationToken.None);
    }

    public class HubspotObjectOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("properties")]
        public Dictionary<string, object?> Properties { get; set; }

        [JsonProperty("createdAt")]
        public string CreatedAt { get; set; }

        [JsonProperty("updatedAt")]
        public string UpdatedAt { get; set; }

        [JsonProperty("archived")]
        public bool Archived { get; set; }

        [JsonConstructor]
        public HubspotObjectOutput(
            string id,
            Dictionary<string, object?> properties,
            string createdAt,
            string updatedAt,
            bool archived)
        {
            Id = id;
            Properties = properties;
            CreatedAt = createdAt;
            UpdatedAt = updatedAt;
            Archived = archived;
        }
    }

    public async Task<Dictionary<string, object?>> GetObjectAsync(
        HubspotAuthentication authentication,
        string objectId,
        string entityTypeName)
    {
        var requestMessage = entityTypeName == "User"
            ? new HttpRequestMessage
            {
                Method = HttpMethod.Get, RequestUri = new Uri($"{_config.HubspotApiUrl}/owners/{objectId}"),
            }
            : new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri =
                    new Uri($"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}/{objectId}"),
            };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg {httpResponseMsg}, str {str}, objectCodeName {GetObjectCodeName(entityTypeName)} is not working");
        }

        var hubspotObjectOutput = str.ToObject<HubspotObjectOutput>();
        if (hubspotObjectOutput == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg {httpResponseMsg}, str {str}, objectCodeName {GetObjectCodeName(entityTypeName)} is not working");
        }

        var dictionary = hubspotObjectOutput.Properties.ToDictionary(p => p.Key, p => p.Value);

        return dictionary;
    }

    private async Task<(List<Dictionary<string, object?>> Objects, string? After)> GetUsersAsync(
        HubspotAuthentication authentication,
        long limit,
        string? after,
        IReadOnlyCollection<SyncConfigFilterGroup> filterGroups,
        IReadOnlyCollection<SyncConfigFieldFilter>? fieldFilters)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri =
                new Uri(
                    $"{_config.HubspotApiUrl}/owners/"
                    + $"?limit={limit}"
                    + (after == null ? string.Empty : $"&after={after}")),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[Users] is not working");
        }

        var hubspotListOutput = str.ToObject<HubspotOwnerListOutput>();
        if (hubspotListOutput == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[Users] is not working");
        }

        var selectedFieldNames = fieldFilters?.Select(ff => ff.Name).ToHashSet();

        return (
            hubspotListOutput.Results
                .Where(
                    dict =>
                    {
                        return filterGroups.All(
                            fg => fg.Filters.Any(
                                syncConfigFilter =>
                                {
                                    var regex = new Regex("^'(.*)'$");
                                    var match = regex.Match(syncConfigFilter.Value);
                                    if (match.Success)
                                    {
                                        return dict.GetValueOrDefault(syncConfigFilter.FieldName)?.ToString()
                                               == match.Groups[1].Value;
                                    }

                                    return dict.GetValueOrDefault(syncConfigFilter.FieldName)?.ToString() ==
                                           syncConfigFilter.Value;
                                }));
                    })
                .Select(
                    dict =>
                    {
                        if (selectedFieldNames == null)
                        {
                            return dict;
                        }

                        return dict
                            .Where(e => selectedFieldNames.Contains(e.Key))
                            .ToDictionary(e => e.Key, e => e.Value);
                    })
                .ToList(),
            hubspotListOutput.Paging?.Next?.After);
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? After)> GetObjectsAsync(
        HubspotAuthentication authentication,
        long limit,
        string? after,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters)
    {
        if (entityTypeName == "User")
        {
            return await GetUsersAsync(
                authentication,
                limit,
                after,
                filterGroups,
                fieldFilters);
        }

        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);

        var fieldNames = new List<string>();
        fieldNames.AddRange(getFieldsOutput.UpdatableFields.Select(f => f.Name));
        fieldNames.AddRange(getFieldsOutput.CreatableFields.Select(f => f.Name));
        fieldNames.AddRange(getFieldsOutput.ViewableFields.Select(f => f.Name));

        EnsureCorrectFieldNames(
            filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName),
            fieldNames.ToHashSet());
        if (fieldFilters != null)
        {
            fieldFilters = fieldFilters.Concat(DefaultIntegratedFields).ToHashSet().ToList();
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri =
                new Uri(
                    $"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}"
                    + $"?limit={limit}"
                    + $"&properties={(fieldFilters?.Any() == true ? string.Join(",", fieldFilters.Select(f => f.Name)) : string.Join(",", fieldNames))}"
                    + (after == null ? string.Empty : $"&after={after}")),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        var hubspotListOutput = str.ToObject<HubspotListOutput>();
        if (hubspotListOutput == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        return (
            hubspotListOutput.Results
                .Select(r => r.Properties)
                .Where(
                    dict =>
                    {
                        return filterGroups.All(
                            fg => fg.Filters.Any(
                                syncConfigFilter =>
                                {
                                    var regex = new Regex("^'(.*)'$");
                                    var match = regex.Match(syncConfigFilter.Value);
                                    if (match.Success)
                                    {
                                        return dict.GetValueOrDefault(syncConfigFilter.FieldName)?.ToString()
                                               == match.Groups[1].Value;
                                    }

                                    return dict.GetValueOrDefault(syncConfigFilter.FieldName)?.ToString() ==
                                           syncConfigFilter.Value;
                                }));
                    })
                .ToList(),
            hubspotListOutput.Paging?.Next?.After);
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? After)> GetRecentlyUpdatedObjectsAsync(
        HubspotAuthentication authentication,
        DateTimeOffset dateTimeOffset,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        string? after)
    {
        if (entityTypeName == "User")
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        var unixTimeMilliseconds = dateTimeOffset.ToUnixTimeMilliseconds();

        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);

        var fieldNames = new List<string>();
        fieldNames.AddRange(getFieldsOutput.UpdatableFields.Select(f => f.Name));
        fieldNames.AddRange(getFieldsOutput.CreatableFields.Select(f => f.Name));
        fieldNames.AddRange(getFieldsOutput.ViewableFields.Select(f => f.Name));

        EnsureCorrectFieldNames(
            filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName),
            fieldNames.ToHashSet());
        if (fieldFilters != null)
        {
            EnsureCorrectFieldNames(fieldFilters.Select(f => f.Name), fieldNames.ToHashSet());
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                JsonConvert.SerializeObject(
                    new HubspotObjectsSearchInput(
                        new List<HubspotObjectsSearchInput.FilterGroup>
                        {
                            new HubspotObjectsSearchInput.FilterGroup(
                                new List<HubspotObjectsSearchInput.Filter>()
                                {
                                    new HubspotObjectsSearchInput.Filter(
                                        "lastmodifieddate",
                                        "GTE",
                                        string.Empty + unixTimeMilliseconds)
                                })
                        },
                        new List<HubspotObjectsSearchInput.Sort>
                        {
                            new HubspotObjectsSearchInput.Sort("lastmodifieddate", "ASCENDING")
                        },
                        100,
                        after,
                        fieldNames)),
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri($"{_config.HubspotApiUrl}/objects/contacts/search"),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        var hubspotListOutput = str.ToObject<HubspotListOutput>();
        if (hubspotListOutput == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        var selectedFieldNames = fieldFilters?.Select(ff => ff.Name).ToHashSet();

        return (
            hubspotListOutput.Results
                .Select(r => r.Properties)
                .Where(
                    dict =>
                    {
                        return filterGroups.All(
                            fg => fg.Filters.Any(
                                syncConfigFilter =>
                                {
                                    var regex = new Regex("^'(.*)'$");
                                    var match = regex.Match(syncConfigFilter.Value);
                                    if (match.Success)
                                    {
                                        return dict.GetValueOrDefault(syncConfigFilter.FieldName)?.ToString()
                                               == match.Groups[1].Value;
                                    }

                                    return dict.GetValueOrDefault(syncConfigFilter.FieldName)?.ToString() ==
                                           syncConfigFilter.Value;
                                }));
                    })
                .Select(
                    dict =>
                    {
                        if (selectedFieldNames == null)
                        {
                            return dict;
                        }

                        return dict
                            .Where(e => selectedFieldNames.Contains(e.Key))
                            .ToDictionary(e => e.Key, e => e.Value);
                    })
                .ToList(),
            hubspotListOutput.Paging?.Next?.After);
    }

    public async Task<long> GetObjectsCountAsync(
        HubspotAuthentication authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups)
    {
        if (entityTypeName == "User")
        {
            return -1;
        }

        if (filterGroups.SelectMany(fg => fg.Filters).Any())
        {
            var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);

            var fieldNames = new List<string>();
            fieldNames.AddRange(getFieldsOutput.UpdatableFields.Select(f => f.Name));
            fieldNames.AddRange(getFieldsOutput.CreatableFields.Select(f => f.Name));
            fieldNames.AddRange(getFieldsOutput.ViewableFields.Select(f => f.Name));

            EnsureCorrectFieldNames(
                filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName),
                fieldNames.ToHashSet());

            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = new StringContent(
                    JsonConvert.SerializeObject(
                        new HubspotObjectsSearchInput(
                            filterGroups
                                .Select(
                                    fg =>
                                    {
                                        return new HubspotObjectsSearchInput.FilterGroup(
                                            fg
                                                .Filters
                                                .Select(
                                                    f =>
                                                    {
                                                        var regex = new Regex("^'(.*)'$");
                                                        var match = regex.Match(f.Value);
                                                        if (match.Success)
                                                        {
                                                            return new HubspotObjectsSearchInput.Filter(
                                                                f.FieldName,
                                                                "EQ",
                                                                match.Groups[1].Value);
                                                        }

                                                        return new HubspotObjectsSearchInput.Filter(
                                                            f.FieldName,
                                                            "EQ",
                                                            f.Value);
                                                    })
                                                .ToList());
                                    })
                                .ToList(),
                            new List<HubspotObjectsSearchInput.Sort>(),
                            100,
                            null,
                            fieldNames.ToList())),
                    Encoding.UTF8,
                    "application/json"),
                RequestUri = new Uri($"{_config.HubspotApiUrl}/objects/contacts/search"),
            };
            requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

            var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
            var str = await httpResponseMsg.Content.ReadAsStringAsync();
            if (!httpResponseMsg.IsSuccessStatusCode)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.HubspotDataApiErrorResponseReceived,
                    new Dictionary<string, string>
                    {
                        { "status_code", httpResponseMsg.StatusCode.ToString() },
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new Exception(
                    $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
            }

            var hubspotListOutput = str.ToObject<HubspotListOutput>();
            if (hubspotListOutput == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.HubspotDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new Exception(
                    $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
            }

            return hubspotListOutput.Total;
        }
        else
        {
            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
#pragma warning disable S1075
                RequestUri = new Uri("https://api.hubapi.com/contacts/v1/contacts/statistics"),
#pragma warning restore S1075
            };
            requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

            var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
            var str = await httpResponseMsg.Content.ReadAsStringAsync();
            if (!httpResponseMsg.IsSuccessStatusCode)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.HubspotDataApiErrorResponseReceived,
                    new Dictionary<string, string>
                    {
                        { "status_code", httpResponseMsg.StatusCode.ToString() },
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new Exception(
                    $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
            }

            var statisticsOutput = str.ToObject<Dictionary<string, object?>>();
            if (statisticsOutput == null)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.HubspotDataApiInvalidOutputReceived,
                    new Dictionary<string, string>
                    {
                        { "sleekflow_company_id", authentication.SleekflowCompanyId },
                        { "authentication_id", authentication.Id },
                    });

                throw new Exception(
                    $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
            }

            return (long) statisticsOutput["contacts"]!;
        }
    }

    public async Task DeleteAsync(
        HubspotAuthentication authentication,
        string objectId,
        string entityTypeName)
    {
        if (entityTypeName == "User")
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Delete,
            RequestUri =
                new Uri($"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}/{objectId}"),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public async Task CreateAsync(
        HubspotAuthentication authentication,
        Dictionary<string, object?> dict,
        string entityTypeName)
    {
        if (entityTypeName == "User")
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                JsonConvert.SerializeObject(
                    new
                    {
                        properties = dict
                    }),
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri($"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public async Task UpdateAsync(
        HubspotAuthentication authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string entityTypeName)
    {
        if (entityTypeName == "User")
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        var objStr = JsonConvert.SerializeObject(
            new
            {
                properties = dict
            },
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
            });

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Patch,
            Content = new StringContent(
                objStr,
                Encoding.UTF8,
                "application/json"),
            RequestUri =
                new Uri($"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}/{objectId}")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public async Task<List<Dictionary<string, object?>>> BatchCreateAsync(
        HubspotAuthentication authentication,
        List<Dictionary<string, object?>> dicts,
        string entityTypeName)
    {
        if (entityTypeName == "User")
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        if (entityTypeName == "Contact" && dicts.Count > 1)
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        var objStr = JsonConvert.SerializeObject(
            new
            {
                inputs = dicts
                    .Select(
                        p => new
                        {
                            properties = p
                        })
                    .ToList()
            });

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                objStr,
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri($"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}/batch/create")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();

        if (httpResponseMsg.StatusCode == HttpStatusCode.BadRequest && str.Contains("VALIDATION_ERROR"))
        {
            // ignore
        }

        // {"status":"error","message":"Contact already exists. Existing ID: 18654201","correlationId":"b23fb909-c868-4227-aded-a4697d88c0cb","category":"CONFLICT"}
        else if (httpResponseMsg.StatusCode == HttpStatusCode.Conflict
                 && str.Contains("Contact already exists"))
        {
            // ignore
            var regex = new Regex("Existing ID: (\\d*)");
            var match = regex.Match(str);
            if (match.Success)
            {
                var existingId = match.Groups[1].Value;

                await BatchUpdateAsync(
                    authentication,
                    dicts
                        .Select(i => new HubspotObjectServiceUpdateItem(existingId, i))
                        .ToList(),
                    entityTypeName);

                return new List<Dictionary<string, object?>>
                {
                    await GetObjectAsync(
                        authentication,
                        existingId,
                        entityTypeName)
                };
            }
        }

        else if (httpResponseMsg.StatusCode == HttpStatusCode.Conflict)
        {
            // ignore
        }

        else if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        var hubspotListOutput = str.ToObject<HubspotListOutput>();

        return hubspotListOutput.Results.Select(x => x.Properties).ToList();
    }

    public async Task BatchUpdateAsync(
        HubspotAuthentication authentication,
        List<HubspotObjectServiceUpdateItem> updateItems,
        string entityTypeName)
    {
        if (entityTypeName == "User")
        {
            throw new SfNotSupportedOperationException(entityTypeName);
        }

        var objStr = JsonConvert.SerializeObject(
            new
            {
                inputs = updateItems
                    .Select(
                        p => new
                        {
                            id = p.ObjectId, properties = p.Dict
                        })
                    .ToList()
            },
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
            });

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                objStr,
                Encoding.UTF8,
                "application/json"),
            RequestUri =
                new Uri($"{_config.HubspotApiUrl}/objects/{GetObjectCodeName(entityTypeName)}/batch/update")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();

        if (httpResponseMsg.StatusCode == HttpStatusCode.Conflict)
        {
            // ignore
        }
        else if (httpResponseMsg.StatusCode == HttpStatusCode.BadRequest && str.Contains("VALIDATION_ERROR"))
        {
            // ignore
        }
        else if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public string ResolveObjectId(Dictionary<string, object?> dict)
    {
        return _hubspotObjectIdResolver.ResolveObjectId(dict);
    }

    public async Task<List<CustomObjectType>> GetCustomObjectTypesAsync(HubspotAuthentication authentication)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = new Uri($"{_config.HubspotApiUrl}/schemas"),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception($"The httpResponseMsg {httpResponseMsg}, str {str} is not working");
        }

        var hubspotResponse = JsonConvert.DeserializeObject<HubspotSchemaListOutput>(str);
        if (hubspotResponse == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception($"The httpResponseMsg {httpResponseMsg}, str {str} is not working");
        }

        var customObjectTypes = hubspotResponse.Results
            .Select(result =>
                new CustomObjectType(
                    apiName: result.ObjectTypeId!,
                    displayName: result.Labels!.Singular!))
            .ToList();

        return customObjectTypes;
    }

    public async Task<List<Dictionary<string, object?>>> SearchObjectsAsync(
        HubspotAuthentication authentication,
        string entityTypeName,
        List<SearchObjectCondition> conditions)
    {
        var getFieldsOutput = await GetFieldsAsync(authentication, entityTypeName);

        var fieldNames = new List<string>();
        fieldNames.AddRange(getFieldsOutput.UpdatableFields.Select(f => f.Name));
        fieldNames.AddRange(getFieldsOutput.CreatableFields.Select(f => f.Name));
        fieldNames.AddRange(getFieldsOutput.ViewableFields.Select(f => f.Name));

        EnsureCorrectFieldNames(
            conditions.Select(c => c.FieldName),
            fieldNames.ToHashSet());

        var searchableFields =
            getFieldsOutput.ViewableFields.Concat(getFieldsOutput.CreatableFields.Concat(getFieldsOutput.UpdatableFields)).ToList();
        var mappedSearchObjectConditions = conditions
            .Where(c => SearchObjectConditionOperatorToHubspotOperatorMappings.ContainsKey(c.Operator))
            .ToList();
        var transformedSearchObjectConditions = new List<SearchObjectCondition>();
        foreach (var mappedSearchObjectCondition in mappedSearchObjectConditions)
        {
            var fieldType = searchableFields
                .First(f => f.Name == mappedSearchObjectCondition.FieldName)
                .Type;

            if (fieldType == "datetime" && mappedSearchObjectCondition.Value is DateTimeOffset dateTimeValue)
            {
                string searchConditionOperator;
                if (mappedSearchObjectCondition.Operator == SearchObjectConditionOperators.IsAfter)
                {
                    searchConditionOperator = "GT";
                }
                else if (mappedSearchObjectCondition.Operator == SearchObjectConditionOperators.IsBefore)
                {
                    searchConditionOperator = "LT";
                }
                else
                {
                    searchConditionOperator = mappedSearchObjectCondition.Operator;
                }

                transformedSearchObjectConditions.Add(new SearchObjectCondition(
                    mappedSearchObjectCondition.FieldName,
                    searchConditionOperator,
                    dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK")));

                continue;
            }

            transformedSearchObjectConditions.Add(mappedSearchObjectCondition);
        }

        var filters = transformedSearchObjectConditions
            .Select(
                c => new HubspotObjectsSearchInput.Filter(
                    c.FieldName,
                    SearchObjectConditionOperatorToHubspotOperatorMappings[c.Operator],
                    c.Operator == SearchObjectConditionOperators.Exists
                    || c.Operator == SearchObjectConditionOperators.DoesNotExist
                        ? string.Empty
                        : c.Value!.ToString()!))
            .ToList();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                JsonConvert.SerializeObject(
                    new HubspotObjectsSearchInput(
                        new List<HubspotObjectsSearchInput.FilterGroup>
                        {
                            new (filters)
                        },
                        new List<HubspotObjectsSearchInput.Sort>(),
                        100,
                        null,
                        fieldNames.ToList())),
                Encoding.UTF8,
                "application/json"),
            RequestUri = new Uri($"{_config.HubspotApiUrl}/objects/contacts/search"),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        var hubspotListOutput = str.ToObject<HubspotListOutput>();
        if (hubspotListOutput == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotDataApiInvalidOutputReceived,
                new Dictionary<string, string>
                {
                    { "sleekflow_company_id", authentication.SleekflowCompanyId },
                    { "authentication_id", authentication.Id },
                });

            throw new Exception(
                $"The httpResponseMsg=[{httpResponseMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }

        return hubspotListOutput.Results.Select(r => r.Properties).ToList();
    }

    private static void EnsureCorrectFieldNames(IEnumerable<string> fieldNames, IReadOnlySet<string> allFieldNames)
    {
        var invalidFieldNamesInFilters = fieldNames
            .Where(fn => allFieldNames.Contains(fn) == false)
            .ToList();
        if (invalidFieldNamesInFilters.Any())
        {
            throw new SfUserFriendlyException(
                $"Some filters are invalid. invalidFieldNames {invalidFieldNamesInFilters}");
        }
    }
}