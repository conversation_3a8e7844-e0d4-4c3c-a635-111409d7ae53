using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.Workers.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers;

public class ExecuteParallelStep
{
    private readonly ILogger<ExecuteParallelStep> _logger;

    public ExecuteParallelStep(
        ILogger<ExecuteParallelStep> logger)
    {
        _logger = logger;
    }

    [Function("ExecuteParallelStep")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<ExecuteParallelStepInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (ExecuteParallelStepInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (executeParallelStepInput, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "ExecuteParallelStep_Orchestrator",
            executeParallelStepInput);

        logger.LogInformation($"Started ExecuteParallelStep_Orchestrator with ID = [{instanceId}]");

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get ExecuteParallelStep_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}