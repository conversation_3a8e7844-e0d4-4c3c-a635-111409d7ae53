using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class EnableUsageLimitAutoScaling : ITrigger<EnableUsageLimitAutoScaling.EnableUsageLimitAutoScalingInput, EnableUsageLimitAutoScaling.EnableUsageLimitAutoScalingOutput>
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public EnableUsageLimitAutoScaling(IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    [method: JsonConstructor]
    public class EnableUsageLimitAutoScalingInput(string sleekflowCompanyId, bool isEnableUsageLimitAutoScaling)
        : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; } = sleekflowCompanyId;

        [JsonProperty("is_enable_usage_limit_auto_scaling")]
        [Required]
        public bool IsEnableUsageLimitAutoScaling { get; set; } = isEnableUsageLimitAutoScaling;
    }

    [method: JsonConstructor]
    public class EnableUsageLimitAutoScalingOutput(FlowHubConfig flowHubConfig)
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; } = flowHubConfig;
    }

    /// <inheritdoc />
    public async Task<EnableUsageLimitAutoScalingOutput> F(EnableUsageLimitAutoScalingInput input)
    {
        var flowHubConfig = await _flowHubConfigService.EnableUsageLimitAutoScalingAsync(input.SleekflowCompanyId, input.IsEnableUsageLimitAutoScaling);
        return new EnableUsageLimitAutoScalingOutput(flowHubConfig);
    }
}