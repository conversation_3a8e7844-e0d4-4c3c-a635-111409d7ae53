using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class SummaryPluginTest
{
    private Kernel _kernel;
    private ISummaryPlugin _summaryPlugin;

    [SetUp]
    public void SetUp()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _summaryPlugin = scope.ServiceProvider.GetRequiredService<ISummaryPlugin>();
    }

    public class SummarizeConversationForHandoverTestCase
    {
        public string ChatHistory { get; set; } = string.Empty;
        public string HandoverReason { get; set; } = string.Empty;
        public string Tone { get; set; } = "Professional";
        public string ResponseLevel { get; set; } = "";
        public string LanguageIsoCode { get; set; } = "en";
        public Dictionary<string, string>? ContactProperties { get; set; }
        public bool ExpectSuccess { get; set; } = true;
    }

    private static IEnumerable<SummarizeConversationForHandoverTestCase> SummarizeConversationForHandoverTestCases
    {
        get
        {
            // Test Case 1: Standard English conversation with basic context
            yield return new SummarizeConversationForHandoverTestCase
            {
                ChatHistory =
                    "User: What are the pricing plans for SleekFlow?\nBot: We offer three plans: Basic ($10/month), Standard ($20/month), and Premium ($30/month).\nUser: What features come with the Standard plan?\nBot: The Standard plan includes WhatsApp messaging, templated messages, and quick replies.\nUser: I'd like to sign up for the Standard plan.",
                HandoverReason = "User expressed interest in signing up for a specific plan.",
                Tone = "Professional",
                ResponseLevel = "Normal",
                LanguageIsoCode = "en_US",
                ContactProperties = new Dictionary<string, string>
                {
                    {
                        "user_name", "John Doe"
                    },
                    {
                        "email", "<EMAIL>"
                    }
                },
                ExpectSuccess = true
            };

            // Test Case 2: Chinese conversation with contact properties
            yield return new SummarizeConversationForHandoverTestCase
            {
                ChatHistory =
                    "用户：SleekFlow 的价格计划是什么？\n机器人：我们提供三种计划：基本版（每月 $10）、标准版（每月 $20）和高级版（每月 $30）。\n用户：标准计划有哪些功能？\n机器人：标准计划包括 WhatsApp 消息、模板消息和快速回复。\n用户：我想注册标准计划。",
                HandoverReason = "用户表示有意注册特定计划。",
                Tone = "Professional",
                ResponseLevel = "Normal",
                LanguageIsoCode = "zh-CN",
                ContactProperties = new Dictionary<string, string>
                {
                    {
                        "user_name", "张三"
                    },
                    {
                        "email", "<EMAIL>"
                    }
                },
                ExpectSuccess = true
            };

            // Test Case 3: Cantonese conversation
            yield return new SummarizeConversationForHandoverTestCase
            {
                ChatHistory =
                    "用戶：SleekFlow 嘅價格計劃係咩？\n機械人：我哋提供三種計劃：基本版（每月 $10）、標準版（每月 $20）和高級版（每月 $30）。\n用戶：標準計劃有邊啲功能？\n機械人：標準計劃包括 WhatsApp 訊息、模板訊息同快速回覆。\n用戶：我想註冊標準計劃。",
                HandoverReason = "用戶表示有意註冊特定計劃。",
                Tone = "Professional",
                ResponseLevel = "Normal",
                LanguageIsoCode = "zh-HK",
                ContactProperties = new Dictionary<string, string>
                {
                    {
                        "user_name", "陳大文"
                    },
                    {
                        "email", "<EMAIL>"
                    }
                },
                ExpectSuccess = true
            };

            // Test Case 4: Default parameters
            yield return new SummarizeConversationForHandoverTestCase
            {
                ChatHistory =
                    "User: I'm having trouble with my account.\nBot: I'm sorry to hear that. Could you please tell me what issues you're experiencing?\nUser: I can't access my previous messages.\nBot: Let me check that for you. It might be a syncing issue.",
                HandoverReason = "User is experiencing account access issues that require further technical support.",
                Tone = "Professional",
                LanguageIsoCode = "en",
                ExpectSuccess = true
            };

            // Test Case 5: Empty chat history
            yield return new SummarizeConversationForHandoverTestCase
            {
                ChatHistory = string.Empty,
                HandoverReason = "User requested to speak with a human agent.",
                Tone = "Professional",
                LanguageIsoCode = "en_US",
                ExpectSuccess = true
            };
        }
    }

    [TestCaseSource(nameof(SummarizeConversationForHandoverTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task SummarizeConversationForHandoverAsyncTest(SummarizeConversationForHandoverTestCase testCase)
    {
        // Act
        var summary = await _summaryPlugin.SummarizeConversationForHandoverAsync(
            _kernel,
            testCase.ChatHistory,
            testCase.HandoverReason,
            testCase.Tone,
            testCase.ResponseLevel,
            testCase.LanguageIsoCode,
            testCase.ContactProperties ?? new Dictionary<string, string>());

        // Assert
        TestContext.WriteLine($"Chat History (excerpt): {GetExcerpt(testCase.ChatHistory)}");
        TestContext.WriteLine($"Handover Reason: {testCase.HandoverReason}");
        TestContext.WriteLine($"Language ISO Code: {testCase.LanguageIsoCode}");

        if (summary != null)
        {
            TestContext.WriteLine($"Summary Context: {summary.Context}");
            TestContext.WriteLine($"Summary Handover Reason: {summary.HandoverReason}");
        }
        else
        {
            TestContext.WriteLine("Summary: null");
        }

        if (testCase.ExpectSuccess)
        {
            Assert.That(summary, Is.Not.Null, "Summary should not be null when success is expected");
            Assert.Multiple(() =>
            {
                Assert.That(summary.Context, Is.Not.Null.And.Not.Empty, "Context should not be empty");
                Assert.That(summary.HandoverReason, Is.Not.Null.And.Not.Empty, "Handover reason should not be empty");
            });
        }
        else
        {
            Assert.That(summary, Is.Null, "Summary should be null when failure is expected");
        }
    }

    [Test]
    public async Task SummarizeConversationForHandoverAsync_WithEmptyChatHistory_ShouldStillReturnSummary()
    {
        // Act
        var summary = await _summaryPlugin.SummarizeConversationForHandoverAsync(
            _kernel,
            string.Empty,
            "User requested to speak with a human agent.",
            "Professional",
            "",
            "en_US",
            new Dictionary<string, string>());

        // Assert
        TestContext.WriteLine($"Summary Context: {summary?.Context ?? "null"}");
        TestContext.WriteLine($"Summary Handover Reason: {summary?.HandoverReason ?? "null"}");

        Assert.That(summary, Is.Not.Null, "Summary should not be null even with empty chat history");
        Assert.Multiple(() =>
        {
            Assert.That(summary.HandoverReason, Is.Not.Null.And.Not.Empty, "Handover reason should not be empty");
        });
    }

    [Test]
    public async Task SummarizeConversationForHandoverAsync_WithNullContactProperties_ShouldUseDefaults()
    {
        // Act
        var summary = await _summaryPlugin.SummarizeConversationForHandoverAsync(
            _kernel,
            "User: Hello\nBot: Hi there, how can I help you today?\nUser: I'd like to speak with a human agent please.",
            "User requested human agent assistance.",
            "Professional",
            "",
            "en",
            null);

        // Assert
        TestContext.WriteLine($"Summary Context: {summary?.Context ?? "null"}");
        TestContext.WriteLine($"Summary Handover Reason: {summary?.HandoverReason ?? "null"}");

        Assert.That(summary, Is.Not.Null, "Summary should not be null with null contact properties");
        Assert.Multiple(() =>
        {
            Assert.That(summary.Context, Is.Not.Null.And.Not.Empty, "Context should not be empty");
            Assert.That(summary.HandoverReason, Is.Not.Null.And.Not.Empty, "Handover reason should not be empty");
        });
    }

    private string GetExcerpt(string text, int maxLength = 100)
    {
        if (string.IsNullOrEmpty(text))
        {
            return "[empty]";
        }

        if (text.Length <= maxLength)
        {
            return text;
        }

        return text.Substring(0, maxLength) + "...";
    }
}