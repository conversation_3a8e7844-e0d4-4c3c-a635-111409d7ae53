using Microsoft.Extensions.Configuration;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Workers.Configs;

public interface IAppConfig
{
    string EmailHubInternalEndpoint { get; }

    string InternalsKey { get; }
}

public class AppConfig : IAppConfig
{
    public string EmailHubInternalEndpoint { get; }

    public string InternalsKey { get; }

    public AppConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        EmailHubInternalEndpoint =
            Environment.GetEnvironmentVariable("EMAIL_HUB_INTERNALS_ENDPOINT", target)
            ?? configuration["EMAIL_HUB_INTERNALS_ENDPOINT"];

        InternalsKey =
            Environment.GetEnvironmentVariable("INTERNALS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("INTERNALS_KEY");
    }
}