using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

public class CompanyAgentConfigActionsDto
{
    [JsonProperty("send_message")]
    public SendMessageActionDto? SendMessage { get; set; }

    [JsonProperty("calculate_lead_score")]
    public CalculateLeadScoreActionDto? CalculateLeadScore { get; set; }

    [JsonProperty("exit_conversation")]
    public ExitConversationActionDto? ExitConversation { get; set; }

    [JsonProperty("add_label")]
    public AddLabelActionDto? AddLabel { get; set; }

    [JsonConstructor]
    public CompanyAgentConfigActionsDto(
        SendMessageActionDto? sendMessage,
        CalculateLeadScoreActionDto? calculateLeadScore,
        ExitConversationActionDto? exitConversation,
        AddLabelActionDto? addLabel)
    {
        SendMessage = sendMessage;
        CalculateLeadScore = calculateLeadScore;
        ExitConversation = exitConversation;
        AddLabel = addLabel;
    }
}