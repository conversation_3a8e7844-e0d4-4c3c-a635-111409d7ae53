using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Triggers.KnowledgeBases;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class ProcessFileDocumentCoreIntegrationTests
{
    private const string SleekflowCompanyId = "sample-company-id";

    private readonly List<(string, string)> _fileNames = new ()
    {
        ("pdfTest.pdf", "pdf")
    };

    private readonly List<string> _loadChunkIds = new ();

    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }
    }

    [Test, Order(1)]
    public async Task GetEditLoadFileDocumentTest()
    {
        foreach (var (fileName, _) in _fileNames)
        {
            // GetFileDocumentInformation
            var createGetFileDocumentInformationInput =
                new GetFileDocumentInformation.GetFileDocumentInformationInput(
                    SleekflowCompanyId,
                    SleekflowCompanyId + "/" + fileName);

            var createGetFileDocumentInformationInputScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createGetFileDocumentInformationInput)
                        .ToUrl("/Documents/GetFileDocumentInformation");
                });

            var createGetFileDocumentInformationOutputOutput =
                await createGetFileDocumentInformationInputScenarioResult
                    .ReadAsJsonAsync<Output<GetFileDocumentInformation.GetFileDocumentInformationOutput>>();

            var createGetFileDocumentInformationOutput = createGetFileDocumentInformationOutputOutput?.Data;

            Assert.That(createGetFileDocumentInformationOutputOutput, Is.Not.Null);
            Assert.That(createGetFileDocumentInformationOutput?.Document, Is.Not.Null);
            Assert.That(createGetFileDocumentInformationOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            // GetFileDocumentChunks
            var documentId = createGetFileDocumentInformationOutput!.Document.Id;
            var createGetFileDocumentChunksInput =
                new GetFileDocumentChunks.GetFileDocumentChunksInput(
                    SleekflowCompanyId,
                    documentId,
                    null,
                    1000);

            var createGetFileDocumentChunksInputScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createGetFileDocumentChunksInput)
                        .ToUrl("/Documents/GetFileDocumentChunks");
                });

            var createGetFileDocumentChunksOutputOutput =
                await createGetFileDocumentChunksInputScenarioResult
                    .ReadAsJsonAsync<Output<GetFileDocumentChunks.GetFileDocumentChunksOutput>>();

            var createGetFileDocumentChunksOutput = createGetFileDocumentChunksOutputOutput?.Data;
            Assert.That(createGetFileDocumentChunksOutputOutput, Is.Not.Null);
            Assert.That(createGetFileDocumentChunksOutput?.DocumentChunks, Is.Not.Null);
            Assert.That(createGetFileDocumentChunksOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            // EditFileDocumentChunks
            var chunkIds = createGetFileDocumentChunksOutput!.DocumentChunks.Select(e => e.Id).ToList();

            var editingChunkDtos = new List<EditingChunkDto>();
            for (var i = 0; i < chunkIds.Count; i++)
            {
                if (i % 100 == 0)
                {
                    var editingChunkDto = new EditingChunkDto(
                        chunkIds[i],
                        "modified content",
                        new List<Category>(),
                        new Dictionary<string, object?>());
                    editingChunkDtos.Add(editingChunkDto);
                }

                _loadChunkIds.Add(chunkIds[i]);
            }

            var createEditFileDocumentChunksInput = new EditFileDocumentChunks.EditFileDocumentChunksInput(
                SleekflowCompanyId,
                documentId,
                editingChunkDtos);

            var createEditFileDocumentChunksInputScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createEditFileDocumentChunksInput)
                        .ToUrl("/Documents/EditFileDocumentChunks");
                });

            var createEditFileDocumentChunksOutputOutput =
                await createEditFileDocumentChunksInputScenarioResult
                    .ReadAsJsonAsync<Output<EditFileDocumentChunks.EditFileDocumentChunksOutput>>();

            var createEditFileDocumentChunksOutput = createEditFileDocumentChunksOutputOutput?.Data;
            Assert.That(createEditFileDocumentChunksOutputOutput, Is.Not.Null);
            Assert.That(createEditFileDocumentChunksOutput?.EditedChunks, Is.Not.Null);
            Assert.That(createEditFileDocumentChunksOutputOutput!.HttpStatusCode, Is.EqualTo(200));
            //
            // // load file doc to kb entry
            // var createLoadFileDocumentChunksToKnowledgeBaseInput =
            //     new LoadFileDocumentChunksToKnowledgeBase.LoadFileDocumentChunksToKnowledgeBaseInput(
            //         SleekflowCompanyId,
            //         documentId,
            //         _loadChunkIds);
            //
            // var createLoadFileDocumentChunksToKnowledgeBaseScenarioResult = await Application.Host.Scenario(
            //     _ =>
            //     {
            //         _.WithRequestHeader("X-Sleekflow-Record", "true");
            //         _.Post.Json(createLoadFileDocumentChunksToKnowledgeBaseInput)
            //             .ToUrl("/KnowledgeBases/LoadFileDocumentChunksToKnowledgeBase");
            //     });
            //
            // var createLoadFileDocumentChunksToKnowledgeBaseOutputOutput =
            //     await createLoadFileDocumentChunksToKnowledgeBaseScenarioResult
            //         .ReadAsJsonAsync<
            //             Output<LoadFileDocumentChunksToKnowledgeBase.LoadFileDocumentChunksToKnowledgeBaseOutput>>();
            //
            // var createLoadFileDocumentChunksToKnowledgeBaseOutput =
            //     createLoadFileDocumentChunksToKnowledgeBaseOutputOutput?.Data;
            // Assert.That(createLoadFileDocumentChunksToKnowledgeBaseOutputOutput, Is.Not.Null);
            // Assert.That(createLoadFileDocumentChunksToKnowledgeBaseOutput, Is.Not.Null);
            // Assert.That(createLoadFileDocumentChunksToKnowledgeBaseOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        }
    }

    [Test, Order(2)]
    public async Task KnowledgeBaseEntriesTest()
    {
        foreach (var (fileName, _) in _fileNames)
        {
            var createGetKnowledgeBaseEntriesInput = new GetKnowledgeBaseEntries.GetKnowledgeBaseEntriesInput(
                SleekflowCompanyId,
                new GetKnowledgeBaseEntriesFilters(
                    SleekflowCompanyId + "/" + fileName,
                    KnowledgeBaseSourceTypes.FileDocument),
                1000,
                null);

            var createGetKnowledgeBaseEntriesScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createGetKnowledgeBaseEntriesInput)
                        .ToUrl("/KnowledgeBases/GetKnowledgeBaseEntries");
                });

            var createGetKnowledgeBaseEntriesOutputOutput =
                await createGetKnowledgeBaseEntriesScenarioResult
                    .ReadAsJsonAsync<Output<GetKnowledgeBaseEntries.GetKnowledgeBaseEntriesOutput>>();

            var createGetKnowledgeBaseEntriesOutput = createGetKnowledgeBaseEntriesOutputOutput?.Data;

            Assert.That(createGetKnowledgeBaseEntriesOutputOutput, Is.Not.Null);
            Assert.That(createGetKnowledgeBaseEntriesOutput?.KnowledgeBaseEntries, Is.Not.Null);
            Assert.That(createGetKnowledgeBaseEntriesOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            var entryIds = createGetKnowledgeBaseEntriesOutput!.KnowledgeBaseEntries.Select(e => e.Id).ToList();

            foreach (var entryId in entryIds)
            {
                // Get entry by Id
                var createGetKnowledgeBaseEntryInput = new GetKnowledgeBaseEntry.GetKnowledgeBaseEntryInput(
                    SleekflowCompanyId,
                    entryId);

                var createGetKnowledgeBaseEntryScenarioResult = await Application.Host.Scenario(
                    _ =>
                    {
                        _.WithRequestHeader("X-Sleekflow-Record", "true");
                        _.Post.Json(createGetKnowledgeBaseEntryInput)
                            .ToUrl("/KnowledgeBases/GetKnowledgeBaseEntry");
                    });

                var createGetKnowledgeBaseEntryOutputOutput =
                    await createGetKnowledgeBaseEntryScenarioResult
                        .ReadAsJsonAsync<Output<GetKnowledgeBaseEntry.GetKnowledgeBaseEntryOutput>>();

                var createGetKnowledgeBaseEntryOutput = createGetKnowledgeBaseEntryOutputOutput?.Data;

                Assert.That(createGetKnowledgeBaseEntryOutputOutput, Is.Not.Null);
                Assert.That(createGetKnowledgeBaseEntryOutput?.KnowledgeBaseEntry, Is.Not.Null);
                Assert.That(createGetKnowledgeBaseEntryOutputOutput!.HttpStatusCode, Is.EqualTo(200));
            }

            // delete entries
            var createRemoveKnowledgeBaseEntriesInput = new RemoveKnowledgeBaseEntries.RemoveKnowledgeBaseEntriesInput(
                SleekflowCompanyId,
                entryIds);

            var createRemoveKnowledgeBaseEntriesScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createRemoveKnowledgeBaseEntriesInput)
                        .ToUrl("/KnowledgeBases/RemoveKnowledgeBaseEntries");
                });

            var createRemoveKnowledgeBaseEntriesOutputOutput =
                await createRemoveKnowledgeBaseEntriesScenarioResult
                    .ReadAsJsonAsync<Output<RemoveKnowledgeBaseEntries.RemoveKnowledgeBaseEntriesOutput>>();

            var createRemoveKnowledgeBaseEntriesOutput = createRemoveKnowledgeBaseEntriesOutputOutput?.Data;

            Assert.That(createRemoveKnowledgeBaseEntriesOutputOutput, Is.Not.Null);
            Assert.That(createRemoveKnowledgeBaseEntriesOutput?.DeletedEntryIds, Is.Not.Null);
            Assert.That(createRemoveKnowledgeBaseEntriesOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        }
    }
}