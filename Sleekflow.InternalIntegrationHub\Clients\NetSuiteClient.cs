using Newtonsoft.Json;
using RestSharp;
using RestSharp.Authenticators;
using RestSharp.Authenticators.OAuth;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Configs.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Clients;

public interface INetSuiteClient
{
    Task<RestResponse<T>> GetAsync<T>(string url, Dictionary<string, string>? queriesParameter);

    Task<RestResponse<T>> PostAsync<T>(string url, object requestBody);

    Task<RestResponse<T>> PatchAsync<T>(string url, object requestBody);

    Task<RestResponse<T>> PutAsync<T>(string url, object requestBody, Dictionary<string, string>? queriesParameter);

    Task<RestResponse<T>> DeleteAsync<T>(string url, object requestBody);
}

public class NetSuiteClient : INetSuiteClient, IScopedService
{
    private readonly INetSuiteConfig _config;
    private readonly ILogger<NetSuiteClient> _logger;

    public NetSuiteClient(INetSuiteConfig config, ILogger<NetSuiteClient> logger)
    {
        _config = config;
        _logger = logger;
    }

    private OAuth1Authenticator ConstructAuthenticator()
    {
        return new OAuth1Authenticator
        {
            Type = OAuthType.AccessToken,
            SignatureMethod = OAuthSignatureMethod.HmacSha256,
            ConsumerKey = _config.ConsumerKey,
            ConsumerSecret = _config.ConsumerSecret,
            Token = _config.AccessToken,
            TokenSecret = _config.AccessTokenSecret,
            Realm = _config.Realm,
            Version = "1.0",
            ParameterHandling = OAuthParameterHandling.HttpAuthorizationHeader,
        };
    }

    public async Task<RestResponse<T>> GetAsync<T>(string url, Dictionary<string, string>? queriesParameter)
    {
        var uriBuilder = new UriBuilder(_config.BaseUrl + url);
        var baseUrl = uriBuilder.Scheme + "://" + uriBuilder.Host;
        var options = new RestClientOptions(baseUrl)
        {
            Authenticator = ConstructAuthenticator()
        };
        var client = new RestClient(options);
        var request = new RestRequest(uriBuilder.Path)
        {
            RequestFormat = DataFormat.Json
        };
        if (queriesParameter != null)
        {
            foreach (var query in queriesParameter)
            {
                request.AddQueryParameter(query.Key, query.Value);
            }
        }

        _logger.LogInformation(
            "NetSuiteClient: GET {CommandName} {ArgsJson}",
            uriBuilder.Path,
            JsonConvert.SerializeObject(queriesParameter));

        RestResponse<T> response = await client.ExecuteAsync<T>(request);

        _logger.LogInformation(
            "NetSuiteClient: GET {CommandName} {ResponseJson}",
            uriBuilder.Path,
            response.Content);

        return response;
    }

    public async Task<RestResponse<T>> PostAsync<T>(string url, object requestBody)
    {
        var uriBuilder = new UriBuilder(_config.BaseUrl + url);
        var baseUrl = uriBuilder.Scheme + "://" + uriBuilder.Host;
        var options = new RestClientOptions(baseUrl)
        {
            Authenticator = ConstructAuthenticator()
        };
        var client = new RestClient(options);
        var request = new RestRequest(uriBuilder.Path, Method.Post)
        {
            RequestFormat = DataFormat.Json
        };
        var jsonBody = JsonConvert.SerializeObject(requestBody);
        request.AddJsonBody(jsonBody);

        _logger.LogInformation(
            "NetSuiteClient: POST {CommandName} {ArgsJson}",
            uriBuilder.Path,
            jsonBody);

        RestResponse<T> response = await client.ExecuteAsync<T>(request);

        _logger.LogInformation(
            "NetSuiteClient: POST {CommandName} {ResponseJson}",
            uriBuilder.Path,
            response.Content);

        return response;
    }

    public async Task<RestResponse<T>> PatchAsync<T>(string url, object requestBody)
    {
        var uriBuilder = new UriBuilder(_config.BaseUrl + url);
        var baseUrl = uriBuilder.Scheme + "://" + uriBuilder.Host;
        var options = new RestClientOptions(baseUrl)
        {
            Authenticator = ConstructAuthenticator()
        };
        var client = new RestClient(options);
        var request = new RestRequest(uriBuilder.Path, Method.Patch)
        {
            RequestFormat = DataFormat.Json
        };
        var jsonBody = JsonConvert.SerializeObject(requestBody);
        request.AddJsonBody(jsonBody);

        _logger.LogInformation(
            "NetSuiteClient: PATCH {CommandName} {ArgsJson}",
            uriBuilder.Path,
            jsonBody);

        RestResponse<T> response = await client.ExecuteAsync<T>(request);

        _logger.LogInformation(
            "NetSuiteClient: PATCH {CommandName} {ResponseJson}",
            uriBuilder.Path,
            response.Content);

        return response;
    }

    public async Task<RestResponse<T>> PutAsync<T>(
        string url,
        object requestBody,
        Dictionary<string, string>? queriesParameter)
    {
        var uriBuilder = new UriBuilder(_config.BaseUrl + url);
        var baseUrl = uriBuilder.Scheme + "://" + uriBuilder.Host;
        var options = new RestClientOptions(baseUrl)
        {
            Authenticator = ConstructAuthenticator()
        };
        var client = new RestClient(options);
        var request = new RestRequest(uriBuilder.Path, Method.Put)
        {
            RequestFormat = DataFormat.Json
        };

        if (queriesParameter != null)
        {
            foreach (var query in queriesParameter)
            {
                request.AddQueryParameter(query.Key, query.Value);
            }
        }

        var jsonBody = JsonConvert.SerializeObject(requestBody);
        request.AddJsonBody(jsonBody);

        _logger.LogInformation(
            "NetSuiteClient: PUT {CommandName} {ArgsJson}",
            uriBuilder.Path,
            jsonBody);

        var response = await client.ExecuteAsync<T>(request);

        _logger.LogInformation(
            "NetSuiteClient: PUT {CommandName} {ResponseJson}",
            uriBuilder.Path,
            response.Content);

        return response;
    }

    public async Task<RestResponse<T>> DeleteAsync<T>(string url, object requestBody)
    {
        var uriBuilder = new UriBuilder(_config.BaseUrl + url);
        var baseUrl = uriBuilder.Scheme + "://" + uriBuilder.Host;
        var options = new RestClientOptions(baseUrl)
        {
            Authenticator = ConstructAuthenticator()
        };
        var client = new RestClient(options);
        var request = new RestRequest(uriBuilder.Path, Method.Delete)
        {
            RequestFormat = DataFormat.Json
        };
        var jsonBody = JsonConvert.SerializeObject(requestBody);
        request.AddJsonBody(jsonBody);

        _logger.LogInformation(
            "NetSuiteClient: DELETE {CommandName} {ArgsJson}",
            uriBuilder.Path,
            jsonBody);

        RestResponse<T> response = await client.ExecuteAsync<T>(request);

        _logger.LogInformation(
            "NetSuiteClient: DELETE {CommandName} {ResponseJson}",
            uriBuilder.Path,
            response.Content);

        return response;
    }
}