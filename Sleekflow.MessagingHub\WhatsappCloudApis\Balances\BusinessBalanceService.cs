using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

public interface IBusinessBalanceService
{
    Task<BusinessBalance> GetAsync(string id, string partitionKey);

    Task<BusinessBalance?> GetWithIdAsync(string id);

    Task<BusinessBalance?> GetWithFacebookBusinessIdAsync(string facebookBusinessId);

    Task<BusinessBalance?> GetOrDefaultBusinessBalanceAsync(string id, string facebookBusinessId);

    Task<BusinessBalance> CreateAndGetBusinessBalanceAsync(
        string facebookBusinessId,
        MarkupProfile markupProfile,
        ConversationUsageInsertState conversationUsageInsertState);

    Task<List<BusinessBalance>> GetAllAsync();

    Task<(List<BusinessBalance> BusinessBalances, string? NextContinuationToken)> GetBusinessBalanceAsync(
        string? continuationToken,
        int limit);

    Task<int> UpsertBusinessBalanceAsync(BusinessBalance businessBalance);

    Task<int> UpsertBusinessBalanceAsync(
        string operation,
        BusinessBalance businessBalanceSnapshot,
        BusinessBalance businessBalance,
        Dictionary<string, object>? additionalChanges = null);

    Task<BusinessBalance?> UpsertBusinessBalanceMarkupProfileAsync(
        BusinessBalance? businessBalance,
        MarkupProfile markupProfile,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    bool IsBusinessBalanceUsageOrCreditChanged(
        BusinessBalance? beforeCalculateBusinessBalanceSnapShot,
        BusinessBalance? afterCalculatedBusinessBalanceSnapShot);

    Task<List<(BusinessBalance BusinessBalance, List<Waba> Wabas)>> GetManagementBusinessBalancesFromCompanyAsync(string sleekflowCompanyId);
}

public class BusinessBalanceService : IBusinessBalanceService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<BusinessBalanceService> _logger;
    private readonly IBusinessBalanceRepository _businessBalanceRepository;
    private readonly IWabaRepository _wabaRepository;

    public BusinessBalanceService(
        IIdService idService,
        IAuditLogService auditLogService,
        ILogger<BusinessBalanceService> logger,
        IBusinessBalanceRepository businessBalanceRepository,
        IWabaRepository wabaRepository)
    {
        _logger = logger;
        _idService = idService;
        _auditLogService = auditLogService;
        _businessBalanceRepository = businessBalanceRepository;
        _wabaRepository = wabaRepository;
    }

    public async Task<BusinessBalance> GetAsync(string id, string partitionKey)
    {
        return await _businessBalanceRepository.GetAsync(id, partitionKey);
    }

    public async Task<BusinessBalance?> GetWithIdAsync(string id)
    {
        return (await _businessBalanceRepository.GetObjectsAsync(b => b.Id == id)).FirstOrDefault();
    }

    public async Task<BusinessBalance?> GetWithFacebookBusinessIdAsync(string facebookBusinessId)
    {
        return (await _businessBalanceRepository.GetObjectsAsync(
            b =>
                b.FacebookBusinessId == facebookBusinessId)).FirstOrDefault();
    }

    public async Task<BusinessBalance?> GetOrDefaultBusinessBalanceAsync(
        string id,
        string facebookBusinessId)
    {
        return await _businessBalanceRepository.GetOrDefaultAsync(id, facebookBusinessId);
    }

    public async Task<List<BusinessBalance>> GetAllAsync()
    {
        return await _businessBalanceRepository.GetObjectsAsync(b => true);
    }

    public Task<(List<BusinessBalance> BusinessBalances, string? NextContinuationToken)> GetBusinessBalanceAsync(string? continuationToken, int limit)
    {
        return _businessBalanceRepository.GetContinuationTokenizedObjectsAsync(w => true, continuationToken, limit);
    }

    public async Task<int> UpsertBusinessBalanceAsync(BusinessBalance businessBalance)
    {
        return await _businessBalanceRepository.UpsertAsync(
            businessBalance,
            businessBalance.FacebookBusinessId,
            eTag: businessBalance.ETag);
    }

    public async Task<int> UpsertBusinessBalanceAsync(
        string operation,
        BusinessBalance businessBalanceSnapshot,
        BusinessBalance businessBalance,
        Dictionary<string, object>? additionalChanges = null)
    {
        var changes = new Dictionary<string, object?>
        {
            {
                "changes", businessBalance
            }
        };

        if (additionalChanges is not null)
        {
            changes = changes.Union(additionalChanges).ToDictionary(c => c.Key, c => c.Value);
        }

        await _auditLogService.AuditBusinessBalanceAsync(
            businessBalanceSnapshot,
            businessBalanceSnapshot.FacebookBusinessId,
            operation,
            changes);

        return await _businessBalanceRepository.UpsertAsync(
            businessBalance,
            businessBalance.FacebookBusinessId,
            eTag: businessBalance.ETag);
    }

    public async Task<BusinessBalance> CreateAndGetBusinessBalanceAsync(
        string facebookBusinessId,
        MarkupProfile markupProfile,
        ConversationUsageInsertState conversationUsageInsertState)
    {
        var businessBalance = new BusinessBalance(
            _idService.GetId(SysTypeNames.BusinessBalance),
            facebookBusinessId,
            markupProfile,
            wabaBalances: null,
            isByWabaBillingEnabled: false, // by default business level credit management is enabled
            conversationUsageInsertState,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var createAndGetBusinessBalance =
            await _businessBalanceRepository.CreateAndGetAsync(businessBalance, facebookBusinessId);
        await _auditLogService.AuditBusinessBalanceAsync(
            businessBalance,
            businessBalance.FacebookBusinessId,
            AuditingOperation.OnCloudApiWabaBusinessConnectedEvent,
            new Dictionary<string, object?>
            {
                {
                    "changes", createAndGetBusinessBalance
                }
            });

        return createAndGetBusinessBalance;
    }

    public async Task<BusinessBalance?> UpsertBusinessBalanceMarkupProfileAsync(
        BusinessBalance? businessBalance,
        MarkupProfile markupProfile,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        businessBalance =
            await GetOrDefaultBusinessBalanceAsync(businessBalance!.Id, businessBalance.FacebookBusinessId);
        if (businessBalance is null)
        {
            throw new SfInternalErrorException("Unable to locate business balance");
        }

        var beforeUpsertBusinessBalanceSnapshot =
            JsonConvert.DeserializeObject<BusinessBalance>(JsonConvert.SerializeObject(businessBalance));

        businessBalance.MarkupProfile = markupProfile;
        businessBalance.UpdatedAt = DateTimeOffset.UtcNow;

        return await UpsertAndAuditBusinessBalance(
            beforeUpsertBusinessBalanceSnapshot!,
            businessBalance,
            sleekflowStaffId,
            sleekflowStaffTeamIds);
    }

    private async Task<BusinessBalance?> UpsertAndAuditBusinessBalance(
        BusinessBalance beforeUpsertBusinessBalanceSnapshot,
        BusinessBalance businessBalance,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var businessBalanceUpsertState = await UpsertBusinessBalanceAsync(businessBalance);

        if (businessBalanceUpsertState == 0)
        {
            throw new SfInternalErrorException("Unable to upsert business balance");
        }

        var afterUpsertBusinessBalance =
            await GetOrDefaultBusinessBalanceAsync(businessBalance.Id, businessBalance.FacebookBusinessId);

        var auditBusinessBalanceState = await _auditLogService.AuditBusinessBalanceAsync(
            beforeUpsertBusinessBalanceSnapshot,
            businessBalance.FacebookBusinessId,
            AuditingOperation.UpdateWhatsappCloudApiBusinessBalanceMarkupProfile,
            new Dictionary<string, object?>
            {
                {
                    "changes", afterUpsertBusinessBalance
                },
                {
                    "sleekflowStaffId", sleekflowStaffId
                },
                {
                    "sleekflowStaffTeamIds", sleekflowStaffTeamIds
                }
            },
            -1);

        if (auditBusinessBalanceState is null)
        {
            _logger.LogError(
                "Unable to audit log business balance changes {BeforeUpsertBusinessBalanceSnapshot}/{AfterUpsertBusinessBalance}",
                JsonConvert.SerializeObject(beforeUpsertBusinessBalanceSnapshot),
                JsonConvert.SerializeObject(afterUpsertBusinessBalance));
        }

        return afterUpsertBusinessBalance;
    }

    public bool IsBusinessBalanceUsageOrCreditChanged(
        BusinessBalance? beforeCalculateBusinessBalanceSnapShot,
        BusinessBalance? afterCalculatedBusinessBalanceSnapShot)
    {
        if (beforeCalculateBusinessBalanceSnapShot is null)
        {
            throw new ArgumentNullException(nameof(beforeCalculateBusinessBalanceSnapShot));
        }

        if (afterCalculatedBusinessBalanceSnapShot is null)
        {
            throw new ArgumentNullException(nameof(afterCalculatedBusinessBalanceSnapShot));
        }

        if (beforeCalculateBusinessBalanceSnapShot.Balance.Amount !=
            afterCalculatedBusinessBalanceSnapShot.Balance.Amount)
        {
            return true;
        }

        if (beforeCalculateBusinessBalanceSnapShot.Credit.Amount !=
            afterCalculatedBusinessBalanceSnapShot.Credit.Amount)
        {
            return true;
        }

        if (beforeCalculateBusinessBalanceSnapShot.AllTimeUsage.Amount !=
            afterCalculatedBusinessBalanceSnapShot.AllTimeUsage.Amount)
        {
            return true;
        }

        return false;
    }

    public async Task<List<(BusinessBalance BusinessBalance, List<Waba> Wabas)>> GetManagementBusinessBalancesFromCompanyAsync(
        string sleekflowCompanyId)
    {
        var wabas = await _wabaRepository.GetObjectsAsync(x => x.SleekflowCompanyIds.Contains(sleekflowCompanyId));

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w => w.FacebookBusinessId != null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        var businessBalanceAndWabasTuples = new List<(BusinessBalance, List<Waba>)>();

        foreach (var (facebookBusinessId, relatedWabas) in facebookBusinessIdToWabasDict)
        {
            var businessBalance = await GetWithFacebookBusinessIdAsync(facebookBusinessId);

            if (businessBalance is not null)
            {
                businessBalanceAndWabasTuples.Add((businessBalance, relatedWabas));
            }
            else
            {
                _logger.LogWarning(
                    "Unable to locate BusinessBalance with {FacebookBusinessId}. {RelatedWabas}",
                    facebookBusinessId,
                    JsonConvert.SerializeObject(relatedWabas));
            }
        }

        return businessBalanceAndWabasTuples;
    }
}