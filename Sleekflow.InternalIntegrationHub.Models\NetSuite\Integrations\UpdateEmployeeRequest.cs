using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class UpdateEmployeeRequest
{
    [JsonProperty(
        PropertyName = "supervisor",
        DefaultValueHandling = DefaultValueHandling.Include,
        NullValueHandling = NullValueHandling.Include)]
    public Supervisor? Supervisor { get; set; }

    [JsonConstructor]
    public UpdateEmployeeRequest(
        Supervisor? supervisor)
    {
        Supervisor = supervisor;
    }
}