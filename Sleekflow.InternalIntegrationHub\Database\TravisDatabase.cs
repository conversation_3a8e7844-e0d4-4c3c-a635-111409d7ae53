using Microsoft.Data.SqlClient;
using Sleekflow.InternalIntegrationHub.Configs;
using Sleekflow.Models.Constants;

namespace Sleekflow.InternalIntegrationHub.Database;

public class TravisDatabase
{
    private readonly ITravisDatabaseConfigs _databaseConfigs;
    public SqlConnection? EasConnection;
    public SqlConnection? EusConnection;
    public SqlConnection? SeasConnection;
    public SqlConnection? UaenConnection;
    public SqlConnection? WeuConnection;

    public TravisDatabase(ITravisDatabaseConfigs databaseConfigs)
    {
        _databaseConfigs = databaseConfigs;
        EasConnection = _databaseConfigs.EasDbConnectionString == string.Empty
            ? null
            : new SqlConnection(_databaseConfigs.EasDbConnectionString);
        EusConnection = _databaseConfigs.EusDbConnectionString == string.Empty
            ? null
            : new SqlConnection(_databaseConfigs.EusDbConnectionString);
        SeasConnection = _databaseConfigs.SeasDbConnectionString == string.Empty
            ? null
            : new SqlConnection(_databaseConfigs.SeasDbConnectionString);
        UaenConnection = _databaseConfigs.UaenDbConnectionString == string.Empty
            ? null
            : new SqlConnection(_databaseConfigs.UaenDbConnectionString);
        WeuConnection = _databaseConfigs.WeuDbConnectionString == string.Empty
            ? null
            : new SqlConnection(_databaseConfigs.WeuDbConnectionString);
    }

    public SqlConnection? GetConnectionByRegion(string region)
    {
        var connection = region switch
        {
            ServerLocations.EastAsia => EasConnection,
            ServerLocations.EastUs => EusConnection,
            ServerLocations.SouthEastAsia => SeasConnection,
            ServerLocations.UaeNorth => UaenConnection,
            ServerLocations.WestEurope => WeuConnection,
            _ => EasConnection
        };
        return connection;
    }
}