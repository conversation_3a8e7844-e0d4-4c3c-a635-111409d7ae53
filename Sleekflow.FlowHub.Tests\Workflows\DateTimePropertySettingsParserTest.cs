using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;

namespace Sleekflow.FlowHub.Tests.Workflows;

[TestFixture]
public class DateTimePropertySettingsParserTest
{
    private WorkflowRecurringSettingsV2Parser _workflowRecurringSettingsV2Parser;
    private Mock<ILogger<DateTimePropertySettingsParser>> _loggerMock;
    private DateTimePropertySettingsParser _parser;

    [SetUp]
    public void Setup()
    {
        _workflowRecurringSettingsV2Parser = new WorkflowRecurringSettingsV2Parser(new Logger<WorkflowRecurringSettingsV2Parser>(new LoggerFactory()));
        _loggerMock = new Mock<ILogger<DateTimePropertySettingsParser>>();
        _parser = new DateTimePropertySettingsParser(_workflowRecurringSettingsV2Parser, _loggerMock.Object);
    }

    [Test]
    public void GetNextOccurrenceAfter_Should_According_To_Recurrring_When_Future()
    {
        // Arrange
        var originalDate = new DateTimeOffset(2023, 1, 15, 10, 0, 0, TimeSpan.Zero);
        var settings = new ContactPropertyDateTimeSettings(
            "abc",
            "before",
            [
                "5",
                "day"
            ],
            null,
            null);
        var recurringSettings = new WorkflowRecurringSettings(
            "weekly",
            null,
            1, // Daily interval
            null,
            null,
            "specific",
            new []{"monday"},
            null, null, null
        );
        var currentTime = new DateTimeOffset(2023, 1, 20, 0, 0, 0, TimeSpan.Zero);
        // Act
        var result = _parser.GetNextOccurrenceAfter(originalDate, settings, recurringSettings, currentTime);

        // Assert
        Assert.That(result, Is.EqualTo(new DateTimeOffset(2023, 1, 23, 10, 0, 0, TimeSpan.Zero)));
    }

    [Test]
    public void GetNextOccurrenceAfter_Not_In_Recurrring_Date()
    {
        // Arrange
        var originalDate = new DateTimeOffset(2023, 1, 15, 10, 0, 0, TimeSpan.Zero);
        var settings = new ContactPropertyDateTimeSettings(
            "abc",
            "after",
            [
                "5",
                "day"
            ],
            null,
            null);
        var recurringSettings = new WorkflowRecurringSettings(
            "weekly",
            null,
            1, // Daily interval
            null,
            null,
            "specific",
            new []{"monday"},
            null, null, null
        );
        var currentTime = new DateTimeOffset(2023, 1, 19, 20, 0, 0, TimeSpan.Zero);
        // Act
        var result = _parser.GetNextOccurrenceAfter(originalDate, settings, recurringSettings, currentTime);

        // Assert
        Assert.That(result, Is.EqualTo(new DateTimeOffset(2023, 1, 20, 10, 0, 0, TimeSpan.Zero)));
    }


    [Test]
    public void GetNextOccurrenceAfter_Should_Recurring_When_Current_Time_Eclipse()
    {
        // Arrange
        var originalDate = new DateTimeOffset(2023, 1, 15, 10, 0, 0, TimeSpan.Zero);
        var settings = new ContactPropertyDateTimeSettings(
            "abc",
            "after",
            [
                "5",
                "day"
            ],
            "custom",
            "2025-05-31T21:03:00.000Z");
        var recurringSettings = new WorkflowRecurringSettings(
            "weekly",
            null,
            1, // Daily interval
            null,
            null,
            "specific",
            new []{"monday"},
            null, null, null
        );
        var currentTime = new DateTimeOffset(2023, 1, 20, 23, 0, 0, TimeSpan.Zero);
        // Act
        var result = _parser.GetNextOccurrenceAfter(originalDate, settings, recurringSettings, currentTime);

        // Assert
        Assert.That(result, Is.EqualTo(new DateTimeOffset(2023, 1, 23, 21, 3, 0, TimeSpan.Zero)));
    }


    [Test]
    public void GetNextOccurrenceAfter_Should_MAX_DATE_TIME_IF_NO_RECURRING_AND_CURRENT_TIME_Eclipse()
    {
        // Arrange
        var originalDate = new DateTimeOffset(2023, 1, 15, 10, 0, 0, TimeSpan.Zero);
        var settings = new ContactPropertyDateTimeSettings(
            "abc",
            "after",
            [
                "1",
                "month"
            ],
            "custom",
            "2025-05-31T21:03:00.000Z");
        WorkflowRecurringSettings recurringSettings = null;
        var currentTime = new DateTimeOffset(2024, 1, 20, 23, 0, 0, TimeSpan.Zero);
        // Act
        var result = _parser.GetNextOccurrenceAfter(originalDate, settings, recurringSettings, currentTime);

        // Assert
        Assert.AreEqual(DateTimeOffset.MaxValue, result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Should_Future_Time_If_No_Recurring()
    {
        // Arrange
        var originalDate = new DateTimeOffset(2023, 1, 15, 10, 0, 0, TimeSpan.Zero);
        var settings = new ContactPropertyDateTimeSettings(
            "abc",
            "after",
            [
                "2",
                "year"
            ],
            "exactly",
            null);
        WorkflowRecurringSettings recurringSettings = null;
        var currentTime = new DateTimeOffset(2024, 1, 20, 23, 0, 0, TimeSpan.Zero);
        // Act
        var result = _parser.GetNextOccurrenceAfter(originalDate, settings, recurringSettings, currentTime);

        // Assert
        Assert.That(result, Is.EqualTo(new DateTimeOffset(2025, 1, 15, 10, 0, 0, TimeSpan.Zero)));
    }
}