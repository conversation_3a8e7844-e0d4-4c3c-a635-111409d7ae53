using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class BusinessHourConfigUpdatedSystemLogData
{
    [JsonProperty("is_enabled")]
    public bool IsEnabled { get; set; }

    [JsonProperty("weekly_hours")]
    public string WeeklyHours { get; set; }

    [JsonConstructor]
    public BusinessHourConfigUpdatedSystemLogData(bool isEnabled, string weeklyHours)
    {
        IsEnabled = isEnabled;
        WeeklyHours = weeklyHours;
    }
}