using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Shopify;
using Sleekflow.CommerceHub.Shopify;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.Stores.ShopifyStores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Shopify;

[TriggerGroup(ControllerNames.Shopify)]
public class CreateShopifyDraftOrder
    : ITrigger<
        CreateShopifyDraftOrder.CreateShopifyDraftOrderInput,
        CreateShopifyDraftOrder.CreateShopifyDraftOrderOutput>
{
    private readonly IStoreService _storeService;
    private readonly IShopifyStoreService _shopifyStoreService;
    private readonly IShopifyClientService _shopifyClientService;

    public CreateShopifyDraftOrder(
        IStoreService storeService,
        IShopifyStoreService shopifyStoreService,
        IShopifyClientService shopifyClientService)
    {
        _storeService = storeService;
        _shopifyStoreService = shopifyStoreService;
        _shopifyClientService = shopifyClientService;
    }

    public class CreateShopifyDraftOrderInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("shopify_store_id")]
        public string ShopifyStoreId { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("shopify_customer_email")]
        public string ShopifyCustomerEmail { get; set; }

        [Required]
        [JsonProperty("shopify_customer_phone_number")]
        public string ShopifyCustomerPhoneNumber { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("order_line_items")]
        public List<ShopifyDraftOrderLineItem> OrderLineItems { get; set; }

        [JsonProperty("order_note")]
        public string? OrderNote { get; set; }

        [JsonProperty("order_tags")]
        public string? OrderTags { get; set; }

        [JsonConstructor]
        public CreateShopifyDraftOrderInput(
            string shopifyStoreId,
            string sleekflowCompanyId,
            string shopifyCustomerEmail,
            string shopifyCustomerPhoneNumber,
            List<ShopifyDraftOrderLineItem> orderLineItems,
            string? orderNote,
            string? orderTags)
        {
            ShopifyStoreId = shopifyStoreId;
            SleekflowCompanyId = sleekflowCompanyId;
            ShopifyCustomerEmail = shopifyCustomerEmail;
            ShopifyCustomerPhoneNumber = shopifyCustomerPhoneNumber;
            OrderLineItems = orderLineItems;
            OrderNote = orderNote;
            OrderTags = orderTags;
        }
    }

    public class CreateShopifyDraftOrderOutput
    {
        [JsonProperty("order_note")]
        public string Note { get; set; }

        [JsonProperty("invoice_url")]
        public string InvoiceUrl { get; set; }

        [JsonConstructor]
        public CreateShopifyDraftOrderOutput(
            string note,
            string invoiceUrl)
        {
            Note = note;
            InvoiceUrl = invoiceUrl;
        }
    }

    public async Task<CreateShopifyDraftOrderOutput> F(
        CreateShopifyDraftOrderInput createShopifyDraftOrderInput)
    {
        var shopifyStore = await _storeService.GetStoreAsync(
            createShopifyDraftOrderInput.ShopifyStoreId,
            createShopifyDraftOrderInput.SleekflowCompanyId);

        var shopifyConfig = await _shopifyStoreService.GetShopifyStoreIntegrationExternalConfigAsync(shopifyStore);

        var shopifyDraftOrder = await _shopifyClientService.CreateDraftOrderAsync(
            shopifyConfig.ShopifyUrl!,
            shopifyConfig.AccessToken,
            createShopifyDraftOrderInput.ShopifyCustomerPhoneNumber,
            createShopifyDraftOrderInput.ShopifyCustomerEmail,
            createShopifyDraftOrderInput.OrderNote,
            createShopifyDraftOrderInput.OrderTags,
            createShopifyDraftOrderInput.OrderLineItems);

        return new CreateShopifyDraftOrderOutput(shopifyDraftOrder.Note, shopifyDraftOrder.InvoiceUrl);
    }
}