﻿using Newtonsoft.Json;

namespace Sleekflow.Outputs;

public class Output<T>
{
    public Output(
        bool success,
        int httpStatusCode,
        T data,
        string? message,
        int? errorCode,
        Dictionary<string, object?>? errorContext)
    {
        this.Success = success;
        this.Data = data;
        this.Message = message;
        this.DateTime = DateTimeOffset.UtcNow;
        this.HttpStatusCode = httpStatusCode;
        this.ErrorCode = errorCode;
        this.ErrorContext = errorContext;
    }

    [JsonConstructor]
    public Output(
        bool success,
        int httpStatusCode,
        T data,
        string? message,
        int? errorCode,
        Dictionary<string, object?>? errorContext,
        string requestId)
    {
        this.Success = success;
        this.Data = data;
        this.Message = message;
        this.DateTime = DateTimeOffset.UtcNow;
        this.HttpStatusCode = httpStatusCode;
        this.ErrorCode = errorCode;
        this.ErrorContext = errorContext;
        this.RequestId = requestId;
    }

    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("data")]
    public T Data { get; set; }

    [JsonProperty("message", NullValueHandling = NullValueHandling.Ignore)]
    public string? Message { get; set; }

    [JsonProperty("date_time")]
    public DateTimeOffset DateTime { get; set; }

    [JsonProperty("http_status_code")]
    public int HttpStatusCode { get; set; }

    [JsonProperty("error_code", NullValueHandling = NullValueHandling.Ignore)]
    public int? ErrorCode { get; set; }

    [JsonProperty("error_context", NullValueHandling = NullValueHandling.Ignore)]
    public Dictionary<string, object?>? ErrorContext { get; set; }

    [JsonProperty("request_id")]
    public string? RequestId { get; set; }
}