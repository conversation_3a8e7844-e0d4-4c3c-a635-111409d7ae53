﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Configs;

public interface ISecretConfig
{
    string SleekflowBackendAse256Key { get; }

    string FacebookAppId { get; }

    string FacebookAppSecret { get; }

    string FacebookBusinessId { get; }

    string FacebookSystemUserId { get; }

    string FacebookSystemUserAccessToken { get; }

    string FacebookBusinessCreditLineId { get; }

    string FacebookWebhookVerificationToken { get; }

    // For Encrypted Utils
    string FacebookWabaUserLongAccessTokenSecret { get; }

    string FacebookBusinessIntegrationSystemUserAccessTokenSecret { get; }

}

public class SecretConfig : IConfig, ISecretConfig
{
    public string SleekflowBackendAse256Key { get; }

    public string FacebookAppId { get; }

    public string FacebookAppSecret { get; }

    public string FacebookBusinessId { get; }

    public string FacebookSystemUserId { get; }

    public string FacebookSystemUserAccessToken { get; }

    public string FacebookBusinessCreditLineId { get; }

    public string FacebookWebhookVerificationToken { get; }

    public string FacebookWabaUserLongAccessTokenSecret { get; }

    public string FacebookBusinessIntegrationSystemUserAccessTokenSecret { get; }


    public SecretConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        SleekflowBackendAse256Key = GetEnvironmentVariable(target, "SLEEKFLOW_BACKEND_AES256_KEY");
        FacebookAppId = GetEnvironmentVariable(target, "FACEBOOK_APP_ID");
        FacebookAppSecret = GetEnvironmentVariable(target, "FACEBOOK_APP_SECRET");
        FacebookBusinessId = GetEnvironmentVariable(target, "FACEBOOK_BUSINESS_ID");
        FacebookSystemUserId = GetEnvironmentVariable(target, "FACEBOOK_SYSTEM_USER_ID");
        FacebookSystemUserAccessToken = GetEnvironmentVariable(target, "FACEBOOK_SYSTEM_USER_ACCESS_TOKEN");
        FacebookBusinessCreditLineId = GetEnvironmentVariable(target, "FACEBOOK_BUSINESS_CREDIT_LINE_ID");
        FacebookWebhookVerificationToken = GetEnvironmentVariable(target, "FACEBOOK_WEBHOOK_VERIFICATION_TOKEN");
        FacebookWabaUserLongAccessTokenSecret =
            GetEnvironmentVariable(target, "FACEBOOK_WABA_USER_LONG_ACCESS_TOKEN_SECRET");
        FacebookBusinessIntegrationSystemUserAccessTokenSecret = GetEnvironmentVariable(
            target,
            "FACEBOOK_BUSINESS_INTEGRATION_SYSTEM_USER_ACCESS_TOKEN_SECRET");
    }

    private string GetEnvironmentVariable(
        EnvironmentVariableTarget target,
        string environmentVariable,
        string? message = null)
    {
        return Environment.GetEnvironmentVariable(environmentVariable, target)
               ?? throw new SfMissingEnvironmentVariableException(message ?? environmentVariable);
    }
}