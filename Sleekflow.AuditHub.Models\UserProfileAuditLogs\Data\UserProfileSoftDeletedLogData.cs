﻿using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileSoftDeletedLogData
{
    [JsonProperty("trigger_details")]
    public UserProfileSoftDeletedTriggerLogData TriggerDetails { get; set; }

    [JsonConstructor]
    public UserProfileSoftDeletedLogData(UserProfileSoftDeletedTriggerLogData triggerDetails)
    {
        TriggerDetails = triggerDetails;
    }
}

public class UserProfileSoftDeletedTriggerLogData
{
    [JsonProperty("trigger_source")]
    public string TriggerSource { get; set; }

    [JsonProperty("staff_id")]
    public string? StaffId { get; set; }

    [JsonProperty("staff_name")]
    public string? StaffName { get; set; }

    [JsonConstructor]
    public UserProfileSoftDeletedTriggerLogData(
        string triggerSource,
        string? staffId,
        string? staffName)
    {
        TriggerSource = triggerSource;
        StaffId = staffId;
        StaffName = staffName;
    }
}