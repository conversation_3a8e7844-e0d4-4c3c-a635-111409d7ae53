﻿using System.Collections.Concurrent;
using System.Net;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.Models;
using Sleekflow.CrmHub.Providers.States;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions;
using Sleekflow.Models.Constants;
using Sleekflow.Outputs;
using Sleekflow.Utils;
using SearchObjectCondition = Sleekflow.CrmHub.Models.InflowActions.SearchObjectCondition;

namespace Sleekflow.CrmHub.Providers;

public interface IProviderService
{
    public Task<CreateObjectOutput> CreateObjectAsync(
        string sleekflowCompanyId,
        object? @object,
        string entityTypeName,
        string? crmHubObjectId = null);

    public Task DeleteObjectAsync(
        string sleekflowCompanyId,
        string providerObjectId,
        string entityTypeName,
        string crmHubObjectId);

    public Task<GetTypeFieldsOutput> GetTypeFieldsAsync(string sleekflowCompanyId, string entityTypeName);

    public Task<GetSupportedTypesOutput> GetSupportedTypesAsync(string sleekflowCompanyId);

    public Task InitTypeSyncAsync(string sleekflowCompanyId, string entityTypeName, SyncConfig? syncConfig);

    public Task<InitProviderOutput> InitProviderAsync(
        string sleekflowCompanyId,
        string returnToUrl,
        Dictionary<string, object?>? additionalDetails);

    public Task<InitProviderOutput> InitProviderAsync(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails);

    public Task DeactivateTypeSyncAsync(string sleekflowCompanyId, string entityTypeName);

    public Task ReauthenticateAsync(string sleekflowCompanyId);

    public Task SyncObjectAsync(string sleekflowCompanyId, string providerObjectId, string entityTypeName);

    public Task<SyncObjectsOutput> SyncObjectsAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters);

    public Task<GetSyncObjectsProgressOutput> GetSyncObjectsProgressAsync(
        string providerStateId,
        string sleekflowCompanyId);

    public Task<GetObjectsCountOutput> GetObjectsCountAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups);

    public Task<UpdateObjectOutput> UpdateObjectAsync(
        string sleekflowCompanyId,
        string providerObjectId,
        Dictionary<string, object?> dict,
        string entityTypeName,
        string? crmHubObjectId = null);

    public Task<UpdateObjectOutput> UpdateObjectV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        string? providerObjectId,
        Dictionary<string, object?> dict,
        string entityTypeName,
        string? crmHubObjectId = null,
        List<TypedId>? typedIds = null);

    public Task<string?> ResolveObjectIdAsync(
        Dictionary<string, object?> dict,
        string entityTypeName);

    public Task<PreviewObjectsOutput> PreviewObjectsAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters);

    public Task<PreviewObjectsV2Output> PreviewObjectsV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        string? nextRecordsUrl);

    public Task<SearchObjectsOutput> SearchObjectsAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        List<SearchObjectCondition>? conditions,
        List<TypedId>? typedIds = null);

    public Task<GetObjectDirectRefUrlOutput> GetObjectDirectRefUrl(
        string sleekflowCompanyId,
        string providerObjectId,
        string entityTypeName);

    public Task<GetCustomObjectTypesOutput> GetCustomObjectTypesAsync(
        string sleekflowCompanyId,
        string providerConnectionId);

    public Task<LoopThroughAndEnrollObjectsToFlowHubOutput>
        LoopThroughAndEnrollObjectsToFlowHubAsync(
            string sleekflowCompanyId,
            string providerConnectionId,
            string entityTypeName,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId,
            bool isCustomObject);

    public Task<GetLoopThroughObjectsProgressOutput?> GetLoopThroughObjectsProgressAsync(
        string sleekflowCompanyId,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId);

    Task<bool> TerminateInProgressLoopThroughExecutionAsync(
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId);

    Task<List<string>> TerminateInProgressLoopThroughExecutionsAsync(
        string flowHubWorkflowId,
        string sleekflowCompanyId);

    public Task<GetProviderConnectionsOutput> GetProviderConnectionsAsync(
        string sleekflowCompanyId);

    public Task<ReInitProviderConnectionOutput> ReInitProviderConnectionAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string successUrl,
        string failureUrl);

    public Task<RenameProviderConnectionOutput> RenameProviderConnectionAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string name);

    public Task DeleteProviderConnectionAsync(
        string providerConnectionId,
        string sleekflowCompanyId);

    public Task InitTypeSyncAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        int? syncInterval,
        bool? isFlowsBased,
        List<TypedId>? typedIds);

    public Task<GetProviderSubscriptionsOutput> GetProviderSubscriptionsAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        List<TypedId>? typedIds);

    public Task<CreateObjectOutput> CreateObjectV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        object? @object,
        string entityTypeName,
        List<TypedId>? typedIds = null);

    public Task<GetTypeFieldsOutput> GetTypeFieldsV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        List<TypedId>? typedIds,
        string entityTypeName);

    public Task<PrepareMigrationToFlowHubOutput> PrepareMigrationToFlowHubAsync(
        string sleekflowCompanyId,
        List<string> entityTypesToMigrate);

    public Task<GetProviderUserMappingConfigOutput> GetProviderUserMappingConfigAsync(
        string sleekflowCompanyId,
        string providerConnectionId);

    public Task<CreateProviderUserMappingConfigOutput> CreateProviderUserMappingConfigAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        List<UserMapping>? userMappings);

    public Task<UpdateProviderUserMappingConfigOutput> UpdateProviderUserMappingConfigAsync(
        string providerUserMappingConfigId,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings);

    public Task<GetProviderConnectionExternalResourcesOutput> GetProviderConnectionExternalResourcesAsync(
        string sleekflowCompanyId,
        string providerConnectionId);
}

public class GenericProviderService : IProviderService
{
    private readonly IProviderStateService _providerStateService;
    private readonly ILoopThroughObjectsProgressStateService _loopThroughObjectsProgressStateService;
    private readonly HttpClient _httpClient;
    private readonly ICustomSyncConfigService _customSyncConfigService;
    private readonly ILogger<GenericProviderService> _logger;
    private readonly string _providerName;
    private readonly string _endpoint;

    public GenericProviderService(
        IProviderStateService providerStateService,
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        HttpClient httpClient,
        ICustomSyncConfigService customSyncConfigService,
        string providerName,
        string endpoint,
        ILogger<GenericProviderService> logger)
    {
        _providerStateService = providerStateService;
        _loopThroughObjectsProgressStateService = loopThroughObjectsProgressStateService;
        _providerName = providerName;
        _customSyncConfigService = customSyncConfigService;
        _endpoint = endpoint;
        _httpClient = httpClient;
        _logger = logger;
    }

    public virtual async Task<CreateObjectOutput> CreateObjectAsync(
        string sleekflowCompanyId,
        object? @object,
        string entityTypeName,
        string? crmHubObjectId = null)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                dict = @object,
                entity_type_name = entityTypeName,
                crm_hub_object_id = crmHubObjectId,
            });

        var (resMsg, resStr, output) = await PostAsync<CreateObjectOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/CreateObject");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data!;
    }

    public virtual async Task DeleteObjectAsync(
        string sleekflowCompanyId,
        string providerObjectId,
        string entityTypeName,
        string crmHubObjectId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                object_id = providerObjectId,
                entity_type_name = entityTypeName,
                crm_hub_object_id = crmHubObjectId,
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/DeleteObject");
    }

    public virtual async Task<GetObjectsCountOutput> GetObjectsCountAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups)
    {
        if (filterGroups is not null)
        {
            _customSyncConfigService.OverrideSyncConfigFilterGroups(filterGroups, sleekflowCompanyId, _providerName, entityTypeName);
        }

        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                entity_type_name = entityTypeName,
                filter_groups = filterGroups,
            });

        var (_, _, output) = await PostAsync<Dictionary<string, object?>?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetObjectsCount");
        if (output.Data == null || output.Data!["count"] == null)
        {
            return new GetObjectsCountOutput(0);
        }

        return new GetObjectsCountOutput((long) output.Data!["count"]!);
    }

    public virtual async Task<GetSupportedTypesOutput> GetSupportedTypesAsync(string sleekflowCompanyId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
            });

        var (resMsg, resStr, output) = await PostAsync<GetSupportedTypesOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetSupportedTypes");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<GetTypeFieldsOutput> GetTypeFieldsAsync(
        string sleekflowCompanyId,
        string entityTypeName)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId, entity_type_name = entityTypeName,
            });

        var (resMsg, resStr, output) = await PostAsync<GetTypeFieldsOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetTypeFields");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data!;
    }

    public virtual async Task<InitProviderOutput> InitProviderAsync(
        string sleekflowCompanyId,
        string returnToUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                return_to_url = returnToUrl,
                additional_details = additionalDetails
            });

        var (resMsg, resStr, output) = await PostAsync<dynamic>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/InitProvider");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return new InitProviderOutput(
            providerName: _providerName,
            context: output.Data);
    }

    public virtual async Task<InitProviderOutput> InitProviderAsync(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                success_url = successUrl,
                failure_url = failureUrl,
                additional_details = additionalDetails
            });

        var (resMsg, resStr, output) = await PostAsync<dynamic>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/InitProviderV2");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return new InitProviderOutput(
            providerName: _providerName,
            context: output.Data);
    }

    public virtual async Task DeactivateTypeSyncAsync(
        string sleekflowCompanyId,
        string entityTypeName)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId, entity_type_name = entityTypeName,
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/DeactivateTypeSync");
    }

    public virtual async Task InitTypeSyncAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        SyncConfig? syncConfig)
    {
        if (syncConfig is not null)
        {
            _customSyncConfigService.OverrideSyncConfig(syncConfig, sleekflowCompanyId, _providerName, entityTypeName);
        }

        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId, entity_type_name = entityTypeName, sync_config = syncConfig
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/InitTypeSync");
    }

    public virtual async Task ReauthenticateAsync(string sleekflowCompanyId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/ReAuthenticate");
    }

    public virtual async Task SyncObjectAsync(string sleekflowCompanyId, string providerObjectId, string entityTypeName)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                object_id = providerObjectId,
                entity_type_name = entityTypeName
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/SyncObject");
    }

    public virtual async Task<SyncObjectsOutput> SyncObjectsAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters)
    {
        if (filterGroups is not null)
        {
            _customSyncConfigService.OverrideSyncConfigFilterGroups(filterGroups, sleekflowCompanyId, _providerName, entityTypeName);
        }

        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                entity_type_name = entityTypeName,
                filter_groups = filterGroups,
                field_filters = fieldFilters,
            });

        var (resMsg, resStr, output) = await PostAsync<DurablePayload>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/SyncObjects");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        var providerStateId = await _providerStateService.CreateSyncObjectsProgressStateAsync(
            output.Data,
            _providerName,
            sleekflowCompanyId);

        return new SyncObjectsOutput(providerStateId);
    }

    public virtual async Task<GetSyncObjectsProgressOutput> GetSyncObjectsProgressAsync(
        string providerStateId,
        string sleekflowCompanyId)
    {
        var providerState = await _providerStateService.GetStateAsync<SyncObjectsProgressState>(
            providerStateId,
            _providerName,
            sleekflowCompanyId);
        if (providerState == null)
        {
            throw new SfInternalErrorException(
                $"The ProviderStateId is invalid. providerStateId {providerStateId}");
        }

        var stateObj = providerState.StateObj;

        // If the Durable Completed, Return the cached
        if (stateObj.QueryOutput != null && stateObj.QueryOutput.RuntimeStatus == "Completed")
        {
            return new GetSyncObjectsProgressOutput(
                stateObj.QueryOutput.CustomStatus.Count,
                stateObj.QueryOutput.CustomStatus.LastUpdateTime,
                stateObj.QueryOutput.RuntimeStatus);
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(stateObj.DurablePayload.StatusQueryGetUri),
        };
        var resMsg = await _httpClient.SendAsync(requestMessage);
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var statusQueryGetOutput = resStr.ToObject<StatusQueryGetOutput<
            StatusQueryGetOutputInput,
            StatusQueryGetOutputCustomStatus,
            StatusQueryGetOutputOutput>>();
        if (statusQueryGetOutput == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, durablePayload.StatusQueryGetUri {stateObj.DurablePayload.StatusQueryGetUri} is not working");
        }

        var isStatusUpdated = string.Equals(
                                  stateObj.QueryOutput?.RuntimeStatus,
                                  statusQueryGetOutput.RuntimeStatus,
                                  StringComparison.Ordinal)
                              == false;
        if (isStatusUpdated)
        {
            await _providerStateService.PatchSyncObjectsProgressStateAsync(
                providerState.Id,
                sleekflowCompanyId,
                statusQueryGetOutput);
        }

        if (statusQueryGetOutput.RuntimeStatus == "Pending")
        {
            return new GetSyncObjectsProgressOutput(
                0,
                DateTime.UtcNow,
                statusQueryGetOutput.RuntimeStatus);
        }

        return new GetSyncObjectsProgressOutput(
            statusQueryGetOutput.CustomStatus.Count,
            statusQueryGetOutput.CustomStatus.LastUpdateTime,
            statusQueryGetOutput.RuntimeStatus);
    }

    public virtual async Task<UpdateObjectOutput> UpdateObjectAsync(
        string sleekflowCompanyId,
        string providerObjectId,
        Dictionary<string, object?> dict,
        string entityTypeName,
        string? crmHubObjectId = null)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                object_id = providerObjectId,
                dict = dict,
                entity_type_name = entityTypeName,
                crm_hub_object_id = crmHubObjectId,
            });

        var (resMsg, resStr, output) = await PostAsync<UpdateObjectOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/UpdateObject");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<UpdateObjectOutput> UpdateObjectV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        string? providerObjectId,
        Dictionary<string, object?> dict,
        string entityTypeName,
        string? crmHubObjectId = null,
        List<TypedId>? typedIds = null)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                object_id = providerObjectId,
                dict = dict,
                entity_type_name = entityTypeName,
                crm_hub_object_id = crmHubObjectId,
                typed_ids = typedIds,
            });

        var (resMsg, resStr, output) = await PostAsync<UpdateObjectOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/UpdateObjectV2");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<string?> ResolveObjectIdAsync(Dictionary<string, object?> dict, string entityTypeName)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                dict = dict, entity_type_name = entityTypeName,
            });

        var (resMsg, resStr, output) = await PostAsync<ResolveObjectIdOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/ResolveObjectId");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data.Id;
    }

    public virtual async Task<PreviewObjectsOutput> PreviewObjectsAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters)
    {
        if (filterGroups is not null)
        {
            _customSyncConfigService.OverrideSyncConfigFilterGroups(filterGroups, sleekflowCompanyId, _providerName, entityTypeName);
        }

        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                entity_type_name = entityTypeName,
                filter_groups = filterGroups,
                field_filters = fieldFilters,
            });

        var (resMsg, resStr, output) = await PostAsync<PreviewObjectsOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/PreviewObjects");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<PreviewObjectsV2Output> PreviewObjectsV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        string? nextRecordsUrl)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                entity_type_name = entityTypeName,
                filter_groups = filterGroups,
                field_filters = fieldFilters,
                next_records_url = nextRecordsUrl,
            });

        var (resMsg, resStr, output) = await PostAsync<PreviewObjectsV2Output>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/PreviewObjectsV2");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<SearchObjectsOutput> SearchObjectsAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        List<SearchObjectCondition>? conditions,
        List<TypedId>? typedIds = null)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                entity_type_name = entityTypeName,
                conditions = conditions,
                typed_ids = typedIds,
            });

        var (resMsg, resStr, output) = await PostAsync<SearchObjectsOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/SearchObjects");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<GetObjectDirectRefUrlOutput> GetObjectDirectRefUrl(
        string sleekflowCompanyId,
        string providerObjectId,
        string entityTypeName)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                entity_type_name = entityTypeName,
                object_id = providerObjectId,
            });

        var (resMsg, resStr, output) = await PostAsync<GetObjectDirectRefUrlOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetObjectDirectRefUrl");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    protected static async Task<(HttpResponseMessage ResMsg, string ResStr, Output<T?> Output)> PostAsync<T>(
        HttpClient httpClient,
        string inputJsonStr,
        string uriStr)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(uriStr),
        };
        var resMsg = await httpClient.SendAsync(requestMessage);
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<JObject>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.ContainsKey("success") == false || output.GetValue("success")!.Value<bool>() == false)
        {
            throw new ErrorCodeException(output.ToObject<Output<dynamic>>());
        }

        try
        {
            return (resMsg, resStr, output.ToObject<Output<T?>>()!);
        }
        catch (Exception e)
        {
            throw new SfInternalErrorException(
                e,
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }
    }

    public virtual async Task<GetCustomObjectTypesOutput> GetCustomObjectTypesAsync(
        string sleekflowCompanyId,
        string providerConnectionId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId
            });

        var (resMsg, resStr, output) = await PostAsync<GetCustomObjectTypesOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetCustomObjectTypes");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<LoopThroughAndEnrollObjectsToFlowHubOutput>
        LoopThroughAndEnrollObjectsToFlowHubAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        bool isCustomObject)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                entity_type_name = entityTypeName,
                is_custom_object = isCustomObject,
                flow_hub_workflow_id = flowHubWorkflowId,
                flow_hub_workflow_versioned_id = flowHubWorkflowVersionedId,
            });

        var (resMsg, resStr, output) = await PostAsync<DurablePayload>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/LoopThroughAndEnrollObjectsToFlowHub");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        var loopThroughObjectsProgressStateId =
            await _loopThroughObjectsProgressStateService.CreateOrUpdateLoopThroughObjectsProgressStateAsync(
            output.Data,
            _providerName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            sleekflowCompanyId);

        return new LoopThroughAndEnrollObjectsToFlowHubOutput(loopThroughObjectsProgressStateId);
    }

    public virtual async Task<GetLoopThroughObjectsProgressOutput?> GetLoopThroughObjectsProgressAsync(
        string sleekflowCompanyId,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        var state = await _loopThroughObjectsProgressStateService.GetStateAsync<GetLoopThroughObjectsProgressOutput?>(
            providerName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            sleekflowCompanyId);

        if (state is null)
        {
            return null;
        }

        var stateObj = state.StateObj;

        // If the Durable Completed, Return the cached
        if (stateObj.QueryOutput?.RuntimeStatus is "Completed")
        {
            return new GetLoopThroughObjectsProgressOutput(
                stateObj.QueryOutput.CustomStatus.Count,
                stateObj.QueryOutput.CustomStatus.LastUpdateTime,
                stateObj.QueryOutput.RuntimeStatus);
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(stateObj.DurablePayload.StatusQueryGetUri),
        };
        var resMsg = await _httpClient.SendAsync(requestMessage);
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var statusQueryGetOutput = ConstructStatusQueryGetOutput(resStr);
        if (statusQueryGetOutput == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, durablePayload.StatusQueryGetUri {stateObj.DurablePayload.StatusQueryGetUri} is not working");
        }

        var isStatusUpdated = string.Equals(
                                  stateObj.QueryOutput?.RuntimeStatus,
                                  statusQueryGetOutput.RuntimeStatus,
                                  StringComparison.Ordinal)
                              == false;
        if (isStatusUpdated)
        {
            await _loopThroughObjectsProgressStateService.PatchLoopThroughObjectsProgressStateAsync(
                state.Id,
                sleekflowCompanyId,
                statusQueryGetOutput);
        }

        if (statusQueryGetOutput.RuntimeStatus == "Pending")
        {
            return new GetLoopThroughObjectsProgressOutput(
                0,
                DateTime.UtcNow,
                statusQueryGetOutput.RuntimeStatus);
        }

        return new GetLoopThroughObjectsProgressOutput(
            statusQueryGetOutput.CustomStatus.Count,
            statusQueryGetOutput.CustomStatus.LastUpdateTime,
            statusQueryGetOutput.RuntimeStatus);
    }

    public async Task<bool> TerminateInProgressLoopThroughExecutionAsync(
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId)
    {
        var state = await _loopThroughObjectsProgressStateService.GetStateAsync<LoopThroughObjectsProgressState?>(
            _providerName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            sleekflowCompanyId);

        var isTerminated = state is null || await TerminateInprogressLoopThroughExecutionAsync(state);

        return isTerminated;
    }

    public async Task<List<string>> TerminateInProgressLoopThroughExecutionsAsync(
        string flowHubWorkflowId,
        string sleekflowCompanyId)
    {
        var states = await _loopThroughObjectsProgressStateService.GetInProgressLoopThroughObjectsProgressStatesAsync(
            _providerName,
            flowHubWorkflowId,
            sleekflowCompanyId);

        var terminatedStateIds = new ConcurrentBag<string>();
        await Parallel.ForEachAsync(
            states,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3
            },
            async (state, _) =>
            {
                var isTerminated = await TerminateInprogressLoopThroughExecutionAsync(state);

                if (isTerminated)
                {
                    terminatedStateIds.Add(state.Id);
                }
            });

        if (terminatedStateIds.Count > 0)
        {
            _logger.LogInformation(
                "Terminated {Count} inprogress loop through executions: {CompanyId} {WorkflowId} - {StateIds}",
                terminatedStateIds.Count,
                sleekflowCompanyId,
                flowHubWorkflowId,
                terminatedStateIds);
        }

        return terminatedStateIds.ToList();
    }

    public virtual async Task<GetProviderConnectionsOutput> GetProviderConnectionsAsync(
        string sleekflowCompanyId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
            });

        var (resMsg, resStr, output) = await PostAsync<GetProviderConnectionsOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetConnections");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<ReInitProviderConnectionOutput> ReInitProviderConnectionAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string successUrl,
        string failureUrl)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                success_url = successUrl,
                failure_url = failureUrl
            });

        var (resMsg, resStr, output) = await PostAsync<ReInitProviderConnectionOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/ReInitConnection");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<RenameProviderConnectionOutput> RenameProviderConnectionAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string name)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                name = name
            });

        var (resMsg, resStr, output) = await PostAsync<RenameProviderConnectionOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/RenameConnection");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task DeleteProviderConnectionAsync(
        string providerConnectionId,
        string sleekflowCompanyId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
            });

        var (resMsg, resStr, output) = await PostAsync<ProviderConnectionDto>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/DeleteConnection");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }
    }

    public virtual async Task InitTypeSyncAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        int? syncInterval,
        bool? isFlowsBased,
        List<TypedId>? typedIds)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                entity_type_name = entityTypeName,
                sync_interval = syncInterval,
                is_flows_based = isFlowsBased,
                typed_ids = typedIds
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/InitTypeSyncV2");
    }

    public virtual async Task<GetProviderSubscriptionsOutput> GetProviderSubscriptionsAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        string entityTypeName,
        List<TypedId>? typedIds)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                entity_type_name = entityTypeName,
                typed_ids = typedIds
            });

        var (resMsg, resStr, output) = await PostAsync<GetProviderSubscriptionsOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetSubscriptions");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<CreateObjectOutput> CreateObjectV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        object? @object,
        string entityTypeName,
        List<TypedId>? typedIds = null)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                dict = @object,
                entity_type_name = entityTypeName,
                typed_ids = typedIds
            });

        var (resMsg, resStr, output) = await PostAsync<CreateObjectOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/CreateObjectV2");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data!;
    }

    public virtual async Task<GetTypeFieldsOutput> GetTypeFieldsV2Async(
        string sleekflowCompanyId,
        string providerConnectionId,
        List<TypedId>? typedIds,
        string entityTypeName)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                entity_type_name = entityTypeName,
                typed_ids = typedIds
            });

        var (resMsg, resStr, output) = await PostAsync<GetTypeFieldsOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetTypeFieldsV2");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data!;
    }

    public virtual async Task<PrepareMigrationToFlowHubOutput> PrepareMigrationToFlowHubAsync(
        string sleekflowCompanyId,
        List<string> entityTypesToMigrate)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                entity_types_to_migrate = entityTypesToMigrate,
            });

        var (resMsg, resStr, output) = await PostAsync<PrepareMigrationToFlowHubOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Migrations/PrepareMigrationToFlowHub");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data!;
    }

    public virtual async Task<GetProviderUserMappingConfigOutput> GetProviderUserMappingConfigAsync(
        string sleekflowCompanyId,
        string providerConnectionId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId
            });

        var (resMsg, resStr, output) = await PostAsync<GetProviderUserMappingConfigOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetUserMappingConfig");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<CreateProviderUserMappingConfigOutput> CreateProviderUserMappingConfigAsync(
        string sleekflowCompanyId,
        string providerConnectionId,
        List<UserMapping>? userMappings)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId,
                user_mappings = userMappings
            });

        var (resMsg, resStr, output) = await PostAsync<CreateProviderUserMappingConfigOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/CreateUserMappingConfig");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<UpdateProviderUserMappingConfigOutput> UpdateProviderUserMappingConfigAsync(
        string providerUserMappingConfigId,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                user_mapping_config_id = providerUserMappingConfigId,
                sleekflow_company_id = sleekflowCompanyId,
                user_mappings = userMappings
            });

        var (resMsg, resStr, output) = await PostAsync<UpdateProviderUserMappingConfigOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/UpdateUserMappingConfig");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    public virtual async Task<GetProviderConnectionExternalResourcesOutput> GetProviderConnectionExternalResourcesAsync(
        string sleekflowCompanyId,
        string providerConnectionId)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId,
                connection_id = providerConnectionId
            });

        var (resMsg, resStr, output) = await PostAsync<GetProviderConnectionExternalResourcesOutput>(
            _httpClient,
            inputJsonStr,
            _endpoint + "/Integrations/GetConnectionExternalResources");
        if (output.Data == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        return output.Data;
    }

    private async Task<bool> TerminateInprogressLoopThroughExecutionAsync(LoopThroughObjectsProgressState state)
    {
        // early return if the state is not found or the state is already terminal
        if (await IsLoopThroughObjectsExecutionFinished(state))
        {
            return true;
        }

        var res = false;
        try
        {
            await TerminateDurableFunctionAsync(state.StateObj.DurablePayload);
            res = true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to terminate durable function: {CompanyId} {WorkflowId} {WorkflowVersionedId}",
                state.SleekflowCompanyId,
                state.FlowHubWorkflowId,
                state.FlowHubWorkflowVersionedId);
        }

        return res;
    }

    private async Task<bool> IsLoopThroughObjectsExecutionFinished(LoopThroughObjectsProgressState state)
    {
        bool res;

        var cachedRuntimeStatus = state.StateObj.QueryOutput?.RuntimeStatus;
        if (_loopThroughObjectsProgressStateService.IsTerminalRuntimeStatus(cachedRuntimeStatus))
        {
            res = true;
        }
        else
        {
            var realtimeRuntimeStatus = (await GetLoopThroughObjectsProgressAsync(
                    state.SleekflowCompanyId,
                    state.ProviderName,
                    state.FlowHubWorkflowId,
                    state.FlowHubWorkflowVersionedId))!
                .Status;

            res = _loopThroughObjectsProgressStateService.IsTerminalRuntimeStatus(realtimeRuntimeStatus);
        }

        return res;
    }

    private async Task TerminateDurableFunctionAsync(DurablePayload durablePayload)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(durablePayload.TerminatePostUri),
        };
        var resMsg = await _httpClient.SendAsync(requestMessage);
        var resStr = await resMsg.Content.ReadAsStringAsync();

        if (!resMsg.IsSuccessStatusCode
            && resMsg.StatusCode != HttpStatusCode.Gone)
        {
            throw new SfInternalErrorException(
                $"Failed to terminate durable function. Status code: {resMsg.StatusCode}, Response: {resStr}");
        }
    }

    private static StatusQueryGetOutput<
        StatusQueryGetOutputInput,
        StatusQueryGetOutputCustomStatus,
        StatusQueryGetOutputOutput?>? ConstructStatusQueryGetOutput(string resStr)
    {
        var statusQueryGetOutput = resStr.ToObject<StatusQueryGetOutput<
            StatusQueryGetOutputInput,
            StatusQueryGetOutputCustomStatus,
            StatusQueryGetOutputOutput?>>();

        // if deserialization failed,
        // We check if the durable function is terminated or cancelled etc,
        // because Azure may return ["{{text}}"] as the value of [output]
        // In this scenario, we manually construct the object with output assigned to null
        if (statusQueryGetOutput is null)
        {
            var statusQueryGetOutputWithoutOutput = resStr.ToObject<StatusQueryGetOutput<
                StatusQueryGetOutputInput,
                StatusQueryGetOutputCustomStatus,
                object?>>();

            if (statusQueryGetOutputWithoutOutput is
                {
                    RuntimeStatus: AzureDurableFunctionRuntimeStatuses.Terminated
                    or AzureDurableFunctionRuntimeStatuses.Canceled
                    or AzureDurableFunctionRuntimeStatuses.Failed
                    or AzureDurableFunctionRuntimeStatuses.Suspended
                })
            {
                statusQueryGetOutput =
                    new StatusQueryGetOutput<StatusQueryGetOutputInput, StatusQueryGetOutputCustomStatus, StatusQueryGetOutputOutput?>(
                        statusQueryGetOutputWithoutOutput.Name,
                        statusQueryGetOutputWithoutOutput.InstanceId,
                        statusQueryGetOutputWithoutOutput.RuntimeStatus,
                        statusQueryGetOutputWithoutOutput.Input,
                        statusQueryGetOutputWithoutOutput.CustomStatus,
                        null, // set to null
                        statusQueryGetOutputWithoutOutput.CreatedTime,
                        statusQueryGetOutputWithoutOutput.LastUpdatedTime);
            }
        }

        return statusQueryGetOutput;
    }
}