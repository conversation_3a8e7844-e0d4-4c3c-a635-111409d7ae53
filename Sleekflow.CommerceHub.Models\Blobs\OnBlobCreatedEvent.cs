using Sleekflow.Events;

namespace Sleekflow.CommerceHub.Models.Blobs;

public class OnBlobCreatedEvent : IEvent
{
    /// <summary>
    /// Gets or sets the property stores the event belongs to which sleekflow company.
    /// </summary>
    public string SleekflowCompanyId { get; set; }

    /// <summary>
    /// Gets or sets the property stores the event belongs to which sleekflow company.
    /// </summary>
    public string ContainerName { get; set; }

    /// <summary>
    /// Gets or sets the property stores the blob id.
    /// </summary>
    public string BlobId { get; set; }

    /// <summary>
    /// Gets or sets the property stores the relative action type.
    /// </summary>
    public string BlobType { get; set; }

    public OnBlobCreatedEvent(
        string sleekflowCompanyId,
        string containerName,
        string blobId,
        string blobType)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ContainerName = containerName;
        BlobId = blobId;
        BlobType = blobType;
    }
}