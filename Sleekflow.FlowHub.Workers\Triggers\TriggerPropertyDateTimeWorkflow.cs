using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers;

public class TriggerPropertyDateTimeWorkflow
{
    private readonly ILogger<TriggerPropertyDateTimeWorkflow> _logger;

    public TriggerPropertyDateTimeWorkflow(ILogger<TriggerPropertyDateTimeWorkflow> logger)
    {
        _logger = logger;
    }

    [Function("TriggerPropertyDateTimeWorkflow")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient client)
    {
        return await Func.Run2Async<TriggerPropertyWorkflowInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            client,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (TriggerPropertyWorkflowInput Input, ILogger Logger, DurableTaskClient Starter) context)
    {
        var (input, logger, starter) = context;
        _logger.LogInformation(
            "Received property datetime trigger for Workflow {WorkflowId}, Version {Version}. Input: {Input}",
            input.WorkflowId,
            input.WorkflowVersion,
            JsonConvert.SerializeObject(input));

        var orchestratorName = "PropertyDateTimeWorkflow_Orchestrator";

        // start orchestrator immediately
        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            orchestratorName,
            input);

        _logger.LogInformation(
            "Started {OrchestratorName} with InstanceId: {InstanceId}",
            orchestratorName,
            instanceId);

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);

        if (httpManagementPayload == null)
        {
            _logger.LogError("failed to create http management payload");
            throw new Exception("Unable to get ExecuteSleepStep_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}