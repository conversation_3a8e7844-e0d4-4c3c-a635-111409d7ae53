using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Orders.ShopifyOrders;

public class ShopifyOrderExternalIntegrationInfo : OrderExternalIntegrationInfo
{
    [JsonProperty("order_name")]
    public string OrderName { get; set; }

    [JsonProperty("order_note")]
    public string OrderNote { get; set; }

    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("shopify_customer_id")]
    public string? ShopifyCustomerId { get; set; }

    [JsonProperty("shopify_order_status")]
    public string ShopifyOrderStatus { get; set; }

    [JsonProperty("shopify_payment_status")]
    public string ShopifyPaymentStatus { get; set; }

    [JsonProperty("fulfillment_status")]
    public string FulfillmentStatus { get; set; }

    [JsonProperty("conversion_status")]
    public string ConversionStatus { get; set; }

    [JsonProperty("cart_token")]
    public string CartToken { get; set; }

    [JsonProperty("sleekflow_platform_country")]
    public string? SleekflowPlatformCountry { get; set; }

    [JsonProperty("total_tax")]
    public decimal? TotalTax { get; set; }

    [JsonProperty("tags")]
    public List<string>? Tags { get; set; }

    [JsonConstructor]
    public ShopifyOrderExternalIntegrationInfo(
        string orderName,
        string orderNote,
        string url,
        string? shopifyCustomerId,
        string shopifyOrderStatus,
        string shopifyPaymentStatus,
        string fulfillmentStatus,
        string conversionStatus,
        string cartToken,
        string? sleekflowPlatformCountry,
        decimal? totalTax,
        List<string>? tags,
        string providerOrderId)
        : base("shopify", providerOrderId)
    {
        OrderName = orderName;
        OrderNote = orderNote;
        Url = url;
        ShopifyCustomerId = shopifyCustomerId;
        ShopifyOrderStatus = shopifyOrderStatus;
        ShopifyPaymentStatus = shopifyPaymentStatus;
        FulfillmentStatus = fulfillmentStatus;
        ConversionStatus = conversionStatus;
        CartToken = cartToken;
        SleekflowPlatformCountry = sleekflowPlatformCountry;
        TotalTax = totalTax;
        Tags = tags;
    }
}