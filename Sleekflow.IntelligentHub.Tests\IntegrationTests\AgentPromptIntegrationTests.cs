using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Tests.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class AgentPromptIntegrationTests
{
    private string sleekflowBowtieSource =
        "[RS_sales briefing_Bowtie_20241204.docx.pdf]: ACQ MS MKT 新增 香港寬頻x Bowtie 四合一醫療服務計劃\n香港寬頻市務部與Bowtie將於2024年6月27日推出全新「四合一醫療服務計劃」,免診 金、免排隊、免藥費及包醫生紙,讓客戶以超值價格獲得全面的醫療保障。低至每月$99 就可享12個月計劃期內無限次普通科西醫視像會診、洗牙及全面牙科檢查、全身檢查 及流感疫苗接種*,新客戶及現有客戶同樣可享計劃內高達$3,500醫療保健服務。\n\u25cf 新客戶需登記指定寬頻或流動通訊計劃,才可以$99加購「四合一醫療服務計 劃」 AS/MS optional bundle\n\u25cf 現有客戶 可以$99登記「四合一醫療服務計劃」EC upsell\n注意:每個PPS只限登記此服務5次。合約期12個月,合約期後自動取消。\n計劃詳情如下:\n*Bowtie 四合一醫療服務計劃:\n1)「Bowtie 四合一醫療保健服務,包無限次普通科西醫視像會診、2次洗牙及全面牙科 檢查、1次全身檢查、1次流感疫苗接種」月費$99(12個月合約)\n有關計劃詳情,請參閱下表:\nBowtie 四合一醫療服務計劃:\nBowtie 提供一項名為「四合一醫療服務計劃」的套餐，其月費僅為 $99。在合約期內，該計劃的行政費將被豁免，使得您可以享受更具成本效益的醫療服務。這項計劃的合約期為12個月，期間Bowtie將為用戶提供全方位的支持。此外，計劃還包括了推遲生效日期（Defer Start bill）的靈活選項，這意味著您可以選擇計劃自1天後或30天後開始生效，以便根據您的個人需求進行調整。\n合約期後月費\n自動取消\nBowtie Healthcare Service熱門常見問題\nFAQ\n在這份問答指南中，圍繞著由保泰人壽保險有限公司推出的香港寬頻 x Bowtie 四合一醫療服務計劃提供了詳細的資訊。這家公司是香港第一家獲保監局發牌的虛擬保險公司，您可瀏覽其網頁以了解更多內容。這項計劃並不是保險產品，而是一個健康會員計劃，旨在提供一系列健康產品和服務，滿足不同健康需求。\n\n關於遙距視像會診，這項服務適用於症狀輕微及非緊急的病患，如果有嚴重問題，需要即刻聯絡緊急服務。隨著科技的進步，這種會診方式在國外越來越普及，並且香港的多家公私營機構也開始提供類似的服務。MyDoc 通過擁有專業學歷和資格的註冊醫生，幫助患者足不出戶便能獲得專業的意見和治療，並保証其資料的保密性。\n\n遙距視像會診的服務時間為週一至週五，上午9時至下午7時，和週六上午9時至下午1時，但不涵蓋公眾假期以及在黑色暴雨警告或八號或以上颱風信號生效期間。這項服務對象的年齡限制為18至80歲，基本上涵蓋了廣泛的人群。\n\n若在遙距視像會診後需要拿取處方藥物，MyDoc 會安排免費的藥物快遞服務給予病人，但僅適用於香港地區。這樣的設計，讓整個遙距醫療從諮詢到藥物獲取的過程都變得極為方便。\n當您需要知道藥物何時可以送達時，請注意，除公眾假期以及黑色暴雨警告或八號或以上颱風信號在香港生效的日子外，正常的送貨服務時間是星期一到星期五的上午9:00至晚上11:00，以及星期六的上午9:00至下午5:00。需要冷藏的藥物或位於離島區及一些需要特別政府許可才能進入的邊境禁區，如沙頭角、打鼓嶺、落馬洲等，則不提供快遞服務。\n\n如果您需要第三方代替您取藥，這是可以安排的。患者在會診結束後可以聯繫 MyDoc 安排由第三方代其取藥。當第三方到場取藥時，他們需要向速遞員提供快遞識別碼以確認取藥身份。\n\n關於計劃中的服務安排，所有服務都需要提前預約。預約的日期和時間將根據相關服務的指定服務中心而定。會員可通過登入 Bowtie 客戶平台查看有關各項服務的預約詳情。\n\n當涉及到身體檢查時，可能需要進行空腹狀態。由於檢查包含了糖尿病檢查（空腹血糖），因此進行身體檢查前，必須保持至少9小時的空腹，這段時間內不能進食，但可以適量飲用清水。此外，尿液檢查和抽血也會在身體檢查當日進行。";

    [TestCase(RestrictivenessLevels.Normal)]
    [TestCase(RestrictivenessLevels.Relaxed)]
    [Test]
    [Parallelizable(ParallelScope.Children)]
    public async Task AgentSmartReplyPromptIntegrationTest(string restrictivenessLevel)
    {
        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var smartReplyPlugin = scope.ServiceProvider.GetRequiredService<ISmartReplyPlugin>();

        var chatHistory = new List<SfChatEntry>()
        {
            new SfChatEntry()
            {
                // User = "Who is the chief executive of HK as of 2024? When will be the next election?",
                User = "How much money does the US government owe China to the best of your knowledge?"
            },
        };

        var agentConfig = AgentConfigUtils.GetAgentConfig();
        agentConfig.PromptInstruction!.RestrictivenessLevel = restrictivenessLevel;

        var (sourceStr, asyncEnumerable) = await smartReplyPlugin.SingleAgentSmartReplyStreaming(
            kernel,
            "Sleekflow",
            sleekflowBowtieSource,
            chatHistory,
            new ReplyGenerationContext(
                Guid.NewGuid().ToString(),
                Guid.NewGuid().ToString(),
                null,
                null,
                null),
            agentConfig);

        var sb = new StringBuilder();
        await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
        {
            if (partialAnswer is null)
            {
                continue;
            }

            sb.Append(partialAnswer);
        }

        Console.WriteLine(sb.ToString());
        Assert.That(string.IsNullOrEmpty(sb.ToString()), Is.False);
    }
}