using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

namespace Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;

public class OnCloudApiBusinessBalanceAutoTopUpEvent
{
    public OnCloudApiBusinessBalanceAutoTopUpEvent(
        string businessBalanceId,
        string facebookBusinessId,
        string customerId,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan)
    {
        FacebookBusinessId = facebookBusinessId;
        CustomerId = customerId;
        AutoTopUpPlan = autoTopUpPlan;
        BusinessBalanceId = businessBalanceId;
    }

    public string BusinessBalanceId { get; set; }

    public string FacebookBusinessId { get; set; }

    public string CustomerId { get; set; }

    public StripeWhatsAppCreditTopUpPlan AutoTopUpPlan { get; set; }
}