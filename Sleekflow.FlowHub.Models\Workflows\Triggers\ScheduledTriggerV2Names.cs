using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Models.Workflows.Triggers;

public static class ScheduledTriggerV2Names
{
    public const string ContactPropertyTriggerName = "contact_property_date_and_time_enrolled";
    public const string ScheduledDateTimeTriggerName = "scheduled_date_and_time_enrolled";
    public const string CustomObjectDateTimeTriggerName = "schemafull_object_property_date_and_time_enrolled";

    public static string GetTriggerNameByScheduledType(string scheduleType)
    {
        switch (scheduleType)
        {
            case WorkflowScheduleTypes.PredefinedDateTime:
                return ScheduledDateTimeTriggerName;
            case WorkflowScheduleTypes.ContactPropertyBasedDateTime:
                return ContactPropertyTriggerName;
            case WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime:
                return CustomObjectDateTimeTriggerName;
            default:
                return string.Empty;
        }
    }
}