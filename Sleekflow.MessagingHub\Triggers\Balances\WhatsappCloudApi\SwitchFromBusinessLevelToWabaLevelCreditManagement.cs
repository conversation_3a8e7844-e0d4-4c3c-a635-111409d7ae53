﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class SwitchFromBusinessLevelToWabaLevelCreditManagement
    : ITrigger<SwitchFromBusinessLevelToWabaLevelCreditManagement.
        SwitchFromBusinessLevelToWabaLevelCreditManagementInput,
        SwitchFromBusinessLevelToWabaLevelCreditManagement.SwitchFromBusinessLevelToWabaLevelCreditManagementOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;

    public SwitchFromBusinessLevelToWabaLevelCreditManagement(
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService)
    {
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
    }

    public class SwitchFromBusinessLevelToWabaLevelCreditManagementInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("eTag")]
        public string ETag { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("credit_allocation")]
        [Validations.ValidateObject]
        public CreditAllocationObject CreditAllocation { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public SwitchFromBusinessLevelToWabaLevelCreditManagementInput(
            string sleekflowCompanyId,
            string facebookBusinessId,
            string eTag,
            CreditAllocationObject creditAllocation,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookBusinessId = facebookBusinessId;
            ETag = eTag;
            CreditAllocation = creditAllocation;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class SwitchFromBusinessLevelToWabaLevelCreditManagementOutput
    {
        [JsonProperty("business_balance")]
        public BusinessBalanceDto BusinessBalance { get; set; }

        [JsonConstructor]
        public SwitchFromBusinessLevelToWabaLevelCreditManagementOutput(
            BusinessBalanceDto businessBalance)
        {
            BusinessBalance = businessBalance;
        }
    }

    public async Task<SwitchFromBusinessLevelToWabaLevelCreditManagementOutput> F(
        SwitchFromBusinessLevelToWabaLevelCreditManagementInput input)
    {
        var facebookBusinessId = input.FacebookBusinessId;

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

        if (!wabas.Any())
        {
            throw new SfNotFoundObjectException(
                $"Unable to locate any valid waba from facebook business id {facebookBusinessId}.");
        }

        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance == null)
        {
            throw new SfNotFoundObjectException(
                $"Unable to locate any valid business balance from facebook business id {facebookBusinessId}.",
                facebookBusinessId);
        }

        businessBalance =
            await _wabaLevelCreditManagementService.SwitchFromBusinessLevelToWabaLevelCreditManagementAsync(
                businessBalance.FacebookBusinessId,
                input.ETag,
                input.CreditAllocation,
                input.SleekflowCompanyId,
                input.SleekflowStaffId,
                input.SleekflowStaffTeamIds);

        var unCalculatedCreditTransferTransactionLogs =
            await _wabaLevelCreditManagementService.GetUnCalculatedCreditTransferTransactionLogsAsync(businessBalance);

        return new SwitchFromBusinessLevelToWabaLevelCreditManagementOutput(
            new BusinessBalanceDto(businessBalance, wabas, unCalculatedCreditTransferTransactionLogs));
    }
}