using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReInitConnection : ITrigger
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public ReInitConnection(
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class ReInitConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        [ValidateObject]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public ReInitConnectionInput(
            string sleekflowCompanyId,
            string connectionId,
            string successUrl,
            string failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class ReInitConnectionOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("is_re_authentication_required")]
        public bool IsReAuthenticationRequired { get; set; }

        [JsonProperty("salesforce_authentication_url")]
        public string? SalesforceAuthenticationUrl { get; set; }

        [JsonConstructor]
        public ReInitConnectionOutput(
            string providerName,
            bool isReAuthenticationRequired,
            string? salesforceAuthenticationUrl)
        {
            ProviderName = providerName;
            IsReAuthenticationRequired = isReAuthenticationRequired;
            SalesforceAuthenticationUrl = salesforceAuthenticationUrl;
        }
    }

    public async Task<ReInitConnectionOutput> F(
        ReInitConnectionInput reInitConnectionInput)
    {
        var connection = await _salesforceConnectionService.GetByIdAsync(
            reInitConnectionInput.ConnectionId,
            reInitConnectionInput.SleekflowCompanyId);
        if (connection.IsActive)
        {
            return new ReInitConnectionOutput(
                "salesforce-integrator",
                false,
                null);
        }

        try
        {
            await _salesforceAuthenticationService.ReAuthenticateAndStoreAsync(
                connection.AuthenticationId,
                connection.SleekflowCompanyId);

            await _salesforceConnectionService.PatchAsync(
                connection.Id,
                connection.SleekflowCompanyId,
                connection.Name,
                true);

            return new ReInitConnectionOutput(
                "salesforce-integrator",
                false,
                null);
        }
        catch (Exception)
        {
            var redirectUrl =
                await _salesforceAuthenticationService.AuthenticateV2Async(
                    reInitConnectionInput.SleekflowCompanyId,
                    reInitConnectionInput.SuccessUrl,
                    reInitConnectionInput.FailureUrl,
                    reInitConnectionInput.AdditionalDetails);

            return new ReInitConnectionOutput(
                "salesforce-integrator",
                true,
                redirectUrl);
        }
    }
}