using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUp;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopup;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopUpProfiles;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Models.NetSuite;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class
    OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer
    : IConsumer<
        OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent>
{
    private readonly ILogger<OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer> _logger;

    private readonly IBus _bus;
    private readonly ILockService _lockService;
    private readonly IAuditLogService _auditLogService;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWhatsappCloudApiBusinessBalanceWebhookService _whatsappCloudApiBusinessBalanceWebhookService;
    private readonly IBusinessBalanceAutoTopUpService _businessBalanceAutoTopUpService;
    private readonly IBusinessBalanceAutoTopUpProfileService _businessBalanceAutoTopUpProfileService;
    private readonly IWabaBalanceAutoTopUpProfileService _wabaBalanceAutoTopUpProfileService;
    private readonly IWabaBalanceAutoTopUpService _wabaBalanceAutoTopUpService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;

    public OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer(
        IBus bus,
        ILockService lockService,
        IAuditLogService auditLogService,
        IBusinessBalanceService businessBalanceService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWhatsappCloudApiBusinessBalanceWebhookService whatsappCloudApiBusinessBalanceWebhookService,
        ILogger<OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer> logger,
        IWabaService wabaService,
        IBusinessBalanceAutoTopUpProfileService businessBalanceAutoTopUpProfileService,
        IBusinessBalanceAutoTopUpService businessBalanceAutoTopUpService,
        IWabaBalanceAutoTopUpProfileService wabaBalanceAutoTopUpProfileService,
        IWabaBalanceAutoTopUpService wabaBalanceAutoTopUpService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService)
    {
        _bus = bus;
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceAutoTopUpProfileService = businessBalanceAutoTopUpProfileService;
        _businessBalanceAutoTopUpService = businessBalanceAutoTopUpService;
        _lockService = lockService;
        _auditLogService = auditLogService;
        _businessBalanceService = businessBalanceService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _whatsappCloudApiBusinessBalanceWebhookService = whatsappCloudApiBusinessBalanceWebhookService;
        _wabaBalanceAutoTopUpProfileService = wabaBalanceAutoTopUpProfileService;
        _wabaBalanceAutoTopUpService = wabaBalanceAutoTopUpService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
    }

    public async Task Consume(
        ConsumeContext<OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent> context)
    {
        var onCloudApiBusinessBalancePendingTransactionLogCreatedEvent = context.Message;
        var retryCount = context.GetRedeliveryCount();

        if (retryCount > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent}",
                JsonConvert.SerializeObject(onCloudApiBusinessBalancePendingTransactionLogCreatedEvent));

            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var cancellationToken = context.CancellationToken;

        var facebookBusinessId = onCloudApiBusinessBalancePendingTransactionLogCreatedEvent.FacebookBusinessId;

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId
            },
            TimeSpan.FromSeconds(
                60 * (OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumerDefinition
                          .LockDuration +
                      OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumerDefinition
                          .MaxAutoRenewDuration)),
            cancellationToken);

        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));

            return;
        }

        try
        {
            var businessBalance =
                await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

            if (businessBalance is null)
            {
                _logger.LogWarning(
                    "Unable to locate business account balance object with {FacebookBusinessId}",
                    facebookBusinessId);
                await _lockService.ReleaseAsync(@lock, cancellationToken);

                return;
            }

            // Snapshot before calculate business balance
            var beforeBusinessBalanceCalculatedSnapshot =
                JsonConvert.DeserializeObject<BusinessBalance>(
                    JsonConvert.SerializeObject(businessBalance));

            var businessBalanceTransactionLogs =
                await _businessBalanceTransactionLogService.GetUnCalculatedLogsWithFacebookBusinessIdAsync(
                    businessBalance.FacebookBusinessId);

            if (businessBalanceTransactionLogs.Count == 0)
            {
                _logger.LogInformation(
                    "No more unCalculate transaction logs for business account balance object with {FacebookBusinessId}",
                    facebookBusinessId);
                await _lockService.ReleaseAsync(@lock, cancellationToken);

                return;
            }

            foreach (var businessBalanceTransactionLog in businessBalanceTransactionLogs)
            {
                var currentBusinessBalanceTransactionLog =
                    await _businessBalanceTransactionLogService
                        .GetOrDefaultBusinessBalanceTransactionLogAsync(
                            businessBalanceTransactionLog.Id,
                            businessBalanceTransactionLog.FacebookBusinessId);

                if (currentBusinessBalanceTransactionLog is null)
                {
                    continue;
                }

                // Create NetSuite Invoice
            if (currentBusinessBalanceTransactionLog.WabaTopUp is { PayAmount.Amount: > 0 })
            {
                var wabaTopUp = currentBusinessBalanceTransactionLog.WabaTopUp;
                var companyId =
                    (wabaTopUp.InternalTopUpCreditDetail?.Metadata.GetValueOrDefault("sleekflow_company_id") ??
                     wabaTopUp.StripeTopUpCreditDetail?.Metadata.GetValueOrDefault("sleekflow_company_id"))
                    ?.ToString();
                if (companyId != null)
                {
                    var createInvoiceEvent = new CreateNetSuiteInvoiceEvent(
                        $"wa-{currentBusinessBalanceTransactionLog.UniqueId}",
                        companyId,
                        0,
                        null,
                        0,
                        null,
                        wabaTopUp.PayAmount.Amount,
                        wabaTopUp.PaymentMethod,
                        null,
                        null,
                        null,
                        wabaTopUp.PayAmount.CurrencyIsoCode,
                        (double) wabaTopUp.PayAmount.Amount,
                        DateTime.Now);
                    await _bus.Publish(createInvoiceEvent, cancellationToken);
                }
            }var beforeCalculateBusinessBalanceSnapshot =
                    JsonConvert.DeserializeObject<BusinessBalance>(
                        JsonConvert.SerializeObject(businessBalance));

                if (businessBalance is { IsByWabaBillingEnabled: true } &&
                    !string.IsNullOrEmpty(currentBusinessBalanceTransactionLog.FacebookWabaId))
                {
                    if (businessBalance.WabaBalances != null)
                    {
                        if (businessBalance.WabaBalances.TrueForAll(
                                x => x.FacebookWabaId != currentBusinessBalanceTransactionLog.FacebookWabaId))
                        {
                            businessBalance = await _wabaLevelCreditManagementService.InitWabaBalancesInBusinessBalanceAsync(
                                new HashSet<string>()
                                {
                                    currentBusinessBalanceTransactionLog.FacebookWabaId!
                                },
                                businessBalance);
                        }
                    }
                    else
                    {
                        // switch back to FBBA Level Credit Management if no Waba balance found
                        businessBalance.IsByWabaBillingEnabled = false;
                        var upsertedResult = await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);

                        if (upsertedResult == 0)
                        {
                            businessBalanceTransactionLog.SetCalculationStatusToFailed();

                            await _businessBalanceTransactionLogService
                                .UpsertBusinessBalanceTransactionLogAsync(businessBalanceTransactionLog);

                            await _lockService.ReleaseAsync(@lock, cancellationToken);
                            await context.Redeliver(TimeSpan.FromSeconds(8));

                            return;
                        }

                        await _auditLogService.AuditBusinessWabaLevelCreditManagementSwitchBySystemAsync(
                            beforeCalculateBusinessBalanceSnapshot!,
                            facebookBusinessId,
                            AuditingOperation.SwitchBusinessLevelCreditManagement,
                            new Dictionary<string, object?>()
                            {
                                {
                                    "change", businessBalance
                                }
                            });

                        beforeBusinessBalanceCalculatedSnapshot = JsonConvert.DeserializeObject<BusinessBalance>(
                            JsonConvert.SerializeObject(businessBalance));
                    }
                }

                businessBalance!.CalculateTransactionLog(currentBusinessBalanceTransactionLog);
                businessBalance.UpdatedAt = DateTimeOffset.UtcNow;

                var patchBusinessBalanceStatus =
                    await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);

                if (patchBusinessBalanceStatus == 0)
                {
                    businessBalanceTransactionLog.SetCalculationStatusToFailed();

                    await _businessBalanceTransactionLogService
                        .UpsertBusinessBalanceTransactionLogAsync(businessBalanceTransactionLog);

                    await _lockService.ReleaseAsync(@lock, cancellationToken);
                    await context.Redeliver(TimeSpan.FromSeconds(8));

                    return;
                }

                var afterCalculatedBusinessBalance =
                    await _businessBalanceService.GetOrDefaultBusinessBalanceAsync(
                        businessBalance.Id,
                        businessBalance.FacebookBusinessId);

                await _auditLogService.AuditBusinessBalanceAsync(
                    beforeCalculateBusinessBalanceSnapshot,
                    businessBalance.FacebookBusinessId,
                    AuditingOperation.OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent,
                    new Dictionary<string, object?>
                    {
                        {
                            "changes", afterCalculatedBusinessBalance
                        }
                    });

                // Refresh the object;
                businessBalance = afterCalculatedBusinessBalance;
                currentBusinessBalanceTransactionLog.SetCalculationStatusToSuccess();

                await _businessBalanceTransactionLogService.UpsertBusinessBalanceTransactionLogAsync(
                    currentBusinessBalanceTransactionLog);
            }

            // After calculated business balance capture a snapshot
            // for deciding we should send a webhook to travis backend
            // for notifying frontend whatsapp cloud api business balance has changed
            var afterBusinessBalanceCalculatedSnapshot = JsonConvert.DeserializeObject<BusinessBalance>(
                JsonConvert.SerializeObject(businessBalance));

            if (_businessBalanceService.IsBusinessBalanceUsageOrCreditChanged(
                    beforeBusinessBalanceCalculatedSnapshot,
                    afterBusinessBalanceCalculatedSnapshot))
            {
                // Get webhook entity by facebookBusinessId
                // We should only publish the webhook event once
                var onWhatsappCloudApiBusinessBalanceChangedWebhooks = await _whatsappCloudApiBusinessBalanceWebhookService
                        .GetOnWhatsappCloudApiBusinessBalanceChangedWebhooksAsync(
                            WebhookEntityTypeNames.BusinessBalance,
                            WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiBusinessBalanceChanged,
                            facebookBusinessId,
                            cancellationToken);

                var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

                var unCalculatedCreditTransferTransactionLogs =
                    await _wabaLevelCreditManagementService.GetUnCalculatedCreditTransferTransactionLogsAsync(
                        businessBalance!);

                var businessBalanceDto = new BusinessBalanceDto(
                    afterBusinessBalanceCalculatedSnapshot!,
                    wabas,
                    unCalculatedCreditTransferTransactionLogs);

                var payload = new WhatsappCloudApiWebhookOnBusinessBalanceChangedMessage(
                    businessBalanceDto);

                await _whatsappCloudApiBusinessBalanceWebhookService.SendWebhooksAsync(
                    onWhatsappCloudApiBusinessBalanceChangedWebhooks,
                    payload,
                    cancellationToken);
            }

            if (businessBalance!.IsByWabaBillingEnabled == true && businessBalance.WabaBalances is not null)
            {
                var wabaBalanceAutoTopProfiles =
                    await _wabaBalanceAutoTopUpProfileService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

                foreach (var wabaBalance in businessBalance.WabaBalances)
                {
                    var beforeSnapshotWabaBalance =
                        beforeBusinessBalanceCalculatedSnapshot!.WabaBalances!.First(x => x.Id == wabaBalance.Id);

                    if (beforeSnapshotWabaBalance.Balance.Amount < 0 && wabaBalance.Balance.Amount >= 0)
                    {
                        var onCloudApiRechargeBalanceEvent = new OnCloudApiRechargeBalanceEvent(
                            facebookBusinessId,
                            wabaBalance.FacebookWabaId);
                        await _bus.Publish(onCloudApiRechargeBalanceEvent, cancellationToken);
                    }

                    switch (wabaBalance.Balance.Amount)
                    {
                        case < 0:
                            var onCloudApiWabaOutOfBalanceEvent = new OnCloudApiWabaOutOfBalanceEvent(
                                facebookBusinessId,
                                wabaBalance.FacebookWabaId);
                            await _bus.Publish(onCloudApiWabaOutOfBalanceEvent, cancellationToken);

                            break;

                        case >= 0 and < 50:
                            var onCloudApiWabaLowBalanceEvent = new OnCloudApiWabaLowBalanceEvent(
                                facebookBusinessId,
                                wabaBalance.FacebookWabaId);
                            await _bus.Publish(onCloudApiWabaLowBalanceEvent, cancellationToken);

                            break;
                    }

                    var wabaBalanceAutoTopProfile =
                        wabaBalanceAutoTopProfiles.Find(x => x.FacebookWabaId == wabaBalance.FacebookWabaId);

                    if (wabaBalanceAutoTopProfile is not null)
                    {
                        var performAutoTopUp = _wabaBalanceAutoTopUpService.ShouldPerformAutoTopUp(
                            facebookBusinessId,
                            wabaBalance.FacebookWabaId,
                            wabaBalance,
                            wabaBalanceAutoTopProfile);

                        if (performAutoTopUp)
                        {
                            await _wabaBalanceAutoTopUpService.PublishAutoTopUpEvent(
                                facebookBusinessId,
                                wabaBalance.FacebookWabaId,
                                businessBalance.Id,
                                wabaBalanceAutoTopProfile,
                                cancellationToken);
                        }
                    }
                }
            }
            else if (businessBalance.IsByWabaBillingEnabled is null or false)
            {
                if (beforeBusinessBalanceCalculatedSnapshot!.Balance.Amount < 0 && businessBalance.Balance.Amount >= 0)
                {
                    var onCloudApiRechargeBalanceEvent = new OnCloudApiRechargeBalanceEvent(facebookBusinessId);
                    await _bus.Publish(onCloudApiRechargeBalanceEvent, cancellationToken);
                }

                switch (businessBalance.Balance.Amount)
                {
                    // case < -500:
                    //     var onCloudApiDeactivateBusinessEvent = new OnCloudApiDeactivateBusinessEvent(facebookBusinessId);
                    //     await _bus.Publish(onCloudApiDeactivateBusinessEvent, cancellationToken);
                    //     break;
                    case < 0:
                        var onCloudApiBusinessOutOfBalanceEvent =
                            new OnCloudApiBusinessOutOfBalanceEvent(facebookBusinessId);
                        await _bus.Publish(onCloudApiBusinessOutOfBalanceEvent, cancellationToken);

                        break;
                    case >= 0 and < 50:
                        var onCloudApiBusinessLowBalanceEvent =
                            new OnCloudApiBusinessLowBalanceEvent(facebookBusinessId);
                        await _bus.Publish(onCloudApiBusinessLowBalanceEvent, cancellationToken);

                        break;
                }

                var businessBalanceAutoTopProfile =
                    await _businessBalanceAutoTopUpProfileService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

                if (businessBalanceAutoTopProfile is not null)
                {
                    var performAutoTopUp = _businessBalanceAutoTopUpService.ShouldPerformAutoTopUp(
                        facebookBusinessId,
                        businessBalance,
                        businessBalanceAutoTopProfile);

                    if (performAutoTopUp)
                    {
                        await _businessBalanceAutoTopUpService.PublishAutoTopUpEvent(
                            facebookBusinessId,
                            businessBalance.Id,
                            businessBalanceAutoTopProfile,
                            cancellationToken);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "Error occur during OnCloudApiBusinessBalancePendingTransactionLogCreatedEventConsumer {Context}/{Exception}",
                JsonConvert.SerializeObject(context),
                JsonConvert.SerializeObject(exception));
        }
        finally
        {
            if (@lock != null)
            {
                await _lockService.ReleaseAsync(@lock, cancellationToken);
            }
        }
    }
}