using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;
using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using Auth0.ManagementApi;
using Auth0.ManagementApi.Models;
using Newtonsoft.Json;
using Sleekflow.Auth0.BulkImporter.Models;

namespace Sleekflow.Auth0.BulkImporter.Services;

public class Auth0UserImporter
{
    private const int RateLimit = 0;

    private readonly Auth0TokenRequest _clientInfo;
    private readonly string _auth0BaseDomain;

    private AccessTokenResponse? _managementApiAccessTokenResponse;
    private ManagementApiClient? _managementApiClient;

    public ManagementApiClient? ManagementClient => _managementApiClient;

    public string? AccessToken => _managementApiAccessTokenResponse?.AccessToken;

    private Auth0UserImporter(
        string clientId,
        string clientSecret,
        string audience,
        string grantType)
    {
        _clientInfo = new Auth0TokenRequest(
            clientId,
            clientSecret,
            audience,
            grantType);
        _auth0BaseDomain = GetBaseDomain(audience);
    }

    public static async Task<Auth0UserImporter> InitAsync(
        string clientId,
        string clientSecret,
        string audience,
        string grantType)
    {
        var auth0UsersHelper = new Auth0UserImporter(
            clientId,
            clientSecret,
            audience,
            grantType);

        await auth0UsersHelper.InitClientsAsync();

        return auth0UsersHelper;
    }

    private static string GetBaseDomain(string urlPath)
    {
        var uri = new Uri(urlPath);
        return uri.Scheme + "://" + uri.Host;
    }

    private async Task InitClientsAsync()
    {
        try
        {
            var uri = new Uri(_clientInfo.Audience);

            var client = new HttpClient();

            var authenticationApiClient = new AuthenticationApiClient(uri.Host);

            _managementApiAccessTokenResponse = await authenticationApiClient.GetTokenAsync(
                new ClientCredentialsTokenRequest()
                {
                    ClientId = _clientInfo.ClientId,
                    ClientSecret = _clientInfo.ClientSecret,
                    Audience = _clientInfo.Audience
                });
            var httpClientManagementConnection = new HttpClientManagementConnection(
                client,
                new HttpClientManagementConnectionOptions()
                {
                    NumberOfHttpRetries = RateLimit
                });
            _managementApiClient = new ManagementApiClient(
                _managementApiAccessTokenResponse.AccessToken,
                uri.Host,
                httpClientManagementConnection);
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e.InnerException);
        }
    }

    public async Task<Job> GetJobStatus(string jobId)
    {
        var response = await _managementApiClient?.Jobs.GetAsync(jobId)!;
        if (_managementApiClient?.Jobs != null)
        {
            return response;
        }

        throw new Exception("Auth0 Management api client does not initialize");
    }

    public async Task<List<Job>> RefreshJobsStatus(List<Job> jobs, bool showDebug = false)
    {
        var statusList = new List<Job>();

        foreach (var job in jobs)
        {
            if (job.Status == "completed")
            {
                statusList.Add(job);
            }
            else
            {
                try
                {
                    if (_managementApiClient != null)
                    {
                        var jobStatus = await _managementApiClient.Jobs.GetAsync(job.Id);
                        if (jobStatus != null)
                        {
                            statusList.Add(jobStatus);
                        }
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"{e.Message}\n{e.InnerException}");
                }
            }
        }

        if (showDebug)
        {
            var debugString = string.Empty;
            foreach (var status in statusList)
            {
                debugString = debugString == string.Empty
                    ? $"{status.Id}:{status.Status}"
                    : $"{debugString}, {status.Id}:{status.Status}";
            }

            Console.Write($"\r({debugString}):{jobs.Count}");
        }

        return statusList;
    }

    /// <summary>
    /// Fail details cannot found in Auth0 Jobs lib.
    /// Use HttpClient instead.
    /// </summary>
    /// <param name="jobId">Job id.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    public async Task<(List<Auth0JobErrorDetailsResponse>? ErrorResponse, string Message)> GetFailDetails(string? jobId)
    {
        var client = new HttpClient();
        var request = new HttpRequestMessage();

        request.Method = HttpMethod.Get;
        request.Headers.Add("Accept", "*/*");
        request.Headers.Add(
            "Authorization",
            $"{_managementApiAccessTokenResponse!.TokenType} {_managementApiAccessTokenResponse!.AccessToken}");

        request.RequestUri = new Uri(_auth0BaseDomain + $"/api/v2/jobs/{jobId}/errors");

        var response = await client.SendAsync(request);
        var resString = await response.Content.ReadAsStringAsync();
        var jobDetails = JsonConvert.DeserializeObject<List<Auth0JobErrorDetailsResponse>>(resString);

        return (jobDetails, resString);
    }

    public async Task<List<Auth0JobErrorDetailsResponse?>> GetAllFailDetails(List<Job> jobs)
    {
        var result = new List<Auth0JobErrorDetailsResponse?>();
        foreach (var job in jobs)
        {
            var (failedDetails, value) = await GetFailDetails(job.Id);
            if (failedDetails != null)
            {
                result = result.Concat(failedDetails).ToList();
            }
        }

        return result;
    }

    /// <summary>
    /// (Obsolete) Post the users list file.
    /// </summary>
    /// <param name="connectionId">The connection id.</param>
    /// <param name="filePath">User file path.</param>
    /// <param name="isUpsert">Overwrite the users table?.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    /// <exception cref="Exception">Return the error response.</exception>
    [Obsolete("This method is using HttpClient. Please use jobs import instead.")]
    public async Task<Auth0JobResponse?> PostUsersFile(string connectionId, string filePath, bool isUpsert = false)
    {
        var file = new FileInfo(filePath);
        var client = new HttpClient();
        var request = new HttpRequestMessage();

        request.RequestUri = new Uri(_auth0BaseDomain + "/api/v2/jobs/users-imports");
        request.Method = HttpMethod.Post;
        request.Headers.Add("Accept", "*/*");
        request.Headers.Add(
            "Authorization",
            $"{_managementApiAccessTokenResponse!.TokenType} {_managementApiAccessTokenResponse!.AccessToken}");

        var sendBody = new MultipartFormDataContent()
        {
            {
                new StringContent(isUpsert ? "true" : "false", Encoding.UTF8, "multipart/form-data"), "upsert"
            },
            {
                new StringContent(connectionId, Encoding.UTF8, "multipart/form-data"), "connection_id"
            },
            {
                new StreamContent(new FileStream($"{filePath}", FileMode.Open)), "users", $"{file.Name}"
            }
        };

        request.Content = sendBody;

        var response = await client.SendAsync(request);
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<Auth0JobResponse>(responseString);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception(responseString);
        }

        return result;
    }

    public static async Task WriteUserIdsFile(List<string> userIds, string path)
    {
        var cache = string.Empty;
        foreach (var id in userIds)
        {
            cache += $"auth0|{id}\n";
        }

        await File.WriteAllTextAsync($"{path}/UserIds.txt", cache);
    }

    public static async Task<int> WriteUsersToFiles(
        List<ImportUser> users,
        string path,
        int runBatchSize = 10,
        long fileSizeLimit = 500000)
    {
        var pagination = users.Count / runBatchSize + (users.Count % runBatchSize > 0 ? 1 : 0);
        var lastPageCount = users.Count % runBatchSize;

        for (var i = 0; i < pagination; i++)
        {
            var rangeValue = runBatchSize;
            if (lastPageCount > 0 && i == pagination - 1)
            {
                rangeValue = lastPageCount;
            }

            var tmpData = users.GetRange(i * runBatchSize, rangeValue);
            var tmpJson = JsonConvert.SerializeObject(
                tmpData,
                Formatting.Indented,
                new JsonSerializerSettings()
                {
                    NullValueHandling = NullValueHandling.Ignore
                });
            var size = Encoding.Unicode.GetByteCount(tmpJson);

            if (size > fileSizeLimit)
            {
                throw new Exception("\nError: File sizes is over the size limit.");
            }

            await File.WriteAllTextAsync($"{path}/{i + 1}.json", tmpJson);
            Console.Write($"\r{i + 1}/{pagination} files created. ");
        }

        Console.Write('\n');
        return pagination;
    }

    public async Task CleanData(List<string> clearList)
    {
        foreach (var cleanItem in clearList)
        {
            try
            {
                if (_managementApiClient != null)
                {
                    await _managementApiClient.Users.DeleteAsync($"auth0|{cleanItem}");
                }

                Console.Write($"\rSent delete request {cleanItem}");
                await Task.Delay(500);
            }
            catch (Exception err)
            {
                Console.WriteLine($"Error when delete {cleanItem}: {err.Message}");
            }
        }

        Console.Write($"\rAll delete requests are sent.\n");
    }

    public async Task<ConcurrentBag<Job>> ImportUsersAsync(string connectionId, FileInfo[] files, int maxNumOfConcurrentReqs)
    {
        var fileImportIndex = 1;
        var jobStatusList = new ConcurrentBag<Job>();
        var throttler = new SemaphoreSlim(initialCount: maxNumOfConcurrentReqs);
        var tasks = files.Select(
            async file =>
            {
                await throttler.WaitAsync();
                try
                {
                    Stream stream = new FileStream(file.FullName, FileMode.Open);

                    Debug.Assert(ManagementClient != null, "userImporter.ManagementClient != null");
                    var job =
                        await ManagementClient.Jobs.ImportUsersAsync(
                            connectionId,
                            file.Name,
                            stream,
                            true,
                            null,
                            true);

                    Console.Write($"\r{(float) fileImportIndex / (float) files.Length:P} " +
                                  $"({fileImportIndex} of {files.Length})");

                    fileImportIndex++;

                    while (true)
                    {
                        var jobStatus = await ManagementClient.Jobs.GetAsync(job.Id);
                        if (jobStatus.Status is not "pending")
                        {
                            jobStatusList.Add(jobStatus);

                            break;
                        }

                        await Task.Delay(500);
                    }
                }
                finally
                {
                    throttler.Release();
                }
            });
        await Task.WhenAll(tasks);

        return jobStatusList;
    }
}