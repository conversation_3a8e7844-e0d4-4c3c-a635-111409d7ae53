﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowExecutionRestrictedEventConsumerDefinition : ConsumerDefinition<OnWorkflowExecutionRestrictedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowExecutionRestrictedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowExecutionRestrictedEventConsumer : IConsumer<OnWorkflowExecutionRestrictedEvent>
{
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public OnWorkflowExecutionRestrictedEventConsumer(
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
    }

    public async Task Consume(ConsumeContext<OnWorkflowExecutionRestrictedEvent> context)
    {
        var @event = context.Message;

        var stateId = @event.StateId;
        var stateIdentity = @event.StateIdentity;
        var restrictedAt = @event.RestrictedAt;
        var workflowType = @event.WorkflowType;

        await _workflowExecutionService.CreateWorkflowExecutionAsync(
            stateId,
            stateIdentity,
            WorkflowExecutionStatuses.Restricted,
            null,
            0,
            workflowType,
            null,
            restrictedAt);
    }
}