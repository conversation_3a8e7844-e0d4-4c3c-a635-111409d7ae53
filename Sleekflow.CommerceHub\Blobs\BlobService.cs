using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using MassTransit;
using MassTransit.Util;
using MimeDetective;
using MimeDetective.Definitions;
using MimeDetective.Storage;
using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.CommerceHub.Blobs;

public interface IBlobService
{
    BlobContainerClient GetFileBlobServiceClient();

    BlobContainerClient GetImageBlobServiceClient();

    Task<bool> IsBlobExistsAsync(string blobId, string blobType);

    bool IsValidBlobId(string blobId, string sleekflowCompanyId, string storeId);

    Task<long> GetBlobSizeAsync(string blobId, string blobType);

    Task<List<GetBlobSasUrlOutput>> CreateUploadBlobSasUrlsAsync(
        string sleekflowCompanyId,
        string storeId,
        string blobType,
        int numberOfBlobs);

    Task<List<Image>> ConvertImageBlobNamesToImagesAsync(
        List<string> imageBlobNames,
        string sleekflowCompanyId,
        string storeId);

    Task<List<Blob>> ConvertFileBlobNamesToFilesAsync(
        List<string> blobNames,
        string sleekflowCompanyId,
        string storeId);

    Task DeleteBlobsAsync(List<Blob> blobs, string blobType);

    Task DeleteBlobsAsync(List<string> blobNames, string sleekflowCompanyId, string storeId, string blobType);

    string GetBlobId(string sleekflowCompanyId, string storeId, string blobName);

    public record struct GetBlobSasUrlOutput(string BlobId, string Url, DateTimeOffset ExpiresOn, string BlobName);
}

public class BlobService : IBlobService, ISingletonService
{
    private readonly IBus _bus;
    private readonly ILogger<BlobService> _logger;
    private readonly IIdService _idService;
    private readonly BlobContainerClient _fileBlobServiceClient;
    private readonly BlobContainerClient _imageBlobServiceClient;

    public BlobService(
        IBus bus,
        IIdService idService,
        ILogger<BlobService> logger,
        IStorageConfig storageConfig)
    {
        _bus = bus;
        _logger = logger;
        _idService = idService;
        _fileBlobServiceClient =
            new BlobServiceClient(storageConfig.FileStorageConnStr)
                .GetBlobContainerClient(BlobContainerNames.FileContainer);
        _imageBlobServiceClient =
            new BlobServiceClient(storageConfig.ImageStorageConnStr)
                .GetBlobContainerClient(BlobContainerNames.ImageContainer);
    }

    public BlobContainerClient GetFileBlobServiceClient()
    {
        return _fileBlobServiceClient;
    }

    public BlobContainerClient GetImageBlobServiceClient()
    {
        return _imageBlobServiceClient;
    }

    public async Task<bool> IsBlobExistsAsync(string blobId, string blobType)
    {
        return await GetBlobContainerClient(blobType).GetBlobClient(blobId).ExistsAsync();
    }

    private async Task<bool> IsBlobExistsAsync(BlobClient blobClient)
    {
        return await blobClient.ExistsAsync();
    }

    public bool IsValidBlobId(string blobId, string sleekflowCompanyId, string storeId)
    {
        return blobId.StartsWith(string.Join("/", sleekflowCompanyId, storeId));
    }

    public async Task<long> GetBlobSizeAsync(string blobId, string blobType)
    {
        var blobClient = GetBlobContainerClient(blobType).GetBlobClient(blobId);
        var properties = await blobClient.GetPropertiesAsync();
        var size = properties.Value.ContentLength;

        return size;
    }

    public async Task<List<IBlobService.GetBlobSasUrlOutput>> CreateUploadBlobSasUrlsAsync(
        string sleekflowCompanyId,
        string storeId,
        string blobType,
        int numberOfBlobs)
    {
        var blobContainerClient = GetBlobContainerClient(blobType);

        var blobSasUrlOutputs = new List<IBlobService.GetBlobSasUrlOutput>();
        for (var i = 0; i < numberOfBlobs; i++)
        {
            var blobName = _idService.GetId("Blob");
            var blobId = GetBlobId(sleekflowCompanyId, storeId, blobName);

            var blobClient = blobContainerClient.GetBlockBlobClient(blobId);

            // Create an empty blob for frontend to upload the file
            await blobClient.UploadAsync(Stream.Null);

            var expiresDelay = TimeSpan.FromHours(1);
            var expiresOn = DateTimeOffset.UtcNow.Add(expiresDelay);

            var onBlobCreatedEvent = new OnBlobCreatedEvent(
                sleekflowCompanyId,
                blobContainerClient.Name,
                blobId,
                blobType);

            // Publish an event for deleting unused blob
            await _bus.Publish(
                onBlobCreatedEvent,
                context => { context.Delay = expiresDelay; });

            var uri = blobClient.GenerateSasUri(
                BlobSasPermissions.Write,
                expiresOn);
            blobSasUrlOutputs.Add(
                new IBlobService.GetBlobSasUrlOutput(
                    blobId,
                    uri.ToString(),
                    expiresOn,
                    blobName));
        }

        return blobSasUrlOutputs;
    }

    public async Task<List<Image>> ConvertImageBlobNamesToImagesAsync(
        List<string> imageBlobNames,
        string sleekflowCompanyId,
        string storeId)
    {
        var images = new List<Image>();
        if (imageBlobNames.Count == 0)
        {
            return images;
        }

        foreach (var imageBlobName in imageBlobNames)
        {
            var (url, blob) = await GetReadOnlyUrlAndBlobAsync(
                imageBlobName,
                sleekflowCompanyId,
                storeId,
                BlobTypes.Image);
            if (url is null || blob is null)
            {
                continue;
            }

            images.Add(
                new Image(
                    blob.ContainerName,
                    blob.BlobName,
                    blob.BlobId,
                    url));
        }

        return images;
    }

    public async Task<List<Blob>> ConvertFileBlobNamesToFilesAsync(
        List<string> blobNames,
        string sleekflowCompanyId,
        string storeId)
    {
        var blobs = new List<Blob>();
        if (blobNames.Count == 0)
        {
            return blobs;
        }

        foreach (var blobName in blobNames)
        {
            var (_, blob) = await GetReadOnlyUrlAndBlobAsync(
                blobName,
                sleekflowCompanyId,
                storeId,
                BlobTypes.File);
            if (blob is null)
            {
                continue;
            }

            blobs.Add(blob);
        }

        return blobs;
    }

    public async Task DeleteBlobsAsync(List<Blob> blobs, string blobType)
    {
        var blobContainerClient = GetBlobContainerClient(blobType);

        foreach (var blob in blobs)
        {
            await blobContainerClient.GetBlobClient(blob.BlobId).DeleteIfExistsAsync();
        }
    }

    public async Task DeleteBlobsAsync(
        List<string> blobNames,
        string sleekflowCompanyId,
        string storeId,
        string blobType)
    {
        var blobContainerClient = GetBlobContainerClient(blobType);

        foreach (var blobName in blobNames)
        {
            var blobId = GetBlobId(sleekflowCompanyId, storeId, blobName);

            await blobContainerClient.GetBlobClient(blobId).DeleteIfExistsAsync();
        }
    }

    private BlobContainerClient GetBlobContainerClient(string blobType)
    {
        var blobContainerClient = blobType switch
        {
            BlobTypes.Image => _imageBlobServiceClient,
            BlobTypes.File => _fileBlobServiceClient,
            _ => throw new NotImplementedException()
        };

        return blobContainerClient;
    }

    public string GetBlobId(string sleekflowCompanyId, string storeId, string blobName)
    {
        return string.Join("/", sleekflowCompanyId, storeId, blobName);
    }

    private string GetBlobReadOnlySasUrl(string blobId, string blobType)
    {
        var blobClient = GetBlobContainerClient(blobType).GetBlobClient(blobId);
        var expiresOn = DateTime.MaxValue;

        return blobClient.GenerateSasUri(BlobSasPermissions.Read, expiresOn).ToString();
    }

    private async Task<(string? ReadOnlyUrl, Blob? Blob)> GetReadOnlyUrlAndBlobAsync(
        string blobName,
        string sleekflowCompanyId,
        string storeId,
        string blobType)
    {
        var blobContainerClient = GetBlobContainerClient(blobType);
        var blobId = GetBlobId(sleekflowCompanyId, storeId, blobName);

        var blobClient = GetBlobContainerClient(blobType).GetBlobClient(blobId);

        if (!await IsBlobExistsAsync(blobClient))
        {
            _logger.LogError(
                "The blob does not exists. blobName: {BlobName}, sleekflowCompanyId: {SleekflowCompanyId}, storeId: {StoreId}, blobType: {BlobType}, blobId: {BlobId}",
                blobName,
                sleekflowCompanyId,
                storeId,
                blobType,
                blobId);

            return (null, null);
        }

        if (blobType == BlobTypes.File)
        {
            var url = GetBlobReadOnlySasUrl(blobId, blobType);

            return (url, new Blob(blobContainerClient.Name, blobName, blobId));
        }
        else if (blobType == BlobTypes.Image)
        {
            var blobStream = await blobClient.OpenReadAsync();
            var inspector = new ContentInspectorBuilder
                {
                    Definitions = DefaultDefinitions.All()
                }
                .Build();
            var definitionMatches = inspector.Inspect(blobStream);

            var matchedFileTypes = definitionMatches.Select(dm => dm.Definition.File).ToList();
            var definition = DefaultDefinitions.FileTypes.Images.All()
                .FirstOrDefault(
                    dm =>
                        matchedFileTypes.Contains(
                            dm.File,
                            new LambdaEqualityComparer<FileType>((ft1, ft2) => ft1.MimeType == ft2.MimeType)));
            if (definition != null)
            {
                await blobClient.SetHttpHeadersAsync(
                    new BlobHttpHeaders
                    {
                        ContentType = definition.File.MimeType ?? "application/octet-stream"
                    });

                var url = GetBlobReadOnlySasUrl(blobId, blobType);

                return (url, new Blob(blobContainerClient.Name, blobName, blobId));
            }
        }

        _logger.LogError(
            "The blob is not an image. blobName: {BlobName}, sleekflowCompanyId: {SleekflowCompanyId}, storeId: {StoreId}, blobType: {BlobType}, blobId: {BlobId}",
            blobName,
            sleekflowCompanyId,
            storeId,
            blobType,
            blobId);

        return (null, null);
    }
}