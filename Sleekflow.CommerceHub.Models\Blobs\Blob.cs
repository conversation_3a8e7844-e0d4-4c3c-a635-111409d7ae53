using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Blobs;

public class Blob
{
    [JsonConstructor]
    public Blob(
        string? containerName,
        string? blobName,
        string? blobId)
    {
        ContainerName = containerName;
        BlobName = blobName;
        BlobId = blobId;
    }

    [SimpleField]
    [JsonProperty("container_name")]
    public string? ContainerName { get; set; }

    [SimpleField]
    [JsonProperty("blob_name")]
    public string? BlobName { get; set; }

    [JsonProperty("blob_id")]
    public string? BlobId { get; set; }
}