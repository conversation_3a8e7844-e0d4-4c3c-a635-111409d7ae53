﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyDatasetResponse
{
    [JsonProperty(PropertyName = "data")]
    public ApifyDatasetResponseData Data { get; set; }

    [JsonConstructor]
    public ApifyDatasetResponse(ApifyDatasetResponseData data)
    {
        Data = data;
    }
}

public class ApifyDatasetResponseData
{
    [JsonProperty(PropertyName = "id")]
    public string Id { get; set; }

    [JsonProperty(PropertyName = "name")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "userId")]
    public string UserId { get; set; }

    [JsonProperty(PropertyName = "createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonProperty(PropertyName = "modifiedAt")]
    public DateTime ModifiedAt { get; set; }

    [JsonProperty(PropertyName = "accessedAt")]
    public DateTime AccessedAt { get; set; }

    [JsonProperty(PropertyName = "itemCount")]
    public int ItemCount { get; set; }

    [JsonProperty(PropertyName = "cleanItemCount")]
    public int CleanItemCount { get; set; }

    [JsonProperty(PropertyName = "actId")]
    public string ActId { get; set; }

    [JsonProperty(PropertyName = "actRunId")]
    public string ActRunId { get; set; }

    [JsonProperty(PropertyName = "fields")]
    public List<string> Fields { get; set; }

    [JsonConstructor]
    public ApifyDatasetResponseData(
        string id,
        string name,
        string userId,
        DateTime createdAt,
        DateTime modifiedAt,
        DateTime accessedAt,
        int itemCount,
        int cleanItemCount,
        string actId,
        string actRunId,
        List<string> fields)
    {
        Id = id;
        Name = name;
        UserId = userId;
        CreatedAt = createdAt;
        ModifiedAt = modifiedAt;
        AccessedAt = accessedAt;
        ItemCount = itemCount;
        CleanItemCount = cleanItemCount;
        ActId = actId;
        ActRunId = actRunId;
        Fields = fields;
    }
}