using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Triggers.Balances;

[TriggerGroup(ControllerNames.Balances)]
public class OneTimePatchExistingWabaBalances
    : ITrigger<OneTimePatchExistingWabaBalances.OneTimePatchExistingWabaBalancesInput,
        OneTimePatchExistingWabaBalances.OneTimePatchExistingWabaBalancesOutput>
{
    private readonly IBusinessBalanceRepository _businessBalanceRepository;
    private readonly IWabaLevelCreditManagementService _creditManagementService;
    private readonly ILogger<OneTimePatchExistingWabaBalances> _logger;

    public OneTimePatchExistingWabaBalances(
        IBusinessBalanceRepository businessBalanceRepository,
        IWabaLevelCreditManagementService creditManagementService,
        ILogger<OneTimePatchExistingWabaBalances> logger)
    {
        _businessBalanceRepository = businessBalanceRepository;
        _creditManagementService=creditManagementService;
        _logger = logger;
    }

    public class OneTimePatchExistingWabaBalancesInput
    {
        public OneTimePatchExistingWabaBalancesInput(string? secret)
        {
            Secret = secret;
        }

        [JsonProperty("secret")]
        public string? Secret { get; set; }
    }

    public class OneTimePatchExistingWabaBalancesOutput
    {
    }

    public async Task<OneTimePatchExistingWabaBalancesOutput> F(
        OneTimePatchExistingWabaBalancesInput
            oneTimePatchExistingWabaBalancesInput)
    {
        if (oneTimePatchExistingWabaBalancesInput.Secret is not "only-for-sleekflow")
        {
            return new OneTimePatchExistingWabaBalancesOutput();
        }

        var balances = await _businessBalanceRepository.GetObjectsAsync(
            x=>x.WabaBalances != null);

        try
        {
            foreach (var oldBalance in balances)
            {
                var clonedOldBalance = JsonConvert.DeserializeObject<BusinessBalance>(JsonConvert.SerializeObject(oldBalance));
                var oldWabaBalanceRecord = oldBalance.WabaBalances!
                    .ToDictionary(x => x.FacebookWabaId, x => x.Balance);

                // reset the waba balances with previous usage
                oldBalance.WabaBalances = null;
                var newBalance = await _creditManagementService
                    .InitWabaBalancesInBusinessBalanceAsync(
                        oldWabaBalanceRecord.Keys.ToHashSet(),
                        oldBalance);

                // add back previous balance to credit, so the total balance remain the same
                foreach (var oldWabaBalance in newBalance.WabaBalances!)
                {
                    var newWabaBalance = newBalance.WabaBalances!
                        .First(x => x.FacebookWabaId == oldWabaBalance.FacebookWabaId);

                    if (oldWabaBalanceRecord.TryGetValue(newWabaBalance.FacebookWabaId, out var oldBalanceMoney))
                    {
                        newWabaBalance.Credit = MoneyExtensions.Add(newWabaBalance.Credit, oldBalanceMoney);
                    }
                }

                // save the changes
                var updatedBalance = await _businessBalanceRepository.UpsertAndGetAsync(
                    newBalance,
                    newBalance.FacebookBusinessId);

                _logger.LogInformation(
                    "[OneTimePatchExistingWabaBalances] Old Balance:{oldBalance}, New Balance {updatedBalance}",
                    clonedOldBalance,
                    updatedBalance);
            }
        }
        catch(Exception e)
        {
            _logger.LogError("[OneTimePatchExistingWabaBalances] {error}", e);
        }

        return new OneTimePatchExistingWabaBalancesOutput();
    }
}