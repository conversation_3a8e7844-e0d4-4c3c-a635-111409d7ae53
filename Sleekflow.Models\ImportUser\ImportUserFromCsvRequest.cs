using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Models.ImportUser;

public class ImportUserFromCsvRequest : IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public List<string> BlobNames { get; }

    public string Location { get; }

    public bool? IsEnterpriseUsers { get; }

    public ImportUserFromCsvRequest(
        List<string> blobNames,
        string location,
        string sleekflowCompanyId,
        bool? isEnterpriseUsers = false)
    {
        BlobNames = blobNames;
        Location = location;
        IsEnterpriseUsers = isEnterpriseUsers;
        SleekflowCompanyId = sleekflowCompanyId;
    }
}