using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetWhatsappCloudApiBusinessBalanceAutoTopUpProfile
    : ITrigger<
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfile.GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput,
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfile.GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput>
{
    private readonly IBusinessBalanceAutoTopUpProfileService _businessBalanceAutoTopUpProfileService;

    public class GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("facebook_business_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput(
            string sleekflowCompanyId,
            string facebookBusinessId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput
    {
        [JsonProperty("business_balance_auto_top_up_profile")]
        public BusinessBalanceAutoTopUpProfileDto? BusinessBalanceAutoTopUpProfile { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput(
            BusinessBalanceAutoTopUpProfile? businessBalanceAutoTopUpProfile)
        {
            BusinessBalanceAutoTopUpProfile = businessBalanceAutoTopUpProfile is null
                ? null
                : new BusinessBalanceAutoTopUpProfileDto(businessBalanceAutoTopUpProfile);
        }
    }

    public GetWhatsappCloudApiBusinessBalanceAutoTopUpProfile(
        IBusinessBalanceAutoTopUpProfileService businessBalanceAutoTopUpProfileService)
    {
        _businessBalanceAutoTopUpProfileService = businessBalanceAutoTopUpProfileService;
    }

    public async Task<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput> F(
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput input)
    {
        var result =
            await _businessBalanceAutoTopUpProfileService.GetWithFacebookBusinessIdAsync(input.FacebookBusinessId);

        return result is null
            ? new GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput(null!)
            : new GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput(result);
    }
}