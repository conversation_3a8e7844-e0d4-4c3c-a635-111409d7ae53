using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.WorkflowAgentConfigMappings;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.FlowHub;

namespace Sleekflow.FlowHub.Consumers;

public class GetActiveWorkflowCountsByAgentConfigIdsRequestConsumerDefinition
  : ConsumerDefinition<GetActiveWorkflowCountsByAgentConfigIdsRequestConsumer>
{
  protected override void ConfigureConsumer(
    IReceiveEndpointConfigurator endpointConfigurator,
    IConsumerConfigurator<GetActiveWorkflowCountsByAgentConfigIdsRequestConsumer> consumerConfigurator,
    IRegistrationContext context)
  {
    if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
    {
      serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
      serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
      serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
      serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
    }
    else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
    {
      // do nothing
    }
    else
    {
      throw new Exception("Unable to handle the endpointConfigurator");
    }
  }
}

public class GetActiveWorkflowCountsByAgentConfigIdsRequestConsumer
  : IConsumer<GetActiveWorkflowCountsByAgentConfigIdsRequest>
{
  private readonly IWorkflowService _workflowService;
  private readonly ILogger<GetActiveWorkflowCountsByAgentConfigIdsRequestConsumer> _logger;
  private readonly IWorkflowAgentConfigMappingService _workflowAgentConfigMappingService;

  public GetActiveWorkflowCountsByAgentConfigIdsRequestConsumer(
    IWorkflowService workflowService,
    ILogger<GetActiveWorkflowCountsByAgentConfigIdsRequestConsumer> logger,
    IWorkflowAgentConfigMappingService workflowAgentConfigMappingService)
  {
    _workflowService = workflowService;
    _logger = logger;
    _workflowAgentConfigMappingService = workflowAgentConfigMappingService;
  }

  public async Task Consume(ConsumeContext<GetActiveWorkflowCountsByAgentConfigIdsRequest> context)
  {
    var request = context.Message;
    var workflowCounts = new Dictionary<string, int>();

    try
    {
      // Initialize all counts to 0
      foreach (var agentConfigId in request.AgentConfigIds)
      {
        workflowCounts[agentConfigId] = 0;
      }

      if (!request.AgentConfigIds.Any())
      {
        await context.RespondAsync(new GetActiveWorkflowCountsByAgentConfigIdsReply(workflowCounts));
        return;
      }

      // Get workflow counts for each agent config ID concurrently
      var tasks = request.AgentConfigIds.Select(async agentConfigId =>
      {
        try
        {
          // Get workflow IDs for this agent config
          var workflowIdsFromAgentConfig =
            await _workflowAgentConfigMappingService.GetWorkflowIdsByAgentConfigIdAsync(
              request.SleekflowCompanyId,
              agentConfigId);

          if (!workflowIdsFromAgentConfig.Any())
          {
            return new
            {
              AgentConfigId = agentConfigId, Count = 0
            };
          }

          // Count active workflows for each workflow ID and sum them up
          var countTasks = workflowIdsFromAgentConfig.Select(async workflowId =>
            await _workflowService.CountWorkflowsAsync(
              request.SleekflowCompanyId,
              workflowId: workflowId,
              activationStatus: WorkflowActivationStatuses.Active));

          var counts = await Task.WhenAll(countTasks);
          var totalCount = counts.Sum();

          return new
          {
            AgentConfigId = agentConfigId, Count = totalCount
          };
        }
        catch (Exception ex)
        {
          _logger.LogWarning(
            ex,
            "Failed to get workflow count for agent config {AgentConfigId} in company {SleekflowCompanyId}",
            agentConfigId,
            request.SleekflowCompanyId);

          return new
          {
            AgentConfigId = agentConfigId, Count = 0
          };
        }
      });

      var results = await Task.WhenAll(tasks);

      foreach (var result in results)
      {
        workflowCounts[result.AgentConfigId] = result.Count;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(
        ex,
        "Failed to retrieve workflow counts for company {SleekflowCompanyId} and agent configs {AgentConfigIds}",
        request.SleekflowCompanyId,
        string.Join(", ", request.AgentConfigIds));

      // Return zeros for all agent configs if there's an error
      // This ensures the request doesn't fail completely
    }

    await context.RespondAsync(new GetActiveWorkflowCountsByAgentConfigIdsReply(workflowCounts));
  }
} 