using System.Linq.Expressions;
using Microsoft.Azure.Cosmos;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Expressions;
using Sleekflow.FlowHub.Companies;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.WorkflowExecutions;

public interface IWorkflowExecutionService
{
    Task<WorkflowExecution> CreateWorkflowExecutionAsync(
        string stateId,
        StateIdentity stateIdentity,
        string workflowExecutionStatus,
        string? workflowExecutionReasonCode,
        int numOfNodesExecuted,
        string? workflowType,
        AuditEntity.SleekflowStaff? createdBy,
        DateTimeOffset createdAt);

    Task<(List<WorkflowExecution> WorkflowExecutions, string? NextContinuationToken)>
        GetWorkflowExecutionsAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            WorkflowExecutionFilters workflowExecutionFilters);

    Task<List<WorkflowExecution>> GetWorkflowExecutionsAsync(
        string sleekflowCompanyId,
        List<string> stateIds);

    Task<Dictionary<string, WorkflowStatistics>> GetExecutionStatisticsByWorkflowsAsync(
        string sleekflowCompanyId,
        List<string> workflowIds,
        WorkflowExecutionStatisticsFilters? workflowExecutionStatisticsFilters);

    Task<Dictionary<string, WorkflowStatistics>> GetExecutionStatisticsByWorkflowVersionsAsync(
        string sleekflowCompanyId,
        List<string> workflowVersionedIds,
        WorkflowExecutionStatisticsFilters? workflowExecutionStatisticsFilters);

    Task<WorkflowStatistics> GetWorkflowExecutionStatisticsAsync(
        string sleekflowCompanyId,
        string? workflowId,
        WorkflowExecutionStatisticsFilters workflowExecutionStatisticsFilters);

    Task SetWorkflowExecutionNodesCountAsync(
        string id,
        string stateId,
        string workflowId,
        int numOfNodesExecuted);

    Task<List<string>> GetCompleteExecutionWorkflowIdsAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId);

    Task<List<WorkflowExecutionUsage>> GetExecutionUsagesByWorkflowsAsync(
        string sleekflowCompanyId,
        List<string> workflowIds,
        DateTimeOffset? fromDateTime,
        DateTimeOffset? toDateTime);

    Task<int> GetUniqueWorkflowExecutionCountAsync(
        string sleekflowCompanyId,
        string? workflowType,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime);

    Task<MonthlyWorkflowExecutionUsageInfo> GetMonthlyWorkflowExecutionUsageInfoAsync(FlowHubConfig flowHubConfig);

    Task MarkWorkflowExecutionsAsReenrolledAsync(string sleekflowCompanyId, string stateId);
}

public class WorkflowExecutionService : IWorkflowExecutionService, IScopedService
{
    private readonly ILogger<WorkflowExecutionService> _logger;
    private readonly IIdService _idService;
    private readonly ICompanyUsageCycleService _companyUsageCycleService;
    private readonly IWorkflowExecutionRepository _workflowExecutionRepository;
    private readonly ICacheService _cacheService;

    public WorkflowExecutionService(
        ILogger<WorkflowExecutionService> logger,
        IIdService idService,
        ICompanyUsageCycleService companyUsageCycleService,
        IWorkflowExecutionRepository workflowExecutionRepository,
        ICacheService cacheService)
    {
        _logger = logger;
        _idService = idService;
        _companyUsageCycleService = companyUsageCycleService;
        _workflowExecutionRepository = workflowExecutionRepository;
        _cacheService = cacheService;
    }

    public async Task<WorkflowExecution> CreateWorkflowExecutionAsync(
        string stateId,
        StateIdentity stateIdentity,
        string workflowExecutionStatus,
        string? workflowExecutionReasonCode,
        int numOfNodesExecuted,
        string? workflowType,
        AuditEntity.SleekflowStaff? createdBy,
        DateTimeOffset createdAt)
    {
        try
        {
            return await _workflowExecutionRepository.CreateAndGetAsync(
                new WorkflowExecution(
                    _idService.GetId("WorkflowExecution"),
                    3600 * 24 * 365,
                    stateIdentity.SleekflowCompanyId,
                    stateId,
                    stateIdentity,
                    workflowExecutionStatus,
                    numOfNodesExecuted,
                    workflowExecutionReasonCode,
                    workflowType,
                    createdAt,
                    createdBy),
                new PartitionKeyBuilder()
                    .Add(stateIdentity.WorkflowId)
                    .Add(stateId)
                    .Build());
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Unable to create workflow execution of status {WorkflowExecutionStatus} for state id {StateId} in company {CompanyId}",
                workflowExecutionStatus,
                stateId,
                stateIdentity.SleekflowCompanyId);

            throw;
        }
    }

    public async Task<(List<WorkflowExecution> WorkflowExecutions, string? NextContinuationToken)>
        GetWorkflowExecutionsAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            WorkflowExecutionFilters workflowExecutionFilters)
    {
        Expression<Func<WorkflowExecution, bool>> expression = o =>
            o.SleekflowCompanyId == sleekflowCompanyId;

        var (workflowExecutions, nextContinuationToken) =
            await _workflowExecutionRepository.GetContinuationTokenizedObjectsAsync(
                expression
                    .IfAndAlso(
                        () => workflowExecutionFilters is { WorkflowId: not null },
                        s => s.StateIdentity.WorkflowId == workflowExecutionFilters.WorkflowId)
                    .IfAndAlso(
                        () => workflowExecutionFilters is { WorkflowExecutionStatus: not null },
                        s =>
                            s.WorkflowExecutionStatus == workflowExecutionFilters.WorkflowExecutionStatus)
                    .IfAndAlso(
                        () =>
                            workflowExecutionFilters is { FromDateTime: not null, ToDateTime: not null },
                        s =>
                            workflowExecutionFilters.FromDateTime <= s.CreatedAt
                            && s.CreatedAt <= workflowExecutionFilters.ToDateTime),
                continuationToken,
                limit);

        return (workflowExecutions, nextContinuationToken);
    }

    public async Task<List<WorkflowExecution>> GetWorkflowExecutionsAsync(
        string sleekflowCompanyId,
        List<string> stateIds)
    {
        var workflowExecutions =
            await _workflowExecutionRepository.GetObjectsAsync(
                o =>
                    o.SleekflowCompanyId == sleekflowCompanyId
                    && stateIds.Contains(o.StateId),
                stateIds.Count * 10);

        return workflowExecutions;
    }

    public async Task<Dictionary<string, WorkflowStatistics>> GetExecutionStatisticsByWorkflowsAsync(
        string sleekflowCompanyId,
        List<string> workflowIds,
        WorkflowExecutionStatisticsFilters? workflowExecutionStatisticsFilters)
    {
        if (workflowIds.Count == 0)
        {
            return new Dictionary<string, WorkflowStatistics>();
        }

        var objects =
            await _workflowExecutionRepository.GetObjectsAsync<Dictionary<string, object>>(
                new QueryDefinition(
                        $"""
                         SELECT c.state_identity.workflow_id, c.workflow_execution_status, COUNT(1) count
                         FROM c
                         WHERE c.sleekflow_company_id = @sleekflowCompanyId AND ARRAY_CONTAINS(@workflowIds, c.state_identity.workflow_id) {(
                             workflowExecutionStatisticsFilters is { FromDateTime: not null }
                                 ? "AND c.created_at >= @fromDateTime "
                                 : string.Empty)} {(
                                 workflowExecutionStatisticsFilters is { ToDateTime: not null }
                                     ? "AND c.created_at <= @toDateTime "
                                     : string.Empty)}
                         GROUP BY c.state_identity.workflow_id, c.workflow_execution_status
                         """)
                    .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                    .WithParameter("@workflowIds", workflowIds)
                    .WithParameter("@fromDateTime", workflowExecutionStatisticsFilters?.FromDateTime)
                    .WithParameter("@toDateTime", workflowExecutionStatisticsFilters?.ToDateTime));

        var workflowIdToStatusToNumDicts = objects
            .GroupBy(o => (string) o["workflow_id"])
            .ToDictionary(
                o => o.Key,
                groups =>
                    groups
                        .GroupBy(gg => (string) gg["workflow_execution_status"])
                        .ToDictionary(
                            gg => gg.Key,
                            gg => gg.Sum(g => (long) g["count"])));

        return workflowIdToStatusToNumDicts
            .Select(
                entry =>
                (
                    entry.Key,
                    new WorkflowStatistics(
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Started, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Complete, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Cancelled, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Blocked, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Failed, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Restricted, 0))))
            .ToDictionary(t => t.Key, t => t.Item2);
    }

    public async Task<Dictionary<string, WorkflowStatistics>> GetExecutionStatisticsByWorkflowVersionsAsync(
        string sleekflowCompanyId,
        List<string> workflowVersionedIds,
        WorkflowExecutionStatisticsFilters? workflowExecutionStatisticsFilters)
    {
        if (workflowVersionedIds.Count == 0)
        {
            return new Dictionary<string, WorkflowStatistics>();
        }

        var objects =
            await _workflowExecutionRepository.GetObjectsAsync<Dictionary<string, object>>(
                new QueryDefinition(
                        $"""
                         SELECT c.state_identity.workflow_versioned_id, c.workflow_execution_status, COUNT(1) count
                         FROM c
                         WHERE c.sleekflow_company_id = @sleekflowCompanyId
                            AND ARRAY_CONTAINS(@workflowVersionedIds, c.state_identity.workflow_versioned_id)
                            {( workflowExecutionStatisticsFilters is { FromDateTime: not null }
                                 ? "AND c.created_at >= @fromDateTime "
                                 : string.Empty)}
                            {( workflowExecutionStatisticsFilters is { ToDateTime: not null }
                                     ? "AND c.created_at <= @toDateTime "
                                     : string.Empty)}
                            {( workflowExecutionStatisticsFilters is { WorkflowType: not null }
                                     ? "AND c.workflow_type = @workflowType "
                                     : string.Empty)}
                         GROUP BY c.state_identity.workflow_versioned_id, c.workflow_execution_status
                         """)
                    .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                    .WithParameter(
                        "@workflowVersionedIds",
                        workflowVersionedIds)
                    .WithParameter("@fromDateTime", workflowExecutionStatisticsFilters?.FromDateTime)
                    .WithParameter("@toDateTime", workflowExecutionStatisticsFilters?.ToDateTime)
                    .WithParameter("@workflowType", workflowExecutionStatisticsFilters?.WorkflowType));

        var workflowVersionedIdToStatusDict = objects
            .GroupBy(o => (string) o["workflow_versioned_id"])
            .ToDictionary(
                o => o.Key,
                groups =>
                    groups
                        .GroupBy(gg => (string) gg["workflow_execution_status"])
                        .ToDictionary(
                            gg => gg.Key,
                            gg => gg.Sum(g => (long) g["count"])));

        return workflowVersionedIdToStatusDict
            .Select(
                entry =>
                (
                    entry.Key,
                    new WorkflowStatistics(
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Started, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Complete, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Cancelled, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Blocked, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Failed, 0),
                        entry.Value.GetValueOrDefault(WorkflowExecutionStatuses.Restricted, 0))))
            .ToDictionary(t => t.Key, t => t.Item2);
    }

    public async Task<WorkflowStatistics> GetWorkflowExecutionStatisticsAsync(
        string sleekflowCompanyId,
        string? workflowId,
        WorkflowExecutionStatisticsFilters workflowExecutionStatisticsFilters)
    {
        var objects = await _workflowExecutionRepository.GetObjectsAsync<Dictionary<string, object>>(
            new QueryDefinition(
                    $"""
                     SELECT c.workflow_execution_status, COUNT(1) count
                     FROM c
                     WHERE c.sleekflow_company_id = @sleekflowCompanyId
                     {( workflowId != null
                         ? " AND c.state_identity.workflow_id = @workflowId "
                         : string.Empty )}
                     {( !string.IsNullOrEmpty(workflowExecutionStatisticsFilters.WorkflowType)
                         ? "AND c.workflow_type = @workflowType "
                         : string.Empty )}
                     {( workflowExecutionStatisticsFilters.FromDateTime.HasValue
                        ? "AND c.created_at >= @fromDateTime "
                        : string.Empty )}
                     {( workflowExecutionStatisticsFilters.ToDateTime.HasValue
                         ? "AND c.created_at <= @toDateTime "
                         : string.Empty )}
                     GROUP BY c.workflow_execution_status
                     """)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@workflowId", workflowId)
                .WithParameter("@fromDateTime", workflowExecutionStatisticsFilters.FromDateTime)
                .WithParameter("@toDateTime", workflowExecutionStatisticsFilters.ToDateTime)
                .WithParameter("@workflowType", workflowExecutionStatisticsFilters.WorkflowType));

        var statusToNumDict = objects
            .GroupBy(o => (string) o["workflow_execution_status"])
            .ToDictionary(o => o.Key, groups => groups.Sum(g => (long) g["count"]));

        return new WorkflowStatistics(
            statusToNumDict.GetValueOrDefault(WorkflowExecutionStatuses.Started, 0),
            statusToNumDict.GetValueOrDefault(WorkflowExecutionStatuses.Complete, 0),
            statusToNumDict.GetValueOrDefault(WorkflowExecutionStatuses.Cancelled, 0),
            statusToNumDict.GetValueOrDefault(WorkflowExecutionStatuses.Blocked, 0),
            statusToNumDict.GetValueOrDefault(WorkflowExecutionStatuses.Failed, 0),
            statusToNumDict.GetValueOrDefault(WorkflowExecutionStatuses.Restricted, 0));
    }

    public async Task SetWorkflowExecutionNodesCountAsync(
        string id,
        string stateId,
        string workflowId,
        int numOfNodesExecuted)
    {
        await _workflowExecutionRepository.PatchAsync(
            id,
            new PartitionKeyBuilder()
                .Add(workflowId)
                .Add(stateId)
                .Build(),
            new List<PatchOperation>()
            {
                PatchOperation.Set($"/{WorkflowExecutionFieldNames.NumOfNodesExecuted}", numOfNodesExecuted)
            });
    }

    public async Task<List<string>> GetCompleteExecutionWorkflowIdsAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId)
    {
        var completeExecutionEnrollments =
            await _workflowExecutionRepository.GetObjectsAsync(
                o =>
                    o.SleekflowCompanyId == sleekflowCompanyId
                    && o.StateIdentity.ObjectId == objectId
                    && o.StateIdentity.ObjectType == objectType
                    && o.WorkflowExecutionStatus == WorkflowExecutionStatuses.Complete);

        return completeExecutionEnrollments
            .Select(e => e.StateIdentity.WorkflowId)
            .Distinct()
            .ToList();
    }

    public async Task<List<WorkflowExecutionUsage>> GetExecutionUsagesByWorkflowsAsync(
        string sleekflowCompanyId,
        List<string> workflowIds,
        DateTimeOffset? fromDateTime,
        DateTimeOffset? toDateTime)
    {
        if (workflowIds.Count == 0)
        {
            return new List<WorkflowExecutionUsage>();
        }

        var workflowsExecutionCount =
            await _workflowExecutionRepository.GetObjectsAsync<Dictionary<string, object>>(
                queryDefinition: new QueryDefinition(
                        query: $"""
                                SELECT c.state_identity.workflow_id,
                                    COUNT(c.workflow_execution_status = '{WorkflowExecutionStatuses.Started}' ? 1 : undefined) total_execution_count,
                                    COUNT(c.workflow_execution_status = '{WorkflowExecutionStatuses.Failed}' ? 1 : undefined) failed_execution_count
                                FROM c
                                WHERE c.sleekflow_company_id = @sleekflowCompanyId
                                   AND ARRAY_CONTAINS(@workflowIds, c.state_identity.workflow_id)
                                   AND (c.workflow_execution_status = '{WorkflowExecutionStatuses.Started}' OR c.workflow_execution_status = '{WorkflowExecutionStatuses.Failed}')
                                   {(
                                       fromDateTime.HasValue
                                           ? "AND c.created_at >= @fromDateTime "
                                           : string.Empty)} {(
                                           toDateTime.HasValue
                                               ? "AND c.created_at <= @toDateTime "
                                               : string.Empty)}
                                GROUP BY c.state_identity.workflow_id
                                """)
                    .WithParameter(name: "@sleekflowCompanyId", value: sleekflowCompanyId)
                    .WithParameter(name: "@workflowIds", value: workflowIds)
                    .WithParameter(name: "@fromDateTime", value: fromDateTime)
                    .WithParameter(name: "@toDateTime", value: toDateTime));

        var workflowsLastEnrolledAt = await _workflowExecutionRepository.GetObjectsAsync<Dictionary<string, object>>(
            queryDefinition: new QueryDefinition(
                query: $"""
                        SELECT c.state_identity.workflow_id, MAX(c.created_at) last_enrolled_at
                        FROM c
                        WHERE c.sleekflow_company_id = @sleekflowCompanyId
                           AND ARRAY_CONTAINS(@workflowIds, c.state_identity.workflow_id)
                           AND c.workflow_execution_status = '{WorkflowExecutionStatuses.Started}'
                           {(
                               fromDateTime.HasValue
                                   ? "AND c.created_at >= @fromDateTime "
                                   : string.Empty)}{(
                                   toDateTime.HasValue
                                       ? "AND c.created_at <= @toDateTime "
                                       : string.Empty)}
                        GROUP BY c.state_identity.workflow_id
                        """)
                .WithParameter(name: "@sleekflowCompanyId", value: sleekflowCompanyId)
                .WithParameter(name: "@workflowIds", value: workflowIds)
                .WithParameter(name: "@fromDateTime", value: fromDateTime)
                .WithParameter(name: "@toDateTime", value: toDateTime));

        var workflowsExecutionCountDict = workflowsExecutionCount
            .GroupBy(x => x["workflow_id"])
            .ToDictionary(
                x => (string) x.Key,
                x => x.Single());

        var workflowsLastEnrolledAtDict = workflowsLastEnrolledAt
            .GroupBy(x => x["workflow_id"])
            .ToDictionary(
                x => (string) x.Key,
                x => x.Single());

        return workflowIds.Select(
                workflowId => new WorkflowExecutionUsage(
                    workflowId,
                    workflowsExecutionCountDict.GetValueOrDefault(workflowId, new Dictionary<string, object>())
                        .TryGetValue("total_execution_count", out var totalExecutionCount) ? (long) totalExecutionCount : 0,
                    workflowsExecutionCountDict.GetValueOrDefault(workflowId, new Dictionary<string, object>())
                        .TryGetValue("failed_execution_count", out var totalFailedCount) ? (long) totalFailedCount : 0,
                    workflowsLastEnrolledAtDict.GetValueOrDefault(workflowId, new Dictionary<string, object>())
                        .TryGetValue("last_enrolled_at", out var lastEnrolledAt) ? (DateTimeOffset) lastEnrolledAt : null))
            .ToList();
    }

    public Task<int> GetUniqueWorkflowExecutionCountAsync(
        string sleekflowCompanyId,
        string? workflowType,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime)
        => _workflowExecutionRepository.GetUniqueWorkflowExecutionCountAsync(
            sleekflowCompanyId,
            workflowType,
            fromDateTime,
            toDateTime);

    public async Task<MonthlyWorkflowExecutionUsageInfo> GetMonthlyWorkflowExecutionUsageInfoAsync(FlowHubConfig flowHubConfig)
    {
        if (!flowHubConfig.IsUsageLimitEnabled || flowHubConfig.UsageLimit is null)
        {
            return new(false, 0d);
        }

        var usageLimit = flowHubConfig.UsageLimit;
        var usageLimitOffset = flowHubConfig.UsageLimitOffset;
        var sleekflowCompanyId = flowHubConfig.SleekflowCompanyId;

        var workflowExecutionsStatistics = await GetCurrentUsageCycleWorkflowExecutionStatisticsAsync(flowHubConfig);
        var workflowExecutionsOfCurrentMonthCount = workflowExecutionsStatistics.NumOfStartedWorkflows -
                                                    workflowExecutionsStatistics.NumOfFailedWorkflows;

        var maximumNumOfMonthlyWorkflowExecutions = usageLimit.MaximumNumOfMonthlyWorkflowExecutions;

        if (flowHubConfig.IsUsageLimitOffsetEnabled && usageLimitOffset?.MaximumNumOfMonthlyWorkflowExecutionsOffset is not null)
        {
            maximumNumOfMonthlyWorkflowExecutions += usageLimitOffset.MaximumNumOfMonthlyWorkflowExecutionsOffset;
        }

        var isExceedMonthlyWorkflowExecutions = workflowExecutionsOfCurrentMonthCount >= maximumNumOfMonthlyWorkflowExecutions;
        var usagePercentage = Math.Round((double) workflowExecutionsOfCurrentMonthCount / maximumNumOfMonthlyWorkflowExecutions!.Value, 2) * 100;

        if (isExceedMonthlyWorkflowExecutions)
        {
            _logger.LogWarning(
                "Company {CompanyId} exceeded monthly workflow execution limit of {MonthlyWorkflowExecutionLimit}, current count is {CurrentMonthWorkflowExecutionCount}",
                sleekflowCompanyId,
                maximumNumOfMonthlyWorkflowExecutions,
                workflowExecutionsOfCurrentMonthCount);
        }

        return new(isExceedMonthlyWorkflowExecutions, usagePercentage);
    }

    public async Task MarkWorkflowExecutionsAsReenrolledAsync(
        string sleekflowCompanyId,
        string stateId)
    {
        var workflowExecutionsToDelete = await _workflowExecutionRepository.GetObjectsAsync(
            x =>
                x.SleekflowCompanyId == sleekflowCompanyId
                && x.StateId == stateId
                && x.WorkflowExecutionStatus == WorkflowExecutionStatuses.Restricted);

        await Parallel.ForEachAsync(
            workflowExecutionsToDelete,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 2
            },
            async (we, ct) =>
            {
                await _workflowExecutionRepository.PatchAsync(
                    we.Id,
                    new PartitionKeyBuilder()
                        .Add(we.StateIdentity.WorkflowId)
                        .Add(we.StateId)
                        .Build(),
                    new List<PatchOperation>()
                    {
                        PatchOperation.Set("/workflow_execution_status", WorkflowExecutionStatuses.Reenrolled)
                    },
                    cancellationToken: ct);
            });
    }

    private async Task<WorkflowStatistics> GetCurrentUsageCycleWorkflowExecutionStatisticsAsync(FlowHubConfig flowHubConfig)
    {
        return await _cacheService.CacheAsync(
            $"{nameof(GetCurrentUsageCycleWorkflowExecutionStatisticsAsync)}:{flowHubConfig.SleekflowCompanyId}",
            async () =>
            {
                var (fromDateTime, toDateTime) = await _companyUsageCycleService.GetUsageCycleAsync(flowHubConfig);

                var filter = new WorkflowExecutionStatisticsFilters(fromDateTime, toDateTime, WorkflowType.Normal);

                return await GetWorkflowExecutionStatisticsAsync(flowHubConfig.SleekflowCompanyId, null, filter);
            },
            TimeSpan.FromMinutes(1));
    }
}