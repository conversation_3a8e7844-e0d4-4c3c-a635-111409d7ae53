﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.CrmHub.SchemafulObjects;

public interface ISchemafulObjectRepository : IRepository<SchemafulObject>
{
    Task<SchemafulObject> PatchAndGetSchemafulObjectAsync(
        string id,
        PartitionKey partitionKey,
        Dictionary<string, object?> propertyValues,
        Dictionary<string, object?> indexedPropertyValues,
        string sleekflowUserProfileId,
        string updatedVia,
        AuditEntity.SleekflowStaff? updatedBy = null,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    Task<int> GetSchemafulObjectCountBySchemasAsync(string sleekflowCompanyId, List<string> schemaIds);
}

public class SchemafulObjectRepository
    : BaseRepository<SchemafulObject>, ISchemafulObjectRepository, IScopedService
{
    private readonly ILogger<SchemafulObjectRepository> _logger;

    public SchemafulObjectRepository(
        ILogger<SchemafulObjectRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
        _logger = logger;
    }

    public async Task<SchemafulObject> PatchAndGetSchemafulObjectAsync(
        string id,
        PartitionKey partitionKey,
        Dictionary<string, object?> propertyValues,
        Dictionary<string, object?> indexedPropertyValues,
        string sleekflowUserProfileId,
        string updatedVia,
        AuditEntity.SleekflowStaff? updatedBy = null,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        var container = GetContainer();
        var retryPolicy = GetRetryPolicy();
        var patchOperations = new List<PatchOperation>
        {
            Replace(SchemafulObject.PropertyNamePropertyValues, propertyValues),
            Replace(SchemafulObject.PropertyNameIndexedPropertyValues, indexedPropertyValues),
            Replace(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId, sleekflowUserProfileId),
            Set(AuditEntity.PropertyNameUpdatedBy, updatedBy),
            Set(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow),
            Set(SchemafulObject.PropertyNameUpdatedVia, updatedVia)
        };

        var policyResult = await retryPolicy.ExecuteAndCaptureAsync(
            async _ =>
            {
                var itemResponse =
                    await container.PatchItemAsync<SchemafulObject>(
                        id,
                        partitionKey,
                        patchOperations,
                        cancellationToken: cancellationToken,
                        requestOptions: new PatchItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = true,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "PatchAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return itemResponse.Resource;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to patch the item {Id} {PartitionKey}",
            id,
            partitionKey);

        throw new SfQueryException(policyResult.FinalException, nameof(PatchAndGetSchemafulObjectAsync));
    }

    public async Task<int> GetSchemafulObjectCountBySchemasAsync(string sleekflowCompanyId, List<string> schemaIds)
    {
        if (!schemaIds.Any())
        {
            return 0;
        }

        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                $"""
                 SELECT
                     VALUE COUNT(1)
                 FROM
                     %%CONTAINER_NAME%% r
                 WHERE
                     r.sleekflow_company_id = @sleekflowCompanyId
                 AND
                     ARRAY_CONTAINS(@schemaIds, r.{SchemafulObject.PropertyNameSchemaId})
                 """)
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@schemaIds", schemaIds);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int?>(qd);
        var queryResult = await itemQueryIterator.ReadNextAsync();

        return queryResult.FirstOrDefault() ?? 0;
    }
}