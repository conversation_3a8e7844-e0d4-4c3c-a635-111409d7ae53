using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using Azure.Storage.Blobs;
using Microsoft.SemanticKernel;
using PdfSharp.Pdf;
using PdfSharp.Pdf.IO;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.FaqAgents;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.FaqAgents;

public interface IFaqAgentFileProcessingService
{
    Task ProcessFilesAsync(string sleekflowCompanyId);
}

public class FaqAgentFileProcessingService : IFaqAgentFileProcessingService, IScopedService
{
    private readonly IAzureFormRecognizerConfig _azureFormRecognizerConfig;
    private readonly IStorageConfig _storageConfig;
    private readonly IFaqAgentSearchIndexer _faqAgentSearchIndexer;
    private readonly Kernel _kernel;
    private readonly ILogger<FaqAgentFileProcessingService> _logger;
    private readonly ISummaryPlugin _summaryPlugin;

    public FaqAgentFileProcessingService(
        IAzureFormRecognizerConfig azureFormRecognizerConfig,
        IStorageConfig storageConfig,
        IFaqAgentSearchIndexer faqAgentSearchIndexer,
        Kernel kernel,
        ILogger<FaqAgentFileProcessingService> logger,
        ISummaryPlugin summaryPlugin)
    {
        _azureFormRecognizerConfig = azureFormRecognizerConfig;
        _storageConfig = storageConfig;
        _faqAgentSearchIndexer = faqAgentSearchIndexer;
        _kernel = kernel;
        _logger = logger;
        _summaryPlugin = summaryPlugin;
    }

    public async Task ProcessFilesAsync(string sleekflowCompanyId)
    {
        await _faqAgentSearchIndexer.CreateSearchIndexAsync(sleekflowCompanyId);

        var blobContainerClient = new BlobServiceClient(_storageConfig.SourceFileStorageAccountConnStr)
            .GetBlobContainerClient(sleekflowCompanyId);

        var blobNames = new List<string>();
        await foreach (var blob in blobContainerClient.GetBlobsAsync())
        {
            blobNames.Add(blob.Name);
        }

        foreach (var name in blobNames)
        {
            _logger.LogInformation("Processing file {Name}", name);

            var blobClient = blobContainerClient.GetBlobClient(name);

            using var stream = new MemoryStream();
            await blobClient.DownloadToAsync(stream);
            stream.Position = 0;

            List<(int Index, string Text)> allPageIndexAndTextTuples = new ();

            var chunks = SplitPdfIntoChunks(stream, 20);
            foreach (var (chunkPdfStream, startPage, endPage) in chunks)
            {
                _logger.LogInformation(
                    "Processing chunk: {StartPage} - {EndPage}",
                    startPage,
                    endPage);

                var pageIndexAndTextTuples = await GetDocumentAsIndexAndTextTuplesAsync(chunkPdfStream);
                allPageIndexAndTextTuples.AddRange(pageIndexAndTextTuples);

                _logger.LogInformation(
                    "Processed chunk: {StartPage} - {EndPage}",
                    startPage,
                    endPage);

                await chunkPdfStream.DisposeAsync();
            }

            var allPageDetails = new List<FaqAgentPageDetail>();

            var offset = 0L;
            var index = 0;
            foreach (var (_, text) in allPageIndexAndTextTuples)
            {
                allPageDetails.Add(new FaqAgentPageDetail(index, offset, text));

                index += 1;
                offset += text.Length;
            }

            var sections = CreateSections(
                allPageDetails,
                Path.GetFileNameWithoutExtension(name));
            await _faqAgentSearchIndexer.IndexDocumentsAsync(sections, sleekflowCompanyId);

            _logger.LogInformation("Processed file {Name}", name);
        }
    }

    private static List<(Stream Stream, int StartPage, int EndPage)> SplitPdfIntoChunks(
        Stream stream,
        int pagesPerFile)
    {
        var outputStreams = new List<(Stream Stream, int StartPage, int EndPage)>();

        using var inputPdf = PdfReader.Open(stream, PdfDocumentOpenMode.Import);

        var pageCount = inputPdf.PageCount;
        for (var startPage = 0; startPage < pageCount; startPage += pagesPerFile)
        {
            using var outputPdf = new PdfDocument();

            for (var i = startPage; i < Math.Min(startPage + pagesPerFile, pageCount); i++)
            {
                outputPdf.AddPage(inputPdf.Pages[i]);
            }

            var outputStream = new MemoryStream();
            outputPdf.Save(outputStream);
            outputStream.Position = 0;

            // Calculate the endPage for the current chunk
            var endPage = Math.Min(startPage + pagesPerFile, pageCount) - 1;

            // Add the output stream along with start and end page numbers
            outputStreams.Add((outputStream, startPage, endPage));
        }

        return outputStreams;
    }

    private async Task<IReadOnlyList<(int Index, string Text )>> GetDocumentAsIndexAndTextTuplesAsync(Stream stream)
    {
        var documentClient = new DocumentAnalysisClient(
            new Uri(_azureFormRecognizerConfig.AzureFormRecognizerEndpoint),
            new AzureKeyCredential(_azureFormRecognizerConfig.AzureFormRecognizerKey),
            new DocumentAnalysisClientOptions
            {
                Diagnostics =
                {
                    IsLoggingContentEnabled = true
                }
            });

        var operation = await documentClient.AnalyzeDocumentAsync(WaitUntil.Started, "prebuilt-layout", stream);

        List<(int Index, string Text)> pageTuples = new ();

        var analyzeResponse = await operation.WaitForCompletionAsync();
        var documentPages = analyzeResponse.Value.Pages;

        await Parallel.ForEachAsync(
            Enumerable.Range(0, documentPages.Count),
            async (i, ct) =>
            {
                _logger.LogInformation("Processing the page {Page}", i);

                var documentPage = documentPages[i];

                var spanIndex = documentPage.Spans[0].Index;
                var spanLength = documentPage.Spans[0].Length;

                // Mark all positions of the spans in the page
                var spanChars = Enumerable.Repeat(-1, spanLength).ToArray();

                IReadOnlyList<DocumentTable> tablesOnPage =
                    analyzeResponse.Value.Tables
                        .Where(t => t.BoundingRegions[0].PageNumber == i + 1)
                        .ToList();
                for (var tableId = 0; tableId < tablesOnPage.Count; tableId++)
                {
                    var documentTable = tablesOnPage[tableId];

                    foreach (var span in documentTable.Spans)
                    {
                        // Replace all table spans with "tableId" in tableChars array
                        for (var j = 0; j < span.Length; j++)
                        {
                            var index = span.Index - spanIndex + j;
                            if (index >= 0 && index < spanLength)
                            {
                                spanChars[index] = tableId;
                            }
                        }
                    }
                }

                StringBuilder textSb = new ();

                HashSet<int> processedTable = new ();
                for (var j = 0; j < spanChars.Length; j++)
                {
                    var isTableSpan = spanChars[j] != -1;

                    if (isTableSpan)
                    {
                        if (processedTable.Contains(spanChars[j]))
                        {
                            // Already processed this table
                            continue;
                        }

                        var table = tablesOnPage[spanChars[j]];

                        var tableHtmlStr = GetTableHtml(table);
                        var tableAsText = await _summaryPlugin.TransformHtmlTableToText(_kernel, tableHtmlStr);

                        textSb.Append(tableAsText);
                        processedTable.Add(spanChars[j]);
                    }
                    else if (!isTableSpan)
                    {
                        textSb.Append(analyzeResponse.Value.Content[spanIndex + j]);
                    }
                }

                textSb.Append(' ');

                lock (pageTuples)
                {
                    pageTuples.Add((i, textSb.ToString()));
                }

                _logger.LogInformation("Processed the page {Page}", i);
            });

        return pageTuples.OrderBy(pd => pd.Index).ToList().AsReadOnly();
    }

    private static string GetTableHtml(DocumentTable table)
    {
        var tableHtmlSb = new StringBuilder("<table>");

        var rows = new List<DocumentTableCell>[table.RowCount];
        for (var k = 0; k < table.RowCount; k++)
        {
            rows[k] = table.Cells
                .Where(c => c.RowIndex == k)
                .OrderBy(c => c.ColumnIndex)
                .ToList();
        }

        foreach (var rowCells in rows)
        {
            tableHtmlSb.Append("<tr>");

            foreach (var cell in rowCells)
            {
                var cellTag = (cell.Kind == "columnHeader" || cell.Kind == "rowHeader") ? "th" : "td";
                var colRowSpan =
                    string.Empty
                    + (cell.ColumnSpan > 1 ? $" colspan='{cell.ColumnSpan}'" : string.Empty)
                    + (cell.RowSpan > 1 ? $" rowspan='{cell.RowSpan}'" : string.Empty);

                tableHtmlSb.AppendFormat(
                    "<{0}{1}>{2}</{0}>",
                    cellTag,
                    colRowSpan,
                    WebUtility.HtmlEncode(cell.Content));
            }

            tableHtmlSb.Append("</tr>");
        }

        tableHtmlSb.Append("</table>");

        return tableHtmlSb.ToString();
    }

    private static IEnumerable<FaqAgentDocument> CreateSections(
        IReadOnlyList<FaqAgentPageDetail> pageDetails,
        string fileName)
    {
        const int maxSectionLength = 1_000;
        const int sentenceSearchLimit = 100;
        const int sectionOverlap = 100;

        var sentenceEndings = new[]
        {
            '.',
            '。',
            '．',

            '!',
            '！',
            '﹗',

            '?',
            '﹖',
            '？',
        };
        var wordBreaks = new[]
        {
            ',',
            '﹐',
            '，',
            '､',
            '、',

            ';',
            '﹔',
            '；',

            ':',
            '﹕',
            '︰',

            ' ',
            '(',
            ')',
            '[',
            ']',
            '{',
            '}',

            '\t',
            '\n',
        };
        var allText = string.Concat(pageDetails.Select(p => p.Text));
        var length = allText.Length;
        var start = 0;
        var end = length;
        var pattern = "[^0-9a-zA-Z_-]";

        while (start + sectionOverlap < length)
        {
            var lastWord = -1;
            end = start + maxSectionLength;

            if (end > length)
            {
                end = length;
            }
            else
            {
                // Try to find the end of the sentence
                while (end < length
                       && (end - start - maxSectionLength) < sentenceSearchLimit
                       && !sentenceEndings.Contains(allText[end]))
                {
                    if (wordBreaks.Contains(allText[end]))
                    {
                        lastWord = end;
                    }

                    end++;
                }

                if (end < length && !sentenceEndings.Contains(allText[end]) && lastWord > 0)
                {
                    end = lastWord; // Fall back to at least keeping a whole word
                }
            }

            if (end < length)
            {
                end++;
            }

            // Try to find the start of the sentence or at least a whole word boundary
            lastWord = -1;
            while (start > 0
                   && start > end - maxSectionLength - (2 * sentenceSearchLimit)
                   && !sentenceEndings.Contains(allText[start]))
            {
                if (wordBreaks.Contains(allText[start]))
                {
                    lastWord = start;
                }

                start--;
            }

            if (!sentenceEndings.Contains(allText[start]) && lastWord > 0)
            {
                start = lastWord;
            }

            if (start > 0)
            {
                start++;
            }

            var sectionText = allText[start..end];

            yield return new FaqAgentDocument(
                id: Regex.Replace($"{fileName}-{start}", pattern, "_").TrimStart('_'),
                content: sectionText,
                sourcePage: $"{fileName}-{FindPage(pageDetails, start)}.pdf",
                sourceFile: $"{fileName}.pdf");

            var lastTableStart = sectionText.LastIndexOf("<table", StringComparison.Ordinal);
            if (lastTableStart > 2 * sentenceSearchLimit &&
                lastTableStart > sectionText.LastIndexOf("</table", StringComparison.Ordinal))
            {
                // If the section ends with an unclosed table, we need to start the next section with the table.
                // If table starts inside SentenceSearchLimit, we ignore it, as that will cause an infinite loop for tables longer than MaxSectionLength
                // If last table starts inside SectionOverlap, keep overlapping.
                start = Math.Min(end - sectionOverlap, start + lastTableStart);
            }
            else
            {
                start = end - sectionOverlap;
            }
        }

        if (start + sectionOverlap < end)
        {
            yield return new FaqAgentDocument(
                id: Regex.Replace($"{fileName}-{start}", pattern, "_").TrimStart('_'),
                content: allText[start..end],
                sourcePage: $"{fileName}-{FindPage(pageDetails, start)}.pdf",
                sourceFile: fileName);
        }
    }

    private static int FindPage(IReadOnlyList<FaqAgentPageDetail> pageMap, int offset)
    {
        var length = pageMap.Count;
        for (var i = 0; i < length - 1; i++)
        {
            if (offset >= pageMap[i].Offset && offset < pageMap[i + 1].Offset)
            {
                return i;
            }
        }

        return length - 1;
    }
}