﻿using System.Collections.Concurrent;
using System.Diagnostics;
using Auth0.ManagementApi.Models;
using Newtonsoft.Json;
using Sleekflow.Auth0.BulkImporter.Models;
using Sleekflow.Auth0.BulkImporter.Services;

const int maxNumOfConcurrentReqs = 1;
const int runBatchSize = 80;
const int testGetUsersSize = 0; // set testGetUsersSize = 0  if get all users.
const long fileSizeLimit = 500000;
const bool upsertRequest = true;
/*
 * Use the following Auth0 Management API to get the connection id.
 * https://sleekflow-dev.eu.auth0.com/api/v2/connections
 */

#region Self DEV

/*
const string clientId = "xWV8z3fWUf5Wzqm0l623dWaG22D8ZgE3";
const string clientSecret = "FJ2tb0MGcr2bwf4dAO9LweYzgkamXQFcIPFOot91K9or3fapfDpsjzdiUoWSj8eD";
const string connectionId = "con_l8nbZRyIu1TW05bM";
const string audience = "https://powerflow-test.jp.auth0.com/api/v2/";
const string connString =
    "Server=tcp:traviscrmdbhk.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=travis;Password=*********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Column Encryption Setting=Enabled;Connection Timeout=120;";
*/

/*
// Should be use Machine to Machine application.
const string clientId = "LNsWg9R2OS2zwPH9neVVj55ls2OAti4n";
const string clientSecret = "C8v_WLvROqgeNlgiiOYqzzku8m0_Iku09Eawzklzy_6aLpR0zGMNuAbzEmGHjMe2";
// In Authentication => Database => Identifier
const string connectionId = "con_5XDkgEYlvGZZqIOD";
const string audience = "https://pulumi-test4.jp.auth0.com/api/v2/";
const string connString =
    "Server=tcp:traviscrmdbhk.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=travis;Password=*********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Column Encryption Setting=Enabled;Connection Timeout=120;";
*/

#endregion

#region Sleekflow DEV

const string clientId = "KkA7yjPAuBFWl50cE1drTdyf7HiusQMg";
const string clientSecret = "lDDbBMcEzWfOFFQjeMLDT--eVBaWDMoELdHbyWyTb2uP5_cAKgSj7XHHkN4bZ8fe";
const string connectionId = "con_K0uHCFuKNuiBpLqV";
const string audience = "https://sleekflow-dev.eu.auth0.com/api/v2/";
const string connString =
    "Server=tcp:traviscrmdbhk.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=travis;Password=*****************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Column Encryption Setting=Enabled;Connection Timeout=120;";

#endregion

#region Sleekflow Pre-Production
/*
const string clientId = "KkA7yjPAuBFWl50cE1drTdyf7HiusQMg";
const string clientSecret = "lDDbBMcEzWfOFFQjeMLDT--eVBaWDMoELdHbyWyTb2uP5_cAKgSj7XHHkN4bZ8fe";
const string connectionId = "con_K0uHCFuKNuiBpLqV";
const string audience = "https://sleekflow-dev.eu.auth0.com/api/v2/";
const string connString =
    "Server=tcp:traviscrmdbhk.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=travis;Password=*****************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Column Encryption Setting=Enabled;Connection Timeout=120;";
*/
#endregion

#region Sleekflow Production

/*
const string clientId = "";
const string clientSecret = "";
const string connectionId = "";
const string audience = "https://sleekflow.eu.auth0.com/api/v2/";
const string connString =
    "Server=tcp:sleekflow-sql.database.windows.net,1433;Initial Catalog=SleekFlow_Main_DB;Persist Security Info=False;User ID=sleekflow-maindb-svr;Password=*****$Xyn8vCbACUw;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
*/

#endregion

const string tmpPath = "_tmp";
const string pwdUsersDir = tmpPath + "/userPwd";
const string socialUsersDir = tmpPath + "/userSocial";
const string updateUsersDir = tmpPath + "/userUpdate";
const string grantType = "client_credentials";

Console.WriteLine("\nAuth0 users importer started.\n");

var userImporter = await Auth0UserImporter.InitAsync(clientId, clientSecret, audience, grantType);
var sleekflowUserService = new SleekflowUserService(connString);

try
{
    var progress = new Progress<float>(percent => Console.Write($"\r{percent:P}"));

    var pwdUsers = new List<ImportUser>();
    var socialUsers = new List<ImportUser>();
    var allUserIds = new List<string>(); // For bulk delete.

    Console.Write("Fetching users from database.. \n");

    var users = await sleekflowUserService.FetchAndConvertUsers(testGetUsersSize, progress);
    foreach (var user in users)
    {
        if (user.CustomPasswordHash == null)
        {
            /*
             * Assign Random password to social users.
             * Auth0 Compatible passwords should be hashed using bcrypt $2a$ or $2b$ and have 10 saltRounds.
             */
            user.PasswordHash = BCrypt.Net.BCrypt.EnhancedHashPassword(
                inputKey: EmailUtils.GenerateRandomString(32),
                workFactor: 10);
            socialUsers.Add(user);
        }
        else
        {
            pwdUsers.Add(user);
        }

        Debug.Assert(user.UserId != null, "user.UserId != null");
        allUserIds.Add(user.UserId);
    }

    Console.Write(
        $"  done. \n\nReturn results: \nTotal: {users.Count}, Password users: {pwdUsers.Count}, Social Users: {socialUsers.Count}\n");
    Console.Write("\n\nCleaning up cache folder .. ");
    await Task.Delay(200);

    var exists = Directory.Exists(tmpPath);
    if (!exists)
    {
        Directory.CreateDirectory(tmpPath);
    }

    var dirInfo = new DirectoryInfo(tmpPath);
    if (exists)
    {
        foreach (var dir in dirInfo.GetFiles())
        {
            await Task.Run(() => dir.Delete());
        }

        foreach (var dir in dirInfo.GetDirectories())
        {
            dir.Delete(true);
        }
    }

    await Auth0UserImporter.WriteUserIdsFile(allUserIds, tmpPath);

    Directory.CreateDirectory(pwdUsersDir);
    Directory.CreateDirectory(socialUsersDir);
    Directory.CreateDirectory(updateUsersDir);

    Console.Write(" done.\n");
    Console.Write("---------------------------------------------------\n");
    Console.Write("Creating batch file .. \n");
    Console.Write($"Writing {users.Count} results in to file.. \n");
    Console.Write("Writing password user files .. \n");

    var totalPwdUserFiles =
        await Auth0UserImporter.WriteUsersToFiles(pwdUsers, pwdUsersDir, runBatchSize, fileSizeLimit);
    Console.Write("Writing social user files .. \n");
    var totalSocialUserFiles =
        await Auth0UserImporter.WriteUsersToFiles(socialUsers, socialUsersDir, runBatchSize, fileSizeLimit);

    Console.Write("\n---------------------------------------------------\n");
    Console.Write($"Sending users files to server (upsert={upsertRequest.ToString()}) ... \n");

    var pwdUsersDirInfo = new DirectoryInfo(pwdUsersDir);
    var socialUsersDirInfo = new DirectoryInfo(socialUsersDir);
    var jobStatusList = new ConcurrentBag<Job>();

    Console.Write("Uploading user password files .. \n");
    if (pwdUsersDirInfo.GetFiles().Length > 0)
    {
        var files = pwdUsersDirInfo.GetFiles().Concat(socialUsersDirInfo.GetFiles()).ToArray();
        var fileImportIndex = 1;

        jobStatusList = await userImporter.ImportUsersAsync(connectionId, files, maxNumOfConcurrentReqs);
    }

    Console.Write(" done.");

    Console.Write("\n---------------------------------------------------\n");
    Console.Write("\nUploading social user files ..\n");

    var failJobs = jobStatusList.Where(o => o.Summary?.Failed > 0).ToList();
    var failCount = jobStatusList.Sum(o => o.Summary.Failed);
    var failSummaries = new List<Auth0JobErrorDetailsResponse?>();

    Console.WriteLine($"\nCheck and writing all fail jobs .. ({failCount} fails)");

    if (failJobs.Count > 0)
    {
        failSummaries = await userImporter.GetAllFailDetails(failJobs);
        var importFailedSummaries = failSummaries
            .Where(o => o?.Errors?[0]?.Code != "CONFLICT")
            .ToList();

        var log = JsonConvert.SerializeObject(failSummaries, Formatting.Indented);
        var importLog = JsonConvert.SerializeObject(importFailedSummaries, Formatting.Indented);
        await File.WriteAllTextAsync($"{tmpPath}/allFailedJobs.json", log);
        await File.WriteAllTextAsync($"{tmpPath}/importFailedJobs.json", importLog);

        Console.WriteLine($"Check and writing import fail jobs .. ({importFailedSummaries.Count} fails)");
        Console.WriteLine($"Fail details are written into '{tmpPath}/importFailedJobs.json'.");

        // Second try for update users.
        Console.WriteLine($"\nRetrying the failed users to update jobs ..");

        var updateUsers = failSummaries
            .Where(o => o?.Errors?[0]?.Code == "CONFLICT")
            .Select(o => o?.User).ToList();

        if (updateUsers.Count > 0)
        {
            updateUsers.ForEach(o => o!.CustomPasswordHash = null);
            updateUsers.ForEach(o => o!.PasswordHash = null);

            var updateFilesCount =
                await Auth0UserImporter.WriteUsersToFiles(
                    updateUsers,
                    updateUsersDir,
                    runBatchSize,
                    fileSizeLimit);

            var updateUsersDirInfo = new DirectoryInfo(updateUsersDir);
            var updateJobs =
                await userImporter.ImportUsersAsync(
                    connectionId,
                    updateUsersDirInfo.GetFiles(),
                    maxNumOfConcurrentReqs);

            Console.Write(" done.");
            var upsertFailedSummaries = updateJobs.Where(o => o.Summary?.Failed > 0).ToList();

            failSummaries = await userImporter.GetAllFailDetails(upsertFailedSummaries);

            Console.WriteLine($"\nCheck and writing update fail jobs .. ({upsertFailedSummaries.Count} fails)");

            var updateLog = JsonConvert.SerializeObject(failSummaries, Formatting.Indented);
            await File.WriteAllTextAsync($"{tmpPath}/updateFailedJobs.json", updateLog);

            Console.WriteLine($"Fail details are written into '{tmpPath}/updateFailedJobs.json'.");
        }
        else
        {
            Console.WriteLine($"\nNo update users found.\n");
        }
    }

    Console.WriteLine("\n\nImport users completed.");
    return 0;
}
catch (Exception err)
{
    Console.WriteLine($"\nProgram return exception '{err.Message}'. exit with exception.");
    return -1;
}