using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.WorkflowWebhookTriggers;

public interface IWorkflowWebhookTriggerService
{
    Task<WorkflowWebhookTrigger> CreateWorkflowWebhookTriggerAsync(
        WorkflowWebhookTrigger workflowWebhookTrigger,
        string sleekflowCompanyId);

    Task<WorkflowWebhookTrigger?> GetWorkflowWebhookTriggerOrDefaultAsync(
        string workflowWebhookTriggerId,
        string workflowValidationToken);

    Task<WorkflowWebhookTrigger?> GetWorkflowWebhookTriggerOrDefaultAsync(
        string workflowId,
        string sleekflowCompanyId,
        string objectType);

    Task<WorkflowWebhookTrigger> GetWorkflowWebhookTriggerAsync(
        string workflowWebhookTriggerId,
        string workflowValidationToken);

    Task<List<WorkflowWebhookTrigger>> GetWorkflowWebhookTriggersAsync(
        string workflowId,
        string sleekflowCompanyId);

    Task<WorkflowWebhookTrigger> UpdateWorkflowWebhookTriggerAsync(
        string workflowWebhookTriggerId,
        string sleekflowCompanyId,
        string objectIdExpression,
        string objectType,
        AuditEntity.SleekflowStaff updatedBy);

    Task DeleteWorkflowWebhookTriggerAsync(
        string workflowWebhookTriggerId,
        string sleekflowCompanyId);
}

public class WorkflowWebhookTriggerService : IWorkflowWebhookTriggerService, IScopedService
{
    private readonly IWorkflowWebhookTriggerRepository _workflowWebhookTriggerRepository;

    public WorkflowWebhookTriggerService(
        IWorkflowWebhookTriggerRepository workflowWebhookTriggerRepository)
    {
        _workflowWebhookTriggerRepository = workflowWebhookTriggerRepository;
    }

    public async Task<WorkflowWebhookTrigger> CreateWorkflowWebhookTriggerAsync(
        WorkflowWebhookTrigger workflowWebhookTrigger,
        string sleekflowCompanyId)
    {
        var createdWorkflowWebhookTrigger =
            await _workflowWebhookTriggerRepository.CreateAndGetAsync(workflowWebhookTrigger, sleekflowCompanyId);

        return createdWorkflowWebhookTrigger;
    }

    public async Task<WorkflowWebhookTrigger?> GetWorkflowWebhookTriggerOrDefaultAsync(
        string workflowWebhookTriggerId,
        string workflowValidationToken)
    {
        var workflowWebhookTriggers =
            await _workflowWebhookTriggerRepository.GetObjectsAsync(
                t =>
                    t.Id == workflowWebhookTriggerId
                    && t.ValidationToken == workflowValidationToken);

        return workflowWebhookTriggers.SingleOrDefault();
    }

    public async Task<WorkflowWebhookTrigger?> GetWorkflowWebhookTriggerOrDefaultAsync(
        string workflowId,
        string sleekflowCompanyId,
        string objectType)
    {
        var workflowWebhookTriggers =
            await _workflowWebhookTriggerRepository.GetObjectsAsync(
                t =>
                    t.WorkflowId == workflowId
                    && t.SleekflowCompanyId == sleekflowCompanyId
                    && t.ObjectType == objectType);

        return workflowWebhookTriggers.MaxBy(t => t.UpdatedAt);
    }

    public async Task<WorkflowWebhookTrigger> GetWorkflowWebhookTriggerAsync(
        string workflowWebhookTriggerId,
        string workflowValidationToken)
    {
        var workflowWebhookTriggers =
            await _workflowWebhookTriggerRepository.GetObjectsAsync(
                t =>
                    t.Id == workflowWebhookTriggerId
                    && t.ValidationToken == workflowValidationToken);

        var workflowWebhookTrigger = workflowWebhookTriggers.SingleOrDefault();
        if (workflowWebhookTrigger == null)
        {
            throw new SfWebhookEventMatchingException();
        }

        return workflowWebhookTrigger;
    }

    public async Task<List<WorkflowWebhookTrigger>> GetWorkflowWebhookTriggersAsync(
        string workflowId,
        string sleekflowCompanyId)
    {
        var workflowWebhookTriggers =
            await _workflowWebhookTriggerRepository.GetObjectsAsync(
                t =>
                    t.WorkflowId == workflowId
                    && t.SleekflowCompanyId == sleekflowCompanyId);

        return workflowWebhookTriggers;
    }

    public async Task<WorkflowWebhookTrigger> UpdateWorkflowWebhookTriggerAsync(
        string workflowWebhookTriggerId,
        string sleekflowCompanyId,
        string objectIdExpression,
        string objectType,
        AuditEntity.SleekflowStaff updatedBy)
    {
        var workflowWebhookTriggers =
            await _workflowWebhookTriggerRepository.GetObjectsAsync(
                t =>
                    t.Id == workflowWebhookTriggerId
                    && t.SleekflowCompanyId == sleekflowCompanyId);

        var workflowWebhookTrigger = workflowWebhookTriggers.SingleOrDefault();

        if (workflowWebhookTrigger is null)
        {
            throw new SfWorkflowWebhookTriggerNotFoundException();
        }

        if (!string.Equals(objectIdExpression, workflowWebhookTrigger.ObjectIdExpression)
            || !string.Equals(objectType, workflowWebhookTrigger.ObjectType))
        {
            workflowWebhookTrigger = await _workflowWebhookTriggerRepository.PatchAndGetAsync(
                workflowWebhookTriggerId,
                sleekflowCompanyId,
                new List<PatchOperation>()
                {
                    PatchOperation.Set($"/{WorkflowWebhookTrigger.PropertyNameObjectIdExpression}", objectIdExpression),
                    PatchOperation.Set($"/object_type", objectType),
                    PatchOperation.Set($"/{AuditEntity.PropertyNameUpdatedBy}", updatedBy),
                    PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTime.UtcNow)
                });
        }

        return workflowWebhookTrigger;
    }

    public async Task DeleteWorkflowWebhookTriggerAsync(
        string workflowWebhookTriggerId,
        string sleekflowCompanyId)
    {
        var workflowWebhookTriggers =
            await _workflowWebhookTriggerRepository.GetObjectsAsync(
                t =>
                    t.Id == workflowWebhookTriggerId
                    && t.SleekflowCompanyId == sleekflowCompanyId);

        await Parallel.ForEachAsync(
            workflowWebhookTriggers,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (workflowWebhookTrigger, cancellationToken) =>
            {
                await _workflowWebhookTriggerRepository.DeleteAsync(
                    workflowWebhookTrigger.Id,
                    workflowWebhookTrigger.SleekflowCompanyId,
                    cancellationToken: cancellationToken);
            });
    }
}