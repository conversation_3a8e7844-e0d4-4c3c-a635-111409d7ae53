﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class ZohoObjectUpdatedEventRequestConsumerDefinition : ConsumerDefinition<ZohoObjectUpdatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<ZohoObjectUpdatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class ZohoObjectUpdatedEventRequestConsumer : IConsumer<ZohoObjectUpdatedEventRequest>
{
    private readonly IBus _bus;

    public ZohoObjectUpdatedEventRequestConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<ZohoObjectUpdatedEventRequest> context)
    {
        var zohoObjectUpdatedEventRequest = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnZohoObjectUpdatedEventBody(
                zohoObjectUpdatedEventRequest.CreatedAt,
                zohoObjectUpdatedEventRequest.ConnectionId,
                zohoObjectUpdatedEventRequest.ObjectType,
                zohoObjectUpdatedEventRequest.ObjectDict),
            zohoObjectUpdatedEventRequest.ObjectId,
            zohoObjectUpdatedEventRequest.ObjectType,
            zohoObjectUpdatedEventRequest.SleekflowCompanyId));
    }
}