﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IWorkflowPotentialInfiniteLoopConfig
{
    int SpecificStepRequestRateLimitWindowSeconds { get; }

    int SpecificStepRequestAllowedWithinWindow { get; }
}

public class WorkflowPotentialInfiniteLoopConfig : IWorkflowPotentialInfiniteLoopConfig, IConfig
{
    public int SpecificStepRequestRateLimitWindowSeconds { get; }

    public int SpecificStepRequestAllowedWithinWindow { get; }

    public WorkflowPotentialInfiniteLoopConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        SpecificStepRequestRateLimitWindowSeconds =
            int.TryParse(
                Environment.GetEnvironmentVariable("SPECIFIC_STEP_REQUEST_RATE_LIMIT_WINDOW_SECONDS", target),
                out var specificStepRequestRateLimitWindowSeconds)
                ? specificStepRequestRateLimitWindowSeconds
                : throw new SfMissingEnvironmentVariableException("SPECIFIC_STEP_REQUEST_RATE_LIMIT_WINDOW_SECONDS");

        SpecificStepRequestAllowedWithinWindow =
            int.TryParse(
                Environment.GetEnvironmentVariable("SPECIFIC_STEP_REQUEST_MAX_ALLOWED_WITHIN_WINDOW", target),
                out var specificStepRequestAllowedWithinWindow)
                ? specificStepRequestAllowedWithinWindow
                : throw new SfMissingEnvironmentVariableException("SPECIFIC_STEP_REQUEST_MAX_ALLOWED_WITHIN_WINDOW");
    }
}