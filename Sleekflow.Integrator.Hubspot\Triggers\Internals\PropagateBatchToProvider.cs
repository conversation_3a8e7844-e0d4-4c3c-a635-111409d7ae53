using MassTransit;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.HubspotQueues;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Locks;

namespace Sleekflow.Integrator.Hubspot.Triggers.Internals;

[TriggerGroup(TriggerGroups.Internals)]
public class PropagateBatchToProvider : ITrigger
{
    private const int PerTenSecondsRateLimit = 50;

    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotQueueService _hubspotQueueService;
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly ILockService _lockService;
    private readonly IBus _bus;
    private readonly ILogger<PropagateBatchToProvider> _logger;

    public PropagateBatchToProvider(
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotQueueService hubspotQueueService,
        IHubspotObjectService hubspotObjectService,
        ILockService lockService,
        IBus bus,
        ILogger<PropagateBatchToProvider> logger)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotQueueService = hubspotQueueService;
        _hubspotObjectService = hubspotObjectService;
        _lockService = lockService;
        _bus = bus;
        _logger = logger;
    }

    public class PropagateBatchToProviderInput
    {
    }

    public class PropagateBatchToProviderOutput
    {
    }

    public async Task<PropagateBatchToProviderOutput> F(
        PropagateBatchToProviderInput propagateBatchToProviderInput)
    {
        var tuples = await _hubspotQueueService.GetDistinctCompanyIdAndEntityTypeNameTuples();

        await Parallel.ForEachAsync(
            tuples.Take(PerTenSecondsRateLimit),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 5
            },
            async (tuple, token) =>
            {
                var (sleekflowCompanyId, entityTypeName) = tuple;

                var @lock = await _lockService.LockAsync(
                    new[]
                    {
                        nameof(PropagateBatchToProvider),
                        sleekflowCompanyId,
                        entityTypeName
                    },
                    TimeSpan.FromSeconds(120),
                    token);

                if (@lock == null)
                {
                    return;
                }

                try
                {
                    for (var i = 0; i < (entityTypeName == "Contact" ? 100 : 1); i++)
                    {
                        await PropagateBatchAsync(
                            sleekflowCompanyId,
                            entityTypeName,
                            ObjectOperations.CreateObjectOperation);

                        await PropagateBatchAsync(
                            sleekflowCompanyId,
                            entityTypeName,
                            ObjectOperations.UpdateObjectOperation);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Error while propagating batch to the provider");
                }
                finally
                {
                    await _lockService.ReleaseAsync(@lock, token);
                }
            });

        return new PropagateBatchToProviderOutput();
    }

    private async Task PropagateBatchAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string objectOperation)
    {
        var batchSize = entityTypeName == "Contact" ? 1 : 100;

        var hubspotQueueItems = await _hubspotQueueService.PeekItemsAsync(
            batchSize,
            sleekflowCompanyId,
            objectOperation,
            entityTypeName);

        if (!hubspotQueueItems.Any())
        {
            return;
        }

        var authentication = await _hubspotAuthenticationService.GetAsync(sleekflowCompanyId);

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        if (objectOperation == ObjectOperations.CreateObjectOperation)
        {
            var items = hubspotQueueItems
                .Take(batchSize)
                .ToList();

            var objects = await _hubspotObjectService.BatchCreateAsync(
                authentication,
                items
                    .DistinctBy(i => i.CrmHubObjectId)
                    .Select(i => i.Dict)
                    .ToList(),
                entityTypeName);

            await _hubspotQueueService.DeleteItemsAsync(
                items.Select(i => i.Id).ToList(),
                sleekflowCompanyId);

            foreach (var @object in objects)
            {
                await _bus.Publish(
                    new OnObjectOperationEvent(
                        @object,
                        OnObjectOperationEvent.OperationCreateOrUpdateObject,
                        "hubspot-integrator",
                        sleekflowCompanyId,
                        _hubspotObjectService.ResolveObjectId(@object),
                        entityTypeName,
                        null),
                    context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
            }

            await OnCompleteAsync(items, sleekflowCompanyId);
        }
        else if (objectOperation == ObjectOperations.UpdateObjectOperation)
        {
            var items = hubspotQueueItems

                // Make sure that we don't update the same object twice
                .DistinctBy(i => i.CrmHubObjectId)
                .Take(batchSize)
                .ToList();

            await _hubspotObjectService.BatchUpdateAsync(
                authentication,
                items
                    .Select(i => new HubspotObjectServiceUpdateItem(i.HubspotObjectId!, i.Dict))
                    .ToList(),
                entityTypeName);

            await _hubspotQueueService.DeleteItemsAsync(
                items.Select(i => i.Id).ToList(),
                sleekflowCompanyId);

            await OnCompleteAsync(items, sleekflowCompanyId);
        }
    }

    private async Task OnCompleteAsync(
        List<HubspotQueueItem> hubspotQueueItems,
        string sleekflowCompanyId)
    {
        var events = hubspotQueueItems
            .Select(
                i => new OnProviderObjectOperationCompleteEvent(
                    i.SleekflowCompanyId,
                    i.ObjectOperation,
                    i.CrmHubObjectId,
                    sleekflowCompanyId,
                    i.Dict,
                    "HubspotIntegrator"))
            .ToList();

        await _bus.PublishBatch(events);
    }
}