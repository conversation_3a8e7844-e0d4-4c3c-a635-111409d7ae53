using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetConversationLastMessagesInput
{
    [Required]
    [Validations.ValidateObject]
    [JsonProperty("state_identity")]
    public StateIdentity StateIdentity { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [Validations.ValidateArray]
    [JsonProperty("target_channels")]
    public List<string> TargetChannels { get; set; }

    [JsonProperty("target_channel_id")]
    public string? TargetChannelId { get; set; }

    [Required]
    [Range(0, 999)]
    [JsonProperty("offset")]
    public int Offset { get; set; }

    [Required]
    [Range(0, 999)]
    [JsonProperty("limit")]
    public int Limit { get; set; }

    [Validations.ValidateObject]
    [JsonProperty("retrieval_window_timestamp")]
    public DateTimeOffset? RetrievalWindowTimestamp { get; set; }

    [JsonConstructor]
    public GetConversationLastMessagesInput(
        StateIdentity stateIdentity,
        string contactId,
        List<string> targetChannels,
        string? targetChannelId,
        int offset,
        int limit,
        DateTimeOffset? retrievalWindowTimestamp = null)
    {
        StateIdentity = stateIdentity;
        ContactId = contactId;
        TargetChannels = targetChannels;
        TargetChannelId = targetChannelId;
        Offset = offset;
        Limit = limit;
        RetrievalWindowTimestamp = retrievalWindowTimestamp;
    }
}