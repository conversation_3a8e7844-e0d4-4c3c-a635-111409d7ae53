using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;

public interface IMethod<in TConfig, TOutput>
{
    string MethodName { get; }

    Task<TOutput> CompleteAsync(
        TConfig chatEvalConfig,
        ChatMessageContent[]? questionContexts,
        List<string>? sourceFilenames,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig,
        SfChatEntry[]? sfChatEntriesQuestionContexts);
}