using MassTransit;
using Microsoft.Extensions.Logging;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Workers.Consumers;

public class StartFileIngestionEventConsumer : IConsumer<StartFileIngestionEvent>
{
    private readonly ILogger<StartFileIngestionEventConsumer> _logger;

    public StartFileIngestionEventConsumer(
        ILogger<StartFileIngestionEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<StartFileIngestionEvent> context)
    {
        _logger.LogInformation("Event received: StartFileIngestionEvent");
    }
}