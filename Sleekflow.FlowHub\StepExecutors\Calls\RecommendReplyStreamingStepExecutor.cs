using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Chats;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IRecommendReplyStreamingStepExecutor : IStepExecutor
{
}

public class RecommendReplyStreamingStepExecutor
    : GeneralStepExecutor<CallStep<RecommendReplyStreamingStepArgs>>,
        IRecommendReplyStreamingStepExecutor,
        IScopedService
{
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IBus _bus;

    public RecommendReplyStreamingStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator,
        IBus bus)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
        _bus = bus;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);
        try
        {
            // Obtain the conversation information from SleekflowCore
            var response = await _coreCommander.ExecuteAsync(
                state.Origin,
                "GetConversionLastMessages",
                await GetArgs(callStep, state));

            var conversationMessages = JsonConvert.DeserializeObject<GetConversationLastMessagesOutput>(
                response,
                JsonConfig.DefaultJsonSerializerSettings);

            // Obtain the IntelligentHub usage filter from the conversation messages
            var intelligentHubUsageFilter = await GetIntelligentHubUsageFilterAsync(state);

            // Internal request to IntelligentHub to obtain the RecommendedReply
            await _bus.Publish(
                new GenerateRecommendedReplyEvent(
                    step.Id,
                    state.Id,
                    state.Identity.SleekflowCompanyId,
                    conversationMessages?.ConversationMessages
                        .Select(
                            c =>
                            {
                                var chatEntry = new SfChatEntry();
                                if (c.IsSentFromSleekflow)
                                {
                                    chatEntry.Bot = c.MessageContent;
                                }
                                else
                                {
                                    chatEntry.User = c.MessageContent;
                                }

                                return chatEntry;
                            })
                        .ToList() ?? new List<SfChatEntry>(),
                    intelligentHubUsageFilter?.FromDateTime,
                    intelligentHubUsageFilter?.ToDateTime));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<GetConversationLastMessagesInput> GetArgs(
        CallStep<RecommendReplyStreamingStepArgs> callStep,
        ProxyState state)
    {
        var contactId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                                  ?? callStep.Args.ContactIdExpr);

        return new GetConversationLastMessagesInput(
            state.Identity,
            contactId,
            [ChannelTypes.WhatsAppCloudApi],
            null,
            0,
            10);
    }

    private async Task<GetIntelligentHubUsageFilterOutput.IntelligentHubUsageFilterOutput?>
        GetIntelligentHubUsageFilterAsync(ProxyState state)
    {
        var response = await _coreCommander.ExecuteAsync<GetIntelligentHubUsageFilterOutput>(
            state.Origin,
            "GetIntelligentHubUsageFilter",
            new GetIntelligentHubUsageFilterInput(state.Identity.SleekflowCompanyId));

        return response?.IntelligentHubUsageFilter;
    }
}