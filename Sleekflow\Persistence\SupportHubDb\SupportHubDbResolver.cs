﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.SupportHubDb;

public interface ISupportHubDbResolver : IContainerResolver
{
}

public class SupportHubDbResolver : ISupportHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public SupportHubDbResolver(ISupportHubDbConfig supportHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            supportHubDbConfig.Endpoint,
            supportHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}