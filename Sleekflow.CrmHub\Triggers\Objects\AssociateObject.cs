﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class AssociateObject : ITrigger
{
    private readonly IBus _bus;

    public AssociateObject(
        IBus bus)
    {
        _bus = bus;
    }

    public class AssociateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        [StringLength(128)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        [StringLength(64)]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        [StringLength(128)]
        public string ObjectId { get; set; }

        [JsonProperty("provider_object_id")]
        [Required]
        [StringLength(128)]
        public string ProviderObjectId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        [StringLength(128)]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public AssociateObjectInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string objectId,
            string providerObjectId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
            ProviderObjectId = providerObjectId;
            ProviderName = providerName;
        }
    }

    public class AssociateObjectOutput
    {
    }

    public async Task<AssociateObjectOutput> F(
        AssociateObjectInput associateObjectInput)
    {
        if (associateObjectInput.ProviderName == "sleekflow" && associateObjectInput.EntityTypeName == "User")
        {
            var onObjectOperationEvent = new OnObjectOperationEvent(
                new Dictionary<string, object?>
                {
                    {
                        "Id", associateObjectInput.ProviderObjectId
                    }
                },
                OnObjectOperationEvent.OperationAssociateObject,
                associateObjectInput.ProviderName,
                associateObjectInput.SleekflowCompanyId,
                associateObjectInput.ProviderObjectId,
                associateObjectInput.EntityTypeName,
                associateObjectInput.ObjectId);

            await _bus.Publish(
                onObjectOperationEvent,
                context => { context.ConversationId = Guid.Parse(associateObjectInput.SleekflowCompanyId); });

            return new AssociateObjectOutput();
        }

        throw new NotImplementedException();
    }
}