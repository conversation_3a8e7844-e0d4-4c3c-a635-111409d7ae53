using Microsoft.SemanticKernel.Agents;
using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public interface IAgentRegistry
{
    void RegisterAgent(Agent agent, params string[] capabilities);
    Agent GetAgentForCapability(string capability);
    bool HasCapability(string capability);
}

public class AgentRegistry : IAgentRegistry, IScopedService
{
    private readonly Dictionary<string, Agent> _capabilityToAgentMap = new ();

    public void RegisterAgent(Agent agent, params string[] capabilities)
    {
        foreach (var capability in capabilities)
        {
            _capabilityToAgentMap[capability] = agent;
        }
    }

    public Agent GetAgentForCapability(string capability)
    {
        if (_capabilityToAgentMap.TryGetValue(capability, out var agent))
        {
            return agent;
        }

        throw new KeyNotFoundException($"No agent registered for capability: {capability}");
    }

    public bool HasCapability(string capability)
    {
        return _capabilityToAgentMap.ContainsKey(capability);
    }
}