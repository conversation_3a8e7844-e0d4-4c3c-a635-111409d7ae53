using System.Collections.Concurrent;
using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Ids;
using Sleekflow.JsonConfigs;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;

public interface IProductCatalogService
{
    Task<List<FacebookConnectedWabaProductCatalogMappingDto>>
        GetWabasConnectedFacebookProductCatalogsWithFacebookAuthorizationCodeAsync(
            string facebookAuthorizationCode,
            string sleekflowCompanyId,
            AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<List<FacebookConnectedWabaProductCatalogMappingDto>>
        GetWabasConnectedFacebookProductCatalogsWithUserAccessTokenAsync(
            string userAccessToken,
            string sleekflowCompanyId,
            AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<List<FacebookConnectedWabaProductCatalogMappingDto>> GetWabasConnectedFacebookProductCatalogsAsync(
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<WabaPhoneNumber> SetWabaPhoneNumberProductCatalogVisibleEnableCartAsync(
        string sleekflowCompanyId,
        string wabaId,
        string phoneNumberId,
        bool? isCatalogVisible,
        bool? isCartEnabled,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<WabaPhoneNumber> RetrieveWabaPhoneNumberWhatsappCommerceSettingAsync(
        string sleekflowCompanyId,
        string wabaId,
        string phoneNumberId,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        bool? shouldRefresh = false);

    Task<Waba> RefreshWabaProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<WabaProductCatalog?> GetWabaProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId);

    Task<ProductItemDto> GetProductItemByRetailerIdAsync(
        string sleekflowCompanyId,
        string wabaId,
        string facebookProductCatalogId,
        string facebookRetailerId);

    Task<List<ProductItemDto>> GetProductItemsAsync(
        string sleekflowCompanyId,
        string wabaId,
        string facebookProductCatalogId);

    Task<List<WabaProductCatalogDto>> GetFacebookBusinessProductCatalogsAsync(
        string sleekflowCompanyId,
        string facebookBusinessId);

    Task<List<Waba>> ConnectWabasProductCatalogsAsync(
        string sleekflowCompanyId,
        Dictionary<string, string> wabaIdCatalogIdDictionary,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<Waba> UpdateWabaConnectedFacebookProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId,
        string? facebookProductCatalogId,
        AuditEntity.SleekflowStaff? sleekflowStaff);
}

public class ProductCatalogService : IProductCatalogService, ISingletonService
{
    private readonly IWabaService _wabaService;
    private readonly IWabaRepository _wabaRepository;
    private readonly ILogger<ProductCatalogService> _logger;
    private readonly IFacebookCommerceClient _facebookCommerceClient;
    private readonly ICommonRetryPolicyService _commonRetryPolicyService;
    private readonly IAuditLogService _auditLogService;
    private readonly ISecretConfig _secretConfig;
    private readonly IIdService _idService;
    private readonly IWhatsappCloudApiBspClient _whatsappCloudApiBspClient;
    private readonly IWhatsappCloudApiAuthenticationClient _whatsappCloudApiAuthenticationClient;
    private readonly HttpClient _httpClient;

    public ProductCatalogService(
        IWabaService wabaService,
        IWabaRepository wabaRepository,
        ILogger<ProductCatalogService> logger,
        ICloudApiClients cloudApiClients,
        ICommonRetryPolicyService commonRetryPolicyService,
        IAuditLogService auditLogService,
        ISecretConfig secretConfig,
        IIdService idService,
        IHttpClientFactory httpClientFactory)
    {
        _wabaService = wabaService;
        _wabaRepository = wabaRepository;
        _logger = logger;
        _facebookCommerceClient = cloudApiClients.FacebookCommerceClient;
        _commonRetryPolicyService = commonRetryPolicyService;
        _auditLogService = auditLogService;
        _secretConfig = secretConfig;
        _idService = idService;
        _whatsappCloudApiBspClient = cloudApiClients.WhatsappCloudApiBspClient;
        _whatsappCloudApiAuthenticationClient = cloudApiClients.WhatsappCloudApiAuthenticationClient;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<WabaPhoneNumber> SetWabaPhoneNumberProductCatalogVisibleEnableCartAsync(
        string sleekflowCompanyId,
        string wabaId,
        string phoneNumberId,
        bool? isCatalogVisible,
        bool? isCartEnabled,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var waba = await _wabaService.GetActiveWabaWithIdAndWabaPhoneNumberIdAsync(
            wabaId,
            phoneNumberId,
            sleekflowCompanyId);

        if (waba is null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var wabaPhoneNumber = waba.WabaPhoneNumbers.First(x => x.Id == phoneNumberId);

        var facebookPhoneNumberId = wabaPhoneNumber.FacebookPhoneNumberId;

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var businessIntegrationSystemUserAccessToken =
            hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
                ? decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken
                : null;

        var whatsappCommerceSetting = await UpdateFacebookPhoneNumberCommerceSettingAsync(
            waba,
            facebookPhoneNumberId,
            isCatalogVisible,
            isCartEnabled,
            sleekflowCompanyId,
            businessIntegrationSystemUserAccessToken);

        wabaPhoneNumber.WhatsappCommerceSetting = whatsappCommerceSetting;

        if (sleekflowStaff is not null)
        {
            wabaPhoneNumber.UpdatedBy = sleekflowStaff;
        }

        wabaPhoneNumber.UpdatedAt = DateTimeOffset.UtcNow;

        if (!await PatchWabaAsync(sleekflowCompanyId, waba, waba.WabaPhoneNumbers, wabaPhoneNumber))
        {
            throw new SfInternalErrorException("Unable to patch waba phone number with whatsapp commerce setting");
        }

        return wabaPhoneNumber;
    }

    public async Task<WabaPhoneNumber> RetrieveWabaPhoneNumberWhatsappCommerceSettingAsync(
        string sleekflowCompanyId,
        string wabaId,
        string phoneNumberId,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        bool? shouldRefresh = false)
    {
        var waba = await _wabaService.GetActiveWabaWithIdAndWabaPhoneNumberIdAsync(
            wabaId,
            phoneNumberId,
            sleekflowCompanyId);

        if (waba is null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            _logger.LogWarning(
                "Unable to locate any valid waba {Waba}",
                JsonConvert.SerializeObject(waba));
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var wabaPhoneNumber = waba.WabaPhoneNumbers.First(x => x.Id == phoneNumberId);

        if (shouldRefresh is true)
        {
            try
            {
                var whatsappCommerceSettingOutput =
                    await _facebookCommerceClient.GetWhatsappCommerceSettings(wabaPhoneNumber.FacebookPhoneNumberId);

                if (whatsappCommerceSettingOutput.Data == null || !whatsappCommerceSettingOutput.Data.Any())
                {
                    _logger.LogWarning(
                        "Unable to get {WabaId} {PhoneNumberId} whatsapp commerce setting",
                        wabaId,
                        phoneNumberId);
                    throw new SfInternalErrorException(
                        "Unable to retrieve waba phone number with whatsapp commerce setting");
                }

                wabaPhoneNumber.WhatsappCommerceSetting = new WhatsappCommerceSetting(
                    whatsappCommerceSettingOutput.Data.FirstOrDefault()!.Id,
                    whatsappCommerceSettingOutput.Data.FirstOrDefault()!.IsCatalogVisible,
                    whatsappCommerceSettingOutput.Data.FirstOrDefault()!.IsCartEnabled);

                if (sleekflowStaff is not null)
                {
                    wabaPhoneNumber.UpdatedBy = sleekflowStaff;
                }

                wabaPhoneNumber.UpdatedAt = DateTimeOffset.UtcNow;

                if (!await PatchWabaAsync(
                        sleekflowCompanyId,
                        waba,
                        waba.WabaPhoneNumbers,
                        wabaPhoneNumber))
                {
                    throw new SfInternalErrorException(
                        "Unable to patch waba phone number with whatsapp commerce setting");
                }

                return wabaPhoneNumber;
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    "Unable to retrieve waba phone number {PhoneNumberId} with whatsapp commerce setting with error {Exception}",
                    phoneNumberId,
                    JsonConvert.SerializeObject(exception));
                throw new SfInternalErrorException(
                    "Unable to retrieve waba phone number with whatsapp commerce setting");
            }
        }

        return wabaPhoneNumber;
    }

    public async Task<Waba> RefreshWabaProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(wabaId, sleekflowCompanyId);

        if (waba is null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        waba = await GetWabaConnectedProductCatalogAsync(sleekflowCompanyId, waba, sleekflowStaff);

        if (waba.WabaProductCatalog == null)
        {
            _logger.LogWarning(
                "Unable to refresh any waba product catalog for waba {Waba}",
                JsonConvert.SerializeObject(waba));

            return waba;
        }

        if (!await PatchWabaAsync(sleekflowCompanyId, waba, waba.WabaProductCatalog))
        {
            throw new SfInternalErrorException("Unable to patch waba product catalog");
        }

        return waba;
    }

    public async Task<WabaProductCatalog?> GetWabaProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId)
    {
        var (_, wabaProductCatalog) = await GetWabaAndProductCatalogAsync(sleekflowCompanyId, wabaId);

        return wabaProductCatalog;
    }

    private async Task<(Waba Waba, WabaProductCatalog? WabaProductCatalog)> GetWabaAndProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(wabaId, sleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        return (waba, waba.WabaProductCatalog);
    }

    private async Task<Waba> GetWabaConnectedProductCatalogAsync(
        string sleekflowCompanyId,
        Waba waba,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var businessIntegrationSystemUserAccessToken =
            hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
                ? decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken
                : null;

        var facebookWabaConnectedProductCatalog =
            await GetFacebookWabaConnectedProductCatalogAsync(waba.FacebookWabaId, businessIntegrationSystemUserAccessToken);

        var existingWabaProductCatalog = waba.WabaProductCatalog;

        if (facebookWabaConnectedProductCatalog == null)
        {
            _logger.LogWarning(
                "Unable to get any product catalog connected to waba {Waba}",
                JsonConvert.SerializeObject(waba));

            if (existingWabaProductCatalog != null)
            {
                existingWabaProductCatalog.Status = "inactive";
                existingWabaProductCatalog.UpdatedAt = DateTimeOffset.UtcNow;
                if (sleekflowStaff != null)
                {
                    existingWabaProductCatalog.UpdatedBy = sleekflowStaff;
                }

                waba.WabaProductCatalog = existingWabaProductCatalog;
            }
        }
        else
        {
            if (existingWabaProductCatalog != null && existingWabaProductCatalog.FacebookProductCatalogId ==
                facebookWabaConnectedProductCatalog.Id)
            {
                existingWabaProductCatalog.FacebookProductCatalogName = facebookWabaConnectedProductCatalog.Name;
                existingWabaProductCatalog.SleekflowCompanyId = sleekflowCompanyId;
                existingWabaProductCatalog.DefaultImageUrl = facebookWabaConnectedProductCatalog.DefaultImageUrl;
                existingWabaProductCatalog.Vertical = facebookWabaConnectedProductCatalog.Vertical;
                existingWabaProductCatalog.ProductCount = facebookWabaConnectedProductCatalog.ProductCount;
                existingWabaProductCatalog.Status = "active";
                existingWabaProductCatalog.UpdatedAt = DateTimeOffset.UtcNow;
                if (sleekflowStaff != null)
                {
                    existingWabaProductCatalog.UpdatedBy = sleekflowStaff;
                }

                waba.WabaProductCatalog = existingWabaProductCatalog;
            }
            else
            {
                // upsert
                waba.WabaProductCatalog = new WabaProductCatalog(
                    _idService.GetId(SysTypeNames.WabaProductCatalog),
                    facebookWabaConnectedProductCatalog.Id,
                    facebookWabaConnectedProductCatalog.Name,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    sleekflowCompanyId,
                    facebookWabaConnectedProductCatalog.DefaultImageUrl,
                    facebookWabaConnectedProductCatalog.ProductCount,
                    facebookWabaConnectedProductCatalog.Vertical,
                    "active",
                    sleekflowStaff,
                    sleekflowStaff);
            }
        }

        return waba;
    }

    public async Task<ProductItemDto> GetProductItemByRetailerIdAsync(
        string sleekflowCompanyId,
        string wabaId,
        string facebookProductCatalogId,
        string facebookRetailerId)
    {
        var (waba, wabaProductCatalog) = await GetWabaAndProductCatalogAsync(sleekflowCompanyId, wabaId);

        if (wabaProductCatalog == null || wabaProductCatalog.Status == "inactive")
        {
            _logger.LogError(
                "Unable to fetch waba {WabaId} connected active product catalog {FacebookProductCatalogId}",
                wabaId,
                facebookProductCatalogId);

            throw new SfInternalErrorException(
                $"unable to  fetch waba {wabaId} connected active product catalog {facebookProductCatalogId}");
        }

        var filterJsonString = $@"{{""retailer_id"":{{""eq"": ""{facebookRetailerId}""}}}}";

        try
        {
            var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
                _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

            var facebookCommerceClient =
                hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
                    ? new FacebookCommerceClient(decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken, _httpClient)
                    : _facebookCommerceClient;

            var getProductCatalogProductItemsResponse = await facebookCommerceClient.GetProductCatalogProductItems(
                facebookProductCatalogId,
                null,
                null,
                filterJsonString);

            if (getProductCatalogProductItemsResponse.Data.Any())
            {
                return new ProductItemDto(getProductCatalogProductItemsResponse.Data.First());
            }

            _logger.LogError("Unable to fetch the product information of {RetailerId}", facebookRetailerId);

            throw new SfInternalErrorException($"unable to get product item {facebookRetailerId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for fetching the product information of {RetailerId} from {FacebookProductCatalogId}",
                facebookRetailerId,
                facebookProductCatalogId);

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "unable to get product item",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }

            throw new SfInternalErrorException(
                ex,
                $"Caught error for fetching the product information of retailer id {facebookRetailerId} from product catalog id {facebookProductCatalogId}");
        }
    }

    public async Task<List<ProductItemDto>> GetProductItemsAsync(
        string sleekflowCompanyId,
        string wabaId,
        string facebookProductCatalogId)
    {
        var (waba, wabaActiveProductCatalog) = await GetWabaAndProductCatalogAsync(sleekflowCompanyId, wabaId);

        if (wabaActiveProductCatalog == null || wabaActiveProductCatalog.Status == "inactive")
        {
            _logger.LogError(
                "Unable to fetch waba {WabaId} connected active product catalog {FacebookProductCatalogId}",
                wabaId,
                facebookProductCatalogId);

            throw new SfInternalErrorException(
                $"unable to  fetch waba {wabaId} connected active product catalog {facebookProductCatalogId}");
        }

        List<GetProductItemResponse> productItems;
        try
        {
            var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
                _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

            var facebookCommerceClient =
                hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
                    ? new FacebookCommerceClient(decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken, _httpClient)
                    : _facebookCommerceClient;

            var getProductCatalogProductItemsResponse =
                await facebookCommerceClient.GetProductCatalogProductItems(facebookProductCatalogId);
            productItems = getProductCatalogProductItemsResponse.Data.ToList();

            do
            {
                getProductCatalogProductItemsResponse = await facebookCommerceClient.GetProductCatalogProductItems(
                    facebookProductCatalogId,
                    null,
                    new CursorBasedPaginationParam()
                    {
                        After = getProductCatalogProductItemsResponse.Paging?.Cursors?.After,
                        Limit = 1000
                    });

                productItems.AddRange(getProductCatalogProductItemsResponse.Data);
            }
            while (getProductCatalogProductItemsResponse.Data.Count != 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for getting waba {WabaId} product items",
                wabaId);

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "unable to get product items",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }

            throw new SfInternalErrorException(ex, $"Caught error for getting waba id {wabaId} product items");
        }

        WabaProductCatalog? wabaProductCatalog;

        if (wabaActiveProductCatalog.FacebookProductCatalogId == facebookProductCatalogId)
        {
            wabaProductCatalog = wabaActiveProductCatalog;
        }
        else
        {
            var refreshedWaba = await RefreshWabaProductCatalogAsync(sleekflowCompanyId, wabaId, null);

            wabaProductCatalog = refreshedWaba.WabaProductCatalog;
        }

        var productItemDtos = new List<ProductItemDto>();
        foreach (var item in productItems)
        {
            productItemDtos.Add(
                wabaProductCatalog != null ? new ProductItemDto(item, wabaProductCatalog) : new ProductItemDto(item));
        }

        return productItemDtos;
    }

    public async Task<List<WabaProductCatalogDto>> GetFacebookBusinessProductCatalogsAsync(
        string sleekflowCompanyId,
        string facebookBusinessId)
    {
        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

        if (!wabas.Any())
        {
            _logger.LogWarning(
                "Unable to locate any valid waba");
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var waba = wabas.First(x => x.SleekflowCompanyIds.Contains(sleekflowCompanyId));

        _logger.LogInformation(
            "Got Waba {Waba} with sendWhatsappCloudApiMessageInput",
            JsonConvert.SerializeObject(waba, JsonConfig.DefaultJsonSerializerSettings));
        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) = _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var businessIntegrationSystemUserAccessToken =
            hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
                ? decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken
                : null;

        var facebookProductCatalogs = await GetFacebookBusinessOwnedProductCatalogsAsync(facebookBusinessId, businessIntegrationSystemUserAccessToken);

        var productCatalogDtos = new List<WabaProductCatalogDto>();

        foreach (var facebookProductCatalog in facebookProductCatalogs)
        {
            productCatalogDtos.Add(new WabaProductCatalogDto(facebookProductCatalog));
        }

        return productCatalogDtos;
    }

    public async Task<List<Waba>> ConnectWabasProductCatalogsAsync(
        string sleekflowCompanyId,
        Dictionary<string, string> wabaIdCatalogIdDictionary,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var connectedProductCatalogWabas = new List<Waba>();

        foreach (var (wabaId, facebookProductCatalogId) in wabaIdCatalogIdDictionary)
        {
            try
            {
                var waba = await _wabaService.GetWabaOrDefaultAsync(
                    wabaId,
                    sleekflowCompanyId);

                if (waba is null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
                {
                    throw new SfNotSupportedOperationException("Unable to locate any valid waba");
                }

                var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
                    _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

                var businessIntegrationSystemUserAccessToken =
                    hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null && decryptedBusinessIntegrationSystemUserAccessTokenDto.Scopes.Contains("catalog_management")
                        ? decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken
                        : null;

                if (!await ProductCatalogAssignedUsersAsync(facebookProductCatalogId, sleekflowCompanyId, decryptedBusinessIntegrationSystemUserAccessTokenDto))
                {
                    _logger.LogWarning(
                        "Unable to assign user with {FacebookProductCatalogId}",
                        facebookProductCatalogId);
                    continue;
                }

                var connectedFacebookProductCatalog =
                    await GetFacebookWabaConnectedProductCatalogAsync(waba.FacebookWabaId, businessIntegrationSystemUserAccessToken);

                if (connectedFacebookProductCatalog == null)
                {
                    if (!await ConnectFacebookWabaProductCatalogAsync(
                            waba.FacebookWabaId,
                            facebookProductCatalogId,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken))
                    {
                        _logger.LogWarning(
                            "Unable to connect {FacebookProductCatalog} with {WabaId}",
                            facebookProductCatalogId,
                            wabaId);

                        continue;
                    }
                }
                else if (connectedFacebookProductCatalog.Id != facebookProductCatalogId)
                {
                    if (!await DisconnectFacebookWabaProductCatalogAsync(
                            waba.FacebookWabaId,
                            connectedFacebookProductCatalog.Id,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken))
                    {
                        _logger.LogWarning(
                            "Unable to disconnect {FacebookProductCatalogId} with {WabaId}",
                            connectedFacebookProductCatalog.Id,
                            wabaId);

                        continue;
                    }

                    if (!await ConnectFacebookWabaProductCatalogAsync(
                            waba.FacebookWabaId,
                            facebookProductCatalogId,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken))
                    {
                        _logger.LogWarning(
                            "Unable to connect {FacebookProductCatalog} with {WabaId}",
                            facebookProductCatalogId,
                            wabaId);

                        continue;
                    }
                }

                var updatedWabaPhoneNumbers = waba.WabaPhoneNumbers;
                foreach (var wabaPhoneNumber in waba.WabaPhoneNumbers
                             .Where(x => x.RecordStatus == WabaPhoneNumberStatuses.Active).ToList())
                {
                    var whatsappCommerceSetting = await UpdateFacebookPhoneNumberCommerceSettingAsync(
                        waba,
                        wabaPhoneNumber.FacebookPhoneNumberId,
                        true,
                        true,
                        sleekflowCompanyId,
                        businessIntegrationSystemUserAccessToken);

                    wabaPhoneNumber.WhatsappCommerceSetting = whatsappCommerceSetting;

                    updatedWabaPhoneNumbers = waba.WabaPhoneNumbers
                        .Select(wpn => wpn.Id == wabaPhoneNumber.Id ? wabaPhoneNumber : wpn)
                        .ToHashSet();
                }

                var fetchedCatalogWaba =
                    await GetWabaConnectedProductCatalogAsync(sleekflowCompanyId, waba, sleekflowStaff);

                if (fetchedCatalogWaba.WabaProductCatalog == null)
                {
                    continue;
                }

                if (!await PatchWabaAsync(
                        sleekflowCompanyId,
                        fetchedCatalogWaba,
                        updatedWabaPhoneNumbers,
                        fetchedCatalogWaba.WabaProductCatalog))
                {
                    _logger.LogWarning(
                        "Unable to patch waba {Waba} when finishing fetch waba product catalog {FacebookProductCatalogId}",
                        JsonConvert.SerializeObject(fetchedCatalogWaba),
                        facebookProductCatalogId);

                    continue;
                }

                connectedProductCatalogWabas.Add(fetchedCatalogWaba);
            }
            catch (Exception exception)
            {
                _logger.LogWarning(
                    "Error occur during connect waba {WabaId} product catalog {FacebookProductCatalogId} / {Exception} ",
                    wabaId,
                    facebookProductCatalogId,
                    JsonConvert.SerializeObject(exception));
            }
        }

        return connectedProductCatalogWabas;
    }

    public async Task<Waba> UpdateWabaConnectedFacebookProductCatalogAsync(
        string sleekflowCompanyId,
        string wabaId,
        string? facebookProductCatalogId,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(
            wabaId,
            sleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) = _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var businessIntegrationSystemUserAccessToken =
            hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null &&
            decryptedBusinessIntegrationSystemUserAccessTokenDto.Scopes.Contains("catalog_management")
                ? decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken
                : null;

        var facebookCommerceClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new FacebookCommerceClient(businessIntegrationSystemUserAccessToken, _httpClient)
            : _facebookCommerceClient;

        var getWabaProductCatalogsResponse = await facebookCommerceClient.GetWabaProductCatalogs(waba.FacebookWabaId);

        var existingConnectedFacebookProductCatalogId = getWabaProductCatalogsResponse.Data.FirstOrDefault()?.Id;

        if (!string.IsNullOrEmpty(existingConnectedFacebookProductCatalogId))
        {
            if (existingConnectedFacebookProductCatalogId == facebookProductCatalogId)
            {
                _logger.LogWarning(
                    "Waba {WabaId} already connected with {FacebookProductCatalogId}",
                    wabaId,
                    existingConnectedFacebookProductCatalogId);

                throw new SfInternalErrorException($"Waba {wabaId} already connected with {facebookProductCatalogId}");
            }

            // update waba connected product catalog id to facebookProductCatalogId
            if (!string.IsNullOrEmpty(facebookProductCatalogId) &&
                facebookProductCatalogId != existingConnectedFacebookProductCatalogId)
            {
                try
                {
                    var facebookBusinessProductCatalogs =
                        await GetFacebookBusinessOwnedProductCatalogsAsync(
                            waba.FacebookBusinessId!,
                            businessIntegrationSystemUserAccessToken);

                    if (facebookBusinessProductCatalogs.All(x => x.Id != facebookProductCatalogId))
                    {
                        _logger.LogWarning(
                            "Unable to find {FacebookProductCatalogId} with {FacebookBusinessId} in {FacebookProductCatalogList}",
                            existingConnectedFacebookProductCatalogId,
                            waba.FacebookBusinessId,
                            JsonConvert.SerializeObject(facebookBusinessProductCatalogs));

                        throw new SfInternalErrorException(
                            $"unable to find facebook product catalog {facebookProductCatalogId} from facebook business {waba.FacebookBusinessId}");
                    }

                    if (!await ProductCatalogAssignedUsersAsync(facebookProductCatalogId, sleekflowCompanyId, decryptedBusinessIntegrationSystemUserAccessTokenDto))
                    {
                        _logger.LogWarning(
                            "Unable to assign user with {FacebookProductCatalogId}",
                            facebookProductCatalogId);
                        throw new SfInternalErrorException(
                            $"unable to assign system user to facebook product catalog {facebookProductCatalogId}");
                    }

                    if (!await DisconnectFacebookWabaProductCatalogAsync(
                            waba.FacebookWabaId,
                            existingConnectedFacebookProductCatalogId,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken))
                    {
                        _logger.LogWarning(
                            "Unable to disconnect {FacebookProductCatalogId} with {WabaId}",
                            existingConnectedFacebookProductCatalogId,
                            wabaId);

                        throw new SfInternalErrorException(
                            $"unable to disconnect waba {wabaId} from existing facebook product catalog {existingConnectedFacebookProductCatalogId}");
                    }

                    if (!await ConnectFacebookWabaProductCatalogAsync(
                            waba.FacebookWabaId,
                            facebookProductCatalogId,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken))
                    {
                        _logger.LogWarning(
                            "Unable to connect {FacebookProductCatalog} with {WabaId}",
                            existingConnectedFacebookProductCatalogId,
                            wabaId);

                        throw new SfInternalErrorException(
                            $"unable to disconnect waba {wabaId} from existing facebook product catalog {existingConnectedFacebookProductCatalogId}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Caught error for updating facebook waba {WabaId} connected facebook product catalog {FacebookProductCatalogId}",
                        wabaId,
                        facebookProductCatalogId);

                    if (ex is GraphApiClientException gx)
                    {
                        throw new SfGraphApiErrorException(
                            "unable to update facebook waba connected product catalog",
                            JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
                    }

                    throw new SfInternalErrorException(
                        ex,
                        $"Caught error for updating facebook waba {wabaId} connected facebook product catalog {facebookProductCatalogId}");
                }
            }

            // disconnect product catalog from waba
            else if (string.IsNullOrEmpty(facebookProductCatalogId))
            {
                try
                {
                    if (!await DisconnectFacebookWabaProductCatalogAsync(
                            waba.FacebookWabaId,
                            existingConnectedFacebookProductCatalogId,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken))
                    {
                        _logger.LogWarning(
                            "Unable to disconnect {FacebookProductCatalogId} with {WabaId}",
                            existingConnectedFacebookProductCatalogId,
                            wabaId);

                        throw new SfInternalErrorException(
                            $"unable to disconnect waba {wabaId} from existing facebook product catalog {existingConnectedFacebookProductCatalogId}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Caught error for disconnecting facebook waba {WabaId} from existing facebook product catalog {FacebookProductCatalogId}",
                        wabaId,
                        existingConnectedFacebookProductCatalogId);

                    if (ex is GraphApiClientException gx)
                    {
                        throw new SfGraphApiErrorException(
                            "unable to disconnect facebook waba product catalog",
                            JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
                    }

                    throw new SfInternalErrorException(
                        ex,
                        $"Caught error for disconnecting facebook waba {wabaId} from existing facebook product catalog {existingConnectedFacebookProductCatalogId}");
                }
            }
        }

        // connect waba to product catalog in facebook
        else if (string.IsNullOrEmpty(existingConnectedFacebookProductCatalogId) &&
                 !string.IsNullOrEmpty(facebookProductCatalogId))
        {
            var wabas = await ConnectWabasProductCatalogsAsync(
                sleekflowCompanyId,
                new Dictionary<string, string>()
                {
                    {
                        wabaId, facebookProductCatalogId
                    }
                },
                sleekflowStaff);

            return wabas.First();
        }

        // refresh waba product catalog and save into cosmos db
        return await RefreshWabaProductCatalogAsync(sleekflowCompanyId, wabaId, sleekflowStaff);
    }

    public async Task<List<FacebookConnectedWabaProductCatalogMappingDto>>
        GetWabasConnectedFacebookProductCatalogsWithFacebookAuthorizationCodeAsync(
            string facebookAuthorizationCode,
            string sleekflowCompanyId,
            AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var businessIntegrationSystemUserAccessToken = await _wabaService.ExchangeAccessTokenFromFacebookAuthorizationCodeAsync(facebookAuthorizationCode);

        var debugTokenResponse =
            await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(businessIntegrationSystemUserAccessToken);

        if (!debugTokenResponse.Data.Scopes.Contains("catalog_management"))
        {
            throw new SfInternalErrorException($"missing catalog_management permission in scopes {JsonConvert.SerializeObject(debugTokenResponse.Data.Scopes)}");
        }

        var scopes = new WabaDebugTokenGranularScopes(
            _logger,
            debugTokenResponse);

        var facebookBusinessIds = new ConcurrentBag<string>();

        var facebookConnectedWabaProductCatalogMappingDtos = new ConcurrentBag<FacebookConnectedWabaProductCatalogMappingDto>();

        await Parallel.ForEachAsync(
            scopes.WabaIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 5
            },
            async (facebookWabaId, cancellationToken) =>
            {
                Waba waba;

                try
                {
                    waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);

                    if (!string.IsNullOrEmpty(waba.FacebookBusinessId))
                    {
                        facebookBusinessIds.Add(waba.FacebookBusinessId);
                    }
                }
                catch (Exception)
                {
                    _logger.LogWarning(
                        "{FacebookWabaId} not found in sleekflow when getting wabas connected facebook product catalogs with user access token",
                        facebookWabaId);

                    return;
                }

                if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
                {
                    return;
                }

                if (!waba.SleekflowCompanyIds.Contains(sleekflowCompanyId))
                {
                    _logger.LogWarning(
                        "Unable to locate any valid waba {Waba} with {SleekflowCompanyId}",
                        JsonConvert.SerializeObject(waba),
                        sleekflowCompanyId);

                    return;
                }

                var encryptedFacebookBusinessIntegrationSystemUserAccessToken =
                    _wabaService.GetEncryptedFacebookBusinessIntegrationSystemUserAccessToken(
                        businessIntegrationSystemUserAccessToken,
                        debugTokenResponse);

                if (!await PatchWabaAsync(
                        sleekflowCompanyId,
                        waba,
                        encryptedFacebookBusinessIntegrationSystemUserAccessToken))
                {
                    throw new SfInternalErrorException(
                        "Unable to patch waba with encrypted facebook business integration system user access token");
                }

                var facebookProductCatalog = await GetFacebookWabaConnectedProductCatalogAsync(
                    facebookWabaId,
                    businessIntegrationSystemUserAccessToken);

                var refreshedWaba = await _wabaService.GetAndRefreshWabaAsync(
                    waba.Id,
                    sleekflowCompanyId,
                    true,
                    sleekflowStaff,
                    businessIntegrationSystemUserAccessToken);

                facebookConnectedWabaProductCatalogMappingDtos.Add(
                    new FacebookConnectedWabaProductCatalogMappingDto(
                        new WabaDto(refreshedWaba),
                        facebookProductCatalog != null ? new WabaProductCatalogDto(facebookProductCatalog) : null));
            });

        if (!scopes.WabaIds.Any() || !facebookBusinessIds.Any())
        {
            return facebookConnectedWabaProductCatalogMappingDtos.ToList();
        }

        foreach (var facebookBusinessId in facebookBusinessIds.Distinct().ToList())
        {
            var fbProductCatalogs = await GetFacebookBusinessOwnedProductCatalogsAsync(
                facebookBusinessId,
                businessIntegrationSystemUserAccessToken);
            var catalogIds = fbProductCatalogs.Select(x => x.Id).Distinct().ToList();

            var wabaFacebookProductCatalogIds = facebookConnectedWabaProductCatalogMappingDtos
                .Where(x => x.WabaProductCatalog != null)
                .Select(x => x.WabaProductCatalog?.FacebookProductCatalogId).Distinct().ToList();

            var notWabaConnectedCatalogIds = catalogIds.Except(wabaFacebookProductCatalogIds).ToList();

            fbProductCatalogs.Where(x => notWabaConnectedCatalogIds.Contains(x.Id)).ToList().ForEach(
                x =>
                {
                    facebookConnectedWabaProductCatalogMappingDtos.Add(
                        new FacebookConnectedWabaProductCatalogMappingDto(null, new WabaProductCatalogDto(x)));
                });
        }

        return facebookConnectedWabaProductCatalogMappingDtos.ToList();
    }

    public async Task<List<FacebookConnectedWabaProductCatalogMappingDto>>
        GetWabasConnectedFacebookProductCatalogsWithUserAccessTokenAsync(
            string userAccessToken,
            string sleekflowCompanyId,
            AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var granularScopes = await _wabaService.GetUserAccessTokenGranularScopes(userAccessToken);

        if (granularScopes == null)
        {
            throw new SfInternalErrorException("unable to debug the granular scopes of access token");
        }

        var scopes = new WabaDebugTokenGranularScopes(_logger, granularScopes);

        var permissionsRequired = new List<string>()
        {
            "catalog_management",
            "whatsapp_business_messaging",
            "whatsapp_business_management",
            "public_profile",
            "business_management"
        };

        if (granularScopes.Data.Scopes.Intersect(permissionsRequired).Count() < permissionsRequired.Count)
        {
            throw new SfInternalErrorException(
                $"insufficient permission granted with {JsonConvert.SerializeObject(granularScopes.Data.Scopes)} while permissions required are {JsonConvert.SerializeObject(permissionsRequired)}");
        }

        var facebookConnectedWabaProductCatalogMappingDtos = new List<FacebookConnectedWabaProductCatalogMappingDto>();

        foreach (var facebookWabaId in scopes.WabaIds)
        {
            Waba waba;

            try
            {
                waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);
            }
            catch (Exception)
            {
                _logger.LogWarning(
                    "{FacebookWabaId} not found in sleekflow when getting wabas connected facebook product catalogs with user access token",
                    facebookWabaId);

                continue;
            }

            if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
            {
                continue;
            }

            if (!waba.SleekflowCompanyIds.Contains(sleekflowCompanyId))
            {
                _logger.LogWarning(
                    "Unable to locate any valid waba {Waba} with {SleekflowCompanyId}",
                    JsonConvert.SerializeObject(waba),
                    sleekflowCompanyId);

                continue;
            }

            var facebookProductCatalog = await GetFacebookWabaConnectedProductCatalogAsync(facebookWabaId, null);

            var refreshedWaba = await _wabaService.GetAndRefreshWabaAsync(
                waba.Id,
                sleekflowCompanyId,
                true,
                sleekflowStaff,
                userAccessToken: userAccessToken);

            facebookConnectedWabaProductCatalogMappingDtos.Add(
                new FacebookConnectedWabaProductCatalogMappingDto(
                    new WabaDto(refreshedWaba),
                    facebookProductCatalog != null ? new WabaProductCatalogDto(facebookProductCatalog) : null));
        }

        var facebookBusinessIds = scopes.BusinessIds;

        if (facebookBusinessIds == null)
        {
            return facebookConnectedWabaProductCatalogMappingDtos;
        }

        foreach (var facebookBusinessId in facebookBusinessIds)
        {
            var fbProductCatalogs = await GetFacebookBusinessOwnedProductCatalogsAsync(facebookBusinessId, null);
            var catalogIds = fbProductCatalogs.Select(x => x.Id).Distinct().ToList();

            var wabaFacebookProductCatalogIds = facebookConnectedWabaProductCatalogMappingDtos
                .Where(x => x.WabaProductCatalog != null)
                .Select(x => x.WabaProductCatalog?.FacebookProductCatalogId).Distinct().ToList();

            var notWabaConnectedCatalogIds = catalogIds.Except(wabaFacebookProductCatalogIds).ToList();

            fbProductCatalogs.Where(x => notWabaConnectedCatalogIds.Contains(x.Id)).ToList().ForEach(
                x =>
                {
                    facebookConnectedWabaProductCatalogMappingDtos.Add(
                        new FacebookConnectedWabaProductCatalogMappingDto(null, new WabaProductCatalogDto(x)));
                });
        }

        return facebookConnectedWabaProductCatalogMappingDtos;
    }

    public async Task<List<FacebookConnectedWabaProductCatalogMappingDto>>
        GetWabasConnectedFacebookProductCatalogsAsync(
            string sleekflowCompanyId,
            bool shouldRefresh,
            AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var facebookConnectedWabaProductCatalogMappingDtos = new List<FacebookConnectedWabaProductCatalogMappingDto>();

        var wabas = await _wabaService.GetWabasAsync(sleekflowCompanyId);

        if (!wabas.Any())
        {
            return facebookConnectedWabaProductCatalogMappingDtos;
        }

        foreach (var waba in wabas)
        {
            if (shouldRefresh)
            {
                var refreshedWaba = await RefreshWabaProductCatalogAsync(sleekflowCompanyId, waba.Id, sleekflowStaff);

                facebookConnectedWabaProductCatalogMappingDtos.Add(
                    new FacebookConnectedWabaProductCatalogMappingDto(
                        new WabaDto(refreshedWaba),
                        refreshedWaba.WabaProductCatalog != null
                            ? new WabaProductCatalogDto(refreshedWaba.WabaProductCatalog)
                            : null));
            }
            else
            {
                var wabaProductCatalog = await GetWabaProductCatalogAsync(sleekflowCompanyId, waba.Id);

                facebookConnectedWabaProductCatalogMappingDtos.Add(
                    new FacebookConnectedWabaProductCatalogMappingDto(
                        new WabaDto(waba),
                        wabaProductCatalog != null ? new WabaProductCatalogDto(wabaProductCatalog) : null));
            }
        }

        return facebookConnectedWabaProductCatalogMappingDtos;
    }

    private async Task<bool> PatchWabaAsync(
        string sleekflowCompanyId,
        Waba waba,
        FacebookBusinessIntegrationSystemUserAccessToken
            encryptedFacebookBusinessIntegrationSystemUserAccessToken)
    {
        var newWaba = waba;

        // limit encrypted business integration system user access token list to at most latest 5 records
        if (waba.FacebookBusinessIntegrationSystemUserAccessTokens != null &&
            waba.FacebookBusinessIntegrationSystemUserAccessTokens.Count >= 5)
        {
            var latestBusinessIntegrationSystemUserAccessTokens = waba
                .FacebookBusinessIntegrationSystemUserAccessTokens.OrderByDescending(x => x.CreatedAt).Take(4);

            newWaba.FacebookBusinessIntegrationSystemUserAccessTokens = latestBusinessIntegrationSystemUserAccessTokens.Prepend(encryptedFacebookBusinessIntegrationSystemUserAccessToken).ToList();
        }
        else
        {
            newWaba.FacebookBusinessIntegrationSystemUserAccessTokens =
                waba.FacebookBusinessIntegrationSystemUserAccessTokens != null
                    ? waba.FacebookBusinessIntegrationSystemUserAccessTokens
                        .Prepend(encryptedFacebookBusinessIntegrationSystemUserAccessToken).ToList()
                    : new List<FacebookBusinessIntegrationSystemUserAccessToken>()
                    {
                        encryptedFacebookBusinessIntegrationSystemUserAccessToken
                    };
        }

        var patchSuccess = await _commonRetryPolicyService.GetAsyncRetryPolicy()
            .ExecuteAsync(
                async () =>
                {
                    try
                    {
                        return await _wabaRepository.ReplaceWabaAsync(waba, newWaba);
                    }
                    catch (Exception exception)
                    {
                        _logger.LogError(
                            "Unable to patch waba with encrypted facebook business integration system user access token {EncryptedFacebookBusinessIntegrationSystemUserAccessToken} with exception {Exception}",
                            JsonConvert.SerializeObject(
                                encryptedFacebookBusinessIntegrationSystemUserAccessToken),
                            JsonConvert.SerializeObject(exception));
                        var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);

                        waba = refreshWabaEntity ??
                               throw new SfNotFoundObjectException(
                                   "Unable to patch waba with encrypted facebook business integration system user access token");

                        throw;
                    }
                });

        return patchSuccess;
    }

    private async Task<bool> PatchWabaAsync(
        string sleekflowCompanyId,
        Waba waba,
        HashSet<WabaPhoneNumber> updatedWabaPhoneNumbers,
        WabaPhoneNumber targetWabaPhoneNumber)
    {
        updatedWabaPhoneNumbers = updatedWabaPhoneNumbers
            .Select(wpn => wpn.Id == targetWabaPhoneNumber.Id ? targetWabaPhoneNumber : wpn).ToHashSet();

        var patchSuccess = await _commonRetryPolicyService.GetAsyncRetryPolicy()
            .ExecuteAsync(
                async () =>
                {
                    try
                    {
                        return await _wabaRepository.PatchWabaAsync(waba, updatedWabaPhoneNumbers);
                    }
                    catch (Exception exception)
                    {
                        _logger.LogError(
                            "Unable to patch waba phone number {PhoneNumberId} with updated whatsapp commerce setting with error exception {Exception}",
                            targetWabaPhoneNumber.Id,
                            JsonConvert.SerializeObject(exception));
                        var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);
                        waba = refreshWabaEntity ??
                               throw new SfNotFoundObjectException("Unable to patch waba phone number");
                        throw;
                    }
                });

        _logger.LogInformation(
            "Patched Waba {Waba} with updatedWabaPhoneNumbers {UpdatedWabaPhoneNumbers} for targetWabaPhoneNumber {TargetWabaPhoneNumber}",
            JsonConvert.SerializeObject(waba, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(updatedWabaPhoneNumbers, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(targetWabaPhoneNumber, JsonConfig.DefaultJsonSerializerSettings));

        await _auditLogService.AuditWabaAsync(
            waba,
            waba.FacebookWabaId,
            sleekflowCompanyId,
            AuditingOperation.WabaPhoneNumberEnableProductCatalog,
            JsonConvertExtensions.ToDictionary(
                new Dictionary<string, object>
                {
                    {
                        "changes", updatedWabaPhoneNumbers
                    }
                }));

        return patchSuccess;
    }

    private async Task<bool> PatchWabaAsync(
        string sleekflowCompanyId,
        Waba waba,
        WabaProductCatalog updatedWabaProductCatalog)
    {
        var patchSuccess = await _commonRetryPolicyService.GetAsyncRetryPolicy()
            .ExecuteAsync(
                async () =>
                {
                    try
                    {
                        return await _wabaRepository.PatchWabaAsync(waba, updatedWabaProductCatalog);
                    }
                    catch (Exception exception)
                    {
                        _logger.LogError(
                            "Unable to patch waba product catalog {WabaId} with updated whatsapp commerce setting with error exception {Exception}",
                            waba.Id,
                            JsonConvert.SerializeObject(exception));
                        var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);
                        waba = refreshWabaEntity ??
                               throw new SfNotFoundObjectException("Unable to patch waba phone number");
                        throw;
                    }
                });

        _logger.LogInformation(
            "Patched Waba {Waba} with product catalog {ProductCatalog}",
            JsonConvert.SerializeObject(waba, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(updatedWabaProductCatalog, JsonConfig.DefaultJsonSerializerSettings));

        await _auditLogService.AuditWabaAsync(
            waba,
            waba.FacebookWabaId,
            sleekflowCompanyId,
            AuditingOperation.RefreshWabaProductCatalog,
            JsonConvertExtensions.ToDictionary(
                new Dictionary<string, object>
                {
                    {
                        "changes", updatedWabaProductCatalog
                    }
                }));

        return patchSuccess;
    }

    private async Task<bool> PatchWabaAsync(
        string sleekflowCompanyId,
        Waba waba,
        HashSet<WabaPhoneNumber> updatedWabaPhoneNumbers,
        WabaProductCatalog updatedWabaProductCatalog)
    {
        var patchSuccess = await _commonRetryPolicyService.GetAsyncRetryPolicy()
            .ExecuteAsync(
                async () =>
                {
                    try
                    {
                        return await _wabaRepository.PatchWabaAsync(
                            waba,
                            updatedWabaPhoneNumbers,
                            updatedWabaProductCatalog);
                    }
                    catch (Exception exception)
                    {
                        _logger.LogError(
                            "Unable to patch waba {WabaId} phone numbers and product catalog with updated whatsapp commerce setting with error exception {Exception}",
                            waba.Id,
                            JsonConvert.SerializeObject(exception));
                        var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);
                        waba = refreshWabaEntity ??
                               throw new SfNotFoundObjectException("Unable to patch waba phone number");
                        throw;
                    }
                });

        _logger.LogInformation(
            "Patched Waba {Waba} with product catalog {ProductCatalog} and phone numbers {PhoneNumbers}",
            JsonConvert.SerializeObject(waba, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(updatedWabaProductCatalog, JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(updatedWabaPhoneNumbers, JsonConfig.DefaultJsonSerializerSettings));

        await _auditLogService.AuditWabaAsync(
            waba,
            waba.FacebookWabaId,
            sleekflowCompanyId,
            AuditingOperation.RefreshWabasPhoneNumbersProductCatalogs,
            JsonConvertExtensions.ToDictionary(
                new Dictionary<string, object>
                {
                    {
                        "changes_waba_phone_numbers", updatedWabaPhoneNumbers
                    },
                    {
                        "changes_waba_active_product_catalog", updatedWabaProductCatalog
                    }
                }));

        return patchSuccess;
    }

    private async Task<bool> ProductCatalogAssignedUsersAsync(
        string facebookProductCatalogId,
        string sleekflowCompanyId,
        DecryptedBusinessIntegrationSystemUserAccessTokenDto? decryptedBusinessIntegrationSystemUserAccessTokenDto)
    {
        var whatsappCloudApiBspClient = decryptedBusinessIntegrationSystemUserAccessTokenDto != null
            ? new WhatsappCloudApiBspClient(decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken, _httpClient)
            : _whatsappCloudApiBspClient;

        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => whatsappCloudApiBspClient.AddProductCatalogAssignedUser(
                facebookProductCatalogId,
                decryptedBusinessIntegrationSystemUserAccessTokenDto != null ? decryptedBusinessIntegrationSystemUserAccessTokenDto.FacebookBusinessSystemUserId : _secretConfig.FacebookSystemUserId,
                ProductCatalogAssignedUserPermissionConst.MANAGE),
            new BaseAuditingObject(facebookProductCatalogId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "product_catalog_id", facebookProductCatalogId
                    },
                    {
                        "facebook_system_user_id", decryptedBusinessIntegrationSystemUserAccessTokenDto != null ? decryptedBusinessIntegrationSystemUserAccessTokenDto.FacebookBusinessSystemUserId : _secretConfig.FacebookSystemUserId
                    },
                    {
                        "product_catalog_assigned_user_permission", ProductCatalogAssignedUserPermissionConst.MANAGE
                    }
                },
                Operation = AuditingOperation.ConnectProductCatalogAddProductCatalogAssignedUser
            })).Success;
    }

    private async Task<bool> DisconnectFacebookWabaProductCatalogAsync(
        string facebookWabaId,
        string facebookProductCatalogId,
        string sleekflowCompanyId,
        string? businessIntegrationSystemUserAccessToken)
    {
        var facebookCommerceClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new FacebookCommerceClient(businessIntegrationSystemUserAccessToken, _httpClient)
            : _facebookCommerceClient;

        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => facebookCommerceClient.DisconnectWabaProductCatalog(
                facebookWabaId,
                facebookProductCatalogId),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "facebook_waba_id", facebookWabaId
                    },
                    {
                        "facebook_product_catalog_id", facebookProductCatalogId
                    }
                },
                Operation = AuditingOperation.DisconnectFacebookWabaProductCatalog
            })).Success;
    }

    private async Task<bool> ConnectFacebookWabaProductCatalogAsync(
        string facebookWabaId,
        string facebookProductCatalogId,
        string sleekflowCompanyId,
        string? businessIntegrationSystemUserAccessToken)
    {
        var facebookCommerceClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new FacebookCommerceClient(businessIntegrationSystemUserAccessToken, _httpClient)
            : _facebookCommerceClient;

        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => facebookCommerceClient.ConnectWabaProductCatalog(
                facebookWabaId,
                facebookProductCatalogId),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "facebook_waba_id", facebookWabaId
                    },
                    {
                        "facebook_product_catalog_id", facebookProductCatalogId
                    }
                },
                Operation = AuditingOperation.ConnectFacebookWabaProductCatalog
            })).Success;
    }

    private async Task<List<FacebookProductCatalog>> GetFacebookBusinessOwnedProductCatalogsAsync(
        string facebookBusinessId,
        string? businessIntegrationSystemUserAccessToken)
    {
        List<FacebookProductCatalog> facebookProductCatalogs;
        try
        {
            var facebookCommerceClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new FacebookCommerceClient(businessIntegrationSystemUserAccessToken, _httpClient)
                : _facebookCommerceClient;

            var getBusinessProductCatalogsResponse =
                await facebookCommerceClient.GetBusinessProductCatalogs(facebookBusinessId);
            facebookProductCatalogs = getBusinessProductCatalogsResponse.Data.ToList();

            do
            {
                getBusinessProductCatalogsResponse = await facebookCommerceClient.GetBusinessProductCatalogs(
                    facebookBusinessId,
                    null,
                    new CursorBasedPaginationParam()
                    {
                        After = getBusinessProductCatalogsResponse.Paging?.Cursors?.After,
                        Limit = 1000
                    });

                facebookProductCatalogs.AddRange(getBusinessProductCatalogsResponse.Data.ToList());
            }
            while (getBusinessProductCatalogsResponse.Data.Any());
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for facebook business id {FacebookBusinessId} owned product catalogs",
                facebookBusinessId);

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "unable to get business owned product catalogs",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }

            throw new SfInternalErrorException(
                ex,
                $"Caught error for getting facebook business id {facebookBusinessId} owned product catalogs");
        }

        return facebookProductCatalogs;
    }

    private async Task<FacebookProductCatalog?> GetFacebookWabaConnectedProductCatalogAsync(
        string facebookWabaId,
        string? businessIntegrationSystemUserAccessToken)
    {
        try
        {
            var facebookCommerceClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new FacebookCommerceClient(businessIntegrationSystemUserAccessToken, _httpClient)
                : _facebookCommerceClient;

            var getWabaProductCatalogsResponse = await facebookCommerceClient.GetWabaProductCatalogs(facebookWabaId);

            if (getWabaProductCatalogsResponse.Data.Any())
            {
                return getWabaProductCatalogsResponse.Data.First();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for getting facebook waba {FacebookWabaId} product catalogs",
                facebookWabaId);
        }

        return null;
    }

    private async Task<WhatsappCommerceSetting> UpdateFacebookPhoneNumberCommerceSettingAsync(
        Waba waba,
        string facebookPhoneNumberId,
        bool? isCatalogVisible,
        bool? isCartEnabled,
        string sleekflowCompanyId,
        string? businessIntegrationSystemUserAccessToken)
    {
        var facebookCommerceClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new FacebookCommerceClient(businessIntegrationSystemUserAccessToken, _httpClient)
            : _facebookCommerceClient;

        var editSuccess = (await _auditLogService.GetCloudApiAuditedResponse(
            () => facebookCommerceClient.EditWhatsappCommerceSettings(
                facebookPhoneNumberId,
                isCatalogVisible ?? true,
                isCartEnabled ?? true),
            new BaseAuditingObject(facebookPhoneNumberId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "phone_number_id", facebookPhoneNumberId
                    },
                    {
                        "is_catalog_visible",
                        isCatalogVisible is null ? true.ToString() : isCatalogVisible.Value.ToString()
                    },
                    {
                        "is_cart_enabled", isCartEnabled is null ? true.ToString() : isCartEnabled.Value.ToString()
                    }
                },
                Operation = AuditingOperation.UpdateFacebookPhoneNumberCommerceSetting
            })).Success;

        if (!editSuccess)
        {
            _logger.LogError(
                "Unable to set {WabaId} {FacebookPhoneNumberId} with visible catalog and functional cart button",
                waba.Id,
                facebookPhoneNumberId);
            throw new SfInternalErrorException("Unable to edit whatsapp phone number commerce setting");
        }

        var whatsappCommerceSettingOutput =
            await facebookCommerceClient.GetWhatsappCommerceSettings(facebookPhoneNumberId);

        if (whatsappCommerceSettingOutput.Data == null || !whatsappCommerceSettingOutput.Data.Any())
        {
            _logger.LogError(
                "Unable to get {WabaId} {FacebookPhoneNumberId} whatsapp commerce setting",
                waba.Id,
                facebookPhoneNumberId);
            throw new SfInternalErrorException("Unable to get whatsapp phone number commerce setting");
        }

        return new WhatsappCommerceSetting(
            whatsappCommerceSettingOutput.Data.FirstOrDefault()!.Id,
            whatsappCommerceSettingOutput.Data.FirstOrDefault()!.IsCatalogVisible,
            whatsappCommerceSettingOutput.Data.FirstOrDefault()!.IsCartEnabled);
    }
}