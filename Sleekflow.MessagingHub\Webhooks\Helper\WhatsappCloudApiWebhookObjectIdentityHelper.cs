using GraphApi.Client.Models.WebhookObjects;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Webhooks.Constants;

namespace Sleekflow.MessagingHub.Webhooks.Helper;

public static class WhatsappCloudApiWebhookObjectIdentityHelper
{
    public static WhatsappCloudApiWebhookObjectIdentity? ResolveWebhookObjectIdentity(
        Waba waba,
        CloudApiWebhookChangeObject cloudApiWebhookChange,
        CloudApiWebhookValueObject cloudApiWebhookValue)
    {
        return cloudApiWebhookChange.Field switch
        {
            WhatsappCloudApiWebhookTypeNames.AccountAlerts => cloudApiWebhookValue.EntityType switch
            {
                WhatsappCloudApiWebhookEntityTypeNames.Business => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    cloudApiWebhookValue.EntityId,
                    null,
                    null),
                WhatsappCloudApiWebhookEntityTypeNames.PhoneNumber => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    cloudApiWebhookValue.EntityId,
                    null),
                WhatsappCloudApiWebhookEntityTypeNames.Waba => new WhatsappCloudApiWebhookObjectIdentity(
                    cloudApiWebhookValue.EntityId,
                    null,
                    null,
                    null),
                _ => null
            },
            WhatsappCloudApiWebhookTypeNames.AccountReviewUpdate => null,
            WhatsappCloudApiWebhookTypeNames.AccountUpdate => cloudApiWebhookValue.Event switch
            {
                WhatsappCloudApiWebhookEventTypeNames.BusinessVerificationStatusUpdate => new
                    WhatsappCloudApiWebhookObjectIdentity(
                        waba.FacebookWabaId,
                        waba.FacebookBusinessId,
                        null,
                        null),
                WhatsappCloudApiWebhookEventTypeNames.PartnerAdded => new WhatsappCloudApiWebhookObjectIdentity(
                    cloudApiWebhookValue.WabaInfo?.WabaId ?? waba.FacebookWabaId,
                    cloudApiWebhookValue.WabaInfo?.OwnerBusinessId ?? waba.FacebookBusinessId,
                    null,
                    null),
                WhatsappCloudApiWebhookEventTypeNames.AccountDeleted => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    null,
                    null),
                WhatsappCloudApiWebhookEventTypeNames.PhoneNumberAdded => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    waba.WabaPhoneNumbers
                        .Where(
                            x => x.FacebookPhoneNumberDetail.DisplayPhoneNumber != null &&
                                 PurePhoneNumberFormatter.FormatPhoneNumber(
                                     x.FacebookPhoneNumberDetail.DisplayPhoneNumber) ==
                                 cloudApiWebhookValue.PhoneNumber).Select(x => x.FacebookPhoneNumberId)
                        .FirstOrDefault(),
                    null),
                WhatsappCloudApiWebhookEventTypeNames.PhoneNumberRemoved => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    waba.WabaPhoneNumbers
                        .Where(
                            x => x.FacebookPhoneNumberDetail.DisplayPhoneNumber != null &&
                                 PurePhoneNumberFormatter.FormatPhoneNumber(
                                     x.FacebookPhoneNumberDetail.DisplayPhoneNumber) ==
                                 cloudApiWebhookValue.PhoneNumber).Select(x => x.FacebookPhoneNumberId)
                        .FirstOrDefault(),
                    null),
                WhatsappCloudApiWebhookEventTypeNames.AccountViolation => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    null,
                    null),
                WhatsappCloudApiWebhookEventTypeNames.AccountRestriction => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    null,
                    null),
                WhatsappCloudApiWebhookEventTypeNames.VerifiedAccount => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    null,
                    null),
                WhatsappCloudApiWebhookEventTypeNames.DisabledUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookWabaId,
                    null,
                    null,
                    null),
                WhatsappCloudApiWebhookEventTypeNames.AdAccountLinked => new WhatsappCloudApiWebhookObjectIdentity(
                    waba.FacebookBusinessId,
                    null,
                    null,
                    null),
                _ => null
            },
            WhatsappCloudApiWebhookTypeNames.BusinessCapabilityUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                waba.FacebookBusinessId,
                null,
                null),
            WhatsappCloudApiWebhookTypeNames.BusinessStatusUpdate => cloudApiWebhookValue.Event switch
            {
                WhatsappCloudApiWebhookEventTypeNames.CompromisedNotification => new
                    WhatsappCloudApiWebhookObjectIdentity(
                        waba.FacebookWabaId,
                        cloudApiWebhookValue.BusinessId.ToString(),
                        null,
                        null),
                _ => null
            },
            WhatsappCloudApiWebhookTypeNames.TemplateCategoryUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                null,
                null,
                cloudApiWebhookValue.MessageTemplateId),
            WhatsappCloudApiWebhookTypeNames.MessageTemplateStatusUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                null,
                null,
                cloudApiWebhookValue.MessageTemplateId),
            WhatsappCloudApiWebhookTypeNames.MessageTemplateQualityUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                null,
                null,
                cloudApiWebhookValue.MessageTemplateId),
            WhatsappCloudApiWebhookTypeNames.PhoneNumberNameUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                null,
                waba.WabaPhoneNumbers
                    .Where(
                        x => x.FacebookPhoneNumberDetail.DisplayPhoneNumber != null &&
                             PurePhoneNumberFormatter.FormatPhoneNumber(
                                 x.FacebookPhoneNumberDetail.DisplayPhoneNumber) ==
                             cloudApiWebhookValue.DisplayPhoneNumber).Select(x => x.FacebookPhoneNumberId)
                    .FirstOrDefault(),
                null),
            WhatsappCloudApiWebhookTypeNames.PhoneNumberQualityUpdate => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                null,
                waba.WabaPhoneNumbers
                    .Where(
                        x => x.FacebookPhoneNumberDetail.DisplayPhoneNumber != null &&
                             PurePhoneNumberFormatter.FormatPhoneNumber(
                                 x.FacebookPhoneNumberDetail.DisplayPhoneNumber) ==
                             cloudApiWebhookValue.DisplayPhoneNumber).Select(x => x.FacebookPhoneNumberId)
                    .FirstOrDefault(),
                null),
            WhatsappCloudApiWebhookTypeNames.Security => new WhatsappCloudApiWebhookObjectIdentity(
                waba.FacebookWabaId,
                null,
                waba.WabaPhoneNumbers
                    .Where(
                        x => x.FacebookPhoneNumberDetail.DisplayPhoneNumber != null &&
                             PurePhoneNumberFormatter.FormatPhoneNumber(
                                 x.FacebookPhoneNumberDetail.DisplayPhoneNumber) ==
                             cloudApiWebhookValue.DisplayPhoneNumber).Select(x => x.FacebookPhoneNumberId)
                    .FirstOrDefault(),
                null),
            _ => null,
        };
    }

    public static string? ResolveWabaIdFromFacebookBusinessIdEntryWebhook(
        CloudApiWebhookChangeObject cloudApiWebhookChange,
        CloudApiWebhookValueObject cloudApiWebhookValue)
    {
        if (cloudApiWebhookChange.Field == WhatsappCloudApiWebhookTypeNames.AccountUpdate &&
            !string.IsNullOrEmpty(cloudApiWebhookValue.Event) &&
            (
                cloudApiWebhookValue is { Event: WhatsappCloudApiWebhookEventTypeNames.PartnerAdded, WabaInfo: not null } ||
                cloudApiWebhookValue is { Event: WhatsappCloudApiWebhookEventTypeNames.AdAccountLinked, WabaInfo: not null }
            ) &&
            !string.IsNullOrEmpty(cloudApiWebhookValue.WabaInfo.WabaId))
        {
            return cloudApiWebhookValue.WabaInfo.WabaId;
        }

        return null;
    }
}

public class WhatsappCloudApiWebhookObjectIdentity
{
    public string? FacebookWabaId { get; set; }

    public string? FacebookBusinessId { get; set; }

    public string? FacebookPhoneNumberId { get; set; }

    public string? FacebookMessageTemplateId { get; set; }

    public WhatsappCloudApiWebhookObjectIdentity(
        string? facebookWabaId,
        string? facebookBusinessId,
        string? facebookPhoneNumberId,
        string? facebookMessageTemplateId)
    {
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        FacebookPhoneNumberId = facebookPhoneNumberId;
        FacebookMessageTemplateId = facebookMessageTemplateId;
    }
}