using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class DeleteCategory
    : ITrigger<
        DeleteCategory.DeleteCategoryInput,
        DeleteCategory.DeleteCategoryOutput>
{
    private readonly ICategoryService _categoryService;

    public DeleteCategory(ICategoryService categoryService)
    {
        _categoryService = categoryService;
    }

    public class DeleteCategoryInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteCategoryInput(
            string id,
            string sleekflowCompanyId,
            string storeId,
            string sleekflowStaffId,
            List<string>? sleekflowTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowTeamIds;
        }
    }

    public class DeleteCategoryOutput
    {
    }

    public async Task<DeleteCategoryOutput> F(DeleteCategoryInput deleteCategoryInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteCategoryInput.SleekflowStaffId,
            deleteCategoryInput.SleekflowStaffTeamIds);

        await _categoryService.DeleteCategoryAsync(
            deleteCategoryInput.Id,
            deleteCategoryInput.SleekflowCompanyId,
            deleteCategoryInput.StoreId,
            sleekflowStaff);

        return new DeleteCategoryOutput();
    }
}