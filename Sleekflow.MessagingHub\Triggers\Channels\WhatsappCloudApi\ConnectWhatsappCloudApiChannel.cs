using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class ConnectWhatsappCloudApiChannel
    : ITrigger<
        ConnectWhatsappCloudApiChannel.ConnectWhatsappCloudApiChannelInput,
        ConnectWhatsappCloudApiChannel.ConnectWhatsappCloudApiChannelOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<ConnectWhatsappCloudApiChannel> _logger;

    public ConnectWhatsappCloudApiChannel(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<ConnectWhatsappCloudApiChannel> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class ConnectWhatsappCloudApiChannelInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [JsonProperty("webhook_url")]
        public string? WebhookUrl { get; set; }

        [JsonProperty("pin")]
        public string? Pin { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ConnectWhatsappCloudApiChannelInput(
            string sleekflowCompanyId,
            string wabaId,
            string wabaPhoneNumberId,
            string? webhookUrl,
            string? pin,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            WebhookUrl = webhookUrl;
            Pin = pin;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ConnectWhatsappCloudApiChannelOutput
    {
        [JsonProperty("connected_cloud_api_channel")]
        public ConnectedCloudApiChannelDto ConnectedCloudApiChannels { get; set; }

        [JsonConstructor]
        public ConnectWhatsappCloudApiChannelOutput(ConnectedCloudApiChannelDto connectedCloudApiChannels)
        {
            ConnectedCloudApiChannels = connectedCloudApiChannels;
        }
    }

    public async Task<ConnectWhatsappCloudApiChannelOutput> F(
        ConnectWhatsappCloudApiChannelInput connectWhatsappCloudApiChannelInput)
    {
        var sleekflowCompanyId = connectWhatsappCloudApiChannelInput.SleekflowCompanyId;
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            connectWhatsappCloudApiChannelInput.SleekflowStaffId,
            connectWhatsappCloudApiChannelInput.SleekflowStaffTeamIds);

        var waba = await _wabaService.GetAndRefreshWabaAsync(
            connectWhatsappCloudApiChannelInput.WabaId,
            sleekflowCompanyId,
            true,
            sleekflowStaff);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            _logger.LogWarning(
                "Unable to locate any valid waba {Waba}",
                JsonConvert.SerializeObject(waba));
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var connectedChannelWaba = await _channelService.ConnectCloudApiChannelAsync(
            waba,
            sleekflowCompanyId,
            connectWhatsappCloudApiChannelInput.WabaPhoneNumberId,
            connectWhatsappCloudApiChannelInput.Pin,
            connectWhatsappCloudApiChannelInput.WebhookUrl,
            sleekflowStaff);

        return new ConnectWhatsappCloudApiChannelOutput(new ConnectedCloudApiChannelDto(connectedChannelWaba));
    }
}