using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

#pragma warning disable SF1001
#pragma warning disable SF1004
public class OnUserEventHappenedEventHubEvent
#pragma warning restore SF1004
#pragma warning restore SF1001
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("event_type")]
    public string EventType { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("object_id")]
    public string ObjectId { get; set; }

    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [JsonProperty("source")]
    public string Source { get; set; }

    [JsonProperty("properties")]
    public OnUserEventHappenedEventHubEventProperties Properties { get; set; }

    [JsonProperty("metadata")]
    public OnUserEventHappenedEventHubEventMetadata Metadata { get; set; }

    [JsonConstructor]
    public OnUserEventHappenedEventHubEvent(
        string id,
        string eventType,
        string sleekflowCompanyId,
        string objectId,
        string objectType,
        string source,
        OnUserEventHappenedEventHubEventProperties properties,
        OnUserEventHappenedEventHubEventMetadata metadata)
    {
        Id = id;
        EventType = eventType;
        SleekflowCompanyId = sleekflowCompanyId;
        ObjectId = objectId;
        ObjectType = objectType;
        Source = source;
        Properties = properties;
        Metadata = metadata;
    }
}

public class OnUserEventHappenedEventHubEventProperties
{
    [JsonProperty("campaign_id")]
    public string? CampaignId { get; set; }

    [JsonConstructor]
    public OnUserEventHappenedEventHubEventProperties(string? campaignId)
    {
        CampaignId = campaignId;
    }
}

public class OnUserEventHappenedEventHubEventMetadata
{
    [JsonProperty("source_workflow_id")]
    public string? SourceWorkflowId { get; set; }

    [JsonProperty("source_workflow_versioned_id")]
    public string? SourceWorkflowVersionedId { get; set; }

    [JsonProperty("source_channel")]
    public string? SourceChannel { get; set; }

    [JsonProperty("source_device")]
    public string? SourceDevice { get; set; }

    [JsonProperty("source_location")]
    public string? SourceLocation { get; set; }

    [JsonProperty("source_ip_address")]
    public string? SourceIpAddress { get; set; }

    [JsonConstructor]
    public OnUserEventHappenedEventHubEventMetadata(
        string? sourceWorkflowId,
        string? sourceWorkflowVersionedId,
        string? sourceChannel,
        string? sourceDevice,
        string? sourceLocation,
        string? sourceIpAddress)
    {
        SourceWorkflowId = sourceWorkflowId;
        SourceWorkflowVersionedId = sourceWorkflowVersionedId;
        SourceChannel = sourceChannel;
        SourceDevice = sourceDevice;
        SourceLocation = sourceLocation;
        SourceIpAddress = sourceIpAddress;
    }
}