using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Communications;

public class EmailAttachment
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("email_address")]
    public string EmailAddress { get; set; }

    [JsonProperty("email_id")]
    public string EmailId { get; set; }

    [JsonProperty("file_url_to_blob")]
    public string FileUrlToBlob { get; set; }

    [JsonProperty("file_name")]
    public string FileName { get; set; }

    [JsonConstructor]
    public EmailAttachment(
        string sleekflowCompanyId,
        string emailAddress,
        string emailId,
        string fileUrlToBlob,
        string fileName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EmailAddress = emailAddress;
        EmailId = emailId;
        FileUrlToBlob = fileUrlToBlob;
        FileName = fileName;
    }
}