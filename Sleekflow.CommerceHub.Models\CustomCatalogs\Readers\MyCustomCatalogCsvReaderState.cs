using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;

public class MyCustomCatalogCsvReaderState
{
    [JsonProperty("last_byte_position")]
    public long LastBytePosition { get; }

    [JsonProperty("num_of_records")]
    public long NumOfRecords { get; }

    [JsonProperty("headers")]
    public string[]? Headers { get; }

    [JsonProperty("is_completed")]
    public bool IsCompleted { get; set; }

    [JsonConstructor]
    public MyCustomCatalogCsvReaderState(
        long lastBytePosition,
        long numOfRecords,
        string[]? headers,
        bool isCompleted)
    {
        LastBytePosition = lastBytePosition;
        NumOfRecords = numOfRecords;
        Headers = headers;
        IsCompleted = isCompleted;
    }
}