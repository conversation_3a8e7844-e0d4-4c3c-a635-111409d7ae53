﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Utils;

namespace Sleekflow.DistributedInvocations;

/***
 * Distributed invocation context are the consolidation of the type of information share between the Sleekflow and SleekflowCore
 * Utilizing the Header Param X-Sleekflow-Logger-Properties
 */
public interface IDistributedInvocationContextService
{
    public const string XSleekflowDistributedInvocationContext = "X-Sleekflow-Distributed-Invocation-Context";

    public DistributedInvocationContext? GetContext();

    public void SetIncomingContext(string headerValue);

    string GetSerializedContextHeader();

    void SetContext(string sleekflowCompanyId, string sleekflowStaffId, List<string> sleekflowStaffTeamIds);

    void SetFlowBuilderWorkflowContext(string sleekflowCompanyId, string workflowVersionedId, string stepId);
}

public class DistributedInvocationContextService : IDistributedInvocationContextService
{
    public DistributedInvocationContext? IncomingContext { get; set; }

    private DistributedInvocationContext? OutgoingContext { get; set; }

    private readonly ILogger _logger;

    public DistributedInvocationContextService(ILogger<DistributedInvocationContextService> logger)
    {
        _logger = logger;
        IncomingContext = null;
        OutgoingContext = null;
    }

    public DistributedInvocationContext? GetContext()
    {
        return IncomingContext;
    }

    public void SetIncomingContext(string headerValue)
    {
        var distributedInvocationContext = DeserializeFromHttpHeader(headerValue);
        IncomingContext = distributedInvocationContext;
        OutgoingContext = distributedInvocationContext;
    }

    public string GetSerializedContextHeader()
    {
        return SerializeAsHttpHeader(OutgoingContext);
    }

    public void SetContext(string sleekflowCompanyId, string sleekflowStaffId, List<string> sleekflowStaffTeamIds)
    {
        OutgoingContext ??= new DistributedInvocationContext(sleekflowCompanyId, sleekflowStaffId, sleekflowStaffTeamIds);
    }

    public void SetFlowBuilderWorkflowContext(string sleekflowCompanyId, string workflowVersionedId, string stepId)
    {
        OutgoingContext ??= new DistributedInvocationContext(sleekflowCompanyId);
        OutgoingContext.FlowBuilderWorkflowContext = new FlowBuilderWorkflowContext(workflowVersionedId, stepId);
    }

    private string SerializeAsHttpHeader(DistributedInvocationContext? context)
    {
        try
        {
            var contextsString = JsonConvert.SerializeObject(context);
            return Utf8Utils.Utf8EncodeBase64(contextsString);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to serialize distributed invocation contexts");
            return string.Empty;
        }
    }

    private static DistributedInvocationContext? DeserializeFromHttpHeader(string headerValue)
    {
        var decodedContexts = Utf8Utils.Utf8DecodeBase64(headerValue);
        return JsonConvert.DeserializeObject<DistributedInvocationContext>(decodedContexts);
    }
}