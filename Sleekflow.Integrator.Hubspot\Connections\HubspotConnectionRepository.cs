﻿using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Connections;

public interface IHubspotConnectionRepository : IRepository<HubspotConnection>
{
}

public class HubspotConnectionRepository
    : BaseRepository<HubspotConnection>,
        IHubspotConnectionRepository,
        ISingletonService
{
    public HubspotConnectionRepository(
        ILogger<BaseRepository<HubspotConnection>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}