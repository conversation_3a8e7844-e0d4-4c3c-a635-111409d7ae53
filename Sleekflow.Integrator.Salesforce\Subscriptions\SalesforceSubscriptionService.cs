﻿using MassTransit.Testing;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Salesforce.Subscriptions;

public interface ISalesforceSubscriptionService
{
    Task ClearAsync(string entityTypeName, string sleekflowCompanyId);

    Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId);

    Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval);

    Task UpsertAsync(
        string entityTypeName,
        string sleekflowCompanyId,
        int interval,
        string connectionId,
        bool isFlowsBased);

    Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId);

    Task<List<SalesforceSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName);
}

public class SalesforceSubscriptionService : ISalesforceSubscriptionService, ISingletonService
{
    private readonly ISalesforceSubscriptionRepository _salesforceSubscriptionRepository;
    private readonly IIdService _idService;

    public SalesforceSubscriptionService(
        ISalesforceSubscriptionRepository salesforceSubscriptionRepository,
        IIdService idService)
    {
        _salesforceSubscriptionRepository = salesforceSubscriptionRepository;
        _idService = idService;
    }

    public async Task ClearAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _salesforceSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == SalesforceSubscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _salesforceSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var subscription in _salesforceSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                                s.SysTypeName == SalesforceSubscription.SysTypeNameValue
                                && s.ConnectionId == connectionId
                                && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _salesforceSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval)
    {
        var subscription = await _salesforceSubscriptionRepository.GetObjectEnumerableAsync(
                s =>
                    s.EntityTypeName == entityTypeName
                    && s.SysTypeName == SalesforceSubscription.SysTypeNameValue
                    && s.SleekflowCompanyId == sleekflowCompanyId)
            .FirstOrDefault();
        if (subscription != null)
        {
            subscription.Interval = interval;

            await _salesforceSubscriptionRepository.ReplaceAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                subscription);

            return;
        }

        var createCount = await _salesforceSubscriptionRepository.CreateAsync(
            new SalesforceSubscription(
                _idService.GetId(SalesforceSubscription.SysTypeNameValue),
                sleekflowCompanyId,
                entityTypeName,
                interval,
                DateTimeOffset.UtcNow,
                null,
                null,
                null,
                null),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task UpsertAsync(
        string entityTypeName,
        string sleekflowCompanyId,
        int interval,
        string connectionId,
        bool isFlowsBased)
    {
        var subscription = await _salesforceSubscriptionRepository.GetObjectEnumerableAsync(
                s =>
                    s.EntityTypeName == entityTypeName
                    && s.ConnectionId == connectionId
                    && s.SysTypeName == SalesforceSubscription.SysTypeNameValue
                    && s.SleekflowCompanyId == sleekflowCompanyId)
            .FirstOrDefault();
        if (subscription != null)
        {
            subscription.Interval = interval;

            await _salesforceSubscriptionRepository.ReplaceAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                subscription);

            return;
        }

        var createCount = await _salesforceSubscriptionRepository.CreateAsync(
            new SalesforceSubscription(
                _idService.GetId(SalesforceSubscription.SysTypeNameValue),
                sleekflowCompanyId,
                entityTypeName,
                interval,
                DateTimeOffset.UtcNow,
                null,
                null,
                isFlowsBased,
                connectionId),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _salesforceSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == SalesforceSubscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _salesforceSubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{SalesforceSubscription.PropertyNameDurablePayload}",
                        new object()),
                });
        }
    }

    public async Task<List<SalesforceSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName)
    {
        return await _salesforceSubscriptionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId
                 && x.ConnectionId == connectionId
                 && x.EntityTypeName == entityTypeName);
    }
}