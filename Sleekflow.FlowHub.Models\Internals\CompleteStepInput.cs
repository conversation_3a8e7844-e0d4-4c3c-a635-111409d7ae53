using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Internals;

public class CompleteStepInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StepId { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("execution_status")]
    [System.ComponentModel.DataAnnotations.Required]
    public string ExecutionStatus { get; set; }

    [JsonConstructor]
    public CompleteStepInput(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string executionStatus)
    {
        StateId = stateId;
        StepId = stepId;
        StackEntries = stackEntries;
        ExecutionStatus = executionStatus;
    }
}