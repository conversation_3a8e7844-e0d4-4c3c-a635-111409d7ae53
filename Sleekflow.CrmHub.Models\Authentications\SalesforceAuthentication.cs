﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Authentications;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("salesforce_authentication")]
public class SalesforceAuthentication : Entity
{
    [JsonConstructor]
    public SalesforceAuthentication(
        string id,
        string sleekflowCompanyId,
        string accessToken,
        string refreshToken,
        string scope,
        string idToken,
        string instanceUrl,
        string salesforceId,
        string tokenType,
        string issuedAt,
        dynamic rawRes,
        dynamic? refreshRes,
        string? baseUrl)
        : base(id, "Authentication")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        Scope = scope;
        IdToken = idToken;
        InstanceUrl = instanceUrl;
        SalesforceId = salesforceId;
        TokenType = tokenType;
        IssuedAt = issuedAt;
        RawRes = rawRes;
        RefreshRes = refreshRes;
        BaseUrl = baseUrl;
    }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty("scope")]
    public string Scope { get; set; }

    [JsonProperty("id_token")]
    public string IdToken { get; set; }

    [JsonProperty("instance_url")]
    public string InstanceUrl { get; set; }

    [JsonProperty("salesforce_id")]
    public string SalesforceId { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("issued_at")]
    public string IssuedAt { get; set; }

    [JsonProperty("raw_res")]
    public dynamic RawRes { get; set; }

    [JsonProperty("refresh_res")]
    public dynamic? RefreshRes { get; set; }

    [JsonProperty("base_url")]
    public string? BaseUrl { get; set; }
}