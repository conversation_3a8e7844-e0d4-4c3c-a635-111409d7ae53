using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

public class SelectedUrl
{
    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("page_url")]
    public string PageUrl { get; set; }

    [JsonProperty("page_size")]
    public int PageSize { get; set; }

    [JsonConstructor]
    public SelectedUrl(string status, string pageUrl, int pageSize)
    {
        Status = status;
        PageUrl = pageUrl;
        PageSize = pageSize;
    }
}