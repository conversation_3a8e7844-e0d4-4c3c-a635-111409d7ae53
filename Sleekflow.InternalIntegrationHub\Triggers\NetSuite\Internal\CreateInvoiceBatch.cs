using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreateInvoiceBatch
    : ITrigger<CreateInvoiceBatch.CreateInvoiceBatchInput,
        CreateInvoiceBatch.CreateInvoiceBatchOutput>
{
    private readonly INetSuiteInvoiceService _netSuiteInvoiceService;

    public CreateInvoiceBatch(INetSuiteInvoiceService netSuiteInvoiceService)
    {
        _netSuiteInvoiceService = netSuiteInvoiceService;
    }

    public class CreateInvoiceBatchInput
    {
        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("is_enable_check_bill_record")]
        public bool IsEnableCheckBillRecord { get; set; }

        [Required]
        [JsonProperty("is_enable_check_whatsapp_credit")]
        public bool IsEnableCheckWhatsappCredit { get; set; }

        [JsonConstructor]
        public CreateInvoiceBatchInput(
            string companyId,
            bool? isEnableCheckBillRecord,
            bool? isEnableCheckWhatsappCredit)
        {
            CompanyId = companyId;
            IsEnableCheckWhatsappCredit = isEnableCheckBillRecord ?? false;
            IsEnableCheckWhatsappCredit = isEnableCheckWhatsappCredit ?? false;
        }
    }

    public class CreateInvoiceBatchOutput
    {
        [JsonProperty("invoice_count")]
        public int InvoiceCount { get; set; }

        [JsonConstructor]
        public CreateInvoiceBatchOutput(int invoiceCount)
        {
            InvoiceCount = invoiceCount;
        }
    }

    public async Task<CreateInvoiceBatchOutput> F(CreateInvoiceBatchInput input)
    {
        var invoiceCount = await _netSuiteInvoiceService.CreateInvoiceBatchAsync(
            input.CompanyId,
            input.IsEnableCheckBillRecord,
            input.IsEnableCheckWhatsappCredit);
        return new CreateInvoiceBatchOutput(invoiceCount);
    }
}