using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class UpsertWhatsappCloudApiChannelWebhook
    : ITrigger<
        UpsertWhatsappCloudApiChannelWebhook.UpsertWhatsappCloudApiChannelWebhookInput,
        UpsertWhatsappCloudApiChannelWebhook.UpsertWhatsappCloudApiChannelWebhookOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<UpsertWhatsappCloudApiChannelWebhook> _logger;

    public UpsertWhatsappCloudApiChannelWebhook(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<UpsertWhatsappCloudApiChannelWebhook> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class UpsertWhatsappCloudApiChannelWebhookInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("webhook_url")]
        public string WebhookUrl { get; set; }

        [JsonConstructor]
        public UpsertWhatsappCloudApiChannelWebhookInput(
            string sleekflowCompanyId,
            string wabaId,
            string wabaPhoneNumberId,
            string webhookUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            WebhookUrl = webhookUrl;
        }
    }

    public class UpsertWhatsappCloudApiChannelWebhookOutput
    {
        [JsonProperty("connected_cloud_api_channel")]
        public ConnectedCloudApiChannelDto ConnectedCloudApiChannels { get; set; }

        [JsonConstructor]
        public UpsertWhatsappCloudApiChannelWebhookOutput(ConnectedCloudApiChannelDto connectedCloudApiChannels)
        {
            ConnectedCloudApiChannels = connectedCloudApiChannels;
        }
    }

    public async Task<UpsertWhatsappCloudApiChannelWebhookOutput> F(
        UpsertWhatsappCloudApiChannelWebhookInput upsertWhatsappCloudApiChannelWebhookInput)
    {
        var sleekflowCompanyId = upsertWhatsappCloudApiChannelWebhookInput.SleekflowCompanyId;

        var waba = await _wabaService.GetAndRefreshWabaAsync(
            upsertWhatsappCloudApiChannelWebhookInput.WabaId,
            sleekflowCompanyId,
            true,
            null);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var connectedChannelWaba = await _channelService.UpsertWhatsappCloudApiChannelWebhook(
            waba,
            sleekflowCompanyId,
            upsertWhatsappCloudApiChannelWebhookInput.WabaPhoneNumberId,
            upsertWhatsappCloudApiChannelWebhookInput.WebhookUrl);
        return new UpsertWhatsappCloudApiChannelWebhookOutput(new ConnectedCloudApiChannelDto(connectedChannelWaba));
    }
}