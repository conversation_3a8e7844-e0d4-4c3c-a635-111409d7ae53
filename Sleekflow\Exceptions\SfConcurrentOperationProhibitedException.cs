﻿namespace Sleekflow.Exceptions;

public class SfConcurrentOperationProhibitedException : ErrorCodeException
{
    public SfConcurrentOperationProhibitedException(
        TimeSpan? retryAfter = null)
        : base(
            ErrorCodeConstant.SfConcurrentOperationProhibitedException,
            "Concurrent operation is prohibited.",
            new Dictionary<string, object?>
            {
                { "is_retryable", retryAfter.HasValue },
                { "retry_after_seconds", retryAfter.HasValue ? Convert.ToInt64(Math.Ceiling(retryAfter.Value.TotalSeconds)) : null }
            })
    {
    }
}