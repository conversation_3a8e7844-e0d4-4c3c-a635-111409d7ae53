using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Webhooks;

namespace Sleekflow.MessagingHub.Triggers.Webhook.WhatsappCloudApi;

public class GetWhatsappCloudApiWebhook : ITrigger
{
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;

    public GetWhatsappCloudApiWebhook(IMessagingHubWebhookService messagingHubWebhookService)
    {
        _messagingHubWebhookService = messagingHubWebhookService;
    }

    public bool F(string token)
    {
        return _messagingHubWebhookService.VerifyFacebookWebhookToken(token);
    }
}