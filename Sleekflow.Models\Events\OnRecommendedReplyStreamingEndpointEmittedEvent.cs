using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

public class OnRecommendedReplyStreamingEndpointEmittedEvent
{
    [JsonProperty("correlation_id")]
    public Guid CorrelationId { get; set; }

    [JsonProperty("session_id")]
    public string SessionId { get; set; }

    [JsonProperty("stream_sequence_number")]
    public int StreamSequenceNumber { get; set; }

    [JsonProperty("partial_recommended_reply")]
    public string PartialRecommendedReply { get; set; }

    [JsonProperty("client_request_id")]
    public string ClientRequestId { get; set; }

    [JsonConstructor]
    public OnRecommendedReplyStreamingEndpointEmittedEvent(
        Guid correlationId,
        string sessionId,
        int streamSequenceNumber,
        string partialRecommendedReply,
        string clientRequestId)
    {
        CorrelationId = correlationId;
        SessionId = sessionId;
        StreamSequenceNumber = streamSequenceNumber;
        PartialRecommendedReply = partialRecommendedReply;
        ClientRequestId = clientRequestId;
    }
}