﻿using System.Text.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

namespace Sleekflow.IntelligentHub.WebScrapers;

public interface IWebScraperWebhookService
{
    public Task HandleApifyWebhookAsync(ApifyWebhook apifyWebhook);
}

public class WebScraperWebhookService : IWebScraperWebhookService, IScopedService
{
    private readonly IWebScraperService _webScraperService;
    private readonly ILogger<WebScraperWebhookService> _logger;

    public WebScraperWebhookService(
        IWebScraperService webScraperService,
        ILogger<WebScraperWebhookService> logger)
    {
        _webScraperService = webScraperService;
        _logger = logger;
    }

    public async Task HandleApifyWebhookAsync(ApifyWebhook apifyWebhook)
    {
        _logger.LogInformation(
            "Start handling Apify Webhook. Event Type: {EventType}, apifyRunId: {ApifyRunId}, {WebhookBody}",
            apifyWebhook.EventType,
            apifyWebhook.Data.ActorRunId,
            JsonSerializer.Serialize(apifyWebhook));

        try
        {
            // Using switch in case need to add retry logic for different event types.
            switch (apifyWebhook.EventType)
            {
                case ApifyRunEventTypes.Created:
                    break;
                case ApifyRunEventTypes.Succeeded:
                    // Retrieve data from Apify Dataset and store.
                    await _webScraperService.UpdateRunStatusAsync(apifyWebhook);
                    await _webScraperService.StoreWebPageContentAsync(apifyWebhook.Data.ActorRunId);
                    break;
                case ApifyRunEventTypes.Failed:
                case ApifyRunEventTypes.Aborted:
                case ApifyRunEventTypes.TimedOut:
                case ApifyRunEventTypes.Resurrected:
                    await _webScraperService.UpdateRunStatusAsync(apifyWebhook);
                    break;
                default:
                    throw new ArgumentException($"Unknown Apify Run Event Type: {apifyWebhook.EventType ?? "NULL"}");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Error occurred when handling Apify Webhook. {Error} {ApifyRunId} {WebhookBody}",
                e.Message,
                apifyWebhook.Data.ActorRunId,
                JsonSerializer.Serialize(apifyWebhook));
            throw;
        }
    }
}