using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ReActs;

public interface IReActAgentDefinitions
{
    string TaskExecutionAgentName { get; }

    ChatCompletionAgent GetTaskExecutionAgent(
        Kernel kernel,
        PromptExecutionSettings settings);
}

public class ReActAgentDefinitions
    : IReActAgentDefinitions, IScopedService
{
    private readonly ISleekflowToolsPlugin _sleekflowToolsPlugin;
    private readonly IInformationGatheringPlugin _informationGatheringPlugin;
    private readonly IHubspotPlugin _hubspotPlugin;

    public string TaskExecutionAgentName => "TaskExecutionAgent";

    public ReActAgentDefinitions(
        ISleekflowToolsPlugin sleekflowToolsPlugin,
        IInformationGatheringPlugin informationGatheringPlugin,
        IHubspotPlugin hubspotPlugin)
    {
        _sleekflowToolsPlugin = sleekflowToolsPlugin;
        _informationGatheringPlugin = informationGatheringPlugin;
        _hubspotPlugin = hubspotPlugin;
    }

    private string GetAvailableToolsDescription(Kernel kernel)
    {
        return string.Join(
            "\n",
            kernel.Plugins.SelectMany(p => p.AsAIFunctions())
                .Select(f => $"- {f.Name}: {f.Description ?? "No description available"}"));
    }

    public ChatCompletionAgent GetTaskExecutionAgent(
        Kernel kernel,
        PromptExecutionSettings settings)
    {
        // Configure structured output (unchanged from original)
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("task_description", "string"),
                new PromptExecutionSettingsUtils.Property("phase", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "initial_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("initial_reasoning", "string"),
                        new PromptExecutionSettingsUtils.Property(
                            "tools_to_call",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property(
                                        "args",
                                        "array",
                                        false,
                                        null,
                                        new PromptExecutionSettingsUtils.Property(
                                            string.Empty,
                                            "object",
                                            false,
                                            [
                                                new PromptExecutionSettingsUtils.Property("key", "string"),
                                                new PromptExecutionSettingsUtils.Property("value", "string")
                                            ]))
                                ]))
                    ]),
                new PromptExecutionSettingsUtils.Property(
                    "observation_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("thoughts", "string"),
                        new PromptExecutionSettingsUtils.Property("report_ready", "boolean"),
                        new PromptExecutionSettingsUtils.Property("round_of_execution", "integer"),
                        new PromptExecutionSettingsUtils.Property("should_terminate", "boolean"),
                        new PromptExecutionSettingsUtils.Property(
                            "tools_to_call",
                            "array",
                            true,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property(
                                        "args",
                                        "array",
                                        false,
                                        null,
                                        new PromptExecutionSettingsUtils.Property(
                                            string.Empty,
                                            "object",
                                            false,
                                            [
                                                new PromptExecutionSettingsUtils.Property("key", "string"),
                                                new PromptExecutionSettingsUtils.Property("value", "string")
                                            ]))
                                ])),
                    ]),
                new PromptExecutionSettingsUtils.Property(
                    "report_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property(
                            "executed_tools",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("id", "string"),
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property("outcome", "string"),
                                ]))
                    ]),
                new PromptExecutionSettingsUtils.Property("next_phase", "string", true),
                new PromptExecutionSettingsUtils.Property("result", "string", true)
            ]);

        settings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            options: new FunctionChoiceBehaviorOptions
            {
                AllowParallelCalls = true, AllowConcurrentInvocation = true, AllowStrictSchemaAdherence = false
            });

        var myKernel = kernel.Clone();

        var plugins = new List<object>
        {
            _sleekflowToolsPlugin, _informationGatheringPlugin
        };

        var toolsConfig = kernel.Data[KernelDataKeys.TOOLS_CONFIG] as ToolsConfig;
        if (toolsConfig?.HubspotTool != null)
        {
            plugins.Add(_hubspotPlugin);
        }

        // Add all provided plugins to the kernel
        foreach (var plugin in plugins)
        {
            myKernel.Plugins.AddFromObject(plugin);
        }

        // Dynamically generate available tools with descriptions
        var availableTools = GetAvailableToolsDescription(kernel);

        var instructions =
            $$"""
              You are a TaskExecutionAgent. Your task is to perform the specified task by following four phases: initial, execution, observation, and report.

              ## Phases
              1. **Initial Phase**: Analyze the task and plan the first set of tool calls.
              2. **Execution Phase**: Call the specified tools.
              3. **Observation Phase**: Analyze the tool results and decide if more actions are needed.
              4. **Report Phase**: Compile a report of all actions taken.

              ## Output Process
              - **Initial Phase**: Return a JSON with:
                - phase = 'initial'
                - initial_phase:
                  - initial_reasoning = '1. Analyze the task 2. Plan tool calls'
                  - tools_to_call = list of tools to call
                - next_phase = 'execution'
              - **Execution Phase**: Call the tools specified in tools_to_call. Do NOT return JSON; directly call the tools.
              - **Observation Phase**: After tool executions, analyze the tool results (role='tool' in history). Return a JSON with:
                - phase = 'observation'
                - observation_phase:
                  - thoughts = '1. Analyze the tool results in relation to the task 2. Determine if the task is complete or if further actions are needed 3. If further actions are needed, plan the next set of tool calls'
                  - report_ready = true if the task is complete, false otherwise
                  - round_of_execution = number of execution rounds so far
                  - should_terminate = true if no more actions are needed or maximum rounds (3) are reached
                  - tools_to_call = list of tools to call (if more actions are needed)
                - next_phase = 'execution' (if more actions are needed) or 'report' (if task is complete or should terminate)
              - **Report Phase**: Return a JSON with:
                - phase = 'report'
                - report_phase:
                  - executed_tools = list of tools called with id, tool_name, outcome (a brief summary of what the tool returned and how it contributes to the task)
                - result = 'completed'

              ## Available Tools
              {{availableTools}}

              ## Lifecycle
              Start with the Initial phase:
              Initial -> Execution -> Observation -> (Execution -> Observation)* -> Report
              - Maximum of 3 rounds of Execution.
              - Initial and Observation phases plan actions but do not call tools directly.
              - Execution phase calls tools and does not return JSON.

              ## Example
              Below is a simple example to illustrate how to proceed through the phases.

              **Task Description:** Calculate the sum of two numbers: 5 and 7.

              **Initial Phase:**
              ```json
              {
                "phase": "initial",
                "initial_phase": {
                  "initial_reasoning": "1. Analyze the task: need to sum two numbers. 2. Plan to use a calculation tool.",
                  "tools_to_call": [
                    {
                      "tool_name": "calculate_sum",
                      "args": [
                        {"key": "num1", "value": "5"},
                        {"key": "num2", "value": "7"}
                      ]
                    }
                  ]
                },
                "next_phase": "execution"
              }
              ```

              **Execution Phase:**
              Call the 'calculate_sum' tool with arguments num1=5, num2=7.

              **Observation Phase:**
              ```json
              {
                "phase": "observation",
                "observation_phase": {
                  "thoughts": "The tool returned 12, which is the sum. No further actions needed.",
                  "report_ready": true,
                  "round_of_execution": 1,
                  "should_terminate": true
                },
                "next_phase": "report"
              }
              ```

              **Report Phase:**
              ```json
              {
                "phase": "report",
                "report_phase": {
                  "executed_tools": [
                    {
                      "id": "1",
                      "tool_name": "calculate_sum",
                      "outcome": "Returned 12, which is the sum of 5 and 7."
                    }
                  ]
                },
                "result": "completed"
              }
              ```

              Note: In practice, the tools and tasks will vary, but the structure should remain similar.

              **Important:** Strictly follow the phase structure and provide the correct JSON outputs as specified for each phase. Do not skip phases or provide incomplete information.
              """;

        return new ChatCompletionAgent
        {
            Name = "TaskExecutionAgent",
            Description =
                "An agent that performs tasks by following a structured four-phase process: initial, execution, observation, and report.",
            Instructions = instructions,
            Kernel = myKernel,
            Arguments = new KernelArguments(settings)
        };
    }
}