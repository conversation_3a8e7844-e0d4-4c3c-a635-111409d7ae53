using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Salesforce;
using Sleekflow.Integrator.Salesforce.ApexTriggerAuthentications;
using Sleekflow.Integrator.Salesforce.ProviderConfigs;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Public;

public class ApexCallback : ITrigger
{
    private readonly IApexTriggerAuthenticationService _apexTriggerAuthenticationService;
    private readonly IBus _bus;
    private readonly ILogger<ApexCallback> _logger;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly IProviderConfigService _providerConfigService;

    public ApexCallback(
        IApexTriggerAuthenticationService apexTriggerAuthenticationService,
        IBus bus,
        ILogger<ApexCallback> logger,
        ISalesforceObjectService salesforceObjectService,
        IProviderConfigService providerConfigService)
    {
        _apexTriggerAuthenticationService = apexTriggerAuthenticationService;
        _bus = bus;
        _logger = logger;
        _salesforceObjectService = salesforceObjectService;
        _providerConfigService = providerConfigService;
    }

    public class ApexCallbackInput
    {
        [JsonProperty("new")]
        [Required]
        public List<Dictionary<string, object?>> New { get; set; }

        [JsonProperty("old")]
        [Required]
        public List<Dictionary<string, object?>> Old { get; set; }

        [JsonProperty("user_id")]
        [Required]
        public string? ApexTriggeringUserId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string? SleekflowCompanyId { get; set; }

        [JsonProperty("type")]
        [Required]
        public string? SObjectTypeName { get; set; }

        [JsonProperty("key")]
        [Required]
        public string? Key { get; set; }

        [JsonConstructor]
        public ApexCallbackInput(
            List<Dictionary<string, object?>> @new,
            List<Dictionary<string, object?>> old,
            string? apexTriggeringUserId,
            string? sleekflowCompanyId,
            string? sObjectTypeName,
            string? key)
        {
            New = @new;
            Old = old;
            ApexTriggeringUserId = apexTriggeringUserId;
            SleekflowCompanyId = sleekflowCompanyId;
            SObjectTypeName = sObjectTypeName;
            Key = key;
        }
    }

    public class ApexCallbackOutput
    {
    }

    public async Task<ApexCallbackOutput> F(
        ApexCallbackInput apexCallbackInput)
    {
        _logger.LogInformation(
            "Received the UserId {UserId}, SleekflowCompanyId {SleekflowCompanyId}, Type {Type}, Key {Key}",
            apexCallbackInput.ApexTriggeringUserId,
            apexCallbackInput.SleekflowCompanyId,
            apexCallbackInput.SObjectTypeName,
            apexCallbackInput.Key);

        if (apexCallbackInput.SleekflowCompanyId == null)
        {
            throw new SfUserFriendlyException("SleekflowCompanyId must be present");
        }

        if (apexCallbackInput.Key == null)
        {
            throw new SfUserFriendlyException("Key must be present");
        }

        if (apexCallbackInput.SObjectTypeName == null)
        {
            throw new SfUserFriendlyException("SObjectTypeName must be present");
        }

        var ids = new HashSet<string>();
        var idToNew = apexCallbackInput.New.ToDictionary(n => _salesforceObjectService.ResolveObjectId(n), n => n);
        var idToOld = apexCallbackInput.Old.ToDictionary(n => _salesforceObjectService.ResolveObjectId(n), n => n);

        var apexTriggerAuthentication = await _apexTriggerAuthenticationService.GetAsync(
            apexCallbackInput.SleekflowCompanyId,
            apexCallbackInput.SObjectTypeName,
            apexCallbackInput.Key);
        if (apexTriggerAuthentication == null)
        {
            throw new SfUserFriendlyException("Key is unauthorized");
        }

        var providerConfig =
            await _providerConfigService.GetProviderConfigAsync(
                apexCallbackInput.SleekflowCompanyId,
                "salesforce-integrator");
        if (providerConfig.EntityTypeNameToSyncConfigDict.ContainsKey(apexCallbackInput.SObjectTypeName) == false)
        {
            return new ApexCallbackOutput();
        }

        var syncConfig = providerConfig.EntityTypeNameToSyncConfigDict[apexCallbackInput.SObjectTypeName];

        foreach (var id in idToNew.Keys)
        {
            ids.Add(id);
        }

        foreach (var id in idToOld.Keys)
        {
            ids.Add(id);
        }

        var events = new List<OnObjectOperationEvent>();

        foreach (var id in ids)
        {
            var @new = idToNew.GetValueOrDefault(id);
            var old = idToOld.GetValueOrDefault(id);

            var operation = DetermineOperation(@new, old);

            switch (operation)
            {
                case Operation.Create:
                {
                    var isNotMatched = syncConfig.FilterGroups.All(
                        fg => fg.Filters.Any(
                            f =>
                            {
                                var value = @new![f.FieldName];
                                var filterValue = f.Value;

                                return value == null || value.ToString() != filterValue.Replace("\"", string.Empty);
                            }));
                    if (isNotMatched)
                    {
                        continue;
                    }

                    var onObjectOperationEvent = new OnObjectOperationEvent(
                        @new!,
                        OnObjectOperationEvent.OperationCreateObject,
                        "salesforce-integrator",
                        apexCallbackInput.SleekflowCompanyId,
                        id,
                        apexCallbackInput.SObjectTypeName!,
                        null);

                    events.Add(onObjectOperationEvent);

                    _logger.LogInformation(
                        "Publishing Operation.Create the id {Id}, sleekflowCompanyId {SleekflowCompanyId}",
                        id,
                        apexCallbackInput.SleekflowCompanyId);

                    break;
                }

                case Operation.Update:
                {
                    var isNotMatched = syncConfig.FilterGroups.All(
                        fg => fg.Filters.Any(
                            f =>
                            {
                                var value = @new![f.FieldName];
                                var filterValue = f.Value;

                                return value == null || value.ToString() != filterValue.Replace("\"", string.Empty);
                            }));
                    if (isNotMatched)
                    {
                        continue;
                    }

                    var onObjectOperationEvent = new OnObjectOperationEvent(
                        @new!,
                        OnObjectOperationEvent.OperationUpdateObject,
                        "salesforce-integrator",
                        apexCallbackInput.SleekflowCompanyId,
                        id,
                        apexCallbackInput.SObjectTypeName!,
                        null);

                    events.Add(onObjectOperationEvent);

                    _logger.LogInformation(
                        "Publishing Operation.Update the id {Id}, sleekflowCompanyId {SleekflowCompanyId}",
                        id,
                        apexCallbackInput.SleekflowCompanyId);

                    break;
                }

                case Operation.Delete:
                {
                    var onObjectOperationEvent = new OnObjectOperationEvent(
                        old!,
                        OnObjectOperationEvent.OperationDeleteObject,
                        "salesforce-integrator",
                        apexCallbackInput.SleekflowCompanyId,
                        id,
                        apexCallbackInput.SObjectTypeName!,
                        null);

                    events.Add(onObjectOperationEvent);

                    _logger.LogInformation(
                        "Publishing Operation.Delete the id {Id}, sleekflowCompanyId {SleekflowCompanyId}",
                        id,
                        apexCallbackInput.SleekflowCompanyId);

                    break;
                }

                default:
                    throw new ArgumentOutOfRangeException(nameof(operation));
            }
        }

        await _bus.PublishBatch(
            events,
            context => { context.ConversationId = Guid.Parse(apexCallbackInput.SleekflowCompanyId); });

        return new ApexCallbackOutput();
    }

    private static Operation DetermineOperation(Dictionary<string, object?>? @new, Dictionary<string, object?>? old)
    {
        if (@new != null && old == null)
        {
            return Operation.Create;
        }
        else if (@new == null && old != null)
        {
            return Operation.Delete;
        }
        else if (@new != null && old != null)
        {
            return Operation.Update;
        }

        throw new SfUnhandledApexOperationException();
    }

    private enum Operation
    {
        Create,
        Update,
        Delete
    }
}