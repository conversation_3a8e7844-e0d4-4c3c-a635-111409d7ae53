using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.IntelligentHub.Consumers;
using Sleekflow.IntelligentHub.Events;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Triggers.RecommendedReplies;
using Sleekflow.Models.Chats;
using Sleekflow.Persistence;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class RecommendReplyStreamingCoreIntegrationTest
{
    private const string MockCompanyId = "sample-company-id";
    private readonly IntelligentHubUsageFilter _intelligentHubUsageFilter;
    private readonly AuditEntity.SleekflowStaff _sleekflowStaff;

    public RecommendReplyStreamingCoreIntegrationTest()
    {
        var utcNow = DateTimeOffset.UtcNow;
        _intelligentHubUsageFilter = new IntelligentHubUsageFilter(
            new DateTimeOffset(utcNow.Year, utcNow.Month, 1, 0, 0, 0, TimeSpan.Zero),
            new DateTimeOffset(
                utcNow.Year,
                utcNow.Month,
                DateTime.DaysInMonth(utcNow.Year, utcNow.Month),
                23,
                59,
                59,
                TimeSpan.Zero));
        _sleekflowStaff = new AuditEntity.SleekflowStaff(
            "3880",
            new List<string>
            {
                "233", "282"
            });
    }

    [SetUp]
    public void Init()
    {
        var provider = Application.InMemoryBusHost.Server.Services;
        var connectionMultiplexer = provider.GetRequiredService<IConnectionMultiplexer>();
        var database = connectionMultiplexer.GetDatabase();
        database.KeyDelete($"{nameof(IntelligentHubConcurrentUsageService)}-{MockCompanyId}");
    }

    [Test]
    public async Task RecommendReplyStreamingTest()
    {
        var sfChatTest1Entry1 = new SfChatEntry()
        {
            User = "Hello, what does your company do?", Bot = "Hi, we are hong kong sue yan university",
        };
        var sfChatTest1Entry2 = new SfChatEntry()
        {
            User = "Ok, can you provide the course lists that you offer?",
        };

        var recommendReplyStreamingInput1 =
            new RecommendReplyStreaming.RecommendReplyStreamingInput(
                new List<SfChatEntry>()
                {
                    sfChatTest1Entry1, sfChatTest1Entry2,
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff,
                "session-id",
                "client_request_id");

        var recommendReplyScenarioResult1 = await Application.InMemoryBusHost.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(recommendReplyStreamingInput1).ToUrl("/RecommendedReplies/RecommendReplyStreaming");
            });

        Assert.That(recommendReplyScenarioResult1, Is.Not.Null);
        Assert.That(recommendReplyScenarioResult1.Context.Response.StatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task RecommendReplyStreamingConcurrentUsageCountTest()
    {
        var sfChatTest1Entry1 = new SfChatEntry()
        {
            User = "Hello, what does your company do?", Bot = "Hi, we are hong kong sue yan university",
        };
        var sfChatTest1Entry2 = new SfChatEntry()
        {
            User = "Ok, can you provide the course lists that you offer?",
        };

        var recommendReplyStreamingInput1 =
            new RecommendReplyStreaming.RecommendReplyStreamingInput(
                new List<SfChatEntry>()
                {
                    sfChatTest1Entry1, sfChatTest1Entry2,
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff,
                "session-id",
                "client_request_id");

        await Application.InMemoryBusHost.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(recommendReplyStreamingInput1).ToUrl("/RecommendedReplies/RecommendReplyStreaming");
            });

        Assert.That(await GetConcurrentUsageCount(), Is.EqualTo(1));

        var provider = Application.InMemoryBusHost.Server.Services;
        var harness = provider.GetRequiredService<ITestHarness>();
        Assert.IsTrue(await harness.Published.Any<RecommendReplyStreamingEndpointEvent>());
        Assert.IsTrue(await harness.Consumed.Any<RecommendReplyStreamingEndpointEvent>());

        var consumerHarness = harness.GetConsumerHarness<RecommendReplyStreamingEndpointEventConsumer>();
        Assert.IsTrue(await consumerHarness.Consumed.Any<RecommendReplyStreamingEndpointEvent>());

        // consumer finished so concurrent count should be decremented
        Assert.That(await GetConcurrentUsageCount(), Is.EqualTo(0));
    }

    [Test]
    public async Task RecommendReplyStreamingConcurrentUsageCountExceededTest()
    {
        var sfChatTest1Entry1 = new SfChatEntry()
        {
            User = "Hello, what does your company do?", Bot = "Hi, we are hong kong sue yan university",
        };
        var sfChatTest1Entry2 = new SfChatEntry()
        {
            User = "Ok, can you provide the course lists that you offer?",
        };

        var recommendReplyStreamingInput1 =
            new RecommendReplyStreaming.RecommendReplyStreamingInput(
                new List<SfChatEntry>()
                {
                    sfChatTest1Entry1, sfChatTest1Entry2,
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff,
                "session-id",
                "client_request_id");

        var tasks = new List<Task>();
        for (var i = 0; i < IntelligentHubConcurrentUsageService.MAX_CONCURRENT_USAGE_LIMIT + 1; ++i)
        {
            tasks.Add(Application.InMemoryBusHost.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(recommendReplyStreamingInput1).ToUrl("/RecommendedReplies/RecommendReplyStreaming");
                }));
        }

        await Task.WhenAll(tasks);

        Assert.That(await GetConcurrentUsageCount(), Is.EqualTo(IntelligentHubConcurrentUsageService.MAX_CONCURRENT_USAGE_LIMIT));
    }

    private static async Task<int> GetConcurrentUsageCount()
    {
        var provider = Application.InMemoryBusHost.Server.Services;
        var connectionMultiplexer = provider.GetRequiredService<IConnectionMultiplexer>();
        var database = connectionMultiplexer.GetDatabase();
        var concurrentUsageCount = await database.StringGetAsync($"{nameof(IntelligentHubConcurrentUsageService)}-{MockCompanyId}");
        concurrentUsageCount.TryParse(out int value);
        return value;
    }
}