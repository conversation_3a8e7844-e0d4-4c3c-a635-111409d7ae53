using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.WebCrawlingSessions;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetWebCrawlingSessionStatus
    : ITrigger<GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusInput,
        GetWebCrawlingSessionStatus.GetWebCrawlingSessionStatusOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;

    public GetWebCrawlingSessionStatus(
        ISleekflowAuthorizationContext authorizationContext,
        IWebCrawlingSessionService webCrawlingSessionService)
    {
        _authorizationContext = authorizationContext;
        _webCrawlingSessionService = webCrawlingSessionService;
    }

    public class GetWebCrawlingSessionStatusInput
    {
        [JsonProperty("web_crawling_session_id")]
        [Required]
        public string WebCrawlingSessionId { get; set; }

        [JsonConstructor]
        public GetWebCrawlingSessionStatusInput(string webCrawlingSessionId)
        {
            WebCrawlingSessionId = webCrawlingSessionId;
        }
    }

    public class GetWebCrawlingSessionStatusOutput
    {
        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("base_url")]
        public string BaseUrl { get; set; }

        [JsonProperty("crawling_results")]
        public List<CrawlingResult> CrawlingResults { get; set; }

        [JsonConstructor]
        public GetWebCrawlingSessionStatusOutput(string status, string baseUrl, List<CrawlingResult> crawlingResults)
        {
            Status = status;
            BaseUrl = baseUrl;
            CrawlingResults = crawlingResults;
        }
    }

    public async Task<GetWebCrawlingSessionStatusOutput> F(
        GetWebCrawlingSessionStatusInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        var session = await _webCrawlingSessionService.GetWebCrawlingSessionAsync(
            sleekflowCompanyId,
            input.WebCrawlingSessionId);

        return new GetWebCrawlingSessionStatusOutput(
            session.Status,
            session.BaseUrl,
            session.CrawlingResults);
    }
}