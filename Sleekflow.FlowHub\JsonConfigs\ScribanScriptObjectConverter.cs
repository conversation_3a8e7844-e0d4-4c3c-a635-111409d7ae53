using Newtonsoft.Json;
using Scriban.Runtime;

namespace Sleekflow.FlowHub.JsonConfigs;

public class ScribanScriptObjectConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(ScriptObject);
    }

    public override object ReadJson(
        JsonReader reader,
        Type objectType,
        object? existingValue,
        JsonSerializer serializer)
    {
        // Deserialization is not supported in this implementation.
        throw new NotImplementedException();
    }

    public override void Write<PERSON><PERSON>(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        if (value is not ScriptObject scriptObject)
        {
            return;
        }

        writer.WriteStartObject();

        foreach (var member in scriptObject.GetMembers())
        {
            writer.WritePropertyName(member);
            var propertyValue = scriptObject[member];
            serializer.Serialize(writer, propertyValue);
        }

        writer.WriteEndObject();
    }
}