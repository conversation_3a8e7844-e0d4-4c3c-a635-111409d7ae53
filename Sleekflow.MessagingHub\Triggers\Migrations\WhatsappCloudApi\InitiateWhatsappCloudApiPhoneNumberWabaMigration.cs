using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class InitiateWhatsappCloudApiPhoneNumberWabaMigration
    : ITrigger<
        InitiateWhatsappCloudApiPhoneNumberWabaMigration.InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput,
        InitiateWhatsappCloudApiPhoneNumberWabaMigration.InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput>
{
    private readonly IMigrationService _migrationService;
    private readonly IWabaService _wabaService;

    public InitiateWhatsappCloudApiPhoneNumberWabaMigration(IMigrationService migrationService, IWabaService wabaService)
    {
        _migrationService = migrationService;
        _wabaService = wabaService;
    }

    public class InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("country_code")]
        public string CountryCode { get; set; }

        [Required]
        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; }

        [JsonConstructor]
        public InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string countryCode,
            string phoneNumber)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            CountryCode = countryCode;
            PhoneNumber = phoneNumber;
        }
    }

    public class InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput> F(
        InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput initiateWhatsappCloudApiPhoneNumberWabaMigrationInput)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(
            initiateWhatsappCloudApiPhoneNumberWabaMigrationInput.FacebookWabaId);

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput(
            await _migrationService.InitiateWhatsappCloudApiPhoneNumberWabaMigrationAsync(
                initiateWhatsappCloudApiPhoneNumberWabaMigrationInput.SleekflowCompanyId,
                initiateWhatsappCloudApiPhoneNumberWabaMigrationInput.FacebookWabaId,
                initiateWhatsappCloudApiPhoneNumberWabaMigrationInput.CountryCode,
                initiateWhatsappCloudApiPhoneNumberWabaMigrationInput.PhoneNumber,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}