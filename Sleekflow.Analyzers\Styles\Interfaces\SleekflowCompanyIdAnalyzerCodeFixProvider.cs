using System.Collections.Immutable;
using System.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CodeActions;
using Microsoft.CodeAnalysis.CodeFixes;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Editing;

namespace Sleekflow.Analyzers.Styles;

[ExportCodeFixProvider(LanguageNames.CSharp, Name = nameof(SleekflowCompanyIdAnalyzerCodeFixProvider))]
[Shared]
public class SleekflowCompanyIdAnalyzerCodeFixProvider : CodeFixProvider
{
    private const string Title = "Implement IHasSleekflowCompanyId interface";

    public sealed override ImmutableArray<string> FixableDiagnosticIds
    {
        get { return ImmutableArray.Create(SleekflowCompanyIdAnalyzer.DiagnosticId); }
    }

    public sealed override FixAllProvider GetFixAllProvider()
    {
        return WellKnownFixAllProviders.BatchFixer;
    }

    public sealed override async Task RegisterCodeFixesAsync(CodeFixContext context)
    {
        var root = await context.Document.GetSyntaxRootAsync(context.CancellationToken).ConfigureAwait(false);

        // Find the first diagnostic with a fixable location
        var diagnostic = context.Diagnostics.First();
        var diagnosticSpan = diagnostic.Location.SourceSpan;

        var declaration = root!.FindToken(diagnosticSpan.Start).Parent!.AncestorsAndSelf()
            .OfType<TypeDeclarationSyntax>()
            .First();

        context.RegisterCodeFix(
            CodeAction.Create(
                title: Title,
                createChangedDocument: c => ImplementIHasSleekflowCompanyIdAsync(context.Document, declaration, c),
                equivalenceKey: Title),
            diagnostic);
    }

    private async Task<Document> ImplementIHasSleekflowCompanyIdAsync(
        Document document,
        TypeDeclarationSyntax typeDeclaration,
        CancellationToken cancellationToken)
    {
        var editor = await DocumentEditor.CreateAsync(document, cancellationToken).ConfigureAwait(false);
        var generator = editor.Generator;

        var root = await document.GetSyntaxRootAsync(cancellationToken).ConfigureAwait(false);
        var compilationUnit = root as CompilationUnitSyntax;

        if (compilationUnit != null)
        {
            // Create the using directive for Sleekflow.Persistence.Abstractions
            var usingDirective = SyntaxFactory.UsingDirective(
                SyntaxFactory.QualifiedName(
                    SyntaxFactory.IdentifierName("Sleekflow"),
                    SyntaxFactory.IdentifierName("Persistence.Abstractions")));

            // Check if the using directive already exists
            bool usingExists = compilationUnit.Usings.Any(u => u.Name.ToString() == usingDirective.Name.ToString());

            // If the using directive does not exist, add it
            if (!usingExists)
            {
                if (compilationUnit.Usings.Any())
                {
                    editor.InsertBefore(compilationUnit.Usings.First(), usingDirective);
                }
                else
                {
                    editor.InsertAfter(compilationUnit.Externs.Last(), usingDirective);
                }
            }
        }

        // Add the IHasSleekflowCompanyId interface to the class
        var iHasSleekflowCompanyIdInterface = generator.IdentifierName("Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId");
        editor.AddInterfaceType(typeDeclaration, iHasSleekflowCompanyIdInterface);

        return editor.GetChangedDocument();
    }
}