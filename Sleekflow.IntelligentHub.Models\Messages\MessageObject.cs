using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Messages;

public class MessageObject
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("message_body")]
    public MessageBodyObject MessageBody { get; set; }

    [JsonConstructor]
    public MessageObject(string id, MessageBodyObject messageBody)
    {
        Id = id;
        MessageBody = messageBody;
    }
}

public class MessageBodyObject
{
    [JsonProperty("text_message")]
    public TextMessageObject TextMessage { get; set; }

    [JsonConstructor]
    public MessageBodyObject(TextMessageObject textMessage)
    {
        TextMessage = textMessage;
    }
}

public class TextMessageObject
{
    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonConstructor]
    public TextMessageObject(string text)
    {
        Text = text;
    }
}