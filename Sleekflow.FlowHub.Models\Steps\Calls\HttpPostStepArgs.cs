using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class HttpPostStepArgs : TypedCallStepArgs
{
    public const string CallName = "http.post";

    [Required]
    [JsonProperty("url__expr")]
    public string UrlExpr { get; set; }

    [JsonProperty("headers__key_expr_dict")]
    public Dictionary<string, string?>? HeadersKeyExprDict { get; set; }

    [JsonProperty("body__key_expr_dict")]
    public Dictionary<string, string?>? BodyKeyExprDict { get; set; }

    [JsonProperty("body__expr")]
    public string? BodyExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.ExternalIntegration;

    [JsonConstructor]
    public HttpPostStepArgs(
        string urlExpr,
        Dictionary<string, string?>? headersKeyExprDict,
        Dictionary<string, string?>? bodyKeyExprDict,
        string? bodyExpr)
    {
        UrlExpr = urlExpr;
        HeadersKeyExprDict = headersKeyExprDict;
        BodyKeyExprDict = bodyKeyExprDict;
        BodyExpr = bodyExpr;
    }
}