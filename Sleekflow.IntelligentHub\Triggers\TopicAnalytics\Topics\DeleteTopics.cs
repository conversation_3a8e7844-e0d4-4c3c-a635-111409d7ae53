using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.TopicAnalytics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TopicAnalytics.Topics;

[TriggerGroup(ControllerNames.TopicAnalytics)]
public class DeleteTopics : ITrigger<DeleteTopics.DeleteTopicsInput, DeleteTopics.DeleteTopicsOutput>
{
    private readonly ITopicAnalyticsService _topicAnalyticsService;

    public DeleteTopics(ITopicAnalyticsService topicAnalyticsService)
    {
        _topicAnalyticsService = topicAnalyticsService;
    }

    public class DeleteTopicsInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("ids")]
        public List<string> Ids { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteTopicsInput(string sleekflowCompanyId, List<string> ids, string sleekflowStaffId, List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Ids = ids;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DeleteTopicsOutput
    {
    }

    public async Task<DeleteTopicsOutput> F(DeleteTopicsInput input)
    {
        await _topicAnalyticsService.DeleteTopicAnalyticsTopics(
            input.SleekflowCompanyId,
            input.Ids,
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);
        return new DeleteTopicsOutput();
    }
}