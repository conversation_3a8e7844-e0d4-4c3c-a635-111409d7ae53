﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowBulkReenrollmentInitiatedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string WorkflowVersionedId { get; set; }

    public DateTimeOffset PeriodFrom { get; set; }

    public DateTimeOffset PeriodTo { get; set; }

    public OnWorkflowBulkReenrollmentInitiatedEvent(
        string sleekflowCompanyId,
        string workflowVersionedId,
        DateTimeOffset periodFrom,
        DateTimeOffset periodTo)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
        PeriodFrom = periodFrom;
        PeriodTo = periodTo;
    }
}