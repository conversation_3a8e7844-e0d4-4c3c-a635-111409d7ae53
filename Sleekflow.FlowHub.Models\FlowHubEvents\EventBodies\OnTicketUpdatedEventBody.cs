using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnTicketUpdatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnTicketUpdated; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [Required]
    [JsonProperty("updated_properties")]
    public Dictionary<string, object?> UpdatedProperties { get; set; }

    [JsonProperty("updated_property_list")]
    public List<string> UpdatedPropertyList { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [Required]
    [JsonProperty("ticket")]
    public OnTicketUpdatedPropertyEventBody Ticket { get; set; }

    [JsonConstructor]
    public OnTicketUpdatedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        Dictionary<string, object?> updatedProperties,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId,
        OnTicketUpdatedPropertyEventBody ticket)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        UpdatedProperties = updatedProperties;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
        UpdatedPropertyList = updatedProperties.Keys.ToList();
        Ticket = ticket;
    }
}

public class OnTicketUpdatedPropertyEventBody : OnTicketCommonEventBody
{
    [JsonProperty("current_assignee")]
    public string CurrentAssignee { get; set; }

    [JsonProperty("current_assignee_email")]
    public string CurrentAssigneeEmail { get; set; }

    [JsonProperty("first_respondent")]
    public string FirstRespondent { get; set; }

    [JsonProperty("first_respondent_email")]
    public string FirstRespondentEmail { get; set; }

    [JsonProperty("first_response_timestamp")]
    public DateTimeOffset? FirstResponseTimestamp { get; set; }

    [JsonProperty("resolution_agent")]
    public string ResolutionAgent { get; set; }

    [JsonProperty("resolution_agent_email")]
    public string ResolutionAgentEmail { get; set; }

    [JsonProperty("resolution_timestamp")]
    public DateTimeOffset? ResolutionTimestamp { get; set; }

    [JsonProperty("ticket_reassigned_before_resolution")]
    public bool TicketReassignedBeforeResolution { get; set; }

    public OnTicketUpdatedPropertyEventBody(
        string currentAssignee,
        string currentAssigneeEmail,
        string firstRespondent,
        string firstRespondentEmail,
        DateTimeOffset? firstResponseTimestamp,
        string resolutionAgent,
        string resolutionAgentEmail,
        DateTimeOffset? resolutionTimestamp,
        bool ticketReassignedBeforeResolution,
        DateTimeOffset createdAt,
        string createdBy,
        string createdByEmail,
        string id,
        string title,
        OnTicketCommonEventBodyTicketChannel channel,
        string statusName,
        string statusId,
        string priorityName,
        string priorityId,
        DateTimeOffset? dueDate,
        string typeName,
        string typeId,
        string description,
        string firstAssignee,
        string firstAssigneeEmail,
        string contactName,
        string contactPhoneNumber,
        string contactEmail,
        string sleekflowUserProfileId)
        : base(
            createdAt,
            createdBy,
            createdByEmail,
            id,
            title,
            channel,
            statusName,
            statusId,
            priorityName,
            priorityId,
            dueDate,
            typeName,
            typeId,
            description,
            firstAssignee,
            firstAssigneeEmail,
            contactName,
            contactPhoneNumber,
            contactEmail,
            sleekflowUserProfileId)
    {
        CurrentAssignee = currentAssignee;
        CurrentAssigneeEmail = currentAssigneeEmail;
        FirstRespondent = firstRespondent;
        FirstRespondentEmail = firstRespondentEmail;
        FirstResponseTimestamp = firstResponseTimestamp;
        ResolutionAgent = resolutionAgent;
        ResolutionAgentEmail = resolutionAgentEmail;
        ResolutionTimestamp = resolutionTimestamp;
        TicketReassignedBeforeResolution = ticketReassignedBeforeResolution;
    }
}