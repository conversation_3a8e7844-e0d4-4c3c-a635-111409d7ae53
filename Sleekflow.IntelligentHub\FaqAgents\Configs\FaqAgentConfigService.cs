using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.FaqAgents.Configs;

public interface IFaqAgentConfigService
{
    FaqAgentConfig GetAgentConfigOrDefault(string sleekflowCompanyId);
}

public class FaqAgentConfigService : IFaqAgentConfigService, ISingletonService
{
    private readonly List<FaqAgentConfig> _agentConfigs = new ()
    {
        new FaqAgentConfig(
            "EGL Tours",
            "HEADLESS_BbMhGRJ562xJW2m",
            "2ee85f9fcb3d94bfb014ca4afe5ddc97502ae58e552c9ea39d69331dea6556f4",
            "e9fbb41b-bfd5-46de-b219-f9bdf97ec5bf",
            new List<string>
            {
                "You don't need to include EGL Tours in the search query. All knowledge base data comes from EGL Tours.",
            },
            new List<string>
            {
                "You serve as an energetic travel agent for EGL Tours. EGL Tours provides Japan, Southeast Asia, Europe, China long and short tour groups, free travel packages, overseas wedding travel, incentive travel, cruise holidays, air ticket booking, hotel, staycation, park tickets and car rental services for self-driving tours.",
                "If the user is asking about how to contact EGL Tours, WhatsApp the human agent through https://wa.me/85266880312.",
            },
            false,
            3),
        new FaqAgentConfig(
            "Midland",
            "HEADLESS_P2BhakWQaG4bv8x",
            "16ef39acc54dc0767f18cdae418559c16c35fd48bd9912cf38932ee3dae25ea7",
            "90e800e0-b6d3-4786-82ab-99338f7456b0",
            new List<string>
            {
                "You don't need to include Midland in the search query. All knowledge base data comes from Midland.",
            },
            new List<string>
            {
                "You serve as an online real estate agent for Midland Realty. Midland Realty is a leading real estate agency in Hong Kong. It provides a full range of real estate agency services, including residential, commercial, industrial and retail properties for sale and rent, as well as international properties for sale.",
            },
            false,
            5),
        new FaqAgentConfig(
            "HKSYU",
            "HEADLESS_z3rhxm87A28x3mm",
            "330b1ffdfe45a162be6d20c890a4b4997b734f8954b1bd04ab0923bcab04d45e",
            "6925631d-6e26-4d95-8206-a216d243dc25",
            new List<string>
            {
                "You don't need to include HKSYU in the search query. All knowledge base data comes from HKSYU.",
            },
            new List<string>
            {
                "You serve as an admission officer for Hong Kong Shue Yan University (HKSYU). HKSYU is a private liberal-arts university in Hong Kong. It provides a full range of study programmes, including arts, social sciences, commerce and business administration.",
            },
            false,
            3),
        new FaqAgentConfig(
            "iBilik",
            "HEADLESS_1Akh8A165aWd6lx",
            "fba1f26a75877a173793ed4a7c5ca8e3c2d1fc4441f604f901d30c8fabdf71d3",
            "bf5d31f9-8506-45fb-aa8c-4f67aa549a37",
            new List<string>
            {
                "You don't need to include iBilik in the search query. All knowledge base data comes from iBilik.",
            },
            new List<string>
            {
                "You serve as an online property leasing agent for iBilik. iBilik is a leading property leasing website in Malaysia. It is Malaysia's largest and No.1 website for rooms, homestays, and short term rentals with over 100,000 listings posted online all across Malaysia.",
                "If the user is asking about how to contact iBilik, answer with the WhatsApp link https://wa.me/601159978085.",
            },
            false,
            5),
        new FaqAgentConfig(
            "SleekFlow",
            "HEADLESS_KVRh3VoKYARK3ZZ",
            "1b1292ab274b4141fbefaae42310d1e8fc26452e71737206898d0b7315df7165",
            "163ff3c4-378e-477f-bb36-71e5a5e42fdc",
            new List<string>
            {
                "You don't need to include SleekFlow in the search query. All knowledge base data comes from SleekFlow.",
            },
            new List<string>
            {
                "You serve as an online FAQ bot for SleekFlow. SleekFlow is an omnichannel social commerce platform that enables full conversational journeys across SMS, live chat on websites, and popular social and messaging applications.",
                """
                If the user is asking about how to contact SleekFlow, answer with the following emails:\"\"\"
                Support: <EMAIL>
                General Enquiry: <EMAIL>
                Media Enquiry: <EMAIL>
                Sales: <EMAIL>
                \"\"\"
                """
            },
            true,
            5),
        new FaqAgentConfig(
            "Index Holdings Limited",
            "HEADLESS_KVRhXxGl5oN7VbZ",
            "41dacde9931930d7605befe841eeb150f415175eae2d3ed74df41f70ba85b3e5",
            "cbf6050c-5836-41b9-ae0c-35ae8a7183ad",
            new List<string>
            {
                "You don't need to include Index Holdings Limited in the search query. All knowledge base data comes from Index Holdings Limited.",
            },
            new List<string>
            {
                "You serve as an online exhibition assistant for Index Holdings Limited. You should provide answers on details of the exhibition, and share contact point information."
            },
            false,
            5),
        new FaqAgentConfig(
            "HKBN",
            "HEADLESS_p1ki64b7b52m8R2",
            "c0bd18681ef582befcca98a00dd0d57307d78df0fd060a5e74ee1a75cbe63bde",
            "64e9cbfe-89e5-45ce-b388-88e0c1ea1989",
            new List<string>
            {
                "You don't need to include HKBN in the search query. All knowledge base data comes from HKBN.",
            },
            new List<string>
            {
                "You serve as an online sales assistant for Hong Kong Broadband Network (HKBN). HKBN is Hong Kong's leading telecom and IT system integration provider. You should provide answers on details of the products."
            },
            false,
            5),
        new FaqAgentConfig(
            "Aureus Academy",
            "HEADLESS_41riZ834NPQMLAr",
            "d026dc7221e377d142618759b2f0f1cc3087a2bdd60307d814c685dd98e922e0",
            "51c82d9a-b49b-4214-9060-7ef3ef66ea22",
            new List<string>
            {
                "You don't need to include Aureus Academy in the search query. All knowledge base data comes from Aureus Academy.",
            },
            new List<string>
            {
                "You serve as an online sales assistant for Aureus Academy. Aureus Academy is Singapore's leading music school with over 18,000 students enrolled between our school. Aureus Academy specialises in providing individually tailored piano lessons, violin lessons, guitar lessons, and voice lessons and more to students of all ages and abilities. You should provide answers on details of the lessons and admission."
            },
            false,
            5),
        new FaqAgentConfig(
            "GLO Travel",
            "HEADLESS_MzSd6gde4bKd52q",
            "1c81242c5a68d19b7d00443c58effcc32bb1c9465dca210c74b91411b39b5054",
            "124e4759-69d8-44ba-be11-8fa8d2682767",
            new List<string>
            {
                "You don't need to include GLO Travel in the search query. All knowledge base data comes from GLO Travel.",
            },
            new List<string>
            {
                "You serve as an online travel assistant for GLO Travel. GLO Travel is Hong Kong's travel agency specializes in in-depth cultural tours and adheres to the concept of Go Local to provide travelers with an intellectual, unforgettable and interesting in-depth travel experience."
            },
            false,
            5),
        new FaqAgentConfig(
            "FWD AI",
            "HEADLESS_rQSbEo03Vlqb33n",
            "d44d890a51551c8e4ba0645d0b1730e5315ddd13dcc46b73aaa39199433b51a6",
            "eff1f5f4-bf6a-48c8-a78f-8b8005a6ef23",
            new List<string>
            {
                "You don't need to include FWD AI in the search query. All knowledge base data comes from FWD AI.",
            },
            new List<string>
            {
                "You serve as an online sales assistant for FWD AI. FWD AI is a insurance company which focusing on making the insurance journey simpler, faster and smoother, with innovative propositions and easy-to-understand products, supported by digital technology. You should answer FAQs and product knowledge regarding to the FWD AI handbooks."
            },
            false,
            5)
    };

    public FaqAgentConfig GetAgentConfigOrDefault(string sleekflowCompanyId)
    {
        var faqAgentConfig = _agentConfigs.Find(x => x.SleekflowCompanyId == sleekflowCompanyId);
        if (faqAgentConfig == null)
        {
            return new FaqAgentConfig(
                string.Empty,
                string.Empty,
                string.Empty,
                sleekflowCompanyId,
                new List<string>
                {
                },
                new List<string>
                {
                },
                false,
                5);
        }

        return faqAgentConfig;
    }
}