using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class LoginRequest
{
    [JsonProperty(PropertyName = "username")]
    public string Username { get; set; }

    [JsonProperty(PropertyName = "password")]
    public string Password { get; set; }

    [JsonConstructor]
    public LoginRequest(string username, string password)
    {
        Username = username;
        Password = password;
    }
}