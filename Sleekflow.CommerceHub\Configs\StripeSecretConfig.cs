using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface IStripeSecretConfig
{
    string StripeApiKeyHk { get; }

    string StripeApiKeySg { get; }

    string StripeApiKeyMy { get; }

    string StripeApiKeyGb { get; }

    string StripePaymentWebhookSecretHk { get; }

    string StripePaymentWebhookSecretSg { get; }

    string StripePaymentWebhookSecretMy { get; }

    string StripePaymentWebhookSecretGb { get; }

    string StripeConnectWebhookSecretHk { get; }

    string StripeConnectWebhookSecretSg { get; }

    string StripeConnectWebhookSecretMy { get; }

    string StripeConnectWebhookSecretGb { get; }
}

public class StripeSecretConfig : IConfig, IStripeSecretConfig
{
    public string StripeApiKeyHk { get; }

    public string StripeApiKeySg { get; }

    public string StripeApiKeyMy { get; }

    public string StripeApiKeyGb { get; }

    public string StripePaymentWebhookSecretHk { get; }

    public string StripePaymentWebhookSecretSg { get; }

    public string StripePaymentWebhookSecretMy { get; }

    public string StripePaymentWebhookSecretGb { get; }

    public string StripeConnectWebhookSecretHk { get; }

    public string StripeConnectWebhookSecretSg { get; }

    public string StripeConnectWebhookSecretMy { get; }

    public string StripeConnectWebhookSecretGb { get; }

    public StripeSecretConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        StripeApiKeyHk = GetEnvironmentVariable(target, "STRIPE_API_KEY_HK");
        StripeApiKeySg = GetEnvironmentVariable(target, "STRIPE_API_KEY_SG");
        StripeApiKeyMy = GetEnvironmentVariable(target, "STRIPE_API_KEY_MY");
        StripeApiKeyGb = GetEnvironmentVariable(target, "STRIPE_API_KEY_GB");
        StripePaymentWebhookSecretHk = GetEnvironmentVariable(target, "STRIPE_PAYMENT_WEBHOOK_SECRET_HK");
        StripePaymentWebhookSecretSg = GetEnvironmentVariable(target, "STRIPE_PAYMENT_WEBHOOK_SECRET_SG");
        StripePaymentWebhookSecretMy = GetEnvironmentVariable(target, "STRIPE_PAYMENT_WEBHOOK_SECRET_MY");
        StripePaymentWebhookSecretGb = GetEnvironmentVariable(target, "STRIPE_PAYMENT_WEBHOOK_SECRET_GB");
        StripeConnectWebhookSecretHk = GetEnvironmentVariable(target, "STRIPE_CONNECT_WEBHOOK_SECRET_HK");
        StripeConnectWebhookSecretSg = GetEnvironmentVariable(target, "STRIPE_CONNECT_WEBHOOK_SECRET_SG");
        StripeConnectWebhookSecretMy = GetEnvironmentVariable(target, "STRIPE_CONNECT_WEBHOOK_SECRET_MY");
        StripeConnectWebhookSecretGb = GetEnvironmentVariable(target, "STRIPE_CONNECT_WEBHOOK_SECRET_GB");
    }

    private static string GetEnvironmentVariable(
        EnvironmentVariableTarget target,
        string environmentVariable,
        string? message = null)
    {
        return Environment.GetEnvironmentVariable(environmentVariable, target)
               ?? throw new SfMissingEnvironmentVariableException(message ?? environmentVariable);
    }
}