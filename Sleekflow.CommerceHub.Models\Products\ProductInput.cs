using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Products;

public class ProductInput : IHasMetadata
{
    [Required]
    [JsonProperty(Product.PropertyNameCategoryIds)]
    public List<string> CategoryIds { get; set; }

    [JsonProperty(Product.PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(Product.PropertyNameUrl)]
    public string? Url { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(Product.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(Product.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty("images")]
    public List<ImageDto> Images { get; set; }

    [Required]
    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public ProductInput(
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<ImageDto> images,
        Dictionary<string, object?> metadata)
    {
        CategoryIds = categoryIds;
        Sku = sku;
        Url = url;
        Names = names;
        Descriptions = descriptions;
        Images = images;
        Metadata = metadata;
    }
}