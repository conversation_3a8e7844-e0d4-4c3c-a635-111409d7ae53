﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Integrator.Zoho.UserMappingConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.Zoho.Consumers;

public class UpdateZohoObjectRequestConsumerDefinition : ConsumerDefinition<UpdateZohoObjectRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<UpdateZohoObjectRequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class UpdateZohoObjectRequestConsumer : IConsumer<UpdateZohoObjectRequest>
{
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly ILogger<UpdateZohoObjectRequestConsumer> _logger;

    public UpdateZohoObjectRequestConsumer(
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService,
        IBus bus,
        IZohoConnectionService zohoConnectionService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        ILogger<UpdateZohoObjectRequestConsumer> logger)
    {
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<UpdateZohoObjectRequest> context)
    {
        var request = context.Message;

        var consumeId = Guid.NewGuid().ToString();

        try
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoUpdateObjectRequestReceived,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                    {
                        "object_id", request.ObjectId
                    }
                });

            var connection =
                await _zohoConnectionService.GetByIdAsync(
                    request.ConnectionId,
                    request.SleekflowCompanyId);

            var authentication =
                await _zohoAuthenticationService.GetAsync(
                    connection.AuthenticationId,
                    request.SleekflowCompanyId);
            if (authentication == null)
            {
                throw new SfUnauthorizedException();
            }

            var getFieldsOutput =
                await _zohoObjectService.GetFieldsAsync(authentication, request.EntityTypeName);
            var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();
            var updatableBooleanFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "boolean")
                .Select(f => f.Name)
                .ToList();
            var updatableDateFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "date")
                .Select(f => f.Name)
                .ToList();
            var updatableDateTimeFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "datetime")
                .Select(f => f.Name)
                .ToList();

            var dict = request.ObjectProperties
                .Where(e => updatableFieldNames.Contains(e.Key))
                .ToDictionary(
                    e => e.Key,
                    e =>
                    {
                        if (updatableBooleanFieldNames.Contains(e.Key) && e.Value is string value)
                        {
                            return value.ToLower();
                        }

                        if (updatableDateFieldNames.Contains(e.Key))
                        {
                            if (e.Value is DateTimeOffset dateValue)
                            {
                                return dateValue.ToString("yyyy-MM-dd");
                            }

                            if (e.Value is string dateString && DateTimeOffset.TryParse(
                                    dateString,
                                    out var parsedDateValue))
                            {
                                return parsedDateValue.ToString("yyyy-MM-dd");
                            }
                        }

                        if (updatableDateTimeFieldNames.Contains(e.Key))
                        {
                            if (e.Value is DateTimeOffset dateTimeValue)
                            {
                                return dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                            }

                            if (e.Value is string dateTimeString && DateTimeOffset.TryParse(
                                    dateTimeString,
                                    out var parsedDateTimeValue))
                            {
                                return parsedDateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                            }
                        }

                        return e.Value;
                    });

            await _zohoObjectService.UpdateAsync(
                authentication,
                dict,
                request.ObjectId,
                request.EntityTypeName);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoUpdateObjectRequestHandled,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                    {
                        "object_id", request.ObjectId
                    }
                });

            await _bus.Publish(
                new OnZohoCompleteStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null));
        }
        catch (Exception ex)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoUpdateObjectRequestFailed,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                    {
                        "object_id", request.ObjectId
                    }
                });

            _logger.LogError(
                ex,
                "UpdateZohoObjectRequest failed,"
                + " ConsumeId: {ConsumeId},"
                + " SleekflowCompanyId: {SleekflowCompanyId},"
                + " ConnectionId: {ConnectionId},"
                + " EntityTypeName: {EntityTypeName},"
                + " ObjectId: {ObjectId},"
                + " ObjectProperties: {ObjectProperties},"
                + " AggregateStepId: {AggregateStepId},"
                + " ProxyStateId: {ProxyStateId},"
                + " StackEntries: {StackEntries},",
                consumeId,
                request.SleekflowCompanyId,
                request.ConnectionId,
                request.EntityTypeName,
                request.ObjectId,
                JsonConvert.SerializeObject(request.ObjectProperties),
                request.AggregateStepId,
                request.ProxyStateId,
                request.StackEntries);

            await _bus.Publish(
                new OnZohoFailStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null,
                    ex));
        }
    }
}