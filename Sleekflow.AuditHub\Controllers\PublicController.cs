using Microsoft.AspNetCore.Mvc;

namespace Sleekflow.AuditHub.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }
}