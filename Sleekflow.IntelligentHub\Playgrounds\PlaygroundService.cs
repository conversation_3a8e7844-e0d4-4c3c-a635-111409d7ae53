﻿using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Playgrounds;

namespace Sleekflow.IntelligentHub.Playgrounds;

public interface IPlaygroundService
{
    Task<Playground> CreateAsync(
        string playgroundId,
        string sleekflowCompanyId,
        string sleekflowUserId,
        CompanyAgentConfigDto agentConfig);

    Task<Playground> GetAsync(string id, string sleekflowCompanyId);

    Task<Playground?> GetOrDefaultAsync(string id, string sleekflowCompanyId, string sleekflowUserId);

    Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        List<PlaygroundRecommendedReply> recommendedReplies,
        string? eTag = null);
}

public class PlaygroundService : IPlaygroundService, IScopedService
{
    private readonly IIdService _idService;
    private readonly IPlaygroundRepository _playgroundRepository;

    public PlaygroundService(IPlaygroundRepository playgroundRepository, IIdService idService)
    {
        _playgroundRepository = playgroundRepository;
        _idService = idService;
    }

    public async Task<Playground> CreateAsync(
        string playgroundId,
        string sleekflowCompanyId,
        string sleekflowUserId,
        CompanyAgentConfigDto agentConfig)
    {
        var playground = new Playground(
            playgroundId,
            sleekflowCompanyId,
            sleekflowUserId,
            [],
            agentConfig,
            null);

        return await _playgroundRepository.CreateAndGetAsync(playground, sleekflowCompanyId);
    }

    public async Task<Playground> GetAsync(string id, string sleekflowCompanyId)
    {
        return await _playgroundRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<Playground?> GetOrDefaultAsync(
        string id,
        string sleekflowCompanyId,
        string sleekflowUserId)
    {
        var playgrounds = await _playgroundRepository.GetObjectsAsync(p =>
            p.SleekflowCompanyId == sleekflowCompanyId && p.SleekflowUserId == sleekflowUserId &&
            p.Id == id);
        return playgrounds.FirstOrDefault();
    }

    public async Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        List<PlaygroundRecommendedReply> recommendedReplies,
        string? eTag = null)
    {
        await _playgroundRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            recommendedReplies,
            eTag);
    }
}