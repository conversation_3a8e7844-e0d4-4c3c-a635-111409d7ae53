using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Events;

namespace Sleekflow.Models.WorkflowSteps;

public class GetAgentRecommendedReplyEvent : AgentEventBase
{
    public string SleekflowCompanyId { get; set; }

    public string? AgentId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public Dictionary<string, string>? ContactProperties { get; set; }

    public string? ContactId { get; set; }

    public DateTimeOffset? IntelligentHubUsageFilterFromDateTime { get; set; }

    public DateTimeOffset? IntelligentHubUsageFilterToDateTime { get; set; }

    public string? Input { get; set; }

    public bool IsCompanyHasAiPocPlan { get; set; }

    public GetAgentRecommendedReplyEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string? agentId,
        List<SfChatEntry> conversationContext,
        Dictionary<string, string>? contactProperties = null,
        string? contactId = null,
        DateTimeOffset? intelligentHubUsageFilterFromDateTime = null,
        DateTimeOffset? intelligentHubUsageFilterToDateTime = null,
        string? input = null,
        bool isCompanyHasAiPocPlan = false)
        : base(aggregateStepId, proxyStateId, stackEntries)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        AgentId = agentId;
        ConversationContext = conversationContext;
        ContactProperties = contactProperties;
        ContactId = contactId;
        IntelligentHubUsageFilterFromDateTime = intelligentHubUsageFilterFromDateTime;
        IntelligentHubUsageFilterToDateTime = intelligentHubUsageFilterToDateTime;
        Input = input;
        IsCompanyHasAiPocPlan = isCompanyHasAiPocPlan;
    }

    public class Response
    {
        [JsonProperty("recommended_reply")]
        public string RecommendedReply { get; set; }

        [JsonProperty("confidence_score")]
        public int ConfidenceScore { get; set; }

        [JsonConstructor]
        public Response(string recommendedReply, int confidenceScore)
        {
            RecommendedReply = recommendedReply;
            ConfidenceScore = confidenceScore;
        }
    }
}