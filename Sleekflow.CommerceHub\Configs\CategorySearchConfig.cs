using Sleekflow.Configs;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface ICategorySearchConfig : IBaseCognitiveSearchConfig
{
}

public class CategorySearchConfig : BaseCognitiveSearchConfig, ICategorySearchConfig
{
    public CategorySearchConfig()
        : base(EnvironmentVariableTarget.Process)
    {
    }

    public override string GetIndexName()
    {
        return Environment.GetEnvironmentVariable("COGNITIVE_SEARCH_COMMERCE_HUB_CATEGORY_INDEX") ??
               throw new SfMissingEnvironmentVariableException("COGNITIVE_SEARCH_COMMERCE_HUB_CATEGORY_INDEX");
    }
}