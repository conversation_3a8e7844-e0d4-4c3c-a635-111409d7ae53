using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Payloads.Models.ConversationalComponent;
using GraphApi.Client.Payloads.Whatsapp.ConversationalComponent;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.ConversationalComponents;

public interface IConversationalAutomationService
{
    Task<GetConversationalAutomationsByWabaIdResponse> GetConversationalAutomationsAsync(
        Waba waba,
        CursorBasedPaginationParam? paginationParam);

    Task<GetConversationalAutomationByPhoneNumberIdResponse> GetConversationalAutomationAsync(
        Waba waba,
        string facebookPhoneNumberId);

    public Task<SuccessGraphApiResponse> UpdateConversationalAutomationAsync(
        string sfStaffId,
        Waba waba,
        string facebookPhoneNumberId,
        ConversationalAutomation updateObject);
}

public class ConversationalAutomationService
    : IConversationalAutomationService, ISingletonService
{
    private readonly IWabaService _wabaService;
    private readonly IWhatsappCloudApiConversationalAutomationClient _whatsappCloudApiConversationalAutomationClient;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<ConversationalAutomationService> _logger;

    public ConversationalAutomationService(
        IWabaService wabaService,
        ICloudApiClients cloudApiClients,
        IHttpClientFactory httpClientFactory,
        ILogger<ConversationalAutomationService> logger)
    {
        _wabaService = wabaService;
        _whatsappCloudApiConversationalAutomationClient = cloudApiClients.WhatsappCloudApiConversationalAutomationClient;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public Task<GetConversationalAutomationsByWabaIdResponse> GetConversationalAutomationsAsync(
        Waba waba,
        CursorBasedPaginationParam? paginationParam)
    {
        return GetWhatsappCloudApiConversationalAutomationClient(waba)
            .GetConversationalAutomationsAsync(
                waba.FacebookWabaId,
                paginationParam ?? new CursorBasedPaginationParam()
                {
                    Limit = 500
                });
    }

    public Task<GetConversationalAutomationByPhoneNumberIdResponse> GetConversationalAutomationAsync(
        Waba waba,
        string facebookPhoneNumberId)
    {
        return GetWhatsappCloudApiConversationalAutomationClient(waba)
            .GetConversationalAutomationAsync(facebookPhoneNumberId);
    }

    public Task<SuccessGraphApiResponse> UpdateConversationalAutomationAsync(
        string sfStaffId,
        Waba waba,
        string facebookPhoneNumberId,
        ConversationalAutomation updateObject)
    {
        try
        {
            _logger.LogInformation(
                $"[WhatsappCloudApi][{nameof(ConversationalAutomation)}] executor: {{sfStaffId}}, object: {{updateObject}}",
                sfStaffId,
                updateObject);

            return GetWhatsappCloudApiConversationalAutomationClient(waba)
                .UpdateConversationalAutomationAsync(facebookPhoneNumberId, updateObject);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfGraphApiErrorException(
                    $"Unable to update config.",
                    JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
            }

            throw;
        }
    }

    private IWhatsappCloudApiConversationalAutomationClient GetWhatsappCloudApiConversationalAutomationClient(Waba waba)
    {
         var (_, decryptedBusinessIntegrationSystemUserAccessTokenDto) = _wabaService
             .GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

         var accessToken = decryptedBusinessIntegrationSystemUserAccessTokenDto?.DecryptedToken;

         return string.IsNullOrEmpty(accessToken)
             ? _whatsappCloudApiConversationalAutomationClient
             : new WhatsappCloudApiConversationalAutomationClient(accessToken, _httpClientFactory.CreateClient("default-handler"));
    }
}