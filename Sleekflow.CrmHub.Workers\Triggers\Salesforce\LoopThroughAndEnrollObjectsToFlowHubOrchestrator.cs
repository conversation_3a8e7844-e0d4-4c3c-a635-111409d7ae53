﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class LoopThroughAndEnrollObjectsToFlowHubOrchestrator : ITrigger
{
    public class LoopThroughAndEnrollObjectsToFlowHubOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubOrchestratorCustomStatusOutput(long count, DateTime lastUpdateTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class LoopThroughAndEnrollObjectsToFlowHubOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Salesforce_LoopThroughAndEnrollObjectsToFlowHub_Orchestrator")]
    public async Task<LoopThroughAndEnrollObjectsToFlowHubOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var loopThroughAndEnrollObjectsToFlowHubInput = context.GetInput<LoopThroughAndEnrollObjectsToFlowHub.LoopThroughAndEnrollObjectsToFlowHubInput>();

        context.SetCustomStatus(new LoopThroughAndEnrollObjectsToFlowHubOrchestratorCustomStatusOutput(0, context.CurrentUtcDateTime));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;
        var nextRecordsUrl = null as string;
        while (true)
        {
            var loopThroughAndEnrollObjectsToFlowHubBatchOutput = await context
                .CallActivityAsync<LoopThroughAndEnrollObjectsToFlowHubBatch.LoopThroughAndEnrollObjectsToFlowHubBatchOutput>(
                    "Salesforce_LoopThroughAndEnrollObjectsToFlowHub_Batch",
                    new LoopThroughAndEnrollObjectsToFlowHubBatch.LoopThroughAndEnrollObjectsToFlowHubBatchInput(
                        loopThroughAndEnrollObjectsToFlowHubInput.SleekflowCompanyId,
                        loopThroughAndEnrollObjectsToFlowHubInput.ConnectionId,
                        loopThroughAndEnrollObjectsToFlowHubInput.EntityTypeName,
                        loopThroughAndEnrollObjectsToFlowHubInput.IsCustomObject,
                        nextRecordsUrl,
                        loopThroughAndEnrollObjectsToFlowHubInput.FlowHubWorkflowId,
                        loopThroughAndEnrollObjectsToFlowHubInput.FlowHubWorkflowVersionedId),
                    taskOptions);

            totalCount += loopThroughAndEnrollObjectsToFlowHubBatchOutput.Count;
            nextRecordsUrl = loopThroughAndEnrollObjectsToFlowHubBatchOutput.NextRecordsUrl;

            context.SetCustomStatus(
                new LoopThroughAndEnrollObjectsToFlowHubOrchestratorCustomStatusOutput(totalCount, context.CurrentUtcDateTime));

            if (nextRecordsUrl == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        return new LoopThroughAndEnrollObjectsToFlowHubOrchestratorOutput(totalCount);
    }
}