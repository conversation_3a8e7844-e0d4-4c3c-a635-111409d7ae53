using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.Unifies;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.Triggers.UnifyRules;

[TriggerGroup("UnifyRules")]
public class UpsertUnifyRules : ITrigger
{
    private readonly IUnifyService _unifyService;
    private readonly IIdService _idService;

    public UpsertUnifyRules(
        IUnifyService unifyService,
        IIdService idService)
    {
        _unifyService = unifyService;
        _idService = idService;
    }

    public class UnifyRuleDto
    {
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonProperty("strategy")]
        public string Strategy { get; set; }

        [JsonProperty("provider_precedences")]
        public List<string> ProviderPrecedences { get; set; }

        public UnifyRuleDto(
            string fieldName,
            string strategy,
            List<string> providerPrecedences)
        {
            FieldName = fieldName;
            Strategy = strategy;
            ProviderPrecedences = providerPrecedences;
        }
    }

    public class UpsertUnifyRulesInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("unify_rules")]
        [Required]
        public List<UnifyRuleDto> UnifyRules { get; set; }

        [JsonConstructor]
        public UpsertUnifyRulesInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<UnifyRuleDto> unifyRules)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            UnifyRules = unifyRules;
        }
    }

    public class UpsertUnifyRulesOutput
    {
    }

    public async Task<UpsertUnifyRulesOutput> F(
        UpsertUnifyRulesInput upsertUnifyRulesInput)
    {
        var unifyRules = upsertUnifyRulesInput.UnifyRules;

        var fieldNameToUnifyRuleDict = GetFieldNameToUnifyRuleDict(
            unifyRules);

        // Add Default Rules
        var defaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule(
                "SleekflowId",
                "time",
                new List<string>
                {
                    "sleekflow:Id"
                },
                true),
            new UnifyRule(
                "HubspotIntegratorId",
                "time",
                new List<string>
                {
                    "hubspot-integrator:hs_object_id", "hubspot-integrator:id"
                },
                true),
            new UnifyRule(
                "SalesforceIntegratorId",
                "time",
                new List<string>
                {
                    "salesforce-integrator:Id"
                },
                true),
            new UnifyRule(
                "D365Id",
                "time",
                new List<string>
                {
                    "d365:id"
                },
                true),
        };
        switch (upsertUnifyRulesInput.EntityTypeName)
        {
            case "Contact":
                defaultUnifyRules.AddRange(GetContactDefaultUnifyRules());
                break;
            case "Lead":
                defaultUnifyRules.AddRange(GetLeadDefaultUnifyRules());
                break;
            case "Opportunity":
                defaultUnifyRules.AddRange(GetOpportunityDefaultUnifyRules());
                break;
            case "User":
                defaultUnifyRules.AddRange(GetUserDefaultUnifyRules());
                break;
            case "SalesOrder":
                defaultUnifyRules.AddRange(GetSalesOrderDefaultUnifyRules());
                break;
            case "SalesOrderItem":
                defaultUnifyRules.AddRange(GetSalesOrderItemDefaultUnifyRules());
                break;
            case "Store":
                defaultUnifyRules.AddRange(GetStoreDefaultUnifyRules());
                break;
            case "Campaign":
                defaultUnifyRules.AddRange(GetCampaignDefaultUnifyRules());
                break;
            case "CampaignMember":
                defaultUnifyRules.AddRange(GetCampaignMemberDefaultUnifyRules());
                break;
            case "Language":
                defaultUnifyRules.AddRange(GetLanguageDefaultUnifyRules());
                break;
            case "Product":
                defaultUnifyRules.AddRange(GetProductDefaultUnifyRules());
                break;
            case "ProductVariant":
                defaultUnifyRules.AddRange(GetProductVariantDefaultUnifyRules());
                break;
            case "Currency":
                defaultUnifyRules.AddRange(GetCurrencyDefaultUnifyRules());
                break;
            case "Salesperson":
                defaultUnifyRules.AddRange(GetSalespersonDefaultUnifyRules());
                break;
            case "AwarenessSource":
                defaultUnifyRules.AddRange(GetAwarenessSourceDefaultUnifyRules());
                break;
        }

        foreach (var defaultUnifyRule in defaultUnifyRules
                     .Where(
                         defaultUnifyRule =>
                             fieldNameToUnifyRuleDict.ContainsKey(defaultUnifyRule.FieldName) == false))
        {
            fieldNameToUnifyRuleDict.Add(defaultUnifyRule.FieldName, defaultUnifyRule);
        }

        var distinctUnifyRules = fieldNameToUnifyRuleDict
            .Select(e => e.Value)
            .ToList();
        var flattenedUnifyRules = fieldNameToUnifyRuleDict
            .Select(e => e.Value)
            .SelectMany(
                r => r
                    .ProviderPrecedences
                    .Select(precedence => new FlattenedUnifyRule(r.FieldName, precedence)))
            .ToList();

        var unifyId = await GetOrGenerateIdAsync(upsertUnifyRulesInput);
        var unify = new Unify(
            unifyId,
            upsertUnifyRulesInput.SleekflowCompanyId,
            upsertUnifyRulesInput.EntityTypeName,
            distinctUnifyRules,
            flattenedUnifyRules);

        await _unifyService.UpsertAsync(unify);

        return new UpsertUnifyRulesOutput();
    }

    private IEnumerable<UnifyRule> GetStoreDefaultUnifyRules()
    {
        // @formatter:off
        var salesOrderDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Name", "time",
                new List<string> { "d365:new_name" }, true),
        };

        // @formatter:on
        return salesOrderDefaultUnifyRules;
    }

    private IEnumerable<UnifyRule> GetCampaignDefaultUnifyRules()
    {
        // @formatter:off
        var campaignDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Name", "time",
                new List<string> { "salesforce-integrator:Name" }, true),
            new UnifyRule("Type", "time",
                new List<string> { "salesforce-integrator:Type" }, true),
            new UnifyRule("Status", "time",
                new List<string> { "salesforce-integrator:Status" }, true),
            new UnifyRule("StartDate", "time",
                new List<string> { "salesforce-integrator:StartDate" }, true),
            new UnifyRule("EndDate", "time",
                new List<string> { "salesforce-integrator:EndDate" }, true),
            new UnifyRule("CurrencyIsoCode", "time",
                new List<string> { "salesforce-integrator:CurrencyIsoCode" }, true),
            new UnifyRule("ExpectedRevenue", "time",
                new List<string> { "salesforce-integrator:ExpectedRevenue" }, true),
            new UnifyRule("BudgetedCost", "time",
                new List<string> { "salesforce-integrator:BudgetedCost" }, true),
            new UnifyRule("ActualCost", "time",
                new List<string> { "salesforce-integrator:ActualCost" }, true),
            new UnifyRule("ExpectedResponse", "time",
                new List<string> { "salesforce-integrator:ExpectedResponse" }, true),
            new UnifyRule("NumberSent", "time",
                new List<string> { "salesforce-integrator:NumberSent" }, true),
            new UnifyRule("IsActive", "time",
                new List<string> { "salesforce-integrator:IsActive" }, true),
            new UnifyRule("Description", "time",
                new List<string> { "salesforce-integrator:Description" }, true),
            new UnifyRule("SalesforceIntegratorOwnerId", "time",
                new List<string> { "salesforce-integrator:OwnerId" }, true),
            new UnifyRule("CampaignMemberRecordTypeId", "time",
                new List<string> { "salesforce-integrator:CampaignMemberRecordTypeId" }, true),
            new UnifyRule("IsDeleted", "time",
                new List<string> { "salesforce-integrator:IsDeleted" }, true),
            new UnifyRule("CreatedDate", "time",
                new List<string> { "salesforce-integrator:CreatedDate" }, true),
            new UnifyRule("LastModifiedDate", "time",
                new List<string> { "salesforce-integrator:LastModifiedDate" }, true),
            new UnifyRule("SystemModstamp", "time",
                new List<string> { "salesforce-integrator:SystemModstamp" }, true),
            new UnifyRule("LastActivityDate", "time",
                new List<string> { "salesforce-integrator:LastActivityDate" }, true),
            new UnifyRule("LastViewedDate", "time",
                new List<string> { "salesforce-integrator:LastViewedDate" }, true),
            new UnifyRule("LastReferencedDate", "time",
                new List<string> { "salesforce-integrator:LastReferencedDate" }, true),
        };

        // @formatter:on
        return campaignDefaultUnifyRules;
    }

    private IEnumerable<UnifyRule> GetCampaignMemberDefaultUnifyRules()
    {
        // @formatter:off
        var campaignMemberDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Status", "time",
                new List<string> { "salesforce-integrator:Status" }, true),
            new UnifyRule("CurrencyIsoCode", "time",
                new List<string> { "salesforce-integrator:CurrencyIsoCode" }, true),
            new UnifyRule("SalesforceIntegratorCampaignId", "time",
                new List<string> { "salesforce-integrator:CampaignId" }, true),
            new UnifyRule("SalesforceIntegratorLeadId", "time",
                new List<string> { "salesforce-integrator:LeadId" }, true),
            new UnifyRule("SalesforceIntegratorContactId", "time",
                new List<string> { "salesforce-integrator:ContactId" }, true),
            new UnifyRule("IsDeleted", "time",
                new List<string> { "salesforce-integrator:IsDeleted" }, true),
            new UnifyRule("HasResponded", "time",
                new List<string> { "salesforce-integrator:HasResponded" }, true),
            new UnifyRule("CreatedDate", "time",
                new List<string> { "salesforce-integrator:CreatedDate" }, true),
            new UnifyRule("LastModifiedDate", "time",
                new List<string> { "salesforce-integrator:LastModifiedDate" }, true),
            new UnifyRule("SystemModstamp", "time",
                new List<string> { "salesforce-integrator:SystemModstamp" }, true),
            new UnifyRule("FirstRespondedDate", "time",
                new List<string> { "salesforce-integrator:FirstRespondedDate" }, true),
            new UnifyRule("Salutation", "time",
                new List<string> { "salesforce-integrator:Salutation" }, true),
            new UnifyRule("Name", "time",
                new List<string> { "salesforce-integrator:Name" }, true),
            new UnifyRule("FirstName", "time",
                new List<string> { "salesforce-integrator:FirstName" }, true),
            new UnifyRule("LastName", "time",
                new List<string> { "salesforce-integrator:LastName" }, true),
            new UnifyRule("Title", "time",
                new List<string> { "salesforce-integrator:Title" }, true),
            new UnifyRule("Street", "time",
                new List<string> { "salesforce-integrator:Street" }, true),
            new UnifyRule("City", "time",
                new List<string> { "salesforce-integrator:City" }, true),
            new UnifyRule("State", "time",
                new List<string> { "salesforce-integrator:State" }, true),
            new UnifyRule("PostalCode", "time",
                new List<string> { "salesforce-integrator:PostalCode" }, true),
            new UnifyRule("Country", "time",
                new List<string> { "salesforce-integrator:Country" }, true),
            new UnifyRule("Email", "time",
                new List<string> { "salesforce-integrator:Email" }, true),
            new UnifyRule("Phone", "time",
                new List<string> { "salesforce-integrator:Phone" }, true),
            new UnifyRule("Fax", "time",
                new List<string> { "salesforce-integrator:Fax" }, true),
            new UnifyRule("MobilePhone", "time",
                new List<string> { "salesforce-integrator:MobilePhone" }, true),
            new UnifyRule("Description", "time",
                new List<string> { "salesforce-integrator:Description" }, true),
            new UnifyRule("DoNotCall", "time",
                new List<string> { "salesforce-integrator:DoNotCall" }, true),
            new UnifyRule("HasOptedOutOfEmail", "time",
                new List<string> { "salesforce-integrator:HasOptedOutOfEmail" }, true),
            new UnifyRule("HasOptedOutOfFax", "time",
                new List<string> { "salesforce-integrator:HasOptedOutOfFax" }, true),
            new UnifyRule("LeadSource", "time",
                new List<string> { "salesforce-integrator:LeadSource" }, true),
            new UnifyRule("CompanyOrAccount", "time",
                new List<string> { "salesforce-integrator:CompanyOrAccount" }, true),
            new UnifyRule("Type", "time",
                new List<string> { "salesforce-integrator:Type" }, true),
        };

        // @formatter:on
        return campaignMemberDefaultUnifyRules;
    }

    private IEnumerable<UnifyRule> GetSalesOrderItemDefaultUnifyRules()
    {
        // @formatter:off
        var salesOrderDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Sku", "time",
                new List<string> { "d365:productname" }, true),
            new UnifyRule("Quantity", "time",
                new List<string> { "d365:quantity" }, true),
            new UnifyRule("Price", "time",
                new List<string> { "d365:thk_gross_amount" }, true),
            new UnifyRule("UnitPrice", "time",
                new List<string> { "d365:priceperunit" }, true),
            new UnifyRule("PurchaseDate", "time",
                new List<string> { "d365:createdon" }, true),
            new UnifyRule("WarrantyUntil", "time",
                new List<string> { "%%EXPR%% ((string) d[\"d365:productname\"]).ToUpperInvariant() == \"REPAIR\" ? ((DateTimeOffset) d[\"d365:createdon\"]).AddMonths(6) : ((DateTimeOffset) d[\"d365:createdon\"]).AddYears(2)" }, true),
            new UnifyRule("ProductUrl", "time",
                new List<string> { "%%EXPR%% \"https://imgcdn.apm-monaco.cn/products-images/\" + (string) d[\"d365:productname\"] + \".jpg?x-oss-process=image/resize,w_350,h_350/quality,q_80\"" }, true),
            new UnifyRule("D365SalesOrderId", "time",
                new List<string> { "d365:_salesorderid_value" }, true),
            new UnifyRule("D365ProductId", "time",
                new List<string> { "d365:_productid_value" }, true),
            new UnifyRule("D365ProductVariantId", "time",
                new List<string> { "d365:_thk_variant_code_value" }, true),
            new UnifyRule("D365CurrencyId", "time",
                new List<string> { "d365:_transactioncurrencyid_value" }, true),
            new UnifyRule("Name", "time",
                new List<string> { "d365:thk_variant_code:thk_name" }, true),
            new UnifyRule("Size", "time",
                new List<string> { "d365:thk_variant_code:thk_code" }, true),
            new UnifyRule("PriceCurrency", "time",
                new List<string> { "d365:transactioncurrencyid:isocurrencycode" }, true)
        };

        // @formatter:on
        return salesOrderDefaultUnifyRules;
    }

    private IEnumerable<UnifyRule> GetSalesOrderDefaultUnifyRules()
    {
        // @formatter:off
        var salesOrderDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("TotalPrice", "time",
                new List<string> { "d365:totalamountlessfreight" }, true),
            new UnifyRule("PurchasePlace", "time",
                new List<string> { "d365:thk_type" }, true),
            new UnifyRule("PurchaseStoreId", "time",
                new List<string> { "%%EXPR%% ((string) d[\"d365:thk_type\"]).ToUpperInvariant() == \"RETAIL\" ? d[\"d365:_thk_store_no_value\"] : null" }, true),
            new UnifyRule("PurchaseDate", "time",
                new List<string> { "d365:createdon" }, true),
            new UnifyRule("OrderId", "time",
                new List<string> { "%%EXPR%% string.IsNullOrWhiteSpace((string) d[\"d365:thk_doc_no\"]) ? d[\"d365:thk_doc_no\"] : d[\"d365:apm_invoice_no\"]" }, true),
            new UnifyRule("D365ContactId", "time", new List<string> { "d365:_customerid_value" }, true),
        };

        // @formatter:on
        return salesOrderDefaultUnifyRules;
    }

    private static List<UnifyRule> GetUserDefaultUnifyRules()
    {
        // @formatter:off
        var userDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Username", "time",
                new List<string> { "salesforce-integrator:Username", "sleekflow:UserName", "d365:domainname" }, true),
            new UnifyRule("LastName", "time",
                new List<string> { "salesforce-integrator:LastName", "sleekflow:LastName", "hubspot-integrator:firstName", "d365:lastname" }, true),
            new UnifyRule("FirstName", "time",
                new List<string> { "salesforce-integrator:FirstName", "sleekflow:FirstName", "hubspot-integrator:lastName", "d365:firstname" }, true),
            new UnifyRule("CompanyName", "time",
                new List<string> { "salesforce-integrator:CompanyName", "d365:organizationid" }, true),
            new UnifyRule("Division", "time",
                new List<string> { "salesforce-integrator:Division" }, true),
            new UnifyRule("Department", "time",
                new List<string> { "salesforce-integrator:Department" }, true),
            new UnifyRule("Role", "time",
                new List<string> { "sleekflow:UserRole" }, true),
            new UnifyRule("Title", "time",
                new List<string> { "salesforce-integrator:Title", "sleekflow:Position" }, true),
            new UnifyRule("Email", "time",
                new List<string> { "salesforce-integrator:Email", "sleekflow:Email", "hubspot-integrator:email", "d365:internalemailaddress" }, true),
            new UnifyRule("SenderEmail", "time",
                new List<string> { "salesforce-integrator:SenderEmail", "sleekflow:Email", "hubspot-integrator:email" }, true),
            new UnifyRule("SenderName", "time",
                new List<string> { "salesforce-integrator:SenderName", "sleekflow:Name", "hubspot-integrator:email" }, true),
            new UnifyRule("Phone", "time",
                new List<string> { "salesforce-integrator:Phone", "sleekflow:PhoneNumber", "d365:homephone" }, true),
            new UnifyRule("MobilePhone", "time",
                new List<string> { "salesforce-integrator:MobilePhone", "d365:mobilephone" }, true),
            new UnifyRule("Alias", "time",
                new List<string> { "salesforce-integrator:Alias" }, true),
            new UnifyRule("CommunityNickname", "time",
                new List<string> { "salesforce-integrator:CommunityNickname", "d365:nickname" }, true),
            new UnifyRule("IsActive", "time",
                new List<string> { "salesforce-integrator:IsActive", "salesforce-integrator:IsAcceptedInvitation" }, true),
            new UnifyRule("EmployeeNumber", "time",
                new List<string> { "salesforce-integrator:EmployeeNumber", "d365:employeeid" }, true),
            new UnifyRule("StaffId", "time",
                new List<string> { "sleekflow:StaffId" }, true),
            new UnifyRule("Name", "time",
                new List<string> { "salesforce-integrator:Name", "d365:fullname" }, true),
            new UnifyRule("BadgeText", "time",
                new List<string> { "salesforce-integrator:BadgeText" }, true),
            new UnifyRule("LastLoginDate", "time",
                new List<string> { "salesforce-integrator:LastLoginDate" }, true),
            new UnifyRule("CreatedDate", "time",
                new List<string> { "salesforce-integrator:CreatedDate", "hubspot-integrator:createdAt", "d365:createdon" }, true),
            new UnifyRule("LastModifiedDate", "time",
                new List<string> { "salesforce-integrator:LastModifiedDate", "hubspot-integrator:updatedAt", "d365:modifiedon" }, true),
            new UnifyRule("SystemModstamp", "time",
                new List<string> { "salesforce-integrator:SystemModstamp" }, true),
            new UnifyRule("FullPhotoUrl", "time",
                new List<string> { "salesforce-integrator:FullPhotoUrl", "sleekflow:ProfilePictureUrl" }, true),
            new UnifyRule("SmallPhotoUrl", "time",
                new List<string> { "salesforce-integrator:SmallPhotoUrl", "sleekflow:ProfilePictureUrl" }, true),
            new UnifyRule("LastViewedDate", "time",
                new List<string> { "salesforce-integrator:LastViewedDate" }, true),
            new UnifyRule("LastReferencedDate", "time",
                new List<string> { "salesforce-integrator:LastReferencedDate" }, true),
            new UnifyRule("BannerPhotoUrl", "time",
                new List<string> { "salesforce-integrator:BannerPhotoUrl" }, true),
            new UnifyRule("IsProfilePhotoActive", "time",
                new List<string> { "salesforce-integrator:IsProfilePhotoActive" }, true),
            new UnifyRule("TimeZoneId", "time",
                new List<string> { "sleekflow:TimeZoneId" }, true),
        };

        // @formatter:on
        return userDefaultUnifyRules;
    }

    private static List<UnifyRule> GetOpportunityDefaultUnifyRules()
    {
        // @formatter:off
        var opportunityDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Name", "time",
                new List<string> { "salesforce-integrator:Name", "sleekflow:Name" }, true),
            new UnifyRule("Description", "time",
                new List<string> { "salesforce-integrator:Description", "sleekflow:Description" }, true),
            new UnifyRule("StageName", "time",
                new List<string> { "salesforce-integrator:StageName", "sleekflow:StageName" }, true),
            new UnifyRule("Amount", "time",
                new List<string> { "salesforce-integrator:Amount", "sleekflow:Amount" }, true),
            new UnifyRule("Probability", "time",
                new List<string> { "salesforce-integrator:Probability", "sleekflow:Probability" }, true),
            new UnifyRule("TotalOpportunityQuantity", "time",
                new List<string> { "salesforce-integrator:TotalOpportunityQuantity", "sleekflow:TotalOpportunityQuantity" }, true),
            new UnifyRule("CloseDate", "time",
                new List<string> { "salesforce-integrator:CloseDate", "sleekflow:CloseDate" }, true),
            new UnifyRule("Type", "time",
                new List<string> { "salesforce-integrator:Type", "sleekflow:Type" }, true),
            new UnifyRule("NextStep", "time",
                new List<string> { "salesforce-integrator:NextStep", "sleekflow:NextStep" }, true),
            new UnifyRule("LeadSource", "time",
                new List<string> { "salesforce-integrator:LeadSource", "sleekflow:LeadSource" }, true),
            new UnifyRule("ForecastCategoryName", "time",
                new List<string> { "salesforce-integrator:ForecastCategoryName", "sleekflow:ForecastCategoryName" }, true),
            new UnifyRule("IsDeleted", "time",
                new List<string> { "salesforce-integrator:IsDeleted", "sleekflow:IsDeleted" }, true),
            new UnifyRule("IsClosed", "time",
                new List<string> { "salesforce-integrator:IsClosed", "sleekflow:IsClosed" }, true),
            new UnifyRule("IsWon", "time",
                new List<string> { "salesforce-integrator:IsWon", "sleekflow:IsWon" }, true),
            new UnifyRule("HasOpportunityLineItem", "time",
                new List<string> { "salesforce-integrator:HasOpportunityLineItem", "sleekflow:HasOpportunityLineItem" }, true),
            new UnifyRule("CreatedDate", "time",
                new List<string> { "salesforce-integrator:CreatedDate", "sleekflow:CreatedDate" }, true),
            new UnifyRule("LastModifiedDate", "time",
                new List<string> { "salesforce-integrator:LastModifiedDate", "sleekflow:LastModifiedDate" }, true),
            new UnifyRule("SystemModstamp", "time",
                new List<string> { "salesforce-integrator:SystemModstamp", "sleekflow:SystemModstamp" }, true),
            new UnifyRule("LastActivityDate", "time",
                new List<string> { "salesforce-integrator:LastActivityDate", "sleekflow:LastActivityDate" }, true),
            new UnifyRule("LastStageChangeDate", "time",
                new List<string> { "salesforce-integrator:LastStageChangeDate", "sleekflow:LastStageChangeDate" }, true),
            new UnifyRule("LastViewedDate", "time",
                new List<string> { "salesforce-integrator:LastViewedDate", "sleekflow:LastViewedDate" }, true),
            new UnifyRule("LastReferencedDate", "time",
                new List<string> { "salesforce-integrator:LastReferencedDate", "sleekflow:LastReferencedDate" }, true),
            new UnifyRule("HasOpenActivity", "time",
                new List<string> { "salesforce-integrator:HasOpenActivity", "sleekflow:HasOpenActivity" }, true),
            new UnifyRule("HasOverdueTask", "time",
                new List<string> { "salesforce-integrator:HasOverdueTask", "sleekflow:HasOverdueTask" }, true),
        };

        // @formatter:on
        return opportunityDefaultUnifyRules;
    }

    private static List<UnifyRule> GetLeadDefaultUnifyRules()
    {
        // @formatter:off
        var leadDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("LastName", "time",
                new List<string> { "salesforce-integrator:LastName", "sleekflow:LastName" }, true),
            new UnifyRule("FirstName", "time",
                new List<string> { "salesforce-integrator:FirstName", "sleekflow:FirstName" }, true),
            new UnifyRule("Salutation", "time",
                new List<string> { "salesforce-integrator:Salutation", "sleekflow:Salutation" }, true),
            new UnifyRule("Title", "time",
                new List<string> { "salesforce-integrator:Title", "sleekflow:Title" }, true),
            new UnifyRule("Company", "time",
                new List<string> { "salesforce-integrator:Company", "sleekflow:Company" }, true),
            new UnifyRule("Street", "time",
                new List<string> { "salesforce-integrator:Street", "sleekflow:Street" }, true),
            new UnifyRule("City", "time",
                new List<string> { "salesforce-integrator:City", "sleekflow:City" }, true),
            new UnifyRule("State", "time",
                new List<string> { "salesforce-integrator:State", "sleekflow:State" }, true),
            new UnifyRule("PostalCode", "time",
                new List<string> { "salesforce-integrator:PostalCode", "sleekflow:PostalCode" }, true),
            new UnifyRule("Country", "time",
                new List<string> { "salesforce-integrator:Country", "sleekflow:Country" }, true),
            new UnifyRule("Phone", "time",
                new List<string> { "salesforce-integrator:Phone", "sleekflow:PhoneNumber" }, true),
            new UnifyRule("MobilePhone", "time",
                new List<string> { "salesforce-integrator:MobilePhone", "sleekflow:MobilePhone" }, true),
            new UnifyRule("Fax", "time",
                new List<string> { "salesforce-integrator:Fax", "sleekflow:Fax" }, true),
            new UnifyRule("Email", "time",
                new List<string> { "salesforce-integrator:Email", "sleekflow:Email" }, true),
            new UnifyRule("Website", "time",
                new List<string> { "salesforce-integrator:Website", "sleekflow:Website" }, true),
            new UnifyRule("Description", "time",
                new List<string> { "salesforce-integrator:Description", "sleekflow:Description" }, true),
            new UnifyRule("LeadSource", "time",
                new List<string> { "salesforce-integrator:LeadSource", "sleekflow:LeadSource" }, true),
            new UnifyRule("Status", "time",
                new List<string> { "salesforce-integrator:Status", "sleekflow:Status" }, true),
            new UnifyRule("Industry", "time",
                new List<string> { "salesforce-integrator:Industry", "sleekflow:Industry" }, true),
            new UnifyRule("IsDeleted", "time",
                new List<string> { "salesforce-integrator:IsDeleted", "sleekflow:IsDeleted" }, true),
            new UnifyRule("Rating", "time",
                new List<string> { "salesforce-integrator:Rating", "sleekflow:Rating" }, true),
            new UnifyRule("CleanStatus", "time",
                new List<string> { "salesforce-integrator:CleanStatus", "sleekflow:CleanStatus" }, true),
            new UnifyRule("Name", "time",
                new List<string> { "salesforce-integrator:Name", "sleekflow:Name" }, true),
            new UnifyRule("LastModifiedDate", "time",
                new List<string> { "salesforce-integrator:LastModifiedDate", "sleekflow:UpdatedAt" }, true),
            new UnifyRule("SystemModstamp", "time",
                new List<string> { "salesforce-integrator:SystemModstamp", "sleekflow:CreatedAt" }, true),
            new UnifyRule("LastActivityDate", "time",
                new List<string> { "salesforce-integrator:LastActivityDate", "sleekflow:LastContact" }, true),
            new UnifyRule("LastViewedDate", "time",
                new List<string> { "salesforce-integrator:LastViewedDate", "sleekflow:UpdatedAt" }, true),
            new UnifyRule("LastReferencedDate", "time",
                new List<string> { "salesforce-integrator:LastReferencedDate", "sleekflow:UpdatedAt" }, true),
        };

        // @formatter:on
        return leadDefaultUnifyRules;
    }

    private static List<UnifyRule> GetContactDefaultUnifyRules()
    {
        // @formatter:off
        var contactDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("PhoneNumber", "time",
                new List<string> { "salesforce-integrator:Phone", "sleekflow:PhoneNumber", "hubspot-integrator:phone", "d365:telephone1", "d365:telephone2", "d365:telephone3" }, true),
            new UnifyRule("MobilePhone", "time",
                new List<string> { "salesforce-integrator:MobilePhone", "sleekflow:MobilePhone", "hubspot-integrator:mobilephone", "d365:mobilephone" }, true),
            new UnifyRule("HomePhone", "time",
                new List<string> { "salesforce-integrator:HomePhone", "sleekflow:HomePhone" }, true),
            new UnifyRule("OtherPhone", "time",
                new List<string> { "salesforce-integrator:OtherPhone", "sleekflow:OtherPhone" }, true),
            new UnifyRule("AssistantPhone", "time",
                new List<string> { "salesforce-integrator:AssistantPhone", "sleekflow:AssistantPhone", "d365:assistantphone" }, true),
            new UnifyRule("Email", "time",
                new List<string> { "salesforce-integrator:Email", "sleekflow:Email", "hubspot-integrator:email", "d365:emailaddress1", "d365:emailaddress2", "d365:emailaddress3" }, true),
            new UnifyRule("FirstName", "time",
                new List<string> { "salesforce-integrator:FirstName", "sleekflow:FirstName", "hubspot-integrator:firstname", "d365:firstname" }, true),
            new UnifyRule("LastName", "time",
                new List<string> { "salesforce-integrator:LastName", "sleekflow:LastName", "hubspot-integrator:lastname", "d365:lastname" }, true),

            // APM Monaco Specific
            // new UnifyRule("TotalNumOfPurchaseOrders", "time",
            //     new List<string> { "d365:apm_totalpurchasetimes" }, true),
            // new UnifyRule("TotalNumOfPurchaseProducts", "time",
            //     new List<string> { "d365:new_totalpurchasedquantity" }, true),
            // new UnifyRule("D365LanguageId", "time",
            //     new List<string> { "d365:_new_languageid_value" }, true),
            // new UnifyRule("MemberSince", "time",
            //     new List<string> { "d365:createdon" }, true),
            // new UnifyRule("Generation", "time",
            //     new List<string> { "d365:new_generation" }, true),
            // new UnifyRule("City", "time",
            //     new List<string> { "d365:thk_city" }, true),
            // new UnifyRule("TotalPurchasedAmount", "time",
            //     new List<string> { "%%EXPR%% d[\"d365:new_totalamount_base\"] is double or decimal ? Math.Round(d[\"d365:new_totalamount_base\"], 2) : d[\"d365:new_totalamount_base\"]" }, true),
            // new UnifyRule("LastPurchaseDate", "time",
            //     new List<string> { "d365:new_lastpurchasetime" }, true),
            // new UnifyRule("Address", "time",
            //     new List<string> { "d365:thk_address" }, true),
            // new UnifyRule("D365SalespersonId", "time",
            //     new List<string> { "d365:_new_apm_salesrepid_value" }, true),
            // new UnifyRule("D365AwarenessSourceId", "time",
            //     new List<string> { "d365:_thk_howdoyouknowus_value" }, true)
        };

        // @formatter:on
        return contactDefaultUnifyRules;
    }

    private static List<UnifyRule> GetLanguageDefaultUnifyRules()
    {
        // @formatter:off
        var languageDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Code", "time",
                new List<string> { "d365:new_code" }, true),
            new UnifyRule("Name", "time",
                new List<string> { "d365:new_name" }, true),
        };

        // @formatter:on
        return languageDefaultUnifyRules;
    }

    private static List<UnifyRule> GetProductDefaultUnifyRules()
    {
        // @formatter:off
        var productDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("CollectionName", "time",
                new List<string> { "d365:thk_collection_name" }, true),
            new UnifyRule("ProductType", "time",
                new List<string> { "d365:thk_product_type" }, true),
            new UnifyRule("MetalType", "time",
                new List<string> { "d365:thk_metal_type" }, true)
        };

        // @formatter:on
        return productDefaultUnifyRules;
    }

    private static List<UnifyRule> GetProductVariantDefaultUnifyRules()
    {
        // @formatter:off
        var productVariantDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Name", "time",
                new List<string> { "d365:thk_name" }, true),
            new UnifyRule("Size", "time",
                new List<string> { "d365:thk_code" }, true),
        };

        // @formatter:on
        return productVariantDefaultUnifyRules;
    }

    private static List<UnifyRule> GetCurrencyDefaultUnifyRules()
    {
        // @formatter:off
        var currencyDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("PriceCurrency", "time",
                new List<string> { "d365:isocurrencycode" }, true)
        };

        // @formatter:on
        return currencyDefaultUnifyRules;
    }

    private static List<UnifyRule> GetSalespersonDefaultUnifyRules()
    {
        // @formatter:off
        var salespersonDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Name", "time",
                new List<string> { "d365:thk_name" }, true)
        };

        // @formatter:on
        return salespersonDefaultUnifyRules;
    }

    private static List<UnifyRule> GetAwarenessSourceDefaultUnifyRules()
    {
        // @formatter:off
        var awarenessSourceDefaultUnifyRules = new List<UnifyRule>
        {
            new UnifyRule("Name", "time",
                new List<string> { "d365:thk_name" }, true)
        };

        // @formatter:on
        return awarenessSourceDefaultUnifyRules;
    }

    private static Dictionary<string, UnifyRule> GetFieldNameToUnifyRuleDict(List<UnifyRuleDto> unifyRules)
    {
        var fieldNameToUnifyRuleDict = new Dictionary<string, UnifyRule>();

        foreach (var unifyRule in unifyRules)
        {
            if (fieldNameToUnifyRuleDict.ContainsKey(unifyRule.FieldName))
            {
                throw new SfDuplicateUnifyRuleException(unifyRule.FieldName);
            }

            fieldNameToUnifyRuleDict.Add(
                unifyRule.FieldName,
                new UnifyRule(unifyRule.FieldName, unifyRule.Strategy, unifyRule.ProviderPrecedences, false));
        }

        return fieldNameToUnifyRuleDict;
    }

    private async Task<string> GetOrGenerateIdAsync(UpsertUnifyRulesInput upsertUnifyRulesInput)
    {
        var ruleDefinition =
            await _unifyService.GetAsync(
                upsertUnifyRulesInput.SleekflowCompanyId,
                upsertUnifyRulesInput.EntityTypeName);
        if (ruleDefinition != null)
        {
            return ruleDefinition.Id;
        }

        return _idService.GetId("Unify");
    }
}