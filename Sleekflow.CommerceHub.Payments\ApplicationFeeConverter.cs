using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Payments;

public interface IApplicationFeeConverter
{
    long GetApplicationFee(string sleekflowCompanyId, string currencyCode, decimal totalAmount);
}

public class ApplicationFeeConverter : IApplicationFeeConverter, ISingletonService
{
    public long GetApplicationFee(string sleekflowCompanyId, string currencyCode, decimal totalAmount)
    {
        return currencyCode.ToLowerInvariant() switch
        {
            "usd" => 30,
            "hkd" => 280,
            "myr" => 173,
            "sgd" => 58,
            "gbp" => 29,
            _ => throw new SfInternalErrorException("Not recognized currencyCode")
        };
    }
}