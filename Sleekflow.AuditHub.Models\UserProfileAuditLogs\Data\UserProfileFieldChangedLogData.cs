using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileFieldChangedLogData
{
    [JsonProperty("changed_fields")]
    public List<ChangedFieldLogData> ChangedFields { get; set; }

    [JsonConstructor]
    public UserProfileFieldChangedLogData(List<ChangedFieldLogData> changedFields)
    {
        ChangedFields = changedFields;
    }
}

public class ChangedFieldLogData
{
    [JsonConstructor]
    public ChangedFieldLogData(string fieldName, string value)
    {
        FieldName = fieldName;
        Value = value;
    }

    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }
}