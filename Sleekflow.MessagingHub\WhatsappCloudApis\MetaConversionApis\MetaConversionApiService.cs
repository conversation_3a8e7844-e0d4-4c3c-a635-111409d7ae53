﻿using GraphApi.Client.ApiClients;
using GraphApi.Client.Payloads.ConversionsApi;
using GraphApi.Client.Payloads.Models.ConversionApi;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.MetaConversionApis;

public interface IMetaConversionApiService
{
    Task<GetWabaDatasetResponse> GetWabaDatasetAsync(
        string facebookWabaId,
        string? businessIntegrationSystemUserAccessToken = null);

    Task<CreateWabaDatasetResponse> CreateWabaDatasetAsync(
        string facebookWabaId,
        string facebookDatasetName,
        string? businessIntegrationSystemUserAccessToken = null);

    Task<SendConversionApiEventResponse> SendConversionApiEventAsync(
        string facebookDatasetId,
        SendConversionApiEventObject sendConversionApiEvent,
        string? businessIntegrationSystemUserAccessToken = null);
}

public class MetaConversionApiService : IMetaConversionApiService, ISingletonService
{
    private readonly IMetaConversionApiClient _metaConversionApiClient;
    private readonly IHttpClientFactory _httpClientFactory;

    public MetaConversionApiService(
        ICloudApiClients cloudApiClients,
        IHttpClientFactory httpClientFactory)
    {
        _metaConversionApiClient = cloudApiClients.MetaConversionApiClient;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<GetWabaDatasetResponse> GetWabaDatasetAsync(
        string facebookWabaId,
        string? businessIntegrationSystemUserAccessToken = null)
    {
        if (!string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            await GetMetaConversionApiClient(businessIntegrationSystemUserAccessToken)
                .GetWabaDatasetAsync(facebookWabaId);
        }

        return await _metaConversionApiClient.GetWabaDatasetAsync(facebookWabaId);
    }

    public async Task<CreateWabaDatasetResponse> CreateWabaDatasetAsync(
        string facebookWabaId,
        string facebookDatasetName,
        string? businessIntegrationSystemUserAccessToken = null)
    {
        if (!string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            await GetMetaConversionApiClient(businessIntegrationSystemUserAccessToken)
                .CreateWabaDatasetAsync(facebookWabaId, facebookDatasetName);
        }

        return await _metaConversionApiClient.CreateWabaDatasetAsync(facebookWabaId, facebookDatasetName);
    }

    public async Task<SendConversionApiEventResponse> SendConversionApiEventAsync(
        string facebookDatasetId,
        SendConversionApiEventObject sendConversionApiEvent,
        string? businessIntegrationSystemUserAccessToken = null)
    {
        if (!string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            await GetMetaConversionApiClient(businessIntegrationSystemUserAccessToken)
                .SendConversionApiEventAsync(facebookDatasetId, sendConversionApiEvent);
        }

        return await _metaConversionApiClient.SendConversionApiEventAsync(facebookDatasetId, sendConversionApiEvent);
    }

    private IMetaConversionApiClient GetMetaConversionApiClient(string businessIntegrationSystemUserAccessToken)
    {
        return new MetaConversionApiClient(
            businessIntegrationSystemUserAccessToken,
            _httpClientFactory.CreateClient("default-handler"));
    }
}