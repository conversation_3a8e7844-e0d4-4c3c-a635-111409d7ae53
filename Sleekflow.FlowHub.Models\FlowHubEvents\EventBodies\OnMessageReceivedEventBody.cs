using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.Messages;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnMessageReceivedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnMessageReceived; }
    }

    [Required]
    [JsonProperty("message")]
    public OnMessageReceivedEventBodyMessage Message { get; set; }

    [JsonProperty("channel")]
    public string Channel { get; set; }

    [Required]
    [JsonProperty("channel_id")]
    public string ChannelId { get; set; }

    [Required]
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [Required]
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("message_unique_id")]
    public string MessageUniqueId { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [Required]
    [JsonProperty("is_new_contact")]
    public bool IsNewContact { get; set; }

    [JsonProperty("is_sent_from_sleekflow")]
    public bool IsSentFromSleekflow => false;

    [JsonConstructor]
    public OnMessageReceivedEventBody(
        DateTimeOffset createdAt,
        OnMessageReceivedEventBodyMessage message,
        string channel,
        string channelId,
        string conversationId,
        string messageId,
        string messageUniqueId,
        string contactId,
        Dictionary<string, object?> contact,
        bool isNewContact)
        : base(createdAt)
    {
        Message = message;
        Channel = channel;
        ChannelId = channelId;
        ConversationId = conversationId;
        MessageId = messageId;
        MessageUniqueId = messageUniqueId;
        ContactId = contactId;
        Contact = contact;
        IsNewContact = isNewContact;
    }
}

public class OnMessageReceivedEventBodyMessage : BaseMessage
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("quoted_message")]
    public QuotedMessage? QuotedMessage { get; set; }

    [JsonConstructor]
    public OnMessageReceivedEventBodyMessage(
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        MessageBody messageBody,
        QuotedMessage? quotedMessage,
        string messageType,
        string id,
        string messageContent,
        string messageStatus,
        string messageDeliveryType)
        : base(
            createdAt,
            updatedAt,
            messageBody,
            messageType,
            messageContent,
            messageStatus,
            messageDeliveryType)
    {
        Id = id;
        QuotedMessage = quotedMessage;
        MessageContent = messageContent;
    }
}