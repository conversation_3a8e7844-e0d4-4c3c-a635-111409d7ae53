using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactOwnerRelationshipsStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-owner-relationships";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty("staff_ids__expr")]
    public string? StaffIdsExpr { get; set; }

    [JsonProperty("team_ids__expr")]
    public string? TeamIdsExpr { get; set; }

    [Required]
    [JsonProperty("strategy")]
    public string Strategy { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Conversation;

    [JsonConstructor]
    public UpdateContactOwnerRelationshipsStepArgs(
        string contactIdExpr,
        string? staffIdsExpr,
        string? teamIdsExpr,
        string strategy)
    {
        ContactIdExpr = contactIdExpr;
        StaffIdsExpr = staffIdsExpr;
        TeamIdsExpr = teamIdsExpr;
        Strategy = strategy;
    }
}

public static class UpdateContactOwnerRelationshipsStepArgsStrategy
{
#pragma warning disable SA1310
    public const string Unassigned = "Unassigned";
    public const string Company_RoundRobbin_All_Staffs = "Company_RoundRobbin_All_Staffs";
    public const string RoundRobbin_StaffOnly = "RoundRobbin_StaffOnly";
    public const string RoundRobbin_TeamOnly = "RoundRobbin_TeamOnly";
    public const string Team_And_RoundRobbin_All_Staffs_In_Team = "Team_And_RoundRobbin_All_Staffs_In_Team";
    public const string Team_And_RoundRobbin_Specific_Staffs_In_Team = "Team_And_RoundRobbin_Specific_Staffs_In_Team";
    public const string Team_And_Unassigned_Staff = "Team_And_Unassigned_Staff";
#pragma warning restore SA1310
}