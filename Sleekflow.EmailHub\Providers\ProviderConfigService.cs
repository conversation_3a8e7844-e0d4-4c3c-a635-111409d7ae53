using System.Linq.Expressions;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Companys;
using Sleekflow.EmailHub.Models.Providers;
using Sleekflow.EmailHub.Models.Subscriptions;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.Ids;

namespace Sleekflow.EmailHub.Providers;

public interface IProviderConfigService
{
    Task<ProviderConfig> GetOrCreateProviderConfigAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string providerName,
        EmailSubscriptionMetadata? emailSubscriptionMetadata = null,
        CancellationToken cancellationToken = default);

    public Task<List<ProviderConfig>> GetObjectsAsync(
        Expression<Func<ProviderConfig, bool>> predicate,
        CancellationToken cancellationToken = default);

    public Task UpsertAsync(
        ProviderConfig obj,
        CancellationToken cancellationToken = default);

    Task<List<string>> GetCompanyIdsByEmailAddressAsync(
        string emailAddress,
        CancellationToken cancellationToken = default);

    Task<List<Dictionary<string, string>>> GetEmailAddressesByProviderNameAsync(
        string providerName,
        CancellationToken cancellationToken = default);

    Task<List<CompanyProviderWithEmails>> GetConnectedProviderAsync(
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default);
}

public class ProviderConfigService : IProviderConfigService, IScopedService
{
    private readonly IProviderConfigRepository _providerConfigRepository;
    private readonly IIdService _idService;

    public ProviderConfigService(
        IProviderConfigRepository providerConfigRepository,
        IIdService idService)
    {
        _providerConfigRepository = providerConfigRepository;
        _idService = idService;
    }

    public async Task<ProviderConfig> GetOrCreateProviderConfigAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string providerName,
        EmailSubscriptionMetadata? emailSubscriptionMetadata = null,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = (await _providerConfigRepository.GetObjectsAsync(
            x =>
                x.ProviderName == providerName &&
                x.SleekflowCompanyId == sleekflowCompanyId &&
                x.EmailAddress == emailAddress,
            cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (providerConfig == null)
        {
            var newProviderConfigId = _idService.GetId("ProviderConfig");
            providerConfig = await _providerConfigRepository.CreateAndGetAsync(
                new ProviderConfig(
                newProviderConfigId,
                sleekflowCompanyId,
                providerName,
                emailAddress,
                new EmailSubscription(emailSubscriptionMetadata: emailSubscriptionMetadata)),
                newProviderConfigId,
                cancellationToken);
        }

        return providerConfig;
    }

    public async Task<List<ProviderConfig>> GetObjectsAsync(
        Expression<Func<ProviderConfig, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        return await _providerConfigRepository.GetObjectsAsync(predicate, cancellationToken: cancellationToken);
    }

    public async Task UpsertAsync(ProviderConfig obj, CancellationToken cancellationToken = default)
    {
        await _providerConfigRepository.UpsertAsync(obj, obj.Id, cancellationToken: cancellationToken);
    }

    public async Task<List<string>> GetCompanyIdsByEmailAddressAsync(
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var companyIds = (await _providerConfigRepository.GetObjectsAsync(x => x.EmailAddress == emailAddress, cancellationToken: cancellationToken))
            .Select(x => x.SleekflowCompanyId)
            .ToList();

        return companyIds;
    }

    public async Task<List<Dictionary<string, string>>> GetEmailAddressesByProviderNameAsync(
        string providerName,
        CancellationToken cancellationToken = default)
    {
        return (await _providerConfigRepository.GetObjectsAsync(x => x.ProviderName == providerName, cancellationToken: cancellationToken))
            .Select(x => new Dictionary<string, string>
            {
                {
                    "sleekflow_company_id", x.SleekflowCompanyId
                },
                {
                    "email_address", x.EmailAddress
                }
            })
            .ToList();
    }

    public async Task<List<CompanyProviderWithEmails>> GetConnectedProviderAsync(
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default)
    {
        var emailWithProvider = (await _providerConfigRepository.GetObjectsAsync(
                x => x.SleekflowCompanyId == sleekflowCompanyId,
                cancellationToken: cancellationToken))
            .GroupBy(
                x => x.ProviderName,
                (name, providerConfig) =>
                    new CompanyProviderWithEmails(
                        name,
                        providerConfig
                            .Select(x => x.EmailAddress)
                            .ToList()))
            .ToList();

        return emailWithProvider;
    }
}