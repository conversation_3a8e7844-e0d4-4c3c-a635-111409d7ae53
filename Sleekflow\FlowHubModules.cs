﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Caches;
using Sleekflow.OpenTelemetry.FlowHub;
using Sleekflow.Persistence.FlowHubDb;
using Sleekflow.Persistence.FlowHubIntegrationDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class FlowHubModules
{
    public static void BuildFlowHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<IFlowHubDbConfig>(new Mock<IFlowHubDbConfig>().Object);
        b.AddSingleton<IFlowHubDbResolver>(new Mock<IFlowHubDbResolver>().Object);
#else
        var flowHubDbConfig = new FlowHubDbConfig();

        b.AddSingleton<IFlowHubDbConfig>(flowHubDbConfig);
        b.AddSingleton<IFlowHubDbResolver, FlowHubDbResolver>();
#endif
    }

    public static void BuildFlowHubIntegrationDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<IFlowHubIntegrationDbConfig>(new Mock<IFlowHubIntegrationDbConfig>().Object);
        b.AddSingleton<IFlowHubIntegrationDbResolver>(new Mock<IFlowHubIntegrationDbResolver>().Object);

#else
        var flowHubIntegrationDbConfig = new FlowHubIntegrationDbConfig();

        b.AddSingleton<IFlowHubIntegrationDbConfig>(flowHubIntegrationDbConfig);
        b.AddSingleton<IFlowHubIntegrationDbResolver, FlowHubIntegrationDbResolver>();
#endif
    }

    public static void BuildFlowHubLruMemoryCacheServices(IServiceCollection b, string workflowStepsContainerName, string workflowMetadataContainerName)
    {
#if SWAGGERGEN
        b.AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(_ => new Mock<LruMemoryCacheService>().Object);
        b.AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(_ => new Mock<LruMemoryCacheService>().Object);

#else
        b.AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(
                _ => new LruMemoryCacheService(
                    workflowStepsContainerName,
                    300))
            .AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(
                _ => new LruMemoryCacheService(
                    workflowMetadataContainerName,
                    300));
#endif
    }

    public static void BuildOpenTelemetryServices(IServiceCollection b)
    {
        ModuleUtils.BuildOpenTelemetryServices(b);
#if SWAGGERGEN
        b.AddSingleton<IFlowHubMeters>(new Mock<IFlowHubMeters>().Object);
#else
        b.AddSingleton<IFlowHubMeters, FlowHubMeters>();
#endif
    }
}