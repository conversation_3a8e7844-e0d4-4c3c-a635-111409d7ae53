using System.Reflection;
using MassTransit;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.IntelligentHub;
using Sleekflow.IntelligentHub.Workers.Configs;
using Sleekflow.IntelligentHub.Workers.Consumers;
using Sleekflow.IntelligentHub.Workers.Triggers;
using Sleekflow.JsonConfigs;
using Sleekflow.Mvc;
using Sleekflow.Mvc.Https;

const string name = "SleekflowIntelligentHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildConfigs(services, Assembly.Load("Sleekflow.IntelligentHub"));
        Modules.BuildServices(services, Assembly.Load("Sleekflow.IntelligentHub"));
        Modules.BuildHttpClients(services);
        Modules.BuildDbServices(services);
        Modules.BuildCacheServices(services);
        MvcModules.BuildFuncServices(services, name);
        IntelligentHubModules.BuildIntelligentHubDbServices(services);

        services
            .AddHttpClient("default-intelligent-hub-handler")
            .ConfigureHttpClient((_, client) =>
            {
                client.Timeout = TimeSpan.FromSeconds(60);
                client.MaxResponseContentBufferSize = 1024 * 1024 * 2;
            })
            .ConfigurePrimaryHttpMessageHandler(() => new PrivateNetworkHttpClientHandler
            {
                AllowAutoRedirect = false,
            });
#if SWAGGERGEN
#else
        services.AddSemanticKernelServices();
#endif

        services.AddDurableTaskClient(builder =>
        {
        });

        services.AddSingleton<IAppConfig, AppConfig>();
        services.AddHttpContextAccessor();

        services
            .AddScoped<StartFileIngestionTrigger>()
            .AddScoped<StartUploadToAgentKnowledgeBasesTrigger>()
            .AddScoped<StartWebCrawlingSessionTrigger>()
            .AddScoped<StartWebsiteIngestionTrigger>()
            .AddMassTransitForAzureFunctions(
                cfg =>
                {
                    cfg.AddConsumer(typeof(StartFileIngestionEventConsumer));
                    cfg.AddConsumer(typeof(StartUploadToAgentKnowledgeBasesEventConsumer));
                    cfg.AddConsumer(typeof(StartWebCrawlingSessionEventConsumer));
                    cfg.AddConsumer(typeof(StartWebsiteIngestionEventConsumer));
                },
                "SERVICE_BUS_CONN_STR",
                (context, cfg) =>
                {
                    cfg.UseNewtonsoftJsonSerializer();
                    cfg.UseNewtonsoftJsonDeserializer(true);
                    cfg.ConfigureNewtonsoftJsonSerializer(JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                    cfg.ConfigureNewtonsoftJsonDeserializer(JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                });
    });

hostBuilder.Build().Run();