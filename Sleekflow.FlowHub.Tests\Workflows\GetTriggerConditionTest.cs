using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Triggers.Internals;

namespace Sleekflow.FlowHub.Tests.Triggers.Internals
{
    [TestFixture]
    public class GetTriggerConditionTests
    {
        public class Trigger2 : WorkflowTrigger
        {
            public Trigger2(string condition)
                : base(condition)
            {
            }
        }

        public class Triggers
        {
            [JsonProperty("trigger1")]
            public WorkflowTrigger Trigger1 { get; set; }

            [JsonProperty("trigger2")]
            public WorkflowTrigger Trigger2 { get; set; }

            [JsonProperty("trigger3")]
            public WorkflowTrigger Trigger3 { get; set; }
        }

        // Mock class for WorkflowTriggers with properties decorated with JsonPropertyAttribute
        public class MockWorkflowTriggers
        {
            [JsonProperty("triggers")]
            public Triggers Triggers { get; set; }


            [JsonProperty("others")]
            public Triggers others { get; set; }
        }


        [Test]
        public void FindTriggerProperty_ShouldReturnProperty_WhenTriggerçIdMatches()
        {
            var mockWorkflowTriggers = new MockWorkflowTriggers()
            {
                Triggers = new Triggers()
                {
                    Trigger2 = new Trigger2("new condition"),
                }
            };
            // Arrange
            var getTriggerCondition = new GetTriggerCondition(null, null); // IWorkflowService is not needed for this test
            var triggerId = "trigger2"; // Matches the JsonProperty of Trigger2

            // Act
            var result = getTriggerCondition.GetJsonPropertyNestedValue(mockWorkflowTriggers, "triggers","trigger2");

            var propertyInfo = result.GetType().GetProperty("Condition");

            // Assert
            Assert.IsNotNull(propertyInfo.GetValue(result), "new condition");
        }
    }
}