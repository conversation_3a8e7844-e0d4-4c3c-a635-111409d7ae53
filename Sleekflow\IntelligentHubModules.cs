﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.IntelligentHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class IntelligentHubModules
{
    public static void BuildIntelligentHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON><PERSON><PERSON><PERSON><PERSON><IIntelligentHubDbConfig>(new Mock<IIntelligentHubDbConfig>().Object);
        b.<PERSON><PERSON><IIntelligentHubDbResolver>(new Mock<IIntelligentHubDbResolver>().Object);

#else
        var intelligentHubDbConfig = new IntelligentHubDbConfig();

        b.<PERSON><PERSON><PERSON><PERSON><IIntelligentHubDbConfig>(intelligentHubDbConfig);
        b.<PERSON>d<PERSON><PERSON><PERSON><IIntelligentHubDbResolver, IntelligentHubDbResolver>();
#endif
    }
}