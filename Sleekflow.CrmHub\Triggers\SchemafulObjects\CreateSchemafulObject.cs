﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class CreateSchemafulObject : ITrigger<CreateSchemafulObject.CreateSchemafulObjectInput, CreateSchemafulObject.CreateSchemafulObjectOutput>
{
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public CreateSchemafulObject(
        ISchemaService schemaService,
        ISchemafulObjectService schemafulObjectService)
    {
        _schemaService = schemaService;
        _schemafulObjectService = schemafulObjectService;
    }

    public class CreateSchemafulObjectInput : IHasSleekflowCompanyId, IHasSleekflowUserProfileId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty(SchemafulObject.PropertyNamePrimaryPropertyValue)]
        public string PrimaryPropertyValue { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
        [Validations.ValidateObject]
        public Dictionary<string, object?> PropertyValues { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty(SchemafulObject.PropertyNameCreatedVia)]
        public string CreatedVia { get; set; }

        [JsonConstructor]
        public CreateSchemafulObjectInput(
            string schemaId,
            string sleekflowCompanyId,
            string primaryPropertyValue,
            Dictionary<string, object?> propertyValues,
            string sleekflowUserProfileId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string createdVia)
        {
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
            PrimaryPropertyValue = primaryPropertyValue;
            PropertyValues = propertyValues;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            CreatedVia = createdVia;
        }
    }

    public class CreateSchemafulObjectOutput
    {
        [JsonProperty("schemaful_object")]
        public SchemafulObjectDto SchemafulObject { get; set; }

        [JsonConstructor]
        public CreateSchemafulObjectOutput(SchemafulObjectDto schemafulObject)
        {
            SchemafulObject = schemafulObject;
        }
    }

    public async Task<CreateSchemafulObjectOutput> F(CreateSchemafulObjectInput createSchemafulObjectInput)
    {
        var schema = await _schemaService.GetAsync(
            createSchemafulObjectInput.SchemaId,
            createSchemafulObjectInput.SleekflowCompanyId);

        var schemafulObject = await _schemafulObjectService.CreateAndGetSchemafulObjectAsync(
            schema,
            createSchemafulObjectInput.SleekflowCompanyId,
            createSchemafulObjectInput.PrimaryPropertyValue,
            createSchemafulObjectInput.PropertyValues,
            createSchemafulObjectInput.SleekflowUserProfileId,
            createSchemafulObjectInput.CreatedVia,
            new AuditEntity.SleekflowStaff(
                createSchemafulObjectInput.SleekflowStaffId,
                createSchemafulObjectInput.SleekflowStaffTeamIds));

        return new CreateSchemafulObjectOutput(new SchemafulObjectDto(schemafulObject));
    }
}