using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Models.Fields;

#pragma warning disable SF1003
public class Hu<PERSON>potProperty
#pragma warning restore SF1003
{
    public class Option
    {
        [JsonProperty("hidden")]
        public bool Hidden { get; set; }

        [JsonProperty("label")]
        public string Label { get; set; }

        [JsonProperty("displayOrder")]
        public int DisplayOrder { get; set; }

        [JsonProperty("readOnly")]
        public bool? ReadOnly { get; set; }

        [JsonProperty("doubleData")]
        public double? DoubleData { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonConstructor]
        public Option(
            bool hidden,
            string label,
            int displayOrder,
            bool? readOnly,
            double? doubleData,
            string description,
            string value)
        {
            Hidden = hidden;
            Label = label;
            DisplayOrder = displayOrder;
            ReadOnly = readOnly;
            DoubleData = doubleData;
            Description = description;
            Value = value;
        }
    }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("groupName")]
    public string GroupName { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("fieldType")]
    public string FieldType { get; set; }

    [JsonProperty("fieldLevelPermission")]
    public object FieldLevelPermission { get; set; }

    [JsonProperty("hidden")]
    public bool Hidden { get; set; }

    [JsonProperty("formField")]
    public bool FormField { get; set; }

    [JsonProperty("displayOrder")]
    public int DisplayOrder { get; set; }

    [JsonProperty("readOnlyValue")]
    public bool ReadOnlyValue { get; set; }

    [JsonProperty("readOnlyDefinition")]
    public bool ReadOnlyDefinition { get; set; }

    [JsonProperty("mutableDefinitionNotDeletable")]
    public bool MutableDefinitionNotDeletable { get; set; }

    [JsonProperty("favorited")]
    public bool Favorited { get; set; }

    [JsonProperty("favoritedOrder")]
    public int FavoritedOrder { get; set; }

    [JsonProperty("calculated")]
    public bool Calculated { get; set; }

    [JsonProperty("externalOptions")]
    public bool ExternalOptions { get; set; }

    [JsonProperty("displayMode")]
    public string DisplayMode { get; set; }

    [JsonProperty("showCurrencySymbol")]
    public bool? ShowCurrencySymbol { get; set; }

    [JsonProperty("hubspotDefined")]
    public bool HubspotDefined { get; set; }

    [JsonProperty("referencedObjectType")]
    public string ReferencedObjectType { get; set; }

    [JsonProperty("createdUserId")]
    public object CreatedUserId { get; set; }

    [JsonProperty("searchableInGlobalSearch")]
    public bool SearchableInGlobalSearch { get; set; }

    [JsonProperty("hasUniqueValue")]
    public bool HasUniqueValue { get; set; }

    [JsonProperty("numberDisplayHint")]
    public string NumberDisplayHint { get; set; }

    [JsonProperty("isCustomizedDefault")]
    public bool IsCustomizedDefault { get; set; }

    [JsonProperty("textDisplayHint")]
    public string TextDisplayHint { get; set; }

    [JsonProperty("externalOptionsReferenceType")]
    public string ExternalOptionsReferenceType { get; set; }

    [JsonProperty("optionSortStrategy")]
    public object OptionSortStrategy { get; set; }

    [JsonProperty("optionsAreMutable")]
    public bool? OptionsAreMutable { get; set; }

    [JsonProperty("searchTextAnalysisMode")]
    public string SearchTextAnalysisMode { get; set; }

    [JsonProperty("currencyPropertyName")]
    public object CurrencyPropertyName { get; set; }

    [JsonProperty("updatedUserId")]
    public object UpdatedUserId { get; set; }

    [JsonProperty("options")]
    public List<Option> Options { get; set; }

    [JsonProperty("deleted")]
    public bool? Deleted { get; set; }

    [JsonProperty("updatedAt")]
    public object UpdatedAt { get; set; }

    [JsonProperty("createdAt")]
    public object CreatedAt { get; set; }

    [JsonConstructor]
    public HubspotProperty(
        string name,
        string label,
        string description,
        string groupName,
        string type,
        string fieldType,
        object fieldLevelPermission,
        bool hidden,
        bool formField,
        int displayOrder,
        bool readOnlyValue,
        bool readOnlyDefinition,
        bool mutableDefinitionNotDeletable,
        bool favorited,
        int favoritedOrder,
        bool calculated,
        bool externalOptions,
        string displayMode,
        bool? showCurrencySymbol,
        bool hubspotDefined,
        string referencedObjectType,
        object createdUserId,
        bool searchableInGlobalSearch,
        bool hasUniqueValue,
        string numberDisplayHint,
        bool isCustomizedDefault,
        string textDisplayHint,
        string externalOptionsReferenceType,
        object optionSortStrategy,
        bool? optionsAreMutable,
        string searchTextAnalysisMode,
        object currencyPropertyName,
        object updatedUserId,
        List<Option> options,
        bool? deleted,
        object updatedAt,
        object createdAt)
    {
        Name = name;
        Label = label;
        Description = description;
        GroupName = groupName;
        Type = type;
        FieldType = fieldType;
        FieldLevelPermission = fieldLevelPermission;
        Hidden = hidden;
        FormField = formField;
        DisplayOrder = displayOrder;
        ReadOnlyValue = readOnlyValue;
        ReadOnlyDefinition = readOnlyDefinition;
        MutableDefinitionNotDeletable = mutableDefinitionNotDeletable;
        Favorited = favorited;
        FavoritedOrder = favoritedOrder;
        Calculated = calculated;
        ExternalOptions = externalOptions;
        DisplayMode = displayMode;
        ShowCurrencySymbol = showCurrencySymbol;
        HubspotDefined = hubspotDefined;
        ReferencedObjectType = referencedObjectType;
        CreatedUserId = createdUserId;
        SearchableInGlobalSearch = searchableInGlobalSearch;
        HasUniqueValue = hasUniqueValue;
        NumberDisplayHint = numberDisplayHint;
        IsCustomizedDefault = isCustomizedDefault;
        TextDisplayHint = textDisplayHint;
        ExternalOptionsReferenceType = externalOptionsReferenceType;
        OptionSortStrategy = optionSortStrategy;
        OptionsAreMutable = optionsAreMutable;
        SearchTextAnalysisMode = searchTextAnalysisMode;
        CurrencyPropertyName = currencyPropertyName;
        UpdatedUserId = updatedUserId;
        Options = options;
        Deleted = deleted;
        UpdatedAt = updatedAt;
        CreatedAt = createdAt;
    }
}