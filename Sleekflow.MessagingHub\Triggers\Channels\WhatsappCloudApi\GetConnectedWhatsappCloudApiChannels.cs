using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class GetConnectedWhatsappCloudApiChannels
    : ITrigger<
        GetConnectedWhatsappCloudApiChannels.GetConnectedWhatsappCloudApiChannelsInput,
        GetConnectedWhatsappCloudApiChannels.GetConnectedWhatsappCloudApiChannelsOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetConnectedWhatsappCloudApiChannels> _logger;

    public GetConnectedWhatsappCloudApiChannels(
        IWabaService wabaService,
        ILogger<GetConnectedWhatsappCloudApiChannels> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
    }

    public class GetConnectedWhatsappCloudApiChannelsInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("should_refresh")]
        public bool ShouldRefresh { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public GetConnectedWhatsappCloudApiChannelsInput(
            string sleekflowCompanyId,
            bool shouldRefresh,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ShouldRefresh = shouldRefresh;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class GetConnectedWhatsappCloudApiChannelsOutput
    {
        [JsonProperty("connected_cloud_apis")]
        public List<ConnectedCloudApiChannelDto> ConnectedCloudApis { get; set; }

        [JsonConstructor]
        public GetConnectedWhatsappCloudApiChannelsOutput(List<ConnectedCloudApiChannelDto> connectedCloudApis)
        {
            ConnectedCloudApis = connectedCloudApis;
        }
    }

    public async Task<GetConnectedWhatsappCloudApiChannelsOutput> F(
        GetConnectedWhatsappCloudApiChannelsInput getConnectedWhatsappCloudApiChannelsInput)
    {
        var sleekflowCompanyId = getConnectedWhatsappCloudApiChannelsInput.SleekflowCompanyId;
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            getConnectedWhatsappCloudApiChannelsInput.SleekflowStaffId,
            getConnectedWhatsappCloudApiChannelsInput.SleekflowStaffTeamIds);
        var wabas = await _wabaService.GetAndRefreshWabasAsync(
            sleekflowCompanyId,
            getConnectedWhatsappCloudApiChannelsInput.ShouldRefresh,
            sleekflowStaff,
            null);

        var filteredWabas = wabas.Where(w => CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, w)).ToList();

        filteredWabas = filteredWabas.Select(
            w =>
            {
                w.WabaPhoneNumbers =
                    w.WabaPhoneNumbers.Where(p => p.SleekflowCompanyId == sleekflowCompanyId).ToHashSet();
                return w;
            }).ToList();

        if (filteredWabas.Count == 0)
        {
            _logger.LogWarning(
                "Unable to locate any valid wabas {Wabas}",
                JsonConvert.SerializeObject(wabas));
            throw new SfNotSupportedOperationException("Unable to locate any valid wabas");
        }

        return new GetConnectedWhatsappCloudApiChannelsOutput(
            wabas.Select(
                w
                    => new ConnectedCloudApiChannelDto(w)).ToList());
    }
}