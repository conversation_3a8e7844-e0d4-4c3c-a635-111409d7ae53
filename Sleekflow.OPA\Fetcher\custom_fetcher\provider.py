# blob_fetcher/provider.py
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from opal_common.fetcher.fetch_provider import BaseFetchProvider
from opal_common.fetcher.events import FetcherConfig, FetchEvent
from azure.storage.blob.aio import BlobServiceClient
import json
import asyncio
import logging
import os

logger = logging.getLogger(__name__)

class BlobFetcherConfig(FetcherConfig):
  """
  Configuration for BlobFetcher
  """
  fetcher: str = "BlobFetcher"
  # connection_string: str = Field(
  #   default_factory=lambda: os.environ.get('OPA_DATA_PATH', ''),
  #   description="Azure storage connection string"
  # )
  connection_string: str = Field(
    default_factory=lambda: os.environ.get("OPA_DATA_PATH"),
    description="Azure storage connection string"
  )



  container_name: str = Field(
    default_factory=lambda: os.environ.get("OPA_BLOB_CONTAINER_NAME"),
    description="Azure blob container name"
  )
class BlobFetchEvent(FetchEvent):
  """
  Custom fetch event for the BlobFetcher
  """
  fetcher: str = "BlobFetcher"
  config: BlobFetcherConfig = None

class BlobFetcher(BaseFetchProvider):
  """
  A fetch provider that retrieves all JSON from Azure Blob Storage
  """

  def __init__(self, event: BlobFetchEvent) -> None:
    super().__init__(event)
    self._blob_service: Optional[BlobServiceClient] = None

  def parse_event(self, event: FetchEvent) -> BlobFetchEvent:
    """
    Parse the generic FetchEvent into our custom event type
    """
    return BlobFetchEvent(**event.dict(exclude={"config"}), config=event.config)

  async def __aenter__(self):
    """
    Initialize blob service client
    """
    self._blob_service = BlobServiceClient.from_connection_string(
      self._event.config.connection_string
    )
    return self

  async def __aexit__(self, exc_type=None, exc_val=None, tb=None):
    """
    Cleanup resources
    """
    if self._blob_service:
      await self._blob_service.close()

  async def _fetch_blob_content(self, container_client, blob_name: str) -> Dict:
    """
    Fetch and parse content of a single blob
    """
    try:
      blob_client = container_client.get_blob_client(blob_name)
      stream = await blob_client.download_blob()
      content = await stream.content_as_text()

      # Extract policy name from blob name (remove .json extension)
      policy_name = blob_name.rsplit('.', 1)[0]

      return {
        "name": policy_name,
        "data": json.loads(content)
      }
    except Exception as e:
      logger.error(f"Error fetching blob {blob_name}: {str(e)}")
      return None

  async def _fetch_(self) -> Any:
    """
    Fetch all JSON files from blob storage
    """
    if not self._blob_service:
      raise RuntimeError("Blob service client not initialized")

    try:
      container_client = self._blob_service.get_container_client(
        self._event.config.container_name
      )

      # List all JSON blobs in the container
      blob_list = []
      async for blob in container_client.list_blobs():
        if blob.name.lower().endswith('.json'):
          blob_list.append(blob.name)


      # Fetch all blobs concurrently
      tasks = [
        self._fetch_blob_content(container_client, blob_name)
        for blob_name in blob_list
      ]
      results = await asyncio.gather(*tasks)

      # Filter out None results (failed fetches)
      return [r for r in results if r is not None]

    except Exception as e:
      logger.error(f"Error listing blobs: {str(e)}")
      raise

  async def _process_(self, data: List[Dict]) -> Dict[str, Any]:
    """
    Process the fetched JSON data into policy store format
    """
    if not data:
      return {}

    # Create a merged dictionary where each JSON file becomes a top-level key
    processed_data = {}
    for item in data:
      processed_data[item["name"]] = item["data"]

    return processed_data