﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateGoogleSheetsRowStepExecutor : IStepExecutor
{
}

public class UpdateGoogleSheetsRowStepExecutor
    : GeneralStepExecutor<CallStep<UpdateGoogleSheetsRowStepArgs>>,
        IUpdateGoogleSheetsRowStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public UpdateGoogleSheetsRowStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var updateGoogleSheetsRowInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new UpdateGoogleSheetsRowRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    updateGoogleSheetsRowInput.StateIdentity.SleekflowCompanyId,
                    updateGoogleSheetsRowInput.ConnectionId,
                    updateGoogleSheetsRowInput.SpreadsheetId,
                    updateGoogleSheetsRowInput.WorksheetId,
                    updateGoogleSheetsRowInput.HeaderRowId,
                    updateGoogleSheetsRowInput.RowId,
                    updateGoogleSheetsRowInput.FieldsDict));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnGoogleSheetsFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("GoogleSheets update row operation timed out after 5 minutes")),
                typeof(OnGoogleSheetsFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateGoogleSheetsRowInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("spreadsheet_id")]
        [Required]
        public string SpreadsheetId { get; set; }

        [JsonProperty("worksheet_id")]
        [Required]
        public string WorksheetId { get; set; }

        [JsonProperty("header_row_id")]
        [Required]
        public string HeaderRowId { get; set; }

        [JsonProperty("row_id")]
        [Required]
        public string RowId { get; set; }

        [JsonProperty("fields_dict")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> FieldsDict { get; set; }

        [JsonConstructor]
        public UpdateGoogleSheetsRowInput(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string spreadsheetId,
            string worksheetId,
            string headerRowId,
            string rowId,
            Dictionary<string, object?> fieldsDict)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            SpreadsheetId = spreadsheetId;
            WorksheetId = worksheetId;
            HeaderRowId = headerRowId;
            RowId = rowId;
            FieldsDict = fieldsDict;
        }
    }

    private async Task<UpdateGoogleSheetsRowInput> GetArgs(
        CallStep<UpdateGoogleSheetsRowStepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var spreadsheetId = callStep.Args.SpreadsheetId;
        var worksheetId = callStep.Args.WorksheetId;
        var headerRowId = callStep.Args.HeaderRowId;

        string? rowId;
        var rowSource = callStep.Args.RowSource;
        if (rowSource == "static")
        {
            if (callStep.Args.RowId is null)
            {
                throw new InvalidOperationException("No static row Id provided");
            }

            rowId = callStep.Args.RowId;
        }
        else if (rowSource == "variable")
        {
            if (callStep.Args.RowIdExpr is null)
            {
                throw new InvalidOperationException("No variable row Id provided");
            }

            rowId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.RowIdExpr)
                              ?? throw new InvalidOperationException("Invalid variable row Id"));
        }
        else
        {
            throw new InvalidOperationException(
            $"Invalid row source {rowSource} provided");
        }

        var fieldsKeyExprDict = callStep.Args.FieldsIdExprSet
            .GroupBy(x => x.FieldId)
            .ToDictionary(
                x => x.Key,
                g => g.Last().FieldValueExpr);

        var fieldsDict = new Dictionary<string, object?>();

        foreach (var entry in fieldsKeyExprDict)
        {
            fieldsDict[entry.Key] = string.IsNullOrWhiteSpace(entry.Value)
                ? null
                : await _stateEvaluator.EvaluateExpressionAsync(state, entry.Value);
        }

        return new UpdateGoogleSheetsRowInput(
            state.Id,
            state.Identity,
            connectionId,
            spreadsheetId,
            worksheetId,
            headerRowId,
            rowId!,
            fieldsDict);
    }
}