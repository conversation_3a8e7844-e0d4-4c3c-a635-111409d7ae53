using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.EmailHub.Repositories;

public interface IEmailRepository : IRepository<Email>
{
}

public class EmailRepository
    : BaseRepository<Email>,
        IEmailRepository,
        ISingletonService
{
    public EmailRepository(
        ILogger<EmailRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}