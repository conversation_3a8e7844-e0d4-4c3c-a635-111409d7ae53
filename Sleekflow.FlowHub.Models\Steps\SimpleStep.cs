using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps;

public class SimpleStep : Step
{
    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public SimpleStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId)
        : base(id, name, assign, nextStepId)
    {
    }
}