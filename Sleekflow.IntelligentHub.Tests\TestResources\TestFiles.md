# Test Resources for FileContentExtractionPlugin

This document contains information about test files and their expected outputs for testing the FileContentExtractionPlugin.

## Sample Images with Text Content

### 1. Simple Test Image (Placeholder)
- **URL**: `https://picsum.photos/id/24/800/600`
- **Expected Keywords**: (placeholder image - no text expected)
- **Description**: Simple placeholder image for testing image processing pipeline

### 2. Lorem Picsum Text Image
- **URL**: `https://picsum.photos/id/225/800/600`
- **Expected Keywords**: (placeholder image - minimal text)
- **Description**: Placeholder image that may contain some text elements

### 3. Basic Test Image
- **URL**: `https://picsum.photos/id/102/600/400`
- **Expected Keywords**: (placeholder image - basic processing test)
- **Description**: Basic image for testing image processing capabilities

## Sample Text Files

### 1. README File
- **URL**: `https://raw.githubusercontent.com/microsoft/semantic-kernel/main/README.md`
- **Expected Keywords**: Seman<PERSON>, <PERSON><PERSON>, AI, Microsoft
- **Description**: Technical documentation in markdown format

### 2. JSON Sample Data
- **URL**: `https://jsonplaceholder.typicode.com/posts/1`
- **Expected Keywords**: title, body, userId, id
- **Description**: Sample JSON data from JSONPlaceholder

### 3. CSV Data File
- **URL**: `https://people.sc.fsu.edu/~jburkardt/data/csv/addresses.csv`
- **Expected Keywords**: name, address, city, state
- **Description**: Sample address data in CSV format

### 4. Small Text File
- **URL**: `https://www.w3.org/TR/PNG/iso_8859-1.txt`
- **Expected Keywords**: character, encoding, ISO, 8859
- **Description**: Simple text file for basic text processing

## Sample Audio Files (Binary Analysis)

### 1. HTTP Test Audio (Simulated)
- **URL**: `https://httpbin.org/base64/aGVsbG8gd29ybGQ%3D`
- **Expected Keywords**: hello, world, base64, binary
- **Description**: Base64 encoded test data for binary analysis testing

### 2. Test Audio Data
- **URL**: `https://httpbin.org/bytes/1024`
- **Expected Keywords**: binary, data, bytes, random
- **Description**: Random binary data for audio file simulation

## Sample Video Files (Binary Analysis)

### 1. Binary Test Data
- **URL**: `https://httpbin.org/bytes/2048`
- **Expected Keywords**: binary, data, video, bytes
- **Description**: Binary data for video file simulation

## Large Text Files (Summarization Testing)

### 1. Classic Literature
- **URL**: `https://www.gutenberg.org/files/1342/1342-0.txt`
- **Expected Keywords**: Pride, Prejudice, novel, summary, story
- **Description**: Full text of "Pride and Prejudice" for large file summarization testing
- **Expected Behavior**: Should be summarized to under 2000 characters

### 2. Technical Documentation (Alternative)
- **URL**: `https://raw.githubusercontent.com/microsoft/semantic-kernel/main/CONTRIBUTING.md`
- **Expected Keywords**: contributing, development, guidelines, code
- **Description**: Large technical document for summarization testing

### 3. Public Domain Text
- **URL**: `https://www.gutenberg.org/files/11/11-0.txt`
- **Expected Keywords**: Alice, Wonderland, story, chapter
- **Description**: Alice's Adventures in Wonderland for large text processing

## Error Test Cases

### 1. Invalid URLs
- **URL**: `https://invalid-url-that-does-not-exist.com/file.txt`
- **Expected**: HttpRequestException or similar network error

### 2. Malformed URLs
- **URL**: `not-a-valid-url`
- **Expected**: UriFormatException or InvalidOperationException

### 3. HTTP Error Codes
- **URL**: `https://httpbin.org/status/404`
- **Expected**: Should handle 404 Not Found gracefully

### 4. Large Binary Files (Size Test)
- **URL**: `https://httpbin.org/bytes/10485760`
- **Expected**: Should handle large files gracefully, possibly with timeout or size limits

## Working Test URLs (Verified Reliable)

### Stable API Endpoints
- **JSONPlaceholder**: `https://jsonplaceholder.typicode.com/` - Reliable JSON test data
- **HTTPBin**: `https://httpbin.org/` - HTTP testing service
- **Lorem Picsum**: `https://picsum.photos/` - Placeholder images
- **Project Gutenberg**: `https://www.gutenberg.org/` - Public domain texts
- **GitHub Raw**: `https://raw.githubusercontent.com/` - Direct file access

## Expected Behaviors

### Image Text Extraction
- Should use GPT-4o vision capabilities
- Should extract readable text from images
- Should handle various image formats (PNG, JPEG, GIF, BMP, WebP)
- Should return meaningful content or indicate if no text found
- Note: Placeholder images may not contain extractable text

### Audio File Processing
- Should recognize as binary/audio file
- Should provide basic file analysis using LLM
- Should not attempt OCR (since it's audio)
- Should return descriptive information about the file type

### Text File Processing
- Should return direct content for small files
- Should summarize large files (>10KB)
- Should maintain important information in summaries
- Should handle various text formats (TXT, CSV, JSON, XML, HTML, etc.)

### Error Handling
- Should gracefully handle network errors
- Should timeout on excessively large files
- Should provide meaningful error messages
- Should not crash on malformed input

## Test Validation Criteria

### Content Quality
1. **Relevance**: Extracted content should be relevant to the original file
2. **Accuracy**: OCR results should capture the main text elements
3. **Completeness**: Important information should not be omitted
4. **Clarity**: Extracted text should be readable and well-formatted

### Performance
1. **Speed**: Should complete within reasonable time limits (30-60 seconds max)
2. **Resource Usage**: Should not consume excessive memory
3. **Reliability**: Should consistently produce similar results for the same input

### Error Handling
1. **Graceful Degradation**: Should handle errors without crashing
2. **Informative Messages**: Error messages should be helpful for debugging
3. **Recovery**: Should continue processing other files if one fails in batch operations

## Notes

- All URLs have been updated to use reliable, stable services
- Placeholder image URLs may not contain extractable text content
- JSON and text file URLs should provide consistent, testable content
- Error test cases are designed to validate proper exception handling
- Large file tests use Project Gutenberg's stable public domain texts