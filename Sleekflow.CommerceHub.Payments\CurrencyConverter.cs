using Newtonsoft.Json;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Payments;

public interface ICurrencyConverter
{
    Task<decimal> ConvertToUsdAsync(decimal amount, string currency);
}

public class CurrencyConverter : ICurrencyConverter, ISingletonService
{
    private readonly HttpClient _httpClient;

    public CurrencyConverter(
        IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<decimal> ConvertToUsdAsync(decimal amount, string currency)
    {
        var c = currency.ToUpperInvariant();
        if (c == "USD")
        {
            return amount;
        }

        var response = await _httpClient.GetAsync(
            $"https://query1.finance.yahoo.com/v7/finance/quote?symbols={c}USD=X");
        response.EnsureSuccessStatusCode();

        var responseContent = await response.Content.ReadAsStringAsync();
        var responseObject = JsonConvert.DeserializeObject<YahooFinanceResponse>(responseContent)!;

        var exchangeRate = responseObject.QuoteResponse.Result[0].RegularMarketPrice;

        return amount * exchangeRate;
    }

    public class YahooFinanceResponse
    {
        [JsonProperty("quoteResponse")]
        public QuoteResponse QuoteResponse { get; set; }

        [JsonConstructor]
        public YahooFinanceResponse(QuoteResponse quoteResponse)
        {
            QuoteResponse = quoteResponse;
        }
    }

    public class QuoteResponse
    {
        [JsonProperty("result")]
        public List<Result> Result { get; set; }

        public QuoteResponse(List<Result> result)
        {
            Result = result;
        }
    }

    public class Result
    {
        [JsonProperty("regularMarketPrice")]
        public decimal RegularMarketPrice { get; set; }

        [JsonConstructor]
        public Result(decimal regularMarketPrice)
        {
            RegularMarketPrice = regularMarketPrice;
        }
    }
}