﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

public record VtexOrderSellerDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("logo")]
    public string Logo { get; set; }

    [JsonProperty("fulfillmentEndpoint")]
    public string FulfillmentEndpoint { get; set; }

    [JsonConstructor]
    public VtexOrderSellerDto(string id, string name, string logo, string fulfillmentEndpoint)
    {
        Id = id;
        Name = name;
        Logo = logo;
        FulfillmentEndpoint = fulfillmentEndpoint;
    }
}