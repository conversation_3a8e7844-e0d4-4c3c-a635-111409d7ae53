using GraphApi.Client.Payloads;
using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;

public class ProductItemDto
{
    [JsonProperty("facebook_product_id")]
    public string FacebookProductId { get; set; }

    [JsonProperty("retailer_id")]
    public string RetailerId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("retailer_product_group_id")]
    public string? RetailerProductGroupId { get; set; }

    [JsonProperty("image_url")]
    public string? ImageUrl { get; set; }

    [JsonProperty("images")]
    public List<string>? Images { get; set; }

    [JsonProperty("additional_image_urls")]
    public List<string>? AdditionalImageUrls { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("short_description")]
    public string? ShortDescription { get; set; }

    [JsonProperty("product_catalog")]
    public FacebookProductCatalogDto? ProductCatalog { get; set; } // TODO Replace WabaProductCatalog class with smth else

    [JsonProperty("product_type")]
    public string? ProductType { get; set; }

    [JsonProperty("quantity_to_sell_on_facebook")]
    public int? QuantityToSellOnFacebook { get; set; }

    [JsonProperty("inventory")]
    public int? Inventory { get; set; }

    [JsonProperty("review_status")]
    public string? ReviewStatus { get; set; }

    [JsonProperty("review_rejection_reasons")]
    public List<string>? ReviewRejectReasons { get; set; }

    [JsonProperty("price")]
    public string? Price { get; set; }

    [JsonProperty("currency")]
    public string? Currency { get; set; }

    [JsonProperty("availability")]
    public string? Availability { get; set; }

    [JsonProperty("condition")]
    public string? Condition { get; set; }

    [JsonProperty("brand")]
    public string? Brand { get; set; }

    [JsonConstructor]
    public ProductItemDto(
        string facebookProductId,
        string retailerId,
        string name,
        string? retailerProductGroupId,
        string? imageUrl,
        List<string>? images,
        List<string>? additionalImageUrls,
        string? url,
        string? description,
        string? shortDescription,
        FacebookProductCatalogDto? facebookProductCatalog,
        string? productType,
        int? quantityToSellOnFacebook,
        int? inventory,
        string? reviewStatus,
        List<string>? reviewRejectReasons,
        string? price,
        string? currency,
        string? availability,
        string? condition,
        string? brand)
    {
        FacebookProductId = facebookProductId;
        RetailerId = retailerId;
        Name = name;
        RetailerProductGroupId = retailerProductGroupId;
        ImageUrl = imageUrl;
        Images = images;
        AdditionalImageUrls = additionalImageUrls;
        Url = url;
        Description = description;
        ShortDescription = shortDescription;
        ProductCatalog = facebookProductCatalog;
        ProductType = productType;
        QuantityToSellOnFacebook = quantityToSellOnFacebook;
        Inventory = inventory;
        ReviewStatus = reviewStatus;
        ReviewRejectReasons = reviewRejectReasons;
        Price = price;
        Currency = currency;
        Availability = availability;
        Condition = condition;
        Brand = brand;
    }

    public ProductItemDto(GetProductItemResponse getProductItemResponse, WabaProductCatalog wabaProductCatalog)
        : this(
            getProductItemResponse.Id,
            getProductItemResponse.RetailerId,
            getProductItemResponse.Name,
            getProductItemResponse.RetailerProductGroupId,
            getProductItemResponse.ImageUrl,
            getProductItemResponse.Images,
            getProductItemResponse.AdditionalImageUrls,
            getProductItemResponse.Url,
            getProductItemResponse.Description,
            getProductItemResponse.ShortDescription,
            new FacebookProductCatalogDto(wabaProductCatalog),
            getProductItemResponse.ProductType,
            getProductItemResponse.QuantityToSellOnFacebook,
            getProductItemResponse.Inventory,
            getProductItemResponse.ReviewStatus,
            getProductItemResponse.ReviewRejectReasons,
            getProductItemResponse.Price,
            getProductItemResponse.Currency,
            getProductItemResponse.Availability,
            getProductItemResponse.Condition,
            getProductItemResponse.Brand)
    {
    }

    public ProductItemDto(GetProductItemResponse getProductItemResponse)
        : this(
            getProductItemResponse.Id,
            getProductItemResponse.RetailerId,
            getProductItemResponse.Name,
            getProductItemResponse.RetailerProductGroupId,
            getProductItemResponse.ImageUrl,
            getProductItemResponse.Images,
            getProductItemResponse.AdditionalImageUrls,
            getProductItemResponse.Url,
            getProductItemResponse.Description,
            getProductItemResponse.ShortDescription,
            null,
            getProductItemResponse.ProductType,
            getProductItemResponse.QuantityToSellOnFacebook,
            getProductItemResponse.Inventory,
            getProductItemResponse.ReviewStatus,
            getProductItemResponse.ReviewRejectReasons,
            getProductItemResponse.Price,
            getProductItemResponse.Currency,
            getProductItemResponse.Availability,
            getProductItemResponse.Condition,
            getProductItemResponse.Brand)
    {
    }
}