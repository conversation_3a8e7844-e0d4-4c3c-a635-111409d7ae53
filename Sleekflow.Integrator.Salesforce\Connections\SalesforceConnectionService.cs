using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Salesforce.Connections;

public interface ISalesforceConnectionService
{
    Task<List<SalesforceConnection>> GetConnectionsAsync(
        string sleekflowCompanyId);

    Task<SalesforceConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string organizationId,
        string authenticationId,
        string name,
        string environment,
        bool isActive);

    Task<SalesforceConnection> GetByIdAsync(
        string id,
        string sleekflowCompanyId);

    Task<SalesforceConnection?> GetByAuthenticationIdAsync(
        string sleekflowCompanyId,
        string authenticationId);

    Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        string name,
        bool isActive);

    Task<SalesforceConnection> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string name);

    Task DeleteAsync(
        string id,
        string sleekflowCompanyId);
}

public class SalesforceConnectionService : ISingletonService, ISalesforceConnectionService
{
    private readonly ISalesforceConnectionRepository _salesforceConnectionRepository;
    private readonly IIdService _idService;

    public SalesforceConnectionService(
        ISalesforceConnectionRepository salesforceConnectionRepository,
        IIdService idService)
    {
        _salesforceConnectionRepository = salesforceConnectionRepository;
        _idService = idService;
    }

    public async Task<List<SalesforceConnection>> GetConnectionsAsync(string sleekflowCompanyId)
    {
        return await _salesforceConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<SalesforceConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string organizationId,
        string authenticationId,
        string name,
        string environment,
        bool isActive)
    {
        var connection = new SalesforceConnection(
            _idService.GetId("SalesforceConnection"),
            sleekflowCompanyId,
            organizationId,
            authenticationId,
            name,
            environment,
            isActive);

        return await _salesforceConnectionRepository.CreateAndGetAsync(connection, sleekflowCompanyId);
    }

    public async Task<SalesforceConnection> GetByIdAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await _salesforceConnectionRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<SalesforceConnection?> GetByAuthenticationIdAsync(
        string sleekflowCompanyId,
        string authenticationId)
    {
        var connections = await _salesforceConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId && x.AuthenticationId == authenticationId);

        return connections.FirstOrDefault();
    }

    public async Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        string name,
        bool isActive)
    {
        await _salesforceConnectionRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
                PatchOperation.Replace("/is_active", isActive),
            });
    }

    public async Task<SalesforceConnection> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string name)
    {
        return await _salesforceConnectionRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
            });
    }

    public async Task DeleteAsync(
        string id,
        string sleekflowCompanyId)
    {
        var connection = await _salesforceConnectionRepository.GetAsync(
            id,
            sleekflowCompanyId);
        if (connection is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var deleteAsync = await _salesforceConnectionRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the SalesforceConnection with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}