using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class DeactivateProvider : ITrigger
{
    private readonly ILogger<DeactivateProvider> _logger;
    private readonly IProviderSelector _providerSelector;
    private readonly IProviderConfigService _providerConfigService;

    public DeactivateProvider(
        ILogger<DeactivateProvider> logger,
        IProviderSelector providerSelector,
        IProviderConfigService providerConfigService)
    {
        _logger = logger;
        _providerSelector = providerSelector;
        _providerConfigService = providerConfigService;
    }

    public class DeactivateProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public DeactivateProviderInput(
            string sleekflowCompanyId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
        }
    }

    public class DeactivateProviderOutput
    {
        [JsonConstructor]
        public DeactivateProviderOutput()
        {
        }
    }

    public async Task<DeactivateProviderOutput> F(
        DeactivateProviderInput deactivateProviderInput)
    {
        var entity = await _providerConfigService.GetProviderConfigAsync(
            deactivateProviderInput.SleekflowCompanyId,
            deactivateProviderInput.ProviderName);

        var entityTypeNames = entity.EntityTypeNameToSyncConfigDict.Select(x => x.Key).ToList();

        var count = await _providerConfigService.DeleteProviderConfigAsync(
            entity.Id,
            deactivateProviderInput.SleekflowCompanyId);
        if (count == 0)
        {
            throw new SfNotFoundObjectException("No provider config is found");
        }

        var providerService = _providerSelector.GetProviderService(deactivateProviderInput.ProviderName);

        foreach (var entityTypeName in entityTypeNames)
        {
            try
            {
                await providerService.DeactivateTypeSyncAsync(
                    deactivateProviderInput.SleekflowCompanyId,
                    entityTypeName);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Unable to DeactivateTypeSync");
            }
        }

        return new DeactivateProviderOutput();
    }
}