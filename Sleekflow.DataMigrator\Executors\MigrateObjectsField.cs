using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

public class MigrateObjectsField : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private string? _containerId;

    private string? _sourceFieldName;

    private string? _destinationFieldName;

    public MigrateObjectsField(DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Migrate Objects Field";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerId = Prompt.Select(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerId = selectedContainerId;

        _sourceFieldName = Prompt.Input<string>("What's your source field name?");
        _destinationFieldName = Prompt.Input<string>("What's your destination field name?");
    }

    public async Task ExecuteAsync()
    {
        if (!string.IsNullOrEmpty(_containerId) && !string.IsNullOrEmpty(_sourceFieldName) && !string.IsNullOrEmpty(_destinationFieldName))
        {
            var count = await MigrateObjectFieldAsync();
            Console.WriteLine($"Completed {_containerId} for {count} objects");
        }
        else
        {
            Console.WriteLine("No Objects Fields Can Be Migrated.");
        }
    }

    private async Task<int> MigrateObjectFieldAsync()
    {
        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(_containerId);
        var partitionKeyPaths =
            await CosmosUtils.GetContainerPartitionKeyPathsAsync(_cosmosClient, _databaseId!, _containerId!);

        var i = 0;

        await foreach (var dictionary in CosmosUtils.GetObjectsAsync(container))
        {
            var partitionKeyBuilder = new PartitionKeyBuilder();
            foreach (var partitionKeyPath in partitionKeyPaths!)
            {
                partitionKeyBuilder.Add((string?) dictionary[partitionKeyPath.TrimStart('/')]);
            }

            if (!dictionary.TryGetValue(_sourceFieldName!, out var sourceFieldValue))
            {
                continue;
            }

            if (sourceFieldValue == null)
            {
                continue;
            }

            dictionary[_destinationFieldName!] = sourceFieldValue;

            await container.UpsertItemAsync(
                dictionary,
                partitionKeyBuilder.Build(),
                new ItemRequestOptions
                {
                    EnableContentResponseOnWrite = false,
                });

            Interlocked.Increment(ref i);

            if (i % 1000 == 0)
            {
                Console.WriteLine("In Progress " + i);
            }
        }

        return i;
    }
}