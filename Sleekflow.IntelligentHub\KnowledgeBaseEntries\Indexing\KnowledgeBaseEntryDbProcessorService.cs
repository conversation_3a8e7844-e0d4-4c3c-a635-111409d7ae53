using Microsoft.Azure.Cosmos;
using Sleekflow.Constants;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.KnowledgeBaseEntries.Indexing;

public interface IKnowledgeBaseEntryDbProcessorService
{
}

public class KnowledgeBaseEntryDbProcessorService : BackgroundService, IKnowledgeBaseEntryDbProcessorService
{
    private readonly List<ChangeFeedProcessor> _changeFeedProcessors = new List<ChangeFeedProcessor>();
    private readonly ILogger<KnowledgeBaseEntryDbProcessorService> _logger;
    private readonly IIntelligentHubDbResolver _intelligentHubDbResolver;
    private readonly IDbProcessorConfig _dbProcessorConfig;
    private readonly IKnowledgeBaseEntryIndexingService _knowledgeBaseEntryIndexingService;

    public KnowledgeBaseEntryDbProcessorService(
        ILogger<KnowledgeBaseEntryDbProcessorService> logger,
        IIntelligentHubDbResolver intelligentHubDbResolver,
        IDbProcessorConfig dbProcessorConfig,
        IKnowledgeBaseEntryIndexingService knowledgeBaseEntryIndexingService)
    {
        _logger = logger;
        _intelligentHubDbResolver = intelligentHubDbResolver;
        _dbProcessorConfig = dbProcessorConfig;
        _knowledgeBaseEntryIndexingService = knowledgeBaseEntryIndexingService;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var knowledgeBaseEntryContainerName = ContainerNames.KnowledgeBaseEntry;
        _logger.LogInformation(
            "Starting Change Feed Processor for changeFeedHandler.GetEntityTypeName {EntityTypeName}",
            knowledgeBaseEntryContainerName);

        var knowledgeBaseEntryProcessorName =
            $"{knowledgeBaseEntryContainerName.ToLower()}-change-feed-processor-{_dbProcessorConfig.CosmosChangeFeedEnvId}";
        var container = _intelligentHubDbResolver
            .Resolve(ContainerNames.DatabaseId, ContainerNames.KnowledgeBaseEntry);

        var knowledgeBaseEntryChangeFeedProcessor = container.GetChangeFeedProcessorBuilder<KnowledgeBaseEntry>(
                processorName: knowledgeBaseEntryProcessorName,
                onChangesDelegate: async (_, changes, _) =>
                {
                    var deletedChanges = changes
                        .Where(
                            p =>
                                p.SysTypeName == SysTypeNames.KnowledgeBaseEntry
                                && p.RecordStatuses.Contains(RecordStatuses.Deleted))
                        .ToList();

                    if (deletedChanges.Any())
                    {
                        var deleteIndexes = deletedChanges
                            .GroupBy(c => c.SleekflowCompanyId)
                            .ToDictionary(
                                group => group.Key,
                                group => group.ToList());
                        await _knowledgeBaseEntryIndexingService.DeleteEntryIndexesAsync(deleteIndexes);
                    }

                    var updatedChanges = changes
                        .Where(
                            p =>
                                p.SysTypeName == SysTypeNames.KnowledgeBaseEntry
                                && p.RecordStatuses.Contains(RecordStatuses.Active))
                        .ToList();
                    if (updatedChanges.Any())
                    {
                        var updateIndexes = updatedChanges
                            .GroupBy(c => c.SleekflowCompanyId)
                            .ToDictionary(
                                group => group.Key,
                                group => group.ToList());

                        // create index for cognitive search
                        foreach (var knowledgeBaseEntry in updatedChanges)
                        {
                            await _knowledgeBaseEntryIndexingService.CreateSearchIndexAsync(
                                knowledgeBaseEntry.SleekflowCompanyId);
                        }

                        await _knowledgeBaseEntryIndexingService.IndexEntriesAsync(updateIndexes);
                    }
                })
            .WithMaxItems(50)
            .WithPollInterval(TimeSpan.FromSeconds(60))
            .WithInstanceName(Environment.MachineName)
            .WithLeaseContainer(_intelligentHubDbResolver.Resolve(ContainerNames.DatabaseId, "sys_changefeed_lease"))
            .Build();

        try
        {
            await knowledgeBaseEntryChangeFeedProcessor.StartAsync();

            _logger.LogInformation(
                "Started Change Feed Processor for changeFeedHandler.GetTableName {TableName}",
                knowledgeBaseEntryContainerName);

            _changeFeedProcessors.Add(knowledgeBaseEntryChangeFeedProcessor);
        }
        catch (TaskCanceledException e)
        {
            _logger.LogError(e, "There is an exception thrown");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        {
            foreach (var processor in _changeFeedProcessors)
            {
                try
                {
                    await processor.StopAsync();
                }
                catch (Exception e)
                {
                    _logger.LogError(e.Message);
                }
            }
        }
    }
}