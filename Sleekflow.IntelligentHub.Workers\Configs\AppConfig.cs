using Microsoft.Extensions.Configuration;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Workers.Configs;

public interface IAppConfig
{
    string IntelligentHubInternalsEndpoint { get; }

    string InternalsKey { get; }
}

public class AppConfig : IAppConfig
{
    public string IntelligentHubInternalsEndpoint { get; }

    public string InternalsKey { get; }

    public AppConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        IntelligentHubInternalsEndpoint =
            Environment.GetEnvironmentVariable("INTELLIGENT_HUB_INTERNALS_ENDPOINT", target)
            ?? configuration["INTELLIGENT_HUB_INTERNALS_ENDPOINT"];
        InternalsKey =
            Environment.GetEnvironmentVariable("INTERNALS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("INTERNALS_KEY");
    }
}