using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Subscriptions;

[TriggerGroup(ControllerNames.Subscriptions)]
public class SubscribeProviderEmailAddress : ITrigger
{
    private readonly IEmailProviderSelector _emailProviderSelector;

    public SubscribeProviderEmailAddress(IEmailProviderSelector emailProviderSelector)
    {
        _emailProviderSelector = emailProviderSelector;
    }

    public class SubscribeProviderEmailAddressInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email_address")]
        [Required]
        public string EmailAddress { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("subscription_metadata")]
        public Dictionary<string, string>? SubscriptionMetadata { get; set; }

        [JsonConstructor]
        public SubscribeProviderEmailAddressInput(
            string sleekflowCompanyId,
            string emailAddress,
            string providerName,
            Dictionary<string, string> subscriptionMetadata)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EmailAddress = emailAddress;
            ProviderName = providerName;
            SubscriptionMetadata = subscriptionMetadata;
        }
    }

    public class SubscribeProviderEmailAddressOutput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email_address")]
        [Required]
        public string EmailAddress { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public SubscribeProviderEmailAddressOutput(
            string sleekflowCompanyId,
            string providerName,
            string emailAddress)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EmailAddress = emailAddress;
            ProviderName = providerName;
        }
    }

    public async Task<SubscribeProviderEmailAddressOutput> F(
        SubscribeProviderEmailAddressInput subscribeProviderEmailAddressInput)
    {
        var emailProvider = _emailProviderSelector.GetEmailProvider(subscribeProviderEmailAddressInput.ProviderName);
        var subscribeProviderOutput = await emailProvider.SubscribeProviderAsync(
            subscribeProviderEmailAddressInput.SleekflowCompanyId,
            subscribeProviderEmailAddressInput.EmailAddress,
            subscribeProviderEmailAddressInput.SubscriptionMetadata);

        return new SubscribeProviderEmailAddressOutput(
            subscribeProviderOutput.SleekflowCompanyId,
            subscribeProviderOutput.ProviderName,
            subscribeProviderOutput.EmailAddress);
    }
}