﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.ShareHubDb;

public interface IShareHubDbResolver : IContainerResolver
{
}

public class ShareHubDbResolver : IShareHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public ShareHubDbResolver(IShareHubDbConfig shareHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            shareHubDbConfig.Endpoint,
            shareHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}