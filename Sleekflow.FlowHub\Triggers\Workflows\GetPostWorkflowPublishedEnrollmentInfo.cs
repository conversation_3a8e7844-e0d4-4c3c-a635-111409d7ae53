using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Scriban;
using Scriban.Functions;
using Scriban.Runtime;
using Scriban.Syntax;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.NeedConfigs;
using Sleekflow.FlowHub.States.Functions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public partial class GetPostWorkflowPublishedEnrollmentInfo : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly INeedConfigService _needConfigService;

    [GeneratedRegex("^{{(.*)}}$")]
    private static partial Regex DoubleCurlyBracesRegex();

    public GetPostWorkflowPublishedEnrollmentInfo(
        IWorkflowService workflowService,
        INeedConfigService needConfigService)
    {
        _workflowService = workflowService;
        _needConfigService = needConfigService;
    }

    public class GetPostWorkflowPublishedEnrollmentInfoInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetPostWorkflowPublishedEnrollmentInfoInput(
            string workflowVersionedId,
            string sleekflowCompanyId)
        {
            WorkflowVersionedId = workflowVersionedId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetPostWorkflowPublishedEnrollmentInfoOutput
    {
        [JsonProperty("enroll_request_path")]
        public string EnrollRequestPath { get; set; }

        [JsonProperty("enroll_progress_path")]
        public string EnrollProgressPath { get; set; }

        [JsonProperty("request_body_args")]
        public PostWorkflowPublishedEnrollmentRequestBodyArgs RequestBodyArgs { get; set; }

        [JsonConstructor]
        public GetPostWorkflowPublishedEnrollmentInfoOutput(
            string enrollRequestPath,
            string enrollProgressPath,
            PostWorkflowPublishedEnrollmentRequestBodyArgs requestBodyArgs)
        {
            EnrollRequestPath = enrollRequestPath;
            EnrollProgressPath = enrollProgressPath;
            RequestBodyArgs = requestBodyArgs;
        }
    }

    public async Task<GetPostWorkflowPublishedEnrollmentInfoOutput> F(GetPostWorkflowPublishedEnrollmentInfoInput input)
    {
        var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId);

        if (workflow == null)
        {
            throw new InvalidOperationException($"Workflow with versioned ID {input.WorkflowVersionedId} not found");
        }

        if (workflow.ActivationStatus != WorkflowActivationStatuses.Active)
        {
            throw new SfWorkflowNotActiveException();
        }

        if (workflow.ManualEnrollmentSource != "external_integration_loop_through")
        {
            throw new InvalidOperationException($"Workflow with versioned ID {input.WorkflowVersionedId} doesn't support manual enrollment");
        }

        var eventV1 = WorkflowMetadataUtils.TryExtractEventMetadataFromWorkflow(workflow.Metadata)
            ?? throw new InvalidOperationException($"Workflow with versioned ID {input.WorkflowVersionedId} doesn't have event in metadata");

        var postEnrollmentConfigs = await _needConfigService.GetPostWorkflowPublishedEnrollmentConfigsAsync(
            null,
            eventV1.Id)
            ?? throw new InvalidOperationException($"Unable to find post-enrollment config for workflow {input.WorkflowVersionedId}");

        var config = postEnrollmentConfigs.Find(x => x.TriggerId == eventV1.Id);

        if (config == null)
        {
            throw new InvalidOperationException($"Unable to find post-enrollment config for workflow {input.WorkflowVersionedId}");
        }

        if (config.RequestBodyArgs == null)
        {
            throw new InvalidOperationException($"Unable to find request body args for post-enrollment config for workflow {input.WorkflowVersionedId}");
        }

        var requestBodyArgs = JsonConvert.DeserializeObject<PostWorkflowPublishedEnrollmentRequestBodyArgs>(
            JsonConvert.SerializeObject(config.RequestBodyArgs));

        if (requestBodyArgs == null)
        {
            throw new InvalidOperationException($"Failed to deserialize request body args for post-enrollment config for workflow {input.WorkflowVersionedId}");
        }

        var properties = typeof(PostWorkflowPublishedEnrollmentRequestBodyArgs).GetProperties();

        foreach (var property in properties)
        {
            var value = property.GetValue(requestBodyArgs)?.ToString();
            if (value != null)
            {
                var evaluatedValue = await EvaluateExpressionAsync(value, workflow);
                property.SetValue(requestBodyArgs, evaluatedValue?.ToString());
            }
        }

        return new GetPostWorkflowPublishedEnrollmentInfoOutput(
            config.EnrollRequestPath,
            config.EnrollProgressPath,
            requestBodyArgs);
    }

    public async Task<object?> EvaluateExpressionAsync(string expression, ProxyWorkflow workflow)
    {
        try
        {
            var scriptObject = new ScriptObject
            {
                {
                    "workflow", workflow
                },
                {
                    "json", new JsonFunctions()
                },
                {
                    "array", new ExtendedArrayFunctions()
                },
            };
            scriptObject.IsReadOnly = true;

            var templateContext = new TemplateContext();
            templateContext.PushGlobal(scriptObject);
            ((DateTimeFunctions) templateContext.BuiltinObject["date"]).Format = "%FT%T%Z";

            var myRegex = DoubleCurlyBracesRegex();
            var match = myRegex.Match(expression);

            if (match.Success)
            {
                expression = match.Groups[1].Value;
            }

            var evaluatedExpression = await Template.EvaluateAsync(
                expression.Trim(),
                templateContext);

            return evaluatedExpression;
        }
        catch (InvalidOperationException ex)
        {
            if (ex.Message.Contains("This template has errors."))
            {
                return expression;
            }

            throw;
        }
        catch (ScriptRuntimeException ex)
        {
            throw new SfScriptingException(ex);
        }
    }
}