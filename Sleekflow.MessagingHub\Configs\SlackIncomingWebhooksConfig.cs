using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Configs;

public interface ISlackIncomingWebhooksConfig
{
    public string Env { get; }

    public string WhatsappCloudApiErrorAlertsWebhookUrl { get; }
}

public class SlackIncomingWebhooksConfig : IConfig, ISlackIncomingWebhooksConfig
{
    public string Env { get; }

    public string WhatsappCloudApiErrorAlertsWebhookUrl { get; }

    public SlackIncomingWebhooksConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Env = Environment.GetEnvironmentVariable("SF_ENV_NAME", target) ??
                      throw new SfMissingEnvironmentVariableException("SF_ENV_NAME");

        // Set up experiment variable
        WhatsappCloudApiErrorAlertsWebhookUrl = Environment.GetEnvironmentVariable(
                                                    "WHATSAPP_CLOUD_API_ERROR_ALERTS_WEBHOOK_URL",
                                                    target)
                                                ?? throw new SfMissingEnvironmentVariableException(
                                                    "WHATSAPP_CLOUD_API_ERROR_ALERTS_WEBHOOK_URL");
    }
}