using System.Collections;
using Newtonsoft.Json.Linq;
using Scriban;
using Scriban.Functions;
using Scriban.Parsing;
using Scriban.Runtime;
using Scriban.Syntax;

namespace Sleekflow.FlowHub.States.Functions;

public class ExtendedArrayFunctions : ArrayFunctions
{
    /// <summary>
    /// Determines if at least one element in the input list passes the test implemented by the provided function.
    /// </summary>
    /// <param name="context">The template context.</param>
    /// <param name="span">The source span.</param>
    /// <param name="list">An input list.</param>
    /// <param name="function">The function to test each item in the list.</param>
    /// <returns>Returns true if at least one element passes the test, otherwise returns false.</returns>
    /// <remarks>
    /// ```scriban-html
    /// {{ [1, 3, 5, 6] | array.some @(do; ret $0 % 2 == 0; end) }}
    /// ```
    /// ```html
    /// true
    /// ```
    /// </remarks>
    public static bool Some(TemplateContext context, SourceSpan span, IEnumerable list, object function)
    {
        return ApplyFunctionBool(context, span, list, function, SomeProcessor);
    }

    private static bool SomeInternal(
        TemplateContext context,
        ScriptNode callerContext,
        SourceSpan span,
        IEnumerable list,
        IScriptCustomFunction function,
        Type destType)
    {
        var arg = new ScriptArray(1);
        foreach (var item in list)
        {
            var itemToTest = context.ToObject(span, item, destType);
            arg[0] = itemToTest;
            var testResult = ScriptFunctionCall.Call(context, callerContext, function, arg);

            if (context.ToBool(span, testResult))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Determines if all elements in the input list pass the test implemented by the provided function.
    /// </summary>
    /// <param name="context">The template context.</param>
    /// <param name="span">The source span.</param>
    /// <param name="list">An input list.</param>
    /// <param name="function">The function to test each item in the list.</param>
    /// <returns>Returns true if all elements pass the test, otherwise returns false.</returns>
    /// <remarks>
    /// ```scriban-html
    /// {{ [2, 4, 6, 8] | array.every @(do; ret $0 % 2 == 0; end) }}
    /// ```
    /// ```html
    /// true
    /// ```
    /// </remarks>
    public static bool Every(TemplateContext context, SourceSpan span, IEnumerable list, object function)
    {
        return ApplyFunctionBool(context, span, list, function, EveryProcessor);
    }

    private static bool EveryInternal(
        TemplateContext context,
        ScriptNode callerContext,
        SourceSpan span,
        IEnumerable list,
        IScriptCustomFunction function,
        Type destType)
    {
        var arg = new ScriptArray(1);
        foreach (var item in list)
        {
            var itemToTest = context.ToObject(span, item, destType);
            arg[0] = itemToTest;
            var testResult = ScriptFunctionCall.Call(context, callerContext, function, arg);

            if (!context.ToBool(span, testResult))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// Retrieves distinct string values from a JSON object based on a specified field path.
    /// </summary>
    /// <param name="target">The target object to extract values from. Can be null.</param>
    /// <param name="fieldPath">The JSON path to the field whose values are to be extracted.</param>
    /// <returns>A ScriptArray containing distinct string values from the specified field path.</returns>
    public static ScriptArray<string?> GetStringValuesByPath(
        object? target,
        string fieldPath)
    {
        ScriptArray<string?> values = [];

        if (target is null)
        {
            return values;
        }

        var token = JToken.FromObject(target);

        var basePath = fieldPath
            .Split('.')
            .FirstOrDefault();

        if (string.IsNullOrWhiteSpace(basePath))
        {
            basePath = fieldPath
                .Split("['")
                .First();
        }

        var actualPath = fieldPath[(fieldPath.IndexOf(basePath, StringComparison.Ordinal) + basePath.Length)..];

        actualPath = actualPath.StartsWith('.') ? actualPath[1..] : actualPath;

        var tokenValues = token
            .SelectTokens(
                actualPath,
                false)
            .Select(x => x.Value<string?>())
            .Distinct()
            .ToList();

        values.AddRange(tokenValues);

        return values;
    }

    private static readonly ListProcessorBool SomeProcessor = SomeInternal;
    private static readonly ListProcessorBool EveryProcessor = EveryInternal;

    private delegate bool ListProcessorBool(
        TemplateContext context,
        ScriptNode callerContext,
        SourceSpan span,
        IEnumerable list,
        IScriptCustomFunction function,
        Type destType);

    private static bool ApplyFunctionBool(
        TemplateContext context,
        SourceSpan span,
        IEnumerable? list,
        object? function,
        ListProcessorBool impl)
    {
        if (list == null)
        {
            return false;
        }

        if (function == null)
        {
            return false;
        }

        var scriptingFunction = function as IScriptCustomFunction;
        if (scriptingFunction == null)
        {
            throw new ArgumentException(
                $"The parameter `{function}` is not a function. Maybe prefix it with @?",
                nameof(function));
        }

        var callerContext = context.CurrentNode;

        return impl(
            context,
            callerContext,
            span,
            list,
            scriptingFunction,
            scriptingFunction.GetParameterInfo(0).ParameterType);
    }
}