﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Models.UserMappingConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Zoho.UserMappingConfigs;

public interface IZohoUserMappingConfigService
{
    Task<ZohoUserMappingConfig> GetAsync(
        string sleekflowCompanyId,
        string connectionId);

    Task<ZohoUserMappingConfig> GetByIdAsync(
        string id,
        string sleekflowCompanyId);

    Task<ZohoUserMappingConfig> CreateAndGetAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings);

    Task<ZohoUserMappingConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings);

    Task ClearAsync(string connectionId, string sleekflowCompanyId);
}

public class ZohoUserMappingConfigService : ISingletonService, IZohoUserMappingConfigService
{
    private readonly IZohoUserMappingConfigRepository _zohoUserMappingConfigRepository;
    private readonly IIdService _idService;

    public ZohoUserMappingConfigService(
        IZohoUserMappingConfigRepository zohoUserMappingConfigRepository,
        IIdService idService)
    {
        _zohoUserMappingConfigRepository = zohoUserMappingConfigRepository;
        _idService = idService;
    }

    public async Task<ZohoUserMappingConfig> GetAsync(
        string sleekflowCompanyId,
        string connectionId)
    {
        var userMappingConfig = (await _zohoUserMappingConfigRepository.GetObjectsAsync(
            c => c.SleekflowCompanyId == sleekflowCompanyId
                 && c.ConnectionId == connectionId)).FirstOrDefault();

        if (userMappingConfig is null)
        {
            throw new SfNotFoundObjectException(connectionId, sleekflowCompanyId);
        }

        return userMappingConfig;
    }

    public async Task<ZohoUserMappingConfig> GetByIdAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await _zohoUserMappingConfigRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<ZohoUserMappingConfig> CreateAndGetAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings)
    {
        var userMappingConfig = new ZohoUserMappingConfig(
            _idService.GetId("ZohoUserMappingConfig"),
            sleekflowCompanyId,
            connectionId,
            userMappings);

        return await _zohoUserMappingConfigRepository.CreateAndGetAsync(
            userMappingConfig,
            sleekflowCompanyId);
    }

    public async Task<ZohoUserMappingConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings)
    {
        return await _zohoUserMappingConfigRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/user_mappings", userMappings)
            });
    }

    public async Task ClearAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var config in _zohoUserMappingConfigRepository.GetObjectEnumerableAsync(
                           c =>
                               c.SysTypeName == "UserMappingConfig"
                               && c.ConnectionId == connectionId
                               && c.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _zohoUserMappingConfigRepository.DeleteAsync(
                config.Id,
                config.SleekflowCompanyId);
        }
    }
}