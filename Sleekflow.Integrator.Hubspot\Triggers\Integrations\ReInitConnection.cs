﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReInitConnection : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;

    public ReInitConnection(
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
    }

    public class ReInitConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonConstructor]
        public ReInitConnectionInput(
            string sleekflowCompanyId,
            string connectionId,
            string successUrl,
            string failureUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
        }
    }

    public class ReInitConnectionOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("is_re_authentication_required")]
        public bool IsReAuthenticationRequired { get; set; }

        [JsonProperty("hubspot_authentication_url")]
        public string? HubspotAuthenticationUrl { get; set; }

        [JsonConstructor]
        public ReInitConnectionOutput(
            string providerName,
            bool isReAuthenticationRequired,
            string? hubspotAuthenticationUrl)
        {
            ProviderName = providerName;
            IsReAuthenticationRequired = isReAuthenticationRequired;
            HubspotAuthenticationUrl = hubspotAuthenticationUrl;
        }
    }

    public async Task<ReInitConnectionOutput> F(
        ReInitConnectionInput reInitConnectionInput)
    {
        var connection = await _hubspotConnectionService.GetByIdAsync(
            reInitConnectionInput.ConnectionId,
            reInitConnectionInput.SleekflowCompanyId);
        if (connection.IsActive)
        {
            return new ReInitConnectionOutput(
                "hubspot-integrator",
                false,
                null);
        }

        try
        {
            await _hubspotAuthenticationService.ReAuthenticateAndStoreAsync(
                connection.AuthenticationId,
                connection.SleekflowCompanyId);

            await _hubspotConnectionService.PatchAsync(
                connection.Id,
                connection.SleekflowCompanyId,
                connection.Name,
                true);

            return new ReInitConnectionOutput(
                "hubspot-integrator",
                false,
                null);
        }
        catch (Exception)
        {
            var redirectUrl =
                await _hubspotAuthenticationService.AuthenticateV2Async(
                    reInitConnectionInput.SleekflowCompanyId,
                    reInitConnectionInput.SuccessUrl,
                    reInitConnectionInput.FailureUrl);

            return new ReInitConnectionOutput(
                "hubspot-integrator",
                true,
                redirectUrl);
        }
    }
}