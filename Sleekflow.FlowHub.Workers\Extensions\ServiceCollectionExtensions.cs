﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.FlowHub.Workers.Services;

namespace Sleekflow.FlowHub.Workers.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddContactProcessingServices(this IServiceCollection services)
    {
        services.AddSingleton<IContactBatchService, ContactBatchService>();
        services.AddSingleton<IContactEligibilityService, ContactEligibilityService>();
        services.AddSingleton<IContactEnrollmentService, ContactEnrollmentService>();

        return services;
    }
}