using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class ListReplyMessageObject : BaseMessageObject
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonConstructor]
    public ListReplyMessageObject(string id, string title, string? description)
    {
        Id = id;
        Title = title;
        Description = description;
    }
}