using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;

public class ShopifyCartExternalIntegrationInfo : CartExternalIntegrationInfo
{
    [JsonProperty("cart_token")]
    public string CartToken { get; set; }

    [JsonProperty("checkout_url")]
    public string CheckoutUrl { get; set; }

    [JsonProperty("conversion_status")]
    public string ConversionStatus { get; set; }

    [JsonProperty("total_price")]
    public decimal TotalPrice { get; set; }

    [JsonProperty("total_tax")]
    public decimal TotalTax { get; set; }

    [JsonProperty("subtotal_price")]
    public decimal SubtotalPrice { get; set; }

    [JsonProperty("shopify_customer_id")]
    public string? ShopifyCustomerId { get; set; }

    [JsonProperty("abandoned_date")]
    public DateTimeOffset? AbandonedDate { get; set; }

    [JsonProperty("recovered_date")]
    public DateTimeOffset? RecoveredDate { get; set; }

    [JsonConstructor]
    public ShopifyCartExternalIntegrationInfo(
        string cartToken,
        string checkoutUrl,
        string conversionStatus,
        decimal totalPrice,
        decimal totalTax,
        decimal subtotalPrice,
        string? shopifyCustomerId,
        DateTimeOffset? abandonedDate,
        DateTimeOffset? recoveredDate)
        : base("shopify")
    {
        CartToken = cartToken;
        CheckoutUrl = checkoutUrl;
        ConversionStatus = conversionStatus;
        TotalPrice = totalPrice;
        TotalTax = totalTax;
        SubtotalPrice = subtotalPrice;
        ShopifyCustomerId = shopifyCustomerId;
        AbandonedDate = abandonedDate;
        RecoveredDate = recoveredDate;
    }
}