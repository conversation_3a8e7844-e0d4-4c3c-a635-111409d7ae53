namespace Sleekflow.Models.Events;

public class OnFlowHubWorkflowPatchedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string? SleekflowStaffId { get; set; }

    public string WorkflowId { get; set; }

    public string WorkflowVersionedId { get; set; }

    public object OriginalWorkflowSnapshot { get; set; }

    public OnFlowHubWorkflowPatchedEvent(
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string workflowId,
        string workflowVersionedId,
        object originalWorkflowSnapshot)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowStaffId = sleekflowStaffId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        OriginalWorkflowSnapshot = originalWorkflowSnapshot;
    }
}