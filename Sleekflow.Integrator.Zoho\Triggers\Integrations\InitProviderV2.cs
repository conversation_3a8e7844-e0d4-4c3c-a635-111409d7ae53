﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Zoho.Authentications;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProviderV2 : ITrigger
{
    private readonly IZohoAuthenticationService _zohoAuthenticationService;

    public InitProviderV2(IZohoAuthenticationService zohoAuthenticationService)
    {
        _zohoAuthenticationService = zohoAuthenticationService;
    }

    public class InitProviderV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderV2Input(
            string sleekflowCompanyId,
            string successUrl,
            string failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderV2Output
    {
        [JsonProperty("zoho_authentication_url")]
        public string ZohoAuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderV2Output(string zohoAuthenticationUrl)
        {
            ZohoAuthenticationUrl = zohoAuthenticationUrl;
        }
    }

    public async Task<InitProviderV2Output> F(
        InitProviderV2Input initProviderInput)
    {
        return new InitProviderV2Output(await _zohoAuthenticationService.AuthenticateAsync(
            initProviderInput.SleekflowCompanyId,
            initProviderInput.SuccessUrl,
            initProviderInput.FailureUrl,
            initProviderInput.AdditionalDetails));
    }
}