﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class SubscriptionsCheckEnd
{
    private readonly IHubspotSubscriptionRepository _hubspotSubscriptionRepository;

    public SubscriptionsCheckEnd(
        IHubspotSubscriptionRepository hubspotSubscriptionRepository)
    {
        _hubspotSubscriptionRepository = hubspotSubscriptionRepository;
    }

    public class SubscriptionsCheckEndInput
    {
        [JsonProperty("subscription")]
        [Required]
        public HubspotSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTime? LastObjectModificationTime { get; set; }

        [JsonProperty("last_execution_start_time")]
        [Required]
        public DateTime LastExecutionStartTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckEndInput(
            HubspotSubscription subscription,
            DateTime? lastObjectModificationTime,
            DateTime lastExecutionStartTime)
        {
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
            LastExecutionStartTime = lastExecutionStartTime;
        }
    }

    [Function("Hubspot_SubscriptionsCheck_End")]
    public async Task End(
        [ActivityTrigger]
        SubscriptionsCheckEndInput subscriptionsCheckEndInput)
    {
        var patchOperations = new List<PatchOperation>
        {
            PatchOperation.Replace(
                $"/{HubspotSubscription.PropertyNameDurablePayload}",
                (HttpManagementPayload?) null),
            PatchOperation.Replace(
                $"/{HubspotSubscription.PropertyNameLastExecutionStartTime}",
                subscriptionsCheckEndInput.LastExecutionStartTime),
        };
        if (subscriptionsCheckEndInput.LastObjectModificationTime != null)
        {
            patchOperations.Add(
                PatchOperation.Replace(
                    $"/{HubspotSubscription.PropertyNameLastObjectModificationTime}",
                    subscriptionsCheckEndInput.LastObjectModificationTime));
        }

        await _hubspotSubscriptionRepository.PatchAsync(
            subscriptionsCheckEndInput.Subscription.Id,
            subscriptionsCheckEndInput.Subscription.SleekflowCompanyId,
            patchOperations);
    }
}