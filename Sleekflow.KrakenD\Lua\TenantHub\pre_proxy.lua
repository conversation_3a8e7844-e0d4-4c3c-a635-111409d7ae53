function pre_proxy(endpoint, path)
  local request = request.load()
  local json = require("lua/Libraries/json")

  local url = endpoint .. path
  local requestBody = {
    sleekflow_tenanthub_user_id = request:headers("X-Sleekflow-TenantHub-User-Id"),
    sleekflow_user_id = request:headers("X-Sleekflow-User-Id"),
    targeted_company_id = request:headers("X-Targeted-Company-Id"),
    sleekflow_email = request:headers("X-Sleekflow-Email"),
    is_rbac_enabled = request:headers("X-Sleekflow-Is-RBAC-Enabled"),
  }
  if (request:headers("X-Sleekflow-Login-As-User") ~= '') then
    local loginAsUser = json.decode(request:headers("X-Sleekflow-Login-As-User"))
    if (loginAsUser.user_id ~= nil and loginAsUser.user_id ~= '' and
      loginAsUser.company_id ~= nil and loginAsUser.company_id ~= '') then
      requestBody.login_as_user = {
        tenanthub_user_id = loginAsUser.tenanthub_user_id,
        user_id = loginAsUser.user_id,
        company_id = loginAsUser.company_id,
        expire_at = loginAsUser.expire_at
      }
    end
  end

  local get_user_auth_details_res = http_response.new(url, "POST", json.encode(requestBody), { ["Content-Type"] = "application/json" })

  if (get_user_auth_details_res:statusCode() ~= 200) then
    get_user_auth_details_res:close()
    custom_error("Unauthorized.", 401)
    return
  end

  local body = json.decode(get_user_auth_details_res:body())
  get_user_auth_details_res:close()

  if (body.success ~= true) then
    custom_error("Unauthorized.", 401)
    return
  end

  local data = body.data
  local sleekflow_tenanthub_user_id = data.sleekflow_tenanthub_user_id
  local sleekflow_user_id = data.sleekflow_user_id
  local sleekflow_staff_id = data.sleekflow_staff_id
  local sleekflow_company_id = data.sleekflow_company_id
  local sleekflow_roles = data.sleekflow_roles
  local sleekflow_role_ids = data.sleekflow_role_ids
  local sleekflow_team_ids = data.sleekflow_team_ids
  local sleekflow_impersonator = data.sleekflow_impersonator
  local sleekflow_is_rbac_enabled = data.is_rbac_enabled

  if (sleekflow_user_id == nil or sleekflow_user_id == ''
    or sleekflow_staff_id == nil or sleekflow_staff_id == ''
    or sleekflow_company_id == nil or sleekflow_company_id == '') then
    custom_error("Unauthorized.", 401)
    return
  end
  -- Insert the request headers for internal backend endpoints
  request:headers("X-Sleekflow-TenantHub-User-Id", sleekflow_tenanthub_user_id)
  request:headers("X-Sleekflow-User-Id", sleekflow_user_id)
  request:headers("X-Sleekflow-Staff-Id", sleekflow_staff_id)
  request:headers("X-Sleekflow-Company-Id", sleekflow_company_id)
  request:headers("X-Sleekflow-Is-RBAC-Enabled", sleekflow_is_rbac_enabled)

  if (sleekflow_roles ~= nil) then
    request:headers("X-Sleekflow-Roles", table.concat(sleekflow_roles, ","))
  end

  if (sleekflow_role_ids ~= nil) then
    request:headers("X-Sleekflow-Role-Ids", table.concat(sleekflow_role_ids, ","))
  end

  if (sleekflow_team_ids ~= nil) then
    request:headers("X-Sleekflow-Team-Ids", table.concat(sleekflow_team_ids, ","))
  end

  if (sleekflow_impersonator ~= nil) then
    request:headers("X-Sleekflow-Impersonator-TenantHub-User-Id", sleekflow_impersonator.sleekflow_tenanthub_user_id)
    request:headers("X-Sleekflow-Impersonator-User-Id", sleekflow_impersonator.sleekflow_user_id)
    request:headers("X-Sleekflow-Impersonator-Company-Id", sleekflow_impersonator.sleekflow_company_id)
    request:headers("X-Sleekflow-Impersonator-Staff-Id", sleekflow_impersonator.sleekflow_staff_id)
  end

end