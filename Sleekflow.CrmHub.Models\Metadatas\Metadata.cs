﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.Metadatas;

public class MetadataFieldDto
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("types")]
    public List<dynamic> Types { get; set; }

    [JsonConstructor]
    public MetadataFieldDto(
        string name,
        List<dynamic> types)
    {
        Name = name;
        Types = types;
    }
}

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("metadata")]
public class Metadata : Entity
{
    [JsonProperty("sys_sleekflow_company_id")]
    public string SysSleekflowCompanyId { get; set; }

    [JsonProperty("keys")]
    public List<string> Keys { get; set; }

    [JsonProperty("fields")]
    public Dictionary<string, MetadataFieldDto> Fields { get; set; }

    [JsonConstructor]
    public Metadata(
        string id,
        string sysSleekflowCompanyId,
        List<string> keys,
        Dictionary<string, MetadataFieldDto> fields)
        : base(id, "Metadata")
    {
        Id = id;
        SysSleekflowCompanyId = sysSleekflowCompanyId;
        Keys = keys;
        Fields = fields;
    }
}