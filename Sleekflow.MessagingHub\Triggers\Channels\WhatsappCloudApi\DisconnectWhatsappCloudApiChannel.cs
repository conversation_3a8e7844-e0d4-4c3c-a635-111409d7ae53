using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class DisconnectWhatsappCloudApiChannel
    : ITrigger<
        DisconnectWhatsappCloudApiChannel.DisconnectWhatsappCloudApiChannelInput,
        DisconnectWhatsappCloudApiChannel.DisconnectWhatsappCloudApiChannelOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<DisconnectWhatsappCloudApiChannel> _logger;

    public DisconnectWhatsappCloudApiChannel(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<DisconnectWhatsappCloudApiChannel> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class DisconnectWhatsappCloudApiChannelInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DisconnectWhatsappCloudApiChannelInput(
            string sleekflowCompanyId,
            string wabaId,
            string wabaPhoneNumberId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DisconnectWhatsappCloudApiChannelOutput
    {
        [JsonProperty("disconnected")]
        public bool Disconnected { get; set; }

        [JsonConstructor]
        public DisconnectWhatsappCloudApiChannelOutput(bool isDisconnected)
        {
            Disconnected = isDisconnected;
        }
    }

    public async Task<DisconnectWhatsappCloudApiChannelOutput> F(
        DisconnectWhatsappCloudApiChannelInput disconnectWhatsappCloudApiChannelInput)
    {
        var wabaPhoneNumberId = disconnectWhatsappCloudApiChannelInput.WabaPhoneNumberId;
        var sleekflowCompanyId = disconnectWhatsappCloudApiChannelInput.SleekflowCompanyId;
        var wabaId = disconnectWhatsappCloudApiChannelInput.WabaId;

        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            disconnectWhatsappCloudApiChannelInput.SleekflowStaffId,
            disconnectWhatsappCloudApiChannelInput.SleekflowStaffTeamIds);
        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            wabaId,
            sleekflowCompanyId,
            wabaPhoneNumberId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        return new DisconnectWhatsappCloudApiChannelOutput(
            await _channelService.DisconnectCloudApiChannelAsync(
                waba,
                wabaPhoneNumberId,
                sleekflowCompanyId,
                sleekflowStaff));
    }
}