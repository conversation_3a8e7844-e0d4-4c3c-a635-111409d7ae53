using MassTransit;
using Newtonsoft.Json;
using Polly;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.Workers;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.Steps;

/// <summary>
/// Provides an interface for orchestrating the execution of workflow steps, including
/// single steps, delayed steps, try-catch steps, and parallel steps. It also allows
/// notifying the status of step executions.
/// </summary>
public interface IStepOrchestrationService
{
    Task ExecuteStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries);

    Task ExecuteSleepSleep(
        string stateId,
        string stepId,
        TimeSpan timeSpan,
        Stack<StackEntry> stackEntries);

    Task ExecuteTryCatchStepAsync(
        string stateId,
        string stepId,
        string tryStepId,
        string catchStepId,
        Stack<StackEntry> stackEntries);

    Task ExecuteParallelStepAsync(
        string stateId,
        string stepId,
        List<string> parallelStepIds,
        Stack<StackEntry> stackEntries);

    Task NotifyStatusAsync(
        string stateId,
        string stepId,
        string? workerInstanceId,
        string stepExecutionStatus,
        Exception? exception = null);
}

/// <summary>
/// Implements step orchestration by delegating to either a MassTransit consumer
/// or an Azure Durable Functions orchestrator.
/// </summary>
public class StepOrchestrationService : IStepOrchestrationService, IScopedService
{
    private readonly IWorkerService _workerService;
    private readonly IWorkerConfig _workerConfig;
    private readonly IStepRequester _stepRequester;
    private readonly HttpClient _httpClient;
    private readonly ILogger<StepOrchestrationService> _logger;
    private readonly IServiceBusManager _serviceBusManager;

    public StepOrchestrationService(
        IWorkerService workerService,
        IHttpClientFactory httpClientFactory,
        IWorkerConfig workerConfig,
        IStepRequester stepRequester,
        ILogger<StepOrchestrationService> logger,
        IServiceBusManager serviceBusManager)
    {
        _workerService = workerService;
        _workerConfig = workerConfig;
        _stepRequester = stepRequester;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _logger = logger;
        _serviceBusManager = serviceBusManager;
    }

    public async Task ExecuteStepAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries)
    {
        await _stepRequester.RequestAsync(
            stateId,
            stepId,
            stackEntries,
            null);
    }

    public async Task ExecuteSleepSleep(
        string stateId,
        string stepId,
        TimeSpan timeSpan,
        Stack<StackEntry> stackEntries)
    {
        await _workerService.PostAsync<object>(
            _httpClient,
            JsonConvert.SerializeObject(
                new ExecuteSleepStepInput(
                    stateId,
                    stepId,
                    Convert.ToInt64(timeSpan.TotalSeconds),
                    stackEntries),
                JsonConfig.DefaultJsonSerializerSettings),
            _workerConfig.WorkerHostname + "/api/ExecuteSleepStep");
    }

    public async Task ExecuteTryCatchStepAsync(
        string stateId,
        string stepId,
        string tryStepId,
        string catchStepId,
        Stack<StackEntry> stackEntries)
    {
        await _workerService.PostAsync<object>(
            _httpClient,
            JsonConvert.SerializeObject(
                new ExecuteTryCatchStepInput(
                    stateId,
                    stepId,
                    tryStepId,
                    catchStepId,
                    stackEntries),
                JsonConfig.DefaultJsonSerializerSettings),
            _workerConfig.WorkerHostname + "/api/ExecuteTryCatchStep");
    }

    public async Task ExecuteParallelStepAsync(
        string stateId,
        string stepId,
        List<string> parallelStepIds,
        Stack<StackEntry> stackEntries)
    {
        await _workerService.PostAsync<object>(
            _httpClient,
            JsonConvert.SerializeObject(
                new ExecuteParallelStepInput(
                    stateId,
                    stepId,
                    parallelStepIds,
                    stackEntries),
                JsonConfig.DefaultJsonSerializerSettings),
            _workerConfig.WorkerHostname + "/api/ExecuteParallelStep");
    }

    public async Task NotifyStatusAsync(
        string stateId,
        string stepId,
        string? workerInstanceId,
        string stepExecutionStatus,
        Exception? exception = null)
    {
        if (workerInstanceId != null)
        {
            // Notify Azure Durable Functions about the status
            var retryPolicy = Policy
                .Handle<SfInternalErrorException>()
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: currentRetryCount => TimeSpan.FromSeconds(currentRetryCount * 2),
                    onRetry: (e, _, retryCount, _) =>
                    {
                        _logger.LogError(
                            e,
                            "NotifyStatus error occurred during attempt {RetryCount}. Error: {ErrorMessage}",
                            retryCount,
                            e.Message);
                    });

            var policyResult = await retryPolicy.ExecuteAndCaptureAsync(
                async () =>
                    await _workerService.PostAsync<object>(
                        _httpClient,
                        JsonConvert.SerializeObject(
                            new NotifyStatusInput(
                                workerInstanceId,
                                stepExecutionStatus,
                                exception == null
                                    ? null
                                    : new UserFriendlyError(
                                        "I0000",
                                        exception.Message)),
                            JsonConfig.DefaultJsonSerializerSettings),
                        _workerConfig.WorkerHostname + "/api/NotifyStatus"));
            if (policyResult.FinalException is not null)
            {
                _logger.LogError("NotifyStatus failed. Error: {ErrorMessage}", policyResult.FinalException.Message);
            }
        }

        UserFriendlyError? userFriendlyError = null;

        if (exception is SfFlowHubUserFriendlyException sfFlowHubUserFriendlyException)
        {
            userFriendlyError = new UserFriendlyError(
                sfFlowHubUserFriendlyException.UserFriendlyErrorCode,
                sfFlowHubUserFriendlyException.UserFriendlyErrorMessage);
        }
        else if (exception is not null)
        {
            userFriendlyError = new UserFriendlyError(
                UserFriendlyErrorCodes.InternalError,
                string.Empty);
        }

        var onStepExecutionStatusChangedEvent = new OnStepExecutionStatusChangedEvent(
            stateId,
            stepId,
            stepExecutionStatus,
            workerInstanceId,
            userFriendlyError);
        await _serviceBusManager.PublishAsync(onStepExecutionStatusChangedEvent);
    }
}