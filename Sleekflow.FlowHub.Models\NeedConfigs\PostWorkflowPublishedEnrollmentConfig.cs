using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class PostWorkflowPublishedEnrollmentConfig
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("trigger_id")]
    public string TriggerId { get; set; }

    [JsonProperty("trigger_name")]
    public string TriggerName { get; set; }

    [JsonProperty("trigger_group")]
    public string TriggerGroup { get; set; }

    [JsonProperty("enroll_request_path")]
    public string EnrollRequestPath { get; set; }

    [JsonProperty("enroll_progress_path")]
    public string EnrollProgressPath { get; set; }

    [JsonProperty("request_body_args")]
    public PostWorkflowPublishedEnrollmentRequestBodyArgs RequestBodyArgs { get; set; }

    [JsonConstructor]
    public PostWorkflowPublishedEnrollmentConfig(
        string id,
        string triggerId,
        string triggerName,
        string triggerGroup,
        string enrollRequestPath,
        string enrollProgressPath,
        PostWorkflowPublishedEnrollmentRequestBodyArgs requestBodyArgs)
    {
        Id = id;
        TriggerId = triggerId;
        TriggerName = triggerName;
        TriggerGroup = triggerGroup;
        EnrollRequestPath = enrollRequestPath;
        EnrollProgressPath = enrollProgressPath;
        RequestBodyArgs = requestBodyArgs;
    }
}