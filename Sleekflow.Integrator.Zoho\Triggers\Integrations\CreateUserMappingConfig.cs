﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class CreateUserMappingConfig : ITrigger
{
    private readonly IZohoUserMappingConfigService _zohoUserMappingConfigService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;

    public CreateUserMappingConfig(
        IZohoUserMappingConfigService zohoUserMappingConfigService,
        IZohoConnectionService zohoConnectionService,
        IZohoAuthenticationService zohoAuthenticationService)
    {
        _zohoUserMappingConfigService = zohoUserMappingConfigService;
        _zohoConnectionService = zohoConnectionService;
        _zohoAuthenticationService = zohoAuthenticationService;
    }

    public class CreateUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [ValidateArray]
        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }

        [JsonConstructor]
        public CreateUserMappingConfigInput(
            string sleekflowCompanyId,
            string connectionId,
            List<UserMapping>? userMappings)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            UserMappings = userMappings;
        }
    }

    public class CreateUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public CreateUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<CreateUserMappingConfigOutput> F(
        CreateUserMappingConfigInput createUserMappingConfigInput)
    {
        var sleekflowCompanyId = createUserMappingConfigInput.SleekflowCompanyId;
        var connectionId = createUserMappingConfigInput.ConnectionId;
        var userMappings = createUserMappingConfigInput.UserMappings;

        var connection =
            await _zohoConnectionService.GetByIdAsync(
                connectionId,
                sleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var userMappingConfig =
            await _zohoUserMappingConfigService.CreateAndGetAsync(
                sleekflowCompanyId,
                connectionId,
                userMappings);

        return new CreateUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}