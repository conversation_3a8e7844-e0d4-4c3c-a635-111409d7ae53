using System.ComponentModel.DataAnnotations;
using Azure.Search.Documents.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class AutocompleteProducts
    : ITrigger<
        AutocompleteProducts.AutocompleteProductsInput,
        AutocompleteProducts.AutocompleteProductsOutput>
{
    private readonly IProductSearchService _productSearchService;

    public AutocompleteProducts(IProductSearchService productSearchService)
    {
        _productSearchService = productSearchService;
    }

    public class AutocompleteProductsInput
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(256, MinimumLength = 1)]
        [JsonProperty("search_text")]
        public string SearchText { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public AutocompleteProductsInput(
            string sleekflowCompanyId,
            string storeId,
            string searchText,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            SearchText = searchText;
            Limit = limit;
        }
    }

    public class AutocompleteProductsOutput
    {
        [JsonProperty("autocomplete_items")]
        public List<AutocompleteItem> AutocompleteItems { get; set; }

        [JsonConstructor]
        public AutocompleteProductsOutput(
            List<AutocompleteItem> autocompleteItems)
        {
            AutocompleteItems = autocompleteItems;
        }
    }

    public async Task<AutocompleteProductsOutput> F(AutocompleteProductsInput autocompleteProductsInput)
    {
        var autocompleteItems =
            await _productSearchService.AutocompleteProductsAsync(
                autocompleteProductsInput.SleekflowCompanyId,
                autocompleteProductsInput.StoreId,
                autocompleteProductsInput.SearchText,
                autocompleteProductsInput.Limit);

        return new AutocompleteProductsOutput(autocompleteItems);
    }
}