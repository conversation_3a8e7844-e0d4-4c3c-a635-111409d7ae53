﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Helpers;
using Sleekflow.Integrator.GoogleSheets.IntegrationObjects;
using Sleekflow.Integrator.GoogleSheets.Services;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Internals;

[TriggerGroup("Internals")]
public class SubscriptionsCheckBatch : ITrigger
{
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly ILogger<SubscriptionsCheckBatch> _logger;
    private readonly IIntegrationObjectService _integrationObjectService;
    private readonly IBus _bus;

    public SubscriptionsCheckBatch(
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        IGoogleSheetsObjectService googleSheetsObjectService,
        ILogger<SubscriptionsCheckBatch> logger,
        IIntegrationObjectService integrationObjectService,
        IBus bus)
    {
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _googleSheetsObjectService = googleSheetsObjectService;
        _logger = logger;
        _integrationObjectService = integrationObjectService;
        _bus = bus;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public GoogleSheetsSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            GoogleSheetsSubscription subscription,
            DateTimeOffset lastObjectModificationTime)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            DateTimeOffset nextLastObjectModificationTime)
        {
            Count = count;
            NextLastObjectModificationTime = nextLastObjectModificationTime;
        }
    }

    public async Task<SubscriptionsCheckBatchOutput> F(
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var subscription = subscriptionsCheckBatchInput.Subscription;
        var lastObjectModificationTime = subscriptionsCheckBatchInput.LastObjectModificationTime;

        var connection = await _googleSheetsConnectionService.GetByIdAsync(
            subscription.ConnectionId,
            subscription.SleekflowCompanyId);

        var authentication =
            await _googleSheetsAuthenticationService.GetAsync(
                connection.AuthenticationId,
                subscriptionsCheckBatchInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started sleekflowCompanyId {SleekflowCompanyId}, " +
            "subscription.Id {SubscriptionId}, " +
            "{TypedIds} " +
            "lastObjectModificationTime {LastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            string.Join(", ", subscription.TypedIds.Select(t => $"{t.Type} Id {t.Id}")),
            lastObjectModificationTime);

        var latestObjectData = await _googleSheetsObjectService.GetObjectAsync(
            authentication,
            subscription.TypedIds,
            subscription.EntityTypeName);
        var currentPersistedObject = await _integrationObjectService.GetAsync(
            subscription.SleekflowCompanyId,
            "google-sheets-integrator",
            subscription.TypedIds,
            subscription.EntityTypeName);
        if (currentPersistedObject is null)
        {
            var persistedObject = await _integrationObjectService.CreateAndGetAsync(
                subscription.SleekflowCompanyId,
                "google-sheets-integrator",
                subscription.EntityTypeName,
                latestObjectData,
                subscription.TypedIds,
                DateTimeOffset.UtcNow);

            return new SubscriptionsCheckBatchOutput(0, persistedObject.LastModificationTime!.Value);
        }
        else
        {
            var currentPersistedObjectData = currentPersistedObject.Data;

            var nextLastObjectModificationTime = currentPersistedObject.LastModificationTime!.Value;
            var isObjectModified = await HandleObjectCheck(
                subscription,
                subscription.EntityTypeName,
                currentPersistedObjectData!,
                latestObjectData);
            if (isObjectModified)
            {
                var patchedPersistedObject = await _integrationObjectService.PatchAndGetAsync(
                    currentPersistedObject.Id,
                    subscription.SleekflowCompanyId,
                    latestObjectData,
                    DateTimeOffset.UtcNow);
                nextLastObjectModificationTime = patchedPersistedObject.LastModificationTime!.Value;
            }

            return new SubscriptionsCheckBatchOutput(1, nextLastObjectModificationTime);
        }
    }

    private async Task<bool> HandleObjectCheck(
        GoogleSheetsSubscription subscription,
        string entityTypeName,
        object currentPersistedObjectData,
        object latestObjectData)
    {
        switch (entityTypeName)
        {
            case "Worksheet":
                return await HandleWorksheetObjectCheck(
                    subscription,
                    currentPersistedObjectData,
                    latestObjectData);

                break;
            default:
                throw new NotSupportedException($"Entity type {entityTypeName} is not supported");
        }
    }

    private async Task<bool> HandleWorksheetObjectCheck(
        GoogleSheetsSubscription subscription,
        object currentPersistedObjectData,
        object latestObjectData)
    {
        var currentWorksheet = (currentPersistedObjectData as JArray)?.ToObject<List<Dictionary<string, object?>>>();
        var latestWorksheet = latestObjectData as List<Dictionary<string, object?>>;
        var difference = WorksheetDifferenceHelper.CalculateDifference(
                currentWorksheet!,
                latestWorksheet!);
        var addedRows = difference.AddedRows;
        var updatedRows = difference.UpdatedRows;

        var (spreadsheetId, worksheetId) = GetSpreadsheetAndWorksheetIds(subscription.TypedIds);

        var googleSheetsWorksheetRowCreatedEventRequests = new List<GoogleSheetsRowCreatedEventRequest>();
        for (var i = 0; i < addedRows.Count; i++)
        {
            googleSheetsWorksheetRowCreatedEventRequests.Add(new GoogleSheetsRowCreatedEventRequest(
                DateTimeOffset.UtcNow,
                subscription.SleekflowCompanyId,
                subscription.ConnectionId,
                spreadsheetId,
                worksheetId,
                (string)addedRows[i]["Id"]!,
                addedRows[i]));
        }

        var googleSheetsWorksheetRowUpdatedEventRequests = new List<GoogleSheetsRowUpdatedEventRequest>();
        for (var i = 0; i < updatedRows.Count; i++)
        {
            googleSheetsWorksheetRowUpdatedEventRequests.Add(new GoogleSheetsRowUpdatedEventRequest(
                DateTimeOffset.UtcNow,
                subscription.SleekflowCompanyId,
                subscription.ConnectionId,
                spreadsheetId,
                worksheetId,
                (string)updatedRows[i].NewRow["Id"]!,
                updatedRows[i].NewRow));
        }

        if (googleSheetsWorksheetRowCreatedEventRequests.Count > 0)
        {
            await _bus.PublishBatch(
                googleSheetsWorksheetRowCreatedEventRequests,
                context =>
                {
                    context.ConversationId = Guid.Parse(subscription.SleekflowCompanyId);
                });
        }

        if (googleSheetsWorksheetRowUpdatedEventRequests.Count > 0)
        {
            await _bus.PublishBatch(
                googleSheetsWorksheetRowUpdatedEventRequests,
                context =>
                {
                    context.ConversationId = Guid.Parse(subscription.SleekflowCompanyId);
                });
        }

        return googleSheetsWorksheetRowCreatedEventRequests.Count > 0 || googleSheetsWorksheetRowUpdatedEventRequests.Count > 0;
    }

    private (string SpreadsheetId, string WorksheetId) GetSpreadsheetAndWorksheetIds(List<TypedId> typedIds)
    {
        var spreadsheetId = typedIds.Find(i => i.Type == "Spreadsheet")?.Id;
        var worksheetId = typedIds.Find(i => i.Type == "Worksheet")?.Id;
        if (spreadsheetId == null || worksheetId == null)
        {
            throw new Exception("Missing spreadsheet or worksheet id");
        }

        return (spreadsheetId, worksheetId);
    }
}