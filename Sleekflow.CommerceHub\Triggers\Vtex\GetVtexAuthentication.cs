﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex.ViewModels;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Vtex;

[TriggerGroup(ControllerNames.Vtex)]
public class GetVtexAuthentication
    : ITrigger<
        GetVtexAuthentication.GetVtexAuthenticationInput,
        GetVtexAuthentication.GetVtexAuthenticationOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;
    private readonly IVtexOrderHookRegister _vtexOrderHookRegister;

    public GetVtexAuthentication(IVtexAuthenticationService vtexAuthenticationService, IVtexOrderHookRegister vtexOrderHookRegister)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
        _vtexOrderHookRegister = vtexOrderHookRegister;
    }

    public class GetVtexAuthenticationInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("vtex_authentication_id")]
        public string VtexAuthenticationId { get; set; }

        [JsonConstructor]
        public GetVtexAuthenticationInput(string sleekflowCompanyId, string vtexAuthenticationId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            VtexAuthenticationId = vtexAuthenticationId;
        }
    }

    public class GetVtexAuthenticationOutput
    {
        [JsonProperty("vtex_authentication")]
        public VtexAuthenticationViewModel VtexAuthentication { get; set; }

        [JsonConstructor]
        public GetVtexAuthenticationOutput(VtexAuthenticationViewModel vtexAuthentication)
        {
            VtexAuthentication = vtexAuthentication;
        }
    }

    public async Task<GetVtexAuthenticationOutput> F(
        GetVtexAuthenticationInput input)
    {
        var vtexAuthentication = await _vtexAuthenticationService.GetAsync(
            input.VtexAuthenticationId,
            input.SleekflowCompanyId);

        if (vtexAuthentication == null)
        {
            throw new SfNotFoundObjectException($"Authentication with ID {input.VtexAuthenticationId} not found.");
        }

        var isActive = await _vtexOrderHookRegister.ValidateRegistrationAsync(vtexAuthentication.Credential);

        return new GetVtexAuthenticationOutput(
            new VtexAuthenticationViewModel(vtexAuthentication, isActive));
    }
}