using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnTicketCommonEventBody : IHasSleekflowUserProfileId, IHasCreatedAt
{
    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("created_by")]
    public string CreatedBy { get; set; }

    [JsonProperty("created_by_email")]
    public string CreatedByEmail { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("channel")]
    public OnTicketCommonEventBodyTicketChannel Channel { get; set; }

    [JsonProperty("status_name")]
    public string StatusName { get; set; }

    [JsonProperty("status_id")]
    public string StatusId { get; set; }

    [JsonProperty("priority_name")]
    public string PriorityName { get; set; }

    [JsonProperty("priority_id")]
    public string PriorityId { get; set; }

    [JsonProperty("due_date")]
    public DateTimeOffset? DueDate { get; set; }

    [JsonProperty("type_name")]
    public string TypeName { get; set; }

    [JsonProperty("type_id")]
    public string TypeId { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("first_assignee")]
    public string FirstAssignee { get; set; }

    [JsonProperty("first_assignee_email")]
    public string FirstAssigneeEmail { get; set; }

    [JsonProperty("contact_name")]
    public string ContactName { get; set; }

    [JsonProperty("contact_phone_number")]
    public string ContactPhoneNumber { get; set; }

    [JsonProperty("contact_email")]
    public string ContactEmail { get; set; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonConstructor]
    public OnTicketCommonEventBody(
        DateTimeOffset createdAt,
        string createdBy,
        string createdByEmail,
        string id,
        string title,
        OnTicketCommonEventBodyTicketChannel channel,
        string statusName,
        string statusId,
        string priorityName,
        string priorityId,
        DateTimeOffset? dueDate,
        string typeName,
        string typeId,
        string description,
        string firstAssignee,
        string firstAssigneeEmail,
        string contactName,
        string contactPhoneNumber,
        string contactEmail,
        string sleekflowUserProfileId)
    {
        CreatedAt = createdAt;
        CreatedBy = createdBy;
        CreatedByEmail = createdByEmail;
        Id = id;
        Title = title;
        Channel = channel;
        StatusName = statusName;
        StatusId = statusId;
        PriorityName = priorityName;
        PriorityId = priorityId;
        DueDate = dueDate;
        TypeName = typeName;
        TypeId = typeId;
        Description = description;
        FirstAssignee = firstAssignee;
        FirstAssigneeEmail = firstAssigneeEmail;
        ContactName = contactName;
        ContactPhoneNumber = contactPhoneNumber;
        ContactEmail = contactEmail;
        SleekflowUserProfileId = sleekflowUserProfileId;
    }
}

public class OnTicketCommonEventBodyTicketChannel
{
    [JsonProperty("channel_type")]
    public string ChannelType { get; set; }

    [JsonProperty("channel_identity_id")]
    public string ChannelIdentityId { get; set; }

    [JsonProperty("channel_name")]
    public string ChannelName { get; set; }


    [JsonConstructor]
    public OnTicketCommonEventBodyTicketChannel(
        string channelType,
        string channelIdentityId,
        string channelName)
    {
        ChannelType = channelType;
        ChannelIdentityId = channelIdentityId;
        ChannelName = channelName;
    }
}