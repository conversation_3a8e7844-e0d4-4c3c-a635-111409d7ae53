using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class SwapWorkflows : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public SwapWorkflows(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class SwapWorkflowsInput : IHasSleekflowStaff, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("source_workflow_versioned_id")]
        [Required]
        public string SourceWorkflowVersionedId { get; set; }

        [JsonProperty("target_workflow_versioned_id")]
        [Required]
        public string TargetWorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public SwapWorkflowsInput(
            string sleekflowCompanyId,
            string sourceWorkflowVersionedId,
            string targetWorkflowVersionedId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SourceWorkflowVersionedId = sourceWorkflowVersionedId;
            TargetWorkflowVersionedId = targetWorkflowVersionedId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class SwapWorkflowsOutput
    {
        [JsonProperty("source_workflow")]
        public WorkflowDto SourceWorkflow { get; set; }

        [JsonProperty("target_workflow")]
        public WorkflowDto TargetWorkflow { get; set; }

        [JsonConstructor]
        public SwapWorkflowsOutput(WorkflowDto sourceWorkflow, WorkflowDto targetWorkflow)
        {
            SourceWorkflow = sourceWorkflow;
            TargetWorkflow = targetWorkflow;
        }
    }

    public async Task<SwapWorkflowsOutput> F(SwapWorkflowsInput swapWorkflowsInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            swapWorkflowsInput.SleekflowStaffId,
            swapWorkflowsInput.SleekflowStaffTeamIds);

        var (source, target) = await _workflowService.SwapWorkflowsAsync(
            swapWorkflowsInput.SourceWorkflowVersionedId,
            swapWorkflowsInput.TargetWorkflowVersionedId,
            swapWorkflowsInput.SleekflowCompanyId,
            sleekflowStaff);

        return new SwapWorkflowsOutput(new WorkflowDto(source), new WorkflowDto(target));
    }
}