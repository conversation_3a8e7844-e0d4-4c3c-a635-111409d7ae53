﻿using MassTransit.Testing;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Hubspot.Subscriptions;

public interface IHubspotSubscriptionService
{
    Task ClearAsync(string entityTypeName, string sleekflowCompanyId);

    Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval);

    Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval, string connectionId, bool isFlowsBased);

    Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId);

    Task<List<HubspotSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName);

    Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId);
}

public class HubspotSubscriptionService : IHubspotSubscriptionService, ISingletonService
{
    private readonly IHubspotSubscriptionRepository _hubspotSubscriptionRepository;
    private readonly IIdService _idService;

    public HubspotSubscriptionService(
        IHubspotSubscriptionRepository hubspotSubscriptionRepository,
        IIdService idService)
    {
        _hubspotSubscriptionRepository = hubspotSubscriptionRepository;
        _idService = idService;
    }

    public async Task ClearAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _hubspotSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == HubspotSubscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _hubspotSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval)
    {
        var subscription = await _hubspotSubscriptionRepository.GetObjectEnumerableAsync(
                s =>
                    s.EntityTypeName == entityTypeName
                    && s.SysTypeName == HubspotSubscription.SysTypeNameValue
                    && s.SleekflowCompanyId == sleekflowCompanyId)
            .FirstOrDefault();
        if (subscription != null)
        {
            subscription.Interval = interval;

            await _hubspotSubscriptionRepository.ReplaceAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                subscription);

            return;
        }

        var createCount = await _hubspotSubscriptionRepository.CreateAsync(
            new HubspotSubscription(
                _idService.GetId(HubspotSubscription.SysTypeNameValue),
                sleekflowCompanyId,
                entityTypeName,
                interval,
                null,
                null,
                DateTimeOffset.UtcNow,
                null,
                null),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task UpsertAsync(
        string entityTypeName,
        string sleekflowCompanyId,
        int interval,
        string connectionId,
        bool isFlowsBased)
    {
        var subscription = await _hubspotSubscriptionRepository.GetObjectEnumerableAsync(
                s =>
                    s.EntityTypeName == entityTypeName
                    && s.SysTypeName == HubspotSubscription.SysTypeNameValue
                    && s.SleekflowCompanyId == sleekflowCompanyId
                    && s.ConnectionId == connectionId
                    && s.IsFlowsBased == isFlowsBased)
            .FirstOrDefault();
        if (subscription != null)
        {
            subscription.Interval = interval;

            await _hubspotSubscriptionRepository.ReplaceAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                subscription);

            return;
        }

        var createCount = await _hubspotSubscriptionRepository.CreateAsync(
            new HubspotSubscription(
                _idService.GetId(HubspotSubscription.SysTypeNameValue),
                sleekflowCompanyId,
                entityTypeName,
                interval,
                connectionId,
                isFlowsBased,
                DateTimeOffset.UtcNow,
                null,
                null),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _hubspotSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == HubspotSubscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _hubspotSubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{HubspotSubscription.PropertyNameDurablePayload}",
                        new object()),
                });
        }
    }

    public async Task<List<HubspotSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName)
    {
        return await _hubspotSubscriptionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId
                 && x.ConnectionId == connectionId
                 && x.EntityTypeName == entityTypeName);
    }

    public async Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var subscription in _hubspotSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.SysTypeName == SalesforceSubscription.SysTypeNameValue
                               && s.ConnectionId == connectionId
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _hubspotSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }
}