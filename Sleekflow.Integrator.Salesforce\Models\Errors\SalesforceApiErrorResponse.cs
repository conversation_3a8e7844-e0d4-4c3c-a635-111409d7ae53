﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Salesforce.Models.Errors;

public class SalesforceApiErrorResponse
{
    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("errorCode")]
    public string? ErrorCode { get; set; }

    [JsonConstructor]
    public SalesforceApiErrorResponse(string? message, string? errorCode)
    {
        Message = message;
        ErrorCode = errorCode;
    }
}