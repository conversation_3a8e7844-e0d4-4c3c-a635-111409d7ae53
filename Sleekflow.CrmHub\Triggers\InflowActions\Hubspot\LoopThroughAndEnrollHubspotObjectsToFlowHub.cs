﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Hubspot;

[TriggerGroup(TriggerGroups.InflowActions)]
public class LoopThroughAndEnrollHubspotObjectsToFlowHub : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public LoopThroughAndEnrollHubspotObjectsToFlowHub(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class LoopThroughAndEnrollHubspotObjectsToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("hubspot_connection_id")]
        [Required]
        public string HubspotConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollHubspotObjectsToFlowHubInput(
            string sleekflowCompanyId,
            string hubspotConnectionId,
            string entityTypeName,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId,
            bool isCustomObject)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            HubspotConnectionId = hubspotConnectionId;
            EntityTypeName = entityTypeName;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
            IsCustomObject = isCustomObject;
        }
    }

    public class LoopThroughAndEnrollHubspotObjectsToFlowHubOutput
    {
        [JsonProperty("loop_through_objects_progress_state_id")]
        public string LoopThroughObjectsProgressStateId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollHubspotObjectsToFlowHubOutput(string loopThroughObjectsProgressStateId)
        {
            LoopThroughObjectsProgressStateId = loopThroughObjectsProgressStateId;
        }
    }

    public async Task<LoopThroughAndEnrollHubspotObjectsToFlowHubOutput> F(
        LoopThroughAndEnrollHubspotObjectsToFlowHubInput loopThroughAndEnrollHubspotObjectsToFlowHubInput)
    {
        var hubspotProviderService = _providerSelector.GetProviderService(
            "hubspot-integrator");

        var sleekflowCompanyId = loopThroughAndEnrollHubspotObjectsToFlowHubInput.SleekflowCompanyId;
        var hubspotConnectionId = loopThroughAndEnrollHubspotObjectsToFlowHubInput.HubspotConnectionId;
        var entityTypeName = loopThroughAndEnrollHubspotObjectsToFlowHubInput.EntityTypeName;
        var flowHubWorkflowId = loopThroughAndEnrollHubspotObjectsToFlowHubInput.FlowHubWorkflowId;
        var flowHubWorkflowVersionedId = loopThroughAndEnrollHubspotObjectsToFlowHubInput.FlowHubWorkflowVersionedId;
        var isCustomObject = loopThroughAndEnrollHubspotObjectsToFlowHubInput.IsCustomObject;

        await hubspotProviderService.TerminateInProgressLoopThroughExecutionsAsync(
            flowHubWorkflowId,
            sleekflowCompanyId);

        var output = await hubspotProviderService.LoopThroughAndEnrollObjectsToFlowHubAsync(
            sleekflowCompanyId,
            hubspotConnectionId,
            entityTypeName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            isCustomObject);

        return new LoopThroughAndEnrollHubspotObjectsToFlowHubOutput(output.LoopThroughObjectsProgressStateId);
    }
}