using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.CommerceHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class CommerceHubModules
{
    public static void BuildCommerceHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON>d<PERSON><PERSON><PERSON><ICommerceHubDbConfig>(new Mock<ICommerceHubDbConfig>().Object);
        b.<PERSON><PERSON><ICommerceHubDbResolver>(new Mock<ICommerceHubDbResolver>().Object);

#else
        var commerceHubDbConfig = new CommerceHubDbConfig();

        b.<PERSON><PERSON><PERSON><PERSON><PERSON><ICommerceHubDbConfig>(commerceHubDbConfig);
        b.<PERSON><PERSON><PERSON><PERSON><PERSON><ICommerceHubDbResolver, CommerceHubDbResolver>();
#endif
    }
}