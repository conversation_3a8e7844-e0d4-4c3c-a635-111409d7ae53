using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

[ContainerId(ContainerNames.WorkflowExecution)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class WorkflowExecution : Entity, IHasCreatedAt
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    public StateIdentity StateIdentity { get; set; }

    [JsonProperty("workflow_execution_status")]
    public string WorkflowExecutionStatus { get; set; }

    [JsonProperty(WorkflowExecutionFieldNames.NumOfNodesExecuted)]
    public int? NumOfNodesExecuted { get; set; }

    [JsonProperty(WorkflowExecutionFieldNames.WorkflowExecutionReasonCode)]
    public string? WorkflowExecutionReasonCode { get; set; }

    [JsonProperty("workflow_type")]
    public string? WorkflowType { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(AuditEntity.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonConstructor]
    public WorkflowExecution(
        string id,
        int? ttl,
        string sleekflowCompanyId,
        string stateId,
        StateIdentity stateIdentity,
        string workflowExecutionStatus,
        int? numOfNodesExecuted,
        string? workflowExecutionReasonCode,
        string? workflowType,
        DateTimeOffset createdAt,
        AuditEntity.SleekflowStaff? createdBy)
        : base(id, SysTypeNames.WorkflowExecution, ttl)
    {
        StateId = stateId;
        SleekflowCompanyId = sleekflowCompanyId;
        StateIdentity = stateIdentity;
        WorkflowExecutionStatus = workflowExecutionStatus;
        NumOfNodesExecuted = numOfNodesExecuted;
        WorkflowExecutionReasonCode = workflowExecutionReasonCode;
        WorkflowType = workflowType;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
    }
}