using Newtonsoft.Json;
using Sleekflow.DurablePayloads;

namespace Sleekflow.CommerceHub.Models.Workers;

public class ProcessCustomCatalogCsvOutput : DurablePayload
{
    [JsonConstructor]
    public ProcessCustomCatalogCsvOutput(
        string id,
        string statusQueryGetUri,
        string sendEventPostUri,
        string terminatePostUri,
        string rewindPostUri,
        string purgeHistoryDeleteUri,
        string restartPostUri)
        : base(
            id,
            statusQueryGetUri,
            sendEventPostUri,
            terminatePostUri,
            rewindPostUri,
            purgeHistoryDeleteUri,
            restartPostUri)
    {
    }
}