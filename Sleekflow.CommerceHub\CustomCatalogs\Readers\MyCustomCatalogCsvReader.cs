using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using Azure.Storage.Blobs;
using CsvHelper;
using CsvHelper.Configuration;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.CustomCatalogs.Readers;

#pragma warning disable SA1601
public partial class MyCustomCatalogCsvReader
#pragma warning restore SA1601
{
    private const string PropertyCategoryName = "category_name";
    private const string PropertySku = "sku_code";
    private const string PropertyUrl = "url";
    private const string PropertyProductName = "product_name";
    private const string PropertyDescription = "description";
    private const string PropertyPrice = "price";
    private const string PropertyImageUrls = "image_urls";
    private const string PropertyImageBlobIds = "image_blob_ids";
    private const string PropertyAttribute = "attribute";
    private const string PropertyMetadata = "metadata";

    private readonly BlobClient _blobClient;
    private readonly ILogger<MyCustomCatalogCsvReader> _logger;

    private bool _canRead = true;

    private long _lastByteCount;
    private long _lastBytePosition;
    private long _numOfRecords;
    private string[]? _headers;

    public MyCustomCatalogCsvReader(
        BlobClient blobClient,
        MyCustomCatalogCsvReaderState? myCustomCatalogCsvReader,
        ILogger<MyCustomCatalogCsvReader> logger)
    {
        _blobClient = blobClient;
        _logger = logger;

        if (myCustomCatalogCsvReader == null)
        {
            _lastByteCount = 0;
            _lastBytePosition = 0;
            _numOfRecords = 0;
            _headers = null;
        }
        else
        {
            _lastByteCount = 0;
            _lastBytePosition = myCustomCatalogCsvReader.LastBytePosition;
            _numOfRecords = myCustomCatalogCsvReader.NumOfRecords;
            _headers = myCustomCatalogCsvReader.Headers;
        }
    }

    public static string GetCsvTemplate()
    {
        return string.Empty
               + PropertyCategoryName + "_{ISO-639-1-lang-code},"
               + PropertySku + ","
               + PropertyUrl + ","
               + PropertyProductName + "_{ISO-639-1-lang-code},"
               + PropertyDescription + "__{idx}_text_{ISO-639-1-lang-code},"
               + PropertyPrice + "_{3-letter-currency-code},"
               + PropertyImageUrls + ","
               + PropertyImageBlobIds + ","
               + PropertyAttribute + "__{attribute_name}_{ISO-639-1-lang-code},"
               + PropertyMetadata + "_{name}";
    }

    public static string GetCsvTemplateSample()
    {
        return string.Empty
               + PropertySku + ","
               + PropertyUrl + ","
               + PropertyProductName + "_en,"
               + PropertyDescription + "__1_text_en,"
               + PropertyPrice + "_HKD,"
               + PropertyImageUrls + "\n"
               + "SKU-001,"
               + "https://example.com/p/SKU-001,"
               + "Hello Drink 1,"
               + "Drink description 1,"
               + "100.0,"
               + "\"https://sc32add32.blob.core.windows.net/public-container/drink-1.jpg\"\n"
               + "SKU-002,"
               + "https://example.com/p/SKU-002,"
               + "Hello Drink 2,"
               + "Drink description 2,"
               + "250.0,"
               + "\"https://sc32add32.blob.core.windows.net/public-container/drink-2.jpg\"\n"
               + "SKU-003,"
               + "https://example.com/p/SKU-003,"
               + "Hello Drink 3,"
               + "Drink description 3,"
               + "500.0,"
               + "\"https://sc32add32.blob.core.windows.net/public-container/drink-3.jpg\"\n"
               + "SKU-004,"
               + "https://example.com/p/SKU-004,"
               + "Hello Drink 4,"
               + "Drink description 4,"
               + "100.0,"
               + "\"https://sc32add32.blob.core.windows.net/public-container/drink-4.jpg\"\n";
    }

    public MyCustomCatalogCsvReaderState GetState()
    {
        var isCompleted = !_canRead;

        return new MyCustomCatalogCsvReaderState(
            _lastBytePosition,
            _numOfRecords,
            _headers,
            isCompleted);
    }

    public async Task<string[]> GetHeadersAsync()
    {
        if (_headers != null)
        {
            return _headers;
        }

        if (_lastBytePosition != 0)
        {
            throw new Exception("The headers have already been read.");
        }

        await using var stream = await _blobClient.OpenReadAsync(false, 0L, 1024);
        using var streamReader = new StreamReader(stream, Encoding.UTF8);
        using var csv = new CsvReader(
            streamReader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                CountBytes = true
            });

        _canRead = await csv.ReadAsync();
        _canRead = csv.ReadHeader();

        _lastBytePosition = csv.Parser.ByteCount;
        _headers = csv.HeaderRecord!;

        var duplicatedHeaders = _headers
            .GroupBy(h => h)
            .Where(g => g.Count() > 1)
            .ToList();
        if (duplicatedHeaders.Any())
        {
            throw new SfUserFriendlyException(
                $"There are some duplicated headers {string.Join(", ", duplicatedHeaders.Select(g => g.Key))}");
        }

        return _headers;
    }

    public async Task<List<MyReaderEntry>> ReadLinesAsync(int limit = 1)
    {
        if (_headers == null)
        {
            throw new Exception("Please get headers first");
        }

        if (!_canRead)
        {
            throw new Exception("No more records to read");
        }

        var entries = new List<MyReaderEntry>();

        await using var stream = await _blobClient.OpenReadAsync(false, _lastBytePosition, 1024);
        using var streamReader = new StreamReader(stream, Encoding.UTF8);
        using var csv = new CsvReader(
            streamReader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                CountBytes = true
            });

        const string suffixedProductName = PropertyProductName + "_";
        const string suffixedCategoryName = PropertyCategoryName + "_";
        const string suffixedPrice = PropertyPrice + "_";
        const string suffixedDescription = PropertyDescription + "__";
        const string suffixedAttribute = PropertyAttribute + "__";
        const string suffixedMetadata = "metadata_";

        var globalLimit = _numOfRecords + limit;
        while (true)
        {
            _canRead = await csv.ReadAsync();
            if (!_canRead)
            {
                break;
            }

            var categoryNames = new Dictionary<string, string>();
            var sku = (string?) null;
            var url = (string?) null;
            var names = new Dictionary<string, string>();
            var descriptions = new List<MyReaderProductDescriptionDto>();
            var prices = new Dictionary<string, string>();
            var imageUrls = new List<string>();
            var imageBlobIds = new List<string>();
            var attributes = new Dictionary<string, Dictionary<string, string>>();
            var metadata = new Dictionary<string, string>();

            try
            {
                for (var headerIdx = 0; headerIdx < _headers!.Length; headerIdx++)
                {
                    var header = _headers[headerIdx];

                    if (header.StartsWith(suffixedCategoryName))
                    {
                        categoryNames.Add(
                            header.Substring(suffixedCategoryName.Length),
                            csv.GetField(headerIdx)!);
                    }
                    else if (header == PropertySku)
                    {
                        sku = csv.GetField(headerIdx)!;
                    }
                    else if (header == PropertyUrl)
                    {
                        url = csv.GetField(headerIdx)!;
                    }
                    else if (header.StartsWith(suffixedProductName))
                    {
                        names.Add(header[suffixedProductName.Length..], csv.GetField(headerIdx)!);
                    }
                    else if (header.StartsWith(suffixedDescription))
                    {
                        var regex = DescriptionRegex();
                        var match = regex.Match(header);

                        var idx = int.Parse(match.Groups[1].Value);
                        var type = match.Groups[2].Value;
                        var threeLetterLanguageCode = match.Groups[3].Value;

                        descriptions.Add(
                            new MyReaderProductDescriptionDto(
                                idx,
                                type,
                                threeLetterLanguageCode,
                                csv.GetField(headerIdx)!));
                    }
                    else if (header.StartsWith(suffixedPrice))
                    {
                        prices.Add(header[suffixedPrice.Length..], csv.GetField(headerIdx)!);
                    }
                    else if (header == PropertyImageUrls)
                    {
                        if (!string.IsNullOrEmpty(csv.GetField(headerIdx)))
                        {
                            imageUrls.Add(csv.GetField(headerIdx)!);
                        }
                    }
                    else if (header == PropertyImageBlobIds)
                    {
                        imageBlobIds.Add(csv.GetField(headerIdx)!);
                    }
                    else if (header.StartsWith(suffixedAttribute))
                    {
                        var regex = AttributeRegex();
                        var match = regex.Match(header);

                        var attributeName = match.Groups[1].Value;
                        var threeLetterLanguageCode = match.Groups[2].Value;

                        attributes.TryAdd(attributeName, new Dictionary<string, string>());
                        attributes[attributeName].Add(threeLetterLanguageCode, csv.GetField(headerIdx)!);
                    }
                    else if (header.StartsWith(suffixedMetadata))
                    {
                        metadata.Add(header[suffixedMetadata.Length..], csv.GetField(headerIdx)!);
                    }
                    else
                    {
                        throw new Exception($"The header=[{header}] is unknown");
                    }
                }

                entries.Add(
                    new MyReaderEntry(
                        null,
                        new MyReaderProductDto(
                            categoryNames,
                            sku,
                            url,
                            names,
                            descriptions,
                            prices,
                            imageUrls,
                            imageBlobIds,
                            attributes,
                            metadata),
                        csv.Parser.Row));
            }
            catch (Exception e)
            {
                var message = $"Unable to read the csv at the row=[{csv.Parser.Row}], message=[{e.Message}]";

                _logger.LogError(
                    e,
                    message);

                entries.Add(
                    new MyReaderEntry(
                        message,
                        null,
                        csv.Parser.Row));
            }

            _lastBytePosition += csv.Parser.ByteCount - _lastByteCount;
            _lastByteCount = csv.Parser.ByteCount;
            _numOfRecords++;

            if (!_canRead || _numOfRecords >= globalLimit)
            {
                break;
            }
        }

        return entries;
    }

    public async Task<long> ReadLineCountAsync()
    {
        if (_headers == null)
        {
            throw new Exception("Please get headers first");
        }

        if (!_canRead)
        {
            throw new Exception("No more records to read");
        }

        // Buffer size 1 MB = 1024 KB and 1 KB = 1024 bytes -> 16 * 1,048,576 = 16,777,216 bytes
        await using var stream = await _blobClient.OpenReadAsync(false, _lastBytePosition, 16777216);
        using var streamReader = new StreamReader(stream, Encoding.UTF8);
        using var csv = new CsvReader(
            streamReader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                CountBytes = true
            });

        while (true)
        {
            _canRead = await csv.ReadAsync();
            if (!_canRead)
            {
                break;
            }

            _lastBytePosition += csv.Parser.ByteCount - _lastByteCount;
            _lastByteCount = csv.Parser.ByteCount;
            _numOfRecords++;

            if (!_canRead)
            {
                break;
            }
        }

        return _numOfRecords;
    }

    [GeneratedRegex("^attribute__(.+)_([a-zA-Z]{1,16})$")]
    private static partial Regex AttributeRegex();

    [GeneratedRegex("^description__([0-9]+)_(text)+_([a-zA-Z]{1,16})$")]
    private static partial Regex DescriptionRegex();
}