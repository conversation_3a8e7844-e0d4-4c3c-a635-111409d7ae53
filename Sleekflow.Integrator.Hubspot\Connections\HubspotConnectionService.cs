﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Hubspot.Connections;

public interface IHubspotConnectionService
{
    Task<List<HubspotConnection>> GetConnectionsAsync(
        string sleekflowCompanyId);

    Task<HubspotConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string? organizationId,
        string authenticationId,
        string? name,
        string environment,
        bool isActive);

    Task<HubspotConnection> GetByIdAsync(
        string id,
        string sleekflowCompanyId);

    Task<HubspotConnection?> GetByAuthenticationIdAsync(
        string sleekflowCompanyId,
        string authenticationId);

    Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        string? name,
        bool isActive);

    Task<HubspotConnection> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string? name);

    Task DeleteAsync(
        string id,
        string sleekflowCompanyId);
}

public class HubspotConnectionService : ISingletonService, IHubspotConnectionService
{
    private readonly IHubspotConnectionRepository _hubspotConnectionRepository;
    private readonly IIdService _idService;

    public HubspotConnectionService(
        IHubspotConnectionRepository hubspotConnectionRepository,
        IIdService idService)
    {
        _hubspotConnectionRepository = hubspotConnectionRepository;
        _idService = idService;
    }

    public async Task<List<HubspotConnection>> GetConnectionsAsync(string sleekflowCompanyId)
    {
        return await _hubspotConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<HubspotConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string? organizationId,
        string authenticationId,
        string? name,
        string environment,
        bool isActive)
    {
        var connection = new HubspotConnection(
            _idService.GetId("HubspotConnection"),
            sleekflowCompanyId,
            organizationId,
            authenticationId,
            name,
            environment,
            isActive);

        return await _hubspotConnectionRepository.CreateAndGetAsync(connection, sleekflowCompanyId);
    }

    public async Task<HubspotConnection> GetByIdAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await _hubspotConnectionRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<HubspotConnection?> GetByAuthenticationIdAsync(
        string sleekflowCompanyId,
        string authenticationId)
    {
        var connections = await _hubspotConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId && x.AuthenticationId == authenticationId);

        return connections.FirstOrDefault();
    }

    public async Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        string? name,
        bool isActive)
    {
        await _hubspotConnectionRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
                PatchOperation.Replace("/is_active", isActive),
            });
    }

    public async Task<HubspotConnection> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string? name)
    {
        return await _hubspotConnectionRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
            });
    }

    public async Task DeleteAsync(
        string id,
        string sleekflowCompanyId)
    {
        var connection = await _hubspotConnectionRepository.GetAsync(
            id,
            sleekflowCompanyId);
        if (connection is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var deleteAsync = await _hubspotConnectionRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the HubspotConnection with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}