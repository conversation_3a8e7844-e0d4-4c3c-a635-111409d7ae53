using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class AssignError : ITrigger
{
    private readonly IStateAggregator _stateAggregator;
    private readonly IWorkflowStepLocator _workflowStepLocator;
    private readonly IStateService _stateService;

    public AssignError(
        IStateAggregator stateAggregator,
        IWorkflowStepLocator workflowStepLocator,
        IStateService stateService)
    {
        _stateAggregator = stateAggregator;
        _workflowStepLocator = workflowStepLocator;
        _stateService = stateService;
    }

    public class AssignErrorInput : Sleekflow.FlowHub.Models.Internals.AssignErrorInput
    {
        [JsonConstructor]
        public AssignErrorInput(
            string stateId,
            string tryCatchStepId,
            Stack<StackEntry> stackEntries,
            UserFriendlyError error)
            : base(stateId, tryCatchStepId, stackEntries, error)
        {
        }
    }

    public class AssignErrorOutput : Sleekflow.FlowHub.Models.Internals.AssignErrorOutput
    {
        [JsonConstructor]
        public AssignErrorOutput()
        {
        }
    }

    public async Task<AssignErrorOutput> F(AssignErrorInput assignErrorInput)
    {
        var proxyState = await _stateService.GetProxyStateAsync(assignErrorInput.StateId);
        var step = _workflowStepLocator.GetStep(
            proxyState.WorkflowContext.SnapshottedWorkflow,
            assignErrorInput.TryCatchStepId);

        if (step is TryCatchStep tryStep)
        {
            await _stateAggregator.AggregateStateError(proxyState, tryStep.Catch.As, assignErrorInput.Error);
        }
        else
        {
            throw new NotImplementedException();
        }

        return new AssignErrorOutput();
    }
}