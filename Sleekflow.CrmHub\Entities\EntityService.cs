﻿using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.EntityEvents;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.EntityEvents;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.Entities;

public class Identity
{
    [JsonProperty("phone_number")]
    public string? PhoneNumber { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("external_id")]
    public string? ExternalId { get; set; }

    [JsonProperty("object_id")]
    public string? ObjectId { get; set; }

    public Identity(
        string? phoneNumber,
        string? email,
        string? externalId,
        string? objectId)
    {
        PhoneNumber = phoneNumber;
        Email = email;
        ExternalId = externalId;
        ObjectId = objectId;
    }
}

public interface IEntityService
{
    Task<CrmHubEntity> GetAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName);

    Task<CrmHubEntity?> GetOrDefaultAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName);

    Task<List<CrmHubEntity>> GetObjectsByIdentitiesAsync(
        List<Identity> identities,
        string sleekflowCompanyId,
        string entityTypeName);

    Task UpsertAsync(
        string entityTypeName,
        Dictionary<string, object?> currDict,
        Dictionary<string, (object? FromValue, object? ToValue)> propModDict,
        string entityId,
        string providerName,
        string sleekflowCompanyId);
}

public class EntityService : ISingletonService, IEntityService
{
    private readonly ILogger<EntityService> _logger;
    private readonly IIdService _idService;
    private readonly IEntityRepository _entityRepository;
    private readonly IEntityEventRepository _entityEventRepository;

    public EntityService(
        ILogger<EntityService> logger,
        IIdService idService,
        IEntityRepository entityRepository,
        IEntityEventRepository entityEventRepository)
    {
        _logger = logger;
        _idService = idService;
        _entityRepository = entityRepository;
        _entityEventRepository = entityEventRepository;
    }

    public async Task<CrmHubEntity> GetAsync(string id, string sleekflowCompanyId, string entityTypeName)
    {
        var entities = await _entityRepository.GetObjectsAsync(
            e =>
                (string) e[CrmHubEntityContext.PropertyNameId]! == id
                && (string) e[CrmHubEntityContext.PropertyNameSysSleekflowCompanyId]! == sleekflowCompanyId
                && (string) e[CrmHubEntityContext.PropertyNameSysEntityTypeName]! == entityTypeName
                && (string) e[CrmHubEntityContext.PropertyNameSysTypeName]! == "Entity");

        if (entities.Count != 1)
        {
            throw new SfInternalErrorException("The object does not exist");
        }

        return entities[0];
    }

    public async Task<CrmHubEntity?> GetOrDefaultAsync(string id, string sleekflowCompanyId, string entityTypeName)
    {
        var entities = await _entityRepository.GetObjectsAsync(
            e =>
                (string) e[CrmHubEntityContext.PropertyNameId]! == id
                && (string) e[CrmHubEntityContext.PropertyNameSysSleekflowCompanyId]! == sleekflowCompanyId
                && (string) e[CrmHubEntityContext.PropertyNameSysEntityTypeName]! == entityTypeName
                && (string) e[CrmHubEntityContext.PropertyNameSysTypeName]! == "Entity");

        if (entities.Count > 0)
        {
            return entities[0];
        }

        return default;
    }

    public async Task<List<CrmHubEntity>> GetObjectsByIdentitiesAsync(
        List<Identity> identities,
        string sleekflowCompanyId,
        string entityTypeName)
    {
        var externalIds = identities
            .Where(i => i.ExternalId != null)
            .Select(i => i.ExternalId)
            .ToArray();
        var entityIds = identities
            .Where(i => i.ObjectId != null)
            .Select(i => i.ObjectId)
            .ToArray();
        var phoneNumberEmailIdentities = identities
            .Where(i => i.PhoneNumber != null && i.Email != null)
            .SelectMany(
                i =>
                {
                    return new string?[][]
                    {
                        new[]
                        {
                            i.PhoneNumber,
                            i.Email
                        },
                        new[]
                        {
                            null,
                            i.Email
                        },
                        new[]
                        {
                            i.PhoneNumber,
                            null
                        },
                    };
                })
            .ToArray();
        var phoneNumberIdentities = identities
            .Where(i => i.PhoneNumber != null && i.Email == null)
            .SelectMany(
                i =>
                {
                    return new string?[][]
                    {
                        new[]
                        {
                            i.PhoneNumber,
                            null
                        },
                    };
                })
            .ToArray();
        var emailIdentities = identities
            .Where(i => i.PhoneNumber == null && i.Email != null)
            .SelectMany(
                i => new string?[][]
                {
                    new[]
                    {
                        null,
                        i.Email
                    },
                })
            .ToArray();

        // @formatter:off
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE ("
                        + "ARRAY_CONTAINS(@phoneNumberEmailIdentities, [c.sys_resolved_phone_number, c.sys_resolved_email]) "
                        + "OR ARRAY_CONTAINS(@phoneNumberIdentities, [c.sys_resolved_phone_number, null]) "
                        + "OR ARRAY_CONTAINS(@emailIdentities, [null, c.sys_resolved_email]) "
                        + "OR ARRAY_CONTAINS(@entityIds, c.id) "
                        + "OR ARRAY_CONTAINS(@externalIds, c.ctx_external_id) "
                        + "OR EXISTS(SELECT VALUE n FROM n IN c.ctx_external_ids WHERE ARRAY_CONTAINS (@externalIds, n))"
                    + ") "
                    + "AND c.sys_sleekflow_company_id = @sleekflowCompanyId "
                    + "AND c.sys_entity_type_name = @entityTypeName "
                    + "AND c.sys_type_name = 'Entity' ")
                .WithParameter("@phoneNumberEmailIdentities", phoneNumberEmailIdentities)
                .WithParameter("@phoneNumberIdentities", phoneNumberIdentities)
                .WithParameter("@emailIdentities", emailIdentities)
                .WithParameter("@entityIds", entityIds)
                .WithParameter("@externalIds", externalIds)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@entityTypeName", entityTypeName);

        // @formatter:on
        _logger.LogInformation(
            "Executing queryText {QueryText}, queryParameters {QueryParameters}",
            queryDefinition.QueryText,
            queryDefinition.GetQueryParameters());

        return await _entityRepository.GetObjectsAsync(queryDefinition);
    }

    public async Task UpsertAsync(
        string entityTypeName,
        Dictionary<string, object?> currDict,
        Dictionary<string, (object? FromValue, object? ToValue)> propModDict,
        string entityId,
        string providerName,
        string sleekflowCompanyId)
    {
        if (propModDict.Any() == false)
        {
            // If no changes, no upsert
            return;
        }

        await UpsertEntityEventAsync(
            currDict,
            propModDict,
            entityId,
            providerName,
            sleekflowCompanyId,
            entityTypeName);
        await UpsertEntityAsync(currDict);
    }

    private async Task UpsertEntityAsync(
        Dictionary<string, object?> currDict)
    {
        if (currDict["_etag"] != null)
        {
            await _entityRepository.ReplaceAsync(
                id: (string) currDict[CrmHubEntityContext.PropertyNameId]!,
                partitionKey: (string) currDict[CrmHubEntityContext.PropertyNameId]!,
                obj: (CrmHubEntity) currDict,
                eTag: (string) currDict[CrmHubEntityContext.PropertyNameETag]!);
        }
        else
        {
            await _entityRepository.ReplaceAsync(
                id: (string) currDict[CrmHubEntityContext.PropertyNameId]!,
                partitionKey: (string) currDict[CrmHubEntityContext.PropertyNameId]!,
                obj: (CrmHubEntity) currDict);
        }
    }

    private async Task UpsertEntityEventAsync(
        Dictionary<string, object?> currDict,
        Dictionary<string, (object? FromValue, object? ToValue)> propModDict,
        string entityId,
        string providerName,
        string sleekflowCompanyId,
        string entityTypeName)
    {
        // If only updatedAt is changed, no upsert
        var updatedAt = new List<string>()
        {
            "sleekflow:UpdatedAt"
        };

        if (propModDict.All(d => updatedAt.Contains(d.Key))
            || propModDict.Count == 0)
        {
            return;
        }

        var entityEventTypeName = currDict[CrmHubEntity.PropertyNameSysVersion] switch
        {
            1L => EntityEventTypeNames.New,
            > 1L => EntityEventTypeNames.Update,
            _ => EntityEventTypeNames.Unknown
        };

        var @event = new EntityEvent(
            _idService.GetId("EntityEvent", parentId: entityId),
            propModDict
                .Select(
                    e => new EntityEventChangeEntry(
                        e.Key,
                        e.Value.FromValue,
                        e.Value.ToValue))
                .ToList(),
            DateTimeOffset.UtcNow,
            providerName,
            sleekflowCompanyId,
            sysPartitionId: entityId,
            entityId: entityId,
            entityEventTypeName,
            entityTypeName,
            60 * 60 * 24 * 30);

        var createCount = await _entityEventRepository.CreateAsync(
            @event,
            new PartitionKeyBuilder()
                .Add(sleekflowCompanyId)
                .Add(entityId)
                .Build());
        if (createCount == 0)
        {
            _logger.LogError(
                "Unable to create the EntityEvent {EntityEvent}",
                @event);
        }
    }

    public static CrmHubEntity Sanitize(Dictionary<string, object?> rawRecord)
    {
        var dict = new CrmHubEntity();

        foreach (var (key, value) in rawRecord)
        {
            switch (key)
            {
                case "_attachments":
                case "_rid":
                case "_self":
                case "_ts":
                case CrmHubEntityContext.PropertyNameSysEntityTypeName:
                case CrmHubEntityContext.PropertyNameSysTypeName:
                case CrmHubEntityContext.PropertyNameETag:
                    continue;
                case CrmHubEntityContext.PropertyNameId:
                    dict["id"] = value;
                    break;
                default:
                {
                    if (value is JObject jObject)
                    {
                        if (jObject.ContainsKey(SnapshottedValue.PropertyNameValue))
                        {
                            dict[key] = jObject.GetValue(SnapshottedValue.PropertyNameValue);
                        }
                        else if (jObject.ContainsKey("value"))
                        {
                            dict[key] = jObject.GetValue("value");
                        }
                    }
                    else
                    {
                        dict[key] = value;
                    }

                    break;
                }
            }
        }

        return dict;
    }
}