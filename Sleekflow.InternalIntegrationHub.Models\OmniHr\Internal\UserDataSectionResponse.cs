using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class UserDataSectionResponse
{
    [JsonProperty(PropertyName = "id")]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "name")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "attributes")]
    public List<object> Attributes { get; set; }

    [JsonProperty(PropertyName = "section_type")]
    public int SectionType { get; set; }

    [JsonConstructor]
    public UserDataSectionResponse(
        int id,
        string name,
        List<object> attributes,
        int sectionType)
    {
        Id = id;
        Name = name;
        Attributes = attributes;
        SectionType = sectionType;
    }
}