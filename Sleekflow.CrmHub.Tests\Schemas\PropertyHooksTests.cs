﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests.Schemas;

public class PropertyHooksTests
{
    private IDefaultPropertyHook _defaultPropertyHook = null!;
    private ISingleChoicePropertyHook _singleChoicePropertyHook = null!;
    private IMultipleChoicePropertyHook _multipleChoicePropertyHook = null!;
    private IArrayObjectPropertyHook _arrayObjectPropertyHook = null!;

    [SetUp]
    public void SetUp()
    {
        var services = new ServiceCollection();

        var idService = GetIdService();

        services.AddSingleton<IIdService>(idService);
        services.AddSingleton<ISchemaRepository>(GetSchemaRepository());
        services.AddSingleton<ISchemaValidator, SchemaValidator>();

        services.AddSingleton<IOptionService, OptionService>();
        services.AddSingleton<IDataTypeInnerSchemaService, DataTypeInnerSchemaService>();
        services.AddSingleton<IDefaultPropertyHook, DefaultPropertyHook>();
        services.AddSingleton<ISingleChoicePropertyHook, SingleChoicePropertyHook>();
        services.AddSingleton<IMultipleChoicePropertyHook, MultipleChoicePropertyHook>();
        services.AddSingleton<IArrayObjectPropertyHook, ArrayObjectPropertyHook>();

        services.AddSingleton<IPropertyConstructor, PropertyConstructor>();

        var serviceProvider = services.BuildServiceProvider();

        _defaultPropertyHook = serviceProvider.GetRequiredService<IDefaultPropertyHook>();
        _singleChoicePropertyHook = serviceProvider.GetRequiredService<ISingleChoicePropertyHook>();
        _multipleChoicePropertyHook = serviceProvider.GetRequiredService<IMultipleChoicePropertyHook>();
        _arrayObjectPropertyHook = serviceProvider.GetRequiredService<IArrayObjectPropertyHook>();
    }

    [Test]
    public void DefaultPropertyHook_WithCorrectInputs_ShouldReturnCorrectValues()
    {
       /*
        * PreConstruct(PropertyInput propertyInput)
        */
       var booleanPropertyInput = new PropertyInput(
           "Test Boolean",
           "boolean",
           new BooleanDataType(),
           false,
           false,
           false,
           false,
           false,
           0,
           null,
           null);

       var res_PrCconstruct_ByPropertyInput = _defaultPropertyHook.PreConstruct(booleanPropertyInput);

       Assert.That(res_PrCconstruct_ByPropertyInput.DataType, Is.TypeOf<BooleanDataType>());
       Assert.That(res_PrCconstruct_ByPropertyInput.Options, Is.Null);

       /*
        * PreConstruct(Property property)
        */
       var booleanProperty = new Property(
           "id",
           "Test Boolean",
           "boolean",
           new BooleanDataType(),
           false,
           false,
           false,
           false,
           0,
           null,
           DateTimeOffset.UtcNow,
           null);
       var res_PreConstruct_ByProperty = _defaultPropertyHook.PreConstruct(booleanProperty);

       Assert.That(res_PreConstruct_ByProperty.DataType, Is.TypeOf<BooleanDataType>());
       Assert.That(res_PreConstruct_ByProperty.Options, Is.Null);

       /*
        * (Property originalProperty, Property receivedProperty)
        */
       var receivedBooleanProperty = new Property(
           "id",
           "Test Boolean",
           "boolean",
           new BooleanDataType(),
           false,
           false,
           false,
           true,
           0,
           null,
           DateTimeOffset.UtcNow,
           null);

       var res = _defaultPropertyHook.PreUpdate(receivedBooleanProperty, booleanProperty);

       Assert.That(res.ToBeDeletedOptionIds, Is.Empty);
       Assert.That(res.NeedReindexPropertyValue, Is.True);
    }

    [Test]
    public void SingleChoicePropertyHook_WithCorrectInputs_ShouldReturnCorrectValues()
    {
        /*
         * Method - PreConstruct(PropertyInput propertyInput)
         */
        var singleChoicePropertyInput = new PropertyInput(
            "Test SingleChoice",
            "single_choice",
            new SingleChoiceDataType(),
            false,
            false,
            false,
            false,
            false,
            0,
            null,
            new List<Option> { new Option("0", "0", 0) });

        var res_PrCconstruct_ByPropertyInput = _singleChoicePropertyHook.PreConstruct(singleChoicePropertyInput);

        Assert.That(res_PrCconstruct_ByPropertyInput.DataType, Is.TypeOf<SingleChoiceDataType>());
        Assert.That(res_PrCconstruct_ByPropertyInput.Options, Is.Not.Null);
        Assert.That(res_PrCconstruct_ByPropertyInput.Options![0].Id.Length, Is.EqualTo(15)); // should assign a real Id

        /*
         * Method - PreConstruct(Property property)
         */
        var singleChoiceProperty = new Property(
            "0",
            "Test SingleChoice",
            "single_choice",
            new SingleChoiceDataType(),
            false,
            false,
            false,
            false,
            0,
            null,
            DateTimeOffset.UtcNow,
            new List<Option> { new Option("mock-option-id", "0", 0) });

        var res_PreConstruct_ByProperty = _singleChoicePropertyHook.PreConstruct(singleChoiceProperty);

        Assert.That(res_PreConstruct_ByProperty.DataType, Is.TypeOf<SingleChoiceDataType>());
        Assert.That(res_PreConstruct_ByProperty.Options, Is.Not.Null);
        Assert.That(res_PreConstruct_ByProperty.Options![0].Id.Length, Is.EqualTo(15)); // should assign a real Id

        /*
         * Method - PreUpdate(Property originalProperty, Property receivedProperty)
         * More scenarios see OptionServiceTests, PropertyConstructorTests and SchemaIntegrationTests.
         */
        var receivedSingleChoiceProperty = new Property(
            "0",
            "Test SingleChoice",
            "single_choice",
            new SingleChoiceDataType(),
            false,
            false,
            false,
            true,
            0,
            null,
            DateTimeOffset.UtcNow,
            new List<Option> { new Option(string.Empty, "1", 1) });

        var res = _singleChoicePropertyHook.PreUpdate(singleChoiceProperty, receivedSingleChoiceProperty);

        Assert.That(res.ToBeDeletedOptionIds[0], Is.EqualTo("mock-option-id"));
        Assert.That(res.NeedReindexPropertyValue, Is.True);

        Assert.That(singleChoiceProperty.Options![0].Id.Length, Is.EqualTo(15));
        Assert.That(singleChoiceProperty.Options![0].Value, Is.EqualTo("1"));
    }

    [Test]
    public void MultipleChoicePropertyHook_WithCorrectInputs_ShouldReturnCorrectValues()
    {
        /*
         * Method - PreConstruct(PropertyInput propertyInput)
         */
        var multipleChoicePropertyInput = new PropertyInput(
            "Test MultipleChoice",
            "multiple_choice",
            new MultipleChoiceDataType(),
            false,
            false,
            false,
            false,
            false,
            0,
            null,
            new List<Option> { new Option("0", "0", 0) });

        var res_PrCconstruct_ByPropertyInput = _multipleChoicePropertyHook.PreConstruct(multipleChoicePropertyInput);

        Assert.That(res_PrCconstruct_ByPropertyInput.DataType, Is.TypeOf<MultipleChoiceDataType>());
        Assert.That(res_PrCconstruct_ByPropertyInput.Options, Is.Not.Null);
        Assert.That(res_PrCconstruct_ByPropertyInput.Options![0].Id.Length, Is.EqualTo(15)); // should assign a real Id

        /*
         * Method - PreConstruct(Property property)
         */
        var multipleChoiceProperty = new Property(
            "0",
            "Test MultipleChoice",
            "multiple_choice",
            new MultipleChoiceDataType(),
            false,
            false,
            false,
            false,
            0,
            null,
            DateTimeOffset.UtcNow,
            new List<Option> { new Option("mock-option-id", "0", 0) });

        var res_PreConstruct_ByProperty = _multipleChoicePropertyHook.PreConstruct(multipleChoiceProperty);

        Assert.That(res_PreConstruct_ByProperty.DataType, Is.TypeOf<MultipleChoiceDataType>());
        Assert.That(res_PreConstruct_ByProperty.Options, Is.Not.Null);
        Assert.That(res_PreConstruct_ByProperty.Options![0].Id.Length, Is.EqualTo(15)); // should assign a real Id

        /*
         * Method - PreUpdate(Property originalProperty, Property receivedProperty)
         * More scenarios see OptionServiceTests, PropertyConstructorTests and SchemaIntegrationTests.
         */
        var receivedMultipleChoiceProperty = new Property(
            "0",
            "Test MultipleChoice",
            "multiple_choice",
            new SingleChoiceDataType(),
            false,
            false,
            false,
            true,
            0,
            null,
            DateTimeOffset.UtcNow,
            new List<Option> { new Option(string.Empty, "1", 1) });

        var res = _multipleChoicePropertyHook.PreUpdate(multipleChoiceProperty, receivedMultipleChoiceProperty);

        Assert.That(res.ToBeDeletedOptionIds[0], Is.EqualTo("mock-option-id"));
        Assert.That(res.NeedReindexPropertyValue, Is.True);

        Assert.That(multipleChoiceProperty.Options![0].Id.Length, Is.EqualTo(15));
        Assert.That(multipleChoiceProperty.Options![0].Value, Is.EqualTo("1"));
    }

    [Test]
    public void ArrayObjectPropertyHook_WithCorrectInputs_ShouldReturnCorrectValues()
    {
        /*
         * Method - PreConstruct(PropertyInput propertyInput)
         * More scenarios see OptionServiceTests, PropertyConstructorTests and SchemaIntegrationTests.
         */
        var arrayObjectPropertyInput = new PropertyInput(
            "Test ArrayObject",
            "array_object",
            new ArrayObjectDataType(new InnerSchema(GeneratePropertiesForSchemaCreation())),
            false,
            false,
            false,
            false,
            false,
            0,
            null,
            null);

        var res_PrCconstruct_ByPropertyInput = _arrayObjectPropertyHook.PreConstruct(arrayObjectPropertyInput);

        Assert.That(res_PrCconstruct_ByPropertyInput.DataType, Is.TypeOf<ArrayObjectDataType>());
        Assert.That(res_PrCconstruct_ByPropertyInput.Options, Is.Null);
        Assert.Multiple(
            () =>
            {
                var createdInnerSchema = res_PrCconstruct_ByPropertyInput.DataType.GetInnerSchema();

                Assert.That(createdInnerSchema.Properties.Count, Is.EqualTo(2));
                Assert.That(createdInnerSchema.Properties[1].Id.Length, Is.EqualTo(15));
                Assert.That(createdInnerSchema.Properties[1].Options, Is.Not.Null);
                Assert.That(createdInnerSchema.Properties[1].Options!.Count, Is.EqualTo(3));
                Assert.That(createdInnerSchema.Properties[1].Options![0].Id.Length, Is.EqualTo(15));
            });

        /*
         * Method - PreConstruct(Property property)
         * More scenarios see OptionServiceTests, PropertyConstructorTests and SchemaIntegrationTests.
         */
        var arrayObjectProperty = new Property(
            "0",
            "Test ArrayObject",
            "array_object",
            new ArrayObjectDataType(new InnerSchema(GeneratePropertiesForSchemaCreation())),
            false,
            false,
            false,
            false,
            0,
            null,
            DateTimeOffset.UtcNow,
            null);

        var res_PreConstruct_ByProperty = _arrayObjectPropertyHook.PreConstruct(arrayObjectProperty);

        Assert.That(res_PreConstruct_ByProperty.DataType, Is.TypeOf<ArrayObjectDataType>());
        Assert.That(res_PreConstruct_ByProperty.Options, Is.Null);
        Assert.Multiple(
            () =>
            {
                var createdInnerSchema = res_PreConstruct_ByProperty.DataType.GetInnerSchema();

                Assert.That(createdInnerSchema.Properties.Count, Is.EqualTo(2));
                Assert.That(createdInnerSchema.Properties[1].Id.Length, Is.EqualTo(15));
                Assert.That(createdInnerSchema.Properties[1].Options, Is.Not.Null);
                Assert.That(createdInnerSchema.Properties[1].Options!.Count, Is.EqualTo(3));
                Assert.That(createdInnerSchema.Properties[1].Options![0].Id.Length, Is.EqualTo(15));
            });

        /*
         * Method - PreUpdate(Property originalProperty, Property receivedProperty)
         * More scenarios see OptionServiceTests, PropertyConstructorTests and SchemaIntegrationTests.
         */
        var receivedInnerSchema = DeepCopy(res_PreConstruct_ByProperty.DataType.GetInnerSchema());
        receivedInnerSchema.Properties.RemoveAll(p => p.UniqueName == "multiple_choice");
        receivedInnerSchema.Properties.Add(new Property(
            "0",
            "Test SingleChoice",
            "single_choice",
            new SingleChoiceDataType(),
            false,
            false,
            false,
            true,
            11,
            null,
            DateTimeOffset.UtcNow,
            new List<Option> { new Option(string.Empty, "1", 1) }));

        var receivedArrayObjectProperty = new Property(
            "0",
            "Test ArrayObject",
            "array_object",
            new ArrayObjectDataType(receivedInnerSchema),
            false,
            false,
            false,
            true,
            0,
            null,
            DateTimeOffset.UtcNow,
            null);

        // manual assign the constructed DataSchema
        arrayObjectProperty.DataType.InnerSchema = res_PreConstruct_ByProperty.DataType.GetInnerSchema();

        var res = _arrayObjectPropertyHook.PreUpdate(arrayObjectProperty, receivedArrayObjectProperty);

        Assert.That(res.ToBeDeletedOptionIds, Is.Empty);
        Assert.That(res.NeedReindexPropertyValue, Is.True);
        Assert.Multiple(
            () =>
            {
                var updatedInnerSchema = arrayObjectProperty.DataType.GetInnerSchema();

                Assert.That(updatedInnerSchema.Properties.Count, Is.EqualTo(2));
                Assert.That(updatedInnerSchema.Properties[1].Id.Length, Is.EqualTo(15));
                Assert.That(updatedInnerSchema.Properties[1].UniqueName, Is.EqualTo("single_choice"));
                Assert.That(updatedInnerSchema.Properties[1].Options, Is.Not.Null);
                Assert.That(updatedInnerSchema.Properties[1].Options!.Count, Is.EqualTo(1));
                Assert.That(updatedInnerSchema.Properties[1].Options![0].Id.Length, Is.EqualTo(15));
            });
    }

    private static T DeepCopy<T>(T obj)
    {
        return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(obj))!;
    }

    private List<Property> GeneratePropertiesForSchemaCreation()
    {
        return new List<Property>
        {
            new Property(
                string.Empty,
                "Test Numeric",
                "numeric",
                new NumericDataType(),
                false,
                false,
                true,
                true,
                2,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                string.Empty,
                "Test MultipleChoice",
                "multiple_choice",
                new MultipleChoiceDataType(),
                true,
                true,
                false,
                false,
                9,
                null,
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(string.Empty, "option_0", 0),
                    new Option(string.Empty, "option_1", 1),
                    new Option(string.Empty, "option_2", 2)
                })
        };
    }

    private IdService GetIdService()
    {
        var dbContainerResolver = new DbContainerResolver(new MyCrmHubDbConfig());

        var idService = new IdService(
            NullLogger<IdService>.Instance,
            dbContainerResolver);

        return idService;
    }

    private SchemaRepository GetSchemaRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var schemaRepository = new SchemaRepository(
            NullLogger<SchemaRepository>.Instance,
            serviceProvider);

        return schemaRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig, IDbConfig
    {
        public string Endpoint => "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key => "****************************************************************************************";

        public string DatabaseId => "crmhubdb";
    }
}