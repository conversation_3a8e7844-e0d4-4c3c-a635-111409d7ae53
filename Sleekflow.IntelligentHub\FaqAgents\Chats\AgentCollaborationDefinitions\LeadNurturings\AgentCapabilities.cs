namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public static class AgentCapabilities
{
    public const string ClassifyLead = "classify_lead";
    public const string DecideAction = "decide_action";
    public const string DefineStrategy = "define_strategy";
    public const string CraftResponse = "craft_response";
    public const string CraftStandardResponse = "craft_standard_response";
    public const string CraftTransitionResponse = "craft_transition_response";
    public const string RetrieveKnowledge = "retrieve_knowledge";
    public const string AssignLead = "assign_lead";
    public const string ScheduleDemo = "schedule_demo";
    public const string ReviewResponse = "review_response";
    public const string GatherInfo = "gather_info";
    public const string ConfirmActions = "confirm_actions";
    public const string ExecuteActions = "execute_actions";
    public const string SendFollowUp = "send_followup";
}