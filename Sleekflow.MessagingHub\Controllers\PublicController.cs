﻿using Microsoft.AspNetCore.Mvc;

namespace Sleekflow.MessagingHub.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    public PublicController()
    {
    }

    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }
}