﻿using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.ProviderConfigs;

public interface IProviderConfigRepository : IDynamicFiltersRepository<ProviderConfig>
{
}

public class ProviderConfigRepository : DynamicFiltersBaseRepository<ProviderConfig>, IProviderConfigRepository
{
    public ProviderConfigRepository(
        ILogger<DynamicFiltersBaseRepository<ProviderConfig>> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}