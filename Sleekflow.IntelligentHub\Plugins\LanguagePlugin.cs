using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins;

public interface ILanguagePlugin
{
    Task<DetectTextLanguageResponse> DetectTextLanguage(Kernel kernel, string inputText);

    Task<DetectTextLanguageResponse> DetectTextLanguageBCP47(Kernel kernel, string inputText);

    Task<DetectAppropriateResponseLanguageResponse> DetectAppropriateResponseLanguageAsync(
        Kernel kernel,
        string inputText);

    Task<string> TranslateText(Kernel kernel, string inputText, string targetLanguageCode);

    Task<string> TranslateTextWithIsoCodeAsync(Kernel kernel, string inputText, string targetLanguageCode);
}

public class LanguagePlugin
    : ILanguagePlugin, IScopedService
{
    private readonly ILogger<LanguagePlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public LanguagePlugin(
        ILogger<LanguagePlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("DetectTextLanguage")]
    [Description(
        "Detects the language being used in the input text and returns the language in the ISO-639 language code format.")]
    [return: Description("The ISO-639 language code of the text and the language name in json format.")]
    public async Task<DetectTextLanguageResponse> DetectTextLanguage(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI);

        var detectTextLanguageFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "DetectTextLanguage",
                Description =
                    "Detects the language being used in the input text and returns the language in the ISO-639 language code format.",
                Template =
                    """
                    <message role="system">
                    Detects the language being used in the input and returns its language in the ISO 639-3 code format.
                    Please respond with the ISO 639-3 code and the language name of the text in json format. The property name should be 'inputTextLanguageCode' and 'inputTextLanguageName'.
                    </message>

                    <message role="user">
                    Bonjour, comment ça va ?
                    </message>
                    <message role="assistant">
                    {"inputTextLanguageCode": "fra", "inputTextLanguageName": "French"}
                    </message>

                    <message role="user">
                    呢度係香港
                    </message>
                    <message role="assistant">
                    {"inputTextLanguageCode": "yue", "inputTextLanguageName": "Cantonese"}
                    </message>

                    <message role="user">
                    这里是中国
                    </message>
                    <message role="assistant">
                    {"inputTextLanguageCode": "zh_Hans", "inputTextLanguageName": "Simplified Chinese"}
                    </message>

                    <message role="user">
                    這裡是台灣
                    </message>
                    <message role="assistant">
                    {"inputTextLanguageCode": "zh_Hant", "inputTextLanguageName": "Traditional Chinese"}
                    </message>

                    <message role="user">
                    My favorite fruit is an apple.
                    </message>
                    <message role="assistant">
                    {"inputTextLanguageCode": "eng", "inputTextLanguageName": "English"}
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The ISO-639 language code of the text and the language name in json format.",
                    JsonSchema =
                        """
                        {
                           "type": "object",
                           "properties": {
                               "inputTextLanguageCode": {
                                   "type": "string"
                               },
                               "inputTextLanguageName": {
                                   "type": "string"
                               }
                           }
                        }
                        """
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await detectTextLanguageFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        var unknownResponse = new DetectTextLanguageResponse(
            "unknown",
            "unknown",
            "Unknown");

        if (chatMessageContent?.Content == null)
        {
            return unknownResponse;
        }

        try
        {
            return JsonConvert.DeserializeObject<DetectTextLanguageResponse>(chatMessageContent.Content) ??
                   unknownResponse;
        }
        catch (JsonException)
        {
            return unknownResponse;
        }
    }

    [KernelFunction("DetectTextLanguageBCP47")]
    [Description(
        "Detects the language being used in the input text and returns the language in the BCP 47 language tag.")]
    [return: Description("The BCP 47 language tag of the text and the language name in json format.")]
    public async Task<DetectTextLanguageResponse> DetectTextLanguageBCP47(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI);

        var detectTextLanguageFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "DetectTextLanguageBCP47",
                Description =
                    "Detects the language being used in the input text and returns the language in the BCP 47 language tag.",
                Template =
                    """
                    <message role="system">
                    Detects the language being used in the input and returns its language in the BCP 47 language tag.
                    Please respond with the BCP 47 language tag (including any subtags) and the language name of the text in json format. The property name should be 'inputTextLanguageCode' and 'inputTextLanguageName'.
                    </message>

                    <message role="user">
                    Bonjour, comment ça va ?
                    </message>
                    <message role="assistant">
                    {"inputTextLanguageCode": "fr", "inputTextLanguageName": "French"}
                    </message>

                    <message role="user">
                    呢度係香港
                    </message>
                    <message role="assistant">
                     {"inputTextLanguageCode": "zh", "inputTextLanguageName": "Chinese"}
                    </message>

                    <message role="user">
                    My favorite fruit is an apple.
                    </message>
                    <message role="assistant">
                     {"inputTextLanguageCode": "en", "inputTextLanguageName": "English"}
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The BCP 47 language tag of the text and the language name in json format.",
                    JsonSchema =
                        """
                        {
                           "type": "object",
                           "properties": {
                               "inputTextLanguageCode": {
                                   "type": "string"
                               },
                               "inputTextLanguageName": {
                                   "type": "string"
                               }
                           }
                        }
                        """
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await detectTextLanguageFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        if (chatMessageContent?.Content == null)
        {
            throw new SfInternalErrorException("Failed to detect text language.");
        }

        return JsonConvert.DeserializeObject<DetectTextLanguageResponse>(chatMessageContent.Content) ??
               throw new SfInternalErrorException("Failed to detect text language.");
    }

    [KernelFunction("DetectAppropriateResponseLanguage")]
    [Description("Detects the appropriate response language for the input text.")]
    [return: Description("The ISO language code and language name for the detected appropriate response language.")]
    public async Task<DetectAppropriateResponseLanguageResponse> DetectAppropriateResponseLanguageAsync(
        Kernel kernel,
        string inputText)
    {
        var unknownResponse = new DetectAppropriateResponseLanguageResponse(
            "unknown",
            "en",
            "English");

        // Base case: empty input text
        if (string.IsNullOrEmpty(inputText))
        {
            return unknownResponse;
        }

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        var detectTextLanguageFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "DetectAppropriateResponseLanguage",
                Description =
                    "Detects the language being used in the input text and returns the language in ISO format (ISO 639-1)-(ISO 15924)-(ISO 3166).",
                Template =
                    """
                    <message role="system">
                    Think step by step. Determine the appropriate language for the response based on the input text and any explicit requests, and return the ISO language code in JSON format.

                    **OUTPUT FORMAT:**
                    {
                        "reasoning": "Step-by-step analysis of how the response language was determined. Consider: 1. Explicit language requests 2. Grammatical structure 3. Cultural expression patterns 4. Regional linguistic features 5. Vocabulary choice 6. Majority script used",
                        "responseLanguageCode": "[code]",
                        "responseLanguageName": "[name]"
                    }

                    **LANGUAGE CODE FORMATS:**
                    1. **Basic:** [language]
                       - ISO 639-1 (2-letter language code)
                       - Use for languages with a single standard writing system
                       - Examples:
                         - "ja" for Japanese
                         - "ko" for Korean

                    2. **With Script:** [language]-[script]
                       - Use only when script variation is significant
                       - Example: "mn-Cyrl" for Mongolian in Cyrillic

                    3. **With Region:** [language]-[region]
                       - Use when regional variant is significant
                       - Example: "en-US" for American English

                    4. **Complete:** [language]-[script]-[region]
                       - Use for languages with both significant script and regional variations
                       - Example: "zh-Hant-HK" for Hong Kong Traditional Chinese

                    **CHINESE TEXT HANDLING:**
                    - Default to Traditional Chinese (zh-Hant) when all characters are common to both Simplified and Traditional scripts
                    - Regional marker examples:
                      - zh-Hant-TW: Taiwan-specific terms (e.g., 機車, 計程車)
                      - zh-Hant-HK: Hong Kong terms + English mixing (e.g., 巴士, OK)
                      - zh-Hant: When region cannot be determined

                    **GUIDELINES:**
                    - Use the simplest applicable format
                    - Include script/region only when the variant is significant
                    - Consider cultural context for regional identification when the input is ambiguous
                    - If the input contains an explicit request like "reply/answer in [language]", use the requested language for the response. Consider the context to understand the requested language.
                    - If no explicit request is present, use the detected language of the input text.
                    </message>

                    <message role="user">
                    Bonjour, je m'appelle Pierre.
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in French with no explicit language request, so response should be in French.", "responseLanguageCode": "fr", "responseLanguageName": "French"}
                    </message>

                    <message role="user">
                    Hello, how are you?
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in English with no explicit language request, so response should be in English.", "responseLanguageCode": "en", "responseLanguageName": "English"}
                    </message>

                    <message role="user">
                    你好。
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in Chinese using characters common to both Simplified and Traditional scripts, with no explicit language request. Defaulting to Traditional Chinese.", "responseLanguageCode": "zh-Hant", "responseLanguageName": "Traditional Chinese"}
                    </message>

                    <message role="user">
                    今天在台北車站附近逛街，買了一些伴手禮。
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in Traditional Chinese with Taiwan-specific terms and place names, with no explicit language request.", "responseLanguageCode": "zh-Hant-TW", "responseLanguageName": "Traditional Chinese - Taiwan"}
                    </message>

                    <message role="user">
                    依家啲後生仔成日都係office做嘢，放工先去街市買餸。
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in Cantonese with Hong Kong-specific terms and context, with no explicit language request.", "responseLanguageCode": "zh-Hant-HK", "responseLanguageName": "Traditional Chinese - Hong Kong"}
                    </message>

                    <message role="user">
                    這個程式需要更新，請與資訊部門聯絡。
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in Traditional Chinese without region-specific markers, with no explicit language request.", "responseLanguageCode": "zh-Hant", "responseLanguageName": "Traditional Chinese"}
                    </message>

                    <message role="user">
                    计算机专业的学生需要学习编程语言和数据结构。
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text uses Simplified Chinese characters, with no explicit language request.", "responseLanguageCode": "zh-Hans", "responseLanguageName": "Simplified Chinese"}
                    </message>

                    <message role="user">
                    請問 IT department 最近 upgrade 咗個 system，點樣 set up 先至啱？
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in Cantonese with English IT terms and Hong Kong workplace context, with no explicit language request.", "responseLanguageCode": "zh-Hant-HK", "responseLanguageName": "Traditional Chinese - Hong Kong"}
                    </message>

                    <message role="user">
                    Please reply in Spanish
                    </message>
                    <message role="assistant">
                    {"reasoning": "User explicitly requests response in Spanish.", "responseLanguageCode": "es", "responseLanguageName": "Spanish"}
                    </message>

                    <message role="user">
                    Hola, ¿puedes responder en inglés?
                    </message>
                    <message role="assistant">
                    {"reasoning": "Input text is in Spanish but explicitly requests response in English.", "responseLanguageCode": "en", "responseLanguageName": "English"}
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "input"
                    }
                ],
                OutputVariable = new OutputVariable
                {
                    Description =
                        "The ISO language code of the text and the language name in json format. If the language cannot be detected, the response will be 'unknown'.",
                    JsonSchema =
                        """
                        {
                           "type": "object",
                           "properties": {
                               "reasoning": {
                                   "type": "string"
                               },
                               "responseLanguageCode": {
                                   "type": "string"
                               },
                               "responseLanguageName": {
                                   "type": "string"
                               }
                           }
                        }
                        """
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await detectTextLanguageFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        if (chatMessageContent?.Content == null)
        {
            return unknownResponse;
        }

        try
        {
            var content = chatMessageContent.Content;

            _logger.LogInformation("DetectAppropriateResponseLanguageAsync: {Content}", content);

            return
                JsonConvert.DeserializeObject<DetectAppropriateResponseLanguageResponse>(content)
                ?? unknownResponse;
        }
        catch (JsonException)
        {
            return unknownResponse;
        }
    }

    [KernelFunction("TranslateText")]
    [Description("Translates the input text to the target language.")]
    [return: Description("The translated text.")]
    public async Task<string> TranslateText(Kernel kernel, string inputText, string targetLanguageCode)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var translateTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "TranslateText",
                Description =
                    "Translates the input text to the target language.",
                Template =
                    """
                    <message role="system">
                    Translate text from one language to another using the given ISO-639-1, ISO-639-2, ISO-639-3 Language Code, you MUST retain the original form of certain types of words to avoid confusion or misinterpretation.
                    Specifically, you MUST FOLLOW these rules:
                    - Proper Nouns: Always keep the names of people, cities, countries, companies, and brands in their original form.
                        - For example, 'New York' should not become 'Nueva York' in a Spanish translation.
                        - For example, 'Apple Corporation' should not become 'société Apple' in a French translation.
                        - For example, 'University of Texas' should not become 'Universiti Texas' in a Malay translation.
                    - Technical Terms: In specialized fields like medicine, law, engineering, and computer science, certain technical terms and jargon have precise meanings. These terms should remain unchanged to preserve their specific implications.
                        - For example, a medical term like 'Myocardial infarction' should remain as they are in any language.
                    - Trademarks and Brand Names: These are legally protected and universally recognized. Therefore, they should be kept intact in any translation.
                        - For example, 'Microsoft' or 'Adidas' should remain as they are in any language.
                    - Acronyms and Initialisms: These abbreviations are generally recognized and understood globally in their original form. Hence, they should not be translated.
                        - For example, 'NASA' or 'FBI' should remain as they are in any language.
                    - Code or Programming Languages: Elements of programming, including code snippets, programming languages, and related terminology, are universally used in their original form. They should not be translated since this can lead to confusion or errors in the context of programming and software development.
                    </message>
                    <message role="user">
                    ISO-639-1, ISO-639-2, ISO-639-3 Language Code:
                    fr

                    Original Text:
                    I programmed a function in JavaScript to enhance the user interface of the Microsoft application.
                    </message>
                    <message role="assistant">
                    J'ai programmé une fonction en JavaScript pour améliorer l'interface utilisateur de l'application Microsoft.
                    </message>
                    <message role="user">
                    ISO-639-1, ISO-639-2, ISO-639-3 Language Code:
                    en

                    Original Text:
                    Google嘅算法經常更新，以提高搜尋引擎嘅表現。
                    </message>
                    <message role="assistant">
                    Google's algorithms are often updated to improve search engine performance.
                    </message>
                    <message role="user">
                    ISO-639-1, ISO-639-2, ISO-639-3 Language Code:
                    yue

                    Original Text:
                    Bagaimana tidurmu tadi malam?
                    </message>
                    <message role="assistant">
                    你琴晚訓成點？
                    </message>
                    <message role="user">
                    ISO-639-1, ISO-639-2, ISO-639-3 Language Code:
                    en

                    Original Text:
                    How old are you?
                    </message>
                    <message role="assistant">
                    How old are you?
                    </message>
                    <message role="user">
                    ISO-639-1, ISO-639-2, ISO-639-3 Language Code:
                    {{$LANGUAGE_CODE}}

                    Original Text:
                    {{$TEXT}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "LANGUAGE_CODE"
                    },
                    new InputVariable
                    {
                        Name = "TEXT"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The translated text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await translateTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "LANGUAGE_CODE", targetLanguageCode
                },
                {
                    "TEXT", inputText
                }
            });

        return chatMessageContent?.Content ?? "Translation failed";
    }

    [KernelFunction("TranslateTextISO")]
    [Description(
        "Translates the input text to the target language using ISO format (ISO 639-1)-(ISO 15924)-(ISO 3166).")]
    [return: Description("The translated text.")]
    public async Task<string> TranslateTextWithIsoCodeAsync(Kernel kernel, string inputText, string targetLanguageCode)
    {
        if (string.IsNullOrEmpty(targetLanguageCode) || string.IsNullOrEmpty(inputText))
        {
            return "Translation failed";
        }

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var translateTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "TranslateTextISO",
                Description =
                    "Translates the input text to the target language using ISO format (ISO 639-1)-(ISO 15924).",
                Template =
                    """
                    <message role="system">
                    Translate text from one language to another using ISO language codes in the format:
                    - [language] (required, ISO 639-1, 2 letters)
                    - [language]-[script] (ISO 15924, 4 letters, first capitalized)

                    For Chinese translations, ensure proper script (Hans for Simplified, Hant for Traditional) is respected. Retain the intended meaning, even if it means using different vocabulary or grammar rules. For example:
                    - When translating from Chinese to Simplified (zh-Hans), use standard Simplified characters and phrases.
                    - When translating from Chinese to Traditional (zh-Hant), ensure the formality or regional differences are preserved.

                    You MUST retain the original form of:
                    - Proper Nouns (people, places, companies)
                    - Technical Terms
                    - Trademarks and Brand Names
                    - Acronyms and Initialisms
                    - Programming Language Elements

                    If the translation seems to lose context or meaning (like a literal translation), please adapt the output to reflect the local usage and avoid misunderstanding.
                    </message>

                    <message role="user">
                    Target Language Code:
                    zh-Hans

                    Original Text:
                    I am studying at the University of Hong Kong using Microsoft Office.
                    </message>
                    <message role="assistant">
                    我在香港大学学习，使用Microsoft Office。
                    </message>

                    <message role="user">
                    Target Language Code:
                    zh-Hant

                    Original Text:
                    I am studying at the University of Hong Kong using Microsoft Office.
                    </message>
                    <message role="assistant">
                    我在香港大學學習，使用Microsoft Office。
                    </message>

                    <message role="user">
                    Target Language Code:
                    fr

                    Original Text:
                    The Apple iPhone supports WeChat and LINE apps.
                    </message>
                    <message role="assistant">
                    L'iPhone d'Apple prend en charge les applications WeChat et LINE.
                    </message>

                    <message role="user">
                    Target Language Code:
                    {{$LANGUAGE_CODE}}

                    Original Text:
                    {{$TEXT}}
                    </message>
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "LANGUAGE_CODE"
                    },

                    new InputVariable
                    {
                        Name = "TEXT"
                    }
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "The translated text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await translateTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "LANGUAGE_CODE", targetLanguageCode
                },
                {
                    "TEXT", inputText
                }
            });

        return chatMessageContent?.Content ?? "Translation failed";
    }
}