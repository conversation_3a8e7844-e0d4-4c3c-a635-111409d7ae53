﻿using Newtonsoft.Json;
using Sleekflow.DependencyInjection;

namespace Sleekflow.Integrator.Hubspot.Services;

public interface IProviderService
{
    List<ProviderService.SupportedEntityType> GetSupportedEntityTypes();

    bool IsSupportedEntityType(string entityTypeName);
}

public class ProviderService : ISingletonService, IProviderService
{
    public class SupportedEntityType
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        public SupportedEntityType(string name)
        {
            Name = name;
        }
    }

    private readonly List<string> _entityTypeNames = new List<string>
    {
        "Contact", "User",
    };

    public List<SupportedEntityType> GetSupportedEntityTypes()
    {
        return _entityTypeNames.Select(n => new SupportedEntityType(n)).ToList();
    }

    public bool IsSupportedEntityType(string entityTypeName)
    {
        return _entityTypeNames.Contains(entityTypeName);
    }
}