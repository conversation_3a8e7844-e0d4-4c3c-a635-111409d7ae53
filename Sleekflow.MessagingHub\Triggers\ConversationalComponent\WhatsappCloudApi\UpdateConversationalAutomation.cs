using System.ComponentModel.DataAnnotations;
using GraphApi.Client.ApiClients.Models;
using GraphApi.Client.Payloads.Models.ConversationalComponent;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.ConversationalComponents;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ConversationalComponent.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ConversationalAutomations)]
public class UpdateConversationalAutomation
    : ITrigger<
        UpdateConversationalAutomation.UpdateConversationalAutomationInput,
        UpdateConversationalAutomation.UpdateConversationalAutomationOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IConversationalAutomationService _conversationalAutomationService;
    private readonly ILogger<UpdateConversationalAutomation> _logger;

    public UpdateConversationalAutomation(
        IWabaService wabaService,
        IConversationalAutomationService conversationalAutomationService,
        ILogger<UpdateConversationalAutomation> logger)
    {
        _wabaService = wabaService;
        _conversationalAutomationService = conversationalAutomationService;
        _logger = logger;
    }

    public class UpdateConversationalAutomationInput
        : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("facebook_phone_number_id")]
        public string FacebookPhoneNumberId { get; set; }

        [Required]
        [Validations.ValidateObject]
        [JsonProperty("conversation_automation")]
        public ConversationalAutomation ConversationalAutomation { get; set; }

        [JsonConstructor]
        public UpdateConversationalAutomationInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string sleekflowStaffId,
            string facebookPhoneNumberId,
            ConversationalAutomation conversationalAutomation)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            SleekflowStaffId = sleekflowStaffId;
            FacebookPhoneNumberId = facebookPhoneNumberId;
            ConversationalAutomation = conversationalAutomation;
        }
    }

    public class UpdateConversationalAutomationOutput
    {
        [JsonProperty("success_response")]
        public SuccessGraphApiResponse SuccessResponse { get; set; }

        [JsonConstructor]
        public UpdateConversationalAutomationOutput(
            SuccessGraphApiResponse successResponse)
        {
            SuccessResponse = successResponse;
        }
    }

    public async Task<UpdateConversationalAutomationOutput> F(
        UpdateConversationalAutomationInput input)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAndFacebookPhoneNumberIdAsync(
            input.FacebookWabaId,
            input.FacebookPhoneNumberId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        return new UpdateConversationalAutomationOutput(
            await _conversationalAutomationService.UpdateConversationalAutomationAsync(
                input.SleekflowStaffId,
                waba!,
                input.FacebookPhoneNumberId,
                input.ConversationalAutomation));
    }
}