using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.EmailHub.Repositories;

public interface IEmailAuthenticationRepository : IRepository<EmailAuthentication>
{
}

public class EmailAuthenticationRepository
    : BaseRepository<EmailAuthentication>,
        IEmailAuthenticationRepository,
        IScopedService
{
    public EmailAuthenticationRepository(
        ILogger<EmailAuthenticationRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}