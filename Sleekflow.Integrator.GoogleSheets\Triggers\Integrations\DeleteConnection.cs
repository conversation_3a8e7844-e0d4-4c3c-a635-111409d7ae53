﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeleteConnection : ITrigger
{
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsSubscriptionService _googleSheetsSubscriptionService;

    public DeleteConnection(
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsSubscriptionService googleSheetsSubscriptionService)
    {
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsSubscriptionService = googleSheetsSubscriptionService;
    }

    public class DeleteConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public DeleteConnectionInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class DeleteConnectionOutput
    {
    }

    public async Task<DeleteConnectionOutput> F(DeleteConnectionInput deleteConnectionInput)
    {
        var connection = await _googleSheetsConnectionService.GetByIdAsync(
            deleteConnectionInput.ConnectionId,
            deleteConnectionInput.SleekflowCompanyId);

        if (connection is null)
        {
            throw new SfNotFoundObjectException(
                deleteConnectionInput.ConnectionId,
                deleteConnectionInput.SleekflowCompanyId);
        }

        await _googleSheetsSubscriptionService.ClearByConnectionIdAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _googleSheetsAuthenticationService.DeleteAsync(
            connection.AuthenticationId,
            connection.SleekflowCompanyId);

        await _googleSheetsConnectionService.DeleteAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        return new DeleteConnectionOutput();
    }
}