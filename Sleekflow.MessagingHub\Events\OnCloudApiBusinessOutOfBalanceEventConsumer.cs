using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiBusinessOutOfBalanceEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiBusinessOutOfBalanceEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiBusinessOutOfBalanceEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiBusinessOutOfBalanceEventConsumer : IConsumer<OnCloudApiBusinessOutOfBalanceEvent>
{
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiBusinessOutOfBalanceEventConsumer> _logger;

    public OnCloudApiBusinessOutOfBalanceEventConsumer(
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiBusinessOutOfBalanceEventConsumer> logger)
    {
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnCloudApiBusinessOutOfBalanceEvent> context)
    {
        var onCloudApiBusinessOutOfBalanceEvent = context.Message;
        var facebookBusinessId = onCloudApiBusinessOutOfBalanceEvent.FacebookBusinessId;
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(
                $"Unable to locate Waba balance {JsonConvert.SerializeObject(businessBalance)}");
        }

        if (businessBalance.Balance.Amount >= 0)
        {
            throw new SfInternalErrorException(
                $"Not supported operation OnCloudApiBusinessOutOfBalanceEventConsumer while balance is greater then zero {JsonConvert.SerializeObject(businessBalance)}");
        }

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);
        _logger.LogInformation("Obtained require to be blocked wabas {Wabas}", JsonConvert.SerializeObject(wabas));

        var notBlockedWabas = wabas
            .Where(w => w.MessagingFunctionLimitation != MessagingFunctionLimitationType.BlockAll).ToList();

        _logger.LogInformation(
            "Filter out already blocked wabas {NotBlockedWabas}",
            JsonConvert.SerializeObject(notBlockedWabas));
        foreach (var waba in notBlockedWabas)
        {
            try
            {
                if (await _wabaService.UnblockOrBlockWabaAsync(waba.Id, waba.FacebookWabaId, false) == 0)
                {
                    throw new SfInternalErrorException("Unable to BlockAllActionAndUpsertWaba ");
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    "Exception occur during OnCloudApiBusinessOutOfBalanceEventConsumer {Waba}/{Exception},",
                    JsonConvert.SerializeObject(waba), JsonConvert.SerializeObject(exception));
            }
        }
    }
}