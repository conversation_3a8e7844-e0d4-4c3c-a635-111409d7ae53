﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.WebScrapers;

public interface IWebScraperRun
{
    public string ApifyRunId { get; set; }

    public string Status { get; set; }

    public string? StatusMessage { get; set; }

    public string ApifyKeyValueStoreId { get; set; }

    public string ApifyDatasetId { get; set; }

    public string ApifyRequestQueueId { get; set; }
}

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId("intelligenthubdb")]
[ContainerId("web_scraper_run")]
public class WebScraperRun : Entity, IWebScraperRun, IHasCreatedAt, IHasSleekflowCompanyId
{
    public const string PropertyNameWebScraperRun = "web_scraper_run";
    public const string PropertyNameApifyRunId = "apify_run_id";
    public const string PropertyNameOneTimeRunName = "one_time_run_name";

    [JsonProperty(PropertyName = PropertyNameApifyRunId)]
    public string ApifyRunId { get; set; }

    [JsonProperty(PropertyName = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyName = WebScraperTask.PropertyNameApifyTaskId)]
    public string? ApifyTaskId { get; set; }

    [JsonProperty(PropertyName = "is_one_time_run")]
    public bool IsOneTimeRun { get; set; }

    [JsonProperty(PropertyName = "one_time_run_name")]
    public string? OneTimeRunName { get; set; }

    [JsonProperty(PropertyName = "created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(PropertyName = "started_at")]
    public DateTimeOffset? StartedAt { get; set; }

    [JsonProperty(PropertyName = "finished_at")]
    public DateTimeOffset? FinishedAt { get; set; }

    [JsonProperty(PropertyName = "status")]
    public string Status { get; set; }

    [JsonProperty(PropertyName = "status_message")]
    public string? StatusMessage { get; set; }

    [JsonProperty(PropertyName = "apify_key_value_store_id")]
    public string ApifyKeyValueStoreId { get; set; }

    [JsonProperty(PropertyName = "apify_dataset_id")]
    public string ApifyDatasetId { get; set; }

    [JsonProperty(PropertyName = "apify_request_queue_id")]
    public string ApifyRequestQueueId { get; set; }

    [JsonProperty(PropertyName = WebScraperSetting.PropertyNameWebScraperSetting)]
    public WebScraperSetting WebScraperSetting { get; set; }

    [JsonProperty(PropertyName = "web_pages")]
    public List<WebPage>? WebPages { get; set; }

    [JsonConstructor]
    public WebScraperRun(
        string id,
        string apifyRunId,
        string sleekflowCompanyId,
        string? apifyTaskId,
        bool isOneTimeRun,
        string? oneTimeRunName,
        DateTimeOffset createdAt,
        DateTimeOffset? startedAt,
        DateTimeOffset? finishedAt,
        string status,
        string? statusMessage,
        string apifyKeyValueStoreId,
        string apifyDatasetId,
        string apifyRequestQueueId,
        WebScraperSetting webScraperSetting,
        List<WebPage>? webPages)
        : base(id, SysTypeNames.WebScraperRun)
    {
        ApifyRunId = apifyRunId;
        SleekflowCompanyId = sleekflowCompanyId;
        ApifyTaskId = apifyTaskId;
        IsOneTimeRun = isOneTimeRun;
        OneTimeRunName = oneTimeRunName;
        CreatedAt = createdAt;
        StartedAt = startedAt;
        FinishedAt = finishedAt;
        Status = status;
        StatusMessage = statusMessage;
        ApifyKeyValueStoreId = apifyKeyValueStoreId;
        ApifyDatasetId = apifyDatasetId;
        ApifyRequestQueueId = apifyRequestQueueId;
        WebScraperSetting = webScraperSetting;
        WebPages = webPages;
    }
}