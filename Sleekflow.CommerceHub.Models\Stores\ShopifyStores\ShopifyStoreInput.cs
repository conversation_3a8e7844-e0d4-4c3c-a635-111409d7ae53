using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Stores.ShopifyStores;

public class ShopifyStoreInput : IHasMetadata
{
    [Required]
    [JsonProperty("shopify_url")]
    public string ShopifyUrl { get; set; }

    [Required]
    [JsonProperty("is_view_enabled")]
    public bool IsViewEnabled { get; set; }

    [Required]
    [JsonProperty("is_payment_enabled")]
    public bool IsPaymentEnabled { get; set; }

    [ValidateObject]
    [JsonProperty("subscription_status")]
    public StoreSubscriptionStatus? SubscriptionStatus { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("template_dict")]
    public StoreTemplateDict TemplateDict { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("shopify_sync_config")]
    public ShopifySyncConfig ShopifySyncConfig { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("shopify_payment_config")]
    public ShopifyPaymentConfig ShopifyPaymentConfig { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_sync_status")]
    public ShopifySyncStatus? ShopifySyncStatus { get; set; }

    [ValidateArray]
    [JsonProperty("shopify_message_templates")]
    public List<ShopifyMessageTemplate>? ShopifyMessageTemplates { get; set; }

    [ValidateObject]
    [JsonProperty("is_shopify_billing_owner")]
    public bool? IsShopifyBillingOwner { get; set; }

    [ValidateObject]
    [JsonProperty("charge_updated_at")]
    public DateTimeOffset? ChargeUpdatedAt { get; set; }

    [JsonProperty("charge_id")]
    public string? ChargeId { get; set; }

    [JsonConstructor]
    public ShopifyStoreInput(
        string shopifyUrl,
        bool isViewEnabled,
        bool isPaymentEnabled,
        StoreSubscriptionStatus? subscriptionStatus,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        ShopifySyncConfig shopifySyncConfig,
        ShopifyPaymentConfig shopifyPaymentConfig,
        ShopifySyncStatus? shopifySyncStatus,
        List<ShopifyMessageTemplate>? shopifyMessageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId)
    {
        ShopifyUrl = shopifyUrl;
        IsViewEnabled = isViewEnabled;
        IsPaymentEnabled = isPaymentEnabled;
        SubscriptionStatus = subscriptionStatus;
        Metadata = metadata;
        TemplateDict = templateDict;
        ShopifySyncConfig = shopifySyncConfig;
        ShopifyPaymentConfig = shopifyPaymentConfig;
        ShopifySyncStatus = shopifySyncStatus;
        ShopifyMessageTemplates = shopifyMessageTemplates;
        IsShopifyBillingOwner = isShopifyBillingOwner;
        ChargeUpdatedAt = chargeUpdatedAt;
        ChargeId = chargeId;
    }
}