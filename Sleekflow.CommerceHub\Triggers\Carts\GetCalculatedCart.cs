using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.LineItems;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.LineItems;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Carts;

[TriggerGroup(ControllerNames.Carts)]
public class GetCalculatedCart
    : ITrigger<
        GetCalculatedCart.GetCalculatedCartInput,
        GetCalculatedCart.GetCalculatedCartOutput>
{
    private readonly ICartService _cartService;
    private readonly ILineItemCalculator _lineItemCalculator;
    private readonly IProductVariantService _productVariantService;
    private readonly IOrderPriceCalculator _orderPriceCalculator;
    private readonly IProductService _productService;

    public GetCalculatedCart(
        ICartService cartService,
        ILineItemCalculator lineItemCalculator,
        IProductVariantService productVariantService,
        IOrderPriceCalculator orderPriceCalculator,
        IProductService productService)
    {
        _cartService = cartService;
        _lineItemCalculator = lineItemCalculator;
        _productVariantService = productVariantService;
        _orderPriceCalculator = orderPriceCalculator;
        _productService = productService;
    }

    public class GetCalculatedCartInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [ValidateIsoCurrencyCode]
        [JsonProperty(Cart.PropertyNameCurrencyIsoCode)]
        public string CurrencyIsoCode { get; set; }

        [Required]
        [ValidateIsoLanguageCode]
        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [JsonConstructor]
        public GetCalculatedCartInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            string currencyIsoCode,
            string languageIsoCode)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            CurrencyIsoCode = currencyIsoCode;
            LanguageIsoCode = languageIsoCode;
        }
    }

    public class GetCalculatedCartOutput
    {
        [JsonProperty("calculated_cart")]
        public CalculatedCartDto CalculatedCart { get; set; }

        [JsonConstructor]
        public GetCalculatedCartOutput(
            CalculatedCartDto calculatedCart)
        {
            CalculatedCart = calculatedCart;
        }
    }

    public async Task<GetCalculatedCartOutput> F(
        GetCalculatedCartInput getCalculatedCartInput)
    {
        var cart = await _cartService.GetOrCreateStoreUserCartAsync(
            getCalculatedCartInput.SleekflowCompanyId,
            getCalculatedCartInput.StoreId,
            getCalculatedCartInput.SleekflowUserProfileId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            cart.LineItems.Select(x => x.ProductVariantId).ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var calculatedLineItems = await _lineItemCalculator.CalculateAsync(
            getCalculatedCartInput.SleekflowCompanyId,
            getCalculatedCartInput.StoreId,
            cart.LineItems
                .Select(
                    l =>
                    {
                        var productVariant = productVariantIdToProductVariantDtoDict[l.ProductVariantId];

                        return new SnapshottedLineItem(
                            l.ProductVariantId,
                            l.ProductId,
                            l.Description,
                            l.Quantity,
                            l.LineItemDiscount,
                            l.Metadata,
                            productVariant);
                    })
                .ToList(),
            cart.CartDiscount,
            new ILineItemCalculator.CalculatorContext(
                getCalculatedCartInput.CurrencyIsoCode,
                getCalculatedCartInput.LanguageIsoCode));

        var (subtotalPrice, totalPrice) = _orderPriceCalculator.Calculate(
            getCalculatedCartInput.SleekflowCompanyId,
            getCalculatedCartInput.StoreId,
            calculatedLineItems);

        return new GetCalculatedCartOutput(
            new CalculatedCartDto(
                cart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict,
                calculatedLineItems,
                subtotalPrice,
                totalPrice));
    }
}