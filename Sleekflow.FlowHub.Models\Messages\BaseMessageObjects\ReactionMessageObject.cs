using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class ReactionMessageObject : BaseMessageObject
{
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("emoji")]
    public string Emoji { get; set; }

    [JsonConstructor]
    public ReactionMessageObject(string messageId, string emoji)
    {
        MessageId = messageId;
        Emoji = emoji;
    }
}