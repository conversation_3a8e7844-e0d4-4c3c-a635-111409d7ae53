﻿namespace Sleekflow.Exceptions;

public class SfDuplicateEnvVarException : ErrorCodeException
{
    public List<string> DuplicateKeys { get; }

    public SfDuplicateEnvVarException(
        List<string> duplicateKeys)
        : base(
            ErrorCodeConstant.SfDuplicateEnvVarException,
            $"Detected Duplicate Environmental Variables: [{string.Join(",", duplicateKeys.Select(x => x.ToString()))}]")
    {
        DuplicateKeys = duplicateKeys;
    }
}