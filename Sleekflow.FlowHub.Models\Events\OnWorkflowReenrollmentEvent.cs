﻿namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowReenrollmentEvent
{
    public string SleekflowCompanyId { get; set; }

    // Only reenroll the latest restricted enrollment
    // if there are multiple restricted enrollments of the same contact
    public string StateIdToReenroll { get; set; }

    public List<string> StateIdsToMarkAsReenrolled { get; set; }

    public OnWorkflowReenrollmentEvent(
        string sleekflowCompanyId,
        string stateIdToReenroll,
        List<string> stateIdsToMarkAsReenrolled)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateIdToReenroll = stateIdToReenroll;
        StateIdsToMarkAsReenrolled = stateIdsToMarkAsReenrolled;
    }
}