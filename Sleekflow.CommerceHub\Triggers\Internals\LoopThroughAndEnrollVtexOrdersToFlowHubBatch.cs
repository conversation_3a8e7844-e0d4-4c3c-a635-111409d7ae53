﻿using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.RateLimiters;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class LoopThroughAndEnrollVtexOrdersToFlowHubBatch
    : ITrigger<
        LoopThroughAndEnrollVtexOrdersToFlowHubBatch.LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput,
        LoopThroughAndEnrollVtexOrdersToFlowHubBatch.LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>
{
    private const int BatchSize = 50;

    private readonly IVtexAuthenticationService _vtexAuthenticationService;
    private readonly IVtexOrderCommander _vtexOrderCommander;
    private readonly IVtexEventRateLimitProducer _vtexEventRateLimitProducer;
    private readonly ILogger<LoopThroughAndEnrollVtexOrdersToFlowHubBatch> _logger;

    public LoopThroughAndEnrollVtexOrdersToFlowHubBatch(
        IVtexAuthenticationService vtexAuthenticationService,
        IVtexOrderCommander vtexOrderCommander,
        IVtexEventRateLimitProducer vtexEventRateLimitProducer,
        ILogger<LoopThroughAndEnrollVtexOrdersToFlowHubBatch> logger)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
        _vtexOrderCommander = vtexOrderCommander;
        _vtexEventRateLimitProducer = vtexEventRateLimitProducer;
        _logger = logger;
    }

    public class LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput : Sleekflow.CommerceHub.Models.Workers.LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput
    {
        public LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput(string sleekflowCompanyId, string flowHubWorkflowId, string flowHubWorkflowVersionedId, string vtexAuthenticationId, int page, VtexGetOrdersSearchCondition condition) : base(sleekflowCompanyId, flowHubWorkflowId, flowHubWorkflowVersionedId, vtexAuthenticationId, page, condition)
        {
        }
    }

    public class LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput : Sleekflow.CommerceHub.Models.Workers.LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput
    {
        public LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(int count, bool hasNextPage, DateTimeOffset? processedEarliestOrderCreatedAt) : base(count, hasNextPage, processedEarliestOrderCreatedAt)
        {
        }
    }

    public async Task<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput> F(
        LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput input)
    {
        var vtexAuthentication = await _vtexAuthenticationService.GetAsync(
            input.VtexAuthenticationId,
            input.SleekflowCompanyId);

        if (vtexAuthentication == null)
        {
            throw new SfNotFoundObjectException(input.VtexAuthenticationId, input.SleekflowCompanyId);
        }

        var vtexGetOrdersResponse = await _vtexOrderCommander.GetOrdersAsync(
            vtexAuthentication.Credential,
            input.Condition,
            input.Page,
            BatchSize);

        // early return when no records fetched
        if (vtexGetOrdersResponse.Orders.Count == 0)
        {
            return new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(
                0,
                false,
                null);
        }

        // as VTEX API has an avg response time for ~1.5 seconds,
        // we publish an unique event for each order overview to prevent Timeout and Rate Limit issues
        var tasks = vtexGetOrdersResponse
            .Orders
            .Select(
                simplifiedOrder => _vtexEventRateLimitProducer.PublishWithRateLimitAsync(
                    new OnVtexOrderForFlowHubEnrollmentFetchedEvent(
                        input.SleekflowCompanyId,
                        input.VtexAuthenticationId,
                        simplifiedOrder.OrderId,
                        input.FlowHubWorkflowId,
                        input.FlowHubWorkflowVersionedId),
                    input.SleekflowCompanyId))
            .ToList();
        await Task.WhenAll(tasks);

        _logger.LogInformation(
            "[Loop Through and Enroll VTEX Orders] Enrolled {Count} orders to FlowHub workflow {FlowHubWorkflowId} for company {SleekflowCompanyId}. OrderIds: {OrderIds}",
            vtexGetOrdersResponse.Orders.Count,
            input.FlowHubWorkflowId,
            input.SleekflowCompanyId,
            string.Join(", ", vtexGetOrdersResponse.Orders.Select(o => o.OrderId)));

        // statistics
        var count = vtexGetOrdersResponse.Orders.Count;
        var hasNextPage = vtexGetOrdersResponse.Paging.CurrentPage * BatchSize < vtexGetOrdersResponse.Paging.Total;
        var earliestProcessedOrderCreatedAt = vtexGetOrdersResponse.Orders
            .Select(o => o.CreationDate)
            .Min();

        return new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(
            count,
            hasNextPage,
            earliestProcessedOrderCreatedAt);
    }
}