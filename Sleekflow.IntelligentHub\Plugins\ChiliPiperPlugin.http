### Create a meeting with <PERSON><PERSON>

# "chili_piper_config": {
#     "api_url": "https://sleekflow.chilipiper.com/concierge-router/inbound_router/rest",
#     "field_mappings": {
#         "company_name": "CompanyName",
#         "country": "ef26de54-ca30-4a90-b233-69088e5741dc",
#         "country_region": "4b39f91e-f426-4ba3-aa3b-12f301734f97",
#         "ecommerce_platform": "e2f82b15-4a2a-4438-b068-34742b8acf97",
#         "email": "PersonEmail",
#         "employees_count": "134d6b7e-edda-465f-bde4-d14826719a2c",
#         "first_name": "PersonFirstName",
#         "job_title": "0e613939-d851-4b4c-8c24-b59d02dd7992",
#         "last_name": "PersonLastName",
#         "message": "6333efc1-81ca-44c6-ae28-df0ff0e3ec42",
#         "phone_number": "bd87fc44-7fdd-40f0-8a3a-c4635ec2066a",
#         "website": "f8d47804-05e5-4a11-a2b6-d733a96e06d4"
#     }
# }

POST https://sleekflow.chilipiper.com/concierge-router/inbound_router/rest
Content-Type: application/json

{
  "form": {
    "PersonFirstName": "John",
    "PersonLastName": "Doe",
    "PersonEmail": "<EMAIL>",
    "CompanyName": "Example Corp",
    "bd87fc44-7fdd-40f0-8a3a-c4635ec2066a": "+1234567890",
    "134d6b7e-edda-465f-bde4-d14826719a2c": "50-100",
    "ef26de54-ca30-4a90-b233-69088e5741dc": "United States",
    "4b39f91e-f426-4ba3-aa3b-12f301734f97": "North America",
    "6333efc1-81ca-44c6-ae28-df0ff0e3ec42": "Interested in scheduling a demo to learn more about your platform",
    "e2f82b15-4a2a-4438-b068-34742b8acf97": "Shopify",
    "0e613939-d851-4b4c-8c24-b59d02dd7992": "Marketing Manager",
    "f8d47804-05e5-4a11-a2b6-d733a96e06d4": "https://example.com"
  },
  "options": {
    "dynamicRedirectLink": "https://sleekflow.io/thank-you/booking-confirmed"
  }
}
