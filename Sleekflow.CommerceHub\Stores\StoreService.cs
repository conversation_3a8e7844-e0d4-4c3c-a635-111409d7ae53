using System.Linq.Expressions;
using Microsoft.AspNetCore.Mvc.Rendering;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Expressions;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Stores;

public interface IStoreService
{
    Task<Store> CreateStoreAsync(Store store, string sleekflowCompanyId);

    Task<Store> GetStoreAsync(
        string id,
        string sleekflowCompanyId);

    Task<(List<Store> Stores, string? NextContinuationToken)> GetStoresAsync(
        string? continuationToken,
        string sleekflowCompanyId,
        int limit,
        bool? isViewEnabled,
        bool? isPaymentEnabled,
        string? providerName);

    Task<Store> PatchAndGetStoreAsync(
        string id,
        string sleekflowCompanyId,
        List<Multilingual> names,
        List<Description> descriptions,
        bool isViewEnabled,
        bool isPaymentEnabled,
        List<Language> languages,
        List<Currency> currencies,
        StoreTemplateDict storeTemplateDict,
        Dictionary<string, object?> metadata,
        StoreIntegrationExternalConfig? storeIntegrationExternalConfig,
        StoreSubscriptionStatus? storeSubscriptionStatus,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeleteStoreAsync(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);
}

public class StoreService : IStoreService, IScopedService
{
    private readonly IStoreRepository _storeRepository;
    private readonly IStoreValidator _storeValidator;

    public StoreService(
        IStoreRepository storeRepository,
        IStoreValidator storeValidator)
    {
        _storeRepository = storeRepository;
        _storeValidator = storeValidator;
    }

    public async Task<Store> CreateStoreAsync(Store store, string sleekflowCompanyId)
    {
        if (store.Languages.Count == 0)
        {
            store.Languages = new List<Language>
            {
                Language.DefaultLanguage()
            };
        }

        _storeValidator.AssertValidStoreProperties(
            store.Names,
            store.Descriptions,
            store.Languages,
            store.PlatformData,
            null);

        return await _storeRepository.CreateAndGetAsync(
            store,
            sleekflowCompanyId);
    }

    public async Task<Store> GetStoreAsync(string id, string sleekflowCompanyId)
    {
        var store = await _storeRepository.GetAsync(id, sleekflowCompanyId);
        if (store == null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        return store;
    }

    public async Task<(List<Store> Stores, string? NextContinuationToken)> GetStoresAsync(
        string? continuationToken,
        string sleekflowCompanyId,
        int limit,
        bool? isViewEnabled,
        bool? isPaymentEnabled,
        string? providerName)
    {
        Expression<Func<Store, bool>> predicate = store =>
            store.SleekflowCompanyId == sleekflowCompanyId;

        return await _storeRepository.GetContinuationTokenizedObjectsAsync(
            predicate
                .IfAndAlso(
                    () => isViewEnabled.HasValue,
                    obj => obj.IsViewEnabled == isViewEnabled)
                .IfAndAlso(
                    () => isPaymentEnabled.HasValue,
                    obj => obj.IsPaymentEnabled == isPaymentEnabled)
                .IfAndAlso(
                    () => providerName != null,
                    obj => obj.StoreIntegrationExternalConfig != null &&
                           obj.StoreIntegrationExternalConfig.ProviderName == providerName),
            continuationToken,
            limit,
            CancellationToken.None);
    }

    public async Task<Store> PatchAndGetStoreAsync(
        string id,
        string sleekflowCompanyId,
        List<Multilingual> names,
        List<Description> descriptions,
        bool isViewEnabled,
        bool isPaymentEnabled,
        List<Language> languages,
        List<Currency> currencies,
        StoreTemplateDict storeTemplateDict,
        Dictionary<string, object?> metadata,
        StoreIntegrationExternalConfig? storeIntegrationExternalConfig,
        StoreSubscriptionStatus? storeSubscriptionStatus,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var store = await GetStoreAsync(id, sleekflowCompanyId);

        _storeValidator.AssertValidStoreProperties(
            names,
            descriptions,
            languages,
            store.PlatformData);

        store.Names = names;
        store.Descriptions = descriptions;
        store.IsViewEnabled = isViewEnabled;
        store.IsPaymentEnabled = isPaymentEnabled;
        store.Languages = languages;
        store.Currencies = currencies;
        store.TemplateDict = storeTemplateDict;
        store.Metadata = metadata;
        store.StoreIntegrationExternalConfig = storeIntegrationExternalConfig;
        store.SubscriptionStatus = storeSubscriptionStatus;
        store.UpdatedBy = sleekflowStaff;
        store.UpdatedAt = DateTimeOffset.UtcNow;

        var numOfPatched = await _storeRepository.UpsertAsync(store, sleekflowCompanyId);
        if (numOfPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the store with id, id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }

        return await GetStoreAsync(id, sleekflowCompanyId);
    }

    public async Task DeleteStoreAsync(string id, string sleekflowCompanyId, AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var store = await GetStoreAsync(id, sleekflowCompanyId);

        // TODO: UpdatedBy

        var deleteAsync = await _storeRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the store with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }

        // TODO: Publish a event to remove other objects
    }
}