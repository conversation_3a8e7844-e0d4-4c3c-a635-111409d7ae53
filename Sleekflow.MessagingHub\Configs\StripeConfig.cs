using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Configs;

public interface IStripeConfig
{
    string PaymentGatewayRedirectUrl { get; }

    string StripeApiKey { get; }
}

public class StripeConfig : IConfig, IStripeConfig
{
    public string PaymentGatewayRedirectUrl { get; }

    public string StripeApiKey { get; }

    public StripeConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        // Set up experiment variable
        PaymentGatewayRedirectUrl = Environment.GetEnvironmentVariable("PAYMENT_GATEWAY_REDIRECT_URL", target)
                                    ?? throw new SfMissingEnvironmentVariableException("PAYMENT_GATEWAY_REDIRECT_URL");

        StripeApiKey = Environment.GetEnvironmentVariable("STRIPE_API_KEY", target)
                       ?? throw new SfMissingEnvironmentVariableException("STRIPE_API_KEY");
    }
}