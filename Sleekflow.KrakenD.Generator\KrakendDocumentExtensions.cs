using Newtonsoft.Json;

namespace Sleekflow.KrakenD.Generator;

public partial class HttpClientPluginsSeeHttpsWwwKrakendIoDocsExtendingInjectingPlugins
{
    [JsonProperty("krakend-authentication-plugin", NullValueHandling = NullValueHandling.Ignore)]
    public HttpClientPluginsKrakendAuthenticationPluginConfig? KrakendAuthenticationPlugin { get; set; }
}

public partial class HttpClientPluginsKrakendAuthenticationPluginConfig
{
    [JsonProperty("get_user_auth_details_url", NullValueHandling = NullValueHandling.Ignore)]
    public string? GetUserAuthDetailsUrl { get; set; }
}