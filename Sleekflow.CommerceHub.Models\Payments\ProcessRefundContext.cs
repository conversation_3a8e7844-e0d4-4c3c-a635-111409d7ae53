using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Payments;

public abstract class ProcessRefundContext
{
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("provider_refund_id")]
    public string ProviderRefundId { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("amount")]
    public decimal? Amount { get; set; }

    [JsonConstructor]
    protected ProcessRefundContext(
        string providerName,
        string providerRefundId,
        string status,
        decimal? amount)
    {
        ProviderName = providerName;
        ProviderRefundId = providerRefundId;
        Status = status;
        Amount = amount;
    }
}