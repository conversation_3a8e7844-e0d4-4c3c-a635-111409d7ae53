﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.ActionEvents.CrmHub;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateSchemafulObjectV2StepExecutor : IStepExecutor
{
}

public class UpdateSchemafulObjectV2StepExecutor
    : GeneralStepExecutor<CallStep<UpdateSchemafulObjectV2StepArgs>>,
        IUpdateSchemafulObjectV2StepExecutor,
        IScopedService
{
    private const string AuditSource = "flow_builder";

    private readonly IStateEvaluator _stateEvaluator;
    private readonly IRequestClient<UpdateSchemafulObjectRequest> _updateSchemafulObjectRequestClient;

    public UpdateSchemafulObjectV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IRequestClient<UpdateSchemafulObjectRequest> updateSchemafulObjectRequestClient)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _updateSchemafulObjectRequestClient = updateSchemafulObjectRequestClient;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _updateSchemafulObjectRequestClient.GetResponse<UpdateSchemafulObjectReply>(
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<UpdateSchemafulObjectRequest> GetArgs(
        CallStep<UpdateSchemafulObjectV2StepArgs> callStep,
        ProxyState state)
    {
        var schemaId = callStep.Args.SchemaId;
        var recordSource = callStep.Args.RecordSource;

        var primaryPropertyValue = string.Empty;
        var sleekflowUserProfileIdForRecordLocator = string.Empty;

        switch (recordSource)
        {
            case "last_object_record":
                sleekflowUserProfileIdForRecordLocator = (string)
                    (await _stateEvaluator.EvaluateExpressionAsync(
                        state,
                        "{{ (trigger_event_body.contact_id | string.whitespace) ? usr_var_dict.contact.id : trigger_event_body.contact_id }}") ?? string.Empty);
                break;
            case "record_id":
            case "variable":
                primaryPropertyValue = (string)
                    (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
                        state,
                        callStep.Args.PrimaryPropertyValueExpr ?? string.Empty) ?? string.Empty);
                break;
            default:
                throw new InvalidOperationException($"Unsupported record source: {recordSource}");
        }

        var propertiesKeyExprDict = callStep.Args.CustomObjectPropertiesIdExprSet
            .GroupBy(x => x.PropertyId)
            .ToDictionary(x => x.Key, g => g.Last().PropertyValueExpr);

        var schemaPropertiesDict = new Dictionary<string, object?>();

        foreach (var entry in propertiesKeyExprDict)
        {
            schemaPropertiesDict[entry.Key] = string.IsNullOrWhiteSpace(entry.Value)
                ? null
                : await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, entry.Value);
        }

        var sleekflowCompanyId = state.Identity.SleekflowCompanyId;

        return new UpdateSchemafulObjectRequest(
            schemaId,
            sleekflowCompanyId,
            primaryPropertyValue,
            schemaPropertiesDict,
            string.Empty,
            sleekflowUserProfileIdForRecordLocator,
            AuditSource);
    }
}