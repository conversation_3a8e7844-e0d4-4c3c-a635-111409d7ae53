using Alba;
using GraphApi.Client.Models.MessageObjects;
using GraphApi.Client.Models.MessageObjects.ReactionObjects;
using GraphApi.Client.Models.MessageObjects.TemplateObjects;
using Moq;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Tests.Constants;
using Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;
using Sleekflow.MessagingHub.Triggers.Messages.WhatsappCloudApi;
using Sleekflow.MessagingHub.Triggers.Template.WhatsappCloudApi;
using Sleekflow.MessagingHub.Triggers.Wabas;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi;

// Test requires Waba Token
public class CoreIntegrationTests
{
    private
        Output<ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaOutput> _connectTestWhatsappCloudApiWabaOutput;

    [SetUp]
    public async Task Setup()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Waba token varies overtime");
        }

        // var connectTestWhatsappCloudApiWabaOutput = await Mocks.ConnectTestWhatsappCloudApiWabaOutputAsync();
        // _connectTestWhatsappCloudApiWabaOutput = connectTestWhatsappCloudApiWabaOutput;
    }

    // Test cases are disabled due to tests need valid Waba tokens to send messages, as tokens will automatically expire after certain time period
    // Team: Ironman
    // [Test]
    // public async Task HealthzTest()
    // {
    //     await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.Get.Url(Endpoints.Healthz);
    //             _.ContentShouldBe("HEALTH");
    //             _.StatusCodeShouldBeOk();
    //         });
    // }

    // [Test]
    // public async Task WabaTest()
    // {
    //     var connectWhatsappCloudApiWabaInput = new ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaInput(
    //         Mocks.SleekflowCompanyId,
    //         Secrets.WhatsappCloudApiUserAccessToken,
    //         new List<string>(),
    //         Mocks.SleekflowStaffId,
    //         Mocks.SleekflowStaffTeamIds);
    //
    //     var connectWhatsappCloudApiWabaResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(connectWhatsappCloudApiWabaInput).ToUrl(Endpoints.ConnectWhatsappCloudApiWaba);
    //         });
    //     var connectWhatsappCloudApiWabaOutputOutput =
    //         await connectWhatsappCloudApiWabaResult
    //             .ReadAsJsonAsync<Output<ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaOutput>>();
    //
    //     Assert.That(connectWhatsappCloudApiWabaOutputOutput, Is.Not.Null);
    //     Assert.That(connectWhatsappCloudApiWabaOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(connectWhatsappCloudApiWabaOutputOutput.Data, Is.Not.Null);
    //
    //     var connectWhatsappCloudApiWabaDtos = connectWhatsappCloudApiWabaOutputOutput.Data.WabaDtoDtos;
    //     Assert.That(connectWhatsappCloudApiWabaDtos?.Count, Is.EqualTo(1));
    //     var connectWhatsappCloudApiWabaDto = connectWhatsappCloudApiWabaDtos[0];
    //     Assert.That(connectWhatsappCloudApiWabaDto.FacebookWabaName, Is.EqualTo("Alex's Meta Business Channel"));
    //
    //     var getConnectedWhatsappCloudApiWabasInput =
    //         new GetConnectedWhatsappCloudApiWabas.GetConnectedWhatsappCloudApiWabasInput(
    //             Mocks.SleekflowCompanyId,
    //             false,
    //             null,
    //             null,
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //
    //     var getConnectedWhatsappCloudApiWabasResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getConnectedWhatsappCloudApiWabasInput).ToUrl(Endpoints.GetConnectedWhatsappCloudApiWabas);
    //         });
    //     var getConnectedWhatsappCloudApiWabasOutputOutput =
    //         await getConnectedWhatsappCloudApiWabasResult
    //             .ReadAsJsonAsync<Output<GetConnectedWhatsappCloudApiWabas.GetConnectedWhatsappCloudApiWabasOutput>>();
    //
    //     Assert.That(getConnectedWhatsappCloudApiWabasOutputOutput, Is.Not.Null);
    //     Assert.That(getConnectedWhatsappCloudApiWabasOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(getConnectedWhatsappCloudApiWabasOutputOutput.Data, Is.Not.Null);
    //
    //     var getConnectedWhatsappCloudApiWabaDtos =
    //         getConnectedWhatsappCloudApiWabasOutputOutput.Data.ConnectedConnectedWabas;
    //     Assert.That(getConnectedWhatsappCloudApiWabaDtos?.Count, Is.EqualTo(1));
    //
    //     var getConnectedWhatsappCloudApiWabaDto = getConnectedWhatsappCloudApiWabaDtos[0];
    //
    //     Assert.That(connectWhatsappCloudApiWabaDto.Id, Is.EqualTo(getConnectedWhatsappCloudApiWabaDto.Id));
    // }

    // [Test]
    // public async Task ChannelsTest()
    // {
    //     var waba = _connectTestWhatsappCloudApiWabaOutput.Data.WabaDtoDtos[0];
    //     var wabaPhoneNumber = waba.WabaPhoneNumbers[0];
    //     var connectWhatsappCloudApiChannelInput = new ConnectWhatsappCloudApiChannel
    //         .ConnectWhatsappCloudApiChannelInput(
    //             Mocks.SleekflowCompanyId,
    //             waba.Id,
    //             wabaPhoneNumber.Id,
    //             "TESTING_WEBHOOK_URL",
    //             "000000",
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //     var connectWhatsappCloudAPiChannelResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(connectWhatsappCloudApiChannelInput).ToUrl(Endpoints.ConnectWhatsappCloudApiChannel);
    //         });
    //
    //     var connectWhatsappCloudAPiChannelOutputOutput =
    //         await connectWhatsappCloudAPiChannelResult
    //             .ReadAsJsonAsync<Output<ConnectWhatsappCloudApiChannel.ConnectWhatsappCloudApiChannelOutput>>();
    //
    //     Assert.That(connectWhatsappCloudAPiChannelOutputOutput, Is.Not.Null);
    //     Assert.That(connectWhatsappCloudAPiChannelOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(connectWhatsappCloudAPiChannelOutputOutput.Data, Is.Not.Null);
    //
    //     Assert.That(
    //         connectWhatsappCloudAPiChannelOutputOutput?.Data.ConnectedCloudApiChannels.ConnectedApiChannels.Count,
    //         Is.EqualTo(1));
    //
    //     var getConnectedWhatsappCloudApiChannels = new GetConnectedWhatsappCloudApiChannels.
    //         GetConnectedWhatsappCloudApiChannelsInput(
    //             Mocks.SleekflowCompanyId,
    //             true,
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //
    //     var getConnectedWhatsappCloudApiChannelsResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getConnectedWhatsappCloudApiChannels).ToUrl(Endpoints.GetConnectedWhatsappCloudApiChannels);
    //         });
    //
    //     var getConnectedWhatsappCloudApiChannelsOutputOutput = await getConnectedWhatsappCloudApiChannelsResult
    //         .ReadAsJsonAsync<Output<GetConnectedWhatsappCloudApiChannels.GetConnectedWhatsappCloudApiChannelsOutput>>();
    //
    //     Assert.That(getConnectedWhatsappCloudApiChannelsOutputOutput, Is.Not.Null);
    //     Assert.That(getConnectedWhatsappCloudApiChannelsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(getConnectedWhatsappCloudApiChannelsOutputOutput.Data, Is.Not.Null);
    //     Assert.That(getConnectedWhatsappCloudApiChannelsOutputOutput.Data.ConnectedCloudApis.Count, Is.EqualTo(1));
    //
    //     var disconnectWhatsappCloudApiChannel =
    //         new DisconnectWhatsappCloudApiChannel.DisconnectWhatsappCloudApiChannelInput(
    //             Mocks.SleekflowCompanyId,
    //             waba.Id,
    //             wabaPhoneNumber.Id,
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //
    //     var disconnectWhatsappCloudApiChannelResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(disconnectWhatsappCloudApiChannel).ToUrl(Endpoints.DisconnectWhatsappCloudApiChannel);
    //         });
    //
    //     var disconnectWhatsappCloudApiChannelOutputOutput =
    //         await disconnectWhatsappCloudApiChannelResult
    //             .ReadAsJsonAsync<Output<DisconnectWhatsappCloudApiChannel.DisconnectWhatsappCloudApiChannelOutput>>();
    //
    //     Assert.That(disconnectWhatsappCloudApiChannelOutputOutput, Is.Not.Null);
    //     Assert.That(disconnectWhatsappCloudApiChannelOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(disconnectWhatsappCloudApiChannelOutputOutput.Data, Is.Not.Null);
    //     Assert.That(disconnectWhatsappCloudApiChannelOutputOutput.Data.Disconnected, Is.True);
    // }

    // [Test]
    // public async Task TemplatesTest()
    // {
    //     var waba = _connectTestWhatsappCloudApiWabaOutput.Data.WabaDtoDtos[0];
    //     var createWhatsappCloudApiTemplateInput =
    //         new CreateWhatsappCloudApiTemplate.CreateWhatsappCloudApiTemplateInput(
    //             waba.Id,
    //             Mocks.SleekflowCompanyId,
    //             new WhatsappCloudApiCreateTemplateObject());
    //
    //     var createWhatsappCloudApiTemplateResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(createWhatsappCloudApiTemplateInput).ToUrl(Endpoints.GetConnectedWhatsappCloudApiChannels);
    //         });
    //
    //     var createWhatsappCloudApiTemplateOutputOutput = await createWhatsappCloudApiTemplateResult
    //         .ReadAsJsonAsync<Output<CreateWhatsappCloudApiTemplate.CreateWhatsappCloudApiTemplateInput>>();
    //
    //     Assert.That(createWhatsappCloudApiTemplateOutputOutput, Is.Not.Null);
    //     Assert.That(createWhatsappCloudApiTemplateOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(createWhatsappCloudApiTemplateOutputOutput.Data, Is.Not.Null);
    // }

    // [Test]
    // public async Task MessagesTest()
    // {
    //     WabaPhoneNumberDto wabaPhoneNumberDto = null;
    //     var waba = _connectTestWhatsappCloudApiWabaOutput.Data.WabaDtoDtos[0];
    //     var getConnectedWhatsappCloudApiChannels = new GetConnectedWhatsappCloudApiChannels.
    //         GetConnectedWhatsappCloudApiChannelsInput(
    //             Mocks.SleekflowCompanyId,
    //             true,
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //     var getConnectedWhatsappCloudApiChannelsResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getConnectedWhatsappCloudApiChannels).ToUrl(Endpoints.GetConnectedWhatsappCloudApiChannels);
    //         });
    //
    //     var getConnectedWhatsappCloudApiChannelsOutputOutput = await getConnectedWhatsappCloudApiChannelsResult
    //         .ReadAsJsonAsync<Output<GetConnectedWhatsappCloudApiChannels.GetConnectedWhatsappCloudApiChannelsOutput>>();
    //
    //     if (!getConnectedWhatsappCloudApiChannelsOutputOutput.Data.ConnectedCloudApis[0].ConnectedApiChannels
    //             .Any(x => x.SleekflowCompanyId == Mocks.SleekflowCompanyId))
    //     {
    //         var wabaPhoneNumber = waba.WabaPhoneNumbers[0];
    //         var connectWhatsappCloudApiChannelInput = new ConnectWhatsappCloudApiChannel
    //             .ConnectWhatsappCloudApiChannelInput(
    //                 Mocks.SleekflowCompanyId,
    //                 waba.Id,
    //                 wabaPhoneNumber.Id,
    //                 "TESTING_WEBHOOK_URL",
    //                 "0000",
    //                 Mocks.SleekflowStaffId,
    //                 Mocks.SleekflowStaffTeamIds);
    //         var connectWhatsappCloudAPiChannelResult = await Application.Host.Scenario(
    //             _ =>
    //             {
    //                 _.WithRequestHeader("X-Sleekflow-Record", "true");
    //                 _.Post.Json(connectWhatsappCloudApiChannelInput).ToUrl(Endpoints.ConnectWhatsappCloudApiChannel);
    //             });
    //
    //         var connectWhatsappCloudAPiChannelOutputOutput =
    //             await connectWhatsappCloudAPiChannelResult
    //                 .ReadAsJsonAsync<Output<ConnectWhatsappCloudApiChannel.ConnectWhatsappCloudApiChannelOutput>>();
    //
    //         if (connectWhatsappCloudAPiChannelOutputOutput.HttpStatusCode != 200)
    //         {
    //             throw new Exception("unable to connect whatsapp cloud api channels ");
    //         }
    //
    //         wabaPhoneNumberDto = connectWhatsappCloudAPiChannelOutputOutput
    //             .Data.ConnectedCloudApiChannels.ConnectedApiChannels[0];
    //     }
    //     else
    //     {
    //         wabaPhoneNumberDto = getConnectedWhatsappCloudApiChannelsOutputOutput
    //             .Data.ConnectedCloudApis[0].ConnectedApiChannels[0];
    //     }
    //
    //     var sendWhatsappCloudApiMessageInput = new SendWhatsappCloudApiMessage
    //         .SendWhatsappCloudApiMessageInput(
    //             wabaPhoneNumberDto.Id,
    //             Mocks.SleekflowCompanyId,
    //             new WhatsappCloudApiMessageObject
    //             {
    //                 RecipientType = "individual",
    //                 To = "00000000",
    //                 Type = "reaction",
    //                 Reaction = new WhatsappCloudApiReactionObject
    //                 {
    //                     MessageId = "wamid.HBgLODUyNTEwNjU5MDIVAgASGBQzQThCNzcwQjM0QTNBM0VDRjZGMQA=", Emoji = "🙂"
    //                 }
    //             },
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //
    //     var sendWhatsappCloudApiMessageResult = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(sendWhatsappCloudApiMessageInput).ToUrl(Endpoints.SendWhatsappCloudApiMessage);
    //         });
    //
    //     var sendWhatsappCloudApiMessageOutputOutput = await sendWhatsappCloudApiMessageResult
    //         .ReadAsJsonAsync<Output<SendWhatsappCloudApiMessage.SendWhatsappCloudApiMessageOutput>>();
    //
    //     Assert.That(sendWhatsappCloudApiMessageOutputOutput, Is.Not.Null);
    //     Assert.That(sendWhatsappCloudApiMessageOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(sendWhatsappCloudApiMessageOutputOutput.Data, Is.Not.Null);
    //
    //     var disconnectWhatsappCloudApiChannel =
    //         new DisconnectWhatsappCloudApiChannel.DisconnectWhatsappCloudApiChannelInput(
    //             Mocks.SleekflowCompanyId,
    //             waba.Id,
    //             wabaPhoneNumberDto.Id,
    //             Mocks.SleekflowStaffId,
    //             Mocks.SleekflowStaffTeamIds);
    //
    //     await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(disconnectWhatsappCloudApiChannel).ToUrl(Endpoints.DisconnectWhatsappCloudApiChannel);
    //         });
    // }
}