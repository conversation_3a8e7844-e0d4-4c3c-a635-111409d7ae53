using MassTransit;
using Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration;
using Sleekflow.InternalIntegrationHub.Models.Events;
using Sleekflow.Locks;

namespace Sleekflow.InternalIntegrationHub.Events;

public class OmniHrEmployeeInfoToNetSuiteIntegrationEventConsumerDefinition
    : ConsumerDefinition<
        OmniHrEmployeeInfoToNetSuiteIntegrationEventConsumer
    >
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OmniHrEmployeeInfoToNetSuiteIntegrationEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OmniHrEmployeeInfoToNetSuiteIntegrationEventConsumer
    : IConsumer<OmniHrEmployeeInfoToNetSuiteIntegrationEvent>
{
    private readonly IOmniHrEmployeeInfoToNetSuiteExternalIntegration _externalIntegrations;
    private readonly ILockService _lockService;

    public OmniHrEmployeeInfoToNetSuiteIntegrationEventConsumer(
        IOmniHrEmployeeInfoToNetSuiteExternalIntegration externalIntegrations,
        ILockService lockService)
    {
        _externalIntegrations = externalIntegrations;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<OmniHrEmployeeInfoToNetSuiteIntegrationEvent> context)
    {
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            [
                "OmniHrEmployeeInfoToNetSuiteIntegrationEvent"
            ],
            TimeSpan.FromSeconds(
                180),
            cancellationToken);
        if (@lock is null)
        {
            return;
        }

        try
        {
            await _externalIntegrations.Run(cancellationToken);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}