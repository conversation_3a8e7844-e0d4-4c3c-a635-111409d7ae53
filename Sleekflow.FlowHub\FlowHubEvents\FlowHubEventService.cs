using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.FlowHubEvents;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.FlowHubEvents;

public interface IFlowHubEventService
{
    Task CreateFlowHubEventAsync(
        string objectId,
        string sleekflowCompanyId,
        EventBody eventBody);
}

public class FlowHubEventService : IFlowHubEventService, IScopedService
{
    private readonly IIdService _idService;
    private readonly IFlowHubEventRepository _flowHubEventRepository;

    public FlowHubEventService(
        IIdService idService,
        IFlowHubEventRepository flowHubEventRepository)
    {
        _idService = idService;
        _flowHubEventRepository = flowHubEventRepository;
    }

    public async Task CreateFlowHubEventAsync(string objectId, string sleekflowCompanyId, EventBody eventBody)
    {
        try
        {
            await _flowHubEventRepository.CreateAsync(
                new FlowHubEvent(
                    _idService.GetId("FlowHubEvent"),
                    null,
                    eventBody,
                    sleekflowCompanyId,
                    objectId),
                new PartitionKeyBuilder()
                    .Add(sleekflowCompanyId)
                    .Add(objectId)
                    .Build());
        }
        catch
        {
            // ignored
        }
    }
}