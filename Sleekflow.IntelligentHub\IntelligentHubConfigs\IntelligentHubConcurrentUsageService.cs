using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TicketingHub;
using Sleekflow.Locks;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubConcurrentUsageService
{
    public Task TryIncrementConcurrentUsageAsync(string sleekflowCompanyId);

    public Task DecrementConcurrentUsageAsync(string sleekflowCompanyId);
}

public class IntelligentHubConcurrentUsageService : IIntelligentHubConcurrentUsageService, IScopedService
{
    public const int MAX_CONCURRENT_USAGE_LIMIT = 5;

    private readonly ILogger<IntelligentHubConcurrentUsageService> _logger;
    private readonly ILockService _lockService;
    private readonly IConnectionMultiplexer _connectionMultiplexer;

    public IntelligentHubConcurrentUsageService(
        ILogger<IntelligentHubConcurrentUsageService> logger,
        ILockService lockService,
        IConnectionMultiplexer connectionMultiplexer)
    {
        _logger = logger;
        _lockService = lockService;
        _connectionMultiplexer = connectionMultiplexer;
    }

    public async Task TryIncrementConcurrentUsageAsync(string sleekflowCompanyId)
    {
        _logger.LogInformation("TryIncrementConcurrentUsageAsync {SleekflowCompanyId}.", sleekflowCompanyId);

        var @lock = await _lockService.WaitUnitLockAsync(
            [
                nameof(IntelligentHubConcurrentUsageService),
                sleekflowCompanyId
            ],
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(10));

        try
        {
            var redisKey = GetCacheKey(sleekflowCompanyId);

            var database = _connectionMultiplexer.GetDatabase();
            var value = await database.StringGetAsync(redisKey);

            if (value == RedisValue.Null)
            {
                await database.StringSetAsync(redisKey, 1);
            }
            else
            {
                value.TryParse(out int concurrentUsageCount);
                if (concurrentUsageCount >= MAX_CONCURRENT_USAGE_LIMIT)
                {
                    throw new SfExceedLimitException(MAX_CONCURRENT_USAGE_LIMIT);
                }

                concurrentUsageCount++;
                await database.StringSetAsync(redisKey, concurrentUsageCount);
            }
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task DecrementConcurrentUsageAsync(string sleekflowCompanyId)
    {
        _logger.LogInformation("DecrementConcurrentUsageAsync {SleekflowCompanyId}.", sleekflowCompanyId);

        var @lock = await _lockService.WaitUnitLockAsync(
            [
                nameof(IntelligentHubConcurrentUsageService),
                sleekflowCompanyId
            ],
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(10));

        try
        {
            var redisKey = GetCacheKey(sleekflowCompanyId);

            var database = _connectionMultiplexer.GetDatabase();
            var value = await database.StringGetAsync(redisKey);

            if (value == RedisValue.Null)
            {
                throw new SfNotFoundObjectException(sleekflowCompanyId);
            }

            value.TryParse(out int concurrentUsageCount);
            if (concurrentUsageCount == 0)
            {
                throw new SfInternalErrorException("Attempting to decrement concurrent usage when it is 0.");
            }

            concurrentUsageCount--;
            await database.StringSetAsync(redisKey, concurrentUsageCount);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    private string GetCacheKey(string sleekflowCompanyId)
    {
        return $"{nameof(IntelligentHubConcurrentUsageService)}-{sleekflowCompanyId}";
    }
}