using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Events;

public class OnStepRequestedEvent : IHighTrafficEvent
{
    public string StateId { get; set; }

    public string StepId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string? WorkerInstanceId { get; set; }

    public OnStepRequestedEvent(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId)
    {
        StepId = stepId;
        StateId = stateId;
        StackEntries = stackEntries;
        WorkerInstanceId = workerInstanceId;
    }
}