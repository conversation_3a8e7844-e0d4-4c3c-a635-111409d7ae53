﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Webhooks;

namespace Sleekflow.CrmHub.Triggers.Webhooks;

[TriggerGroup("Webhooks")]
public class GetWebhooks : ITrigger
{
    private readonly IWebhookService _webhookService;

    public GetWebhooks(
        IWebhookService webhookService)
    {
        _webhookService = webhookService;
    }

    public class GetWebhooksInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("event_type_name")]
        [Required]
        public string EventTypeName { get; set; }

        [JsonConstructor]
        public GetWebhooksInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string eventTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            EventTypeName = eventTypeName;
        }
    }

    public class GetWebhooksOutput
    {
        [JsonProperty("webhooks")]
        public List<Webhook> Webhooks { get; set; }

        [JsonConstructor]
        public GetWebhooksOutput(List<Webhook> webhooks)
        {
            Webhooks = webhooks;
        }
    }

    public async Task<GetWebhooksOutput> F(
        GetWebhooksInput getWebhooksInput)
    {
        var webhooks = await _webhookService.GetWebhooksAsync(
            getWebhooksInput.SleekflowCompanyId,
            getWebhooksInput.EntityTypeName,
            getWebhooksInput.EventTypeName,
            CancellationToken.None);

        return new GetWebhooksOutput(webhooks);
    }
}