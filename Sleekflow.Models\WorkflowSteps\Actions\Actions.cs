using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps.Actions;

public class AgentActions
{
    [JsonProperty("send_message")]
    public SendMessageAction SendMessage { get; set; }

    [JsonProperty("calculate_lead_score")]
    public CalculateLeadScoreAction CalculateLeadScore { get; set; }

    [JsonProperty("exit_conversation")]
    public ExitConversationAction ExitConversation { get; set; }

    [JsonProperty("add_label")]
    public AddLabelAction AddLabel { get; set; }
}
