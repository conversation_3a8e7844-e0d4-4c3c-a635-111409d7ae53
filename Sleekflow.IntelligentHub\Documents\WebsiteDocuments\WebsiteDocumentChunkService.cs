using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

namespace Sleekflow.IntelligentHub.Documents.WebsiteDocuments;

public interface IWebsiteDocumentChunkService
{
    Task<WebsiteDocumentChunk> CreateWebsiteDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        string pageUrl,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task<List<string>> GetWebsiteDocumentChunkIdsAsync(
        string sleekflowCompanyId,
        string documentId);

    Task<WebsiteDocumentChunk> GetWebsiteDocumentChunkAsync(
        string sleekflowCompanyId,
        string websiteDocumentChunkId);
}

public class WebsiteDocumentChunkService : IWebsiteDocumentChunkService, IScopedService
{
    private readonly IWebsiteDocumentChunkRepository _websiteDocumentChunkRepository;
    private readonly IIdService _idService;

    public WebsiteDocumentChunkService(
        IWebsiteDocumentChunkRepository websiteDocumentChunkRepository,
        IIdService idService)
    {
        _websiteDocumentChunkRepository = websiteDocumentChunkRepository;
        _idService = idService;
    }

    public async Task<WebsiteDocumentChunk> CreateWebsiteDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        string pageUrl,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        var createdWebsiteDocumentChunk = await _websiteDocumentChunkRepository.CreateAndGetAsync(
            new WebsiteDocumentChunk(
                _idService.GetId(SysTypeNames.WebsiteDocumentChunk),
                sleekflowCompanyId,
                documentId,
                pageUrl,
                content,
                contentEn,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                categories,
                metadata),
            sleekflowCompanyId);

        return createdWebsiteDocumentChunk;
    }

    public async Task<List<string>> GetWebsiteDocumentChunkIdsAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        return await _websiteDocumentChunkRepository.GetWebsiteDocumentChunkIdsAsync(
            sleekflowCompanyId,
            documentId);
    }

    public async Task<WebsiteDocumentChunk> GetWebsiteDocumentChunkAsync(
        string sleekflowCompanyId,
        string websiteDocumentChunkId)
    {
        var websiteDocumentChunk =
            await _websiteDocumentChunkRepository.GetAsync(websiteDocumentChunkId, sleekflowCompanyId);
        if (websiteDocumentChunk == null)
        {
            throw new SfNotFoundObjectException(websiteDocumentChunkId, sleekflowCompanyId);
        }

        return websiteDocumentChunk;
    }
}