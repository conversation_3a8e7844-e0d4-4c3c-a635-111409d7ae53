using Sleekflow.MessagingHub.Tests.Constants;
using Sleekflow.MessagingHub.Triggers.Wabas;
using Sleekflow.Outputs;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi;

public static class Mocks
{
    public const string SleekflowCompanyId = "81ab95d5-bb44-4678-9765-f63640a305d8";
    public const string SleekflowStaffId = "TestStaffId";
    public static readonly List<string> SleekflowStaffTeamIds = new ();
    public const string SleekflowCoreWebhookUrl = "https://sleekflow-core-app-eas-dev.azurewebsites.net";

    public static async Task<Output<ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaOutput>>
        ConnectTestWhatsappCloudApiWabaOutputAsync()
    {
        var connectWhatsappCloudApiWabaInput = new ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaInput(
            SleekflowCompanyId,
            Secrets.WhatsappCloudApiUserAccessToken,
            new List<string>(),
            SleekflowCoreWebhookUrl,
            SleekflowStaffId,
            SleekflowStaffTeamIds);

        var connectWhatsappCloudApiWabaResult = await Application.Host.Scenario(
            _ =>
            {
                _.Post.Json(connectWhatsappCloudApiWabaInput).ToUrl(Endpoints.ConnectWhatsappCloudApiWaba);
            });
        var connectWhatsappCloudApiWabaOutputOutput =
            await connectWhatsappCloudApiWabaResult
                .ReadAsJsonAsync<Output<ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaOutput>>();
        if (connectWhatsappCloudApiWabaOutputOutput?.Data.WabaDtoDtos is null)
        {
            throw new Exception("Could not connect waba");
        }

        return connectWhatsappCloudApiWabaOutputOutput;
    }
}