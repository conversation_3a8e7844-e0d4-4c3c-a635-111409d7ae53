{"en": {"signup-password": {"buttonText": "Sign up", "title": "Create your account", "loginActionLinkText": "Sign in", "email-in-use": "We couldn’t complete your registration. Please try again or contact us for support.", "email-taken": "We couldn’t complete your registration. Please try again or contact us for support.", "description": "Fill in details to sign up for your SleekFlow account"}, "email-verification": {"email-verification-result": {"buttonText": "Back"}}, "login-id": {"auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "authentication-failure": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Continue", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Sign in to continue to SleekFlow", "footerLinkText": "Sign up", "footerText": "Don't have an account?", "forgotPasswordText": "Forgot password?", "invalid-connection": "Invalid connection. Please try again later.", "invalid-email-format": "Invalid email.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "no-db-connection": "Invalid connection. Please try again later.", "no-email": "Please enter an email address.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Password", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "OR", "title": "Welcome back", "user-blocked": "Your account has been blocked after multiple consecutive login attempts. Please contact the administrator.", "usernamePlaceholder": "Email or username", "wrong-credentials": "Wrong username or password.", "wrong-email-credentials": "Wrong email or password."}, "login": {"auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "authentication-failure": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Sign in", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Sign in to continue", "footerLinkText": "Sign up", "footerText": "Don't have an account?", "forgotPasswordText": "Forgot password?", "invalid-connection": "Invalid connection. Please try again later.", "invalid-email-format": "Invalid email.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "no-db-connection": "Invalid connection. Please try again later.", "no-email": "Please enter an email address.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Password", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "OR", "title": "Welcome back", "user-blocked": "Your account has been blocked after multiple consecutive login attempts. Please contact the administrator.", "usernamePlaceholder": "Email or username", "wrong-credentials": "Incorrect username or password.", "wrong-email-credentials": "Wrong email or password."}, "login-password": {"description": "Fill in details to continue to SleekFlow", "title": "Sign in to continue", "buttonText": "Sign in", "var-tos": "Keep me signed in"}, "signup-id": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "This is required", "loginActionLinkText": "Sign in", "buttonText": "Sign up", "title": "Create your account", "description": "Sign up for your SleekFlow account to continue"}, "signup": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "This is required", "auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Continue", "custom-script-error-code": "The information you have filled in is incorrect. Please check and try again.", "description": "Sign up to continue", "email-in-use": "We couldn’t complete your registration. Please try again or contact us for support.", "emailPlaceholder": "Email", "invalid-connection": "Invalid connection.", "invalid-email-format": "<PERSON><PERSON> is invalid.", "invalid-username": "Username can only contain alphanumeric characters or: ‘${characters}’. Username should have between ${min} and ${max} characters.", "invalid-username-email-not-allowed": "The username cannot be an email.", "invalid-username-invalid-characters": "The username has invalid characters.", "invalid-username-max-length": "The username must not be longer than ${max} characters.", "invalid-username-min-length": "The username must have at least ${min} characters.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "ip-signup-blocked": "Too many signups from the same IP address.", "loginActionLinkText": "Sign in", "loginActionText": "Already have an account?", "no-db-connection": "Invalid connection.", "no-email": "Please enter an email address.", "no-password": "Password is required.", "no-re-enter-password": "Please re-enter your password.", "no-username": "Username is required.", "password-contains-user-information": "Password contains user information.", "password-mismatch": "Passwords don’t match.", "password-policy-not-conformant": "The password is too weak.", "password-previously-used": "Password has previously been used.", "password-too-common": "The password is too common.", "passwordPlaceholder": "Password", "separatorText": "Or", "title": "Welcome to SleekFlow", "username-taken": "The username provided is in use already.", "usernamePlaceholder": "Username"}, "reset-password": {"reset-password": {"auth0-users-expired-ticket": "This session was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "buttonText": "Continue", "custom-script-error-code": "Something went wrong, please try again later.", "description": "Enter a new password below.", "hidePasswordText": "Hide password", "logoAltText": "${companyName}", "no-re-enter-password": "New password confirmation is missing", "pageTitle": "Reset your password | ${clientName}", "password-contains-user-information": "Password contains user information", "password-mismatch": "Passwords don't match", "passwordPlaceholder": "New password", "passwordSecurityText": "Your password must contain:", "reEnterpasswordPlaceholder": "Re-enter password", "showPasswordText": "Show password", "title": "Change Your Password"}, "reset-password-email": {"emailDescription": "Please check the email address ${email} for instructions to reset your password.", "pageTitle": "Check your email | ${clientName}", "resendLinkText": "Resend email", "title": "Check Your Email", "usernameDescription": "Please check the email address associated with the username ${email} for instructions to reset your password."}, "reset-password-error": {"auth0-users-expired-ticket": "This ticket was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "backToLoginLinkText": "Back to ${clientName}", "custom-script-error-code": "Something went wrong, please try again later.", "descriptionExpired": "To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "descriptionGeneric": "To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "descriptionUsed": "This link has already been used. To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "eventTitleExpired": "Link Expired", "eventTitleGeneric": "Invalid Link", "eventTitleUsed": "Invalid Link", "pageTitle": "Password reset error | ${clientName}", "reset-password-error": "We had a problem sending the email, please try again later."}, "reset-password-mfa-email-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "backText": "Go Back", "buttonText": "Continue", "description": "We've sent an email with your code to", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "mfa-email-challenge-authenticator-error": "We couldn't send the email. Please try again later.", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive an email?", "title": "Verify Your Identity", "too-many-email": "You have exceeded the amount of emails. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-otp-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "Check your preferred one-time password application for a code.", "logoAltText": "${companyName}", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your one-time code", "rememberMeText": "Remember this device for 30 days", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "usePasswordText": "Use password"}, "reset-password-mfa-phone-challenge": {"changePhoneText": "Choose another phone number.", "chooseMessageTypeText": "How do you want to receive the code?", "continueButtonText": "Continue", "description": "We will send a 6-digit code to the following phone number:", "invalid-phone": "It seems that your phone number is not valid. Please check and retry.", "invalid-phone-format": "Phone number can only include digits.", "logoAltText": "${companyName}", "no-phone": "Please enter a phone number", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your phone number", "send-sms-failed": "There was a problem sending the SMS", "send-voice-failed": "There was a problem making the voice call", "smsButtonText": "Text message", "title": "Verify Your Identity", "too-many-sms": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "too-many-voice": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "voiceButtonText": "Voice call"}, "reset-password-mfa-push-challenge-push": {"buttonText": "I've responded on my device", "challenge-transaction-pending": "You must accept the notification via the ${appName} app on your mobile device.", "description": "We’ve sent a notification to the following device via the ${appName} app:", "enterOtpCode": "Manually Enter Code", "logoAltText": "${companyName}", "mfa-push-challenge-authenticator-error": "We couldn't send the notification. Please try again later.", "mfa-push-verify-authenticator-error": "We couldn't verify the enrollment. Please try again later.", "mfa-push-verify-transaction-pending": "We have not received a confirmation, please try scanning the code again.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "polling-interval-exceeded": "We have not received a confirmation, please slow down.", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive a notification?", "separatorText": "OR", "title": "Verify Your Identity", "too-many-push": "We have received too many notification requests. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "transaction-rejected": "Notification rejected"}, "reset-password-mfa-recovery-code-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "Enter the recovery code you were provided during your initial enrollment.", "invalid-code": "The code you entered is invalid", "invalid-code-format": "Recovery code must have 24 alphanumeric characters", "logoAltText": "${companyName}", "no-confirmation": "Please confirm you have recorded the code", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your recovery code", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-sms-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "We've sent a text message to:", "editLinkScreenReadableText": "Edit phone number", "editText": "Edit", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the 6-digit code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive a code?", "resendVoiceActionSeparatorTextBefore": "or", "resendVoiceActionText": "get a call", "send-sms-failed": "There was a problem sending the SMS", "sms-authenticator-error": "We couldn't send the SMS. Please try again later.", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "too-many-sms": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-voice-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "changePhoneText": "Choose another phone number.", "description": "We've sent a 6-digit code via voice phone call to the following phone number:", "editLinkScreenReadableText": "Edit phone number", "editText": "Edit", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the 6-digit code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Call again", "resendSmsActionSeparatorTextBefore": "or", "resendSmsActionText": "send a text", "resendText": "Didn't receive a call?", "send-voice-failed": "There was a problem making the voice call", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "too-many-voice": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "voice-authenticator-error": "We couldn't make the voice call. Please try again later."}, "reset-password-mfa-webauthn-platform-challenge": {"awaitingConfirmation": "Awaiting device confirmation", "continueButtonText": "Continue", "description": "Press the button below and follow your browser's steps to log in.", "logoAltText": "${companyName}", "pickAuthenticatorText": "Try another method", "rememberMeText": "Remember this device for 30 days", "title": "Use fingerprint or face recognition to reset password | ${clientName}", "too-many-webauthn-challenge-attempts-error": "Too many failed authentication attempts. Please try again later.", "usePasswordText": "Use password", "webauthn-platform-challenge-error": "We could not start the device verification. Please try again later."}, "reset-password-mfa-webauthn-roaming-challenge": {"awaitingConfirmation": "Awaiting Security Key", "continueButtonText": "Use security key", "description": "Make sure your Security Key is nearby. Once you continue, you will be prompted to use it.", "logoAltText": "${companyName}", "pageTitle": "Use your security key to reset password | ${clientName}", "pickAuthenticatorText": "Try another method", "rememberMeText": "Remember this device for 30 days", "title": "Verify Your Identity", "too-many-webauthn-challenge-attempts-error": "Too many failed authentication attempts. Please try again later.", "webauthn-challenge-error": "We could not start the security key verification. Please try again later."}, "reset-password-request": {"auth0-users-expired-ticket": "This ticket was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "backToLoginLinkText": "Back", "buttonText": "Send link", "custom-script-error-code": "Something went wrong, please try again later.", "descriptionEmail": "Enter your email address, and we’ll send you a link to reset your password.", "descriptionUsername": "Enter your email address, and we’ll send you a link to reset your password.", "invalid-email-format": "Email is not valid.", "logoAltText": "${companyName}", "no-email": "Please enter an email address", "no-username": "Email is required", "pageTitle": "Reset your password | ${clientName}", "placeholderEmail": "Email address", "placeholderUsername": "EMAIL", "reset-password-error": "We had a problem sending the email, please try again later.", "title": "Forgot your password?", "too-many-email": "You have exceeded the amount of emails. Wait a few minutes and try again.", "too-many-requests": "You have exceeded the amount of emails. Wait a few minutes and try again."}, "reset-password-success": {"buttonText": "Back", "description": "Your password has been changed successfully.", "eventTitle": "Password Changed!", "pageTitle": "Password reset successful | ${clientName}"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "Enter your one-time passcode to sign in ${clientName}", "title": "Verify your account", "description": "Open the authenticator app you have previously linked your SleekFlow account to, and enter the passcode to continue. ", "placeholder": "Enter your one-time passcode", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. "}, "mfa-otp-enrollment-code": {"pageTitle": "Copy the code to sign in using a one-time passcode ${clientName}", "description": "Copy this code to your authenticator app to complete setting up 2FA. Once 2FA is set up, enter the one-time passcode from the authenticator app.   (We recommend downloading  Microsoft Authenticator or Google Authenticator from App Store or Google Play Store.)", "placeholder": "Enter your one-time passcode", "title": "Set up 2FA manually", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. "}, "mfa-otp-enrollment-qr": {"description": "Scan the QR code below with an authenticator app and enter the one-time passcode to continue. We recommend downloading  Microsoft Authenticator or Google Authenticator from App Store or Google Play Store. ", "codeEnrollmentText": "Set up manually instead", "pageTitle": "Scan the code to sign in using a one-time passcode ${clientName}", "title": "Secure your account", "placeholder": "Enter your one-time passcode", "separatorText": "AND", "invalid-code": "The passcode you entered is invalid", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. ", "user-already-enrolled": "You have already enrolled on 2FA. Please go back to the previous page to sign in."}}}, "zh-CN": {"signup-password": {"buttonText": "注册", "title": "创建帐户", "loginActionLinkText": "登录", "description": "填写资料以继续注册 SleekFlow 帐户"}, "email-verification": {"email-verification-result": {"buttonText": "返回"}}, "login-id": {"auth0-users-validation": "你所输入的资料有误，请检查后再尝试。", "authentication-failure": "你所输入的资料有误，请检查后再尝试。", "buttonText": "继续", "custom-script-error-code": "你所输入的资料有误，请检查后再尝试。", "description": "登入以继续使用 SleekFlow", "footerLinkText": "立即注册", "footerText": "未有 SleekFlow 帐户？", "forgotPasswordText": "忘记密码？", "invalid-connection": "连线无效，请稍后再尝试。", "invalid-email-format": "电邮地址无效。", "ip-blocked": "系统侦测到可疑的登入行为，并已阻止及后的登入尝试。请联系管理员。", "no-db-connection": "连线无效，请稍后再尝试。", "no-email": "请输入电邮地址。", "no-password": "必须填写密码。", "no-username": "必须填写。", "password-breached": "系统侦测到潜在安全风险，为保障你的帐户安全，系统已阻止这次登入。请重置密码后再登入。", "passwordPlaceholder": "密码", "same-user-login": "此帐号登录尝试次数过多，请稍后再试。", "separatorText": "或用以下方式登入", "title": "欢迎回来", "user-blocked": "你已多次尝试登入并失败，帐户已被封锁。请联系管理员。", "usernamePlaceholder": "电邮地址或帐户名称", "wrong-credentials": "用户名称或密码错误。", "wrong-email-credentials": "电邮地或密码错误。"}, "login": {"auth0-users-validation": "你所输入的资料有误，请检查后再尝试。", "authentication-failure": "你所输入的资料有误，请检查后再尝试。", "buttonText": "登入", "custom-script-error-code": "你所输入的资料有误，请检查后再尝试。", "description": "登入以继续使用 SleekFlow", "footerLinkText": "注册", "footerText": "未有 SleekFlow 帐户？", "forgotPasswordText": "忘记密码？", "invalid-connection": "连线无效，请稍后再尝试。", "invalid-email-format": "电邮地址无效。", "ip-blocked": "系统侦测到可疑的登入行为，并已阻止及后的登入尝试。请联系管理员。", "no-db-connection": "连线无效，请稍后再尝试。", "no-email": "请输入电邮地址。", "no-password": "必须填写密码。", "no-username": "必须填写。", "password-breached": "系统侦测到潜在安全风险，为保障你的帐户安全，系统已阻止这次登入。请重置密码后再登入。", "passwordPlaceholder": "密码", "same-user-login": "此帐号登录尝试次数过多，请稍后再试。", "separatorText": "或用以下方式登入", "title": "欢迎回来", "user-blocked": "你已多次尝试登入并失败，帐户已被封锁。请联系管理员。", "usernamePlaceholder": "电邮地址或帐户名称", "wrong-credentials": "用户名称或密码错误。", "wrong-email-credentials": "电邮地或密码错误。"}, "signup-id": {"var-tos": "剔选此方格，即代表我／我们已阅读及同意 SleekFlow 的[服务条款](https://sleekflow.io/terms)及[私隐政策](https://sleekflow.io/privacy)。", "var-required-tos": "必须填写", "loginActionLinkText": "注册", "buttonText": "注册", "title": "创建帐户", "description": "注册 SleekFlow 帐户以继续下一步"}, "login-password": {"description": "填写资料以继续下一步", "title": "登入 SleekFlow", "buttonText": "登入", "var-tos": "保持登入状态"}, "signup": {"var-tos": "剔选此方格，即代表我／我们已阅读及同意 SleekFlow 的[服务条款](https://sleekflow.io/terms)及[私隐政策](https://sleekflow.io/privacy)。", "var-required-tos": "必须填写", "auth0-users-validation": "你所输入的资料有误，请检查后再尝试。", "buttonText": "下一步", "custom-script-error-code": "你所输入的资料有误，请检查后再尝试。", "description": "注册以继续使用 SleekFlow", "email-in-use": "此电邮地址已被注册。", "emailPlaceholder": "电邮地址", "invalid-connection": "连结无效。", "invalid-email-format": "电邮地址无效。", "invalid-username": "帐户名称只能包含字母或数字，并需为 ${min} 至 ${max} 个字元内。", "invalid-username-email-not-allowed": "帐户名称不能与电邮地址相同。", "invalid-username-invalid-characters": "你所输入的帐户名称含有无效字元。", "invalid-username-max-length": "帐户名称不能超过 ${max} 个字元。", "invalid-username-min-length": "帐户名称不能少于 ${min} 个字元。", "ip-blocked": "系统侦测到可疑的登入行为，并已阻止及后的登入尝试。请联系管理员。", "ip-signup-blocked": "同一个 IP 地址注册次数过多。", "loginActionLinkText": "登入", "loginActionText": "已有 SleekFlow 帐户？", "no-db-connection": "连结无效。", "no-email": "请输入电邮地址。", "no-password": "必须输入密码。", "no-re-enter-password": "请重新输入密码。", "no-username": "必须输入帐户名称。", "password-contains-user-information": "你所输入的密码含有个人资料。", "password-mismatch": "你所输入的密码不匹配。", "password-policy-not-conformant": "你所输入的密码不安全。", "password-previously-used": "你所输入的密码之前已被使用。", "password-too-common": "你所输入的密码太常见。", "passwordPlaceholder": "密码", "separatorText": "或用以下方式注册", "title": "欢迎使用 SleekFlow", "username-taken": "你所输入的帐户名称已被使用。", "usernamePlaceholder": "帐户名称"}, "reset-password": {"reset-password": {"buttonText": "下一步", "description": "请输入新密码", "passwordPlaceholder": "新密码", "reEnterpasswordPlaceholder": "再次输入密码", "title": "更改密码"}, "reset-password-success": {"buttonText": "返回"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "输入一次性验证码以登入至 ${clientName}", "title": "验证帐户", "description": "开启早前已设置 SleekFlow 双重验证登入的验证器，并输入验证码以继续。", "pickAuthenticatorText": "使用其他登入方式", "buttonText": "下一步", "placeholder": "输入一次性验证码", "rememberMeText": "未来 30 天记住此装置", "usePasswordText": "使用密码", "authenticator-error": "无法核实此验证码，请稍后再尝试", "too-many-failures": "多次登入失败，请稍后片刻后，再尝试登入", "transaction-not-found": "期限已结束，请返回上一次再验证"}, "mfa-otp-enrollment-code": {"pageTitle": "复制此密码完成设置双重验证登入至 ${clientName}", "buttonText": "下一步", "altText": "复制验证码", "copyCodeButtonText": "复制验证码", "description": "复制此密码至你的验证器应用程式，以完成设置双重验证。完成后，请输入显示于验证器应用程式的验证码。 （建议于 App Store 或 Google Play Store 下载 Microsoft Authenticator 或 Google Authenticator 验证器应用程式。）", "pickAuthenticatorText": "使用其他验证方式", "placeholder": "输入一次性验证码", "title": "手动设置双重验证方式", "too-many-failures": "多次登入失败，请稍后片刻后，再尝试登入", "transaction-not-found": "验证期限已结束，请返回上一次再验证"}, "mfa-otp-enrollment-qr": {"pageTitle": "扫描二维码即可用一次性验证码登入至 ${clientName}", "title": "保障帐户安全", "buttonText": "下一步", "description": "用 Authenticator 验证器应用程式扫描以下二维码，并输入一次性验证码以继续。建议于 App Store 或 Google Play Store 下载 Microsoft Authenticator 或 Google Authenticator 验证器应用程式。", "codeEnrollmentText": "手动设置验证器", "pickAuthenticatorText": "使用其他验证方式", "placeholder": "输入一次性验证码", "separatorText": "及", "invalid-code": "输入一次性验证码", "too-many-failures": "多次登入失败，请稍后片刻后，再尝试登入", "transaction-not-found": "验证期限已结束，请返回上一次再验证", "user-already-enrolled": "已设置双重验证方式，请返回上一页以登入 SleekFlow"}}}, "zh-TW": {"signup-password": {"buttonText": "註冊", "title": "創建帳戶", "loginActionLinkText": "登入", "description": "填寫資料以繼續註冊 SleekFlow 帳戶"}, "email-verification": {"email-verification-result": {"buttonText": "返回"}}, "login-id": {"auth0-users-validation": "你所輸入的資料有誤，請檢查後再嘗試。", "authentication-failure": "你所輸入的資料有誤，請檢查後再嘗試。", "buttonText": "繼續", "custom-script-error-code": "你所輸入的資料有誤，請檢查後再嘗試。", "description": "登入以繼續使用 SleekFlow ", "footerLinkText": "註冊", "footerText": "未有 SleekFlow 帳戶？", "forgotPasswordText": "忘記密碼？", "invalid-connection": "連線無效，請稍後再嘗試。", "invalid-email-format": "電郵地址無效。", "ip-blocked": "系統偵測到可疑的登入行為，並已阻止及後的登入嘗試。請聯繫管理員。", "no-db-connection": "連線無效，請稍後再嘗試。", "no-email": "請輸入電郵地址。", "no-password": "必須填寫密碼。", "no-username": "必須填寫。", "password-breached": "系統偵測到潛在安全風險，為保障你的帳戶安全，系統已阻止這次登入。請重置密碼後再登入。", "passwordPlaceholder": "密碼", "same-user-login": "帳號登錄嘗試次數過多，請稍後再試。", "separatorText": "或用以下方式登入", "title": "歡迎回來", "user-blocked": "你已多次嘗試登入並失敗，帳戶已被封鎖。請聯繫管理員。", "usernamePlaceholder": "電郵地址或帳戶名稱", "wrong-credentials": "用戶名稱或密碼錯誤。", "wrong-email-credentials": "電郵地或密碼錯誤。"}, "login": {"auth0-users-validation": "你所輸入的資料有誤，請檢查後再嘗試。", "authentication-failure": "你所輸入的資料有誤，請檢查後再嘗試。", "buttonText": "登入", "custom-script-error-code": "你所輸入的資料有誤，請檢查後再嘗試。", "description": "登入以繼續使用 SleekFlow ", "footerLinkText": "立即註冊", "footerText": "未有 SleekFlow 帳戶？", "forgotPasswordText": "忘記密碼？", "invalid-connection": "連線無效，請稍後再嘗試。", "invalid-email-format": "電郵地址無效。", "ip-blocked": "系統偵測到可疑的登入行為，並已阻止及後的登入嘗試。請聯繫管理員。", "no-db-connection": "連線無效，請稍後再嘗試。", "no-email": "請輸入電郵地址。", "no-password": "必須填寫密碼。", "no-username": "必須填寫。", "password-breached": "系統偵測到潛在安全風險，為保障你的帳戶安全，系統已阻止這次登入。請重置密碼後再登入。", "passwordPlaceholder": "密碼", "same-user-login": "帳號登錄嘗試次數過多，請稍後再試。", "separatorText": "或用以下方式登入", "title": "歡迎回來", "user-blocked": "你已多次嘗試登入並失敗，帳戶已被封鎖。請聯繫管理員。", "usernamePlaceholder": "電郵地址或帳戶名稱", "wrong-credentials": "用戶名稱或密碼錯誤。", "wrong-email-credentials": "電郵地或密碼錯誤。"}, "signup-id": {"var-tos": "剔選此方格，即代表我／我們已閱讀及同意 SleekFlow 的[服務條款](https://sleekflow.io/terms)及[私隱政策](https://sleekflow.io/privacy)。", "var-required-tos": "必須填寫", "loginActionLinkText": "登入", "buttonText": "註冊", "title": "創建帳戶", "description": "註冊 SleekFlow 帳戶以繼續下一步"}, "login-password": {"description": "填寫資料以繼續下一步", "title": "登入以繼續前往 SleekFlow ", "buttonText": "登入", "var-tos": "保持登入狀態"}, "signup": {"var-tos": "剔選此方格，即代表我／我們已閱讀及同意 SleekFlow 的[服務條款](https://sleekflow.io/terms)及[私隱政策](https://sleekflow.io/privacy)。", "var-required-tos": "必須填寫", "auth0-users-validation": "你所輸入的資料有誤，請檢查後再嘗試。", "buttonText": "下一步", "custom-script-error-code": "你所輸入的資料有誤，請檢查後再嘗試。", "description": "註冊以繼續使用 SleekFlow", "email-in-use": "此電郵地址已被註冊。", "emailPlaceholder": "電郵地址", "invalid-connection": "連結無效。", "invalid-email-format": "電郵地址無效。", "invalid-username": "帳戶名稱只能包含字母或數字，並需為 ${min} 至 ${max} 個字元內。", "invalid-username-email-not-allowed": "帳戶名稱不能與電郵地址相同。", "invalid-username-invalid-characters": "你所輸入的帳戶名稱含有無效字元。", "invalid-username-max-length": "帳戶名稱不能超過 ${max} 個字元。", "invalid-username-min-length": "帳戶名稱不能少於 ${min} 個字元。", "ip-blocked": "系統偵測到可疑的登入行為，並已阻止及後的登入嘗試。請聯繫管理員。", "ip-signup-blocked": "同一個 IP 地址註冊次數過多。", "loginActionLinkText": "立即登入", "loginActionText": "已有 SleekFlow 帳戶？", "no-db-connection": "連結無效。", "no-email": "請輸入電郵地址。", "no-password": "必須輸入密碼。", "no-re-enter-password": "請重新輸入密碼。", "no-username": "必須輸入帳戶名稱。", "password-contains-user-information": "你所輸入的密碼含有個人資料。", "password-mismatch": "你所輸入的密碼不匹配。", "password-policy-not-conformant": "你所輸入的密碼不安全。", "password-previously-used": "你所輸入的密碼之前已被使用。", "password-too-common": "你所輸入的密碼太常見。", "passwordPlaceholder": "密碼", "separatorText": "或用以下方式註冊", "title": "歡迎使用 SleekFlow", "username-taken": "你所輸入的帳戶名稱已被使用。", "usernamePlaceholder": "帳戶名稱"}, "reset-password": {"reset-password": {"auth0-users-expired-ticket": "此連結已逾期。", "auth0-users-used-ticket": "此連結已被使用。", "auth0-users-validation": "你所輸入的資料有誤，請檢查後再嘗試。", "buttonText": "下一步", "custom-script-error-code": "你所輸入的資料有誤，請檢查後再嘗試。", "description": "請輸入新密碼", "no-re-enter-password": "請重新輸入密碼。", "passwordPlaceholder": "新密碼", "passwordSecurityText": "你所輸入的密碼必須包含：", "reEnterpasswordPlaceholder": "再次輸入密碼", "title": "更改密碼"}, "reset-password-success": {"buttonText": "返回"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "輸入一次性驗證碼以登入至 ${clientName}", "title": "驗證帳戶", "description": "開啟早前已設置 SleekFlow 雙重驗證登入的驗證器，並輸入驗證碼以繼續。", "buttonText": "下一步", "pickAuthenticatorText": "使用其他登入方式", "placeholder": "輸入一次性驗證碼", "rememberMeText": "未來 30 天記住此裝置", "authenticator-error": "無法核實此驗證碼，請稍後再嘗試", "too-many-failures": "多次登入失敗，請稍後片刻後，再嘗試登入", "transaction-not-found": "期限已結束，請返回上一次再驗證"}, "mfa-otp-enrollment-code": {"pageTitle": "複製此密碼完成設置雙重驗證登入至 ${clientName}", "buttonText": "下一步", "altText": "複製驗證碼", "copyCodeButtonText": "複製驗證碼", "description": "複製此密碼至你的驗證器應用程式，以完成設置雙重驗證。完成後，請輸入顯示於驗證器應用程式的驗證碼。  （建議於 App Store 或 Google Play Store 下載 Microsoft Authenticator 或 Google Authenticator 驗證器應用程式。）", "pickAuthenticatorText": "使用其他驗證方式", "placeholder": "輸入一次性驗證碼", "title": "手動設置雙重驗證方式", "too-many-failures": "多次登入失敗，請稍後片刻後，再嘗試登入", "transaction-not-found": "驗證期限已結束，請返回上一次再驗證"}, "mfa-otp-enrollment-qr": {"pageTitle": "掃描二維碼即可用一次性驗證碼登入至 ${clientName}", "title": "保障帳戶安全", "buttonText": "下一步", "description": "用 Authenticator 驗證器應用程式掃描以下二維碼，並輸入一次性驗證碼以繼續。建議於 App Store 或 Google Play Store 下載 Microsoft Authenticator 或 Google Authenticator 驗證器應用程式。", "codeEnrollmentText": "手動設置驗證器", "pickAuthenticatorText": "使用其他驗證方式", "placeholder": "輸入一次性驗證碼", "separatorText": "及", "invalid-code": "輸入的一次性驗證碼無效", "too-many-failures": "多次登入失敗，請稍後片刻後，再嘗試登入", "transaction-not-found": "驗證期限已結束，請返回上一次再驗證", "user-already-enrolled": "已設置雙重驗證方式，請返回上一頁以登入 SleekFlow"}}}, "id": {"signup-password": {"buttonText": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> akun <PERSON>", "loginActionLinkText": "<PERSON><PERSON><PERSON>", "description": "Isi detail untuk daftar akun Sleek<PERSON>low"}, "email-verification": {"email-verification-result": {"buttonText": "Back"}}, "login-id": {"auth0-users-validation": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "authentication-failure": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "buttonText": "Lanjutkan", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Masuk untuk melanjutkan", "footerLinkText": "Registre-se", "footerText": "Belum punya akun?", "forgotPasswordText": "<PERSON><PERSON> password?", "invalid-connection": "<PERSON><PERSON><PERSON><PERSON> gagal. <PERSON><PERSON>an coba lagi nanti.", "invalid-email-format": "<PERSON><PERSON> sa<PERSON>.", "ip-blocked": "<PERSON>mi telah mendeteksi perilaku masuk yang mencurigakan dan upaya lebih lanjut akan diblokir. Silakan hubungi admin.", "no-db-connection": "<PERSON><PERSON><PERSON><PERSON> gagal. <PERSON><PERSON>an coba lagi nanti.", "no-email": "Ma<PERSON>kka<PERSON> email.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "Kami telah mendeteksi potensi masalah keamanan pada akun ini. Untuk melindungi akun <PERSON>, kami telah mencegah proses login. <PERSON>p atur password untuk melanjutkan.", "passwordPlaceholder": "Password", "same-user-login": "Percobaan login terlalu banyak. <PERSON>lakan coba lagi nanti.", "separatorText": "atau", "title": "Selamat datang", "user-blocked": "<PERSON><PERSON><PERSON> di<PERSON>r karena terlalu banyak melakukan login. <PERSON>lakan kontak admin.", "usernamePlaceholder": "Email atau username", "wrong-credentials": "Wrong username or password.", "wrong-email-credentials": "Wrong email or password."}, "login": {"auth0-users-validation": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "authentication-failure": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "custom-script-error-code": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "invalid-connection": "<PERSON><PERSON><PERSON><PERSON> gagal. <PERSON><PERSON>an coba lagi nanti.", "invalid-email-format": "<PERSON><PERSON> sa<PERSON>.", "ip-blocked": "<PERSON>mi telah mendeteksi perilaku masuk yang mencurigakan dan upaya lebih lanjut akan diblokir. Silakan hubungi admin.", "no-db-connection": "<PERSON><PERSON><PERSON><PERSON> gagal. <PERSON><PERSON>an coba lagi nanti.", "no-email": "Ma<PERSON>kka<PERSON> email.", "no-password": "Password wajib diisi.", "no-username": "<PERSON><PERSON><PERSON> wa<PERSON>.", "password-breached": "Kami telah mendeteksi potensi masalah keamanan pada akun ini. Untuk melindungi akun <PERSON>, kami telah mencegah proses login. <PERSON>p atur password untuk melanjutkan.", "same-user-login": "Percobaan login terlalu banyak. <PERSON>lakan coba lagi nanti.", "user-blocked": "<PERSON><PERSON><PERSON> di<PERSON>r karena terlalu banyak melakukan login. <PERSON>lakan kontak admin.", "wrong-credentials": "<PERSON><PERSON><PERSON> atau password salah.", "wrong-email-credentials": "Email atau password salah.", "description": "Masuk untuk melanjutkan", "usernamePlaceholder": "Email atau username", "passwordPlaceholder": "Password", "forgotPasswordText": "<PERSON><PERSON> password?", "buttonText": "<PERSON><PERSON><PERSON>"}, "signup-id": {"var-tos": "Dengan mencentang kotak ini, saya/kami mengonfirmasi bahwa saya/kami telah membaca serta menyetujui [Ketentuan Layanan](https://sleekflow.io/terms) dan [<PERSON><PERSON><PERSON><PERSON>](https://sleekflow.io/privacy) SleekFlow.", "var-required-tos": "<PERSON><PERSON><PERSON>", "loginActionLinkText": "<PERSON><PERSON><PERSON>", "buttonText": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> akun <PERSON>", "description": "Daftar akun <PERSON> untuk melanjutkan"}, "login-password": {"description": "Isi detail untuk melanjutkan ke SleekFlow", "title": "Masuk untuk melanjutkan", "buttonText": "<PERSON><PERSON><PERSON>", "var-tos": "Tetap masuk"}, "signup": {"var-tos": "Dengan mencentang kotak ini, saya/kami mengonfirmasi bahwa saya/kami telah membaca serta menyetujui [Ketentuan Layanan](https://sleekflow.io/terms) dan [<PERSON><PERSON><PERSON><PERSON>](https://sleekflow.io/privacy) SleekFlow.", "var-required-tos": "<PERSON><PERSON><PERSON>", "email-in-use": "Email ini sudah terdaftar", "invalid-email-format": "<PERSON><PERSON>", "password-too-common": "Password terlalu lemah", "password-previously-used": "Password terlalu umum", "password-mismatch": "Password tidak sesuai.", "password-contains-user-information": "Password mengandung informasi user.", "invalid-username": "Username hanya bisa mengandung karakter alfanumerik atau:${characters}'. Username harus memiliki ${min} dan ${max} karakter.", "invalid-username-max-length": "Username tidak boleh lebih dari ${max} karakter.", "invalid-username-min-length": "Username setidaknya memiliki ${min} karakter.", "invalid-username-invalid-characters": "<PERSON>rname menggunakan karakter yang tidak diizinkan.", "invalid-username-email-not-allowed": "Username tidak boleh menggunakan format email.", "username-taken": "<PERSON><PERSON>me sudah digunakan.", "custom-script-error-code": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON> coba lagi nanti.", "auth0-users-validation": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "loginActionLinkText": "<PERSON><PERSON><PERSON>", "loginActionText": "Sudah punya akun?", "invalid-connection": "<PERSON><PERSON><PERSON><PERSON> gagal.", "ip-blocked": "<PERSON>mi telah mendeteksi perilaku masuk yang mencurigakan dan upaya lebih lanjut akan diblokir. Silakan hubungi admin.", "ip-signup-blocked": "Terlalu banyak pendaftaran dari IP address yang sama.", "no-db-connection": "<PERSON><PERSON><PERSON><PERSON> gagal.", "no-email": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> email.", "no-password": "Password wajib diisi.", "no-re-enter-password": "<PERSON><PERSON><PERSON><PERSON> kembali password.", "no-username": "<PERSON><PERSON><PERSON> wa<PERSON>."}, "reset-password": {"reset-password": {"auth0-users-expired-ticket": "Sesi ini sudah kedalu<PERSON>.", "auth0-users-validation": "Informasi yang <PERSON>a masukkan salah. Silakan cek dan coba lagi\n.", "custom-script-error-code": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON> coba lagi nanti.", "no-re-enter-password": "<PERSON><PERSON><PERSON> masukkan kembali password.", "passwordPlaceholder": "Password harus mengandung:"}, "reset-password-success": {"buttonText": "Back"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "Ma<PERSON>kkan kode sandi satu kali untuk masuk ${clientName}", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> akun <PERSON>", "description": "Buka aplikasi autentikator yang sebelumnya telah ditautkan ke akun SleekFlow Anda, kem<PERSON>an masukkan kode sandi untuk melanjutkan.", "buttonText": "Lanjutkan", "placeholder": "<PERSON><PERSON><PERSON>n kode sandi satu kali Anda", "usePasswordText": "<PERSON><PERSON><PERSON> password", "authenticator-error": "<PERSON>mi tidak bisa memverifikasi kode. Silakan coba lagi nanti.", "too-many-failures": "Terlalu banyak percobaan yang gagal. <PERSON>ng<PERSON> beberapa menit sebelum mencoba lagi.", "transaction-not-found": "<PERSON><PERSON> be<PERSON>. <PERSON><PERSON><PERSON> kembali ke halaman sebelumnya untuk memulai lagi."}, "mfa-otp-enrollment-code": {"pageTitle": "Salin kode untuk masuk menggunakan sandi satu kali ${clientName}", "buttonText": "Lanjutkan", "altText": "Amankan kode untuk disalin", "description": "<PERSON>in kode ini ke aplikasi autentikator untuk menyelesaikan pengaturan 2FA. Setelah pengaturan 2FA selesai, masukkan sandi satu kali dari aplikasi autentikator.  (<PERSON><PERSON>n untuk mengunduh Microsoft Authenticator atau Google Authenticator dari App Store atau Google Play Store.)", "placeholder": "<PERSON><PERSON><PERSON><PERSON> sandi satu kali", "title": "Atur 2FA secara manual", "too-many-failures": "Terlalu banyak percobaan yang gagal. <PERSON>ng<PERSON> beberapa menit sebelum mencoba lagi.", "transaction-not-found": "<PERSON><PERSON> be<PERSON>. <PERSON><PERSON><PERSON> kembali ke halaman sebelumnya untuk memulai lagi."}, "mfa-otp-enrollment-qr": {"pageTitle": "Pindai kode untuk masuk menggunakan kode sandi satu kali ${clientName}", "buttonText": "Lanjutkan", "description": "Pindai kode QR di bawah dengan aplikasi autentikator dan masukkan kode sandi satu kali untuk melanjutkan. <PERSON><PERSON> penggunaan Microsoft Authenticator atau Google Authenticator yang bisa Anda download di App Store dan Google Play Store.", "codeEnrollmentText": "Lakukan secara manual?", "placeholder": "<PERSON><PERSON><PERSON>n kode sandi satu kali Anda", "separatorText": "DAN", "invalid-code": "Kode sandi yang Anda masukkan salah", "too-many-failures": "Terlalu banyak percobaan yang gagal. <PERSON>ng<PERSON> beberapa menit sebelum mencoba lagi.", "transaction-not-found": "<PERSON><PERSON> be<PERSON>. <PERSON><PERSON><PERSON> kembali ke halaman sebelumnya untuk memulai lagi.", "user-already-enrolled": "<PERSON>a telah mendaftar 2FA. <PERSON><PERSON><PERSON> kembali ke halaman sebelumnya untuk masuk."}}}, "pt": {"signup-password": {"buttonText": "Registre-se", "title": "Crie sua conta", "loginActionLinkText": "<PERSON><PERSON><PERSON> login", "description": "Preen<PERSON> os detalhes criar sua conta do SleekFlow"}, "email-verification": {"email-verification-result": {"buttonText": "Back"}}, "login-id": {"auth0-users-validation": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "authentication-failure": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "buttonText": "Continue", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Faça login para continuar no SleekFlow ", "footerLinkText": "Registre-se", "footerText": "Não tem uma conta?", "forgotPasswordText": "<PERSON><PERSON><PERSON> a senha?", "invalid-connection": "Conexão inválida. Por favor, tente de novo mais tarde.", "invalid-email-format": "<PERSON>ail <PERSON>.", "ip-blocked": "Detectamos um comportamento suspeito e novas tentativas de login serão bloqueadas. Por favor, entre em contato com a administração.", "no-db-connection": "Conexão inválida. Por favor, tente de novo mais tarde.", "no-email": "Por favor, informe um endereço de email.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Password", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "ou", "title": "Olá novamente!", "user-blocked": "Devido a diversas tentativas sucessivas de login, sua conta foi bloqueada. Por favor, entre em contato com a administração. ", "usernamePlaceholder": "Email ou nome de usuário", "wrong-credentials": "Wrong username or password.", "wrong-email-credentials": "Wrong email or password."}, "login": {"wrong-credentials": "<PERSON><PERSON> ou nome de usuário inv<PERSON>.", "invalid-email-format": "<PERSON>ail <PERSON>.", "wrong-email-credentials": "<PERSON><PERSON> ou email inválido.", "custom-script-error-code": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "auth0-users-validation": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "authentication-failure": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "invalid-connection": "Conexão inválida. Por favor, tente de novo mais tarde.", "ip-blocked": "Detectamos um comportamento suspeito e novas tentativas de login serão bloqueadas. Por favor, entre em contato com a administração.", "no-db-connection": "Conexão inválida. Por favor, tente de novo mais tarde.", "password-breached": "Detectamos um possível problema de segurança relacionado a essa conta. Para protegê-la, impedimos esse login. Por favor, redefina sua senha para continuar.", "user-blocked": "Devido a diversas tentativas sucessivas de login, sua conta foi bloqueada. Por favor, entre em contato com a administração. ", "same-user-login": "Tentativas excessivas de login para o mesmo usuário. Por favor, tente de novo mais tarde.", "no-email": "Por favor, informe um endereço de email.", "no-password": "<PERSON><PERSON>.", "no-username": "Nome de usuário obrigatório."}, "signup-id": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "Obrigatório", "loginActionLinkText": "<PERSON><PERSON><PERSON> login", "buttonText": "Registre-se", "title": "Crie sua conta", "description": "Crie sua conta do SleekFlow para continuar"}, "login-password": {"description": "Preencha os detalhes para continuar no SleekFlow", "title": "Faça login para continuar", "buttonText": "<PERSON><PERSON><PERSON> login", "var-tos": "Mantenha-me conectado"}, "signup": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "Obrigatório", "password-mismatch": "A mesma senha deve ser informada nos dois campos.", "password-contains-user-information": "A senha contém dados do usuário.", "invalid-username": "O nome de usuário só pode conter caracteres alfanuméricos ou: ‘${characters}’. O nome de usuário deve ter entre ${min} e ${max} caracteres.", "invalid-username-max-length": "O nome de usuário não pode ultrapassar ${max} caracteres.", "invalid-username-min-length": "O nome de usuário deve ter no mínimo ${min} caracteres.", "invalid-username-invalid-characters": "O nome de usuário possui caracteres inválidos.", "username-taken": "Este nome de usuário já está sendo utilizado.", "custom-script-error-code": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "auth0-users-validation": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "invalid-connection": "Conexão inválida.", "ip-blocked": "Detectamos um comportamento suspeito e novas tentativas de login serão bloqueadas. Por favor, entre em contato com a administração.", "loginActionLinkText": "Entrar", "loginActionText": "Já tem uma conta?", "ip-signup-blocked": "Houve muitas tentativas de cadastro do mesmo endereço IP.", "no-db-connection": "Conexão inválida.", "no-email": "Por favor, informe um endereço de email.", "no-password": "<PERSON><PERSON>.", "no-re-enter-password": "Por favor, digite a senha novamente.", "no-username": "Nome de usuário obrigatório."}, "reset-password": {"reset-password": {"auth0-users-expired-ticket": "A sessão expirou.", "auth0-users-used-ticket": "Este ticket já foi usado.", "auth0-users-validation": "Os dados informados estão incorretos. Por favor, verifique e tente novamente.", "custom-script-error-code": "Ocorreu um erro. Por favor, tente de novo mais tarde.", "no-re-enter-password": "Por favor, digite a senha novamente.", "passwordSecurityText": "A senha deve conter:"}, "reset-password-success": {"buttonText": "Back"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "Informe seu código de verificação para entrar no ${clientName}", "title": "Verifique sua conta", "description": "Abra o app de autenticação que você vinculou à conta do SleekFlow e informe seu código de uso único.", "pickAuthenticatorText": "Usar outro método", "placeholder": "Informe seu código de verificação", "rememberMeText": "Lembrar desse dispositivo por 30 dias", "usePasswordText": "<PERSON><PERSON> se<PERSON>a", "authenticator-error": "Não foi possível verificar o código. Tente de novo mais tarde.", "too-many-failures": "Houve muitas tentativas com erro. Aguarde alguns minutos e tente novamente.", "transaction-not-found": "A sessão expirou. Por favor, volte à página anterior e recomece o processo."}, "mfa-otp-enrollment-code": {"pageTitle": "Copie o código para entrar no ${clientName} usando a verificação em duas etapas", "altText": "Código de segurança a ser copiado", "description": "Insira esse código no seu app de autenticação para configurar a verificação em duas etapas (2FA). Em seguida, informe o código de verificação gerado pelo aplicativo.   Recomendamos o uso do Microsoft Authenticator ou Google Authenticator (disponíveis tanto na App Store quanto no Google Play Store).", "pickAuthenticatorText": "Usar outro método", "placeholder": "Insira o código de verificação ", "title": "Configurar a verificação em duas etapas (2FA) manualmente", "too-many-failures": "Houve muitas tentativas com erro. Aguarde alguns minutos e tente novamente.", "transaction-not-found": "A sessão expirou. Por favor, volte à página anterior e recomece o processo."}, "mfa-otp-enrollment-qr": {"pageTitle": "Escaneie o código QR para entrar na sua conta do ${clientName} usando um código de verificação único ", "description": "Escaneie o código QR abaixo com um app de autenticação e faça a verificação em duas etapas. Recomendamos o uso do Microsoft Authenticator ou Google Authenticator (disponíveis tanto na App Store quanto no Google Play Store).", "codeEnrollmentText": "Configurar manualmente", "pickAuthenticatorText": "Usar outro método", "placeholder": "Insira o código de verificação", "separatorText": "E", "invalid-code": "O código que você informou é inválido", "too-many-failures": "Houve muitas tentativas com erro. Aguarde alguns minutos e tente novamente.", "transaction-not-found": "A sessão expirou. Por favor, volte à página anterior e recomece o processo.", "user-already-enrolled": "Você já se cadastrou na verificação em duas etapas (2FA). Volte à página anterior para entrar na conta."}}}, "de": {"signup-password": {"buttonText": "Anmelden", "title": "<PERSON><PERSON><PERSON> dei<PERSON> Account", "loginActionLinkText": "Einloggen", "email-in-use": "We couldn’t complete your registration. Please try again or contact us for support.", "email-taken": "We couldn’t complete your registration. Please try again or contact us for support.", "description": "<PERSON><PERSON><PERSON> Sie die Details ein, um sich für Ihr SleekFlow-Konto anzumelden"}, "email-verification": {"email-verification-result": {"buttonText": "Back"}}, "login-id": {"auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "authentication-failure": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Weitermachen", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Melden Sie sich an, um mit SleekFlow fortzufahren", "footerLinkText": "Anmelden", "footerText": "Sie haben noch kein Konto?", "forgotPasswordText": "Forgot password?", "invalid-connection": "Invalid connection. Please try again later.", "invalid-email-format": "Invalid email.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "no-db-connection": "Invalid connection. Please try again later.", "no-email": "Please enter an email address.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Password", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "oder", "title": "Willkommen zurück", "user-blocked": "Your account has been blocked after multiple consecutive login attempts. Please contact the administrator.", "usernamePlaceholder": "E-Mail Adresse oder Benutzername", "wrong-credentials": "Wrong username or password.", "wrong-email-credentials": "Wrong email or password."}, "login": {"auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "authentication-failure": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Sign in", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Sign in to continue", "footerLinkText": "Sign up", "footerText": "Don't have an account?", "forgotPasswordText": "Forgot password?", "invalid-connection": "Invalid connection. Please try again later.", "invalid-email-format": "Invalid email.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "no-db-connection": "Invalid connection. Please try again later.", "no-email": "Please enter an email address.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Password", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "OR", "title": "Welcome back", "user-blocked": "Your account has been blocked after multiple consecutive login attempts. Please contact the administrator.", "usernamePlaceholder": "Email or username", "wrong-credentials": "Incorrect username or password.", "wrong-email-credentials": "Wrong email or password."}, "login-password": {"description": "<PERSON>üllen Sie die Details aus, um zu SleekFlow zu gelangen", "title": "Melden Sie sich an, um fortzufahren", "buttonText": "Einloggen", "var-tos": "Ang<PERSON><PERSON><PERSON> bleiben"}, "signup-id": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "<PERSON>s ist erford<PERSON>lich", "loginActionLinkText": "Einloggen", "buttonText": "Anmelden", "title": "<PERSON><PERSON><PERSON> dei<PERSON> Account", "description": "<PERSON><PERSON><PERSON> Sie die Details ein, um sich für Ihr SleekFlow-Konto anzumelden"}, "signup": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "This is required", "auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Continue", "custom-script-error-code": "The information you have filled in is incorrect. Please check and try again.", "description": "Sign up to continue", "email-in-use": "We couldn’t complete your registration. Please try again or contact us for support.", "emailPlaceholder": "Email", "invalid-connection": "Invalid connection.", "invalid-email-format": "<PERSON><PERSON> is invalid.", "invalid-username": "Username can only contain alphanumeric characters or: ‘${characters}’. Username should have between ${min} and ${max} characters.", "invalid-username-email-not-allowed": "The username cannot be an email.", "invalid-username-invalid-characters": "The username has invalid characters.", "invalid-username-max-length": "The username must not be longer than ${max} characters.", "invalid-username-min-length": "The username must have at least ${min} characters.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "ip-signup-blocked": "Too many signups from the same IP address.", "loginActionLinkText": "Sign in", "loginActionText": "Already have an account?", "no-db-connection": "Invalid connection.", "no-email": "Please enter an email address.", "no-password": "Password is required.", "no-re-enter-password": "Please re-enter your password.", "no-username": "Username is required.", "password-contains-user-information": "Password contains user information.", "password-mismatch": "Passwords don’t match.", "password-policy-not-conformant": "The password is too weak.", "password-previously-used": "Password has previously been used.", "password-too-common": "The password is too common.", "passwordPlaceholder": "Password", "separatorText": "Or", "title": "Welcome to SleekFlow", "username-taken": "The username provided is in use already.", "usernamePlaceholder": "Username"}, "reset-password": {"reset-password": {"auth0-users-expired-ticket": "This session was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "buttonText": "Continue", "custom-script-error-code": "Something went wrong, please try again later.", "description": "Enter a new password below.", "hidePasswordText": "Hide password", "logoAltText": "${companyName}", "no-re-enter-password": "New password confirmation is missing", "pageTitle": "Reset your password | ${clientName}", "password-contains-user-information": "Password contains user information", "password-mismatch": "Passwords don't match", "passwordPlaceholder": "New password", "passwordSecurityText": "Your password must contain:", "reEnterpasswordPlaceholder": "Re-enter password", "showPasswordText": "Show password", "title": "Change Your Password"}, "reset-password-email": {"emailDescription": "Please check the email address ${email} for instructions to reset your password.", "pageTitle": "Check your email | ${clientName}", "resendLinkText": "Resend email", "title": "Check Your Email", "usernameDescription": "Please check the email address associated with the username ${email} for instructions to reset your password."}, "reset-password-error": {"auth0-users-expired-ticket": "This ticket was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "backToLoginLinkText": "Back to ${clientName}", "custom-script-error-code": "Something went wrong, please try again later.", "descriptionExpired": "To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "descriptionGeneric": "To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "descriptionUsed": "This link has already been used. To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "eventTitleExpired": "Link Expired", "eventTitleGeneric": "Invalid Link", "eventTitleUsed": "Invalid Link", "pageTitle": "Password reset error | ${clientName}", "reset-password-error": "We had a problem sending the email, please try again later."}, "reset-password-mfa-email-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "backText": "Go Back", "buttonText": "Continue", "description": "We've sent an email with your code to", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "mfa-email-challenge-authenticator-error": "We couldn't send the email. Please try again later.", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive an email?", "title": "Verify Your Identity", "too-many-email": "You have exceeded the amount of emails. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-otp-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "Check your preferred one-time password application for a code.", "logoAltText": "${companyName}", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your one-time code", "rememberMeText": "Remember this device for 30 days", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "usePasswordText": "Use password"}, "reset-password-mfa-phone-challenge": {"changePhoneText": "Choose another phone number.", "chooseMessageTypeText": "How do you want to receive the code?", "continueButtonText": "Continue", "description": "We will send a 6-digit code to the following phone number:", "invalid-phone": "It seems that your phone number is not valid. Please check and retry.", "invalid-phone-format": "Phone number can only include digits.", "logoAltText": "${companyName}", "no-phone": "Please enter a phone number", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your phone number", "send-sms-failed": "There was a problem sending the SMS", "send-voice-failed": "There was a problem making the voice call", "smsButtonText": "Text message", "title": "Verify Your Identity", "too-many-sms": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "too-many-voice": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "voiceButtonText": "Voice call"}, "reset-password-mfa-push-challenge-push": {"buttonText": "I've responded on my device", "challenge-transaction-pending": "You must accept the notification via the ${appName} app on your mobile device.", "description": "We’ve sent a notification to the following device via the ${appName} app:", "enterOtpCode": "Manually Enter Code", "logoAltText": "${companyName}", "mfa-push-challenge-authenticator-error": "We couldn't send the notification. Please try again later.", "mfa-push-verify-authenticator-error": "We couldn't verify the enrollment. Please try again later.", "mfa-push-verify-transaction-pending": "We have not received a confirmation, please try scanning the code again.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "polling-interval-exceeded": "We have not received a confirmation, please slow down.", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive a notification?", "separatorText": "OR", "title": "Verify Your Identity", "too-many-push": "We have received too many notification requests. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "transaction-rejected": "Notification rejected"}, "reset-password-mfa-recovery-code-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "Enter the recovery code you were provided during your initial enrollment.", "invalid-code": "The code you entered is invalid", "invalid-code-format": "Recovery code must have 24 alphanumeric characters", "logoAltText": "${companyName}", "no-confirmation": "Please confirm you have recorded the code", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your recovery code", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-sms-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "We've sent a text message to:", "editLinkScreenReadableText": "Edit phone number", "editText": "Edit", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the 6-digit code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive a code?", "resendVoiceActionSeparatorTextBefore": "or", "resendVoiceActionText": "get a call", "send-sms-failed": "There was a problem sending the SMS", "sms-authenticator-error": "We couldn't send the SMS. Please try again later.", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "too-many-sms": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-voice-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "changePhoneText": "Choose another phone number.", "description": "We've sent a 6-digit code via voice phone call to the following phone number:", "editLinkScreenReadableText": "Edit phone number", "editText": "Edit", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the 6-digit code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Call again", "resendSmsActionSeparatorTextBefore": "or", "resendSmsActionText": "send a text", "resendText": "Didn't receive a call?", "send-voice-failed": "There was a problem making the voice call", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "too-many-voice": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "voice-authenticator-error": "We couldn't make the voice call. Please try again later."}, "reset-password-mfa-webauthn-platform-challenge": {"awaitingConfirmation": "Awaiting device confirmation", "continueButtonText": "Continue", "description": "Press the button below and follow your browser's steps to log in.", "logoAltText": "${companyName}", "pickAuthenticatorText": "Try another method", "rememberMeText": "Remember this device for 30 days", "title": "Use fingerprint or face recognition to reset password | ${clientName}", "too-many-webauthn-challenge-attempts-error": "Too many failed authentication attempts. Please try again later.", "usePasswordText": "Use password", "webauthn-platform-challenge-error": "We could not start the device verification. Please try again later."}, "reset-password-mfa-webauthn-roaming-challenge": {"awaitingConfirmation": "Awaiting Security Key", "continueButtonText": "Use security key", "description": "Make sure your Security Key is nearby. Once you continue, you will be prompted to use it.", "logoAltText": "${companyName}", "pageTitle": "Use your security key to reset password | ${clientName}", "pickAuthenticatorText": "Try another method", "rememberMeText": "Remember this device for 30 days", "title": "Verify Your Identity", "too-many-webauthn-challenge-attempts-error": "Too many failed authentication attempts. Please try again later.", "webauthn-challenge-error": "We could not start the security key verification. Please try again later."}, "reset-password-request": {"auth0-users-expired-ticket": "This ticket was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "backToLoginLinkText": "Back", "buttonText": "Send link", "custom-script-error-code": "Something went wrong, please try again later.", "descriptionEmail": "Enter your email address, and we’ll send you a link to reset your password.", "descriptionUsername": "Enter your email address, and we’ll send you a link to reset your password.", "invalid-email-format": "Email is not valid.", "logoAltText": "${companyName}", "no-email": "Please enter an email address", "no-username": "Email is required", "pageTitle": "Reset your password | ${clientName}", "placeholderEmail": "Email address", "placeholderUsername": "EMAIL", "reset-password-error": "We had a problem sending the email, please try again later.", "title": "Forgot your password?", "too-many-email": "You have exceeded the amount of emails. Wait a few minutes and try again.", "too-many-requests": "You have exceeded the amount of emails. Wait a few minutes and try again."}, "reset-password-success": {"buttonText": "Back", "description": "Your password has been changed successfully.", "eventTitle": "Password Changed!", "pageTitle": "Password reset successful | ${clientName}"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "Enter your one-time passcode to sign in ${clientName}", "title": "Verify your account", "description": "Open the authenticator app you have previously linked your SleekFlow account to, and enter the passcode to continue. ", "placeholder": "Enter your one-time passcode", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. "}, "mfa-otp-enrollment-code": {"pageTitle": "Copy the code to sign in using a one-time passcode ${clientName}", "description": "Copy this code to your authenticator app to complete setting up 2FA. Once 2FA is set up, enter the one-time passcode from the authenticator app.   (We recommend downloading  Microsoft Authenticator or Google Authenticator from App Store or Google Play Store.)", "placeholder": "Enter your one-time passcode", "title": "Set up 2FA manually", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. "}, "mfa-otp-enrollment-qr": {"description": "Scan the QR code below with an authenticator app and enter the one-time passcode to continue. We recommend downloading  Microsoft Authenticator or Google Authenticator from App Store or Google Play Store. ", "codeEnrollmentText": "Set up manually instead", "pageTitle": "Scan the code to sign in using a one-time passcode ${clientName}", "title": "Secure your account", "placeholder": "Enter your one-time passcode", "separatorText": "AND", "invalid-code": "The passcode you entered is invalid", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. ", "user-already-enrolled": "You have already enrolled on 2FA. Please go back to the previous page to sign in."}}}, "it": {"signup-password": {"buttonText": "Iscrizione", "title": "crea il tuo account", "loginActionLinkText": "Accedi", "email-in-use": "We couldn’t complete your registration. Please try again or contact us for support.", "email-taken": "We couldn’t complete your registration. Please try again or contact us for support.", "description": "Inserisci i dettagli per registrarti per il tuo account SleekFlow"}, "email-verification": {"email-verification-result": {"buttonText": "Back"}}, "login-id": {"auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "authentication-failure": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Continua", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Accedi per continuare su SleekFlow", "footerLinkText": "Iscrizione", "footerText": "Non hai un account?", "forgotPasswordText": "Forgot password?", "invalid-connection": "Invalid connection. Please try again later.", "invalid-email-format": "Invalid email.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "no-db-connection": "Invalid connection. Please try again later.", "no-email": "Please enter an email address.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Password", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "O", "title": "Bentornato", "user-blocked": "Your account has been blocked after multiple consecutive login attempts. Please contact the administrator.", "usernamePlaceholder": "Email o nome utente", "wrong-credentials": "Wrong username or password.", "wrong-email-credentials": "Wrong email or password."}, "login": {"auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "authentication-failure": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Sign in", "custom-script-error-code": "Something went wrong. Please try again later.", "description": "Accedi per continuare", "footerLinkText": "Iscrizione", "footerText": "Non hai un account?", "forgotPasswordText": "Ha dimenticato la password?", "invalid-connection": "Invalid connection. Please try again later.", "invalid-email-format": "Invalid email.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "no-db-connection": "Invalid connection. Please try again later.", "no-email": "Please enter an email address.", "no-password": "Password is required", "no-username": "Username is required.", "password-breached": "We have detected a potential security issue with this account. To protect your account, we have prevented this login. Please reset your password to proceed.", "passwordPlaceholder": "Parola d'or<PERSON>", "same-user-login": "Too many login attempts for this user. Please try again later.", "separatorText": "OR", "title": "Welcome back", "user-blocked": "Your account has been blocked after multiple consecutive login attempts. Please contact the administrator.", "usernamePlaceholder": "Email o nome utente", "wrong-credentials": "Incorrect username or password.", "wrong-email-credentials": "Wrong email or password."}, "login-password": {"description": "Compila i dettagli per continuare su SleekFlow", "title": "Accedi per continuare", "buttonText": "Accedi", "var-tos": "<PERSON><PERSON><PERSON> con<PERSON>o"}, "signup-id": {"var-tos": "Se<PERSON><PERSON><PERSON><PERSON> questa casella, io/noi confermiamo di aver letto e di accettare i [Termini di servizio](https://sleekflow.io/terms) e l'[Informativa sulla privacy](https://sleekflow.io/privacy) di SleekFlow.", "var-required-tos": "Questo è obbligatorio", "loginActionLinkText": "Accedi", "buttonText": "Iscrizione", "title": "Crea il tuo account", "description": "Registrati per il tuo account SleekFlow per continuare"}, "signup": {"var-tos": "By checking this box, I/We confirm that I/we have read and agree to SleekFlow's [Terms of Service](https://sleekflow.io/terms) and [Privacy Policy](https://sleekflow.io/privacy).", "var-required-tos": "This is required", "auth0-users-validation": "The information you have filled in is incorrect. Please check and try again.", "buttonText": "Continue", "custom-script-error-code": "The information you have filled in is incorrect. Please check and try again.", "description": "Sign up to continue", "email-in-use": "We couldn’t complete your registration. Please try again or contact us for support..", "emailPlaceholder": "Email", "invalid-connection": "Invalid connection.", "invalid-email-format": "<PERSON><PERSON> is invalid.", "invalid-username": "Username can only contain alphanumeric characters or: ‘${characters}’. Username should have between ${min} and ${max} characters.", "invalid-username-email-not-allowed": "The username cannot be an email.", "invalid-username-invalid-characters": "The username has invalid characters.", "invalid-username-max-length": "The username must not be longer than ${max} characters.", "invalid-username-min-length": "The username must have at least ${min} characters.", "ip-blocked": "We have detected suspicious login behavior and further attempts will be blocked. Please contact the administrator.", "ip-signup-blocked": "Too many signups from the same IP address.", "loginActionLinkText": "Sign in", "loginActionText": "Already have an account?", "no-db-connection": "Invalid connection.", "no-email": "Please enter an email address.", "no-password": "Password is required.", "no-re-enter-password": "Please re-enter your password.", "no-username": "Username is required.", "password-contains-user-information": "Password contains user information.", "password-mismatch": "Passwords don’t match.", "password-policy-not-conformant": "The password is too weak.", "password-previously-used": "Password has previously been used.", "password-too-common": "The password is too common.", "passwordPlaceholder": "Password", "separatorText": "Or", "title": "Welcome to SleekFlow", "username-taken": "The username provided is in use already.", "usernamePlaceholder": "Username"}, "reset-password": {"reset-password": {"auth0-users-expired-ticket": "This session was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "buttonText": "Continue", "custom-script-error-code": "Something went wrong, please try again later.", "description": "Enter a new password below.", "hidePasswordText": "Hide password", "logoAltText": "${companyName}", "no-re-enter-password": "New password confirmation is missing", "pageTitle": "Reset your password | ${clientName}", "password-contains-user-information": "Password contains user information", "password-mismatch": "Passwords don't match", "passwordPlaceholder": "New password", "passwordSecurityText": "Your password must contain:", "reEnterpasswordPlaceholder": "Re-enter password", "showPasswordText": "Show password", "title": "Change Your Password"}, "reset-password-email": {"emailDescription": "Please check the email address ${email} for instructions to reset your password.", "pageTitle": "Check your email | ${clientName}", "resendLinkText": "Resend email", "title": "Check Your Email", "usernameDescription": "Please check the email address associated with the username ${email} for instructions to reset your password."}, "reset-password-error": {"auth0-users-expired-ticket": "This ticket was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "backToLoginLinkText": "Back to ${clientName}", "custom-script-error-code": "Something went wrong, please try again later.", "descriptionExpired": "To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "descriptionGeneric": "To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "descriptionUsed": "This link has already been used. To reset your password, return to the login page and select \"Forgot Your Password\" to send a new email.", "eventTitleExpired": "Link Expired", "eventTitleGeneric": "Invalid Link", "eventTitleUsed": "Invalid Link", "pageTitle": "Password reset error | ${clientName}", "reset-password-error": "We had a problem sending the email, please try again later."}, "reset-password-mfa-email-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "backText": "Go Back", "buttonText": "Continue", "description": "We've sent an email with your code to", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "mfa-email-challenge-authenticator-error": "We couldn't send the email. Please try again later.", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive an email?", "title": "Verify Your Identity", "too-many-email": "You have exceeded the amount of emails. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-otp-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "Check your preferred one-time password application for a code.", "logoAltText": "${companyName}", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your one-time code", "rememberMeText": "Remember this device for 30 days", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "usePasswordText": "Use password"}, "reset-password-mfa-phone-challenge": {"changePhoneText": "Choose another phone number.", "chooseMessageTypeText": "How do you want to receive the code?", "continueButtonText": "Continue", "description": "We will send a 6-digit code to the following phone number:", "invalid-phone": "It seems that your phone number is not valid. Please check and retry.", "invalid-phone-format": "Phone number can only include digits.", "logoAltText": "${companyName}", "no-phone": "Please enter a phone number", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your phone number", "send-sms-failed": "There was a problem sending the SMS", "send-voice-failed": "There was a problem making the voice call", "smsButtonText": "Text message", "title": "Verify Your Identity", "too-many-sms": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "too-many-voice": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "voiceButtonText": "Voice call"}, "reset-password-mfa-push-challenge-push": {"buttonText": "I've responded on my device", "challenge-transaction-pending": "You must accept the notification via the ${appName} app on your mobile device.", "description": "We’ve sent a notification to the following device via the ${appName} app:", "enterOtpCode": "Manually Enter Code", "logoAltText": "${companyName}", "mfa-push-challenge-authenticator-error": "We couldn't send the notification. Please try again later.", "mfa-push-verify-authenticator-error": "We couldn't verify the enrollment. Please try again later.", "mfa-push-verify-transaction-pending": "We have not received a confirmation, please try scanning the code again.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "polling-interval-exceeded": "We have not received a confirmation, please slow down.", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive a notification?", "separatorText": "OR", "title": "Verify Your Identity", "too-many-push": "We have received too many notification requests. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "transaction-rejected": "Notification rejected"}, "reset-password-mfa-recovery-code-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "Enter the recovery code you were provided during your initial enrollment.", "invalid-code": "The code you entered is invalid", "invalid-code-format": "Recovery code must have 24 alphanumeric characters", "logoAltText": "${companyName}", "no-confirmation": "Please confirm you have recorded the code", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter your recovery code", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-sms-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "description": "We've sent a text message to:", "editLinkScreenReadableText": "Edit phone number", "editText": "Edit", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the 6-digit code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Resend", "resendText": "Didn't receive a code?", "resendVoiceActionSeparatorTextBefore": "or", "resendVoiceActionText": "get a call", "send-sms-failed": "There was a problem sending the SMS", "sms-authenticator-error": "We couldn't send the SMS. Please try again later.", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "too-many-sms": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again."}, "reset-password-mfa-voice-challenge": {"authenticator-error": "We couldn't verify the code. Please try again later.", "buttonText": "Continue", "changePhoneText": "Choose another phone number.", "description": "We've sent a 6-digit code via voice phone call to the following phone number:", "editLinkScreenReadableText": "Edit phone number", "editText": "Edit", "invalid-code": "The code you entered is invalid", "invalid-otp-code-format": "OTP Code must have 6 numeric characters", "logoAltText": "${companyName}", "no-transaction-in-progress": "Notification was not sent. Try resending the code.", "pageTitle": "Enter your one-time password to change your password for | ${clientName}", "pickAuthenticatorText": "Try another method", "placeholder": "Enter the 6-digit code", "rememberMeText": "Remember this device for 30 days", "resendActionText": "Call again", "resendSmsActionSeparatorTextBefore": "or", "resendSmsActionText": "send a text", "resendText": "Didn't receive a call?", "send-voice-failed": "There was a problem making the voice call", "title": "Verify Your Identity", "too-many-failures": "Too many failed codes. Wait for some minutes before retrying.", "too-many-voice": "You have exceeded the maximum number of phone messages per hour. Wait a few minutes and try again.", "transaction-not-found": "Your enrollment transaction expired, you will need to start again.", "voice-authenticator-error": "We couldn't make the voice call. Please try again later."}, "reset-password-mfa-webauthn-platform-challenge": {"awaitingConfirmation": "Awaiting device confirmation", "continueButtonText": "Continue", "description": "Press the button below and follow your browser's steps to log in.", "logoAltText": "${companyName}", "pickAuthenticatorText": "Try another method", "rememberMeText": "Remember this device for 30 days", "title": "Use fingerprint or face recognition to reset password | ${clientName}", "too-many-webauthn-challenge-attempts-error": "Too many failed authentication attempts. Please try again later.", "usePasswordText": "Use password", "webauthn-platform-challenge-error": "We could not start the device verification. Please try again later."}, "reset-password-mfa-webauthn-roaming-challenge": {"awaitingConfirmation": "Awaiting Security Key", "continueButtonText": "Use security key", "description": "Make sure your Security Key is nearby. Once you continue, you will be prompted to use it.", "logoAltText": "${companyName}", "pageTitle": "Use your security key to reset password | ${clientName}", "pickAuthenticatorText": "Try another method", "rememberMeText": "Remember this device for 30 days", "title": "Verify Your Identity", "too-many-webauthn-challenge-attempts-error": "Too many failed authentication attempts. Please try again later.", "webauthn-challenge-error": "We could not start the security key verification. Please try again later."}, "reset-password-request": {"auth0-users-expired-ticket": "This ticket was expired.", "auth0-users-used-ticket": "This ticket was already used.", "auth0-users-validation": "Something went wrong, please try again later", "backToLoginLinkText": "Back", "buttonText": "Send link", "custom-script-error-code": "Something went wrong, please try again later.", "descriptionEmail": "Enter your email address, and we’ll send you a link to reset your password.", "descriptionUsername": "Enter your email address, and we’ll send you a link to reset your password.", "invalid-email-format": "Email is not valid.", "logoAltText": "${companyName}", "no-email": "Please enter an email address", "no-username": "Email is required", "pageTitle": "Reset your password | ${clientName}", "placeholderEmail": "Email address", "placeholderUsername": "EMAIL", "reset-password-error": "We had a problem sending the email, please try again later.", "title": "Forgot your password?", "too-many-email": "You have exceeded the amount of emails. Wait a few minutes and try again.", "too-many-requests": "You have exceeded the amount of emails. Wait a few minutes and try again."}, "reset-password-success": {"buttonText": "Back", "description": "Your password has been changed successfully.", "eventTitle": "Password Changed!", "pageTitle": "Password reset successful | ${clientName}"}}, "mfa-otp": {"mfa-otp-challenge": {"pageTitle": "Enter your one-time passcode to sign in ${clientName}", "title": "Verify your account", "description": "Open the authenticator app you have previously linked your SleekFlow account to, and enter the passcode to continue. ", "placeholder": "Enter your one-time passcode", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. "}, "mfa-otp-enrollment-code": {"pageTitle": "Copy the code to sign in using a one-time passcode ${clientName}", "description": "Copy this code to your authenticator app to complete setting up 2FA. Once 2FA is set up, enter the one-time passcode from the authenticator app.   (We recommend downloading  Microsoft Authenticator or Google Authenticator from App Store or Google Play Store.)", "placeholder": "Enter your one-time passcode", "title": "Set up 2FA manually", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. "}, "mfa-otp-enrollment-qr": {"description": "Scan the QR code below with an authenticator app and enter the one-time passcode to continue. We recommend downloading  Microsoft Authenticator or Google Authenticator from App Store or Google Play Store. ", "codeEnrollmentText": "Set up manually instead", "pageTitle": "Scan the code to sign in using a one-time passcode ${clientName}", "title": "Secure your account", "placeholder": "Enter your one-time passcode", "separatorText": "AND", "invalid-code": "The passcode you entered is invalid", "too-many-failures": "Too many failed attempts. Wait for some minutes before retrying.", "transaction-not-found": "Session expired. Please go back to the previous page to start again. ", "user-already-enrolled": "You have already enrolled on 2FA. Please go back to the previous page to sign in."}}}}