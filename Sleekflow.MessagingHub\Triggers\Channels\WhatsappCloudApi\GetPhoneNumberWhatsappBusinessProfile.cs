using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class GetPhoneNumberWhatsappBusinessProfile
    : ITrigger<GetPhoneNumberWhatsappBusinessProfile.GetPhoneNumberWhatsappBusinessProfileInput,
        GetPhoneNumberWhatsappBusinessProfile.GetPhoneNumberWhatsappBusinessProfileOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;

    public GetPhoneNumberWhatsappBusinessProfile(IWabaService wabaService, IChannelService channelService)
    {
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class GetPhoneNumberWhatsappBusinessProfileInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [JsonConstructor]
        public GetPhoneNumberWhatsappBusinessProfileInput(
            string sleekflowCompanyId,
            string messagingHubWabaId,
            string messagingHubPhoneNumberId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            MessagingHubWabaId = messagingHubWabaId;
            MessagingHubPhoneNumberId = messagingHubPhoneNumberId;
        }
    }

    public class GetPhoneNumberWhatsappBusinessProfileOutput
    {
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [JsonProperty("whatsapp_business_profile")]
        public FacebookBusinessProfileResult WhatsappBusinessProfile { get; set; }

        [JsonConstructor]
        public GetPhoneNumberWhatsappBusinessProfileOutput(
            string messagingHubWabaId,
            string messagingHubPhoneNumberId,
            FacebookBusinessProfileResult whatsappBusinessProfile)
        {
            MessagingHubWabaId = messagingHubWabaId;
            MessagingHubPhoneNumberId = messagingHubPhoneNumberId;
            WhatsappBusinessProfile = whatsappBusinessProfile;
        }
    }

    public async Task<GetPhoneNumberWhatsappBusinessProfileOutput> F(GetPhoneNumberWhatsappBusinessProfileInput input)
    {
        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            input.MessagingHubWabaId,
            input.SleekflowCompanyId,
            input.MessagingHubPhoneNumberId);

        var whatsappBusinessProfile = await _channelService.GetPhoneNumberWhatsappBusinessProfileAsync(
            waba,
            input.SleekflowCompanyId,
            input.MessagingHubPhoneNumberId);

        return new GetPhoneNumberWhatsappBusinessProfileOutput(
            input.MessagingHubWabaId,
            input.MessagingHubPhoneNumberId,
            whatsappBusinessProfile);
    }
}