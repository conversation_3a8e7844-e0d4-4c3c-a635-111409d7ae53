﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class GetWebScraperTaskRuns : ITrigger<GetWebScraperTaskRuns.GetWebScraperTaskRunsInput, GetWebScraperTaskRuns.GetWebScraperTaskRunsOutput>
{
    private readonly IWebScraperService _webScraperService;

    public GetWebScraperTaskRuns(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class GetWebScraperTaskRunsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [Required]
        [JsonProperty("offset")]
        public int Offset { get; set; }

        [Required]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetWebScraperTaskRunsInput(
            string sleekflowCompanyId,
            string apifyTaskId,
            int offset,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyTaskId = apifyTaskId;
            Offset = offset;
            Limit = limit;
        }
    }

    public class GetWebScraperTaskRunsOutput
    {
        [JsonProperty("web_scraper_runs")]
        public List<WebScraperRun> WebScraperRuns { get; set; }

        [JsonConstructor]
        public GetWebScraperTaskRunsOutput(List<WebScraperRun> webScraperRuns)
        {
            WebScraperRuns = webScraperRuns;
        }
    }

    public async Task<GetWebScraperTaskRunsOutput> F(GetWebScraperTaskRunsInput getWebScraperTaskInput)
    {
        var taskRuns = await _webScraperService.GetWebScraperTaskRunsAsync(
            getWebScraperTaskInput.SleekflowCompanyId,
            getWebScraperTaskInput.ApifyTaskId,
            getWebScraperTaskInput.Offset,
            getWebScraperTaskInput.Limit);

        return new GetWebScraperTaskRunsOutput(taskRuns);
    }
}