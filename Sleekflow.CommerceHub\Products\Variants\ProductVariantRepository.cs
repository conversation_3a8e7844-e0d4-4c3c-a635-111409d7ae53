using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Products.Variants;

public interface IProductVariantRepository : IDynamicFiltersRepository<ProductVariant>
{
    Task<ProductVariant> GetProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId);

    Task<List<ProductVariant>> GetProductVariantsAsync(
        List<string> productVariantIds,
        string storeId,
        string sleekflowCompanyId);

    Task<List<ProductVariant>> GetProductVariantsAsync(
        string storeId,
        string sleekflowCompanyId,
        string productId);

    Task<List<ProductVariant>> GetProductVariantsAsync(
        string storeId,
        string sleekflowCompanyId,
        List<string> productIds);

    Task<int> GetCountAsync(
        string sleekflowCompanyId,
        int? limit = 10000,
        CancellationToken cancellationToken = default);
}

public class ProductVariantRepository
    : DynamicFiltersBaseRepository<ProductVariant>, IProductVariantRepository, IScopedService
{
    private readonly ILogger<ProductVariantRepository> _logger;

    public ProductVariantRepository(
        IServiceProvider serviceProvider,
        ILogger<ProductVariantRepository> logger,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
        _logger = logger;
    }

    public async Task<ProductVariant> GetProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId)
    {
        var productVariants = await GetObjectsAsync(
            pv =>
                pv.Id == id
                && pv.StoreId == storeId
                && pv.SleekflowCompanyId == sleekflowCompanyId
                && pv.SysTypeName == SysTypeNames.ProductVariant);
        if (productVariants.Any())
        {
            return productVariants.First();
        }

        throw new SfNotFoundObjectException(id, sleekflowCompanyId);
    }

    public Task<List<ProductVariant>> GetProductVariantsAsync(
        List<string> productVariantIds,
        string storeId,
        string sleekflowCompanyId)
    {
        return GetObjectsAsync(
            pv =>
                pv.StoreId == storeId
                && pv.SleekflowCompanyId == sleekflowCompanyId
                && productVariantIds.Contains(pv.Id)
                && pv.SysTypeName == SysTypeNames.ProductVariant);
    }

    public Task<List<ProductVariant>> GetProductVariantsAsync(
        string storeId,
        string sleekflowCompanyId,
        string productId)
    {
        return GetObjectsAsync(
            pv =>
                pv.StoreId == storeId
                && pv.SleekflowCompanyId == sleekflowCompanyId
                && pv.ProductId == productId
                && pv.SysTypeName == SysTypeNames.ProductVariant);
    }

    public async Task<List<ProductVariant>> GetProductVariantsAsync(
        string storeId,
        string sleekflowCompanyId,
        List<string> productIds)
    {
        var productVariants = await GetObjectsAsync(
            pv =>
                pv.StoreId == storeId
                && pv.SleekflowCompanyId == sleekflowCompanyId
                && productIds.Contains(pv.ProductId)
                && pv.SysTypeName == SysTypeNames.ProductVariant);

        return productVariants;
    }

    public override Task<int> CreateAsync(
        ProductVariant productVariant,
        string partitionKey,
        TransactionalBatch? transactionalBatch = null,
        CancellationToken cancellationToken = default)
    {
        // This is intended to be NOT implemented
        throw new NotImplementedException();
    }

    public async Task<int> GetCountAsync(
        string sleekflowCompanyId,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT VALUE COUNT(1)" +
                "FROM c " +
                "WHERE c.sleekflow_company_id = @sleekflowCompanyId " +
                "AND c.sys_type_name = @sys_type_name " +
                "AND ARRAY_CONTAINS(c.record_statuses, @record_status, true)")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@sys_type_name", SysTypeNames.ProductVariant)
            .WithParameter("@record_status", "Active");
        var container = GetContainer();
        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int>(
            qd,
            requestOptions: new QueryRequestOptions
            {
                MaxItemCount = limit > 1000 ? 2500 : limit,
                MaxBufferedItemCount = limit > 1000 ? 2500 : limit,
                MaxConcurrency = 4,
            });

        var count = 0;
        var objs = new List<int>();

        while (itemQueryIterator.HasMoreResults && count < limit)
        {
            var response = await itemQueryIterator.ReadNextAsync(cancellationToken);
            objs.AddRange(response);

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "GetCountAsync {QueryText} {RequestCharge}",
                    qd.QueryText,
                    response.RequestCharge);
            }

            count += response.Count;
        }

        return objs.Any() ? objs.First() : 0;
    }
}