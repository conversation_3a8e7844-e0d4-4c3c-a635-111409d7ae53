using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProviderV2 : ITrigger
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public InitProviderV2(ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class InitProviderV2Input : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        [ValidateObject]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderV2Input(
            string sleekflowCompanyId,
            string successUrl,
            string failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderV2Output
    {
        [JsonProperty("salesforce_authentication_url")]
        public string SalesforceAuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderV2Output(string salesforceAuthenticationUrl)
        {
            SalesforceAuthenticationUrl = salesforceAuthenticationUrl;
        }
    }

    public async Task<InitProviderV2Output> F(
        InitProviderV2Input initProviderInput)
    {
        var redirectUrl =
            await _salesforceAuthenticationService.AuthenticateV2Async(
                initProviderInput.SleekflowCompanyId,
                initProviderInput.SuccessUrl,
                initProviderInput.FailureUrl,
                initProviderInput.AdditionalDetails);

        return new InitProviderV2Output(redirectUrl);
    }
}