using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Blobs;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowStepsService
{
    Task<List<Step>> SaveWorkflowStepsAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        List<Step> workflowSteps);

    Task<List<Step>> GetWorkflowStepsAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);

    Task<bool> DeleteWorkflowStepsAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);
}

public sealed class WorkflowStepsService : IWorkflowStepsService, IScopedService
{
    public const string WorkflowStepsContainerName = "workflow-steps";

    private readonly IBlobService _blobService;
    private readonly ICacheService _cacheService;
    private readonly ISpecializedMemoryCacheService _specializedMemoryCache;

    public WorkflowStepsService(
        IBlobService blobService,
        ICacheService cacheService,
        IEnumerable<ISpecializedMemoryCacheService> specializedMemoryCacheServices)
    {
        _blobService = blobService;
        _cacheService = cacheService;
        _specializedMemoryCache = specializedMemoryCacheServices
            .First(x => x.CacheName == WorkflowStepsContainerName);
    }

    public async Task<List<Step>> SaveWorkflowStepsAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        List<Step> workflowSteps)
    {
        await _blobService.UploadAsJsonAsync(
            WorkflowStepsContainerName,
            GetWorkflowStepsBlobName(
                sleekflowCompanyId,
                workflowVersionedId),
            workflowSteps);

        return workflowSteps;
    }

    public async Task<List<Step>> GetWorkflowStepsAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        var cacheKey = $"WorkflowSteps:{sleekflowCompanyId}:{workflowVersionedId}";

        var steps = await _specializedMemoryCache.GetOrCreateAsync(
            cacheKey,
            () => _cacheService.CacheAsync(
                cacheKey,
                () => _blobService.DownloadJsonAsAsync<List<Step>>(
                    WorkflowStepsContainerName,
                    GetWorkflowStepsBlobName(
                        sleekflowCompanyId,
                        workflowVersionedId)),
                TimeSpan.FromMinutes(10)));

        return steps ?? new();
    }

    public Task<bool> DeleteWorkflowStepsAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        return _blobService.RemoveIfExistsAsync(
            WorkflowStepsContainerName,
            GetWorkflowStepsBlobName(
                sleekflowCompanyId,
                workflowVersionedId));
    }

    private static string GetWorkflowStepsBlobName(
        string sleekflowCompanyId,
        string workflowVersionedId)
        => Path.Combine(sleekflowCompanyId, workflowVersionedId) + ".json";
}