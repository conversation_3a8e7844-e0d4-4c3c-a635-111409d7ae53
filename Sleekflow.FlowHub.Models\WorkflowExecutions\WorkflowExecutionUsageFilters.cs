using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionUsageFilters : IValidatableObject
{
    [JsonProperty("execution_from_date_time")]
    public DateTimeOffset? ExecutionFromDateTime { get; set; }

    [JsonProperty("execution_to_date_time")]
    public DateTimeOffset? ExecutionToDateTime { get; set; }

    [JsonProperty("workflow_type")]
    public string? WorkflowType { get; set; }

    [JsonProperty("workflow_status")]
    public string? WorkflowStatus { get; set; }

    [JsonProperty("workflow_name")]
    public string? WorkflowName { get; set; }

    [JsonConstructor]
    public WorkflowExecutionUsageFilters(
        DateTimeOffset? executionFromDateTime,
        DateTimeOffset? executionToDateTime,
        string? workflowType,
        string? workflowStatus,
        string? workflowName)
    {
        ExecutionFromDateTime = executionFromDateTime;
        ExecutionToDateTime = executionToDateTime;
        WorkflowType = workflowType;
        WorkflowStatus = workflowStatus;
        WorkflowName = workflowName;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (ExecutionFromDateTime.HasValue && !ExecutionToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both ExecutionFromDateTime and ExecutionToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(ExecutionFromDateTime)
                });
        }

        if (!ExecutionFromDateTime.HasValue && ExecutionToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both ExecutionFromDateTime and ExecutionToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(ExecutionToDateTime)
                });
        }

        if (ExecutionFromDateTime.HasValue && ExecutionToDateTime.HasValue)
        {
            if (ExecutionFromDateTime.Value > ExecutionToDateTime.Value)
            {
                yield return new ValidationResult(
                    "ExecutionFromDateTime can't be greater than ExecutionToDateTime",
                    new[]
                    {
                        nameof(ExecutionFromDateTime),
                        nameof(ExecutionToDateTime)
                    });
            }

            if ((ExecutionToDateTime.Value - ExecutionFromDateTime.Value).TotalDays > 31)
            {
                yield return new ValidationResult(
                    "The duration between ExecutionFromDateTime and ExecutionToDateTime can't be more than 31 days",
                    new[]
                    {
                        nameof(ExecutionFromDateTime),
                        nameof(ExecutionToDateTime)
                    });
            }
        }
    }
}