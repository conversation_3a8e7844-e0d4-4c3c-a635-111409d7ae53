using MassTransit;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.Exceptions;
using Sleekflow.Models.ActionEvents.CrmHub;
using Schema = Sleekflow.CrmHub.Models.Schemas.Schema;

namespace Sleekflow.CrmHub.Events;

public class CreatePropertyDrivenSchemafulObjectConsumerDefinition
    : ConsumerDefinition<CreatePropertyDrivenSchemafulObjectConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<CreatePropertyDrivenSchemafulObjectConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class CreatePropertyDrivenSchemafulObjectConsumer : IConsumer<CreatePropertyDrivenSchemafulObjectEvent>
{
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public CreatePropertyDrivenSchemafulObjectConsumer(
        ISchemaService schemaService,
        ISchemafulObjectService schemafulObjectService)
    {
        _schemaService = schemaService;
        _schemafulObjectService = schemafulObjectService;
    }

    public async Task Consume(ConsumeContext<CreatePropertyDrivenSchemafulObjectEvent> context)
    {
        var request = context.Message;
        var sleekflowCompanyId = request.SleekflowCompanyId;

        // Obtain the Schema with Unique Name;
        var (schemas, _) = await _schemaService.GetContinuationTokenizedSchemasAsync(
            SchemaQueryBuilder.BuildQueryDef(
                [],
                [
                    new SchemaQueryBuilder.FilterGroup(
                    [
                        new SchemaQueryBuilder.SchemaFilter(
                            Schema.PropertyNameUniqueName,
                            "=",
                            request.SchemaUniqueName)
                    ])
                ],
                [],
                request.SleekflowCompanyId),
            null,
            1);

        var schema = schemas.FirstOrDefault();
        if (schema is null)
        {
            throw new SfNotFoundObjectException(
                $"No schema found for company {sleekflowCompanyId} {request.SchemaUniqueName}");
        }

        await _schemafulObjectService.CreateAndGetSchemafulObjectAsync(
            schema,
            sleekflowCompanyId,
            string.Empty,
            SanitizePropertyValues(schema, request.PropertyValues),
            request.SleekflowUserProfileId,
            request.CreatedVia);
    }

    private static Dictionary<string, object?> SanitizePropertyValues(
        Schema schema,
        Dictionary<string, object?> propertyValues)
    {
        // from [propertyUniqueName: Value] to [propertyId: Value]
        return propertyValues.Select(
                kvp => new
                {
                    propertyId = schema.Properties.First(p => p.UniqueName == kvp.Key).Id, value = kvp.Value
                })
            .ToDictionary(x => x.propertyId, x => x.value);
    }
}