using System.Text.RegularExpressions;
using GraphApi.Client.Const.WhatsappCloudApi;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Utils.CloudApis;

public static class CloudApiUtils
{
    public static bool IsWabaMessagingFunctionAvailable(
        ILogger logger,
        Waba? waba,
        bool isMessageFunctionLimited = false)
    {
        if (waba is null)
        {
            return false;
        }

        if (isMessageFunctionLimited && waba.MessagingFunctionLimitation == MessagingFunctionLimitationType.BlockAll)
        {
            return false;
        }

        return true;
    }

    public static FacebookTimezone GetFacebookTimezone(string? facebookTimezoneId)
    {
        if (string.IsNullOrEmpty(facebookTimezoneId))
        {
            // UTC+0
            return FacebookTimezoneConst.FacebookTimezoneDictionary.Value["0"];
        }

        return FacebookTimezoneConst.FacebookTimezoneDictionary.Value[facebookTimezoneId];
    }

    public static string FormatDateTimeOffsetByGranularity(DateTimeOffset dateTimeOffset, string granularity)
    {
        return granularity switch
        {
            WhatsappConversationAnalyticGranularityConst.HALF_HOUR => dateTimeOffset.ToString(
                "yyyy-MM-ddTHH:mm:ss.fffZ"),
            WhatsappConversationAnalyticGranularityConst.DAILY => dateTimeOffset.ToString("yyyy-MM-dd"),
            WhatsappConversationAnalyticGranularityConst.MONTHLY => dateTimeOffset.ToString("yyyy-MM"),
            _ => dateTimeOffset.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
        };
    }

    public static Dictionary<string, int> SumConversationCategoryQuantities(
        IEnumerable<Dictionary<string, int>> conversationCategoryQuantitiesList)
    {
        var sumConversationCategoryQuantities = new Dictionary<string, int>();

        foreach (var conversationCategoryQuantities in conversationCategoryQuantitiesList)
        {
            foreach (var (key, value) in conversationCategoryQuantities)
            {
                sumConversationCategoryQuantities.TryAdd(key, 0);

                sumConversationCategoryQuantities[key] += value;
            }
        }

        return sumConversationCategoryQuantities;
    }

    public static bool IsConversationCategoryQuantitiesEqual(
        Dictionary<string, int>? left,
        Dictionary<string, int>? right)
    {
        if (left != null && right == null)
        {
            return false;
        }

        if (left == null && right != null)
        {
            return false;
        }

        if (left == null && right == null)
        {
            return true;
        }

        return left!.Count == right!.Count && !left.Except(right).Any();
    }

    public static bool IsConversationCategoryCostsEqual(
        Dictionary<string, decimal>? left,
        Dictionary<string, decimal>? right)
    {
        if (left != null && right == null)
        {
            return false;
        }

        if (left == null && right != null)
        {
            return false;
        }

        if (left == null && right == null)
        {
            return true;
        }

        return left!.Count == right!.Count && !left.Except(right).Any();
    }

    public static string NormalizePhoneNumber(string phoneNumber)
    {
        return new Regex("[^0-9]", RegexOptions.Compiled).Replace(phoneNumber, string.Empty);
    }

    // In order to preserved the Facebook rate limit
    public static IEnumerable<(long ActualLastConversationStartTimestamp, long ActualLatestConversationEndTimestamp)>
        ExtractDateTimes(
            long lastConversationStartTimestamp,
            long latestConversationEndTimestamp,
            int defaultPeriods = 1)
    {
        var periods = defaultPeriods;
        var dateTimes = new List<(long, long)>();
        var actualStartDate = lastConversationStartTimestamp;
        var lastConversationStartUnixTimeSeconds = DateTimeOffset.FromUnixTimeSeconds(lastConversationStartTimestamp);
        while (lastConversationStartUnixTimeSeconds.AddMonths(periods) <
               DateTimeOffset.FromUnixTimeSeconds(latestConversationEndTimestamp))
        {
            var startDateInstance = lastConversationStartUnixTimeSeconds.AddMonths(periods);

            // Offset Time Period To Be Either .00 or .30
            var endDateInstance =
                startDateInstance.ToUnixTimeSeconds() - (startDateInstance.ToUnixTimeSeconds() % 1800L);
            dateTimes.Add((actualStartDate, endDateInstance));
            actualStartDate = endDateInstance;
            periods++;
        }

        dateTimes.Add((actualStartDate, latestConversationEndTimestamp));
        return dateTimes;
    }
}