﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.Entities;

public interface IEntityContextService
{
    Task<CrmHubEntityContext?> GetEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName);

    public readonly record struct GetAndAssociateEntityContextOutput(
        CrmHubEntityContext? PrevEntityContext,
        CrmHubEntityContext NewEntityContext);

    Task<GetAndAssociateEntityContextOutput> GetAndAssociateEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId);

    public readonly record struct GetAndUnassociateEntityContextOutput(
        CrmHubEntityContext PrevEntityContext,
        CrmHubEntityContext NewEntityContext);

    Task<GetAndUnassociateEntityContextOutput> GetAndUnassociateEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId);

    Task<CrmHubEntityContext> GetOrInitEntityContextAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string? phoneNumber,
        string? email,
        string providerName,
        string providerObjectId);

    Task<CrmHubEntityContext?> DeleteExternalIdFromEntityContextAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId);

    Task<CrmHubEntityContext?> CleanUpEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName);
}

public class EntityContextService : ISingletonService, IEntityContextService
{
    private readonly IIdService _idService;
    private readonly ILogger<EntityContextService> _logger;
    private readonly IEntityContextRepository _entityContextRepository;

    public EntityContextService(
        IIdService idService,
        ILogger<EntityContextService> logger,
        IEntityContextRepository entityContextRepository)
    {
        _idService = idService;
        _logger = logger;
        _entityContextRepository = entityContextRepository;
    }

    public async Task<CrmHubEntityContext?> GetEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName)
    {
        var entityContexts = await _entityContextRepository.GetObjectsAsync(
            ec => ec.Id == id
                  && ec.SysSleekflowCompanyId == sleekflowCompanyId
                  && ec.SysEntityTypeName == entityTypeName);

        return entityContexts[0];
    }

    public async Task<IEntityContextService.GetAndAssociateEntityContextOutput> GetAndAssociateEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId)
    {
        var externalId = providerName + ":" + providerObjectId;

        var exactMatchedEntityContext =
            await GetExactMatchedEntityContextAsync(sleekflowCompanyId, entityTypeName, externalId);
        if (exactMatchedEntityContext != null && exactMatchedEntityContext.Id == id)
        {
            // This is the target CrmHubEntityContext
            return new IEntityContextService.GetAndAssociateEntityContextOutput(
                null,
                exactMatchedEntityContext);
        }

        var targetEntityContext = await GetEntityContextAsync(id, sleekflowCompanyId, entityTypeName);
        if (targetEntityContext == null)
        {
            throw new Exception($"Unable to resolve the CrmHubEntityContext. id=[{id}], externalId=[{externalId}]");
        }
        else if (targetEntityContext.CtxExternalId != null)
        {
            if (exactMatchedEntityContext != null)
            {
                var patchOperations = new List<PatchOperation>
                {
                    PatchOperation.Replace<string?>(
                        $"/{CrmHubEntityContext.PropertyNameCtxExternalId}",
                        null),
                };
                if (exactMatchedEntityContext.CtxExternalIds != null)
                {
                    patchOperations.Add(
                        PatchOperation.Replace<List<string>>(
                            $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                            exactMatchedEntityContext.CtxExternalIds.Where(i => i != externalId).ToList()));
                }

                await _entityContextRepository.PatchAsync(
                    exactMatchedEntityContext.Id,
                    exactMatchedEntityContext.Id,
                    patchOperations,
                    eTag: exactMatchedEntityContext.ETag);
            }

            await _entityContextRepository.PatchAsync(
                targetEntityContext.Id,
                targetEntityContext.Id,
                new List<PatchOperation>
                {
                    PatchOperation.Replace<string?>(
                        $"/{CrmHubEntityContext.PropertyNameCtxExternalId}",
                        null),
                    PatchOperation.Replace<List<string>>(
                        $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                        new List<string>
                        {
                            targetEntityContext.CtxExternalId, externalId
                        })
                },
                eTag: targetEntityContext.ETag);

            return new IEntityContextService.GetAndAssociateEntityContextOutput(
                exactMatchedEntityContext,
                targetEntityContext);
        }
        else if (targetEntityContext.CtxExternalIds != null && targetEntityContext.CtxExternalIds.Count > 0)
        {
            var ctxExternalIds = targetEntityContext.CtxExternalIds;

            // If available for matching, merge into the same CrmHubEntityContext
            var idOfSameProvider = ctxExternalIds.Find(pei => pei.StartsWith(providerName + ":"));
            if (idOfSameProvider == null)
            {
                ctxExternalIds.Add(externalId);

                if (exactMatchedEntityContext != null)
                {
                    var patchOperations = new List<PatchOperation>
                    {
                        PatchOperation.Replace<string?>(
                            $"/{CrmHubEntityContext.PropertyNameCtxExternalId}",
                            null),
                    };
                    if (exactMatchedEntityContext.CtxExternalIds != null)
                    {
                        patchOperations.Add(
                            PatchOperation.Replace<List<string>>(
                                $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                                exactMatchedEntityContext.CtxExternalIds.Where(i => i != externalId).ToList()));
                    }

                    await _entityContextRepository.PatchAsync(
                        exactMatchedEntityContext.Id,
                        exactMatchedEntityContext.Id,
                        patchOperations,
                        eTag: exactMatchedEntityContext.ETag);
                }

                await _entityContextRepository.PatchAsync(
                    targetEntityContext.Id,
                    targetEntityContext.Id,
                    new List<PatchOperation>
                    {
                        PatchOperation.Replace<string?>(
                            $"/{CrmHubEntityContext.PropertyNameCtxExternalId}",
                            null),
                        PatchOperation.Replace<List<string>>(
                            $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                            ctxExternalIds)
                    },
                    eTag: targetEntityContext.ETag);

                return new IEntityContextService.GetAndAssociateEntityContextOutput(
                    exactMatchedEntityContext,
                    targetEntityContext);
            }

            // Conflicted - Already has entities from the same provider.
            throw new Exception(
                $"The associating CrmHubEntityContext is conflicted. id=[{id}], externalId=[{externalId}]");
        }

        throw new Exception($"Unable to associate the CrmHubEntityContext. id=[{id}], externalId=[{externalId}]");
    }

    public async Task<IEntityContextService.GetAndUnassociateEntityContextOutput> GetAndUnassociateEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId)
    {
        // TODO Retry on etag not matched
        var externalId = providerName + ":" + providerObjectId;

        var entityContext = await GetEntityContextAsync(id, sleekflowCompanyId, entityTypeName);
        if (entityContext == null)
        {
            throw new Exception($"Unable to resolve the CrmHubEntityContext. id=[{id}], externalId=[{externalId}]");
        }
        else if (entityContext.CtxExternalId != null)
        {
            throw new Exception(
                $"This is an external CrmHubEntityContext. You should unassociate the correct one. id=[{id}], externalId=[{externalId}]");
        }
        else if (entityContext.CtxExternalIds == null || entityContext.CtxExternalIds.Count == 0)
        {
            throw new Exception(
                $"This is an external CrmHubEntityContext. You should unassociate the correct one. id=[{id}], externalId=[{externalId}]");
        }
        else if (entityContext.CtxExternalIds.All(cei => cei != externalId))
        {
            throw new Exception(
                $"The CrmHubEntityContext doesn't contain the externalId=[{externalId}]. id=[{id}]");
        }

        var allExternalIds = GetAllExternalIds(entityContext);
        var hasNoMoreExternalIds = allExternalIds.All(ei => ei == externalId);
        if (hasNoMoreExternalIds)
        {
            throw new Exception(
                $"The CrmHubEntityContext is only associated with the externalId=[{externalId}] so it is not allowed to unassociate the CrmHubEntityContext.");
        }

        try
        {
            // Update CrmHubEntityContext to exclude the ExternalId
            await _entityContextRepository.PatchAsync(
                entityContext.Id,
                entityContext.Id,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                        entityContext.CtxExternalIds.Where(i => i != externalId).ToList())
                },
                eTag: entityContext.ETag);

            var s = _idService.GetId(entityTypeName);

            // Create a new ExternalEntityContext
            var newEntityContext = await _entityContextRepository.CreateAndGetAsync(
                CrmHubEntityContext.GetExternalEntityContext(
                    s,
                    entityTypeName,
                    sleekflowCompanyId,
                    ctxExternalId: externalId),
                s);

            return new IEntityContextService.GetAndUnassociateEntityContextOutput(
                entityContext,
                newEntityContext);
        }
        catch (Exception e)
        {
            throw new Exception("Unable to patch or create the CrmHubEntityContext.", e);
        }
    }

    public async Task<CrmHubEntityContext> GetOrInitEntityContextAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string? phoneNumber,
        string? email,
        string providerName,
        string providerObjectId)
    {
        // ExternalId = ProviderName + ":" + ProviderObjectId;
        // Merged   - (CtxPhoneNumber != null || CtxEmail != null) && ExternalId == null
        // External -  CtxPhoneNumber == null && CtxEmail == null  && ExternalId != null
        var externalId = providerName + ":" + providerObjectId;

        var exactMatchedEntityContext =
            await GetExactMatchedEntityContextAsync(sleekflowCompanyId, entityTypeName, externalId);
        if (exactMatchedEntityContext != null)
        {
            // If the exact matched identity does not match the incoming identity,
            // Remove the identity in the EntityContext
            var isPhoneNumberChanged = exactMatchedEntityContext.CtxPhoneNumber != null
                                       && exactMatchedEntityContext.CtxPhoneNumber != phoneNumber;
            var isEmailChanged = exactMatchedEntityContext.CtxEmail != email
                                 && exactMatchedEntityContext.CtxEmail != null;
            if (isPhoneNumberChanged || isEmailChanged)
            {
                await _entityContextRepository.PatchAsync(
                    exactMatchedEntityContext.Id,
                    exactMatchedEntityContext.Id,
                    new List<PatchOperation>
                    {
                        PatchOperation.Set<string?>(
                            $"/{CrmHubEntityContext.PropertyNameCtxPhoneNumber}",
                            null),
                        PatchOperation.Set<string?>(
                            $"/{CrmHubEntityContext.PropertyNameCtxEmail}",
                            null)
                    },
                    transactionalBatch: null,
                    eTag: exactMatchedEntityContext.ETag);
            }

            return exactMatchedEntityContext;
        }

        if (string.IsNullOrWhiteSpace(phoneNumber) && string.IsNullOrWhiteSpace(email))
        {
            return await InitAndGetExternalEntityContextAsync(
                sleekflowCompanyId,
                entityTypeName,
                externalId);
        }

        // The following introduces three variants
        // 1. Phone Number + Email
        // 2. Phone Number
        // 3. Email
        // Three variants are saved.
        var queryDefinition = new QueryDefinition(
                "SELECT " + CrmHubEntityContext.QuerySelectStr + " " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sys_sleekflow_company_id = @sysSleekflowCompanyId AND e.sys_entity_type_name = @sysEntityTypeName AND e.sys_type_name = 'Entity' AND e.ctx_phone_number = @ctxPhoneNumber AND e.ctx_email = @ctxEmail")
            .WithParameter("@sysSleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@sysEntityTypeName", entityTypeName)
            .WithParameter("@ctxPhoneNumber", phoneNumber)
            .WithParameter("@ctxEmail", email);

        var entityContexts =
            await _entityContextRepository.GetObjectsAsync(
                queryDefinition);
        if (entityContexts.Count == 0)
        {
            var s = _idService.GetId(entityTypeName);

            return await _entityContextRepository.CreateAndGetAsync(
                CrmHubEntityContext.GetMergedEntityContext(
                    s,
                    entityTypeName,
                    sleekflowCompanyId,
                    externalId,
                    phoneNumber,
                    email),
                s);
        }
        else
        {
            var mergedEntityContexts = entityContexts
                .Where(ec => ec.CtxExternalId == null)
                .ToList();
            if (mergedEntityContexts.Count > 1)
            {
                _logger.LogWarning(
                    "More than one MergedEntityContexts are found, mergedEntityContextIds {MergedEntityContextIds}",
                    mergedEntityContexts.Select(ec => ec.Id));
            }

            if (mergedEntityContexts.Count == 0)
            {
                var s = _idService.GetId(entityTypeName);

                return await _entityContextRepository.CreateAndGetAsync(
                    CrmHubEntityContext.GetMergedEntityContext(
                        s,
                        entityTypeName,
                        sleekflowCompanyId,
                        externalId,
                        phoneNumber,
                        email),
                    s);
            }

            // TODO Need Retry When ETag Not Matched
            var mergedEntityContext =
                await FindAndMergeAvailableEntityContextAsync(providerName, mergedEntityContexts, externalId);
            if (mergedEntityContext != null)
            {
                return mergedEntityContext;
            }

            // All MergedEntityContexts are conflicted. Create a ExternalEntityContext.
            return await InitAndGetExternalEntityContextAsync(
                sleekflowCompanyId,
                entityTypeName,
                externalId);
        }
    }

    public async Task<CrmHubEntityContext?> DeleteExternalIdFromEntityContextAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId)
    {
        var externalId = providerName + ":" + providerObjectId;

        var entityContext = await GetExactMatchedEntityContextAsync(sleekflowCompanyId, entityTypeName, externalId);
        if (entityContext == null)
        {
            throw new Exception($"Unable to resolve the CrmHubEntityContext. externalId=[{externalId}]");
        }

        var allExternalIds = GetAllExternalIds(entityContext);
        var hasNoMoreExternalIds = allExternalIds.All(ei => ei == externalId);
        if (hasNoMoreExternalIds)
        {
            // The ExternalId is the only one Id
            // Remove the CrmHubEntityContext instead
            await _entityContextRepository.DeleteAsync(
                entityContext.Id,
                entityContext.Id,
                eTag: entityContext.ETag);

            return null;
        }

        if (entityContext.CtxExternalIds == null)
        {
            throw new Exception("Unable to handle the ExternalIds when it is null");
        }

        // Update CrmHubEntityContext to exclude the ExternalId
        await _entityContextRepository.PatchAsync(
            entityContext.Id,
            entityContext.Id,
            new List<PatchOperation>
            {
                PatchOperation.Replace(
                    $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                    entityContext.CtxExternalIds!.Where(i => i != externalId).ToList())
            },
            eTag: entityContext.ETag);

        return entityContext;
    }

    public async Task<CrmHubEntityContext?> CleanUpEntityContextAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName)
    {
        var entityContexts = await _entityContextRepository.GetObjectsAsync(
            ec => ec.Id == id
                  && ec.SysSleekflowCompanyId == sleekflowCompanyId
                  && ec.SysEntityTypeName == entityTypeName);

        foreach (var entityContext in entityContexts)
        {
            var allExternalIds = GetAllExternalIds(entityContext);
            if (allExternalIds.Count != 0)
            {
                return entityContext;
            }

            await _entityContextRepository.DeleteAsync(
                id,
                id,
                eTag: entityContext.ETag);
        }

        return null;
    }

    private async Task<CrmHubEntityContext?> FindAndMergeAvailableEntityContextAsync(
        string providerName,
        List<CrmHubEntityContext> mergedEntityContexts,
        string externalId)
    {
        foreach (var entityContext in mergedEntityContexts)
        {
            var ctxExternalIds = entityContext.CtxExternalIds ?? new List<string>();

            // If matched, return
            var isMatchedEntityContext = ctxExternalIds.Find(pei => pei == externalId);
            if (isMatchedEntityContext != null)
            {
                return entityContext;
            }

            // If available for matching, merge into the same CrmHubEntityContext
            var idOfSameProvider = ctxExternalIds.Find(pei => pei.StartsWith(providerName + ":"));
            if (idOfSameProvider == null)
            {
                var newExternalIds = new List<string>();
                newExternalIds.AddRange(ctxExternalIds);
                newExternalIds.Add(externalId);

                await _entityContextRepository.PatchAsync(
                    entityContext.Id,
                    entityContext.Id,
                    new List<PatchOperation>
                    {
                        PatchOperation.Replace(
                            $"/{CrmHubEntityContext.PropertyNameCtxExternalIds}",
                            newExternalIds)
                    },
                    eTag: entityContext.ETag);

                entityContext.CtxExternalIds = newExternalIds;

                return entityContext;
            }
            else
            {
                // Conflicted - Already has entities from the same provider.
            }
        }

        return null;
    }

    private async Task<CrmHubEntityContext?> GetExactMatchedEntityContextAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string externalId)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT " + CrmHubEntityContext.QuerySelectStr + " " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sys_sleekflow_company_id = @sysSleekflowCompanyId AND e.sys_entity_type_name = @sysEntityTypeName AND e.sys_type_name = 'Entity' AND (e.ctx_external_id = @ctxExternalId OR ARRAY_CONTAINS(e.ctx_external_ids, @ctxExternalId))")
            .WithParameter("@sysSleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@sysEntityTypeName", entityTypeName)
            .WithParameter("@ctxExternalId", externalId);

        var exactMatchedEntityContexts = await _entityContextRepository.GetObjectsAsync(
            queryDefinition);
        if (exactMatchedEntityContexts.Any())
        {
            if (exactMatchedEntityContexts.Count > 1)
            {
                _logger.LogWarning(
                    "More than one EntityContexts are found, externalId {ExternalId}, exactMatchedEntityContexts.Count {Count}",
                    externalId,
                    exactMatchedEntityContexts.Count);
            }

            return exactMatchedEntityContexts[0];
        }

        return null;
    }

    private Task<CrmHubEntityContext> InitAndGetExternalEntityContextAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string externalId)
    {
        var s = _idService.GetId(entityTypeName);

        return _entityContextRepository.CreateAndGetAsync(
            CrmHubEntityContext.GetExternalEntityContext(
                s,
                sysSleekflowCompanyId: sleekflowCompanyId,
                ctxExternalId: externalId,
                sysEntityTypeName: entityTypeName),
            s);
    }

    private static HashSet<string> GetAllExternalIds(CrmHubEntityContext entityContext)
    {
        var allExternalIds = new HashSet<string>();

        if (entityContext.CtxExternalId != null)
        {
            allExternalIds.Add(entityContext.CtxExternalId);
        }

        if (entityContext.CtxExternalIds != null)
        {
            foreach (var ctxExternalId in entityContext.CtxExternalIds)
            {
                allExternalIds.Add(ctxExternalId);
            }
        }

        return allExternalIds;
    }
}