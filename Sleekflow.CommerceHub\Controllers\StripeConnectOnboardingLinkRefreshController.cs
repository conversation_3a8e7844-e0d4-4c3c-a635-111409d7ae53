using Microsoft.AspNetCore.Mvc;
using Sleekflow.CommerceHub.Stripe.Connect;

namespace Sleekflow.CommerceHub.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("[Controller]")]
public class StripeConnectOnboardingLinkRefreshController : ControllerBase
{
    private readonly IStripeConnectService _stripeConnectService;

    public StripeConnectOnboardingLinkRefreshController(
        IStripeConnectService stripeConnectService)
    {
        _stripeConnectService = stripeConnectService;
    }

    [HttpGet]
    [Route("refresh")]
    public async Task<ActionResult> Refresh()
    {
        var platformCountry = HttpContext.Request.Query["platformCountry"].ToString();
        var stripeAccountId = HttpContext.Request.Query["stripeAccountId"].ToString();

        try
        {
            var stripeConnectOnboardingLink =
                await _stripeConnectService.GenerateStripeConnectOnboardingLinkAsync(platformCountry, stripeAccountId);

            return new RedirectResult(stripeConnectOnboardingLink);
        }
        catch (Exception ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
    }
}