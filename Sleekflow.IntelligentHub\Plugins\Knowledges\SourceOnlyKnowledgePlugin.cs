using System.ComponentModel;
using System.Text.RegularExpressions;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.LightRags;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.LightRag;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

public interface ISourceOnlyKnowledgePlugin
{
    [KernelFunction("query_knowledge_sources")]
    [Description(
        "Retrieves tailored information from the knowledge base based on your queries. Provide clear context, specific requirements, and any relevant background details for the most accurate and useful results. The search adapts to both broad conceptual questions and detailed technical inquiries.")]
    [return: Description(
        "Tailored information from the knowledge base based on your queries.")]
    Task<string> QueryKnowledgeSourcesAsync(
        Kernel kernel,
        [Description(
            "Express your information needs naturally, whether as direct questions, scenario descriptions, or specific information requests. Include relevant context, constraints, and preferences in your query. The more specific and contextual your query, the more targeted and useful the response will be.")]
        string query,
        [Description("The number of top K results to return.")]
        int topK);
}

public class SourceOnlyKnowledgePlugin : ISourceOnlyKnowledgePlugin, IScopedService
{
    private const string NewChunkIndicator = "--New Chunk--";

    private readonly ILogger _logger;
    private readonly ILightRagClient _lightRagClient;

    public SourceOnlyKnowledgePlugin(
        ILogger<SourceOnlyKnowledgePlugin> logger,
        ILightRagClient lightRagClient)
    {
        _logger = logger;
        _lightRagClient = lightRagClient;
    }

    [KernelFunction("query_knowledge_sources")]
    [Description(
        "Retrieves tailored information from the knowledge base based on your queries. Provide clear context, specific requirements, and any relevant background details for the most accurate and useful results. The search adapts to both broad conceptual questions and detailed technical inquiries.")]
    [return: Description(
        "Tailored information from the knowledge base based on your queries.")]
    public async Task<string> QueryKnowledgeSourcesAsync(
        Kernel kernel,
        [Description(
            "Express your information needs naturally, whether as direct questions, scenario descriptions, or specific information requests. Include relevant context, constraints, and preferences in your query. The more specific and contextual your query, the more targeted and useful the response will be.")]
        string query,
        [Description(
            "The number of top K results to return.")]
        int topK)
    {
        var sleekflowCompanyId = (string) kernel.Data[KernelDataKeys.SLEEKFLOW_COMPANY_ID]!;
        var agentId = (string) kernel.Data[KernelDataKeys.AGENT_ID]!;

        var confirmedKnowledge = await GetConfirmedKnowledgeAsync(kernel, query, sleekflowCompanyId, agentId, topK);

        return confirmedKnowledge;
    }

    private async Task<string> GetConfirmedKnowledgeAsync(
        Kernel kernel,
        string query,
        string sleekflowCompanyId,
        string agentId,
        int topK)
    {
        try
        {
            var getLightRagSearchResultRequest = new GetLightRagSearchResultRequest(
                query,
                LightRagQueryMode.Naive,
                LightRagClient.GetAgentKnowledgeBaseId(sleekflowCompanyId, agentId),
                true,
                [],
                [],
                topK: topK);
            var getLightRagSearchResultResponse =
                await _lightRagClient.ExecuteAsync<GetLightRagSearchResultResponse>(
                    LightRagPaths.Query,
                    sleekflowCompanyId,
                    agentId,
                    getLightRagSearchResultRequest);
            var data = getLightRagSearchResultResponse?.Response ?? string.Empty;
            var newChunkCount = Regex.Matches(data, Regex.Escape(NewChunkIndicator)).Count;
            if (newChunkCount >= topK)
            {
                _logger.LogInformation(
                    "Confirmed knowledge source new chunks are same as the top k {Query} {SleekflowCompanyId} {AgentId} {NewChunkCount}",
                    query,
                    sleekflowCompanyId,
                    agentId,
                    newChunkCount);
            }

            var cleanedData = KnowledgeCleansing(data);

            var confirmedKnowledge =
                $"""
                 <CONFIRMED_KNOWLEDGE>
                 {cleanedData}
                 </CONFIRMED_KNOWLEDGE>
                 """;

            _logger.LogInformation(
                "Final Confirmed Knowledge: {Query}/{ConfirmedKnowledge}",
                query,
                confirmedKnowledge);

            return confirmedKnowledge;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting confirmed knowledge: {Error}", e.Message);

            var confirmedKnowledge =
                $"""
                 <CONFIRMED_KNOWLEDGE>
                 - No confirmed knowledge found for the query: {query}.
                 </CONFIRMED_KNOWLEDGE>
                 """;
            return confirmedKnowledge;
        }
    }

    private static string KnowledgeCleansing(string data)
    {
        var removedDocumentTags = Regex.Replace(data, @"<document[^>]*>(.*?)</document>", "$1", RegexOptions.Singleline);
        return removedDocumentTags;
    }
}