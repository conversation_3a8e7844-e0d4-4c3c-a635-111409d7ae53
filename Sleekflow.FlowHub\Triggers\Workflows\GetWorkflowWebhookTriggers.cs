﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetWorkflowWebhookTriggers :
    ITrigger<
        GetWorkflowWebhookTriggers.GetWorkflowWebhookTriggersInput,
        GetWorkflowWebhookTriggers.GetWorkflowWebhookTriggersOutput>
{
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;

    public GetWorkflowWebhookTriggers(IWorkflowWebhookTriggerService workflowWebhookTriggerService)
    {
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
    }

    public class GetWorkflowWebhookTriggersInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameWorkflowId)]
        [Required]
        public string WorkflowId { get; set; }

        [JsonConstructor]
        public GetWorkflowWebhookTriggersInput(
            string sleekflowCompanyId,
            string workflowId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
        }
    }

    public class GetWorkflowWebhookTriggersOutput
    {
        [JsonProperty("workflow_webhook_triggers")]
        public List<WorkflowWebhookTrigger> WorkflowWebhookTriggers { get; set; }

        [JsonConstructor]
        public GetWorkflowWebhookTriggersOutput(
            List<WorkflowWebhookTrigger> workflowWebhookTriggers)
        {
            WorkflowWebhookTriggers = workflowWebhookTriggers;
        }
    }

    public async Task<GetWorkflowWebhookTriggersOutput> F(GetWorkflowWebhookTriggersInput input)
    {
        var workflowWebHookTriggers =
            await _workflowWebhookTriggerService.GetWorkflowWebhookTriggersAsync(
                input.WorkflowId,
                input.SleekflowCompanyId);

        return new GetWorkflowWebhookTriggersOutput(workflowWebHookTriggers);
    }
}