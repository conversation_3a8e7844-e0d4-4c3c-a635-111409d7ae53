﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Webhooks.Events;

namespace Sleekflow.Webhooks;

public interface IWebhookService
{
    Task RegisterAsync(Webhook webhook, CancellationToken cancellationToken = default);

    Task<List<Webhook>> GetWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        CancellationToken cancellationToken = default);

    Task SendWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        CancellationToken cancellationToken = default);

    Task RemoveWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        CancellationToken cancellationToken = default);
}

public class WebhookService : IWebhookService
{
    private readonly IWebhookRepository _webhookRepository;
    private readonly IBus _bus;

    public WebhookService(
        IWebhookRepository webhookRepository,
        IBus bus)
    {
        _webhookRepository = webhookRepository;
        _bus = bus;
    }

    public async Task RegisterAsync(Webhook webhook, CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksAsync(
            webhook.SleekflowCompanyId,
            webhook.EntityTypeName,
            webhook.EventTypeName,
            cancellationToken);
        if (webhooks.Any())
        {
            throw new SfDuplicateWebhookException(webhook.EntityTypeName, webhook.EventTypeName);
        }

        await _webhookRepository.CreateAsync(
            webhook,
            webhook.Id,
            cancellationToken: cancellationToken);
    }

    public async Task<List<Webhook>> GetWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT * " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sleekflow_company_id = @sleekflowCompanyId AND e.entity_type_name = @entityTypeName AND e.event_type_name = @eventTypeName")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@entityTypeName", entityTypeName)
            .WithParameter("@eventTypeName", eventTypeName);

        var webhooks = await _webhookRepository.GetObjectsAsync(
            queryDefinition,
            cancellationToken: cancellationToken);

        return webhooks;
    }

    public async Task SendWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksAsync(
            sleekflowCompanyId,
            entityTypeName,
            eventTypeName,
            cancellationToken);

        var events = webhooks
            .Select(
                webhook => new OnWebhookRequestedEvent(
                    webhook.SleekflowCompanyId,
                    webhook.Context,
                    payloadObj,
                    webhook))
            .ToList();

        await _bus.PublishBatch(
            events,
            cancellationToken: cancellationToken);
    }

    public async Task RemoveWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksAsync(sleekflowCompanyId, entityTypeName, eventTypeName, cancellationToken);

        foreach (var webhookId in webhooks.Select(w => w.Id).Distinct())
        {
            await _webhookRepository.DeleteAsync(
                webhookId,
                webhookId,
                cancellationToken: cancellationToken);
        }
    }
}