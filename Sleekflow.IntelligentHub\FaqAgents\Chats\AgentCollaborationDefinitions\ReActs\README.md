# ReAct Agent Collaboration Mode

## Overview

The ReAct collaboration mode provides a specialized agent group focused solely on executing actions rather than generating conversational responses. Unlike other collaboration modes that prioritize crafting natural language responses to users, ReAct is designed to programmatically execute specific instructions through the ActionAgent and return structured JSON reports of the actions performed.

## Purpose

The primary purpose of ReAct is to enable direct action execution without the overhead of conversational exchanges. This is useful for scenarios where:

1. Actions need to be executed by AI without generating a human-readable response
2. System-to-system interactions require structured output of action results
3. Workflow automation steps involve programmatic operations rather than conversational responses

## Architecture

ReAct uses a minimal architecture with a single agent:

```mermaid
graph TD
    subgraph "ReAct Agent Group"
        CF[Coordination Function]
        AA[ActionAgent]
    end

    I[Instruction] --> CF
    CF --> AA
    AA --> JSON[JSON Report]
```

### Key Components

1. **ReActAgentCollaborationDefinition**: The main class that configures and orchestrates the ReAct agent group.
   - Implements `IReActAgentCollaborationDefinition` and `IScopedService` for auto-registration
   - Focuses execution on a single agent (ActionAgent)
   - Extracts instructions from the last chat entry
   - Returns raw JSON output instead of conversational responses

2. **ActionAgent**: The sole agent in the group, responsible for executing instructions.
   - Configured with SleekflowToolsPlugin and ChiliPiperPlugin for executing actions
   - Implements a two-phase process:
     1. Execution Phase: Uses tools to perform the requested actions
     2. Report Phase: Generates a structured JSON report of the actions and outcomes

3. **Termination Protocol**: The agent group terminates precisely after the ActionAgent produces its JSON report.
   - Uses RegexTerminationStrategy to identify completion of the JSON report
   - Detects the presence of "result":"success" or "result":"failure" to mark completion
   - The IsActionAgentFinalReport method validates proper JSON structure

## Input/Output Protocol

### Input

The input to ReAct should be a clear instruction for ActionAgent to execute. This is extracted from the last entry in the provided chatEntries list.

Example inputs:
```
Assign lead 'L123' to the 'Sales Team' with reason 'Hot lead requesting pricing'
```

```
Schedule a demo for the following details:
- First Name: John
- Last Name: Smith
- Email: <EMAIL>
- Phone: ******-123-4567
- Company Name: ABC Corp
```

### Output

The output is a structured JSON object containing:

```json
{
  "agent_name": "ActionAgent",
  "executed_tools": [
    {
      "tool_name": "AssignToTeam",
      "args": [
        {"name": "target_team", "value": "Sales Team"},
        {"name": "reason", "value": "Hot lead requesting pricing"}
      ],
      "tool_result": "Success",
      "outcome": "Lead L123 assigned to Sales Team"
    }
  ],
  "result": "success"
}
```

## Usage

To use the ReAct collaboration mode, specify "ReAct" as the collaboration mode when calling the GroupChatService:

```csharp
var agentConfig = new CompanyAgentConfig
{
    CollaborationMode = AgentCollaborationModes.ReAct
};

var (sourceStr, reply) = await _groupChatService.HandleMultiAgentChatStream(
    companyId,
    chatEntries,
    agentConfig,
    replyGenerationContext);

// The 'reply' will contain the raw JSON report from ActionAgent
var actionResult = JsonConvert.DeserializeObject<ActionAgentResult>(reply);
```

## Available Operations

The ActionAgent in ReAct mode can execute the following operations:

### Lead Assignment Actions
- `AssignToTeam(target_team, assignment_reason)`
- `AddHandsOffCustomObject(assignment_reason, lead_score_as_int, conversation_summary)`

### Demo Scheduling Actions
- `ScheduleDemoWithChiliPiper(fields)`

## Differences from Other Collaboration Modes

1. **Single Agent**: Unlike other modes that use multiple specialized agents, ReAct uses only ActionAgent.
2. **No Conversational Response**: ReAct returns structured JSON output rather than a natural language response.
3. **Direct Instruction Execution**: The input is treated as a direct instruction to execute rather than a conversation to respond to.
4. **Raw JSON Output**: The output is not processed for HTML decoding or Markdown conversion like in other modes.
5. **Termination After One Reply**: The group terminates immediately after ActionAgent produces its report.

## Implementation Details

### Selection Strategy

The selection strategy for ReAct is straightforward:
- If history is empty or only contains the initial instruction, ActionAgent should act
- If ActionAgent has already responded, no further action is needed

### Termination Strategy

The termination strategy uses regex patterns to identify when ActionAgent has produced its final JSON report:
- Looks for either "result":"success" or "result":"failure" in the response
- Verifies that the response is properly formatted JSON with the expected fields

### JSON Structure Validation

The IsActionAgentFinalReport method verifies that:
1. The content is valid JSON
2. It contains the agent_name field set to "ActionAgent"
3. It includes the executed_tools array
4. It contains the result field

## Future Extensions

The ReAct agent group can be extended by:
1. Adding support for additional tools/actions
2. Enhancing error handling and reporting
3. Supporting more complex instruction parsing
4. Adding capability to work with other plugins beyond the current set