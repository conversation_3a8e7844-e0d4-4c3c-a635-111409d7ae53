﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Workers.Triggers.Zoho;

public class SubscriptionsCheckOrchestrator
{
    public class SubscriptionsCheckOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTimeOffset LastUpdateTime { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorCustomStatusOutput(
            long count,
            DateTimeOffset lastUpdateTime,
            DateTimeOffset? lastObjectModificationTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Zoho_SubscriptionsCheck_Orchestrator")]
    public async Task<SubscriptionsCheckOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var startTime = context.CurrentUtcDateTime;
        var subscriptionsCheckInput = context.GetInput<SubscriptionsCheck.SubscriptionsCheckInput>();
        var zohoSubscription = subscriptionsCheckInput.Subscription;

        context.SetCustomStatus(new SubscriptionsCheckOrchestratorCustomStatusOutput(0, startTime, null));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;

        var lastObjectModificationTime =
            zohoSubscription.LastObjectModificationTime?.AddSeconds(1)
            ?? zohoSubscription.LastExecutionStartTime;
        var nextLastObjectModificationTime =
            zohoSubscription.LastObjectModificationTime
            ?? zohoSubscription.LastExecutionStartTime;

        var subscriptionsCheckBatchOutput =
            await context.CallActivityAsync<SubscriptionsCheckBatch.SubscriptionsCheckBatchOutput>(
                "Zoho_SubscriptionsCheck_Batch",
                new SubscriptionsCheckBatch.SubscriptionsCheckBatchInput(
                    subscriptionsCheckInput.SleekflowCompanyId,
                    zohoSubscription,
                    lastObjectModificationTime),
                taskOptions);

        totalCount += subscriptionsCheckBatchOutput.Count;
        nextLastObjectModificationTime =
            subscriptionsCheckBatchOutput.NextLastObjectModificationTime > nextLastObjectModificationTime
                ? subscriptionsCheckBatchOutput.NextLastObjectModificationTime
                : nextLastObjectModificationTime;

        context.SetCustomStatus(
            new SubscriptionsCheckOrchestratorCustomStatusOutput(
                totalCount,
                context.CurrentUtcDateTime,
                nextLastObjectModificationTime));

        await context.CreateTimer(
            context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
            CancellationToken.None);

        await context.CallActivityAsync(
            "Zoho_SubscriptionsCheck_End",
            new SubscriptionsCheckEnd.SubscriptionsCheckEndInput(
                zohoSubscription,
                nextLastObjectModificationTime,
                startTime));

        return new SubscriptionsCheckOrchestratorOutput(totalCount);
    }
}