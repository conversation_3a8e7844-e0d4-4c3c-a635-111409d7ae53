using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Models.Crm;

public class GetPropertyValuesByContactIdsInput
{
    [Required]
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("contact_ids")]
    [Validations.ValidateArray]

    public List<string> ContactIds { get; set; }

    [Required]
    [JsonProperty("property_id")]
    public string PropertyId { get; set; }

    [JsonConstructor]
    public GetPropertyValuesByContactIdsInput(string sleekflowCompanyId, string schemaId, List<string> contactIds, string propertyId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemaId = schemaId;
        ContactIds = contactIds;
        PropertyId = propertyId;
    }
}
