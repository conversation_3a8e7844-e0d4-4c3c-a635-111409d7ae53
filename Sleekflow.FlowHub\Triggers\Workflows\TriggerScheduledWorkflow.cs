using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class TriggerScheduledWorkflow : ITrigger
{
    private readonly IBurstyWorkflowService _burstyWorkflowService;

    public TriggerScheduledWorkflow(IBurstyWorkflowService burstyWorkflowService)
    {
        _burstyWorkflowService = burstyWorkflowService;
    }

    public class TriggerScheduledWorkflowInput : IHasSleekflowCompanyId
    {
        [JsonProperty("workflow_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string WorkflowId { get; set; }

        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public TriggerScheduledWorkflowInput(string workflowId, string sleekflowCompanyId)
        {
            WorkflowId = workflowId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class TriggerScheduledWorkflowOutput
    {
        [JsonProperty("workflow")]
        [System.ComponentModel.DataAnnotations.Required]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public TriggerScheduledWorkflowOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<TriggerScheduledWorkflowOutput> F(TriggerScheduledWorkflowInput input)
    {
        var proxyWorkflow =
            await _burstyWorkflowService.TriggerScheduledWorkflowAsync(input.SleekflowCompanyId, input.WorkflowId);

        return new TriggerScheduledWorkflowOutput(new WorkflowDto(proxyWorkflow));
    }
}