using Sleekflow.EmailHub.Models.Outlook.Communications;
using Sleekflow.Events;

namespace Sleekflow.EmailHub.Models.Outlook.Events;

public class OnOutlookChangeNotificationReceivedEvent : IEvent
{
    public string SleekflowCompanyId { get; set; }

    public OutlookChangeNotification OutlookChangeNotification { get; set; }

    public OnOutlookChangeNotificationReceivedEvent(
        string sleekflowCompanyId,
        OutlookChangeNotification outlookChangeNotification)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        OutlookChangeNotification = outlookChangeNotification;
    }
}