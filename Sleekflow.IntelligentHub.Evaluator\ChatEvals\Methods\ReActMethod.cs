using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using static Sleekflow.IntelligentHub.Evaluator.Constants.MethodNames;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;

public class ReActMethod : IMethod<ChatEvalConfig, ChatEvalOutput>
{
    private readonly IChatService _chatService;

    public ReActMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _chatService = scope.ServiceProvider.GetRequiredService<IChatService>();
    }

    public string MethodName => ReAct;

    public async Task<ChatEvalOutput> CompleteAsync(
        ChatEvalConfig chatEvalConfig,
        ChatMessageContent[]? questionContexts,
        List<string>? sourceFilenames,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig,
        SfChatEntry[]? sfChatEntriesQuestionContexts)
    {
        try
        {
            // Set the test ID in AsyncLocal for the mock plugins to use
            if (ReActEvalTest.CurrentTestId.Value != null)
            {
                ChatEvalTest.CurrentTestId.Value = ReActEvalTest.CurrentTestId.Value;
            }

            // Use SfChatEntriesQuestionContexts if available, otherwise convert questionContexts to SfChatEntries
            IEnumerable<SfChatEntry> sfChatEntries;
            if (sfChatEntriesQuestionContexts != null)
            {
                sfChatEntries = sfChatEntriesQuestionContexts;
            }
            else if (questionContexts != null)
            {
                sfChatEntries = questionContexts.Select(ChatEntriesUtil.ToChatEntries);
            }
            else
            {
                throw new ArgumentException("Either questionContexts or sfChatEntriesQuestionContexts must be provided");
            }

            // Create a clone of the agent config and set the collaboration mode to ReAct
            var reactAgentConfig =
                JsonConvert.DeserializeObject<CompanyAgentConfig>(JsonConvert.SerializeObject(agentConfig))!;
            reactAgentConfig.CollaborationMode = AgentCollaborationModes.ReAct;

            // Use chat service to execute the agent with ReAct mode
            var (asyncEnumerable, sourcesStr, _) = await _chatService.StreamAgentAnswerAsync(
                [..sfChatEntries],
                chatEvalConfig.SleekflowCompanyId,
                replyGenerationContext,
                reactAgentConfig);

            var answerBuilder = new StringBuilder();
            await foreach (var item in asyncEnumerable)
            {
                answerBuilder.Append(item);
            }

            var finalAnswer = answerBuilder.ToString();
            if (string.IsNullOrEmpty(finalAnswer))
            {
                throw new Exception("Failed to generate answer.");
            }

            return new ChatEvalOutput(sourcesStr!, finalAnswer);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ReActMethod: {ex}");
            return new ChatEvalOutput("Error", $"Error executing ReAct agent: {ex.Message}");
        }
        finally
        {
            // Clear the test ID
            ChatEvalTest.CurrentTestId.Value = null;
            ReActEvalTest.CurrentTestId.Value = null;
        }
    }
}