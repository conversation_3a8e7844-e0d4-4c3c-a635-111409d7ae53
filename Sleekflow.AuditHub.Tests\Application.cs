using System.Text;
using System.Xml;
using Alba;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Newtonsoft.Json;
using Sleekflow.Mvc.Tests;
using Formatting = Newtonsoft.Json.Formatting;

namespace Sleekflow.AuditHub.Tests;

[SetUpFixture]
public class Application
{
    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }

    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.AuditHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        Host = await AlbaHost.For<Program>(
            webHostBuilder => { webHostBuilder.ConfigureServices(services => { }); },
            new IAlbaExtension[]
            {
            });

        Host.AfterEachAsync(
            async context =>
            {
                await BaseTestHost.InterceptAfterEachAsync(context);
            });
    }

    public static IAlbaHost Host { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
    }
}