using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Consumers;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.Prompts;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentCalculateLeadScoreConsumerDefinition : ConsumerDefinition<GetAgentCalculateLeadScoreConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentCalculateLeadScoreConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentCalculateLeadScoreConsumer :
    FlowHubAgentGenericConsumer<GetCalculateLeadScoreEvent>,
    IConsumer<GetCalculateLeadScoreEvent>
{
    private readonly IReviewerService _reviewerService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetAgentCalculateLeadScoreConsumer(
        IBus bus,
        IReviewerService reviewerService,
        ILogger<GetAgentCalculateLeadScoreConsumer> logger,
        ICompanyAgentConfigService companyAgentConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
        : base(logger, bus)
    {
        _reviewerService = reviewerService;
        _companyAgentConfigService = companyAgentConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetCalculateLeadScoreEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;
        var agentId = message.AgentId;

        var companyAgentConfig = await _companyAgentConfigService.GetOrDefaultAsync(
            agentId,
            sleekflowCompanyId);

        var calcuteCriteria = companyAgentConfig?.Actions?.CalculateLeadScore?.Criteria;
        if (calcuteCriteria == null || calcuteCriteria.Count == 0)
        {
            throw new Exception(
                $"No calculate lead score criteria found for company {sleekflowCompanyId} and agent {agentId}. " +
                "Please configure the calculate lead score criteria in the company agent config.");
        }


        using var d1 = Serilog.Context.LogContext.PushProperty("SleekflowCompanyId", sleekflowCompanyId);
        using var d2 = Serilog.Context.LogContext.PushProperty("StateId", message.ProxyStateId);

        _logger.LogInformation(
            "Getting calculate lead score for {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(conversationContext, JsonConfig.DefaultLoggingJsonSerializerSettings));

        CalculateLeadScoreResult? calculateLeadScore = null;
        try
        {
            calculateLeadScore = await _reviewerService.GetCalculateLeadScoreAsync(
                conversationContext,
                calcuteCriteria
                );

            _logger.LogInformation("Calculate score: {CalculateLeadScore}", JsonConvert.SerializeObject(calculateLeadScore));

            await _intelligentHubUsageService.RecordUsageAsync(
                sleekflowCompanyId,
                PriceableFeatures.Scoring,
                null,
                new CalculateLeadScoreSnapshot(
                    conversationContext,
                    calculateLeadScore));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get calculate score");
        }

        var response = new GetCalculateLeadScoreEvent.Response(
            calculateLeadScore?.Score ?? 0,
            calculateLeadScore?.Reason ?? "Failed to calculate");

        _logger.LogInformation(
            "Agent calculate score published to OnAgentCompleteStepActivationEvent {Response} {ProxyStateId} {AggregateStepId} {StackEntries}",
            JsonConvert.SerializeObject(response),
            message.ProxyStateId,
            message.AggregateStepId,
            JsonConvert.SerializeObject(message.StackEntries));

        await _bus.Publish(
            new OnAgentCompleteStepActivationEvent(
                message.AggregateStepId,
                message.ProxyStateId,
                message.StackEntries,
                JsonConvert.SerializeObject(response)));
    }
}