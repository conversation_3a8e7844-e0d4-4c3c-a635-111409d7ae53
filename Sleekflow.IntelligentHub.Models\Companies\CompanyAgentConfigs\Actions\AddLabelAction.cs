using Newtonsoft.Json;


namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class Label
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("hashtag")]
    public string Hashtag { get; set; }

    [JsonProperty("hashTagColor")]
    public string HashTagColor { get; set; }

    [JsonConstructor]
    public Label(string id, string hashtag, string hashTagColor)
    {
        Id = id;
        Hashtag = hashtag;
        HashTagColor = hashTagColor;
    }

    public Label(LabelDto dto)
    {
        Id = dto.Id;
        Hashtag = dto.Hashtag;
        HashTagColor = dto.HashTagColor;
    }

    public LabelDto ToDto()
    {
        return new LabelDto(this);
    }
}

public class LabelDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("hashtag")]
    public string Hashtag { get; set; }

    [JsonProperty("hashTagColor")]
    public string HashTagColor { get; set; }

    [JsonConstructor]
    public LabelDto(string id, string hashtag, string hashTagColor)
    {
        Id = id;
        Hashtag = hashtag;
        HashTagColor = hashTagColor;
    }

    public LabelDto(Label label)
    {
        Id = label.Id;
        Hashtag = label.Hashtag;
        HashTagColor = label.HashTagColor;
    }
}

public class AddLabelAction : BaseAction
{
    [JsonProperty("labels")]
    public List<Label> Labels { get; set; }

    [JsonProperty("instructions")]
    public string Instructions { get; set; }

    [JsonConstructor]
    public AddLabelAction(bool enabled, List<Label> labels, string instructions)
        : base(enabled)
    {
        Labels = labels;
        Instructions = instructions;
    }

    public AddLabelAction(AddLabelActionDto dto)
        : base(dto)
    {
        Labels = dto.Labels.Select(l => new Label(l)).ToList();
        Instructions = dto.Instructions;
    }

    public override BaseActionDto ToDto()
    {
        return new AddLabelActionDto(this);
    }
}
