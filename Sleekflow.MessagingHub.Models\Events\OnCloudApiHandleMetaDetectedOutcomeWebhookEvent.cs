using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;

namespace Sleekflow.MessagingHub.Models.Events;

public class OnCloudApiHandleMetaDetectedOutcomeWebhookEvent
{
    public OnCloudApiHandleMetaDetectedOutcomeWebhookEvent(string wabaId, WhatsappCloudApiWebhookMessages webhookMessages, WhatsappCloudApiWebhookMessages.WhatsappCloudApiEntry entry, WhatsappCloudApiWebhookMessages.WhatsappCloudApiChange change)
    {
        WabaId = wabaId;
        WebhookMessages = webhookMessages;
        Entry = entry;
        Change = change;
    }

    public string WabaId { get; set; }

    public WhatsappCloudApiWebhookMessages WebhookMessages { get; set; }

    public WhatsappCloudApiWebhookMessages.WhatsappCloudApiEntry Entry { get; set; }

    public WhatsappCloudApiWebhookMessages.WhatsappCloudApiChange Change { get; set; }
}