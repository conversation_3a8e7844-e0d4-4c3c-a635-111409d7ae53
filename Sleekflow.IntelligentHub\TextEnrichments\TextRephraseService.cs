using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.TextEnrichments;

public interface ITextRephraseService
{
    Task<string> RephraseAsync(string text, string targetType);
}

public class TextRephraseService : ITextRephraseService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger<TextRephraseService> _logger;
    private readonly ITextPlugin _textPlugin;

    public TextRephraseService(
        Kernel kernel,
        ILogger<TextRephraseService> logger,
        ITextPlugin textPlugin)
    {
        _kernel = kernel;
        _logger = logger;
        _textPlugin = textPlugin;
    }

    public async Task<string> RephraseAsync(string text, string targetType)
    {
        switch (targetType)
        {
            case TargetRephraseTypes.Bulletize:
                return await _textPlugin.BulletizeText(_kernel, text);
            case TargetRephraseTypes.Enumerate:
                return await _textPlugin.EnumerateText(_kernel, text);
            case TargetRephraseTypes.GrammarCheck:
                return await _textPlugin.GrammarCorrectText(_kernel, text);
            case TargetRephraseTypes.Lengthen:
                return await _textPlugin.LengthenText(_kernel, text);
            case TargetRephraseTypes.Shorten:
                return await _textPlugin.ShortenText(_kernel, text);
            case TargetRephraseTypes.Simplify:
                return await _textPlugin.SimplifyText(_kernel, text);
        }

        _logger.LogError("Failed to find the corresponding type {TargetType} in TextRephraseService", targetType);
        throw new Exception($"Failed to find the corresponding type {targetType} in TextRephraseService");
    }
}