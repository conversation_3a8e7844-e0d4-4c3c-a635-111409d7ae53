using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Workers.Utils;

public static class Func
{
    public static async Task<IActionResult> Run1Async<TInput, TOutput>(
        HttpRequest req,
        ILogger logger,
        Func<(TInput Input, ILogger Logger), Task<TOutput>> f)
    {
        try
        {
            var input = await DeserializeAndValidateAsync<TInput>(req.Body);

            return Ok(await f.Invoke((input, logger)));
        }
        catch (SfValidationException sfValidationException)
        {
            logger.LogError(sfValidationException, "Caught an SfValidationException");

            return BadRequest(sfValidationException.ValidationResults);
        }
        catch (SfUnauthorizedException sfUnauthorizedException)
        {
            logger.LogError(sfUnauthorizedException, "Caught an SfUnauthorizedException");

            return Error(401, "Unauthorized");
        }
        catch (ErrorCodeException errorCodeException)
        {
            logger.LogError(errorCodeException, "Caught an ErrorCodeException");

            return Error(
                500,
                errorCodeException.Message,
                errorCodeException.ErrorCode,
                JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                    errorCodeException.SerializedContext,
                    JsonConfig.DefaultJsonSerializerSettings));
        }
        catch (Exception exception)
        {
            logger.LogError(exception, "Caught an Exception");

            return Error(500, "Internal exception", ErrorCodeConstant.SfInternalException);
        }
    }

    public static async Task<IActionResult> Run2Async<TInput, TOutput, TBindings>(
        HttpRequest req,
        ILogger logger,
        TBindings bindings,
        Func<(TInput Input, ILogger Logger, TBindings Bindings), Task<TOutput>> f)
    {
        try
        {
            var input = await DeserializeAndValidateAsync<TInput>(req.Body);

            return Ok(await f.Invoke((input, logger, bindings)));
        }
        catch (SfValidationException sfValidationException)
        {
            logger.LogError(sfValidationException, "Caught an SfValidationException");

            return BadRequest(sfValidationException.ValidationResults);
        }
        catch (SfUnauthorizedException sfUnauthorizedException)
        {
            logger.LogError(sfUnauthorizedException, "Caught an SfUnauthorizedException");

            return Error(401, "Unauthorized");
        }
        catch (ErrorCodeException errorCodeException)
        {
            logger.LogError(errorCodeException, "Caught an ErrorCodeException");

            return Error(
                500,
                errorCodeException.Message,
                errorCodeException.ErrorCode,
                JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                    errorCodeException.SerializedContext,
                    JsonConfig.DefaultJsonSerializerSettings));
        }
        catch (Exception exception)
        {
            logger.LogError(exception, "Caught an Exception");

            return Error(500, "Internal exception", ErrorCodeConstant.SfInternalException);
        }
    }

    private static async Task<T> DeserializeAndValidateAsync<T>(Stream body)
    {
        var requestBody = await new StreamReader(body).ReadToEndAsync();
        var validationResults = new List<ValidationResult>();

        var input = requestBody.ToObject<T>();
        if (input == null)
        {
            throw new SfValidationException(new List<ValidationResult>());
        }

        var isValid = Validator.TryValidateObject(
            input,
            new ValidationContext(input, null, null),
            validationResults,
            validateAllProperties: true);
        if (isValid == false)
        {
            throw new SfValidationException(validationResults);
        }

        return input;
    }

    private static OkObjectResult Ok(object? data)
    {
        return new OkObjectResult(
            new Output<object?>(
                true,
                200,
                data,
                message: null,
                errorCode: null,
                errorContext: null));
    }

    private static OkObjectResult Error(
        int statusCode,
        string message,
        int? errorCode = null,
        Dictionary<string, object?>? errorContext = null)
    {
        return new OkObjectResult(
            new Output<object?>(
                false,
                statusCode,
                data: null,
                message: message,
                errorCode: errorCode,
                errorContext: errorContext));
    }

    private static OkObjectResult BadRequest(
        List<ValidationResult> validationResults,
        int? errorCode = null,
        Dictionary<string, object?>? errorContext = null)
    {
        return new OkObjectResult(
            new Output<object?>(
                false,
                400,
                data: validationResults,
                message: "The input is incorrect",
                errorCode: errorCode,
                errorContext: errorContext));
    }
}