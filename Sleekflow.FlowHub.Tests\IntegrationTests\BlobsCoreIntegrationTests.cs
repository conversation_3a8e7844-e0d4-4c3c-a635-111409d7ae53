using Azure.Storage.Blobs.Specialized;
using Sleekflow.FlowHub.Triggers.Blobs;
using Sleekflow.Models.Blobs;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class BlobsCoreIntegrationTests
{
    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }
    }

    [Test]
    public async Task DefaultBlobsTest()
    {
        var mockCompanyId = nameof(DefaultBlobsTest);

        // /Blobs/CreateBlobUploadSasUrls
        var createBlobUploadSasUrlsInput =
            new CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsInput(
                mockCompanyId,
                1,
                BlobTypes.Image);
        var createBlobUploadSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createBlobUploadSasUrlsInput).ToUrl("/Blobs/CreateBlobUploadSasUrls");
            });
        var createBlobUploadSasUrlsOutput =
            await createBlobUploadSasUrlsScenarioResult.ReadAsJsonAsync<
                Output<CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsOutput>>();

        Assert.That(createBlobUploadSasUrlsOutput, Is.Not.Null);
        Assert.That(createBlobUploadSasUrlsOutput!.HttpStatusCode, Is.EqualTo(200));

        var uploadBlobs = createBlobUploadSasUrlsOutput.Data.UploadBlobs;

        Assert.That(uploadBlobs, Is.Not.Null);
        Assert.That(uploadBlobs.Count, Is.EqualTo(1));

        var targetUploadBlob = uploadBlobs[0];

        var uri = new Uri(targetUploadBlob.Url);
        var blockBlobClient = new BlockBlobClient(uri);

        await using var fileStream = File.OpenRead("sleekflow-logo.jfif");
        await blockBlobClient.UploadAsync(fileStream);

        // /Blobs/CreateBlobDownloadSasUrls
        var createBlobDownloadSasUrlsInput =
            new CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsInput(
                mockCompanyId,
                new List<string>
                {
                    targetUploadBlob.BlobName!
                },
                BlobTypes.Image);
        var createBlobDownloadSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createBlobDownloadSasUrlsInput).ToUrl("/Blobs/CreateBlobDownloadSasUrls");
            });
        var createBlobDownloadSasUrlsOutput =
            await createBlobDownloadSasUrlsScenarioResult.ReadAsJsonAsync<
                Output<CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsOutput>>();

        Assert.That(createBlobDownloadSasUrlsOutput, Is.Not.Null);
        Assert.That(createBlobDownloadSasUrlsOutput!.Data.DownloadBlobs.Count, Is.GreaterThan(0));
        Assert.That(createBlobDownloadSasUrlsOutput!.HttpStatusCode, Is.EqualTo(200));
    }
}