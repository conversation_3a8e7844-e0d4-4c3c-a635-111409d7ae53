﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class SubscriptionsCheckOrchestrator
{
    public class SubscriptionsCheckOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTime? LastObjectModificationTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorCustomStatusOutput(
            long count,
            DateTime lastUpdateTime,
            DateTime? lastObjectModificationTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Hubspot_SubscriptionsCheck_Orchestrator")]
    public async Task<SubscriptionsCheckOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var subscriptionsCheckInput = context.GetInput<SubscriptionsCheck.SubscriptionsCheckInput>();
        var startTime = context.CurrentUtcDateTime;

        context.SetCustomStatus(new SubscriptionsCheckOrchestratorCustomStatusOutput(0, startTime, null));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;
        var after = (string?) null;
        DateTime? lastObjectModificationTime;
        while (true)
        {
            var subscriptionsCheckBatchOutput = await context
                .CallActivityAsync<SubscriptionsCheckBatch.SubscriptionsCheckBatchOutput>(
                    "Hubspot_SubscriptionsCheck_Batch",
                    new SubscriptionsCheckBatch.SubscriptionsCheckBatchInput(
                        subscriptionsCheckInput.SleekflowCompanyId,
                        subscriptionsCheckInput.Subscription,
                        after,
                        subscriptionsCheckInput.EntityTypeName,
                        subscriptionsCheckInput.FilterGroups,
                        fieldFilters: subscriptionsCheckInput.FieldFilters),
                    taskOptions);

            totalCount += subscriptionsCheckBatchOutput.Count;
            after = subscriptionsCheckBatchOutput.After;
            lastObjectModificationTime = subscriptionsCheckBatchOutput.LastObjectModificationTime;

            context.SetCustomStatus(
                new SubscriptionsCheckOrchestratorCustomStatusOutput(
                    totalCount,
                    context.CurrentUtcDateTime,
                    lastObjectModificationTime));

            if (subscriptionsCheckBatchOutput.After == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        await context.CallActivityAsync(
            "Hubspot_SubscriptionsCheck_End",
            new SubscriptionsCheckEnd.SubscriptionsCheckEndInput(
                subscriptionsCheckInput.Subscription,
                lastObjectModificationTime,
                startTime));

        return new SubscriptionsCheckOrchestratorOutput(totalCount);
    }
}