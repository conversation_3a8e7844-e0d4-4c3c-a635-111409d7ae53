using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Internals;

public class GetWorkflowStatusInput : IHasSleekflowCompanyId
{
    [JsonConstructor]
    public GetWorkflowStatusInput(string sleekflowCompanyId, string workflowVersionId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionId = workflowVersionId;
    }

    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("workflow_version_id")]
    public string WorkflowVersionId { get; set; }
}