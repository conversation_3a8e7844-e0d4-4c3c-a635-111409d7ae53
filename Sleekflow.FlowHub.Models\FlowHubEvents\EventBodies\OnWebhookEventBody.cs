using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnWebhookEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnWebhook; }
    }

    [Required]
    [JsonProperty("request_body_str")]
    public string RequestBodyStr { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonConstructor]
    public OnWebhookEventBody(
        DateTimeOffset createdAt,
        string requestBodyStr,
        string workflowId)
        : base(createdAt)
    {
        RequestBodyStr = requestBodyStr;
        WorkflowId = workflowId;
    }
}