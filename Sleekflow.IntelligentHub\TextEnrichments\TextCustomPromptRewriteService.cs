using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.TextEnrichments;

public interface ITextCustomPromptRewriteService
{
    Task<string> RewriteAsync(string text, string prompt);
}

public class TextCustomPromptRewriteService : ITextCustomPromptRewriteService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly IPromptPlugin _promptPlugin;

    public TextCustomPromptRewriteService(
        Kernel kernel,
        IPromptPlugin promptPlugin)
    {
        _kernel = kernel;
        _promptPlugin = promptPlugin;
    }

    public async Task<string> RewriteAsync(string text, string prompt)
    {
        return await _promptPlugin.CustomPromptRewriteText(
            _kernel,
            text,
            prompt);
    }
}