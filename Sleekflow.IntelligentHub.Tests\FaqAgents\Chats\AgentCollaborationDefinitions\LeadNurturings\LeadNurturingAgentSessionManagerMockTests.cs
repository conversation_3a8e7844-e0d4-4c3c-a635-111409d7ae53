using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.Tests.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

/// <summary>
/// Unit tests for LeadNurturingAgentSessionManager using mocks instead of an actual Redis connection
/// </summary>
[TestFixture]
public class LeadNurturingAgentSessionManagerMockTests
{
    private Mock<IConnectionMultiplexer> _connectionMultiplexerMock;
    private Mock<IDatabase> _databaseMock;
    private Mock<ILogger<LeadNurturingAgentSessionManager>> _loggerMock;
    private Mock<ICacheConfig> _cacheConfigMock;
    private LeadNurturingAgentSessionManager _sessionManager;

    private const string TestCachePrefix = "TEST-PREFIX";

    [SetUp]
    public void Setup()
    {
        // Mock setup
        _connectionMultiplexerMock = new Mock<IConnectionMultiplexer>();
        _databaseMock = new Mock<IDatabase>();
        _loggerMock = new Mock<ILogger<LeadNurturingAgentSessionManager>>();
        _cacheConfigMock = new Mock<ICacheConfig>();

        // Configure mocks
        _cacheConfigMock.Setup(c => c.CachePrefix).Returns(TestCachePrefix);
        _connectionMultiplexerMock.Setup(c => c.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
            .Returns(_databaseMock.Object);

        // Create session manager with mocks
        _sessionManager = new LeadNurturingAgentSessionManager(
            _loggerMock.Object,
            _connectionMultiplexerMock.Object,
            _cacheConfigMock.Object);
    }

    [Test]
    public async Task RegisterAgentSession_ShouldCreateTransactionCorrectly()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");
        var mockTransaction = new Mock<ITransaction>();

        mockTransaction.Setup(t => t.ExecuteAsync(It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        _databaseMock.Setup(d => d.CreateTransaction(It.IsAny<object>()))
            .Returns(mockTransaction.Object);

        // Act
        var result = await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Assert
        Assert.That(result, Is.True);

        // Verify session data was stored correctly in Redis transaction
        mockTransaction.Verify(
            t => t.StringSetAsync(
                It.Is<RedisKey>(key =>
                    key.ToString().Contains("agent-session") && key.ToString().Contains(groupChatId.SessionId)),
                It.Is<RedisValue>(value => value.ToString().Contains(groupChatId.CompanyId) &&
                                           value.ToString().Contains(groupChatId.ObjectId) &&
                                           value.ToString().Contains(groupChatId.SessionId) &&
                                           value.ToString().Contains("\"HasPerformedActions\":false") &&
                                           value.ToString().Contains("\"ShouldCancel\":false")),
                It.IsAny<TimeSpan?>(),
                It.IsAny<When>(),
                It.IsAny<CommandFlags>()),
            Times.Once);

        // Verify session was added to the set of sessions for this object
        mockTransaction.Verify(
            t => t.SetAddAsync(
                It.Is<RedisKey>(key =>
                    key.ToString().Contains("all-agent-session-ids") && key.ToString().Contains(groupChatId.ObjectId)),
                It.Is<RedisValue>(value => value.ToString() == groupChatId.SessionId),
                It.IsAny<CommandFlags>()),
            Times.Once);

        // Verify expiration was set for the set
        mockTransaction.Verify(
            t => t.KeyExpireAsync(
                It.Is<RedisKey>(key =>
                    key.ToString().Contains("all-agent-session-ids") && key.ToString().Contains(groupChatId.ObjectId)),
                It.IsAny<TimeSpan?>(),
                ExpireWhen.Always,
                CommandFlags.None),
            Times.Once);
    }

    [Test]
    public async Task MarkSessionActionsPerformed_ShouldExecuteCorrectLuaScript()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");
        var expectedSessionKey = $"{TestCachePrefix}:agent-session:{groupChatId.SessionId}";

        _databaseMock.Setup(d => d.ScriptEvaluateAsync(
                It.IsAny<string>(),
                It.Is<RedisKey[]>(keys => keys.Length == 1 && keys[0] == expectedSessionKey),
                It.IsAny<RedisValue[]>(),
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisResult.Create(1L));

        // Act
        await _sessionManager.MarkSessionActionsPerformedAsync(groupChatId);

        // Assert
        _databaseMock.Verify(
            d => d.ScriptEvaluateAsync(
                It.Is<string>(script => script.Contains("HasPerformedActions")),
                It.Is<RedisKey[]>(keys => keys.Length == 1 && keys[0] == expectedSessionKey),
                It.Is<RedisValue[]>(values => values.Length == 1),
                It.IsAny<CommandFlags>()),
            Times.Once);
    }

    [Test]
    public async Task ShouldCancelSession_WhenSessionExists_ShouldReturnCorrectValue()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");
        var expectedSessionKey = $"{TestCachePrefix}:agent-session:{groupChatId.SessionId}";

        var sessionState = new AgentSessionState(
            groupChatId.CompanyId,
            groupChatId.ObjectId,
            groupChatId.SessionId,
            hasPerformedActions: true,
            shouldCancel: true);

        var serializedState = JsonConvert.SerializeObject(sessionState);

        _databaseMock.Setup(d => d.StringGetAsync(
                It.Is<RedisKey>(key => key == expectedSessionKey),
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(serializedState);

        // Act
        var result = await _sessionManager.ShouldCancelSessionAsync(groupChatId);

        // Assert
        Assert.That(result, Is.True);
        _databaseMock.Verify(
            d => d.StringGetAsync(
                It.Is<RedisKey>(key => key == expectedSessionKey),
                It.IsAny<CommandFlags>()),
            Times.Once);
    }

    [Test]
    public async Task ShouldCancelSession_WhenSessionDoesNotExist_ShouldReturnFalse()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");

        _databaseMock.Setup(d => d.StringGetAsync(
                It.IsAny<RedisKey>(),
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _sessionManager.ShouldCancelSessionAsync(groupChatId);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CancelPreviousSessions_ShouldExecuteCorrectLuaScript()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");
        var expectedSetKey = $"{TestCachePrefix}:all-agent-session-ids:{groupChatId.ObjectId}";

        _databaseMock.Setup(d => d.ScriptEvaluateAsync(
                It.IsAny<string>(),
                It.Is<RedisKey[]>(keys => keys.Length == 1 && keys[0] == expectedSetKey),
                It.IsAny<RedisValue[]>(),
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisResult.Create(2L)); // Simulating 2 sessions canceled

        // Act
        await _sessionManager.CancelPreviousSessionsAsync(groupChatId);

        // Assert
        _databaseMock.Verify(
            d => d.ScriptEvaluateAsync(
                It.Is<string>(script => script.Contains("ShouldCancel") && script.Contains("HasPerformedActions")),
                It.Is<RedisKey[]>(keys => keys.Length == 1 && keys[0] == expectedSetKey),
                It.Is<RedisValue[]>(values =>
                    values.Length == 3 &&
                    values[0] == groupChatId.SessionId &&
                    values[2].ToString().Contains(TestCachePrefix)),
                It.IsAny<CommandFlags>()),
            Times.Once);

        // Verify we logged the cancel count
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Canceled 2 previous agent sessions")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Test]
    public async Task RegisterAgentSession_WhenTransactionFails_ShouldReturnFalse()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");
        var mockTransaction = new Mock<ITransaction>();

        mockTransaction.Setup(t => t.ExecuteAsync(It.IsAny<CommandFlags>()))
            .ReturnsAsync(false);

        _databaseMock.Setup(d => d.CreateTransaction(It.IsAny<object>()))
            .Returns(mockTransaction.Object);

        // Act
        var result = await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Assert
        Assert.That(result, Is.False);

        // Verify warning was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Failed to register agent session transaction")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Test]
    public async Task CancelPreviousSessions_WhenExceptionOccurs_ShouldHandleGracefully()
    {
        // Arrange
        var groupChatId = new GroupChatId("company123", "object123", "session123");

        _databaseMock.Setup(d => d.ScriptEvaluateAsync(
                It.IsAny<string>(),
                It.IsAny<RedisKey[]>(),
                It.IsAny<RedisValue[]>(),
                It.IsAny<CommandFlags>()))
            .ThrowsAsync(new RedisException("Simulated Redis exception"));

        // Act & Assert - Should not throw the exception
        await _sessionManager.CancelPreviousSessionsAsync(groupChatId);

        // Verify error was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Failed to cancel previous sessions")),
                It.IsAny<RedisException>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
}