using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetIsCompanyHasAiPocPlanOutput
{
    [JsonProperty("is_company_has_ai_poc_plan")]
    public bool IsCompanyHasAiPocPlan { get; set; }

    [JsonConstructor]
    public GetIsCompanyHasAiPocPlanOutput(bool isCompanyHasAiPocPlan)
    {
        IsCompanyHasAiPocPlan = isCompanyHasAiPocPlan;
    }
}