﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(ControllerNames.Events)]
public class OnContactManuallyEnrolledEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnContactManuallyEnrolledEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnContactManuallyEnrolledEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnContactManuallyEnrolledEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnContactManuallyEnrolledEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnContactManuallyEnrolledEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnContactManuallyEnrolledEventOutput
    {
    }

    public async Task<OnContactManuallyEnrolledEventOutput> F(
        OnContactManuallyEnrolledEventInput onContactManuallyEnrolledEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onContactManuallyEnrolledEventInput.EventBody,
                onContactManuallyEnrolledEventInput.ContactId,
                "Contact",
                onContactManuallyEnrolledEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onContactManuallyEnrolledEventInput.SleekflowCompanyId));

        return new OnContactManuallyEnrolledEventOutput();
    }
}