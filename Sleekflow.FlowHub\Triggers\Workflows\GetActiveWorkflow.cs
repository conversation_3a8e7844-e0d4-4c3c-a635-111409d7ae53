using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetActiveWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public GetActiveWorkflow(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class GetActiveWorkflowInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonConstructor]
        public GetActiveWorkflowInput(
            string sleekflowCompanyId,
            string workflowId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
        }
    }

    public class GetActiveWorkflowOutput
    {
        [JsonProperty("active_workflow")]
        public WorkflowDto? ActiveWorkflow { get; set; }

        [JsonConstructor]
        public GetActiveWorkflowOutput(WorkflowDto? activeWorkflow)
        {
            ActiveWorkflow = activeWorkflow;
        }
    }

    public async Task<GetActiveWorkflowOutput> F(GetActiveWorkflowInput getActiveWorkflowInput)
    {
        var activeWorkflow = await _workflowService.GetActiveWorkflowAsync(
            getActiveWorkflowInput.WorkflowId,
            getActiveWorkflowInput.SleekflowCompanyId);

        return new GetActiveWorkflowOutput(
            activeWorkflow == null ? null : new WorkflowDto(activeWorkflow));
    }
}