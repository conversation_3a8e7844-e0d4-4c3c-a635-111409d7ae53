using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.CommerceHub.Stores.ShopifyStores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Stores.ShopifyStores;

[TriggerGroup(ControllerNames.ShopifyStores)]
public class IntegrateOutOfBoxShopifyStore
    : ITrigger<
        IntegrateOutOfBoxShopifyStore.IntegrateOutOfBoxShopifyStoreInput,
        IntegrateOutOfBoxShopifyStore.IntegrateOutOfBoxShopifyStoreOutput>
{
    private readonly IShopifyStoreService _shopifyStoreService;

    public IntegrateOutOfBoxShopifyStore(
        IShopifyStoreService shopifyStoreService)
    {
        _shopifyStoreService = shopifyStoreService;
    }

    public class IntegrateOutOfBoxShopifyStoreInput :
        ShopifyStoreInput, IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("shop_access_token")]
        public string ShopAccessToken { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public IntegrateOutOfBoxShopifyStoreInput(
            string sleekflowCompanyId,
            string shopifyUrl,
            string shopAccessToken,
            bool isViewEnabled,
            bool isPaymentEnabled,
            StoreSubscriptionStatus? subscriptionStatus,
            Dictionary<string, object?> metadata,
            StoreTemplateDict templateDict,
            ShopifySyncConfig shopifySyncConfig,
            ShopifyPaymentConfig shopifyPaymentConfig,
            ShopifySyncStatus? shopifySyncStatus,
            List<ShopifyMessageTemplate>? shopifyMessageTemplates,
            bool? isShopifyBillingOwner,
            DateTimeOffset? chargeUpdatedAt,
            string? chargeId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                shopifyUrl,
                isViewEnabled,
                isPaymentEnabled,
                subscriptionStatus,
                metadata,
                templateDict,
                shopifySyncConfig,
                shopifyPaymentConfig,
                shopifySyncStatus,
                shopifyMessageTemplates,
                isShopifyBillingOwner,
                chargeUpdatedAt,
                chargeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ShopAccessToken = shopAccessToken;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class IntegrateOutOfBoxShopifyStoreOutput
    {
        [JsonProperty("store")]
        public StoreDto Store { get; set; }

        [JsonConstructor]
        public IntegrateOutOfBoxShopifyStoreOutput(StoreDto store)
        {
            Store = store;
        }
    }

    public async Task<IntegrateOutOfBoxShopifyStoreOutput> F(
        IntegrateOutOfBoxShopifyStoreInput integrateOutOfBoxShopifyStoreInput)
    {
        var shopifyStore = await _shopifyStoreService.CreateAndGetOutOfBoxShopifyStoreAsync(
            integrateOutOfBoxShopifyStoreInput.SleekflowCompanyId,
            integrateOutOfBoxShopifyStoreInput.ShopifyUrl,
            integrateOutOfBoxShopifyStoreInput.ShopAccessToken,
            integrateOutOfBoxShopifyStoreInput.IsViewEnabled,
            integrateOutOfBoxShopifyStoreInput.IsPaymentEnabled,
            integrateOutOfBoxShopifyStoreInput.SubscriptionStatus,
            integrateOutOfBoxShopifyStoreInput.Metadata,
            integrateOutOfBoxShopifyStoreInput.TemplateDict,
            integrateOutOfBoxShopifyStoreInput.ShopifySyncConfig,
            integrateOutOfBoxShopifyStoreInput.ShopifyPaymentConfig,
            integrateOutOfBoxShopifyStoreInput.ShopifySyncStatus,
            integrateOutOfBoxShopifyStoreInput.ShopifyMessageTemplates,
            integrateOutOfBoxShopifyStoreInput.IsShopifyBillingOwner,
            integrateOutOfBoxShopifyStoreInput.ChargeUpdatedAt,
            integrateOutOfBoxShopifyStoreInput.ChargeId,
            integrateOutOfBoxShopifyStoreInput.SleekflowStaffId,
            integrateOutOfBoxShopifyStoreInput.SleekflowStaffTeamIds);

        return new IntegrateOutOfBoxShopifyStoreOutput(new StoreDto(shopifyStore));
    }
}