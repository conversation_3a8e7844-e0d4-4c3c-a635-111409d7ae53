using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Blobs;
using Sleekflow.CommerceHub.CustomCatalogs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class
    PatchCustomCatalogFileProcessStatus
    : ITrigger<
        PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusInput,
        PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusOutput>
{
    private readonly ICustomCatalogFileService _customCatalogFileService;
    private readonly IBlobService _blobService;

    public PatchCustomCatalogFileProcessStatus(
        ICustomCatalogFileService customCatalogFileService,
        IBlobService blobService)
    {
        _customCatalogFileService = customCatalogFileService;
        _blobService = blobService;
    }

    public class PatchCustomCatalogFileProcessStatusInput
    {
        [JsonProperty("id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string Id { get; set; }

        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("file_process_status")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FileProcessStatus { get; set; }

        [JsonConstructor]
        public PatchCustomCatalogFileProcessStatusInput(
            string id,
            string sleekflowCompanyId,
            string fileProcessStatus)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            FileProcessStatus = fileProcessStatus;
        }
    }

    public class PatchCustomCatalogFileProcessStatusOutput
    {
    }

    public async Task<PatchCustomCatalogFileProcessStatusOutput> F(
        PatchCustomCatalogFileProcessStatusInput patchCustomCatalogFileProcessStatusInput)
    {
        if (patchCustomCatalogFileProcessStatusInput.FileProcessStatus == CustomCatalogFileProcessStatuses.Completed)
        {
            var customCatalogFile = await _customCatalogFileService.GetCustomCatalogFileAsync(
                patchCustomCatalogFileProcessStatusInput.Id,
                patchCustomCatalogFileProcessStatusInput.SleekflowCompanyId);

            var customCatalogFileProcessLogger = await new CustomCatalogFileProcessLogger(
                    _blobService,
                    customCatalogFile.SleekflowCompanyId,
                    customCatalogFile.StoreId,
                    customCatalogFile.Id)
                .InitAsync();
            if (await customCatalogFileProcessLogger.AnyAsync())
            {
                await _customCatalogFileService.PatchCustomCatalogFileFileProcessStatusAsync(
                    patchCustomCatalogFileProcessStatusInput.Id,
                    patchCustomCatalogFileProcessStatusInput.SleekflowCompanyId,
                    CustomCatalogFileProcessStatuses.Failed);
            }
            else
            {
                await _customCatalogFileService.PatchCustomCatalogFileFileProcessStatusAsync(
                    patchCustomCatalogFileProcessStatusInput.Id,
                    patchCustomCatalogFileProcessStatusInput.SleekflowCompanyId,
                    CustomCatalogFileProcessStatuses.Completed);
            }
        }
        else
        {
            await _customCatalogFileService.PatchCustomCatalogFileFileProcessStatusAsync(
                patchCustomCatalogFileProcessStatusInput.Id,
                patchCustomCatalogFileProcessStatusInput.SleekflowCompanyId,
                patchCustomCatalogFileProcessStatusInput.FileProcessStatus);
        }

        return new PatchCustomCatalogFileProcessStatusOutput();
    }
}