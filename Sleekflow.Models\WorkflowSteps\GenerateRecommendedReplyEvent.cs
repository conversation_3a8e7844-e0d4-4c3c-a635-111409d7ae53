using Newtonsoft.Json;
using Sleekflow.Models.Chats;

namespace Sleekflow.Models.WorkflowSteps;

public class GenerateRecommendedReplyEvent
{
    [JsonProperty("recommended_reply_step_id")]
    public string RecommendReplyStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("intelligent_hub_usage_filter_from_date_time")]
    public DateTimeOffset? IntelligentHubUsageFilterFromDateTime { get; set; }

    [JsonProperty("intelligent_hub_usage_filter_to_date_time")]
    public DateTimeOffset? IntelligentHubUsageFilterToDateTime { get; set; }

    [JsonConstructor]
    public GenerateRecommendedReplyEvent(
        string recommendReplyStepId,
        string proxyStateId,
        string sleekflowCompanyId,
        List<SfChatEntry> conversationContext,
        DateTimeOffset? intelligentHubUsageFilterFromDateTime = null,
        DateTimeOffset? intelligentHubUsageFilterToDateTime = null)
    {
        RecommendReplyStepId = recommendReplyStepId;
        ProxyStateId = proxyStateId;
        SleekflowCompanyId = sleekflowCompanyId;
        ConversationContext = conversationContext;
        IntelligentHubUsageFilterFromDateTime = intelligentHubUsageFilterFromDateTime;
        IntelligentHubUsageFilterToDateTime = intelligentHubUsageFilterToDateTime;
    }
}