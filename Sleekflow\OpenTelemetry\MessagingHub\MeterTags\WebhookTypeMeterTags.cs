using Sleekflow.OpenTelemetry.Meters.Interfaces;

namespace Sleekflow.OpenTelemetry.MessagingHub.MeterTags;

public class WebhookTypeMeterTags : IMeterTags
{
    private string WebhookType { get; }

    private string? MessageType { get; set; }

    private string? MessageStatus { get; set; }


    public WebhookTypeMeterTags(string webhookType, string? messageType = null, string? messageStatus = null)
    {
        WebhookType = webhookType;
        MessageType = messageType;
        MessageStatus = messageStatus;
    }

    public KeyValuePair<string, object>[] ToMeterTags()
    {
        if (MessageType != null)
        {
            return new[]
            {
                new KeyValuePair<string, object>("type", WebhookType),
                new KeyValuePair<string, object>("message_type", MessageType)
            };
        }

        if (MessageStatus != null)
        {
            return new[]
            {
                new KeyValuePair<string, object>("type", WebhookType),
                new KeyValuePair<string, object>("message_status", MessageStatus)
            };
        }

        return new[]
        {
            new KeyValuePair<string, object>("type", WebhookType)
        };
    }
}