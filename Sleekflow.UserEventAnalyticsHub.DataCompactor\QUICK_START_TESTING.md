# Quick Start: Testing DataCompactor with Real Data

## 🚀 Get Started in 5 Minutes

### Step 1: Set Up Environment Variables
```bash
# Interactive setup (recommended)
./setup-test-env.sh

# Or set manually (Migration Mode)
export ANALYTICS_STORAGE_ACCOUNT_NAME="s1ca15031"
export ANALYTICS_STORAGE_ACCOUNT_KEY="****************************************************************************************"
export EVENTS_CONTAINER_NAME="events"
export POSTGRES_CONNECTION_STRING='Host=sleekflow-ueah-b53fcf41.postgres.database.azure.com;Database=userevents;Username=sleekflowadmin;Password=*******$n}[Dx>!IxhZ6yGt2;SSL Mode=Require;Trust Server Certificate=true;'
export POSTGRES_SERVER_NAME="ueah"
export POSTGRES_DATABASE_NAME="postgres"

# Additional for SQL Job Testing
export ANALYTICS_STORAGE_CONN_STR="DefaultEndpointsProtocol=https;AccountName=s1ca15031;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
export RESULTS_CONTAINER_NAME="results"
```

### Step 2: Test Connectivity
```bash
# Verify all services can connect
dotnet run

# Expected output:
# - "Testing connectivity to all services..."
# - "SqlJobProcessingService is available for testing"
# - "All connectivity tests ****ed."
```

### Step 3: Discovery Mode (Safe Testing)
```bash
# Discover companies and files without processing
export COMPACTOR_DISCOVERY_ONLY="true"
export COMPACTOR_SHOW_FILE_DETAILS="true"
export COMPACTOR_MAX_DISCOVERY_COMPANIES="3"
export COMPACTOR_MAX_FILES_TO_SHOW="5"

dotnet run
```

### Step 4: Test Schema Creation
```bash
# Test database schema creation
export COMPACTOR_TEST_SCHEMA_CREATION="true"
dotnet run
```

### Step 5: Small-Scale Data Migration
```bash
# Process 1 file from 1 company
unset COMPACTOR_DISCOVERY_ONLY
unset COMPACTOR_TEST_SCHEMA_CREATION
export COMPACTOR_MAX_CONCURRENT_COMPANIES="1"
export COMPACTOR_FILE_BATCH_SIZE="1"

dotnet run
```

### Step 6: 🆕 SQL Job Performance Testing
```bash
# Test PostgreSQL query performance
unset COMPACTOR_DISCOVERY_ONLY
unset COMPACTOR_TEST_SCHEMA_CREATION
export COMPACTOR_SQL_JOB_TEST="true"
export TEST_COMPANY_ID="39ee5f12-7997-45fa-a960-e4feecba425c"
export TEST_SQL_QUERY=" SELECT strftime(make_date(year, month, day), '%m/%d') as date, count(case when eventType = 'r6rSbKlbkMGGPxdZ' and objectType = 'Contact' then 1 end) as \"Read Campaign\" , count(case when eventType = '3RYS7eemLEzXXX7k' and objectType = 'Contact' then 1 end) as \"regression 60 #2\" , count(case when eventType = 'XbytR5zn73vRW5Pd' and objectType = 'Contact' then 1 end) as \"[Don't Delete] Testing Purpose\" FROM events WHERE make_date(year , month , day) BETWEEN '2025-06-29' AND '2025-07-06' GROUP BY year, month, day ORDER BY year, month, day "

dotnet run
```

## 🧪 Testing Environment Variables

### Discovery Mode
```bash
export COMPACTOR_DISCOVERY_ONLY="true"           # Run discovery without processing
export COMPACTOR_SHOW_FILE_DETAILS="true"        # Show file details
export COMPACTOR_MAX_DISCOVERY_COMPANIES="5"     # Limit companies shown
export COMPACTOR_MAX_FILES_TO_SHOW="3"           # Limit files shown per company
export COMPACTOR_TEST_SCHEMA_CREATION="true"     # Test schema creation
```

### Processing Mode
```bash
export COMPACTOR_MAX_CONCURRENT_COMPANIES="1"    # Process 1 company at a time
export COMPACTOR_FILE_BATCH_SIZE="10"            # Process 10 files per batch
export COMPACTOR_RETRY_ATTEMPTS="3"              # Retry failed operations
export COMPACTOR_PROCESSING_TIMEOUT_MINUTES="30" # Timeout for processing
```

### 🆕 SQL Job Testing Mode
```bash
export COMPACTOR_SQL_JOB_TEST="true"             # Enable SQL job testing
export TEST_COMPANY_ID="company-123"             # Company ID to test
export TEST_JOB_ID="job-456"                     # Job ID for testing (optional)
export TEST_SQL_QUERY="SELECT COUNT(*) FROM events" # SQL query to test (optional)
```

## 📊 Quick Validation

### Check Migrated Data
```sql
-- Connect to PostgreSQL
psql -d sleekflow_events

-- List created tables
SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE 'events_%';

-- Check data in a company table (replace 12345 with actual company ID)
SELECT COUNT(*) FROM events_12345;
SELECT * FROM events_12345 LIMIT 5;

-- Check migration tracking
SELECT * FROM file_migration_history_12345 WHERE migration_status = 'completed';
```

### 🆕 SQL Job Performance Validation
```bash
# Check application logs for performance metrics
grep "Short SQL job completed" logs/application.log
grep "Returned.*results" logs/application.log

# Look for timing information like:
# "Short SQL job completed in 00:02.345"
# "Returned 1 results"
```

### Common Issues & Solutions

**Connection Failed:**
```bash
# Test individual connections
ping your-postgres-host
telnet your-postgres-host 5432
```

**Out of Memory:**
```bash
export COMPACTOR_FILE_BATCH_SIZE="1"
export COMPACTOR_MAX_CONCURRENT_COMPANIES="1"
```

**Permission Denied:**
```bash
# Verify Azure credentials
az storage account show --name $ANALYTICS_STORAGE_ACCOUNT_NAME

# Verify PostgreSQL permissions
psql -d sleekflow_events -c "SELECT current_user;"
```

**SQL Job Test Fails:**
```bash
# Ensure PostgreSQL connection string is correct
export POSTGRES_CONNECTION_STRING="Host=host;Database=db;Username=user;Password=****"

# Verify company has migrated data
psql -d sleekflow_events -c "SELECT COUNT(*) FROM events_yourcompanyid;"

# Check if company ID format is correct (sanitized)
echo "events_$(echo 'your-company-id' | tr -c 'a-zA-Z0-9_' '_' | tr '[:upper:]' '[:lower:]')"
```

## 🔄 Iterative Testing Approach

1. **Start Small**: 1 company, 1 file
2. **Scale Gradually**: 1 company, 10 files
3. **Multi-Company**: 2 companies, 5 files each
4. **Performance Testing**: Test SQL queries on migrated data
5. **Production-Like**: Full dataset for 1 company

## 📈 Success Indicators

### Migration Success
- ✅ Connectivity tests ****
- ✅ Companies and files discovered
- ✅ Schema creation successful
- ✅ Data appears in PostgreSQL
- ✅ Migration tracking records created
- ✅ No errors in logs

### 🆕 Performance Testing Success
- ✅ SQL Job Processing Service loads successfully
- ✅ DuckDB connects to PostgreSQL
- ✅ SQL queries execute without errors
- ✅ Query execution time < 2 seconds (target)
- ✅ Results match expected data structure
- ✅ Performance improvement vs parquet scanning

## 🆘 Need Help?

See the comprehensive [TESTING_GUIDE.md](./TESTING_GUIDE.md) for detailed instructions, troubleshooting, and advanced testing scenarios.

## 🎯 Next Steps

After successful testing:
1. Scale up to full production dataset
2. Monitor performance and adjust batch sizes
3. Set up production monitoring
4. Schedule regular migrations for new data