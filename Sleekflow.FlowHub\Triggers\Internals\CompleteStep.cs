using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Steps;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class CompleteStep : ITrigger
{
    private readonly IStepExecutorActivator _stepExecutorActivator;

    public CompleteStep(
        IStepExecutorActivator stepExecutorActivator)
    {
        _stepExecutorActivator = stepExecutorActivator;
    }

    public class CompleteStepInput : Sleekflow.FlowHub.Models.Internals.CompleteStepInput
    {
        [JsonConstructor]
        public CompleteStepInput(string stateId, string stepId, Stack<StackEntry> stackEntries, string executionStatus)
            : base(stateId, stepId, stackEntries, executionStatus)
        {
        }
    }

    public class CompleteStepOutput : Sleekflow.FlowHub.Models.Internals.CompleteStepOutput
    {
        [JsonConstructor]
        public CompleteStepOutput()
        {
        }
    }

    public async Task<CompleteStepOutput> F(CompleteStepInput completeStepInput)
    {
        await _stepExecutorActivator.CompleteStepAsync(
            completeStepInput.StateId,
            completeStepInput.StepId,
            completeStepInput.StackEntries,
            completeStepInput.ExecutionStatus,
            null);

        return new CompleteStepOutput();
    }
}