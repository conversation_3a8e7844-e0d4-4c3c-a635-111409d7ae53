﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Evaluator.Constants;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

namespace Sleekflow.IntelligentHub.Evaluator.AddLabel.Methods;

public class DefaultAddLabelMethod : IAddLabelMethod<AddLabelTestResult>
{
    private readonly IReviewerService _reviewerService;

    public string MethodName => MethodNames.AddLabel;

    public DefaultAddLabelMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _reviewerService = scope.ServiceProvider.GetRequiredService<IReviewerService>();
    }

    public async Task<AddLabelTestResult> CompleteAsync(
        ChatMessageContent[] questionContexts,
        List<Label> labels,
        string instructions)
    {
        var chatEntries = questionContexts.Select(ChatEntriesUtil.ToChatEntries).ToList();
        var addLabelResult = await _reviewerService.GetAddLabelAsync(chatEntries, labels, instructions);
        
        return new AddLabelTestResult(
            MethodName,
            addLabelResult,
            0);
    }
}