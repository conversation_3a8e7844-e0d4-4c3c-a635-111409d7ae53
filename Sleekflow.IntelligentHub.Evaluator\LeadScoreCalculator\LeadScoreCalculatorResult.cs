using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator;

public class LeadScoreCalculatorResult(
    string agentName,
    CalculateLeadScoreResult? answer,
    long elapsedMilliseconds)
{
    public string AgentName { get; init; } = agentName;

    public CalculateLeadScoreResult? Answer { get; init; } = answer;

    public long ElapsedMilliseconds { get; init; } = elapsedMilliseconds;

    public double CalculateMatchingScore(EvaluatedScore expectedScore)
    {
        if (Answer == null) return 0.0;

        double scoreDifference = Math.Abs(Answer.Score - expectedScore.Score);
        return Math.Max(0, (100 - scoreDifference) / 100.0);
    }
}