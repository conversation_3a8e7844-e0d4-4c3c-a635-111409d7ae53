using System.Text.RegularExpressions;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using MessageBody = Sleekflow.FlowHub.Models.Messages.MessageBody;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISendMessageStepExecutor : IStepExecutor
{
}

public class SendMessageStepExecutor
    : GeneralStepExecutor<CallStep<SendMessageStepArgs>>,
        ISendMessageStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;
    private readonly IBus _bus;
    private readonly ILogger<SendMessageStepExecutor> _logger;

    public SendMessageStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander,
        IBus bus,
        ILogger<SendMessageStepExecutor> logger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
        _bus = bus;
        _logger = logger;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var args = await GetArgs(callStep, state);

            _logger.LogInformation("Looking for streaming variable in {Expression}", callStep.Args.MessageBodyExpr);
            var streamingVariableMatch = Regex.Match(
                callStep.Args.MessageBodyExpr,
                @"{{[^{}]*streaming_var[^{}]*\\""([^{}]*)\\""[^{}]*}}");

            if (args.MessageBody.TextMessage != null && streamingVariableMatch.Success)
            {
                await SendMessageStreaming(state, step, stackEntries, streamingVariableMatch, onActivatedAsync, args);
            }
            else
            {
                await SendMessage(state, onActivatedAsync, args);
            }
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task SendMessageStreaming(
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Match streamingVariableMatch,
        Func<ProxyState, string, Task> onActivatedAsync,
        SendMessageInput args)
    {
        _logger.LogInformation(
            "SendMessageStreaming: {RecommendedReplyStepId}, {StateId}",
            streamingVariableMatch.Groups[1].Value,
            state.Id);

        // the placeholder is in the format "{{ streaming_var "guid" }}"
        // the guid is in regex group 1
        var recommendedReplyStepId = streamingVariableMatch.Groups[1].Value;

        var cachedStreamResults = state.SysVarDict[recommendedReplyStepId] as string;
        if (!string.IsNullOrEmpty(cachedStreamResults))
        {
            _logger.LogInformation("Cache result found: {CachedStreamResults}", cachedStreamResults);

            // the recommend reply node has been streamed before and the results are already cached in sys var dict
            args.MessageBody.TextMessage!.Text = cachedStreamResults;

            await _coreCommander.ExecuteAsync(
                state.Origin,
                "SendMessage",
                args);

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        else
        {
            await _bus.Publish(
                new StartStreamingSendMessageEvent(
                    recommendedReplyStepId,
                    state.Id,
                    args,
                    step.Id,
                    stackEntries));

            // delay step complete in StreamingRecommendedReplyStateMachine
        }
    }

    private async Task SendMessage(
        ProxyState state,
        Func<ProxyState, string, Task> onActivatedAsync,
        SendMessageInput args)
    {
        _logger.LogInformation("SendMessage");

        await _coreCommander.ExecuteAsync(
            state.Origin,
            "SendMessage",
            args);

        await onActivatedAsync(state, StepExecutionStatuses.Complete);
    }

    private async Task<SendMessageInput> GetArgs(
        CallStep<SendMessageStepArgs> callStep,
        ProxyState state)
    {
        var channel =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ChannelExpr)
                      ?? callStep.Args.ChannelExpr);

        var messageType =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.MessageTypeExpr)
                      ?? callStep.Args.MessageTypeExpr);

        var messageBodyObj =
            await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.MessageBodyExpr)
            ?? callStep.Args.MessageBodyExpr;

        var deliveryType = !string.IsNullOrWhiteSpace(callStep.Args.DeliveryTypeExpr)
            ? (int?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                callStep.Args.DeliveryTypeExpr!)
            : null;

        // This is to impersonate a staff when sending a message.
        var staffId = !string.IsNullOrWhiteSpace(callStep.Args.StaffIdExpr)
            ? (string?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                callStep.Args.StaffIdExpr!)
            : null;

        // This is to specify the delivery type for AI Agent recommended replies.
        if (deliveryType == null
            && callStep.Args.MessageBodyExpr != null
            && callStep.Args.MessageBodyExpr.Contains("recommended_reply"))
        {
            deliveryType = 6;
        }

        switch (channel)
        {
            case "whatsappcloudapi":
                var fromToFromPhoneNumber =
                    callStep.Args.FromTo.FromPhoneNumberExpr != null
                        ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                        state,
                                        callStep.Args.FromTo.FromPhoneNumberExpr)
                                    ?? callStep.Args.FromTo.FromPhoneNumberExpr)
                        : null;

                var fromToToPhoneNumber =
                    callStep.Args.FromTo.ToPhoneNumberExpr != null
                        ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                        state,
                                        callStep.Args.FromTo.ToPhoneNumberExpr)
                                    ?? callStep.Args.FromTo.ToPhoneNumberExpr)
                        : null;

                var fromToToContactId =
                    callStep.Args.FromTo.ToContactIdExpr != null
                        ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                        state,
                                        callStep.Args.FromTo.ToContactIdExpr)
                                    ?? callStep.Args.FromTo.ToContactIdExpr)
                        : null;

                var fromToFromChannelId =
                    callStep.Args.FromTo.FromChannelIdExpr != null
                        ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                        state,
                                        callStep.Args.FromTo.FromChannelIdExpr)
                                    ?? callStep.Args.FromTo.FromChannelIdExpr)
                        : null;

                return new SendMessageInput(
                    state.Id,
                    state.Identity,
                    channel,
                    new WhatsappCloudApiSendMessageInputFromTo(
                        fromToFromPhoneNumber,
                        fromToToPhoneNumber,
                        fromToToContactId,
                        fromToFromChannelId),
                    messageType,
                    JsonConvert.DeserializeObject<MessageBody>(
                        JsonConvert.SerializeObject(
                            messageBodyObj,
                            JsonConfig.DefaultJsonSerializerSettings),
                        JsonConfig.DefaultJsonSerializerSettings)!,
                    deliveryType,
                    staffId);

            case "facebook":
                var fromToFromFacebookPageId = callStep.Args.FromTo.FromFacebookPageIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.FromFacebookPageIdExpr)
                                ?? callStep.Args.FromTo.FromFacebookPageIdExpr)
                    : null;

                var fromToToFacebookId = callStep.Args.FromTo.ToFacebookIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.ToFacebookIdExpr)
                                ?? callStep.Args.FromTo.ToFacebookIdExpr)
                    : null;

                var fromToFromFacebookPostId = callStep.Args.FromTo.FromFacebookPostIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.FromFacebookPostIdExpr)
                                ?? callStep.Args.FromTo.FromFacebookPostIdExpr)
                    : null;

                var fromToToFacebookCommentId = callStep.Args.FromTo.ToFacebookCommentIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.ToFacebookCommentIdExpr)
                                ?? callStep.Args.FromTo.ToFacebookCommentIdExpr)
                    : null;

                return new SendMessageInput(
                    state.Id,
                    state.Identity,
                    channel,
                    new FacebookPageMessengerSendMessageInputFromTo(
                        fromToFromFacebookPageId,
                        fromToToFacebookId,
                        new FacebookPostCommentDetailsObject(fromToFromFacebookPostId, fromToToFacebookCommentId)),
                    messageType,
                    JsonConvert.DeserializeObject<MessageBody>(
                        JsonConvert.SerializeObject(
                            messageBodyObj,
                            JsonConfig.DefaultJsonSerializerSettings),
                        JsonConfig.DefaultJsonSerializerSettings)!,
                    deliveryType,
                    staffId);

            case "instagram":
                var fromToFromInstagramPageId = callStep.Args.FromTo.FromInstagramPageIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.FromInstagramPageIdExpr)
                                ?? callStep.Args.FromTo.FromInstagramPageIdExpr)
                    : null;

                var fromToToInstagramId = callStep.Args.FromTo.ToInstagramIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.ToInstagramIdExpr)
                                ?? callStep.Args.FromTo.ToInstagramIdExpr)
                    : null;

                var fromToFromInstagramMediaId = callStep.Args.FromTo.FromInstagramMediaIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.FromInstagramMediaIdExpr)
                                ?? callStep.Args.FromTo.FromInstagramMediaIdExpr)
                    : null;

                var fromToToInstagramCommentId = callStep.Args.FromTo.ToInstagramCommentIdExpr != null
                    ? (string) (await _stateEvaluator.EvaluateExpressionAsync(
                                    state,
                                    callStep.Args.FromTo.ToInstagramCommentIdExpr)
                                ?? callStep.Args.FromTo.ToInstagramCommentIdExpr)
                    : null;

                return new SendMessageInput(
                    state.Id,
                    state.Identity,
                    channel,
                    new InstagramPageMessengerSendMessageInputFromTo(
                        fromToFromInstagramPageId,
                        fromToToInstagramId,
                        new InstagramMediaCommentDetailsObject(fromToFromInstagramMediaId, fromToToInstagramCommentId)),
                    messageType,
                    JsonConvert.DeserializeObject<MessageBody>(
                        JsonConvert.SerializeObject(
                            messageBodyObj,
                            JsonConfig.DefaultJsonSerializerSettings),
                        JsonConfig.DefaultJsonSerializerSettings)!,
                    deliveryType,
                    staffId);

            default:
                throw new NotImplementedException($"{channel} is unsupported channel");
        }
    }
}