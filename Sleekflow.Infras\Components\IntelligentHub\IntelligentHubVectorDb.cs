﻿using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.IntelligentHub;

public class IntelligentHubVectorDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;

    public IntelligentHubVectorDb(ResourceGroup resourceGroup, DocumentDB.DatabaseAccount databaseAccount)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
    }

    public class IntelligentHubVectorDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public IntelligentHubVectorDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public IntelligentHubVectorDbOutput InitIntelligentHubVectorDb()
    {
        const string cosmosDbId = "intelligenthubvectordb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs()
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Currently, unable to config the vector store policy need to be manually created
        // var containerParams = new ContainerParam[]
        // {
        //     new ContainerParam(
        //         "vector_store",
        //         "vector_store",
        //         ["/SleekflowCompanyId"],
        //         MaxThroughput: 1000,
        //         Ttl: 600),
        // };
        //
        // var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
        //     _resourceGroup,
        //     _databaseAccount,
        //     cosmosDb,
        //     cosmosDbId,
        //     containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new IntelligentHubVectorDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}