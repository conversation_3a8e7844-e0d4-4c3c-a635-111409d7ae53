﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.CrmHubConfigs;

[TriggerGroup(TriggerGroups.CrmHubConfigs)]
public class InitializeCrmHubConfig : ITrigger<InitializeCrmHubConfig.InitializeCrmHubConfigInput, InitializeCrmHubConfig.InitializeCrmHubConfigOutput>
{
    private readonly ICrmHubConfigService _crmHubConfigService;

    public InitializeCrmHubConfig(ICrmHubConfigService crmHubConfigService)
    {
        _crmHubConfigService = crmHubConfigService;
    }

    public class InitializeCrmHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CrmHubConfig.PropertyNameUsageLimit)]
        [Validations.ValidateObject]
        public UsageLimit UsageLimit { get; set; }

        [JsonProperty(CrmHubConfig.PropertyNameFeatureAccessibilitySettings)]
        [Validations.ValidateObject]
        public FeatureAccessibilitySettings? FeatureAccessibilitySettings { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public InitializeCrmHubConfigInput(
            string sleekflowCompanyId,
            UsageLimit usageLimit,
            FeatureAccessibilitySettings? featureAccessibilitySettings,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UsageLimit = usageLimit;
            FeatureAccessibilitySettings = featureAccessibilitySettings;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class InitializeCrmHubConfigOutput
    {
        [JsonProperty("crm_hub_config")]
        public CrmHubConfig CrmHubConfig { get; set; }

        [JsonConstructor]
        public InitializeCrmHubConfigOutput(CrmHubConfig crmHubConfig)
        {
            CrmHubConfig = crmHubConfig;
        }
    }

    public async Task<InitializeCrmHubConfigOutput> F(InitializeCrmHubConfigInput initializeCrmHubConfigInput)
    {
        var createdBy = string.IsNullOrWhiteSpace(initializeCrmHubConfigInput.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                initializeCrmHubConfigInput.SleekflowStaffId,
                initializeCrmHubConfigInput.SleekflowStaffTeamIds);

        var crmHubConfig = await _crmHubConfigService.InitializeCrmHubConfigAsync(
            initializeCrmHubConfigInput.SleekflowCompanyId,
            initializeCrmHubConfigInput.UsageLimit,
            initializeCrmHubConfigInput.FeatureAccessibilitySettings,
            createdBy);

        return new InitializeCrmHubConfigOutput(crmHubConfig);
    }
}