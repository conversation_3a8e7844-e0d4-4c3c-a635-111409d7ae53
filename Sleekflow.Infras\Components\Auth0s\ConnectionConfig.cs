using Newtonsoft.Json;

namespace Sleekflow.Infras.Components.Auth0s;

public class ConnectionConfig
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("strategy")]
    public string Strategy { get; set; }

    [JsonProperty("enabled_clients")]
    public List<string> EnableClients { get; set; }

    // public Input<ExpandoObject?> Options { get; set; }

    [JsonProperty("force_replace")]
    public bool ForceReplace { get; set; }

    [JsonProperty("options")]
    public ConnectionConfigOptions Options { get; set; }

    public ConnectionConfig(string name, string strategy, List<string> enableClients, ConnectionConfigOptions options)
    {
        Name = name;
        Strategy = strategy;
        EnableClients = enableClients;
        Options = options;
    }
}

public class ConnectionConfigOptions
{
    [JsonProperty("scopes")]
    public List<string> Scopes { get; set; }

    [JsonProperty("client_id")]
    public string ClientId { get; set; }

    [JsonProperty("client_secret")]
    public string ClientSecret { get; set; }

    [JsonProperty("kid", NullValueHandling = NullValueHandling.Ignore)]
    public string? KeyId { get; set; }

    [JsonProperty("team_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? TeamId { get; set; }

    [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
    public bool? NameAttributes { get; set; }

    [JsonProperty("email", NullValueHandling = NullValueHandling.Ignore)]
    public bool? EmailAttributes { get; set; }

    public ConnectionConfigOptions(List<string> scopes, string clientId, string clientSecret)
    {
        Scopes = scopes;
        ClientId = clientId;
        ClientSecret = clientSecret;
    }
}