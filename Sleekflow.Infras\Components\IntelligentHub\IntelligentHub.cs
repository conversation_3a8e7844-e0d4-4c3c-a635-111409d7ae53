﻿using Pulumi;
using Pulumi.AzureNative.App.V20240301.Inputs;
using Pulumi.AzureNative.AppConfiguration;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using CognitiveServices = Pulumi.AzureNative.CognitiveServices;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Search = Pulumi.AzureNative.Search;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.IntelligentHub;

public class IntelligentHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly IntelligentHubDb.IntelligentHubDbOutput _intelligentHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;
    private readonly IntelligentHubVectorDb.IntelligentHubVectorDbOutput _intelligentHubVectorDb;
    private readonly ConfigurationStore _configurationStore;

    public IntelligentHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        IntelligentHubDb.IntelligentHubDbOutput intelligentHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        IntelligentHubVectorDb.IntelligentHubVectorDbOutput intelligentHubVectorDb,
        ConfigurationStore configurationStore)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _intelligentHubDbOutput = intelligentHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
        _intelligentHubVectorDb = intelligentHubVectorDb;
        _configurationStore = configurationStore;
    }

    public List<App.ContainerApp> InitIntelligentHub()
    {
        Output<string>? openaiAccountName = null;
        Output<string>? chatDeploymentName = null;

        // Only create OpenAI resources for production because of the regional quota.
        // Development will utilize the same resources.
        if (_myConfig.Name == "production")
        {
            var openaiRandomId = new Random.RandomId(
                "sleekflow-ih-openai-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "sleekflow-ih-openai", "v0"
                        }
                    },
                });
            var openaiAccount = new CognitiveServices.Account(
                "sleekflow-ih-openai",
                new CognitiveServices.AccountArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    AccountName = openaiRandomId.Hex.Apply(h => "sleekflow-ih-openai" + h),
                    Kind = "OpenAI",
                    Sku = new CognitiveServices.Inputs.SkuArgs
                    {
                        Name = "S0"
                    },
                    Location = "eastus"
                });

            openaiAccountName = openaiAccount.Name;

            // DO NOT MODIFY. THIS IS NOW CONFIGURED IN THE AZURE PORTAL.
            var chatDeployment = new CognitiveServices.Deployment(
                "sleekflow-ih-openai-deployment-chat",
                new ()
                {
                    AccountName = openaiAccount.Name,
                    DeploymentName = "chat",
                    Properties = new CognitiveServices.Inputs.DeploymentPropertiesArgs
                    {
                        Model = new CognitiveServices.Inputs.DeploymentModelArgs
                        {
                            Format = "OpenAI", Name = "gpt-4", Version = "0613",
                        },
                    },
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new CognitiveServices.Inputs.SkuArgs
                    {
                        Capacity = 20, Name = "Standard",
                    },
                });

            chatDeploymentName = chatDeployment.Name;

            var turboDeployment = new CognitiveServices.Deployment(
                "sleekflow-ih-openai-deployment-turbo",
                new ()
                {
                    AccountName = openaiAccount.Name,
                    DeploymentName = "turbo",
                    Properties = new CognitiveServices.Inputs.DeploymentPropertiesArgs
                    {
                        Model = new CognitiveServices.Inputs.DeploymentModelArgs
                        {
                            Format = "OpenAI", Name = "gpt-35-turbo", Version = "0613",
                        },
                    },
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new CognitiveServices.Inputs.SkuArgs
                    {
                        Capacity = 120, Name = "Standard",
                    },
                });

            var turboLongDeployment = new CognitiveServices.Deployment(
                "sleekflow-ih-openai-deployment-turbo-long",
                new ()
                {
                    AccountName = openaiAccount.Name,
                    DeploymentName = "turbo-long",
                    Properties = new CognitiveServices.Inputs.DeploymentPropertiesArgs
                    {
                        Model = new CognitiveServices.Inputs.DeploymentModelArgs
                        {
                            Format = "OpenAI", Name = "gpt-35-turbo-16k", Version = "0613",
                        },
                    },
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new CognitiveServices.Inputs.SkuArgs
                    {
                        Capacity = 120, Name = "Standard",
                    },
                });
        }

        var formRecognizerRandomId = new Random.RandomId(
            "sleekflow-ih-form-recognizer-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "sleekflow-ih-form-recognizer", "v0"
                    }
                },
            });
        var formRecognizerAccount = new CognitiveServices.Account(
            "sleekflow-ih-form-recognizer",
            new CognitiveServices.AccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = formRecognizerRandomId.Hex.Apply(h => "sleekflow-ih-form-recognizer" + h),
                Kind = "FormRecognizer",
                Sku = new CognitiveServices.Inputs.SkuArgs
                {
                    Name = "S0"
                },
            });

        var textTranslationRandomId = new Random.RandomId(
            "sleekflow-ih-text-translation-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "sleekflow-ih-text-translation", "v0"
                    }
                },
            });
        var textTranslationAccount = new CognitiveServices.Account(
            "sleekflow-ih-text-translation",
            new CognitiveServices.AccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = "Global",
                AccountName = textTranslationRandomId.Hex.Apply(h => "sleekflow-ih-text-translation" + h),
                Kind = "TextTranslation",
                Sku = new CognitiveServices.Inputs.SkuArgs
                {
                    Name = "S1"
                },
            },
            new CustomResourceOptions()
            {
                RetainOnDelete = true
            });

        var searchRandomId = new Random.RandomId(
            "sleekflow-ih-search-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "sleekflow-ih-search", "v0"
                    }
                },
            });
        var search = new Search.Service(
            "sleekflow-ih-search",
            new Search.ServiceArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                SearchServiceName = searchRandomId.Hex.Apply(h => "sleekflow-ih-search" + h),
                Sku = new Search.Inputs.SkuArgs
                {
                    Name = Search.SkuName.Standard
                }
            });
        var search2 = _myConfig.Name == "production"
            ? new Search.Service(
                "sleekflow-ih-search2",
                new Search.ServiceArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    SearchServiceName = searchRandomId.Hex.Apply(h => "sleekflow-ih-search2" + h),
                    Sku = new Search.Inputs.SkuArgs
                    {
                        Name = Search.SkuName.Standard2
                    }
                })
            : search;
        var search3 = _myConfig.Name == "production"
            ? new Search.Service(
                "sleekflow-ih-search3",
                new Search.ServiceArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    SearchServiceName = searchRandomId.Hex.Apply(h => "sleekflow-ih-search3" + h),
                    Sku = new Search.Inputs.SkuArgs
                    {
                        Name = Search.SkuName.Standard
                    }
                })
            : search;

        var sourceFileRandomId = new Random.RandomId(
            "sleekflow-ih-source-file-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "sleekflow-ih-source-file-storage-account", "v0"
                    }
                },
            });
        var sourceFileStorageAccount = new Storage.StorageAccount(
            "sleekflow-ih-source-file-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-intelligent-hub-source-file-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = sourceFileRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.IntelligentHub),
            _myConfig.BuildTime);

        new IntelligentHubWorker(
            _resourceGroup,
            _intelligentHubDbOutput,
            _configurationStore,
            _managedEnvAndAppsTuples,
            _dbOutput,
            _gcpConfig,
            _myConfig,
            formRecognizerAccount,
            textTranslationAccount).InitWorker();

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.IntelligentHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            var worker = managedEnvAndAppsTuple.GetWorkerApp(ServiceNames.IntelligentHub);

            var listWorkerHostKeysResult = Output
                .Tuple(worker!.Name, _resourceGroup.Name, worker.Id)
                .Apply(
                    items => Web.ListWebAppHostKeys.Invoke(
                        new Web.ListWebAppHostKeysInvokeArgs
                        {
                            Name = items.Item1, ResourceGroupName = items.Item2,
                        }));

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.IntelligentHub));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 3 : 1,
                            MaxReplicas = 20,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "80"
                                            }
                                        }
                                    }
                                }
                            },
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-ih-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 1.0, Memory = "2.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_ENDPOINT",
                                            Value = _myConfig.Name == "production"
                                                ? Output.Format($"https://{openaiAccountName}.openai.azure.com/")

                                                // sleekflow-ih-openai216b80d4 is actually the Production endpoint. Dev and Prod shares the same one.
                                                : "https://sleekflow-ih-openai216b80d4.openai.azure.com/"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_KEY",
                                            Value = _myConfig.Name == "production"
                                                ? CognitiveServicesUtils.GetCognitiveServicesAccountKey(
                                                    _resourceGroup.Name,
                                                    openaiAccountName!)
                                                : "584c5a9fec2c43d5975087da47009d63"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_EUS_ENDPOINT",
                                            Value = "https://sleekflow-openai-eus2.openai.azure.com/"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_EUS_KEY", Value = "0b0b5fec514f4a3da1a2c966874684a5"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURECOGNITIVESEARCH_ENDPOINT",
                                            Value = Output.Format($"https://{search.Name}.search.windows.net/")
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURECOGNITIVESEARCH_ADMIN_KEY",
                                            Value = CognitiveServicesUtils.GetCognitiveSearchAdminKey(
                                                _resourceGroup.Name,
                                                search.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURECOGNITIVESEARCH_ENDPOINT_2",
                                            Value = Output.Format($"https://{search2.Name}.search.windows.net/")
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURECOGNITIVESEARCH_ADMIN_KEY_2",
                                            Value = CognitiveServicesUtils.GetCognitiveSearchAdminKey(
                                                _resourceGroup.Name,
                                                search2.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURECOGNITIVESEARCH_ENDPOINT_3",
                                            Value = Output.Format($"https://{search3.Name}.search.windows.net/")
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURECOGNITIVESEARCH_ADMIN_KEY_3",
                                            Value = CognitiveServicesUtils.GetCognitiveSearchAdminKey(
                                                _resourceGroup.Name,
                                                search3.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "HEADLESS_WHATSAPP_MESSAGE_INTEGRATION_ENDPOINT",
                                            Value =
                                                "https://gw.sleekflow.io/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Messages/SendMessage"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "PROMPT_FILTER_PRINT_FUNCTIONS",
                                            Value =
                                                "EvaluateScoring,ExitCondition,CalculateLeadScore"
                                        },

                                        #region IntelligentHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_INTELLIGENT_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_intelligentHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_INTELLIGENT_HUB_DB_KEY",
                                            Value = _intelligentHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_INTELLIGENT_HUB_DB_DATABASE_ID",
                                            Value = _intelligentHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion


                                        #region DbProcessorConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CHANGE_FEED_ENV_ID", Value = "default",
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region WorkerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_HOSTNAME",
                                            Value = worker.DefaultHostName.Apply(hn => "https://" + hn),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_FUNCTIONS_KEY",
                                            Value = listWorkerHostKeysResult
                                                .Apply(l => l.FunctionKeys!["default"]),
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.IntelligentHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SOURCE_FILE_STORAGE_ACCOUNT_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                sourceFileStorageAccount.Name)
                                        },

                                        #endregion

                                        #region FormRecognizerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURE_FORM_RECOGNIZER_ENDPOINT",
                                            Value =
                                                Output.Format(
                                                    // this url is currently not usable  $"https://{formRecognizerAccount.Name}.cognitiveservices.azure.com/")
                                                    $"https://eastasia.api.cognitive.microsoft.com/")
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURE_FORM_RECOGNIZER_KEY",
                                            Value = CognitiveServicesUtils.GetCognitiveServicesAccountKey(
                                                _resourceGroup.Name,
                                                formRecognizerAccount.Name)
                                        },

                                        #endregion

                                        #region TextTranslatorConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURE_TEXT_TRANSLATOR_KEY",
                                            Value = CognitiveServicesUtils.GetCognitiveServicesAccountKey(
                                                _resourceGroup.Name,
                                                textTranslationAccount.Name)
                                        },

                                        #endregion

                                        #region WebScraperConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APIFY_TOKEN",
                                            Value = _myConfig.Name == "production"
                                                ? "**********************************************"
                                                : "**********************************************"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APIFY_WEBHOOKURI",
                                            Value = _myConfig.Name == "production"
                                                ? "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub/webhook/web-scraper"
                                                : "https://webhook.site/cb370659-7adc-4789-9f95-4280065e745e"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APIFY_ACTOR_ID",
                                            Value = _myConfig.Name == "production"
                                                ? "aYG0l9s7dbB7j3gbS"
                                                : "aYG0l9s7dbB7j3gbS"
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion

                                        #region LightRagConfig

                                        new EnvironmentVarArgs
                                        {
                                            Name = "LIGHT_RAG_DOMAIN",
                                            // Value = Output
                                            //     .Tuple(
                                            //         managedEnvironment.DefaultDomain,
                                            //         containerApps[ServiceNames.IntelligentHubLightRag].Name)
                                            //     .Apply(t => $"https://{t.Item2}.{t.Item1}")
                                            Value = managedEnvAndAppsTuple
                                                .WebApps[ServiceNames.IntelligentHubLightRag].GetDomainName()
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "LIGHT_RAG_SUPPORTED_COMPANIES",
                                            Value = GetLightRagSupportedCompanies(_myConfig.Name)
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "LIGHT_RAG_API_KEY", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "LIGHT_RAG_WITH_NEO4J_DOMAIN",
                                            Value = _myConfig.Name == "production"
                                                ? managedEnvAndAppsTuple
                                                    .WebApps[ServiceNames.IntelligentHubLightRagWithNeo4J]
                                                    .GetDomainName()
                                                : string.Empty
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "LIGHT_RAG_WITH_NEO4J_SUPPORTED_COMPANIES",
                                            Value = GetLightRagWithNeo4JSupportedCompanies(_myConfig.Name)
                                        },

                                        #endregion

                                        #region AZUREOPENAI_EMBEDDING_CONFIG

                                        new EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_EMBEDDING_ENDPOINT",
                                            Value = "https://sleekflow-ih-openai216b80d4.openai.azure.com"
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_EMBEDDING_KEY",
                                            Value = "584c5a9fec2c43d5975087da47009d63"
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "AZUREOPENAI_EMBEDDING_DEPLOYMENT_NAME", Value = "embedding"
                                        },

                                        #endregion

                                        #region AZURE_COSMOSDB_VECTOR_STORE_DB_CONFIG

                                        new EnvironmentVarArgs
                                        {
                                            Name = "AZURE_COSMOS_VECTOR_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_intelligentHubVectorDb.AccountName}.documents.azure.com:443/")
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "AZURE_COSMOS_VECTOR_DB_KEY",
                                            Value = _intelligentHubVectorDb.AccountKey
                                        },
                                        new EnvironmentVarArgs
                                        {
                                            Name = "AZURE_COSMOS_VECTOR_DB_DATABASE_ID",
                                            Value = _intelligentHubVectorDb.DatabaseId
                                        }

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.IntelligentHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }

    public static string GetLightRagSupportedCompanies(string env)
    {
        return env switch
        {
            "dev" =>
                "b6d7e442-38ae-4b9a-b100-2951729768bc,0254e8a3-3a5b-4bf8-a7ce-78f7fe861b85,cddf6a22-0751-47ff-94c8-4d78c17b37b1,5873424d-5f26-4c81-9e16-5b248f7944de,5406fdfa-1e8c-4a76-91f6-89301c0d95a1,aecdf113-2b29-4ee1-90d2-ff061b6f073d,b5148215-4473-45f5-b820-89a7abbe88b5,0112a3be-6eda-4bf7-b36f-0ef59edcac9e,d218796b-b954-4afb-82c5-6735a7f3a162,d88d6bca-8d18-491e-9654-c9623705e3fc,18e2da38-52a7-4729-8102-6f5673fd3952,577da285-4d3d-410a-a40b-5c1857d1516a,532376d8-3d72-458d-b19e-1326c3130502,1b6c02ab-8554-4078-a6aa-6539b4256092,154ec87c-0761-41b7-bbba-6a3e81988928,f841ab07-c0c9-44f8-98e7-addd0f8aa757",
            "staging" => "39ee5f12-7997-45fa-a960-e4feecba425c",
            "production" =>
                "a4a1ead4-a6e7-4594-bfdd-1b5295ebad8a,0dfe2367-1704-486a-93e9-cdf27b053930,b0e6d1c9-29b9-46e5-b862-786c8663ddc1,0e4c6233-57f5-41a2-8999-96a67e3f77d4,ed43bd11-90e2-45aa-8cd3-49405acf2226,dba65ba3-74a3-4d2f-be92-fe2b445e2c6e,f843b437-7083-4635-b476-48e544a877de,3f4bee9f-6aae-4fdb-a467-db2b50444175,e852d604-f995-475d-a510-ed3bc2275d15,3f4bee9f-6aae-4fdb-a467-db2b50444175,59bb90c5-4e00-4bb7-b44e-f605a5783338,b2eeea34-8fd0-42cf-8e68-d4dcfb17731a,bf8b7f88-a71c-478f-b72c-7bf6e01216fe,1d3deb6d-da42-43db-8e60-ce52c3c8aa68,6f6c2916-0e50-4f57-9683-235dd92099e9,471a6289-b9b7-43c3-b6ad-395a1992baea,93656749-a407-49c7-884a-75f63750cdeb,1d180640-1310-4d1b-a122-4aa9c64e2948",
            _ => throw new ArgumentOutOfRangeException(nameof(env), env, null)
        };
    }

    public static string GetLightRagWithNeo4JSupportedCompanies(string env)
    {
        return env switch
        {
            "dev" => string.Empty,
            "staging" => string.Empty,
            "production" => "3f4bee9f-6aae-4fdb-a467-db2b50444175",
            _ => throw new ArgumentOutOfRangeException(nameof(env), env, null)
        };
    }
}