using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Serilog;
using Serilog.Enrichers.Span;
using Serilog.Events;
using Sleekflow.JsonConfigs;
using Sleekflow.Loggings;

var loggerConfig = new LoggerConfig();
var loggerConfiguration = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
    .MinimumLevel.Override("Azure.Messaging.ServiceBus", LogEventLevel.Warning)
    .MinimumLevel.Override("Azure.Core", LogEventLevel.Warning)
    .MinimumLevel.Override("DurableTask.AzureStorage", LogEventLevel.Warning)
    .MinimumLevel.Override("Host.Aggregator", LogEventLevel.Warning)
    .MinimumLevel.Override("Host.Results", LogEventLevel.Warning)
    .Enrich.WithSpan()
    .Enrich.FromLogContext()
    .Enrich.WithMachineName();
if (loggerConfig.IsLogAnalyticsEnabled == "TRUE")
{
    loggerConfiguration = loggerConfiguration
        .WriteTo.AzureAnalytics(
            loggerConfig.WorkspaceId,
            loggerConfig.AuthenticationId,
            "SleekflowDbScalar",
            flattenObject: false);
}
else
{
    loggerConfiguration = loggerConfiguration
        .WriteTo.Async(
            wt => wt.Console(
                outputTemplate:
                "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
}

JsonConvert.DefaultSettings = () => JsonConfig.DefaultJsonSerializerSettings;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureServices(
        services =>
        {
            services.ConfigureFunctionsApplicationInsights();
            services.AddLogging(lb => lb.AddSerilog(loggerConfiguration.CreateLogger()));
            services
                .AddHttpClient("default-handler")
                .ConfigurePrimaryHttpMessageHandler(
                    () => new HttpClientHandler
                    {
                        AllowAutoRedirect = false,
                    });
            services.AddHttpContextAccessor();
        })
    .Build();

host.Run();