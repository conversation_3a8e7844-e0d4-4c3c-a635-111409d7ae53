using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class WabaConversationInsertionException
{
    public const string PropertyNameLastConversationUsageInsertTimestamp = "last_conversation_usage_insert_timestamp";

    [JsonProperty(PropertyNameLastConversationUsageInsertTimestamp)]
    public long LastConversationUsageInsertTimestamp { get; set; }

    [JsonConstructor]
    public WabaConversationInsertionException(long lastConversationUsageInsertTimestamp)
    {
        LastConversationUsageInsertTimestamp = lastConversationUsageInsertTimestamp;
    }
}