using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator;

public partial class ScoreCalculatorTestCases
{
    private static readonly List<LeadScoreCriterion> BasicTestCasesCriteria = [
        new LeadScoreCriterion(
            30,
            @"#Intent and interest level
            - Is the lead asking about features, benefits, or credibility?
            - Is the lead comparing your product or service to competitors?
            - Is the lead showing signs of a potential purchase decision?"),
        new LeadScoreCriterion(
            40,
            @"#Buying signals
            - Is the lead mentioning urgency or a specific decision timeline?
            - Is the lead inquiring about pricing, discounts, or payment options?
            - Is the lead trying to negotiate the price?"),
        new LeadScoreCriterion(
            25,
            @"#Depth and specificity of questions
            - Do the lead's questions reflect domain knowledge or product understanding?
            - Is the lead asking follow-up questions or seeking clarification?
            - Is the lead actively participating in a focused discussion?"),
        new LeadScoreCriterion(
            5,
            @"#Sentiment and engagement tone
            - Is the lead expressing a positive or enthusiastic tone?
            - Does the lead's tone stay positive or improve after clarification?")
    ];

    public static IEnumerable<LeadScoreCalculatorTestCase> GetTestCases()
    {
        return GetBasicTestCases()
            .Concat(GetBroadbandTestCases())
            .Concat(GetMobileTestCases())
            .Concat(GetHealthcareTestCases())
            .Concat(GetBusinessTestCases())
            .Concat(GetEdgeCaseTestCases());
    }

    public static IEnumerable<LeadScoreCalculatorTestCase> GetSimpleTestCases()
    {
        return GetBasicTestCases();
    }

    private static IEnumerable<LeadScoreCalculatorTestCase> GetBasicTestCases()
    {
        // Hot Lead Test Cases
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Immediate Purchase Intent",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to sign up for your 1000M broadband plan today. What do I need to do?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! I can help you sign up right now. Let me get your details and check availability in your area."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Perfect, here's my address: 123 Main Street. When can installation happen?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                95,
                "Immediate purchase intent with willingness to provide details and proceed with installation scheduling."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Price Comparison and Ready to Switch",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm paying $150/month for 500M with my current provider. Your 1000M for $109 sounds much better."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "You're absolutely right! You'd get double the speed for less money. Shall I help you switch?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, let's do it. How long does the switch take?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                92,
                "Clear cost comparison shows purchase intent, asking about switch timeline indicates readiness to proceed."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Urgent Business Need",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Our office internet is terrible. We need a business plan ASAP - our team can't work properly."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I understand the urgency. Our business plans start at $299 for 1000M with priority support. Would you like me to expedite the setup?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, please. We need this resolved this week.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                94,
                "Urgent business need with specific timeline and willingness to pay for expedited service."));
        }

        // Warm Lead Test Cases
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Warm Lead - Feature Interest",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Tell me more about your 1000M plan. What's included?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our 1000M plan includes fiber connection, free Wi-Fi 6 router, free installation, and 24/7 support for $109/month."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That's interesting. Do you have a contract requirement?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                68,
                "Shows interest in plan details and asks relevant questions about contract terms."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Warm Lead - Comparison Shopping",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm comparing internet providers. How do you compare to competitors?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We offer competitive speeds with better customer service. Our 1000M plan is faster than most competitors at this price point."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll need to think about it and compare a few more options.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                55,
                "Actively comparing providers but needs more time to make decision."));
        }

        // Cold Lead Test Cases
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Cold Lead - Price Sensitive",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Do you have anything cheaper than $50/month?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our most affordable plan is 100M for $69/month. It's great value for the speed."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That's still too expensive for me.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Low",
                25,
                "Price-sensitive customer with budget constraints, unlikely to convert to current offerings."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Cold Lead - Just Browsing",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Just looking at what's available. Not planning to switch anytime soon."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "No problem! Feel free to browse. Let me know if you have any questions."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks, will do.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Low",
                20,
                "Explicitly states no intention to switch, minimal engagement."));
        }
    }

    private static IEnumerable<LeadScoreCalculatorTestCase> GetBroadbandTestCases()
    {
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Installation Timeline",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I need internet installed at my new apartment next week. Can you do that?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes! We can typically install within 3-5 business days. Let me check availability for next week."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Perfect! I'll take the 1000M plan. Here's my address.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                93,
                "Urgent timeline with immediate plan selection and address sharing."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Warm Lead - Speed Requirements",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I work from home and do video calls all day. What speed do I need?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "For heavy video conferencing, I'd recommend our 500M or 1000M plans for the best experience."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "What's the price difference between those two?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                72,
                "Has specific need and actively comparing plan options based on requirements."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Cold Lead - General Inquiry",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What broadband plans do you offer?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We offer plans from 100M to 1000M, starting at $69/month with free installation."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I see. Thanks for the info.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Low",
                35,
                "General inquiry with minimal engagement and no specific needs expressed."));
        }
    }

    private static IEnumerable<LeadScoreCalculatorTestCase> GetMobileTestCases()
    {
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Plan Upgrade",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to upgrade my mobile plan to unlimited data. Can you do that now?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Absolutely! Our unlimited 5G plan is $118/month. I can upgrade you immediately."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, please upgrade me right now.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                96,
                "Immediate upgrade request with willingness to proceed instantly."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Warm Lead - Roaming Needs",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I travel to Asia frequently. Do you have good roaming plans?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes! Our APAC plan includes 20GB local data plus 10-day roaming for $138/month."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That sounds reasonable. What countries are covered?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                75,
                "Has specific travel needs and is actively exploring plan options."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Cold Lead - Price Shopping",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What's your cheapest mobile plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our basic plan starts at $98/month with 5GB data."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll keep looking around.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Low",
                30,
                "Only interested in cheapest option with no commitment to proceed."));
        }
    }

    private static IEnumerable<LeadScoreCalculatorTestCase> GetHealthcareTestCases()
    {
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Immediate Health Need",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I need a doctor consultation today. Can your Bowtie plan help?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes! With our $99/month healthcare plan, you get unlimited video consultations. I can sign you up right now."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Perfect! How quickly can I access the service after signing up?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                91,
                "Immediate health need with urgency and willingness to sign up."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Warm Lead - Healthcare Benefits",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What exactly is included in the Bowtie healthcare plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "It includes unlimited video consultations, dental cleaning, full body check, and flu vaccine for $99/month."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That's quite comprehensive. Is there a waiting period?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                66,
                "Shows interest in comprehensive benefits and asks relevant questions about service terms."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Cold Lead - Healthcare Skepticism",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I don't really trust online healthcare services."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I understand your concern. Our doctors are fully licensed and the service is regulated by health authorities."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'd rather stick with traditional healthcare.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Low",
                18,
                "Expresses distrust in the service model and prefers traditional alternatives."));
        }
    }

    private static IEnumerable<LeadScoreCalculatorTestCase> GetBusinessTestCases()
    {
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Hot Lead - Business Expansion",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "We're expanding our office and need business internet for 50 employees. What do you recommend?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "For 50 employees, I'd recommend our enterprise plan with dedicated fiber and priority support."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sounds good. Can you send me a quote? We need this set up by month-end.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                94,
                "Clear business need with timeline and request for formal quote."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Warm Lead - Business Inquiry",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What business plans do you offer for small companies?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We have several options starting from $299/month with 1000M dedicated bandwidth and business support."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll need to discuss this with my partners.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                58,
                "Business interest but needs to consult with decision makers."));
        }
    }

    private static IEnumerable<LeadScoreCalculatorTestCase> GetEdgeCaseTestCases()
    {
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Edge Case - Multiple Services Interest",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm interested in bundling broadband, mobile, and healthcare. What kind of discount can you offer?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! We offer bundle discounts. The total would be around $250/month instead of $306 separately."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That's a good saving. Can you send me the detailed breakdown?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                89,
                "High-value multi-service interest with positive response to pricing and request for details."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Edge Case - Competitor Mention",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "CompetitorX is offering me a better deal. Can you match their pricing?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'd be happy to see what we can do. Can you share the details of their offer?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "They're offering 1000M for $89/month.")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "Medium",
                73,
                "Active comparison with competitor shows purchase intent, open to negotiation."));
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreCalculatorTestCase(
                "Edge Case - Technical Issues",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "My current provider has constant outages. I need something more reliable."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I understand your frustration. Our fiber network has 99.9% uptime with redundant connections."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That's exactly what I need. What's the setup process?")
            ],
            BasicTestCasesCriteria,
            new EvaluatedScore(
                "High",
                88,
                "Pain point with current service and shows strong interest in reliable solution."));
        }
    }
}