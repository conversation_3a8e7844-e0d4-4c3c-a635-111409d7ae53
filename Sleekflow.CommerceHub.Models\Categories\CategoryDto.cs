using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Categories;

public class CategoryDto : IHasMetadata
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(Category.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(Category.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(Category.PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public CategoryDto(
        string id,
        List<Multilingual> names,
        List<Description> descriptions,
        string storeId,
        PlatformData platformData,
        Dictionary<string, object?> metadata)
    {
        Id = id;
        Names = names;
        Descriptions = descriptions;
        StoreId = storeId;
        PlatformData = platformData;
        Metadata = metadata;
    }

    public CategoryDto(
        Category category)
        : this(
            category.Id,
            category.Names,
            category.Descriptions,
            category.StoreId,
            category.PlatformData,
            category.Metadata)
    {
    }
}