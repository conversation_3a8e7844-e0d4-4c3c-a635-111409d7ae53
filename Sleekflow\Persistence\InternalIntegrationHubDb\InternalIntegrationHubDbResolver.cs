using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.InternalIntegrationHubDb;

public interface IInternalIntegrationHubDbResolver : IContainerResolver
{
}

public class InternalIntegrationHubDbResolver : IInternalIntegrationHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public InternalIntegrationHubDbResolver(IInternalIntegrationHubDbConfig internalIntegrationHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            internalIntegrationHubDbConfig.Endpoint,
            internalIntegrationHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),

                // Consistency, Session, Properties, and Triggers are not allowed when AllowBulkExecution is set to true.
                // AllowBulkExecution = true,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}