using System.Net;
using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.Https.Services;

public interface IWebhookBridgeRedirectService
{
    Task<(bool IsSuccess, string Response, HttpStatusCode HttpStatusCode)> RedirectAsync(
        string url,
        HttpMethod method,
        Dictionary<string, object?> headerDict,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict,
        CancellationToken cancellationToken = default);

    Task<(bool IsSuccess, string Response, HttpStatusCode HttpStatusCode)> RedirectAsync(
        string url,
        HttpMethod method,
        Dictionary<string, object?> headers,
        string? bodyContent,
        string? contentType,
        string? bodyType,
        CancellationToken cancellationToken = default);

    bool ShouldRedirect(string companyId);
}

public class WebhookBridgeRedirectService : IScopedService, IWebhookBridgeRedirectService
{
    private readonly HashSet<string> _companyIds =
    [
        // Sleekflow Dev
        "b6d7e442-38ae-4b9a-b100-2951729768bc",

        // Sleekflow Staging
        "39ee5f12-7997-45fa-a960-e4feecba425c",

        // Sleekflow Prod
        "471a6289-b9b7-43c3-b6ad-395a1992baea",

        // HKBN RS
        "0e779f07-e591-4ed4-ab95-a09436430708",

        // HKBN Enterprise
        "748e5000-6d31-4f3b-9263-da8cfb24eea2"
    ];

    private readonly IRequestClient<OnHttpRedirectRequest> _client;
    private readonly IRequestClient<OnHttpRedirectV2Request> _clientV2;
    private readonly ILogger<WebhookBridgeRedirectService> _logger;


    public WebhookBridgeRedirectService(
        IRequestClient<OnHttpRedirectRequest> client,
        IRequestClient<OnHttpRedirectV2Request> clientV2,
        ILogger<WebhookBridgeRedirectService> logger)
    {
        _client = client;
        _clientV2 = clientV2;
        _logger = logger;
    }

    public bool ShouldRedirect(string companyId)
    {
        return _companyIds.Contains(companyId);
    }

    public async Task<(bool IsSuccess, string Response, HttpStatusCode HttpStatusCode)> RedirectAsync(
        string url,
        HttpMethod method,
        Dictionary<string, object?> headerDict,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var evt = new OnHttpRedirectRequest(
                url,
                headerDict,
                bodyStr,
                bodyDict,
                method);

            var res = await _client.GetResponse<OnHttpRedirectReply>(evt, cancellationToken);

            return (res.Message.Success, res.Message.HttpResponseMessage, res.Message.HttpStatusCode);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[WebhookBridgeRedirectService]: failed to redirect due to {Error}",
                e.Message);
            return (false, e.Message, HttpStatusCode.InternalServerError);
        }
    }

    public async Task<(bool IsSuccess, string Response, HttpStatusCode HttpStatusCode)> RedirectAsync(
        string url,
        HttpMethod method,
        Dictionary<string, object?> headers,
        string? bodyContent,
        string? contentType,
        string? bodyType,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var evt = new OnHttpRedirectV2Request(
                url,
                method,
                headers,
                bodyContent,
                contentType,
                bodyType);

            var response = await _clientV2.GetResponse<OnHttpRedirectV2Reply>(evt, cancellationToken);

            return (response.Message.Success, response.Message.HttpResponseMessage, response.Message.HttpStatusCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[WebhookBridgeRedirectV2Service]: failed to redirect due to {Error}",
                ex.Message);

            return (false, ex.Message, HttpStatusCode.InternalServerError);
        }
    }
}