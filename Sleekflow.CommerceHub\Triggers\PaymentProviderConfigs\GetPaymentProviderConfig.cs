using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class GetPaymentProviderConfig
    : ITrigger<
        GetPaymentProviderConfig.GetPaymentProviderConfigInput,
        GetPaymentProviderConfig.GetPaymentProviderConfigOutput>
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;

    public GetPaymentProviderConfig(
        IPaymentProviderConfigService paymentProviderConfigService)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
    }

    public class GetPaymentProviderConfigInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("payment_provider_config_id")]
        public string PaymentProviderConfigId { get; set; }

        [JsonConstructor]
        public GetPaymentProviderConfigInput(
            string sleekflowCompanyId,
            string paymentProviderConfigId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PaymentProviderConfigId = paymentProviderConfigId;
        }
    }

    public class GetPaymentProviderConfigOutput
    {
        [JsonProperty("payment_provider_config")]
        public PaymentProviderConfigDto PaymentProviderConfig { get; set; }

        [JsonConstructor]
        public GetPaymentProviderConfigOutput(
            PaymentProviderConfigDto paymentProviderConfig)
        {
            PaymentProviderConfig = paymentProviderConfig;
        }
    }

    public async Task<GetPaymentProviderConfigOutput> F(
        GetPaymentProviderConfigInput getPaymentProviderConfigInput)
    {
        var paymentProviderConfig = await _paymentProviderConfigService.GetPaymentProviderConfigAsync(
            getPaymentProviderConfigInput.PaymentProviderConfigId,
            getPaymentProviderConfigInput.SleekflowCompanyId);

        return new GetPaymentProviderConfigOutput(new PaymentProviderConfigDto(paymentProviderConfig));
    }
}