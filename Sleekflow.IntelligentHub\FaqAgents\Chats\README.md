# GroupChatService Response Generation Flow

## Overview

The GroupChatService is responsible for orchestrating multi-agent collaboration to generate responses for chat conversations. This document explains the flow of how responses are generated through the service.

## Complete End-to-End Flow

The complete flow from user interaction to AI response involves multiple systems working together:

```mermaid
flowchart TD
    subgraph "User Interfaces"
        Website["Website UI"]
        Mobile["Mobile App"]
        Webhook["External Webhook"]
        WhatsApp["WhatsApp Messages"]
    end

    subgraph "FlowHub - Workflow Engine"
        WorkflowExecution["Workflow Execution"]
        AgentStep["Agent Recommend Reply Step"]
        WorkflowTriggers["Workflow Triggers"]
    end

    subgraph "Message Bus"
        GetAgentEvent["GetAgentRecommendedReplyEvent"]
        CompleteEvent["OnAgentCompleteStepActivationEvent"]
    end

    subgraph "IntelligentHub"
        EventConsumer["GetAgentRecommendedReplyEventConsumer"]
        APITrigger["GetAgentRecommendedReply API"]
        ChatService["ChatService"]
        SmartReplyPlugin["SmartReplyPlugin"]
        GroupChatService["GroupChatService"]
    end

    Website --> WorkflowTriggers
    Mobile --> WorkflowTriggers
    Webhook --> WorkflowTriggers
    WhatsApp --> WorkflowTriggers

    WorkflowTriggers --> WorkflowExecution
    WorkflowExecution --> AgentStep

    AgentStep --> GetAgentEvent
    GetAgentEvent --> EventConsumer

    Website --> APITrigger
    Mobile --> APITrigger

    EventConsumer --> ChatService
    APITrigger --> ChatService

    ChatService --> SmartReplyPlugin
    SmartReplyPlugin --> GroupChatService
    GroupChatService --> SmartReplyPlugin
    SmartReplyPlugin --> ChatService

    EventConsumer --> CompleteEvent
    CompleteEvent --> WorkflowExecution
```

### 1. User Interaction Entry Points

User requests can enter the system through several channels:

1. **Direct API Calls**: Web or mobile applications directly calling the `GetAgentRecommendedReply` API endpoint
2. **Workflow Automation**: Business processes in the FlowHub workflow engine using agent steps
3. **Webhooks**: External systems triggering workflows via webhook endpoints
4. **Messaging Platforms**: Incoming messages from WhatsApp and other messaging channels

### 2. FlowHub Workflow System

The FlowHub system provides workflow automation capabilities:

1. **Workflow Triggers**: Configured events that initiate workflow execution
   - API-based triggers
   - Webhook triggers (configured through `GetOrCreateWorkflowWebhookTrigger`)
   - Scheduled triggers
   - Message-based triggers

2. **Workflow Execution**: When triggered, a workflow creates a state object and processes steps sequentially
   - `WorkflowExecutionService` manages the execution lifecycle
   - `WorkflowRuntimeService` handles the actual step processing

3. **Agent Recommend Reply Step**: A specialized workflow step type
   - Defined by `AgentRecommendReplyStepArgs` with the call name "sleekflow.v1.agent-recommend-reply"
   - Executed by `AgentRecommendReplyStepExecutor` when encountered in a workflow
   - Fetches conversation history and contact properties
   - Publishes a `GetAgentRecommendedReplyEvent` to the message bus

### 3. Message Bus Communication

Events flow through the message bus to coordinate between systems:

1. **GetAgentRecommendedReplyEvent**: Published by the workflow system
   - Contains workflow state ID, step ID, conversation context, and other metadata
   - Consumed by the `GetAgentRecommendedReplyEventConsumer` in IntelligentHub

2. **OnAgentCompleteStepActivationEvent**: Published when processing is complete
   - Contains the generated reply and confidence score
   - Consumed by the workflow system to continue workflow execution

### 4. IntelligentHub Processing

The actual AI agent processing happens in IntelligentHub:

1. **Event Consumers**: Background processes that handle asynchronous events
   - `GetAgentRecommendedReplyEventConsumer` processes agent recommendation requests from workflows
   - Other consumers handle playground and standard recommendation requests

2. **Direct API Triggers**: HTTP endpoints that directly handle user requests
   - `GetAgentRecommendedReply` - User-facing API endpoint for getting agent-based responses
   - Primarily used for testing and UI-based interactions

## Client Flow: Before GroupChatService

Before reaching the GroupChatService, requests flow through several layers:

```mermaid
flowchart TD
    subgraph "External Clients"
        Trigger["Trigger Endpoints"]
        Event["Event Consumers"]
        WebhookEvent["Webhook Event Consumer"]
    end

    subgraph "Intermediate Services"
        ChatService["ChatService"]
        SmartReplyPlugin["SmartReplyPlugin"]
    end

    subgraph "Core Processing"
        GroupChatService["GroupChatService"]
    end

    Trigger -->|"StreamAgentAnswerAsync"| ChatService
    Event -->|"StreamAgentAnswerAsync/StreamAnswerAsync"| ChatService
    WebhookEvent -->|"StreamAnswerAsync"| ChatService

    ChatService -->|"AgentSmartReplyStreaming"| SmartReplyPlugin
    SmartReplyPlugin -->|"HandleMultiAgentChatStream"| GroupChatService
```

### Primary Client Entry Points

1. **Trigger Endpoints**: HTTP endpoints that directly handle user requests
   - `GetAgentRecommendedReply` - User-facing API endpoint for getting agent-based responses
   - Other playground and recommendation reply endpoints

2. **Event Consumers**: Background processes that handle asynchronous events
   - `GetAgentRecommendedReplyEventConsumer` - Processes agent recommendation requests
   - `GenerateRecommendedReplyEventConsumer` - Generates standard recommendations
   - `GeneratePlaygroundRecommendedReplyEventConsumer` - Handles playground-specific requests

3. **Webhook Events**: External integration event handlers
   - `HeadlessWhatsappIntegrations` - Processes incoming WhatsApp messages

### Intermediate Services

1. **ChatService**: The primary aggregation layer that:
   - Determines which type of response to generate (standard or agent-based)
   - Retrieves relevant sources and company configurations
   - Calls the appropriate plugin for generating responses
   - Returns the response stream to the client
   - Two main methods: `StreamAnswerAsync` and `StreamAgentAnswerAsync`

2. **SmartReplyPlugin**: Connects ChatService to GroupChatService
   - Provides different types of smart reply capabilities
   - Handles language detection and prompt formatting
   - For agent-based replies, calls `HandleMultiAgentChatStream` on GroupChatService
   - Primary method: `AgentSmartReplyStreaming`

## Response Generation Flow

```mermaid
sequenceDiagram
    participant Client
    participant GroupChatService
    participant AgentCollaborationDefinition
    participant Agents
    participant AgentGroupChat

    Client->>GroupChatService: HandleMultiAgentChatStream(companyId, chatEntries, promptContext, agentConfig)
    GroupChatService->>AgentCollaborationDefinition: GetAgentCollaborationDefinition(collaborationMode)
    GroupChatService->>AgentCollaborationDefinition: CreateAgents(kernel, chatEntries, companyId, promptContext)
    AgentCollaborationDefinition->>Agents: Create specialized agents
    Agents-->>GroupChatService: Return agent collection

    GroupChatService->>AgentGroupChat: Configure with agents and settings
    GroupChatService->>GroupChatService: Get company config
    GroupChatService->>AgentCollaborationDefinition: InitializeChatHistoryAsync(agentGroupChat, groupChatId, chatEntries, companyConfig, promptContext)

    GroupChatService->>AgentDurationTracker: StartTracking()

    loop For each response
        GroupChatService->>AgentGroupChat: InvokeAsync()
        AgentGroupChat-->>GroupChatService: Agent response
        GroupChatService->>AgentCollaborationDefinition: InterceptAgentReplyAsync(response, groupChatId, agentGroupChat)
        AgentCollaborationDefinition-->>GroupChatService: shouldContinue
    end

    GroupChatService->>AgentDurationTracker: GetMetricReport(collaborationMode)
    GroupChatService->>AgentCollaborationDefinition: GetSourceAsync(agentGroupChat, groupChatId)
    GroupChatService->>AgentCollaborationDefinition: GetFinalReplyAsync(agentGroupChat)

    GroupChatService-->>Client: Return (sourceStr, finalReply)
```

## Key Components

1. **GroupChatService**: Main orchestrator for multi-agent chat processing.

2. **AgentCollaborationDefinition**: Defines the strategy and behavior for agent collaboration. Different types exist:
   - Default mode
   - Lead Nurturing mode (detailed in `LeadNurturings/README.md`)

3. **AgentGroupChat**: Microsoft Semantic Kernel component that facilitates agent collaboration.

4. **Agents**: Specialized AI agents with specific capabilities.

5. **AgentDurationTracker**: Tracks and reports on agent performance metrics.

## Process Steps

1. **Selection of Collaboration Definition**:
   - Based on the `CollaborationMode` in agent configuration
   - Default or specialized modes like Lead Nurturing

2. **Agent Creation**:
   - Agents are created dynamically based on the collaboration definition
   - For Lead Nurturing mode, this includes specialized agents for different capabilities

3. **Group Chat Configuration**:
   - Agents are assembled into an `AgentGroupChat`
   - Selection and termination strategies are defined by the collaboration definition

4. **Chat History Initialization**:
   - Previous chat entries are used to establish context
   - Company configuration is applied

5. **Response Generation**:
   - Responses are processed in a streaming fashion
   - Each response is intercepted by the collaboration definition
   - Processing continues until termination strategy is triggered or all responses are processed

6. **Result Compilation**:
   - Sources and final reply are extracted from the chat
   - If no consensus is reached, the last reply is used with a warning

7. **Return**:
   - A tuple of (sourceStr, finalReply) is returned to the caller

## Lead Nurturing Workflow

For the Lead Nurturing collaboration mode, the process follows a capability-based architecture where agents are selected based on their capabilities:

1. Lead classification
2. Decision making
3. Strategy definition
4. Knowledge retrieval
5. Response crafting
6. Lead assignment or demo scheduling (if applicable)
7. Response review

See `LeadNurturings/README.md` for detailed information on the Lead Nurturing workflow.

## Entry Point

```csharp
public async Task<(string SourceStr, string Reply)> HandleMultiAgentChatStream(
    string sleekflowCompanyId,
    List<SfChatEntry> chatEntries,
    PromptContext? promptContext,
    CompanyAgentConfig? agentConfig = null)
```

## Output Format

The method returns a tuple containing:
- `string SourceStr`: Source information (e.g., references or citations)
- `string Reply`: The final response to be shown to the user