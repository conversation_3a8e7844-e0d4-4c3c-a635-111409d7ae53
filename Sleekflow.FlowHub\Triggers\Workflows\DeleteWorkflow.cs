using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class DeleteWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public DeleteWorkflow(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class DeleteWorkflowInput : IHasSleekflowStaff, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteWorkflowInput(
            string sleekflowCompanyId,
            string workflowId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DeleteWorkflowOutput
    {
    }

    public async Task<DeleteWorkflowOutput> F(DeleteWorkflowInput deleteWorkflowInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteWorkflowInput.SleekflowStaffId,
            deleteWorkflowInput.SleekflowStaffTeamIds);

        var childWorkflows = await _workflowService.GetAllLatestWorkflowAndStatusTuplesAsync(
            deleteWorkflowInput.SleekflowCompanyId,
            null,
            100,
            null,
            new WorkflowFilters(
                null,
                null,
                null,
                null,
                null,
                WorkflowType.AIAgent,
                deleteWorkflowInput.WorkflowId)
        );

        var childWorkflowIds = childWorkflows.Workflows.Select(w => w.WorkflowId).ToList();
        //FIXME: need to batch delete
        foreach (var childWorkflowId in childWorkflowIds)
        {
            await _workflowService.DeleteWorkflowAsync(
                childWorkflowId,
                deleteWorkflowInput.SleekflowCompanyId,
                sleekflowStaff);
        }

        await _workflowService.DeleteWorkflowAsync(
            deleteWorkflowInput.WorkflowId,
            deleteWorkflowInput.SleekflowCompanyId,
            sleekflowStaff);

        return new DeleteWorkflowOutput();
    }
}