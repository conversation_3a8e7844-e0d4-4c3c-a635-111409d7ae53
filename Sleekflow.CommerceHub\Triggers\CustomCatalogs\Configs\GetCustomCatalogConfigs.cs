using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Configs;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs.Configs;

[TriggerGroup(ControllerNames.CustomCatalogConfigs)]
public class GetCustomCatalogConfigs
    : ITrigger<
        GetCustomCatalogConfigs.GetCustomCatalogConfigsInput,
        GetCustomCatalogConfigs.GetCustomCatalogConfigsOutput>
{
    private readonly ICustomCatalogConfigService _customCatalogConfigService;

    public GetCustomCatalogConfigs(ICustomCatalogConfigService customCatalogConfigService)
    {
        _customCatalogConfigService = customCatalogConfigService;
    }

    public class GetCustomCatalogConfigsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetCustomCatalogConfigsInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetCustomCatalogConfigsOutput
    {
        [JsonProperty("custom_catalog_configs")]
        public List<CustomCatalogConfig> CustomCatalogConfigs { get; set; }

        [JsonConstructor]
        public GetCustomCatalogConfigsOutput(List<CustomCatalogConfig> customCatalogConfigs)
        {
            CustomCatalogConfigs = customCatalogConfigs;
        }
    }

    public async Task<GetCustomCatalogConfigsOutput> F(GetCustomCatalogConfigsInput getCustomCatalogConfigsInput)
    {
        return new GetCustomCatalogConfigsOutput(
            await _customCatalogConfigService.GetCustomCatalogConfigsAsync(
                getCustomCatalogConfigsInput.SleekflowCompanyId));
    }
}