﻿using Microsoft.AspNetCore.Cryptography.KeyDerivation;

namespace Sleekflow.Auth0.BulkImporter.Services;

public static class PasswordUtils
{
    public static string ToAuth0PasswordHash(string passwordHash)
    {
        var decodePasswordHashOutput = DecodePasswordHash(Convert.FromBase64String(passwordHash));
        var variant = decodePasswordHashOutput.Prf switch
        {
            KeyDerivationPrf.HMACSHA1 => "sha1",
            KeyDerivationPrf.HMACSHA256 => "sha256",
            KeyDerivationPrf.HMACSHA512 => "sha512",
            _ => throw new ArgumentOutOfRangeException()
        };

        var str =
            $"$pbkdf2-{variant}$i={decodePasswordHashOutput.IterCount},l={decodePasswordHashOutput.SubkeyLength}${ToBase64StringNoPadding(decodePasswordHashOutput.Salt)}${ToBase64StringNoPadding(decodePasswordHashOutput.ExpectedSubkey)}";

        return str;
    }

    private static string ToBase64StringNoPadding(byte[] buffer)
    {
        return Convert.ToBase64String(buffer).Replace("=", string.Empty);
    }

    private static uint ReadNetworkByteOrder(byte[] buffer, int offset)
    {
        return ((uint) buffer[offset + 0] << 24)
               | ((uint) buffer[offset + 1] << 16)
               | ((uint) buffer[offset + 2] << 8)
               | buffer[offset + 3];
    }

    private static DecodePasswordHashOutput DecodePasswordHash(byte[] passwordHashBuffer)
    {
        if (passwordHashBuffer.Length == 0)
        {
            throw new Exception();
        }

        switch (passwordHashBuffer[0])
        {
            case 0x00: // Microsoft.AspNetCore.Identity.PasswordHasher[TUser].VerifyHashedPasswordV2
                throw new NotImplementedException();
            case 0x01: // Microsoft.AspNetCore.Identity.PasswordHasher[TUser].VerifyHashedPasswordV3
                break;
            default:
                throw new NotImplementedException();
        }

        // Microsoft.AspNetCore.Identity.PasswordHasher[TUser].VerifyHashedPasswordV3
        // Read header information
        var prf = (KeyDerivationPrf) ReadNetworkByteOrder(passwordHashBuffer, 1);
        var iterCount = (int) ReadNetworkByteOrder(passwordHashBuffer, 5);
        var saltLength = (int) ReadNetworkByteOrder(passwordHashBuffer, 9);

        // Read the salt: must be >= 128 bits
        if (saltLength < 128 / 8)
        {
            throw new Exception();
        }

        var salt = new byte[saltLength];
        Buffer.BlockCopy(passwordHashBuffer, 13, salt, 0, salt.Length);

        // Read the subkey (the rest of the payload): must be >= 128 bits
        var subkeyLength = passwordHashBuffer.Length - 13 - salt.Length;
        if (subkeyLength < 128 / 8)
        {
            throw new Exception();
        }

        var subkey = new byte[subkeyLength];
        Buffer.BlockCopy(passwordHashBuffer, 13 + salt.Length, subkey, 0, subkey.Length);

        return new DecodePasswordHashOutput(salt, prf, iterCount, subkeyLength, subkey);
    }
}