﻿using System.ClientModel;
using Azure.AI.OpenAI;
using Google.Apis.Auth.OAuth2;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Serilog;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub;

/// <summary>
/// Extension methods for registering Semantic Kernel related services.
/// </summary>
public static class SemanticKernelExtensions
{
    public const string DEFAULT_SERVICE_ID = "gpt-4.1-mini";
    public const string S_O3_MINI = "o3-mini";
    public const string S_O4_MINI = "o4-mini";
    public const string S_GPT_4_1 = "gpt-4.1";
    public const string S_GPT_4_1_MINI = "gpt-4.1-mini";
    public const string S_FLASH = "flash";
    public const string S_FLASH_2_5 = "flash-2.5";
    public const string S_PRO_2_5 = "pro-2.5";
    public const string S_GPT_4o = "gpt-4o";

    public static readonly string[] AzureOpenAiChatCompletionServices =
    [
        S_O3_MINI,
        S_O4_MINI,
        S_GPT_4_1,
        S_GPT_4_1_MINI,
        S_GPT_4o
    ];

    public static readonly string[] GoogleGeminiChatCompletionServices =
    [
        S_FLASH,
        S_FLASH_2_5,
        S_PRO_2_5
    ];

    /// <summary>
    /// Add Semantic Kernel services.
    /// </summary>
    /// <param name="services">Current service collection.</param>
    /// <returns>The service collection.</returns>
    public static IServiceCollection AddSemanticKernelServices(this IServiceCollection services)
    {
        var config = new AzureOpenAIConfig();

        var tokenCountingService = new TokenCountingService();
        services.AddSingleton<ITokenCountingService>(tokenCountingService);

        services.AddScoped<Kernel>(_ =>
        {
            var kernelBuilder = Kernel.CreateBuilder();

            kernelBuilder = AddGoogleGemini(kernelBuilder);
            kernelBuilder = AddAzureOpenAI(kernelBuilder, config);

            kernelBuilder.Services.AddSingleton<IPromptRenderFilter, SafePromptFilter>();

            kernelBuilder.Services.AddScoped<IFunctionInvocationFilter, KernelFunctionInvocationFilter>();

            for (int i = 0; i < kernelBuilder.Services.Count; i++)
            {
                var descriptor = kernelBuilder.Services[i];
                try
                {
                    if (descriptor.IsKeyedService && descriptor.KeyedImplementationFactory != null)
                    {
                        // Cache the original factory
                        var originalFactory = descriptor.KeyedImplementationFactory;

                        // Replace with a new descriptor using the wrapped factory
                        kernelBuilder.Services[i] = ServiceDescriptor.DescribeKeyed(
                            descriptor.ServiceType,
                            descriptor.ServiceKey,
                            (kernelSp, key) =>
                            {
                                var service = originalFactory(kernelSp, key);
                                if (service is not IChatCompletionService chatCompletionService)
                                {
                                    return service;
                                }

                                var loggerFactory = new LoggerFactory().AddSerilog(Log.Logger);

                                return new MyChatCompletionService(
                                    loggerFactory.CreateLogger<MyChatCompletionService>(),
                                    chatCompletionService,
                                    tokenCountingService);
                            },
                            descriptor.Lifetime);
                    }
                }
                catch (Exception e)
                {
                    Log.Logger.Error(
                        e,
                        "Error wrapping service: {ServiceType}",
                        descriptor.ServiceType);

                    throw;
                }
            }

            return kernelBuilder.Build();
        });
        return services;
    }

    private static IKernelBuilder AddAzureOpenAI(IKernelBuilder kernelBuilder, AzureOpenAIConfig config)
    {
        var eusEndpoint = new Uri(config.EusEndpoint);
        var customClientOptions = new AzureOpenAIClientOptions
        {
            Transport = new AzureOpenAICustomHttpClientPipelineTransport("2025-01-01-preview")
        };

        kernelBuilder = kernelBuilder
            .AddAzureOpenAIChatCompletion(
                "o3-mini",
                new AzureOpenAIClient(eusEndpoint, new ApiKeyCredential(config.EusKey), customClientOptions),
                serviceId: S_O3_MINI)
            .AddAzureOpenAIChatCompletion(
                "o4-mini",
                new AzureOpenAIClient(eusEndpoint, new ApiKeyCredential(config.EusKey), customClientOptions),
                serviceId: S_O4_MINI)
            .AddAzureOpenAIChatCompletion(
                "gpt-4.1",
                new AzureOpenAIClient(eusEndpoint, new ApiKeyCredential(config.EusKey), customClientOptions),
                serviceId: S_GPT_4_1)
            .AddAzureOpenAIChatCompletion(
                "gpt-4.1-mini",
                new AzureOpenAIClient(eusEndpoint, new ApiKeyCredential(config.EusKey), customClientOptions),
                serviceId: S_GPT_4_1_MINI)
            .AddAzureOpenAIChatCompletion(
                "gpt-4o",
                new AzureOpenAIClient(eusEndpoint, new ApiKeyCredential(config.EusKey), customClientOptions),
                serviceId: S_GPT_4o)

            // The default has to be the last one
            // When serviceId is not specified, this one will be used.
            .AddAzureOpenAIChatCompletion(
                "gpt-4.1-mini",
                new AzureOpenAIClient(eusEndpoint, new ApiKeyCredential(config.EusKey), customClientOptions),
                serviceId: DEFAULT_SERVICE_ID);

        return kernelBuilder;
    }

    private static IKernelBuilder AddGoogleGemini(IKernelBuilder kernelBuilder)
    {
#if false
        var httpClient = new HttpClient(new GeminiResponseInterceptorHandler());
#else
        HttpClient? httpClient = null;
#endif

        string? cachedToken = null;
        var tokenExpiration = DateTime.MinValue;
        Func<ValueTask<string>> bearerTokenProvider = async () =>
        {
            // If the cached token exists and hasn't expired, return it.
            if (!string.IsNullOrEmpty(cachedToken) && DateTime.UtcNow < tokenExpiration)
            {
                return cachedToken;
            }

            var googleCredential = GoogleCredential.FromJson(
                """
                **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                """);

            if (googleCredential.IsCreateScopedRequired)
            {
                googleCredential =
                    googleCredential.CreateScoped("https://www.googleapis.com/auth/cloud-platform");
            }

            cachedToken = await googleCredential.UnderlyingCredential.GetAccessTokenForRequestAsync();
            tokenExpiration = DateTime.UtcNow.AddMinutes(5);

            return cachedToken;
        };


#pragma warning disable SKEXP0070
#pragma warning disable SKEXP0010

        kernelBuilder = kernelBuilder
            .AddVertexAIGeminiChatCompletion(
                modelId: "gemini-2.0-flash-001",
                bearerTokenProvider: bearerTokenProvider,
                location: "us-east1",
                projectId: "e103-project-46727",
                serviceId: S_FLASH,
                httpClient: httpClient)
            .AddVertexAIGeminiChatCompletion(
                modelId: "gemini-2.5-flash",
                bearerTokenProvider: bearerTokenProvider,
                location: "us-central1",
                projectId: "e103-project-46727",
                serviceId: S_FLASH_2_5,
                httpClient: httpClient)
            .AddVertexAIGeminiChatCompletion(
                modelId: "gemini-2.5-pro",
                bearerTokenProvider: bearerTokenProvider,
                location: "us-central1",
                projectId: "e103-project-46727",
                serviceId: S_PRO_2_5,
                httpClient: httpClient);

#pragma warning restore SKEXP0010
#pragma warning restore SKEXP0070

        return kernelBuilder;
    }
}