namespace Sleekflow.CrmHub.Models.Constants;

public static class AuditLogTypes
{
    public static readonly AuditLogType PropagatingObjectUpdateToProvider = new AuditLogType(
        "PropagatingObjectUpdateToProvider",
        "Propagating an object to the provider.");

    public static readonly AuditLogType PropagatedObjectUpdateToProvider = new AuditLogType(
        "PropagatedObjectUpdateToProvider",
        "Propagated an object to the provider.");

    public static readonly AuditLogType PropagateObjectUpdateToProviderFailure = new AuditLogType(
        "PropagateObjectUpdateToProviderFailure",
        "Unable to propagate an object to the provider.");
}

public record AuditLogType(string Name, string Description)
{
    public string Name { get; } = Name;

    public string Description { get; } = Description;
}