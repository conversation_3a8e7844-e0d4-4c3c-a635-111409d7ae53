using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class LanguageTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetLanguageTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HKBN/",
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc");

        // Selected Test Cases
        yield return new ChatEvalQuestion(
            testConfig,
            "Switch to Simplified Chinese after explicit request",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, can you tell me about the INDICAID products?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Sure! INDICAID offers a variety of rapid test kits, such as the 6-in-1 respiratory test."),
                new ChatMessageContent(AuthorRole.User, "请用简体中文回复。"),
                new ChatMessageContent(AuthorRole.Assistant, "当然可以！INDICAID提供多种快速检测试剂盒，例如6合1呼吸道检测。"),
                new ChatMessageContent(AuthorRole.User, "这个检测有多准确？")
            ],
            "6合1呼吸道检测的准确度高达99%，是香港多家私家医院采用的本地快测品牌。",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "Default and stick to Traditional Chinese as conversation language",
            [
                new ChatMessageContent(AuthorRole.User, "你好，請問INDICAID有哪些產品？"),
                new ChatMessageContent(AuthorRole.Assistant, "INDICAID提供多種快速檢測試劑盒，例如6合1呼吸道檢測。"),
                new ChatMessageContent(AuthorRole.User, "請問這個6合1檢測包括什麼？"),
                new ChatMessageContent(AuthorRole.Assistant, "6合1檢測包括新冠病毒、甲型及乙型流感、呼吸道合胞病毒等六種病毒。"),
                new ChatMessageContent(AuthorRole.User, "準確度如何？")
            ],
            "準確度高達99%，是香港最多私家醫院採用的快測品牌。",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "Stick to English as conversation language",
            [
                new ChatMessageContent(AuthorRole.User, "Hi, what products does INDICAID offer?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "INDICAID offers various rapid test kits, like the 6-in-1 respiratory test."),
                new ChatMessageContent(AuthorRole.User, "请问6合1包括哪些检测？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The 6-in-1 test detects COVID-19, Influenza A and B, RSV, and more."),
                new ChatMessageContent(AuthorRole.User, "准确率怎么样？")
            ],
            "The accuracy rate is up to 99%, making it a trusted choice among Hong Kong hospitals.",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "Respond in written Cantonese for Cantonese-specific input",
            [
                new ChatMessageContent(AuthorRole.User, "喂，INDICAID有咩產品啊？"),
                new ChatMessageContent(AuthorRole.Assistant, "INDICAID有好多種快速檢測試劑盒，例如6合1呼吸道檢測。"),
                new ChatMessageContent(AuthorRole.User, "6合1包唔包新冠啊？"),
                new ChatMessageContent(AuthorRole.Assistant, "包啊，6合1檢測包括新冠病毒、流感甲乙型同其他病毒。"),
                new ChatMessageContent(AuthorRole.User, "準唔準啊？")
            ],
            "好準啊，準確度有99%，係香港好多私家醫院用緊嘅快測牌子。",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "Default to Traditional Chinese for ambiguous mixed input",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, 请问INDICAID有哪些产品？"),
                new ChatMessageContent(AuthorRole.Assistant, "INDICAID提供多種快速檢測試劑盒，例如6合1呼吸道檢測。"),
                new ChatMessageContent(AuthorRole.User, "Can you tell me more about the 6-in-1?"),
                new ChatMessageContent(AuthorRole.Assistant, "當然可以！6合1檢測包括新冠病毒、甲型及乙型流感等六種病毒。"),
                new ChatMessageContent(AuthorRole.User, "How accurate is it?")
            ],
            "準確度高達99%，是香港最多私家醫院採用的快測品牌。",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "Stick to English despite language switches in long context",
            [
                new ChatMessageContent(AuthorRole.User, "Hi, what are INDICAID’s main products?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our main products include the 6-in-1 respiratory test kit."),
                new ChatMessageContent(AuthorRole.User, "请问6合1包括什么？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "It includes tests for COVID-19, Influenza A and B, RSV, and more."),
                new ChatMessageContent(AuthorRole.User, "請問有冇腸胃檢測？"),
                new ChatMessageContent(AuthorRole.Assistant, "Yes, we have the 3-in-1 gut health test."),
                new ChatMessageContent(AuthorRole.User, "How fast are the results?")
            ],
            "Results are available in just 15 minutes for both the respiratory and gut tests.",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);

        var indicaidDocuments =
            new List<string>
            {
                "INDICAID「月月測」產品小冊子.md", "RS_sales briefing_INDICAID_20241204.docx.md"
            };
        var bowtieDocuments =
            new List<string>
            {
                "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md",
                "RS_sales briefing_Bowtie_20241204.docx.md"
            };
        var evercareDocuments =
            new List<string>
            {
                "HKBN x Evercare Whatsapp Guide_20241204.md", "RS_sales briefing_Evercare_20241204.docx.md"
            };

        yield return new ChatEvalQuestion(
            testConfig,
            "Customer inquiring about HKBN services and plans without prior knowledge",
            [
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！感謝您對香港寬頻的關注。我們提供多種服務，並與Bowtie及Evercare等公司合作，為您帶來更多個人化選擇。\n\n*Bowtie 4合1醫療服務計劃*，涵蓋多項醫療保障。\n*Evercare居家照護計劃*，提供多種居家照護服務。\n\n請問您對哪方面的服務或計劃特別感興趣？讓我們能更精準地為您推薦！"),
                new ChatMessageContent(AuthorRole.User, "仲有冇其他醫療計劃"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！當然，我們還有其他醫療計劃可以供您參考：\n\n*香港寬頻 x Bowtie 4合1醫療服務計劃*：月費只需$99，提供全面的醫療保障，包括門診諮詢、牙齒清潔、身體檢查和疫苗接種。\n*香港寬頻 x Evercare居家照護計劃*：月費由$399起，提供多種居家照護服務，包括上門護理、傷口護理和醫療陪診。\n\n請問您對以上哪項計劃感興趣？或者您有其他想了解的資訊嗎？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲健康檢測？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您提供更多關於健康檢查計劃的資訊：\n\n*香港寬頻 x Bowtie 4合1醫療服務計劃*：此計劃包括全面的醫療保障，其中一項是身體檢查。月費只需$99，就能享有年度身體檢查。\n*香港寬頻 x Evercare居家照護計劃*：此計劃提供多種居家照護服務，雖然主要以居家照護為主，但部分計劃可能包含健康評估。\n\n請問您對哪一個計劃的身體檢查或健康評估部分特別感興趣？我可以提供更詳細的資訊。"),
                new ChatMessageContent(AuthorRole.User, "Indicaid？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！Indicaid與香港寬頻合作推出「Monthly Test」快速測試組合計劃，為您提供便捷的健康檢測方案：\n\n*基本計劃*：包含快速測試劑。\n*進階計劃*：除快速測試劑外，更包含癌症篩查自測包。\n\n此外，我們還與Bowtie及Evercare合作，提供更多元化的醫療服務。\n\n請問您對Indicaid的哪項計劃感興趣？或想了解更多關於Bowtie和Evercare的資訊？"),
                new ChatMessageContent(AuthorRole.User, "可唔可以講多啲？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然，樂意為您詳細介紹Indicaid「Monthly Test」計劃。\n\nIndicaid「Monthly Test」計劃提供兩種選擇：基本計劃和進階計劃。基本計劃價格為每月$48，提供快速測試套件。進階計劃價格為每月$68，除快速測試套件外，還包括癌症篩查自測包。\n\n進階計劃讓您可以更全面地監測健康狀況。請問您對哪種計劃更感興趣？或者您有其他想了解的資訊嗎？"),
                new ChatMessageContent(AuthorRole.User, "有咩疫苗提供㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！感謝您對我們健康服務的關注。除了健康檢查和Indicaid計劃，我們也提供疫苗接種服務。Bowtie 4合1醫療服務計劃包含流感疫苗接種。請問您對流感疫苗或其他疫苗有興趣嗎？我們可以提供更多詳細資訊。"),
                new ChatMessageContent(AuthorRole.User, "唔好意思，唔係講疫苗 有咩Test Kidd提供？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！非常感謝您的提問。關於Test Kidd，目前我們沒有相關的服務資訊。請問您是否對我們其他的健康服務感興趣？例如，HKBN x Bowtie 4合1醫療服務計劃、HKBN x Evercare 家居護理計劃，或者HKBN x INDICAID「每月測」快速測試組合計劃。您想了解更多關於這些計劃的詳情嗎？"),
                new ChatMessageContent(AuthorRole.User, "Test kit"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！關於檢測套裝，我們與 INDICAID 合作提供多種選擇，包括新冠病毒、甲乙型流感、呼吸道 6 合 1、愛滋病毒/梅毒，以及癌症篩查測試。我們亦有 INDICAID「每月檢測」快速測試組合計劃，分為基本和進階版。想了解更多關於測試種類、價格和啟用流程的詳細資訊嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀講多啲呀不如"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然，樂意為您提供更多關於INDICAID測試套裝的資訊。\n\n我們與INDICAID合作提供多種測試套裝，包括：\n*COVID-19快速測試*\n*甲/乙型流感快速測試*\n*呼吸道病毒快速測試*\n*愛滋病/梅毒快速測試*\n*大腸癌篩查測試*\n\n此外，我們還有INDICAID「每月檢測」計劃，分為基礎計劃和進階計劃。基礎計劃提供每月一次的COVID-19測試，而進階計劃則提供更多種類的測試選擇。\n\n關於價格和啟動流程：\n*價格*：不同測試套裝和計劃的價格各異，請告知您感興趣的具體項目，以便我們提供準確的價格資訊。\n*啟動流程*：購買後，您會收到啟動碼，按照指示在INDICAID應用程式或網站上註冊並啟動服務即可。\n\n如果您想了解更多關於特定測試套裝或「每月檢測」計劃的詳細資訊，請隨時提出。我們很樂意為您解答。"),
                new ChatMessageContent(AuthorRole.User, "基礎計劃同進階計劃有咩分別？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！想了解基礎計劃同進階計劃嘅分別？\n\n*基礎計劃*：每月$48，包括2個指定測試套裝。\n*進階計劃*：每月$68，包括2個指定測試套裝，額外送您INDICAID™️癌症篩查自測包。\n\n希望以上資訊可以幫到您！如有其他疑問，歡迎隨時提出。"),
                new ChatMessageContent(AuthorRole.User, "我比多$20就係為咗嗰個癌症篩查至七包？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！是的，多付的$20主要是為了INDICAID™️癌症篩查自測包。這個自測包包含三種測試：\n\n*   幽幽管 幽門螺旋菌抗原測試 (檢測幽門螺旋菌)\n*   INDICAID™️ 大便隱血測試 (檢測大便隱血)\n*   INDICAID™️ HPV 尿液測試 (檢測高風險 HPV 類型)\n\n希望這些資訊能幫助您更好地了解進階計劃的價值！如有任何其他問題，請隨時提出。"),
                new ChatMessageContent(AuthorRole.User, "呢三個測試係另外嗰個冇㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！是的，幽門螺旋菌抗原測試、大便隱血測試和HPV 尿液測試僅包含在進階計劃中。這些測試可以幫助您及早發現潛在的健康風險。\n\n*幽門螺旋菌抗原測試*：檢測幽門螺旋菌感染，有助於預防胃潰瘍和胃癌。\n*大便隱血測試*：檢測糞便中的隱藏血液，有助於篩查結直腸癌。\n*HPV 尿液測試*：檢測尿液中的HPV病毒，有助於篩查子宮頸癌風險。\n\n如果您有任何其他問題，請隨時提出。"),
                new ChatMessageContent(AuthorRole.User, "好似唔係好抵咁"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白您對價格的考慮。讓我解釋一下，INDICAID™️癌症篩查自測包中的大便隱血測試，是香港政府認可的大腸癌初步篩查方法。及早發現潛在風險，能讓您及時採取行動，不僅能更有效地管理健康，長遠來看，也能節省潛在的醫療費用。這份安心，其實非常值得。"),
                new ChatMessageContent(AuthorRole.User, "/clear"),
                new ChatMessageContent(AuthorRole.User, "hi hkbn"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Good day! I am HKBN AI. We offer a wide range of services, including broadband, fixed line, and mobile services, and partner with different companies to provide personalized solutions. How may I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您推薦我們的服務。HKBN提供多樣化的選擇，包括寬頻、固網及流動通訊服務。以下是一些熱門服務及優惠：\n\n*寬頻服務*：我們提供高速穩定的寬頻服務，讓您在家工作或娛樂都能暢通無阻。\n*固網服務*：清晰穩定的通話品質，是您商務及家庭的最佳選擇。\n*流動通訊服務*：我們與N mobile合作，提供全球數據漫遊服務，讓您出外旅遊或公幹都能輕鬆上網。\n*夥伴優惠*：\n> Evercare家居護理計劃\n> Bowtie 4合1醫療服務計劃\n> INDICAID快速測試組合計劃\n\n想了解更多詳情，請隨時提問！"),
                new ChatMessageContent(AuthorRole.User, "可唔可以講多 d bowtie"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您詳細介紹 Bowtie 4合1醫療服務計劃：\n\n*計劃內容：*\n> 無限次西醫視像諮詢\n> 兩次牙科洗牙及檢查\n> 一次全面身體檢查\n> 一次流感疫苗接種\n\n*計劃優勢：*\n> 無需診金/藥費\n> 無需排隊\n> 提供醫生證明\n\n*費用：*每月$99（需簽訂12個月合約）\n\n*申請資格：*18-80歲香港居民，每張香港身份證僅限登記一次。\n\n此計劃提供全面的醫療保障，讓您足不出戶也能輕鬆獲得醫療服務。請問您還有其他想了解的嗎？"),
                new ChatMessageContent(AuthorRole.User, "咁抵？兩次洗牙可以係邊洗 要唔要加錢㗎"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！關於Bowtie 4合1醫療服務計劃的洗牙服務，兩次洗牙及檢查可以在指定Cimer牙科診所進行，診所位於中環、鑽石山、觀塘和沙田。這兩次洗牙已包含在計劃內，*不需額外加錢*。如有其他牙科服務需求，可能會有額外費用。請問還有其他想了解的嗎？"),
                new ChatMessageContent(AuthorRole.User, "好似幾好 不過我仲想瞭解多 d 其他 果個家居咩黎㗎"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興您對我們的服務感興趣。既然您對Bowtie 4合1醫療服務計劃感興趣，相信您也會對Evercare家居護理計劃感興趣！\n\nEvercare提供多種家居護理計劃，包括：\n\n*家居護理計劃（基本及進階）：*提供上門護理服務。\n*傷口護理計劃：*專為需要傷口護理的人士而設。\n*陪診服務計劃：*提供陪同就診服務。\n\n*申請資格：*詳情請向Evercare查詢。\n\n想了解更多關於Evercare家居護理計劃的詳情，例如費用、最新優惠及如何啟動計劃，請隨時提問！由於最新的促銷活動可能隨時變更，建議您直接向Evercare確認。\n\n請問您對哪個計劃特別感興趣呢？"),
                new ChatMessageContent(AuthorRole.User, "幾錢㗎 點向 evercare查詢呀？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！關於Evercare家居護理計劃的價格和查詢方式，以下為您提供相關資訊：\n\n*Evercare家居護理計劃價格：*\n\n*   基本計劃：請透過以下方式查詢\n*   進階計劃：請透過以下方式查詢\n*   傷口護理服務計劃：請透過以下方式查詢\n*   陪診服務計劃：請透過以下方式查詢\n\n*如何查詢Evercare：*\n\n您可以透過以下方式聯絡Evercare了解更多詳情及最新優惠：\n\n*   WhatsApp：3997 7778\n*   網站：https://www.evercare.com.hk/\n\n由於價格會因應不同因素而有所調整，建議您直接向Evercare查詢以獲取最準確的資訊。\n\n希望這些資訊對您有幫助！如有其他問題，歡迎隨時提出。"),
                new ChatMessageContent(AuthorRole.User, "你唔知幾錢㗎咩？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！明白您想了解Evercare家居護理計劃的價錢。由於價格會因應個別情況而有所不同，建議您直接向Evercare查詢以獲取最準確的報價。\n\n您可以透過以下方式聯絡Evercare：\n* WhatsApp：3997 7778\n* 網站：https://www.evercare.com.hk/\n\n如果同時申請Bowtie 4合1醫療服務計劃，可能有特別折扣優惠。如需更多協助，歡迎隨時提問！或者，我可以安排專人回覆您。"),
                new ChatMessageContent(AuthorRole.User, "打左電話 話唔係 evercare"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！非常抱歉您致電 Evercare 時遇到問題。我明白您想了解 Evercare 居家照護計劃的價格。以下是一些計劃選項和相關價格的概述：\n\n*居家照護服務計劃（基本）*\n*居家照護服務計劃（進階）*\n*傷口護理服務計劃*\n*陪診服務計劃*\n\n此外，我們還有 Bowtie 4合1 醫療服務計劃的捆綁優惠。如需了解更多關於這些計劃的詳細價格和內容，請隨時告訴我，我很樂意為您提供更多信息，或者我可以安排 Evercare 的專員直接與您聯繫，這樣可以更精確地根據您的具體需求提供報價。您覺得哪個方式對您最方便呢？"),
                // new ChatMessageContent(AuthorRole.User, "好呀 安排下"),
                // new ChatMessageContent(
                //     AuthorRole.Assistant,
                //     "您好！非常抱歉您之前未能成功聯絡 Evercare。我會立即為您安排 Evercare 專員直接與您聯絡，詳細講解「Care@Home」計劃的各項優點和特別優惠。此計劃能為您提供全面的居家照護服務。此外，與HKBN服務組合選購更可享有特別折扣！請您放心，我會盡快安排專員與您聯絡。"),
                // new ChatMessageContent(AuthorRole.User, "Hi hkbn"),
                // new ChatMessageContent(
                //     AuthorRole.Assistant,
                //     "您好！已為您安排 Evercare 專員盡快與您聯絡，詳細講解「Care@Home」居家照護計劃，當中包括居家照護、傷口護理及陪診服務等。同時選購 HKBN 服務組合更可享有特別折扣！專員將會直接聯絡您，以便更精確地根據您的具體需求提供報價。請您放心，專員將會盡快與您聯絡。"),
                // new ChatMessageContent(AuthorRole.User, "Hi hkbn 我仲有其他問題"),
                // new ChatMessageContent(
                //     AuthorRole.Assistant,
                //     "您好！感謝您的耐心等候，Evercare 專員將會盡快與您聯絡。\n\n同時，如果您有任何關於 HKBN 服務或其他合作計劃的疑問，請隨時提出。我們非常樂意為您解答，並協助您了解更多關於 HKBN 服務與 Evercare 居家照護計劃結合的獨家優惠。\n\n請隨時提出您的問題，我們將竭誠為您服務！"),
                // new ChatMessageContent(AuthorRole.User, "除咗 evercare你地好似仲有上網 plan介紹下")
            ],
            "您好！很高興您對我們的服務感興趣。除了 Evercare 居家照護計劃，我們還有多種上網plan供您選擇。雖然未能找到具體寬頻上網plan的詳細資訊，但我們有HKBN流動數據plan，以及與 Bowtie 和 Evercare 合作的獨家優惠。\n\n請問您對上網速度、用量有什麼特別需求嗎？例如，您主要用來做什麼？有多少設備需要連接？ 了解您的具體需求後，我們就能為您推薦最合適的方案。同時，選用指定服務組合更可享有特別折扣！",
            SourceFilenames: indicaidDocuments.Concat(bowtieDocuments).Concat(evercareDocuments).ToList());
    }
}