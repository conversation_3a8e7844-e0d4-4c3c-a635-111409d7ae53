using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class LeadScoreCriterion
{
    [JsonProperty("weight")]
    public int Weight { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonConstructor]
    public LeadScoreCriterion(int weight, string description)
    {
        Weight = weight;
        Description = description;
    }

    public LeadScoreCriterion(LeadScoreCriterionDto dto)
    {
        Weight = dto.Weight;
        Description = dto.Description;
    }

    public LeadScoreCriterionDto ToDto()
    {
        return new LeadScoreCriterionDto(this);
    }
}