using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class ConnectWabasProductCatalogs
    : ITrigger<ConnectWabasProductCatalogs.ConnectWabasProductCatalogsInput, ConnectWabasProductCatalogs.ConnectWabasProductCatalogsOutput>
{
    private readonly ILogger<ConnectWabasProductCatalogs> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public ConnectWabasProductCatalogs(ILogger<ConnectWabasProductCatalogs> logger, IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class ConnectWabasProductCatalogsInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id_catalog_id_dictionary")]
        public Dictionary<string, string> WabaIdCatalogIdDictionary { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ConnectWabasProductCatalogsInput(string sleekflowCompanyId, Dictionary<string, string> wabaIdCatalogIdDictionary, string? sleekflowStaffId, List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaIdCatalogIdDictionary = wabaIdCatalogIdDictionary;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ConnectWabasProductCatalogsOutput
    {
        [JsonProperty("connected_wabas")]
        public List<WabaDto> ConnectedWabas { get; set; }

        [JsonConstructor]
        public ConnectWabasProductCatalogsOutput(List<WabaDto> connectedWabas)
        {
            ConnectedWabas = connectedWabas;
        }
    }

    public async Task<ConnectWabasProductCatalogsOutput> F(ConnectWabasProductCatalogsInput connectWabasProductCatalogsInput)
    {
        _logger.LogInformation("connecting waba ids {WabaIds} to product catalog {FacebookProductCatalogIds}", connectWabasProductCatalogsInput.WabaIdCatalogIdDictionary.Keys.ToString(), connectWabasProductCatalogsInput.WabaIdCatalogIdDictionary.Values.ToString());

        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            connectWabasProductCatalogsInput.SleekflowStaffId,
            connectWabasProductCatalogsInput.SleekflowStaffTeamIds);

        var wabas = await _productCatalogService.ConnectWabasProductCatalogsAsync(
            connectWabasProductCatalogsInput.SleekflowCompanyId,
            connectWabasProductCatalogsInput.WabaIdCatalogIdDictionary,
            sleekflowStaff);

        var wabaDtos = new List<WabaDto>();

        wabas.ForEach(x=> wabaDtos.Add(new WabaDto(x)));

        return new ConnectWabasProductCatalogsOutput(wabaDtos);
    }
}