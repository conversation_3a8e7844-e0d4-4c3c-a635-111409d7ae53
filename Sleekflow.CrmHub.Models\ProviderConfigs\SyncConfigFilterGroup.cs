using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

public sealed class SyncConfigFilterGroup : IEquatable<SyncConfigFilterGroup>
{
    [JsonProperty("filters")]
    public List<SyncConfigFilter> Filters { get; set; }

    [JsonConstructor]
    public SyncConfigFilterGroup(List<SyncConfigFilter> filters)
    {
        Filters = filters;
    }

    public bool Equals(SyncConfigFilterGroup? other)
    {
        if (other == null)
        {
            return false;
        }

        return Enumerable.SequenceEqual(Filters, other.Filters);
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj))
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (obj.GetType() != this.GetType())
        {
            return false;
        }

        return Equals((SyncConfigFilterGroup) obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Filters);
    }
}