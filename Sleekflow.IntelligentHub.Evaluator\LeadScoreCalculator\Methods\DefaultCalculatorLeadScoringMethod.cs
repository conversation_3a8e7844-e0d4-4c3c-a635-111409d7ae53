﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Evaluator.Constants;
using Sleekflow.IntelligentHub.Evaluator.LeadScores.Methods;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator.Methods;

public class DefaultLeadScoringCalculatorMethod : ILeadScoreCalculatorMethod<LeadScoreCalculatorResult>
{
    private readonly IReviewerService _reviewerService;

    public DefaultLeadScoringCalculatorMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _reviewerService = scope.ServiceProvider.GetRequiredService<IReviewerService>();
    }

    public string MethodName => MethodNames.LeadScoring;

    public async Task<LeadScoreCalculatorResult> CompleteAsync(
        ChatMessageContent[] questionContexts,
        List<LeadScoreCriterion> criteria)
    {
        var chatEntries = questionContexts.Select(ChatEntriesUtil.ToChatEntries).ToList();
        var evaluatedScore = await _reviewerService.GetCalculateLeadScoreAsync(chatEntries, criteria);
        return new LeadScoreCalculatorResult(
            MethodName,
            evaluatedScore,
            0);
    }
}