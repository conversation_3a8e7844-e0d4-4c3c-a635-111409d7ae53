﻿using Newtonsoft.Json;

namespace Sleekflow.Models.ActionEvents.CrmHub;

public class UpdateSchemafulObjectRequest
{
    public string SchemaId { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string PrimaryPropertyValue { get; set; }

    public Dictionary<string, object?> PropertyValues { get; set; }

    public string SleekflowUserProfileId { get; set; }

    public string SleekflowUserProfileIdForRecordLocator { get; set; }

    public string UpdatedVia { get; set; }

    [JsonConstructor]
    public UpdateSchemafulObjectRequest(
        string schemaId,
        string sleekflowCompanyId,
        string primaryPropertyValue,
        Dictionary<string, object?> propertyValues,
        string sleekflowUserProfileId,
        string sleekflowUserProfileIdForRecordLocator,
        string updatedVia)
    {
        SchemaId = schemaId;
        SleekflowCompanyId = sleekflowCompanyId;
        PrimaryPropertyValue = primaryPropertyValue;
        PropertyValues = propertyValues;
        SleekflowUserProfileId = sleekflowUserProfileId;
        SleekflowUserProfileIdForRecordLocator = sleekflowUserProfileIdForRecordLocator;
        UpdatedVia = updatedVia;
    }
}