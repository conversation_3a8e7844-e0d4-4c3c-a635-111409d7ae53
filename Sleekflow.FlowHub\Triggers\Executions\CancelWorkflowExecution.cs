﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class CancelWorkflowExecution
    : ITrigger<
        CancelWorkflowExecution.CancelWorkflowExecutionInput,
        CancelWorkflowExecution.CancelWorkflowExecutionOutput>
{
    private readonly IWorkflowRuntimeService _workflowRuntimeService;

    public CancelWorkflowExecution(
        IWorkflowRuntimeService workflowRuntimeService)
    {
        _workflowRuntimeService = workflowRuntimeService;
    }

    public class CancelWorkflowExecutionInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CancelWorkflowExecutionInput(
            string sleekflowCompanyId,
            string stateId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StateId = stateId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CancelWorkflowExecutionOutput
    {
    }

    public async Task<CancelWorkflowExecutionOutput> F(CancelWorkflowExecutionInput input)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        await _workflowRuntimeService.CancelWorkflowAsync(
            input.SleekflowCompanyId,
            input.StateId,
            StateReasonCodes.ManualCancellation,
            sleekflowStaff);

        return new CancelWorkflowExecutionOutput();
    }
}