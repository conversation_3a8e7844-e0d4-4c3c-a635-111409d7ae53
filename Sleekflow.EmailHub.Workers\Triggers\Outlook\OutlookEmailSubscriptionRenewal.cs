using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Workers.Configs;
using Sleekflow.JsonConfigs;

namespace Sleekflow.EmailHub.Workers.Triggers.Outlook;

public class OutlookEmailSubscriptionRenewal
{
    private readonly ILogger<OutlookEmailSubscriptionRenewal> _logger;
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public OutlookEmailSubscriptionRenewal(
        ILogger<OutlookEmailSubscriptionRenewal> logger,
        IAppConfig appConfig,
        HttpClient httpClient)
    {
        _logger = logger;
        _appConfig = appConfig;
        _httpClient = httpClient;
    }

    [Function("Outlook_Subscription_Renewal")]
    public async Task OutlookSubscriptionRenewalTimerTrigger([TimerTrigger("0 0 0 * * *")] TimerInfo timerInfo)
    {
        _logger.LogInformation($"OutlookEmailSubscriptionRenewal timer trigger function executed at {DateTime.UtcNow}");
        var inputJsonStr =
            JsonConvert.SerializeObject(
                new
                {
                    ProviderName = ProviderNames.Outlook
                }, JsonConfig.DefaultJsonSerializerSettings);
        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(_appConfig.EmailHubInternalEndpoint + "/Subscriptions/RenewProviderEmailSubscription"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
        };
        var _ = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        _logger.LogInformation($"OutlookEmailSubscriptionRenewal timer trigger function finished execution at {DateTime.UtcNow}");
    }
}