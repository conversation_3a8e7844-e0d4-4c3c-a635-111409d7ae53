using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.Commons;

public interface IRange<T>
{
    public T Start { get; set; }

    public T End { get; set; }

    public bool IsInRange(T start, T end);
}

public class DateTimeOffsetRange : IRange<DateTimeOffset>
{
    [JsonProperty("start")]
    public DateTimeOffset Start { get; set; }

    [JsonProperty("end")]
    public DateTimeOffset End { get; set; }

    [JsonConstructor]
    public DateTimeOffsetRange(DateTimeOffset start, DateTimeOffset end)
    {
        Start = start;
        End = end;
    }

    public bool IsInRange(DateTimeOffset start, DateTimeOffset end) => Start >= start && End <= end;
}

public class TimestampRange : IRange<long>
{
    [JsonProperty("start")]
    public long Start { get; set; }

    [JsonProperty("end")]
    public long End { get; set; }

    [JsonConstructor]
    public TimestampRange(long start, long end)
    {
        Start = start;
        End = end;
    }

    public bool IsInRange(long start, long end) => Start >= start && End <= end;
}