﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.SupportHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class SupportHubModules
{
    public static void BuildSupportHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.Add<PERSON><PERSON><PERSON><ISupportHubDbConfig>(new Mock<ISupportHubDbConfig>().Object);
        b.<PERSON><PERSON><PERSON><ISupportHubDbResolver>(new Mock<ISupportHubDbResolver>().Object);

#else
        var supportHubDbConfig = new SupportHubDbConfig();

        b.<PERSON>d<PERSON><PERSON><PERSON><ISupportHubDbConfig>(supportHubDbConfig);
        b.<PERSON>d<PERSON><PERSON><PERSON><ISupportHubDbResolver, SupportHubDbResolver>();
#endif
    }
}