using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

public class WabaLongLivedAccessToken
{
    [JsonProperty("encrypted_token")]
    public string EncryptedToken { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("expiry_datetime")]
    public DateTimeOffset? ExpiryDateTime { get; set; }

    [JsonProperty("is_valid")]
    public bool? IsValid { get; set; }

    [JsonConstructor]
    public WabaLongLivedAccessToken(
        string encryptedToken,
        string tokenType,
        DateTimeOffset? expiryDateTime,
        bool? isValid = true)
    {
        EncryptedToken = encryptedToken;
        TokenType = tokenType;
        ExpiryDateTime = expiryDateTime;
        IsValid = isValid;
    }
}