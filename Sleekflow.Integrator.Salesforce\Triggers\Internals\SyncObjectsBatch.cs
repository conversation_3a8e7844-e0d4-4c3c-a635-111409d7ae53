﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Internals;

[TriggerGroup("Internals")]
public class SyncObjectsBatch : ITrigger
{
    private readonly ILogger<SyncObjectsBatch> _logger;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly IBus _bus;

    public SyncObjectsBatch(
        ILogger<SyncObjectsBatch> logger,
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        IBus bus)
    {
        _logger = logger;
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _bus = bus;
    }

    public class SyncObjectsBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public SyncObjectsBatchInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters,
            string? nextRecordsUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public class SyncObjectsBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; }

        [JsonConstructor]
        public SyncObjectsBatchOutput(long count, string? nextRecordsUrl)
        {
            Count = count;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public async Task<SyncObjectsBatchOutput> F(
        SyncObjectsBatchInput syncObjectsBatchInput)
    {
        var sleekflowCompanyId = syncObjectsBatchInput.SleekflowCompanyId;
        var entityTypeName = syncObjectsBatchInput.EntityTypeName;

        var authentication =
            await _salesforceAuthenticationService.GetAsync(sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Start sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, nextRecordsUrl {NextRecordsUrls}",
            sleekflowCompanyId,
            entityTypeName,
            syncObjectsBatchInput.NextRecordsUrl);

        var (objects, nextRecordsUrl) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            entityTypeName,
            syncObjectsBatchInput.FilterGroups,
            syncObjectsBatchInput.FieldFilters,
            syncObjectsBatchInput.NextRecordsUrl);

        _logger.LogInformation(
            "End sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, prevNextRecordsUrl {PrevNextRecordsUrls}, nextRecordsUrl {NextRecordsUrls}, count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            syncObjectsBatchInput.NextRecordsUrl,
            nextRecordsUrl,
            objects.Count);

        var events = objects
            .Select(
                dict =>
                {
                    var onObjectOperationEvent = new OnObjectOperationEvent(
                        dict,
                        OnObjectOperationEvent.OperationCreateOrUpdateObject,
                        "salesforce-integrator",
                        sleekflowCompanyId,
                        _salesforceObjectService.ResolveObjectId(dict),
                        entityTypeName,
                        null);

                    return onObjectOperationEvent;
                })
            .ToList();

        foreach (var onObjectOperationEvents in events.Chunk(30))
        {
            await _bus.PublishBatch(
                onObjectOperationEvents,
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
        }

        _logger.LogInformation(
            "Flushed sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, prevNextRecordsUrl {PrevNextRecordsUrls}, nextRecordsUrl {NextRecordsUrls}, count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            syncObjectsBatchInput.NextRecordsUrl,
            nextRecordsUrl,
            objects.Count);

        return new SyncObjectsBatchOutput(objects.Count, nextRecordsUrl);
    }
}