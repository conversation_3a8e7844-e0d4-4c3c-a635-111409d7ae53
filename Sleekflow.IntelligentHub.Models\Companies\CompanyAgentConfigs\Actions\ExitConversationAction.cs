using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class ExitConversationAction : BaseAction
{
    [JsonProperty("conditions")]
    public List<ExitCondition> Conditions { get; set; }

    [JsonConstructor]
    public ExitConversationAction(bool enabled, List<ExitCondition> conditions)
        : base(enabled)
    {
        Conditions = conditions;
    }

    public ExitConversationAction(ExitConversationActionDto dto)
        : base(dto)
    {
        Conditions = dto.Conditions.Select(c => new ExitCondition(c)).ToList();
    }

    public override BaseActionDto ToDto()
    {
        return new ExitConversationActionDto(this);
    }
}