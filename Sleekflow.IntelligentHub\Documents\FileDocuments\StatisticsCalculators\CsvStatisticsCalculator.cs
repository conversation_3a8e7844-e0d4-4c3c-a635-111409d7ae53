using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class CsvStatisticsCalculator : IDocumentStatisticsCalculator
{
    public const int RowsPerPage = 50;

    private readonly IDocumentCounterService _documentCounterService;

    public CsvStatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        var reader = new StreamReader(stream);
        var content = reader.ReadToEnd();
        var totalTokens = _documentCounterService.CountTokens(content);
        var totalWords = _documentCounterService.CountWords(content);
        var totalCharacters = _documentCounterService.CountCharacters(content);
        var numOfRows = content.Count(c => c.Equals('\n')) + 1;
        var totalPages = (numOfRows / RowsPerPage) + (numOfRows % RowsPerPage == 0 ? 0 : 1);
        var fileSizeBytes = (int) stream.Length;
        stream.Position = 0;
        return new DocumentStatistics(totalTokens, totalWords, totalCharacters, totalPages, fileSizeBytes);
    }
}