using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Reviewers;

public class EvaluatedScore
{
    [JsonProperty("category")]
    public string Category { get; set; }

    [JsonProperty("score")]
    public int Score { get; set; }

    [JsonProperty("scores")]
    public Scores? Scores { get; set; }

    [JsonProperty("reason")]
    public string Reason { get; set; }

    [JsonConstructor]
    public EvaluatedScore(string category, int score, string reason, Scores? scores = null)
    {
        Category = category;
        Score = score;
        Reason = reason;
        Scores = scores;
    }
}

public class Scores
{
    [JsonProperty("intent_interest")]
    public int IntentInterest { get; set; }

    [JsonProperty("buying_signals")]
    public int BuyingSignals { get; set; }

    [JsonProperty("depth_specificity")]
    public int DepthSpecificity { get; set; }

    [JsonProperty("sentiment_tone")]
    public int SentimentTone { get; set; }
}