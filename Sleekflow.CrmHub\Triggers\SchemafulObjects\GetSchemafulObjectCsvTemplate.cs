﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects.FileProcessors;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class GetSchemafulObjectCsvTemplate
    : ITrigger<GetSchemafulObjectCsvTemplate.GetSchemafulObjectCsvTemplateInput,
        GetSchemafulObjectCsvTemplate.GetSchemafulObjectCsvTemplateOutput>
{
    private readonly ISchemafulObjectFileService _schemafulObjectFileService;

    public GetSchemafulObjectCsvTemplate(ISchemafulObjectFileService schemafulObjectFileService)
    {
        _schemafulObjectFileService = schemafulObjectFileService;
    }

    public class GetSchemafulObjectCsvTemplateInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectCsvTemplateInput(string schemaId, string sleekflowCompanyId)
        {
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetSchemafulObjectCsvTemplateOutput
    {
        [JsonProperty("csv_string")]
        public string CsvString { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectCsvTemplateOutput(string csvString)
        {
            CsvString = csvString;
        }
    }

    public async Task<GetSchemafulObjectCsvTemplateOutput> F(
        GetSchemafulObjectCsvTemplateInput getSchemafulObjectCsvTemplateInput)
    {
        var csvString = await _schemafulObjectFileService.GetSchemafulObjectCsvTemplateAsync(
            getSchemafulObjectCsvTemplateInput.SchemaId,
            getSchemafulObjectCsvTemplateInput.SleekflowCompanyId);

        return new GetSchemafulObjectCsvTemplateOutput(csvString);
    }
}