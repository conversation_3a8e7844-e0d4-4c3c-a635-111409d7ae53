using Newtonsoft.Json;
using Orleans.TestingHost;
using Sleekflow.FlowHub.Grains;

namespace Sleekflow.FlowHub.Tests.Grains
{
    /// <summary>
    /// Tests interactions with IWorkflowGrain using the TestCluster.
    /// These tests simulate an external client (like an Azure Function) interacting
    /// with the grain hosted in the Orleans cluster (like Flow Hub in Container Apps).
    /// It covers:
    /// 1. Basic client operations: Setting, getting, checking existence, and clearing data
    ///    in the workflow grain's persistent dictionary state via the external client.
    /// 2. Client-Host interaction: Validating that state changes made by the external client
    ///    are correctly reflected and accessible when retrieving the grain state from within
    ///    the host cluster's perspective (using the internal GrainFactory).
    /// </summary>
    [TestFixture]
    public class WorkflowGrainClientTests
    {
        private static TestCluster Cluster => Application.Cluster;

        private IClusterClient _client;

        private IGrainFactory _hostGrainFactory; // Represents access from within the host cluster

        [OneTimeSetUp]
        public void FixtureSetup()
        {
            _client = Cluster.Client;
            _hostGrainFactory = Cluster.GrainFactory;
        }

        [Test]
        public async Task ClientCanSetWorkflowData()
        {
            var workflowVersionedId = $"test-workflow-{Guid.NewGuid()}";

            // Get grain reference via the client
            var workflowGrain = _client.GetGrain<IWorkflowGrain>(workflowVersionedId);

            var key = "Status";
            var value = "Started";

            // Act: Use the client to call the grain's SetAsync method
            await workflowGrain.SetAsync(key, value);

            // Assert: Verify the data was set by getting it back
            var retrievedValue = await workflowGrain.GetAsync(key);
            Assert.AreEqual(value, retrievedValue);
        }

        [Test]
        public async Task ClientCanGetWorkflowData()
        {
            var workflowVersionedId = $"test-workflow-{Guid.NewGuid()}";
            var workflowGrain = _client.GetGrain<IWorkflowGrain>(workflowVersionedId);
            var key = "InitialData";

            var initialValue = new Dictionary<string, object>
            {
                {
                    "Step", 1
                },
                {
                    "Complete", false
                }
            };

            // Arrange: Set some initial data directly (or via another method if applicable)
            await workflowGrain.SetAsync(key, initialValue);

            // Act: Use the client to call the grain's GetAsync method
            var retrievedValue = await workflowGrain.GetAsync(key);

            // Assert: Verify the correct data was retrieved
            Assert.IsNotNull(retrievedValue);
            Assert.IsInstanceOf<Dictionary<string, object>>(retrievedValue);
            var retrievedDict = (Dictionary<string, object>) retrievedValue;
            Assert.AreEqual(1L, retrievedDict["Step"]); // Note: JSON deserialization might convert int to long
            Assert.AreEqual(false, retrievedDict["Complete"]);
        }

        [Test]
        public async Task ClientCanCheckIfKeyExists()
        {
            var workflowVersionedId = $"test-workflow-{Guid.NewGuid()}";
            var workflowGrain = _client.GetGrain<IWorkflowGrain>(workflowVersionedId);
            var existingKey = "CurrentStep";
            var nonExistingKey = "CompletionTime";

            // Arrange: Ensure a key exists
            await workflowGrain.SetAsync(existingKey, "ProcessingStepA");

            // Act & Assert: Use the client to call ContainsKeyAsync
            Assert.IsTrue(await workflowGrain.ContainsKeyAsync(existingKey));
            Assert.IsFalse(await workflowGrain.ContainsKeyAsync(nonExistingKey));
        }

        [Test]
        public async Task ClientCanClearWorkflowData()
        {
            var workflowVersionedId = $"test-workflow-{Guid.NewGuid()}";
            var workflowGrain = _client.GetGrain<IWorkflowGrain>(workflowVersionedId);

            // Arrange: Add some data
            await workflowGrain.SetAsync("Key1", "Value1");
            await workflowGrain.SetAsync("Key2", 123);
            Assert.IsTrue(await workflowGrain.ContainsKeyAsync("Key1")); // Pre-check

            // Act: Use the client to call ClearAsync
            await workflowGrain.ClearAsync();

            // Assert: Verify data is cleared
            Assert.IsFalse(await workflowGrain.ContainsKeyAsync("Key1"));
            Assert.IsFalse(await workflowGrain.ContainsKeyAsync("Key2"));
            Assert.AreEqual(0, await workflowGrain.CountAsync()); // Assuming CountAsync exists
        }

        [Test]
        public async Task ClientSets_HostRetrieves_SimpleString()
        {
            var workflowVersionedId = $"client-host-test-{Guid.NewGuid()}";
            var key = "CurrentStage";
            var valueSetByClient = "Review";

            // Get grain references from both client and host perspectives
            var clientGrainRef = _client.GetGrain<IWorkflowGrain>(workflowVersionedId);
            var hostGrainRef = _hostGrainFactory.GetGrain<IWorkflowGrain>(workflowVersionedId); // Same ID

            // Act (Client): Set data using the client reference
            await clientGrainRef.SetAsync(key, valueSetByClient);

            // Act (Host): Retrieve data using the host reference
            var valueRetrievedByHost = await hostGrainRef.GetAsync(key);

            // Assert: Host should retrieve the same data set by the client
            Assert.AreEqual(valueSetByClient, valueRetrievedByHost);
        }

        [Test]
        public async Task ClientSets_HostRetrieves_ComplexObject()
        {
            var workflowVersionedId = $"client-host-complex-{Guid.NewGuid()}";
            var key = "ExecutionDetails";

            // Use a simple DTO or anonymous type for testing
            var dataSetByClient = new TestExecutionDetails("<EMAIL>", DateTimeOffset.UtcNow, 1);

            var clientGrainRef = _client.GetGrain<IWorkflowGrain>(workflowVersionedId);
            var hostGrainRef = _hostGrainFactory.GetGrain<IWorkflowGrain>(workflowVersionedId);

            // Act (Client): Set complex data
            await clientGrainRef.SetAsync(key, dataSetByClient);

            // Act (Host): Retrieve complex data
            var valueRetrievedByHost = await hostGrainRef.GetAsync(key);

            // Assert:
            Assert.IsNotNull(valueRetrievedByHost);
            Assert.IsInstanceOf<TestExecutionDetails>(valueRetrievedByHost);
            var retrievedData = (TestExecutionDetails)valueRetrievedByHost;

            Assert.AreEqual(dataSetByClient.StartedBy, retrievedData.StartedBy);
            Assert.AreEqual(dataSetByClient.Priority, retrievedData.Priority);

            // Calculate the difference
            TimeSpan difference = dataSetByClient.StartTime - retrievedData.StartTime;
            // Assert that the absolute difference is small (e.g., less than 5 milliseconds)
            Assert.Less(difference.Duration(), TimeSpan.FromMilliseconds(5),
                $"DateTimeOffset difference exceeded tolerance. Expected: {dataSetByClient.StartTime}, Actual: {retrievedData.StartTime}");
        }

        [Test]
        public async Task ClientSets_HostChecksExistence()
        {
            var workflowId = $"client-host-exists-{Guid.NewGuid()}";
            var keySetByClient = "HasApproval";
            var keyNotSet = "RejectionReason";


            var clientGrainRef = _client.GetGrain<IWorkflowGrain>(workflowId);
            var hostGrainRef = _hostGrainFactory.GetGrain<IWorkflowGrain>(workflowId);

            // Act (Client): Set a key
            await clientGrainRef.SetAsync(keySetByClient, true);

            // Act (Host): Check existence using the host reference
            var keyExistsOnHost = await hostGrainRef.ContainsKeyAsync(keySetByClient);
            var keyDoesNotExistOnHost = await hostGrainRef.ContainsKeyAsync(keyNotSet);


            // Assert: Host should correctly report existence based on client action
            Assert.IsTrue(keyExistsOnHost);
            Assert.IsFalse(keyDoesNotExistOnHost);
        }

        [Test]
        public async Task ClientClears_HostVerifiesCleared()
        {
            var workflowId = $"client-host-clear-{Guid.NewGuid()}";
            var key1 = "TempData1";
            var key2 = "TempData2";

            var clientGrainRef = _client.GetGrain<IWorkflowGrain>(workflowId);
            var hostGrainRef = _hostGrainFactory.GetGrain<IWorkflowGrain>(workflowId);

            // Arrange: Client sets some data first
            await clientGrainRef.SetAsync(key1, "value1");
            await clientGrainRef.SetAsync(key2, 123);

            Assert.IsTrue(
                await hostGrainRef.ContainsKeyAsync(key1),
                "Pre-check failed: Host could not see data set by client."); // Verify initial state visible to host

            // Act (Client): Clear data using the client reference
            await clientGrainRef.ClearAsync();

            // Act (Host): Check data existence and count using the host reference
            var key1ExistsOnHost = await hostGrainRef.ContainsKeyAsync(key1);
            var key2ExistsOnHost = await hostGrainRef.ContainsKeyAsync(key2);
            var countOnHost = await hostGrainRef.CountAsync(); // Assuming CountAsync exists

            // Assert: Host should see that the data is gone
            Assert.IsFalse(key1ExistsOnHost, "Key1 should not exist on host after client clear.");
            Assert.IsFalse(key2ExistsOnHost, "Key2 should not exist on host after client clear.");
            Assert.AreEqual(0, countOnHost, "Count on host should be 0 after client clear.");
        }
    }

    public class TestExecutionDetails
    {
        [JsonProperty("started_by")]
        public string StartedBy { get; set; }

        [JsonProperty("start_time")]
        public DateTimeOffset StartTime { get; set; }

        [JsonProperty("priority")]
        public int Priority { get; set; } // Keep as int here

        [JsonConstructor]
        public TestExecutionDetails(string startedBy, DateTimeOffset startTime, int priority)
        {
            StartedBy = startedBy;
            StartTime = startTime;
            Priority = priority;
        }
    }
}