using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Zoho.Connections;

public interface IZohoConnectionService
{
    Task<List<ZohoConnection>> GetConnectionsAsync(string sleekflowCompanyId);

    Task<ZohoConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string organizationId,
        string authenticationId,
        string name,
        string environment,
        bool isActive);

    Task<ZohoConnection> GetByIdAsync(string id, string sleekflowCompanyId);

    Task<ZohoConnection?> GetByAuthenticationIdAsync(string sleekflowCompanyId, string authenticationId);

    Task PatchAsync(string id, string sleekflowCompanyId, string name, bool isActive);

    Task<ZohoConnection> PatchAndGetAsync(string id, string sleekflowCompanyId, string name);

    Task DeleteAsync(string id, string sleekflowCompanyId);
}

public class ZohoConnectionService : ISingletonService, IZohoConnectionService
{
    private readonly IZohoConnectionRepository _zohoConnectionRepository;
    private readonly IIdService _idService;

    public ZohoConnectionService(
        IZohoConnectionRepository zohoConnectionRepository,
        IIdService idService)
    {
        _zohoConnectionRepository = zohoConnectionRepository;
        _idService = idService;
    }

    public async Task<List<ZohoConnection>> GetConnectionsAsync(string sleekflowCompanyId)
    {
        return await _zohoConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<ZohoConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string organizationId,
        string authenticationId,
        string name,
        string environment,
        bool isActive)
    {
        var connection = new ZohoConnection(
            _idService.GetId("ZohoConnection"),
            sleekflowCompanyId,
            organizationId,
            authenticationId,
            name,
            environment,
            isActive);

        return await _zohoConnectionRepository.CreateAndGetAsync(connection, sleekflowCompanyId);
    }

    public async Task<ZohoConnection> GetByIdAsync(string id, string sleekflowCompanyId)
    {
        return await _zohoConnectionRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<ZohoConnection?> GetByAuthenticationIdAsync(string sleekflowCompanyId, string authenticationId)
    {
        var connections = await _zohoConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId && x.AuthenticationId == authenticationId);

        return connections.FirstOrDefault();
    }

    public async Task PatchAsync(string id, string sleekflowCompanyId, string name, bool isActive)
    {
        await _zohoConnectionRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
                PatchOperation.Replace("/is_active", isActive),
            });
    }

    public async Task<ZohoConnection> PatchAndGetAsync(string id, string sleekflowCompanyId, string name)
    {
        return await _zohoConnectionRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
            });
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId)
    {
        var connection = await _zohoConnectionRepository.GetAsync(id, sleekflowCompanyId);
        if (connection is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var deleteAsync = await _zohoConnectionRepository.DeleteAsync(id, sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the ZohoConnection with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}