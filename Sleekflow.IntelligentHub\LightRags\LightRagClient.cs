using System.Text;
using System.Text.Json;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Https;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Models;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.JsonConfigs;

namespace Sleekflow.IntelligentHub.LightRags;

public interface ILightRagClient
{
    Task<(List<string> HighLevelKeywords, List<string> LowLevelKeywords)> GenerateKeywordsAsync(
        Kernel kernel,
        string query);

    Task<T?> ExecuteAsync<T>(string path, string sleekflowCompanyId, string agentId, object args);
}

public class LightRagClient : ILightRagClient, IScopedService
{
    private readonly ILogger _logger;
    private readonly HttpClient _httpClient;
    private readonly ILightRagConfig _lightRagConfig;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public LightRagClient(
        ILogger<LightRagClient> logger,
        ILightRagConfig lightRagConfig,
        IHttpClientFactory httpClientFactory,
        ITextTranslationService textTranslationService,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _lightRagConfig = lightRagConfig;
        _textTranslationService = textTranslationService;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _httpClient = httpClientFactory.CreateClient("default-light-rag-client");
    }

    public async Task<(List<string> HighLevelKeywords, List<string> LowLevelKeywords)> GenerateKeywordsAsync(
        Kernel kernel,
        string query)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

        EnforceGenerateKeywordsOutputSchema(promptExecutionSettings);

        var generateKeywordsFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "GenerateKeywords",
                Description =
                    "An intelligent keyword extraction system that analyzes input text to generate two distinct sets of keywords: high-level conceptual terms and low-level specific details. This dual-layer approach ensures comprehensive topic coverage and improved semantic understanding.",
                Template =
                    """
                    <message role="system">
                    You are an expert semantic analyzer specialized in categorizing search keywords for knowledge base queries. Your primary role is to analyze input queries and extract two types of keywords:

                    1. High-Level Keywords:
                    - Single-word, broad category terms that represent domains, fields, or general concepts
                    - Examples: healthcare, education, technology, business, science
                    - Should be general enough to cover wide categories in knowledge bases
                    - Typically used for category or domain-level searches

                    2. Low-Level Keywords:
                    - Specific terms that represent concrete items, actions, or attributes
                    - Must be precise terms that can stand alone in searches
                    - Examples: "discount packages", "agile", "software licenses", "machine learning algorithms"
                    - Should reflect actual database content terminology
                    - Focus on searchable terms that yield specific results

                    For each input query, you will:
                    1. Analyze the semantic structure and intent
                    2. Extract and categorize keywords considering their independent search value
                    3. Output a JSON structure with both keyword types
                    4. Ensure high-level keywords remain single terms for broad coverage
                    5. Ensure low-level keywords are specific and precise enough for targeted searches
                    6. Translate the keywords into English for further processing

                    Keep in mind that the final search results will be a union of individual keyword searches, so each keyword must be meaningful and specific enough to stand alone in a query.

                    Format your response as:
                    ```json
                    {
                        "high_level_keywords": [
                            {
                                "reasoning": "detailed explanation of why this term is chosen and its coverage",
                                "keyword": "single word broad term"
                            }
                        ],
                        "low_level_keywords": [
                            {
                                "reasoning": "detailed explanation of why this term is relevant and its search value",
                                "keyword": "specific search term"
                            }
                        ]
                    }
                    ```
                    </message>
                    <message role="user">
                    What are the current healthcare products available, including any bundled offers or special discounts?
                    </message>
                    <message role="assistant">
                    {
                        "high_level_keywords": [
                            {
                                "reasoning": "Primary domain keyword that directly matches the query's focus on healthcare products and offerings",
                                "keyword": "healthcare"
                            },
                            {
                                "reasoning": "Essential category covering medical supplies and equipment that fall under healthcare products",
                                "keyword": "medical"
                            },
                            {
                                "reasoning": "Broader category encompassing general health and preventive products that complement traditional healthcare items",
                                "keyword": "wellness"
                            },
                            {
                                "reasoning": "Captures all promotional aspects mentioned in the query including special discounts and bundled offers",
                                "keyword": "promotion"
                            }
                        ],
                        "low_level_keywords": [
                            {
                                "reasoning": "Direct match to the query's core focus on available healthcare products",
                                "keyword": "healthcare products"
                            },
                            {
                                "reasoning": "Specifically addresses the bundled offerings mentioned in the query",
                                "keyword": "bundle"
                            },
                            {
                                "reasoning": "Captures all types of promotional deals and special arrangements mentioned in the query",
                                "keyword": "offers"
                            },
                            {
                                "reasoning": "Explicitly mentioned in the query as a specific type of promotional benefit",
                                "keyword": "discounts"
                            }
                        ]
                    }
                    </message>
                    <message role="user">
                    Can you provide detailed information about the 6-in-1 Rapid Antigen Test Kit from INDICAID, including its features, benefits, and any relevant usage instructions?
                    </message>
                    <message role="assistant">
                    {
                        "high_level_keywords": [
                            {
                                "reasoning": "Primary category as the query focuses on a medical diagnostic device used for health testing",
                                "keyword": "medical"
                            },
                            {
                                "reasoning": "Essential category since the query specifically relates to a diagnostic test kit for antigen detection",
                                "keyword": "diagnostics"
                            },
                            {
                                "reasoning": "Fundamental category covering all testing-related aspects, including rapid tests and detection kits",
                                "keyword": "test"
                            },
                            {
                                "reasoning": "Broader category encompassing product specifications and technical information requested in the query",
                                "keyword": "technical"
                            }
                        ],
                        "low_level_keywords": [
                            {
                                "reasoning": "Manufacturer brand explicitly mentioned in the query",
                                "keyword": "INDICAID"
                            },
                            {
                                "reasoning": "Specific product type mentioned in the query, capturing the exact testing methodology",
                                "keyword": "rapid antigen"
                            },
                            {
                                "reasoning": "Particular product model referenced in the query",
                                "keyword": "6-in-1"
                            },

                            {
                                "reasoning": "Addresses the query's specific request for product usage guidance",
                                "keyword": "instructions"
                            },
                            {
                                "reasoning": "Captures the query's request for detailed product specifications and characteristics",
                                "keyword": "features"
                            },
                            {
                                "reasoning": "Relates to the query's interest in understanding product advantages",
                                "keyword": "benefits"
                            }
                        ]
                    }
                    </message>
                    <message role="user">
                    {{$query}}
                    </message>
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "query",
                        Description =
                            "The input text query to be analyzed for keyword extraction. Can handle multiple languages and will process queries of any length while maintaining contextual relevance.",
                        IsRequired = true
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description =
                        "A structured JSON response containing two categorized arrays of keywords: high-level concepts and low-level details.",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });
        var results = await generateKeywordsFunction.InvokeAsync<string>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "query", query
                }
            });
        var generatedKeywords =
            JsonConvert.DeserializeObject<GenerateKeywordsResponse>(
                results ?? """{"highLevelKeywords":[],"lowLevelKeywords":[]}""")!;

        return (
            generatedKeywords.HighLevelKeywords.Select(k => k.Keyword).ToList(),
            generatedKeywords.LowLevelKeywords.Select(k => k.Keyword).ToList());
    }

    private static void EnforceGenerateKeywordsOutputSchema(
        PromptExecutionSettings promptExecutionSettings)
    {
        if (promptExecutionSettings is OpenAIPromptExecutionSettings openAiPromptExecutionSettings)
        {
            var outputSchema =
                """
                {
                    "$schema": "http://json-schema.org/draft-07/schema#",
                    "type": "object",
                    "strict": true,
                    "additionalProperties": false,
                    "required": ["high_level_keywords", "low_level_keywords"],
                    "properties": {
                        "high_level_keywords": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "additionalProperties": false,
                                "required": ["reasoning", "keyword"],
                                "properties": {
                                    "reasoning": {
                                        "type": "string",
                                        "description": "Detailed explanation of the keyword's selection and scope"
                                    },
                                    "keyword": {
                                        "type": "string",
                                        "description": "A single word representing a broad category or domain"
                                    }
                                }
                            }
                        },
                        "low_level_keywords": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "additionalProperties": false,
                                "required": ["reasoning", "keyword"],
                                "properties": {
                                    "reasoning": {
                                        "type": "string",
                                        "description": "Detailed explanation of the term's relevance and search value"
                                    },
                                    "keyword": {
                                        "type": "string",
                                        "description": "A specific term or phrase for precise searching"
                                    }
                                }
                            }
                        }
                    }
                }
                """;

#pragma warning disable SKEXP0010
            openAiPromptExecutionSettings.ResponseFormat = ChatResponseFormat.CreateJsonSchemaFormat(
                jsonSchemaFormatName: "GenerateKeywordsResponse",
                jsonSchema: BinaryData.FromString(outputSchema),
                jsonSchemaIsStrict: true);
#pragma warning restore SKEXP0010
        }
#pragma warning disable SKEXP0070
        else if (promptExecutionSettings is GeminiPromptExecutionSettings geminiPromptExecutionSettings)
        {
            // Define the JSON schema for the structured output
            var outputSchema =
                """
                {
                    "type": "object",
                    "required": ["high_level_keywords", "low_level_keywords"],
                    "properties": {
                        "high_level_keywords": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "required": ["reasoning", "keyword"],
                                "properties": {
                                    "reasoning": {
                                        "type": "string",
                                        "description": "Detailed explanation of the keyword's selection and scope"
                                    },
                                    "keyword": {
                                        "type": "string",
                                        "description": "A single word representing a broad category or domain"
                                    }
                                }
                            }
                        },
                        "low_level_keywords": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "required": ["reasoning", "keyword"],
                                "properties": {
                                    "reasoning": {
                                        "type": "string",
                                        "description": "Detailed explanation of the term's relevance and search value"
                                    },
                                    "keyword": {
                                        "type": "string",
                                        "description": "A specific term or phrase for precise searching"
                                    }
                                }
                            }
                        }
                    }
                }
                """;

            geminiPromptExecutionSettings.ResponseMimeType = "application/json";
            geminiPromptExecutionSettings.ResponseSchema = JsonDocument.Parse(outputSchema);
        }
#pragma warning restore SKEXP0070
    }

    /**
     * Get translated keywords
     * It will translate the keywords to the target language and return the translated keywords with the original keywords
     * @param targetLanguage The target language to translate the keywords
     * @param keywords The keywords to translate
     * @return The translated keywords with the original keywords
     */
    private async Task<List<string>> GetTranslatedKeywords(string targetLanguage, List<string>? keywords)
    {
        if (keywords is null)
        {
            return [];
        }

        var keyword = string.Join(",", keywords);
        var translatedKeywords = await _textTranslationService.TranslateByAzureTranslationServiceAsync(
            targetLanguage,
            keyword);
        return translatedKeywords.Split(',').Concat(keywords).Select(x => x.Trim()).Distinct().ToList();
    }

    public async Task<T?> ExecuteAsync<T>(string path, string sleekflowCompanyId, string agentId, object args)
    {
        var domain = _lightRagConfig.SupportedCompaniesWithNeo4JSupportedCompanies.Contains(sleekflowCompanyId)
            ? _lightRagConfig.DomainWithNeo4j
            : _lightRagConfig.Domain;

        var url = $"{domain}/{path}";

        var argsJson = JsonConvert.SerializeObject(
            args,
            JsonConfig.DefaultJsonSerializerSettings);

        var response = await HttpPolicies.HttpTransientErrorRetryPolicy
            .ExecuteAsync(async () =>
            {
                var reqMsg = new HttpRequestMessage(HttpMethod.Post, url);

                // Add API key to the request header
                reqMsg.Headers.Add("X-API-Key", _lightRagConfig.APIKey);
                reqMsg.Headers.Add("X-Sleekflow-Company-Id", GetAgentKnowledgeBaseId(sleekflowCompanyId, agentId));
                reqMsg.Content = new StringContent(argsJson, Encoding.UTF8, "application/json");
                var response = await _httpClient.SendAsync(reqMsg);

                _logger.LogInformation(
                    "RAG Client: {CommandName} {ArgsJson} {StatusCode}",
                    path,
                    argsJson,
                    response.StatusCode);

                response.EnsureSuccessStatusCode();
                return response;
            });

        return await response.Content.ReadFromJsonAsync<T>();
    }

    public static string GetAgentKnowledgeBaseId(string sleekflowCompanyId, string agentId)
    {
        if (agentId is "TEST")
        {
            return sleekflowCompanyId;
        }

        return $"{sleekflowCompanyId}_{agentId}";
    }
}