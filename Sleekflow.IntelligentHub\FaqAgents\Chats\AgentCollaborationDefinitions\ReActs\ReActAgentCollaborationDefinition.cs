using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;
using JsonException = System.Text.Json.JsonException;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ReActs;

/// <summary>
/// Defines the contract for the ReAct agent collaboration definition.
/// The ReAct collaboration mode provides a specialized agent group focused solely on
/// executing actions rather than generating conversational responses.
/// </summary>
public interface IReActAgentCollaborationDefinition : IAgentCollaborationDefinition
{
}

/// <summary>
/// Implementation of the ReAct agent collaboration definition which provides a specialized agent group
/// focused solely on executing actions rather than generating conversational responses.
/// </summary>
public class ReActAgentCollaborationDefinition
    : BaseAgentCollaborationDefinition, IReActAgentCollaborationDefinition, IScopedService
{
    private readonly ILogger<ReActAgentCollaborationDefinition> _logger;
    private readonly Kernel _kernel;
    private readonly ISummaryPlugin _summaryPlugin;
    private readonly ICompanyConfigService _companyConfigService;
    private readonly IChatHistoryEnricherFactory _enricherFactory;
    private readonly IReActAgentDefinitions _reActAgentDefinitions;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public ReActAgentCollaborationDefinition(
        ILogger<ReActAgentCollaborationDefinition> logger,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IChatHistoryEnricherFactory enricherFactory,
        IReActAgentDefinitions reActAgentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IFileContentExtractionPlugin fileContentExtractionPlugin)
        : base(
            logger,
            kernel,
            summaryPlugin,
            languagePlugin,
            agentDurationTracker,
            companyConfigService,
            enricherFactory,
            fileContentExtractionPlugin)
    {
        _logger = logger;
        _kernel = kernel;
        _summaryPlugin = summaryPlugin;
        _companyConfigService = companyConfigService;
        _enricherFactory = enricherFactory;
        _reActAgentDefinitions = reActAgentDefinitions;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    public override Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig)
    {
        var taskExecutionAgent = _reActAgentDefinitions.GetTaskExecutionAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1));

        return Task.FromResult(
            new List<Agent>
            {
                taskExecutionAgent
            });
    }

    public override SelectionStrategy CreateSelectionStrategy(Kernel kernel)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

        return new KernelFunctionSelectionStrategy(
            KernelFunctionFactory.CreateFromMethod(
#pragma warning disable SA1114

                // The parameter name should not be discarded, as it is being used in the reflection
                (string history) => _reActAgentDefinitions.TaskExecutionAgentName,
#pragma warning restore SA1114
                "ReActCoordinatingFunction"),
            kernel)
        {
            HistoryReducer = new ChatHistoryTruncationReducer(1),
            HistoryVariableName = "history",
            ResultParser = result => result.GetValue<string>() ?? throw new NotImplementedException(),
            Arguments = new KernelArguments(promptExecutionSettings),
            UseInitialAgentAsFallback = true
        };
    }

    public override RegexTerminationStrategy CreateTerminationStrategy(List<Agent> agents)
    {
        // Terminate when TaskExecutionAgent produces a message with "phase": "report"
        return new RegexTerminationStrategy(
            expressions:
            [
                "\"phase\":\\s*\"report\""
            ])
        {
            MaximumIterations = 15,
            AutomaticReset = true,
            Agents = agents.Where(a => a.Name == _reActAgentDefinitions.TaskExecutionAgentName).ToList()
        };
    }

    public override async Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? agentConfig)
    {
        var companyConfig = await _companyConfigService.GetConfigAsync(replyGenerationContext.SleekflowCompanyId);

        var sanitizedChatResult = await GetSanitizedChatResult(
            chatEntries,
            agentCollaborationConfig,
            companyConfig);

        var chatHistoryStr = string.Join("--\n", sanitizedChatResult.EntryTexts);
        _logger.LogInformation("Chat history: {ChatHistoryStr}", chatHistoryStr);

        var allContactProperties = replyGenerationContext.ContactProperties ?? new Dictionary<string, string>();
        _logger.LogInformation(
            "All Contact Properties: {AllContactProperties}",
            JsonConvert.SerializeObject(allContactProperties));

        var (enricherSectionExplanations, enrichmentSectionsStr) = await GetEnrichmentSectionsStr(
            agentCollaborationConfig,
            replyGenerationContext);

        // Prepare the explanation of each section for the agent
        var sectionExplanations = new List<string>
        {
            "CURRENT TIME is the current UTC time when this conversation is happening. Reference this time when discussing time-sensitive topics, operating hours, or when the customer asks about current time-related information."
        };
        sectionExplanations.AddRange(
            enricherSectionExplanations);
        sectionExplanations.Add(
            "CONVERSATION CONTEXT is the historical messages between our company and the customer.");
        sectionExplanations.Add(
            "CORE INSTRUCTION is a configuration specifying the core instruction that you should understand and always follow.");
        sectionExplanations.Add(
            "INPUT is the input from the user. e.g. the event that triggered the current action.");

        var context =
            $"""
             ====CURRENT TIME====
             {DateTimeOffset.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")} UTC
             ====CURRENT TIME====

             {enrichmentSectionsStr}

             ====CONVERSATION CONTEXT====
             {chatHistoryStr}
             ====CONVERSATION CONTEXT====

             ====CORE INSTRUCTION====
             {agentCollaborationConfig.AdditionalInstructionCore ?? "No core instructions. You should just end."}
             ====CORE INSTRUCTION====

             ====INPUT====
             {replyGenerationContext.Input ?? "No input."}
             ====INPUT====

             {string.Join("\n", sectionExplanations)}
             """;

        // Modify the chat message to include enrichment sections
        agentGroupChat?.AddChatMessage(
            new ChatMessageContent(
                AuthorRole.User,
                context)
            {
                AuthorName = "Context"
            });

        return (chatHistoryStr, context);
    }

    private bool IsTaskExecutionAgentFinalReport(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            return false;
        }

        try
        {
            // Parse the content as JSON and validate the report structure
            var jsonReport = JsonDocument.Parse(content);
            var root = jsonReport.RootElement;

            return root.TryGetProperty("result", out var phaseElement) &&
                   phaseElement.GetString() == "completed" &&
                   root.TryGetProperty("agent_name", out var agentNameElement) &&
                   agentNameElement.GetString() == _reActAgentDefinitions.TaskExecutionAgentName;
        }
        catch (JsonException)
        {
            // Content is not valid JSON, so it's not the report
            return false;
        }
    }

    public override string GetFinalReplyTag()
    {
        // For ReAct, we don't need a specific tag for the final reply
        // as we're returning the raw JSON output
        return "executed_tools";
    }

    public override string GetSourceTag()
    {
        // For ReAct, we don't track sources in the traditional sense
        return string.Empty;
    }

    public override async Task<string> GetFinalReplyAsync(ChatHistory chatHistory)
    {
        // First message is the latest one
        var finalMessage = chatHistory.FirstOrDefault();

        if (finalMessage != null &&
            finalMessage.AuthorName == _reActAgentDefinitions.TaskExecutionAgentName &&
            IsTaskExecutionAgentFinalReport(finalMessage.Content!))
        {
            return finalMessage.Content ?? JsonConvert.SerializeObject(
                new
                {
                    agent_name = _reActAgentDefinitions.TaskExecutionAgentName,
                    result = "failure",
                    error = "Final report not found"
                });
        }
        else
        {
            _logger.LogWarning("Expected final report not found");
            return JsonConvert.SerializeObject(
                new
                {
                    agent_name = _reActAgentDefinitions.TaskExecutionAgentName,
                    result = "failure",
                    error = "Final report not found"
                });
        }
    }

    public override async Task<string> GetSourceAsync(ChatHistory chatHistory, string groupChatIdStr)
    {
        // For ReAct, we don't have traditional sources, so we return an empty string
        return await Task.FromResult(string.Empty);
    }
}