﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;

public abstract class DataTypeBase : IDataType
{
    [JsonProperty(IDataType.PropertyNameName)]
    public abstract string Name { get; set; }

    [JsonProperty(IDataType.PropertyNameInnerSchema)]
    public virtual InnerSchema? InnerSchema { get; set; }

    public virtual bool HasInnerSchema()
        => false;

    public virtual InnerSchema GetInnerSchema()
    {
        throw new InvalidOperationException("The property does not support InnerSchema.");
    }
}