using System.Text;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Utils;

public static class AgentUtils
{
    public static int GetResponseLength(string responseLevel)
    {
        return responseLevel switch
        {
            ResponseLevels.Short => 50,
            ResponseLevels.Normal => 100,
            ResponseLevels.Long => 150,
            _ => 100
        };
    }

    public static string GetAgenticRestrictivenessPrompt(string restrictivenessLevel)
    {
        return restrictivenessLevel switch
        {
            RestrictivenessLevels.Normal =>
                "Note that you are not allowed to use any information not found within the tool output.",
            RestrictivenessLevels.Relaxed =>
                """
                ---Core Responsibilities: Supply additional information from the Internet---
                - You need to fetch relevant information from the Internet and include as much information as possible to supplement our knowledge.
                - Answer in extreme detail and cite the source URL of all information from the Internet.
                - Make sure the information is recent and accurate.
                - If no additional information is found, just say so.
                """,
            _ => "Note that you are not allowed to use any information not found within the tool output."
        };
    }

    public static string GetAgenticRestrictivenessOutputPrompt(string restrictivenessLevel)
    {
        return restrictivenessLevel switch
        {
            RestrictivenessLevels.Normal => string.Empty,
            RestrictivenessLevels.Relaxed => "[Any additional information from the Internet]",
            _ => string.Empty
        };
    }

    public static string TranslateGuardrailsToInstructions(List<Guardrail>? guardrails)
    {
        if (guardrails == null || guardrails.Count == 0)
        {
            return "No guardrails.";
        }

        // Pre-calculate approximate capacity to reduce StringBuilder reallocations
        // Rough estimate: header (80) + each guardrail (~200 chars average) + footer (80)
        var estimatedCapacity = 160 + (guardrails.Count * 200);
        var sb = new StringBuilder(estimatedCapacity);

        sb.AppendLine("## Important Guidelines and Restrictions");
        sb.AppendLine();
        sb.AppendLine("You must follow these specific guardrails when generating the response:");

        for (int i = 0; i < guardrails.Count; i++)
        {
            var guardrail = guardrails[i];
            sb.AppendLine();
            sb.Append("### ").Append(i + 1).Append(". ").AppendLine(guardrail.Title);
            sb.AppendLine();
            sb.Append("**Watch out for:** ").AppendLine(guardrail.ObserveFor);
            sb.AppendLine();
            sb.Append("**How to respond:** ").AppendLine(guardrail.HowToReact);
        }

        sb.AppendLine();
        sb.AppendLine(
            "**Priority:** If multiple guardrails apply to the same situation, follow the most restrictive guideline to ensure safety and compliance.");
        sb.AppendLine();
        sb.AppendLine("Please ensure you strictly adhere to these guidelines when generating the response.");
        sb.AppendLine();

        return sb.ToString();
    }
}