using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;

namespace Sleekflow.Infras.DbScalar.ScaleDbDownTimerTrigger;

public static class ScaleDownDbTimerTrigger
{
    private static readonly HttpClient Client = new ();

    [Function("ScaleDownDb")]
    public static async Task RunAsync([TimerTrigger("0 0 13 * * 1-5")] TimerInfo myTimer, ILogger log)
    {
        log.LogInformation($"C# Timer trigger function executed at: {DateTime.UtcNow}");
        var scaleDbConfig = new MyConfig();

        // Find via this link : https://learn.microsoft.com/en-us/answers/questions/834401/hi-i-want-my-client-id-and-client-secret-key
        var tenantId = scaleDbConfig.TenantId;
        var clientId = scaleDbConfig.ClientId;
        var clientSecret = scaleDbConfig.ClientSecret;

        var accessToken = await DbScalar.GetAccessTokenAsync(Client, tenantId, clientId, clientSecret);

        var subscriptionId = scaleDbConfig.SubscriptionId;
        var resourceGroupName = scaleDbConfig.ResourceGroupName;
        var serverName = scaleDbConfig.ServerName;
        var databaseName = scaleDbConfig.DatabaseName;

        var desiredCapacity = scaleDbConfig.DesiredCapacityDown;
        var desiredHighAvailabilityReplicaCount = scaleDbConfig.DesiredHighAvailabilityReplicaCountDown;

        // Find via this link : https://learn.microsoft.com/en-us/azure/active-directory/fundamentals/active-directory-how-to-find-tenant
        await DbScalar.ScaleDbAsync(
            Client,
            accessToken,
            subscriptionId,
            resourceGroupName,
            serverName,
            databaseName,
            desiredCapacity,
            desiredHighAvailabilityReplicaCount,
            log);
    }
}