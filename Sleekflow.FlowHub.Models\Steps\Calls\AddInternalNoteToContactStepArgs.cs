using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AddInternalNoteToContactStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.add-internal-note-to-contact";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [Required]
    [JsonProperty("content__expr")]
    public string ContentExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Messaging;

    [JsonConstructor]
    public AddInternalNoteToContactStepArgs(
        string contactIdExpr,
        string contentExpr)
    {
        ContactIdExpr = contactIdExpr;
        ContentExpr = contentExpr;
    }
}