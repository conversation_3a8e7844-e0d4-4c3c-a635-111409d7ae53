using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Events;

public class OnExternalStepSubmittedEvent
{
    public string StateId { get; set; }

    public JObject Step { get; set; }

#pragma warning disable JA1001
#pragma warning disable S2365
    [JsonIgnore]
    public Step InternalStep
    {
        get
        {
            return Step.ToObject<Step>()!;
        }
    }
#pragma warning restore JA1001
#pragma warning restore S2365

    [JsonConstructor]
    public OnExternalStepSubmittedEvent(string stateId, JObject step)
    {
        StateId = stateId;
        Step = step;
    }
}