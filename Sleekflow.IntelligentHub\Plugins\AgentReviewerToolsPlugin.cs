using System.ComponentModel;
using System.Text.Json;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;
using Polly;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Constants;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Plugins;

[method: JsonConstructor]
public class EvaluationResult(string decision, string reasoning)
{
    [JsonProperty("decision")]
    public string Decision { get; set; } = decision;

    [JsonProperty("reasoning")]
    public string Reasoning { get; set; } = reasoning;
}

public interface IAgentReviewerToolsPlugin
{
    [KernelFunction("evaluate_reply_comprehensively")]
    [Description(
        """
        Conducts a comprehensive evaluation of the proposed customer reply across five critical criteria:
        1. Strategic Alignment: Checks alignment with provided strategic guidance.
        2. Knowledge Integration: Verifies accurate use of provided knowledge.
        3. Placeholder Absence: Ensures no placeholder text remains.
        4. Personalization: Confirms relevance to the customer's inquiry.
        5. Language Alignment: Matches the reply to the customer's regional language.
        This all-in-one evaluation ensures the reply is complete, accurate, and tailored to the customer's needs while adhering to communication strategies.
        """)]
    [return: Description(
        "A structured evaluation result containing individual assessments for each of the five criteria—Strategic Alignment, Knowledge Integration, Placeholder Absence, Personalization, and Language Alignment—each with a decision (Yes or No) and a reasoning explaining the decision.")]
    Task<Dictionary<string, EvaluationResult>> EvaluateReplyComprehensivelyAsync(
        Kernel kernel);
}

public class AgentReviewerToolsPlugin : IAgentReviewerToolsPlugin, IScopedService
{
    private readonly ILogger<AgentReviewerToolsPlugin> _logger;
    private readonly ILongAgentCollaborationChatCacheService _longAgentCollaborationChatCacheService;
    private readonly ILanguagePlugin _languagePlugin;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public AgentReviewerToolsPlugin(
        ILogger<AgentReviewerToolsPlugin> logger,
        ILongAgentCollaborationChatCacheService longAgentCollaborationChatCacheService,
        ILanguagePlugin languagePlugin,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _longAgentCollaborationChatCacheService = longAgentCollaborationChatCacheService;
        _languagePlugin = languagePlugin;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    /// <summary>
    /// Truncates a string for logging by replacing newlines with spaces and limiting length.
    /// </summary>
    /// <param name="input">The string to truncate.</param>
    /// <param name="maxLength">Maximum length of the output string (default: 50).</param>
    /// <returns>Truncated string with newlines replaced.</returns>
    private static string TruncateForLogging(string? input, int maxLength = 50)
    {
        if (string.IsNullOrEmpty(input))
        {
            return string.Empty;
        }

        var noNewlines = input.Replace(Environment.NewLine, " ");
        return noNewlines.Length <= maxLength
            ? noNewlines
            : noNewlines[..maxLength] + "...";
    }

    [KernelFunction("evaluate_reply_comprehensively")]
    [Description(
        """
        Conducts a comprehensive evaluation of the proposed customer reply across five critical criteria:
        1. Strategic Alignment: Checks alignment with provided strategic guidance.
        2. Knowledge Integration: Verifies accurate use of provided knowledge.
        3. Placeholder Absence: Ensures no placeholder text remains.
        4. Personalization: Confirms relevance to the customer's inquiry.
        5. Language Alignment: Matches the reply to the customer's regional language.
        This all-in-one evaluation ensures the reply is complete, accurate, and tailored to the customer's needs while adhering to communication strategies.
        """)]
    [return: Description(
        "A structured evaluation result containing individual assessments for each of the five criteria—Strategic Alignment, Knowledge Integration, Placeholder Absence, Personalization, and Language Alignment—each with a decision (Yes or No) and a reasoning explaining the decision.")]
    public async Task<Dictionary<string, EvaluationResult>> EvaluateReplyComprehensivelyAsync(
        Kernel kernel)
    {
        // Define a retry policy (you can adjust parameters as needed)
        var retryPolicy = Policy
            .Handle<Exception>() // Retry on any exception
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider:
                attempt => TimeSpan.FromSeconds(Math.Pow(2, attempt)), // Exponential backoff: 2s, 4s, 8s
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        exception,
                        "Retry {RetryCount} in {TimeSpan} due to {ExceptionType}: {ExceptionMessage}",
                        retryCount,
                        timeSpan,
                        exception.GetType().Name,
                        exception.Message);
                });

        // Execute tasks in parallel with retry policy
        var tasks = new[]
        {
            retryPolicy.ExecuteAsync(() => EvaluateStrategicAlignmentAsync(kernel)),
            retryPolicy.ExecuteAsync(() => EvaluateKnowledgeIntegrationAsync(kernel)),
            retryPolicy.ExecuteAsync(() => EvaluatePlaceholderAbsenceAsync(kernel)),
            retryPolicy.ExecuteAsync(() => EvaluatePersonalizationAsync(kernel)),
            retryPolicy.ExecuteAsync(() => EvaluateLanguageAlignmentAsync(kernel))
        };

        var results = await Task.WhenAll(tasks);

        // Assign results to individual variables
        var strategicAlignmentAsync = results[0];
        var knowledgeIntegrationAsync = results[1];
        var placeholderAbsenceAsync = results[2];
        var personalizationAsync = results[3];
        var languageAlignmentAsync = results[4];

        return new Dictionary<string, EvaluationResult>()
        {
            {
                "strategicAlignment", strategicAlignmentAsync
            },
            {
                "knowledgeIntegration", knowledgeIntegrationAsync
            },
            {
                "placeholderAbsence", placeholderAbsenceAsync
            },
            {
                "personalization", personalizationAsync
            },
            {
                "languageAlignment", languageAlignmentAsync
            }
        };
    }

    private async Task<EvaluationResult> EvaluateStrategicAlignmentAsync(
        Kernel kernel)
    {
        var groupChatId = (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!;

        var strategy = await _longAgentCollaborationChatCacheService.GetStrategyAsync(groupChatId);
        var proposedReply = await _longAgentCollaborationChatCacheService.GetLatestProposedReplyToCustomerAsync(groupChatId);

        _logger.LogInformation(
            "EvaluateStrategicAlignmentAsync started with strategyText: {StrategyText}, proposedReply: {ProposedReply}",
            TruncateForLogging(strategy),
            TruncateForLogging(proposedReply));

        var promptExecutionSettings =
            (OpenAIPromptExecutionSettings) _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI);

        EnforceResponseFormat(promptExecutionSettings);

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluateStrategicAlignment",
                Description = "Evaluate if the proposed reply aligns with the provided strategy.",
                Template =
                    """
                    <message role="system">
                    You are tasked with evaluating whether the proposed customer-facing reply aligns with the provided strategic guidance, enclosed in <STRATEGY> tags. Alignment means the reply reflects the key points, tone, style, and content outlined in the strategy as closely as reasonably possible. Focus on what is explicitly stated in the strategy, but if a specific element (e.g., customer testimonials or personalized details) is mentioned and seems unavailable or impractical to include, assess whether the reply still captures the strategy's intent and core goals without it. If the strategy is vague, determine if the reply fits within its general scope.

                    Respond with a JSON object containing 'decision' ("Yes" or "No") and 'reasoning'.

                    ### Examples:

                    #### Example 1:
                    Strategy:
                    <STRATEGY>
                    - Stage: "Interest" (post-orthodontic customer seeking retainer info).
                    - AIDA: Attention: Acknowledge cost/maintenance concerns. Interest: Explain retainer importance. Desire: Highlight benefits and student discounts. Action: Clear booking instructions.
                    - Tone: Warm, friendly.
                    - Personalization: Address student status, retainer breakage concern.
                    </STRATEGY>
                    Proposed Reply: Hey [Name]! 😊 Just finished your ortho treatment? Congrats! 🎉 Retainers are super important to keep that awesome smile. Worried about costs? We've got student discounts! And if anything breaks, we're here to help. Ready to book? Just click here: [link]
                    { "decision": "Yes", "reasoning": "Uses warm, friendly tone with emojis; acknowledges cost concerns; explains retainer importance; highlights student discounts and breakage support; provides clear booking instructions—fully aligns with the strategy's specific points." }

                    #### Example 2:
                    Strategy:
                    <STRATEGY>
                    - Stage: "Interest" (post-orthodontic customer seeking retainer info).
                    - AIDA: Attention: Acknowledge cost/maintenance concerns. Interest: Explain retainer importance. Desire: Highlight benefits and student discounts. Action: Clear booking instructions.
                    - Tone: Warm, friendly.
                    - Personalization: Address student status, retainer breakage concern, include specific testimonial from a student.
                    </STRATEGY>
                    Proposed Reply: Hey [Name]! 😊 Congrats on finishing ortho! Retainers are key to keeping your smile perfect. Worried about costs or breakage? We've got student discounts and support if anything happens. Book here: [link]
                    { "decision": "Yes", "reasoning": "Matches warm, friendly tone; addresses cost concerns, retainer importance, student discounts, and breakage support; includes clear booking instructions. While it omits the specific student testimonial, this seems unavailable or impractical to include, and the reply still captures the strategy's intent effectively." }

                    #### Example 3:
                    Strategy:
                    <STRATEGY>
                    - Stage: "Decision" (ready to buy, needs reassurance).
                    - Focus: Highlight quality, urgency, include exact delivery timeline.
                    - Tone: Confident, persuasive.
                    </STRATEGY>
                    Proposed Reply: Yo [Name], our retainers are top-notch—perfect for locking in that smile! 😎 Order by Friday for 10% off: [link]. Don't miss out!
                    { "decision": "Yes", "reasoning": "Uses confident, persuasive tone; highlights quality and urgency with a discount and deadline. The exact delivery timeline is missing, but if unavailable, the reply still aligns with the core intent of reassuring and motivating a purchase." }

                    #### Example 4:
                    Strategy:
                    <STRATEGY>
                    - Stage: "Interest" (post-orthodontic customer seeking retainer info).
                    - AIDA: Attention: Acknowledge cost/maintenance concerns. Interest: Explain retainer importance. Desire: Highlight benefits and student discounts. Action: Clear booking instructions.
                    - Tone: Warm, friendly.
                    - Personalization: Address student status, retainer breakage concern.
                    </STRATEGY>
                    Proposed Reply: Hello, we have retainers available. Please contact us for more information.
                    { "decision": "No", "reasoning": "Uses formal tone instead of warm and friendly; fails to acknowledge cost/maintenance concerns, explain retainer importance, mention student discounts, or address breakage; booking instructions are vague. Misses the strategy's intent beyond basic availability." }
                    </message>

                    <message role="user">
                    ### Strategy:
                    <STRATEGY>
                    {{$strategy}}
                    </STRATEGY>

                    ### Proposed Reply:
                    {{$proposed_reply}}
                    </message>

                    """,
                InputVariables = new List<InputVariable>
                {
                    new ()
                    {
                        Name = "strategy"
                    },
                    new ()
                    {
                        Name = "proposed_reply"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation result in JSON format."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var result = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "strategy", strategy
                },
                {
                    "proposed_reply", proposedReply
                }
            });

        try
        {
            var evalResult =
                JsonConvert.DeserializeObject<EvaluationResult>(ExtractJsonContent(result?.Content ?? "{}"))!;

            _logger.LogInformation("EvaluateStrategicAlignmentAsync completed. {Reasoning}", evalResult.Reasoning);

            return evalResult;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to deserialize the evaluation result. {ResultContent}", result?.Content);

            throw;
        }
    }

    private async Task<EvaluationResult> EvaluateKnowledgeIntegrationAsync(
        Kernel kernel)
    {
        var groupChatId = (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!;

        var allConfirmedKnowledges = await _longAgentCollaborationChatCacheService.GetAllConfirmedKnowledgesAsync(groupChatId);
        var knowledge = string.Join("\n", allConfirmedKnowledges);

        var proposedReply = await _longAgentCollaborationChatCacheService.GetLatestProposedReplyToCustomerAsync(groupChatId);

        _logger.LogInformation(
            "EvaluateKnowledgeIntegrationAsync started with knowledge: {Knowledge}, proposedReply: {ProposedReply}",
            TruncateForLogging(knowledge),
            TruncateForLogging(proposedReply));

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        EnforceResponseFormat(promptExecutionSettings);

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluateKnowledgeIntegration",
                Description = "Verify if the proposed reply accurately uses provided knowledge.",
                Template =
                    """
                    <message role="system">
                    Verify if the proposed reply contains hallucinations—specific factual claims not supported by or contradicting the provided knowledge. The reply may omit details from the knowledge, as long as it avoids unsupported or contradictory claims.

                    - **Decision "Yes"**: Reply aligns with knowledge or is general without unsupported claims. Reasoning explains accuracy or generality.
                    - **Decision "No"**: Reply has specific claims not supported by or contradicting knowledge. Reasoning identifies the issue.

                    Respond with a JSON object:
                    - "reasoning": Explanation (max 100 characters)
                    - "decision": "Yes" or "No"

                    ### Examples:

                    #### Example 1:
                    **Knowledge**: The $78 plan includes 10GB data. The $98 plan includes 20GB data.
                    **Proposed Reply**: Our $78 plan includes 10GB of data.
                    { "reasoning": "Reply matches knowledge.", "decision": "Yes" }

                    #### Example 2:
                    **Knowledge**: No specific information available.
                    **Proposed Reply**: We have various plans that might suit your needs. Would you like to hear about them?
                    { "reasoning": "Reply is general, no specific claims.", "decision": "Yes" }

                    #### Example 3:
                    **Knowledge**: The $78 plan includes 10GB data.
                    **Proposed Reply**: Our $78 plan includes 20GB of data.
                    { "reasoning": "Reply contradicts knowledge on data limit.", "decision": "No" }

                    #### Example 4:
                    **Knowledge**: Plans vary by region.
                    **Proposed Reply**: Our plans are the same everywhere.
                    { "reasoning": "Reply contradicts knowledge.", "decision": "No" }

                    #### Example 5:
                    **Knowledge**: The $78 plan includes 10GB data.
                    **Proposed Reply**: Our $78 plan includes 10GB of data, which is great for streaming and browsing.
                    { "reasoning": "Reply matches knowledge; extra note is general.", "decision": "Yes" }

                    #### Example 6:
                    **Knowledge**: No specific information available.
                    **Proposed Reply**: Our most popular plan is the $50 plan with 5GB data.
                    { "reasoning": "Reply makes unsupported specific claims.", "decision": "No" }
                    </message>

                    <message role="user">
                    ### Knowledge:
                    {{$knowledge}}

                    ### Proposed Reply:
                    {{$proposed_reply}}
                    </message>

                    """,
                InputVariables = new List<InputVariable>
                {
                    new ()
                    {
                        Name = "knowledge"
                    },
                    new ()
                    {
                        Name = "proposed_reply"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation result in JSON format."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var result = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "knowledge", knowledge
                },
                {
                    "proposed_reply", proposedReply
                }
            });

        try
        {
            var evalResult =
                JsonConvert.DeserializeObject<EvaluationResult>(ExtractJsonContent(result?.Content ?? "{}"))!;

            _logger.LogInformation("EvaluateKnowledgeIntegrationAsync completed. {Reasoning}", evalResult.Reasoning);

            return evalResult;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to deserialize the evaluation result. {ResultContent}", result?.Content);

            throw;
        }
    }

    private async Task<EvaluationResult> EvaluatePlaceholderAbsenceAsync(Kernel kernel)
    {
        var groupChatId = (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!;

        var proposedReply = await _longAgentCollaborationChatCacheService.GetLatestProposedReplyToCustomerAsync(groupChatId);

        _logger.LogInformation(
            "EvaluatePlaceholderAbsenceAsync started with proposedReply: {ProposedReply}",
            TruncateForLogging(proposedReply));

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        EnforceResponseFormat(promptExecutionSettings);

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluatePlaceholderAbsence",
                Description = "Check for the absence of placeholder text in the proposed reply.",
                Template =
                    """
                    <message role="system">
                    Scan the proposed reply for any placeholder text such as '[insert hotline number]', '[add closing statement]', '[placeholder]', etc. These are indicators that the reply is incomplete.

                    - If the reply contains any placeholders, the decision should be "No". The reasoning should specify the placeholder(s) found.
                    - If the reply does not contain any placeholders, the decision should be "Yes". The reasoning should explain the absence of placeholders.

                    Respond with a JSON object containing 'reasoning' (always included) followed by 'decision' ('Yes' or 'No'). The reasoning should be at max 100 characters.

                    ### Examples:
                    #### Example 1:
                    Proposed Reply: Please call [insert hotline number] for more information.
                    { "decision": "No", "reasoning": "Contains placeholder '[insert hotline number]'." }

                    #### Example 2:
                    Proposed Reply: Please call 1234-5678 for more information.
                    { "decision": "No", "reasoning": "Contains placeholder 1234-5678. Not an actual phone number." }

                    #### Example 3:
                    Proposed Reply: Thank you for your inquiry. [add closing statement]
                    { "decision": "No", "reasoning": "Contains placeholder '[add closing statement]'." }
                    </message>

                    <message role="user">
                    ### Proposed Reply:
                    {{$proposed_reply}}
                    </message>

                    """,
                InputVariables = new List<InputVariable>
                {
                    new ()
                    {
                        Name = "proposed_reply"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation result in JSON format."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var result = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "proposed_reply", proposedReply
                }
            });

        try
        {
            var evalResult =
                JsonConvert.DeserializeObject<EvaluationResult>(ExtractJsonContent(result?.Content ?? "{}"))!;

            _logger.LogInformation("EvaluatePlaceholderAbsenceAsync completed. {Reasoning}", evalResult.Reasoning);

            return evalResult;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to deserialize the evaluation result. {ResultContent}", result?.Content);

            throw;
        }
    }

    private async Task<EvaluationResult> EvaluatePersonalizationAsync(
        Kernel kernel)
    {
        var groupChatId = (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!;

        var customerInquiry = await _longAgentCollaborationChatCacheService.GetCustomerInquiryAsync(groupChatId);
        var proposedReply = await _longAgentCollaborationChatCacheService.GetLatestProposedReplyToCustomerAsync(groupChatId);

        _logger.LogInformation(
            "EvaluatePersonalizationAsync started with customerInquiry: {CustomerInquiry}, proposedReply: {ProposedReply}",
            TruncateForLogging(customerInquiry),
            TruncateForLogging(proposedReply));

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI);

        EnforceResponseFormat(promptExecutionSettings);

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluatePersonalization",
                Description = "Determine if the proposed reply is personalized and contextually relevant.",
                Template =
                    """
                    <message role="system">
                    Determine if the proposed reply is personalized and tailored to the specific customer context based on their inquiry. Personalization means directly addressing the customer's specific needs, questions, or concerns.

                    If the reply is generic or does not directly respond to the inquiry, the decision should be "No".

                    Respond with a JSON object containing 'decision' ("Yes" or "No") and 'reasoning'.

                    ### Examples:
                    #### Example 1:
                    Customer Inquiry: I need a plan with lots of data.
                    Proposed Reply: Our premium plan offers unlimited data, perfect for your needs.
                    { "decision": "Yes", "reasoning": "Directly addresses the need for lots of data." }

                    #### Example 2:
                    Customer Inquiry: What are your cheapest plans?
                    Proposed Reply: We have various plans to suit different needs.
                    { "decision": "No", "reasoning": "Generic response; does not address 'cheapest plans'." }

                    #### Example 3:
                    Customer Inquiry: Can I get a plan with international calling?
                    Proposed Reply: Yes, our global plan includes international calling.
                    { "decision": "Yes", "reasoning": "Specifically addresses international calling." }
                    </message>

                    <message role="user">
                    ### Customer Inquiry:
                    {{$customer_inquiry}}

                    ### Proposed Reply:
                    {{$proposed_reply}}
                    </message>

                    """,
                InputVariables = new List<InputVariable>
                {
                    new ()
                    {
                        Name = "customer_inquiry"
                    },
                    new ()
                    {
                        Name = "proposed_reply"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation result in JSON format."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var result = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "customer_inquiry", customerInquiry
                },
                {
                    "proposed_reply", proposedReply
                }
            });

        try
        {
            var evalResult =
                JsonConvert.DeserializeObject<EvaluationResult>(ExtractJsonContent(result?.Content ?? "{}"))!;

            _logger.LogInformation("EvaluatePersonalizationAsync completed. {Reasoning}", evalResult.Reasoning);

            return evalResult;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to deserialize the evaluation result. {ResultContent}", result?.Content);

            throw;
        }
    }

    private async Task<EvaluationResult> EvaluateLanguageAlignmentAsync(Kernel kernel)
    {
        var groupChatId = (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!;
        var customerInquiry = await _longAgentCollaborationChatCacheService.GetCustomerInquiryAsync(groupChatId);
        var proposedReply = await _longAgentCollaborationChatCacheService.GetLatestProposedReplyToCustomerAsync(groupChatId);

        _logger.LogInformation(
            "EvaluateLanguageAlignmentAsync started with customerInquiry: {CustomerInquiry}, proposedReply: {ProposedReply}",
            TruncateForLogging(customerInquiry),
            TruncateForLogging(proposedReply));

        var detectAppropriateResponseLanguageResponse =
            await _languagePlugin.DetectAppropriateResponseLanguageAsync(kernel, customerInquiry);

        _logger.LogInformation(
            "EvaluateLanguageAlignmentAsync detected language: {InputTextLanguageName}",
            detectAppropriateResponseLanguageResponse.ResponseLanguageName);

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        EnforceResponseFormat(promptExecutionSettings);

        // Create the function with the prompt and structured output settings
        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluateLanguageAlignment",
                Description = "Ensure the proposed reply matches the language and style of the customer's inquiry.",
                Template =
                    """
                    <message role="system">
                    Determine if the proposed reply is largely or reasonably written in the provided language, as specified (e.g., 'English', 'Spanish', 'Traditional Chinese', 'Cantonese'). For languages with different writing systems, the reply should predominantly match the specified variant. Focus solely on whether the language and variant match; do not consider tone, style, or content.

                    - If the reply is largely or reasonably in the provided language and matches the specified variant (if applicable), respond with 'Yes' and provide reasoning explaining why it matches, noting any minor deviations if present.
                    - If the reply is not largely or reasonably in the provided language or does not match the specified variant, respond with 'No' and provide reasoning with specific examples of words or phrases that significantly deviate.

                    Respond with a JSON object containing 'reasoning' (always included) followed by 'decision' ('Yes' or 'No'). The reasoning should be at max 100 characters.
                    </message>

                    <message role="user">
                    ### Language:
                    Cantonese

                    ### Proposed Reply:
                    你好，點樣可以幫到你呀？
                    </message>
                    <message role="assistant">
                    {"reasoning": "The expected language is Cantonese. The reply uses Cantonese-specific phrasing and characters like '點樣' (how) and '呀' (particle), written in Traditional Chinese as typical for Cantonese.", "decision": "Yes"}
                    </message>

                    <message role="user">
                    ### Language:
                    Traditional Chinese

                    ### Proposed Reply:
                    你好，我会尽快回复你的邮件。
                    </message>
                    <message role="assistant">
                    {"reasoning": "The expected language is Traditional Chinese. The reply uses mostly Simplified Chinese characters, such as '会' instead of '會', '尽' instead of '盡', '复' instead of '覆'.", "decision": "No"}
                    </message>

                    <message role="user">
                    ### Language:
                    Traditional Chinese

                    ### Proposed Reply:
                    你好，請問有什麼我可以幫助你的嗎？Hi!
                    </message>
                    <message role="assistant">
                    {"reasoning": "The expected language is Traditional Chinese. The reply is largely in Traditional Chinese with characters like '請問' and '幫助', with only a minor English word 'Hi'.", "decision": "Yes"}
                    </message>

                    <message role="user">
                    ### Language:
                    English

                    ### Proposed Reply:
                    Bonjour, comment puis-je vous aider?
                    </message>
                    <message role="assistant">
                    {"reasoning": "The expected language is English. The reply uses mostly French words like 'Bonjour' and 'aider', not English.", "decision": "No"}
                    </message>

                    <message role="user">
                    ### Language:
                    English

                    ### Proposed Reply:
                    Hello, how can I assist you today?
                    </message>
                    <message role="assistant">
                    {"reasoning": "The expected language is English. The reply uses English words such as 'Hello' and 'assist' throughout.", "decision": "Yes"}
                    </message>

                    <message role="user">
                    ### Language:
                    Spanish

                    ### Proposed Reply:
                    Hola, ¿cómo puedo ayudarte? Thanks!
                    </message>
                    <message role="assistant">
                    {"reasoning": "The expected language is Spanish. The reply is largely in Spanish with words like 'Hola' and 'ayudarte', with only a minor English word 'Thanks'.", "decision": "Yes"}
                    </message>

                    <message role="user">
                    ### Language:
                    {{$language_name}}

                    ### Proposed Reply:
                    {{$proposed_reply}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new ()
                    {
                        Name = "language_name"
                    },
                    new ()
                    {
                        Name = "proposed_reply"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation result in JSON format."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        // Invoke the function to get a JSON string
        var result = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "language_name", detectAppropriateResponseLanguageResponse.ResponseLanguageName
                },
                {
                    "proposed_reply", proposedReply
                }
            });

        try
        {
            var evalResult =
                JsonConvert.DeserializeObject<EvaluationResult>(ExtractJsonContent(result?.Content ?? "{}"))!;

            _logger.LogInformation("EvaluateLanguageAlignmentAsync completed. {Reasoning}", evalResult.Reasoning);

            return evalResult;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to deserialize the evaluation result. {ResultContent}", result?.Content);

            throw;
        }
    }

    private static void EnforceResponseFormat(PromptExecutionSettings promptExecutionSettings)
    {
        if (promptExecutionSettings is OpenAIPromptExecutionSettings openAiPromptExecutionSettings)
        {
            // Define the JSON schema for the structured output
            var outputSchema =
                """
                {
                  "$schema": "http://json-schema.org/draft-07/schema#",
                  "type": "object",
                  "strict": true,
                  "additionalProperties": false,
                  "required": ["reasoning", "decision"],
                  "properties": {
                    "reasoning": {
                      "type": "string",
                      "description": "Explanation of the evaluation"
                    },
                    "decision": {
                      "type": "string",
                      "enum": ["Yes", "No"],
                      "description": "Decision on language alignment"
                    }
                  }
                }
                """;

#pragma warning disable SKEXP0010
            openAiPromptExecutionSettings.ResponseFormat = ChatResponseFormat.CreateJsonSchemaFormat(
                jsonSchemaFormatName: "EvaluateLanguageAlignmentResponse",
                jsonSchema: BinaryData.FromString(outputSchema),
                jsonSchemaIsStrict: true);
#pragma warning restore SKEXP0010
        }
#pragma warning disable SKEXP0070
        else if (promptExecutionSettings is GeminiPromptExecutionSettings geminiPromptExecutionSettings)
        {
            // Define the JSON schema for the structured output
            var outputSchema =
                """
                {
                  "type": "object",
                  "properties": {
                    "reasoning": {
                      "type": "string",
                      "description": "Explanation of the evaluation"
                    },
                    "decision": {
                      "type": "string",
                      "enum": ["Yes", "No"],
                      "description": "Decision on language alignment"
                    }
                  },
                  "propertyOrdering": ["reasoning", "decision"]
                }
                """;

            geminiPromptExecutionSettings.ResponseMimeType = "application/json";
            geminiPromptExecutionSettings.ResponseSchema = JsonDocument.Parse(outputSchema);
        }
#pragma warning restore SKEXP0070
    }

    private static string ExtractJsonContent(string inputText)
    {
        // Split the text into lines
        var lines = inputText.Split(
            [
                "\r\n",
                "\r",
                "\n"
            ],
            StringSplitOptions.None);

        var startIndex = -1;
        var endIndex = -1;

        // Find opening and closing markers
        for (var i = 0; i < lines.Length; i++)
        {
            if (lines[i].Trim() == "```json")
            {
                startIndex = i;
            }
            else if (lines[i].Trim() == "```" && startIndex != -1)
            {
                endIndex = i;
                break;
            }
        }

        // If markers not found properly, return original text
        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex)
        {
            return inputText;
        }

        // Extract content between markers
        var contentLines = lines.Skip(startIndex + 1)
            .Take(endIndex - startIndex - 1);

        return string.Join(Environment.NewLine, contentLines).Trim();
    }
}