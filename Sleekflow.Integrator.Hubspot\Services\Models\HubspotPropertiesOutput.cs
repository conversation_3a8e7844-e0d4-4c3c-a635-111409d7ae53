﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Services.Models;

public class ModificationMetadata
{
    [JsonProperty("archivable")]
    public bool Archivable { get; set; }

    [JsonProperty("readOnlyDefinition")]
    public bool ReadOnlyDefinition { get; set; }

    [JsonProperty("readOnlyValue")]
    public bool ReadOnlyValue { get; set; }

    [JsonProperty("readOnlyOptions")]
    public bool? ReadOnlyOptions { get; set; }

    [JsonConstructor]
    public ModificationMetadata(bool archivable, bool readOnlyDefinition, bool readOnlyValue, bool? readOnlyOptions)
    {
        Archivable = archivable;
        ReadOnlyDefinition = readOnlyDefinition;
        ReadOnlyValue = readOnlyValue;
        ReadOnlyOptions = readOnlyOptions;
    }
}

public class Option
{
    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("displayOrder")]
    public int DisplayOrder { get; set; }

    [JsonProperty("hidden")]
    public bool Hidden { get; set; }

    [JsonConstructor]
    public Option(string label, string value, string description, int displayOrder, bool hidden)
    {
        Label = label;
        Value = value;
        Description = description;
        DisplayOrder = displayOrder;
        Hidden = hidden;
    }
}

public class HubspotPropertiesResult
{
    [JsonProperty("updatedAt")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty("createdAt")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("fieldType")]
    public string FieldType { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("groupName")]
    public string GroupName { get; set; }

    [JsonProperty("options")]
    public List<Option> Options { get; set; }

    [JsonProperty("displayOrder")]
    public int DisplayOrder { get; set; }

    [JsonProperty("calculated")]
    public bool Calculated { get; set; }

    [JsonProperty("externalOptions")]
    public bool ExternalOptions { get; set; }

    [JsonProperty("hasUniqueValue")]
    public bool HasUniqueValue { get; set; }

    [JsonProperty("hidden")]
    public bool Hidden { get; set; }

    [JsonProperty("hubspotDefined")]
    public bool HubspotDefined { get; set; }

    [JsonProperty("modificationMetadata")]
    public ModificationMetadata ModificationMetadata { get; set; }

    [JsonProperty("formField")]
    public bool FormField { get; set; }

    [JsonProperty("calculationFormula")]
    public string CalculationFormula { get; set; }

    [JsonConstructor]
    public HubspotPropertiesResult(
        DateTimeOffset updatedAt,
        DateTimeOffset createdAt,
        string name,
        string label,
        string type,
        string fieldType,
        string description,
        string groupName,
        List<Option> options,
        int displayOrder,
        bool calculated,
        bool externalOptions,
        bool hasUniqueValue,
        bool hidden,
        bool hubspotDefined,
        ModificationMetadata modificationMetadata,
        bool formField,
        string calculationFormula)
    {
        UpdatedAt = updatedAt;
        CreatedAt = createdAt;
        Name = name;
        Label = label;
        Type = type;
        FieldType = fieldType;
        Description = description;
        GroupName = groupName;
        Options = options;
        DisplayOrder = displayOrder;
        Calculated = calculated;
        ExternalOptions = externalOptions;
        HasUniqueValue = hasUniqueValue;
        Hidden = hidden;
        HubspotDefined = hubspotDefined;
        ModificationMetadata = modificationMetadata;
        FormField = formField;
        CalculationFormula = calculationFormula;
    }
}

public class HubspotPropertiesOutput
{
    [JsonProperty("results")]
    public List<HubspotPropertiesResult> Results { get; set; }

    [JsonConstructor]
    public HubspotPropertiesOutput(List<HubspotPropertiesResult> results)
    {
        Results = results;
    }
}