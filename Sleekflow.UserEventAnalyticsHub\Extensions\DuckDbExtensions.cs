using DuckDB.NET.Data;
using Microsoft.Extensions.Logging;
using Sleekflow.UserEventAnalyticsHub.Configs;

namespace Sleekflow.UserEventAnalyticsHub.Extensions;

public class PostgreSqlConnectionParams
{
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; } = 5432;
    public string Database { get; set; } = string.Empty;
    public string User { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public static class DuckDbExtensions
{
    public static async Task ConfigureDuckDbAsync(DuckDBConnection connection, IPostgreSqlConfig pgConfig, ILogger logger)
    {
        logger.LogInformation("Configuring DuckDB with PostgreSQL for UserEventAnalyticsHub");

        // Install and load required extensions
        await ExecuteNonQueryAsync(connection, "INSTALL postgres;");
        await ExecuteNonQueryAsync(connection, "LOAD postgres;");

        // Set memory limits (using hardcoded values for simplicity)
        await ExecuteNonQueryAsync(connection, "SET memory_limit='1536MB';");
        await ExecuteNonQueryAsync(connection, "SET max_memory='2GB';");

        // Set thread configuration
        await ExecuteNonQueryAsync(connection, "SET threads TO 16;");

        // Optimize for bulk operations
        await ExecuteNonQueryAsync(connection, "SET enable_progress_bar=false;");
        await ExecuteNonQueryAsync(connection, "SET preserve_insertion_order=false;");

        // Set up security configuration
        await ExecuteNonQueryAsync(connection, "SET allow_unredacted_secrets = false;");
        await ExecuteNonQueryAsync(connection, "SET allow_persistent_secrets = false;");
        await ExecuteNonQueryAsync(connection, "SET allow_community_extensions = false;");

        // Restrict filesystem access to system temporary directories
        // Use environment-specific temp directories for cross-platform compatibility
        var tempDir = Environment.GetEnvironmentVariable("TMPDIR") ??
                      Environment.GetEnvironmentVariable("TMP") ??
                      Environment.GetEnvironmentVariable("TEMP") ??
                      "/tmp";
        logger.LogInformation("DuckDB allowed temp directory: {TempDir}", tempDir);
        await ExecuteNonQueryAsync(connection, $"SET allowed_directories = ['{tempDir}'];");

        // Parse PostgreSQL connection parameters from connection string
        var pgParams = ParsePostgreSqlConnectionString(pgConfig.ConnectionString);

        // Create PostgreSQL secret for authentication
        var pgSecretCommand = $@"
            CREATE SECRET postgres_secret (
                TYPE POSTGRES,
                HOST '{pgParams.Host}',
                PORT {pgParams.Port},
                DATABASE '{pgParams.Database}',
                USER '{pgParams.User}',
                PASSWORD '{pgParams.Password}'
            );";
        await ExecuteNonQueryAsync(connection, pgSecretCommand);

        // Attach PostgreSQL database using the secret
        await ExecuteNonQueryAsync(connection, "ATTACH '' AS postgres_db (TYPE postgres, SECRET postgres_secret);");

        // Lock configuration after all setup is complete
        await ExecuteNonQueryAsync(connection, "SET lock_configuration = true;");

        logger.LogInformation("DuckDB configuration with PostgreSQL completed successfully");
    }

    private static async Task ExecuteNonQueryAsync(DuckDBConnection connection, string sql)
    {
        using var command = new DuckDBCommand(sql, connection);
        await command.ExecuteNonQueryAsync();
    }



    public static PostgreSqlConnectionParams ParsePostgreSqlConnectionString(string connectionString)
    {
        var parameters = new Dictionary<string, string>();
        var result = new PostgreSqlConnectionParams();

        // Parse .NET-style connection string (semicolon-separated key=value pairs)
        var parts = connectionString.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var part in parts)
        {
            var keyValue = part.Split('=', 2);
            if (keyValue.Length == 2)
            {
                var key = keyValue[0].Trim();
                var value = keyValue[1].Trim();
                parameters[key] = value;
            }
        }

        // Extract individual parameters
        if (parameters.TryGetValue("Host", out var host) || parameters.TryGetValue("Server", out host))
            result.Host = host;

        if (parameters.TryGetValue("Database", out var database))
            result.Database = database;

        if (parameters.TryGetValue("Username", out var username) || parameters.TryGetValue("User", out username) || parameters.TryGetValue("User ID", out username))
            result.User = username;

        if (parameters.TryGetValue("Password", out var password))
            result.Password = password;

        if (parameters.TryGetValue("Port", out var portString) && int.TryParse(portString, out var port))
            result.Port = port;

        return result;
    }

    public static async Task<bool> TestDuckDbConnectionAsync(ILogger logger)
    {
        try
        {
            await using var connection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await connection.OpenAsync();

            using var command = new DuckDBCommand("SELECT 1", connection);
            var result = await command.ExecuteScalarAsync();

            logger.LogInformation("DuckDB connection test successful");
            return Convert.ToInt32(result) == 1;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "DuckDB connection test failed");
            return false;
        }
    }
}