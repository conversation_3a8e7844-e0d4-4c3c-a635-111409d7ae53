using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.UserEventHub.Models.Constants;
using Sleekflow.UserEventHub.Models.Notifications;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.UserEventHub.Notifications;

public interface INotificationSettingsRepository : IRepository<NotificationSettings>
{
    public Task<NotificationSettings> CreateDefaultNotificationSettingsAsync(
        string userId,
        string sleekflowCompanyId);

    public Task<NotificationSettings> CreateDefaultNotificationSettingsWithMobileRegistrationAsync(
        string handle,
        string regId,
        string userId,
        string sleekflowCompanyId,
        string? platform,
        string? hubName);

    Task<NotificationSettings?> GetNotificationSettingsByUserIdAsync(
        string userId,
        string sleekflowCompanyId);

    Task<NotificationSettings> PatchAndGetNotificationSettingsAsync(
        NotificationSettings notificationSettings);
}

public class NotificationSettingsRepository
    : BaseRepository<NotificationSettings>, INotificationSettingsRepository, ISingletonService
{
    private readonly ILogger<NotificationSettingsRepository> _logger;

    private readonly IIdService _idService;

    public NotificationSettingsRepository(
        ILogger<NotificationSettingsRepository> logger,
        IServiceProvider serviceProvider,
        IIdService idService)
        : base(logger, serviceProvider)
    {
        _logger = logger;
        _idService = idService;
    }

    public async Task<NotificationSettings> CreateDefaultNotificationSettingsAsync(
        string userId,
        string sleekflowCompanyId)
    {
        var defaultMobileNotificationSettings = MobileNotificationSettings.CreateDefault();

        var defaultNotificationSettings = new NotificationSettings(
            id: _idService.GetId(SysTypeNames.NotificationSettings),
            sleekflowStaffId: userId,
            sleekflowCompanyId: sleekflowCompanyId,
            web: new PlatformSpecificNotificationSettings(),
            mobile: defaultMobileNotificationSettings,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow);


        return await CreateAndGetAsync(defaultNotificationSettings, sleekflowCompanyId);
    }

    public async Task<NotificationSettings> CreateDefaultNotificationSettingsWithMobileRegistrationAsync(
        string handle,
        string regId,
        string userId,
        string sleekflowCompanyId,
        string? platform,
        string? hubName)
    {
        var defaultMobileNotificationSettings = MobileNotificationSettings.CreateDefaultWithRegister(
            handle,
            regId,
            platform,
            hubName);

        var defaultNotificationSettings = new NotificationSettings(
            id: _idService.GetId(SysTypeNames.NotificationSettings),
            sleekflowStaffId: userId,
            sleekflowCompanyId: sleekflowCompanyId,
            web: new PlatformSpecificNotificationSettings(),
            mobile: defaultMobileNotificationSettings,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow);


        return await CreateAndGetAsync(defaultNotificationSettings, sleekflowCompanyId);
    }

    public async Task<NotificationSettings?> GetNotificationSettingsByUserIdAsync(
        string userId,
        string sleekflowCompanyId)
    {
        var settings = await GetObjectsAsync(
            s => s.SleekflowStaffId == userId && s.SleekflowCompanyId == sleekflowCompanyId);
        return settings.Count == 0 ? null : settings[0];
    }

    public async Task<NotificationSettings> PatchAndGetNotificationSettingsAsync(
        NotificationSettings notificationSettings)
    {
        var patchOperations = new List<PatchOperation>
        {
            Replace(IHasUpdatedAt.PropertyNameUpdatedAt, notificationSettings.UpdatedAt)
        };

        if (notificationSettings.Web != null)
        {
            patchOperations.Add(Replace(NotificationSettings.PropertyNameWeb, notificationSettings.Web));
        }

        if (notificationSettings.Mobile != null)
        {
            patchOperations.Add(Replace(NotificationSettings.PropertyNameMobile, notificationSettings.Mobile));
        }

        return await PatchAndGetAsync(
            notificationSettings.Id,
            notificationSettings.SleekflowCompanyId,
            patchOperations);
    }
}