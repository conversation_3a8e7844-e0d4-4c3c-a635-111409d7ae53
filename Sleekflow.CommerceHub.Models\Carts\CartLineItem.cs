using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.LineItems;

namespace Sleekflow.CommerceHub.Models.Carts;

public class CartLineItem : LineItem
{
    [JsonConstructor]
    public CartLineItem(
        string productVariantId,
        string productId,
        string? description,
        int quantity,
        Discount? lineItemDiscount,
        Dictionary<string, object?> metadata)
        : base(
            productVariantId,
            productId,
            description,
            quantity,
            lineItemDiscount,
            metadata)
    {
        LineItemDiscount = lineItemDiscount;
    }
}