using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactPropertiesStepExecutor : IStepExecutor
{
}

public class UpdateContactPropertiesStepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactPropertiesStepArgs>>,
        IUpdateContactPropertiesStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactPropertiesStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactProperties",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateContactPropertiesInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [JsonProperty("properties_dict")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> PropertiesDict { get; set; }

        [JsonConstructor]
        public UpdateContactPropertiesInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactId,
            Dictionary<string, object?> propertiesDict)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactId = contactId;
            PropertiesDict = propertiesDict;
        }
    }

    private async Task<UpdateContactPropertiesInput> GetArgs(
        CallStep<UpdateContactPropertiesStepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                      ?? callStep.Args.ContactIdExpr);
        var propertiesDict =
            await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.PropertiesKeyExprDict);

        return new UpdateContactPropertiesInput(
            state.Id,
            state.Identity,
            contactId,
            propertiesDict);
    }
}