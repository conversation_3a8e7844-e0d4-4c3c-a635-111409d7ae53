using DynamicExpresso;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.EntityEvents;
using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.Providers.Resolvers;
using Sleekflow.CrmHub.Unifies;
using Sleekflow.JsonConfigs;

namespace Sleekflow.CrmHub.Tests;

public class UnifyUtilsTests
{
    [SetUp]
    public void Setup()
    {
        // Method intentionally left empty.
    }

    [Test]
    public void Test1()
    {
        var utcNow = DateTimeOffset.UtcNow;

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
            };

        var flattenedDict = UnifyUtils.FlattenDictionary(dict);

        Assert.That(flattenedDict.ContainsKey("Address:Flat"), Is.EqualTo(true));
        Assert.That(flattenedDict.ContainsKey("Address:Floor"), Is.EqualTo(true));
        Assert.That(flattenedDict["Address:Flat"]!.Value, Is.EqualTo("C1"));
        Assert.That(flattenedDict["Address:Floor"]!.Value, Is.EqualTo("26/F"));
    }

    [Test]
    public void Test2()
    {
        var utcNow = DateTimeOffset.UtcNow;

        var unifyRule = new UnifyRule(
            "Hello",
            "time",
            new List<string>()
            {
                "LastName"
            },
            true);

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
            };

        var (key, value) = UnifyUtils.UnifyField(
            unifyRule,
            dict,
            null!,
            DateTimeOffset.UtcNow);

        Assert.That(key, Is.EqualTo("unified:Hello"));
        Assert.That(value.Value, Is.EqualTo("Choi"));
        Assert.That(value.ValueCi, Is.EqualTo("choi"));
    }

    [Test]
    public void Test3()
    {
        var utcNow = DateTimeOffset.UtcNow;
        var utcNow2 = utcNow.AddMilliseconds(1);

        var unifyRule = new UnifyRule(
            "Hello",
            "time",
            new List<string>()
            {
                "LastName", "LastName2",
            },
            true);

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "LastName2", new SnapshottedValue(utcNow2, "Choi2")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
            };

        var (key, value) = UnifyUtils.UnifyField(
            unifyRule,
            dict,
            null!,
            DateTimeOffset.UtcNow);

        Assert.That(key, Is.EqualTo("unified:Hello"));
        Assert.That(value.Value, Is.EqualTo("Choi2"));
        Assert.That(value.ValueCi, Is.EqualTo("choi2"));
    }

    [Test]
    public void Test4()
    {
        var utcNow = DateTimeOffset.UtcNow;
        var utcNow2 = utcNow.AddMilliseconds(1);

        var unifyRule = new UnifyRule(
            "Hello",
            "time",
            new List<string>()
            {
                "LastName", "LastName2",
            },
            true);

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow2, "Choi")
                },
                {
                    "LastName2", new SnapshottedValue(utcNow, "Choi2")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
            };

        var (key, value) = UnifyUtils.UnifyField(
            unifyRule,
            dict,
            null!,
            DateTimeOffset.UtcNow);

        Assert.That(key, Is.EqualTo("unified:Hello"));
        Assert.That(value.Value, Is.EqualTo("Choi"));
        Assert.That(value.ValueCi, Is.EqualTo("choi"));
    }

    [Test]
    public void Test5()
    {
        var utcNow = DateTimeOffset.UtcNow;
        var utcNow2 = utcNow.AddMilliseconds(1);

        var unifyRule = new UnifyRule(
            "Hello",
            "time",
            new List<string>
            {
                "LastName", "Address:Flat",
            },
            true);

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow2,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
            };

        UnifyUtils.UnifyAndTrack(
            dict,
            new Dictionary<string, (object? FromValue, object? ToValue)>(),
            new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(DateTimeOffset.UtcNow),
            new List<UnifyRule>
            {
                unifyRule
            });

        Assert.That(dict["LastName"]!.Value, Is.EqualTo("Choi"));
        Assert.That(dict["FirstName"]!.Value, Is.EqualTo("Leo"));
        Assert.That(((JObject) dict["Address"]!.Value!).Value<string>("Flat"), Is.EqualTo("C1"));
        Assert.That(((JObject) dict["Address"]!.Value!).Value<string>("Floor"), Is.EqualTo("26/F"));
        Assert.That(dict.ContainsKey("Address:Flat"), Is.EqualTo(false));
        Assert.That(dict.ContainsKey("Address:Floor"), Is.EqualTo(false));
        Assert.That(dict["unified:Hello"]!.Value, Is.EqualTo("C1"));
        Assert.That(dict["unified:Hello"]!.ValueCi, Is.EqualTo("c1"));
    }

    [Test]
    public void Test6()
    {
        var utcNow = DateTimeOffset.UtcNow;

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
                {
                    "PurchaseDate", new SnapshottedValue(utcNow, utcNow)
                },
            };

        var dictWrapper = new SnapshottedValueDictWrapper(dict);

        Assert.That(dictWrapper.GetLatestSnapshotTime(), Is.Null);

        var interpreter = new Interpreter()
            .Reference(typeof(DateTimeOffset))
            .SetVariable("dict", dictWrapper);
        var expression = "((DateTimeOffset) dict[\"PurchaseDate\"]).AddYears(2)";
        var parsedExpression = interpreter.Parse(expression);
        var result = (DateTimeOffset) parsedExpression.Invoke();

        Assert.That(result, Is.EqualTo(((DateTimeOffset) dictWrapper["PurchaseDate"]!).AddYears(2)));
        Assert.That(dictWrapper.GetLatestSnapshotTime(), Is.EqualTo(utcNow));
    }

    [Test]
    public void Test7()
    {
        var utcNow = DateTimeOffset.UtcNow;
        var utcNow2 = utcNow.AddMilliseconds(1);
        var utcNow3 = utcNow.AddYears(1);

        var unifyRule = new UnifyRule(
            "Hello",
            "time",
            new List<string>
            {
                "%%EXPR%% ((DateTimeOffset) d[\"PurchaseDate\"]).AddYears(2)",
                "%%EXPR%% ((DateTimeOffset) d[\"WarrantyDate\"]).AddYears(2)",
                "WarrantyDate",
            },
            true);
        var unifyRule2 = new UnifyRule(
            "Hello2",
            "time",
            new List<string>
            {
                "%%EXPR%% \"https://google.com/\" + d[\"LastName\"]",
            },
            true);
        var unifyRule3 = new UnifyRule(
            "Hello3",
            "time",
            new List<string>
            {
                // "%%EXPR%% d[\"productname\"].toUpperCase() == \"REPAIR\" ? ((DateTimeOffset) d[\"PurchaseDate\"]).AddMonths(6) : ((DateTimeOffset) d[\"PurchaseDate\"]).AddYears(2)",
                "%%EXPR%% ((String) d[\"productname\"]).ToUpperInvariant() == \"REPAIR\" ? ((DateTimeOffset) d[\"WarrantyDate\"]).AddMonths(6) : ((DateTimeOffset) d[\"WarrantyDate\"]).AddYears(2)",

                // "WarrantyDate",
            },
            true);

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
                {
                    "PurchaseDate", new SnapshottedValue(utcNow, utcNow)
                },
                {
                    "WarrantyDate", new SnapshottedValue(utcNow2, utcNow3)
                },
                {
                    "productname", new SnapshottedValue(utcNow, "REPAIR")
                }
            };

        UnifyUtils.UnifyAndTrack(
            dict,
            new Dictionary<string, (object? FromValue, object? ToValue)>(),
            new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(DateTimeOffset.UtcNow),
            new List<UnifyRule>
            {
                unifyRule, unifyRule2, unifyRule3,
            });

        Assert.That(
            dict["unified:Hello"]!.Value,
            Is.EqualTo(
                JsonConvert
                    .SerializeObject(utcNow3.AddYears(2), JsonConfig.DefaultJsonSerializerSettings)
                    .Trim('\"')));
        Assert.That(dict["unified:Hello2"]!.Value, Is.EqualTo("https://google.com/Choi"));
        Assert.That(
            dict["unified:Hello3"]!.Value,
            Is.EqualTo(
                JsonConvert
                    .SerializeObject(utcNow3.AddMonths(6), JsonConfig.DefaultJsonSerializerSettings)
                    .Trim('\"')));
    }

    [Test]
    public void Test8()
    {
        var utcNow = DateTimeOffset.UtcNow;

        var dict =
            new Dictionary<string, SnapshottedValue?>()
            {
                {
                    "LastName", new SnapshottedValue(utcNow, "Choi")
                },
                {
                    "FirstName", new SnapshottedValue(utcNow, "Leo")
                },
                {
                    "Address", new SnapshottedValue(
                        utcNow,
                        JObject.FromObject(
                            new
                            {
                                Flat = "C1", Floor = "26/F"
                            }))
                },
                {
                    "PurchaseDate", new SnapshottedValue(utcNow, utcNow)
                },
            };

        UnifyUtils.UnifyAndTrack(
            dict,
            new Dictionary<string, (object? FromValue, object? ToValue)>(),
            new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(DateTimeOffset.UtcNow),
            new List<UnifyRule>
            {
                new UnifyRule(
                    "1",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d",
                    },
                    true),
                new UnifyRule(
                    "2",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d[]",
                    },
                    true),
                new UnifyRule(
                    "3",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d[\"\"]",
                    },
                    true),
                new UnifyRule(
                    "4",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d[*]",
                    },
                    true),
                new UnifyRule(
                    "5",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d[\"123123\"]",
                    },
                    true),
                new UnifyRule(
                    "6",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d[\"Address\"]",
                    },
                    true),
                new UnifyRule(
                    "7",
                    "time",
                    new List<string>
                    {
                        "%%EXPR%% d[\"Address\"][\"Flat\"]",
                    },
                    true),
            });

        Assert.That(dict["unified:1"]!.Value, Is.EqualTo("d"));
        Assert.That(dict["unified:2"]!.Value, Is.EqualTo("Not Evaluatable Expression"));
        Assert.That(dict["unified:3"]!.Value, Is.Null);
        Assert.That(dict["unified:4"]!.Value, Is.EqualTo("Not Evaluatable Expression"));
        Assert.That(dict["unified:5"]!.Value, Is.Null);
        Assert.That(dict["unified:6"]!.Value, Is.Null);
        Assert.That(dict["unified:7"]!.Value, Is.EqualTo("Not Evaluatable Expression"));
    }

    [Test]
    public void Test9()
    {
        var json =
            @"{""snapshot_time"":""2022-09-30T07:53:30.000Z"",""value"":""312 Constitution Place\nAustin, TX 78767\nUSA"",""value_ci"": ""312 constitution place\naustin, tx 78767\nusa""}";
        var deserializeObject = JsonConvert.DeserializeObject(json, JsonConfig.DefaultJsonSerializerSettings);
        var snapshottedValue = SnapshottedValue.FromObject(deserializeObject)!;

        var json2 =
            @"{""t"":1664524410000,""v"":""312 Constitution Place\nAustin, TX 78767\nUSA"",""i"":""312 constitution place\naustin, tx 78767\nusa""}";
        var deserializeObject2 = JsonConvert.DeserializeObject(json2, JsonConfig.DefaultJsonSerializerSettings);
        var snapshottedValue2 = SnapshottedValue.FromObject(deserializeObject2)!;

        Assert.That(snapshottedValue, Is.Not.Null);
        Assert.That(snapshottedValue2, Is.Not.Null);
        Assert.That(snapshottedValue.SnapshotTime, Is.EqualTo(snapshottedValue2.SnapshotTime));
        Assert.That(snapshottedValue.Value, Is.EqualTo(snapshottedValue2.Value));
        Assert.That(snapshottedValue.ValueCi, Is.EqualTo(snapshottedValue2.ValueCi));
        Assert.That(
            JsonConvert.SerializeObject(snapshottedValue, JsonConfig.DefaultJsonSerializerSettings),
            Is.EqualTo(json2));
        Assert.That(
            JsonConvert.SerializeObject(snapshottedValue2, JsonConfig.DefaultJsonSerializerSettings),
            Is.EqualTo(json2));
    }

    [Test]
    public void Test10()
    {
        var json =
            @"{""snapshot_time"":""2022-09-30T15:53:30.000+08:00"",""value"":""312 Constitution Place\nAustin, TX 78767\nUSA"",""value_ci"": ""312 constitution place\naustin, tx 78767\nusa""}";
        var deserializeObject = JsonConvert.DeserializeObject(json, JsonConfig.DefaultJsonSerializerSettings);
        var snapshottedValue = SnapshottedValue.FromObject(deserializeObject)!;

        var json2 =
            @"{""t"":1664524410000,""v"":""312 Constitution Place\nAustin, TX 78767\nUSA"",""i"":""312 constitution place\naustin, tx 78767\nusa""}";
        var deserializeObject2 = JsonConvert.DeserializeObject(json2, JsonConfig.DefaultJsonSerializerSettings);
        var snapshottedValue2 = SnapshottedValue.FromObject(deserializeObject2)!;

        Assert.That(snapshottedValue, Is.Not.Null);
        Assert.That(snapshottedValue2, Is.Not.Null);
        Assert.That(snapshottedValue.SnapshotTime, Is.EqualTo(snapshottedValue2.SnapshotTime));
        Assert.That(snapshottedValue.Value, Is.EqualTo(snapshottedValue2.Value));
        Assert.That(snapshottedValue.ValueCi, Is.EqualTo(snapshottedValue2.ValueCi));
        Assert.That(
            JsonConvert.SerializeObject(snapshottedValue, JsonConfig.DefaultJsonSerializerSettings),
            Is.EqualTo(json2));
        Assert.That(
            JsonConvert.SerializeObject(snapshottedValue2, JsonConfig.DefaultJsonSerializerSettings),
            Is.EqualTo(json2));
    }

    [Test]
    public void Test11()
    {
        var json =
            @"{""t"":1664524410000,""v"":{""Hello"":""312 Constitution Place\nAustin, TX 78767\nUSA""},""i"":{""Hello"":""312 Constitution Place\nAustin, TX 78767\nUSA""}}";
        var deserializeObject = JsonConvert.DeserializeObject(json, JsonConfig.DefaultJsonSerializerSettings);
        var snapshottedValue = SnapshottedValue.FromObject(deserializeObject)!;

        Assert.That(snapshottedValue, Is.Not.Null);
        Assert.That(snapshottedValue.ValueCi, Is.EqualTo(new JObject()));

        var json2 = JsonConvert.SerializeObject(snapshottedValue);
        Assert.That(
            json2,
            Is.EqualTo(
                @"{""t"":1664524410000,""v"":{""Hello"":""312 Constitution Place\nAustin, TX 78767\nUSA""},""i"":{}}"));
    }

    [Test]
    public void Test12()
    {
        var json =
            @"{""n"":""Hello"",""f"":""123123"",""t"":""456456""}";
        var deserializeObject = JsonConvert.DeserializeObject(json, JsonConfig.DefaultJsonSerializerSettings);
        var entityEventChangeEntry = EntityEventChangeEntry.FromObject(deserializeObject)!;

        Assert.That(entityEventChangeEntry, Is.Not.Null);
        Assert.That(entityEventChangeEntry.Name, Is.EqualTo("Hello"));
        Assert.That(entityEventChangeEntry.FromValue, Is.EqualTo("123123"));
        Assert.That(entityEventChangeEntry.ToValue, Is.EqualTo("456456"));
    }
}