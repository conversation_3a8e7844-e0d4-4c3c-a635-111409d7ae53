openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7070
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/audit-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
    description: Prod Apigw
paths:
  /AuditLogs/CreateAutomationTriggeredLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAutomationTriggeredLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAutomationTriggeredLogOutputOutput'
  /AuditLogs/CreateConversationAssignedTeamChangedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationAssignedTeamChangedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationAssignedTeamChangedLogOutputOutput'
  /AuditLogs/CreateConversationAssigneeChangedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationAssigneeChangedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationAssigneeChangedLogOutputOutput'
  /AuditLogs/CreateConversationChannelSwitchedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationChannelSwitchedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationChannelSwitchedLogOutputOutput'
  /AuditLogs/CreateConversationCollaboratorAddedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationCollaboratorAddedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationCollaboratorAddedLogOutputOutput'
  /AuditLogs/CreateConversationCollaboratorRemovedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationCollaboratorRemovedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationCollaboratorRemovedLogOutputOutput'
  /AuditLogs/CreateConversationLabelAddedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationLabelAddedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationLabelAddedLogOutputOutput'
  /AuditLogs/CreateConversationLabelRemovedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationLabelRemovedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationLabelRemovedLogOutputOutput'
  /AuditLogs/CreateConversationReadLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationReadLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationReadLogOutputOutput'
  /AuditLogs/CreateConversationStatusChangedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConversationStatusChangedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConversationStatusChangedLogOutputOutput'
  /AuditLogs/CreateStaffManualAddedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStaffManualAddedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateStaffManualAddedLogOutputOutput'
  /AuditLogs/CreateUserProfileAddedToListLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileAddedToListLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileAddedToListLogOutputOutput'
  /AuditLogs/CreateUserProfileChatHistoryBackedUpLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileChatHistoryBackedUpLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileChatHistoryBackedUpLogOutputOutput'
  /AuditLogs/CreateUserProfileDeletedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileDeletedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileDeletedLogOutputOutput'
  /AuditLogs/CreateUserProfileDeletedLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileDeletedLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileDeletedLogsOutputOutput'
  /AuditLogs/CreateUserProfileEnrolledIntoFlowHubWorkflowLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileEnrolledIntoFlowHubWorkflowLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutputOutput'
  /AuditLogs/CreateUserProfileFieldChangedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileFieldChangedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileFieldChangedLogOutputOutput'
  /AuditLogs/CreateUserProfileImportedLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileImportedLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileImportedLogsOutputOutput'
  /AuditLogs/CreateUserProfileRecoveredLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileRecoveredLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileRecoveredLogOutputOutput'
  /AuditLogs/CreateUserProfileRecoveredLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileRecoveredLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileRecoveredLogsOutputOutput'
  /AuditLogs/CreateUserProfileRemovedFromListLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileRemovedFromListLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileRemovedFromListLogOutputOutput'
  /AuditLogs/CreateUserProfileSoftDeletedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileSoftDeletedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileSoftDeletedLogOutputOutput'
  /AuditLogs/CreateUserProfileSoftDeletedLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserProfileSoftDeletedLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserProfileSoftDeletedLogsOutputOutput'
  /AuditLogs/DeleteStaffManualAddedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteStaffManualAddedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteStaffManualAddedLogOutputOutput'
  /AuditLogs/GetAuditLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAuditLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAuditLogsOutputOutput'
  /AuditLogs/GetUserProfileAuditLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserProfileAuditLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserProfileAuditLogOutputOutput'
  /AuditLogs/GetUserProfileAuditLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserProfileAuditLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserProfileAuditLogsOutputOutput'
  /AuditLogs/GetUserProfileAuditLogsV2:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserProfileAuditLogsV2Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserProfileAuditLogsV2OutputOutput'
  /AuditLogs/MigrateUserProfileAuditLogToAuditHub:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrateUserProfileAuditLogToAuditHubInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrateUserProfileAuditLogToAuditHubOutputOutput'
  /AuditLogs/UpdateStaffManualAddedLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStaffManualAddedLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateStaffManualAddedLogOutputOutput'
  /authorized/SystemAuditLogs/GetSystemAuditLogs:
    post:
      tags:
        - AuthorizedSystemAuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSystemAuditLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSystemAuditLogsOutputOutput'
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /SystemAuditLogs/CreateSystemAuditLog:
    post:
      tags:
        - SystemAuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSystemAuditLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSystemAuditLogOutputOutput'
components:
  schemas:
    AssignedTeamChangedLogData:
      type: object
      properties:
        original_team:
          $ref: '#/components/schemas/TeamLogData'
        new_team:
          $ref: '#/components/schemas/TeamLogData'
      additionalProperties: false
    AssigneeChangedLogData:
      type: object
      properties:
        original_assignee:
          $ref: '#/components/schemas/AssigneeLogData'
        new_assignee:
          $ref: '#/components/schemas/AssigneeLogData'
      additionalProperties: false
    AssigneeLogData:
      type: object
      properties:
        assignee_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
      additionalProperties: false
    AuditLog:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_time:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    AutomationTriggeredLogData:
      type: object
      properties:
        automation_name:
          type: string
          nullable: true
        automation_action_type:
          type: string
          nullable: true
        automation_action_log_id:
          type: integer
          format: int64
          nullable: true
        automation_status:
          type: string
          nullable: true
        automation_failed_error_message:
          type: string
          nullable: true
      additionalProperties: false
    BroadcastDeletedSystemLogData:
      type: object
      properties:
        broadcast_id:
          type: string
          nullable: true
        broadcast_name:
          type: string
          nullable: true
      additionalProperties: false
    BusinessHourConfigUpdatedSystemLogData:
      type: object
      properties:
        is_enabled:
          type: boolean
        weekly_hours:
          type: string
          nullable: true
      additionalProperties: false
    ChangedFieldLogData:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    CollaboratorAddedLogData:
      type: object
      properties:
        collaborators_added:
          type: array
          items:
            $ref: '#/components/schemas/AssigneeLogData'
          nullable: true
      additionalProperties: false
    CollaboratorRemovedLogData:
      type: object
      properties:
        collaborators_removed:
          type: array
          items:
            $ref: '#/components/schemas/AssigneeLogData'
          nullable: true
      additionalProperties: false
    ConversationChannelSwitchedLogData:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    ConversationReadLogData:
      type: object
      properties:
        read_staff:
          $ref: '#/components/schemas/AssigneeLogData'
      additionalProperties: false
    ConversationStatusChangedLogData:
      type: object
      properties:
        original_status:
          type: string
          nullable: true
        new_status:
          type: string
          nullable: true
        scheduled_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    CreateAutomationTriggeredLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/AutomationTriggeredLogData'
      additionalProperties: false
    CreateAutomationTriggeredLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateAutomationTriggeredLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateAutomationTriggeredLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationAssignedTeamChangedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/AssignedTeamChangedLogData'
      additionalProperties: false
    CreateConversationAssignedTeamChangedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationAssignedTeamChangedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationAssignedTeamChangedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationAssigneeChangedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/AssigneeChangedLogData'
      additionalProperties: false
    CreateConversationAssigneeChangedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationAssigneeChangedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationAssigneeChangedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationChannelSwitchedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/ConversationChannelSwitchedLogData'
      additionalProperties: false
    CreateConversationChannelSwitchedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationChannelSwitchedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationChannelSwitchedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationCollaboratorAddedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/CollaboratorAddedLogData'
      additionalProperties: false
    CreateConversationCollaboratorAddedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationCollaboratorAddedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationCollaboratorAddedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationCollaboratorRemovedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/CollaboratorRemovedLogData'
      additionalProperties: false
    CreateConversationCollaboratorRemovedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationCollaboratorRemovedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationCollaboratorRemovedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationLabelAddedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/LabelAddedLogData'
      additionalProperties: false
    CreateConversationLabelAddedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationLabelAddedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationLabelAddedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationLabelRemovedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/LabelRemovedLogData'
      additionalProperties: false
    CreateConversationLabelRemovedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationLabelRemovedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationLabelRemovedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationReadLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/ConversationReadLogData'
      additionalProperties: false
    CreateConversationReadLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationReadLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationReadLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationStatusChangedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/ConversationStatusChangedLogData'
      additionalProperties: false
    CreateConversationStatusChangedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateConversationStatusChangedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateConversationStatusChangedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateStaffManualAddedLogInput:
      required:
        - audit_log_text
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CreateStaffManualAddedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateStaffManualAddedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateStaffManualAddedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateSystemAuditLogInput:
      required:
        - data
        - type
      type: object
      properties:
        sleekflow_user_profile_id:
          type: string
          nullable: true
        type:
          minLength: 1
          type: string
        data: { }
      additionalProperties: false
    CreateSystemAuditLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateSystemAuditLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateSystemAuditLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileAddedToListLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_ids
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileAddedToListLogData'
      additionalProperties: false
    CreateUserProfileAddedToListLogOutput:
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileAddedToListLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileAddedToListLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileChatHistoryBackedUpLogInput:
      required:
        - audit_log_text
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CreateUserProfileChatHistoryBackedUpLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileChatHistoryBackedUpLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileChatHistoryBackedUpLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileDeletedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileDeletedLogData'
      additionalProperties: false
    CreateUserProfileDeletedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileDeletedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileDeletedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileDeletedLogsInput:
      required:
        - audit_log_text
        - data
        - sleekflow_company_id
        - sleekflow_user_profile_ids
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileDeletedLogData'
      additionalProperties: false
    CreateUserProfileDeletedLogsOutput:
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileDeletedLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileDeletedLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileEnrolledIntoFlowHubWorkflowLogInput:
      required:
        - sleekflow_user_profile_id
        - state_id
        - workflow_id
        - workflow_name
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_name:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileFieldChangedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileFieldChangedLogData'
      additionalProperties: false
    CreateUserProfileFieldChangedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileFieldChangedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileFieldChangedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileImportedLogsInput:
      required:
        - user_profile_imported_logs
      type: object
      properties:
        user_profile_imported_logs:
          type: array
          items:
            $ref: '#/components/schemas/UserProfileImportedLog'
      additionalProperties: false
    CreateUserProfileImportedLogsOutput:
      type: object
      additionalProperties: false
    CreateUserProfileImportedLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileImportedLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileRecoveredLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileRecoveredLogData'
      additionalProperties: false
    CreateUserProfileRecoveredLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileRecoveredLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileRecoveredLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileRecoveredLogsInput:
      required:
        - audit_log_text
        - data
        - sleekflow_company_id
        - sleekflow_user_profile_ids
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileRecoveredLogData'
      additionalProperties: false
    CreateUserProfileRecoveredLogsOutput:
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileRecoveredLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileRecoveredLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileRemovedFromListLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_ids
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileRemovedFromListLogData'
      additionalProperties: false
    CreateUserProfileRemovedFromListLogOutput:
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileRemovedFromListLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileRemovedFromListLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileSoftDeletedLogInput:
      required:
        - audit_log_text
        - data
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileSoftDeletedLogData'
      additionalProperties: false
    CreateUserProfileSoftDeletedLogOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileSoftDeletedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileSoftDeletedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileSoftDeletedLogsInput:
      required:
        - audit_log_text
        - data
        - sleekflow_company_id
        - sleekflow_user_profile_ids
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileSoftDeletedLogData'
      additionalProperties: false
    CreateUserProfileSoftDeletedLogsOutput:
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateUserProfileSoftDeletedLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserProfileSoftDeletedLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteStaffManualAddedLogInput:
      required:
        - id
        - sleekflow_user_profile_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteStaffManualAddedLogOutput:
      type: object
      properties:
        deleted_count:
          type: integer
          format: int32
      additionalProperties: false
    DeleteStaffManualAddedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteStaffManualAddedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Filter:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          minLength: 1
          pattern: '^[a-zA-Z0-9_]+$'
          type: string
        operator:
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|array_contains|startswith|in)$
          type: string
        value:
          nullable: true
      additionalProperties: false
    FlowBuilderEnrolledSystemLogData:
      type: object
      additionalProperties: false
    FlowBuilderUnenrolledSystemLogData:
      type: object
      additionalProperties: false
    FlowBuilderWorkflowDeletedSystemLogData:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAuditLogsInput:
      required:
        - filter_groups
        - limit
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetAuditLogsInputFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
      additionalProperties: false
    GetAuditLogsInputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
      additionalProperties: false
    GetAuditLogsOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetAuditLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAuditLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSystemAuditLogsInput:
      required:
        - filters
        - limit
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        filters:
          $ref: '#/components/schemas/GetSystemAuditLogsInputFilters'
        limit:
          maximum: 100
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetSystemAuditLogsInputFilters:
      type: object
      properties:
        types:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        from_created_time:
          type: string
          format: date-time
          nullable: true
        to_created_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    GetSystemAuditLogsOutput:
      type: object
      properties:
        user_profile_audit_logs:
          type: array
          items:
            $ref: '#/components/schemas/SystemAuditLog'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetSystemAuditLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSystemAuditLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserProfileAuditLogInput:
      required:
        - id
        - sleekflow_user_profile_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserProfileAuditLogOutput:
      type: object
      properties:
        user_profile_audit_log:
          $ref: '#/components/schemas/UserProfileAuditLog'
      additionalProperties: false
    GetUserProfileAuditLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserProfileAuditLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserProfileAuditLogsInput:
      required:
        - limit
        - offset
        - sleekflow_company_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        offset:
          maximum: 4000
          minimum: 0
          type: integer
          format: int32
        limit:
          maximum: 1000
          minimum: 0
          type: integer
          format: int32
      additionalProperties: false
    GetUserProfileAuditLogsOutput:
      type: object
      properties:
        userProfileAuditLogs:
          type: array
          items:
            $ref: '#/components/schemas/UserProfileAuditLog'
          nullable: true
      additionalProperties: false
    GetUserProfileAuditLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserProfileAuditLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserProfileAuditLogsV2Input:
      required:
        - filters
        - limit
        - sleekflow_company_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        filters:
          $ref: '#/components/schemas/GetUserProfileAuditLogsV2InputFilters'
        limit:
          type: integer
          format: int32
      additionalProperties: false
    GetUserProfileAuditLogsV2InputFilters:
      type: object
      properties:
        types:
          type: array
          items:
            type: string
          nullable: true
        has_sleekflow_staff_id:
          type: boolean
          nullable: true
      additionalProperties: false
    GetUserProfileAuditLogsV2Output:
      type: object
      properties:
        user_profile_audit_logs:
          type: array
          items:
            $ref: '#/components/schemas/UserProfileAuditLog'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetUserProfileAuditLogsV2OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserProfileAuditLogsV2Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ImpactedFieldLogData:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        import_action:
          type: string
          nullable: true
      additionalProperties: false
    LabelAddedLogData:
      type: object
      properties:
        labels_added:
          type: array
          items:
            $ref: '#/components/schemas/LabelLogData'
          nullable: true
      additionalProperties: false
    LabelLogData:
      type: object
      properties:
        label:
          type: string
          nullable: true
      additionalProperties: false
    LabelRemovedLogData:
      type: object
      properties:
        labels_removed:
          type: array
          items:
            $ref: '#/components/schemas/LabelLogData'
          nullable: true
      additionalProperties: false
    MessageTemplateUnbookmarkedSystemLogData:
      type: object
      properties:
        template_id:
          type: string
          nullable: true
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
        template_name:
          type: string
          nullable: true
      additionalProperties: false
    MigrateLog:
      required:
        - audit_log_text
        - sleekflow_company_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
        type:
          type: string
          nullable: true
      additionalProperties: false
    MigrateUserProfileAuditLogToAuditHubInput:
      required:
        - migrate_logs
      type: object
      properties:
        migrate_logs:
          type: array
          items:
            $ref: '#/components/schemas/MigrateLog'
      additionalProperties: false
    MigrateUserProfileAuditLogToAuditHubOutput:
      type: object
      additionalProperties: false
    MigrateUserProfileAuditLogToAuditHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigrateUserProfileAuditLogToAuditHubOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PiiMaskingConfigCreatedSystemLogData:
      type: object
      properties:
        pii_masking_config_id:
          type: integer
          format: int64
        display_name:
          type: string
          nullable: true
        regex_patterns:
          type: array
          items:
            type: string
          nullable: true
        masking_custom_object_schema_ids:
          type: array
          items:
            type: string
          nullable: true
        masking_roles:
          type: integer
          format: int32
        masking_locations:
          type: integer
          format: int32
        is_platform_api_masked:
          type: boolean
      additionalProperties: false
    PiiMaskingConfigDeletedSystemLogData:
      type: object
      properties:
        pii_masking_config_id:
          type: integer
          format: int64
        display_name:
          type: string
          nullable: true
      additionalProperties: false
    PiiMaskingConfigUpdatedSystemLogData:
      type: object
      properties:
        pii_masking_config_id:
          type: integer
          format: int64
        display_name:
          type: string
          nullable: true
        regex_patterns:
          type: array
          items:
            type: string
          nullable: true
        masking_custom_object_schema_ids:
          type: array
          items:
            type: string
          nullable: true
        masking_roles:
          type: integer
          format: int32
        masking_locations:
          type: integer
          format: int32
        is_platform_api_masked:
          type: boolean
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sort:
      required:
        - direction
        - field_name
        - is_case_sensitive
      type: object
      properties:
        field_name:
          minLength: 1
          type: string
        direction:
          minLength: 1
          type: string
        is_case_sensitive:
          type: boolean
      additionalProperties: false
    Staff2faRevokedSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
      additionalProperties: false
    StaffAddedAsCollaboratorSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        user_profile_id:
          type: string
          nullable: true
        user_profile_first_name:
          type: string
          nullable: true
        user_profile_last_name:
          type: string
          nullable: true
      additionalProperties: false
    StaffAddedToTeamsSystemLogData:
      required:
        - staff_id
        - team_ids
        - team_names
      type: object
      properties:
        staff_id:
          minLength: 1
          type: string
        team_ids:
          type: array
          items:
            type: string
        team_names:
          type: array
          items:
            type: string
      additionalProperties: false
    StaffAssignedToConversationSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        user_profile_id:
          type: string
          nullable: true
      additionalProperties: false
    StaffDeletedSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
      additionalProperties: false
    StaffRemovedAsCollaboratorSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        user_profile_id:
          type: string
          nullable: true
        user_profile_first_name:
          type: string
          nullable: true
        user_profile_last_name:
          type: string
          nullable: true
      additionalProperties: false
    StaffRemovedFromTeamSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        team_ids:
          type: array
          items:
            type: string
          nullable: true
        team_names:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    StaffRoleUpdatedSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
      additionalProperties: false
    StaffStatusUpdatedSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
      additionalProperties: false
    StaffUnassignedFromConversationSystemLogData:
      type: object
      properties:
        staff_id:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        user_profile_id:
          type: string
          nullable: true
      additionalProperties: false
    SystemAuditLog:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_time:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    TeamDeletedSystemLogData:
      type: object
      properties:
        team_id:
          type: string
          nullable: true
        team_name:
          type: string
          nullable: true
      additionalProperties: false
    TeamLogData:
      type: object
      properties:
        team_id:
          type: integer
          format: int64
        name:
          type: string
          nullable: true
      additionalProperties: false
    UpdateStaffManualAddedLogInput:
      required:
        - id
        - sleekflow_user_profile_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        audit_log_text:
          type: string
          nullable: true
        data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    UpdateStaffManualAddedLogOutput:
      type: object
      properties:
        user_profile_audit_log:
          $ref: '#/components/schemas/UserProfileAuditLog'
      additionalProperties: false
    UpdateStaffManualAddedLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateStaffManualAddedLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileAddedToListLogData:
      type: object
      properties:
        user_profile_added_to_list:
          $ref: '#/components/schemas/UserProfileListLogData'
      additionalProperties: false
    UserProfileAuditLog:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        audit_log_text:
          type: string
          nullable: true
        data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_time:
          type: string
          format: date-time
        updated_time:
          type: string
          format: date-time
          nullable: true
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UserProfileCreatedSystemLogData:
      type: object
      properties:
        user_profile_id:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileDeletedLogData:
      type: object
      properties:
        trigger_details:
          $ref: '#/components/schemas/UserProfileDeletedTriggerLogData'
      additionalProperties: false
    UserProfileDeletedSystemLogData:
      type: object
      properties:
        user_profile_id:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileDeletedTriggerLogData:
      type: object
      properties:
        trigger_source:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        staff_name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileFieldChangedLogData:
      type: object
      properties:
        changed_fields:
          type: array
          items:
            $ref: '#/components/schemas/ChangedFieldLogData'
          nullable: true
      additionalProperties: false
    UserProfileImportedLog:
      required:
        - audit_log_text
        - data
        - sleekflow_company_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        audit_log_text:
          minLength: 1
          type: string
        data:
          $ref: '#/components/schemas/UserProfileImportedLogData'
      additionalProperties: false
    UserProfileImportedLogData:
      type: object
      properties:
        impacted_fields:
          type: array
          items:
            $ref: '#/components/schemas/ImpactedFieldLogData'
          nullable: true
      additionalProperties: false
    UserProfileLabelCreatedsSystemLogData:
      type: object
      properties:
        label_name:
          type: string
          nullable: true
        label_color:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileLabelDeletedSystemLogData:
      type: object
      properties:
        label_name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileLabelUpdatedSystemLogData:
      type: object
      properties:
        label_name:
          type: string
          nullable: true
        label_color:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileListCreatedSystemLogData:
      required:
        - list_id
        - list_name
      type: object
      properties:
        list_id:
          minLength: 1
          type: string
        list_name:
          minLength: 1
          type: string
      additionalProperties: false
    UserProfileListDeletedSystemLogData:
      type: object
      properties:
        list_id:
          type: string
          nullable: true
        list_name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileListExportedSystemLogData:
      type: object
      properties:
        list_id:
          type: string
          nullable: true
        list_name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileListLogData:
      type: object
      properties:
        list_id:
          type: integer
          format: int64
        name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfilePropertyUpdatedSystemLogData:
      type: object
      properties:
        user_profile_id:
          type: string
          nullable: true
        property_name:
          type: string
          nullable: true
        old_value:
          type: string
          nullable: true
        new_value:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileRecoveredLogData:
      type: object
      properties:
        trigger_details:
          $ref: '#/components/schemas/UserProfileRecoveredTriggerLogData'
      additionalProperties: false
    UserProfileRecoveredTriggerLogData:
      type: object
      properties:
        trigger_source:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        staff_name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfileRemovedFromListLogData:
      type: object
      properties:
        user_profile_removed_from_list:
          $ref: '#/components/schemas/UserProfileListLogData'
      additionalProperties: false
    UserProfileSoftDeletedLogData:
      type: object
      properties:
        trigger_details:
          $ref: '#/components/schemas/UserProfileSoftDeletedTriggerLogData'
      additionalProperties: false
    UserProfileSoftDeletedTriggerLogData:
      type: object
      properties:
        trigger_source:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        staff_name:
          type: string
          nullable: true
      additionalProperties: false