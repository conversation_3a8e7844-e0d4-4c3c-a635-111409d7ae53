﻿using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.Models.Chats;
using static Microsoft.SemanticKernel.ChatCompletion.AuthorRole;

namespace Sleekflow.IntelligentHub.Evaluator.Utils;

public static class ChatEntriesUtil
{
    public static SfChatEntry ToChatEntries(ChatMessageContent context)
    {
        if (context.Role == AuthorRole.System)
        {
            return new SfChatEntry
            {
                Sys = context.Content,
            };
        }

        if (context.Role == User)
        {
            return new SfChatEntry
            {
                User = context.Content,
            };
        }

        if (context.Role == Assistant)
        {
            return new SfChatEntry
            {
                Bot = context.Content,
            };
        }

        throw new InvalidOperationException($"Unknown role: {context.Role}");
    }
}