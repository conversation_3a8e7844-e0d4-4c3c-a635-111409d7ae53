using System.ComponentModel.DataAnnotations;
using GraphApi.Client.ApiClients.Exceptions;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.Triggers.Wabas;

[TriggerGroup(ControllerNames.Wabas)]
public class OnboardPartnersToMMLite
    : ITrigger<
        OnboardPartnersToMMLite.OnboardPartnersToMMLiteInput,
        OnboardPartnersToMMLite.OnboardPartnersToMMLiteOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<OnboardPartnersToMMLite> _logger;

    public OnboardPartnersToMMLite(IWabaService wabaService, ILogger<OnboardPartnersToMMLite> logger)
    {
        _wabaService = wabaService;
        _logger = logger;
    }

    public class OnboardPartnersToMMLiteInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public OnboardPartnersToMMLiteInput(
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string> sleekflowStaffTeamIds,
            string facebookBusinessId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class OnboardPartnersToMMLiteOutput
    {
        [JsonProperty("wabas")]
        public List<WabaDto> Wabas { get; set; }

        [JsonConstructor]
        public OnboardPartnersToMMLiteOutput(List<WabaDto> wabas)
        {
            Wabas = wabas;
        }
    }

    public async Task<OnboardPartnersToMMLiteOutput> F(OnboardPartnersToMMLiteInput input)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var wabas = await _wabaService.OnboardPartnersToMMLiteAsync(
            input.SleekflowCompanyId,
            input.FacebookBusinessId,
            sleekflowStaff);

        return new OnboardPartnersToMMLiteOutput(wabas.Select(w => new WabaDto(w)).ToList());
    }
}