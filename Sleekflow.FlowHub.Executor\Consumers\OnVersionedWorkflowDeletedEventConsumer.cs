﻿using MassTransit;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnVersionedWorkflowDeletedEventConsumerDefinition
    : ConsumerDefinition<OnVersionedWorkflowDeletedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnVersionedWorkflowDeletedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnVersionedWorkflowDeletedEventConsumer : IConsumer<OnVersionedWorkflowDeletedEvent>
{
    private readonly IWorkflowService _workflowService;
    private readonly IStateService _stateService;
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IBurstyWorkflowService _burstyWorkflowService;
    private readonly ILogger<OnVersionedWorkflowDeletedEventConsumer> _logger;

    public OnVersionedWorkflowDeletedEventConsumer(
        IWorkflowService workflowService,
        IStateService stateService,
        IWorkflowRuntimeService workflowRuntimeService,
        IBurstyWorkflowService burstyWorkflowService,
        ILogger<OnVersionedWorkflowDeletedEventConsumer> logger)
    {
        _workflowService = workflowService;
        _stateService = stateService;
        _workflowRuntimeService = workflowRuntimeService;
        _burstyWorkflowService = burstyWorkflowService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnVersionedWorkflowDeletedEvent> context)
    {
        var @event = context.Message;

        var runningStates = await _stateService.GetRunningStatesByWorkflowVersionedIdAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowVersionedId);

        if (runningStates.Count > 0)
        {
            foreach (var state in runningStates)
            {
                await _workflowRuntimeService.CancelWorkflowAsync(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    StateReasonCodes.VersionedWorkflowDeleted,
                    @event.DeletedBy);
            }
        }

        await _workflowService.DeleteLeftoverVersionedWorkflowAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowVersionedId);

        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowVersionedId);

        if (proxyWorkflow is not null
            && (proxyWorkflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema is true ||
                proxyWorkflow.WorkflowScheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted is true)
            && proxyWorkflow.WorkflowScheduleSettings.DurablePayload is not null)
        {
            try
            {
                await _burstyWorkflowService.TerminateScheduledWorkflowAsync(
                    @event.SleekflowCompanyId,
                    @event.WorkflowId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to terminate scheduled workflow {WorkflowVersionedId} for company {CompanyId}",
                    @event.WorkflowVersionedId,
                    @event.SleekflowCompanyId);
            }
        }
    }
}