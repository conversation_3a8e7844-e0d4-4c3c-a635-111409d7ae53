using Newtonsoft.Json;
using Sleekflow.Configs;

namespace Sleekflow.Infras.Components.Auth0s;

public class Auth0Config
{
    public string Name { get; }

    public string Domain { get; }

    public bool EnableCustomDomainInEmails { get; }

    public string PostLoginWebhook { get; }

    public string PreUserRegistrationWebhook { get; }

    public string PostChangePasswordWebhook { get; }

    public string? RequiresMfaWebhook { get; }

    public string ActionIssuer { get; }

    public string ActionAudience { get; }

    public string UserEmailCheckSecretKey { get; }

    public IEnumerable<ClientConfig> ClientConfigs { get; }

    public IEnumerable<ConnectionConfig> ConnectionConfigs { get; }

    public CustomDomainConfig CustomDomainConfig { get; }

    public Auth0Config()
    {
        Domain = new Pulumi.Config("auth0").Require("domain");

        var sleekflowConfig = new Pulumi.Config("sleekflow");

        var connectionsStr = sleekflowConfig.Require("connections");
        var clientsStr = sleekflowConfig.Require("clients");
        var customDomainStr = sleekflowConfig.Require("custom_domain");

        Name = sleekflowConfig.Require("name");
        EnableCustomDomainInEmails = sleekflowConfig.GetBoolean("enable_custom_domain_in_emails") ?? false;
        ActionIssuer = sleekflowConfig.Require("action_issuer");
        ActionAudience = sleekflowConfig.Require("action_audience");

        PostLoginWebhook = sleekflowConfig.Require("post_login_webhook");
        PreUserRegistrationWebhook = sleekflowConfig.Require("pre_user_registration_webhook");
        PostChangePasswordWebhook = sleekflowConfig.Require("post_change_password_webhook");
        RequiresMfaWebhook = sleekflowConfig.Require("requires_mfa_webhook");
        UserEmailCheckSecretKey = sleekflowConfig.Require("user_email_check_secret_key");

        ConnectionConfigs = JsonConvert.DeserializeObject<List<ConnectionConfig>>(connectionsStr)!;
        ClientConfigs = JsonConvert.DeserializeObject<List<ClientConfig>>(clientsStr)!;
        CustomDomainConfig = JsonConvert.DeserializeObject<CustomDomainConfig>(customDomainStr)!;
    }
}