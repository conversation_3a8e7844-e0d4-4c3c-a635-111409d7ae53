using Sleekflow.Infras.Constants;

namespace Sleekflow.Infras.Tests;

public class FilteredManagedEnvAndAppsTupleTest
{
    private readonly FilteredManagedEnvAndAppsTuple _filteredManagedEnvAndAppsTuple =
        InitFilteredManagedEnvAndAppsTuple();

    [Test]
    public void FormatContainerAppName()
    {
        var containerAppName =
            _filteredManagedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.CrmHub));
        Assert.That(containerAppName, Is.EqualTo("sleekflow-crm-hub-app-eus"));

        var secondaryFilterManagedEnvAndAppsTuple = InitFilteredManagedEnvAndAppsTuple("sec");
        var secondaryContainerAppName =
            secondaryFilterManagedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.CrmHub));
        Assert.That(secondaryContainerAppName, Is.EqualTo("sleekflow-crm-hub-app-eus-sec"));
    }

    private static FilteredManagedEnvAndAppsTuple InitFilteredManagedEnvAndAppsTuple(string? name = null)
    {
        return new FilteredManagedEnvAndAppsTuple(
            null!,
            null!,
            null!,
            null!,
            null!,
            name ?? "pri",
            LocationNames.EastUs,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!);
    }
}