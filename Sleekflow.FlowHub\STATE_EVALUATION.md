# Understanding State Evaluation in FlowHub

## Introduction

State evaluation is a core mechanism in the FlowHub workflow engine that allows dynamic values and expressions to be processed during workflow execution. The `IStateEvaluator` interface and its implementations provide the ability to interpret and evaluate expressions embedded in workflow steps, enabling dynamic and context-aware workflow behavior.

## The State Evaluator

The `IStateEvaluator` is responsible for resolving expressions against the current workflow state. It parses expressions written in a templating syntax and evaluates them to produce concrete values at runtime.

### Core Functionality

```csharp
public interface IStateEvaluator
{
    Task<object?> EvaluateExpressionAsync(ProxyState state, string? expression);
    Task<Dictionary<string, object?>> EvaluateDictExpressionAsync(ProxyState state, Dictionary<string, string> expressionDict);
    // Additional methods...
}
```

## Expression Syntax

FlowHub uses the [Scriban](https://github.com/scriban/scriban) templating engine for expression evaluation. Expressions are written within double curly braces:

```
{{ expression }}
```

### Basic Expressions

```
{{ state.contact.firstName }}          // Access a property in the state
{{ state.order.amount * 1.08 }}        // Mathematical operation
{{ state.isVip == true ? "VIP" : "" }} // Conditional expression
```

### Accessing State Data

The most common pattern is accessing values stored in the workflow state:

```
{{ state.contact.emailAddress }}
{{ state.conversation.lastMessageContent }}
{{ state.workflow.triggerData.requestBody }}
```

### Functions and Utilities

FlowHub provides several built-in functions that can be used in expressions:

```
{{ json.parse(state.responseBody) }}        // Parse JSON string
{{ array.first(state.results) }}             // Get first item from array
{{ string.to_lower(state.user.name) }}       // Convert to lowercase
{{ date.now | date.add_days 7 | date.to_string }} // Date manipulation
```

## Using StateEvaluator in Step Executors

### Single Expression Evaluation

The most common use case is evaluating a single expression string:

```csharp
// Converting expression to a string value
string url = (string)(await _stateEvaluator.EvaluateExpressionAsync(
    state,
    callStep.Args.UrlExpr) ?? callStep.Args.UrlExpr);
```

This pattern handles cases where:
- The expression evaluates to a value (e.g., `{{ state.apiEndpoint }}` becomes `"https://api.example.com"`)
- The expression is a literal string without template syntax (e.g., `"https://api.example.com"` remains unchanged)
- The expression evaluates to null, in which case the original expression is used as a fallback

### Dictionary Expression Evaluation

For evaluating multiple expressions in a key-value structure:

```csharp
// Evaluating a dictionary of header values
var headersDict = callStep.Args.HeadersKeyExprDict == null
    ? new Dictionary<string, object?>()
    : await _stateEvaluator.EvaluateDictExpressionAsync(
        state,
        callStep.Args.HeadersKeyExprDict);
```

This is useful for HTTP headers, query parameters, or any collection of key-value pairs.

## Advanced Features

### JSON Parsing and Manipulation

Working with JSON data is common in workflows, especially when integrating with APIs:

```
// In a workflow expression
{{ json.parse(state.apiResponse).results[0].id }}
```

```csharp
// In a step executor
var responseObj = JsonConvert.DeserializeObject<JObject>(responseString);
var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
    state,
    step.Id,
    responseObj.ToString());
```

### Expression Error Handling

When expressions fail to evaluate, appropriate error handling is crucial:

```csharp
try {
    var result = await _stateEvaluator.EvaluateExpressionAsync(state, expression);
    // Use result
} catch (SfScriptingException ex) {
    // Handle expression evaluation error
    _logger.LogError(ex, "Failed to evaluate expression: {Expression}", expression);
    throw new SfFlowHubUserFriendlyException(
        UserFriendlyErrorCodes.ExpressionEvaluationError,
        $"Failed to evaluate expression: {expression}",
        ex);
}
```

## State Update After Evaluation

After processing steps and evaluating expressions, the updated state should be stored for future steps:

```csharp
// Update state with new content
var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
    state,
    step.Id,
    resultContent);

// Complete the step with the updated state
await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
```

## Best Practices

### 1. Validate Required Expressions

Always validate that required expressions evaluate to expected values:

```csharp
var evaluated = await _stateEvaluator.EvaluateExpressionAsync(state, expression);
if (evaluated == null) {
    throw new SfFlowHubUserFriendlyException(
        UserFriendlyErrorCodes.ValidationError,
        $"Required expression '{expression}' evaluated to null");
}
```

### 2. Type Safety

The `EvaluateExpressionAsync` method returns `object?`, so proper type casting and checking is important:

```csharp
var evaluatedValue = await _stateEvaluator.EvaluateExpressionAsync(state, expression);
if (evaluatedValue is not string stringValue) {
    throw new SfFlowHubUserFriendlyException(
        UserFriendlyErrorCodes.ValidationError,
        $"Expression '{expression}' must evaluate to a string");
}
```

### 3. Expression Complexity

Keep expressions simple and composable. Complex expressions are harder to maintain and debug:

```
// Avoid
{{ if state.contact.premium == true && date.parse(state.contact.subscriptionEndDate) > date.now then "Active Premium" else "Standard" end }}

// Prefer multiple simpler steps and expressions
// Step 1: Evaluate subscription status
{{ state.contact.premium == true && date.parse(state.contact.subscriptionEndDate) > date.now }}
// Step 2: Use result in a subsequent expression
{{ if state.subscriptionActive then "Active Premium" else "Standard" end }}
```

### 4. Error Messages

Provide clear error messages when expressions fail to evaluate:

```csharp
throw new SfFlowHubUserFriendlyException(
    UserFriendlyErrorCodes.ExpressionEvaluationError,
    $"Could not evaluate '{expression}'. Ensure the expression is valid and all referenced properties exist.");
```

### 5. Performance Considerations

Expression evaluation can be resource-intensive for complex expressions or large data structures:

- Cache frequently accessed state values
- Avoid deeply nested expressions
- Consider splitting complex operations across multiple steps

## Common Pitfalls

### Null Reference Errors

The most common error in expression evaluation is trying to access properties on null objects:

```
// If state.contact is null, this will fail:
{{ state.contact.firstName }}

// Safer approach with null checking:
{{ state.contact != null ? state.contact.firstName : "" }}
```

### Type Conversion Issues

Be careful when working with different data types:

```
// This might fail if state.amount is not a number:
{{ state.amount + 10 }}

// Safer approach:
{{ (string.to_number(state.amount) ?? 0) + 10 }}
```

### String vs. Non-String Values

Pay attention to automatic type conversion in expressions:

```
// If state.limit is a number (e.g., 5), this comparison will fail:
{{ state.limit == "5" }}

// Proper numeric comparison:
{{ state.limit == 5 }}
```

## Integration with Other FlowHub Components

### With IStateAggregator

The `IStateAggregator` works closely with `IStateEvaluator` to update the workflow state:

```csharp
// First, evaluate expressions to get concrete values
var apiUrl = (string)await _stateEvaluator.EvaluateExpressionAsync(state, step.Args.ApiUrl);

// Make API call and get response
var response = await _httpClient.GetStringAsync(apiUrl);

// Update state with response data
var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
    state,
    step.Id,
    response);
```

### With Step Validation

Expression validation often happens during step validation:

```csharp
public async Task ValidateStepAsync(Step step)
{
    var callStep = step as CallStep<MyStepArgs>;
    if (callStep == null)
        throw new ArgumentException("Invalid step type");

    if (string.IsNullOrEmpty(callStep.Args.RequiredExpression))
        throw new ValidationException("RequiredExpression cannot be empty");

    // Additional validation...
}
```

## Conclusion

The state evaluation mechanism is central to FlowHub's ability to create dynamic, context-aware workflows. Understanding how to properly use the `IStateEvaluator` to work with expressions enables developers to create powerful and flexible workflow steps.

For more information on implementing custom steps that leverage state evaluation, see the [Getting Started Guide](./GETTING_STARTED.md).