using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers;

public static class TypeSymbolUtils
{
    public static bool IsCollectionType(ITypeSymbol typeSymbol)
    {
        if (typeSymbol is INamedTypeSymbol namedTypeSymbol
            && namedTypeSymbol.TypeArguments.Length == 1 // Generic with one type argument
            && namedTypeSymbol.Name is "List" or "IList" // List<T> or IList<T>
           )
        {
            return true;
        }

        return false;
    }

    public static bool IsNonPrimitiveType(ITypeSymbol typeSymbol)
    {
        return typeSymbol.SpecialType == SpecialType.None
               && typeSymbol is not INamedTypeSymbol { Name: "String" or "DateTime" or "DateTimeOffset" };
    }

    public static string GetNamespace(SyntaxNode syntaxNode)
    {
        return string.Join(
            ".",
            syntaxNode
                .Ancestors()
                .OfType<BaseNamespaceDeclarationSyntax>()
                .Reverse()
                .Select(_ => _.Name));
    }

    public static bool IsJsonPropertyAttribute(SyntaxNodeAnalysisContext context, AttributeSyntax attributeSyntax)
    {
        var attributeType = context.SemanticModel.GetTypeInfo(attributeSyntax).Type;

        return attributeType?.ContainingNamespace.ToString() == "Newtonsoft.Json"
               && attributeType.Name == "JsonPropertyAttribute";
    }

    public static bool IsJsonConstructorAttribute(SyntaxNodeAnalysisContext context, AttributeSyntax attributeSyntax)
    {
        var attributeType = context.SemanticModel.GetTypeInfo(attributeSyntax).Type;

        return attributeType?.ContainingNamespace.ToString() == "Newtonsoft.Json"
               && attributeType.Name == "JsonConstructorAttribute";
    }
}