using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs;

public class CustomCatalogFileDto : EntityDto, IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty("blob")]
    public Blob Blob { get; set; }

    [JsonProperty("file_process_status")]
    public string FileProcessStatus { get; set; }

    [JsonProperty("progress")]
    public int Progress { get; }

    [JsonProperty("log_sas_url")]
    public string? LogSasUrl { get; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public CustomCatalogFileDto(
        string id,
        string sleekflowCompanyId,
        string storeId,
        Blob blob,
        string fileProcessStatus,
        int progress,
        string? logSasUrl,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StoreId = storeId;
        Blob = blob;
        FileProcessStatus = fileProcessStatus;
        Progress = progress;
        LogSasUrl = logSasUrl;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public CustomCatalogFileDto(
        CustomCatalogFile customCatalogFile,
        int progress,
        string? logSasUrl)
        : this(
            customCatalogFile.Id,
            customCatalogFile.SleekflowCompanyId,
            customCatalogFile.StoreId,
            customCatalogFile.Blob,
            customCatalogFile.FileProcessStatus,
            progress,
            logSasUrl,
            customCatalogFile.CreatedAt,
            customCatalogFile.UpdatedAt)
    {
    }
}