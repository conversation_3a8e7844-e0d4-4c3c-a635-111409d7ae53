﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class GetWebScraperTask : ITrigger<GetWebScraperTask.GetWebScraperTaskInput, GetWebScraperTask.GetWebScraperTaskOutput>
{
    private readonly IWebScraperService _webScraperService;

    public GetWebScraperTask(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class GetWebScraperTaskInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [JsonConstructor]
        public GetWebScraperTaskInput(string sleekflowCompanyId, string apifyTaskId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyTaskId = apifyTaskId;
        }
    }

    public class GetWebScraperTaskOutput
    {
        [JsonProperty(WebScraperTask.PropertyNameWebScraperTask)]
        public WebScraperTask WebScraperTask { get; set; }

        [JsonConstructor]
        public GetWebScraperTaskOutput(WebScraperTask webScraperTask)
        {
            WebScraperTask = webScraperTask;
        }
    }

    public async Task<GetWebScraperTaskOutput> F(GetWebScraperTaskInput getWebScraperTaskInput)
    {
        var webScraperTask = await _webScraperService.GetWebScraperTaskAsync(
            getWebScraperTaskInput.SleekflowCompanyId,
            getWebScraperTaskInput.ApifyTaskId);

        return new GetWebScraperTaskOutput(webScraperTask);
    }
}