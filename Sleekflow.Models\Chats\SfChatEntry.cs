using Newtonsoft.Json;

namespace Sleekflow.Models.Chats;

public class SfChatEntry
{
    [JsonProperty("user")]
    public string? User { get; set; }

    [JsonProperty("bot")]
    public string? Bot { get; set; }

    [JsonProperty("sys")]
    public string? Sys { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset? CreatedAt { get; set; }

    [JsonProperty("files")]
    public List<SfChatEntryFile>? Files { get; set; }
}

public class SfChatEntryFile
{

    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("mime_type")]
    public string MimeType { get; set; }

    [JsonProperty("file_size")]
    public long FileSize { get; set; }

    [JsonConstructor]
    public SfChatEntryFile(string url, string mimeType, long fileSize)
    {
        Url = url;
        MimeType = mimeType;
        FileSize = fileSize;
    }
}