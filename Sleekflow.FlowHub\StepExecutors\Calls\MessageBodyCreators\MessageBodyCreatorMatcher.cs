using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IMessageBodyCreatorMatcher
{
    List<IMessageBodyCreator> GetMessageBodyCreators();

    IMessageBodyCreator MatchCreator(string channelType);
}

public class MessageBodyCreatorMatcher : IScopedService, IMessageBodyCreatorMatcher
{
    private readonly List<IMessageBodyCreator> _messageBodyCreators;

    public MessageBodyCreatorMatcher(
        IFacebookMessageBodyCreator facebookMessageBodyCreator,
        IInstagramMessageBodyCreator instagramMessageBodyCreator,
        ILineMessageBodyCreator lineMessageBodyCreator,
        ILiveChatMessageBodyCreator liveChatMessageBodyCreator,
        ISmsMessageBodyCreator smsMessageBodyCreator,
        ITelegramMessageBodyCreator telegram<PERSON>essageBodyCreator,
        IViberMessageBodyCreator viberMessageBodyCreator,
        IWeChatMessageBodyCreator wechatMessageBodyCreator,
        IWhatsAppMessageBodyCreator whatsAppMessageBodyCreator
    )
    {
        _messageBodyCreators = new List<IMessageBodyCreator>
        {
            facebookMessageBodyCreator,
            instagramMessageBodyCreator,
            lineMessageBodyCreator,
            liveChatMessageBodyCreator,
            smsMessageBodyCreator,
            telegramMessageBodyCreator,
            viberMessageBodyCreator,
            whatsAppMessageBodyCreator,
            wechatMessageBodyCreator,
        };
    }

    public List<IMessageBodyCreator> GetMessageBodyCreators()
    {
        return _messageBodyCreators;
    }

    public IMessageBodyCreator MatchCreator(string channelType)
    {
        var creator = _messageBodyCreators.Find(c => c.CanHandle(channelType));
        if (creator != null)
        {
            return creator;
        }

        throw new ArgumentOutOfRangeException(nameof(channelType), $"No message body creator found for channel type '{channelType}'");
    }
}