using AutoMapper;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

namespace Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile.TypeConverter;

public class SubsidiaryTypeConverter : ITypeConverter<string, Subsidiary>
{
    public Subsidiary Convert(string source, Subsidiary destination, ResolutionContext context)
    {
        return new Subsidiary(source);
    }
}