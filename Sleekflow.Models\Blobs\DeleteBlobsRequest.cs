using Newtonsoft.Json;

namespace Sleekflow.Models.Blobs;

public class DeleteBlobsRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("blob_names")]
    public List<string> BlobNames { get; set; }

    [JsonProperty("blob_type")]
    public string BlobType { get; set; }

    [JsonConstructor]
    public DeleteBlobsRequest(
        string sleekflowCompanyId,
        List<string> blobNames,
        string blobType)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        BlobNames = blobNames;
        BlobType = blobType;
    }
}