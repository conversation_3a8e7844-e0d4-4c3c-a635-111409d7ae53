using Sleekflow.CommerceHub.Models.CustomCatalogs.Configs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.CustomCatalogs.Configs;

public interface ICustomCatalogConfigRepository : IRepository<CustomCatalogConfig>
{
}

public class CustomCatalogConfigRepository
    : BaseRepository<CustomCatalogConfig>, ICustomCatalogConfigRepository, IScopedService
{
    public CustomCatalogConfigRepository(
        IServiceProvider serviceProvider,
        ILogger<CustomCatalogConfigRepository> logger)
        : base(logger, serviceProvider)
    {
    }
}