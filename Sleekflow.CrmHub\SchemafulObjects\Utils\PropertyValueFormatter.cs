﻿using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;

namespace Sleekflow.CrmHub.SchemafulObjects.Utils;

public static class PropertyValueFormatter
{
    /// <summary>
    /// Format property value to meet the requirement of FlowHub trigger event body
    /// - map Option.Id to Option.Value
    /// - add a set of Property.UniqueName KVP into property values.
    /// </summary>
    /// <param name="properties">Properties.</param>
    /// <param name="inputPropertyValues">Raw property values.</param>
    /// <param name="logger">Logger.</param>
    /// <returns>Formatted property values.</returns>
    public static Dictionary<string, object?> FormatPropertyValueForFlowHubTriggerEventBody(
        List<Property> properties,
        Dictionary<string, object?> inputPropertyValues,
        ILogger logger)
    {
        var formattedPropertyValues = new Dictionary<string, object?>();

        foreach (var kvp in inputPropertyValues)
        {
            try
            {
                var property = properties.Find(p => p.Id == kvp.Key);

                switch (property!.DataType.Name)
                {
                    case SchemaPropertyDataTypes.SingleChoice:
                        var optionValue = property.Options!.Find(o => o.Id == (string) kvp.Value!)!.Value;
                        formattedPropertyValues.Add(kvp.Key, kvp.Value);
                        formattedPropertyValues.Add(property.UniqueName, optionValue);

                        break;
                    case SchemaPropertyDataTypes.MultipleChoice:
                        var inputValue =
                            kvp.Value as List<string>
                            ?? (kvp.Value as JArray)!.Select(x => x.ToString()).ToList(); // option ids
                        var mappedValue = inputValue.Select(id => property.Options!.Find(o => o.Id == id)!.Value)
                            .ToList(); // option values
                        formattedPropertyValues.Add(kvp.Key, kvp.Value);
                        formattedPropertyValues.Add(property.UniqueName, mappedValue);

                        break;

                    case SchemaPropertyDataTypes.ArrayObject:
                        var innerSchemafulObjects = FormatArrayObjectPropertyValue(property, kvp.Value, logger);

                        formattedPropertyValues.Add(kvp.Key, kvp.Value);
                        formattedPropertyValues.Add(property.UniqueName, innerSchemafulObjects);

                        break;
                    default:
                        formattedPropertyValues.Add(kvp.Key, kvp.Value);
                        formattedPropertyValues.Add(property.UniqueName, kvp.Value);

                        break;
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(
                    ex,
                    "{MethodName} Failed to format property value. {PropertyKeyValuePair}",
                    nameof(FormatPropertyValueForFlowHubTriggerEventBody),
                    kvp);

                formattedPropertyValues.TryAdd(kvp.Key, kvp.Value);
            }
        }

        return formattedPropertyValues;
    }

    private static List<InnerSchemafulObjectDto> FormatArrayObjectPropertyValue(
        Property property,
        object? value,
        ILogger logger)
    {
        var innerSchema = property.DataType.GetInnerSchema();

        var innerSchemafulObjects = InnerSchemafulObjectDto.Parse(value);

        foreach (var innerSchemafulObject in innerSchemafulObjects)
        {
            innerSchemafulObject.PropertyValues = FormatPropertyValueForFlowHubTriggerEventBody(
                innerSchema.Properties,
                innerSchemafulObject.PropertyValues,
                logger);
        }

        return innerSchemafulObjects;
    }
}