﻿using System.Collections.Concurrent;

namespace Sleekflow.Extensions;

public static class TaskEnumerableExtensions
{
    public static Task WhenAllFailFast(this IEnumerable<Task>? taskEnumerable)
    {
        if (taskEnumerable is null)
        {
            throw new ArgumentNullException(nameof(taskEnumerable));
        }

        var tasks = taskEnumerable
            .Where(t => t is not null)
            .ToList();

        if (tasks.Count == 0)
        {
            return Task.CompletedTask;
        }

        var remaining = tasks.Count;
        var tcs = new TaskCompletionSource<object?>(
            TaskCreationOptions.RunContinuationsAsynchronously);

        foreach (var task in tasks)
        {
            HandleCompletion(task);
        }

        return tcs.Task;

        async void HandleCompletion(Task task)
        {
            try
            {
                await task;

                if (Interlocked.Decrement(ref remaining) == 0)
                {
                    tcs.TrySetResult(null);
                }
            }
            catch (OperationCanceledException)
            {
                tcs.TrySetCanceled();
            }
            catch (Exception ex)
            {
                tcs.TrySetException(ex);
            }
        }
    }

    public static Task<IEnumerable<T>> WhenAllFailFast<T>(
        this IEnumerable<Task<T>>? taskEnumerable,
        Func<T, bool> shouldFailPredicate)
        where T : class
    {
        if (taskEnumerable is null)
        {
            throw new ArgumentNullException(nameof(taskEnumerable));
        }

        var tasks = taskEnumerable
            .Where(t => t is not null)
            .ToList();

        if (tasks.Count == 0)
        {
            return Task.FromResult(Enumerable.Empty<T>());
        }

        var remaining = tasks.Count;
        var tcs = new TaskCompletionSource<IEnumerable<T>>(
            TaskCreationOptions.RunContinuationsAsynchronously);

        var results = new BlockingCollection<T>();

        foreach (var task in tasks)
        {
            HandleCompletion(task);
        }

        return tcs.Task;

        async void HandleCompletion(Task<T> task)
        {
            try
            {
                var result = await task;

                results.Add(result);

                bool shouldFailResult;
                try
                {
                    shouldFailResult = shouldFailPredicate(result);
                }
                catch
                {
                    shouldFailResult = true;
                }

                if (shouldFailResult)
                {
                    tcs.TrySetResult(results);
                }
                else if (Interlocked.Decrement(ref remaining) == 0)
                {
                    tcs.TrySetResult(results);
                }
            }
            catch (OperationCanceledException)
            {
                tcs.TrySetCanceled();
            }
            catch (Exception ex)
            {
                tcs.TrySetException(ex);
            }
        }
    }
}