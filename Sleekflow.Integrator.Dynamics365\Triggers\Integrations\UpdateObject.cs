using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class UpdateObject : ITrigger
{
    private readonly IDynamics365ObjectService _dynamics365ObjectService;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;

    public UpdateObject(
        IDynamics365ObjectService dynamics365ObjectService,
        IDynamics365AuthenticationService dynamics365AuthenticationService)
    {
        _dynamics365ObjectService = dynamics365ObjectService;
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
    }

    public class UpdateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public UpdateObjectInput(
            string sleekflowCompanyId,
            string objectId,
            Dictionary<string, object?> dict,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            Dict = dict;
            EntityTypeName = entityTypeName;
        }
    }

    public class UpdateObjectOutput
    {
        [JsonConstructor]
        public UpdateObjectOutput(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }

        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }
    }

    public async Task<UpdateObjectOutput> F(UpdateObjectInput updateObjectInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(updateObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var contact = await _dynamics365ObjectService.GetObjectAsync(
            authentication,
            updateObjectInput.ObjectId,
            updateObjectInput.EntityTypeName);
        if (contact == null)
        {
            throw new SfNotFoundObjectException(updateObjectInput.ObjectId);
        }

        var getFieldsOutput =
            await _dynamics365ObjectService.GetFieldsAsync(authentication, updateObjectInput.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();

        var dict = updateObjectInput.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e => e.Value);

        await _dynamics365ObjectService.UpdateAsync(
            authentication,
            dict,
            updateObjectInput.ObjectId,
            updateObjectInput.EntityTypeName);

        return new UpdateObjectOutput(false);
    }
}