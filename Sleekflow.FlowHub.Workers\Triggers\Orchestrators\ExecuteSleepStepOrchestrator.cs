using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Workers;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class ExecuteSleepStepOrchestrator
{
    private readonly ILogger<ExecuteSleepStepOrchestrator> _logger;

    public ExecuteSleepStepOrchestrator(
        ILogger<ExecuteSleepStepOrchestrator> logger)
    {
        _logger = logger;
    }

    [Function("ExecuteSleepStep_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var executeSleepStepInput = context.GetInput<ExecuteSleepStepInput>();

        try
        {
            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(executeSleepStepInput.TotalSeconds)),
                CancellationToken.None);

            await context.CallActivityAsync(
                "CompleteStep",
                new CompleteStepInput(
                    executeSleepStepInput.StateId,
                    executeSleepStepInput.StepId,
                    executeSleepStepInput.StackEntries,
                    StepExecutionStatuses.Complete));
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error executing ExecuteSleepStep {ExecuteSleepStep}",
                executeSleepStepInput.StepId);

            await context.CallActivityAsync(
                "CompleteStep",
                new CompleteStepInput(
                    executeSleepStepInput.StateId,
                    executeSleepStepInput.StepId,
                    executeSleepStepInput.StackEntries,
                    StepExecutionStatuses.Failed));
        }
    }
}