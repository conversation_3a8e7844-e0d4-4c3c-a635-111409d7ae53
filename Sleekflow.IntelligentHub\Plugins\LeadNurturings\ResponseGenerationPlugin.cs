using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IResponseGenerationPlugin
{
    [KernelFunction("craft_transition_response")]
    [Description(
        "Crafts transition responses using data from data pane and stores response results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with transition response details.")]
    Task<string> CraftTransitionResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where trigger information is stored in the data pane.")]
        string triggerInfoKey);

    [KernelFunction("craft_information_gathering_response")]
    [Description(
        "Crafts information gathering responses using data from data pane and stores response results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with information gathering response details.")]
    Task<string> CraftInformationGatheringResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where planning information is stored in the data pane.")]
        string planningInfoKey);
}

public class ResponseGenerationPlugin : BaseLeadNurturingPlugin, IResponseGenerationPlugin
{
    public ResponseGenerationPlugin(
        ILogger<ResponseGenerationPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingCollaborationChatCacheService chatCacheService,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel,
            chatCacheService)
    {
    }

    private async Task<string> CraftTransitionResponseAsync(
        Kernel kernel,
        string conversationContext,
        string triggerInfo,
        string responseLanguage = "English")
    {
        var transitioningResponseCrafterAgent = _agentDefinitions.GetTransitioningResponseCrafterAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH_2_5, true),
            responseLanguage,
            _chatCacheService!);

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, triggerInfo);

        return await ExecuteAgentWithTelemetryAsync(
            transitioningResponseCrafterAgent,
            agentThread,
            "ResponseGenerationPlugin-Transition");
    }

    private async Task<string> CraftInformationGatheringResponseAsync(
        Kernel kernel,
        string conversationContext,
        string planningInfo,
        string responseLanguage = "English")
    {
        var informationGatheringResponseCrafterAgent = _agentDefinitions.GetInformationGatheringResponseCrafterAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH_2_5, true),
            responseLanguage,
            _chatCacheService!);

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, planningInfo);

        return await ExecuteAgentWithTelemetryAsync(
            informationGatheringResponseCrafterAgent,
            agentThread,
            "ResponseGenerationPlugin-InfoGathering");
    }

    [KernelFunction("craft_transition_response")]
    [Description(
        "Crafts transition responses using data from data pane and stores response results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with transition response details.")]
    public async Task<string> CraftTransitionResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where trigger information is stored in the data pane.")]
        string triggerInfoKey)
    {
        return await ExecutePluginOperationAsync<
            LeadNurturingAgentDefinitions.TransitioningResponseCrafterAgentResponse>(
            sessionKey,
            "TransitionResponse",
            dataRetrievalFunc: async () => await RetrieveDataWithConfiguration(
                sessionKey,
                conversationContextKey,
                triggerInfoKey,
                AgentOutputKeys.ActionComplete),
            agentExecutionFunc: async (data) => await CraftTransitionResponseAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["triggerInfo"],
                data["responseLanguage"]),
            storageKey: _keyManager.GetResponseKey(sessionKey),
            storageDataType: AgentOutputKeys.TransitionResponseComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreTransitionResponseComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    [KernelFunction("craft_information_gathering_response")]
    [Description(
        "Crafts information gathering responses using data from data pane and stores response results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with information gathering response details.")]
    public async Task<string> CraftInformationGatheringResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where planning information is stored in the data pane.")]
        string planningInfoKey)
    {
        return await ExecutePluginOperationAsync<
            LeadNurturingAgentDefinitions.InformationGatheringResponseCrafterAgentResponse>(
            sessionKey,
            "InformationGatheringResponse",
            dataRetrievalFunc: async () => await RetrieveDataWithConfiguration(
                sessionKey,
                conversationContextKey,
                planningInfoKey,
                AgentOutputKeys.PlanningDemoComplete),
            agentExecutionFunc: async (data) => await CraftInformationGatheringResponseAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["planningInfo"],
                data["responseLanguage"]),
            storageKey: _keyManager.GetResponseKey(sessionKey),
            storageDataType: AgentOutputKeys.InformationGatheringResponseComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreInformationGatheringResponseComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task<Dictionary<string, string>> RetrieveDataWithConfiguration(
        string sessionKey,
        string conversationContextKey,
        string dataKey,
        string dataType)
    {
        var results = await RetrieveMultipleDataAsync(
            (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
            (dataKey, dataType, dataType == AgentOutputKeys.ActionComplete ? "triggerInfo" : "planningInfo")
        );

        // Retrieve configuration
        results["responseLanguage"] = await GetConfigurationAsync(
            sessionKey,
            AgentOutputKeys.ResponseLanguage,
            "English");

        return results;
    }

    private async Task StoreTransitionResponseComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.TransitioningResponseCrafterAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "TransitioningResponseCrafterAgent",
                        AgentOutputKeys.ResponseNatural),
                    AgentOutputKeys.ResponseNatural,
                    parsedResponse.Response);

                _logger.LogInformation(
                    "Transition response crafting completed for session {SessionKey}",
                    sessionKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to store transition response components for session {SessionKey}",
                sessionKey);
        }
    }

    private async Task StoreInformationGatheringResponseComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.InformationGatheringResponseCrafterAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "InformationGatheringResponseCrafterAgent",
                        AgentOutputKeys.ResponseNatural),
                    AgentOutputKeys.ResponseNatural,
                    parsedResponse.Response);

                _logger.LogInformation(
                    "Information gathering response crafting completed for session {SessionKey}",
                    sessionKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to store information gathering response components for session {SessionKey}",
                sessionKey);
        }
    }
}