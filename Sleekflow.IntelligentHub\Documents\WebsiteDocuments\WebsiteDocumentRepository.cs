using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.IntelligentHub.Documents.WebsiteDocuments;

public interface IWebsiteDocumentRepository : IDynamicFiltersRepository<WebsiteDocument>
{
    Task<WebsiteDocument> PatchSelectedUrlsAsync(
        string sleekflowCompanyId,
        string documentId,
        List<SelectedUrl> selectedUrls);
}

public class WebsiteDocumentRepository
    : DynamicFiltersBaseRepository<WebsiteDocument>, IWebsiteDocumentRepository, IScopedService
{
    private readonly ILogger<WebsiteDocumentRepository> _logger;

    public WebsiteDocumentRepository(
        ILogger<WebsiteDocumentRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
        _logger = logger;
    }

    public async Task<WebsiteDocument> PatchSelectedUrlsAsync(
        string sleekflowCompanyId,
        string documentId,
        List<SelectedUrl> selectedUrls)
    {
        return await PatchAndGetAsync(
            documentId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace("selected_urls", selectedUrls.ToArray()),
                Replace($"{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            });
    }
}