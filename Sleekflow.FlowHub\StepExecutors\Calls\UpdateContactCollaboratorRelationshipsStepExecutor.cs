using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactCollaboratorRelationshipsStepExecutor : IStepExecutor
{
}

public class UpdateContactCollaboratorRelationshipsStepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactCollaboratorRelationshipsStepArgs>>,
        IUpdateContactCollaboratorRelationshipsStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactCollaboratorRelationshipsStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactCollaboratorRelationships",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateContactCollaboratorRelationshipsInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [JsonProperty("add_staff_ids")]
        public List<string>? AddStaffIds { get; set; }

        [JsonProperty("remove_staff_ids")]
        public List<string>? RemoveStaffIds { get; set; }

        [JsonProperty("set_staff_ids")]
        public List<string>? SetStaffIds { get; set; }

        [JsonConstructor]
        public UpdateContactCollaboratorRelationshipsInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactId,
            List<string>? addStaffIds,
            List<string>? removeStaffIds,
            List<string>? setStaffIds)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactId = contactId;
            AddStaffIds = addStaffIds;
            RemoveStaffIds = removeStaffIds;
            SetStaffIds = setStaffIds;
        }
    }

    private async Task<UpdateContactCollaboratorRelationshipsInput> GetArgs(
        CallStep<UpdateContactCollaboratorRelationshipsStepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                      ?? callStep.Args.ContactIdExpr);
        var addStaffIds =
            callStep.Args.AddStaffIdsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.AddStaffIdsExpr))!).Cast<string>().ToList();
        var removeStaffIds =
            callStep.Args.RemoveStaffIdsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.RemoveStaffIdsExpr))!).Cast<string>().ToList();
        var setStaffIds =
            callStep.Args.SetStaffIdsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.SetStaffIdsExpr))!).Cast<string>().ToList();

        return new UpdateContactCollaboratorRelationshipsInput(
            state.Id,
            state.Identity,
            contactId,
            addStaffIds,
            removeStaffIds,
            setStaffIds);
    }
}