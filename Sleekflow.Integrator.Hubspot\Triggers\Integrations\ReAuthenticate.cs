using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Authentications;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReAuthenticate : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public ReAuthenticate(IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class ReAuthenticateInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public ReAuthenticateInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class ReAuthenticateOutput
    {
    }

    public async Task<ReAuthenticateOutput> F(
        ReAuthenticateInput reAuthenticateInput)
    {
        await _hubspotAuthenticationService.ReAuthenticateAndStoreAsync(
            reAuthenticateInput.SleekflowCompanyId);

        return new ReAuthenticateOutput();
    }
}