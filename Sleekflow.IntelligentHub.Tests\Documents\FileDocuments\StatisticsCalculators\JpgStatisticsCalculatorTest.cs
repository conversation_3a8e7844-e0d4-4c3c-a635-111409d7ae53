using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.StatisticsCalculators;

[TestFixture]
[TestOf(typeof(JpgStatisticsCalculatorTest))]
public class JpgStatisticsCalculatorTest
{
    // Relative path from the test execution directory (usually bin/Debug/netX.X)
    // Adjust if your build output path is different
    const string JpgFilePath = "../../../Binaries/Untung Kaw Kaw.jpg";

    [Test]
    public void CalculateDocumentStatisticsTest()
    {
        var statisticsCalculator = new JpgStatisticsCalculator(new Mock<IDocumentCounterService>().Object);

        using var fileStream = new FileStream(
            JpgFilePath,
            FileMode.Open,
            FileAccess.Read,
            FileShare.Read); // Use FileShare.Read
        var documentStatistics = statisticsCalculator.CalculateDocumentStatistics(fileStream);

        Assert.That(documentStatistics.TotalPages, Is.EqualTo(1));
    }
}