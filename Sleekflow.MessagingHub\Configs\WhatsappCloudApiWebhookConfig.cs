using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Configs;

public interface IWhatsappCloudApiWebhookConfig
{
    bool IsProduction { get; }

    string? WhatsappCloudApiOverrideWebhookUrl { get; }
}

public class WhatsappCloudApiWebhookConfig : IConfig, IWhatsappCloudApiWebhookConfig
{
    public bool IsProduction { get; private set; }

    public string? WhatsappCloudApiOverrideWebhookUrl { get; }

    public WhatsappCloudApiWebhookConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        IsProduction = (Environment.GetEnvironmentVariable("SF_ENV_NAME", target) ??
                        throw new SfMissingEnvironmentVariableException("SF_ENV_NAME")) == "production";
        if (!IsProduction)
        {
            WhatsappCloudApiOverrideWebhookUrl =
                Environment.GetEnvironmentVariable("WHATSAPP_CLOUD_API_OVERRIDE_WEBHOOK_URL", target)
                ?? throw new SfMissingEnvironmentVariableException("WHATSAPP_CLOUD_API_OVERRIDE_WEBHOOK_URL");
        }
        else
        {
            WhatsappCloudApiOverrideWebhookUrl = null;
        }
    }
}