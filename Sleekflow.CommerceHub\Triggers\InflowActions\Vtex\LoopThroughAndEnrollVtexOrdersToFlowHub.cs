﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.States;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Workers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CommerceHub;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.InflowActions.Vtex;

[TriggerGroup(ControllerNames.InflowActions)]
public class LoopThroughAndEnrollVtexOrdersToFlowHub
    : ITrigger<
            LoopThroughAndEnrollVtexOrdersToFlowHub.LoopThroughAndEnrollVtexOrdersToFlowHubInput,
            LoopThroughAndEnrollVtexOrdersToFlowHub.LoopThroughAndEnrollVtexOrdersToFlowHubOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;
    private readonly ILoopThroughObjectsService _loopThroughObjectsService;
    private readonly ICommerceHubWorkerService _commerceHubWorkerService;

    public LoopThroughAndEnrollVtexOrdersToFlowHub(
        IVtexAuthenticationService vtexAuthenticationService,
        ILoopThroughObjectsService loopThroughObjectsService,
        ICommerceHubWorkerService commerceHubWorkerService)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
        _loopThroughObjectsService = loopThroughObjectsService;
        _commerceHubWorkerService = commerceHubWorkerService;
    }

    public class LoopThroughAndEnrollVtexOrdersToFlowHubInput
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonProperty("vtex_authentication_id")]
        [Required]
        public string VtexAuthenticationId { get; set; }

        [JsonProperty("condition")]
        public VtexGetOrdersSearchCondition Condition { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollVtexOrdersToFlowHubInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId,
            string vtexAuthenticationId,
            VtexGetOrdersSearchCondition condition)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
            VtexAuthenticationId = vtexAuthenticationId;
            Condition = condition;
        }
    }

    public class LoopThroughAndEnrollVtexOrdersToFlowHubOutput
    {
        [JsonProperty("loop_through_objects_progress_state_id")]
        public string LoopThroughObjectsProgressStateId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollVtexOrdersToFlowHubOutput(string loopThroughObjectsProgressStateId)
        {
            LoopThroughObjectsProgressStateId = loopThroughObjectsProgressStateId;
        }
    }

    public async Task<LoopThroughAndEnrollVtexOrdersToFlowHubOutput> F(
        LoopThroughAndEnrollVtexOrdersToFlowHubInput input)
    {
        var vtexAuthentication =
            await _vtexAuthenticationService.GetAsync(
                input.VtexAuthenticationId,
                input.SleekflowCompanyId);

        if (vtexAuthentication == null)
        {
            throw new SfVtexInvalidCredentialException();
        }

        var condition = PreprocessCondition(input.Condition);

        await _loopThroughObjectsService.TerminateInProgressLoopThroughExecutionsAsync(
            ProviderNames.Vtex,
            input.FlowHubWorkflowId,
            input.SleekflowCompanyId);

        var output = await _loopThroughObjectsService.LoopThroughAndEnrollObjectsToFlowHubAsync(
            ProviderNames.Vtex,
            input.SleekflowCompanyId,
            input.FlowHubWorkflowId,
            input.FlowHubWorkflowVersionedId,
            () => _commerceHubWorkerService.StartLoopThroughAndEnrollVtexOrdersToFlowHubAsync(
                input.SleekflowCompanyId,
                input.FlowHubWorkflowId,
                input.FlowHubWorkflowVersionedId,
                input.VtexAuthenticationId,
                condition));

        return new LoopThroughAndEnrollVtexOrdersToFlowHubOutput(output.LoopThroughObjectsProgressStateId);
    }

    private static VtexGetOrdersSearchCondition PreprocessCondition(VtexGetOrdersSearchCondition? condition)
    {
        var utcNow = DateTimeOffset.UtcNow;
        var utcDecadeAgo = utcNow.AddYears(-10);

        var statusCode = condition?.OrderStatusCode is null || !VtexOrderStatusCodes.All.Contains(condition.OrderStatusCode)
            ? string.Empty
            : condition.OrderStatusCode;

        return new VtexGetOrdersSearchCondition(
            condition?.CreatedAtFrom ?? utcDecadeAgo,
            condition?.CreatedAtTo ?? utcNow,
            statusCode);
    }
}