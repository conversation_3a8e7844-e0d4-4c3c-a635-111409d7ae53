using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiConversationUsageExceptionEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiConversationUsageExceptionEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiConversationUsageExceptionEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiConversationUsageExceptionEventConsumer : IConsumer<OnCloudApiConversationUsageExceptionEvent>
{
    private readonly ILockService _lockService;
    private readonly IAuditLogService _auditLogService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiConversationUsageExceptionEventConsumer> _logger;

    public OnCloudApiConversationUsageExceptionEventConsumer(
        ILockService lockService,
        IAuditLogService auditLogService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiConversationUsageExceptionEventConsumer> logger)
    {
        _logger = logger;
        _lockService = lockService;
        _auditLogService = auditLogService;
        _businessBalanceService = businessBalanceService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiConversationUsageExceptionEvent> context)
    {
        var onCloudApiConversationUsageExceptionEvent = context.Message;
        var businessBalanceId = onCloudApiConversationUsageExceptionEvent.BusinessBalanceId;
        var facebookBusinessId = onCloudApiConversationUsageExceptionEvent
            .FacebookBusinessId;

        var wabaConversationInsertionExceptions =
            onCloudApiConversationUsageExceptionEvent.WabaConversationInsertionExceptions;
        var cancellationToken = context.CancellationToken;

        var retryCount = context.GetRedeliveryCount();
        if (context.GetRedeliveryCount() > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiConversationUsageExceptionEvent}",
                JsonConvert.SerializeObject(onCloudApiConversationUsageExceptionEvent));
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId
            },
            TimeSpan.FromSeconds(
                60 *
                (OnCloudApiConversationUsageExceptionEventConsumerDefinition.LockDuration +
                 OnCloudApiConversationUsageExceptionEventConsumerDefinition.MaxAutoRenewDuration)),
            cancellationToken);
        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));
            return;
        }

        var businessBalance =
            await _businessBalanceService.GetOrDefaultBusinessBalanceAsync(businessBalanceId, facebookBusinessId);

        if (businessBalance is null)
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
            return;
        }

        var beforeUpsertLastConversationUsageInsertState =
            JsonConvert.DeserializeObject<BusinessBalance>(
                JsonConvert.SerializeObject(businessBalance));

        businessBalance.ConversationUsageInsertState
            .WabaConversationInsertionExceptions = wabaConversationInsertionExceptions;
        businessBalance.ConversationUsageInsertState.UpdatedAt = DateTimeOffset.UtcNow;

        var upsertBusinessBalanceStatus = await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);

        if (upsertBusinessBalanceStatus == 0)
        {
            _logger.LogError(
                "Unable to upsert business balance record with {BusinessBalance}",
                JsonConvert.SerializeObject(businessBalance));
        }

        var afterUpsertLastConversationUsageInsertState =
            await _businessBalanceService.GetOrDefaultBusinessBalanceAsync(
                businessBalance.Id,
                businessBalance.FacebookBusinessId);

        await _auditLogService.AuditBusinessBalanceAsync(
            beforeUpsertLastConversationUsageInsertState,
            businessBalance.FacebookBusinessId,
            AuditingOperation.OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent,
            new Dictionary<string, object?>
            {
                {
                    "changes", afterUpsertLastConversationUsageInsertState
                }
            });

        await _lockService.ReleaseAsync(@lock, cancellationToken);
    }
}