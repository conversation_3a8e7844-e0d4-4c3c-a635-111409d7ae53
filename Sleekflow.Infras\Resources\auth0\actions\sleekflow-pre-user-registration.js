/**
 * Handler that will be called during the execution of a PreUserRegistration flow.
 *
 * @param {Event} event - Details about the context and user that is attempting to register.
 * @param {PreUserRegistrationAPI} api - Interface whose methods can be used to change the behavior of the signup.
 */
exports.onExecutePreUserRegistration = async (event, api) => {
  const domain = event.secrets.domain;
  const clientId = event.secrets.client_id;
  const clientSecret = event.secrets.client_secret;
  const preUserRegistrationWebhookUrl = event.secrets.pre_user_registration_webhook;
  const issuer = event.secrets.issuer;
  const audience = event.secrets.audience;

  const axios = require("axios").default;

  const sendPreUserRegistrationWebhook = async (user) => {
    const jwt = require('jwt-encode');

    const data = {
      iss: issuer,
      aud: audience,
      domain: domain,
      client_id: clientId
    }

    const tokenSecret = issuer + audience + "+wsadz4gI_3DUXI8P";
    const token = jwt(data, tokenSecret);

    const options = {
      method: 'POST',
      url: preUserRegistrationWebhookUrl,
      params: {connection: event.connection.strategy, token: token},
      headers: {'content-type': 'application/json'},
      data: {
        auth0_event_user: user,
        auth0_client_id: event.client.client_id
      }
    };

    const response = await axios.request(options);

    return response;
  };


  const res = await sendPreUserRegistrationWebhook(event.user);
  if (res.status !== 200) {
    console.error(res)
    api.access.deny('Internal Server Error.', 'Internal Server Error.')
  }

  // public static class PreUserRegistrationResponseStatus
  // {
  //   public const string ErrorConflictUsername = "error_conflict_username";
  //   public const string ErrorConflictEmail = "error_conflict_email";
  //   public const string Good = "good";
  //
  //   public static readonly string[] AllStatuses =
  //   {
  //     ErrorConflictUsername,
  //     ErrorConflictEmail,
  //     Good,
  //   };
  // }

  if (res.data.status === "error_conflict_username") {
    api.access.deny('Username already exists.', 'Username already exists.')
  } else if (res.data.status === "error_conflict_email") {
    api.access.deny('Email already exists.', 'Email already exists.')
  } else if (res.data.status === "good") {
    api.user.setAppMetadata("sleekflow_id", res.data.app_metadata.sleekflow_id);
    api.user.setAppMetadata("roles", res.data.app_metadata.roles);
    api.user.setAppMetadata("phone_number", res.data.app_metadata.phone_number);
    api.user.setAppMetadata("login_requires_email_verification", res.data.app_metadata.login_requires_email_verification);
  } else {
    api.access.deny('Internal Server Error.', 'Internal Server Error.')
  }
};
