using Pulumi;
using Pulumi.AzureNative.App.V20230401Preview;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;

namespace Sleekflow.Infras.Components.WebhookBridge;

public class WebhookBridge
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;
    private readonly string _location;
    private readonly ManagedEnvironment _managedEnvironment;
    private readonly Db.DbOutput _dbOutput;
    private readonly Cache.Redis _redis;
    private readonly MyServiceBus.ServiceBusOutput _serviceBus;
    private readonly MyEventHub.EventHubOutput _eventHub;
    private readonly MassTransitBlobStorage.MassTransitBlobStorageOutput _massTransitBlobStorageOutput;


    public WebhookBridge(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        MyConfig myConfig,
        string location,
        ManagedEnvironment managedEnvironment,
        Db.DbOutput dbOutput,
        Cache.Redis redis,
        MyServiceBus.ServiceBusOutput serviceBus,
        MyEventHub.EventHubOutput eventHub,
        MassTransitBlobStorage.MassTransitBlobStorageOutput massTransitBlobStorageOutput)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
        _location = location;
        _managedEnvironment = managedEnvironment;
        _dbOutput = dbOutput;
        _redis = redis;
        _serviceBus = serviceBus;
        _eventHub = eventHub;
        _massTransitBlobStorageOutput = massTransitBlobStorageOutput;
    }

    public App.ContainerApp InitWebhookBridge()
    {
        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.WebhookBridge),
            _myConfig.BuildTime);

        var listRedisKeysOutput = Output
            .Tuple(_resourceGroup.Name, _redis.Name, _redis.Id)
            .Apply(t => Cache.ListRedisKeys.InvokeAsync(
                new Cache.ListRedisKeysArgs
                {
                    ResourceGroupName = t.Item1, Name = t.Item2
                }));

        var containerAppName = FormatContainerAppName(
            ServiceNames.GetShortName(ServiceNames.WebhookBridge),
            LocationNames.GetShortName(_location));

        var containerApp = new App.ContainerApp(
            containerAppName,
            new App.ContainerAppArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ManagedEnvironmentId = _managedEnvironment.Id,
                ContainerAppName = containerAppName,
                Location = LocationNames.GetAzureLocation(_location),
                Configuration = new App.Inputs.ConfigurationArgs
                {
                    ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    Ingress = new App.Inputs.IngressArgs
                    {
                        External = false,
                        TargetPort = 80,
                        Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                        {
                            new App.Inputs.TrafficWeightArgs
                            {
                                LatestRevision = true, Weight = 100
                            }
                        },
                        Transport = App.IngressTransportMethod.Auto
                    },
                    Registries =
                    {
                        new App.Inputs.RegistryCredentialsArgs
                        {
                            Server = _registry.LoginServer,
                            Username = _registryUsername,
                            PasswordSecretRef = "registry-password-secret",
                        }
                    },
                    Secrets =
                    {
                        new App.Inputs.SecretArgs
                        {
                            Name = "registry-password-secret", Value = _registryPassword
                        },
                        new App.Inputs.SecretArgs
                        {
                            Name = "service-bus-conn-str-secret", Value = _serviceBus.CrmHubPolicyKeyPrimaryConnStr
                        },
                        new App.Inputs.SecretArgs
                        {
                            Name = "service-bus-keda-conn-str-secret",
                            Value = _serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                        },
                        new App.Inputs.SecretArgs
                        {
                            Name = "event-hub-conn-str-secret", Value = _eventHub.NamespacePrimaryConnStr
                        },
                    },
                },
                Template = new App.Inputs.TemplateArgs
                {
                    TerminationGracePeriodSeconds = 5 * 60,
                    Scale = new App.Inputs.ScaleArgs
                    {
                        MinReplicas = _myConfig.Name.ToLower() == "production" ? 3 : 1, MaxReplicas = 20,
                    },
                    Containers =
                    {
                        new App.Inputs.ContainerArgs
                        {
                            Name = "sleekflow-whb-app",
                            Image = myImage.BaseImageName,
                            Resources = new App.Inputs.ContainerResourcesArgs
                            {
                                Cpu = 0.5, Memory = "1Gi"
                            },
                            Probes = new List<App.Inputs.ContainerAppProbeArgs>
                            {
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "liveness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 8,
                                    PeriodSeconds = 2,
                                },
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "readiness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 8,
                                    PeriodSeconds = 2,
                                },
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "startup",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 12,
                                    TimeoutSeconds = 8,
                                }
                            },
                            Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                new List<App.Inputs.EnvironmentVarArgs>
                                {
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "CACHE_PREFIX", Value = "Sleekflow.WebhookBridge",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_ENDPOINT",
                                        Value = Output.Format(
                                            $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "REDIS_CONN_STR",
                                        Value = Output
                                            .Tuple(listRedisKeysOutput, _redis.HostName)
                                            .Apply(t =>
                                                $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                    },

                                    #region ServiceBusConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                    },

                                    #endregion

                                    #region EventHubConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                    },

                                    #endregion

                                    #region MassTransitStorageConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "MESSAGE_DATA_CONN_STR",
                                        Value = _massTransitBlobStorageOutput.StorageAccountConnStr
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "MESSAGE_DATA_CONTAINER_NAME",
                                        Value = _massTransitBlobStorageOutput.ContainerName
                                    },

                                    #endregion
                                })
                        }
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _managedEnvironment
            });

        return containerApp;
    }

    private string FormatContainerAppName(string appName, string locationName)
    {
        var containerAppName = $"sleekflow-{appName}-app-{locationName}";
        return containerAppName;
    }
}