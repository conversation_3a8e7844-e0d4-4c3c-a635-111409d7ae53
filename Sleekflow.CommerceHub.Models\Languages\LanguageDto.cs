using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Languages;

public class LanguageDto
{
    [JsonProperty(Language.PropertyNameLanguageIsoCode)]
    public string LanguageIsoCode { get; set; }

    [JsonProperty(Language.PropertyNameLanguageName)]
    public string LanguageName { get; set; }

    [JsonProperty(Language.PropertyNameNativeLanguageName)]
    public string NativeLanguageName { get; set; }

    [JsonConstructor]
    public LanguageDto(
        string languageIsoCode,
        string languageName,
        string nativeLanguageName)
    {
        LanguageIsoCode = languageIsoCode;
        LanguageName = languageName;
        NativeLanguageName = nativeLanguageName;
    }
}