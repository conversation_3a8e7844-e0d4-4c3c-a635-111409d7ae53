using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Workflows;

public interface IProxyGrainWorkflowService
{
    /// <summary>
    /// Gets workflow contact data from the grain associated with the provided ProxyWorkflow.
    /// </summary>
    /// <param name="proxyWorkflow">The base workflow information.</param>
    /// <returns>A ProxyGrainWorkflow containing grain data.</returns>
    ProxyGrainWorkflow GetProxyGrainWorkflow(ProxyWorkflow proxyWorkflow);
}

public class ProxyGrainWorkflowService : IProxyGrainWorkflowService, IScopedService
{
    private readonly IGrainFactory _grainFactory;

    public ProxyGrainWorkflowService(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    public ProxyGrainWorkflow GetProxyGrainWorkflow(ProxyWorkflow proxyWorkflow)
    {
        var workflowContactsDict =
            _grainFactory.GetGrain<IWorkflowGrain>(proxyWorkflow.WorkflowVersionedId + "_contacts");

        return new ProxyGrainWorkflow(proxyWorkflow, new AsyncDictionaryWrapper<string, object?>(workflowContactsDict));
    }
}