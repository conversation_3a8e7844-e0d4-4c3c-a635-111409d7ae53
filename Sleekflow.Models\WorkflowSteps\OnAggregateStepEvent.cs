using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class OnAggregateStepEvent
{
    [JsonProperty("aggregate_step_id")]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [JsonProperty("duration")]
    public int Duration { get; set; }

    [JsonProperty("stack_entries")]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public OnAggregateStepEvent(
        string aggregateStepId,
        string proxyStateId,
        string contactId,
        int duration,
        Stack<StackEntry> stackEntries)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        ContactId = contactId;
        Duration = duration;
        StackEntries = stackEntries;
    }

    public Guid GetCorrelationId()
    {
        // we need an id that is unique to the aggregate step and the contact
        var id = $"{AggregateStepId}:{ContactId}";
        var md5 = MD5.Create();
        var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(id));
        return new Guid(hash);
    }
}