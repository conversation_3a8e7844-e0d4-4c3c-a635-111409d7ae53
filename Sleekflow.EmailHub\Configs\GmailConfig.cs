using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Configs;

public interface IGmailConfig
{
    string ClientId { get; }

    string ClientSecret { get; }

    string RedirectUri { get; }

    string ProjectId { get; }

    string TopicId { get; }

    string SubscriptionId { get; }
}

public class GmailConfig : IGmailConfig, IConfig
{
    public string ClientId { get; }

    public string ClientSecret { get; }

    public string RedirectUri { get; }

    public string ProjectId { get; }

    public string TopicId { get; }

    public string SubscriptionId { get; }

    public GmailConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;
        ClientId =
            Environment.GetEnvironmentVariable("GMAIL_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("GMAIL_CLIENT_ID");
        ClientSecret =
            Environment.GetEnvironmentVariable("GMAIL_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("GMAIL_CLIENT_SECRET");
        RedirectUri =
            Environment.GetEnvironmentVariable("GMAIL_REDIRECT_URI", target)
            ?? throw new SfMissingEnvironmentVariableException("GMAIL_REDIRECT_URI");
        ProjectId =
            Environment.GetEnvironmentVariable("GMAIL_PROJECT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("GMAIL_PROJECT_ID");
        TopicId =
            Environment.GetEnvironmentVariable("GMAIL_TOPIC_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("GMAIL_TOPIC_ID");
        SubscriptionId =
            Environment.GetEnvironmentVariable("GMAIL_SUBSCRIPTION_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("GMAIL_SUBSCRIPTION_ID");
    }
}