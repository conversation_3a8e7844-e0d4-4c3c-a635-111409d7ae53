﻿using Sleekflow.CrmHub.Models.ProviderConfigs;

namespace Sleekflow.CrmHub.Workers.ProviderConfigs;

public interface IProviderConfigService
{
    Task<ProviderConfig?> GetProviderConfigOrDefaultAsync(string sleekflowCompanyId, string providerName);
}

public class ProviderConfigService : IProviderConfigService
{
    private readonly IProviderConfigRepository _providerConfigRepository;

    public ProviderConfigService(
        IProviderConfigRepository providerConfigRepository)
    {
        _providerConfigRepository = providerConfigRepository;
    }

    public async Task<ProviderConfig?> GetProviderConfigOrDefaultAsync(string sleekflowCompanyId, string providerName)
    {
        var providerConfigs = await _providerConfigRepository.GetObjectsAsync(
            pc =>
                pc.SleekflowCompanyId == sleekflowCompanyId
                && pc.ProviderName == providerName);
        if (providerConfigs.Count == 0)
        {
            return null;
        }

        return providerConfigs[0];
    }
}