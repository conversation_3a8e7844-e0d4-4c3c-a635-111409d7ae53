﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Services.Models;

public class Next
{
    [JsonProperty("after")]
    public string After { get; set; }

    [JsonProperty("link")]
    public string Link { get; set; }

    [JsonConstructor]
    public Next(string after, string link)
    {
        After = after;
        Link = link;
    }
}

public class Paging
{
    [JsonProperty("next")]
    public Next? Next { get; set; }

    [JsonConstructor]
    public Paging(Next? next)
    {
        Next = next;
    }
}

public class HubspotListResult
{
    [JsonProperty("properties")]
    public Dictionary<string, object?> Properties { get; set; }

    [JsonConstructor]
    public HubspotListResult(Dictionary<string, object?> properties)
    {
        Properties = properties;
    }
}

public class HubspotListOutput
{
    [JsonProperty("total")]
    public int Total { get; set; }

    [JsonProperty("results")]
    public List<HubspotListResult> Results { get; set; }

    [JsonProperty("paging")]
    public Paging? Paging { get; set; }

    [JsonConstructor]
    public HubspotListOutput(int total, List<HubspotListResult> results, Paging? paging)
    {
        Total = total;
        Results = results;
        Paging = paging;
    }
}