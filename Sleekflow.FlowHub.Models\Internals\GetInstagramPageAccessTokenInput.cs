using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetInstagramPageAccessTokenInput
{
    [JsonProperty("sleekflow_company_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("instagram_page_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string InstagramPageId { get; set; }

    [JsonConstructor]
    public GetInstagramPageAccessTokenInput(string sleekflowCompanyId, string instagramPageId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        InstagramPageId = instagramPageId;
    }
}