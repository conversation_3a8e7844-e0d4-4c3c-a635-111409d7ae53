using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Evaluations;

[SwaggerInclude]
public class EvaluationContext
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("object_id")]
    public string? ObjectId { get; set; }

    [JsonProperty("workflow_id")]
    public string? WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string? WorkflowVersionedId { get; set; }

    [JsonProperty("object_type")]
    public string? ObjectType { get; set; }

    [JsonProperty("origin")]
    public string? Origin { get; set; }

    [JsonConstructor]
    public EvaluationContext(
        string sleekflowCompanyId,
        string? workflowId,
        string? workflowVersionedId,
        string? objectId,
        string? objectType,
        string? origin)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        ObjectType = objectType;
        ObjectId = objectId;
        Origin = origin;
    }

    public EvaluationContext(StateIdentity stateIdentity, string? origin = null)
        : this(
            stateIdentity.SleekflowCompanyId,
            stateIdentity.WorkflowId,
            stateIdentity.WorkflowVersionedId,
            stateIdentity.ObjectId,
            stateIdentity.ObjectType,
            origin)
    {
    }
}