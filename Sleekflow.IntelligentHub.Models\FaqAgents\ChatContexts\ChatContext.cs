using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;
using Entity = Sleekflow.Persistence.Entity;

namespace Sleekflow.IntelligentHub.Models.FaqAgents.ChatContexts;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId("intelligenthubdb")]
[ContainerId("message_processing_checkpoint")]
public class ChatContext : Entity
{
    public const string SysTypeNameValue = "MessageProcessingCheckpoint";

    [JsonProperty(PropertyName = "sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyName = "context_id")]
    public string ContextId { get; set; }

    [JsonProperty(PropertyName = "context_start_time")]
    public DateTimeOffset ContextStartTime { get; set; }

    [JsonProperty(PropertyName = "history_count")]
    public int HistoryCount { get; set; }

    [JsonConstructor]
    public ChatContext(
        string id,
        string sleekflowCompanyId,
        string contextId,
        DateTimeOffset contextStartTime,
        int historyCount)
        : base(id, SysTypeNameValue)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ContextId = contextId;
        ContextStartTime = contextStartTime;
        HistoryCount = historyCount;
    }
}