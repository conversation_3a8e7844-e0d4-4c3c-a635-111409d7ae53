using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;

public class FacebookProductCatalogDto
{
    [JsonProperty("facebook_product_catalog_id")]
    public string FacebookProductCatalogId { get; set; }

    [JsonProperty("facebook_product_catalog_name")]
    public string FacebookProductCatalogName { get; set; }

    [JsonProperty("default_image_url")]
    public string? DefaultImageUrl { get; set; }

    [JsonProperty("product_count")]
    public int? ProductCount { get; set; }

    [JsonProperty("vertical")] // the type of catalog (for example: hotels, commerce, etc)
    public string? Vertical { get; set; }

    [JsonConstructor]
    public FacebookProductCatalogDto(
        string facebookProductCatalogId,
        string facebookProductCatalogName,
        string? defaultImageUrl,
        int? productCount,
        string? vertical)
    {
        FacebookProductCatalogId = facebookProductCatalogId;
        FacebookProductCatalogName = facebookProductCatalogName;
        DefaultImageUrl = defaultImageUrl;
        ProductCount = productCount;
        Vertical = vertical;
    }

    public FacebookProductCatalogDto(WabaProductCatalog wabaProductCatalog)
        : this(
            wabaProductCatalog.FacebookProductCatalogId,
            wabaProductCatalog.FacebookProductCatalogName,
            wabaProductCatalog.DefaultImageUrl,
            wabaProductCatalog.ProductCount,
            wabaProductCatalog.Vertical)
    {
    }
}