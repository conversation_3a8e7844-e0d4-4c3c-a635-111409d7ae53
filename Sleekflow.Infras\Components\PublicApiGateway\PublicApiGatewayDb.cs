using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.PublicApiGateway;

public class PublicApiGatewayDb
{
    private readonly MyConfig _myConfig;
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;

    public PublicApiGatewayDb(
        MyConfig myConfig,
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount)
    {
        _myConfig = myConfig;
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
    }

    public class PublicApiGatewayDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public PublicApiGatewayDbOutput(Output<string> accountName, Output<string> accountKey, string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public PublicApiGatewayDbOutput InitPublicApiGatewayDb()
    {
        const string cosmosDbId = "publicapigatewaydb";

        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 1000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var containerParams = new ContainerParam[]
        {
            new (
                "api_key_config",
                "api_key_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "sleekflow_company",
                "sleekflow_company",
                new List<string>()
                {
                    "/id"
                }
            ),
            new (
                "sleekflow_company_user",
                "sleekflow_company_user",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "subscription",
                "subscription",
                new List<string>()
                {
                    "/sleekflow_company_id"
                }
            )
        };

        var _ = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new PublicApiGatewayDbOutput(cosmosDbAccountName, cosmosDbAccountKey, cosmosDbId);
    }
}