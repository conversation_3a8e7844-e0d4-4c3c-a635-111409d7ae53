using Google.Apis.Gmail.v1.Data;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.Gmail.Communications;

public class GmailEmailMetadata : EmailMetadata
{
    [JsonProperty("id")]
    public string MessageId { get; set; }

    [JsonProperty("threadId")]
    public string ThreadId { get; set; }

    [JsonProperty("labelIds")]
    public IEnumerable<string> LabelIds { get; set; }

    [JsonProperty("snippet")]
    public string Snippet { get; set; }

    [JsonProperty("payload")]
    public MessagePart Payload { get; set; }

    [JsonProperty("sizeEstimate")]
    public long? SizeEstimate { get; set; }

    [JsonProperty("historyId")]
    public ulong? HistoryId { get; set; }

    [JsonProperty("raw")]
    public string? Raw { get; set; }

    [JsonConstructor]
    public GmailEmailMetadata(
        string messageId,
        string threadId,
        IEnumerable<string> labelIds,
        string snippet,
        MessagePart payload,
        long? sizeEstimate,
        ulong? historyId,
        long? internalDate,
        string? raw)
        : base(
            ProviderNames.Gmail,
            ProviderNames.Gmail,
            DateTimeOffset.FromUnixTimeMilliseconds(internalDate ?? DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()))
    {
        MessageId = messageId;
        ThreadId = threadId;
        LabelIds = labelIds;
        Snippet = snippet;
        Payload = payload;
        SizeEstimate = sizeEstimate;
        HistoryId = historyId;
        Raw = raw;
    }
}