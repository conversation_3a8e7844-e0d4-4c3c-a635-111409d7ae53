using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class UpdatedAtAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1006";
    public const string Category = "Design";

    private static readonly LocalizableString Title = "Class should implement IHasUpdatedAt";

    private static readonly LocalizableString MessageFormat =
        "Class '{0}' has a '{1}' property but does not implement the corresponding interface";

    private static readonly LocalizableString Description =
        "If a class has a UpdatedAt property, it should implement the IHasUpdatedAt interface.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSymbolAction(AnalyzeSymbol, SymbolKind.NamedType);
    }

    private static void AnalyzeSymbol(SymbolAnalysisContext context)
    {
        var namedTypeSymbol = (INamedTypeSymbol) context.Symbol;

        // Check if the class has a UpdatedAt property
        var updatedAtProperty = namedTypeSymbol.GetMembers()
            .FirstOrDefault(m => m.Kind == SymbolKind.Property && m.Name == "UpdatedAt");
        if (updatedAtProperty == null)
        {
            return;
        }

        // Check if the class already implements IHasUpdatedAt
        var iHasUpdatedAtInterface = namedTypeSymbol.Interfaces.FirstOrDefault(
            i => i.ToDisplayString() == "Sleekflow.Persistence.Abstractions.IHasUpdatedAt");
        if (iHasUpdatedAtInterface != null)
        {
            return;
        }

        // Create a diagnostic and report it
        var updatedAtDiagnostic = Diagnostic.Create(
            Rule,
            namedTypeSymbol.Locations[0],
            namedTypeSymbol.Name,
            "UpdatedAt");
        context.ReportDiagnostic(updatedAtDiagnostic);
    }
}