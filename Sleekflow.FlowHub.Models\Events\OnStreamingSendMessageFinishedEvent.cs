using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Models.Events;

public class OnStreamingSendMessageFinishedEvent
{
    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("recommend_reply_step_id")]
    public string RecommendReplyStepId { get; set; }

    [JsonProperty("full_recommended_reply")]
    public string FullRecommendedReply { get; set; }

    [JsonProperty("streaming_send_message_subscriptions")]
    public List<StreamingSendMessageSubscription> StreamingSendMessageSubscriptions { get; set; }

    [JsonConstructor]
    public OnStreamingSendMessageFinishedEvent(
        string proxyStateId,
        string recommendReplyStepId,
        string fullRecommendedReply,
        List<StreamingSendMessageSubscription> streamingSendMessageSubscriptions)
    {
        ProxyStateId = proxyStateId;
        RecommendReplyStepId = recommendReplyStepId;
        FullRecommendedReply = fullRecommendedReply;
        StreamingSendMessageSubscriptions = streamingSendMessageSubscriptions;
    }
}