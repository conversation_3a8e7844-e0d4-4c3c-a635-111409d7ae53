using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Triggers.Categories;
using Sleekflow.CommerceHub.Triggers.Products;
using Sleekflow.CommerceHub.Triggers.Stores;
using Sleekflow.Outputs;

namespace Sleekflow.CommerceHub.Tests;

public static class Mocks
{
    public const string SleekflowCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
    public const string SleekflowStaffId = "TestStaffId";
    public const string SleekflowUserProfileId = "TestUserProfileId";

    public static async Task<Output<CreateStore.CreateStoreOutput>?> CreateTestStoreAsync()
    {
        var createStoreInput = new CreateStore.CreateStoreInput(
            new List<Multilingual>
            {
                new ("en", "Test Store"), new ("zh-Hant", "測試店"),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test store."),
                    null,
                    null),
                new (
                    DescriptionTypes.Text,
                    new Multilingual("zh-Hant", "這是一個測試商店。"),
                    null,
                    null)
            },
            true,
            true,
            new List<LanguageInputDto>
            {
                new ("en", true), new ("zh-Hant", false)
            },
            new List<CurrencyInputDto>
            {
                new ("GBP"), new ("HKD"), new ("USD")
            },
            new StoreTemplateDict(
                new List<Multilingual>()
                {
                    new Multilingual(
                        "en",
                        """
Id {{ product_variant.id }}

Store Id {{ product_variant.store.id }}
Store Url {{ product_variant.store.url }}
Store Name {{ product_variant.store.name }}
Store Description {% for description in product_variant.store.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Store Languages {% for language in product_variant.store.languages -%} {{ language.language_iso_code }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}

Product Id {{ product_variant.product.id }}
Product Categories {% for category in product_variant.product.categories -%} {{ category.name }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Sku {{ product_variant.product.sku }}
Product Name {{ product_variant.product.name }}
Product Description {% for description in product_variant.product.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Images {% for image in product_variant.product.images -%} {{ image.image_url }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}

Product Variant Sku {{ product_variant.sku }}
Product Variant Prices {% for price in product_variant.prices -%} {{ price.currency_iso_code }} {{ price.amount }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Variant Name {{ product_variant.name }}
Product Variant Descriptions {% for description in product_variant.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Images {% for image in product_variant.images -%} {{ image.image_url }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
"""),
                }),
            new Dictionary<string, object?>(),
            null,
            null,
            SleekflowCompanyId,
            "MyStaffId",
            null);
        var createStoreScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _
                    .Post
                    .Json(createStoreInput)
                    .ToUrl("/Stores/CreateStore");
            });
        var createStoreOutputOutput =
            await createStoreScenarioResult.ReadAsJsonAsync<Output<CreateStore.CreateStoreOutput>>();

        if (createStoreOutputOutput?.Data.Store == null)
        {
            throw new Exception("Could not create store.");
        }

        return createStoreOutputOutput;
    }

    public static async Task DeleteTestStoreAsync(
        CreateStore.CreateStoreOutput createStoreOutput)
    {
        var deleteStoreInput = new DeleteStore.DeleteStoreInput(
            createStoreOutput!.Store.Id,
            Mocks.SleekflowCompanyId,
            "MyStaffId",
            null);
        await Application.Host.Scenario(
            _ =>
            {
                _
                    .Post
                    .Json(deleteStoreInput)
                    .ToUrl("/Stores/DeleteStore");
            });
    }

    public static async Task<Output<CreateCategory.CreateCategoryOutput>?> CreateTestCategoryAsync(
        CreateStore.CreateStoreOutput createStoreOutput)
    {
        var createCategoryInput = new CreateCategory.CreateCategoryInput(
            SleekflowCompanyId,
            createStoreOutput.Store.Id,
            new List<Multilingual>
            {
                new ("en", "Test Category"),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test category."),
                    null,
                    null)
            },
            null,
            new Dictionary<string, object?>(),
            SleekflowStaffId,
            null);
        var createCategoryScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _
                    .Post
                    .Json(createCategoryInput)
                    .ToUrl("/Categories/CreateCategory");
            });
        var createCategoryOutputOutput =
            await createCategoryScenarioResult.ReadAsJsonAsync<Output<CreateCategory.CreateCategoryOutput>>();

        if (createCategoryOutputOutput?.Data.Category == null)
        {
            throw new Exception("Could not create category.");
        }

        return createCategoryOutputOutput;
    }

    public static async Task DeleteTestCategoryAsync(
        CreateStore.CreateStoreOutput createStoreOutput,
        CreateCategory.CreateCategoryOutput createCategoryOutput)
    {
        var deleteCategoryInput = new DeleteCategory.DeleteCategoryInput(
            createCategoryOutput!.Category.Id,
            Mocks.SleekflowCompanyId,
            createStoreOutput!.Store.Id,
            "MyStaffId",
            null);
        await Application.Host.Scenario(
            _ =>
            {
                _
                    .Post
                    .Json(deleteCategoryInput)
                    .ToUrl("/Categories/DeleteCategory");
            });
    }

    public static async Task<Output<CreateProduct.CreateProductOutput>> CreateTestProductAsync(
        CreateStore.CreateStoreOutput createStoreOutput,
        CreateCategory.CreateCategoryOutput createCategoryOutput)
    {
        var createProductInput = new CreateProduct.CreateProductInput(
            new List<string>
            {
                createCategoryOutput!.Category.Id,
            },
            null,
            null,
            new List<Multilingual>
            {
                new ("en", "Test Product"),
            },
            new List<Description>
            {
                new (
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a test product."),
                    null,
                    null)
            },
            new List<ImageDto>(),
            new Dictionary<string, object?>(),
            Mocks.SleekflowCompanyId,
            createStoreOutput.Store.Id,
            new List<CreateProduct.CreateProductInputProductVariant>
            {
                new CreateProduct.CreateProductInputProductVariant(
                    null,
                    null,
                    new List<Price>()
                    {
                        new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
                    },
                    0,
                    new List<ProductVariant.ProductVariantAttribute>
                    {
                        new ProductVariant.ProductVariantAttribute("color", "red")
                    },
                    new List<Multilingual>
                    {
                        new Multilingual("en", "Test Product - Red")
                    },
                    new List<Description>
                    {
                        new (
                            DescriptionTypes.Text,
                            new Multilingual("en", "This is a test product - red version."),
                            null,
                            null)
                    },
                    new List<ImageDto>(),
                    null),
                new CreateProduct.CreateProductInputProductVariant(
                    null,
                    null,
                    new List<Price>()
                    {
                        new Price("HKD", 100), new Price("GBP", 13), new Price("USD", 13),
                    },
                    0,
                    new List<ProductVariant.ProductVariantAttribute>
                    {
                        new ProductVariant.ProductVariantAttribute("color", "green")
                    },
                    new List<Multilingual>
                    {
                        new Multilingual("en", "Test Product - Green")
                    },
                    new List<Description>
                    {
                        new (
                            DescriptionTypes.Text,
                            new Multilingual("en", "This is a test product - green version."),
                            null,
                            null)
                    },
                    new List<ImageDto>(),
                    null),
            },
            null,
            false,
            Mocks.SleekflowStaffId,
            null);
        var createProductScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _
                    .Post
                    .Json(createProductInput)
                    .ToUrl("/Products/CreateProduct");
            });

        var createProductOutputOutput =
            await createProductScenarioResult.ReadAsJsonAsync<Output<CreateProduct.CreateProductOutput>>();

        if (createProductOutputOutput?.Data.Product == null)
        {
            throw new Exception("Could not create product.");
        }

        return createProductOutputOutput;
    }

    public static async Task DeleteTestProductAsync(
        CreateStore.CreateStoreOutput createStoreOutput,
        CreateProduct.CreateProductOutput createProductOutput)
    {
        var deleteProductInput = new DeleteProduct.DeleteProductInput(
            createProductOutput!.Product.Id,
            Mocks.SleekflowCompanyId,
            createStoreOutput!.Store.Id,
            "MyStaffId",
            null);
        await Application.Host.Scenario(
            _ =>
            {
                _
                    .Post
                    .Json(deleteProductInput)
                    .ToUrl("/Products/DeleteProduct");
            });
    }
}