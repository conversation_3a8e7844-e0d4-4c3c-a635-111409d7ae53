using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class PreviewObjectsV2 : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public PreviewObjectsV2(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class PreviewObjectsV2Input : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filters")]
        [ValidateArray]
        public List<SyncConfigFilter>? Filters { get; set; }

        [JsonProperty("field_filters")]
        [ValidateArray]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public PreviewObjectsV2Input(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            string entityTypeName,
            List<SyncConfigFilter>? filters,
            List<SyncConfigFieldFilter>? fieldFilters,
            string? nextRecordsUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            EntityTypeName = entityTypeName;
            Filters = filters;
            FieldFilters = fieldFilters;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public class PreviewObjectsV2Output
    {
        [JsonProperty("objects")]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public PreviewObjectsV2Output(
            List<Dictionary<string, object?>> objects,
            string? nextRecordsUrl)
        {
            Objects = objects;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public async Task<PreviewObjectsV2Output> F(
        PreviewObjectsV2Input previewObjectsV2Input)
    {
        var sleekflowCompanyId = previewObjectsV2Input.SleekflowCompanyId;
        var providerConnectionId = previewObjectsV2Input.ProviderConnectionId;
        var providerName = previewObjectsV2Input.ProviderName;
        var entityTypeName = previewObjectsV2Input.EntityTypeName;

        var providerService = _providerSelector.GetProviderService(providerName);

        var filterGroups = new List<SyncConfigFilterGroup>();
        if (previewObjectsV2Input.Filters != null)
        {
            filterGroups.Add(new SyncConfigFilterGroup(previewObjectsV2Input.Filters));
        }

        var previewObjectsV2Output = await providerService.PreviewObjectsV2Async(
            sleekflowCompanyId,
            providerConnectionId,
            entityTypeName,
            filterGroups,
            previewObjectsV2Input.FieldFilters,
            previewObjectsV2Input.NextRecordsUrl);

        return new PreviewObjectsV2Output(
            previewObjectsV2Output.Objects,
            previewObjectsV2Output.NextRecordsUrl);
    }
}