package main

import (
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    "strings"
)

type OPAInput struct {
    Input struct {
        User struct {
            Role    string `json:"role"`
            Company string `json:"company"`
        } `json:"user"`
        HTTPRequest struct {
            Path   string `json:"path"`
            Method string `json:"method"`
        } `json:"http_request"`
    } `json:"input"`
}

func performOPACheck(ctx context.Context, req *http.Request, opa_endpoint string, rbacRoles []interface{}, companyID string, logger Logger) (bool, error) {
    OPAInput := OPAInput{
        Input: struct {
            User struct {
                Role    string `json:"role"`
                Company string `json:"company"`
            } `json:"user"`
            HTTPRequest struct {
                Path   string `json:"path"`
                Method string `json:"method"`
            } `json:"http_request"`
        }{
            User: struct {
                Role    string `json:"role"`
                Company string `json:"company"`
            }{
                Role:    strings.Join(toStringArray(rbacRoles), ","),
                Company: companyID,
            },
            HTTPRequest: struct {
                Path   string `json:"path"`
                Method string `json:"method"`
            }{
                Path:   req.URL.Path,
                Method: req.Method,
            },
        },
    }


    OPAInputJson, _ := json.Marshal(OPAInput)
    OPAReq, _ := http.NewRequestWithContext(ctx, "POST", opa_endpoint+"/v1/data/authz/allow", strings.NewReader(string(OPAInputJson)))
    OPAResp, OPARespErr := myHttpClient.Do(OPAReq)



    if OPARespErr != nil {
        logger.Error(fmt.Sprintf("Error calling OPA: %v", OPARespErr))
        return false, OPARespErr
    }
    defer OPAResp.Body.Close()

    if OPAResp.StatusCode != http.StatusOK {
        logger.Error(fmt.Sprintf("OPA returned non-200 status code: %d", OPAResp.StatusCode))
        return false, fmt.Errorf("OPA returned non-200 status code: %d", OPAResp.StatusCode)
    }

    var OPAOutput map[string]interface{}


    if err := json.NewDecoder(OPAResp.Body).Decode(&OPAOutput); err != nil {
        logger.Error(fmt.Sprintf("Error decoding OPA response: %v", err))
        return false, err
    }

    logger.Error(fmt.Sprintf( "OPA OPAOutput: %v", OPAOutput))

    resultMap, ok := OPAOutput["result"].(map[string]interface{})
    if !ok {
        logger.Error("Invalid OPA response: missing or invalid 'result' field")
        return false, fmt.Errorf("Invalid OPA response: missing or invalid 'result' field")
    }

    allowed, ok := resultMap["allowed"].(bool)
    if !ok {
        logger.Error("Invalid OPA response: 'allowed' is not a boolean")
        return false, fmt.Errorf("Invalid OPA response: 'allowed' is not a boolean")
    }

    action, _ := resultMap["action"].(string)
    resources, ok := resultMap["resources"].([]interface{})
    if ok {
        var resourceList []string
        for _, res := range resources {
            resMap, _ := res.(map[string]interface{})
            action, _ := resMap["action"].(string)
            resource, _ := resMap["resource"].(string)
            resourceList = append(resourceList, action+":"+resource)
        }
        concatenatedResources := strings.Join(resourceList, ",")
        logger.Debug(fmt.Sprintf("OPA decision: Resources: %s, Allowed: %v", concatenatedResources, allowed))
        if allowed {
            req.Header.Set("X-Sleekflow-Permission", concatenatedResources)
        }
    } else {
        resource, _ := resultMap["resource"].(string)
        logger.Debug(fmt.Sprintf("OPA decision: Action: %s, Resource: %s, Allowed: %v", action, resource, allowed))
        if allowed {
            req.Header.Set("X-Sleekflow-Permission", action+":"+resource)
        }
    }
    return allowed, nil
}
