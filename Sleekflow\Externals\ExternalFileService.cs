using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Externals;

public interface IExternalFileService
{
    Task<(string? FileName, string ContentType, byte[] FileContent)> DownloadFileFromUrlAsync(string url);
}

public class ExternalFileService : IExternalFileService, ISingletonService
{
    private readonly HttpClient _httpClient;

    public ExternalFileService(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<(string? FileName, string ContentType, byte[] FileContent)> DownloadFileFromUrlAsync(string url)
    {
        var response = await _httpClient.GetAsync(url);

        if (!response.IsSuccessStatusCode)
        {
            throw new SfInternalErrorException($"Error downloading file: {response.StatusCode}");
        }

        var context = response.Content;
        var headers = context.Headers;
        return (headers.ContentDisposition?.FileName?.Trim('"') ?? string.Empty,
            headers.ContentType?.ToString() ?? throw new SfInternalErrorException("ContentType is null"),
            await context.ReadAsByteArrayAsync());
    }
}