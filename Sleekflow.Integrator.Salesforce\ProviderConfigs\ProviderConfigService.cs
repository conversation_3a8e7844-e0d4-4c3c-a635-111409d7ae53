﻿using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.Integrator.Salesforce.ProviderConfigs;

public interface IProviderConfigService
{
    Task<ProviderConfig> GetProviderConfigAsync(string sleekflowCompanyId, string providerName);
}

public class ProviderConfigService : IScopedService, IProviderConfigService
{
    private readonly IProviderConfigRepository _providerConfigRepository;

    public ProviderConfigService(
        IProviderConfigRepository providerConfigRepository)
    {
        _providerConfigRepository = providerConfigRepository;
    }

    public async Task<ProviderConfig> GetProviderConfigAsync(string sleekflowCompanyId, string providerName)
    {
        var providerConfigs = await _providerConfigRepository.GetObjectsAsync(
            pc =>
                pc.SleekflowCompanyId == sleekflowCompanyId
                && pc.ProviderName == providerName);
        if (providerConfigs.Count == 0)
        {
            throw new SfNotInitializedException(providerName);
        }

        return providerConfigs[0];
    }
}