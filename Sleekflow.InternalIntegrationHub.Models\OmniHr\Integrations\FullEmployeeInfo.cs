using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Integrations;

public class FullEmployeeInfo : IEquatable<FullEmployeeInfo>
{
    [JsonProperty("external_id")]
    public string? ExternalId { get; set; }

    [JsonProperty("first_name")]
    public string? FirstName { get; set; }

    [JsonProperty("last_name")]
    public string? LastName { get; set; }

    [JsonProperty("primary_email")]
    public OmniHrEmail? PrimaryEmail { get; set; }

    [JsonProperty("location_name")]
    public string? LocationName { get; set; }

    [JsonProperty("currency")]
    public string? Currency { get; set; }

    [JsonProperty("manager_email")]
    public OmniHrEmail? ManagerEmail { get; set; }

    [JsonConstructor]
    public FullEmployeeInfo(
        string? externalId,
        string? firstName,
        string? lastName,
        OmniHrEmail? primaryEmail,
        string? locationName,
        string? currency,
        OmniHrEmail? managerEmail)
    {
        ExternalId = externalId;
        FirstName = firstName;
        LastName = lastName;
        PrimaryEmail = primaryEmail;
        LocationName = locationName;
        Currency = currency;
        ManagerEmail = managerEmail;
    }

    public FullEmployeeInfo()
    {
    }

    public bool Equals(FullEmployeeInfo? other)
    {
        if (other is null)
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return ExternalId == other.ExternalId && FirstName == other.FirstName && LastName == other.LastName &&
               Equals(PrimaryEmail, other.PrimaryEmail) && LocationName == other.LocationName &&
               Currency == other.Currency && Equals(ManagerEmail, other.ManagerEmail);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null)
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (obj.GetType() != GetType())
        {
            return false;
        }

        return Equals((FullEmployeeInfo) obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(ExternalId, FirstName, LastName, PrimaryEmail, LocationName, Currency, ManagerEmail);
    }
}