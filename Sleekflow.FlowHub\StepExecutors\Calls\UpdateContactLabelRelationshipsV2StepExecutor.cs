using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactLabelRelationshipsV2StepExecutor : IStepExecutor
{
}

public class UpdateContactLabelRelationshipsV2StepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactLabelRelationshipsV2StepArgs>>,
        IUpdateContactLabelRelationshipsV2StepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactLabelRelationshipsV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactLabelRelationships",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<UpdateContactLabelRelationshipsStepExecutor.UpdateContactLabelRelationshipsInput> GetArgs(
        CallStep<UpdateContactLabelRelationshipsV2StepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, "{{ (trigger_event_body.contact_id | string.whitespace) ? usr_var_dict.contact.id : trigger_event_body.contact_id }}")
                      ?? throw new InvalidOperationException("No contact id found"));

        List<string>? addLabels = null;
        List<string>? removeLabels = null;
        List<string>? setLabels = null;

        switch (callStep.Args.RemovalActionType)
        {
            case "specific_label":
                removeLabels = callStep.Args.Labels;
                break;

            case "all_labels":
                setLabels = new List<string>();
                break;

            default: // add labels
                addLabels = callStep.Args.Labels;
                break;
        }

        return new UpdateContactLabelRelationshipsStepExecutor.UpdateContactLabelRelationshipsInput(
            state.Id,
            state.Identity,
            contactId,
            addLabels,
            removeLabels,
            setLabels);
    }
}