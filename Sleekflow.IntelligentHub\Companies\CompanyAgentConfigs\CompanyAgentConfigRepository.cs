using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.Prompts;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;

public interface ICompanyAgentConfigRepository : IRepository<CompanyAgentConfig>
{
    Task<CompanyAgentConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string etag,
        AuditEntity.SleekflowStaff sleekflowStaff,
        string? name,
        PromptInstruction? promptInstruction,
        bool? isChatHistoryEnabledAsContext,
        bool? isContactPropertiesEnabledAsContext,
        int? numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? collaborationMode,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        string? description = null,
        string? type = null,
        CompanyAgentConfigActions? actions = null);
}

public class CompanyAgentConfigRepository
    : BaseRepository<CompanyAgentConfig>, ICompanyAgentConfigRepository, IScopedService
{
    public CompanyAgentConfigRepository(
        ILogger<CompanyAgentConfigRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<CompanyAgentConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string etag,
        AuditEntity.SleekflowStaff sleekflowStaff,
        string? name,
        PromptInstruction? promptInstruction,
        bool? isChatHistoryEnabledAsContext,
        bool? isContactPropertiesEnabledAsContext,
        int? numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? collaborationMode,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        string? description = null,
        string? type = null,
        CompanyAgentConfigActions? actions = null)
    {
        var patchOperations = new Dictionary<string, object?>
        {
            {
                CompanyAgentConfig.PropertyNameName, name
            },
            {
                CompanyAgentConfig.PropertyNameIsChatHistoryEnabledAsContext, isChatHistoryEnabledAsContext
            },
            {
                CompanyAgentConfig.PropertyNameIsContactPropertiesEnabledAsContext, isContactPropertiesEnabledAsContext
            },
            {
                CompanyAgentConfig.PropertyNameNumberOfPreviousMessagesInChatHistoryAvailableAsContext,
                numberOfPreviousMessagesInChatHistoryAvailableAsContext
            },
            {
                CompanyAgentConfig.PropertyNameChannelType, channelType
            },
            {
                CompanyAgentConfig.PropertyNameChannelId, channelId
            },
            {
                CompanyAgentConfig.PropertyNamePromptInstruction, promptInstruction
            },
            {
                CompanyAgentConfig.PropertyNameCollaborationMode, collaborationMode
            },
            {
                CompanyAgentConfig.PropertyNameKnowledgeRetrievalConfig, knowledgeRetrievalConfig
            },
            {
                CompanyAgentConfig.PropertyNameDescription, description
            },
            {
                CompanyAgentConfig.PropertyNameType, type
            },
            {
                CompanyAgentConfig.PropertyNameActions, actions
            }
        };

        var operations = new List<PatchOperation>
        {
            PatchOperation.Set($"/{AuditEntity.PropertyNameUpdatedBy}", sleekflowStaff),
            PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
        };

        foreach (var patchOperation in patchOperations)
        {
            if (patchOperation.Value != null)
            {
                operations.Add(PatchOperation.Set($"/{patchOperation.Key}", patchOperation.Value));
            }
        }

        return await PatchAndGetAsync(id, sleekflowCompanyId, operations, eTag: etag);
    }
}
