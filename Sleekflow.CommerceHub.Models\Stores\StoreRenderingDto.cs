using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Models.Stores;

public class StoreRenderingDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty(Store.PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty(Store.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(Store.PropertyNameLanguages)]
    public List<Language> Languages { get; set; }

    [JsonProperty(Store.PropertyNameCurrencies)]
    public List<Currency> Currencies { get; set; }

    [JsonConstructor]
    public StoreRenderingDto(
        string id,
        string? url,
        string name,
        List<Description> descriptions,
        List<Language> languages,
        List<Currency> currencies)
    {
        Id = id;
        Url = url;
        Name = name;
        Descriptions = descriptions;
        Languages = languages;
        Currencies = currencies;
    }

    public StoreRenderingDto(Store store, LanguageOption languageOption)
        : this(
            store.Id,
            store.Url,
            store.Names.Find(n => n.LanguageIsoCode == languageOption.LanguageIsoCode)?.Value
            ?? store.Names.First(n => n.LanguageIsoCode == languageOption.DefaultLanguageIsoCode).Value,
            store
                .Descriptions
                .Where(
                    d =>
                        d.Image != null
                        || (d.Text != null && d.Text.LanguageIsoCode == languageOption.LanguageIsoCode))
                .ToList(),
            store.Languages,
            store.Currencies)
    {
    }

    public static StoreRenderingDto Sample()
    {
        var languageIsoCode = "en";
        var languageIsoCodeToCultureInfo = CultureUtils.GetLanguageIsoCodeToCultureInfo();
        var cultureInfo = languageIsoCodeToCultureInfo[languageIsoCode];

        return new StoreRenderingDto(
            "myStoreId",
            "https://shop.sleekflow.io/Store/myStoreId",
            "My Store Name",
            new List<Description>
            {
                new Description(
                    DescriptionTypes.Text,
                    new Multilingual(languageIsoCode, "This is a sample store description"),
                    null,
                    null)
            },
            new List<Language>()
            {
                new Language(languageIsoCode, cultureInfo.DisplayName, cultureInfo.NativeName, true)
            },
            new List<Currency>()
            {
                new Currency("HKD", "Hong Kong Dollar", "HK$")
            });
    }
}