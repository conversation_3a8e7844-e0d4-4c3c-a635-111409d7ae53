using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

public class Customer
{
    [JsonConstructor]
    public Customer(string externalId)
    {
        ExternalId = externalId;
    }

    [Required]
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string? CustomerId { get; set; }

    [Required]
    [JsonProperty("externalId", NullValueHandling = NullValueHandling.Ignore)]
    public string? ExternalId { get; set; }
}