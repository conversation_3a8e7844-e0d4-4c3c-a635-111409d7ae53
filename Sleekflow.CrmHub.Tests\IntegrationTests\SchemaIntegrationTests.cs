﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.CrmHub.Triggers.CrmHubConfigs;
using Sleekflow.CrmHub.Triggers.Schemas;
using Sleekflow.Outputs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests.IntegrationTests;

public class SchemaIntegrationTests
{
    private static readonly string MockCompanyId = $"aaaabbbb-cccc-dddd-eeee-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[..12]}";

    private const string SingleLineTextPropertyUniqueName = "single_line_text";
    private const string NumericPropertyUniqueName = "numeric";
    private const string DecimalPropertyUniqueName = "decimal";
    private const string SingleChoicePropertyUniqueName = "single_choice";
    private const string MultipleChoicePropertyUniqueName = "multiple_choice";
    private const string BooleanPropertyUniqueName = "boolean";
    private const string DatePropertyUniqueName = "date";
    private const string DateTimePropertyUniqueName = "datetime";
    private const string ArrayObjectPropertyUniqueName = "array_object";
    private const string ImagePropertyUniqueName = "image";

    private const string OptionName1 = "option_name_1";
    private const string OptionName2 = "option_name_2";
    private const string OptionName3 = "option_name_3";

    private List<PropertyInput> MockPropertyInputs;
    private PrimaryPropertyInput MockPrimaryPropertyInput;
    private AuditEntity.SleekflowStaff MockSleekflowStaff;
    private CrmHubConfig MockCrmHubConfig;
    private SchemaAccessibilitySettings MockSchemaAccessibilitySettings;

    [SetUp]
    public async Task TestSetUp()
    {
        MockSchemaAccessibilitySettings = new SchemaAccessibilitySettings("custom");
        MockCrmHubConfig = await CreateCrmHubConfig();

        MockSleekflowStaff = new AuditEntity.SleekflowStaff("mocked-staff-id", null);
        MockPropertyInputs = new List<PropertyInput>
        {
            new PropertyInput(
                "Test Numeric Property",
                "test_numeric_property",
                new NumericDataType(),
                false,
                true,
                true,
                true,
                true,
                1,
                MockSleekflowStaff,
                null),
            new PropertyInput(
                "Test Options Property",
                "test_options_property",
                new SingleChoiceDataType(),
                false,
                true,
                false,
                false,
                true,
                1,
                MockSleekflowStaff,
                new List<Option>
                {
                    new Option(string.Empty, "Male", 0),
                    new Option(string.Empty, "Female", 1)
                }),
        };
        MockPrimaryPropertyInput = new PrimaryPropertyInput(
            "Primary Property",
            "primary_property",
            new SingleLineTextDataType(),
            true,
            true,
            true,
            new PrimaryPropertyConfig(true, null),
            null);
    }

    [TearDown]
    public async Task TearDown()
    {
        var schemaRepository = GetSchemaRepository();

        var schemas = await schemaRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var schema in schemas)
        {
            await schemaRepository.DeleteAsync(
                schema.Id,
                MockCompanyId);
        }

        var crmHubConfigRepository = GetCrmHubConfigRepository();
        await crmHubConfigRepository.DeleteAsync(MockCrmHubConfig.Id, MockCompanyId);
    }

    [Test]
    public async Task SchemaLifeCycleTest()
    {
        // /Schemas/CreateSchema
        var createSchemaInput = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            MockPropertyInputs,
            MockPrimaryPropertyInput,
            MockSchemaAccessibilitySettings);

        var createSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
            });

        var createSchemaOutput =
            await createSchemaScenarioResult.ReadAsJsonAsync<
                Output<CreateSchema.CreateSchemaOutput>>();

        Assert.That(createSchemaOutput, Is.Not.Null);
        Assert.That(createSchemaOutput!.HttpStatusCode, Is.EqualTo(200));

        var schemaId = createSchemaOutput.Data.Schema.Id;

        // update schema metadata

        // /Schemas/GetSchema
        var getSchemaInput = new GetSchema.GetSchemaInput(
            MockCompanyId,
            schemaId);

        var getSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemaInput).ToUrl("/Schemas/GetSchema");
            });

        var getSchemaOutput =
            await getSchemaScenarioResult.ReadAsJsonAsync<
                Output<GetSchema.GetSchemaOutput>>();

        Assert.That(getSchemaOutput, Is.Not.Null);
        Assert.That(getSchemaOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Schemas/UpdateSchema
        var updateSchemaInput = new UpdateSchema.UpdateSchemaInput(
            getSchemaOutput.Data.Schema.SleekflowCompanyId,
            getSchemaOutput.Data.Schema.Id,
            "Updated Schema",
            "Updated Primary Property Name",
            false,
            getSchemaOutput.Data.Schema.Properties);

        var updateSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateSchemaInput).ToUrl("/Schemas/UpdateSchema");
            });

        var updateSchemaOutput =
            await updateSchemaScenarioResult.ReadAsJsonAsync<
                Output<UpdateSchema.UpdateSchemaOutput>>();

        Assert.That(updateSchemaOutput, Is.Not.Null);
        Assert.That(updateSchemaOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateSchemaOutput.Data.Schema.IsEnabled, Is.EqualTo(false));
        Assert.That(updateSchemaOutput.Data.Schema.DisplayName, Is.EqualTo("Updated Schema"));
        Assert.That(updateSchemaOutput.Data.Schema.PrimaryProperty.DisplayName, Is.EqualTo("Updated Primary Property Name"));

        // update properties
        var updatedProperties = updateSchemaOutput.Data.Schema.Properties;
        updatedProperties.RemoveAll(p => p.DataType.Name == "numeric");
        updatedProperties.First(p => p.DataType.Name == "single_choice").Options!.First(
            o => o.Value == "Female").Value = "New Female";
        updatedProperties.First(p => p.DataType.Name == "single_choice").Options!.RemoveAll(
            o => o.Value == "Male");
        updatedProperties.First(p => p.DataType.Name == "single_choice").Options!.Add(
            new Option(string.Empty, "Helicopter", 3));
        updatedProperties.Add(
            new Property(
                string.Empty,
                "New Property",
                "new_property",
                new SingleLineTextDataType(),
                false,
                true,
                true,
                true,
                10,
                MockSleekflowStaff,
                DateTimeOffset.UtcNow,
                null));
        updatedProperties.First(p => p.DataType.Name == "single_choice").IsRequired = false;

        updateSchemaInput = new UpdateSchema.UpdateSchemaInput(
            updateSchemaOutput.Data.Schema.SleekflowCompanyId,
            updateSchemaOutput.Data.Schema.Id,
            updateSchemaOutput.Data.Schema.DisplayName,
            updateSchemaOutput.Data.Schema.PrimaryProperty.DisplayName,
            updateSchemaOutput.Data.Schema.IsEnabled,
            updatedProperties);

        updateSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateSchemaInput).ToUrl("/Schemas/UpdateSchema");
            });

        updateSchemaOutput =
            await updateSchemaScenarioResult.ReadAsJsonAsync<
                Output<UpdateSchema.UpdateSchemaOutput>>();

        Assert.That(updateSchemaOutput, Is.Not.Null);
        Assert.That(updateSchemaOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateSchemaOutput.Data.Schema.Properties.Count, Is.EqualTo(2));
        Assert.That(
            updateSchemaOutput.Data.Schema.Properties
                .First(p => p.DataType.Name == "single_line_text").DisplayName,
            Is.EqualTo("New Property"));
        Assert.That(
            updateSchemaOutput.Data.Schema.Properties
                .First(p => p.DataType.Name == "single_choice").Options!.Count(o => o.Value == "Helicopter"),
            Is.EqualTo(1));
        Assert.That(
            updateSchemaOutput.Data.Schema.Properties
                .First(p => p.DataType.Name == "single_choice").Options!.Count(o => o.Value == "New Female"),
            Is.EqualTo(1));
        Assert.That(
            updateSchemaOutput.Data.Schema.Properties
                .First(p => p.DataType.Name == "single_choice").IsRequired,
            Is.EqualTo(false));

        // /Schemas/DeleteSchema
        var deleteSchemaInput = new DeleteSchema.DeleteSchemaInput(
            MockCompanyId,
            schemaId);

        var deleteSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteSchemaInput).ToUrl("/Schemas/DeleteSchema");
            });

        var deleteSchemaOutput =
            await deleteSchemaScenarioResult.ReadAsJsonAsync<
                Output<DeleteSchema.DeleteSchemaOutput>>();

        Assert.That(deleteSchemaOutput, Is.Not.Null);
        Assert.That(deleteSchemaOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task RearrangeSchemaOrderTest()
    {
        for (int i = 0; i < 4; i++)
        {
            await CreateSchema();
        }

        // /Schemas/GetSchemas
        var getSchemasInput = new GetSchemas.GetSchemasInput(
            MockCompanyId,
            10,
            new List<GetSchemas.GetSchemasFilterGroup>
            {
                new GetSchemas.GetSchemasFilterGroup(
                    new List<SchemaQueryBuilder.SchemaFilter>
                    {
                        new SchemaQueryBuilder.SchemaFilter(Schema.PropertyNameIsDeleted, "=", false)
                    })
            },
            new List<SchemaQueryBuilder.SchemaSort>
            {
                new SchemaQueryBuilder.SchemaSort(Schema.PropertyNameSortingWeight, "asc")
            },
            null);

        var getSchemasScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemasInput).ToUrl("/Schemas/GetSchemas");
            });

        var getSchemasOutput =
            await getSchemasScenarioResult.ReadAsJsonAsync<
                Output<GetSchemas.GetSchemasOutput>>();

        var targetSchemaId = getSchemasOutput!.Data.Schemas[2].Id;

        // move to first
        // /Schemas/RearrangeSchemaOrder
        var rearrangeSchemaOrderInput = new RearrangeSchemaOrder.RearrangeSchemaOrderInput(
            MockCompanyId,
            targetSchemaId,
            string.Empty);

        var rearrangeSchemaOrderScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(rearrangeSchemaOrderInput).ToUrl("/Schemas/RearrangeSchemaOrder");
            });

        var rearrangeSchemaOrderOutput =
            await rearrangeSchemaOrderScenarioResult.ReadAsJsonAsync<
                Output<RearrangeSchemaOrder.RearrangeSchemaOrderOutput>>();

        Assert.That(rearrangeSchemaOrderOutput, Is.Not.Null);
        Assert.That(rearrangeSchemaOrderOutput!.HttpStatusCode, Is.EqualTo(200));

        getSchemasScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemasInput).ToUrl("/Schemas/GetSchemas");
            });

        getSchemasOutput =
            await getSchemasScenarioResult.ReadAsJsonAsync<Output<GetSchemas.GetSchemasOutput>>();

        Assert.That(getSchemasOutput, Is.Not.Null);
        Assert.That(getSchemasOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemasOutput.Data.Schemas.FindIndex(s => s.Id == targetSchemaId), Is.EqualTo(0));

        // move to middle
        // /Schemas/RearrangeSchemaOrder
        var currentSecondSchemaId = getSchemasOutput.Data.Schemas[1].Id;

        rearrangeSchemaOrderInput = new RearrangeSchemaOrder.RearrangeSchemaOrderInput(
            MockCompanyId,
            targetSchemaId,
            currentSecondSchemaId);

        rearrangeSchemaOrderScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(rearrangeSchemaOrderInput).ToUrl("/Schemas/RearrangeSchemaOrder");
            });

        rearrangeSchemaOrderOutput =
            await rearrangeSchemaOrderScenarioResult.ReadAsJsonAsync<
                Output<RearrangeSchemaOrder.RearrangeSchemaOrderOutput>>();

        Assert.That(rearrangeSchemaOrderOutput, Is.Not.Null);
        Assert.That(rearrangeSchemaOrderOutput!.HttpStatusCode, Is.EqualTo(200));

        getSchemasScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemasInput).ToUrl("/Schemas/GetSchemas");
            });

        getSchemasOutput =
            await getSchemasScenarioResult.ReadAsJsonAsync<Output<GetSchemas.GetSchemasOutput>>();

        Assert.That(getSchemasOutput, Is.Not.Null);
        Assert.That(getSchemasOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            getSchemasOutput.Data.Schemas.FindIndex(s => s.Id == targetSchemaId),
            Is.EqualTo(getSchemasOutput.Data.Schemas.FindIndex(s => s.Id == currentSecondSchemaId) + 1));

        // move to tail
        // /Schemas/RearrangeSchemaOrder
        var currentLastSchemaId = getSchemasOutput.Data.Schemas[^1].Id;

        rearrangeSchemaOrderInput = new RearrangeSchemaOrder.RearrangeSchemaOrderInput(
            MockCompanyId,
            targetSchemaId,
            currentLastSchemaId);

        rearrangeSchemaOrderScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(rearrangeSchemaOrderInput).ToUrl("/Schemas/RearrangeSchemaOrder");
            });

        rearrangeSchemaOrderOutput =
            await rearrangeSchemaOrderScenarioResult.ReadAsJsonAsync<
                Output<RearrangeSchemaOrder.RearrangeSchemaOrderOutput>>();

        Assert.That(rearrangeSchemaOrderOutput, Is.Not.Null);
        Assert.That(rearrangeSchemaOrderOutput!.HttpStatusCode, Is.EqualTo(200));

        getSchemasScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemasInput).ToUrl("/Schemas/GetSchemas");
            });

        getSchemasOutput =
            await getSchemasScenarioResult.ReadAsJsonAsync<Output<GetSchemas.GetSchemasOutput>>();

        Assert.That(getSchemasOutput, Is.Not.Null);
        Assert.That(getSchemasOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            getSchemasOutput.Data.Schemas.FindIndex(s => s.Id == targetSchemaId),
            Is.EqualTo(getSchemasOutput.Data.Schemas.Count - 1));
    }

    [Test]
    public async Task Schema_WithArrayObjectProperty_Tests()
    {
        // /Schemas/CreateSchema
        var propertyDictionary = GeneratePropertiesForSchemaCreation();
        var innerSchema = new InnerSchema(propertyDictionary.Values.ToList());

        var arrayObjectPropertyInput = new PropertyInput(
            "Test ArrayObject",
            ArrayObjectPropertyUniqueName,
            new ArrayObjectDataType(innerSchema),
            false,
            false,
            true,
            true,
            true,
            1,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            null);

        var createSchemaInput = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            new List<PropertyInput> { arrayObjectPropertyInput },
            MockPrimaryPropertyInput,
            MockSchemaAccessibilitySettings);

        var createSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
            });

        var createSchemaOutput =
            await createSchemaScenarioResult.ReadAsJsonAsync<
                Output<CreateSchema.CreateSchemaOutput>>();

        Assert.That(createSchemaOutput, Is.Not.Null);
        Assert.That(createSchemaOutput!.HttpStatusCode, Is.EqualTo(200));

        var createdArrayObjectProperty = createSchemaOutput.Data.Schema.Properties.Find(
            p => p.UniqueName == ArrayObjectPropertyUniqueName)!;
        var createdInnerSchema = createdArrayObjectProperty.DataType.GetInnerSchema();

        Assert.Multiple(
            () =>
            {
                Assert.That(string.IsNullOrWhiteSpace(createdArrayObjectProperty.Id), Is.False);
                Assert.That(createdArrayObjectProperty.DataType.HasInnerSchema, Is.True);
                Assert.That(createdArrayObjectProperty.IsRequired, Is.EqualTo(arrayObjectPropertyInput.IsRequired));
                Assert.That(createdArrayObjectProperty.DisplayName, Is.EqualTo(arrayObjectPropertyInput.DisplayName));
            });

        var numericInnerProperty = createdInnerSchema.Properties.First(p => p.UniqueName == NumericPropertyUniqueName);
        var multipleChoiceInnerProperty = createdInnerSchema.Properties.First(p => p.UniqueName == MultipleChoicePropertyUniqueName);

        Assert.Multiple(
            () =>
            {
                Assert.That(createdInnerSchema.Properties, Has.Count.EqualTo(innerSchema.Properties.Count));

                Assert.That(string.IsNullOrWhiteSpace(numericInnerProperty.Id), Is.False);
                Assert.That(numericInnerProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Numeric));
                Assert.That(numericInnerProperty.IsRequired, Is.EqualTo(propertyDictionary[NumericPropertyUniqueName].IsRequired));
                Assert.That(numericInnerProperty.IsVisible, Is.EqualTo(propertyDictionary[NumericPropertyUniqueName].IsVisible));
                Assert.That(numericInnerProperty.DisplayOrder, Is.EqualTo(1));
                Assert.That(numericInnerProperty.CreatedBy!.SleekflowStaffId, Is.EqualTo("mocked-staff-id"));

                Assert.That(string.IsNullOrWhiteSpace(multipleChoiceInnerProperty.Id), Is.False);
                Assert.That(multipleChoiceInnerProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.MultipleChoice));
                Assert.That(multipleChoiceInnerProperty.Options!.Count, Is.EqualTo(3));
                Assert.That(string.IsNullOrEmpty(multipleChoiceInnerProperty.Options[0].Id), Is.False);
                Assert.That(multipleChoiceInnerProperty.Options[0].Value, Is.EqualTo(OptionName1));
            });

        // /Schemas/UpdateSchema
        createdInnerSchema.Properties.First(x => x.UniqueName == NumericPropertyUniqueName).IsRequired =
            true;
        createdInnerSchema.Properties.First(x => x.UniqueName == NumericPropertyUniqueName).DisplayName =
            "Updated Inner Numeric";

        createdInnerSchema.Properties.First(x => x.UniqueName == SingleChoicePropertyUniqueName)
            .Options![0]
            .Value = "Updated Option 1";
        createdInnerSchema.Properties.First(x => x.UniqueName == SingleChoicePropertyUniqueName)
            .Options!
            .Add(new Option(string.Empty, "Man!", 10));

        createdInnerSchema.Properties.RemoveAll(x => x.UniqueName == DatePropertyUniqueName);
        createdInnerSchema.Properties.RemoveAll(x => x.UniqueName == MultipleChoicePropertyUniqueName);

        createdInnerSchema.Properties.Add(
            new Property(
                string.Empty,
                "New Inner Boolean",
                BooleanPropertyUniqueName,
                new BooleanDataType(),
                false,
                true,
                true,
                true,
                5,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                null));

        var updateSchemaInput = new UpdateSchema.UpdateSchemaInput(
            createSchemaOutput.Data.Schema.SleekflowCompanyId,
            createSchemaOutput.Data.Schema.Id,
            createSchemaOutput.Data.Schema.DisplayName,
            createSchemaOutput.Data.Schema.PrimaryProperty.DisplayName,
            createSchemaOutput.Data.Schema.IsEnabled,
            new List<Property> { createdArrayObjectProperty });

        var updateSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateSchemaInput).ToUrl("/Schemas/UpdateSchema");
            });

        var updateSchemaOutput =
            await updateSchemaScenarioResult.ReadAsJsonAsync<
                Output<UpdateSchema.UpdateSchemaOutput>>();

        Assert.That(updateSchemaOutput, Is.Not.Null);
        Assert.That(updateSchemaOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateSchemaOutput.Data.Schema.Properties.Count, Is.EqualTo(1));

        var updatedInnerSchema = updateSchemaOutput.Data.Schema.Properties[0].DataType.GetInnerSchema();

        Assert.Multiple(
            () =>
            {
                Assert.That(updatedInnerSchema.Properties, Has.Count.EqualTo(3));

                Assert.That(updatedInnerSchema.Properties.Count(x => x.UniqueName == DatePropertyUniqueName), Is.EqualTo(0));
                Assert.That(updatedInnerSchema.Properties.Count(x => x.UniqueName == MultipleChoicePropertyUniqueName), Is.EqualTo(0));
                Assert.That(updatedInnerSchema.Properties.Count(x => x.UniqueName == BooleanPropertyUniqueName), Is.EqualTo(1));
            });

        var updatedNumericProperty = updatedInnerSchema.Properties.First(x => x.UniqueName == NumericPropertyUniqueName);
        var updatedSingleChoiceProperty = updatedInnerSchema.Properties.First(x => x.UniqueName == SingleChoicePropertyUniqueName);
        var newBooleanProperty = updatedInnerSchema.Properties.First(x => x.UniqueName == BooleanPropertyUniqueName);
        Assert.Multiple(
            () =>
            {
                Assert.That(updatedNumericProperty.IsRequired, Is.True);
                Assert.That(updatedNumericProperty.DisplayName, Is.EqualTo("Updated Inner Numeric"));

                Assert.That(updatedSingleChoiceProperty.Options!, Has.Count.EqualTo(3));
                Assert.That(updatedSingleChoiceProperty.Options![0].Value, Is.EqualTo("Updated Option 1"));
                Assert.That(updatedSingleChoiceProperty.Options![2].Value, Is.EqualTo("Man!"));
                Assert.That(string.IsNullOrWhiteSpace(updatedSingleChoiceProperty.Options![2].Id), Is.False);

                Assert.That(string.IsNullOrWhiteSpace(newBooleanProperty.Id), Is.False);
                Assert.That(newBooleanProperty.IsRequired, Is.False);
                Assert.That(newBooleanProperty.DisplayOrder, Is.EqualTo(3));
            });
    }

    [Test]
    public async Task Schema_WithImageProperty_Tests()
    {
        var imagePropertyInput = new PropertyInput(
            "Test Image Property",
            ImagePropertyUniqueName,
            new ImageDataType(),
            false,
            false,
            true,
            true,
            true,
            1,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            null);

        /*
         * Create Schema - with correct property input - should not throw exception
         */
        {
            var createSchemaInput = new CreateSchema.CreateSchemaInput(
                MockCompanyId,
                $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
                $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
                "one-to-one",
                new List<PropertyInput>
                {
                    imagePropertyInput
                },
                MockPrimaryPropertyInput,
                MockSchemaAccessibilitySettings);

            var createSchemaScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
                });

            var createSchemaOutput =
                await createSchemaScenarioResult.ReadAsJsonAsync<
                    Output<CreateSchema.CreateSchemaOutput>>();

            Assert.That(createSchemaOutput, Is.Not.Null);
            Assert.That(createSchemaOutput!.HttpStatusCode, Is.EqualTo(200));
            Assert.That(createSchemaOutput.Data.Schema.Properties.Count, Is.EqualTo(1));
            Assert.That(createSchemaOutput.Data.Schema.Properties[0].UniqueName, Is.EqualTo(ImagePropertyUniqueName));
        }

        /*
         * Create Schema - with more than 1 image property - should throw SfValidationException
         */
        {
            var imagePropertyInput2 = new PropertyInput(
                "Test Image Property",
                "image_property_2",
                new ImageDataType(),
                false,
                false,
                true,
                true,
                true,
                1,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                null);

            var createSchemaInput = new CreateSchema.CreateSchemaInput(
                MockCompanyId,
                $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
                $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
                "one-to-one",
                new List<PropertyInput>
                {
                    imagePropertyInput,
                    imagePropertyInput2
                },
                MockPrimaryPropertyInput,
                MockSchemaAccessibilitySettings);

            var createSchemaScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
                });

            var createSchemaOutput =
                await createSchemaScenarioResult.ReadAsJsonAsync<
                    Output<object>>();

            Assert.That(createSchemaOutput, Is.Not.Null);
            Assert.That(createSchemaOutput!.HttpStatusCode, Is.EqualTo(400));
            Assert.That(createSchemaOutput.Message, Is.EqualTo("The input is incorrect."));
        }
    }

    [Test]
    public async Task SchemaAccessibilitySettings_Tests()
    {
        for (int i = 0; i < 4; i++)
        {
            await CreateSchema();
        }

        var schemas = (await GetSchemasAsync(
            new GetSchemas.GetSchemasInput(
                MockCompanyId,
                10,
                new List<GetSchemas.GetSchemasFilterGroup>(),
                new List<SchemaQueryBuilder.SchemaSort>(),
                null)))
            .Data
            .Schemas;

        Assert.That(schemas.All(s => s.SchemaAccessibilitySettings.Category == "custom"), Is.True);

        /*
         * UsageLimit tests - create schema with category name "custom" - should throw exception
         */
        var createSchemaInput = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            MockPropertyInputs,
            MockPrimaryPropertyInput,
            MockSchemaAccessibilitySettings);

        var createSchemaOutputOutput = await CreateSchemaAsync(createSchemaInput);
        Assert.That(createSchemaOutputOutput.Success, Is.False);
        Assert.That(createSchemaOutputOutput.ErrorCode, Is.EqualTo(3006));

        /*
         * UsageLimit tests - create schema with category name "facebook_lead_ad" - should return correct value
         */
        var createSchemaInput_FacebookLeadAd = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            MockPropertyInputs,
            MockPrimaryPropertyInput,
            new SchemaAccessibilitySettings("facebook_lead_ad"));

        var createSchemaOutputOutput_FacebookLeadAd = await CreateSchemaAsync(createSchemaInput_FacebookLeadAd);

        Assert.That(createSchemaOutputOutput_FacebookLeadAd.Success, Is.True);
        Assert.That(createSchemaOutputOutput_FacebookLeadAd.Data.Schema.SortingWeight, Is.EqualTo(0));
        Assert.That(createSchemaOutputOutput_FacebookLeadAd.Data.Schema.SchemaAccessibilitySettings.Category, Is.EqualTo("facebook_lead_ad"));

        /*
         * Filter tests - get by SchemaAccessibilitySettings.Category - should return correct value
         */

        schemas = (await GetSchemasAsync(
            new GetSchemas.GetSchemasInput(
                MockCompanyId,
                10,
                new List<GetSchemas.GetSchemasFilterGroup>
                {
                    new GetSchemas.GetSchemasFilterGroup(
                        new List<SchemaQueryBuilder.SchemaFilter>
                        {
                            new SchemaQueryBuilder.SchemaFilter(
                                "schema_accessibility_settings.category",
                                "=",
                                "facebook_lead_ad")
                        })
                },
                new List<SchemaQueryBuilder.SchemaSort>(),
                null))).Data.Schemas;

        Assert.That(schemas.Count, Is.EqualTo(1));
        Assert.That(schemas[0].SchemaAccessibilitySettings.Category, Is.EqualTo("facebook_lead_ad"));
    }

    private async Task<Output<GetSchemas.GetSchemasOutput>> GetSchemasAsync(GetSchemas.GetSchemasInput getSchemasInput)
    {
        var getSchemasScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemasInput).ToUrl("/Schemas/GetSchemas");
            });

        return await getSchemasScenarioResult.ReadAsJsonAsync<Output<GetSchemas.GetSchemasOutput>>();
    }

    private async Task<Output<CreateSchema.CreateSchemaOutput>> CreateSchemaAsync(CreateSchema.CreateSchemaInput createSchemaInput)
    {
        var createSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
            });

        return await createSchemaScenarioResult.ReadAsJsonAsync<Output<CreateSchema.CreateSchemaOutput>>();
    }

    private Dictionary<string, Property> GeneratePropertiesForSchemaCreation()
    {
        var properties = new Dictionary<string, Property>();

        properties.Add(
            NumericPropertyUniqueName,
            new Property(
                string.Empty,
                "Test Numeric",
                NumericPropertyUniqueName,
                new NumericDataType(),
                false,
                false,
                true,
                true,
                2,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                null));

        properties.Add(
            DatePropertyUniqueName,
            new Property(
                string.Empty,
                "Test Date",
                DatePropertyUniqueName,
                new DateDataType(),
                false,
                true,
                true,
                true,
                5,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                null));

        properties.Add(
            SingleChoicePropertyUniqueName,
            new Property(
                string.Empty,
                "Test SingleChoice",
                SingleChoicePropertyUniqueName,
                new SingleChoiceDataType(),
                false,
                true,
                true,
                true,
                7,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(string.Empty, OptionName1, 0),
                    new Option(string.Empty, OptionName2, 1)
                }));

        properties.Add(
            MultipleChoicePropertyUniqueName,
            new Property(
                string.Empty,
                "Test MultipleChoice",
                MultipleChoicePropertyUniqueName,
                new MultipleChoiceDataType(),
                true,
                true,
                false,
                false,
                9,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(string.Empty, OptionName1, 0),
                    new Option(string.Empty, OptionName2, 1),
                    new Option(string.Empty, OptionName3, 2)
                }));

        return properties;
    }

    private async Task CreateSchema()
    {
        // /Schemas/CreateSchema
        var createSchemaInput = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            MockPropertyInputs,
            MockPrimaryPropertyInput,
            MockSchemaAccessibilitySettings);

        await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
            });
    }

    private async Task<CrmHubConfig> CreateCrmHubConfig()
    {
        var initializeCrmHubConfigInput = new InitializeCrmHubConfig.InitializeCrmHubConfigInput(
            MockCompanyId,
            new UsageLimit(4, 100, 100, 100, 100),
            null,
            "3880",
            null);

        var initializeCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(initializeCrmHubConfigInput).ToUrl("/CrmHubConfigs/InitializeCrmHubConfig");
            });

        var initializeCrmHubConfigOutput =
            await initializeCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<InitializeCrmHubConfig.InitializeCrmHubConfigOutput>>();

        return initializeCrmHubConfigOutput!.Data.CrmHubConfig;
    }

    private SchemaRepository GetSchemaRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var schemaRepository = new SchemaRepository(
            NullLogger<SchemaRepository>.Instance,
            serviceProvider);

        return schemaRepository;
    }

    private CrmHubConfigRepository GetCrmHubConfigRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var crmHubConfigRepository = new CrmHubConfigRepository(
            NullLogger<CrmHubConfigRepository>.Instance,
            serviceProvider);

        return crmHubConfigRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }
}