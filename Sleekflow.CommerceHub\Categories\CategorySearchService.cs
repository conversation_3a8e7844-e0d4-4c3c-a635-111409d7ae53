using System.ComponentModel.DataAnnotations;
using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Searches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CognitiveSearch;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Categories;

public interface ICategorySearchService
{
    Task<List<AutocompleteItem>> AutocompleteCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit);

    Task<List<CategoryIndexDto>> SuggestCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit);

    Task<(
            List<CategoryIndexDto> Categories,
            IDictionary<string, IList<FacetResult>> Facets,
            long TotalCount,
            ContinuationTokenDto? NextContinuationToken)>
        SearchCategoriesAsync(
            string sleekflowCompanyId,
            string storeId,
            List<SearchFilterGroup> filterGroups,
            ContinuationTokenDto? continuationToken,
            int limit,
            string? searchText);

    Task IndexCategoriesAsync(List<Category> categories);

    Task DeleteCategoriesAsync(List<Category> categories);

    public class ContinuationTokenDto
    {
        [Required]
        [Range(0, int.MaxValue)]
        [JsonProperty("skip")]
        public int Skip { get; set; }

        [JsonConstructor]
        public ContinuationTokenDto(int skip)
        {
            Skip = skip;
        }
    }
}

public class CategorySearchService : ICategorySearchService, ISingletonService
{
    private static readonly IReadOnlyDictionary<string, SearchFieldDefinition> SearchFieldNameToDefinitionDict =
        new List<SearchFieldDefinition>
            {
                new (
                    Entity.PropertyNameId,
                    "id",
                    SearchFieldTypes.String),
                new (
                    IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                    "sleekflow_company_id",
                    SearchFieldTypes.String),
                new (
                    CommonFieldNames.PropertyNameStoreId,
                    "store_id",
                    SearchFieldTypes.String),
                new (
                    Category.PropertyNameNames,
                    "names/value",
                    SearchFieldTypes.String),
                new (
                    Category.PropertyNameDescriptions,
                    "descriptions/text/value",
                    SearchFieldTypes.String),
                new (
                    CommonFieldNames.PropertyNamePlatformData + "/id",
                    "platform_data/id",
                    SearchFieldTypes.String),
                new (
                    CommonFieldNames.PropertyNamePlatformData + "/type",
                    "platform_data/type",
                    SearchFieldTypes.String),
            }
            .ToDictionary(x => x.FieldName, x => x);

    private readonly ILogger<CategorySearchService> _logger;
    private readonly ISearchFilterBuilder _searchFilterBuilder;
    private readonly SearchClient _searchClient;

    public CategorySearchService(
        ILogger<CategorySearchService> logger,
        ICategorySearchConfig categorySearchConfig,
        ISearchFilterBuilder searchFilterBuilder)
    {
        _logger = logger;
        _searchFilterBuilder = searchFilterBuilder;
        _searchClient = new SearchClient(
            new Uri(categorySearchConfig.SearchClientUri),
            categorySearchConfig.GetIndexName(),
            new AzureKeyCredential(categorySearchConfig.AzureKeyCredential),
            new SearchClientOptions
            {
                Serializer = new NewtonsoftJsonObjectSerializer(
                    NewtonsoftJsonObjectSerializer.CreateJsonSerializerSettings())
            });
    }

    public async Task<List<AutocompleteItem>> AutocompleteCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit)
    {
        var options = new AutocompleteOptions
        {
            Size = limit,
            UseFuzzyMatching = false,
            Mode = AutocompleteMode.TwoTerms,
            Filter = _searchFilterBuilder.BuildFilterStr(
                new List<SearchFilterGroup>(),
                SearchFieldNameToDefinitionDict,
                sleekflowCompanyId,
                storeId)
        };

        var suggestResultsResponse = await _searchClient.AutocompleteAsync(
            searchText,
            "sg",
            options);
        var suggestResults = suggestResultsResponse.Value;

        return suggestResults.Results.ToList();
    }

    public async Task<List<CategoryIndexDto>> SuggestCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit)
    {
        var options = new SuggestOptions
        {
            Size = limit,
            UseFuzzyMatching = true,
            Select =
            {
                "names/value", "descriptions/text/value"
            },
            Filter = _searchFilterBuilder.BuildFilterStr(
                new List<SearchFilterGroup>(),
                SearchFieldNameToDefinitionDict,
                sleekflowCompanyId,
                storeId)
        };

        var suggestResultsResponse = await _searchClient.SuggestAsync<CategoryIndexDto>(
            searchText,
            "sg",
            options);
        var suggestResults = suggestResultsResponse.Value;

        var categories = suggestResults.Results.Select(x => x.Document).ToList();

        return categories;
    }

    public async
        Task<(
            List<CategoryIndexDto> Categories,
            IDictionary<string, IList<FacetResult>> Facets,
            long TotalCount,
            ICategorySearchService.ContinuationTokenDto? NextContinuationToken)> SearchCategoriesAsync(
            string sleekflowCompanyId,
            string storeId,
            List<SearchFilterGroup> filterGroups,
            ICategorySearchService.ContinuationTokenDto? continuationToken,
            int limit,
            string? searchText)
    {
        var skip = continuationToken?.Skip ?? 0;
        var options = new SearchOptions
        {
            Size = limit, Skip = skip, IncludeTotalCount = true
        };

        options.Facets.Add("names/value");
        options.Facets.Add("descriptions/text/value");

        var filter = _searchFilterBuilder.BuildFilterStr(
            filterGroups,
            SearchFieldNameToDefinitionDict,
            sleekflowCompanyId,
            storeId);

        options.Filter = filter;
        options.IncludeTotalCount = true;

        var searchResultsResponse = await _searchClient.SearchAsync<CategoryIndexDto>(
            searchText ?? "*",
            options);
        var searchResults = searchResultsResponse.Value;

        var searchPathToFieldName = SearchFieldNameToDefinitionDict.ToDictionary(k => k.Value.SearchPath, k => k.Key);

        var categories = searchResults.GetResults().Select(x => x.Document).ToList();
        var facets = searchResults.Facets.ToDictionary(k => searchPathToFieldName[k.Key], v => v.Value);
        var totalCount = searchResults.TotalCount!.Value;

        return (
            categories,
            facets,
            totalCount,
            skip + categories.Count == searchResults.TotalCount
                ? null
                : new ICategorySearchService.ContinuationTokenDto(skip + categories.Count));
    }

    public async Task IndexCategoriesAsync(List<Category> categories)
    {
        var categoryIndexDtos = categories.Select(p => new CategoryIndexDto(p)).ToList();
        var batch = IndexDocumentsBatch.Create(
            categoryIndexDtos.Select(IndexDocumentsAction.MergeOrUpload).ToArray());

        try
        {
            IndexDocumentsResult indexDocumentsResult = await _searchClient.IndexDocumentsAsync(batch);

            var failedIndexResults = indexDocumentsResult.Results.Where(r => !r.Succeeded).ToList();
            if (failedIndexResults.Any())
            {
                throw new SfCognitiveSearchException(failedIndexResults);
            }
        }
        catch (SfCognitiveSearchException e)
        {
            _logger.LogWarning(e, "Failed to index the Products {Ids}", e.FailedIndexResults.Select(r => r.Key));
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Failed to index the Products {Ids}", categoryIndexDtos.Select(p => p.Id));
        }
    }

    public async Task DeleteCategoriesAsync(List<Category> categories)
    {
        var categoryIndexDtos = categories.Select(p => new CategoryIndexDto(p)).ToList();
        var batch = IndexDocumentsBatch.Create(
            categoryIndexDtos.Select(IndexDocumentsAction.Delete).ToArray());

        try
        {
            IndexDocumentsResult indexDocumentsResult = await _searchClient.IndexDocumentsAsync(batch);

            var failedIndexResults = indexDocumentsResult.Results.Where(r => !r.Succeeded).ToList();
            if (failedIndexResults.Any())
            {
                throw new SfCognitiveSearchException(failedIndexResults);
            }
        }
        catch (SfCognitiveSearchException e)
        {
            _logger.LogWarning(e, "Failed to un-index the Products {Ids}", e.FailedIndexResults.Select(r => r.Key));
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Failed to un-index the Products {Ids}", categoryIndexDtos.Select(p => p.Id));
        }
    }
}