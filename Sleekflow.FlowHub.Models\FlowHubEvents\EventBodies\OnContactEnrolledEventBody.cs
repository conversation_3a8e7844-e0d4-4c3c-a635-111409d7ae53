using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnContactEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactEnrolled; }
    }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnContactEnrolledEventBody(
        DateTimeOffset createdAt,
        string contactId,
        Dictionary<string, object?> contact,
        string workflowId,
        string workflowVersionedId)
        : base(createdAt)
    {
        ContactId = contactId;
        Contact = contact;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}