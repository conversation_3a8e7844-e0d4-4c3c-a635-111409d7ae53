﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetObjectsCountV2 : ITrigger
{
    private readonly ILogger<GetObjectsCountV2> _logger;
    private readonly IEntityRepository _entityRepository;

    public GetObjectsCountV2(
        ILogger<GetObjectsCountV2> logger,
        IEntityRepository entityRepository)
    {
        _logger = logger;
        _entityRepository = entityRepository;
    }

    public class GetObjectsCountV2InputFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<EntityQueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetObjectsCountV2InputFilterGroup(
            List<EntityQueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetObjectsCountV2Input : IValidatableObject
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [Required]
        [ValidateArray]
        [MaxLength(2)]
        [JsonProperty("group_bys")]
        public List<EntityQueryBuilder.GroupBy> GroupBys { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetObjectsCountV2InputFilterGroup> FilterGroups { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            var crmHubDbEntityService = validationContext.GetRequiredService<ICrmHubDbEntityService>();
            if (!crmHubDbEntityService.IsSupportedEntityTypeName(EntityTypeName))
            {
                results.Add(
                    new ValidationResult(
                        "The TypeName is not supported.",
                        new List<string>
                        {
                            "TypeName"
                        }));
            }

            return results;
        }

        [JsonConstructor]
        public GetObjectsCountV2Input(
            string sleekflowCompanyId,
            string entityTypeName,
            List<EntityQueryBuilder.GroupBy> groupBys,
            List<GetObjectsCountV2InputFilterGroup> filterGroups)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            GroupBys = groupBys;
            FilterGroups = filterGroups;
        }
    }

    public class GetObjectsCountV2Output
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("aggregated_counts")]
        public List<Dictionary<string, object?>> AggregatedCounts { get; set; }

        [JsonConstructor]
        public GetObjectsCountV2Output(
            long count,
            List<Dictionary<string, object?>> aggregatedCounts)
        {
            Count = count;
            AggregatedCounts = aggregatedCounts;
        }
    }

    public async Task<GetObjectsCountV2Output> F(
        GetObjectsCountV2Input getObjectsCountV2Input)
    {
        var filterGroups = getObjectsCountV2Input
            .FilterGroups
            .Select(fg => new EntityQueryBuilder.FilterGroup(fg.Filters.Cast<EntityQueryBuilder.IFilter>().ToList()))
            .ToList();

        var queryDefinition =
            EntityQueryBuilder.BuildQueryDef(
                new List<EntityQueryBuilder.ISelect>
                {
                    new EntityQueryBuilder.PlainSelect("COUNT(1)", "count")
                },
                getObjectsCountV2Input.EntityTypeName,
                filterGroups,
                new List<EntityQueryBuilder.Sort>(),
                getObjectsCountV2Input.GroupBys,
                getObjectsCountV2Input.SleekflowCompanyId);

        _logger.LogInformation(
            "Executing queryText {QueryText}, queryParameters {QueryParameters}",
            queryDefinition.QueryText,
            queryDefinition.GetQueryParameters());

        var rawRecords =
            await _entityRepository.GetObjectsAsync(
                queryDefinition,
                1000);

        if (rawRecords.Count == 0)
        {
            return new GetObjectsCountV2Output(0, new List<Dictionary<string, object?>>());
        }

        if (getObjectsCountV2Input.GroupBys.Count == 0)
        {
            return new GetObjectsCountV2Output(
                (long) rawRecords[0]["count"]!,
                new List<Dictionary<string, object?>>());
        }

        return new GetObjectsCountV2Output(
            rawRecords
                .Select(r => (long) r["count"]!)
                .Sum(),
            rawRecords
                .Select(r => new Dictionary<string, object?>(r))
                .ToList());
    }
}