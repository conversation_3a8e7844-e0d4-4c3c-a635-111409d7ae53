using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class PropagateBatchToProvider
{
    private readonly HttpClient _httpClient;
    private readonly IAppConfig _appConfig;

    public PropagateBatchToProvider(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _appConfig = appConfig;
    }

    public class PropagateBatchToProviderInput
    {
    }

    public class PropagateBatchToProviderOutput
    {
    }

    [Function("Hubspot_PropagateBatchToProvider")]
    public async Task<PropagateBatchToProviderOutput> RunAsync(
        [ActivityTrigger]
        PropagateBatchToProviderInput propagateBatchToProviderInput)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            propagateBatchToProviderInput,
            JsonConfig.DefaultJsonSerializerSettings);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.HubspotIntegratorInternalsEndpoint + "/PropagateBatchToProvider"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<PropagateBatchToProviderOutput>()!;
    }
}