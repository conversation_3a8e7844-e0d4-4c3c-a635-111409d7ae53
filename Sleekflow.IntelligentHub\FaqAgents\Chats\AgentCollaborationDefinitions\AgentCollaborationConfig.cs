using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;

[method: JsonConstructor]
public class AgentCollaborationConfig(
    string tone,
    string restrictivenessLevel,
    string detectedResponseLanguage,
    string? additionalInstructionCore,
    string? additionalInstructionStrategy,
    string? additionalInstructionResponse,
    List<EnricherConfig> enricherConfigs,
    bool enableFollowupAgent = false,
    bool enableFileTranscription = false)
{
    [JsonProperty("tone")]
    public string Tone { get; } = tone;

    [JsonProperty("restrictivenessLevel")]
    public string RestrictivenessLevel { get; } = restrictivenessLevel;

    [JsonProperty("detected_response_language")]
    public string DetectedResponseLanguage { get; set; } = detectedResponseLanguage;

    [JsonProperty("additional_instruction_core")]
    public string? AdditionalInstructionCore { get; } = additionalInstructionCore;

    [JsonProperty("additional_instruction_strategy")]
    public string? AdditionalInstructionStrategy { get; } = additionalInstructionStrategy;

    [JsonProperty("additional_instruction_response")]
    public string? AdditionalInstructionResponse { get; } = additionalInstructionResponse;

    [JsonProperty("enricher_configs")]
    public List<EnricherConfig> EnricherConfigs { get; } = enricherConfigs;

    [JsonProperty("enable_followup_agent")]
    public bool EnableFollowupAgent { get; } = enableFollowupAgent;

    [JsonProperty("enable_file_transcription")]
    public bool EnableFileTranscription { get; } = enableFileTranscription;
}