using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Events;

namespace Sleekflow.Models.WorkflowSteps;

public class GetAgentEvaluateScoreEvent : AgentEventBase
{
    public string SleekflowCompanyId { get; set; }

    public string? AgentId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public string? AdditionalPrompt { get; set; }

    public Dictionary<string, string>? ContactProperties { get; set; }

    public GetAgentEvaluateScoreEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string? agentId,
        List<SfChatEntry> conversationContext,
        string? additionalPrompt = null,
        Dictionary<string, string>? contactProperties = null)
        : base(aggregateStepId, proxyStateId, stackEntries)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        AgentId = agentId;
        ConversationContext = conversationContext;
        AdditionalPrompt = additionalPrompt;
        ContactProperties = contactProperties;
    }

    public class Response
    {
        [JsonProperty("category")]
        public string Category { get; set; }

        [JsonProperty("score")]
        public int Score { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }

        [JsonConstructor]
        public Response(string category, int score, string reason)
        {
            Category = category;
            Score = score;
            Reason = reason;
        }
    }
}