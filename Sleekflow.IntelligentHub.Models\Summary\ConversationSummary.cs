﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Summary;

public class ConversationSummary
{
    [JsonProperty("context")]
    public string Context { get; set; }

    [JsonProperty("handover_reason")]
    public string HandoverReason { get; set; }

    [JsonConstructor]
    public ConversationSummary(
        string context,
        string handoverReason)
    {
        Context = context;
        HandoverReason = handoverReason;
    }
}