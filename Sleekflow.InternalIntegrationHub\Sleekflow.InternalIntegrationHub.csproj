<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Sleekflow.InternalIntegrationHub.Models\Sleekflow.InternalIntegrationHub.Models.csproj" />
      <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Plugins\*\*\*.*">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="13.0.1" />
      <PackageReference Include="Dapper" Version="2.1.66" />
      <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
      <PackageReference Include="RestSharp" Version="112.1.0" />
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>
