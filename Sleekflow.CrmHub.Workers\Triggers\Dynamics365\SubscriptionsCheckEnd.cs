﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Dynamics365;

public class SubscriptionsCheckEnd
{
    private readonly IDynamics365SubscriptionRepository _dynamics365SubscriptionRepository;

    public SubscriptionsCheckEnd(
        IDynamics365SubscriptionRepository dynamics365SubscriptionRepository)
    {
        _dynamics365SubscriptionRepository = dynamics365SubscriptionRepository;
    }

    public class SubscriptionsCheckEndInput
    {
        [JsonProperty("subscription")]
        [Required]
        public Dynamics365Subscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonProperty("last_execution_start_time")]
        [Required]
        public DateTimeOffset LastExecutionStartTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckEndInput(
            Dynamics365Subscription subscription,
            DateTimeOffset? lastObjectModificationTime,
            DateTimeOffset lastExecutionStartTime)
        {
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
            LastExecutionStartTime = lastExecutionStartTime;
        }
    }

    [Function("Dynamics365_SubscriptionsCheck_End")]
    public async Task End(
        [ActivityTrigger]
        SubscriptionsCheckEndInput subscriptionsCheckEndInput)
    {
        var patchOperations = new List<PatchOperation>
        {
            PatchOperation.Replace(
                $"/{Dynamics365Subscription.PropertyNameDurablePayload}",
                (HttpManagementPayload?) null),
            PatchOperation.Replace(
                $"/{Dynamics365Subscription.PropertyNameLastExecutionStartTime}",
                subscriptionsCheckEndInput.LastExecutionStartTime),
        };
        if (subscriptionsCheckEndInput.LastObjectModificationTime != null)
        {
            patchOperations.Add(
                PatchOperation.Replace(
                    $"/{Dynamics365Subscription.PropertyNameLastObjectModificationTime}",
                    subscriptionsCheckEndInput.LastObjectModificationTime));
        }

        await _dynamics365SubscriptionRepository.PatchAsync(
            subscriptionsCheckEndInput.Subscription.Id,
            subscriptionsCheckEndInput.Subscription.SleekflowCompanyId,
            patchOperations);
    }
}