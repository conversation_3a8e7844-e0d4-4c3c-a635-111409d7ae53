using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

public class PdfFileIngestionProgress : IFileIngestionProgress
{
    // cache the PageDescriptionDictionary from the GetPageDescriptions step so we don't redo this every time
    // a map of page_number to page_description
    [JsonProperty("page_description_dictionary")]
    public Dictionary<int, string> PageDescriptionDictionary { get; set; }

    [JsonProperty("processed_pages")]
    public int ProcessedPages { get; set; }

    [JsonProperty("total_page_count")]
    public int TotalPageCount { get; set; }

    [JsonConstructor]
    public PdfFileIngestionProgress(Dictionary<int, string> pageDescriptionDictionary, int processedPages, int totalPageCount)
    {
        PageDescriptionDictionary = pageDescriptionDictionary;
        ProcessedPages = processedPages;
        TotalPageCount = totalPageCount;
    }

    public bool IsCompleted()
    {
        return ProcessedPages == TotalPageCount;
    }

    public double GetProgressPercentage()
    {
        return TotalPageCount == 0 ? 100.0 : ProcessedPages * 100.0 / TotalPageCount;
    }
}