﻿using Sleekflow.CrmHub.Models.Sequences;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Sequences;

public interface ISequenceService
{
    Task<long> GetNextValueAsync(string id, CancellationToken cancellationToken = default);

    Task DeleteAsync(string id, CancellationToken cancellationToken = default);
}

public class SequenceService : ISequenceService, IScopedService
{
    private readonly ILogger<SequenceService> _logger;
    private readonly ISequenceRepository _sequenceRepository;

    public SequenceService(
        ILogger<SequenceService> logger,
        ISequenceRepository sequenceRepository)
    {
        _logger = logger;
        _sequenceRepository = sequenceRepository;
    }

    public async Task<long> GetNextValueAsync(string id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _sequenceRepository.GetNextValueAsync(id, id, cancellationToken);
        }
        catch (SfQueryException)
        {
            await _sequenceRepository.InitializeSequenceSafeAsync(
                new Sequence(
                    id,
                    0,
                    null),
                cancellationToken);
            return await _sequenceRepository.GetNextValueAsync(id, id, cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError("Errors when get sequence next value. {Id} {Message}", id, e.Message);
            throw new SfInternalErrorException("Errors when get sequence next value.");
        }
    }

    public async Task DeleteAsync(string id, CancellationToken cancellationToken = default)
    {
        await _sequenceRepository.DeleteAsync(id, id, cancellationToken: cancellationToken);
    }
}