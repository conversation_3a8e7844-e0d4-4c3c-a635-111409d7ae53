﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Metadatas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Metadatas;

public interface IMetadataRepository : IRepository<Metadata>
{
    Task<int> ExecuteUpsertMetadataStoredProcedureAsync(
        Dictionary<string, object?> dict,
        string sleekflowCompanyId,
        string entityTypeName,
        CancellationToken cancellationToken);
}

public class MetadataRepository
    : BaseRepository<Metadata>,
        IMetadataRepository,
        ISingletonService
{
    private readonly ILogger<BaseRepository<Metadata>> _logger;

    public MetadataRepository(
        ILogger<BaseRepository<Metadata>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
        _logger = logger;
    }

    public async Task<int> ExecuteUpsertMetadataStoredProcedureAsync(
        Dictionary<string, object?> dict,
        string sleekflowCompanyId,
        string entityTypeName,
        CancellationToken cancellationToken)
    {
        var container = GetContainer();
        var retryPolicy = GetRetryPolicy();

        var policyResult = await retryPolicy.ExecuteAndCaptureAsync(
            async _ =>
            {
                var storedProcedureExecuteResponse = await container.Scripts.ExecuteStoredProcedureAsync<int>(
                    "upsert-metadata",
                    new PartitionKey(sleekflowCompanyId),
                    new dynamic[]
                    {
                        dict,
                        entityTypeName,
                        sleekflowCompanyId
                    },
                    cancellationToken: cancellationToken);

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "ExecuteUpsertMetadataStoredProcedureAsync {PartitionKey} {RequestCharge}",
                        sleekflowCompanyId,
                        storedProcedureExecuteResponse.RequestCharge);
                }

                return 1;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to ExecuteUpsertMetadataStoredProcedureAsync the item {Item}",
            dict);

        return 0;
    }
}