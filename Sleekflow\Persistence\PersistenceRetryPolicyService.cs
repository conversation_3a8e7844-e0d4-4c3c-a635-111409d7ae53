﻿using System.Net;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;

namespace Sleekflow.Persistence;

public interface IPersistenceRetryPolicyService
{
    AsyncRetryPolicy GetAsyncRetryPolicy();
}

public class PersistenceRetryPolicyService : IPersistenceRetryPolicyService
{
    private readonly ILogger<PersistenceRetryPolicyService> _logger;
    private readonly AsyncRetryPolicy _asyncRetryPolicy;

    public PersistenceRetryPolicyService(
        ILogger<PersistenceRetryPolicyService> logger)
    {
        _logger = logger;
        _asyncRetryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                9,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(1.37) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    var containerName = context.GetValueOrDefault("containerName");
                    if (containerName != null)
                    {
                        _logger.LogWarning(
                            "TooManyRequests retryCount=[{RetryCount}], timeSpan=[{TimeSpan}], containerName=[{ContainerName}]",
                            retryCount,
                            timeSpan,
                            containerName);
                    }
                    else
                    {
                        _logger.LogWarning(
                            "TooManyRequests retryCount=[{RetryCount}], timeSpan=[{TimeSpan}]",
                            retryCount,
                            timeSpan);
                    }

                    return Task.CompletedTask;
                });
    }

    public AsyncRetryPolicy GetAsyncRetryPolicy()
    {
        return _asyncRetryPolicy;
    }
}