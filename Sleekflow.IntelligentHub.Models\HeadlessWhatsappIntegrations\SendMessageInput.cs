using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.HeadlessWhatsappIntegrations;

[Serializable]
public class SendMessageInput
{
    [JsonProperty(PropertyName = "message_object")]
    public MessageObject? MessageObject { get; set; }
}

[Serializable]
public class MessageObject
{
    [JsonProperty(PropertyName = "recipient_type")]
    public string? RecipientType { get; set; }

    [JsonProperty(PropertyName = "to")]
    public string? To { get; set; }

    [JsonProperty(PropertyName = "type")]
    public string? Type { get; set; }

    [JsonProperty(PropertyName = "text")]
    public Text? Text { get; set; }

    [JsonProperty(PropertyName = "messaging_product")]
    public string? MessagingProduct { get; set; }
}

[Serializable]
public class Text
{
    [JsonProperty(PropertyName = "body")]
    public string? Body { get; set; }

    [JsonProperty(PropertyName = "preview_url")]
    public bool PreviewUrl { get; set; }
}