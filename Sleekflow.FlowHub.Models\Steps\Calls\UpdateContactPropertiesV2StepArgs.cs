﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactPropertiesV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-contact-properties";

    [JsonProperty("properties__id_expr_set")]
    public HashSet<ContactPropertyIdValuePair> PropertiesIdExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactPropertiesV2StepArgs(
        HashSet<ContactPropertyIdValuePair> propertiesIdExprSet)
    {
        PropertiesIdExprSet = propertiesIdExprSet;
    }
}