﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class SubscriptionsCheckBatch
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public SubscriptionsCheckBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public HubspotSubscription Subscription { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            HubspotSubscription subscription,
            string? after,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            EntityTypeName = entityTypeName;
            After = after;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTime LastObjectModificationTime { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            string? after,
            DateTime lastObjectModificationTime)
        {
            Count = count;
            After = after;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    /// <seealso cref="Sleekflow.Integrator.Hubspot.Triggers.Internals.SubscriptionsCheckBatch"/>
    [Function("Hubspot_SubscriptionsCheck_Batch")]
    public async Task<SubscriptionsCheckBatchOutput> Batch(
        [ActivityTrigger]
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(subscriptionsCheckBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.HubspotIntegratorInternalsEndpoint + "/SubscriptionsCheckBatch"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(requestMessage)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();
        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<SubscriptionsCheckBatchOutput>()!;
    }
}