using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Emails;

[TriggerGroup(ControllerNames.Emails)]
public class AuthenticateProvider : ITrigger
{
    private readonly IEmailProviderSelector _providerSelector;

    public AuthenticateProvider(IEmailProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class AuthenticateProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email_address")]
        [Required]
        public string EmailAddress { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("extended_auth_metadata")]
        public Dictionary<string, string>? ExtendedAuthMetadata { get; set; }

        [JsonConstructor]
        public AuthenticateProviderInput(
            string sleekflowCompanyId,
            string emailAddress,
            string providerName,
            Dictionary<string, string>? extendedAuthMetadata)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EmailAddress = emailAddress;
            ProviderName = providerName;
            ExtendedAuthMetadata = extendedAuthMetadata;
        }
    }

    public class AuthenticateProviderOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("email_address")]
        public string EmailAddress { get; set; }

        [JsonProperty("context")]
        public dynamic Context { get; set; }

        [JsonConstructor]
        public AuthenticateProviderOutput(
            string providerName,
            object context,
            string emailAddress)
        {
            ProviderName = providerName;
            Context = context;
            EmailAddress = emailAddress;
        }
    }

    public async Task<AuthenticateProviderOutput> F(
        AuthenticateProviderInput initProviderInput)
    {
        var emailProvider = _providerSelector.GetEmailProvider(initProviderInput.ProviderName);

        var initProviderOutput = await emailProvider.AuthenticateProviderAsync(
            initProviderInput.SleekflowCompanyId,
            initProviderInput.EmailAddress,
            initProviderInput.ExtendedAuthMetadata);

        return new AuthenticateProviderOutput(
            initProviderOutput.ProviderName,
            initProviderOutput.Context,
            initProviderOutput.EmailAddress);
    }
}