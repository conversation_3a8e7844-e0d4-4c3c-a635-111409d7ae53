﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Workers;

public interface IIntelligentHubWorkerService
{
    Task StartFileIngestionAsync(
        string sleekflowCompanyId,
        string documentId,
        string blobId);

    Task StartWebsiteIngestionAsync(
        string sleekflowCompanyId,
        string documentId);

    Task StartUploadToAgentKnowledgeBasesAsync(
        string sleekflowCompanyId,
        string documentId);

    Task StartWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string url,
        string sessionId);
}

public class IntelligentHubWorkerService : IIntelligentHubWorkerService, IScopedService
{
    private readonly ILogger<IntelligentHubWorkerService> _logger;
    private readonly IBlobService _blobService;
    private readonly IBus _bus;

    public IntelligentHubWorkerService(
        ILogger<IntelligentHubWorkerService> logger,
        IBlobService blobService,
        IBus bus)
    {
        _logger = logger;
        _blobService = blobService;
        _bus = bus;
    }

    public async Task StartFileIngestionAsync(
        string sleekflowCompanyId,
        string documentId,
        string blobId)
    {
        _logger.LogInformation("Starting file ingestion for document {DocumentId}", documentId);

        var blobUrl = await GetBlobDownloadSasUrl(sleekflowCompanyId, documentId, blobId);

        await _bus.Publish(
            new StartFileIngestionEvent(
                sleekflowCompanyId,
                documentId,
                blobUrl));
    }

    private async Task<string> GetBlobDownloadSasUrl(string sleekflowCompanyId, string documentId, string blobId)
    {
        _logger.LogInformation(
            "CreateBlobDownloadSasUrls for DocumentId: {DocumentId}, CompanyId: {CompanyId}",
            documentId,
            sleekflowCompanyId);

        var blobName = GetBlobNameFromId(blobId);
        var publicBlobs = await _blobService.CreateBlobDownloadSasUrls(
            sleekflowCompanyId,
            [blobName],
            "File");

        if (publicBlobs.Count != 1)
        {
            throw new InvalidOperationException($"Expected 1 blob, but got {publicBlobs.Count}");
        }

        var blobUrl = publicBlobs[0].Url;

        _logger.LogInformation(
            "CreateBlobDownloadSasUrls completed for DocumentId: {DocumentId}, URL created",
            documentId);
        return blobUrl;
    }

    private string GetBlobNameFromId(string blobId)
    {
        var splitId = blobId.Split('/');
        if (splitId.Length != 2)
        {
            throw new InvalidOperationException($"Invalid blob ID format: {blobId}");
        }

        return splitId[1];
    }

    public async Task StartWebsiteIngestionAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        _logger.LogInformation("Starting website ingestion for document {DocumentId}", documentId);
        await _bus.Publish(
            new StartWebsiteIngestionEvent(
                sleekflowCompanyId,
                documentId));
    }

    public async Task StartUploadToAgentKnowledgeBasesAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        _logger.LogInformation("StartUploadToAgentKnowledgeBasesAsync");
        await _bus.Publish(
            new StartUploadToAgentKnowledgeBasesEvent(
                sleekflowCompanyId,
                documentId));
    }

    public async Task StartWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string url,
        string sessionId)
    {
        _logger.LogInformation("StartWebCrawlingSessionAsync");
        await _bus.Publish(
            new StartWebCrawlingSessionEvent(
                sleekflowCompanyId,
                url,
                sessionId));
    }
}