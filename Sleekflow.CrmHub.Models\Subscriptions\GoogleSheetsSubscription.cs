﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Subscriptions;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("google_sheets_subscription")]
public class GoogleSheetsSubscription : Entity
{
    public const string SysTypeNameValue = "GoogleSheetsSubscription";
    public const string PropertyNameLastExecutionStartTime = "last_execution_start_time";
    public const string PropertyNameLastObjectModificationTime = "last_object_modification_time";
    public const string PropertyNameDurablePayload = "durable_payload";

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [JsonProperty("typed_ids")]
    public List<TypedId> TypedIds { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("interval")]
    public int Interval { get; set; }

    [JsonProperty("is_flows_based")]
    public bool IsFlowsBased { get; set; }

    [JsonProperty(PropertyNameLastExecutionStartTime)]
    public DateTimeOffset LastExecutionStartTime { get; set; }

    [JsonProperty(PropertyNameLastObjectModificationTime)]
    public DateTimeOffset? LastObjectModificationTime { get; set; }

    [JsonProperty(PropertyNameDurablePayload)]
    public DurablePayload? DurablePayload { get; set; }

    [JsonConstructor]
    public GoogleSheetsSubscription(
        string id,
        string sleekflowCompanyId,
        string connectionId,
        List<TypedId> typedIds,
        string entityTypeName,
        int interval,
        bool isFlowsBased,
        DateTimeOffset lastExecutionStartTime,
        DateTimeOffset? lastObjectModificationTime,
        DurablePayload? durablePayload)
        : base(id, SysTypeNameValue)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        TypedIds = typedIds;
        EntityTypeName = entityTypeName;
        Interval = interval;
        IsFlowsBased = isFlowsBased;
        LastExecutionStartTime = lastExecutionStartTime;
        LastObjectModificationTime = lastObjectModificationTime;
        DurablePayload = durablePayload;
    }
}