﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateHubspotObjectStepExecutor : IStepExecutor
{
}

public class UpdateHubspotObjectStepExecutor
    : GeneralStepExecutor<CallStep<UpdateHubspotObjectStepArgs>>,
        IUpdateHubspotObjectStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public UpdateHubspotObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var updateHubspotObjectInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new UpdateHubspotObjectRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    updateHubspotObjectInput.StateIdentity.SleekflowCompanyId,
                    updateHubspotObjectInput.ObjectId,
                    updateHubspotObjectInput.ConnectionId,
                    updateHubspotObjectInput.EntityTypeName,
                    updateHubspotObjectInput.FieldsDict));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnHubspotFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Hubspot update object operation timed out after 5 minutes")),
                typeof(OnHubspotFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateHubspotObjectInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("fields_dict")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> FieldsDict { get; set; }

        [JsonConstructor]
        public UpdateHubspotObjectInput(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string entityTypeName,
            string objectId,
            Dictionary<string, object?> fieldsDict)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
            FieldsDict = fieldsDict;
        }
    }

    private async Task<UpdateHubspotObjectInput> GetArgs(
        CallStep<UpdateHubspotObjectStepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var entityTypeName = callStep.Args.EntityTypeName;

        string? objectId;
        var objectIdSource = callStep.Args.ObjectIdSource;
        if (objectIdSource == "static")
        {
            if (callStep.Args.ObjectId is null)
            {
                throw new InvalidOperationException("No static object Id provided");
            }

            objectId = callStep.Args.ObjectId;
        }
        else if (objectIdSource == "variable")
        {
            if (callStep.Args.ObjectIdExpr is null)
            {
                throw new InvalidOperationException("No variable object Id provided");
            }

            objectId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectIdExpr)
                                 ?? throw new InvalidOperationException("Invalid variable object Id"));
        }
        else
        {
            throw new InvalidOperationException(
            $"Invalid object Id source {objectIdSource} provided");
        }

        var fieldsKeyExprDict = callStep.Args.FieldsNameExprSet
            .GroupBy(x => x.FieldName)
            .ToDictionary(
                x => x.Key,
                g => g.Last().FieldValueExpr);

        var fieldsDict = new Dictionary<string, object?>();

        foreach (var entry in fieldsKeyExprDict)
        {
            fieldsDict[entry.Key] = string.IsNullOrWhiteSpace(entry.Value)
                ? null
                : await _stateEvaluator.EvaluateExpressionAsync(state, entry.Value);
        }

        return new UpdateHubspotObjectInput(
            state.Id,
            state.Identity,
            connectionId,
            entityTypeName,
            objectId!,
            fieldsDict);
    }
}