using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class UpdateWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowStepValidator _workflowStepValidator;

    public UpdateWorkflow(
        IWorkflowService workflowService,
        IWorkflowStepValidator workflowStepValidator)
    {
        _workflowService = workflowService;
        _workflowStepValidator = workflowStepValidator;
    }

    public class UpdateWorkflowInput : IHasSleekflowStaff, IHasMetadata, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [ValidateObject]
        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings? WorkflowEnrollmentSettings { get; set; }

        [ValidateObject]
        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings? WorkflowScheduleSettings { get; set; }

        [Required]
        [MaxLength(1024)]
        [MinLength(1)]
        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

#pragma warning disable JA1001
#pragma warning disable S2365
        [JsonIgnore]
        [ValidateArray]
        [Required]
        public List<Step> InternalSteps
        {
            get
            {
                return Steps
                    .Select(step => step.ToObject<Step>()!)
                    .ToList();
            }
        }
#pragma warning restore JA1001
#pragma warning restore S2365

        [Required]
        [MaxLength(100)]
        [MinLength(1)]
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_group_id")]
        public string? WorkflowGroupId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [Required]
        [JsonProperty("metadata")]
        [ValidateObject]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonProperty("manual_enrollment_source")]
        public string? ManualEnrollmentSource { get; set; }

        [JsonConstructor]
        public UpdateWorkflowInput(
            string sleekflowCompanyId,
            string workflowId,
            WorkflowTriggers triggers,
            WorkflowEnrollmentSettings? workflowEnrollmentSettings,
            WorkflowScheduleSettings? workflowScheduleSettings,
            List<JObject> steps,
            string name,
            string? workflowGroupId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            Dictionary<string, object?> metadata,
            string? manualEnrollmentSource)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            Triggers = triggers;
            WorkflowEnrollmentSettings = workflowEnrollmentSettings;
            WorkflowScheduleSettings = workflowScheduleSettings;
            Steps = steps;
            Name = name;
            WorkflowGroupId = workflowGroupId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            Metadata = metadata;
            ManualEnrollmentSource = manualEnrollmentSource;
        }
    }

    public class UpdateWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public UpdateWorkflowOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<UpdateWorkflowOutput> F(UpdateWorkflowInput updateWorkflowInput)
    {
        await _workflowStepValidator.AssertAllStepsAreValidAsync(
            updateWorkflowInput.InternalSteps,
            updateWorkflowInput.WorkflowScheduleSettings?.ScheduleType,
            updateWorkflowInput.WorkflowScheduleSettings?.IsNewScheduledWorkflowSchema);

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateWorkflowInput.SleekflowStaffId,
            updateWorkflowInput.SleekflowStaffTeamIds);

        var workflow = await _workflowService.UpdateWorkflowAsync(
            updateWorkflowInput.WorkflowId,
            updateWorkflowInput.SleekflowCompanyId,
            updateWorkflowInput.Triggers,
            updateWorkflowInput.WorkflowEnrollmentSettings,
            updateWorkflowInput.WorkflowScheduleSettings,
            updateWorkflowInput.InternalSteps,
            updateWorkflowInput.Name,
            updateWorkflowInput.WorkflowGroupId,
            sleekflowStaff,
            updateWorkflowInput.Metadata,
            updateWorkflowInput.ManualEnrollmentSource);

        return new UpdateWorkflowOutput(new WorkflowDto(workflow));
    }
}