using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IAzureTextTranslatorConfig
{
    string Key { get; }
}

public class AzureTextTranslatorConfig : IConfig, IAzureTextTranslatorConfig
{
    /// <summary>
    /// Global key to access the Azure Translator Service.
    /// </summary>
    public string Key { get; }

    public AzureTextTranslatorConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Key =
            Environment.GetEnvironmentVariable("AZURE_TEXT_TRANSLATOR_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURE_TEXT_TRANSLATOR_KEY");
    }
}