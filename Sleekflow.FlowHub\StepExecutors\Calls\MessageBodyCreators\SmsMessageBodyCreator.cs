using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface ISmsMessageBodyCreator : IMessageBodyCreator
{
}

public class SmsMessageBodyCreator : BaseMessageBodyCreator, ISmsMessageBodyCreator
{
    public SmsMessageBodyCreator()
        : base(ChannelTypes.SMS)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        var smsMessage = new SmsMessageObject(messageStr);
        return Task.FromResult((CreateBaseMessageBody(smsMessage: smsMessage), "text"));
    }
}