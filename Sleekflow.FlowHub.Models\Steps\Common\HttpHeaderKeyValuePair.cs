using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Steps.Common;

public class HttpHeaderKeyValuePair
{
    [JsonProperty("header_key")]
    public string Header<PERSON>ey { get; set; }

    [JsonProperty("header_value__expr")]
    public string? HeaderValueExpr { get; set; }

    [JsonConstructor]
    public HttpHeaderKeyValuePair(
        string headerKey,
        string? headerValueExpr)
    {
        HeaderKey = headerKey;
        HeaderValueExpr = headerValueExpr;
    }
}
