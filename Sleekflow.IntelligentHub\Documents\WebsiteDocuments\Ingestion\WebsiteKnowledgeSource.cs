using System.Collections.Concurrent;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using HtmlAgilityPack;
using Microsoft.Playwright;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Knowledges;

namespace Sleekflow.IntelligentHub.Documents.WebsiteDocuments.Ingestion;

public interface IWebsiteKnowledgeSource
{
    Task<string> Ingest(string url);
}

public class WebsiteKnowledgeSource : IWebsiteKnowledgeSource, IScopedService
{
    private readonly ILogger<WebsiteKnowledgeSource> _logger;
    private readonly Kernel _kernel;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly ConcurrentDictionary<string, string> _imageMarkdownCache = new ();
    private readonly HttpClient _httpClient; // Reuse HttpClient for better performance

    public WebsiteKnowledgeSource(
        ILogger<WebsiteKnowledgeSource> logger,
        Kernel kernel,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _kernel = kernel;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<string> Ingest(string url)
    {
        // 1. Use playwright to get the html and page screenshot
        // var (html, screenshotBytes) = await ScrapeWebPageAsync(url);
        var html = await ScrapeWebPageNoPlaywrightAsync(url);

        // 2. Convert the html to markdown
        var markdown = await ConvertHtmlToMarkdownAsync(url, html, null /*screenshotBytes*/);
        if (string.IsNullOrEmpty(markdown))
        {
            throw new Exception($"markdown is empty");
        }

        return markdown;
    }

    private async Task<(string Html, byte[] ScreenshotBytes)> ScrapeWebPageAsync(string url)
    {
        using var playwright = await Playwright.CreateAsync();
        await using var browser = await playwright.Chromium.LaunchAsync(
            new BrowserTypeLaunchOptions
            {
                Headless = true,
            });

        var page = await browser.NewPageAsync(
            new BrowserNewPageOptions()
            {
                JavaScriptEnabled = true
            });

        var response = await page.GotoAsync(
            url,
            new PageGotoOptions()
            {
                Timeout = 30000
            });

        if (response == null || response.Status != 200)
        {
            throw new Exception($"Failed to load page {url}, status: {response?.Status}");
        }

        // Get HTML content
        var html = await page.ContentAsync();

        // Take screenshot
        byte[] screenshotBytes = await page.ScreenshotAsync(
            new PageScreenshotOptions
            {
                Type = ScreenshotType.Jpeg, FullPage = true, Timeout = 10000
            });

        await page.CloseAsync();
        await browser.CloseAsync();

        return (html, screenshotBytes);
    }

    private async Task<string> ScrapeWebPageNoPlaywrightAsync(string url)
    {
        try
        {
            var response = await _httpClient.GetAsync(url);

            if (response.StatusCode != System.Net.HttpStatusCode.OK)
            {
                throw new Exception($"Failed to load page {url}, status: {response.StatusCode}");
            }

            var html = await response.Content.ReadAsStringAsync();

            if (string.IsNullOrWhiteSpace(html))
            {
                throw new Exception($"Empty response received from {url}");
            }

            return html;
        }
        catch (HttpRequestException ex)
        {
            throw new Exception($"HTTP request failed for {url}: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            throw new Exception($"Request timeout for {url}: {ex.Message}", ex);
        }
    }

    private async Task<string> ConvertHtmlToMarkdownAsync(string url, string html, byte[]? screenshotBytes)
    {
        const int maxRetryCount = 5;
        var retryCount = maxRetryCount;
        while (retryCount > 0)
        {
            try
            {
                var htmlContent = html;
                var cleanHtmlStr = CleanHtml(htmlContent);

                var doc = new HtmlDocument();
                doc.LoadHtml(cleanHtmlStr);

                await RemoveNavigationalElementsFromHtml(screenshotBytes, cleanHtmlStr, doc);

                var tableMarkdowns = await ProcessTableNodes(screenshotBytes, url, doc);

                await ProcessImgNodes(url, doc);

                var chunkResults = await SemanticChunkHtml(doc.DocumentNode.OuterHtml);

                _logger.LogInformation("Identified {ChunkCount} chunks for URL {Url}", chunkResults.Length, url);

                var allChunks = new ConcurrentDictionary<int, string>();
                await Parallel.ForEachAsync(
                    Enumerable.Range(0, chunkResults.Length),
                    new ParallelOptions
                    {
                        MaxDegreeOfParallelism = 8
                    },
                    async (index, token) =>
                    {
                        var chunkResult = chunkResults[index];
                        var chunkNode = doc.DocumentNode.SelectNodes(chunkResult.ChunkXPath)?.First();
                        if (chunkNode != null)
                        {
                            _logger.LogInformation(
                                "Processing chunk element {ChunkXPath} for URL {Url}",
                                chunkResult.ChunkXPath,
                                url);
                            var chunkHtml = chunkNode.OuterHtml;
                            var markdown = await ConvertToMarkdownAsync(
                                chunkHtml,
                                chunkResult.ChunkContext);

                            if (!string.IsNullOrWhiteSpace(markdown))
                            {
                                var verifyMarkdownResults = await VerifyMarkdown(
                                    chunkHtml,
                                    screenshotBytes,
                                    markdown);
                                if (verifyMarkdownResults.Score < 70)
                                {
                                    throw new Exception(
                                        $"Verification failed: {url} - {chunkResult.ChunkXPath} - {verifyMarkdownResults.Reason} - {markdown}");
                                }

                                // Remove the node from the parent by replacing with an empty div,
                                // somehow node.Remove() does not update the HTML
                                chunkNode.ParentNode.ReplaceChild(HtmlNode.CreateNode("<div/>"), chunkNode);

                                allChunks[index] = markdown;
                            }
                        }
                        else
                        {
                            _logger.LogWarning(
                                "Cannot find node at {ChunkXPath} for URL {Url}",
                                chunkResult.ChunkXPath,
                                url);
                        }
                    });

                // just in case semantic chunking misses some nodes, we pass what's left as the final chunk
                var finalChunkMarkdown = await ConvertToMarkdownAsync(
                    doc.DocumentNode.OuterHtml,
                    string.Empty);
                if (!string.IsNullOrWhiteSpace(finalChunkMarkdown))
                {
                    allChunks[chunkResults.Length] = finalChunkMarkdown;

                    // only verify the last chunk
                    var verifyMarkdownResults = await VerifyMarkdown(
                        doc.DocumentNode.OuterHtml,
                        screenshotBytes,
                        finalChunkMarkdown);
                    if (verifyMarkdownResults.Score < 70)
                    {
                        throw new Exception(
                            $"Verification failed: {url} - {verifyMarkdownResults.Reason}");
                    }
                }

                var finalMarkdownBuilder = new StringBuilder();
                for (var i = 0; i < chunkResults.Length + 1; i++)
                {
                    if (!allChunks.ContainsKey(i))
                    {
                        continue;
                    }

                    finalMarkdownBuilder.AppendLine(allChunks[i]);
                    finalMarkdownBuilder.AppendLine();
                }

                var finalMarkdown = $"# This is a markdown for the website at {url}\n\n" +
                                    finalMarkdownBuilder.ToString();

                // Combine with table markdowns
                if (tableMarkdowns.Any())
                {
                    finalMarkdown += "\n\n" + string.Join("\n\n", tableMarkdowns);
                }

                return finalMarkdown;
            }
            catch (Exception e)
            {
                _logger.LogWarning(
                    e,
                    "Attempt {Attempt} failed for URL {Url}: {Error}",
                    maxRetryCount - retryCount + 1,
                    url,
                    e.Message);
                retryCount--;

                if (retryCount == 0)
                {
                    _logger.LogError(
                        "Failed to process {Url} after {MaxRetryCount} attempts. Last error: {Message}",
                        url,
                        maxRetryCount,
                        e.Message);
                    break;
                }
            }
        }

        _logger.LogInformation(
            "Website to markdown failed, fallback to simple text extraction {Url}",
            url);

        // use the same extraction technique in real time web crawling
        var textContent = WebPageContentPlugin.ExtractTextContent(html);

        _logger.LogInformation(
            "Text extraction results: {TextContent}",
            textContent);

        return textContent;
    }

    private string CleanHtml(string htmlContent)
    {
        var doc = new HtmlDocument();
        doc.LoadHtml(htmlContent);

        // Remove all style tags
        var styleTags = doc.DocumentNode.SelectNodes("//style");
        if (styleTags != null)
        {
            foreach (var style in styleTags)
            {
                style.Remove();
            }
        }

        // Remove all script tags
        var scriptTags = doc.DocumentNode.SelectNodes("//script");
        if (scriptTags != null)
        {
            foreach (var script in scriptTags)
            {
                script.Remove();
            }
        }

        // Remove all link tags
        var linkTags = doc.DocumentNode.SelectNodes("//link");
        if (linkTags != null)
        {
            foreach (var tag in linkTags)
            {
                tag.Remove();
            }
        }

        // Remove all noscript tags
        var noscriptTags = doc.DocumentNode.SelectNodes("//noscript");
        if (noscriptTags != null)
        {
            foreach (var tag in noscriptTags)
            {
                tag.Remove();
            }
        }

        // Remove meta, link, and unnecessary class attributes
        var unnecessaryTags = doc.DocumentNode.SelectNodes("//meta|//link");
        if (unnecessaryTags != null)
        {
            foreach (var tag in unnecessaryTags)
            {
                tag.Remove();
            }
        }

        // Remove all style attributes
        var nodesWithStyle = doc.DocumentNode.SelectNodes("//@style");
        if (nodesWithStyle != null)
        {
            foreach (var node in nodesWithStyle)
            {
                node.Attributes["style"].Remove();
            }
        }

        // Clean up whitespace and format the output
        string cleanedHtml = doc.DocumentNode.OuterHtml;
        cleanedHtml = Regex.Replace(cleanedHtml, @"\s{2,}", " ");
        cleanedHtml = Regex.Replace(cleanedHtml, @">\s+<", "><");
        // Removing Whitespace Around Tags
        cleanedHtml = Regex.Replace(cleanedHtml, @"(?<=>)\s+|\s+(?=<)", "");

        return cleanedHtml;
    }

    private async Task<string> ConvertToMarkdownAsync(string html, string context)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, false, 0.2f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are tasked to convert websites into markdown documents

            Each website is provided in the form of a HTML document.
            The user may also optionally give a description for the HTML, use that to supplement your understanding of the HTML.

            1. Text conversion:
            - Your goal is to convert the text in the website into natural paragraphs.
            - Text in websites can be incoherent. You should convert all text into natural sentences based on the semantic meaning of the website.
            - Convert the HTML to markdown by extracting all the textual information with no information loss.

            2. Image extraction:
            - For image <img> elements, their alt attribute contains the image description.
            - If the alt attribute is missing or is an empty string, you should skip and omit the image completely.
            - Each <img> element should be converted to markdown image syntax. i.e. ![image caption](image url)
            - Some images are mere illustrations and not very important. Use your judgement to decide how much detail to include from the image.
            - If the image description contains markdown tables, and you think it is important, you should make sure the table is unchanged and copied into the final markdown.
                -- It should maintain the markdown table syntax.
            - Each img element should only be included no more than once.

            3. Special Considerations:
            - Make the whole markdown reads like a coherent document.
            - When encountering links in the main document content, preserve the exact URL
            - Treat the website as public information. Do NOT withhold information for any reason
            - Preserve all numerical data, measurements, and specific values exactly
            - Preserve all numbered lists, bullet points etc. Make sure every line is included.
            - Maintain the website's logical flow and hierarchy for text and images
            - You can ignore and remove the mobile layout, we only care about the website as seen on desktop

            Output format:
            ```markdown
                {The markdown output}
            ```

            Output language:
            - The markdown should use the same language as the HTML document.
            """);

        if (!string.IsNullOrWhiteSpace(context))
        {
            chatHistory.AddUserMessage($"Website description: {context}");
        }

        chatHistory.AddUserMessage(html);
        chatHistory.AddAssistantMessage(
            "Here's the markdown conversion of the provided HTML, focusing on extracting the text and image information:");

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeMarkdownOutput.Content == null)
        {
            throw new Exception("Chat completion failed.");
        }

        var result = Regex.Replace(completeMarkdownOutput.Content.Trim(), "```(markdown)?", "");

        if (result == null)
        {
            throw new Exception("Chat completion failed.");
        }

        return result.Trim();
    }

    private async Task RemoveNavigationalElementsFromHtml(
        byte[]? screenshotBytes,
        string cleanHtmlStr,
        HtmlDocument doc)
    {
        var navigationalElementsXPaths =
            await GetXPathQueriesForNavigationalElements(
                screenshotBytes,
                cleanHtmlStr);

        if (navigationalElementsXPaths.HeaderXpath != null)
        {
            var headerNode = doc.DocumentNode.SelectNodes(navigationalElementsXPaths.HeaderXpath)?.First();
            // Remove the header node from the parent by replacing with an empty div,
            // somehow headerNode.Remove() does not update the HTML
            headerNode?.ParentNode.ReplaceChild(HtmlNode.CreateNode("<div/>"), headerNode);
        }

        if (navigationalElementsXPaths.FooterXpath != null)
        {
            var footerNode = doc.DocumentNode.SelectNodes(navigationalElementsXPaths.FooterXpath)?.First();
            // Remove the footer node from the parent by replacing with an empty div,
            // somehow footerNode.Remove() does not update the HTML
            footerNode?.ParentNode.ReplaceChild(HtmlNode.CreateNode("<div/>"), footerNode);
        }
    }

    private async Task<GetNavigationElementsResult> GetXPathQueriesForNavigationalElements(
        byte[]? fullPageScreenshotBytes,
        string websiteHtml)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true, 0.1f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are tasked to identify navigational elements in the website and return the XPath selectors to those elements.

            The user will supply:
            - the HTML of the webpage. It may not necessarily use semantic HTML.
            - (Optional) the screenshot of the webpage
            You should use both to build a complete understanding of the website layout

            You should identify the following elements in the website based on your understanding of the website layout:
            - Header
            - Footer

            XPath instructions:
            - The XPath should point to the root of those elements
            - Always use positional predicate
            - Use relative XPath
            - Make sure the XPath selector points to a valid element in the HTML

            Output Format:
            ```json
            {
                "header_xpath": string?
                "footer_xpath": string?
            }
            ```

            header_xpath: (Optional) XPath pointing to the root of the website header
            footer_xpath: (Optional) XPath pointing to the root of the website footer
            """);
        if (fullPageScreenshotBytes != null)
        {
            chatHistory.AddUserMessage(
            [
                new ImageContent(fullPageScreenshotBytes, "image/jpeg"),
                new TextContent(websiteHtml)
            ]);
        }
        else
        {
            chatHistory.AddUserMessage(
            [
                new TextContent(websiteHtml)
            ]);
        }

        var completion = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completion.Content == null)
        {
            throw new Exception("Navigation elements identification failed.");
        }

        var output = JsonConvert.DeserializeObject<GetNavigationElementsResult>(
            Regex.Replace(completion.Content.Trim(), "```(json)?", ""));

        if (output == null)
        {
            throw new Exception($"Chat completion failed. {completion.Content}");
        }

        return output;
    }

    private async Task<string[]> ProcessTableNodes(
        byte[]? fullPageScreenshotBytes,
        string baseUrl,
        HtmlDocument doc)
    {
        var tableXPaths = await GetXPathQueryForTables(fullPageScreenshotBytes, doc.DocumentNode.OuterHtml);

        // if any table fails, fail the whole processing workflow
        var tableMarkdowns = new ConcurrentBag<string>();
        await Parallel.ForEachAsync(
            tableXPaths.Tables,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 2
            },
            async (table, token) =>
            {
                var tableNode = doc.DocumentNode.SelectNodes(table.Xpath)?.First();
                if (tableNode != null)
                {
                    _logger.LogInformation("Processing table element {TableXPath} for URL {Url}", table.Xpath, baseUrl);

                    try
                    {
                        // some tables cannot produce screenshot, they may be collapsed tables
                        // var tableScreenshotBytes = await TakeTableElementsScreenshotAsync(baseUrl, tableNode.XPath);
                        byte[]? tableScreenshotBytes = null;

                        var tableHtml = tableNode.OuterHtml;
                        var markdown = await ConvertTableToMarkdownAsync(
                            table,
                            tableScreenshotBytes,
                            tableHtml);

                        // Remove the table node from the parent by replacing with an empty div,
                        // somehow tableNode.Remove() does not update the HTML
                        tableNode.ParentNode.ReplaceChild(HtmlNode.CreateNode("<div/>"), tableNode);

                        tableMarkdowns.Add(markdown);
                    }
                    catch (FormatException ex)
                    {
                        _logger.LogInformation("Table is empty, skipping {TableXPath}", table.Xpath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(
                            ex,
                            "Failed to process table element {TableXPath} for URL {Url}: {Error}",
                            table.Xpath,
                            baseUrl,
                            ex.Message);
                        throw;
                    }

                    _logger.LogInformation("Processed table element {TableXPath} for URL {Url}", table.Xpath, baseUrl);
                }
            });

        return tableMarkdowns.ToArray();
    }

    private async Task<byte[]?> TakeTableElementsScreenshotAsync(string baseUrl, string xPathSelector)
    {
        try
        {
            using var playwright = await Playwright.CreateAsync();
            await using var browser = await playwright.Chromium.LaunchAsync(
                new ()
                {
                    Headless = true
                });
            var page = await browser.NewPageAsync(
                new BrowserNewPageOptions()
                {
                    JavaScriptEnabled = true
                });
            await page.GotoAsync(baseUrl);

            // Playwright requires xpath= prefix
            // https://playwright.dev/docs/other-locators#xpath-locator
            var table = page.Locator("xpath=" + xPathSelector).First;
            var tableImageBytes = await table.ScreenshotAsync(
                new LocatorScreenshotOptions()
                {
                    Type = ScreenshotType.Jpeg, Timeout = 10000
                });

            await page.CloseAsync();
            await browser.CloseAsync();
            return tableImageBytes;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to take table element screenshot {XPathSelector}: {Error}",
                xPathSelector,
                ex.Message);
            return null;
        }
    }

    private async Task<string> ConvertTableToMarkdownAsync(
        Table table,
        byte[]? tableScreenshotBytes,
        string htmlTable)
    {
        var retryCount = 3;
        while (retryCount > 0)
        {
            // Splitting to multiple prompts provide much better results
            var markdownTable = await ConvertHtmlTableToMarkdownAsync(htmlTable, tableScreenshotBytes);
            var verifyMarkdownResults = await VerifyTableMarkdownAsync(htmlTable, markdownTable);
            if (verifyMarkdownResults.Score < 70)
            {
                _logger.LogInformation("Table markdown verification failed: {Reason}", verifyMarkdownResults.Reason);
                retryCount--;
                continue;
            }

            if (tableScreenshotBytes == null)
            {
                return await ConvertMarkdownTableToSentencesAsync(table, markdownTable);
            }

            var enrichedTable = await EnrichMarkdownTableWithImageAsync(markdownTable, tableScreenshotBytes);
            return await ConvertMarkdownTableToSentencesAsync(table, enrichedTable);
        }

        throw new Exception($"Failed to convert table to markdown.");
    }

    private async Task<string> ConvertHtmlTableToMarkdownAsync(
        string htmlTable,
        byte[]? tableScreenshotBytes)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, false, 0.2f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            $$"""
              You are an expert HTML table to markdown converter. Your primary role is to convert HTML tables to markdown tables.

              The user will provide the HTML code snippet that contains a grid-like/table layout.

              Core responsibilities:
              - Extract all textual information from the HTML code and convert to markdown tables without any information loss, copy information as-is
              - Use the provided image to aid your understanding of the table layout
              - Multiple tables may exist in the HTML code snippet, in that case convert each into a separate markdown table

              Output Format:
              ```markdown
                  {The markdown output}
              ```

              Output language:
              - The markdown should use the same language as the HTML table.

              The final output should be a complete, accurate representation of the HTML table in valid markdown format
              """);

        // Example prompt
        chatHistory.AddUserMessage(
            """
            <div class="accordion">
              <div class="accordion_group expanded">
                <div class="accordion_header"><span class="accordion_icon"></span>General Information</div>
                <div class="accordion_content" style="display: block;">
                  <ul>

                      <li>
                        <span class="package-option-text">Hospital Type</span>
                        <span class="package-option-label">Private</span>
                      </li>

                      <li>
                        <span class="package-option-text">Hospital Booking</span>
                        <span class="package-option-label"><i class="far fa-check"></i>+</span>
                      </li>

                      <li>
                        <span class="package-option-text">On-Call Phone Support with Midwives</span>
                        <span class="package-option-label">Optional</span>
                      </li>

                  </ul>
                </div>
              </div>
            </div>
            """);

        chatHistory.AddAssistantMessage(
            """
            ```markdown
            | **General Information** | |
            | --- | --- |
            | Hospital Type | Private |
            | Hospital Booking | <i class="far fa-check"></i>+ |
            | On-Call Phone Support with Midwives | Optional |
            ```
            """);

        // Second example
        chatHistory.AddUserMessage(
            """
            <section class="mx-auto px-6 w-full md:max-w-screen-2xl lg:px-12 pt-3 md:pt-12"><div class="grid gap-5 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"><div class="flex flex-col border rounded-lg border-transparent"><div class="h-[30px] font-[Matter] tracking-[1.5px] opacity-0">MOST POPULAR</div><div class="pricing-card-shadow rounded-xl h-[100%] px-4 py-10"><p class="plancard-pro text-2xl md:text-xl font-semibold">Pro</p><div class="h-[225px]"><div class="flex items-center pt-3 h-[50px]"><span class="text-3xl font-semibold text-[#0D122C] tracking-tight">HK$<!-- --><!-- -->1,199</span>&nbsp;<span class="text-md font-light text-[#4D4D4F]">per month</span></div><div class="pt-4 h-[45px]"><p class="text-secondary whitespace-pre-line">For small teams to collaborate better on chats</p></div><div class="pt-[40px]"><a class="button outlined primary mobile-full-width" target="_blank" rel="noopener noreferrer" href="/book-a-demo"><span class="btn-inner-text"><span class="typography-button1">Book a Demo</span></span></a><div class="flex items-center h-[50px] pt-3"><svg width="15" height="13" viewbox="0 0 15 13" fill="none" xmlns="http://www.w3.org/2000/svg" class="me-2" color="#13B22E"><path d="M13.75 9.625V7.125C13.75 3.67322 10.9518 0.875 7.5 0.875C4.04822 0.875 1.25 3.67322 1.25 7.125V9.625M4.6875 12.125C3.82456 12.125 3.125 11.4254 3.125 10.5625V8.6875C3.125 7.82456 3.82456 7.125 4.6875 7.125C5.55044 7.125 6.25 7.82456 6.25 8.6875V10.5625C6.25 11.4254 5.55044 12.125 4.6875 12.125ZM10.3125 12.125C9.44956 12.125 8.75 11.4254 8.75 10.5625V8.6875C8.75 7.82456 9.44956 7.125 10.3125 7.125C11.1754 7.125 11.875 7.82456 11.875 8.6875V10.5625C11.875 11.4254 11.1754 12.125 10.3125 12.125Z" stroke="#13B22E" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path></svg><p class="plancard-pro text-lg font-semibold">Free onboarding support</p></div></div></div><div class="pt-3"><p class="font-semibold">Includes</p><div class="h-[100%] md:h-[220px]"><div class="flex pt-5"><div class="pe-4"><svg width="25" height="24" viewbox="0 0 25 24" fill="#4E95FF" xmlns="http://www.w3.org/2000/svg" class="w-[20px] pb-[4px]"><path d="M21.17 5.54 8.72 17.99 3.15 12.417A1.844 1.844 0 1 0 .54 15.025l6.877 6.877a1.844 1.844 0 0 0 2.608 0L23.778 8.149a1.844 1.844 0 1 0-2.608-2.608Z"></path></svg></div><p class="text-[#4D4D4F]">3 user accounts</p></div><div class="flex pt-5"><div class="pe-4"><svg width="25" height="24" viewbox="0 0 25 24" fill="#4E95FF" xmlns="http://www.w3.org/2000/svg" class="w-[20px] pb-[4px]"><path d="M21.17 5.54 8.72 17.99 3.15 12.417A1.844 1.844 0 1 0 .54 15.025l6.877 6.877a1.844 1.844 0 0 0 2.608 0L23.778 8.149a1.844 1.844 0 1 0-2.608-2.608Z"></path></svg></div><p class="text-[#4D4D4F]">2,000 contacts</p></div><div class="flex pt-5"><div class="pe-4"><svg width="25" height="24" viewbox="0 0 25 24" fill="#4E95FF" xmlns="http://www.w3.org/2000/svg" class="w-[20px] pb-[4px]"><path d="M21.17 5.54 8.72 17.99 3.15 12.417A1.844 1.844 0 1 0 .54 15.025l6.877 6.877a1.844 1.844 0 0 0 2.608 0L23.778 8.149a1.844 1.844 0 1 0-2.608-2.608Z"></path></svg></div><p class="text-[#4D4D4F]">3 Flow Builder active flows</p></div><div class="flex pt-5"><div class="pe-4"><svg width="25" height="24" viewbox="0 0 25 24" fill="#4E95FF" xmlns="http://www.w3.org/2000/svg" class="w-[20px] pb-[4px]"><path d="M21.17 5.54 8.72 17.99 3.15 12.417A1.844 1.844 0 1 0 .54 15.025l6.877 6.877a1.844 1.844 0 0 0 2.608 0L23.778 8.149a1.844 1.844 0 1 0-2.608-2.608Z"></path></svg></div><p class="text-[#4D4D4F]">Support via email</p></div></div><div class="border-b border-[#E8F1FF] pt-5"></div><div><p class="font-semibold pt-9 h-[60px]">Key features</p><div><div class="flex pt-3"><div class="pe-4"><div class="w-[20px] flex justify-center"><svg width="12" height="13" viewbox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[16px] h-[16px] mt-[7px]"><path d="M2.02884 9.73075V9.62787C2.02884 8.76356 2.02884 8.3314 2.19705 8.00128C2.34501 7.71089 2.5811 7.4748 2.87148 7.32685C3.2016 7.15864 3.63376 7.15864 4.49807 7.15864H7.79037C8.65468 7.15864 9.08683 7.15864 9.41696 7.32685C9.70734 7.4748 9.94343 7.71089 10.0914 8.00128C10.2596 8.3314 10.2596 8.76356 10.2596 9.62787V9.73075M2.02884 9.73075C1.46063 9.73075 1 10.1914 1 10.7596C1 11.3278 1.46063 11.7884 2.02884 11.7884C2.59706 11.7884 3.05769 11.3278 3.05769 10.7596C3.05769 10.1914 2.59706 9.73075 2.02884 9.73075ZM10.2596 9.73075C9.69138 9.73075 9.23075 10.1914 9.23075 10.7596C9.23075 11.3278 9.69138 11.7884 10.2596 11.7884C10.8278 11.7884 11.2884 11.3278 11.2884 10.7596C11.2884 10.1914 10.8278 9.73075 10.2596 9.73075ZM6.14422 9.73075C5.576 9.73075 5.11538 10.1914 5.11538 10.7596C5.11538 11.3278 5.576 11.7884 6.14422 11.7884C6.71243 11.7884 7.17306 11.3278 7.17306 10.7596C7.17306 10.1914 6.71243 9.73075 6.14422 9.73075ZM6.14422 9.73075V4.58653M3.05769 4.58653H9.23075C9.71013 4.58653 9.94982 4.58653 10.1389 4.50822C10.391 4.40379 10.5913 4.2035 10.6957 3.95141C10.774 3.76234 10.774 3.52265 10.774 3.04327C10.774 2.56388 10.774 2.32419 10.6957 2.13512C10.5913 1.88303 10.391 1.68274 10.1389 1.57832C9.94982 1.5 9.71013 1.5 9.23075 1.5H3.05769C2.57831 1.5 2.33862 1.5 2.14954 1.57832C1.89745 1.68274 1.69716 1.88303 1.59274 2.13512C1.51442 2.32419 1.51442 2.56388 1.51442 3.04327C1.51442 3.52265 1.51442 3.76234 1.59274 3.95141C1.69716 4.2035 1.89745 4.40379 2.14954 4.50822C2.33862 4.58653 2.57831 4.58653 3.05769 4.58653Z" stroke="#0066FF" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"></path></svg></div></div><p class="text-[#4D4D4F]">Basic chatbots with assignment automation</p></div></div></div></div></div></div></div></section>
            """);

        chatHistory.AddAssistantMessage(
            """
            ```markdown
            |  | Pro | Premium | Enterprise |
            |---|---|---|---|
            |  | HK$ 1,199 per month | HK$ 2,599 per month | Custom |
            |  | For small teams to collaborate better on chats | For scaling businesses to automate workflows | For large businesses to build tailored solutions |
            |  | Book a Demo | Book a Demo | Book a Demo |
            |  | Free onboarding support | Free onboarding support | Free onboarding support |
            | Includes |  |  |  |
            |  | 3 user accounts | 5 user accounts | Custom number of user accounts |
            |  | 2,000 contacts | 10,000 contacts | Custom number of contacts |
            |  | 3 Flow Builder active flows | 25 Flow Builder active flows | 50 Flow Builder active flows |
            |  | Support via email | Support via chats and email | Dedicated customer success |
            | Key features |  | Everything in Pro, plus | Everything in Premium, plus |
            |  | Basic chatbots with assignment automation | Advanced chatbots with external integration | Salesforce integration |
            |  | Facebook Lead Ads integration | Click to WhatsApp ads | Export SleekFlow chats |
            |  | Shopify integration | Custom Objects | PII masking |
            |  | Stripe payment link | HubSpot integration | Analytics export |
            |  | WhatsApp Broadcast | Ticketing | Custom SLAs |
            |  |  | Analytics dashboard & export | Flow Builder and automation setup |
            |  |  | IP allowlisting | Business consultancy service |
            ```
            """);

        // Add the actual content
        chatHistory.AddUserMessage(htmlTable);

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        var markdown = completeMarkdownOutput.Content;
        if (markdown == null)
        {
            throw new Exception("Table conversion failed.");
        }

        var trimmedMarkdown = Regex.Replace(markdown.Trim(), "```(markdown)?", "");

        return trimmedMarkdown;
    }

    private async Task<VerifyTableMarkdownResults> VerifyTableMarkdownAsync(
        string htmlTable,
        string markdownTable)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true, 0.1f);

        if (markdownTable.Contains(new string(' ', 200)))
        {
            return new VerifyTableMarkdownResults(
                0,
                "The markdown table contains abnormally large amount of spaces");
        }

        if (markdownTable.Contains(new string('-', 200)))
        {
            return new VerifyTableMarkdownResults(
                0,
                "The markdown table contains abnormally large amount of dashes\n");
        }

        if (markdownTable.Contains(
                @"_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\"))
        {
            return new VerifyTableMarkdownResults(
                0,
                "The markdown table contains abnormally large amount of dashes\n");
        }

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are tasked to verify whether a markdown is contains all textual information of a HTML code snippet.

            You are provided a HTML code snippet and a markdown.

            Core responsibilities:
            - Make sure the markdown contains all textual information in the HTML.
            - Structures, font, icons and list styles do not matter. You only need to care about the information within.
            - Only check for textual information. Images are removed intentionally so they can be missing.
            - Make sure the markdown is valid

            Special considerations:
            - In particular, look for the following 3 indicators and fail the verification if they are found:
                1. Broken formatting (e.g. Abnormally wide tables)
                2. Abnormally large amount of spaces or unnatural repeating characters
                3. Weird endless loops of texts or sections

            Output format:
            {
                reason: string
                score: int
            }

            Output explanation:
            reason: Any reasoning for the score. Make sure the reason is valid.
            score: A score of 0-100 of whether the markdown contains all textual information of the HTML, false otherwise
            """);

        chatHistory.AddUserMessage(
        [
            new TextContent("HTML table:\n" + htmlTable),
            new TextContent("Markdown table:\n" + markdownTable),
        ]);

        var completeOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeOutput.Content == null)
        {
            throw new Exception("Chat completion failed.");
        }

        var unwrappedOutput = Regex.Replace(completeOutput.Content.Trim(), "```(json)?", "");
        var results = JsonConvert.DeserializeObject<VerifyTableMarkdownResults>(unwrappedOutput);
        if (results == null)
        {
            throw new Exception("Chat completion failed.");
        }

        return new VerifyTableMarkdownResults(results.Score, results.Reason);
    }

    private class VerifyTableMarkdownResults
    {
        [JsonProperty("score")]
        public int Score { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }

        [JsonConstructor]
        public VerifyTableMarkdownResults(int score, string reason)
        {
            Score = score;
            Reason = reason;
        }
    }

    private async Task<string> EnrichMarkdownTableWithImageAsync(
        string markdownTable,
        byte[] tableScreenshotBytes)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, false, 0.1f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            Your primary role is to replace image HTML elements in a markdown table with text/emojis.

            The user will provide the markdown table and an image containing it's visual representation.

            Core responsibilities:
            - Look for any image HTML elements in the markdown table (e.g. <img/>, <i/> etc.). Using the provided image, replace all image elements in the markdown with text/emoji that best matches the provided image.
            - Preserve all text and structure of the given markdown table. Do not collapse or hide any element.

            The final output should be the exactly the same as the original markdown document except with all HTML image elements replaced.
            Respond with the converted markdown directly, do not say things like: "Here is the converted table:".
            """);

        chatHistory.AddUserMessage(
        [
            new ImageContent(tableScreenshotBytes, "image/jpeg"),
            new TextContent(markdownTable)
        ]);

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeMarkdownOutput.Content == null || string.IsNullOrWhiteSpace(completeMarkdownOutput.Content))
        {
            throw new Exception("Markdown enrichment failed.");
        }

        return completeMarkdownOutput.Content;
    }

    private async Task<string> ConvertMarkdownTableToSentencesAsync(
        Table table,
        string markdownTable)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, false, 0.1f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            Your primary role is to convert markdown tables in a webpage into sentences.

            The user will provide a caption for the table and the markdown table.

            Core responsibilities:
            - Understand the entire purpose and meaning of the table using the caption.
            - Convert the markdown table into sentences
            - The sentences should be self-containing and meaningful, you should make use of the description, row headers and column headers etc. to understand the table contexts
            - You can create a sentence per table row or a sentence per table cell, choose the one that best conveys the meaning of the table
            - If you encounter emojis or images, interpret their meaning and include them in the sentences
            - If the table is completely empty, just say so

            Include the website description and table caption in the output as-is, do not modify.

            Output format:
            {Table caption}

            {The table converted to sentences}

            The final output should be the table description followed by a bunch of sentences that contain all the information from the markdown table.
            """);

        chatHistory.AddUserMessage(
            $"""
             Table caption: {table.TableCaption}

             {markdownTable}
             """);

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeMarkdownOutput.Content == null || string.IsNullOrWhiteSpace(completeMarkdownOutput.Content))
        {
            throw new Exception("Markdown to sentence conversion failed.");
        }

        return completeMarkdownOutput.Content;
    }

    private async Task<SemanticChunkHtmlResult[]> SemanticChunkHtml(string html)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(
                SemanticKernelExtensions.S_FLASH_2_5,
                true,
                0.1f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            Your task is to break down a website into chunks via a process known as semantic chunking.

            The user will supply the HTML of the webpage. You should break it down into chunks identified by XPaths.

            Core responsibility - Semantic chunk identification:
            - Identify major sections in the website. These can be tables, blocks of texts etc.
            - Keep chunks small, if the chunk is very big, you need to break it down into smaller chunks.
            - All the chunks combined should not omit any information in the original website
            - You should identify the roots of the chunks
            - The chunks identified should be just large enough to contain a coherent section.
            - Ignore empty or meaningless chunks.

            Core responsibility - Semantic chunk context generation:
            - After chunking, the chunk may not have enough context when interpreted individually to be meaningful,
              so you should generate a detailed context for the chunk based on the meaning of the chunk and the surrounding information.
            - The context should provide complete information for understanding the chunk.

            Core responsibility - XPath rules:
            - All XPaths must be valid
            - All XPaths must point to an actual element in the HTML
            - Use relative xpath and keep the XPath as short as possible
            - Use positional predicates
            - Make sure there is no overlap or duplicate in the elements identified by the XPaths
            - If no chunks could be identified, just return empty array

            You must make sure the XPaths you return are valid and point to actual elements in the website.

            Output Format:
            ```json
            [
                {
                    "chunk_xpaths": string,
                    "chunk_context": string
                }
            ]
            ```
            """);
        chatHistory.AddUserMessage(html);

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeMarkdownOutput.Content == null)
        {
            throw new Exception("Semantic chunking failed.");
        }

        var output = JsonConvert.DeserializeObject<SemanticChunkHtmlResult[]>(
            Regex.Replace(completeMarkdownOutput.Content.Trim(), "```(json)?", ""));

        if (output == null)
        {
            throw new Exception("Chat completion failed.");
        }

        return output;
    }

    private async Task<VerifyMarkdownResults> VerifyMarkdown(
        string convertedHtml,
        byte[]? crawlerResultScreenshotJpgBytes,
        string markdown)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true, 0.1f);

        if (markdown.Trim().Length == 0)
        {
            return new VerifyMarkdownResults(0, "Markdown should not be empty.");
        }

        if (markdown.Contains(new string(' ', 200)))
        {
            return new VerifyMarkdownResults(
                0,
                "The markdown contains abnormally large amount of spaces. It is likely incorrectly generated. Please retry from scratch.");
        }

        if (markdown.Contains(new string('-', 200)))
        {
            return new VerifyMarkdownResults(
                0,
                "The markdown contains abnormally large amount of dashes. It is likely incorrectly generated. Please retry from scratch.");
        }

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            We are trying to convert a HTML document to markdown.
            You are provided a markdown and a HTML code snippet
            You are tasked to verify whether the markdown is a fairly accurate conversion of the main body content of the HTML.

            - The markdown should contain the main textual content of the HTML
            - No need to verify images
            - Formatting and structure do not matter, you only need to verify the content
            - We only care about the main body content, use your judgement to decide what needs to be included for the markdown to be considered an accurate conversion of the HTML
            - Navigational elements like sidebar, breadcrumbs, modals etc. can be omitted
            - The markdown may not necessarily contain all the textual information in each section, but all information contained are accurate
            - All lines in bullet lists or numbered lists in the main content are present
            - It is ok for the markdown to contain extra information not present in the HTML, as long as they are correct.

            Output format:
            {
                score: int
                reason: string
            }

            Output explanation:
            score: A score of 0-100 of whether the markdown is a fairly accurate representation of the main textual content of the HTML
            reason: List all reasons in detail for the score. If something is missing, you should say exactly what was missing and including it should pass the verification. Check and make sure your reasons are actually valid.
            """);

        chatHistory.AddUserMessage(
        [
            new TextContent("This is the website HTML:\n" + convertedHtml),
            new TextContent("This is the markdown:\n" + markdown)
        ]);

        var completeOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeOutput.Content == null)
        {
            throw new Exception("Chat completion failed.");
        }

        var unwrappedOutput = Regex.Replace(completeOutput.Content.Trim(), "```(json)?", "");
        var results = JsonConvert.DeserializeObject<VerifyMarkdownResults>(unwrappedOutput);

        if (results == null)
        {
            throw new Exception("Chat completion failed.");
        }

        return results;
    }

    // Supporting classes
    private class SemanticChunkHtmlResult
    {
        [JsonProperty("chunk_xpaths")]
        public string ChunkXPath { get; set; }

        [JsonProperty("chunk_context")]
        public string ChunkContext { get; set; }

        [JsonConstructor]
        public SemanticChunkHtmlResult(string chunkXPath, string chunkContext)
        {
            ChunkXPath = chunkXPath;
            ChunkContext = chunkContext;
        }
    }

    private class GetNavigationElementsResult
    {
        [JsonProperty("header_xpath")]
        public string? HeaderXpath { get; set; }

        [JsonProperty("footer_xpath")]
        public string? FooterXpath { get; set; }

        [JsonConstructor]
        public GetNavigationElementsResult(string? headerXpath, string? footerXpath)
        {
            HeaderXpath = headerXpath;
            FooterXpath = footerXpath;
        }
    }

    private class Table
    {
        [JsonProperty("xpath")]
        public string Xpath { get; set; }

        [JsonProperty("table_caption")]
        public string TableCaption { get; set; }

        [JsonConstructor]
        public Table(string xpath, string tableCaption)
        {
            Xpath = xpath;
            TableCaption = tableCaption;
        }
    }

    private class XPathQueryResults
    {
        [JsonProperty("tables")]
        public Table[] Tables { get; set; }

        [JsonConstructor]
        public XPathQueryResults(Table[] tables)
        {
            Tables = tables;
        }
    }

    private class VerifyMarkdownResults
    {
        [JsonProperty("score")]
        public int Score { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }

        [JsonConstructor]
        public VerifyMarkdownResults(int score, string reason)
        {
            Score = score;
            Reason = reason;
        }
    }

    private async Task<XPathQueryResults> GetXPathQueryForTables(
        byte[]? fullPageScreenshotBytes,
        string htmlTable)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true, 0.1f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are an expert website table identifier. Your primary role is to identify tables in the website and return an array of XPath selectors to those table elements.

            The user will supply the HTML of the webpage. Forsake any expectation for semantic HTML.

            Core responsibilities - Table identification:
            - All of the following are considered "tables": grid-like layouts, data matrices, tables, presence of keywords like "table", "row, "column", "grid" in classname and id etc.
            - Identify any structures that feel like tables. Generate XPath selectors to those elements in the HTML.
            - You should identify the roots of the tables
            - The elements identified should be as large as possible to contain the whole table, but small enough to leave irrelevant structures outside of the table.
            - If 2 tables are closely located or side-by-side, it may be a comparison and may need to be merged into one table.
            - The table should contain meaningful information in the webpage
            - Make sure the selected elements contain the entire table including the headers

            Core responsibilities - Table identification verifications:
            - Make sure the XPath selector points to a valid element in the HTML
            - Use relative xpath and keep the XPath as short as possible
            - Each XPath should point to a unique table root in the webpage. There should be no duplicates or overlaps.
            - Always use positional predicate

            Core responsibilities - Table description:
            - After identifying a table xpath, use the surrounding text and context to generate a caption for the table
            - The caption should be as detailed as possible and mention the meaning of all the column headers and row headers.
            - The caption should be self-containing and contain all the information necessary understand the purpose and meaning of the table.

            Output Format:
            ```json
            {
                "tables": {
                    "xpath": string
                    "table_caption": string
                }[]
            }
            ```
            """);
        chatHistory.AddUserMessage(htmlTable);

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeMarkdownOutput.Content == null)
        {
            throw new Exception("Table identification failed.");
        }

        var output = JsonConvert.DeserializeObject<XPathQueryResults>(
            Regex.Replace(completeMarkdownOutput.Content.Trim(), "```(json)?", ""));

        if (output == null)
        {
            throw new Exception("Chat completion failed.");
        }

        return output;
    }

    private async Task ProcessImgNodes(string baseUrl, HtmlDocument doc)
    {
        var imageNodes = doc.DocumentNode.SelectNodes("//img[@src]");
        if (imageNodes != null)
        {
            _logger.LogInformation(
                "Identified {ImageCount} image elements for URL {BaseUrl}",
                imageNodes.Count,
                baseUrl);

            await Parallel.ForEachAsync(
                imageNodes,
                new ParallelOptions()
                {
                    MaxDegreeOfParallelism = 4
                },
                async (node, token) =>
                {
                    var imageSrc = node.Attributes["src"].Value;
                    node.Attributes.Remove("alt");
                    if (imageSrc.StartsWith("/_next/image"))
                    {
                        // Next JS optimized URLs
                        var absoluteUrl = new Uri(new Uri(baseUrl).GetLeftPart(UriPartial.Authority) + imageSrc);
                        try
                        {
                            var srcUrl =
                                HttpUtility.UrlDecode(HttpUtility.ParseQueryString(absoluteUrl.Query).Get("url"))
                                ?? throw new Exception("Cannot find url param");
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(absoluteUrl.ToString(), out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var (mimeType, imageBytes) = await DownloadImageAsync(srcUrl);
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(absoluteUrl.ToString(), imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(absoluteUrl.ToString(), cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(
                                ex,
                                "Error processing image {ImageUrl}: {Error}",
                                absoluteUrl.ToString(),
                                ex.Message);
                            _imageMarkdownCache.TryAdd(absoluteUrl.ToString(), string.Empty);
                        }
                    }
                    else if (imageSrc.StartsWith("//")) // relative image path
                    {
                        var absoluteUrl = "https:" + imageSrc;
                        try
                        {
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(absoluteUrl, out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var (mimeType, imageBytes) = await DownloadImageAsync(absoluteUrl);
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(absoluteUrl, imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(absoluteUrl, cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(
                                ex,
                                "Error processing image {ImageUrl}: {Error}",
                                absoluteUrl,
                                ex.Message);
                            _imageMarkdownCache.TryAdd(absoluteUrl, string.Empty);
                        }
                    }
                    else if (imageSrc.StartsWith("/")) // relative image path
                    {
                        var absoluteUrl = new Uri(baseUrl).GetLeftPart(UriPartial.Authority) + imageSrc;
                        try
                        {
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(absoluteUrl, out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var (mimeType, imageBytes) = await DownloadImageAsync(absoluteUrl);
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(absoluteUrl, imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(absoluteUrl, cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(
                                ex,
                                "Error processing image {ImageUrl}: {Error}",
                                absoluteUrl,
                                ex.Message);
                            _imageMarkdownCache.TryAdd(absoluteUrl, string.Empty);
                        }
                    }
                    else if (imageSrc.StartsWith("./")) // relative image path
                    {
                        var lastSlashIndex = baseUrl.LastIndexOf('/');
                        var baseUrlRoot = baseUrl.Substring(0, lastSlashIndex + 1); // Include the trailing '/'

                        var absoluteUrl = baseUrlRoot + imageSrc.Substring(2);
                        try
                        {
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(absoluteUrl, out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var (mimeType, imageBytes) = await DownloadImageAsync(absoluteUrl);
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(absoluteUrl, imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(absoluteUrl, cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(
                                ex,
                                "Error processing image {ImageUrl}: {Error}",
                                absoluteUrl,
                                ex.Message);
                            _imageMarkdownCache.TryAdd(absoluteUrl, string.Empty);
                        }
                    }
                    else if (imageSrc.StartsWith("../")) // relative image path
                    {
                        var lastSlashIndex = baseUrl.LastIndexOf('/');
                        var secondLastSlashIndex = baseUrl.LastIndexOf('/', lastSlashIndex - 1);
                        var baseUrlRoot = baseUrl.Substring(0, secondLastSlashIndex + 1); // Include the trailing '/'

                        var absoluteUrl = baseUrlRoot + imageSrc.Substring(3);
                        try
                        {
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(absoluteUrl, out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var (mimeType, imageBytes) = await DownloadImageAsync(absoluteUrl);
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(absoluteUrl, imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(absoluteUrl, cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(
                                ex,
                                "Error processing image {ImageUrl}: {Error}",
                                absoluteUrl,
                                ex.Message);
                            _imageMarkdownCache.TryAdd(absoluteUrl, string.Empty);
                        }
                    }
                    else if (imageSrc.StartsWith("http://") ||
                             imageSrc.StartsWith("https://")) // absolute image path
                    {
                        try
                        {
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(imageSrc, out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var (mimeType, imageBytes) = await DownloadImageAsync(imageSrc);
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(imageSrc, imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(imageSrc, cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error processing image {ImageUrl}: {Error}", imageSrc, ex.Message);
                            _imageMarkdownCache.TryAdd(imageSrc, string.Empty);
                        }
                    }
                    else if (imageSrc.StartsWith("data:image/")) // base64 image data
                    {
                        try
                        {
                            var cachedMarkdown = _imageMarkdownCache.TryGetValue(imageSrc, out var cached)
                                ? cached
                                : null;
                            if (cachedMarkdown == null)
                            {
                                var parts = imageSrc.Split(',', 2); // Split into MIME type and data
                                var mimeType = parts[0]["data:".Length..].Split(';')[0]; // Extract MIME type
                                var imageBytes = Convert.FromBase64String(parts[1]); // Base64 encoded data
                                cachedMarkdown =
                                    await ConvertImageToMarkdownAsync(imageSrc, imageBytes, mimeType);
                                _imageMarkdownCache.TryAdd(imageSrc, cachedMarkdown);
                            }

                            node.Attributes.Add("alt", cachedMarkdown);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error processing image {ImageUrl}: {Error}", imageSrc, ex.Message);
                            _imageMarkdownCache.TryAdd(imageSrc, string.Empty);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Unsupported image source format: {ImageSrc}", imageSrc);
                        _imageMarkdownCache.TryAdd(imageSrc, string.Empty);
                    }
                });

            _logger.LogInformation("Image elements processed for URL {BaseUrl}", baseUrl);
        }
    }

    private async Task<(string mimeType, byte[] imageBytes)> DownloadImageAsync(string imageUrl)
    {
        var response = await _httpClient.GetAsync(new Uri(imageUrl));
        response.EnsureSuccessStatusCode();

        var mimeType = response.Content.Headers.ContentType?.MediaType ?? throw new Exception("Unknown media type.");
        await using var responseStream = await response.Content.ReadAsStreamAsync();
        using var imageStream = new MemoryStream();
        await responseStream.CopyToAsync(imageStream);

        switch (mimeType)
        {
            // These mimetypes are supported by most AI services
            case "image/png" or "image/jpeg" or "image/webp" or "image/heic" or "image/heif":
            {
                var imageBytes = imageStream.ToArray();
                return (mimeType, imageBytes);
            }

            // For other image types, we'll return as-is and let the AI service handle it
            default:
            {
                _logger.LogInformation("Processing image with mimetype: {MimeType}", mimeType);
                var imageBytes = imageStream.ToArray();
                return (mimeType, imageBytes);
            }
        }
    }

    private class ConvertImageToMarkdownResults
    {
        [JsonProperty("markdown")]
        public string Markdown { get; set; }

        [JsonConstructor]
        public ConvertImageToMarkdownResults(string markdown)
        {
            Markdown = markdown;
        }
    }

    private async Task<string> ConvertImageToMarkdownAsync(
        string imageUrl,
        byte[] imageBytes,
        string mimeType)
    {
        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, false, 0.2f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are an expert image-to-markdown converter. Your primary role is to convert the supplied image to human-readable markdown output.

            Core responsibility - Generate description
            - Generate a text description that is most fitting for the image. Be as specific as possible.

            Core responsibility - Text extraction
            - If the image contains text, extract all text from the image with no information loss.

            Core responsibility - Table extraction
            - Identify all tables found in the image, convert them into markdown table syntax, preserve the exact wordings.
            - When converting tables, make sure merged cells are duplicated to all corresponding columns appropriately. Fill in all cells instead of leaving them empty
            - If the image is a brand logo, identify the brand name. Make sure every letter is correct. Don't confuse similar letters like I and l, C and G etc.
                -- Don't be biased towards brands that you already know. Many brands have similar names. You should just extract the text from the logo and avoid confusion.
                -- Don't guess, brand logos typically draw letters in a confusing manner, if there is any doubt just return empty string.

            Combine all the above into a single markdown output

            Special Considerations:
            - Present the information as-is, do NOT introduce new information from other sources
            - Preserve all special terms, entity names, and technical nomenclature exactly as presented
            - If you cannot generate a markdown for the image, just return empty string as a last resort.

            Output format:
            ```markdown
                {The markdown output}
            ```

            Output language:
            - The markdown should use the same language as the language in the image.

            Output description:
            "markdown": The generated markdown for the image. Remember to escape special characters to keep the JSON valid.
            """);
        chatHistory.AddUserMessage(
        [
            new ImageContent(imageBytes, mimeType),
            new TextContent("Please convert the supplied image:")
        ]);

        var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        if (completeMarkdownOutput.Content == null)
        {
            throw new Exception("Image conversion failed.");
        }

        var result = Regex.Replace(completeMarkdownOutput.Content.Trim(), "```(markdown)?", "");

        if (result == null)
        {
            throw new Exception("Image conversion failed.");
        }

        if (string.IsNullOrEmpty(result.Trim()))
        {
            return "";
        }

        var isVerificationOk = await VerifyImageMarkdownConversionAsync(imageBytes, mimeType, result);
        if (!isVerificationOk)
        {
            return "";
        }

        return result;
    }

    private async Task<bool> VerifyImageMarkdownConversionAsync(
        byte[] imageBytes,
        string mimeType,
        string markdown)
    {
        if (markdown.Contains(new string(' ', 200)))
        {
            _logger.LogWarning("Image markdown contains abnormally large amount of spaces");
            return false;
        }

        if (markdown.Contains(new string('-', 200)))
        {
            _logger.LogWarning("Image markdown contains abnormally large amount of dashes");
            return false;
        }

        var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_GPT_4o);
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4o, false, 0.1f);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are given an image and a markdown description for the image. Your task is to verify that the markdown accurately represents the image.

            Core responsibilities:
            - Make sure the markdown is an accurate description for the image.
            - The markdown must contain all text and tables in the image.
            - If the image is a brand logo, make sure the markdown accurately identifies the brand name.
               -- Use the internet to search and make sure the brand exists.
               -- Many brands have similar names. Don't be biased towards brands that you already know.
               -- Be very strict about brand names. Make sure every letter is correct, especially for similar letters like I and l, C and G etc.
               -- Letters in brand logos can be drawn in a confusing way. If there is any doubt about the brand name, fail the verification. Don't guess.

            Output format:
            TRUE or FALSE

            Output description:
            "TRUE": The markdown is an accurate description of the image.
            "FALSE": The markdown is NOT an accurate description of the image.
            """);
        chatHistory.AddUserMessage(
        [
            new ImageContent(imageBytes, mimeType),
            new TextContent($"Here is the markdown:\n\n {markdown}")
        ]);

        var verificationCompletionOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            promptExecutionSettings,
            _kernel);

        var content = verificationCompletionOutput.Content;
        if (content == null)
        {
            _logger.LogWarning("Image verification failed - no content returned");
            return false;
        }

        if (content != "TRUE" && content != "FALSE")
        {
            _logger.LogWarning("Image verification failed - unexpected response: {Response}", content);
            return false;
        }

        return content == "TRUE";
    }
}