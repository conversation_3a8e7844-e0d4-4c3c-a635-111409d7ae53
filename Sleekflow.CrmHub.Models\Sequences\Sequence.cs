﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.Sequences;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId(ContainerNames.Sequence)]
public class Sequence : Entity, IHasETag
{
    public const string PropertyNameValue = "value";

    [JsonProperty(PropertyNameValue)]
    public long Value { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public Sequence(
        string id,
        long value,
        string? eTag)
        : base(id, SysTypeNames.Sequence)
    {
        Value = value;
        ETag = eTag;
    }
}