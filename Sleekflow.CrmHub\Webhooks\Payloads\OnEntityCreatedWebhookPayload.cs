﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Entities;

namespace Sleekflow.CrmHub.Webhooks.Payloads;

public class OnEntityCreatedWebhookPayload
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [<PERSON>son<PERSON>roperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("date_time")]
    public DateTimeOffset DateTime { get; set; }

    [JsonProperty("object")]
    public CrmHubEntity Object { get; set; }

    [JsonProperty("event_type_name")]
    public string EventTypeName { get; set; }

    [JsonConstructor]
    public OnEntityCreatedWebhookPayload(
        string id,
        DateTimeOffset dateTime,
        string sleekflowCompanyId,
        string entityTypeName,
        CrmHubEntity @object,
        string eventTypeName)
    {
        Id = id;
        DateTime = dateTime;
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        Object = @object;
        EventTypeName = eventTypeName;
    }
}