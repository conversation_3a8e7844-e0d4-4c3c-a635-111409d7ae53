﻿using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.Schemas.Utils;

public interface IOptionService
{
    (List<Option>? SanitizedOptions, List<string> DeletedOptionIds) SanitizeAndSortOptions(
        List<Option>? receivedOptions,
        List<Option>? originalOptions);
}

public class OptionService : IOptionService, ISingletonService
{
    private readonly IIdService _idService;

    public OptionService(IIdService idService)
    {
        _idService = idService;
    }

    public (List<Option>? SanitizedOptions, List<string> DeletedOptionIds) SanitizeAndSortOptions(
        List<Option>? receivedOptions,
        List<Option>? originalOptions)
    {
        if (receivedOptions == null || !receivedOptions.Any())
        {
            return (null, originalOptions?.Select(oo => oo.Id).ToList() ?? new List<string>());
        }

        var sanitizedOptions = new List<Option>();

        foreach (var receivedOption in receivedOptions)
        {
            if (originalOptions is null || string.IsNullOrEmpty(receivedOption.Id))
            {
                sanitizedOptions.Add(
                    new Option(
                        _idService.GetId(SysTypeNames.SchemaOption),
                        receivedOption.Value,
                        receivedOption.DisplayOrder));
            }
            else if (originalOptions.Exists(oo => oo.Id == receivedOption.Id))
            {
                sanitizedOptions.Add(receivedOption);
            }
            else
            {
                throw new SfNotFoundObjectException($"Cannot found option [{receivedOption.Id}]");
            }
        }

        var deletedOptionIds = originalOptions is null
            ? new List<string>()
            : originalOptions
                .Where(oo => !sanitizedOptions.Exists(so => so.Id == oo.Id))
                .Select(p => p.Id)
                .ToList();

        sanitizedOptions = sanitizedOptions
            .OrderBy(so => so.DisplayOrder)
            .Select(
                (so, index) =>
                {
                    so.DisplayOrder = index;
                    return so;
                })
            .ToList();

        return (sanitizedOptions, deletedOptionIds);
    }
}