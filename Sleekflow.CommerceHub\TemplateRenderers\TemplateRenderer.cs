using Scriban;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.TemplateRenderers;

public interface ITemplateRenderer
{
    Task<RenderedTemplate> PreviewProductVariantTemplateAsync(
        string templateStr,
        string languageIsoCode);

    Task<RenderedTemplate> RenderProductVariantTemplateAsync(
        string templateStr,
        ProductVariantRenderingDto productVariantRenderingDto);
}

public class TemplateRenderer : ITemplateRenderer, ISingletonService
{
    public Task<RenderedTemplate> PreviewProductVariantTemplateAsync(
        string templateStr,
        string languageIsoCode)
    {
        return RenderProductVariantTemplateAsync(
            templateStr,
            ProductVariantRenderingDto.Sample());
    }

    public async Task<RenderedTemplate> RenderProductVariantTemplateAsync(
        string templateStr,
        ProductVariantRenderingDto productVariantRenderingDto)
    {
        var template = Template.ParseLiquid(templateStr);
        var renderedTemplate = await template.RenderAsync(
            new
            {
                ProductVariant = productVariantRenderingDto,
            });

        var images = new List<ImageDto>();
        images.AddRange(productVariantRenderingDto.Images);
        images.AddRange(productVariantRenderingDto.Product.Images);

        return new RenderedTemplate(
            images.FirstOrDefault()?.ImageUrl,
            renderedTemplate);
    }
}