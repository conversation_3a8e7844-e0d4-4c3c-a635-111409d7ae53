using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.JsonConfigs;

namespace Sleekflow.FlowHub.Blobs;

public interface IBlobService
{
    Task UploadAsJsonAsync<T>(
        string containerName,
        string blobName,
        T @object);

    Task UploadAsJsonAsync<T>(
        string containerName,
        string blobName,
        T @object,
        JsonSerializerSettings serializerSettings);

    Task<T?> DownloadJsonAsAsync<T>(
        string containerName,
        string blobName);

    Task<bool> RemoveIfExistsAsync(
        string containerName,
        string blobName);

    Task CreateSnapshotAsync(
        string containerName,
        string blobName);
}

public sealed class BlobService : IBlobService, ISingletonService
{
    private readonly ILogger<BlobService> _logger;
    private readonly BlobServiceClient _blobServiceClient;

    public BlobService(
        ILogger<BlobService> logger,
        IStorageConfig storageConfig)
    {
        _logger = logger;
        _blobServiceClient = new (storageConfig.FileStorageConnStr);
    }

    public async Task UploadAsJsonAsync<T>(
        string containerName,
        string blobName,
        T @object)
    {
        await UploadAsJson(
            containerName,
            blobName,
            @object,
            JsonConfig.DefaultJsonSerializerSettings);
    }

    public async Task UploadAsJsonAsync<T>(
        string containerName,
        string blobName,
        T @object,
        JsonSerializerSettings serializerSettings)
    {
        await UploadAsJson(
            containerName,
            blobName,
            @object,
            serializerSettings);
    }

    public async Task<T?> DownloadJsonAsAsync<T>(
        string containerName,
        string blobName)
    {
        var blobClient = GetBlobClient(containerName, blobName);

        if (!await IsBlobExistAsync(blobClient))
        {
            return default;
        }

        await using var blobStream = await blobClient.OpenReadAsync();
        using var streamReader = new StreamReader(blobStream);
        await using var jsonReader = new JsonTextReader(streamReader);

        var deserializer = JsonSerializer.Create(JsonConfig.DefaultJsonSerializerSettings);

        return deserializer.Deserialize<T>(jsonReader);
    }

    public async Task<bool> RemoveIfExistsAsync(
        string containerName,
        string blobName)
    {
        var blobClient = GetBlobClient(containerName, blobName);

        var response = await blobClient.DeleteIfExistsAsync(DeleteSnapshotsOption.IncludeSnapshots);

        return response.Value;
    }

    private async Task<bool> IsBlobExistAsync(BlobBaseClient blobClient)
    {
        try
        {
            return await blobClient.ExistsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to verify if blob [{BlobName}] exists in container {BlobContainerName}",
                blobClient.Name,
                blobClient.BlobContainerName);

            return false;
        }
    }

    public async Task CreateSnapshotAsync(
        string containerName,
        string blobName)
    {
        var blobClient = GetBlobClient(containerName, blobName);

        if (!await IsBlobExistAsync(blobClient))
        {
            return;
        }

        await blobClient.CreateSnapshotAsync();
    }

    private BlobClient GetBlobClient(string containerName, string blobName)
        => _blobServiceClient
            .GetBlobContainerClient(containerName)
            .GetBlobClient(blobName);

    private async Task UploadAsJson<T>(
        string containerName,
        string blobName,
        T @object,
        JsonSerializerSettings serializerSettings)
    {
        var blobClient = GetBlobClient(containerName, blobName);

        var serializer = JsonSerializer.Create(serializerSettings);

        await using var uploadStream = await blobClient.OpenWriteAsync(true);
        await using var streamWriter = new StreamWriter(uploadStream);
        await using var jsonWriter = new JsonTextWriter(streamWriter);

        serializer.Serialize(jsonWriter, @object);
    }
}