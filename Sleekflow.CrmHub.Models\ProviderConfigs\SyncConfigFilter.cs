﻿using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

public sealed class SyncConfigFilter : IEquatable<SyncConfigFilter>
{
    public static readonly Regex ValueRegex = new Regex("^[a-zA-Z0-9_\\-]+$");

    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }

    /// <summary>
    /// Supported in Salesforce only
    /// If it is null, it will default to "equals" "="
    /// </summary>
    [JsonProperty("operator")]
    public string? Operator { get; set; }

    [JsonConstructor]
    public SyncConfigFilter(string fieldName, string value, string? @operator)
    {
        FieldName = fieldName;
        Value = value;
        Operator = @operator;
    }

    public bool Equals(SyncConfigFilter? other)
    {
        if (ReferenceEquals(null, other))
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return FieldName == other.FieldName && Value == other.Value && Operator == other.Operator;
    }

    public override bool Equals(object? obj)
    {
        return ReferenceEquals(this, obj) || (obj is SyncConfigFilter other && Equals(other));
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(FieldName, Value, Operator);
    }
}