﻿using System.Globalization;
using NSubstitute;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.TimeProviders;

namespace Sleekflow.FlowHub.Tests.Workflows;

public class WorkflowRecurringSettingsParserTests
{
    private readonly ITimeProvider _timeProvider = Substitute.For<ITimeProvider>();

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00",
        "2023-07-03T12:34:56+00:00",
        "2023-07-10T12:34:56+00:00",
        false)] // Past occurrence
    [TestCase(
        "2024-07-04T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        "2024-07-10T12:34:56+00:00",
        true)] // Future occurrence
    [TestCase(
        "2024-07-10T11:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        "2024-07-10T12:34:56+00:00",
        true)] // Future occurrence + Already on the scheduled date, but before the time
    [TestCase(
        "2024-07-16T11:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        "2024-07-17T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring
    [TestCase(
        "2024-07-17T11:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        "2024-07-17T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring + Already on the scheduled date, but before the scheduled time
    public void GetNextOccurrence_GivenWeeklyRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        string expectedNextOccurrence,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Weekly,
            null,
            null,
            null,
            null);

        // Act
        var nextOccurrence = parser.GetNextOccurrence(
            Parse(firstOccurrence),
            recurringSettings,
            futureOccurrenceOnly: futureOccurrenceOnly);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(nextOccurrence, Is.EqualTo(Parse(expectedNextOccurrence)));

                if (futureOccurrenceOnly)
                {
                    Assert.That(nextOccurrence, Is.GreaterThan(_timeProvider.UtcNow));
                }
                else
                {
                    Assert.That(nextOccurrence, Is.GreaterThan(Parse(firstOccurrence)));
                    Assert.That((nextOccurrence - Parse(firstOccurrence)).TotalDays, Is.EqualTo(7));
                }
            });
    }

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00",
        "2023-07-03T12:34:56+00:00",
        "2023-08-03T12:34:56+00:00",
        false)] // Past occurrence
    [TestCase(
        "2024-02-01T13:56:30+00:00",
        "2023-01-31T12:34:56+00:00",
        "2023-02-28T12:34:56+00:00",
        false)] // Past occurrence + end of month (31 -> 28)
    [TestCase(
        "2025-02-01T13:56:30+00:00",
        "2024-01-31T12:34:56+00:00",
        "2024-02-29T12:34:56+00:00",
        false)] // Past occurrence + end of month (31 -> 29)
    [TestCase(
        "2024-08-04T12:34:56+00:00",
        "2023-08-31T12:34:56+00:00",
        "2023-09-30T12:34:56+00:00",
        false)] // Past occurrence + end of month (31 -> 30)
    [TestCase(
        "2024-01-04T13:56:30+00:00",
        "2023-12-31T12:34:56+00:00",
        "2024-01-31T12:34:56+00:00",
        false)] // Past occurrence + cross year
    [TestCase(
        "2024-07-04T13:56:30+00:00",
        "2024-06-30T12:34:56+00:00",
        "2024-07-30T12:34:56+00:00",
        true)] // Future occurrence
    [TestCase(
        "2024-08-04T13:56:30+00:00",
        "2024-06-30T12:34:56+00:00",
        "2024-08-30T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring
    [TestCase(
        "2024-08-30T11:56:30+00:00",
        "2024-06-30T12:34:56+00:00",
        "2024-08-30T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring, already on scheduled date, but before scheduled time
    [TestCase(
        "2024-07-30T12:35:30+00:00",
        "2024-06-30T12:34:56+00:00",
        "2024-08-30T12:34:56+00:00",
        true)] // Future occurrence + just passed the previous occurrence
    [TestCase(
        "2023-02-01T13:56:30+00:00",
        "2023-01-31T12:34:56+00:00",
        "2023-02-28T12:34:56+00:00",
        true)] // Future occurrence + end of month (31 -> 28)
    [TestCase(
        "2024-02-01T13:56:30+00:00",
        "2024-01-31T12:34:56+00:00",
        "2024-02-29T12:34:56+00:00",
        true)] // Future occurrence + end of month (31 -> 29)
    [TestCase(
        "2024-09-04T13:56:30+00:00",
        "2024-08-31T12:34:56+00:00",
        "2024-09-30T12:34:56+00:00",
        true)] // Future occurrence + end of month (31 -> 30)
    [TestCase(
        "2025-01-04T13:56:30+00:00",
        "2024-12-31T12:34:56+00:00",
        "2025-01-31T12:34:56+00:00",
        true)] // Future occurrence + cross year
    [TestCase(
        "2025-01-31T12:35:30+00:00",
        "2024-12-31T12:34:56+00:00",
        "2025-02-28T12:34:56+00:00",
        true)] // Future occurrence + cross year + Just passed the previous recurrence
    [TestCase(
        "2025-01-31T11:56:30+00:00",
        "2024-12-31T12:34:56+00:00",
        "2025-01-31T12:34:56+00:00",
        true)] // Future occurrence + cross year + Already on scheduled date, but before scheduled time
    [TestCase(
        "2025-02-28T11:56:30+00:00",
        "2024-12-31T12:34:56+00:00",
        "2025-02-28T12:34:56+00:00",
        true)] // Future occurrence + cross year + 2nd time recurring + Already on scheduled date, but before scheduled time
    public void GetNextOccurrence_GivenMonthlyRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        string expectedNextOccurrence,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Monthly,
            null,
            null,
            null,
            null);

        // Act
        var nextOccurrence = parser.GetNextOccurrence(
            Parse(firstOccurrence),
            recurringSettings,
            futureOccurrenceOnly: futureOccurrenceOnly);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(nextOccurrence, Is.EqualTo(Parse(expectedNextOccurrence)));

                if (futureOccurrenceOnly)
                {
                    Assert.That(nextOccurrence, Is.GreaterThan(_timeProvider.UtcNow));
                }
                else
                {
                    Assert.That(nextOccurrence, Is.GreaterThan(Parse(firstOccurrence)));
                    Assert.That(
                        nextOccurrence.Month - Parse(firstOccurrence).Month
                        + (12 * (nextOccurrence.Year - Parse(firstOccurrence).Year)), Is.EqualTo(1));
                }
            });
    }

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00",
        "2023-07-03T12:34:56+00:00",
        "2024-07-03T12:34:56+00:00",
        false)] // Past occurrence
    [TestCase(
        "2025-03-04T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        "2025-02-28T12:34:56+00:00",
        false)] // Past occurrence + leap year -> non-leap year
    [TestCase(
        "2024-07-04T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        "2025-07-03T12:34:56+00:00",
        true)] // Future occurrence
    [TestCase(
        "2024-07-04T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        "2025-02-28T12:34:56+00:00",
        true)] // Future occurrence + leap year -> non-leap year
    [TestCase(
        "2023-12-29T12:35:30+00:00",
        "2022-12-29T12:34:56+00:00",
        "2024-12-29T12:34:56+00:00",
        true)] // Future occurrence + Just passed the previous recurrence
    [TestCase(
        "2024-01-04T13:56:30+00:00",
        "2022-12-29T12:34:56+00:00",
        "2024-12-29T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring
    [TestCase(
        "2024-12-29T13:35:30+00:00",
        "2022-12-29T12:34:56+00:00",
        "2025-12-29T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring + Just passed the previous recurrence
    [TestCase(
        "2023-12-29T11:56:30+00:00",
        "2022-12-29T12:34:56+00:00",
        "2023-12-29T12:34:56+00:00",
        true)] // Future occurrence + Already on scheduled date, but before scheduled time
    [TestCase(
        "2024-12-29T11:56:30+00:00",
        "2022-12-29T12:34:56+00:00",
        "2024-12-29T12:34:56+00:00",
        true)] // Future occurrence + 2nd time recurring + Already on scheduled date, but before scheduled time
    public void GetNextOccurrence_GivenYearlyRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        string expectedNextOccurrence,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Yearly,
            null,
            null,
            null,
            null);

        // Act
        var nextOccurrence = parser.GetNextOccurrence(
            Parse(firstOccurrence),
            recurringSettings,
            futureOccurrenceOnly: futureOccurrenceOnly);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(nextOccurrence, Is.EqualTo(Parse(expectedNextOccurrence)));

                if (futureOccurrenceOnly)
                {
                    Assert.That(nextOccurrence, Is.GreaterThan(_timeProvider.UtcNow));
                }
                else
                {
                    Assert.That(nextOccurrence, Is.GreaterThan(Parse(firstOccurrence)));
                    Assert.That(nextOccurrence.Year - Parse(firstOccurrence).Year, Is.EqualTo(1));
                }
            });
    }

    [Test]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-03T12:34:56+00:00",
        1,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-03T12:34:56+00:00",
        3,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-03T12:34:56+00:00",
        5,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        1,
        true)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        3,
        true)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        5,
        true)]
    public void GetNextOccurrence_GivenCustomDaysRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        int intervalValue,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Custom,
            intervalValue,
            null,
            null,
            null);

        // Act
        for (var offset = 0; offset < 13; offset++)
        {
            var nextOccurrence = parser.GetNextOccurrence(
                Parse(firstOccurrence),
                recurringSettings,
                offset,
                futureOccurrenceOnly);

            var expectedNextOccurrence = Parse(firstOccurrence).AddDays((offset + 1) * intervalValue);

            // Assert
            Assert.Multiple(
                () =>
                {
                    Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
                    Assert.That((nextOccurrence - Parse(firstOccurrence)).TotalDays, Is.EqualTo((offset + 1) * intervalValue));
                });
        }
    }

    [Test]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-03T12:34:56+00:00",
        1,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-03T12:34:56+00:00",
        3,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-03T12:34:56+00:00",
        5,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        1,
        true)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        3,
        true)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2024-07-03T12:34:56+00:00",
        5,
        true)]
    public void GetNextOccurrence_GivenCustomWeeksRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        int intervalValue,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Custom,
            null,
            intervalValue,
            null,
            null);

        // Act
        for (var offset = 0; offset < 13; offset++)
        {
            var nextOccurrence = parser.GetNextOccurrence(
                Parse(firstOccurrence),
                recurringSettings,
                offset,
                futureOccurrenceOnly);

            var expectedNextOccurrence = Parse(firstOccurrence).AddDays((offset + 1) * intervalValue * 7);

            // Assert
            Assert.Multiple(
                () =>
                {
                    Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
                    Assert.That((nextOccurrence - Parse(firstOccurrence)).TotalDays, Is.EqualTo((offset + 1) * intervalValue * 7));
                });
        }
    }

    [Test]
    [TestCase(
        "2025-07-03T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        1,
        false)]
    [TestCase(
        "2024-07-03T13:56:30+00:00",
        "2023-07-31T12:34:56+00:00",
        2,
        false)]
    [TestCase(
        "2025-07-03T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        3,
        false)]
    [TestCase(
        "2024-03-07T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        1,
        true)]
    [TestCase(
        "2024-04-03T13:56:30+00:00",
        "2024-03-31T12:34:56+00:00",
        2,
        true)]
    [TestCase(
        "2024-03-04T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        3,
        false)]
    public void GetNextOccurrence_GivenCustomMonthsRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        int intervalValue,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Custom,
            null,
            null,
            intervalValue,
            null);

        // Act
        for (var offset = 0; offset < 13; offset++)
        {
            var nextOccurrence = parser.GetNextOccurrence(
                Parse(firstOccurrence),
                recurringSettings,
                offset,
                futureOccurrenceOnly);

            var expectedNextOccurrence = Parse(firstOccurrence).AddMonths((offset + 1) * intervalValue);

            // Assert
            Assert.Multiple(
                () =>
                {
                    Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
                    Assert.That(
                        nextOccurrence.Month - Parse(firstOccurrence).Month
                        + (12 * (nextOccurrence.Year - Parse(firstOccurrence).Year)),
                        Is.EqualTo((offset + 1) * intervalValue));

                    var firstOccurrenceDateTime = Parse(firstOccurrence);

                    if (nextOccurrence.Day != firstOccurrenceDateTime.Day)
                    {
                        Assert.That(
                            DateTime.DaysInMonth(nextOccurrence.Year, nextOccurrence.Month),
                            Is.LessThan(firstOccurrenceDateTime.Day));
                        Assert.That(
                            nextOccurrence.Day,
                            Is.EqualTo(DateTime.DaysInMonth(nextOccurrence.Year, nextOccurrence.Month)));
                    }
                });
        }
    }

    [Test]
    [TestCase(
        "2024-05-20T11:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2024-05-20T12:34:56+00:00",
        2)] // Future occurrence + Already on scheduled date, but before scheduled time
    [TestCase(
        "2024-07-01T13:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2024-07-20T12:34:56+00:00",
        2)] // Future occurrence + 2nd time recurring
    [TestCase(
        "2024-07-20T11:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2024-07-20T12:34:56+00:00",
        2)] // Future occurrence + 2nd time recurring + Already on scheduled date, but before scheduled time
    [TestCase(
        "2024-05-20T13:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2024-07-20T12:34:56+00:00",
        2)] // Future occurrence + Just passed previous occurrence
    public void GetNextOccurrence_GivenCustomMonthsRecurringSettings_EdgeCases_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        string expectedNextOccurrence,
        int intervalValue)
   {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Custom,
            null,
            null,
            intervalValue,
            null);

        // Act
        var nextOccurrence = parser.GetNextOccurrence(
            Parse(firstOccurrence),
            recurringSettings,
            0,
            true);

        // Assert
        Assert.That(nextOccurrence, Is.EqualTo(Parse(expectedNextOccurrence)));
    }

    [Test]
    [TestCase(
        "2025-03-07T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        1,
        false)]
    [TestCase(
        "2024-03-07T13:56:30+00:00",
        "2020-02-29T12:34:56+00:00",
        2,
        false)]
    [TestCase(
        "2024-03-07T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        1,
        true)]
    [TestCase(
        "2025-02-01T13:56:30+00:00",
        "2024-02-29T12:34:56+00:00",
        1,
        true)]
    [TestCase(
        "2020-03-07T13:56:30+00:00",
        "2020-02-29T12:34:56+00:00",
        2,
        true)]
    public void GetNextOccurrence_GivenCustomYearsRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        int intervalValue,
        bool futureOccurrenceOnly)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Custom,
            null,
            null,
            null,
            intervalValue);

        // Act
        for (var offset = 0; offset < 13; offset++)
        {
            var nextOccurrence = parser.GetNextOccurrence(
                Parse(firstOccurrence),
                recurringSettings,
                offset,
                futureOccurrenceOnly);

            var expectedNextOccurrence = Parse(firstOccurrence).AddYears((offset + 1) * intervalValue);

            // Assert
            Assert.Multiple(
                () =>
                {
                    Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
                    Assert.That(nextOccurrence.Year - Parse(firstOccurrence).Year, Is.EqualTo((offset + 1) * intervalValue));

                    var firstOccurrenceDateTime = Parse(firstOccurrence);

                    if (nextOccurrence.Day != firstOccurrenceDateTime.Day)
                    {
                        Assert.That(
                            DateTime.DaysInMonth(nextOccurrence.Year, nextOccurrence.Month),
                            Is.LessThan(firstOccurrenceDateTime.Day));
                        Assert.That(
                            nextOccurrence.Day,
                            Is.EqualTo(DateTime.DaysInMonth(nextOccurrence.Year, nextOccurrence.Month)));
                    }
                });
        }
    }

    [Test]
    [TestCase(
        "2026-03-20T11:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2026-03-20T12:34:56+00:00",
        2)] // Future occurrence + Already on scheduled date, but before scheduled time
    [TestCase(
        "2028-03-01T13:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2028-03-20T12:34:56+00:00",
        2)] // Future occurrence + 2nd time recurring
    [TestCase(
        "2028-03-20T11:56:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2028-03-20T12:34:56+00:00",
        2)] // Future occurrence + 2nd time recurring + Already on scheduled date, but before scheduled time
    [TestCase(
        "2026-03-20T12:35:30+00:00",
        "2024-03-20T12:34:56+00:00",
        "2028-03-20T12:34:56+00:00",
        2)] // Future occurrence + Just passed previous occurrence
    public void GetNextOccurrence_GivenCustomYearsRecurringSettings_EdgeCases_ShouldReturnCorrectNextOccurrence(
        string currentDateTime,
        string firstOccurrence,
        string expectedNextOccurrence,
        int intervalValue)
    {
        // Arrange
        _timeProvider.UtcNow.Returns(Parse(currentDateTime));
        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);

        var recurringSettings = new WorkflowRecurringSettings(
            WorkflowRecurringTypes.Custom,
            null,
            null,
            null,
            intervalValue);

        // Act
        var nextOccurrence = parser.GetNextOccurrence(
            Parse(firstOccurrence),
            recurringSettings,
            0,
            true);

        // Assert
        Assert.That(nextOccurrence, Is.EqualTo(Parse(expectedNextOccurrence)));
    }

    private static DateTimeOffset Parse(string input)
        => DateTimeOffset.Parse(
            input,
            DateTimeFormatInfo.InvariantInfo,
            DateTimeStyles.AdjustToUniversal);

    #region GetNextOccurrenceAfter Tests

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-01T10:00:00+00:00", // currentTimeUtc (before first)
        "2024-07-03T12:34:56+00:00")] // expectedNextOccurrence (returns firstOccurrence due to optimization)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-04T13:56:30+00:00", // currentTimeUtc (after first, before next)
        "2024-07-10T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 7 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-10T11:56:30+00:00", // currentTimeUtc (on the day, before the time)
        "2024-07-10T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 7 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-10T12:34:56+00:00", // currentTimeUtc (exactly at occurrence time)
        "2024-07-17T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 14 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-16T11:56:30+00:00", // currentTimeUtc (before 2nd occurrence)
        "2024-07-17T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 14 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-17T12:34:57+00:00", // currentTimeUtc (just after 2nd occurrence)
        "2024-07-24T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 21 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-08-01T00:00:00+00:00", // currentTimeUtc (far in future)
        "2024-08-07T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 5*7 days)
    public void GetNextOccurrenceAfter_GivenWeeklyRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string firstOccurrenceStr,
        string currentTimeUtcStr,
        string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Weekly, null, null, null, null);

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            // Ensure it's always >= firstOccurrence and > currentTimeUtc
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

    [Test]
    [TestCase(
        "2024-06-30T12:34:56+00:00", // firstOccurrence
        "2024-06-25T10:00:00+00:00", // currentTimeUtc (before first)
        "2024-06-30T12:34:56+00:00")] // expectedNextOccurrence (Optimization path)
    [TestCase(
        "2024-06-30T12:34:56+00:00", // firstOccurrence
        "2024-07-04T13:56:30+00:00", // currentTimeUtc (after first, before next)
        "2024-07-30T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds first > current)
    [TestCase(
        "2024-06-30T12:34:56+00:00", // firstOccurrence
        "2024-07-30T12:34:56+00:00", // currentTimeUtc (exactly at occurrence time)
        "2024-08-30T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds next > current)
    [TestCase(
        "2024-06-30T12:34:56+00:00", // firstOccurrence
        "2024-07-30T12:34:57+00:00", // currentTimeUtc (just after first occurrence)
        "2024-08-30T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds next > current)
    [TestCase(
        "2024-01-31T12:34:56+00:00", // firstOccurrence (end of month)
        "2024-02-15T00:00:00+00:00", // currentTimeUtc (mid Feb)
        "2024-02-29T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds leap year end)
    [TestCase(
        "2023-01-31T12:34:56+00:00", // firstOccurrence (end of month)
        "2023-02-15T00:00:00+00:00", // currentTimeUtc (mid Feb)
        "2023-02-28T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds non-leap year end)
    [TestCase(
        "2024-08-31T12:34:56+00:00", // firstOccurrence (end of month)
        "2024-09-15T00:00:00+00:00", // currentTimeUtc (mid Sep)
        "2024-09-30T12:34:56+00:00")] // expectedNextOccurrence (Iterative: adjusts to 30)
    [TestCase(
        "2024-06-30T12:34:56+00:00", // firstOccurrence
        "2025-01-01T00:00:00+00:00", // currentTimeUtc (far in future)
        "2025-01-30T12:34:56+00:00")] // expectedNextOccurrence (Iterative loop)
    public void GetNextOccurrenceAfter_GivenMonthlyRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string firstOccurrenceStr,
        string currentTimeUtcStr,
        string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Monthly, null, null, null, null);

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-01T10:00:00+00:00", // currentTimeUtc (before first)
        "2024-07-03T12:34:56+00:00")] // expectedNextOccurrence (Optimization path)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-08-01T00:00:00+00:00", // currentTimeUtc (after first, before next)
        "2025-07-03T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds next year)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2025-07-03T12:34:56+00:00", // currentTimeUtc (exactly at occurrence time)
        "2026-07-03T12:34:56+00:00")] // expectedNextOccurrence (Iterative: finds next year)
    [TestCase(
        "2024-02-29T12:34:56+00:00", // firstOccurrence (leap day)
        "2025-01-01T00:00:00+00:00", // currentTimeUtc
        "2025-02-28T12:34:56+00:00")] // expectedNextOccurrence (Iterative: adjusts in non-leap year)
    [TestCase(
        "2024-02-29T12:34:56+00:00", // firstOccurrence (leap day)
        "2028-01-01T00:00:00+00:00", // currentTimeUtc
        "2028-02-29T12:34:56+00:00")] // expectedNextOccurrence (Iterative: lands on leap day again)
    [TestCase(
        "2022-12-29T12:34:56+00:00", // firstOccurrence
        "2026-01-01T00:00:00+00:00", // currentTimeUtc (far in future)
        "2026-12-29T12:34:56+00:00")] // expectedNextOccurrence (Iterative loop)
    public void GetNextOccurrenceAfter_GivenYearlyRecurringSettings_ShouldReturnCorrectNextOccurrence(
       string firstOccurrenceStr,
       string currentTimeUtcStr,
       string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Yearly, null, null, null, null);

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-01T00:00:00+00:00", // currentTimeUtc (before first)
        3, // Interval Days
        "2024-07-03T12:34:56+00:00")] // expectedNextOccurrence (Optimization path)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-06T12:34:56+00:00", // currentTimeUtc (exactly at occurrence time)
        3, // Interval Days
        "2024-07-09T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 2*3 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-07T00:00:00+00:00", // currentTimeUtc (after first occurrence, before second)
        3, // Interval Days
        "2024-07-09T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 2*3 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-08-01T00:00:00+00:00", // currentTimeUtc (far in future)
        5, // Interval Days
        "2024-08-02T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 6*5 days)
    public void GetNextOccurrenceAfter_GivenCustomDaysRecurringSettings_ShouldReturnCorrectNextOccurrence(
       string firstOccurrenceStr,
       string currentTimeUtcStr,
       int intervalValue,
       string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Custom, intervalValue, null, null, null);

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-06-20T00:00:00+00:00", // currentTimeUtc (before first)
        2, // Interval Weeks
        "2024-07-03T12:34:56+00:00")] // expectedNextOccurrence (Optimization path)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-17T12:34:56+00:00", // currentTimeUtc (exactly at first occurrence after start: first + 14 days)
        2, // Interval Weeks
        "2024-07-31T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 2*14 days)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2024-07-20T00:00:00+00:00", // currentTimeUtc (after first occurrence, before second)
        2, // Interval Weeks
        "2024-07-31T12:34:56+00:00")] // expectedNextOccurrence (Calculated: first + 2*14 days)
    public void GetNextOccurrenceAfter_GivenCustomWeeksRecurringSettings_ShouldReturnCorrectNextOccurrence(
       string firstOccurrenceStr,
       string currentTimeUtcStr,
       int intervalValue,
       string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Custom, null, intervalValue, null, null);

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

        [Test]
    [TestCase(
        "2024-06-15T10:00:00+00:00", // firstOccurrence
        "2024-05-01T00:00:00+00:00", // currentTimeUtc (before first)
        2, // Interval Months
        "2024-06-15T10:00:00+00:00")] // expectedNextOccurrence (Optimization path)
    [TestCase(
        "2024-06-15T10:00:00+00:00", // firstOccurrence
        "2024-08-15T10:00:00+00:00", // currentTimeUtc (exactly at first occurrence)
        2, // Interval Months
        "2024-10-15T10:00:00+00:00")] // expectedNextOccurrence (first + 2*2 months)
    [TestCase(
        "2024-06-15T10:00:00+00:00", // firstOccurrence
        "2024-09-01T00:00:00+00:00", // currentTimeUtc (after first, before second)
        2, // Interval Months
        "2024-10-15T10:00:00+00:00")] // expectedNextOccurrence (first + 2*2 months)
    [TestCase(
        "2024-01-31T10:00:00+00:00", // firstOccurrence (end of month)
        "2024-05-01T00:00:00+00:00", // currentTimeUtc
        3, // Interval Months (Jan->Apr->Jul)
        "2024-07-31T10:00:00+00:00")] // expectedNextOccurrence (first + 2*3 months)
    [TestCase(
        "2024-01-31T10:00:00+00:00", // firstOccurrence (end of month)
        "2024-08-01T00:00:00+00:00", // currentTimeUtc
        3, // Interval Months (Jan->Apr->Jul->Oct)
        "2024-10-31T10:00:00+00:00")] // expectedNextOccurrence (first + 3*3 months)
    [TestCase(
        "2024-06-15T10:00:00+00:00", // firstOccurrence
        "2025-12-01T00:00:00+00:00", // currentTimeUtc (far future)
        4, // Interval Months (Jun->Oct->Feb'25->Jun'25->Oct'25->Feb'26)
        "2026-02-15T10:00:00+00:00")] // expectedNextOccurrence (first + 5*4 months)
    public void GetNextOccurrenceAfter_GivenCustomMonthsRecurringSettings_ShouldReturnCorrectNextOccurrence(
        string firstOccurrenceStr,
        string currentTimeUtcStr,
        int intervalValue,
        string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Custom, null, null, intervalValue, null); // Month interval

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

    [Test]
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2023-01-01T00:00:00+00:00", // currentTimeUtc (before first)
        2, // Interval Years
        "2024-07-03T12:34:56+00:00")] // expectedNextOccurrence (Optimization path)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2026-07-03T12:34:56+00:00", // currentTimeUtc (exactly at first occurrence)
        2, // Interval Years
        "2028-07-03T12:34:56+00:00")] // expectedNextOccurrence (first + 2*2 years)
    [TestCase(
        "2024-07-03T12:34:56+00:00", // firstOccurrence
        "2027-01-01T00:00:00+00:00", // currentTimeUtc (after first, before second)
        2, // Interval Years
        "2028-07-03T12:34:56+00:00")] // expectedNextOccurrence (first + 2*2 years)
    [TestCase(
        "2024-02-29T12:00:00+00:00", // firstOccurrence (leap day)
        "2029-01-01T00:00:00+00:00", // currentTimeUtc
        3, // Interval Years (2024->2027->2030)
        "2030-02-28T12:00:00+00:00")] // expectedNextOccurrence (first + 2*3 years, adjusts day)
    [TestCase(
        "2024-02-29T12:00:00+00:00", // firstOccurrence (leap day)
        "2033-01-01T00:00:00+00:00", // currentTimeUtc
        4, // Interval Years (2024->2028->2032->2036)
        "2036-02-29T12:00:00+00:00")] // expectedNextOccurrence (first + 3*4 years, lands on leap day)
    public void GetNextOccurrenceAfter_GivenCustomYearsRecurringSettings_ShouldReturnCorrectNextOccurrence(
       string firstOccurrenceStr,
       string currentTimeUtcStr,
       int intervalValue,
       string expectedNextOccurrenceStr)
    {
        // Arrange
        var firstOccurrence = Parse(firstOccurrenceStr);
        var currentTimeUtc = Parse(currentTimeUtcStr);
        var expectedNextOccurrence = Parse(expectedNextOccurrenceStr);

        IWorkflowRecurringSettingsParser parser = new WorkflowRecurringSettingsParser(_timeProvider);
        var recurringSettings = new WorkflowRecurringSettings(WorkflowRecurringTypes.Custom, null, null, null, intervalValue); // Year interval

        // Act
        var nextOccurrence = parser.GetNextOccurrenceAfter(firstOccurrence, recurringSettings, currentTimeUtc);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(nextOccurrence, Is.EqualTo(expectedNextOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThanOrEqualTo(firstOccurrence));
            Assert.That(nextOccurrence, Is.GreaterThan(currentTimeUtc));
        });
    }

    #endregion
}