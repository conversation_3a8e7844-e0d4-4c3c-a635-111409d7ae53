using System.Collections.Concurrent;
using System.ComponentModel;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Plugins;
using Description = System.ComponentModel.DescriptionAttribute;

namespace Sleekflow.IntelligentHub.Evaluator;

public class MockChiliPiperPlugin : IChiliPiperPlugin
{
    private readonly ILogger<MockChiliPiperPlugin> _logger;

    // Store parameters by test ID
    private static readonly ConcurrentDictionary<string, ChiliPiperRequestParams> RequestParams = new ();

    public MockChiliPiperPlugin(ILogger<MockChiliPiperPlugin> logger)
    {
        _logger = logger;
    }

    [KernelFunction("schedule_demo_with_chili_piper")]
    [Description("Schedules a demo using Chili Piper API.")]
    public Task<string> ScheduleDemoWithChiliPiper(
        Kernel kernel,
        [Description("List of fields with names and values to send to Chili Piper")]
        List<LeadNurturingAgentDefinitions.FieldInfo> fields)
    {
        // Extract test ID from the context
        var testId = ChatEvalTest.CurrentTestId.Value;

        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;
        var tools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;
        var demoTool = tools?.DemoTool;

        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping parameter capture");
            return Task.FromResult("Demo Scheduled (Test ID Missing)");
        }

        // Convert list of fields to dictionary
        var fieldsDictionary = fields.ToDictionary(f => f.FieldName, f => f.FieldValue);

        // Create the request body for Chili Piper API (same logic as real implementation)
        var formData = new Dictionary<string, string>();

        // Map the fields based on configuration or use directly if no mapping available
        if (demoTool?.ChiliPiperConfig?.FieldMappings != null)
        {
            foreach (var mapping in demoTool.ChiliPiperConfig.FieldMappings)
            {
                if (fieldsDictionary.TryGetValue(mapping.Key, out var value))
                {
                    formData[mapping.Value] = value;
                }
            }
        }
        else
        {
            // If no mapping defined, use fields directly
            formData = fieldsDictionary;
        }

        // Create the payload structure
        var payload = new
        {
            form = formData,
            options = new
            {
            }
        };

        // Store the parameters for later verification
        RequestParams[testId] = new ChiliPiperRequestParams
        {
            OriginalFields = new Dictionary<string, string>(fieldsDictionary),
            MappedFields = new Dictionary<string, string>(formData),
            Url = demoTool?.ChiliPiperConfig?.ApiUrl ?? "https://default-test-url.com"
        };

        _logger.LogInformation(
            "Mock ChiliPiper call for test {TestId}: Parameters: {Parameters}",
            testId,
            JsonConvert.SerializeObject(payload));

        // Create a mock response similar to the real ChiliPiper API response
        var mockDemoId = Guid.NewGuid().ToString();
        var mockResponse = new
        {
            routingLink =
                $"https://fire.chilipiper.com/concierge-router/demo/routing/{mockDemoId}?dynamicRedirectLink=http%3A%2F%2Fsleekflow.com",
            url = $"https://calendar.chilipiper.com/booking/demo-link/{mockDemoId}",
            timeoutInMS = 600000,
            timeoutRedirectUrl = (string?) null
        };

        var responseJson = JsonConvert.SerializeObject(mockResponse);
        return Task.FromResult(
            $"The information has been sent to Chili Piper. Use the link to choose a time for the demo. {responseJson}");
    }

    /// <summary>
    /// Retrieves the parameters sent for a specific test.
    /// </summary>
    public static ChiliPiperRequestParams? GetRequestParams(string testId)
    {
        RequestParams.TryGetValue(testId, out var parameters);
        return parameters;
    }

    /// <summary>
    /// Clears the stored parameters for a specific test.
    /// </summary>
    public static void ClearTestState(string testId)
    {
        RequestParams.TryRemove(testId, out _);
    }
}

public class ChiliPiperRequestParams
{
    public Dictionary<string, string> OriginalFields { get; set; } = new ();

    public Dictionary<string, string> MappedFields { get; set; } = new ();

    public string Url { get; set; } = string.Empty;
}