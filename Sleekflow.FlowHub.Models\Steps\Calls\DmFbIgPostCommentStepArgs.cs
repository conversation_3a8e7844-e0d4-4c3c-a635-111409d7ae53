using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class DmFbIgPostCommentStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.send-dm";

    [Required]
    [JsonProperty("dm_type")]
    public string DmType { get; set; }

    [JsonProperty("dm_text")]
    public string DmText { get; set; }

    [JsonProperty("media_parameters")]
    public MediaParameters? MediaParameters { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.FacebookIntegration;





}