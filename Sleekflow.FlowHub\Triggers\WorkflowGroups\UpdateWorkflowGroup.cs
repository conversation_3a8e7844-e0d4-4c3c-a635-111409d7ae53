﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowGroups;
using Sleekflow.FlowHub.WorkflowGroups;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowGroups;

[TriggerGroup(ControllerNames.WorkflowGroups)]
public class UpdateWorkflowGroup
    : ITrigger<
        UpdateWorkflowGroup.UpdateWorkflowGroupInput,
        UpdateWorkflowGroup.UpdateWorkflowGroupOutput>
{
    private readonly IWorkflowGroupService _workflowGroupService;

    public UpdateWorkflowGroup(
        IWorkflowGroupService workflowGroupService)
    {
        _workflowGroupService = workflowGroupService;
    }

    public class UpdateWorkflowGroupInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [Required]
        [MinLength(1)]
        [MaxLength(100)]
        [JsonProperty("name")]
        public string Name { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateWorkflowGroupInput(
            string workflowGroupId,
            string name,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            WorkflowGroupId = workflowGroupId;
            Name = name;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateWorkflowGroupOutput
    {
        [Required]
        [JsonProperty("workflow_group")]
        public WorkflowGroupDto WorkflowGroup { get; set; }

        [JsonConstructor]
        public UpdateWorkflowGroupOutput(
            WorkflowGroupDto workflowGroup)
        {
            WorkflowGroup = workflowGroup;
        }
    }

    public async Task<UpdateWorkflowGroupOutput> F(UpdateWorkflowGroupInput input)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var updatedWorkflowGroup = await _workflowGroupService.UpdateWorkflowGroupAsync(
            input.SleekflowCompanyId,
            input.WorkflowGroupId,
            input.Name,
            sleekflowStaff);

        return new UpdateWorkflowGroupOutput(new WorkflowGroupDto(updatedWorkflowGroup));
    }
}