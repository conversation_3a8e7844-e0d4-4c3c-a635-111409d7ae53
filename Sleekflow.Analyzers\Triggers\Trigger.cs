using System.Linq;

namespace Sleekflow.Analyzers.Triggers;

public readonly record struct Trigger(
    string? BaseRoute,
    string GroupName,
    string NamespaceName,
    string ClassName,
    string VariableName)
{
    public string? BaseRoute { get; } = BaseRoute;

    public string GroupName { get; } = GroupName;

    public string NamespaceName { get; } = NamespaceName;

    public string ClassName { get; } = ClassName;

    public string VariableName { get; } = VariableName;

    public string ControllerName
    {
        get
        {
            var parentGroupName = BaseRoute != null
                ? string.Join(
                    string.Empty,
                    BaseRoute
                        .Split('/', '-')
                        .Select(r => r.First().ToString().ToUpper() + r.Substring(1)))
                : string.Empty;

            return parentGroupName + GroupName;
        }
    }
}