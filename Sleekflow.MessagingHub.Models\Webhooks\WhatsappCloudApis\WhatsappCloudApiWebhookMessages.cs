using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;

public class WhatsappCloudApiWebhookMessages
{
    [JsonProperty("object")]
    public string WhatsappCloudApiObject { get; set; }

    [JsonProperty("entry")]
    public List<WhatsappCloudApiEntry> WhatsappCloudApiEntries { get; set; }

    public WhatsappCloudApiWebhookMessages(
        string whatsappCloudApiObject,
        List<WhatsappCloudApiEntry> whatsappCloudApiEntries)
    {
        WhatsappCloudApiObject = whatsappCloudApiObject;
        WhatsappCloudApiEntries = whatsappCloudApiEntries;
    }

    public class WhatsappCloudApiEntry
    {
        [JsonProperty("id")]
        public string FacebookWabaId { get; set; }

        [JsonProperty("changes")]
        public List<WhatsappCloudApiChange> Changes { get; set; }

        public WhatsappCloudApiEntry(
            string facebookWabaId,
            List<WhatsappCloudApiChange> changes)
        {
            FacebookWabaId = facebookWabaId;
            Changes = changes;
        }
    }

    public class WhatsappCloudApiChange
    {
        [JsonProperty("value")]
        public object Value { get; set; }

        [JsonProperty("field")]
        public string Field { get; set; }

        public WhatsappCloudApiChange(
            object value,
            string field)
        {
            Value = value;
            Field = field;
        }
    }
}