﻿using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Providers;

public interface IProviderSelector
{
    IProviderService GetProviderService(string providerName);
}

public class ProviderSelector : IProviderSelector, ISingletonService
{
    private readonly SalesforceIntegratorService _salesforceIntegratorService;
    private readonly HubspotIntegratorService _hubspotIntegratorService;
    private readonly Dynamics365IntegratorService _dynamics365IntegratorService;
    private readonly GoogleSheetsIntegratorService _googleSheetsIntegratorService;
    private readonly ZohoIntegratorService _zohoIntegratorService;

    public ProviderSelector(
        SalesforceIntegratorService salesforceIntegratorService,
        HubspotIntegratorService hubspotIntegratorService,
        Dynamics365IntegratorService dynamics365IntegratorService,
        GoogleSheetsIntegratorService googleSheetsIntegratorService,
        ZohoIntegratorService zohoIntegratorService)
    {
        _salesforceIntegratorService = salesforceIntegratorService;
        _hubspotIntegratorService = hubspotIntegratorService;
        _dynamics365IntegratorService = dynamics365IntegratorService;
        _googleSheetsIntegratorService = googleSheetsIntegratorService;
        _zohoIntegratorService = zohoIntegratorService;
    }

    public IProviderService GetProviderService(string providerName)
    {
        IProviderService providerService = providerName switch
        {
            SalesforceIntegratorService.ProviderName => _salesforceIntegratorService,
            HubspotIntegratorService.ProviderName => _hubspotIntegratorService,
            Dynamics365IntegratorService.ProviderName => _dynamics365IntegratorService,
            GoogleSheetsIntegratorService.ProviderName => _googleSheetsIntegratorService,
            ZohoIntegratorService.ProviderName => _zohoIntegratorService,
            _ => throw new NotImplementedException()
        };

        return providerService;
    }
}