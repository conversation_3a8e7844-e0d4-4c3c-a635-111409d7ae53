using Newtonsoft.Json;

namespace Sleekflow.Infras.Components.Auth0s;

public class PromptsConfigs
{
    [<PERSON>son<PERSON>roperty("login")]
    public Dictionary<string, string> Login { get; set; }

    [JsonProperty("login-id")]
    public Dictionary<string, string> LoginId { get; set; }

    [JsonProperty("login-password")]
    public Dictionary<string, string> LoginPassword { get; set; }

    [Json<PERSON>roperty("email-verification")]
    public Dictionary<string, object> EmailVerification { get; set; }

    [JsonProperty("signup")]
    public Dictionary<string, string> Signup { get; set; }

    [JsonProperty("signup-id")]
    public Dictionary<string, string> SignupId { get; set; }

    [JsonProperty("signup-password")]
    public Dictionary<string, string> SignupPassword { get; set; }

    [JsonProperty("reset-password")]
    public Dictionary<string, object> ResetPassword { get; set; }

    [Json<PERSON>roperty("mfa-otp")]
    public Dictionary<string, object> MfaOtp { get; set; }

    [JsonConstructor]
    public PromptsConfigs(
        Dictionary<string, string> login,
        Dictionary<string, string> loginId,
        Dictionary<string, string> loginPassword,
        Dictionary<string, string> signupId,
        Dictionary<string, string> signupPassword,
        Dictionary<string, string> signup,
        Dictionary<string, object> resetPassword,
        Dictionary<string, object> mfaOtp,
        Dictionary<string, object> emailVerification)
    {
        Login = login;
        LoginId = loginId;
        LoginPassword = loginPassword;
        SignupId = signupId;
        SignupPassword = signupPassword;
        Signup = signup;
        ResetPassword = resetPassword;
        MfaOtp = mfaOtp;
        EmailVerification = emailVerification;
    }
}