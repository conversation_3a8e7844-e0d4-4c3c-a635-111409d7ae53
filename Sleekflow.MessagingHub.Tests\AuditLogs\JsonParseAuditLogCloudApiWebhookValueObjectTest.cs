using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Audits;

namespace Sleekflow.MessagingHub.Tests.AuditLogs;

public class JsonParseAuditLogCloudApiWebhookValueObjectTest
{
    [TestCase(
        "{\"display_phone_number\": \"6281112017069\", \"event\": \"PIN_RESET_REQUEST\", \"requester\": \"61552501201573\"}")]
    [TestCase("{\"display_phone_number\": \"PHONE_NUMBER\",\"event\": \"FLAGGED\",\"current_limit\": \"TIER_10K\"}")]
    [TestCase(
        "{\"messaging_product\":null,\"metadata\":null,\"contacts\":null,\"messages\":null,\"statuses\":null,\"entity_type\":null,\"entity_id\":null,\"alert_severity\":null,\"alert_status\":null,\"alert_type\":null,\"alert_description\":null,\"decision\":null,\"event\":\"APPROVED\",\"business_verification_status\":null,\"waba_info\":null,\"phone_number\":null,\"ban_info\":null,\"violation_info\":null,\"lock_info\":null,\"restriction_info\":null,\"partner_client_certification_info\":null,\"max_daily_conversation_per_phone\":null,\"max_phone_numbers_per_waba\":null,\"max_phone_numbers_per_business\":null,\"business_id\":null,\"campaign_id\":null,\"campaign_name\":null,\"old_status\":null,\"new_status\":null,\"paused_reasons\":null,\"complete_reason\":null,\"message\":null,\"flow_id\":null,\"p90_latency\":null,\"p50_latency\":null,\"request_count\":null,\"error_rate\":null,\"threshold\":null,\"availability\":null,\"alert_state\":null,\"errors\":null,\"message_template_id\":\"400532802938312\",\"message_template_name\":\"labor_day_promotion\",\"message_template_language\":\"en\",\"previous_category\":null,\"new_category\":null,\"previous_quality_score\":null,\"new_quality_score\":null,\"reason\":\"NONE\",\"disable_info\":null,\"other_info\":null,\"display_phone_number\":null,\"requested_verified_name\":null,\"rejection_reason\":null,\"current_limit\":null,\"old_limit\":null,\"requester\":null}")]
    public void TestParseCloudApiWebhookValueJsonWithNonNullValues(string json)
    {
        var auditLogCloudApiWebhookValueObject =
            JsonConvert.DeserializeObject<AuditLogCloudApiWebhookValueObject>(json);
        var serialisedString = JsonConvert.SerializeObject(auditLogCloudApiWebhookValueObject);

        // check no any json field has null value
        Assert.That(serialisedString, Does.Not.Contain("null"));
    }
}