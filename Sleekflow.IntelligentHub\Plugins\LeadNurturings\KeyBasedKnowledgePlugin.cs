using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IKeyBasedKnowledgePlugin
{
    [KernelFunction("query_knowledge")]
    [Description(
        "Retrieves information from the knowledge base using data from data pane and stores the complete formatted knowledge result for other agents to use directly.")]
    [return:
        Description("Complete formatted knowledge result including tool outputs and confirmed knowledge sections.")]
    Task<QueryKnowledgeResponse> QueryKnowledgeWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where strategy results are stored in the data pane (optional, for context).")]
        string strategyKey = "");
}

public class KeyBasedKnowledgePlugin : BaseLeadNurturingPlugin, IKeyBasedKnowledgePlugin
{
    private readonly IAgenticKnowledgePlugin _agenticKnowledgePlugin;

    public KeyBasedKnowledgePlugin(
        ILogger<KeyBasedKnowledgePlugin> logger,
        IAgenticKnowledgePlugin agenticKnowledgePlugin,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
        _agenticKnowledgePlugin = agenticKnowledgePlugin;
    }

    [KernelFunction("query_knowledge")]
    [Description(
        "Retrieves information from the knowledge base using data from data pane and stores the complete formatted knowledge result for other agents to use directly.")]
    [return:
        Description("Complete formatted knowledge result including tool outputs and confirmed knowledge sections.")]
    public async Task<QueryKnowledgeResponse> QueryKnowledgeWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where strategy results are stored in the data pane (optional, for context).")]
        string strategyKey = "")
    {
        try
        {
            // 1. Retrieve data from data pane
            var conversationContext = await _dataPane.GetData(conversationContextKey, AgentOutputKeys.Conversation);

            if (string.IsNullOrEmpty(conversationContext))
            {
                throw new InvalidOperationException($"No conversation context found for key: {conversationContextKey}");
            }

            // 2. Get strategy info to extract knowledge query
            var strategyInfo = "";
            if (!string.IsNullOrEmpty(strategyKey))
            {
                strategyInfo = await _dataPane.GetData(strategyKey, AgentOutputKeys.StrategyComplete) ?? "";
            }

            // 3. Build knowledge query
            var knowledgeQuery = BuildKnowledgeQuery(conversationContext, strategyInfo);

            // 4. Query the knowledge base
            var knowledgeResult =
                (AgenticKnowledgePluginResponse) await _agenticKnowledgePlugin.QueryKnowledgeAsync(
                    _kernel,
                    knowledgeQuery);

            // 5. Store the complete formatted knowledge result for other agents to use directly
            if (!string.IsNullOrEmpty(knowledgeResult.Knowledge))
            {
                await _dataPane.StoreData(
                    _keyManager.GetKnowledgeKey(sessionKey),
                    AgentOutputKeys.KnowledgeComplete,
                    knowledgeResult.Knowledge);

                _logger.LogInformation(
                    "Knowledge retrieval completed for session {SessionKey}",
                    sessionKey);

                // Return the complete formatted knowledge result for other agents to use directly
                return knowledgeResult;
            }

            // No knowledge found - return empty knowledge message
            var noKnowledgeMessage = "No relevant knowledge found.";

            await _dataPane.StoreData(
                _keyManager.GetKnowledgeKey(sessionKey),
                AgentOutputKeys.KnowledgeComplete,
                noKnowledgeMessage);

            return new AgenticKnowledgePluginResponse(noKnowledgeMessage, Guid.NewGuid().ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Knowledge Plugin for session {SessionKey}", sessionKey);
            throw;
        }
    }

    private string BuildKnowledgeQuery(string conversationContext, string strategyInfo)
    {
        var queryBuilder = new List<string>();

        // Add conversation context for understanding what the customer is asking about
        queryBuilder.Add($"Customer conversation context:\n{conversationContext}");

        // Add strategy context if available
        if (!string.IsNullOrEmpty(strategyInfo))
        {
            queryBuilder.Add($"\nStrategy guidance context:\n{strategyInfo}");
        }

        // Add instruction for knowledge retrieval
        queryBuilder.Add(
            "\nPlease retrieve relevant information from the knowledge base that would help address the customer's needs and questions based on the conversation above.");

        return string.Join("\n", queryBuilder);
    }
}