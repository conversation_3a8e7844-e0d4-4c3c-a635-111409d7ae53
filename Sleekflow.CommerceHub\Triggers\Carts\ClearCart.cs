using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts;

[TriggerGroup(ControllerNames.Carts)]
public class ClearCart
    : ITrigger<
        ClearCart.ClearCartInput,
        ClearCart.ClearCartOutput>
{
    private readonly ICartService _cartService;
    private readonly IProductVariantService _productVariantService;
    private readonly IProductService _productService;

    public ClearCart(
        ICartService cartService,
        IProductVariantService productVariantService,
        IProductService productService)
    {
        _cartService = cartService;
        _productVariantService = productVariantService;
        _productService = productService;
    }

    public class ClearCartInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        [Required]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ClearCartInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ClearCartOutput
    {
        [JsonProperty("cart")]
        public CartDto? Cart { get; set; }

        [JsonConstructor]
        public ClearCartOutput(CartDto? cart)
        {
            Cart = cart;
        }
    }

    public async Task<ClearCartOutput> F(ClearCartInput clearCartInput)
    {
        var cart = await _cartService.ClearAndGetCartAsync(
            clearCartInput.SleekflowCompanyId,
            clearCartInput.StoreId,
            clearCartInput.SleekflowUserProfileId);
        if (cart == null)
        {
            return new ClearCartOutput(null);
        }

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            cart.LineItems.Select(x => x.ProductVariantId).ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        return new ClearCartOutput(
            new CartDto(
                cart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict));
    }
}