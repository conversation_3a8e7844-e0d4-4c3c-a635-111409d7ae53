using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace Sleekflow.FlowHub.Utils
{
    public static class DictionaryTransformUtils
    {
        /// <summary>
        /// Merges properties from a collection of dictionary-like objects (IDictionary<string, object> or JObject) into a single dictionary, including only specified fields.
        /// </summary>
        /// <param name="sourceValues">The collection of inner objects (values from a parent dictionary like state.SysVarDict.Values).</param>
        /// <param name="fieldsToInclude">A collection of keys for the fields to be included in the merged dictionary.</param>
        /// <returns>A new dictionary containing the merged and filtered key-value pairs.</returns>
        public static IDictionary<string, object> MergeAndFilterValues(
            IEnumerable<object?>? sourceValues,
            ICollection<string>? fieldsToInclude)
        {
            var mergedDictionary = new Dictionary<string, object>();

            if (sourceValues == null || fieldsToInclude == null || fieldsToInclude.Count == 0)
            {
                return mergedDictionary; // Return empty if no values or no fields to include
            }

            foreach (var innerDictionaryObject in sourceValues)
            {
                if (innerDictionaryObject == null)
                {
                    continue;
                }

                JObject? jObjectToProcess = null;

                if (innerDictionaryObject is string jsonString)
                {
                    try
                    {
                        jObjectToProcess = JObject.Parse(jsonString);
                    }
                    catch (Newtonsoft.Json.JsonReaderException)
                    {
                        // Handle or log invalid JSON string if necessary
                        continue;
                    }
                }
                else if (innerDictionaryObject is JObject directJObject)
                {
                    jObjectToProcess = directJObject;
                }

                if (innerDictionaryObject is IDictionary<string, object?> propertiesDict)
                {
                    foreach (var property in propertiesDict)
                    {
                        if (fieldsToInclude.Contains(property.Key) && property.Value != null)
                        {
                            mergedDictionary[property.Key] = property.Value;
                        }
                    }
                }
                else if (jObjectToProcess != null) // Process if it was a valid JSON string or a JObject
                {
                    foreach (var property in jObjectToProcess.Properties().Where(p => fieldsToInclude.Contains(p.Name)))
                    {
                        var value = property.Value?.ToObject<object>();
                        if (value != null)
                        {
                            mergedDictionary[property.Name] = value;
                        }
                    }
                }
            }

            return mergedDictionary;
        }
    }
}