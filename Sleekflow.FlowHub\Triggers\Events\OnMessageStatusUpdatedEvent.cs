using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    null,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnMessageStatusUpdatedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnMessageStatusUpdatedEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnMessageStatusUpdatedEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnMessageStatusUpdatedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnMessageStatusUpdatedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnMessageStatusUpdatedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnMessageStatusUpdatedEventOutput
    {
    }

    public async Task<OnMessageStatusUpdatedEventOutput> F(OnMessageStatusUpdatedEventInput onMessageSentEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onMessageSentEventInput.EventBody,
                onMessageSentEventInput.ContactId,
                "Contact",
                onMessageSentEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onMessageSentEventInput.SleekflowCompanyId));

        return new OnMessageStatusUpdatedEventOutput();
    }
}