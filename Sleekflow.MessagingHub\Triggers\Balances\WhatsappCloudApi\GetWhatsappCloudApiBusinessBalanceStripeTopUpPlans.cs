using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetWhatsappCloudApiBusinessBalanceStripeTopUpPlans
    : ITrigger<
        GetWhatsappCloudApiBusinessBalanceStripeTopUpPlans.GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput,
        GetWhatsappCloudApiBusinessBalanceStripeTopUpPlans.GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput>
{
    public class GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput
    {
        [JsonProperty("top_up_plans")]
        public List<StripeWhatsAppCreditTopUpPlan> TopUpPlans { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput(List<StripeWhatsAppCreditTopUpPlan> topUpPlans)
        {
            TopUpPlans = topUpPlans;
        }
    }

    public Task<GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput> F(
        GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput request)
    {
        return Task.FromResult(new GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput(StripeWhatsAppCreditTopUpPlans.TopUpPlans));
    }
}