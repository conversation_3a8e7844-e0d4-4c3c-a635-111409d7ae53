using System.Globalization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows.Settings;

namespace Sleekflow.FlowHub.Commons.Workflows;

public interface IDateTimePropertySettingsParser
{
    DateTimeOffset GetNextOccurrenceAfter(
        DateTimeOffset originalPropertyValue,
        BaseDateTimeSettings baseDateTimeSettings,
        WorkflowRecurringSettings recurringSettings,
        DateTimeOffset currentTimeUtc);
}

public class DateTimePropertySettingsParser : IDateTimePropertySettingsParser
{
    private readonly WorkflowRecurringSettingsV2Parser _workflowRecurringSettingsV2Parser;
    private readonly ILogger<DateTimePropertySettingsParser> _logger;

    public DateTimePropertySettingsParser(
        WorkflowRecurringSettingsV2Parser workflowRecurringSettingsV2Parser,
        ILogger<DateTimePropertySettingsParser> logger)
    {
        _workflowRecurringSettingsV2Parser = workflowRecurringSettingsV2Parser;
        _logger = logger;
    }

    public DateTimeOffset GetNextOccurrenceAfter(
        DateTimeOffset originalPropertyValue,
        BaseDateTimeSettings baseDateTimeSettings,
        WorkflowRecurringSettings recurringSettings,
        DateTimeOffset currentTimeUtc)
    {
        _logger.LogInformation(
            "Begin to get the next occurrence time. originalPropertyValue: {OriginalPropertyValue}, baseDateTimeSettings: {BaseDateTimeSettings}, recurringSettings: {RecurringSettings}, currentTimeUtc: {CurrentTimeUtc}",
            originalPropertyValue,
            baseDateTimeSettings,
            recurringSettings,
            currentTimeUtc);
        var newTimeByDateTimeSettings = GetNewTimeByDateTimeSettings(originalPropertyValue, baseDateTimeSettings);
        _logger.LogInformation("newTimeByDateTimeSettings: {NewTimeByDateTimeSettings}", newTimeByDateTimeSettings);
        if (newTimeByDateTimeSettings >= currentTimeUtc)
        {
            return newTimeByDateTimeSettings;
        }
        var nextOccurrenceAfter = _workflowRecurringSettingsV2Parser.GetNextOccurrenceAfter(
            newTimeByDateTimeSettings,
            recurringSettings,
            currentTimeUtc);
        _logger.LogInformation("nextOccurrence: {NextOccurrenceAfter}", nextOccurrenceAfter);
        return nextOccurrenceAfter;
    }

    private DateTimeOffset GetNewTimeByDateTimeSettings(
        DateTimeOffset originalPropertyValue,
        BaseDateTimeSettings baseDateTimeSettings)
    {
        // Step 1: Handle date adjustment
        DateTimeOffset adjustedDate = originalPropertyValue;

        if (baseDateTimeSettings.TriggerDateType == "before")
        {
            adjustedDate = SubtractDuration(originalPropertyValue, baseDateTimeSettings.TriggerDateDuration);
        }
        else if (baseDateTimeSettings.TriggerDateType == "after")
        {
            adjustedDate = AddDuration(originalPropertyValue, baseDateTimeSettings.TriggerDateDuration);
        }
        // else "exactly" - no date adjustment needed

        // Step 2: Handle time adjustment (UTC)
        DateTimeOffset finalDateTime = adjustedDate;

        if (baseDateTimeSettings.TriggerTimeType == "custom")
        {
            // Parse ISO 8601 UTC string (e.g., "2025-05-27T05:22:00.000Z")
            if (!DateTimeOffset.TryParse(
                    baseDateTimeSettings.TriggerCustomTime,
                    CultureInfo.InvariantCulture,
                    DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal,
                    out var customTimeUtc))
            {
                throw new ArgumentException($"Invalid UTC time format: {baseDateTimeSettings.TriggerCustomTime}");
            }

            // Combine adjusted date with custom UTC time, forcing result to UTC
            finalDateTime = new DateTimeOffset(
                adjustedDate.Year,
                adjustedDate.Month,
                adjustedDate.Day,
                customTimeUtc.Hour,
                customTimeUtc.Minute,
                customTimeUtc.Second,
                customTimeUtc.Millisecond,
                TimeSpan.Zero // UTC offset
            );
        }
        // else "exactly" - no time adjustment needed
        _logger.LogInformation(
            "get the first occurrence time: {FinalDateTime}. OriginalPropertyValue: {OriginalPropertyValue}, BaseDateTimeSettings: {BaseDateTimeSettings}",
            finalDateTime, originalPropertyValue, JsonConvert.SerializeObject(baseDateTimeSettings));
        return finalDateTime;
    }

    private DateTimeOffset AddDuration(DateTimeOffset date, object[] duration)
    {
        int amount = Convert.ToInt32(duration[0]);
        string unit = duration[1].ToString().ToLower();

        return unit switch
        {
            "day" => date.AddDays(amount),
            "week" => date.AddDays(amount * 7),
            "month" => date.AddMonths(amount),
            "year" => date.AddYears(amount),
            _ => throw new ArgumentException($"Unsupported duration unit: {unit}")
        };
    }

    private DateTimeOffset SubtractDuration(DateTimeOffset date, object[] duration)
    {
        int amount = Convert.ToInt32(duration[0]);
        string unit = duration[1].ToString().ToLower();

        return unit switch
        {
            "day" => date.AddDays(-amount),
            "week" => date.AddDays(-amount * 7),
            "month" => date.AddMonths(-amount),
            "year" => date.AddYears(-amount),
            _ => throw new ArgumentException($"Unsupported duration unit: {unit}")
        };
    }
}