using System.Reflection;
using MassTransit;
using Orleans.Configuration;
using Orleans.Serialization;
using Scrutor;
using Serilog;
using Sleekflow;
using Sleekflow.Caches;
using Sleekflow.Constants;
using Sleekflow.Events.QueueNameFormatter;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Commons.Configs;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.Executor.Consumers;
using Sleekflow.FlowHub.Executor.Saga;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.NeedConfigs;
using Sleekflow.FlowHub.Silos;
using Sleekflow.FlowHub.TravisBackend;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;
using Sleekflow.Mvc.Https;
using Sleekflow.Persistence.FlowHubDb;

#if SWAGGERGEN
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using System.Reflection;
using Moq;
#endif

const string appName = "SleekflowFlowHubExecutor";

MvcModules.BuildLogger(appName);

Log.Information("Starting web host");
var builder = WebApplication.CreateBuilder(args);
builder.Host.UseSerilog();
builder.Services.AddHttpContextAccessor();

MvcModules.BuildHealthCheck(builder.Services);
MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
MvcModules.BuildApiBehaviors(builder, list =>
{
    list.AddRange(new List<OpenApiServer>()
    {
        new OpenApiServer()
        {
            Description = "Local",
            Url = $"https://localhost:7081",
        },
        new OpenApiServer()
        {
            Description = "Dev Apigw",
            Url = $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/flow-hub-executor",
        },
        new OpenApiServer()
        {
            Description = "Prod Apigw",
            Url = $"https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub-executor",
        }
    });
}, Assembly.Load("Sleekflow.FlowHub"), Assembly.Load("Sleekflow.FlowHub.Models"));
#else
MvcModules.BuildApiBehaviors(builder);
#endif
Modules.BuildHttpClients(builder.Services);
Modules.BuildConfigs(builder.Services, Assembly.Load("Sleekflow.FlowHub"));
Modules.BuildServices(builder.Services, Assembly.Load("Sleekflow.FlowHub"), Assembly.Load("Sleekflow.FlowHub.Commons"));
Modules.BuildTriggers(builder.Services, Assembly.Load("Sleekflow.FlowHub"));

Modules.BuildServiceBusServices(
    builder.Services,
    x =>
    {
        var dbConfig = new FlowHubDbConfig();

        x.AddSagaStateMachine<AggregateStateMachine, AggregateState>(context =>
            {
                context.UseMessageRetry(r => r.Interval(5, 1000));
                context.UseInMemoryOutbox();

                var partition = context.CreatePartitioner(20);

                context.Message<OnAggregateStepEvent>(x => x.UsePartitioner(
                    partition,
                    m => m.Message.GetCorrelationId()));
            })
            .CosmosRepository(
                dbConfig.Endpoint,
                dbConfig.Key,
                r =>
                {
                    r.DatabaseId = ContainerNames.DatabaseId;
                    r.CollectionId = ContainerNames.AggregateSagaInstances;
                    r.UseNewtonsoftJson();
                });

        x.AddSagaStateMachine<StreamingRecommendedReplyStateMachine, StreamingRecommendedReplyState>(context =>
            {
                context.UseMessageRetry(r => r.Interval(5, 1000));
                context.UseInMemoryOutbox();
            })
            .CosmosRepository(
                dbConfig.Endpoint,
                dbConfig.Key,
                r =>
                {
                    r.DatabaseId = ContainerNames.DatabaseId;
                    r.CollectionId = ContainerNames.StreamingRecommendedReplySagaInstances;
                    r.UseNewtonsoftJson();
                });
    },
    (context, k) =>
    {
        k.ReceiveEndpoint(
            EventHubNames.FlowHubOnStepExecutionStatusChangedEventHubEvent,
            c =>
            {
                c.ConfigureConsumer<OnStepExecutionStatusChangedEventHubEventConsumer>(context);
            });
    },
    Assembly.Load("Sleekflow.FlowHub"),
    Assembly.Load("Sleekflow.FlowHub.Models"));
Modules.BuildHighTrafficServiceBusServices(
    builder.Services);
Modules.BuildServiceBusManager(builder.Services);
Modules.BuildDbServices(builder.Services);
Modules.BuildCacheServices(builder.Services);
Modules.BuildWorkerServices(builder.Services);
MvcModules.BuildFuncServices(builder.Services, appName);
FlowHubModules.BuildFlowHubDbServices(builder.Services);
FlowHubModules.BuildOpenTelemetryServices(builder.Services);

builder.Services
    .AddHttpClient("default-flow-hub-handler")
    .ConfigureHttpClient((_, client) =>
    {
        client.Timeout = TimeSpan.FromSeconds(60);
        client.MaxResponseContentBufferSize = 1024 * 1024 * 2;
    })
    .ConfigurePrimaryHttpMessageHandler(() => new PrivateNetworkHttpClientHandler
    {
        AllowAutoRedirect = false,
    });

builder.Services.AddTravisBackendHttpClient();
builder.Services.Configure<HostOptions>(options =>
{
    // This is the primary timeout controlling graceful shutdown.
    // Give hosted services (including the Orleans Silo) this much time to stop.
    // Set it slightly less than the container orchestrator's grace period (e.g., ACA's default 30s).
    // Orleans performs its graceful shutdown: stopping listeners, deactivating grains, allowing them to write their state, etc.
    // If Orleans finishes its shutdown tasks within the HostOptions.ShutdownTimeout, the process exits cleanly. If it takes longer, the host might force termination.
    // Setting it lower than Container App Level TerminationGracePeriodSeconds(5 * 60)
    options.ShutdownTimeout = TimeSpan.FromMinutes(4.5);
});
#if SWAGGERGEN
#else
builder.Host.UseOrleans(siloBuilder =>
{
    var orleansConfig = new OrleansConfig();

    if (builder.Environment.IsProduction())
    {
        siloBuilder.UseAzureStorageClustering(options =>
            options.ConfigureTableServiceClient(orleansConfig.OrleansStorageConnStr));
    }
    else
    {
        siloBuilder.UseLocalhostClustering();
    }

    siloBuilder.AddAzureTableGrainStorage(
        name: "dictStore",
        configureOptions: options =>
        {
            options.DeleteStateOnClear = true;
            options.ConfigureTableServiceClient(orleansConfig.OrleansStorageConnStr);
        });

    siloBuilder.AddAzureTableGrainStorage(
        name: "workflowStore",
        configureOptions: options =>
        {
            options.DeleteStateOnClear = true;
            options.ConfigureTableServiceClient(orleansConfig.OrleansStorageConnStr);
        });

    siloBuilder.Services.AddSerializer(serializerBuilder =>
    {
        serializerBuilder.AddNewtonsoftJsonSerializer(
            isSupported: type => type.Namespace!.StartsWith("Sleekflow.FlowHub.Models"));
    });

    siloBuilder.ConfigureServices(services =>
    {
        services.AddSingleton<ILifecycleParticipant<ISiloLifecycle>, SiloReadinessObserver>();
        services.AddSingleton<ILifecycleParticipant<ISiloLifecycle>, SiloShutdownObserver>();
    });

    siloBuilder.Configure<SiloMessagingOptions>(options =>
    {
        // Orleans message rerouting timeout during shutdown
        // This should be shorter than HostOptions.ShutdownTimeout (4.5 min)
        // and SiloShutdownObserver will wait for this + 30s buffer (3 min total)
        // Timeline: Orleans reroutes for 2.5min -> SiloShutdownObserver waits 3min -> Host force-kills at 4.5min
        options.ShutdownRerouteTimeout = TimeSpan.FromMinutes(2.5);
        options.MaxForwardCount = 10;
    });

    // *** Add the Startup Task Registration ***
    // Ensure TableStorageInitializerTask and its dependencies (like OrleansConfig via IOptions)
    // are registered in the main DI container before UseOrleans is called,
    // or register the task implementation directly if dependencies are simple.
    // Assuming TableStorageInitializerTask is registered in DI:
    siloBuilder.AddStartupTask<TableStorageInitializerTask>();
});
builder.Services.AddHostedService<WaitForSiloReadyHostedService>();

#endif

builder.Services
    .AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(_ => new LruMemoryCacheService(
        WorkflowStepsService.WorkflowStepsContainerName,
        300))
    .AddSingleton<ISpecializedMemoryCacheService, LruMemoryCacheService>(_ => new LruMemoryCacheService(
        WorkflowMetadataService.WorkflowMetadataContainerName,
        300));

builder.Services.Scan(selector =>
    selector.FromAssemblyOf<IDynamicActionNeedsLoader>()
        .AddClasses(classes =>
            classes
                .Where(c => c is
                {
                    IsAbstract: false,
                    IsInterface: false
                })
                .AssignableTo(typeof(IDynamicActionNeedsLoader)))
        .UsingRegistrationStrategy(RegistrationStrategy.Append)
        .AsImplementedInterfaces()
        .WithScopedLifetime());

var app = builder.Build();

// app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();
HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
app.UseSwagger();
app.UseSwaggerUI(
    options =>
    {
        var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

        foreach (var description in provider.ApiVersionDescriptions)
        {
            options.SwaggerEndpoint(
                $"/swagger/{description.GroupName}/swagger.json",
                description.GroupName.ToUpperInvariant());
        }
    });
#endif

ThreadPool.SetMinThreads(128, 128);
ThreadPool.SetMaxThreads(512, 512);

app.Run();

Log.CloseAndFlush();