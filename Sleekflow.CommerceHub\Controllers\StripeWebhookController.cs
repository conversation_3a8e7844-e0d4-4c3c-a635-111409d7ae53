using Microsoft.AspNetCore.Mvc;
using Sleekflow.CommerceHub.Stripe.Webhooks;

namespace Sleekflow.CommerceHub.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("[Controller]")]
public class StripeWebhookController : ControllerBase
{
    private readonly IStripeWebhookService _stripeWebhookService;

    public StripeWebhookController(IStripeWebhookService stripeWebhookService)
    {
        _stripeWebhookService = stripeWebhookService;
    }

    [HttpPost]
    [Route("connect")]
    public async Task<ActionResult> ConnectWebhook()
    {
        var stripeConnectWebhook = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
        var stripeSignatureHeader = HttpContext.Request.Headers["Stripe-Signature"].ToString();

        await _stripeWebhookService.ProcessStripeConnectWebhookAsync(stripeConnectWebhook, stripeSignatureHeader);

        return Ok();
    }

    [HttpPost]
    [Route("payment")]
    public async Task<ActionResult> PaymentWebhook()
    {
        var stripePaymentWebhook = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
        var stripeSignatureHeader = HttpContext.Request.Headers["Stripe-Signature"].ToString();

        await _stripeWebhookService.ProcessStripePaymentWebhookAsync(stripePaymentWebhook, stripeSignatureHeader);

        return Ok();
    }

    [HttpPost]
    [Route("refund")]
    public async Task<ActionResult> RefundWebhook()
    {
        var stripeRefundWebhook = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
        var stripeSignatureHeader = HttpContext.Request.Headers["Stripe-Signature"].ToString();

        await _stripeWebhookService.ProcessStripeRefundWebhookAsync(stripeRefundWebhook, stripeSignatureHeader);

        return Ok();
    }
}