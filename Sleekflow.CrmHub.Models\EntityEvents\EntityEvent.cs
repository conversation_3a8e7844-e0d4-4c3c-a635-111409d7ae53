﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.EntityEvents;

public class EntityEventChangeEntry
{
    [JsonProperty("n")]
    public string Name { get; set; }

    [JsonProperty("f")]
    public object? FromValue { get; set; }

    [JsonProperty("t")]
    public object? ToValue { get; set; }

    [JsonProperty("name")]
#pragma warning disable S2376
    public string NameAlias
#pragma warning restore S2376
    {
        set => Name = value;
    }

    [JsonProperty("from_value")]
#pragma warning disable S2376
    public object? FromValueAlias
#pragma warning restore S2376
    {
        set => FromValue = value;
    }

    [JsonProperty("to_value")]
#pragma warning disable S2376
    public object? ToValueAlias
#pragma warning restore S2376
    {
        set => ToValue = value;
    }

    [JsonConstructor]
    public EntityEventChangeEntry(
        string name,
        object? fromValue,
        object? toValue)
    {
        Name = name;
        FromValue = fromValue;
        ToValue = toValue;
    }

    public static EntityEventChangeEntry? FromObject(object? o)
    {
        return o switch
        {
            null => null,
            EntityEventChangeEntry sv => sv,
            JObject jObject => jObject.ToObject<EntityEventChangeEntry>(),
            _ => null
        };
    }
}

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("entity_event")]
public class EntityEvent : Entity
{
    public const string PropertyNameSysSleekflowCompanyId = "sys_sleekflow_company_id";
    public const string PropertyNameChangeEntries = "change_entries";
    public const string PropertyNameCreatedTime = "created_time";
    public const string PropertyNameProviderName = "provider_name";
    public const string PropertyNameEntityId = "entity_id";
    public const string PropertyNameEntityEventTypeName = "entity_event_type_name";
    public const string PropertyNameEntityTypeName = "entity_type_name";

    [JsonProperty(PropertyNameSysSleekflowCompanyId)]
    public string SysSleekflowCompanyId { get; set; }

    [JsonProperty("sys_partition_id")]
    public string SysPartitionId { get; set; }

    [JsonProperty(PropertyNameChangeEntries)]
    public List<EntityEventChangeEntry> ChangeEntries { get; set; }

    [JsonProperty(PropertyNameCreatedTime)]
    public DateTimeOffset CreatedTime { get; set; }

    [JsonProperty(PropertyNameProviderName)]
    public string ProviderName { get; set; }

    [JsonProperty(PropertyNameEntityId)]
    public string EntityId { get; set; }

    [JsonProperty(PropertyNameEntityEventTypeName)]
    public string EntityEventTypeName { get; set; }

    [JsonProperty(PropertyNameEntityTypeName)]
    public string EntityTypeName { get; set; }

    public EntityEvent(
        string id,
        List<EntityEventChangeEntry> changeEntries,
        DateTimeOffset createdTime,
        string providerName,
        string sysSleekflowCompanyId,
        string sysPartitionId,
        string entityId,
        string entityEventTypeName,
        string entityTypeName,
        int? ttl)
        : base(id, "EntityEvent", ttl)
    {
        ChangeEntries = changeEntries;
        CreatedTime = createdTime;
        ProviderName = providerName;
        SysSleekflowCompanyId = sysSleekflowCompanyId;
        SysPartitionId = sysPartitionId;
        EntityId = entityId;
        EntityEventTypeName = entityEventTypeName;
        EntityTypeName = entityTypeName;
    }
}