using Sleekflow.EmailHub.Models.Authentications;

namespace Sleekflow.EmailHub.Services;

public interface IEmailAuthenticationService
{
    Task<EmailAuthentication> GetAuthenticationAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string? serverType = null,
        CancellationToken cancellationToken = default);

    Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? extendedAuthMetadata,
        CancellationToken cancellationToken = default);
}