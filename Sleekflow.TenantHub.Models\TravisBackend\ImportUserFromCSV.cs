using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.TenantHub.Models.TravisBackend;

public class ImportUserFromCsvObject
{
    [JsonProperty("tenanthub_user_id")]
    public string TenantHubUserId { get; set; }

    [JsonProperty("sleekflow_user_id")]
    public string SleekflowUserId { get; set; }

    [Required]
    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("password")]
    public string? Password { get; set; }

    [JsonProperty("user_name")]
    public string UserName { get; set; }

    [<PERSON>sonProperty("last_name")]
    public string Lastname { get; set; }

    [Json<PERSON>roperty("first_name")]
    public string Firstname { get; set; }

    [Required]
    [JsonProperty("user_role")]
    public string UserRole { get; set; }

    [JsonProperty("position")]
    public string? Position { get; set; }

    [Json<PERSON>roperty("time_zone_info_id")]
    public string? TimeZoneInfoId { get; set; }

    [JsonProperty("team_ids")]
    public List<long>? TeamIds { get; set; }

    [JsonConstructor]
    public ImportUserFromCsvObject(
        string sleekflowUserId,
        string email,
        string? password,
        string userName,
        string lastname,
        string firstname,
        string userRole,
        string? position,
        string? timeZoneInfoId,
        string tenantHubUserId,
        List<long>? teamIds)
    {
        SleekflowUserId = sleekflowUserId;
        Email = email;
        Password = password;
        UserName = userName;
        Lastname = lastname;
        Firstname = firstname;
        UserRole = userRole;
        Position = position;
        TimeZoneInfoId = timeZoneInfoId;
        TenantHubUserId = tenantHubUserId;
        TeamIds = teamIds;
    }
}

public class ImportUserFromCsvRequest
{
    [JsonProperty("company_id")]
    public string? CompanyId { get; set; }

    [Required]
    [Validations.ValidateArray]
    [JsonProperty("import_users")]
    public List<ImportUserFromCsvObject> ImportUsers { get; set; }

    [JsonConstructor]
    public ImportUserFromCsvRequest(
        string companyId,
        List<ImportUserFromCsvObject> importUsers)
    {
        CompanyId = companyId;
        ImportUsers = importUsers;
    }
}

public class ImportUserFromCsvResponse
{
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("imported_users")]
    public List<InviteUserByEmailResponseObject> ImportedUsers { get; set; }

    [JsonConstructor]
    public ImportUserFromCsvResponse(
        bool success,
        string message,
        List<InviteUserByEmailResponseObject> importedUsers)
    {
        Success = success;
        Message = message;
        ImportedUsers = importedUsers;
    }
}