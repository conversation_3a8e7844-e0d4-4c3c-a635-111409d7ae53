using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface IWeChatMessageBodyCreator : IMessageBodyCreator
{
}

public class WeChatMessageBodyCreator : BaseMessageBodyCreator, IWeChatMessageBodyCreator
{
    public WeChatMessageBodyCreator()
        : base(ChannelTypes.WeChat)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                weChatMessengerMessage: new WeChatMessengerMessageObject(
                    msgType: "text",
                    text: new WeChatTextMessage(messageStr))),
            "text"));
    }
}