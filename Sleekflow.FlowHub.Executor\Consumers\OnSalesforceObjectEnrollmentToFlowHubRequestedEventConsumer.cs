using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnSalesforceObjectEnrollmentToFlowHubRequestedEventConsumerDefinition : ConsumerDefinition<OnSalesforceObjectEnrollmentToFlowHubRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnSalesforceObjectEnrollmentToFlowHubRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnSalesforceObjectEnrollmentToFlowHubRequestedEventConsumer : IConsumer<OnSalesforceObjectEnrollmentToFlowHubRequestedEvent>
{
    private readonly IServiceBusManager _serviceBusManager;

    public OnSalesforceObjectEnrollmentToFlowHubRequestedEventConsumer(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<OnSalesforceObjectEnrollmentToFlowHubRequestedEvent> context)
    {
        var onSalesforceObjectEnrollmentToFlowHubRequestedEvent = context.Message;

        await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
            new OnSalesforceObjectEnrolledEventBody(
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.CreatedAt,
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.SalesforceConnectionId,
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.ObjectType,
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.IsCustomObject,
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.ObjectDict,
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowId,
                onSalesforceObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowVersionedId),
            onSalesforceObjectEnrollmentToFlowHubRequestedEvent.ObjectId,
            onSalesforceObjectEnrollmentToFlowHubRequestedEvent.ObjectType,
            onSalesforceObjectEnrollmentToFlowHubRequestedEvent.SleekflowCompanyId));
    }
}