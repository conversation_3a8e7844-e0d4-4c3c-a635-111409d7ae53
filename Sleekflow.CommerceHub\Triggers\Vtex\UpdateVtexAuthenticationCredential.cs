﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Models.Vtex.ViewModels;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Vtex;

[TriggerGroup(ControllerNames.Vtex)]
public class UpdateVtexAuthenticationCredential
    : ITrigger<
        UpdateVtexAuthenticationCredential.UpdateVtexAuthenticationCredentialInput,
        UpdateVtexAuthenticationCredential.UpdateVtexAuthenticationCredentialOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;

    public UpdateVtexAuthenticationCredential(IVtexAuthenticationService vtexAuthenticationService)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
    }

    public class UpdateVtexAuthenticationCredentialInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("vtex_authentication_id")]
        public string VtexAuthenticationId { get; set; }

        [Required]
        [JsonProperty("credential")]
        public VtexCredential Credential { get; set; }

        [JsonConstructor]
        public UpdateVtexAuthenticationCredentialInput(
            string sleekflowCompanyId,
            string vtexAuthenticationId,
            VtexCredential credential)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            VtexAuthenticationId = vtexAuthenticationId;
            Credential = credential;
        }
    }

    public class UpdateVtexAuthenticationCredentialOutput
    {
        [JsonProperty("vtex_authentication")]
        public VtexAuthenticationViewModel VtexAuthentication { get; set; }

        [JsonConstructor]
        public UpdateVtexAuthenticationCredentialOutput(VtexAuthenticationViewModel vtexAuthentication)
        {
            VtexAuthentication = vtexAuthentication;
        }
    }

    public async Task<UpdateVtexAuthenticationCredentialOutput> F(
        UpdateVtexAuthenticationCredentialInput input)
    {
        var authentication = await _vtexAuthenticationService.UpdateCredentialAndReauthenticateAsync(
            input.VtexAuthenticationId,
            input.SleekflowCompanyId,
            input.Credential);

        return new UpdateVtexAuthenticationCredentialOutput(
            new VtexAuthenticationViewModel(authentication, true));
    }
}