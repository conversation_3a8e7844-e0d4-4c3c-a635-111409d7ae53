using Newtonsoft.Json;
using Sleekflow.DurablePayloads;

namespace Sleekflow.FlowHub.Models.Workflows.Settings;

public abstract class BaseDateTimeSettings
{
    [JsonConstructor]
    protected BaseDateTimeSettings(
        string? triggerDateType,
        string[] triggerDateDuration,
        string? triggerTimeType,
        string? triggerCustomTime)
    {
        TriggerDateType = triggerDateType;
        TriggerDateDuration = triggerDateDuration;
        TriggerTimeType = triggerTimeType;
        TriggerCustomTime = triggerCustomTime;
    }

    [JsonProperty("trigger_date_type")]
    public string? TriggerDateType { get; set; }

    [JsonProperty("trigger_date_duration")]
    // the format should be like [3, "day"]
    public string[] TriggerDateDuration { get; set; }

    [JsonProperty("trigger_time_type")]
    public string? TriggerTimeType { get; set; }

    [JsonProperty("trigger_custom_time")]
    public string? TriggerCustomTime { get; set; }

    [JsonProperty("durable_payload")]
    public DurablePayload? DurablePayload { get; set; }
}