using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;

[TriggerGroup(
    ControllerNames.CompanyAgentConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetCompanyAgentConfig
    : ITrigger<GetCompanyAgentConfig.GetCompanyAgentConfigInput, GetCompanyAgentConfig.GetCompanyAgentConfigOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IIntelligentHubCharacterCountService _characterCountService;

    public GetCompanyAgentConfig(
        ISleekflowAuthorizationContext authorizationContext,
        ICompanyAgentConfigService companyAgentConfigService,
        IIntelligentHubCharacterCountService characterCountService)
    {
        _authorizationContext = authorizationContext;
        _companyAgentConfigService = companyAgentConfigService;
        _characterCountService = characterCountService;
    }

    public class GetCompanyAgentConfigInput
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public GetCompanyAgentConfigInput(string id)
        {
            Id = id;
        }
    }

    public class GetCompanyAgentConfigOutput
    {
        [JsonProperty("company_agent_config")]
        public CompanyAgentConfigDto CompanyAgentConfig { get; set; }

        [JsonProperty("character_count")]
        public int CharacterCount { get; set; }

        [JsonProperty("character_limit")]
        public int CharacterLimit { get; set; }

        [JsonConstructor]
        public GetCompanyAgentConfigOutput(CompanyAgentConfigDto companyAgentConfig, int characterCount, int characterLimit)
        {
            CompanyAgentConfig = companyAgentConfig;
            CharacterCount = characterCount;
            CharacterLimit = characterLimit;
        }
    }

    public async Task<GetCompanyAgentConfigOutput> F(GetCompanyAgentConfigInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        var config = await _companyAgentConfigService.GetOrDefaultAsync(
            input.Id,
            sleekflowCompanyId);

        var characterCount = await _characterCountService.GetCharacterCountForAgent(sleekflowCompanyId, input.Id);
        var characterLimit = await _characterCountService.GetCharacterCountLimit(_authorizationContext.SleekflowCompanyId!);

        return new GetCompanyAgentConfigOutput(new CompanyAgentConfigDto(config!), characterCount, characterLimit);
    }
}