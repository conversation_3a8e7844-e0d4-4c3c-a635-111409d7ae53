using System.ComponentModel;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Data;
using Microsoft.SemanticKernel.Plugins.Web.Google;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins.Models;

#pragma warning disable SKEXP0050

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class WebSearchPluginSearchResponse : QueryKnowledgeResponse
{
    public WebSearchPluginSearchResponse(string knowledge, string id)
        : base(knowledge, id, "WebSearch")
    {
    }
}

public interface IWebSearchPlugin
{
    [KernelFunction("web_search")]
    [Description(
        "Performs a web search using the Open Search API, ideal for general queries, news, articles, and online content. " +
        "Use this for broad information gathering, recent events, or when you need diverse web sources. " +
        "Supports pagination, content filtering, and freshness controls. " +
        "Maximum 20 results per request, with offset for pagination.")]
    Task<WebSearchPluginSearchResponse> WebSearchAsync(
        Kernel kernel,
        [Description("Search query (max 400 chars, 50 words)")]
        string query,
        [Description("Trigger mapping index to use predefined search patterns and sites")]
        int triggerMappingIndex,
        [Description("Number of results (1-20, default 10)")]
        int count = 10,
        [Description("Pagination offset (max 9, default 0)")]
        int offset = 0);
}

public class WebSearchPlugin
    : IWebSearchPlugin, IScopedService
{
    private readonly ILogger<WebSearchPlugin> _logger;
    private readonly GoogleTextSearch _googleTextSearch;
    private readonly IStaticSearchPlugin _staticSearchPlugin;

    public WebSearchPlugin(
        ILogger<WebSearchPlugin> logger,
        IGoogleCustomSearchConfig googleCustomSearchConfig,
        IStaticSearchPlugin staticSearchPlugin)
    {
        _logger = logger;
        _googleTextSearch = new GoogleTextSearch(
            initializer: new ()
            {
                ApiKey = googleCustomSearchConfig.ApiKey
            },
            searchEngineId: googleCustomSearchConfig.SearchEngineId);
        _staticSearchPlugin = staticSearchPlugin;
    }

    [KernelFunction("web_search")]
    [Description(
        "Performs a web search using the Open Search API, ideal for general queries, news, articles, and online content. " +
        "Use this for broad information gathering, recent events, or when you need diverse web sources. " +
        "Supports pagination, content filtering, and freshness controls. " +
        "Maximum 20 results per request, with offset for pagination.")]
    public async Task<WebSearchPluginSearchResponse> WebSearchAsync(
        Kernel kernel,
        [Description("Search query (max 400 chars, 50 words)")]
        string query,
        [Description("Trigger mapping index to use predefined search patterns and sites")]
        int triggerMappingIndex,
        [Description("Number of results (1-20, default 10)")]
        int count = 10,
        [Description("Pagination offset (max 9, default 0)")]
        int offset = 0)
    {
        _logger.LogInformation(
            "Performing web search: Query: {Query}, Count: {Count}, Offset: {Offset}, Trigger Mapping Index: {TriggerMappingIndex}",
            query,
            count,
            offset,
            triggerMappingIndex);

        var responseId = Guid.NewGuid().ToString();

        try
        {
            var searchQuery = query;

            // Apply trigger mapping if specified
            string? appliedSite = null;
            if (triggerMappingIndex >= 0)
            {
                var webSearchConfig = GetWebSearchConfigFromKernel(kernel);
                if (webSearchConfig?.TriggerSiteMappings != null &&
                    triggerMappingIndex < webSearchConfig.TriggerSiteMappings.Count)
                {
                    var mapping = webSearchConfig.TriggerSiteMappings[triggerMappingIndex];
                    appliedSite = mapping.Site;

                    // Add site restriction if specified in mapping
                    if (!string.IsNullOrWhiteSpace(mapping.Site))
                    {
                        searchQuery = $"{searchQuery} site:{mapping.Site}";
                    }
                }
            }

            var kernelSearchResults = await _googleTextSearch.GetTextSearchResultsAsync(
                searchQuery,
                new TextSearchOptions
                {
                    Skip = offset, Top = count,
                });

            var staticSearchPluginResponse = GetStaticSearchConfigFromKernel(kernel) != null
                ? (await _staticSearchPlugin.SearchStaticPagesAsync(kernel, query)).Knowledge
                : string.Empty;

            var results =
                await kernelSearchResults.Results.Select(r => $"Title: {r.Name}\nDescription: {r.Value}\nURL: {r.Link}")
                    .ToListAsync();

            var resultContent = string.Join("\n\n", results);

            _logger.LogInformation(
                """
                Web search completed:
                Query: {Query}
                Trigger Mapping Index: {TriggerMappingIndex}
                Applied Site: {AppliedSite}
                Final Search Query: {SearchQuery}
                Results Count: {ResultsCount}
                Results: {Results}
                """,
                query,
                triggerMappingIndex,
                appliedSite,
                searchQuery,
                results.Count,
                JsonConvert.SerializeObject(results));

            return new WebSearchPluginSearchResponse(
                $"<CONFIRMED_KNOWLEDGE>{resultContent}</CONFIRMED_KNOWLEDGE>{staticSearchPluginResponse}",
                responseId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during web search: {Query}", query);

            return new WebSearchPluginSearchResponse(
                $"Failed to perform web search: {query}",
                responseId);
        }
    }

    /// <summary>
    /// Gets StaticSearchConfig from a kernel.
    /// </summary>
    /// <param name="kernel">The kernel containing configuration data.</param>
    /// <returns>StaticSearchConfig if found, null otherwise.</returns>
    private StaticSearchConfig? GetStaticSearchConfigFromKernel(Kernel kernel)
    {
        // Only get StaticSearchConfig directly from kernel data
        if (kernel.Data.TryGetValue(KernelDataKeys.STATIC_SEARCH_PLUGIN_CONFIG, out var staticSearchConfigObj)
            && staticSearchConfigObj is StaticSearchConfig config)
        {
            return config;
        }

        return null;
    }

    /// <summary>
    /// Gets WebSearchConfig from a kernel.
    /// </summary>
    /// <param name="kernel">The kernel containing configuration data.</param>
    /// <returns>WebSearchConfig if found, null otherwise.</returns>
    private WebSearchConfig? GetWebSearchConfigFromKernel(Kernel kernel)
    {
        // Only get WebSearchConfig directly from kernel data
        if (kernel.Data.TryGetValue(KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG, out var webSearchConfigObj)
            && webSearchConfigObj is WebSearchConfig config)
        {
            return config;
        }

        return null;
    }
}