using System.Text.Encodings.Web;
using System.Text.Json;
using Newtonsoft.Json.Linq;

namespace Sleekflow.Extensions;

public static class StringExtensions
{
    public static bool IsValidJson(this string? source)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(source))
            {
                return false;
            }

            _ = JToken.Parse(source);

            return true;
        }
        catch
        {
            return false;
        }
    }

    public static bool TryParseToJToken(this string? source, out JToken? token)
    {
        token = null;

        try
        {
            if (string.IsNullOrWhiteSpace(source))
            {
                return false;
            }

            token = JToken.Parse(source);

            return true;
        }
        catch
        {
            return false;
        }
    }

    public static string? EscapeSequences(this string? source)
    {
        if (string.IsNullOrWhiteSpace(source))
        {
            return source;
        }

        return JsonEncodedText
            .Encode(source, JavaScriptEncoder.Default)
            .ToString();
    }
}