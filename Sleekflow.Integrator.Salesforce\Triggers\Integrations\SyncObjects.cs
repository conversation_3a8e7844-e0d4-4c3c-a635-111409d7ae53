using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Workers;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SyncObjects : ITrigger
{
    private readonly HttpClient _httpClient;
    private readonly IWorkerService _workerService;
    private readonly IWorkerConfig _workerConfig;
    private readonly ISalesforceAuthenticationRepository _salesforceAuthenticationRepository;

    public SyncObjects(
        IHttpClientFactory httpClientFactory,
        IWorkerService workerService,
        IWorkerConfig workerConfig,
        ISalesforceAuthenticationRepository salesforceAuthenticationRepository)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _workerService = workerService;
        _workerConfig = workerConfig;
        _salesforceAuthenticationRepository = salesforceAuthenticationRepository;
    }

    public class SyncObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SyncObjectsInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class SyncObjectsOutput : DurablePayload
    {
        [JsonConstructor]
        public SyncObjectsOutput(
            string id,
            string statusQueryGetUri,
            string sendEventPostUri,
            string terminatePostUri,
            string rewindPostUri,
            string purgeHistoryDeleteUri,
            string restartPostUri)
            : base(
                id,
                statusQueryGetUri,
                sendEventPostUri,
                terminatePostUri,
                rewindPostUri,
                purgeHistoryDeleteUri,
                restartPostUri)
        {
        }
    }

    public async Task<SyncObjectsOutput> F(
        SyncObjectsInput syncObjectsInput)
    {
        var authentication = await _salesforceAuthenticationRepository.GetOrDefaultAsync(
            syncObjectsInput.SleekflowCompanyId,
            syncObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (_, _, output) = await _workerService.PostAsync<SyncObjectsOutput>(
            _httpClient,
            JsonConvert.SerializeObject(syncObjectsInput),
            _workerConfig.WorkerHostname + "/api/Salesforce_SyncObjects");

        return output.Data!;
    }
}