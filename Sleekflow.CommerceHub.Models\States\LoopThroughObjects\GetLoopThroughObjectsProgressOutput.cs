﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.States.LoopThroughObjects;

public class GetLoopThroughObjectsProgressOutput
{
    [JsonProperty("count")]
    public long Count { get; set; }

    [JsonProperty("last_update_time")]
    public DateTime LastUpdateTime { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonConstructor]
    public GetLoopThroughObjectsProgressOutput(
        long count,
        DateTime lastUpdateTime,
        string status)
    {
        Count = count;
        LastUpdateTime = lastUpdateTime;
        Status = status;
    }
}