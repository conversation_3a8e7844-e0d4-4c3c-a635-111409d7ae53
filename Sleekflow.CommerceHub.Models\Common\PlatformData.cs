using System.ComponentModel.DataAnnotations;
using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Common;

public class PlatformData : IHasMetadata
{
    [JsonConstructor]
    public PlatformData(string? id, string type, Dictionary<string, object?> metadata)
    {
        Id = id;
        Type = type;
        Metadata = metadata;
    }

    [StringLength(128, MinimumLength = 1)]
    [SearchableField(IsFilterable = true, IsFacetable = false, IsSortable = false)]
    [JsonProperty("id")]
    public string? Id { get; set; }

    [Required]
    [StringLength(128, MinimumLength = 1)]
    [SearchableField(IsFilterable = true, IsFacetable = true, IsSortable = true)]
    [JsonProperty("type")]
    public string Type { get; set; }

    [Required]
    [FieldBuilderIgnore]
    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    public static PlatformData CustomCatalog()
    {
        return new PlatformData(null, PlatformDataTypes.CustomCatalog, new Dictionary<string, object?>());
    }

    public static PlatformData Shopify()
    {
        return new PlatformData(null, PlatformDataTypes.Shopify, new Dictionary<string, object?>());
    }
}