using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class SendMetaCapiEventReply
{
    [JsonProperty("events_received")]
    public int EventsReceived { get; set; }

    [JsonProperty("messages")]
    public List<string> Messages { get; set; }

    [JsonProperty("fbtrace_id")]
    public string? FbtraceId { get; set; }

    [JsonConstructor]
    public SendMetaCapiEventReply(
        int eventsReceived,
        List<string> messages,
        string? fbtraceId)
    {
        EventsReceived = eventsReceived;
        Messages = messages;
        FbtraceId = fbtraceId;
    }
}