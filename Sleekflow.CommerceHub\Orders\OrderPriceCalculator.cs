using Sleekflow.CommerceHub.LineItems;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.LineItems;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Orders;

public interface IOrderPriceCalculator
{
    public record CalculatorContext(string CurrencyIsoCode, string LanguageIsoCode);

    Task<(decimal SubtotalPrice, decimal TotalPrice)> CalculateAsync(
        string sleekflowCompanyId,
        string storeId,
        List<OrderLineItem> orderLineItems,
        Discount? orderDiscount,
        CalculatorContext calculatorContext);

    (decimal SubtotalPrice, decimal TotalPrice) Calculate(
        string sleekflowCompanyId,
        string storeId,
        List<CalculatedLineItem> calculatedLineItems);
}

public class OrderPriceCalculator : IOrderPriceCalculator, IScopedService
{
    private readonly ILineItemCalculator _lineItemCalculator;

    public OrderPriceCalculator(
        ILineItemCalculator lineItemCalculator)
    {
        _lineItemCalculator = lineItemCalculator;
    }

    public async Task<(decimal SubtotalPrice, decimal TotalPrice)> CalculateAsync(
        string sleekflowCompanyId,
        string storeId,
        List<OrderLineItem> orderLineItems,
        Discount? orderDiscount,
        IOrderPriceCalculator.CalculatorContext calculatorContext)
    {
        var calculatedLineItems = await _lineItemCalculator.CalculateAsync(
            sleekflowCompanyId,
            storeId,
            orderLineItems
                .Select(oli => (SnapshottedLineItem) oli)
                .ToList(),
            orderDiscount,
            new ILineItemCalculator.CalculatorContext(
                calculatorContext.CurrencyIsoCode,
                calculatorContext.LanguageIsoCode));

        var subtotalPrice = calculatedLineItems.Sum(li => li.LineItemPreCalculatedAmount);
        var totalPrice = calculatedLineItems.Sum(li => li.LineItemCalculatedAmount);

        var roundSubtotalPrice = Math.Round(subtotalPrice, 2);
        var roundTotalPrice = Math.Round(totalPrice, 2);

        return (roundSubtotalPrice, roundTotalPrice);
    }

    public (decimal SubtotalPrice, decimal TotalPrice) Calculate(
        string sleekflowCompanyId,
        string storeId,
        List<CalculatedLineItem> calculatedLineItems)
    {
        var subtotalPrice = calculatedLineItems.Sum(li => li.LineItemPreCalculatedAmount);
        var totalPrice = calculatedLineItems.Sum(li => li.LineItemCalculatedAmount);

        return (subtotalPrice, totalPrice);
    }
}