using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class GetTriggerCondition
{
    private readonly ILogger<GetTriggerCondition> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly HttpClient _httpClient;
    private readonly IAppConfig _appConfig;

    public GetTriggerCondition(
        ILogger<GetTriggerCondition> logger,
        IHttpClientFactory httpClientFactory,
        IAsyncPolicy<HttpResponseMessage> retryPolicy,
        IAppConfig appConfig)
    {
        _logger = logger;
        _retryPolicy = retryPolicy;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _appConfig = appConfig;
    }

    [Function("GetTriggerCondition")]
    public async Task<GetTriggerConditionOutput> RunAsync(
        [ActivityTrigger]
        GetTriggerConditionInput input)
    {
        var pollyContext = new Context();
        pollyContext["logger"] = _logger;

        var inputJson = JsonConvert.SerializeObject(input);

        var response = await _retryPolicy.ExecuteAsync(
            async context =>
            {
                var request = new HttpRequestMessage
                {
                    Method = HttpMethod.Post,
                    Content = new StringContent(inputJson, Encoding.UTF8, "application/json"),
                    RequestUri = new Uri(_appConfig.FlowHubInternalsEndpoint + "/GetTriggerCondition"),
                    Headers =
                    {
                        {
                            "X-Sleekflow-Key", _appConfig.InternalsKey
                        }
                    }
                };

                _logger.LogInformation("Calling Trigger Condition API: {RequestUri}", request.RequestUri);
                return await _httpClient.SendAsync(request);
            },
            pollyContext);

        _logger.LogInformation("[GetTriggerCondition] response: {Response}", response);
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        _logger.LogInformation("[GetTriggerCondition] Response content: {Content}", content);
        Output<GetTriggerConditionOutput> deserializeObject = JsonConvert.DeserializeObject<Output<GetTriggerConditionOutput>>(content)!;
        return deserializeObject == null ? null : deserializeObject.Data;
    }

}