namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs;

public static class UserProfileAuditLogTypes
{
    public const string AutomationTriggered = "automation-triggered";
    public const string ConversationAssignedTeamChanged = "conversation-assigned-team-changed";
    public const string ConversationAssigneeChanged = "conversation-assignee-changed";
    public const string ConversationCollaboratorAdded = "conversation-collaborator-added";
    public const string ConversationCollaboratorRemoved = "conversation-collaborator-removed";
    public const string ConversationLabelAdded = "conversation-label-added";
    public const string ConversationLabelRemoved = "conversation-label-removed";
    public const string ConversationRead = "conversation-read";
    public const string ConversationStatusChanged = "conversation-status-changed";
    public const string ManualLog = "manual-log";
    public const string UserProfileRemovedFromList = "user-profile-removed-from-list";
    public const string UserProfileFieldsChanged = "user-profile-fields-changed";
    public const string UserProfileAddedToList = "user-profile-added-to-list";
    public const string UserProfileDeleted = "user-profile-deleted";
    public const string UserProfileSoftDeleted = "user-profile-soft-deleted";
    public const string UserProfileRecovered = "user-profile-recovered";
    public const string ConversationChannelSwitched = "conversation-channel-switched";
    public const string UserProfileChatHistoryBackedUp = "user-profile-chat-history-backed-up";
    public const string UserProfileEnrolledIntoFlowHubWorkflow = "user-profile-enrolled-into-flowhub-workflow";
    public const string UserProfileImported = "user-profile-imported";
}