using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.Splitters;

[TestFixture]
[TestOf(typeof(ExcelDocumentSplitter))]
public class ExcelDocumentSplitterTest
{
    const string ExcelFilePath = "../../../Binaries/clinic open hour.xlsx";
    const string BigExcelFilePath = "../../../Binaries/100-excel-rows.xlsx";

    [Test]
    public async Task SplitDocumentIntoChunksTest()
    {
        var documentSplitter = new ExcelDocumentSplitter();

        var fileStream = new FileStream(BigExcelFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
        using var spreadsheetDocument = SpreadsheetDocument.Open(fileStream, false);
        var workbookPart = spreadsheetDocument.WorkbookPart;
        var worksheetPart = workbookPart!.WorksheetParts.First();
        var sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();

        var numberOfContentPerFile = 2;
        var rowCount = sheetData.Elements<Row>().Count() - 1;
        var splitChunksStreams =
            await documentSplitter.SplitDocumentIntoChunksAsync(fileStream, numberOfContentPerFile);

        var expectedChunks = (rowCount / numberOfContentPerFile) + (rowCount % numberOfContentPerFile == 0 ? 0 : 1);
        Assert.That(splitChunksStreams.Count, Is.EqualTo(expectedChunks));
        foreach (var splitChunksStream in splitChunksStreams)
        {
            using var spreadsheetDocumentChunk = SpreadsheetDocument.Open(splitChunksStream.Stream, false);
            var chunkWorkbookPart = spreadsheetDocumentChunk.WorkbookPart;
            var chunkWorksheetPart = chunkWorkbookPart!.WorksheetParts.First();
            var chunkSheetData = chunkWorksheetPart.Worksheet.Elements<SheetData>().First();

            foreach (var row in chunkSheetData.Elements<Row>())
            {
                var cells = row.Elements<Cell>().ToArray();
                foreach (var cell in cells)
                {
                    var stringTablePart = chunkWorkbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

                    var value = cell.InnerText;

                    if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString &&
                        stringTablePart != null)
                    {
                        // shared string
                        Assert.That(
                            stringTablePart.SharedStringTable.ElementAt(int.Parse(value)).InnerText,
                            Is.Not.Empty);
                    }
                }
            }
        }
    }
}