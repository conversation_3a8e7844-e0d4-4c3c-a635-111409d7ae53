﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class PartialUpdatePropertyValues
    : ITrigger<PartialUpdatePropertyValues.PartialUpdatePropertyValuesInput,
        PartialUpdatePropertyValues.PartialUpdatePropertyValuesOutput>
{
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public PartialUpdatePropertyValues(
        ISchemafulObjectService schemafulObjectService,
        ISchemaService schemaService)
    {
        _schemafulObjectService = schemafulObjectService;
        _schemaService = schemaService;
    }

    public class PartialUpdatePropertyValuesInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [Required]
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
        [Validations.ValidateObject]
        public Dictionary<string, object?> PropertyValues { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty(SchemafulObject.PropertyNameUpdatedVia)]
        public string UpdatedVia { get; set; }

        [JsonConstructor]
        public PartialUpdatePropertyValuesInput(
            string id,
            string schemaId,
            string sleekflowCompanyId,
            Dictionary<string, object?> propertyValues,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string updatedVia)
        {
            Id = id;
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
            PropertyValues = propertyValues;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            UpdatedVia = updatedVia;
        }
    }

    public class PartialUpdatePropertyValuesOutput
    {
        [JsonProperty("schemaful_object")]
        public SchemafulObjectDto SchemafulObject { get; set; }

        [JsonConstructor]
        public PartialUpdatePropertyValuesOutput(SchemafulObjectDto schemafulObject)
        {
            SchemafulObject = schemafulObject;
        }
    }

    public async Task<PartialUpdatePropertyValuesOutput> F(PartialUpdatePropertyValuesInput partialUpdatePropertyValuesInput)
    {
        var schema = await _schemaService.GetAsync(
            partialUpdatePropertyValuesInput.SchemaId,
            partialUpdatePropertyValuesInput.SleekflowCompanyId);

        var schemafulObject = await _schemafulObjectService.PartialUpdatePropertyValuesAndGetSchemafulObjectAsync(
            partialUpdatePropertyValuesInput.Id,
            schema,
            partialUpdatePropertyValuesInput.SleekflowCompanyId,
            partialUpdatePropertyValuesInput.PropertyValues,
            partialUpdatePropertyValuesInput.UpdatedVia,
            new AuditEntity.SleekflowStaff(
                partialUpdatePropertyValuesInput.SleekflowStaffId,
                partialUpdatePropertyValuesInput.SleekflowStaffTeamIds));

        return new PartialUpdatePropertyValuesOutput(new SchemafulObjectDto(schemafulObject));
    }
}