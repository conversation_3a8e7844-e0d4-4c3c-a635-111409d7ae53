using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactPropertiesByPropertyKeyStepExecutor : IStepExecutor
{
}

public class UpdateContactPropertiesByPropertyKeyStepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactPropertiesByPropertyKeyStepArgs>>,
        IUpdateContactPropertiesByPropertyKeyStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactPropertiesByPropertyKeyStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactPropertiesByPropertyKey",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateContactPropertiesByPropertyKeyInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_property_key_id")]
        [Required]
        public string ContactPropertyKeyId { get; set; }

        [JsonProperty("contact_property_key_value")]
        [Required]
        public string ContactPropertyKeyValue { get; set; }

        [JsonProperty("properties_dict")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> PropertiesDict { get; set; }

        [JsonConstructor]
        public UpdateContactPropertiesByPropertyKeyInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactPropertyKeyId,
            string contactPropertyKeyValue,
            Dictionary<string, object?> propertiesDict)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactPropertyKeyId = contactPropertyKeyId;
            ContactPropertyKeyValue = contactPropertyKeyValue;
            PropertiesDict = propertiesDict;
        }
    }

    private async Task<UpdateContactPropertiesByPropertyKeyInput> GetArgs(
        CallStep<UpdateContactPropertiesByPropertyKeyStepArgs> callStep,
        ProxyState state)
    {
        var contactPropertyKeyId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactPropertyKeyIdExpr)
                      ?? callStep.Args.ContactPropertyKeyIdExpr);
        var contactPropertyKeyValue =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactPropertyKeyValueExpr)
                      ?? callStep.Args.ContactPropertyKeyValueExpr);
        var propertiesDict =
            await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.PropertiesKeyExprDict);

        return new UpdateContactPropertiesByPropertyKeyInput(
            state.Id,
            state.Identity,
            contactPropertyKeyId,
            contactPropertyKeyValue,
            propertiesDict);
    }
}