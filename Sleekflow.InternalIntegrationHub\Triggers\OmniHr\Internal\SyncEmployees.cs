using MassTransit;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.Models.Events;

namespace Sleekflow.InternalIntegrationHub.Triggers.OmniHr.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.OmniHr}")]
public class SyncEmployees : ITrigger<SyncEmployees.SyncEmployeesInput, SyncEmployees.SyncEmployeesOutput>
{
    private readonly IBus _bus;

    public SyncEmployees(IBus bus)
    {
        _bus = bus;
    }

    public class SyncEmployeesInput
    {
    }

    public class SyncEmployeesOutput
    {
    }

    public async Task<SyncEmployeesOutput> F(SyncEmployeesInput input)
    {
        await _bus.Publish(
            new OmniHrEmployeeInfoToNetSuiteIntegrationEvent());
        return new SyncEmployeesOutput();
    }
}