using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Plugins.Knowledges;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;

public class KnowledgeRetrievalAgentGeminiChatHistoryReducer(
    List<string> authorNames,
    ILeadNurturingCollaborationChatCacheService leadNurturingCollaborationChatCacheService,
    string groupChatIdStr,
    IAgenticKnowledgePlugin knowledgePlugin,
    Kernel kernel,
    ILeadNurturingAgentDefinitions leadNurturingAgentDefinitions)
    : AuthorNamesGeminiChatHistoryReducer(authorNames)
{
    public override async Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        var chatMessageContents =
            chatHistory.ToList();

        var messageContent = chatHistory[^1];

        if (messageContent.AuthorName != leadNurturingAgentDefinitions.StrategyAgentName
            || messageContent.Content == null
            || !JsonUtils.TryParseJson<Dictionary<string, string?>>(messageContent.Content, out var strategyAgentJson))
        {
            return await base.ReduceAsync(chatMessageContents, cancellationToken);
        }

        var needKnowledge = strategyAgentJson!["need_knowledge"];
        if (needKnowledge == null)
        {
            return await base.ReduceAsync(chatMessageContents, cancellationToken);
        }

        var allConfirmedKnowledgesAsync =
            await leadNurturingCollaborationChatCacheService.GetAllConfirmedKnowledgesAsync(groupChatIdStr);
        if (allConfirmedKnowledgesAsync.TryGetValue(needKnowledge, out var value))
        {
            AppendChatMessageContent(value, chatMessageContents);

            return await base.ReduceAsync(chatMessageContents, cancellationToken);
        }

        var confirmedKnowledge = await knowledgePlugin.QueryKnowledgeAsync(kernel, needKnowledge);

        AppendChatMessageContent(confirmedKnowledge.Knowledge, chatMessageContents);

        await leadNurturingCollaborationChatCacheService.AppendConfirmedKnowledgeAsync(
            groupChatIdStr,
            needKnowledge,
            confirmedKnowledge.Knowledge);

        return await base.ReduceAsync(chatMessageContents, cancellationToken);
    }

    private void AppendChatMessageContent(string knowledge, List<ChatMessageContent> chatMessageContents)
    {
        var chatMessageContent = new ChatMessageContent
        {
            AuthorName = leadNurturingAgentDefinitions.KnowledgeRetrievalAgentName,
            Content = JsonConvert.SerializeObject(
                new Dictionary<string, string>
                {
                    {
                        "agent_name", leadNurturingAgentDefinitions.KnowledgeRetrievalAgentName
                    },
                    {
                        "knowledge", knowledge
                    },
                }),
            Role = AuthorRole.Assistant,
        };
        chatMessageContents.Add(chatMessageContent);
    }
}