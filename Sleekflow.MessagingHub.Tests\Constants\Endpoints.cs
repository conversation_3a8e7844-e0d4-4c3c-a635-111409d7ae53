using Sleekflow.MessagingHub.Models.Constants;

namespace Sleekflow.MessagingHub.Tests.Constants;

public static class Endpoints
{
    // Balances
    public const string GenerateBusinessBalanceStripeTopUpLink =
        $"/{ControllerNames.Balances}/GenerateBusinessBalanceStripeTopUpLink";

    public const string GetBusinessBalances = $"/{ControllerNames.Balances}/GetBusinessBalances";

    public const string GetBusinessBalanceStripeTopUpPlans =
        $"/{ControllerNames.Balances}/GetBusinessBalanceStripeTopUpPlans";

    public const string GetInternalBusinessBalances = $"/{ControllerNames.Balances}/GetInternalBusinessBalances";

    public const string TopUpBusinessBalance = $"/{ControllerNames.Balances}/TopUpBusinessBalance";

    // Channels
    public const string ConnectWhatsappCloudApiChannel =
        $"/{ControllerNames.Channels}/ConnectWhatsappCloudApiChannel";

    public const string DisconnectWhatsappCloudApiChannel =
        $"/{ControllerNames.Channels}/DisconnectWhatsappCloudApiChannel";

    public const string GetConnectedWhatsappCloudApiChannels =
        $"/{ControllerNames.Channels}/GetConnectedWhatsappCloudApiChannels";

    public const string GetWhatsappCloudApiMedia = $"/{ControllerNames.Channels}/GetWhatsappCloudApiMedia";

    public const string ReconnectWhatsappCloudApiChannel =
        $"/{ControllerNames.Channels}/ReconnectWhatsappCloudApiChannel";

    public const string RegisterWhatsAppPhoneNumber = $"/{ControllerNames.Channels}/RegisterWhatsAppPhoneNumber";

    public const string SendWhatsappCloudApiMessage = $"/{ControllerNames.Channels}/SendWhatsappCloudApiMessage";

    // TransactionsLogs
    public const string EnqueueBusinessBalancePendingTransactionLogCreated =
        $"/{ControllerNames.TransactionLogs}/EnqueueBusinessBalancePendingTransactionLogCreated";

    public const string EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction =
        $"/{ControllerNames.TransactionLogs}/EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction";

    public const string EnqueueWabaBusinessConnected = $"/{ControllerNames.Wabas}/EnqueueWabaBusinessConnected";

    // Migrations
    public const string GetUserBusinesses = $"/{ControllerNames.Migrations}/GetUserBusinesses";

    public const string GetUserBusinessPhoneNumbersByWabaId =
        $"/{ControllerNames.Migrations}//Migrations/GetUserBusinessPhoneNumbersByWabaId";

    public const string GetUserBusinessWabas = $"/{ControllerNames.Migrations}//Migrations/GetUserBusinessWabas";

    public const string InitiatePhoneNumberWabaMigration =
        $"/{ControllerNames.Migrations}//Migrations/InitiatePhoneNumberWabaMigration";

    public const string MigratePhoneNumberToCloudApi =
        $"/{ControllerNames.Migrations}//Migrations/MigratePhoneNumberToCloudApi";

    public const string RequestPhoneNumberOwnershipVerificationCode =
        $"/{ControllerNames.Migrations}//Migrations/RequestPhoneNumberOwnershipVerificationCode";

    public const string VerifyPhoneNumberOwnership =
        $"/{ControllerNames.Migrations}//Migrations/VerifyPhoneNumberOwnership";

    // Public
    public const string Healthz = $"/Public/healthz";

    // Templates
    public const string CreateWhatsappCloudApiTemplate =
        $"/{ControllerNames.Templates}/CreateWhatsappCloudApiTemplate";

    public const string DeleteWhatsappCloudApiTemplate =
        $"/{ControllerNames.Templates}/DeleteWhatsappCloudApiTemplate";

    public const string EditWhatsappCloudApiTemplate = $"/{ControllerNames.Templates}/EditWhatsappCloudApiTemplate";

    public const string GetWhatsappCloudApiTemplates = $"/{ControllerNames.Templates}/GetWhatsappCloudApiTemplates";

    // Wabas
    public const string ConnectWhatsappCloudApiWaba = $"/{ControllerNames.Wabas}/ConnectWhatsappCloudApiWaba";

    public const string GetConnectedWhatsappCloudApiWabas =
        $"/{ControllerNames.Wabas}/GetConnectedWhatsappCloudApiWabas";

    // WhatsappCloudApiWebhook
    public const string WhatsappCloudApiWebhook = "/WhatsappCloudApiWebhook";
}