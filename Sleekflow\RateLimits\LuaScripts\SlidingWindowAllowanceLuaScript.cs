﻿using StackExchange.Redis;

namespace Sleekflow.RateLimits.LuaScripts;

public class SlidingWindowAllowanceLuaScript : ILuaScript
{
    public LuaScript GetScript()
    {
        var rateLimitScript = LuaScript.Prepare(
            """
            local current_time = redis.call('TIME')
            local trim_time = tonumber(current_time[1]) - @window
            redis.call('ZREMRANGEBYSCORE', @key, 0, trim_time)
            local request_count = redis.call('ZCARD', @key)
            local max_requests = tonumber(@max_requests)
            local remaining_requests = max_requests - request_count
            local is_allowed
            if request_count < max_requests then
                redis.call('ZADD', @key, current_time[1], current_time[1] .. current_time[2])
                redis.call('EXPIRE', @key, @window)
                remaining_requests = remaining_requests - 1
            end
            is_allowed = remaining_requests > 0 and 1 or 0
            return {is_allowed, remaining_requests}
            """);

        return rateLimitScript;
    }
}