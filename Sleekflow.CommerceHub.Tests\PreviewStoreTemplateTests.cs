using Alba;
using Scriban;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Triggers.Categories;
using Sleekflow.CommerceHub.Triggers.Products;
using Sleekflow.CommerceHub.Triggers.Stores;

namespace Sleekflow.CommerceHub.Tests;

public class PreviewStoreTemplateTests
{
    private CreateStore.CreateStoreOutput? _createStoreOutput;
    private CreateCategory.CreateCategoryOutput? _createCategoryOutput;
    private CreateProduct.CreateProductOutput? _createProductOutput;

    [SetUp]
    public async Task Setup()
    {
        var createStoreOutputOutput = await Mocks.CreateTestStoreAsync();
        var createCategoryOutputOutput = await Mocks.CreateTestCategoryAsync(createStoreOutputOutput!.Data);
        var createProductOutputOutput = await Mocks.CreateTestProductAsync(
            createStoreOutputOutput.Data,
            createCategoryOutputOutput!.Data);

        _createStoreOutput = createStoreOutputOutput.Data;
        _createCategoryOutput = createCategoryOutputOutput.Data;
        _createProductOutput = createProductOutputOutput.Data;
    }

    [TearDown]
    public async Task TearDown()
    {
        await Mocks.DeleteTestProductAsync(_createStoreOutput!, _createProductOutput!);
        await Mocks.DeleteTestCategoryAsync(_createStoreOutput!, _createCategoryOutput!);
        await Mocks.DeleteTestStoreAsync(_createStoreOutput!);
    }

    [Test]
    public async Task HealthzTest()
    {
        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/Public/healthz");
                _.ContentShouldBe("HEALTH");
                _.StatusCodeShouldBeOk();
            });
    }

    [Test]
    public async Task LiquidTest()
    {
        var languageIsoCode = "en";

        var storeTemplateDict = new StoreTemplateDict(
            new List<Multilingual>()
            {
                new Multilingual(
                    "en",
                    """
Id {{ product_variant.id }}

Store Id {{ product_variant.store.id }}
Store Url {{ product_variant.store.url }}
Store Name {{ product_variant.store.name }}
Store Description {% for description in product_variant.store.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Store Languages {% for language in product_variant.store.languages -%} {{ language.language_iso_code }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}

Product Id {{ product_variant.product.id }}
Product Categories {% for category in product_variant.product.categories -%} {{ category.name }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Sku {{ product_variant.product.sku }}
Product Url {{ product_variant.product.url }}
Product Name {{ product_variant.product.name }}
Product Description {% for description in product_variant.product.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Images {% for image in product_variant.product.images -%} {{ image.image_url }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}

Product Variant Sku {{ product_variant.sku }}
Product Variant Prices {% for price in product_variant.prices -%} {{ price.currency_iso_code }} {{ price.amount }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Variant Name {{ product_variant.name }}
Product Variant Descriptions {% for description in product_variant.descriptions -%} {{ description.text.value }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
Product Images {% for image in product_variant.images -%} {{ image.image_url }} {%- unless forloop.last %}, {% endunless -%} {%- endfor %}
""")
            });

        // Make a sample ProductVariantRenderingDto to pass to the template
        var productVariantRenderingDto = ProductVariantRenderingDto.Sample();

        foreach (var messagePreviewTemplate in storeTemplateDict.MessagePreviewTemplates)
        {
            var template = Template.ParseLiquid(messagePreviewTemplate.Value);
            var renderedTemplate = await template.RenderAsync(
                new
                {
                    ProductVariant = productVariantRenderingDto,
                });

            Assert.That(
                renderedTemplate,
                Is.EqualTo(
                    """
Id myProductVariantId

Store Id myStoreId
Store Url https://shop.sleekflow.io/Store/myStoreId
Store Name My Store Name
Store Description This is a sample store description
Store Languages en

Product Id myProductId
Product Categories My Category Name
Product Sku mySku
Product Url https://google.com
Product Name My Product Name
Product Description This is a sample product description
Product Images https://insideretail.asia/wp-content/uploads/2022/11/bigstock-Icon-Of-The-Whatsapp-App-On-T-413646935.jpg

Product Variant Sku myProductVariantSku
Product Variant Prices USD 1
Product Variant Name My Product Variant Name
Product Variant Descriptions This is a sample product variant description, It allows more than on line of comments
Product Images https://insideretail.asia/wp-content/uploads/2022/11/bigstock-Icon-Of-The-Whatsapp-App-On-T-413646935.jpg
"""));
        }
    }
}