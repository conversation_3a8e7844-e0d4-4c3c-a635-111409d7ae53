using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Auth0s;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Storage = Pulumi.AzureNative.Storage;
using Random = Pulumi.Random;

namespace Sleekflow.Infras.Components.TenantHub;

public class TenantHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly TenantHubDb.TenantHubDbOutput _tenantHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;
    private readonly (Dictionary<string, App.ContainerApp?>? Apps, Storage.StorageAccount? StorageAccount) _rbac;

    public TenantHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        TenantHubDb.TenantHubDbOutput tenantHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        (Dictionary<string, App.ContainerApp?>? Apps, Storage.StorageAccount? StorageAccount) rbac)

    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _tenantHubDbOutput = tenantHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
        _rbac = rbac;
    }

    public List<App.ContainerApp> InitTenantHub()
    {
        var auth0TenantHubConfig = new Auth0TenantHubConfig();

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.TenantHub),
            _myConfig.BuildTime);
        var fileRandomId = new Random.RandomId(
            "sleekflow-th-file-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "tenant hub file storage"
                    }
                },
            });
        var fileStorageAccount = new Storage.StorageAccount(
            "sleekflow-th-file-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-tenant-hub-file-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = fileRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.TenantHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.TenantHub));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 4 : 1,
                            MaxReplicas = 50,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "160"
                                            }
                                        }
                                    }
                                }
                            },
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-th-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 1, Memory = "2Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80
                                        },
                                        InitialDelaySeconds = 8,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80
                                        },
                                        InitialDelaySeconds = 8,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_LOCATION",
                                            Value = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                                        },

                                        #region TenantHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_TENANT_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_tenantHubDbOutput.AccountName}.documents.azure.com"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_TENANT_HUB_DB_KEY", Value = _tenantHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_TENANT_HUB_DB_DATABASE_ID",
                                            Value = _tenantHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.TenantHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = managedEnvAndAppsTuple.MassTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = managedEnvAndAppsTuple.MassTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region TravisBackend

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TRAVIS_BACKEND_BASE_URL",
                                            Value = auth0TenantHubConfig.TravisBackendBaseUrl
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "TRAVIS_BACKEND_AUTH0_SECRET",
                                            Value = "6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o"
                                        },

                                        #endregion

                                        #region Auth0

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUTH0_DOMAIN", Value = auth0TenantHubConfig.Domain
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUTH0_CLIENT_ID", Value = auth0TenantHubConfig.ClientId
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AUTH0_CLIENT_SECRET", Value = auth0TenantHubConfig.ClientSecret
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ACTION_AUDIENCE", Value = auth0TenantHubConfig.ActionAudience
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ACTION_ISSUER", Value = auth0TenantHubConfig.ActionIssuer
                                        },

                                        #endregion

                                        #region PartnerStack

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "PARTNERSTACK_API_KEY",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? "pk_IbzWtnJmfgNly4b9yaMokWqj7FsYwCzI"
                                                : "pk_eeItTObgg6PY5VlABiY6bsV8jAxS0iip",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "PARTNERSTACK_API_SECRET",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? "sk_0b7Q2Ouh3LkhxeozkkNzR8FPD1vkin2a"
                                                : "sk_PyPggsn1BZemgNPaO0h9LPc9RdGMUNlH",
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion

                                        #region OpaConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OPA_STORAGE_CONN_STR",
                                            Value = _rbac.StorageAccount != null
                                                ? StorageUtils.GetConnectionString(
                                                    _resourceGroup.Name,
                                                    _rbac.StorageAccount.Name)
                                                : string.Empty,
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "OPA_STORAGE_URL",
                                            Value = Output.Format(
                                                $"https://{_rbac.StorageAccount.Name}.blob.core.windows.net/ ")
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "OPA_SERVER_URL",
                                            Value = _rbac.Apps != null && _rbac.Apps.ContainsKey(
                                                $"{ServiceNames.OpenPolicyAgent}-{managedEnvAndAppsTuple.LocationName}")
                                                ? _rbac.Apps[
                                                        $"{ServiceNames.OpenPolicyAgent}-{managedEnvAndAppsTuple.LocationName}"]
                                                    ?.Configuration.Apply(c => "https://" + c!.Ingress!.Fqdn)
                                                : string.Empty
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "OPAL_SERVER_URL",
                                            Value = _rbac.Apps != null && _rbac.Apps.ContainsKey(
                                                $"{ServiceNames.OpenPolicyAgent}-{managedEnvAndAppsTuple.LocationName}")
                                                ? _rbac.Apps[
                                                        $"{ServiceNames.OpenPolicyAdministrationLayer}-{managedEnvAndAppsTuple.LocationName}"]
                                                    ?.Configuration.Apply(c => "wss://" + c!.Ingress!.Fqdn)
                                                : string.Empty
                                        },

                                        #endregion

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FILE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                fileStorageAccount.Name),
                                        }

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.TenantHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }
}