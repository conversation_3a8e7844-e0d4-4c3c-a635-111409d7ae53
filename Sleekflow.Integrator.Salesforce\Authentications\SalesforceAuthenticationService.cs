﻿using System.Net;
using System.Net.Http.Headers;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Integrator.Salesforce.Configs;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Models.Errors;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Salesforce.Authentications;

public interface ISalesforceAuthenticationService
{
    Task<SalesforceAuthentication?> GetAsync(string sleekflowCompanyId);

    Task<SalesforceAuthentication?> GetAsync(string id, string sleekflowCompanyId);

    Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string returnToUrl,
        Dictionary<string, object?>? additionalDetails);

    Task<string> AuthenticateV2Async(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails);

    Task<SalesforceAuthentication> ReAuthenticateAndStoreAsync(
        string id,
        string sleekflowCompanyId);

    Task<SalesforceAuthentication> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId);

    Task<bool> IsActiveSessionAsync(SalesforceAuthentication authentication);

    Task<(SalesforceAuthentication Authentication, bool IsV2Authentication, string? ReturnToUrl, string? SuccessUrl, string? FailureUrl, bool? IsSandbox)>
        HandleAuthenticateCallbackAndStoreAsync(
        string code,
        string encryptedState);

    Task DeleteAsync(string id);

    Task<bool> IsApiRequestLimitExceededAsync(
        string id,
        string sleekflowCompanyId);
}

public class SalesforceAuthenticationService : ISingletonService, ISalesforceAuthenticationService
{
    private readonly ISalesforceConfig _salesforceConfig;
    private readonly ILogger<SalesforceAuthenticationService> _logger;
    private readonly ISalesforceAuthenticationRepository _salesforceAuthenticationRepository;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly HttpClient _httpClient;
    private readonly IIdService _idService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public SalesforceAuthenticationService(
        ISalesforceConfig salesforceConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<SalesforceAuthenticationService> logger,
        ISalesforceAuthenticationRepository salesforceAuthenticationRepository,
        ISalesforceConnectionService salesforceConnectionService,
        IIdService idService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _salesforceConfig = salesforceConfig;
        _logger = logger;
        _salesforceAuthenticationRepository = salesforceAuthenticationRepository;
        _salesforceConnectionService = salesforceConnectionService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _idService = idService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public class State
    {
        public string? SleekflowCompanyId { get; set; }

        public string? ReturnToUrl { get; set; }

        public string? SuccessUrl { get; set; }

        public string? FailureUrl { get; set; }

        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public State(
            string? sleekflowCompanyId,
            string? returnToUrl,
            string? successUrl,
            string? failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ReturnToUrl = returnToUrl;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class Oauth2TokenOutput
    {
        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonProperty("access_token")]
        public string? AccessToken { get; set; }

        [JsonProperty("signature")]
        public string? Signature { get; set; }

        [JsonProperty("scope")]
        public string? Scope { get; set; }

        [JsonProperty("content_domain")]
        public string? ContentDomain { get; set; }

        [JsonProperty("content_sid")]
        public string? ContentSid { get; set; }

        [JsonProperty("lighting_domain")]
        public string? LightingDomain { get; set; }

        [JsonProperty("lighting_sid")]
        public string? LightingSid { get; set; }

        [JsonProperty("visualforce_domain")]
        public string? VisualforceDomain { get; set; }

        [JsonProperty("visualforce_sid")]
        public string? VisualforceSid { get; set; }

        [JsonProperty("csrf_token")]
        public string? CsrfToken { get; set; }

        [JsonProperty("id_token")]
        public string? IdToken { get; set; }

        // https://help.salesforce.com/s/articleView?id=sf.domain_name_url_formats_what_determines.htm&type=5
        // MyDomainName.my.salesforce.com
        [JsonProperty("instance_url")]
        public string? InstanceUrl { get; set; }

        [JsonProperty("token_type")]
        public string? TokenType { get; set; }

        [JsonProperty("issued_at")]
        public string? IssuedAt { get; set; }

        [JsonProperty("refresh_token")]
        public string? RefreshToken { get; set; }

        [JsonProperty("sfdc_site_url")]
        public string? SfdcSiteUrl { get; set; }

        [JsonProperty("sfdc_site_id")]
        public string? SfdcSiteId { get; set; }
    }

    public async Task<SalesforceAuthentication?> GetAsync(string sleekflowCompanyId)
    {
        var authentication = await _salesforceAuthenticationRepository.GetOrDefaultAsync(
            sleekflowCompanyId,
            sleekflowCompanyId);
        if (authentication == null)
        {
            return null;
        }

        var authenticationIssuedAt = long.Parse(authentication.IssuedAt);
        if (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - authenticationIssuedAt < 1000 * 60 * 15)
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(sleekflowCompanyId);
    }

    public async Task<SalesforceAuthentication?> GetAsync(string id, string sleekflowCompanyId)
    {
        var authentication = await _salesforceAuthenticationRepository.GetOrDefaultAsync(
            id,
            id);
        if (authentication == null)
        {
            return null;
        }

        var authenticationIssuedAt = long.Parse(authentication.IssuedAt);
        if (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - authenticationIssuedAt < 1000 * 60 * 15)
        {
            return authentication;
        }

        try
        {
            return await ReAuthenticateAndStoreAsync(id, sleekflowCompanyId);
        }
        catch (Exception)
        {
            var connection = await _salesforceConnectionService.GetByAuthenticationIdAsync(
                sleekflowCompanyId,
                id);

            if (connection is not null)
            {
                await _salesforceConnectionService.PatchAsync(
                    connection.Id,
                    connection.SleekflowCompanyId,
                    connection.Name,
                    false);
            }

            throw;
        }
    }

    private static List<string> SandboxSleekflowCompanyIds = new List<string>
    {
        "8494ce7f-f3be-400f-8167-9dab6b28dbaf",
        "4e6b76c1-83e1-47e9-91ca-1582baa490c1",
    };

    private string GetBaseUrl(string sleekflowCompanyId, Dictionary<string, object?>? additionalDetails)
    {
        var isSandbox = SandboxSleekflowCompanyIds.Contains(sleekflowCompanyId);

        if (additionalDetails != null && additionalDetails.ContainsKey("is_sandbox"))
        {
            var isSandboxValue = additionalDetails["is_sandbox"];
            if (isSandboxValue is bool sandboxFlag)
            {
                isSandbox = isSandbox || sandboxFlag;
            }
        }

        return isSandbox ? "https://test.salesforce.com" : "https://login.salesforce.com";
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string returnToUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        var clientId = _salesforceConfig.SalesforceClientId;
        var salesforceOauthCallbackUrl = _salesforceConfig.SalesforceOauthCallbackUrl;

        var state = new State(sleekflowCompanyId, returnToUrl, null, null, additionalDetails);
        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _salesforceConfig.SalesforceOauthStateEncryptionKey);

        _logger.LogInformation(
            "Started AuthenticateAsync. encryptedState {EncryptedState}, state {State}, sleekflowCompanyId {SleekflowCompanyId}",
            encryptedState,
            state,
            sleekflowCompanyId);

        var baseUrl = GetBaseUrl(sleekflowCompanyId, additionalDetails);
        var authorizeUrl = $"{baseUrl}/services/oauth2/authorize?" +
                           "response_type=code" +
                           $"&client_id={clientId}" +
                           $"&redirect_uri={salesforceOauthCallbackUrl}" +
                           $"&state={WebUtility.UrlEncode(encryptedState)}";

        var httpResponseMsg = await _httpClient.PostAsync(authorizeUrl, null);

        var redirectUrl = httpResponseMsg.Headers.Location?.ToString();
        if (redirectUrl == null || httpResponseMsg.StatusCode != HttpStatusCode.Found)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthInitialAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogWarning(
                "The httpResponseMsg doesn't contain the authentication url from Salesforce. httpResponseMsg {HttpResponseMsg}",
                httpResponseMsg);

            throw new Exception($"Unable to get the redirectUrl. authorizeUrl {authorizeUrl}");
        }

        _logger.LogInformation(
            "Completed AuthenticateAsync. encryptedState {EncryptedState}, state {State}, redirectUrl {RedirectUrl}",
            encryptedState,
            state,
            redirectUrl);

        return redirectUrl;
    }

    public async Task<string> AuthenticateV2Async(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        var clientId = _salesforceConfig.SalesforceClientId;
        var salesforceOauthCallbackUrl = _salesforceConfig.SalesforceOauthCallbackUrl;

        var state = new State(sleekflowCompanyId, null, successUrl, failureUrl, additionalDetails);
        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _salesforceConfig.SalesforceOauthStateEncryptionKey);

        _logger.LogInformation(
            "Started AuthenticateAsync. encryptedState {EncryptedState}, state {State}, sleekflowCompanyId {SleekflowCompanyId}",
            encryptedState,
            state,
            sleekflowCompanyId);

        var baseUrl = GetBaseUrl(sleekflowCompanyId, additionalDetails);
        var authorizeUrl = $"{baseUrl}/services/oauth2/authorize?" +
                           "response_type=code" +
                           $"&client_id={clientId}" +
                           $"&redirect_uri={salesforceOauthCallbackUrl}" +
                           $"&state={WebUtility.UrlEncode(encryptedState)}";

        var httpResponseMsg = await _httpClient.PostAsync(authorizeUrl, null);

        var redirectUrl = httpResponseMsg.Headers.Location?.ToString();
        if (redirectUrl == null || httpResponseMsg.StatusCode != HttpStatusCode.Found)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthInitialAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogWarning(
                "The httpResponseMsg doesn't contain the authentication url from Salesforce. httpResponseMsg {HttpResponseMsg}",
                httpResponseMsg);

            throw new Exception($"Unable to get the redirectUrl. authorizeUrl {authorizeUrl}");
        }

        _logger.LogInformation(
            "Completed AuthenticateAsync. encryptedState {EncryptedState}, state {State}, redirectUrl {RedirectUrl}",
            encryptedState,
            state,
            redirectUrl);

        return redirectUrl;
    }

    public async Task<SalesforceAuthentication> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId)
    {
        var authentication = await _salesforceAuthenticationRepository.GetOrDefaultAsync(
            sleekflowCompanyId,
            sleekflowCompanyId);
        if (authentication == null || authentication.RefreshToken == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started ReAuthenticateAndStoreAsync. sleekflowCompanyId {SleekflowCompanyId}",
            sleekflowCompanyId);

        var baseUrl = GetBaseUrl(sleekflowCompanyId, null);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "refresh_token"),
            new ("client_id", _salesforceConfig.SalesforceClientId),
            new ("client_secret", _salesforceConfig.SalesforceClientSecret),
            new ("refresh_token", authentication.RefreshToken),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{baseUrl}/services/oauth2/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);

        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthReAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from salesforce. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from salesforce");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput.AccessToken == null
            || oauth2TokenOutput.IssuedAt == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthReAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from salesforce. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from salesforce");
        }

        authentication.AccessToken = oauth2TokenOutput.AccessToken;
        authentication.IssuedAt = oauth2TokenOutput.IssuedAt;
        authentication.RefreshRes = oauth2TokenOutput;

        var upsertAsync = await _salesforceAuthenticationRepository.UpsertAsync(authentication, sleekflowCompanyId);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert SalesforceAuthentication.");
        }

        _logger.LogInformation(
            "Completed ReAuthenticateAndStoreAsync. sleekflowCompanyId {SleekflowCompanyId}",
            sleekflowCompanyId);

        return authentication;
    }

    public async Task<SalesforceAuthentication> ReAuthenticateAndStoreAsync(
        string id,
        string sleekflowCompanyId)
    {
        var authentication = await _salesforceAuthenticationRepository.GetOrDefaultAsync(
            id,
            id);
        if (authentication == null || authentication.RefreshToken == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started ReAuthenticateAndStoreAsync. id {Id}",
            id);

        var connection =
            await _salesforceConnectionService.GetByAuthenticationIdAsync(
                sleekflowCompanyId,
                authentication.Id);

        var baseUrl = GetBaseUrl(
            sleekflowCompanyId,
            connection?.Environment == "sandbox" ? new Dictionary<string, object?> { { "is_sandbox", true } } : null);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "refresh_token"),
            new ("client_id", _salesforceConfig.SalesforceClientId),
            new ("client_secret", _salesforceConfig.SalesforceClientSecret),
            new ("refresh_token", authentication.RefreshToken),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{baseUrl}/services/oauth2/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);

        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthReAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from salesforce. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from salesforce");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput.AccessToken == null
            || oauth2TokenOutput.IssuedAt == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthReAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from salesforce. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from salesforce");
        }

        authentication.AccessToken = oauth2TokenOutput.AccessToken;
        authentication.IssuedAt = oauth2TokenOutput.IssuedAt;
        authentication.RefreshRes = oauth2TokenOutput;

        var upsertAsync = await _salesforceAuthenticationRepository.UpsertAsync(authentication, authentication.Id);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert SalesforceAuthentication.");
        }

        _logger.LogInformation(
            "Completed ReAuthenticateAndStoreAsync. id {Id}. sleekflowCompanyId {SleekflowCompanyId}",
            id,
            sleekflowCompanyId);

        return authentication;
    }

    public async Task<bool> IsActiveSessionAsync(SalesforceAuthentication authentication)
    {
        var baseUrl = authentication.BaseUrl ?? "https://login.salesforce.com";

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri($"{baseUrl}/services/oauth2/token")
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        if (!httpResponseMsg.IsSuccessStatusCode)
        {
            return false;
        }

        return true;
    }

    public async Task<(SalesforceAuthentication Authentication, bool IsV2Authentication, string? ReturnToUrl, string? SuccessUrl, string? FailureUrl, bool? IsSandbox)>
        HandleAuthenticateCallbackAndStoreAsync(string code, string encryptedState)
    {
        var decryptStateStr =
            AesUtils.AesDecryptBase64(
                encryptedState,
                _salesforceConfig.SalesforceOauthStateEncryptionKey);
        var state = decryptStateStr.ToObject<State>();
        if (state == null || string.IsNullOrEmpty(state.SleekflowCompanyId))
        {
            _logger.LogWarning("The SleekflowCompanyId is null. The state is invalid");

            throw new Exception("Unable to get a correct state.");
        }

        if (state.ReturnToUrl is not null)
        {
            var result = await HandleAuthenticateCallbackAndStore(state, code, encryptedState);

            return (result.Authentication, false, result.ReturnToUrl, null, null, null);
        }

        var resultV2 =
            await HandleAuthenticateCallbackAndStoreV2(state, code, encryptedState);

        return (resultV2.Authentication, true, null, resultV2.SuccessUrl, resultV2.FailureUrl, resultV2.IsSandbox);
    }

    private async Task<(SalesforceAuthentication Authentication, string? SuccessUrl, string? FailureUrl, bool? IsSandbox)>
        HandleAuthenticateCallbackAndStoreV2(State state, string code, string encryptedState)
    {
        var baseUrl = GetBaseUrl(state.SleekflowCompanyId!, state.AdditionalDetails);

        _logger.LogInformation(
            "Started HandleAuthenticateCallbackAndStoreAsyncV2. code {Code}, encryptedState {EncryptedState}], sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "authorization_code"),
            new ("code", code),
            new ("client_id", _salesforceConfig.SalesforceClientId),
            new ("client_secret", _salesforceConfig.SalesforceClientSecret),
            new ("redirect_uri", _salesforceConfig.SalesforceOauthCallbackUrl),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{baseUrl}/services/oauth2/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || string.IsNullOrEmpty(oauth2TokenOutput.AccessToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.RefreshToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.Scope)
            || string.IsNullOrEmpty(oauth2TokenOutput.IdToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.InstanceUrl)
            || string.IsNullOrEmpty(oauth2TokenOutput.Id)
            || string.IsNullOrEmpty(oauth2TokenOutput.TokenType)
            || string.IsNullOrEmpty(oauth2TokenOutput.IssuedAt))
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthCallbackErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", state.SleekflowCompanyId! },
                });

            _logger.LogWarning(
                "The oauth2TokenOutput is null or has null. sleekflowCompanyId {SleekflowCompanyId}, readAsStringAsync {ReadAsStringAsync}, httpResponseMessage {HttpResponseMessage}",
                state.SleekflowCompanyId,
                readAsStringAsync,
                httpResponseMessage);

            throw new Exception("Unable to deserialize a success /oauth2/token response from Salesforce");
        }

        SalesforceAuthentication authentication;

        var authentications = await _salesforceAuthenticationRepository.GetObjectsAsync(
            a => a.SleekflowCompanyId == state.SleekflowCompanyId
                 && a.InstanceUrl == oauth2TokenOutput.InstanceUrl);

        if (authentications.Count > 0)
        {
            authentication = authentications[0];

            var patchAsync =
                await _salesforceAuthenticationRepository.PatchAsync(
                    authentication.Id,
                    state.SleekflowCompanyId!,
                    new List<PatchOperation>
                    {
                        PatchOperation.Set("/access_token", oauth2TokenOutput.AccessToken),
                        PatchOperation.Set("/refresh_token", oauth2TokenOutput.RefreshToken),
                        PatchOperation.Set("/scope", oauth2TokenOutput.Scope),
                        PatchOperation.Set("/id_token", oauth2TokenOutput.IdToken),
                        PatchOperation.Set("/token_type", oauth2TokenOutput.TokenType),
                        PatchOperation.Set("/issued_at", oauth2TokenOutput.IssuedAt),
                        PatchOperation.Set("/raw_res", oauth2TokenOutput),
                        PatchOperation.Set("/base_url", baseUrl),
                    });
            if (patchAsync == 0)
            {
                throw new SfInternalErrorException("Unable to patch SalesforceAuthentication.");
            }
        }
        else
        {
            authentication = new SalesforceAuthentication(
                _idService.GetId("SalesforceAuthentication"),
                state.SleekflowCompanyId!,
                oauth2TokenOutput.AccessToken,
                oauth2TokenOutput.RefreshToken,
                oauth2TokenOutput.Scope,
                oauth2TokenOutput.IdToken,
                oauth2TokenOutput.InstanceUrl,
                oauth2TokenOutput.Id,
                oauth2TokenOutput.TokenType,
                oauth2TokenOutput.IssuedAt,
                oauth2TokenOutput,
                null,
                baseUrl);

            var upsertAsync =
                await _salesforceAuthenticationRepository.UpsertAsync(authentication, authentication.Id);
            if (upsertAsync == 0)
            {
                throw new SfInternalErrorException("Unable to upsert SalesforceAuthentication.");
            }
        }

        _logger.LogInformation(
            "Completed HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        return (authentication, state.SuccessUrl, state.FailureUrl, state.AdditionalDetails?["is_sandbox"] is true);
    }

    public async Task DeleteAsync(string id)
    {
        var authentication = await _salesforceAuthenticationRepository.GetAsync(
            id,
            id);
        if (authentication is null)
        {
            throw new SfNotFoundObjectException(id, id);
        }

        var deleteAsync = await _salesforceAuthenticationRepository.DeleteAsync(
            id,
            id);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the SalesforceAuthentication with id {id}, sleekflowCompanyId {id}");
        }
    }

    private async Task<(SalesforceAuthentication Authentication, string? ReturnToUrl)>
        HandleAuthenticateCallbackAndStore(State state, string code, string encryptedState)
    {
        var baseUrl = GetBaseUrl(state.SleekflowCompanyId!, state.AdditionalDetails);

        _logger.LogInformation(
            "Started HandleAuthenticateCallbackAndStore. code {Code}, encryptedState {EncryptedState}], sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "authorization_code"),
            new ("code", code),
            new ("client_id", _salesforceConfig.SalesforceClientId),
            new ("client_secret", _salesforceConfig.SalesforceClientSecret),
            new ("redirect_uri", _salesforceConfig.SalesforceOauthCallbackUrl),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{baseUrl}/services/oauth2/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || string.IsNullOrEmpty(oauth2TokenOutput.AccessToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.RefreshToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.Scope)
            || string.IsNullOrEmpty(oauth2TokenOutput.IdToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.InstanceUrl)
            || string.IsNullOrEmpty(oauth2TokenOutput.Id)
            || string.IsNullOrEmpty(oauth2TokenOutput.TokenType)
            || string.IsNullOrEmpty(oauth2TokenOutput.IssuedAt))
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceOauthCallbackErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", state.SleekflowCompanyId! },
                });

            _logger.LogWarning(
                "The oauth2TokenOutput is null or has null. sleekflowCompanyId {SleekflowCompanyId}, readAsStringAsync {ReadAsStringAsync}, httpResponseMessage {HttpResponseMessage}",
                state.SleekflowCompanyId,
                readAsStringAsync,
                httpResponseMessage);

            throw new Exception("Unable to deserialize a success /oauth2/token response from Hubspot");
        }

        var authentication = new SalesforceAuthentication(
            state.SleekflowCompanyId!,
            state.SleekflowCompanyId!,
            oauth2TokenOutput.AccessToken,
            oauth2TokenOutput.RefreshToken,
            oauth2TokenOutput.Scope,
            oauth2TokenOutput.IdToken,
            oauth2TokenOutput.InstanceUrl,
            oauth2TokenOutput.Id,
            oauth2TokenOutput.TokenType,
            oauth2TokenOutput.IssuedAt,
            oauth2TokenOutput,
            null,
            baseUrl);

        var upsertAsync =
            await _salesforceAuthenticationRepository.UpsertAsync(authentication, state.SleekflowCompanyId!);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert SalesforceAuthentication.");
        }

        _logger.LogInformation(
            "Completed HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        return (authentication, state.ReturnToUrl);
    }

    public async Task<bool> IsApiRequestLimitExceededAsync(
        string id,
        string sleekflowCompanyId)
    {
        var authentication = await _salesforceAuthenticationRepository.GetOrDefaultAsync(
            id,
            id);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var url = authentication.InstanceUrl
                  + "/services/data/v55.0/query";

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(url),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", authentication.AccessToken);

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        if (httpResponseMsg.IsSuccessStatusCode is false)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceDataApiErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                    { "authentication_id", id },
                });

            var errorResponses =
                JsonConvert.DeserializeObject<List<SalesforceApiErrorResponse>>(
                    await httpResponseMsg.Content.ReadAsStringAsync());
            if (errorResponses is not null
                && errorResponses.Exists(
                    r =>
                        r.ErrorCode == SalesforceApiErrorCodes.RequestLimitExceededErrorCode))
            {
                return true;
            }
        }

        return false;
    }
}