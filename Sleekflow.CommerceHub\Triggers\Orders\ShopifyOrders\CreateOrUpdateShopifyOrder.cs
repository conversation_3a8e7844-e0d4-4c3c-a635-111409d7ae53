using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Orders.ShopifyOrders;
using Sleekflow.CommerceHub.Orders.ShopifyOrders;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Orders.ShopifyOrders;

[TriggerGroup(ControllerNames.ShopifyOrders)]
public class CreateOrUpdateShopifyOrder
    : ITrigger<
        CreateOrUpdateShopifyOrder.CreateOrUpdateShopifyOrderInput,
        CreateOrUpdateShopifyOrder.CreateOrUpdateShopifyOrderOutput>
{
    private readonly IShopifyOrderService _shopifyOrderService;

    public CreateOrUpdateShopifyOrder(
        IShopifyOrderService shopifyOrderService)
    {
        _shopifyOrderService = shopifyOrderService;
    }

    public class CreateOrUpdateShopifyOrderInput :
        IHasSleekflowCompanyId, IHasSleekflowUserProfileId, IHasMetadata
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty("sleekflow_staff_team_ids")]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("provider_order")]
        public Dictionary<string, object?> ProviderOrder { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("user_profile")]
        public UserProfile UserProfile { get; set; }

        [JsonProperty("sleekflow_platform_country")]
        public string? SleekflowPlatformCountry { get; set; }

        [Required]
        [JsonProperty("conversion_status")]
        public string ConversionStatus { get; set; }

        [ValidateObject]
        [JsonProperty("payment_link_sent_at")]
        public DateTimeOffset? PaymentLinkSentAt { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("metadata")]
        public Dictionary<string, object?> Metadata { get; set; }

        [ValidateArray]
        [JsonProperty("record_statuses")]
        public List<string>? RecordStatuses { get; set; }

        [JsonConstructor]
        public CreateOrUpdateShopifyOrderInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string storeId,
            Dictionary<string, object?> providerOrder,
            UserProfile userProfile,
            string? sleekflowPlatformCountry,
            string conversionStatus,
            DateTimeOffset? paymentLinkSentAt,
            Dictionary<string, object?> metadata,
            List<string>? recordStatuses)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            StoreId = storeId;
            ProviderOrder = providerOrder;
            UserProfile = userProfile;
            SleekflowPlatformCountry = sleekflowPlatformCountry;
            ConversionStatus = conversionStatus;
            PaymentLinkSentAt = paymentLinkSentAt;
            Metadata = metadata;
            RecordStatuses = recordStatuses;
        }
    }

    public class CreateOrUpdateShopifyOrderOutput
    {
        [JsonProperty("shopify_order")]
        public ShopifyOrderDto ShopifyOrder { get; set; }

        [JsonConstructor]
        public CreateOrUpdateShopifyOrderOutput(
            ShopifyOrderDto shopifyOrder)
        {
            ShopifyOrder = shopifyOrder;
        }
    }

    public async Task<CreateOrUpdateShopifyOrderOutput> F(
        CreateOrUpdateShopifyOrderInput createOrUpdateShopifyOrderInput)
    {
        var shopifyOrder = await _shopifyOrderService.GetShopifyOrderAsync(
            createOrUpdateShopifyOrderInput.SleekflowCompanyId,
            createOrUpdateShopifyOrderInput.SleekflowUserProfileId,
            createOrUpdateShopifyOrderInput.StoreId,
            (string) createOrUpdateShopifyOrderInput.ProviderOrder["Name"]!);

        if (shopifyOrder is not null)
        {
            return new CreateOrUpdateShopifyOrderOutput(
                new ShopifyOrderDto(
                    await _shopifyOrderService.PatchAndGetShopifyOrderAsync(
                        shopifyOrder.Id,
                        createOrUpdateShopifyOrderInput.SleekflowCompanyId,
                        createOrUpdateShopifyOrderInput.SleekflowStaffId,
                        createOrUpdateShopifyOrderInput.SleekflowStaffTeamIds,
                        createOrUpdateShopifyOrderInput.ProviderOrder,
                        createOrUpdateShopifyOrderInput.ConversionStatus,
                        createOrUpdateShopifyOrderInput.SleekflowPlatformCountry,
                        createOrUpdateShopifyOrderInput.PaymentLinkSentAt,
                        createOrUpdateShopifyOrderInput.Metadata,
                        createOrUpdateShopifyOrderInput.RecordStatuses ?? new List<string> { "Active" })));
        }

        return new CreateOrUpdateShopifyOrderOutput(
            new ShopifyOrderDto(
                await _shopifyOrderService.CreateAndGetShopifyOrderAsync(
                    createOrUpdateShopifyOrderInput.SleekflowCompanyId,
                    createOrUpdateShopifyOrderInput.SleekflowUserProfileId,
                    createOrUpdateShopifyOrderInput.SleekflowStaffId,
                    createOrUpdateShopifyOrderInput.SleekflowStaffTeamIds,
                    createOrUpdateShopifyOrderInput.StoreId,
                    createOrUpdateShopifyOrderInput.ProviderOrder,
                    createOrUpdateShopifyOrderInput.UserProfile,
                    createOrUpdateShopifyOrderInput.ConversionStatus,
                    createOrUpdateShopifyOrderInput.SleekflowPlatformCountry,
                    createOrUpdateShopifyOrderInput.PaymentLinkSentAt,
                    createOrUpdateShopifyOrderInput.Metadata)));
    }
}