using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumerDefinition
    : ConsumerDefinition<OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class
    OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumer
    : IConsumer<
        OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent>
{
    private readonly IBus _bus;
    private readonly ILockService _lockService;
    private readonly IAuditLogService _auditLogService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumer> _logger;

    public OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumer(
        IBus bus,
        ILockService lockService,
        IAuditLogService auditLogService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumer> logger)
    {
        _bus = bus;
        _logger = logger;
        _lockService = lockService;
        _auditLogService = auditLogService;
        _businessBalanceService = businessBalanceService;
    }

    public async Task Consume(
        ConsumeContext<OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent> context)
    {
        var onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent = context.Message;
        var facebookBusinessId = onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent
            .FacebookBusinessId;
        var businessBalanceId = onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent
            .BusinessBalanceId;
        var cancellationToken = context.CancellationToken;

        var lastConversationUsageInsertTimestamp =
            onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent
                .LastConversationUsageInsertTimestamp;
        var isConversationAnalyticsTransactionLogInserted =
            onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent
                .IsConversationAnalyticsTransactionLogInserted;

        var conversationUsageExceptionState = onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent
            .WabaConversationInsertionExceptions;

        var retryCount = context.GetRedeliveryCount();
        if (context.GetRedeliveryCount() > 10)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent}",
                JsonConvert.SerializeObject(onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent));
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId
            },
            TimeSpan.FromSeconds(
                60 *
                (OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumerDefinition
                     .LockDuration +
                 OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedConsumerDefinition
                     .MaxAutoRenewDuration)),
            cancellationToken);
        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));
            return;
        }

        var businessBalance =
            await _businessBalanceService.GetOrDefaultBusinessBalanceAsync(businessBalanceId, facebookBusinessId);

        if (businessBalance is null)
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
            return;
        }

        if (lastConversationUsageInsertTimestamp >
            businessBalance.ConversationUsageInsertState.LastConversationUsageInsertTimestamp)
        {
            var beforeUpsertLastConversationUsageInsertState =
                JsonConvert.DeserializeObject<BusinessBalance>(
                    JsonConvert.SerializeObject(businessBalance));

            businessBalance.ConversationUsageInsertState.LastConversationUsageInsertTimestamp =
                lastConversationUsageInsertTimestamp;
            businessBalance.ConversationUsageInsertState.WabaConversationInsertionExceptions =
                conversationUsageExceptionState;

            businessBalance.ConversationUsageInsertState.UpdatedAt = DateTimeOffset.UtcNow;

            var upsertBusinessBalanceStatus = await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);
            if (upsertBusinessBalanceStatus == 0)
            {
                _logger.LogError(
                    "Unable to upsert business balance record with {BusinessBalance}",
                    JsonConvert.SerializeObject(businessBalance));
            }

            var afterUpsertLastConversationUsageInsertState =
                await _businessBalanceService.GetOrDefaultBusinessBalanceAsync(
                    businessBalance.Id,
                    businessBalance.FacebookBusinessId);

            await _auditLogService.AuditBusinessBalanceAsync(
                beforeUpsertLastConversationUsageInsertState,
                businessBalance.FacebookBusinessId,
                AuditingOperation.OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent,
                new Dictionary<string, object?>
                {
                    {
                        "changes", afterUpsertLastConversationUsageInsertState
                    }
                });
        }

        await _lockService.ReleaseAsync(@lock, cancellationToken);

        if (isConversationAnalyticsTransactionLogInserted)
        {
            var onCloudApiBusinessBalancePendingTransactionLogCreatedEvent =
                new OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent(
                    businessBalance.FacebookBusinessId);
            await _bus.Publish(onCloudApiBusinessBalancePendingTransactionLogCreatedEvent, cancellationToken);
        }
    }
}