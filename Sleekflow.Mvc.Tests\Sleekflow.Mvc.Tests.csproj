<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Http.Extensions" Version="2.2.0" />
      <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="NUnit" Version="3.14.0" />
      <PackageReference Include="NUnit.Analyzers" Version="4.6.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
    </ItemGroup>

</Project>
