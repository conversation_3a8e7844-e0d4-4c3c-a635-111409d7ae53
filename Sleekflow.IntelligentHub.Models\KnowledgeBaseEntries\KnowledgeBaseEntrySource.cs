using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

public class KnowledgeBaseEntrySource
{
    [JsonProperty(PropertyName = "source_id")]
    public string SourceId { get; set; }

    [JsonProperty(PropertyName = "source_type")]
    public string SourceType { get; set; }

    [JsonConstructor]
    public KnowledgeBaseEntrySource(string sourceId, string sourceType)
    {
        SourceId = sourceId;
        SourceType = sourceType;
    }
}