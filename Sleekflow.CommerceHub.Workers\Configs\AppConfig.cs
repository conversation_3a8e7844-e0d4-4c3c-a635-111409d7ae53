using Microsoft.Extensions.Configuration;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Workers.Configs;

public interface IAppConfig
{
    string CommerceHubInternalsEndpoint { get; }

    string InternalsKey { get; }
}

public class AppConfig : IAppConfig
{
    public string CommerceHubInternalsEndpoint { get; }

    public string InternalsKey { get; }

    public AppConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CommerceHubInternalsEndpoint =
            Environment.GetEnvironmentVariable("COMMERCE_HUB_INTERNALS_ENDPOINT", target)
            ?? configuration["COMMERCE_HUB_INTERNALS_ENDPOINT"];
        InternalsKey =
            Environment.GetEnvironmentVariable("INTERNALS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("INTERNALS_KEY");
    }
}