using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Blobs;

[TriggerGroup(ControllerNames.Blobs)]
public class RecordBlobUploadHistory
    : ITrigger<
        RecordBlobUploadHistory.RecordBlobUploadHistoryInput,
        RecordBlobUploadHistory.RecordBlobUploadHistoryOutput>
{
    private readonly IBlobUploadHistoryService _blobUploadHistoryService;

    public RecordBlobUploadHistory(IBlobUploadHistoryService blobUploadHistoryService)
    {
        _blobUploadHistoryService = blobUploadHistoryService;
    }

    public class RecordBlobUploadHistoryInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(BlobUploadHistory.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [Required]
        [JsonProperty(BlobUploadHistory.PropertyNameFileName)]
        public string FileName { get; set; }

        [Required]
        [JsonProperty(BlobUploadHistory.PropertyNameUploadedBy)]
        public string UploadedBy { get; set; }

        [Required]
        [JsonProperty(BlobUploadHistory.PropertyNameSourceType)]
        public string SourceType { get; set; }

        [Required]
        [JsonProperty(BlobUploadHistory.PropertyNameBlobType)]
        public string BlobType { get; set; }

        [JsonConstructor]
        public RecordBlobUploadHistoryInput(
            string sleekflowCompanyId,
            string blobId,
            string fileName,
            string uploadedBy,
            string sourceType,
            string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
            FileName = fileName;
            UploadedBy = uploadedBy;
            SourceType = sourceType;
            BlobType = blobType;
        }
    }

    public class RecordBlobUploadHistoryOutput
    {
        [JsonProperty(SysTypeNames.BlobUploadHistory)]
        public BlobUploadHistory BlobUploadHistory { get; set; }

        [JsonConstructor]
        public RecordBlobUploadHistoryOutput(BlobUploadHistory blobUploadHistory)
        {
            BlobUploadHistory = blobUploadHistory;
        }
    }

    public async Task<RecordBlobUploadHistoryOutput> F(RecordBlobUploadHistoryInput recordBlobUploadHistoryInput)
    {
        var recordBlobUploadHistory = await _blobUploadHistoryService.RecordBlobUploadHistory(
            recordBlobUploadHistoryInput.SleekflowCompanyId,
            recordBlobUploadHistoryInput.BlobId,
            recordBlobUploadHistoryInput.FileName,
            recordBlobUploadHistoryInput.UploadedBy,
            recordBlobUploadHistoryInput.SourceType,
            recordBlobUploadHistoryInput.BlobType);

        return new RecordBlobUploadHistoryOutput(recordBlobUploadHistory);
    }
}