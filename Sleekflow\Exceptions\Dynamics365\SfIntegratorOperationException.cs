﻿namespace Sleekflow.Exceptions.Dynamics365;

public class SfIntegratorOperationException : ErrorCodeException
{
    public string OperationName { get; }

    public string? ResponseStr { get; }

    public string EntityTypeName { get; }

    public HttpResponseMessage HttpResponseMessage { get; }

    public SfIntegratorOperationException(
        string operationName,
        string? responseStr,
        string entityTypeName,
        HttpResponseMessage httpResponseMessage)
        : base(
            ErrorCodeConstant.SfIntegratorOperationException,
            $"The operation request has failed. operationName=[{operationName}], entityTypeName=[{entityTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "operationName", operationName
                },
                {
                    "entityTypeName", entityTypeName
                }
            })
    {
        OperationName = operationName;
        ResponseStr = responseStr;
        EntityTypeName = entityTypeName;
        HttpResponseMessage = httpResponseMessage;
    }

    public SfIntegratorOperationException(
        Exception exception,
        string operationName,
        string? responseStr,
        string entityTypeName,
        HttpResponseMessage httpResponseMessage)
        : base(
            ErrorCodeConstant.SfIntegratorOperationException,
            $"The operation request has failed. operationName=[{operationName}], entityTypeName=[{entityTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "operationName", operationName
                },
                {
                    "entityTypeName", entityTypeName
                },
            },
            exception)
    {
        OperationName = operationName;
        ResponseStr = responseStr;
        EntityTypeName = entityTypeName;
        HttpResponseMessage = httpResponseMessage;
    }
}