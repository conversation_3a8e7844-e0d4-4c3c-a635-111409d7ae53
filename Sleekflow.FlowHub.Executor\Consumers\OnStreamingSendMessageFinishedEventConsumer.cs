using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnStreamingSendMessageFinishedEventConsumerDefinition
    : ConsumerDefinition<OnStreamingSendMessageFinishedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnStreamingSendMessageFinishedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnStreamingSendMessageFinishedEventConsumer : IConsumer<OnStreamingSendMessageFinishedEvent>
{
    private readonly IStateAggregator _stateAggregator;
    private readonly IStateService _stateService;
    private readonly IStepExecutorActivator _stepExecutorActivator;

    public OnStreamingSendMessageFinishedEventConsumer(
        IStateAggregator stateAggregator,
        IStateService stateService,
        IStepExecutorActivator stepExecutorActivator)
    {
        _stateAggregator = stateAggregator;
        _stateService = stateService;
        _stepExecutorActivator = stepExecutorActivator;
    }


    public async Task Consume(ConsumeContext<OnStreamingSendMessageFinishedEvent> context)
    {
        // write into sys_var_dict so subsequent nodes can access the results directly
        var state = await _stateService.GetProxyStateAsync(context.Message.ProxyStateId);
        await _stateAggregator.AggregateStateStepBodyAsync(
            state,
            context.Message.RecommendReplyStepId,
            context.Message.FullRecommendedReply);

        // move all subscribed send message nodes to complete
        foreach (var subscription in context.Message.StreamingSendMessageSubscriptions)
        {
            await _stepExecutorActivator.CompleteStepAsync(
                subscription.ProxyStateId,
                subscription.SendMessageStepId,
                subscription.StackEntries,
                StepExecutionStatuses.Complete);
        }
    }
}