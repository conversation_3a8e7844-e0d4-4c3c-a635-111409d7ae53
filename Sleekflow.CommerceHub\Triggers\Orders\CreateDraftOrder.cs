using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Orders;

[TriggerGroup(ControllerNames.Orders)]
public class CreateDraftOrder
    : ITrigger<
        CreateDraftOrder.CreateDraftOrderInput,
        CreateDraftOrder.CreateDraftOrderOutput>
{
    private readonly IOrderService _orderService;
    private readonly IIdService _idService;
    private readonly IProductVariantService _productVariantService;
    private readonly ICartService _cartService;

    public CreateDraftOrder(
        IOrderService orderService,
        IIdService idService,
        IProductVariantService productVariantService,
        ICartService cartService)
    {
        _orderService = orderService;
        _idService = idService;
        _productVariantService = productVariantService;
        _cartService = cartService;
    }

    public class CreateDraftOrderInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("user_profile")]
        public UserProfile UserProfile { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [ValidateIsoCurrencyCode]
        [JsonProperty("currency_iso_code")]
        public string CurrencyIsoCode { get; set; }

        [Required]
        [JsonProperty("country_iso_code")]
        public string CountryIsoCode { get; set; }

        [Required]
        [ValidateIsoLanguageCode]
        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateDraftOrderInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            UserProfile userProfile,
            string storeId,
            string currencyIsoCode,
            string countryIsoCode,
            string languageIsoCode,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            UserProfile = userProfile;
            StoreId = storeId;
            CurrencyIsoCode = currencyIsoCode;
            CountryIsoCode = countryIsoCode;
            LanguageIsoCode = languageIsoCode;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateDraftOrderOutput
    {
        [JsonProperty("order")]
        public OrderDto Order { get; set; }

        [JsonConstructor]
        public CreateDraftOrderOutput(OrderDto order)
        {
            Order = order;
        }
    }

    public async Task<CreateDraftOrderOutput> F(
        CreateDraftOrderInput createDraftOrderInput)
    {
        var cart = await _cartService.GetOrCreateStoreUserCartAsync(
            createDraftOrderInput.SleekflowCompanyId,
            createDraftOrderInput.StoreId,
            createDraftOrderInput.SleekflowUserProfileId);
        if (cart.LineItems.Count == 0)
        {
            throw new SfUserFriendlyException("This cart has no line items to create order with");
        }

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            cart.LineItems.Select(x => x.ProductVariantId).ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);

        var productVariantIdToProductVariant = productVariants.ToDictionary(pv => pv.Id, pv => pv);

        var lineItems = cart.LineItems
            .Select(
                li =>
                {
                    var productVariantSnapshot = productVariantIdToProductVariant[li.ProductVariantId];

                    return new OrderLineItem(
                        li.ProductVariantId,
                        li.ProductId,
                        null,
                        li.Quantity,
                        li.LineItemDiscount,
                        new Dictionary<string, object?>(),
                        productVariantSnapshot);
                })
            .ToList();

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createDraftOrderInput.SleekflowStaffId,
            createDraftOrderInput.SleekflowStaffTeamIds);

        var order = await _orderService.CreateAndGetOrderAsync(
            new Order(
                _idService.GetId(SysTypeNames.Order),
                createDraftOrderInput.SleekflowCompanyId,
                createDraftOrderInput.StoreId,
                createDraftOrderInput.SleekflowUserProfileId,
                createDraftOrderInput.UserProfile,
                null,
                lineItems,
                createDraftOrderInput.CurrencyIsoCode,
                createDraftOrderInput.LanguageIsoCode,
                0,
                0,
                cart.CartDiscount,
                createDraftOrderInput.CountryIsoCode,
                OrderStatuses.Draft,
                PaymentStatuses.PaymentPending,
                null,
                null,
                new Dictionary<string, object?>(),
                null,
                new List<string>()
                {
                    "Active"
                },
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowStaff,
                sleekflowStaff),
            sleekflowStaff);

        return new CreateDraftOrderOutput(new OrderDto(order));
    }
}