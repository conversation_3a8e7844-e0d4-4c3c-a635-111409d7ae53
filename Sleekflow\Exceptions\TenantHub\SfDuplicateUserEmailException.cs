using System;

namespace Sleekflow.Exceptions.TenantHub;

/// <summary>
/// Exception thrown when attempting to create a user with an email that already exists
/// </summary>
public class SfDuplicateUserEmailException : ErrorCodeException
{
    /// <summary>
    /// Initializes a new instance of the <see cref="SfDuplicateUserEmailException"/> class
    /// </summary>
    /// <param name="email">The email that already exists</param>
    public SfDuplicateUserEmailException(string email)
        : base(
            ErrorCodeConstant.SfDuplicateUserEmailException,
            $"User with email {email} already exists")
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="SfDuplicateUserEmailException"/> class with an inner exception
    /// </summary>
    /// <param name="email">The email that already exists</param>
    /// <param name="innerException">The inner exception that caused this exception</param>
    public SfDuplicateUserEmailException(string email, Exception innerException)
        : base(
            ErrorCodeConstant.SfDuplicateUserEmailException,
            $"User with email {email} already exists",
            innerException)
    {
    }
}
