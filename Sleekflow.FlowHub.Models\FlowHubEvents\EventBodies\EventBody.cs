using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public abstract class EventBody : IHasCreatedAt
{
    [JsonIgnore]
    [JsonProperty("event_name")]
    public abstract string EventName { get; }

    [Required]
    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    protected EventBody(
        DateTimeOffset createdAt)
    {
        CreatedAt = createdAt;
    }
}