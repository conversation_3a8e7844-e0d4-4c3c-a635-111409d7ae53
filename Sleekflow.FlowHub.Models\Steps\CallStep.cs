using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Models.Steps;

public class CallStep<T> : Step
    where T : TypedCallStepArgs
{
    [Required]
    [JsonProperty("call")]
    public string Call { get; set; }

    [ValidateObject]
    [JsonProperty("args")]
    public T Args { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => Args.StepCategory;

    [JsonConstructor]
    public CallStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        string call,
        T args)
        : base(id, name, assign, nextStepId)
    {
        Call = call;
        Args = args;
    }
}