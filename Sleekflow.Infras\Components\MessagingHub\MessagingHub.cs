﻿using Pulumi;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Storage;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.MessagingHub;

public class MessagingHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly MessagingHubDb.MessagingHubDbOutput _messagingHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;

    public MessagingHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        MessagingHubDb.MessagingHubDbOutput messagingHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _messagingHubDbOutput = messagingHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
    }

    public List<App.ContainerApp> InitMessagingHub()
    {
        var messagingHubConfig = new MessagingHubConfig();
        var internalRandomId = new Random.RandomId(
            "sleekflow-mh-internal-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello2", "world2"
                    }
                },
            });
        var internalStorageAccount = new Storage.StorageAccount(
            "sleekflow-mh-internal-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-messaging-hub-internal-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = internalRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var __ = new Storage.BlobContainer(
            "sleekflow-mh-internal-container",
            new Storage.BlobContainerArgs
            {
                AccountName = internalStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "internal-container"
            },
            new CustomResourceOptions
            {
                Parent = internalStorageAccount
            });

        var externalRandomId = new Random.RandomId(
            "sleekflow-mh-external-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "External", "Storage"
                    }
                },
            });
        var externalStorageAccount = new Storage.StorageAccount(
            "sleekflow-mh-external-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-messaging-hub-external-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = externalRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var ___ = new Storage.BlobContainer(
            "sleekflow-mh-external-container",
            new Storage.BlobContainerArgs
            {
                AccountName = externalStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "external-container",
            },
            new CustomResourceOptions
            {
                Parent = externalStorageAccount
            });

        var externalStorageAccountBlobServiceProperties = new Storage.BlobServiceProperties(
            "sleekflow-mh-external-storage-account-blob-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = externalStorageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*"
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.GET),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.HEAD),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.OPTIONS),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.POST)
                            },
                            AllowedOrigins = new[]
                            {
                                "http://localhost",
                                "https://localhost",
                                "https://uat.sleekflow.io",
                                "https://staging.sleekflow.io",
                                "https://app.sleekflow.io",
                                "http://localhost:3000",
                                "https://localhost:3000",
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*"
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.PUT),
                            },
                            AllowedOrigins = new[]
                            {
                                "*"
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                    },
                },
                BlobServicesName = "default",
            });

        var externalStorageManagementPolicy =
            new Storage.ManagementPolicy(
                "sleekflow-mh-external-storage-management-policy",
                new Storage.ManagementPolicyArgs()
                {
                    AccountName = externalStorageAccount.Name,
                    ManagementPolicyName = "default",
                    Policy = new Storage.Inputs.ManagementPolicySchemaArgs
                    {
                        Rules = new[]
                        {
                            new Storage.Inputs.ManagementPolicyRuleArgs
                            {
                                Definition = new Storage.Inputs.ManagementPolicyDefinitionArgs
                                {
                                    Actions = new Storage.Inputs.ManagementPolicyActionArgs
                                    {
                                        BaseBlob = new Storage.Inputs.ManagementPolicyBaseBlobArgs
                                        {
                                            Delete = new Storage.Inputs.DateAfterModificationArgs
                                            {
                                                DaysAfterModificationGreaterThan = 30,
                                            }
                                        },
                                        Snapshot = new Storage.Inputs.ManagementPolicySnapShotArgs
                                        {
                                            Delete = new Storage.Inputs.DateAfterCreationArgs
                                            {
                                                DaysAfterCreationGreaterThan = 30,
                                            },
                                        },
                                    },
                                    Filters = new Storage.Inputs.ManagementPolicyFilterArgs
                                    {
                                        BlobTypes = new[]
                                        {
                                            "blockBlob",
                                        },
                                        PrefixMatch = "external-container/"
                                    },
                                },
                                Enabled = true,
                                Name = "sleekflow-mh-external-storage-blob-lifecycle-policy",
                                Type = "Lifecycle",
                            },
                        },
                    },
                    ResourceGroupName = _resourceGroup.Name
                });

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.MessagingHub),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.MessagingHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            var worker = managedEnvAndAppsTuple.GetWorkerApp(ServiceNames.MessagingHub);

            var listWorkerHostKeysResult = Output
                .Tuple(worker!.Name, _resourceGroup.Name, worker.Id)
                .Apply(
                    items => Web.ListWebAppHostKeys.Invoke(
                        new Web.ListWebAppHostKeysInvokeArgs
                        {
                            Name = items.Item1, ResourceGroupName = items.Item2,
                        }));

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.MessagingHub));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "sleekflow-backend-aes256-key-secret", Value = "cRfUjXn2r5u8x/A?D(G-KaPdSgVkYp3s"
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 3 : 1,
                            MaxReplicas = 20,
                            Rules = new List<string>
                                {
                                    "on-cloud-api-accumulate-half-hour-conversation-usage-transaction-event",
                                    "on-cloud-api-accumulate-half-hour-conversation-usage-transaction-inserted",
                                    "on-cloud-api-business-balance-pending-transaction-log-created-event",
                                    "on-cloud-api-business-balance-resynchronization-event",
                                    "on-cloud-api-business-balance-transaction-log-resynchronization-event",
                                    "on-cloud-api-business-low-balance-event",
                                    "on-cloud-api-business-out-of-balance-event",
                                    "on-cloud-api-conversation-analytics-trigger-event",
                                    "on-cloud-api-conversation-usage-exception-event",
                                    "on-cloud-api-deactivate-business-event",
                                    "on-cloud-api-half-hour-conversation-usage-transaction-resynchronization-event",
                                    "on-cloud-api-recharge-balance-event",
                                    "on-cloud-api-waba-business-connected-event",
                                    "on-cloud-api-waba-resynchronization-trigger-event",
                                    "on-cloud-api-business-balance-auto-top-up-event",
                                    "on-cloud-api-handle-messages-webhook-event"
                                }
                                .Select(
                                    queueName => new App.Inputs.ScaleRuleArgs
                                    {
                                        Name = $"azure-servicebus-{queueName}",
                                        Custom = new App.Inputs.CustomScaleRuleArgs
                                        {
                                            Type = "azure-servicebus",
                                            Metadata = new InputMap<string>
                                            {
                                                {
                                                    "queueName", queueName
                                                },
                                                {
                                                    "messageCount", "400"
                                                },
                                            },
                                            Auth = new InputList<App.Inputs.ScaleRuleAuthArgs>
                                            {
                                                new App.Inputs.ScaleRuleAuthArgs
                                                {
                                                    TriggerParameter = "connection",
                                                    SecretRef = "service-bus-keda-conn-str-secret"
                                                },
                                            }
                                        }
                                    })
                                .Concat(
                                    new List<App.Inputs.ScaleRuleArgs>
                                    {
                                        new App.Inputs.ScaleRuleArgs
                                        {
                                            Name = "http",
                                            Http = new App.Inputs.HttpScaleRuleArgs
                                            {
                                                Metadata = new InputMap<string>
                                                {
                                                    {
                                                        "concurrentRequests", "160"
                                                    }
                                                }
                                            }
                                        }
                                    })
                                .ToList(),
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-mh-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 1, Memory = "2Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },

                                        #region MessagingHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_MESSAGING_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_messagingHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_MESSAGING_HUB_DB_KEY",
                                            Value = _messagingHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_MESSAGING_HUB_DB_DATABASE_ID",
                                            Value = _messagingHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region WorkerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_HOSTNAME",
                                            Value = worker.DefaultHostName.Apply(hn => "https://" + hn),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_FUNCTIONS_KEY",
                                            Value = listWorkerHostKeysResult
                                                .Apply(l => l.FunctionKeys!["default"]),
                                        },

                                        #endregion

                                        #region SecretConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SLEEKFLOW_BACKEND_AES256_KEY",
                                            SecretRef = "sleekflow-backend-aes256-key-secret"
                                        },

                                        #endregion

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "INTERNAL_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                internalStorageAccount.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EXTERNAL_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                externalStorageAccount.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EXTERNAL_STORAGE_CONTAINER_NAME", Value = "external-container"
                                        },

                                        // Cloud Api Configuration - later need to move to key vault
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_APP_ID", Value = "***************"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_APP_SECRET", Value = "********************************"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_BUSINESS_CREDIT_LINE_ID", Value = "****************"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_BUSINESS_ID", Value = "830068934007389"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_SYSTEM_USER_ACCESS_TOKEN",
                                            Value =
                                                "EAALi12GLcZCABAMrGOK00ulr54uMCIe6T0LAf5pmmcg4ve8ZCbedzKCRcyvQNyL9FnQPeHOIEK7AKPx31z0kR28TYmG97g5ZCtyZBsmYaiToUyufnQwmHKYMRPnRVgYKrM6SOZCrmu6GwEiaL7P0KZBsjFGeLz3g6uCumT6ayrMAg0aC6s75rI"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_SYSTEM_USER_ID", Value = "106681425562569"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_WABA_USER_LONG_ACCESS_TOKEN_SECRET",
                                            Value = "bQeThWmZq4t7w!z%C&amp;F)J@NcRfUjXnCA"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_BUSINESS_INTEGRATION_SYSTEM_USER_ACCESS_TOKEN_SECRET",
                                            Value = "zF%U5ZM&3B_+}.V.{Z?CFq,XKmL]bXC@[J%{"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FACEBOOK_WEBHOOK_VERIFICATION_TOKEN",
                                            Value =
                                                "n2r5u8xdAqD1GsKaPdSgVkYp3s6v9y4BdE6HAMbQeThWmZq4t7wGzACFOJaNdRfVE1HDMbQeShVmYq3t6w9zdC3F1JANcRfUjWnZr4u7xLAXDGoKaPdSgVkYp2s5VK"
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.MessagingHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region WhatsappCloudApiWebhook

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENV_NAME", Value = _myConfig.Name,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WHATSAPP_CLOUD_API_OVERRIDE_WEBHOOK_URL",
                                            Value = messagingHubConfig.WhatsappCloudApiOverriderWebhook
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "PAYMENT_GATEWAY_REDIRECT_URL",
                                            Value =
                                                _myConfig.Name == "production"
                                                    ? "https://app.sleekflow.io"
                                                    : "https://uat.sleekflow.io"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "STRIPE_API_KEY",
                                            Value =
                                                _myConfig.Name == "production"
                                                    ? "******************************************"
                                                    : "sk_test_W6snxTtlAExO9vFjvX5fbLdq00lAxGJMuk"
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region SlackIncomingWebhooksConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WHATSAPP_CLOUD_API_ERROR_ALERTS_WEBHOOK_URL",
                                            Value =
                                                _myConfig.Name == "production"
                                                    ? "*******************************************************************************"
                                                    : "*******************************************************************************"
                                        },

                                        #endregion SlackWebhookConfig

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.MessagingHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }
}
