using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class PlanTieringPluginTests
{
    private IPlanTieringPlugin _planTieringPlugin;
    private Kernel _kernel;

    [SetUp]
    public void Setup()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _planTieringPlugin = scope.ServiceProvider.GetRequiredService<IPlanTieringPlugin>();
    }

    [Test]
    public async Task DeterminePlanTierFromPhoneAsync_WithUSNumber_ReturnsCorrectTier()
    {
        // Arrange
        string usPhoneNumber = "****************";

        // Act
        var result = await _planTieringPlugin.DeterminePlanTierAsync(_kernel, usPhoneNumber, "US");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Country, Is.EqualTo("United States"));
        Assert.That(result.Region, Is.EqualTo("AMER"));
        Assert.That(result.Tier, Is.EqualTo("1"));
        Assert.That(result.LocalCurrency, Is.EqualTo("USD"));
    }

    [Test]
    public async Task DeterminePlanTierFromPhoneAsync_WithUKNumber_ReturnsCorrectTier()
    {
        // Arrange
        string ukPhoneNumber = "44 2079 476330";

        // Act
        var result = await _planTieringPlugin.DeterminePlanTierAsync(_kernel, ukPhoneNumber, "UK");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Country, Is.EqualTo("United Kingdom"));
        Assert.That(result.Region, Is.EqualTo("ROW"));
        Assert.That(result.Tier, Is.EqualTo("1"));
        Assert.That(result.LocalCurrency, Is.EqualTo("GBP"));
    }

    [Test]
    public async Task DeterminePlanTierFromPhoneAsync_WithHKNumber_ReturnsCorrectTier()
    {
        // Arrange
        string hkPhoneNumber = "852 9123 4567";

        // Act
        var result = await _planTieringPlugin.DeterminePlanTierAsync(_kernel, hkPhoneNumber, "HK");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Country, Is.EqualTo("Hong Kong SAR"));
        Assert.That(result.Region, Is.EqualTo("GCR & UAE"));
        Assert.That(result.Tier, Is.EqualTo("1"));
        Assert.That(result.LocalCurrency, Is.EqualTo("HKD"));
    }

    [Test]
    public async Task DeterminePlanTierFromPhoneAsync_WithSGNumber_ReturnsCorrectTier()
    {
        // Arrange
        string sgPhoneNumber = "+65 9743 0365";

        // Act
        var result = await _planTieringPlugin.DeterminePlanTierAsync(_kernel, sgPhoneNumber, "SG");

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result.Country, Is.EqualTo("Singapore"));
        Assert.That(result.Region, Is.EqualTo("ASEAN & India"));
        Assert.That(result.Tier, Is.EqualTo("2"));
        Assert.That(result.LocalCurrency, Is.EqualTo("SGD"));
    }
}