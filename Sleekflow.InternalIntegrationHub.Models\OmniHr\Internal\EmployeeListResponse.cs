using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class EmployeeListResponse
{
    [JsonProperty(PropertyName = "count")]
    public int Count { get; set; }

    [JsonProperty(PropertyName = "next")]
    public string? Next { get; set; }

    [JsonProperty(PropertyName = "previous")]
    public string? Previous { get; set; }

    [JsonProperty(PropertyName = "results")]
    public List<OmniHrEmployee> Results { get; set; }

    [JsonConstructor]
    public EmployeeListResponse(int count, string next, string previous, List<OmniHrEmployee> results)
    {
        Count = count;
        Next = next;
        Previous = previous;
        Results = results;
    }
}

public class OmniHrEmployee
{
    [JsonProperty(PropertyName = "id")]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "system_id")]
    public int SystemId { get; set; }

    [JsonProperty(PropertyName = "photo")]
    public string Photo { get; set; }

    [JsonProperty(PropertyName = "first_name")]
    public string FirstName { get; set; }

    [JsonProperty(PropertyName = "last_name")]
    public string LastName { get; set; }

    [JsonProperty(PropertyName = "preferred_name")]
    public string PreferredName { get; set; }

    [JsonProperty(PropertyName = "primary_email")]
    public OmniHrEmail PrimaryEmail { get; set; }

    [JsonProperty(PropertyName = "primary_phone")]
    public OmniHrPhone PrimaryPhone { get; set; }

    [JsonProperty(PropertyName = "location_name")]
    public string LocationName { get; set; }

    [JsonProperty(PropertyName = "position")]
    public string Position { get; set; }

    [JsonProperty(PropertyName = "department")]
    public string Department { get; set; }

    [JsonProperty(PropertyName = "country")]
    public string Country { get; set; }

    [JsonProperty(PropertyName = "employee_type")]
    public string EmployeeType { get; set; }

    [JsonProperty(PropertyName = "employment_status")]
    public string EmploymentStatus { get; set; }

    [JsonProperty(PropertyName = "employment_status_display")]
    public string EmploymentStatusDisplay { get; set; }

    [JsonProperty(PropertyName = "roles")]
    public List<int> Roles { get; set; }

    [JsonProperty(PropertyName = "hired_date")]
    public string HiredDate { get; set; }

    [JsonConstructor]
    public OmniHrEmployee(
        int id,
        int systemId,
        string photo,
        string firstName,
        string lastName,
        string preferredName,
        OmniHrEmail primaryEmail,
        OmniHrPhone primaryPhone,
        string locationName,
        string position,
        string department,
        string country,
        string employeeType,
        string employmentStatus,
        string employmentStatusDisplay,
        List<int> roles,
        string hiredDate)
    {
        Id = id;
        SystemId = systemId;
        Photo = photo;
        FirstName = firstName;
        LastName = lastName;
        PreferredName = preferredName;
        PrimaryEmail = primaryEmail;
        PrimaryPhone = primaryPhone;
        LocationName = locationName;
        Position = position;
        Department = department;
        Country = country;
        EmployeeType = employeeType;
        EmploymentStatus = employmentStatus;
        EmploymentStatusDisplay = employmentStatusDisplay;
        Roles = roles;
        HiredDate = hiredDate;
    }
}

public class OmniHrEmail : IEquatable<OmniHrEmail>
{
    [JsonProperty(PropertyName = "id")]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "value")]
    public string Value { get; set; }

    [JsonProperty(PropertyName = "is_primary")]
    public bool IsPrimary { get; set; }

    [JsonConstructor]
    public OmniHrEmail(int id, string value, bool isPrimary)
    {
        Id = id;
        Value = value;
        IsPrimary = isPrimary;
    }

    public OmniHrEmail(string value)
    {
        Value = value;
    }

    public bool Equals(OmniHrEmail? other)
    {
        if (other is null)
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return Value == other.Value;
    }

    public override bool Equals(object? obj)
    {
        if (obj is null)
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (obj.GetType() != GetType())
        {
            return false;
        }

        return Equals((OmniHrEmail)obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Value);
    }
}

public class OmniHrPhone
{
    [JsonProperty(PropertyName = "id")]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "value")]
    public string Value { get; set; }

    [JsonProperty(PropertyName = "is_primary")]
    public bool IsPrimary { get; set; }

    [JsonConstructor]
    public OmniHrPhone(int id, string value, bool isPrimary)
    {
        Id = id;
        Value = value;
        IsPrimary = isPrimary;
    }
}