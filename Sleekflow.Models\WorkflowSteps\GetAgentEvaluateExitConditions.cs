using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Events;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Models.WorkflowSteps;

public class GetAgentEvaluateExitConditionsEvent : AgentEventBase, IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string AgentConfigId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public int ConfidenceScore { get; set; }

    public int? Score { get; set; }

    public GetAgentEvaluateExitConditionsEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string agentId,
        List<SfChatEntry> conversationContext,
        int confidenceScore,
        int? score = null)
        : base(aggregateStepId, proxyStateId, stackEntries)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        AgentConfigId = agentId;
        ConversationContext = conversationContext;
        Score = score;
        ConfidenceScore = confidenceScore;
    }

    public class Response
    {
        [JsonProperty("is_match_exit_condition")]
        public bool IsMatchExitCondition { get; set; }

        [JsonProperty("exit_condition")]
        public string ExitCondition { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }

        [JsonConstructor]
        public Response(bool isMatchExitCondition, string exitCondition, string reason)
        {
            IsMatchExitCondition = isMatchExitCondition;
            ExitCondition = exitCondition;
            Reason = reason;
        }
    }
}