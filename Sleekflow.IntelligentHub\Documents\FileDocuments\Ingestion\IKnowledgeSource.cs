using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IKnowledgeSource
{
    public record IngestionResult(
        string[] Markdowns, // a knowledge source can produce multiple markdown strings due to semantic chunking
        IFileIngestionProgress UpdatedFileIngestionProgress);

    /**
     * Knowledge source ingestion means converting the knowledge source into a more readable format
     * (a.k.a. markdown) for LLMs.
     *
     * @return The ingestion result (markdowns).
     */
    Task<IngestionResult> Ingest(Stream blobStream, object? fileIngestionProgress);
}