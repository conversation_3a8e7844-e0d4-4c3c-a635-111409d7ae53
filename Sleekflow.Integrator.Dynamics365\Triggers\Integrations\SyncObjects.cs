using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Workers;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SyncObjects : ITrigger
{
    private readonly HttpClient _httpClient;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;
    private readonly IWorkerService _workerService;
    private readonly IWorkerConfig _workerConfig;

    public SyncObjects(
        IHttpClientFactory httpClientFactory,
        IDynamics365AuthenticationService dynamics365AuthenticationService,
        IWorkerService workerService,
        IWorkerConfig workerConfig)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
        _workerService = workerService;
        _workerConfig = workerConfig;
    }

    public class SyncObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SyncObjectsInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class SyncObjectsOutput : DurablePayload
    {
        [JsonConstructor]
        public SyncObjectsOutput(
            string id,
            string statusQueryGetUri,
            string sendEventPostUri,
            string terminatePostUri,
            string rewindPostUri,
            string purgeHistoryDeleteUri,
            string restartPostUri)
            : base(
                id,
                statusQueryGetUri,
                sendEventPostUri,
                terminatePostUri,
                rewindPostUri,
                purgeHistoryDeleteUri,
                restartPostUri)
        {
        }
    }

    public async Task<SyncObjectsOutput> F(
        SyncObjectsInput syncObjectsInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(syncObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (_, _, output) = await _workerService.PostAsync<SyncObjectsOutput>(
            _httpClient,
            JsonConvert.SerializeObject(syncObjectsInput),
            _workerConfig.WorkerHostname + "/api/Dynamics365_SyncObjects");

        return output.Data!;
    }
}