using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Messages;

[ContainerId(ContainerNames.Message)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IMessagingHubDbResolver))]
public class Message : AuditEntity
{
    public const string PropertyNameMessageResponse = "message_response";

    public const string PropertyNameException = "exception";

    public const string PropertyNameUpdatedAt = "updated_at";

    [JsonProperty("message_payload")]
    public Dictionary<string, object?> MessagePayload { get; set; }

    [JsonProperty(PropertyNameMessageResponse)]
    public Dictionary<string, object?>? MessageResponse { get; set; }

    [JsonProperty(PropertyNameException)]
    public Dictionary<string, object?>? Exception { get; set; }

    [JsonConstructor]
    public Message(
        string id,
        string sleekflowCompanyId,
        Dictionary<string, object?> messagePayload,
        Dictionary<string, object?>? messageResponse,
        Dictionary<string, object?>? exception,
        string sysTypeName,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, sysTypeName, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        MessagePayload = messagePayload;
        MessageResponse = messageResponse;
        Exception = exception;
    }
}