using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Products.Variants;

public class ProductVariantRenderingDto
{
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty("store")]
    public StoreRenderingDto Store { get; set; }

    [JsonProperty("product")]
    public ProductRenderingDto Product { get; set; }

    [JsonProperty(ProductVariant.PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(ProductVariant.PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(ProductVariant.PropertyNamePrices)]
    public List<Price> Prices { get; set; }

    [JsonProperty(ProductVariant.PropertyNamePosition)]
    public int Position { get; set; }

    [JsonProperty(ProductVariant.PropertyNameIsDefaultVariantProduct)]
    public bool IsDefaultVariantProduct { get; set; }

    [JsonProperty(ProductVariant.PropertyNameAttributes)]
    public List<ProductVariant.ProductVariantAttribute> Attributes { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty(ProductVariant.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(ProductVariant.PropertyNameImages)]
    public List<ImageDto> Images { get; set; }

    [JsonConstructor]
    public ProductVariantRenderingDto(
        string id,
        StoreRenderingDto store,
        ProductRenderingDto product,
        string? sku,
        string? url,
        List<Price> prices,
        int position,
        bool isDefaultVariantProduct,
        List<ProductVariant.ProductVariantAttribute> attributes,
        string name,
        List<Description> descriptions,
        List<ImageDto> images)
    {
        Id = id;
        Store = store;
        Product = product;
        Sku = sku;
        Url = url;
        Prices = prices;
        Position = position;
        IsDefaultVariantProduct = isDefaultVariantProduct;
        Attributes = attributes;
        Name = name;
        Descriptions = descriptions;
        Images = images;
    }

    public ProductVariantRenderingDto(
        ProductVariant productVariant,
        Store store,
        Product product,
        List<Category> productCategories,
        LanguageOption languageOption)
        : this(
            productVariant.Id,
            new StoreRenderingDto(store, languageOption),
            new ProductRenderingDto(product, productCategories, languageOption),
            productVariant.Sku,
            productVariant.Url,
            productVariant.Prices,
            productVariant.Position,
            productVariant.IsDefaultVariantProduct,
            productVariant.Attributes,
            productVariant.Names.Find(n => n.LanguageIsoCode == languageOption.LanguageIsoCode)?.Value
            ?? productVariant.Names.First(n => n.LanguageIsoCode == languageOption.DefaultLanguageIsoCode).Value,
            productVariant.Descriptions,
            productVariant.Images.Select(x => new ImageDto(x)).ToList())
    {
        if (store.Id != productVariant.StoreId)
        {
            throw new ArgumentException("StoreId of ProductVariant does not match StoreId of Store");
        }

        if (product.Id != productVariant.ProductId)
        {
            throw new ArgumentException("ProductId of ProductVariant does not match ProductId of Product");
        }
    }

    public static ProductVariantRenderingDto Sample()
    {
        var productVariant = new ProductVariantRenderingDto(
            "myProductVariantId",
            StoreRenderingDto.Sample(),
            ProductRenderingDto.Sample(),
            "myProductVariantSku",
            "https://google.com",
            new List<Price>
            {
                new Price("USD", 1)
            },
            1,
            false,
            new List<ProductVariant.ProductVariantAttribute>
            {
                new ProductVariant.ProductVariantAttribute("color", "red")
            },
            "My Product Variant Name",
            new List<Description>
            {
                new Description(
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a sample product variant description"),
                    null,
                    null),
                new Description(
                    DescriptionTypes.Text,
                    new Multilingual("en", "It allows more than on line of comments"),
                    null,
                    null)
            },
            new List<ImageDto>
            {
                new ImageDto(
                    "https://insideretail.asia/wp-content/uploads/2022/11/bigstock-Icon-Of-The-Whatsapp-App-On-T-413646935.jpg",
                    null)
            });

        return productVariant;
    }
}