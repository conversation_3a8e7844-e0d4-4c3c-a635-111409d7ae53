using Microsoft.Extensions.AI.Evaluation;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.Evaluators;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals;

public class ChatEvalResult
{
    public string Label { get; private set; }

    public string Answer { get; private set; }

    public long ElapsedMilliseconds { get; private set; }

    public double AnswerScoringScore { get; private set; }

    public ScoringOutput AnswerScoringDiagnostic { get; private set; }

    public double RagOutputScoringScore { get; private set; }

    public RagOutputScoringOutput RagOutputScoringDiagnostic { get; private set; }

    public ChatEvalResult(string label, string answer, EvaluationResult rawResult, long elapsedMilliseconds)
    {
        Label = label;
        Answer = answer;
        ElapsedMilliseconds = elapsedMilliseconds;

        var answerScoringMetric =
            rawResult.Metrics[ChatEvalAnswerScoringEvaluator.AnswerScoringMetricName] as NumericMetric;

        AnswerScoringScore =
            answerScoringMetric?.Value ?? throw new Exception("Answer scoring score is missing.");
        AnswerScoringDiagnostic =
            JsonConvert.DeserializeObject<ScoringOutput>(
                answerScoringMetric.Diagnostics[0].Message) ??
            throw new Exception("Answer scoring diagnostic is missing.");

        var ragOutputScoringMetric =
            rawResult.Metrics[RagOutputScoringEvaluator.RagOutputScoringMetricName] as NumericMetric;

        RagOutputScoringScore =
            ragOutputScoringMetric?.Value ?? throw new Exception("RAG output scoring score is missing.");
        RagOutputScoringDiagnostic =
            JsonConvert.DeserializeObject<RagOutputScoringOutput>(
                ragOutputScoringMetric.Diagnostics[0].Message) ??
            throw new Exception("RAG output scoring diagnostic is missing.");
    }
}