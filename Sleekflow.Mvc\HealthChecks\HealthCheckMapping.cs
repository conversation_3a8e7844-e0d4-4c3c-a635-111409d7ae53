using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Sleekflow.Mvc.HealthChecks;

public static class HealthCheckMapping
{
    public static void MapHealthChecks(WebApplication app, Func<HealthCheckRegistration, bool>? healthCheckFunc = null)
    {
        app.MapHealthChecks("/healthz/startup");
        app.MapHealthChecks(
            "/healthz/readiness",
            new HealthCheckOptions
            {
                Predicate = healthCheckFunc ?? (healthCheck => healthCheck.Tags.Contains(HealthCheckTags.Ready))
            });
        app.MapHealthChecks(
            "/healthz/liveness",
            new HealthCheckOptions
            {
                Predicate = _ => false
            });
    }
}