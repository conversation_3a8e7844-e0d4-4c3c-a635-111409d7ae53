using PdfSharp.Pdf;
using PdfSharp.Pdf.IO;
using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Readers;


public interface IMyPdfReader
{
    Task<int> GetPagesCountAsync(Stream stream);

    Task<MemoryStream> GetStreamRangeAsync(Stream stream, int lastPage, int numOfPages);
}

public class MyPdfReader : IMyPdfReader, IScopedService
{
    public Task<int> GetPagesCountAsync(Stream stream)
    {
        using var inputPdf = PdfReader.Open(stream, PdfDocumentOpenMode.Import);

        return Task.FromResult(inputPdf.PageCount);
    }

    public Task<MemoryStream> GetStreamRangeAsync(Stream stream, int lastPage, int numOfPages)
    {
        using var inputPdf = PdfReader.Open(stream, PdfDocumentOpenMode.Import);
        var totalPagesCount = inputPdf.PageCount;

        using var outputPdf = new PdfDocument();
        for (var i = lastPage; i < Math.Min(lastPage + numOfPages, totalPagesCount); i++)
        {
            outputPdf.AddPage(inputPdf.Pages[i]);
        }

        var outputStream = new MemoryStream();
        outputPdf.Save(outputStream);
        outputStream.Position = 0;

        return Task.FromResult(outputStream);
    }
}