﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderObjectsCount : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderObjectsCount(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderObjectsCountInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filters")]
        [Required]
        public List<SyncConfigFilter> Filters { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public GetProviderObjectsCountInput(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName,
            List<SyncConfigFilter> filters,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            Filters = filters;
            FieldFilters = fieldFilters;
        }
    }

    public class GetProviderObjectsCountOutput
    {
        [JsonProperty("count")]
        [Required]
        public long Count { get; set; }

        [JsonConstructor]
        public GetProviderObjectsCountOutput(
            long count)
        {
            Count = count;
        }
    }

    public async Task<GetProviderObjectsCountOutput> F(
        GetProviderObjectsCountInput getProviderObjectsCountInput)
    {
        var providerService = _providerSelector.GetProviderService(getProviderObjectsCountInput.ProviderName);

        var getObjectsCountOutput = await providerService.GetObjectsCountAsync(
            getProviderObjectsCountInput.SleekflowCompanyId,
            getProviderObjectsCountInput.EntityTypeName,
            getProviderObjectsCountInput.Filters
                .Select(
                    f => new SyncConfigFilterGroup(
                        new List<SyncConfigFilter>
                        {
                            f
                        }))
                .ToList());

        return new GetProviderObjectsCountOutput(getObjectsCountOutput.Count);
    }
}