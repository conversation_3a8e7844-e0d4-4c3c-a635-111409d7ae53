using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;

namespace Sleekflow.CommerceHub.Models.Orders;

public class OrderLineItemInputDto
{
    [JsonProperty("product_variant_id")]
    public string ProductVariantId { get; set; }

    [JsonProperty("product_id")]
    public string ProductId { get; set; }

    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("line_item_discount")]
    public Discount? LineItemDiscount { get; set; }

    [JsonConstructor]
    public OrderLineItemInputDto(
        string productVariantId,
        string productId,
        int quantity,
        Discount? lineItemDiscount)
    {
        ProductVariantId = productVariantId;
        ProductId = productId;
        Quantity = quantity;
        LineItemDiscount = lineItemDiscount;
    }
}