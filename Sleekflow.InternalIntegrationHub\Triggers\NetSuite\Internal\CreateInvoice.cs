using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.Validations;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreateInvoice
    : ITrigger<CreateInvoice.CreateInvoiceInput,
        CreateInvoice.CreateInvoiceOutput>
{
    private readonly INetSuiteInvoiceService _netSuiteInvoiceService;

    public CreateInvoice(INetSuiteInvoiceService netSuiteInvoiceService)
    {
        _netSuiteInvoiceService = netSuiteInvoiceService;
    }

    public class CreateInvoiceInput
    {
        [JsonProperty("external_id")]
        public string? ExternalId { get; set; }

        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("subscription_fee")]
        [Range(double.MinValue, double.MaxValue)]
        public decimal SubscriptionFee { get; set; }

        [JsonProperty("subscription_description")]
        public string? SubscriptionDescription { get; set; }

        [Required]
        [JsonProperty("one_time_setup_fee")]
        [Range(double.MinValue, double.MaxValue)]
        public decimal OneTimeSetupFee { get; set; }

        [JsonProperty("one_time_setup_description")]
        public string? OneTimeSetupDescription { get; set; }

        [Required]
        [JsonProperty("whatsapp_credit_amount")]
        [Range(double.MinValue, double.MaxValue)]
        public decimal WhatsappCreditAmount { get; set; }

        [JsonProperty("whatsapp_credit_description")]
        public string? WhatsappCreditDescription { get; set; }

        [ValidateObject]
        [JsonProperty("subscription_start_date")]
        public DateTime? SubscriptionStartDate { get; set; }

        [ValidateObject]
        [JsonProperty("subscription_end_date")]
        public DateTime? SubscriptionEndDate { get; set; }

        [ValidateObject]
        [JsonProperty("payment_term")]
        public int? PaymentTerm { get; set; }

        [Required]
        [JsonProperty("currency")]
        public string Currency { get; set; }

        [ValidateObject]
        [JsonProperty("paid_amount")]
        public double? PaidAmount { get; set; }

        [ValidateObject]
        [JsonProperty("created_date")]
        public DateTime? CreatedDate { get; set; }

        [JsonConstructor]
        public CreateInvoiceInput(
            string? externalId,
            string companyId,
            decimal subscriptionFee,
            string? subscriptionDescription,
            decimal oneTimeSetupFee,
            string? oneTimeSetupDescription,
            decimal whatsappCreditAmount,
            string? whatsappCreditDescription,
            DateTime? subscriptionStartDate,
            DateTime? subscriptionEndDate,
            int? paymentTerm,
            string currency,
            double? paidAmount,
            DateTime? createdDate)
        {
            ExternalId = externalId;
            CompanyId = companyId;
            SubscriptionFee = subscriptionFee;
            SubscriptionDescription = subscriptionDescription;
            OneTimeSetupFee = oneTimeSetupFee;
            OneTimeSetupDescription = oneTimeSetupDescription;
            WhatsappCreditAmount = whatsappCreditAmount;
            WhatsappCreditDescription = whatsappCreditDescription;
            SubscriptionStartDate = subscriptionStartDate;
            SubscriptionEndDate = subscriptionEndDate;
            PaymentTerm = paymentTerm;
            Currency = currency;
            PaidAmount = paidAmount;
            CreatedDate = createdDate;
        }
    }

    public class CreateInvoiceOutput
    {
        [JsonProperty("external_id")]
        public string? ExternalId { get; set; }

        [JsonConstructor]
        public CreateInvoiceOutput(string? externalId)
        {
            ExternalId = externalId;
        }
    }

    public async Task<CreateInvoiceOutput> F(CreateInvoiceInput input)
    {
        var response = await _netSuiteInvoiceService.CreateInvoiceAsync(input);
        if (string.Equals(response, string.Empty))
        {
            throw new Exception("Failed to create invoice");
        }

        return new CreateInvoiceOutput(response);
    }
}