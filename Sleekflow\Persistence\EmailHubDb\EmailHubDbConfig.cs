using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.EmailHubDb;

public interface IEmailHubDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class EmailHubDbConfig : IConfig, IEmailHubDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public EmailHubDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_EMAIL_HUB_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_EMAIL_HUB_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_EMAIL_HUB_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_EMAIL_HUB_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_EMAIL_HUB_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_EMAIL_HUB_DB_DATABASE_ID");
    }
}