using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Triggers.Wabas;

[TriggerGroup(ControllerNames.Wabas)]
public class EnqueueWabaBusinessConnected
    : ITrigger<
        EnqueueWabaBusinessConnected.EnqueueWabaBusinessConnectedInput,
        EnqueueWabaBusinessConnected.EnqueueWabaBusinessConnectedOutput>
{
    private readonly IBus _bus;

    public EnqueueWabaBusinessConnected(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueWabaBusinessConnectedInput
    {
        [JsonProperty("facebook_business_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("webhook_url")]
        public string? WebhookUrl { get; set; }

        [JsonConstructor]
        public EnqueueWabaBusinessConnectedInput(string facebookBusinessId, string webhookUrl)
        {
            FacebookBusinessId = facebookBusinessId;
            WebhookUrl = webhookUrl;
        }
    }

    public class EnqueueWabaBusinessConnectedOutput
    {
    }

    public async Task<EnqueueWabaBusinessConnectedOutput> F(
        EnqueueWabaBusinessConnectedInput enqueueWabaBusinessConnectedInput)
    {
        var onCloudApiWabaBusinessConnectedEvent =
            new OnCloudApiWabaBusinessConnectedEvent(
                enqueueWabaBusinessConnectedInput.FacebookBusinessId,
                enqueueWabaBusinessConnectedInput.WebhookUrl);
        await _bus.Publish(onCloudApiWabaBusinessConnectedEvent);

        return new EnqueueWabaBusinessConnectedOutput();
    }
}