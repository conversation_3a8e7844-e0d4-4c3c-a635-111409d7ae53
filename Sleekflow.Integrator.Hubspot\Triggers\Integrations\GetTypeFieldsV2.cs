﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetTypeFieldsV2 : ITrigger
{
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public GetTypeFieldsV2(
        IHubspotConnectionService hubspotConnectionService,
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotConnectionService = hubspotConnectionService;
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class GetTypeFieldsV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetTypeFieldsV2Input(
            string sleekflowCompanyId,
            string entityTypeName,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ConnectionId = connectionId;
        }
    }

    public class GetTypeFieldsV2Output
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetTypeFieldsV2Output(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<GetTypeFieldsV2Output> F(GetTypeFieldsV2Input getTypeFieldsV2Input)
    {
        var connection =
            await _hubspotConnectionService.GetByIdAsync(
                getTypeFieldsV2Input.ConnectionId,
                getTypeFieldsV2Input.SleekflowCompanyId);

        var authentication =
            await _hubspotAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getTypeFieldsV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsV2Output =
            await _hubspotObjectService.GetFieldsAsync(authentication, getTypeFieldsV2Input.EntityTypeName);

        var creatableFields = getFieldsV2Output.CreatableFields;
        var updatableFields = getFieldsV2Output.UpdatableFields;
        var viewableFields = getFieldsV2Output.ViewableFields;

        return new GetTypeFieldsV2Output(updatableFields, creatableFields, viewableFields);
    }
}