﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.KnowledgeBases;

[TriggerGroup(ControllerNames.KnowledgeBases)]
public class RemoveKnowledgeBaseEntries
    : ITrigger<
        RemoveKnowledgeBaseEntries.RemoveKnowledgeBaseEntriesInput,
        RemoveKnowledgeBaseEntries.RemoveKnowledgeBaseEntriesOutput>
{
    private readonly IKnowledgeBaseEntryService _knowledgeBaseEntryService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;
    private readonly IWebPageDocumentChunkService _webPageDocumentChunkService;

    public RemoveKnowledgeBaseEntries(
        IKnowledgeBaseEntryService knowledgeBaseEntryService,
        IFileDocumentChunkService fileDocumentChunkService,
        IWebPageDocumentChunkService webPageDocumentChunkService)
    {
        _knowledgeBaseEntryService = knowledgeBaseEntryService;
        _fileDocumentChunkService = fileDocumentChunkService;
        _webPageDocumentChunkService = webPageDocumentChunkService;
    }

    public class RemoveKnowledgeBaseEntriesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty(PropertyName = "knowledge_base_entry_ids")]
        public List<string> KnowledgeBaseEntryIds { get; set; }

        [JsonConstructor]
        public RemoveKnowledgeBaseEntriesInput(string sleekflowCompanyId, List<string> knowledgeBaseEntryIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            KnowledgeBaseEntryIds = knowledgeBaseEntryIds;
        }
    }

    public class RemoveKnowledgeBaseEntriesOutput
    {
        [JsonProperty("deleted_entry_ids ")]
        public List<string> DeletedEntryIds { get; set; }

        [JsonConstructor]
        public RemoveKnowledgeBaseEntriesOutput(List<string> deletedEntryIds)
        {
            DeletedEntryIds = deletedEntryIds;
        }
    }

    public async Task<RemoveKnowledgeBaseEntriesOutput> F(
        RemoveKnowledgeBaseEntriesInput removeKnowledgeBaseEntriesInput)
    {
        var knowledgeBaseEntries = await _knowledgeBaseEntryService.GetKnowledgeBaseEntriesAsync(
            removeKnowledgeBaseEntriesInput.SleekflowCompanyId,
            removeKnowledgeBaseEntriesInput.KnowledgeBaseEntryIds);

        var (knowledgeBaseEntryIds, fileDocumentChunkIds, webPageDocumentChunkIds) =
            knowledgeBaseEntries.Aggregate(
                (ids: new List<string>(), fileDocs: new List<string>(), webDocs: new List<string>()),
                (accumulator, entry) =>
                {
                    accumulator.ids.Add(entry.Id);
                    switch (entry.KnowledgeBaseEntrySource.SourceType)
                    {
                        case KnowledgeBaseSourceTypes.FileDocument:
                            accumulator.fileDocs.Add(entry.ChunkId);
                            break;

                        case KnowledgeBaseSourceTypes.WebPageDocument:
                            accumulator.webDocs.Add(entry.ChunkId);
                            break;
                    }

                    return accumulator;
                },
                accumulator => accumulator);

        if (fileDocumentChunkIds.Count > 0)
        {
            await _fileDocumentChunkService.DeleteFileDocumentChunkAsync(
                fileDocumentChunkIds,
                removeKnowledgeBaseEntriesInput.SleekflowCompanyId);
        }

        if (webPageDocumentChunkIds.Count > 0)
        {
            await _webPageDocumentChunkService.DeleteWebPageDocumentChunkAsync(
                webPageDocumentChunkIds,
                removeKnowledgeBaseEntriesInput.SleekflowCompanyId);
        }

        await _knowledgeBaseEntryService.DeleteKnowledgeBaseEntriesAsync(
            removeKnowledgeBaseEntriesInput.SleekflowCompanyId,
            knowledgeBaseEntryIds);

        return new RemoveKnowledgeBaseEntriesOutput(knowledgeBaseEntryIds);
    }
}