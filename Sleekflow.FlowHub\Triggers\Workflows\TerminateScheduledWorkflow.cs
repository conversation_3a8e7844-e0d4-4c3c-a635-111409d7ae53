using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class TerminateScheduledWorkflow : ITrigger
{
    private readonly IBurstyWorkflowService _burstyWorkflowService;

    public TerminateScheduledWorkflow(IBurstyWorkflowService burstyWorkflowService)
    {
        _burstyWorkflowService = burstyWorkflowService;
    }

    public class TerminateScheduledWorkflowInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string WorkflowId { get; set; }

        [JsonConstructor]
        public TerminateScheduledWorkflowInput(string sleekflowCompanyId, string workflowId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
        }
    }

    public class TerminateScheduledWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public TerminateScheduledWorkflowOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<TerminateScheduledWorkflowOutput> F(TerminateScheduledWorkflowInput input)
    {
        var proxyWorkflow =
            await _burstyWorkflowService.TerminateScheduledWorkflowAsync(input.SleekflowCompanyId, input.WorkflowId);

        return new TerminateScheduledWorkflowOutput(new WorkflowDto(proxyWorkflow));
    }
}