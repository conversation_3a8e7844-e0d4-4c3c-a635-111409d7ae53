﻿using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Events;

public class GeneratePlaygroundRecommendedReplyEvent : IHasSleekflowCompanyId
{
    public string SessionId { get; set; }

    public string MessageId { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string SleekflowUserId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public GeneratePlaygroundRecommendedReplyEvent(
        string sessionId,
        string messageId,
        string sleekflowCompanyId,
        string sleekflowUserId,
        List<SfChatEntry> conversationContext)
    {
        SessionId = sessionId;
        MessageId = messageId;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowUserId = sleekflowUserId;
        ConversationContext = conversationContext;
    }
}