﻿using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Integrations;

public interface IIntegration : IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameWorkflowId = "workflow_id";
    public const string PropertyNameWorkflowVersionedId = "workflow_versioned_id";

    public string WorkflowId { get; set; }

    public string WorkflowVersionedId { get; set; }
}