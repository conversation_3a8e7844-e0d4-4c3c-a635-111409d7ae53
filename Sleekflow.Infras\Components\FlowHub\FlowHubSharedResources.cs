﻿using Cache = Pulumi.AzureNative.Cache;
using Storage = Pulumi.AzureNative.Storage;

namespace Sleekflow.Infras.Components.FlowHub;

public class FlowHubSharedResources
{
    public Cache.Redis RedisConfig { get; }

    public Storage.StorageAccount FileStorageAccount { get; }

    public FlowHubSharedResources(
        Cache.Redis redisConfig,
        Storage.StorageAccount fileStorageAccount)
    {
        RedisConfig = redisConfig;
        FileStorageAccount = fileStorageAccount;
    }
}