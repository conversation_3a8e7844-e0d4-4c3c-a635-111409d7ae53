﻿using Newtonsoft.Json;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;

namespace Sleekflow.Exceptions;

public class ErrorCodeException : Exception
{
    public int ErrorCode { get; }

    public string SerializedContext { get; } = "{}";

    public string SerializedOutput { get; } = string.Empty;

    public ErrorCodeException(Output<dynamic>? output)
        : base(output?.Message)
    {
        ErrorCode = ErrorCodeConstant.SfInternalException;
        SerializedOutput = output == null
            ? string.Empty
            : JsonConvert.SerializeObject(output, JsonConfig.DefaultJsonSerializerSettings);
    }

    public ErrorCodeException(
        int errorCode)
    {
        ErrorCode = errorCode;
    }

    public ErrorCodeException(
        int errorCode,
        string message)
        : base(message)
    {
        ErrorCode = errorCode;
    }

    public ErrorCodeException(
        int errorCode,
        string message,
        Dictionary<string, object?> context)
        : base(message)
    {
        ErrorCode = errorCode;
        SerializedContext = JsonConvert.SerializeObject(context, JsonConfig.DefaultJsonSerializerSettings);
    }

    public ErrorCodeException(
        int errorCode,
        string message,
        Exception innerException)
        : base(message, innerException)
    {
        ErrorCode = errorCode;
    }

    public ErrorCodeException(
        int errorCode,
        string message,
        Dictionary<string, object?> context,
        Exception innerException)
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        SerializedContext = JsonConvert.SerializeObject(context, JsonConfig.DefaultJsonSerializerSettings);
    }
}