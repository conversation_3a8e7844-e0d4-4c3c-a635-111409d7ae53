using Newtonsoft.Json;

namespace Sleekflow.KrakenD.Generator.InternalObjects;

public class InternalEndpoint : Endpoint
{
    [JsonIgnore]
    public string HubName { get; }

    [JsonIgnore]
    public string HostEnvName { get; }

    [JsonIgnore]
    public string KeyEnvName { get; }

    public InternalEndpoint(string hubName, string hostEnvName, string keyEnvName)
    {
        HubName = hubName.ToKebabCase();
        HostEnvName = hostEnvName;
        KeyEnvName = keyEnvName;

        EndpointEndpoint = "/v1/" + hubName.ToKebabCase() + "/internals/{method}";
        Method = Generator.Method.Post;
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/Internals/{method}",
                Method = Generator.Method.Post,
                Host = new object[]
                {
                    "{{ env \"" + hostEnvName + "\" }}"
                },
            }
        };
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaEndpoint = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/pre_endpoint.lua"
                },
                Pre = "pre_endpoint('{{ env \"" + keyEnvName + "\" }}')",
                Live = false,
                AllowOpenLibs = false
            },
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        };
        InputHeaders = new string[]
        {
            "Content-Type",
            "Traceparent",
            "X-Sleekflow-Distributed-Invocation-Context"
        };
    }
}