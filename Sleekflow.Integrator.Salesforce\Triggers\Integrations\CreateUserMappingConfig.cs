﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class CreateUserMappingConfig : ITrigger
{
    private readonly ISalesforceUserMappingConfigService _salesforceUserMappingConfigService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public CreateUserMappingConfig(
        ISalesforceUserMappingConfigService salesforceUserMappingConfigService,
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceUserMappingConfigService = salesforceUserMappingConfigService;
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class CreateUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [ValidateArray]
        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }

        [JsonConstructor]
        public CreateUserMappingConfigInput(
            string sleekflowCompanyId,
            string connectionId,
            List<UserMapping>? userMappings)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            UserMappings = userMappings;
        }
    }

    public class CreateUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public CreateUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<CreateUserMappingConfigOutput> F(
        CreateUserMappingConfigInput createUserMappingConfigInput)
    {
        var sleekflowCompanyId = createUserMappingConfigInput.SleekflowCompanyId;
        var connectionId = createUserMappingConfigInput.ConnectionId;
        var userMappings = createUserMappingConfigInput.UserMappings;

        var connection =
            await _salesforceConnectionService.GetByIdAsync(
                connectionId,
                sleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var userMappingConfig =
            await _salesforceUserMappingConfigService.CreateAndGetAsync(
                sleekflowCompanyId,
                connectionId,
                userMappings);

        return new CreateUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}