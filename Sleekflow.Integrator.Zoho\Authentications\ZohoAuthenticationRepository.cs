using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Authentications;

public interface IZohoAuthenticationRepository : IRepository<ZohoAuthentication>
{
}

public class ZohoAuthenticationRepository
    : BaseRepository<ZohoAuthentication>,
        IZohoAuthenticationRepository,
        ISingletonService
{
    public ZohoAuthenticationRepository(
        ILogger<BaseRepository<ZohoAuthentication>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}