using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetContactPropertyValueByContactIdsInput
{
  [JsonProperty("sleekflow_company_id")]
  [Required]
  public string SleekflowCompanyId { get; set; }

  [JsonProperty("contact_ids")]
  [Required]
  [Validations.ValidateArray]

  public List<string> ContactIds { get; set; }

  [JsonProperty("contact_property_id")]
  [Required]
  public string ContactPropertyId { get; set; }

  [JsonConstructor]
  public GetContactPropertyValueByContactIdsInput(
      string sleekflowCompanyId,
      List<string> contactIds,
      string contactPropertyId)
  {
    SleekflowCompanyId = sleekflowCompanyId;
    ContactPropertyId = contactPropertyId;
    ContactIds = contactIds;
  }
}