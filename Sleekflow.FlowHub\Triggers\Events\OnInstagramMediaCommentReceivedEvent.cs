﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    null,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnInstagramMediaCommentReceivedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnInstagramMediaCommentReceivedEvent(IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnInstagramMediaCommentReceivedEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [System.ComponentModel.DataAnnotations.Required]
        public OnInstagramMediaCommentReceivedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnInstagramMediaCommentReceivedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnInstagramMediaCommentReceivedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnInstagramMediaCommentReceivedEventOutput
    {

    }

    public async Task<OnInstagramMediaCommentReceivedEventOutput> F(
        OnInstagramMediaCommentReceivedEventInput onInstagramMediaCommentReceivedEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onInstagramMediaCommentReceivedEventInput.EventBody,
                onInstagramMediaCommentReceivedEventInput.ContactId,
                "Contact",
                onInstagramMediaCommentReceivedEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onInstagramMediaCommentReceivedEventInput.SleekflowCompanyId));

        return new OnInstagramMediaCommentReceivedEventOutput();
    }
}