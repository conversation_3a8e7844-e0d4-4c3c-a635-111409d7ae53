using System.Text;
using MailKit.Net.Smtp;
using MassTransit;
using MimeKit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Attachments;
using Sleekflow.EmailHub.Disposable.Authentications;
using Sleekflow.EmailHub.Disposable.Subscriptions;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Disposable.Authentications;
using Sleekflow.EmailHub.Models.Disposable.Communications;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.EmailHub.Disposable.Communications;

public interface IDisposableCommunicationService : IEmailCommunicationService
{
}

public class DisposableCommunicationService : IScopedService, IDisposableCommunicationService
{
    private readonly IEmailRepository _emailRepository;
    private readonly IBus _bus;
    private readonly ILogger<DisposableCommunicationService> _logger;
    private readonly IIdService _idService;
    private readonly IDisposableAuthenticationService _disposableAuthenticationService;
    private readonly IDisposableSubscriptionService _disposableSubscriptionService;
    private readonly IAttachmentService _attachmentService;

    public DisposableCommunicationService(
        IEmailRepository emailRepository,
        IBus bus,
        ILogger<DisposableCommunicationService> logger,
        IIdService idService,
        IDisposableAuthenticationService disposableAuthenticationService,
        IDisposableSubscriptionService disposableSubscriptionService,
        IAttachmentService attachmentService)
    {
        _emailRepository = emailRepository;
        _bus = bus;
        _logger = logger;
        _idService = idService;
        _disposableAuthenticationService = disposableAuthenticationService;
        _disposableSubscriptionService = disposableSubscriptionService;
        _attachmentService = attachmentService;
    }

    public async Task OnReceiveEmailAsync(
        List<string> sleekflowCompanyIds,
        string emailAddress,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            _ = await _disposableAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                emailAddress,
                cancellationToken: cancellationToken);

            _ = await _disposableSubscriptionService.GetSubscriptionAsync(
                sleekflowCompanyId,
                emailAddress,
                cancellationToken);

            if (!(emailMetadata != null
                  && emailMetadata.TryGetValue("email", out var emailBase64String)))
            {
                throw new SfNotFoundObjectException(
                    $"Email field is not found: : emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
            }

            try
            {
                var emailId = _idService.GetId("Email");

                var emailBytes = Encoding.UTF8.GetBytes(emailBase64String);

                using var mm = new MemoryStream(emailBytes);
                var message = await MimeMessage.LoadAsync(mm, cancellationToken);
                var attachments = new List<EmailAttachment>();

                foreach (var attachment in message.Attachments)
                {
                    await _attachmentService.ProcessInboundAttachment(
                        sleekflowCompanyId,
                        emailId,
                        (message.To.FirstOrDefault() ?? throw new SfNotFoundObjectException(string.Empty)) as
                        MailboxAddress ??
                        throw new SfInternalErrorException(string.Empty),
                        attachment,
                        attachments,
                        cancellationToken);
                }

                var email = new Email(
                    emailId,
                    sleekflowCompanyId,
                    message.From.ToList().ConvertAll(x => (MailboxAddress) x)
                        .Select(x => new EmailContact(x.Address, x.Name)).First(),
                    message.From.ToList().ConvertAll(x => (MailboxAddress) x)
                        .Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                    message.To.ToList().ConvertAll(x => (MailboxAddress) x)
                        .Select(x => new EmailContact(x.Address, x.Name))
                        .ToList(),
                    message.Cc.ToList().ConvertAll(x => (MailboxAddress) x)
                        .Select(x => new EmailContact(x.Address, x.Name))
                        .ToList(),
                    message.Bcc.ToList().ConvertAll(x => (MailboxAddress) x)
                        .Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                    message.ReplyTo.ToList().ConvertAll(x => (MailboxAddress) x)
                        .Select(x => new EmailContact(x.Address, x.Name)).ToList(),
                    message.Subject,
                    message.HtmlBody,
                    message.TextBody,
                    false,
                    attachments,
                    new DisposableEmailMetadata(message.Date));

                _logger.LogInformation(
                    "Receive email from disposable webhook: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                    emailAddress,
                    sleekflowCompanyId);
                await _emailRepository.UpsertAsync(email, email.Id, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Error while receiving emails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}, {ex}",
                    emailAddress,
                    sleekflowCompanyId,
                    ex);
            }
        }
    }

    public async Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        var authentication =
            await _disposableAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                sender.EmailAddress,
                cancellationToken: cancellationToken);

        var defaultAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as DisposableAuthenticationMetadata ??
            throw new NullReferenceException(
                $"Cannot parse gmailAuthenticationMetaData: emailAddress {sender.EmailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        _ = await _disposableSubscriptionService.GetSubscriptionAsync(
            sleekflowCompanyId,
            sender.EmailAddress,
            cancellationToken);

        var mimeMessage = new MimeMessage();

        mimeMessage.From.Add(new MailboxAddress(sender.Name, sender.EmailAddress));

        var toAddresses = to;

        foreach (var toAddress in toAddresses)
        {
            mimeMessage.To.Add(new MailboxAddress(toAddress.Name, toAddress.EmailAddress));
        }

        var ccAddresses = cc.ToList();

        foreach (var ccAddress in ccAddresses)
        {
            mimeMessage.Cc.Add(new MailboxAddress(ccAddress.Name, ccAddress.EmailAddress));
        }

        var bccAddresses = bcc.ToList();

        foreach (var bccAddress in bccAddresses)
        {
            mimeMessage.Bcc.Add(new MailboxAddress(bccAddress.Name, bccAddress.EmailAddress));
        }

        mimeMessage.Subject = subject;

        var replyToAddresses = replyTo.ToList();

        foreach (var replyToAddress in replyToAddresses)
        {
            mimeMessage.ReplyTo.Add(new MailboxAddress(replyToAddress.Name, replyToAddress.EmailAddress));
        }

        var builder = new BodyBuilder
        {
            HtmlBody = htmlBody ?? string.Empty, TextBody = textBody ?? string.Empty
        };

        foreach (var emailAttachment in emailAttachments)
        {
            await _attachmentService.ProcessOutboundAttachment(emailAttachment, builder, cancellationToken);
        }

        mimeMessage.Body = builder.ToMessageBody();

        try
        {
            using (var client = new SmtpClient())
            {
                client.CheckCertificateRevocation = false;
                client.ServerCertificateValidationCallback = (s, c, h, e) => true;

                await client.ConnectAsync(
                    defaultAuthenticationMetadata.ServerName,
                    defaultAuthenticationMetadata.PortNumber,
                    cancellationToken: cancellationToken);

                await client.AuthenticateAsync(
                    defaultAuthenticationMetadata.Username,
                    defaultAuthenticationMetadata.Password,
                    cancellationToken);
                await client.SendAsync(mimeMessage, cancellationToken);
                await client.DisconnectAsync(true, cancellationToken);
            }
        }
        catch (Exception)
        {
            throw new SfInternalErrorException(
                $"Error on sending message: emailAddress {sender.EmailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var emailId = _idService.GetId("Email");

        await _emailRepository.UpsertAsync(
            new Email(
                emailId,
                sleekflowCompanyId,
                sender,
                new List<EmailContact>
                {
                    sender
                },
                toAddresses,
                ccAddresses,
                bccAddresses,
                replyToAddresses,
                subject,
                htmlBody,
                textBody,
                true,
                emailAttachments.ToList(),
                new DisposableEmailMetadata(DateTimeOffset.UtcNow)),
            emailId,
            cancellationToken: cancellationToken);
    }

    public async Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        _ =
            await _disposableAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                sender.EmailAddress,
                cancellationToken: cancellationToken);

        _ = await _disposableSubscriptionService.GetSubscriptionAsync(
            sleekflowCompanyId,
            sender.EmailAddress,
            cancellationToken);


        await _bus.Publish(
            new OnSendEmailTriggeredEvent(
                sleekflowCompanyId,
                ProviderNames.Disposable,
                sender,
                subject,
                to,
                cc,
                bcc,
                replyTo,
                htmlBody,
                textBody,
                emailAttachments,
                emailMetadata),
            cancellationToken);
    }

    public Task SyncAllEmailsAsync(string emailAddress, CancellationToken cancellationToken = default)
    {
        // not applicable
        throw new NotImplementedException();
    }
}