using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Twilio;

public class GetProcessedPhoneNumber
{
    public class GetProcessedPhoneNumberInput
    {
        [Required]
        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; }

        [JsonConstructor]
        public GetProcessedPhoneNumberInput(string phoneNumber)
        {
            PhoneNumber = phoneNumber;
        }
    }

    public class GetProcessedPhoneNumberOutput
    {
        [JsonProperty("processed_phone_number")]
        public string ProcessedPhoneNumber { get; set; }

        [JsonConstructor]
        public GetProcessedPhoneNumberOutput(string processedPhoneNumber)
        {
            ProcessedPhoneNumber = processedPhoneNumber;
        }
    }
}