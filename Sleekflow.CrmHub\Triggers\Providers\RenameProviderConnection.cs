using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class RenameProviderConnection : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public RenameProviderConnection(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class RenameProviderConnectionInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [Required]
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonConstructor]
        public RenameProviderConnectionInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            string name)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            Name = name;
        }
    }

    public class RenameProviderConnectionOutput
    {
        [JsonProperty("connection")]
        public ProviderConnectionDto ProviderConnection { get; set; }

        [JsonConstructor]
        public RenameProviderConnectionOutput(ProviderConnectionDto providerConnection)
        {
            ProviderConnection = providerConnection;
        }
    }

    public async Task<RenameProviderConnectionOutput> F(
        RenameProviderConnectionInput renameProviderConnectionInput)
    {
        var providerService = _providerSelector.GetProviderService(renameProviderConnectionInput.ProviderName);

        var output = await providerService.RenameProviderConnectionAsync(
            renameProviderConnectionInput.SleekflowCompanyId,
            renameProviderConnectionInput.ProviderConnectionId,
            renameProviderConnectionInput.Name);

        return new RenameProviderConnectionOutput(output.Connection);
    }
}