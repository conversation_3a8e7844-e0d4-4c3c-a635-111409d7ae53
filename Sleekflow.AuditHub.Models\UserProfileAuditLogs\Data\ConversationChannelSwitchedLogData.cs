using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class ConversationChannelSwitchedLogData
{
    [JsonProperty("channel_type")]
    public string ChannelType { get; set; }

    [JsonProperty("channel_id")]
    public string ChannelId { get; set; }

    [JsonProperty("channel_name")]
    public string ChannelName { get; set; }

    [JsonProperty("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonConstructor]
    public ConversationChannelSwitchedLogData(
        string channelType,
        string channelId,
        string channelName,
        string phoneNumber)
    {
        ChannelType = channelType;
        ChannelId = channelId;
        ChannelName = channelName;
        PhoneNumber = phoneNumber;
    }
}