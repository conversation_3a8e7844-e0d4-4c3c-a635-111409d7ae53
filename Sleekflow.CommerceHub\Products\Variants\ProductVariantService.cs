using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.CustomCatalogs.Configs;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CommerceHub;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Products.Variants;

public interface IProductVariantService
{
    Task<ProductVariant> CreateAndGetProductVariantAsync(
        ProductVariant productVariant,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<ProductVariant> GetProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId);

    Task<ProductVariant> GetProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId,
        string productId);

    Task<List<ProductVariant>> GetProductVariantsAsync(
        List<string> productVariantIds,
        string sleekflowCompanyId,
        string storeId);

    Task<List<ProductVariant>> GetProductVariantsAsync(
        string sleekflowCompanyId,
        string storeId,
        string productId);

    Task<List<ProductVariant>> GetProductVariantsAsync(
        string sleekflowCompanyId,
        string storeId,
        List<string> productIds);

    Task<ProductVariant> PatchAndGetProductVariantAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string productId,
        string? sku,
        string? url,
        List<Price> prices,
        List<ProductVariant.ProductVariantAttribute> attributes,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeleteProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId,
        string productId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<int> GetCountProductVariantsAsync(string sleekflowCompanyId);
}

public class ProductVariantService : IScopedService, IProductVariantService
{
    private readonly IProductVariantRepository _productVariantRepository;
    private readonly IImageService _imageService;
    private readonly IProductVariantValidator _productVariantValidator;
    private readonly IBus _bus;
    private readonly ICustomCatalogConfigService _customCatalogConfigService;

    public ProductVariantService(
        IProductVariantRepository productVariantRepository,
        IImageService imageService,
        IProductVariantValidator productVariantValidator,
        IBus bus,
        ICustomCatalogConfigService customCatalogConfigService)
    {
        _productVariantRepository = productVariantRepository;
        _imageService = imageService;
        _productVariantValidator = productVariantValidator;
        _bus = bus;
        _customCatalogConfigService = customCatalogConfigService;
    }

    public async Task<ProductVariant> CreateAndGetProductVariantAsync(
        ProductVariant productVariant,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var sleekflowCompanyId = productVariant.SleekflowCompanyId;
        var limitCount = await _customCatalogConfigService.GetCustomCatalogConfigLimitAsync(
            sleekflowCompanyId,
            SysTypeNames.ProductVariant);

        var existedProductVariantCount = await _productVariantRepository.GetCountAsync(sleekflowCompanyId);

        if (existedProductVariantCount + 1 > limitCount)
        {
            throw new SfExceedAvailableCountException(SysTypeNames.ProductVariant);
        }

        await _productVariantValidator.ValidateProductVariantPropertiesAsync(
            productVariant.SleekflowCompanyId,
            productVariant.StoreId,
            productVariant.Names,
            productVariant.Descriptions,
            productVariant.Attributes,
            productVariant.Prices,
            productVariant);

        return await _productVariantRepository.CreateAndGetAsync(
            productVariant,
            productVariant.SleekflowCompanyId,
            CancellationToken.None);
    }

    public async Task<ProductVariant> GetProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId)
    {
        var productVariant = await _productVariantRepository.GetProductVariantAsync(
            id,
            storeId,
            sleekflowCompanyId);

        return productVariant;
    }

    public async Task<ProductVariant> GetProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId,
        string productId)
    {
        var productVariant = await _productVariantRepository.GetProductVariantAsync(
            id,
            storeId,
            sleekflowCompanyId);
        if (productVariant.ProductId != productId)
        {
            throw new SfNotFoundObjectException(id, new PartitionKey(sleekflowCompanyId));
        }

        return productVariant;
    }

    public Task<List<ProductVariant>> GetProductVariantsAsync(
        List<string> productVariantIds,
        string sleekflowCompanyId,
        string storeId)
    {
        return _productVariantRepository.GetProductVariantsAsync(
            productVariantIds,
            storeId,
            sleekflowCompanyId);
    }

    public Task<List<ProductVariant>> GetProductVariantsAsync(
        string sleekflowCompanyId,
        string storeId,
        string productId)
    {
        return _productVariantRepository.GetProductVariantsAsync(
            storeId,
            sleekflowCompanyId,
            productId);
    }

    public Task<List<ProductVariant>> GetProductVariantsAsync(
        string sleekflowCompanyId,
        string storeId,
        List<string> productIds)
    {
        return _productVariantRepository.GetProductVariantsAsync(
            storeId,
            sleekflowCompanyId,
            productIds);
    }

    public async Task<ProductVariant> PatchAndGetProductVariantAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string productId,
        string? sku,
        string? url,
        List<Price> prices,
        List<ProductVariant.ProductVariantAttribute> attributes,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        await _productVariantValidator.ValidateProductVariantPropertiesAsync(
            sleekflowCompanyId,
            storeId,
            names,
            descriptions,
            attributes,
            prices);

        var productVariant = await _productVariantRepository.GetProductVariantAsync(
            id,
            storeId,
            sleekflowCompanyId);
        if (productVariant is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        productVariant.Sku = sku;
        productVariant.Url = url;
        productVariant.Prices = prices;
        productVariant.Attributes = attributes
            .Select(
                a => new ProductVariant.ProductVariantAttribute(
                    a.Name.ToLowerInvariant(),
                    a.Value))
            .ToList();
        productVariant.Names = names;
        productVariant.Descriptions = descriptions;
        productVariant.Images = images;
        productVariant.UpdatedBy = sleekflowStaff;
        productVariant.UpdatedAt = DateTimeOffset.UtcNow;

        var replacedProductVariant = await _productVariantRepository.ReplaceAndGetAsync(
            productVariant.Id,
            sleekflowCompanyId,
            productVariant);

        await _imageService.ClearUnusedImagesAsync(
            images,
            productVariant.Images,
            sleekflowCompanyId,
            storeId);

        await _bus.Publish<OnProductVariantChangedEvent>(
            new OnProductVariantChangedEvent(
                sleekflowCompanyId,
                storeId,
                productVariant.ProductId,
                productVariant.Id,
                sleekflowStaff));

        return replacedProductVariant;
    }

    public async Task DeleteProductVariantAsync(
        string id,
        string storeId,
        string sleekflowCompanyId,
        string productId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var productVariant = await _productVariantRepository.GetAsync(
            id,
            sleekflowCompanyId);

        if (productVariant is null || productVariant.StoreId != storeId)
        {
            throw new SfNotFoundObjectException(
                id,
                sleekflowCompanyId);
        }

        // TODO UpdatedBy

        var deleteAsync = await _productVariantRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the ProductVariant with id {id} and sleekflowCompanyId {sleekflowCompanyId}");
        }
    }

    public async Task<int> GetCountProductVariantsAsync(string sleekflowCompanyId)
    {
        return await _productVariantRepository.GetCountAsync(sleekflowCompanyId);
    }
}