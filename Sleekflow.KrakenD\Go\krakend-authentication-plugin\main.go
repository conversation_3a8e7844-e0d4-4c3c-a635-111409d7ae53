package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)


var ClientRegisterer = registerer("krakend-authentication-plugin")

type registerer string

var logger Logger = nil

func (registerer) RegisterLogger(v interface{}) {
	l, ok := v.(Logger)
	if !ok {
		return
	}
	logger = l
	logger.Debug(fmt.Sprintf("[PLUGIN: %s] Logger loaded", ClientRegisterer))
}

func (r registerer) RegisterClients(f func(
	name string,
	handler func(context.Context, map[string]interface{}) (http.Handler, error),
)) {
	f(string(r), r.registerClient)
}

var myHttpClient = &http.Client{
	Timeout:   time.Second * 60,
	Transport: initHttpTransport(),
}

// registerClient is the client handler
func (r registerer) registerClient(ctx context.Context, cfg map[string]interface{}) (http.Handler, error) {
	name, ok := cfg["name"].(string)
	if !ok {
		return nil, errors.New("wrong config")
	}
	if name != string(r) {
		return nil, fmt.<PERSON><PERSON>rf("unknown register %s", name)
	}

	config, _ := cfg["krakend-authentication-plugin"].(map[string]interface{})

	getUserAuthDetailsUrl, _ := config["get_user_auth_details_url"].(string)
	logger.Debug(fmt.Sprintf("The plugin is now initializing with the get_user_auth_details_url %s", getUserAuthDetailsUrl))

	opa_endpoint, _ := config["opa_endpoint"].(string)
  logger.Debug(fmt.Sprintf("The plugin is now initializing with the OPA endpoint: %s", opa_endpoint))


	// Return handler func
	return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		startTime := time.Now() // Start the timer

		// Extract getUserAuthDetailsInput from request
		getUserAuthDetailsInput := GetUserAuthDetailsInput{
		  SleekflowTenantHubUserId: req.Header.Get("X-Sleekflow-TenantHub-User-Id"),
			SleekflowUserId:          req.Header.Get("X-Sleekflow-User-Id"),
			SleekflowEmail:           req.Header.Get("X-Sleekflow-Email"),
			SleekflowCompanyId:       req.Header.Get("X-Sleekflow-Company-Id"),
			TargetedCompanyId:        req.Header.Get("X-Targeted-Company-Id"),
			IsRbacEnabled:            req.Header.Get("X-Sleekflow-Is-Rbac-Enabled"),
		}


		// Parse LoginAsUser from request
		loginAsUserHeader := req.Header.Get("X-Sleekflow-Login-As-User")
		if loginAsUserHeader != "" {
			var loginAsUser *LoginAsUser
			err := json.Unmarshal([]byte(loginAsUserHeader), &loginAsUser)
			if err != nil {
				logger.Error(fmt.Sprintf("1001 in %s getUserAuthDetailsInput %s loginAsUserHeader%s", time.Since(startTime), getUserAuthDetailsInput, loginAsUserHeader))

				http.Error(w, "Internal Server Error 1001", http.StatusInternalServerError)
				return
			}

			if (loginAsUser.UserID != "") && (loginAsUser.CompanyID != "") {
				getUserAuthDetailsInput.LoginAsUser = loginAsUser
			}
		}

		// Make getUserAuthDetails request
		getUserAuthDetailsInputJson, _ := json.Marshal(getUserAuthDetailsInput)

		getUserAuthDetailsReq, _ := http.NewRequestWithContext(ctx, "POST", getUserAuthDetailsUrl, strings.NewReader(string(getUserAuthDetailsInputJson)))
		getUserAuthDetailsReq.Header.Set("Content-Type", "application/json")

		getUserAuthDetailsResp, getUserAuthDetailsRespErr := myHttpClient.Do(getUserAuthDetailsReq)
		if getUserAuthDetailsResp != nil && getUserAuthDetailsResp.Body != nil {
			defer getUserAuthDetailsResp.Body.Close()
		}
		if getUserAuthDetailsRespErr != nil {
			logger.Error(fmt.Sprintf("1002 in %s getUserAuthDetailsInputJson %s getUserAuthDetailsRespErr %s", time.Since(startTime), getUserAuthDetailsInputJson, getUserAuthDetailsRespErr))

			http.Error(w, "Internal Server Error 1002", http.StatusInternalServerError)
			return
		}

		var getUserAuthDetailsOutput map[string]interface{}

		getUserAuthDetailsOutputDecodeErr := json.NewDecoder(getUserAuthDetailsResp.Body).Decode(&getUserAuthDetailsOutput)
		if getUserAuthDetailsOutputDecodeErr != nil {
			logger.Error(fmt.Sprintf("1003 in %s getUserAuthDetailsInputJson %s getUserAuthDetailsOutputDecodeErr %s", time.Since(startTime), getUserAuthDetailsInputJson, getUserAuthDetailsOutputDecodeErr))

			http.Error(w, "Internal Server Error 1003", http.StatusInternalServerError)
			return
		}

		if getUserAuthDetailsOutput["success"] != true {
			logger.Error(fmt.Sprintf("1004 in %s getUserAuthDetailsInputJson %s getUserAuthDetailsOutput %s", time.Since(startTime), getUserAuthDetailsInputJson, getUserAuthDetailsOutput))

			http.Error(w, "Internal Server Error 1004", http.StatusInternalServerError)
			return
		}

		getUserAuthDetailsOutputData := getUserAuthDetailsOutput["data"].(map[string]interface{})


		req.Header.Set("X-Sleekflow-TenantHub-User-Id", getUserAuthDetailsOutputData["sleekflow_tenanthub_user_id"].(string))
		req.Header.Set("X-Sleekflow-User-Id", getUserAuthDetailsOutputData["sleekflow_user_id"].(string))
		req.Header.Set("X-Sleekflow-Staff-Id", getUserAuthDetailsOutputData["sleekflow_staff_id"].(string))
		req.Header.Set("X-Sleekflow-Company-Id", getUserAuthDetailsOutputData["sleekflow_company_id"].(string))
		req.Header.Set("X-Sleekflow-Is-Rbac-Enabled",  fmt.Sprintf("%v", getUserAuthDetailsOutputData["is_rbac_enabled"].(bool)))



		if roles, ok := getUserAuthDetailsOutputData["sleekflow_roles"].([]interface{}); ok {
			req.Header.Set("X-Sleekflow-Roles", strings.Join(toStringArray(roles), ","))
		}

		if roleIds, ok := getUserAuthDetailsOutputData["sleekflow_role_ids"].([]interface{}); ok {
			req.Header.Set("X-Sleekflow-Role-Ids", strings.Join(toStringArray(roleIds), ","))
		}

		if teamIds, ok := getUserAuthDetailsOutputData["sleekflow_team_ids"].([]interface{}); ok {
			req.Header.Set("X-Sleekflow-Team-Ids", strings.Join(toStringArray(teamIds), ","))
		}

		if impersonator, ok := getUserAuthDetailsOutputData["sleekflow_impersonator"].(map[string]interface{}); ok {
			req.Header.Set("X-Sleekflow-Impersonator-TenantHub-User-Id", impersonator["sleekflow_tenanthub_user_id"].(string))
			req.Header.Set("X-Sleekflow-Impersonator-User-Id", impersonator["sleekflow_user_id"].(string))
			req.Header.Set("X-Sleekflow-Impersonator-Company-Id", impersonator["sleekflow_company_id"].(string))
			req.Header.Set("X-Sleekflow-Impersonator-Staff-Id", impersonator["sleekflow_staff_id"].(string))
		}
    var passed_rbac_check = opa_endpoint == ""

    logger.Debug(fmt.Sprintf("opa_endpoint: %v", opa_endpoint))


    if rbacRoles, ok := getUserAuthDetailsOutputData["rbac_role"].([]interface{}); ok && !passed_rbac_check {
        req.Header.Set("X-Sleekflow-RBAC-Role", strings.Join(toStringArray(rbacRoles), ","))

        allowed, err := performOPACheck(ctx, req, opa_endpoint, rbacRoles, getUserAuthDetailsOutputData["sleekflow_company_id"].(string), logger)
        if err != nil {
            http.Error(w, "Internal Server Error", http.StatusInternalServerError)
            return
        }

        if !allowed {
            passed_rbac_check = false

        } else {
            passed_rbac_check = true
          }


    } else {
        passed_rbac_check = true
    }

    // Pass request through if RBAC check passed
    if passed_rbac_check {
        // Forward the original request to the backend service
        resp, err := myHttpClient.Do(req.WithContext(ctx))
        if resp != nil && resp.Body != nil {
            defer resp.Body.Close()
        }

        // Log the response and any errors
        logger.Debug(fmt.Sprintf("Backend response received in %s. Response: %v, Error: %v", time.Since(startTime), resp, err))

        // Handle any errors that occurred during the request
        if err != nil {
            logger.Error(fmt.Sprintf("Error forwarding request to backend: %v", err))
            http.Error(w, "Internal Server Error 1005", http.StatusInternalServerError)
            return
        }

        // Additional logging
        logger.Debug(fmt.Sprintf("Processing backend response in %s. Response: %v, Error: %v", time.Since(startTime), resp, err))

        // Copy headers from the backend response to the client response
        for k, hs := range resp.Header {
            for _, h := range hs {
                w.Header().Add(k, h)
            }
        }

        // Set the status code of the response to match the backend's response
        w.WriteHeader(resp.StatusCode)

        // If the response body is nil, return early
        if resp.Body == nil {
            return
        }

        // Copy the response body from the backend to the client
        io.Copy(w, resp.Body)
    } else {
        // If RBAC check failed, return a custom unauthorized access response
        w.Header().Set("Content-Type", "application/json")
        w.Write([]byte(`{"unauthorized_access": true}`))
    }
	}), nil
}

// Helper to convert interface array to string array
func toStringArray(arr []interface{}) []string {
	res := make([]string, len(arr))
	for i, v := range arr {
		res[i] = v.(string)
	}
	return res
}

func initHttpTransport() *http.Transport {
	var t = http.DefaultTransport.(*http.Transport).Clone()
	t.MaxIdleConns = 300
	t.MaxConnsPerHost = 100
	t.MaxIdleConnsPerHost = 20

	t.IdleConnTimeout = 30 * time.Second
	t.TLSHandshakeTimeout = 5 * time.Second
	t.ResponseHeaderTimeout = 60 * time.Second

	t.DisableKeepAlives = false
	// t.ForceAttemptHTTP2 = true

	fmt.Println("initHttpTransport")

	return t
}

func main() {}

type Logger interface {
	Debug(v ...interface{})
	Info(v ...interface{})
	Warning(v ...interface{})
	Error(v ...interface{})
	Critical(v ...interface{})
	Fatal(v ...interface{})
}

type LoginAsUser struct {
	UserID    string `json:"user_id"`
	TenantHubUserID    string `json:"tenanthub_user_id"`
	CompanyID string `json:"company_id"`
	ExpireAt  string `json:"expire_at"`
}

type GetUserAuthDetailsInput struct {
  SleekflowTenantHubUserId   string       `json:"sleekflow_tenanthub_user_id"`
	SleekflowUserId            string       `json:"sleekflow_user_id"`
	SleekflowEmail             string       `json:"sleekflow_email"`
	SleekflowCompanyId         string       `json:"sleekflow_company_id"`
	TargetedCompanyId          string       `json:"targeted_company_id"`
	IsRbacEnabled          string       `json:"is_rbac_enabled"`
	LoginAsUser                *LoginAsUser `json:"login_as_user"`
}

