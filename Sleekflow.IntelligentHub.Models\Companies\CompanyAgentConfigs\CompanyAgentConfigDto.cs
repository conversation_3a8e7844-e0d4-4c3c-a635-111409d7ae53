using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

public class CompanyAgentConfigDto
    : IHasSleekflowCompanyId, IHasCreatedBy, IHasUpdatedBy, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameIsChatHistoryEnabledAsContext)]
    public bool IsChatHistoryEnabledAsContext { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameIsContactPropertiesEnabledAsContext)]
    public bool IsContactPropertiesEnabledAsContext { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameNumberOfPreviousMessagesInChatHistoryAvailableAsContext)]
    public int NumberOfPreviousMessagesInChatHistoryAvailableAsContext { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameChannelType)]
    public string? ChannelType { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameChannelId)]
    public string? ChannelId { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameDescription)]
    public string? Description { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNamePromptInstruction)]
    public PromptInstructionDto? PromptInstruction { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameCollaborationMode)]
    [RegularExpression(
        $"{AgentCollaborationModes.Long}|{AgentCollaborationModes.Short}|{AgentCollaborationModes.Medium}|{AgentCollaborationModes.LeadNurturing}|{AgentCollaborationModes.ManagerLeadNurturing}|{AgentCollaborationModes.ReAct}")]
    public string CollaborationMode { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameEffectiveCollaborationMode)]
    public string EffectiveCollaborationMode { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameKnowledgeRetrievalConfig)]
    public KnowledgeRetrievalConfig? KnowledgeRetrievalConfig { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameType)]
    public string Type { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameLeadNurturingTools)]
    public LeadNurturingTools? LeadNurturingTools { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameToolsConfig)]
    public ToolsConfig? ToolsConfig { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameEnricherConfigs)]
    public List<EnricherConfig> EnricherConfigs { get; set; } = new ();

    [JsonProperty(CompanyAgentConfig.PropertyNameActiveWorkflowCount)]
    public int ActiveWorkflowCount { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasUpdatedBy.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty(CompanyAgentConfig.PropertyNameActions)]
    public CompanyAgentConfigActionsDto? Actions { get; set; }

    [JsonConstructor]
    public CompanyAgentConfigDto(
        string id,
        string name,
        string sleekflowCompanyId,
        bool isChatHistoryEnabledAsContext,
        bool isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? description,
        PromptInstructionDto? promptInstruction,
        string collaborationMode,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig,
        string type,
        DateTimeOffset createdAt,
        AuditEntity.SleekflowStaff? createdBy,
        DateTimeOffset updatedAt,
        AuditEntity.SleekflowStaff? updatedBy,
        string? eTag = null,
        LeadNurturingTools? leadNurturingTools = null,
        ToolsConfig? toolsConfig = null,
        List<EnricherConfig>? enricherConfigs = null,
        CompanyAgentConfigActionsDto? actions = null,
        int activeWorkflowCount = 0,
        string? effectiveCollaborationMode = null)
    {
        Id = id;
        Name = name;
        SleekflowCompanyId = sleekflowCompanyId;
        IsChatHistoryEnabledAsContext = isChatHistoryEnabledAsContext;
        IsContactPropertiesEnabledAsContext = isContactPropertiesEnabledAsContext;
        NumberOfPreviousMessagesInChatHistoryAvailableAsContext =
            numberOfPreviousMessagesInChatHistoryAvailableAsContext;
        ChannelType = channelType;
        ChannelId = channelId;
        Description = description;
        PromptInstruction = promptInstruction;
        CollaborationMode = collaborationMode;
        KnowledgeRetrievalConfig = knowledgeRetrievalConfig;
        Type = type;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
        UpdatedAt = updatedAt;
        UpdatedBy = updatedBy;
        ETag = eTag;
        LeadNurturingTools = leadNurturingTools;
        ToolsConfig = toolsConfig;
        EnricherConfigs = enricherConfigs ?? new List<EnricherConfig>();
        Actions = actions;
        ActiveWorkflowCount = activeWorkflowCount;
        EffectiveCollaborationMode = effectiveCollaborationMode ?? collaborationMode;
    }

    public CompanyAgentConfigDto(CompanyAgentConfig config, int activeWorkflowCount = 0)
        : this(
            config.Id,
            config.Name,
            config.SleekflowCompanyId,
            config.IsChatHistoryEnabledAsContext,
            config.IsContactPropertiesEnabledAsContext,
            config.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            config.ChannelType,
            config.ChannelId,
            config.Description,
            config.PromptInstruction == null ? null : new PromptInstructionDto(config.PromptInstruction),
            config.CollaborationMode,
            config.KnowledgeRetrievalConfig,
            config.Type,
            config.CreatedAt,
            config.CreatedBy,
            config.UpdatedAt,
            config.UpdatedBy,
            config.ETag,
            config.LeadNurturingTools,
            config.ToolsConfig,
            config.EnricherConfigs,
            config.Actions?.ToDto(),
            activeWorkflowCount,
            config.EffectiveCollaborationMode)
    {
    }
}