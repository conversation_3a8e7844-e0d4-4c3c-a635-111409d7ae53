using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreatePaymentFromStripeRequest
{
    [JsonProperty("undepFunds")]
    public bool UndepFunds { get; set; }

    [JsonProperty("apply")]
    public Collection<ApplyElement> Apply { get; set; }

    [JsonProperty("currency")]
    public Currency Currency { get; set; }

    [JsonProperty("customer")]
    public Customer Customer { get; set; }

    [JsonProperty("tranDate", NullValueHandling = NullValueHandling.Ignore)]
    public string? TranDate { get; set; }

    [JsonConstructor]
    public CreatePaymentFromStripeRequest(
        bool undepFunds,
        Collection<ApplyElement> apply,
        Currency currency,
        Customer customer,
        string? tranDate)
    {
        UndepFunds = undepFunds;
        Apply = apply;
        Currency = currency;
        Customer = customer;
        TranDate = tranDate;
    }
}