using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Chats;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Agents.Reviewers;

public interface IReviewerService
{
    Task<int> GetConfidenceScoringAsync(List<SfChatEntry> sfChatEntries, string messageToEvaluate);

    Task<EvaluatedScore?> GetEvaluateScoreAsync(List<SfChatEntry> sfChatEntries, string? additionalPrompt = null);

    Task<HandoffTeam?> GetHandOffTeamAsync(List<SfChatEntry> sfChatEntries, string teamCategories);

    Task<ExitConditionResult?> GetExitConditionResultAsync(
        List<SfChatEntry> sfChatEntries,
        int? score,
        int confidenceScore,
        List<ExitCondition> exitConditions
    );

    Task<CalculateLeadScoreResult?> GetCalculateLeadScoreAsync(List<SfChatEntry> sfChatEntries, List<LeadScoreCriterion> criteria);

    Task<AddLabelResult?> GetAddLabelAsync(List<SfChatEntry> sfChatEntries, List<Label> labels, string instructions);
}

public class ReviewerService : IReviewerService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly IReviewerPlugin _reviewerPlugin;

    public ReviewerService(Kernel kernel, ILogger<ReviewerService> logger, IReviewerPlugin reviewerPlugin)
    {
        _kernel = kernel;
        _logger = logger;
        _reviewerPlugin = reviewerPlugin;
    }

    public async Task<int> GetConfidenceScoringAsync(List<SfChatEntry> sfChatEntries, string messageToEvaluate)
    {
        // Extract chat history string from the chat entries
        var chatHistory = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation(
            "Evaluate Confidence Scoring {ChatHistory} {MessageToEvaluate}",
            JsonConvert.SerializeObject(chatHistory),
            JsonConvert.SerializeObject(messageToEvaluate)
        );
        return await _reviewerPlugin.GetConfidenceScoringAsync(_kernel, chatHistory, messageToEvaluate);
    }

    public async Task<EvaluatedScore?> GetEvaluateScoreAsync(
        List<SfChatEntry> sfChatEntries,
        string? additionalPrompt = null
    )
    {
        // Extract chat history string from the chat entries
        var chatHistory = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation(
            "Evaluate Context {ChatHistory} {AdditionalPrompt}",
            JsonConvert.SerializeObject(chatHistory),
            JsonConvert.SerializeObject(additionalPrompt)
        );
        return await _reviewerPlugin.GetEvaluateScoreAsync(_kernel, chatHistory, additionalPrompt);
    }

    public async Task<HandoffTeam?> GetHandOffTeamAsync(List<SfChatEntry> sfChatEntries, string teamCategories)
    {
        // Extract chat history string from the chat entries
        var chatHistory = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation(
            "Handoff Team Context {ChatHistory} {TeamCategories}",
            JsonConvert.SerializeObject(chatHistory),
            JsonConvert.SerializeObject(teamCategories)
        );
        return await _reviewerPlugin.GetHandOffTeamAsync(_kernel, chatHistory, teamCategories);
    }

    public async Task<ExitConditionResult?> GetExitConditionResultAsync(
        List<SfChatEntry> sfChatEntries,
        int? score,
        int confidenceScore,
        List<ExitCondition> exitConditions
    )
    {
        // Extract chat history string from the chat entries
        var chatHistory = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation(
            "Evaluate Exit Condition {ChatHistory} {Score} {ConfidenceScore} {ExitConditions}",
            JsonConvert.SerializeObject(chatHistory),
            JsonConvert.SerializeObject(score),
            JsonConvert.SerializeObject(confidenceScore),
            JsonConvert.SerializeObject(exitConditions)
        );
        return await _reviewerPlugin.GetExitConditionResultAsync(_kernel, chatHistory, score, confidenceScore, exitConditions);
    }

    public async Task<CalculateLeadScoreResult?> GetCalculateLeadScoreAsync(
        List<SfChatEntry> sfChatEntries,
        List<LeadScoreCriterion> criteria
    )
    {
        // Extract chat history string from the chat entries
        var chatHistory = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation(
            "Evaluate Context {ChatHistory} {criteria}",
            JsonConvert.SerializeObject(chatHistory),
            JsonConvert.SerializeObject(criteria)
        );
        return await _reviewerPlugin.GetCalculateLeadScoreAsync(_kernel, chatHistory, criteria);
    }

    public async Task<AddLabelResult?> GetAddLabelAsync(List<SfChatEntry> sfChatEntries, List<Label> labels, string instructions)
    {
        // Extract chat history string from the chat entries
        var chatHistory = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation(
            "Evaluate Context {ChatHistory} {Labels} {Instructions}",
            JsonConvert.SerializeObject(chatHistory),
            JsonConvert.SerializeObject(labels),
            JsonConvert.SerializeObject(instructions)
        );
        return await _reviewerPlugin.GetAddLabelAsync(_kernel, chatHistory, labels, instructions);
    }

}
