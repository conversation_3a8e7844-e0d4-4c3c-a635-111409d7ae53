using System.ComponentModel.DataAnnotations;
using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Searches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CognitiveSearch;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Products;

public interface IProductSearchService
{
    Task<List<AutocompleteItem>> AutocompleteProductsAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit);

    Task<List<ProductIndexDto>> SuggestProductsAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit);

    Task<(
            List<ProductIndexDto> Products,
            IDictionary<string, IList<FacetResult>> Facets,
            long TotalCount,
            ContinuationTokenDto NextContinuationToken)>
        SearchProductsAsync(
            string sleekflowCompanyId,
            string storeId,
            List<SearchFilterGroup> filterGroups,
            ContinuationTokenDto? continuationToken,
            int limit,
            string? searchText);

    Task IndexProductsAsync(List<Product> products);

    Task DeleteProductsAsync(List<Product> products);

    public class ContinuationTokenDto
    {
        [Required]
        [Range(0, int.MaxValue)]
        [JsonProperty("skip")]
        public int Skip { get; set; }

        [JsonConstructor]
        public ContinuationTokenDto(int skip)
        {
            Skip = skip;
        }
    }
}

public class ProductSearchService : IProductSearchService, ISingletonService
{
    private static readonly IReadOnlyDictionary<string, SearchFieldDefinition> SearchFieldNameToDefinitionDict =
        new List<SearchFieldDefinition>
            {
                new (
                    Entity.PropertyNameId,
                    "id",
                    SearchFieldTypes.String),
                new (
                    IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                    "sleekflow_company_id",
                    SearchFieldTypes.String),
                new (
                    CommonFieldNames.PropertyNameStoreId,
                    "store_id",
                    SearchFieldTypes.String),
                new (
                    Product.PropertyNameCategoryIds,
                    "category_ids",
                    SearchFieldTypes.CollectionString),
                new (
                    Product.PropertyNameSku,
                    "sku",
                    SearchFieldTypes.String),
                new (
                    Product.PropertyNameIsViewEnabled,
                    "is_view_enabled",
                    SearchFieldTypes.Boolean),
                new (
                    Product.PropertyNameUrl,
                    "url",
                    SearchFieldTypes.String),
                new (
                    "name",
                    "names/value",
                    SearchFieldTypes.String),
                new (
                    "description",
                    "descriptions/text/value",
                    SearchFieldTypes.String),
                new (
                    "product_variant_name",
                    "product_variant_names/text/value",
                    SearchFieldTypes.String),
                new (
                    "product_variant_description",
                    "product_variant_descriptions/text/value",
                    SearchFieldTypes.String),
                new (
                    "product_variant_attribute",
                    "product_variant_attributes/values/value",
                    SearchFieldTypes.String),
                new (
                    "product_variant_price",
                    "product_variant_prices",
                    SearchFieldTypes.CollectionPrice),
                new (
                    CommonFieldNames.PropertyNamePlatformData + "/id",
                    "platform_data/id",
                    SearchFieldTypes.String),
                new (
                    CommonFieldNames.PropertyNamePlatformData + "/type",
                    "platform_data/type",
                    SearchFieldTypes.String),
            }
            .ToDictionary(x => x.FieldName, x => x);

    private readonly ILogger<ProductSearchService> _logger;
    private readonly ISearchFilterBuilder _searchFilterBuilder;
    private readonly SearchClient _searchClient;

    public ProductSearchService(
        ILogger<ProductSearchService> logger,
        ISearchFilterBuilder searchFilterBuilder,
        IProductSearchConfig productSearchConfig)
    {
        _logger = logger;
        _searchFilterBuilder = searchFilterBuilder;
        _searchClient = new SearchClient(
            new Uri(productSearchConfig.SearchClientUri),
            productSearchConfig.GetIndexName(),
            new AzureKeyCredential(productSearchConfig.AzureKeyCredential),
            new SearchClientOptions
            {
                Serializer = new NewtonsoftJsonObjectSerializer(
                    NewtonsoftJsonObjectSerializer.CreateJsonSerializerSettings())
            });
    }

    public async Task<List<AutocompleteItem>> AutocompleteProductsAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit)
    {
        var options = new AutocompleteOptions
        {
            Size = limit,
            UseFuzzyMatching = false,
            Mode = AutocompleteMode.TwoTerms,
            Filter = _searchFilterBuilder.BuildFilterStr(
                new List<SearchFilterGroup>(),
                SearchFieldNameToDefinitionDict,
                sleekflowCompanyId,
                storeId)
        };

        var suggestResultsResponse = await _searchClient.AutocompleteAsync(
            searchText,
            "sg",
            options);
        var suggestResults = suggestResultsResponse.Value;

        return suggestResults.Results.ToList();
    }

    public async Task<List<ProductIndexDto>> SuggestProductsAsync(
        string sleekflowCompanyId,
        string storeId,
        string searchText,
        int limit)
    {
        var options = new SuggestOptions
        {
            Size = limit,
            UseFuzzyMatching = true,
            Select =
            {
                "names/value", "descriptions/text/value",
            },
            Filter = _searchFilterBuilder.BuildFilterStr(
                new List<SearchFilterGroup>(),
                SearchFieldNameToDefinitionDict,
                sleekflowCompanyId,
                storeId)
        };

        var suggestResultsResponse = await _searchClient.SuggestAsync<ProductIndexDto>(
            searchText,
            "sg",
            options);
        var suggestResults = suggestResultsResponse.Value;

        var products = suggestResults.Results.Select(x => x.Document).ToList();

        return products;
    }

    public async
        Task<(
            List<ProductIndexDto> Products,
            IDictionary<string, IList<FacetResult>> Facets,
            long TotalCount,
            IProductSearchService.ContinuationTokenDto NextContinuationToken)> SearchProductsAsync(
            string sleekflowCompanyId,
            string storeId,
            List<SearchFilterGroup> filterGroups,
            IProductSearchService.ContinuationTokenDto? continuationToken,
            int limit,
            string? searchText)
    {
        var skip = continuationToken?.Skip ?? 0;
        var options = new SearchOptions
        {
            Size = limit, Skip = skip, IncludeTotalCount = true
        };

        options.Facets.Add("names/value");
        options.Facets.Add("descriptions/text/value");

        var filter = _searchFilterBuilder.BuildFilterStr(
            filterGroups,
            SearchFieldNameToDefinitionDict,
            sleekflowCompanyId,
            storeId);

        options.Filter = filter;
        options.IncludeTotalCount = true;

        var searchResultsResponse = await _searchClient.SearchAsync<ProductIndexDto>(
            searchText ?? "*",
            options);
        var searchResults = searchResultsResponse.Value;

        var searchPathToFieldName = SearchFieldNameToDefinitionDict.ToDictionary(k => k.Value.SearchPath, k => k.Key);

        var products = searchResults.GetResults().Select(x => x.Document).ToList();
        var facets = searchResults.Facets.ToDictionary(k => searchPathToFieldName[k.Key], v => v.Value);
        var totalCount = searchResults.TotalCount!.Value;

        return (
            products,
            facets,
            totalCount,
            skip + products.Count == searchResults.TotalCount
                ? null
                : new IProductSearchService.ContinuationTokenDto(skip + products.Count));
    }

    public async Task IndexProductsAsync(List<Product> products)
    {
        var productIndexDtos = products.Select(p => new ProductIndexDto(p)).ToList();
        var batch = IndexDocumentsBatch.Create(
            productIndexDtos.Select(IndexDocumentsAction.MergeOrUpload).ToArray());

        try
        {
            IndexDocumentsResult indexDocumentsResult = await _searchClient.IndexDocumentsAsync(batch);

            var failedIndexResults = indexDocumentsResult.Results.Where(r => !r.Succeeded).ToList();
            if (failedIndexResults.Any())
            {
                throw new SfCognitiveSearchException(failedIndexResults);
            }
        }
        catch (SfCognitiveSearchException e)
        {
            _logger.LogWarning(e, "Failed to index the Products {Ids}", e.FailedIndexResults.Select(r => r.Key));
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Failed to index the Products {Ids}", productIndexDtos.Select(p => p.Id));
        }
    }

    public async Task DeleteProductsAsync(List<Product> products)
    {
        var productIndexDtos = products.Select(p => new ProductIndexDto(p)).ToList();
        var batch = IndexDocumentsBatch.Create(
            productIndexDtos.Select(IndexDocumentsAction.Delete).ToArray());

        try
        {
            IndexDocumentsResult indexDocumentsResult = await _searchClient.IndexDocumentsAsync(batch);

            var failedIndexResults = indexDocumentsResult.Results.Where(r => !r.Succeeded).ToList();
            if (failedIndexResults.Any())
            {
                throw new SfCognitiveSearchException(failedIndexResults);
            }
        }
        catch (SfCognitiveSearchException e)
        {
            _logger.LogWarning(e, "Failed to un-index the Products {Ids}", e.FailedIndexResults.Select(r => r.Key));
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Failed to un-index the Products {Ids}", productIndexDtos.Select(p => p.Id));
        }
    }
}