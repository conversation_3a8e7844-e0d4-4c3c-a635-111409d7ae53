﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Zoho.Services.Models;

public class ZohoGetOrganizationsOutput
{
    [JsonProperty("org")]
    public List<Organization>? Organizations { get; set; }
}

public class Organization
{
    [JsonProperty("country")]
    public string? Country { get; set; }

    [JsonProperty("hierarchy_preferences")]
    public HierarchyPreferences? HierarchyPreferences { get; set; }

    [JsonProperty("photo_id")]
    public string? PhotoId { get; set; }

    [JsonProperty("city")]
    public string? City { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("mc_status")]
    public bool? McStatus { get; set; }

    [JsonProperty("gapps_enabled")]
    public bool? GappsEnabled { get; set; }

    [JsonProperty("lite_users_enabled")]
    public bool? LiteUsersEnabled { get; set; }

    [JsonProperty("domain_name")]
    public string? DomainName { get; set; }

    [JsonProperty("translation_enabled")]
    public bool? TranslationEnabled { get; set; }

    [JsonProperty("street")]
    public string? Street { get; set; }

    [JsonProperty("alias")]
    public string? Alias { get; set; }

    [JsonProperty("currency")]
    public string? Currency { get; set; }

    [JsonProperty("deletable_org_account")]
    public bool? DeletableOrgAccount { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("state")]
    public string? State { get; set; }

    [JsonProperty("fax")]
    public string? Fax { get; set; }

    [JsonProperty("employee_count")]
    public string? EmployeeCount { get; set; }

    [JsonProperty("zip")]
    public string? Zip { get; set; }

    [JsonProperty("created_time")]
    public DateTime? CreatedTime { get; set; }

    [JsonProperty("website")]
    public string? Website { get; set; }

    [JsonProperty("currency_symbol")]
    public string? CurrencySymbol { get; set; }

    [JsonProperty("mobile")]
    public string? Mobile { get; set; }

    [JsonProperty("currency_locale")]
    public string? CurrencyLocale { get; set; }

    [JsonProperty("primary_zuid")]
    public string? PrimaryZuid { get; set; }

    [JsonProperty("zia_portal_id")]
    public string? ZiaPortalId { get; set; }

    [JsonProperty("time_zone")]
    public string? TimeZone { get; set; }

    [JsonProperty("zgid")]
    public string? Zgid { get; set; }

    [JsonProperty("country_code")]
    public string? CountryCode { get; set; }

    [JsonProperty("license_details")]
    public LicenseDetails? LicenseDetails { get; set; }

    [JsonProperty("phone")]
    public string? Phone { get; set; }

    [JsonProperty("company_name")]
    public string? CompanyName { get; set; }

    [JsonProperty("privacy_settings")]
    public bool? PrivacySettings { get; set; }

    [JsonProperty("primary_email")]
    public string? PrimaryEmail { get; set; }

    [JsonProperty("hipaa_compliance_enabled")]
    public bool? HipaaComplianceEnabled { get; set; }

    [JsonProperty("iso_code")]
    public string? IsoCode { get; set; }
}

public class HierarchyPreferences
{
    [JsonProperty("type")]
    public string? Type { get; set; }
}

public class LicenseDetails
{
    [JsonProperty("paid_expiry")]
    public DateTime? PaidExpiry { get; set; }

    [JsonProperty("users_license_purchased")]
    public int? UsersLicensePurchased { get; set; }

    [JsonProperty("trial_type")]
    public string? TrialType { get; set; }

    [JsonProperty("trial_expiry")]
    public DateTime? TrialExpiry { get; set; }

    [JsonProperty("paid")]
    public bool? Paid { get; set; }

    [JsonProperty("paid_type")]
    public string? PaidType { get; set; }

    [JsonProperty("portal_users_license_purchased")]
    public int? PortalUsersLicensePurchased { get; set; }
}