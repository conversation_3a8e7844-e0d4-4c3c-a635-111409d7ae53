﻿using System.Reflection;
using Newtonsoft.Json.Serialization;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Commons.Helpers;

public static class ContactDetailHelpers
{
    private static readonly CamelCaseNamingStrategy _camelCaseNamingStrategy = new();

    public static Dictionary<string, object> GetPropertiesAsDictionary(ContactDetail contactDetail)
    {
        var contactProperties = new Dictionary<string, object>();
        var properties = contactDetail.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (!property.CanRead) continue;

            var value = property.GetValue(contactDetail);
            if (value != null)
            {
                var key = _camelCaseNamingStrategy.GetPropertyName(property.Name, false);
                contactProperties[key] = value;
            }
        }

        return contactProperties;
    }
}