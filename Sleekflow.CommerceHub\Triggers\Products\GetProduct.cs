using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class GetProduct
    : ITrigger<
        GetProduct.GetProductInput,
        GetProduct.GetProductOutput>
{
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public GetProduct(
        IProductService productService,
        IProductVariantService productVariantService)
    {
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class GetProductInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [StringLength(128, MinimumLength = 1)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [JsonConstructor]
        public GetProductInput(
            string sleekflowCompanyId,
            string storeId,
            string productId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductId = productId;
        }
    }

    public class GetProductOutput
    {
        [JsonProperty("product")]
        public ProductDto Product { get; set; }

        [JsonConstructor]
        public GetProductOutput(ProductDto product)
        {
            Product = product;
        }
    }

    public async Task<GetProductOutput> F(GetProductInput getProductInput)
    {
        var product = await _productService.GetProductAsync(
            getProductInput.ProductId,
            getProductInput.SleekflowCompanyId,
            getProductInput.StoreId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            getProductInput.SleekflowCompanyId,
            getProductInput.StoreId,
            product.Id);

        var productIdToProductVariantsDict = productVariants
            .GroupBy(pv => pv.ProductId)
            .ToDictionary(e => e.Key, e => e.ToList());

        return new GetProductOutput(
            new ProductDto(
                product,
                productIdToProductVariantsDict.GetValueOrDefault(product.Id, new List<ProductVariant>())));
    }
}