using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnContactLabelRelationshipsChangedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactLabelRelationshipsChanged; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [Required]
    [JsonProperty("added_to_labels")]
    public List<OnContactLabelRelationshipsChangedEventBodyContactLabel> AddedToLabels { get; set; }

    [Required]
    [JsonProperty("removed_from_labels")]
    public List<OnContactLabelRelationshipsChangedEventBodyContactLabel> RemovedFromLabels { get; set; }

    [Required]
    [JsonProperty("labels")]
    public List<OnContactLabelRelationshipsChangedEventBodyContactLabel> Labels { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [JsonConstructor]
    public OnContactLabelRelationshipsChangedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        List<OnContactLabelRelationshipsChangedEventBodyContactLabel> addedToLabels,
        List<OnContactLabelRelationshipsChangedEventBodyContactLabel> removedFromLabels,
        List<OnContactLabelRelationshipsChangedEventBodyContactLabel> labels,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        AddedToLabels = addedToLabels;
        RemovedFromLabels = removedFromLabels;
        Labels = labels;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
    }
}

public class OnContactLabelRelationshipsChangedEventBodyContactLabel
{
    [JsonProperty("label_value")]
    public string LabelValue { get; set; }

    [JsonProperty("label_color")]
    public string LabelColor { get; set; }

    [JsonProperty("label_type")]
    public string LabelType { get; set; }

    [JsonConstructor]
    public OnContactLabelRelationshipsChangedEventBodyContactLabel(
        string labelValue,
        string labelColor,
        string labelType)
    {
        LabelValue = labelValue;
        LabelColor = labelColor;
        LabelType = labelType;
    }
}