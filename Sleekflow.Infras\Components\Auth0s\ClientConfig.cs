using Newtonsoft.Json;

namespace Sleekflow.Infras.Components.Auth0s;

public class ClientConfig
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("initiate_login_uri")]
    public string? InitiateLoginUri { get; set; }

    [JsonProperty("allowed_logout_urls")]
    public List<string> AllowedLogoutUrls { get; set; }

    [JsonProperty("allowed_origins")]
    public List<string> AllowedOrigins { get; set; }

    [JsonProperty("app_type")]
    public string AppType { get; set; }

    [JsonProperty("web_origins")]
    public List<string> WebOrigins { get; set; }

    [JsonProperty("token_endpoint_auth_method")]
    public string? TokenEndpointAuthMethod { get; set; }

    [JsonProperty("grant_types")]
    public List<string>? GrantTypes { get; set; }

    [JsonProperty("callbacks")]
    public List<string> Callbacks { get; set; }

    [JsonProperty("native_client_options", NullValueHandling = NullValueHandling.Ignore)]
    public NativeClientOptions? NativeClientOptions { get; set; }

    /* TODO: delete after test completed !!
    [JsonProperty("team_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? TeamId { get; set; }

    [JsonProperty("app_bundle_identifier", NullValueHandling = NullValueHandling.Ignore)]
    public string? AppBundleIdentifier { get; set; }
    */

    [JsonConstructor]
    public ClientConfig(
        string name,
        List<string> allowedLogoutUrls,
        List<string> allowedOrigins,
        string appType,
        List<string> webOrigins,
        List<string> callbacks)
    {
        Name = name;
        AllowedLogoutUrls = allowedLogoutUrls;
        AllowedOrigins = allowedOrigins;
        AppType = appType;
        WebOrigins = webOrigins;
        Callbacks = callbacks;
    }
}

public class NativeClientOptions
{
    [JsonProperty("ios", NullValueHandling = NullValueHandling.Ignore)]
    public IosClientOptions? Ios { get; set; }

    [JsonProperty("android", NullValueHandling = NullValueHandling.Ignore)]
    public AndroidClientOptions? Android { get; set; }
}

public class IosClientOptions
{
    [JsonProperty("team_id")]
    public string? TeamId { get; set; }

    [JsonProperty("app_bundle_identifier")]
    public string? AppBundleIdentifier { get; set; }
}

public class AndroidClientOptions
{
    [JsonProperty("app_package_name")]
    public string? AppPackageName { get; set; }

    [JsonProperty("key_hashes")]
    public List<string?>? KeyHashes { get; set; }
}