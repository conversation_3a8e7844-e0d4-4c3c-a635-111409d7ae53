using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowStepLocator
{
    public bool IsRootStep(ProxyWorkflow workflow, Step step);

    public string GetStepId(ProxyWorkflow workflow, Step step);

    public Step GetStep(ProxyWorkflow workflow, string stepId);

    public Step GetStep(ProxyWorkflow workflow, Step step);

    public Step? GetNextStep(ProxyWorkflow workflow, string stepId, Stack<StackEntry> stackEntries);

    public Step? GetNextStep(ProxyWorkflow workflow, Step step, Stack<StackEntry> stackEntries);
}


public class WorkflowStepLocator : IWorkflowStepLocator, ISingletonService
{
    private readonly IWorkflowStepEntryProvider _workflowStepEntryProvider;

    public WorkflowStepLocator(IWorkflowStepEntryProvider workflowStepEntryProvider)
    {
        _workflowStepEntryProvider = workflowStepEntryProvider;
    }

    public bool IsRootStep(ProxyWorkflow workflow, Step step)
    {
        var stepEntries = GetStepEntries(workflow);

        return stepEntries.Single(s => s.Step.Id == step.Id).BranchId == "root";
    }

    public string GetStepId(ProxyWorkflow workflow, Step step)
    {
        return step.Id;
    }

    public Step GetStep(ProxyWorkflow workflow, string stepId)
    {
        var stepEntries = GetStepEntries(workflow);

        return stepEntries.Single(s => s.Step.Id == stepId).Step;
    }

    public Step GetStep(ProxyWorkflow workflow, Step step)
    {
        return step;
    }

    public Step? GetNextStep(ProxyWorkflow workflow, string stepId, Stack<StackEntry> stackEntries)
    {
        var stepEntries = GetStepEntries(workflow);
        var index = stepEntries.FindIndex(s => s.Step.Id == stepId);
        var stepEntry = stepEntries[index];

        var nextStepId = stepEntry.Step.NextStepId;
        if (nextStepId != null)
        {
            return stepEntries.Single(s => s.Step.Id == nextStepId).Step;
        }

        try
        {
            var nextIndex = stepEntries.FindIndex(
                index + 1,
                s =>
                    s.Depth == stepEntry.Depth
                    && s.BranchId == stepEntry.BranchId);
            var nextSequentialStepEntry = stepEntries[nextIndex];

            return nextSequentialStepEntry.Step;
        }
        catch (Exception)
        {
            // ignored
        }

        return null;
    }

    public Step? GetNextStep(ProxyWorkflow workflow, Step step, Stack<StackEntry> stackEntries)
    {
        return GetNextStep(workflow, step.Id, stackEntries);
    }

    private List<WorkflowStepEntry> GetStepEntries(ProxyWorkflow workflow)
    {
        return _workflowStepEntryProvider.GetStepEntries(workflow);
    }
}