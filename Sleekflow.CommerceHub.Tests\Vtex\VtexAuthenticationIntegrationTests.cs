﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Models.Vtex.ViewModels;
using Sleekflow.CommerceHub.Triggers.Vtex;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Tests.Vtex;

public class VtexAuthenticationIntegrationTests
{
    private static readonly string MockCompanyId = $"aaaabbbb-cccc-dddd-eeee-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[..12]}";

    private VtexCredential _vtexCredential = new VtexCredential(
        "https://sandboxsleekflo16.myvtex.com/",
        "vtexappkey-sandboxsleekflo16-JDTPBQ",
        "NYIGZILNLCJKHAUOIJRZIXFODNHZDYEXYUIILPEFQPNCWKJDUDKOYJDVZIXAOZTFDRDTLJATSZCOZUVHWZXSJEGEAHXNIYEXDNIHXJCTKFXYCQSCZRDSPWUGKTKFPRGB");

    [SetUp]
    public void Setup()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Vtex app key has a build-in expiration. Exclude from Github action.");
        }

        // set env variable for local testing
        Environment.SetEnvironmentVariable("COMMERCE_HUB_ENDPOINT", "https://webhook.site/bca335e9-34dd-410d-8045-630fb55195fa");
    }

    [TearDown]
    public async Task TestTearDown()
    {
        var vtexAuthenticationRepository = GetVtexAuthenticationRepository();

        var vtexAuthentications = await vtexAuthenticationRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var vtexAuthentication in vtexAuthentications)
        {
            await vtexAuthenticationRepository.DeleteAsync(
                vtexAuthentication.Id,
                MockCompanyId);
        }
    }

    [Test]
    public async Task VtexAuthenticationLifeCycleTest_WithCorrectInput_ShouldNotThrowErrors()
    {
        // Create Authentication
        var createVtexAuthenticationOutput = await CreateVtexAuthenticationAsync();
        var vtexAuthenticationId = createVtexAuthenticationOutput.Id;

        // Get Authentication
        var getVtexAuthenticationOutput = await GetVtexAuthenticationAsync(vtexAuthenticationId);
        Assert.That(getVtexAuthenticationOutput.Title, Is.EqualTo("sandboxsleekflo16"));
        Assert.That(getVtexAuthenticationOutput.IsActive, Is.True);

        // Get Authentications
        var getVtexAuthenticationsOutput = await GetVtexAuthenticationsAsync();
        Assert.That(getVtexAuthenticationsOutput.VtexAuthentications.Count, Is.EqualTo(1));

        // Update Title
        var updateVtexAuthenticationTitleOutput = await UpdateTitleAsync(
            vtexAuthenticationId,
            "new title");

        Assert.That(updateVtexAuthenticationTitleOutput.Title, Is.EqualTo("new title"));

        // Update Credential
        var newCredential = new VtexCredential(
            "https://sandboxsleekflo16.myvtex.com/",
            "vtexappkey-sandboxsleekflo16-GXTEEE",
            "WJDPLCKMILHTFYGTKDBVNQSKCZJORLHFDTJQALABPEUCTJQYWYRLITRNMPTYAAIWLYDQIATDKURNHAIXBLUAUOPCJDMQHCUADDORHIXFWGSCZZDJKUMJGZVWQNWJVGQM");

        var updateVtexAuthenticationCredentialOutput = await UpdateCredentialAsync(
            vtexAuthenticationId,
            newCredential);

        Assert.That(updateVtexAuthenticationCredentialOutput.Credential.AppKey, Is.EqualTo(newCredential.AppKey));

        // constrain by Vtex
        await Task.Delay(TimeSpan.FromSeconds(70));

        // Delete Authentication
        var deleteVtexAuthenticationOutput = await DeleteVtexAuthenticationAsync(vtexAuthenticationId);
        Assert.That(deleteVtexAuthenticationOutput.IsSuccess, Is.True);

        // Get Authentications
        getVtexAuthenticationsOutput = await GetVtexAuthenticationsAsync();
        Assert.That(getVtexAuthenticationsOutput.VtexAuthentications.Count, Is.EqualTo(0));
    }

    private async Task<VtexAuthenticationViewModel> CreateVtexAuthenticationAsync()
    {
        var createVtexAuthenticationInput = new CreateVtexAuthentication.CreateVtexAuthenticationInput(
            MockCompanyId,
            _vtexCredential);

        var scenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createVtexAuthenticationInput).ToUrl("/Vtex/CreateVtexAuthentication");
            });

        var createVtexAuthenticationOutput = await scenarioResult.ReadAsJsonAsync<Output<CreateVtexAuthentication.CreateVtexAuthenticationOutput>>();

        Assert.That(createVtexAuthenticationOutput, Is.Not.Null);
        Assert.That(createVtexAuthenticationOutput!.HttpStatusCode, Is.EqualTo(200));

        return createVtexAuthenticationOutput.Data.VtexAuthentication;
    }

    private async Task<VtexAuthenticationViewModel> GetVtexAuthenticationAsync(string id)
    {
        var scenarioInput = new GetVtexAuthentication.GetVtexAuthenticationInput(
            MockCompanyId,
            id);

        var scenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(scenarioInput).ToUrl("/Vtex/GetVtexAuthentication");
            });

        var scenarioOutput = await scenarioResult.ReadAsJsonAsync<Output<GetVtexAuthentication.GetVtexAuthenticationOutput>>();

        Assert.That(scenarioOutput, Is.Not.Null);
        Assert.That(scenarioOutput!.HttpStatusCode, Is.EqualTo(200));

        return scenarioOutput.Data.VtexAuthentication;
    }

    private async Task<GetVtexAuthentications.GetVtexAuthenticationsOutput> GetVtexAuthenticationsAsync()
    {
        var scenarioInput = new GetVtexAuthentications.GetVtexAuthenticationsInput(
            MockCompanyId);

        var scenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(scenarioInput).ToUrl("/Vtex/GetVtexAuthentications");
            });

        var scenarioOutput = await scenarioResult.ReadAsJsonAsync<Output<GetVtexAuthentications.GetVtexAuthenticationsOutput>>();

        Assert.That(scenarioOutput, Is.Not.Null);
        Assert.That(scenarioOutput!.HttpStatusCode, Is.EqualTo(200));

        return scenarioOutput.Data;
    }

    private async Task<VtexAuthenticationViewModel> UpdateTitleAsync(
        string id,
        string newTitle)
    {
        var updateVtexAuthenticationTitleInput = new UpdateVtexAuthenticationTitle.UpdateVtexAuthenticationTitleInput(
            MockCompanyId,
            id,
            newTitle);

        var scenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateVtexAuthenticationTitleInput).ToUrl("/Vtex/UpdateVtexAuthenticationTitle");
            });

        var scenarioOutput = await scenarioResult.ReadAsJsonAsync<Output<UpdateVtexAuthenticationTitle.UpdateVtexAuthenticationTitleOutput>>();

        Assert.That(scenarioOutput, Is.Not.Null);
        Assert.That(scenarioOutput!.HttpStatusCode, Is.EqualTo(200));

        return scenarioOutput.Data.VtexAuthentication;
    }

    private async Task<VtexAuthenticationViewModel> UpdateCredentialAsync(
        string id,
        VtexCredential newCredential)
    {
        var scenarioInput = new UpdateVtexAuthenticationCredential.UpdateVtexAuthenticationCredentialInput(
            MockCompanyId,
            id,
            newCredential);

        var scenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(scenarioInput).ToUrl("/Vtex/UpdateVtexAuthenticationCredential");
            });

        var scenarioOutput = await scenarioResult.ReadAsJsonAsync<Output<UpdateVtexAuthenticationCredential.UpdateVtexAuthenticationCredentialOutput>>();

        Assert.That(scenarioOutput, Is.Not.Null);
        Assert.That(scenarioOutput!.HttpStatusCode, Is.EqualTo(200));

        return scenarioOutput.Data.VtexAuthentication;
    }

    private async Task<DeleteVtexAuthentication.DeleteVtexAuthenticationOutput> DeleteVtexAuthenticationAsync(
        string id)
    {
        var scenarioInput = new DeleteVtexAuthentication.DeleteVtexAuthenticationInput(
            MockCompanyId,
            id);

        var scenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(scenarioInput).ToUrl("/Vtex/DeleteVtexAuthentication");
            });

        var scenarioOutput = await scenarioResult.ReadAsJsonAsync<Output<DeleteVtexAuthentication.DeleteVtexAuthenticationOutput>>();

        Assert.That(scenarioOutput, Is.Not.Null);
        Assert.That(scenarioOutput!.HttpStatusCode, Is.EqualTo(200));

        return scenarioOutput.Data;
    }

    private VtexAuthenticationRepository GetVtexAuthenticationRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICommerceHubDbResolver>(
            new CommerceHubDbResolver(new MyCommerceHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var crmHubConfigRepository = new VtexAuthenticationRepository(
            NullLogger<VtexAuthenticationRepository>.Instance,
            serviceProvider);

        return crmHubConfigRepository;
    }

    private class MyCommerceHubDbConfig : ICommerceHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "commercehubdb";
    }
}