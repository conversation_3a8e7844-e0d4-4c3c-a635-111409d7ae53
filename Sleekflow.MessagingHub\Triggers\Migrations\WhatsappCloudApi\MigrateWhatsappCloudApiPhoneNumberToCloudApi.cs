using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class MigrateWhatsappCloudApiPhoneNumberToCloudApi
    : ITrigger<
        MigrateWhatsappCloudApiPhoneNumberToCloudApi.MigrateWhatsappCloudApiPhoneNumberToCloudApiInput,
        MigrateWhatsappCloudApiPhoneNumberToCloudApi.MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput>
{
    private readonly IMigrationService _migrationService;

    public MigrateWhatsappCloudApiPhoneNumberToCloudApi(IMigrationService migrationService)
    {
        _migrationService = migrationService;
    }

    public class MigrateWhatsappCloudApiPhoneNumberToCloudApiInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_phone_number_id")]
        public string FacebookPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("pin")]
        public string Pin { get; set; }

        [Required]
        [JsonProperty("user_access_token")]
        public string UserAccessToken { get; set; }

        [JsonProperty("back_up_data")]
        public string? BackUpData { get; set; }

        [JsonProperty("back_up_password")]
        public string? BackUpPassword { get; set; }

        [JsonConstructor]
        public MigrateWhatsappCloudApiPhoneNumberToCloudApiInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string facebookPhoneNumberId,
            string pin,
            string? backUpData,
            string? backUpPassword,
            string userAccessToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            FacebookPhoneNumberId = facebookPhoneNumberId;
            Pin = pin;
            BackUpData = backUpData;
            BackUpPassword = backUpPassword;
            UserAccessToken = userAccessToken;
        }
    }

    public class MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput> F(
        MigrateWhatsappCloudApiPhoneNumberToCloudApiInput migrateWhatsappCloudApiPhoneNumberToCloudApiInput)
    {
        return new MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput(
            await _migrationService.MigrateWhatsappCloudApiPhoneNumberToCloudApiAsync(
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.SleekflowCompanyId,
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.FacebookWabaId,
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.FacebookPhoneNumberId,
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.Pin,
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.UserAccessToken,
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.BackUpData,
                migrateWhatsappCloudApiPhoneNumberToCloudApiInput.BackUpPassword));
    }
}