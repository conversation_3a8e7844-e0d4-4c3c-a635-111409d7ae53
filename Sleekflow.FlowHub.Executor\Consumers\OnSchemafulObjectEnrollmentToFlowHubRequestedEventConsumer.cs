﻿using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnSchemafulObjectEnrollmentToFlowHubRequestedEventConsumerDefinition : ConsumerDefinition<OnSchemafulObjectEnrollmentToFlowHubRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnSchemafulObjectEnrollmentToFlowHubRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnSchemafulObjectEnrollmentToFlowHubRequestedEventConsumer : IConsumer<OnSchemafulObjectEnrollmentToFlowHubRequestedEvent>
{
    private readonly IServiceBusManager _serviceBusManager;

    public OnSchemafulObjectEnrollmentToFlowHubRequestedEventConsumer(
        IServiceBusManager bus)
    {
        _serviceBusManager = bus;
    }

    public async Task Consume(ConsumeContext<OnSchemafulObjectEnrollmentToFlowHubRequestedEvent> context)
    {
        var onSchemafulObjectEnrollmentToFlowHubRequestedEvent = context.Message;

        var identityObjectId = onSchemafulObjectEnrollmentToFlowHubRequestedEvent.SleekflowUserProfileId;

        await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
            new OnSchemafulObjectEnrolledEventBody(
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.CreatedAt,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.SchemafulObjectId,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.SchemaId,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.PrimaryPropertyValue,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.SleekflowUserProfileId,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.PropertyValues,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowId,
                onSchemafulObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowVersionedId),
            identityObjectId,
            "Contact",
            onSchemafulObjectEnrollmentToFlowHubRequestedEvent.SleekflowCompanyId));
    }
}