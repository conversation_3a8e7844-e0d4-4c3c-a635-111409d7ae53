using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.TenantHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class TenantHubModules
{
    public static void BuildTenantHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON>d<PERSON><PERSON><PERSON><ITenantHubDbConfig>(new Mock<ITenantHubDbConfig>().Object);
        b.<PERSON><PERSON><PERSON><PERSON><PERSON><ITenantHubDbResolver>(new Mock<ITenantHubDbResolver>().Object);

#else
        var tenantHubDbConfig = new TenantHubDbConfig();

        b.<PERSON><PERSON><PERSON><PERSON><ITenantHubDbConfig>(tenantHubDbConfig);
        b.<PERSON>d<PERSON><PERSON><PERSON><ITenantHubDbResolver, TenantHubDbResolver>();
#endif
    }
}