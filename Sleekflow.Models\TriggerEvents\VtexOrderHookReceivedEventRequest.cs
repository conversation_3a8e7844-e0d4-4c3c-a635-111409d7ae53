﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class VtexOrderHookReceivedEventRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("vtex_authentication_id")]
    public string VtexAuthenticationId { get; set; }

    [JsonProperty("status_code")]
    public string StatusCode { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("order")]
    public VtexOrderOverview Order { get; set; }

    [JsonConstructor]
    public VtexOrderHookReceivedEventRequest(
        string sleekflowCompanyId,
        string vtexAuthenticationId,
        string statusCode,
        string orderId,
        VtexOrderOverview order)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        VtexAuthenticationId = vtexAuthenticationId;
        StatusCode = statusCode;
        OrderId = orderId;
        Order = order;
    }
}

public record VtexOrderOverview
{
    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("status_code")]
    public string StatusCode { get; set; }

    [JsonProperty("created_at")]
    public DateTime CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("is_completed")]
    public bool IsCompleted { get; set; }

    [JsonProperty("is_checked_in")]
    public bool IsCheckedIn { get; set; }

    [JsonProperty("authorized_date")]
    public DateTime? AuthorizedDate { get; set; }

    [JsonProperty("invoiced_date")]
    public DateTime? InvoicedDate { get; set; }

    [JsonProperty("cancel_reason")]
    public string CancelReason { get; set; }

    [JsonProperty("total_value")]
    public double TotalValue { get; set; }

    [JsonProperty("total_items")]
    public double TotalItems { get; set; }

    [JsonProperty("total_discount")]
    public double TotalDiscount { get; set; }

    [JsonProperty("total_tax")]
    public double TotalTax { get; set; }

    [JsonProperty("total_shipping")]
    public double TotalShipping { get; set; }

    [JsonProperty("order_items")]
    public List<VtexOrderItemOverview> OrderItems { get; set; }

    [JsonProperty("normalized_order_itmes")]
    public string NormalizedOrderItems { get; set; }

    [JsonProperty("store_preferences_data")]
    public VtexStorePreferencesOverview StorePreferencesData { get; set; }

    [JsonProperty("shipping_data")]
    public VtexShippingOverview ShippingData { get; set; }

    [JsonProperty("client_profile_data")]
    public VtexClientProfileOverview ClientProfileData { get; set; }

    [JsonConstructor]
    public VtexOrderOverview(
        string orderId,
        string status,
        string statusCode,
        DateTime createdAt,
        DateTime? updatedAt,
        bool isCompleted,
        bool isCheckedIn,
        DateTime? authorizedDate,
        DateTime? invoicedDate,
        string cancelReason,
        double totalValue,
        double totalItems,
        double totalDiscount,
        double totalTax,
        double totalShipping,
        List<VtexOrderItemOverview> orderItems,
        string normalizedOrderItems,
        VtexStorePreferencesOverview storePreferencesData,
        VtexShippingOverview shippingData,
        VtexClientProfileOverview clientProfileData)
    {
        OrderId = orderId;
        Status = status;
        StatusCode = statusCode;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        IsCompleted = isCompleted;
        IsCheckedIn = isCheckedIn;
        AuthorizedDate = authorizedDate;
        InvoicedDate = invoicedDate;
        CancelReason = cancelReason;
        TotalValue = totalValue;
        TotalItems = totalItems;
        TotalDiscount = totalDiscount;
        TotalTax = totalTax;
        TotalShipping = totalShipping;
        OrderItems = orderItems;
        NormalizedOrderItems = normalizedOrderItems;
        StorePreferencesData = storePreferencesData;
        ShippingData = shippingData;
        ClientProfileData = clientProfileData;
    }
}

public record VtexOrderItemOverview
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("product_id")]
    public string ProductId { get; set; }

    [JsonProperty("ref_id")]
    public string RefId { get; set; }

    [JsonProperty("sku_ame")]
    public string SkuName { get; set; }

    [JsonProperty("image_url")]
    public string ImageUrl { get; set; }

    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("price")]
    public double Price { get; set; }

    [JsonConstructor]
    public VtexOrderItemOverview(
        string id,
        string productId,
        string refId,
        string skuName,
        string imageUrl,
        int quantity,
        double price)
    {
        Id = id;
        ProductId = productId;
        RefId = refId;
        SkuName = skuName;
        ImageUrl = imageUrl;
        Quantity = quantity;
        Price = price;
    }
}

public record VtexStorePreferencesOverview
{
    [JsonProperty("country_code")]
    public string CountryCode { get; set; }

    [JsonProperty("currency_code")]
    public string CurrencyCode { get; set; }

    [JsonProperty("currency_symbol")]
    public string CurrencySymbol { get; set; }

    [JsonProperty("time_zone")]
    public string TimeZone { get; set; }

    [JsonConstructor]
    public VtexStorePreferencesOverview(string countryCode, string currencyCode, string currencySymbol, string timeZone)
    {
        CountryCode = countryCode;
        CurrencyCode = currencyCode;
        CurrencySymbol = currencySymbol;
        TimeZone = timeZone;
    }
}

public record VtexShippingOverview
{
    [JsonProperty("receiver_name")]
    public string ReceiverName { get; set; }

    [JsonProperty("postal_code")]
    public string PostalCode { get; set; }

    [JsonProperty("city")]
    public string City { get; set; }

    [JsonProperty("state")]
    public string State { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("street")]
    public string Street { get; set; }

    [JsonConstructor]
    public VtexShippingOverview(
        string receiverName,
        string postalCode,
        string city,
        string state,
        string country,
        string street)
    {
        ReceiverName = receiverName;
        PostalCode = postalCode;
        City = city;
        State = state;
        Country = country;
        Street = street;
    }
}

public record VtexClientProfileOverview
{
    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("phone")]
    public string Phone { get; set; }

    [JsonConstructor]
    public VtexClientProfileOverview(string email, string firstName, string lastName, string phone)
    {
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        Phone = phone;
    }
}