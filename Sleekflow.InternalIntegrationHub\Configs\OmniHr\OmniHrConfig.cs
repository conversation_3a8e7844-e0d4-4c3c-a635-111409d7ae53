using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.InternalIntegrationHub.Configs.OmniHr;

public interface IOmniHrConfig
{
    string Username { get; }

    string Password { get; }

    string BaseUrl { get; }
}

public class OmniHrConfig : IConfig, IOmniHrConfig
{
    /// <summary>
    /// Username for OmniHr.
    /// </summary>
    public string Username { get; }

    /// <summary>
    /// Password for OmniHr.
    /// </summary>
    public string Password { get; }

    public string BaseUrl { get; }

    public OmniHrConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Username =
            Environment.GetEnvironmentVariable("OMNIHR_USERNAME", target)
            ?? throw new SfMissingEnvironmentVariableException("OMNIHR_USERNAME");

        Password =
            Environment.GetEnvironmentVariable("OMNIHR_PASSWORD", target)
            ?? throw new SfMissingEnvironmentVariableException("OMNIHR_PASSWORD");

        BaseUrl =
            Environment.GetEnvironmentVariable("OMNIHR_BASE_URL", target)
            ?? throw new SfMissingEnvironmentVariableException("OMNIHR_BASE_URL");
    }
}