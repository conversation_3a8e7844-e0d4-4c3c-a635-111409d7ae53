using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class PreviewObjectsV2Output
{
    [JsonProperty("objects")]
    [Required]
    public List<Dictionary<string, object?>> Objects { get; set; }

    [JsonProperty("next_records_url")]
    public string? NextRecordsUrl { get; set; }

    [JsonConstructor]
    public PreviewObjectsV2Output(
        List<Dictionary<string, object?>> objects,
        string? nextRecordsUrl)
    {
        Objects = objects;
        NextRecordsUrl = nextRecordsUrl;
    }
}