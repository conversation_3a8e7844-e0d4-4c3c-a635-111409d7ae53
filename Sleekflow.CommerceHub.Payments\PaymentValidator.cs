using Sleekflow.CommerceHub.Payments.Stripe;
using Sleekflow.DependencyInjection;
using LineItem = Sleekflow.CommerceHub.Payments.Abstractions.LineItem;
using Shipping = Sleekflow.CommerceHub.Payments.Abstractions.Shipping;

namespace Sleekflow.CommerceHub.Payments;

public interface IPaymentValidator
{
    Task AssertValidPaymentAsync(
        string customerId,
        string sleekflowCompanyId,
        Shipping shipping,
        List<LineItem> lineItems,
        string platformCountry);
}

public class PaymentValidator : IPaymentValidator, ISingletonService
{
    private readonly IStripeCustomerRepository _stripeCustomerRepository;

    public PaymentValidator(
        IStripeCustomerRepository stripeCustomerRepository)
    {
        _stripeCustomerRepository = stripeCustomerRepository;
    }

    public async Task AssertValidPaymentAsync(
        string customerId,
        string sleekflowCompanyId,
        Shipping shipping,
        List<LineItem> lineItems,
        string platformCountry)
    {
        var customer = await _stripeCustomerRepository.GetCustomerOrDefaultAsync(customerId, platformCountry);
        if (customer == null)
        {
            throw new Exception();
        }

        // TODO
    }
}