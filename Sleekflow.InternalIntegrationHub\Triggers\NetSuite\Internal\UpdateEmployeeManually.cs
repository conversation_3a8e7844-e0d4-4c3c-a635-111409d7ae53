using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.Validations;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class UpdateEmployeeManually
    : ITrigger<UpdateEmployeeManually.UpdateEmployeeManuallyInput,
        UpdateEmployeeManually.UpdateEmployeeManuallyOutput>
{
    private readonly INetSuiteEmployeeService _netSuiteEmployeeService;

    public UpdateEmployeeManually(INetSuiteEmployeeService netSuiteEmployeeService)
    {
        _netSuiteEmployeeService = netSuiteEmployeeService;
    }

    public class UpdateEmployeeManuallyInput
    {
        [ValidateObject]
        [JsonProperty("update_supervisors")]
        public Dictionary<string, UpdateEmployeeRequest>? UpdateSupervisors { get; set; }

        [ValidateObject]
        [JsonProperty("update_employee_info")]
        public Dictionary<string, CreateEmployeeRequest>? UpdateEmployeeInfo { get; set; }

        [JsonConstructor]
        public UpdateEmployeeManuallyInput(
            Dictionary<string, UpdateEmployeeRequest>? updateSupervisors,
            Dictionary<string, CreateEmployeeRequest>? updateEmployeeInfo)
        {
            UpdateSupervisors = updateSupervisors;
            UpdateEmployeeInfo = updateEmployeeInfo;
        }
    }

    public class UpdateEmployeeManuallyOutput
    {
        [JsonProperty("updated_supervisors_count")]
        public int UpdatedSupervisorsCount { get; set; }

        [JsonProperty("updated_employee_info_count")]
        public int UpdatedEmployeeInfoCount { get; set; }

        [JsonProperty("failed_employee_ids")]
        public List<string> FailedEmployeeIds { get; set; }

        [JsonConstructor]
        public UpdateEmployeeManuallyOutput(
            int updatedSupervisorsCount,
            int updatedEmployeeInfoCount,
            List<string> failedEmployeeIds)
        {
            UpdatedSupervisorsCount = updatedSupervisorsCount;
            UpdatedEmployeeInfoCount = updatedEmployeeInfoCount;
            FailedEmployeeIds = failedEmployeeIds;
        }
    }

    public async Task<UpdateEmployeeManuallyOutput> F(UpdateEmployeeManuallyInput input)
    {
        var updatedSupervisorsCount = 0;
        var updatedEmployeeInfoCount = 0;
        var failedEmployeeIds = new List<string>();
        if (input.UpdateSupervisors != null)
        {
            foreach (var supervisors in input.UpdateSupervisors)
            {
                var isSuccess =
                    await _netSuiteEmployeeService.UpdateEmployeeSupervisorAsync(supervisors.Value, supervisors.Key);
                if (isSuccess)
                {
                    updatedSupervisorsCount++;
                }
                else
                {
                    failedEmployeeIds.Add(supervisors.Key);
                }
            }
        }

        if (input.UpdateEmployeeInfo != null)
        {
            foreach (var employeeInfo in input.UpdateEmployeeInfo)
            {
                var isSuccess = await _netSuiteEmployeeService.UpdateEmployeeAsync(
                    employeeInfo.Value,
                    employeeInfo.Key);
                if (isSuccess)
                {
                    updatedEmployeeInfoCount++;
                }
                else
                {
                    failedEmployeeIds.Add(employeeInfo.Key);
                }
            }
        }

        return new UpdateEmployeeManuallyOutput(updatedSupervisorsCount, updatedEmployeeInfoCount, failedEmployeeIds);
    }
}