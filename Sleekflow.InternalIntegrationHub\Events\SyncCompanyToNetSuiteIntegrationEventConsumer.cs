using MassTransit;
using Sleekflow.InternalIntegrationHub.Integrations.SyncCompanyToNetSuiteIntegration;
using Sleekflow.InternalIntegrationHub.Models.Events;
using Sleekflow.Locks;

namespace Sleekflow.InternalIntegrationHub.Events;

public class SyncCompanyToNetSuiteIntegrationEventConsumerDefinition
    : ConsumerDefinition<
        SyncCompanyToNetSuiteIntegrationEventConsumer
    >
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SyncCompanyToNetSuiteIntegrationEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SyncCompanyToNetSuiteIntegrationEventConsumer
    : IConsumer<SyncCompanyToNetSuiteIntegrationEvent>
{
    private readonly ISyncCompanyToNetSuiteIntegration _integrations;
    private readonly ILockService _lockService;


    public SyncCompanyToNetSuiteIntegrationEventConsumer(
        ISyncCompanyToNetSuiteIntegration integrations,
        ILockService lockService)
    {
        _integrations = integrations;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<SyncCompanyToNetSuiteIntegrationEvent> context)
    {
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            [
                "SyncCompanyToNetSuiteIntegrationEvent"
            ],
            TimeSpan.FromSeconds(
                180),
            cancellationToken);
        if (@lock is null)
        {
            return;
        }

        try
        {
            await _integrations.Run(cancellationToken);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}