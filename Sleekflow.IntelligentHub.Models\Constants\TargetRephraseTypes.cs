using System.Collections.Immutable;

namespace Sleekflow.IntelligentHub.Models.Constants;

public static class TargetRephraseTypes
{
    public const string Lengthen = "Lengthen";
    public const string Shorten = "Shorten";
    public const string GrammarCheck = "GrammarCheck";
    public const string Simplify = "Simplify";
    public const string Enumerate = "Enumerate";
    public const string Bulletize = "Bulletize";

    public static readonly ImmutableList<string> AllRephraseTargetTypes =
        new List<string>
            {
                Lengthen,
                Shorten,
                GrammarCheck,
                Simplify,
                Enumerate,
                Bulletize
            }
            .ToImmutableList();
}