﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.GoogleSheets.Authentications;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProviderV2 : ITrigger
{
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;

    public InitProviderV2(IGoogleSheetsAuthenticationService googleSheetsAuthenticationService)
    {
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
    }

    public class InitProviderV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderV2Input(
            string sleekflowCompanyId,
            string successUrl,
            string failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderV2Output
    {
        [JsonProperty("google_sheets_authentication_url")]
        public string GoogleSheetsAuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderV2Output(string googleSheetsAuthenticationUrl)
        {
            GoogleSheetsAuthenticationUrl = googleSheetsAuthenticationUrl;
        }
    }

    public async Task<InitProviderV2Output> F(
        InitProviderV2Input initProviderInput)
    {
        return new InitProviderV2Output(_googleSheetsAuthenticationService.GetAuthenticationUrl(
            initProviderInput.SleekflowCompanyId,
            initProviderInput.SuccessUrl,
            initProviderInput.FailureUrl,
            initProviderInput.AdditionalDetails));
    }
}