﻿namespace Sleekflow.Integrator.GoogleSheets.Helpers;

public static class WorksheetDifferenceHelper
{
    public class WorksheetDifference
    {
        public List<Dictionary<string, object?>> AddedRows { get; }

        public List<Dictionary<string, object?>> RemovedRows { get; }

        public List<(Dictionary<string, object?> OldRow, Dictionary<string, object?> NewRow)> UpdatedRows { get; }

        public WorksheetDifference()
        {
            AddedRows = new List<Dictionary<string, object?>>();
            RemovedRows = new List<Dictionary<string, object?>>();
            UpdatedRows = new List<(Dictionary<string, object?> OldRow, Dictionary<string, object?> NewRow)>();
        }
    }

    public static WorksheetDifference CalculateDifference(
        List<Dictionary<string, object?>> oldWorksheet,
        List<Dictionary<string, object?>> newWorksheet)
    {
        var difference = new WorksheetDifference();

        var oldWorksheetDict =
            oldWorksheet.ToDictionary(row => row["Id"]!.ToString()!);
        var newWorksheetDict =
            newWorksheet.ToDictionary(row => row["Id"]!.ToString()!);

        // Find added rows
        foreach (var newRow in newWorksheet)
        {
            var id = newRow["Id"]?.ToString();
            if (id != null && !oldWorksheetDict.ContainsKey(id))
            {
                difference.AddedRows.Add(newRow);
            }
        }

        // Find removed rows
        foreach (var oldRow in oldWorksheet)
        {
            var id = oldRow["Id"]?.ToString();
            if (id != null && !newWorksheetDict.ContainsKey(id))
            {
                difference.RemovedRows.Add(oldRow);
            }
        }

        // Find modified rows
        foreach (var newRow in newWorksheet)
        {
            var id = newRow["Id"]?.ToString();
            if (id != null && oldWorksheetDict.TryGetValue(id, out var oldRow) && !DictionariesEqual(oldRow, newRow))
            {
                difference.UpdatedRows.Add((oldRow, newRow));
            }
        }

        return difference;
    }

    private static bool DictionariesEqual(Dictionary<string, object?> oldDict, Dictionary<string, object?> newDict)
    {
        if (oldDict.Count != newDict.Count)
        {
            return false;
        }

        foreach (var oldDictKyp in oldDict)
        {
            if (oldDictKyp.Key == "Id")
            {
                continue;
            }

            if (!newDict.TryGetValue(oldDictKyp.Key, out var newDictValue))
            {
                return false;
            }

            if (DateTimeOffset.TryParse(oldDictKyp.Value?.ToString(), out var oldDictDto) &&
                DateTimeOffset.TryParse(newDictValue?.ToString(), out var newDictDto))
            {
                if (Math.Abs((oldDictDto - newDictDto).TotalSeconds) >= 1)
                {
                    return false;
                }
            }
            else if (!Equals(oldDictKyp.Value, newDictValue))
            {
                return false;
            }
        }

        return true;
    }
}