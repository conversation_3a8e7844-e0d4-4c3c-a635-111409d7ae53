﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Integrations;

namespace Sleekflow.FlowHub.Integrator.Integrations;

public interface IZapierTriggerIntegrationService : IIntegrationService<ZapierTriggerIntegration>
{
}

public class ZapierTriggerIntegrationService
    : IntegrationServiceBase<ZapierTriggerIntegration>, IZapierTriggerIntegrationService, IScopedService
{
    public ZapierTriggerIntegrationService(IZapierTriggerIntegrationRepository zapierTriggerIntegrationRepository)
        : base(zapierTriggerIntegrationRepository)
    {
    }
}