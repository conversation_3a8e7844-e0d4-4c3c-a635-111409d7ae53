name: 'master-coverage-snapshot'
on:
  workflow_dispatch:
  #Runs Once a Week
  schedule:
    - cron: '0 0 * * MON'

env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages
  REGISTRY: ghcr.io

jobs:
  set-env:
    name: Set Environment Variables
    runs-on: ubuntu-latest
    outputs:
      image_prefix: ${{ steps.set-prefix.outputs.prefix }}
      build_time: ${{ steps.set-buildtime.outputs.time }}
      branch_name: ${{ steps.set-branch.outputs.name }}
    steps:
      - id: set-prefix
        run: echo "prefix=$(echo ${{ github.repository_owner }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT
      - id: set-buildtime
        run: echo "time=$(date -u '+%Y%m%d%H%M%S')" >> $GITHUB_OUTPUT
      - id: set-branch
        run: |
          # Get the branch name and replace '/' with '-'
          branch="${{ github.event.pull_request.head.ref }}"
          if [ -z "$branch" ]; then
            echo "name=latest" >> $GITHUB_OUTPUT
          else
            echo "name=${branch//\//-}" >> $GITHUB_OUTPUT
          fi

  build-common:
    needs: set-env
    name: Build Common Image
    runs-on:
      group: "Default Middle Runners"
    permissions:
      contents: read
      packages: write
    outputs:
      image_tags: ${{ steps.meta.outputs.tags }}
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow
          tags: |
            type=sha,prefix=
            type=ref,event=branch
            type=raw,value=latest,enable={{is_default_branch}}

      # Build and cache common base image
      - name: Build common base image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.branch_name }}
          cache-from: type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:buildcache-${{ needs.set-env.outputs.branch_name }}
          cache-to: type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:buildcache-${{ needs.set-env.outputs.branch_name }},mode=max

  build-services:
    name: Build Service Images
    needs: [ build-common, set-env ]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        # see .github/workflows/service-mappings.json
        service: [
          AuditHub,
          CommerceHub,
          CrmHub,
          EmailHub,
          Dynamics365Integrator,
          HubspotIntegrator,
          SalesforceIntegrator,
          ApiGateway,
          GlobalApiGateway,
          InternalApiGateway,
          MessagingHub,
          WebhookHub,
          WebhookBridge,
          ShareHub,
          PublicApiGateway,
          FlowHub,
          FlowHubExecutor,
          FlowHubIntegrator,
          TenantHub,
          IntelligentHub,
          SfmcJourneyBuilderCustomActivity,
          UserEventHub,
          UserEventAnalyticsHub,
          TicketingHub,
          SupportHub,
          Scheduler,
          InternalIntegrationHub,
          OpenPolicyAgent,
          OpenPolicyAdministrationLayer,
          GoogleSheetsIntegrator,
          IntelligentHubLightRag ]
      # Allow all matrix jobs to run even if one fails
      fail-fast: false
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Check for changes
        id: changes
        run: |
          echo "::group::Debug - Git Info"
          echo "Current commit: ${{ github.sha }}"
          echo "Base commit: ${{ github.event.pull_request.base.sha }}"
          echo "::endgroup::"

          echo "::group::Debug - Changed Files"
          # Get the list of changed files
          git fetch origin ${{ github.event.pull_request.base.ref }}
          changed_files=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }})
          echo "$changed_files" > changed_files.txt
          echo "Changed files:"
          cat changed_files.txt
          echo "::endgroup::"

          echo "::group::Debug - Common Files Check"
          # Load common projects configuration
          common_config=$(jq -r '.' .github/workflows/common-projects.json)

          # Create regex pattern from configuration
          common_files_pattern=$(echo "$common_config" | jq -r '.commonFiles | join("|")' | sed 's/\./\\./g')
          common_projects_pattern=$(echo "$common_config" | jq -r '.commonProjects | map(. + "/.*") | join("|")' | sed 's/\./\\./g')

          # Combine patterns and check for changes
          if grep -q -E "^($common_files_pattern|$common_projects_pattern)" changed_files.txt; then
            echo "Common files changed"
            echo "common_changed=true" >> $GITHUB_OUTPUT
          else
            echo "No common files changed"
            echo "common_changed=false" >> $GITHUB_OUTPUT
          fi

          # Debug output
          echo "Common files pattern: $common_files_pattern"
          echo "Common projects pattern: $common_projects_pattern"
          echo "::endgroup::"

          echo "::group::Debug - Service Files Check"
          # Get service info from mappings file
          service_info=$(jq -r --arg svc "${{ matrix.service }}" '.[$svc]' .github/workflows/service-mappings.json)
          path=$(echo "$service_info" | jq -r '.path')
          echo "Checking service: ${{ matrix.service }}"
          echo "Service path: $path"

          # Get dependencies from service info
          dependencies=$(echo "$service_info" | jq -r '.dependencies // [] | join("|")')

          # Create pattern for service path and its dependencies
          if [ -n "$dependencies" ]; then
            check_pattern="^($path|$dependencies)(/|$)"
          else
            check_pattern="^$path(/|$)"
          fi

          echo "Checking pattern: $check_pattern"

          # Check if service-specific files or its dependencies have changed
          if grep -q -E "$check_pattern" changed_files.txt; then
            echo "Service files or dependencies changed"
            echo "service_changed=true" >> $GITHUB_OUTPUT
          else
            echo "No service files or dependencies changed"
            echo "service_changed=false" >> $GITHUB_OUTPUT
          fi
          echo "::endgroup::"

      - name: Set build info
        id: build
        run: |
          # Get service info from mappings file
          service_info=$(jq -r --arg svc "${{ matrix.service }}" '.[$svc]' .github/workflows/service-mappings.json)
          path=$(echo "$service_info" | jq -r '.path')
          image=$(echo "$service_info" | jq -r '.image')
          context=$(echo "$service_info" | jq -r '.context // "."')

          # Extract build args and format them for docker build
          build_args=$(echo "$service_info" | jq -r '.build_args // {} | to_entries | map("\(.key)=\(.value)") | join("\n")')

          echo "path=./$path/" >> $GITHUB_OUTPUT
          echo "dockerfile=./$path/Dockerfile" >> $GITHUB_OUTPUT
          echo "image_name=-$image" >> $GITHUB_OUTPUT
          echo "context=$context" >> $GITHUB_OUTPUT
          echo "build_args<<EOF" >> $GITHUB_OUTPUT
          echo "$build_args" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Process ${{ matrix.service }}
        id: process
        run: |
          echo "::group::Debug - Process Service"
          need_build=false

          # Check if changes require a build
          if [[ "${{ steps.changes.outputs.common_changed }}" == "true" || "${{ steps.changes.outputs.service_changed }}" == "true" ]]; then
            echo "Changes detected, need to build"
            need_build=true
          else
            echo "No changes detected, checking if image exists"
            # Check if image exists in registry without pulling
            if ! docker buildx imagetools inspect ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ needs.set-env.outputs.branch_name }} >/dev/null 2>&1; then
              echo "Branch-specific image not found, need to build"
              need_build=true
            else
              echo "Branch-specific image exists in registry, will re-tag"
            fi
          fi

          if [[ "$need_build" == "true" ]]; then
            echo "action=build" >> $GITHUB_OUTPUT
          else
            echo "action=retag" >> $GITHUB_OUTPUT
          fi
          echo "::endgroup::"

      # Build new image if needed
      - name: Build ${{ matrix.service }}
        if: steps.process.outputs.action == 'build'
        uses: docker/build-push-action@v5
        with:
          context: ${{ steps.build.outputs.context }}
          file: ${{ steps.build.outputs.dockerfile }}
          tags: |
            ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ needs.set-env.outputs.branch_name }}
            ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ needs.set-env.outputs.build_time }}
            ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ github.sha }}
          cache-from: |
            type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:buildcache-${{ needs.set-env.outputs.branch_name }}
            type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:buildcache-${{ needs.set-env.outputs.branch_name }}
          cache-to: type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:buildcache-${{ needs.set-env.outputs.branch_name }},mode=max
          push: true
          build-args: |
            COMMON_IMAGE=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.branch_name }}
            ${{ steps.build.outputs.build_args }}

      # Create new tags without pulling the image
      - name: Create new tags
        if: steps.process.outputs.action == 'retag'
        run: |
          echo "::group::Debug - Create new tags"
          echo "Creating new tags using buildx imagetools"

          # Create build time tag
          docker buildx imagetools create \
            ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ needs.set-env.outputs.branch_name }} \
            --tag ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ needs.set-env.outputs.build_time }}

          # Create SHA tag
          docker buildx imagetools create \
            ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ needs.set-env.outputs.branch_name }} \
            --tag ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ github.sha }}

  preview:
    name: Preview
    needs: [ build-services, set-env ]
    runs-on:
      group: "Default Middle Runners"
    # Serialize Pulumi deployments
    concurrency:
      group: pulumi-deploy
      cancel-in-progress: false
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Build required projects
        run: |
          # Build worker projects needed by Sleekflow.Infras by searching for files containing "bin/Release/net8.0/publish"
          dotnet publish Sleekflow.FlowHub.Workers -c Release
          dotnet publish Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer -c Release
          dotnet publish Sleekflow.MessagingHub.Workers -c Release
          dotnet publish Sleekflow.CrmHub.Workers -c Release
          dotnet publish Sleekflow.InternalIntegrationHub.Workers -c Release
          dotnet publish Sleekflow.Infras.DbScalar -c Release
          dotnet publish Sleekflow.CommerceHub.Workers -c Release
          dotnet publish Sleekflow.EmailHub.Workers -c Release
          dotnet publish Sleekflow.IntelligentHub.Workers -c Release

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Restore Pulumi packages
        working-directory: ./Sleekflow.Infras/
        run: dotnet restore

      - name: Set Pulumi stack name
        id: set-stack
        run: |
          if [[ "${{ github.event.pull_request.base.ref }}" == "master" ]]; then
            echo "stack=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event.pull_request.base.ref }}" == "staging" ]]; then
            echo "stack=staging" >> $GITHUB_OUTPUT
          else
            echo "stack=dev" >> $GITHUB_OUTPUT
          fi

      - name: Pull Images Concurrently
        run: |
          # Create an array of pull commands
          pull_commands=()

          # Add common image
          pull_commands+=("docker pull ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.build_time }}")

          # Get service mappings and extract all services
          mappings=$(cat .github/workflows/service-mappings.json)

          # Generate pull commands for each service directly from mappings
          while IFS= read -r service; do
            # Extract image name from mappings
            image=$(echo "$mappings" | jq -r --arg svc "$service" '.[$svc].image')
            if [ "$image" != "null" ]; then
              pull_commands+=("docker pull ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-$image:${{ needs.set-env.outputs.build_time }}")
            fi
          done < <(echo "$mappings" | jq -r 'keys[]')

          # Execute all pull commands in parallel and wait for completion
          for cmd in "${pull_commands[@]}"; do
            echo "Starting: $cmd"
            eval "$cmd" &
          done

          # Wait for all background processes to complete
          wait

          echo "All images pulled successfully"

      - name: Preview Everything
        uses: pulumi/actions@v4
        with:
          command: preview
          stack-name: ${{ steps.set-stack.outputs.stack }}
          work-dir: ./Sleekflow.Infras/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.ARM_TENANT_ID }}
          BUILD_TIME: ${{ needs.set-env.outputs.build_time }}

  test:
    name: Run Tests
    needs: [ ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Running Tests
        run: |
          dotnet test Sleekflow.AuditHub.Tests/Sleekflow.AuditHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.Auth0.BulkImporter.Tests/Sleekflow.Auth0.BulkImporter.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.CommerceHub.Tests/Sleekflow.CommerceHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.CrmHub.Tests/Sleekflow.CrmHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.IntelligentHub.Tests/Sleekflow.IntelligentHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.KrakenD.Tests/Sleekflow.KrakenD.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.MessagingHub.Tests/Sleekflow.MessagingHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.PublicApiGateway.Tests/Sleekflow.PublicApiGateway.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.ShareHub.Tests/Sleekflow.ShareHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.TenantHub.Tests/Sleekflow.TenantHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.TicketingHub.Tests/Sleekflow.TicketingHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
          dotnet test Sleekflow.UserEventHub.Tests/Sleekflow.UserEventHub.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
        continue-on-error: true

      - name: Generate Report
        run: |
          dotnet tool install -g dotnet-reportgenerator-globaltool
          reportgenerator -reports:coverage/**/coverage.cobertura.xml -targetdir:coverage -reporttypes:Cobertura

      - name: Generate Coverage Report
        uses: clearlyip/code-coverage-report-action@v5
        id: code_coverage_report_action
        with:
          filename: "./coverage/Cobertura.xml"
          only_list_changed_files: true

      - name: Add Coverage PR Comment
        uses: marocchino/sticky-pull-request-comment@v2
        if: github.event_name == 'pull_request'
        with:
          recreate: true
          path: code-coverage-results.md