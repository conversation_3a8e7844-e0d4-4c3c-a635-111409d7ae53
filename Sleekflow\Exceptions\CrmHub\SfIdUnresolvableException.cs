﻿using Newtonsoft.Json;
using Sleekflow.JsonConfigs;

namespace Sleekflow.Exceptions.CrmHub;

public class SfIdUnresolvableException : ErrorCodeException
{
    public string SerializedDict { get; }

    public SfIdUnresolvableException(Dictionary<string, object?> dict)
        : base(
            ErrorCodeConstant.SfIdUnresolvableException,
            $"Unable to resolve the id of the dict = [{JsonConvert.SerializeObject(dict, JsonConfig.DefaultJsonSerializerSettings)}]",
            new Dictionary<string, object?>()
            {
                {
                    "serialized_dict", JsonConvert.SerializeObject(dict, JsonConfig.DefaultJsonSerializerSettings)
                }
            }
        )
    {
        SerializedDict = JsonConvert.SerializeObject(dict, JsonConfig.DefaultJsonSerializerSettings);
    }
}