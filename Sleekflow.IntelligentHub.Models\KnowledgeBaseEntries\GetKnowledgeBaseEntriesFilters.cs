using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

public class GetKnowledgeBaseEntriesFilters
{
    [Required]
    [JsonProperty("source_id")]
    public string SourceId { get; set; }

    [Required]
    [JsonProperty("source_type")]
    public string SourceType { get; set; }

    [JsonConstructor]
    public GetKnowledgeBaseEntriesFilters(string sourceId, string sourceType)
    {
        SourceId = sourceId;
        SourceType = sourceType;
    }
}