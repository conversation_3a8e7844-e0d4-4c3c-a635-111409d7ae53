﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.Salesforce.Consumers;

public class UpdateSalesforceObjectV2RequestConsumerDefinition : ConsumerDefinition<UpdateSalesforceObjectV2RequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<UpdateSalesforceObjectV2RequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class UpdateSalesforceObjectV2RequestConsumer : IConsumer<UpdateSalesforceObjectV2Request>
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly ILogger<UpdateSalesforceObjectV2RequestConsumer> _logger;

    public UpdateSalesforceObjectV2RequestConsumer(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService,
        IBus bus,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        ILogger<UpdateSalesforceObjectV2RequestConsumer> logger)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<UpdateSalesforceObjectV2Request> context)
    {
        var request = context.Message;

        var consumeId = Guid.NewGuid().ToString();

        try
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceUpdateObjectRequestReceived,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                    {
                        "object_id", request.ObjectId
                    }
                });

            var connection =
                await _salesforceConnectionService.GetByIdAsync(
                    request.ConnectionId,
                    request.SleekflowCompanyId);

            var authentication =
                await _salesforceAuthenticationService.GetAsync(
                    connection.AuthenticationId,
                    request.SleekflowCompanyId);
            if (authentication == null)
            {
                throw new SfUnauthorizedException();
            }

            var obj = await _salesforceObjectService.GetObjectAsync(
                authentication,
                request.ObjectId,
                request.EntityTypeName);
            if (obj == null)
            {
                throw new SfNotFoundObjectException(request.ObjectId);
            }

            var getFieldsOutput =
                await _salesforceObjectService.GetFieldsAsync(authentication, request.EntityTypeName);
            var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();
            var updatableBooleanFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "boolean")
                .Select(f => f.Name)
                .ToList();
            var updatableDateFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "date")
                .Select(f => f.Name)
                .ToList();
            var updatableDateTimeFieldNames = getFieldsOutput.UpdatableFields
                .Where(f => f.Type == "datetime")
                .Select(f => f.Name)
                .ToList();

            var dict = request.ObjectProperties
                .Where(e => updatableFieldNames.Contains(e.Key))
                .ToDictionary(
                    e => e.Key,
                    e =>
                    {
                        if (updatableBooleanFieldNames.Contains(e.Key))
                        {
                            if (e.Value is string value && !string.IsNullOrEmpty(value))
                            {
                                return value.ToLower();
                            }

                            return null;
                        }

                        if (updatableDateFieldNames.Contains(e.Key))
                        {
                            if (e.Value is DateTimeOffset dateValue)
                            {
                                return dateValue.ToString("yyyy-MM-dd");
                            }

                            if (e.Value is string dateString && DateTimeOffset.TryParse(
                                    dateString,
                                    out var parsedDateValue))
                            {
                                return parsedDateValue.ToString("yyyy-MM-dd");
                            }

                            return null;
                        }

                        if (updatableDateTimeFieldNames.Contains(e.Key))
                        {
                            if (e.Value is DateTimeOffset dateTimeValue)
                            {
                                return dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                            }

                            if (e.Value is string dateTimeString && DateTimeOffset.TryParse(
                                    dateTimeString,
                                    out var parsedDateTimeValue))
                            {
                                return parsedDateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                            }

                            return null;
                        }

                        return e.Value;
                    });

            await _salesforceObjectService.UpdateAsync(
                authentication,
                dict,
                request.ObjectId,
                request.EntityTypeName);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceUpdateObjectRequestHandled,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                    {
                        "object_id", request.ObjectId
                    }
                });

            await _bus.Publish(
                new OnSalesforceCompleteStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null));
        }
        catch (Exception ex)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.SalesforceUpdateObjectRequestFailed,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                    {
                        "object_id", request.ObjectId
                    }
                });

            _logger.LogError(
                ex,
                "UpdateSalesforceObjectV2Request failed,"
                + " ConsumeId: {ConsumeId},"
                + " SleekflowCompanyId: {SleekflowCompanyId},"
                + " ConnectionId: {ConnectionId},"
                + " EntityTypeName: {EntityTypeName},"
                + " ObjectId: {ObjectId},"
                + " ObjectProperties: {ObjectProperties},"
                + " AggregateStepId: {AggregateStepId},"
                + " ProxyStateId: {ProxyStateId},"
                + " StackEntries: {StackEntries},",
                consumeId,
                request.SleekflowCompanyId,
                request.ConnectionId,
                request.EntityTypeName,
                request.ObjectId,
                JsonConvert.SerializeObject(request.ObjectProperties),
                request.AggregateStepId,
                request.ProxyStateId,
                request.StackEntries);

            await _bus.Publish(
                new OnSalesforceFailStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null,
                    ex));
        }
    }
}