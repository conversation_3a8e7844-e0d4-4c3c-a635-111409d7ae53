using Sleekflow.Caches;
using Sleekflow.FlowHub.Counters;
using StackExchange.Redis;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class SlidingWindowCounterIntegrationTests
{
    private const string RequestKey = "sliding-window-counter";
    private const int WindowSizeSeconds = 5;

    [Test]
    public async Task CountAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        var cacheConfig = new MyCacheConfig();
        var connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(cacheConfig.RedisConnStr);

        var slidingWindowCounter = new SlidingWindowCounter(
            connectionMultiplexer,
            cacheConfig);

        var tasks = Enumerable.Range(1, 5)
            .Select(
                _ => slidingWindowCounter.CountAsync(
                    RequestKey,
                    WindowSizeSeconds));

        await Task.WhenAll(tasks);

        // Act
        var count = await slidingWindowCounter.CountAsync(
            RequestKey,
            WindowSizeSeconds);

        // Assert
        Assert.That(count, Is.EqualTo(6));

        // Clean up
        connectionMultiplexer.GetDatabase()
            .KeyDelete(new RedisKey($"{cacheConfig.CachePrefix}-{RequestKey}"));
    }

    [Test]
    public async Task CountAsync_ShouldReturnOneAfterWindowSlidesOff()
    {
        // Arrange
        var cacheConfig = new MyCacheConfig();
        var connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(cacheConfig.RedisConnStr);

        var slidingWindowCounter = new SlidingWindowCounter(
            connectionMultiplexer,
            cacheConfig);

        var tasks = Enumerable.Range(1, 5)
            .Select(
                _ => slidingWindowCounter.CountAsync(
                    RequestKey,
                    WindowSizeSeconds));

        await Task.WhenAll(tasks);

        await Task.Delay(TimeSpan.FromSeconds(6));

        // Act
        var count = await slidingWindowCounter.CountAsync(
            RequestKey,
            WindowSizeSeconds);

        // Assert
        Assert.That(count, Is.EqualTo(1));

        // Clean up
        connectionMultiplexer.GetDatabase()
            .KeyDelete(new RedisKey($"{cacheConfig.CachePrefix}-{RequestKey}"));
    }

    private class MyCacheConfig : ICacheConfig
    {
        public string CachePrefix => "INTEGRATION-TEST-SLIDING-WINDOW-COUNTER";

        public string RedisConnStr =>
            "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False";
    }
}