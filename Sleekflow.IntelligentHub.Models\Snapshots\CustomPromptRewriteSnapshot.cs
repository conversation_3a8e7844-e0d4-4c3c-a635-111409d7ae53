﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class CustomPromptRewriteSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("input_message")]
    public string InputMessage { get; set; }

    [JsonProperty("prompt")]
    public string Prompt { get; set; }

    [JsonProperty( "output_message")]
    public string OutputMessage { get; set; }

    [JsonConstructor]
    public CustomPromptRewriteSnapshot(string inputMessage, string prompt, string outputMessage)
    {
        InputMessage = inputMessage;
        Prompt = prompt;
        OutputMessage = outputMessage;
    }
}