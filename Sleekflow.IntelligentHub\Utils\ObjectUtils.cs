using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Utils;

public static class ObjectUtils
{
    public static T? ConvertTo<T>(object? obj)
    {
        try
        {
            var json = JsonConvert.SerializeObject(obj);
            return JsonConvert.DeserializeObject<T>(json);
        }
        catch (Exception e)
        {
            throw new Exception("Invalid type");
        }
    }
}