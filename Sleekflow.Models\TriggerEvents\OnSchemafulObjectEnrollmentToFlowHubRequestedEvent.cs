﻿#pragma warning disable SF1004
#pragma warning disable SF1003
#pragma warning disable SF1005

using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class OnSchemafulObjectEnrollmentToFlowHubRequestedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string SchemafulObjectId { get; set; }

    public string SchemaId { get; set; }

    public string PrimaryPropertyValue { get; set; }

    public string SleekflowUserProfileId { get; set; }

    public Dictionary<string, object?> PropertyValues { get; set; }

    public DateTimeOffset CreatedAt { get; set; }

    public string FlowHubWorkflowId { get; set; }

    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnSchemafulObjectEnrollmentToFlowHubRequestedEvent(
        string sleekflowCompanyId,
        string schemafulObjectId,
        string schemaId,
        string primaryPropertyValue,
        string sleekflowUserProfileId,
        Dictionary<string, object?> propertyValues,
        DateTimeOffset createdAt,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemafulObjectId = schemafulObjectId;
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        SleekflowUserProfileId = sleekflowUserProfileId;
        PropertyValues = propertyValues;
        CreatedAt = createdAt;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}