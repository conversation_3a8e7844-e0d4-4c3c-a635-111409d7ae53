using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Configs;

public interface IBaseCognitiveSearchConfig
{
    string SearchClientUri { get; }

    string AzureKeyCredential { get; }

    string GetIndexName();
}

public abstract class BaseCognitiveSearchConfig : IConfig, IBaseCognitiveSearchConfig
{
    private EnvironmentVariableTarget Target { get; set; }

    public string SearchClientUri { get; }

    public string AzureKeyCredential { get; }

    protected BaseCognitiveSearchConfig(EnvironmentVariableTarget target)
    {
        Target = target;
        AzureKeyCredential = Environment.GetEnvironmentVariable("COGNITIVE_SEARCH_CREDENTIAL_KEY", Target) ??
                             throw new SfMissingEnvironmentVariableException("COGNITIVE_SEARCH_CREDENTIAL_KEY");
        SearchClientUri = Environment.GetEnvironmentVariable("COGNITIVE_SEARCH_SERVICE", Target) ??
                          throw new SfMissingEnvironmentVariableException("COGNITIVE_SEARCH_SERVICE");
    }

    public virtual string GetIndexName()
    {
        throw new NotImplementedException();
    }
}