﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class ZohoObjectCreatedEventRequestConsumerDefinition : ConsumerDefinition<ZohoObjectCreatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<ZohoObjectCreatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class ZohoObjectCreatedEventRequestConsumer : IConsumer<ZohoObjectCreatedEventRequest>
{
    private readonly IBus _bus;

    public ZohoObjectCreatedEventRequestConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<ZohoObjectCreatedEventRequest> context)
    {
        var zohoObjectCreatedEventRequest = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnZohoObjectCreatedEventBody(
                zohoObjectCreatedEventRequest.CreatedAt,
                zohoObjectCreatedEventRequest.ConnectionId,
                zohoObjectCreatedEventRequest.ObjectType,
                zohoObjectCreatedEventRequest.ObjectDict),
            zohoObjectCreatedEventRequest.ObjectId,
            zohoObjectCreatedEventRequest.ObjectType,
            zohoObjectCreatedEventRequest.SleekflowCompanyId));
    }
}