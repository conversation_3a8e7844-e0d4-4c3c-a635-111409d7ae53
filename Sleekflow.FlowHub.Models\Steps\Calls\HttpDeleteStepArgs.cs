using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class HttpDeleteStepArgs : TypedCallStepArgs
{
    public const string CallName = "http.delete";

    [Required]
    [JsonProperty("url__expr")]
    public string UrlExpr { get; set; }

    [JsonProperty("headers__key_expr_dict")]
    public Dictionary<string, string?>? HeadersKeyExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.ExternalIntegration;

    [JsonConstructor]
    public HttpDeleteStepArgs(
        string urlExpr,
        Dictionary<string, string?>? headersKeyExprDict)
    {
        UrlExpr = urlExpr;
        HeadersKeyExprDict = headersKeyExprDict;
    }
}