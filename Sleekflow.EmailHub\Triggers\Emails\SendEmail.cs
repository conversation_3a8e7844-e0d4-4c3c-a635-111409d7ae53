using System.ComponentModel.DataAnnotations;
using MimeKit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Emails;

[TriggerGroup(ControllerNames.Emails)]
public class SendEmail : ITrigger
{
    private readonly IEmailProviderSelector _emailProviderSelector;

    public SendEmail(IEmailProviderSelector emailProviderSelector)
    {
        _emailProviderSelector = emailProviderSelector;
    }

    public class SendEmailInput
    {
        [JsonConstructor]
        public SendEmailInput(
            string sleekflowCompanyId,
            string providerName,
            EmailContact sender,
            List<EmailContact> to,
            List<EmailContact> cc,
            List<EmailContact> bcc,
            List<EmailContact> replyTo,
            string subject,
            string htmlBody,
            string textBody,
            List<EmailAttachment> emailAttachments)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            Sender = sender;
            To = to;
            Cc = cc;
            Bcc = bcc;
            Subject = subject;
            ReplyTo = replyTo;
            HtmlBody = htmlBody;
            TextBody = textBody;
            EmailAttachments = emailAttachments;
        }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("sender")]
        [Required]
        public EmailContact Sender { get; set; }

        [JsonProperty("email_metadata", TypeNameHandling = TypeNameHandling.Objects)]
        public Dictionary<string, string>? EmailMetadata { get; set; }

        [JsonProperty("to")]
        [Required]
        public List<EmailContact> To { get; set; }

        [JsonProperty("cc")]
        [Required]
        public List<EmailContact> Cc { get; set; }

        [JsonProperty("bcc")]
        [Required]
        public List<EmailContact> Bcc { get; set; }

        [JsonProperty("subject")]
        [Required]
        public string Subject { get; set; }

        [JsonProperty("reply_to")]
        [Required]
        public List<EmailContact> ReplyTo { get; set; }

        [JsonProperty("html_body")]
        public string? HtmlBody { get; set; }

        [JsonProperty("text_body")]
        public string? TextBody { get; set; }

        [JsonProperty("email_attachments")]
        [Required]
        public List<EmailAttachment> EmailAttachments { get; set; }
    }

    public class SendEmailOutput
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("sender", TypeNameHandling = TypeNameHandling.Objects)]
        public EmailContact Sender { get; set; }

        [JsonConstructor]
        public SendEmailOutput(
            string sleekflowCompanyId,
            string providerName,
            EmailContact sender)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            Sender = sender;
        }
    }

    public async Task<SendEmailOutput> F(SendEmailInput sendEmailInput)
    {
        var emailProvider = _emailProviderSelector.GetEmailProvider(sendEmailInput.ProviderName);
        await emailProvider.SendEmailAsync(
            sendEmailInput.SleekflowCompanyId,
            sendEmailInput.Sender,
            sendEmailInput.Subject,
            sendEmailInput.To,
            sendEmailInput.Cc,
            sendEmailInput.Bcc,
            sendEmailInput.ReplyTo,
            sendEmailInput.HtmlBody,
            sendEmailInput.TextBody,
            sendEmailInput.EmailAttachments,
            sendEmailInput.EmailMetadata);

        return new SendEmailOutput(
            sendEmailInput.SleekflowCompanyId,
            sendEmailInput.ProviderName,
            sendEmailInput.Sender);
    }
}