using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IReplyInstagramMediaCommentStepExecutor : IStepExecutor
{
}

public class ReplyInstagramMediaCommentStepExecutor
    : GeneralStepExecutor<CallStep<ReplyInstagramMediaCommentStepArgs>>,
        IReplyInstagramMediaCommentStepExecutor,
    IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly HttpClient _httpClient;
    private readonly IStateAggregator _stateAggregator;
    private readonly ICoreCommander _coreCommander;

    public ReplyInstagramMediaCommentStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IHttpClientFactory httpClientFactory,
        IStateAggregator stateAggregator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _stateAggregator = stateAggregator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var instagramPageId =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.InstagramPageIdExpr)
                          ?? callStep.Args.InstagramPageIdExpr);

            var replyToInstagramCommentId =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(
                              state,
                              callStep.Args.InstagramCommentIdExpr)
                          ?? callStep.Args.InstagramCommentIdExpr);

            // IG is limited to reply the comment in string text format
            var commentBodyText =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.CommentBodyExpr)
                 ?? callStep.Args.CommentBodyExpr);

            var getInstagramPageAccessToken = JsonConvert.DeserializeObject<GetInstagramPageAccessTokenOutput>(
                await _coreCommander.ExecuteAsync(
                    state.Origin,
                    "GetInstagramPageAccessToken",
                    await GetArgs(callStep, state)));

            if (getInstagramPageAccessToken == null || string.IsNullOrEmpty(getInstagramPageAccessToken.InstagramPageAccessToken))
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.InternalError,
                    $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id} because of unable to get page {instagramPageId} access token");
            }

            var instagramPageCommentClient = new InstagramPageCommentClient(getInstagramPageAccessToken.InstagramPageAccessToken, _httpClient);

            var replyToCommentResponse = await instagramPageCommentClient.PublishInstagramPageCommentAsync(
                replyToInstagramCommentId,
                commentBodyText);

            var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                JsonConvert.SerializeObject(replyToCommentResponse));

            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.MetaGraphApiClientError,
                    $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                    g);
            }

            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<GetInstagramPageAccessTokenInput> GetArgs(
        CallStep<ReplyInstagramMediaCommentStepArgs> callStep,
        ProxyState state)
    {
        var facebookPageId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.InstagramPageIdExpr)
                      ?? callStep.Args.InstagramPageIdExpr);

        return new GetInstagramPageAccessTokenInput(state.Identity.SleekflowCompanyId, facebookPageId);
    }
}