﻿using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.Exceptions;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.CommerceHub.Tests.Vtex;

public class VtexOrderCommanderTests
{
    private IVtexOrderCommander _vtexOrderCommander;

    private VtexCredential _credential;

    [SetUp]
    public void Setup()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Vtex app key has a build-in expiration. Exclude from Github action.");
        }

        _credential = new VtexCredential(
            "https://sandboxsleekflo16.myvtex.com/",
            "vtexappkey-sandboxsleekflo16-JDTPBQ",
            "NYIGZILNLCJKHAUOIJRZIXFODNHZDYEXYUIILPEFQPNCWKJDUDKOYJDVZIXAOZTFDRDTLJATSZCOZUVHWZXSJEGEAHXNIYEXDNIHXJCTKFXYCQSCZRDSPWUGKTKFPRGB");

        _vtexOrderCommander = new VtexOrderCommander(
            new HttpClient(),
            new NullLogger<VtexOrderCommander>());
    }

    [Test]
    public async Task GetOrderAsync_WhenOrderIdIsValid_ShouldReturnOrder()
    {
        // arrange
        var orderId = "1530090500021-01";

        // act
        var order = await _vtexOrderCommander.GetOrderAsync(_credential, orderId);

        // assert
        Assert.IsNotNull(order);
        Assert.That(order.OrderId, Is.EqualTo(orderId));
    }

    [Test]
    public Task GetOrderAsync_WhenOrderIdIsInvalid_ShouldThrowException()
    {
        // arrange
        var orderId = "invalid-order-id";

        // act & assert
        Assert.ThrowsAsync<SfNotFoundObjectException>(
            async () => await _vtexOrderCommander.GetOrderAsync(_credential, orderId));

        return Task.CompletedTask;
    }

    [Test]
    public Task GetOrdersAsync_WhenConditionIsValid_ShouldReturnOrders()
    {
        // arrange
        var condition = new VtexGetOrdersSearchCondition(
            new DateTimeOffset(2025, 5, 1, 0, 0, 0, TimeSpan.Zero),
            new DateTimeOffset(2025, 5, 31, 0, 0, 0, TimeSpan.Zero),
            null);

        VtexGetOrdersResponseDto vtexGetOrdersResponseDto = null;

        // act & assert
        Assert.DoesNotThrowAsync(
            async () => vtexGetOrdersResponseDto = await _vtexOrderCommander.GetOrdersAsync(_credential, condition, 2, 10));

        Assert.That(vtexGetOrdersResponseDto, Is.Not.Null);
        Assert.That(vtexGetOrdersResponseDto!.Orders.Count, Is.GreaterThan(0));
        Assert.That(vtexGetOrdersResponseDto!.Paging.CurrentPage, Is.EqualTo(2));

        return Task.CompletedTask;
    }
}