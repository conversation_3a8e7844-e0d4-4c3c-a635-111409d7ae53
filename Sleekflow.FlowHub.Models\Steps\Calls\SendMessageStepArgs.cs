using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SendMessageStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.send-message";

    [Required]
    [JsonProperty("channel__expr")]
    public string ChannelExpr { get; set; }

    [Required]
    [JsonProperty("from_to")]
    public FromTo FromTo { get; set; }

    [Required]
    [JsonProperty("message_type__expr")]
    public string MessageTypeExpr { get; set; }

    [Required]
    [JsonProperty("message_body__expr")]
    public string MessageBodyExpr { get; set; }

    /// <summary>
    /// Specifies the delivery type for the message
    /// </summary>
    [JsonProperty("delivery_type__expr")]
    public string? DeliveryTypeExpr { get; set; }

    /// <summary>
    /// Impersonate which staff to send message
    /// </summary>
    [JsonProperty("staff_id__expr")]
    public string? StaffIdExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Messaging;

    [JsonConstructor]
    public SendMessageStepArgs(
        string channelExpr,
        FromTo fromTo,
        string messageTypeExpr,
        string messageBodyExpr,
        string? deliveryTypeExpr,
        string? staffIdExpr)
    {
        ChannelExpr = channelExpr;
        FromTo = fromTo;
        MessageTypeExpr = messageTypeExpr;
        MessageBodyExpr = messageBodyExpr;
        DeliveryTypeExpr = deliveryTypeExpr;
        StaffIdExpr = staffIdExpr;
    }
}

public class FromTo
{
    [JsonProperty("from_phone_number__expr")]
    public string? FromPhoneNumberExpr { get; set; }

    [JsonProperty("to_phone_number__expr")]
    public string? ToPhoneNumberExpr { get; set; }

    [JsonProperty("to_contact_id__expr")]
    public string? ToContactIdExpr { get; set; }

    [JsonProperty("from_channel_id__expr")]
    public string? FromChannelIdExpr { get; set; }

    [JsonProperty("from_facebook_page_id__expr")]
    public string? FromFacebookPageIdExpr { get; set; }

    [JsonProperty("to_facebook_id__expr")]
    public string? ToFacebookIdExpr { get; set; }

    [JsonProperty("from_facebook_post_id__expr")]
    public string? FromFacebookPostIdExpr { get; set; }

    [JsonProperty("to_facebook_comment_id__expr")]
    public string? ToFacebookCommentIdExpr { get; set; }

    [JsonProperty("from_instagram_page_id__expr")]
    public string? FromInstagramPageIdExpr { get; set; }

    [JsonProperty("to_instagram_id__expr")]
    public string? ToInstagramIdExpr { get; set; }

    [JsonProperty("from_instagram_media_id__expr")]
    public string? FromInstagramMediaIdExpr { get; set; }

    [JsonProperty("to_instagram_comment_id__expr")]
    public string? ToInstagramCommentIdExpr { get; set; }

    [JsonConstructor]
    public FromTo(
        string? fromPhoneNumberExpr,
        string? toPhoneNumberExpr,
        string? toContactIdExpr,
        string? fromChannelIdExpr,
        string? fromFacebookPageIdExpr,
        string? toFacebookIdExpr,
        string? fromFacebookPostIdExpr,
        string? toFacebookCommentIdExpr,
        string? fromInstagramPageIdExpr,
        string? toInstagramIdExpr,
        string? fromInstagramMediaIdExpr,
        string? toInstagramCommentIdExpr)
    {
        FromPhoneNumberExpr = fromPhoneNumberExpr;
        ToPhoneNumberExpr = toPhoneNumberExpr;
        ToContactIdExpr = toContactIdExpr;
        FromChannelIdExpr = fromChannelIdExpr;
        FromFacebookPageIdExpr = fromFacebookPageIdExpr;
        ToFacebookIdExpr = toFacebookIdExpr;
        FromFacebookPostIdExpr = fromFacebookPostIdExpr;
        ToFacebookCommentIdExpr = toFacebookCommentIdExpr;
        FromInstagramPageIdExpr = fromInstagramPageIdExpr;
        ToInstagramIdExpr = toInstagramIdExpr;
        FromInstagramMediaIdExpr = fromInstagramMediaIdExpr;
        ToInstagramCommentIdExpr = toInstagramCommentIdExpr;
    }
}