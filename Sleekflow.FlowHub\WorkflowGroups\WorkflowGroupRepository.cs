﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.WorkflowGroups;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.WorkflowGroups;

public interface IWorkflowGroupRepository : IRepository<WorkflowGroup>
{
}

public class WorkflowGroupRepository : BaseRepository<WorkflowGroup>, IWorkflowGroupRepository, IScopedService
{
    public WorkflowGroupRepository(
        ILogger<WorkflowGroupRepository> logger,
        IServiceProvider serviceProvider)
        : base(
            logger,
            serviceProvider)
    {
    }
}