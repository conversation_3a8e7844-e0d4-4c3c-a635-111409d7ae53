using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class ValidateObjectAttributeAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1104";
    public const string Category = "Design";

    private static readonly LocalizableString Title =
        "Object properties in Input/Output classes should have [ValidateObject] attribute";

    private static readonly LocalizableString MessageFormat =
        "The property '{0}' should have the [ValidateObject] attribute";

    private static readonly LocalizableString Description =
        "Object properties in Input/Output classes should have [ValidateObject] attribute";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSyntaxNodeAction(AnalyzeNode, SyntaxKind.PropertyDeclaration);
    }

    private static void AnalyzeNode(SyntaxNodeAnalysisContext context)
    {
        var propertyDeclaration = (PropertyDeclarationSyntax) context.Node;
        var propertySymbol = context.SemanticModel.GetDeclaredSymbol(propertyDeclaration);

        if (propertySymbol == null
            || propertySymbol.ContainingType == null)
        {
            return;
        }

        if (!propertySymbol.ContainingType.Name.EndsWith("Input"))
        {
            return;
        }

        if (TypeSymbolUtils.IsCollectionType(propertySymbol.Type))
        {
            return;
        }

        if (!TypeSymbolUtils.IsNonPrimitiveType(propertySymbol.Type))
        {
            return;
        }

        var validateObjectAttribute = propertySymbol
            .GetAttributes()
            .FirstOrDefault(a => a.AttributeClass.Name == "ValidateObjectAttribute");

        if (validateObjectAttribute != null)
        {
            return;
        }

        var diagnostic = Diagnostic.Create(Rule, propertyDeclaration.GetLocation(), propertySymbol.Name);
        context.ReportDiagnostic(diagnostic);
    }
}