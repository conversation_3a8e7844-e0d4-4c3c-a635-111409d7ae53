{"AuditHub": {"image": "ah", "path": "Sleekflow.AuditHub", "dependencies": ["Sleekflow.AuditHub.Models"]}, "CommerceHub": {"image": "commh", "path": "Sleekflow.CommerceHub", "dependencies": ["Sleekflow.CommerceHub.Models", "Sleekflow.CommerceHub.Payments"]}, "CrmHub": {"image": "crm-hub", "path": "Sleekflow.CrmHub", "dependencies": ["Sleekflow.CrmHub.Models"]}, "EmailHub": {"image": "eh", "path": "Sleekflow.EmailHub", "dependencies": ["Sleekflow.EmailHub.Models"]}, "Dynamics365Integrator": {"image": "d365-in", "path": "Sleekflow.Integrator.Dynamics365", "dependencies": ["Sleekflow.CrmHub.Models"]}, "HubspotIntegrator": {"image": "hs-in", "path": "Sleekflow.Integrator.Hubspot", "dependencies": ["Sleekflow.CrmHub.Models"]}, "SalesforceIntegrator": {"image": "sf-in", "path": "Sleekflow.Integrator.Salesforce", "dependencies": ["Sleekflow.CrmHub.Models"]}, "ApiGateway": {"image": "apigw", "path": "Sleekflow.KrakenD", "build_args": {"FILE_NAME": "Sleekflow.KrakenD/krakend.json"}}, "GlobalApiGateway": {"image": "gapigw", "path": "Sleekflow.KrakenD", "build_args": {"FILE_NAME": "Sleekflow.KrakenD/krakend.global.json"}}, "InternalApiGateway": {"image": "igw", "path": "Sleekflow.KrakenD", "build_args": {"FILE_NAME": "Sleekflow.KrakenD/krakend.internal.json"}}, "MessagingHub": {"image": "mh", "path": "Sleekflow.MessagingHub", "dependencies": ["Sleekflow.MessagingHub.Models"]}, "WebhookHub": {"image": "wh", "path": "Sleekflow.WebhookHub", "dependencies": ["Sleekflow.WebhookHub.Models"]}, "WebhookBridge": {"image": "whb", "path": "Sleekflow.WebhookBridge"}, "ShareHub": {"image": "sh", "path": "Sleekflow.ShareHub", "dependencies": ["Sleekflow.ShareHub.Models"]}, "PublicApiGateway": {"image": "pagw", "path": "Sleekflow.PublicApiGateway", "dependencies": ["Sleekflow.PublicApiGateway.Models"]}, "FlowHub": {"image": "fh", "path": "Sleekflow.FlowHub", "dependencies": ["Sleekflow.FlowHub.Models", "Sleekflow.FlowHub.Commons"]}, "FlowHubExecutor": {"image": "fhe", "path": "Sleekflow.FlowHub.Executor", "dependencies": ["Sleekflow.FlowHub", "Sleekflow.FlowHub.Models", "Sleekflow.FlowHub.Commons"]}, "FlowHubIntegrator": {"image": "fh-in", "path": "Sleekflow.FlowHub.Integrator"}, "TenantHub": {"image": "th", "path": "Sleekflow.TenantHub", "dependencies": ["Sleekflow.TenantHub.Models"]}, "IntelligentHub": {"image": "ih", "path": "Sleekflow.IntelligentHub", "dependencies": ["Sleekflow.FlowHub.Models", "Sleekflow.IntelligentHub.Models"]}, "SfmcJourneyBuilderCustomActivity": {"image": "sfmc-jb-ca", "path": "Sleekflow.SfmcJourneyBuilderCustomActivity"}, "UserEventHub": {"image": "ueh", "path": "Sleekflow.UserEventHub", "dependencies": ["Sleekflow.UserEventHub.Models"]}, "UserEventAnalyticsHub": {"image": "ueah", "path": "Sleekflow.UserEventAnalyticsHub", "dependencies": ["Sleekflow.UserEventHub.Models"]}, "TicketingHub": {"image": "tih", "path": "Sleekflow.TicketingHub", "dependencies": ["Sleekflow.TicketingHub.Models"]}, "SupportHub": {"image": "suph", "path": "Sleekflow.SupportHub", "dependencies": ["Sleekflow.SupportHub.Models"]}, "Scheduler": {"image": "sch", "path": "Sleekflow.Scheduler"}, "InternalIntegrationHub": {"image": "iih", "path": "Sleekflow.InternalIntegrationHub", "dependencies": ["Sleekflow.InternalIntegrationHub.Models"]}, "OpenPolicyAgent": {"image": "opa", "path": "Sleekflow.OPA"}, "OpenPolicyAdministrationLayer": {"image": "opal", "path": "Sleekflow.OPAL"}, "GoogleSheetsIntegrator": {"image": "gs-in", "path": "Sleekflow.Integrator.GoogleSheets", "dependencies": ["Sleekflow.CrmHub.Models"]}, "IntelligentHubLightRag": {"image": "ihlr", "path": "Sleekflow.IntelligentHub.LightRAG", "context": "./Sleekflow.IntelligentHub.LightRAG"}, "ZohoIntegrator": {"image": "zh-in", "path": "Sleekflow.Integrator.Zoho", "dependencies": ["Sleekflow.CrmHub.Models"]}}