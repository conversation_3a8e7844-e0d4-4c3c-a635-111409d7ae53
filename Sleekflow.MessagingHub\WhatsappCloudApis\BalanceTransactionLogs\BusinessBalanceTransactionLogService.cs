using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using GraphApi.Client.ApiClients;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Expressions;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Commons;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Filters;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.ConversationUsages;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps.Stripe;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Invoices;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;
using Exception = System.Exception;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;

public interface IBusinessBalanceTransactionLogService
{
    Task<(List<BusinessBalanceTransactionLog> TransactionLogs, string? NextContinuationToken)>
        GetFilteredConversationUsageTransactionLogsAsync(
            BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
            int limit,
            string? continuationToken);

    Task<bool> ConversationAnalyticsInsertionAsync(
        Waba waba,
        BusinessBalance businessBalance,
        long lastConversationUsageInsertTimestamp,
        long nextConversationUsageInsertTimestamp);

    Task<bool> ConversationAnalyticsResynchronizationAsync(
        Waba waba,
        MarkupProfile markupProfile,
        List<BusinessBalanceTransactionLog> businessBalanceTransactionLogs,
        long lastConversationStartTimestamp,
        long latestConversationEndTimestamp);

    Task<BusinessBalanceTransactionLog> CreateAndGetWithBusinessWabaTopUpAsync(
        string facebookBusinessId,
        string? facebookWabaId,
        MarkupProfile markupProfile,
        string uniqueId,
        string topUpPaymentMethod,
        Money credit,
        string? creditedBy,
        string? creditedByDisplayName,
        StripeTopUpCreditDetail? stripeTopUpCreditDetail,
        Dictionary<string, object?> metadata);

    Task<List<BusinessBalanceTransactionLog>> GetResynchronizationBusinessTransactionLogAsync(
        string facebookWabaId,
        string facebookBusinessId,
        long lastConversationInsertStartDateTime,
        long latestConversationInsertEndDateTime);

    Task<List<BusinessBalanceTransactionLog>> GetAllCalculatedTransactionLogAsync(
        string facebookBusinessId,
        string facebookWabaId,
        long lastConversationInsertStartDateTime,
        long latestConversationInsertEndDateTime);

    Task<List<BusinessBalanceTransactionLog>> GetUnCalculatedLogsWithFacebookBusinessIdAsync(
        string facebookBusinessId,
        List<string>? transactionTypes = null);

    Task<BusinessBalanceTransactionLog?> GetOrDefaultBusinessBalanceTransactionLogAsync(
        string id,
        string facebookBusinessId);

    Task<List<BusinessBalanceTransactionLog>> GetConversationUsageTransactionLogAsync(string facebookBusinessId);

    Task<int> UpsertBusinessBalanceTransactionLogAsync(BusinessBalanceTransactionLog businessBalanceTransactionLog);

    Task UpsertMarkupProfileSnapshotStateAsync(
        string facebookBusinessId,
        MarkupProfile markupProfile,
        DateTimeOffset lastRecalculateBusinessBalanceTransactionLog,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<(WhatsappCloudApiDetailedConversationUsageAnalyticDto ConversationUsageAnalyic, Waba Waba)>
        GetWhatsappCloudApiConversationUsageAnalyticByWaba(
            string facebookBusinessId,
            Waba waba,
            DateTimeOffset start,
            DateTimeOffset end,
            string granularity,
            bool isDetailed);

    Task<(List<BusinessBalanceInvoice> Invoices,
            string? NextContinuationToken,
            int TotalNumberOfBusinessBalanceInvoices)>
        GetBusinessBalanceStripeTopUpInvoicesByFacebookBusinessIdAsync(
            Dictionary<string, List<Waba>> facebookBusinessIdToWabasDict,
            DateTimeOffset? start,
            DateTimeOffset? end,
            string? continuationToken,
            int limit = 10);

    BusinessBalanceTransactionLog ConstructBusinessBalanceTransactionLog(
        string facebookWabaId,
        string facebookBusinessId,
        string? facebookTimezoneId,
        MarkupProfile markupProfile,
        long distinctConversationStart,
        WabaConversationUsageTransactionItem wabaConversationUsageTransactionItem);

    public Task<List<ConversationAnalyticsResultData>?> GetConversationAnalyticsAsync(
        Waba waba,
        long lastConversationUsageInsertTimestamp,
        long nextConversationUsageInsertTimestamp,
        string granularity = WhatsappConversationAnalyticGranularityConst.HALF_HOUR,
        string? phoneNumber = null);
}

public class BusinessBalanceTransactionLogService : IBusinessBalanceTransactionLogService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly IAuditLogService _auditLogService;
    private readonly IWhatsappCloudApiBspClient _whatsappCloudApiBspClient;
    private readonly ILogger<BusinessBalanceTransactionLogService> _logger;
    private readonly IBusinessBalanceTransactionLogValidator _businessBalanceTransactionLogValidator;
    private readonly IBusinessBalanceTransactionLogRepository _businessBalanceTransactionLogRepository;
    private readonly IWabaService _wabaService;
    private readonly HttpClient _httpClient;

    public BusinessBalanceTransactionLogService(
        IIdService idService,
        IAuditLogService auditLogService,
        ICloudApiClients cloudApiClients,
        ILogger<BusinessBalanceTransactionLogService> logger,
        IBusinessBalanceTransactionLogValidator businessBalanceTransactionLogValidator,
        IBusinessBalanceTransactionLogRepository businessBalanceTransactionLogRepository,
        IWabaService wabaService,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _idService = idService;
        _auditLogService = auditLogService;
        _whatsappCloudApiBspClient = cloudApiClients.WhatsappCloudApiBspClient;
        _businessBalanceTransactionLogValidator = businessBalanceTransactionLogValidator;
        _businessBalanceTransactionLogRepository = businessBalanceTransactionLogRepository;
        _wabaService = wabaService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<(List<BusinessBalanceTransactionLog> TransactionLogs, string? NextContinuationToken)>
        GetFilteredConversationUsageTransactionLogsAsync(
            BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
            int limit,
            string? continuationToken)
    {
        var (transactionLogs, nextContinuationToken) =
            await _businessBalanceTransactionLogRepository.FilterConversationUsageTransactionLogs(
                businessBalanceTransactionLogFilter,
                continuationToken,
                limit);

        return (transactionLogs, nextContinuationToken);
    }

    public async Task<bool> ConversationAnalyticsInsertionAsync(
        Waba waba,
        BusinessBalance businessBalance,
        long lastConversationUsageInsertTimestamp,
        long nextConversationUsageInsertTimestamp)
    {
        var isConversationAnalyticsTransactionLogInserted = false;
        var conversationAnalyticsResults = await GetConversationAnalyticsAsync(
            waba,
            lastConversationUsageInsertTimestamp,
            nextConversationUsageInsertTimestamp);

        if (conversationAnalyticsResults is null)
        {
            return isConversationAnalyticsTransactionLogInserted;
        }

        foreach (var conversationAnalyticsResultData in conversationAnalyticsResults)
        {
            try
            {
                var createWithWabaConversationUsageResults = await CreateWithWabaConversationUsageAsync(
                    waba,
                    conversationAnalyticsResultData,
                    businessBalance.MarkupProfile);
                if (createWithWabaConversationUsageResults is null || !createWithWabaConversationUsageResults.Any())
                {
                    _logger.LogError(
                        "Unable to create business balance transaction log / {ConversationAnalyticsResultData}",
                        JsonConvert.SerializeObject(conversationAnalyticsResultData));
                    continue;
                }

                isConversationAnalyticsTransactionLogInserted = true;
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    "Error occur during create CreateWithWabaConversationUsageAsync {ConversationAnalyticsResultData} / {Exception}",
                    JsonConvert.SerializeObject(conversationAnalyticsResultData),
                    JsonConvert.SerializeObject(exception));
            }
        }

        return isConversationAnalyticsTransactionLogInserted;
    }

    public async Task<bool> ConversationAnalyticsResynchronizationAsync(
        Waba waba,
        MarkupProfile markupProfile,
        List<BusinessBalanceTransactionLog> businessBalanceTransactionLogs,
        long lastConversationStartTimestamp,
        long latestConversationEndTimestamp)
    {
        var hasConversationAnalyticsRecordModified = false;
        var conversationAnalyticsResults = await GetConversationAnalyticsAsync(
            waba,
            lastConversationStartTimestamp,
            latestConversationEndTimestamp);

        if (conversationAnalyticsResults is null)
        {
            return hasConversationAnalyticsRecordModified;
        }

        foreach (var conversationAnalyticsResultData in conversationAnalyticsResults)
        {
            try
            {
                var resynchronizeWithWabaConversationUsage = await ResynchronizeWithWabaConversationUsageAsync(
                    waba.FacebookWabaId,
                    waba.FacebookBusinessId,
                    waba.FacebookWabaSnapshot["timezone_id"].ToString(),
                    businessBalanceTransactionLogs,
                    markupProfile,
                    lastConversationStartTimestamp,
                    conversationAnalyticsResultData);

                if (resynchronizeWithWabaConversationUsage)
                {
                    hasConversationAnalyticsRecordModified = resynchronizeWithWabaConversationUsage;
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    "Error occur during create CreateWithWabaConversationUsageAsync {ConversationAnalyticsResultData} / {Exception}",
                    JsonConvert.SerializeObject(conversationAnalyticsResultData),
                    JsonConvert.SerializeObject(exception));
            }
        }

        return hasConversationAnalyticsRecordModified;
    }

    public async Task<BusinessBalanceTransactionLog> CreateAndGetWithBusinessWabaTopUpAsync(
        string facebookBusinessId,
        string? facebookWabaId,
        MarkupProfile markupProfile,
        string uniqueId,
        string topUpPaymentMethod,
        Money credit,
        string? creditedBy,
        string? creditedByDisplayName,
        StripeTopUpCreditDetail? stripeTopUpCreditDetail,
        Dictionary<string, object?> metadata)
    {
        if (!TopUpPaymentMethods.TopUpPaymentMethodList.Contains(topUpPaymentMethod))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        $"An invalid payment method is found:{topUpPaymentMethod}")
                });
        }

        var wabaTopUpTransactionItem = new WabaTopUpTransactionItem(
            credit,
            topUpPaymentMethod,
            stripeTopUpCreditDetail,
            new InternalTopUpCreditDetail(
                uniqueId,
                creditedBy,
                creditedByDisplayName,
                DateTimeOffset.UtcNow,
                metadata));

        var isDuplicateUniqueId =
            await _businessBalanceTransactionLogRepository.IsDuplicateUniqueId(uniqueId, facebookBusinessId);

        if (isDuplicateUniqueId)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        $"A duplicate unique id is found:{uniqueId}, wabaTopUpTransactionItem:{JsonConvert.SerializeObject(wabaTopUpTransactionItem)}")
                });
        }

        var utcNow = DateTimeOffset.UtcNow;

        return await _businessBalanceTransactionLogRepository.CreateAndGetAsync(
            new BusinessBalanceTransactionLog(
                _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                facebookWabaId,
                facebookBusinessId,
                wabaTopUpTransactionItem.GetUniqueId,
                credit,
                new Money(CurrencyIsoCodes.USD, 0),
                new Money(CurrencyIsoCodes.USD, 0),
                new Money(CurrencyIsoCodes.USD, 0),
                TransactionTypes.TopUp,
                false,
                wabaTopUpTransactionItem,
                null,
                markupProfile,
                null,
                null,
                utcNow,
                utcNow),
            facebookBusinessId);
    }

    public async Task<List<BusinessBalanceTransactionLog>> GetResynchronizationBusinessTransactionLogAsync(
        string facebookWabaId,
        string facebookBusinessId,
        long lastConversationInsertStartDateTime,
        long latestConversationInsertEndDateTime)
    {
        string? nextContinuationToken = null;
        var businessBalanceTransactionLogs = new List<BusinessBalanceTransactionLog>();
        while (true)
        {
            var (transactionLogs, continuationToken) =
                await _businessBalanceTransactionLogRepository.FilterConversationUsageTransactionLogs(
                    new BusinessBalanceTransactionLogFilter(
                        TransactionTypes.ConversationUsage,
                        facebookBusinessId,
                        facebookWabaId,
                        new TimestampRange(lastConversationInsertStartDateTime, latestConversationInsertEndDateTime),
                        IHasCreatedAt.PropertyNameCreatedAt,
                        "ASC"),
                    nextContinuationToken);

            businessBalanceTransactionLogs.AddRange(transactionLogs);
            nextContinuationToken = continuationToken;

            if (nextContinuationToken == null)
            {
                break;
            }
        }

        return businessBalanceTransactionLogs;
    }

    public async Task<List<BusinessBalanceTransactionLog>> GetAllCalculatedTransactionLogAsync(
        string facebookBusinessId,
        string facebookWabaId,
        long lastConversationInsertStartDateTime,
        long latestConversationInsertEndDateTime)
    {
        string? nextContinuationToken = null;
        var businessBalanceTransactionLogs = new List<BusinessBalanceTransactionLog>();
        while (true)
        {
            var (transactionLogs, continuationToken) =
                await _businessBalanceTransactionLogRepository.FilterConversationUsageTransactionLogs(
                    new BusinessBalanceTransactionLogFilter(
                        TransactionTypes.ConversationUsage,
                        facebookBusinessId,
                        facebookWabaId,
                        new TimestampRange(lastConversationInsertStartDateTime, latestConversationInsertEndDateTime),
                        default,
                        default)
                    {
                        IsCalculated = true
                    },
                    nextContinuationToken);

            businessBalanceTransactionLogs.AddRange(transactionLogs);
            nextContinuationToken = continuationToken;

            if (nextContinuationToken == null)
            {
                break;
            }
        }

        return businessBalanceTransactionLogs;
    }

    public async Task<List<BusinessBalanceTransactionLog>> GetUnCalculatedLogsWithFacebookBusinessIdAsync(
        string facebookBusinessId,
        List<string>? transactionTypes = null)
    {
        Expression<Func<BusinessBalanceTransactionLog, bool>> expression = businessBalanceTransactionLog =>
            businessBalanceTransactionLog.FacebookBusinessId == facebookBusinessId && businessBalanceTransactionLog.IsCalculated == false;

        return await _businessBalanceTransactionLogRepository.GetObjectsAsync(
            expression.IfAndAlso(
                () => transactionTypes != null && transactionTypes.Any(),
                businessBalanceTransactionLog =>
                    transactionTypes.Contains(businessBalanceTransactionLog.TransactionType)));
    }

    public async Task<BusinessBalanceTransactionLog?> GetOrDefaultBusinessBalanceTransactionLogAsync(
        string id,
        string facebookBusinessId)
    {
        return await _businessBalanceTransactionLogRepository.GetOrDefaultAsync(id, facebookBusinessId);
    }

    public async Task<List<BusinessBalanceTransactionLog>> GetConversationUsageTransactionLogAsync(
        string facebookBusinessId)
    {
        return await _businessBalanceTransactionLogRepository.GetObjectsAsync(
            b =>
                b.FacebookBusinessId == facebookBusinessId
                && b.TransactionType == TransactionTypes.ConversationUsage
                && b.MarkupProfileSnapshot != null
                && b.WabaConversationUsage != null
                && b.IsCalculated == true);
    }

    public async Task<int> UpsertBusinessBalanceTransactionLogAsync(
        BusinessBalanceTransactionLog businessBalanceTransactionLog)
    {
        return await _businessBalanceTransactionLogRepository.UpsertAsync(
            businessBalanceTransactionLog,
            businessBalanceTransactionLog.FacebookBusinessId,
            eTag: businessBalanceTransactionLog.ETag);
    }

    public async Task UpsertMarkupProfileSnapshotStateAsync(
        string facebookBusinessId,
        MarkupProfile markupProfile,
        DateTimeOffset lastRecalculateBusinessBalanceTransactionLog,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var businessBalanceTransactionLogs = await _businessBalanceTransactionLogRepository
            .GetObjectsAsync(
                b =>
                    b.WabaConversationUsage != null &&
                    b.FacebookBusinessId == facebookBusinessId &&
                    b.WabaConversationUsage.StartTimestamp >=
                    lastRecalculateBusinessBalanceTransactionLog.ToUnixTimeSeconds());

        foreach (var businessBalanceTransactionLog in businessBalanceTransactionLogs)
        {
            var currentBusinessBalanceTransactionLog = await GetOrDefaultBusinessBalanceTransactionLogAsync(
                businessBalanceTransactionLog.Id,
                businessBalanceTransactionLog.FacebookBusinessId);

            if (currentBusinessBalanceTransactionLog is null)
            {
                continue;
            }

            var currentBusinessBalanceTransactionLogSnapshot =
                JsonConvert.DeserializeObject<BusinessBalanceTransactionLog>(
                    JsonConvert.SerializeObject(currentBusinessBalanceTransactionLog));

            currentBusinessBalanceTransactionLog.MarkupProfileSnapshot = markupProfile;
            var businessBalanceTransactionLogUpsertState =
                await UpsertBusinessBalanceTransactionLogAsync(currentBusinessBalanceTransactionLog);

            if (businessBalanceTransactionLogUpsertState == 0)
            {
                _logger.LogError(
                    "Unable to upsert business balance transaction logs {BusinessBalanceTransactionLog}",
                    JsonConvert.SerializeObject(businessBalanceTransactionLog));
                continue;
            }

            await _auditLogService.AuditBusinessBalanceTransactionLogAsync(
                currentBusinessBalanceTransactionLogSnapshot,
                currentBusinessBalanceTransactionLog.FacebookBusinessId,
                AuditingOperation.ResetBusinessBalanceTransactionLogCalculatedState,
                new Dictionary<string, object?>
                {
                    {
                        "change", currentBusinessBalanceTransactionLog
                    },
                    {
                        "sleekflow_staff_id", sleekflowStaffId
                    },
                    {
                        "sleekflow_staff_team_ids", sleekflowStaffTeamIds
                    }
                });
        }
    }

    public async Task<(List<BusinessBalanceInvoice> Invoices,
            string? NextContinuationToken,
            int TotalNumberOfBusinessBalanceInvoices)>
        GetBusinessBalanceStripeTopUpInvoicesByFacebookBusinessIdAsync(
            Dictionary<string, List<Waba>> facebookBusinessIdToWabasDict,
            DateTimeOffset? start,
            DateTimeOffset? end,
            string? continuationToken,
            int limit = 10)
    {
        var facebookBusinessIds = facebookBusinessIdToWabasDict.Select(x => x.Key).ToList();

        var totalNumberOfBusinessBalanceTransactionLogsContainInvoicePdf =
            (await _businessBalanceTransactionLogRepository
                .FindBusinessBalanceTransactionLogsContainsInvoicePdfByFacebookBusinessId(
                    facebookBusinessIds,
                    start,
                    end,
                    null,
                    int.MaxValue))
            .BusinessBalanceTransactionLogs.Count;

        var (businessBalanceTransactionLogsContainInvoicePdf, nextContinuationToken) =
            await _businessBalanceTransactionLogRepository
                .FindBusinessBalanceTransactionLogsContainsInvoicePdfByFacebookBusinessId(
                    facebookBusinessIds,
                    start,
                    end,
                    continuationToken,
                    limit);

        var invoices = new List<BusinessBalanceInvoice>();

        foreach (var businessBalanceTransactionLogContainInvoicePdf in businessBalanceTransactionLogsContainInvoicePdf)
        {
            var payAmount = businessBalanceTransactionLogContainInvoicePdf.WabaTopUp?.PayAmount;

            if (payAmount is null)
            {
                continue;
            }

            var snapShot = businessBalanceTransactionLogContainInvoicePdf.WabaTopUp?.StripeTopUpCreditDetail?.SnapShot;
            if (snapShot is null)
            {
                continue;
            }

            var hasInvoicePaid = snapShot.TryGetValue(Stripe.Events.InvoicePaid, out var invoicePaid);

            if (!hasInvoicePaid || invoicePaid is null)
            {
                continue;
            }

            var invoiceDictionary = JObject.FromObject(invoicePaid).ToObject<Dictionary<string, object>>();

            if (invoiceDictionary is null)
            {
                continue;
            }

            var isFacebookBusinessIdExists = facebookBusinessIdToWabasDict.TryGetValue(
                businessBalanceTransactionLogContainInvoicePdf.FacebookBusinessId,
                out var wabas);

            if (!isFacebookBusinessIdExists)
            {
                continue;
            }

            var facebookBusinessName = wabas?.Select(w => w.FacebookWabaBusinessName).FirstOrDefault();

            var invoice = new BusinessBalanceInvoice(
                (string) invoiceDictionary["invoice_pdf"],
                (string) invoiceDictionary["status"],
                (long) invoiceDictionary["created"],
                payAmount,
                "Sleekflow WhatsApp Credit",
                businessBalanceTransactionLogContainInvoicePdf.FacebookBusinessId,
                facebookBusinessName);

            invoices.Add(invoice);
        }

        return (invoices, nextContinuationToken, totalNumberOfBusinessBalanceTransactionLogsContainInvoicePdf);
    }

    public async Task<(WhatsappCloudApiDetailedConversationUsageAnalyticDto ConversationUsageAnalyic, Waba Waba)>
        GetWhatsappCloudApiConversationUsageAnalyticByWaba(
            string facebookBusinessId,
            Waba waba,
            DateTimeOffset start,
            DateTimeOffset end,
            string granularity,
            bool isDetailed)
    {
        var facebookTimezone = CloudApiUtils.GetFacebookTimezone(waba.FacebookWabaSnapshot?["timezone_id"]?.ToString());

        var conversationAnalytics = await GetWhatsappCloudApiConversationUsageAnalytics(
            facebookBusinessId,
            waba.FacebookWabaId,
            facebookTimezone,
            start,
            end,
            granularity,
            isDetailed);

        return (conversationAnalytics, waba);
    }

    private async Task<WhatsappCloudApiDetailedConversationUsageAnalyticDto>
        GetWhatsappCloudApiConversationUsageAnalytics(
            string facebookBusinessId,
            string facebookWabaId,
            FacebookTimezone facebookTimezone,
            DateTimeOffset start,
            DateTimeOffset end,
            string granularity,
            bool isDetailed)
    {
        (DateTimeOffset Start, DateTimeOffset End) timezoneOffsetDateTimeRange = granularity switch
        {
            WhatsappConversationAnalyticGranularityConst.HALF_HOUR =>
            (
                new (start.Year, start.Month, start.Day, start.Hour, start.Minute, 0, facebookTimezone.TimezoneOffset),
                new (end.Year, end.Month, end.Day, end.Hour, end.Minute, 0, facebookTimezone.TimezoneOffset)
            ),
            WhatsappConversationAnalyticGranularityConst.DAILY => (
                new DateTimeOffset(start.Year, start.Month, start.Day, 0, 0, 0, facebookTimezone.TimezoneOffset),
                new DateTimeOffset(end.Year, end.Month, end.Day, 0, 0, 0, facebookTimezone.TimezoneOffset)
            ),
            WhatsappConversationAnalyticGranularityConst.MONTHLY => (
                new DateTimeOffset(start.Year, start.Month, 1, 0, 0, 0, facebookTimezone.TimezoneOffset),
                new DateTimeOffset(end.Year, end.Month, 1, 0, 0, 0, facebookTimezone.TimezoneOffset)
            ),
            _ => (
                new DateTimeOffset(
                    start.Year,
                    start.Month,
                    start.Day,
                    start.Hour,
                    start.Minute,
                    start.Second,
                    facebookTimezone.TimezoneOffset),
                new DateTimeOffset(
                    end.Year,
                    end.Month,
                    end.Day,
                    end.Hour,
                    end.Minute,
                    end.Second,
                    facebookTimezone.TimezoneOffset)
            )
        };

        // Get All Conversation Usage Logs
        var allTransactionLogs = new List<BusinessBalanceTransactionLog>();
        string? nextContinuationToken = null;

        while (true)
        {
            var (transactionLogs, continuationToken) =
                await _businessBalanceTransactionLogRepository.FilterConversationUsageTransactionLogs(
                    new BusinessBalanceTransactionLogFilter(
                        TransactionTypes.ConversationUsage,
                        facebookBusinessId,
                        facebookWabaId,
                        null,
                        new TimestampRange(
                            timezoneOffsetDateTimeRange.Start.ToUnixTimeSeconds(),
                            timezoneOffsetDateTimeRange.End.ToUnixTimeSeconds()),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        IHasCreatedAt.PropertyNameCreatedAt,
                        "ASC"),
                    nextContinuationToken);

            allTransactionLogs.AddRange(transactionLogs);
            nextContinuationToken = continuationToken;

            if (nextContinuationToken == null)
            {
                break;
            }
        }

        var granularAnalytics = new List<WhatsappCloudApiGranularConversationUsageAnalyticDto>();
        var dayPoint = timezoneOffsetDateTimeRange.Start;

        while (dayPoint <= timezoneOffsetDateTimeRange.End)
        {
            var nextDayPoint = granularity switch
            {
                WhatsappConversationAnalyticGranularityConst.HALF_HOUR => dayPoint.AddMinutes(30),
                WhatsappConversationAnalyticGranularityConst.DAILY => dayPoint.AddDays(1),
                WhatsappConversationAnalyticGranularityConst.MONTHLY => dayPoint.AddMonths(1),
                _ => throw new ArgumentOutOfRangeException(nameof(granularity), granularity, null)
            };

            if (nextDayPoint > timezoneOffsetDateTimeRange.End)
            {
                break;
            }

            var transactionLogsWithinPeriod = allTransactionLogs.Where(
                t => t.WabaConversationUsage!.StartTimestamp >= dayPoint.ToUnixTimeSeconds() &&
                     t.WabaConversationUsage!.EndTimestamp <= nextDayPoint.ToUnixTimeSeconds()).ToArray();

            var usedSum = new Money("USD", 0);
            var markupSum = new Money("USD", 0);
            var transactionHandlingFeeSum = new Money("USD", 0);

            foreach (var businessBalanceTransactionLog in transactionLogsWithinPeriod)
            {
                usedSum = MoneyExtensions.Add(usedSum, businessBalanceTransactionLog.Used);
                markupSum = MoneyExtensions.Add(markupSum, businessBalanceTransactionLog.Markup);
                if (businessBalanceTransactionLog.TransactionHandlingFee != null)
                {
                    transactionHandlingFeeSum = MoneyExtensions.Add(
                        transactionHandlingFeeSum,
                        businessBalanceTransactionLog.TransactionHandlingFee!);
                }
            }

            var totalUsed = MoneyExtensions.AddAll(usedSum, markupSum, transactionHandlingFeeSum);

            var analytic = new WhatsappCloudApiGranularConversationUsageAnalyticDto(
                transactionLogsWithinPeriod.Sum(x => x.WabaConversationUsage!.BusinessInitiatedPaidQuantity),
                transactionLogsWithinPeriod.Sum(x => x.WabaConversationUsage!.BusinessInitiatedFreeTierQuantity),
                transactionLogsWithinPeriod.Sum(x => x.WabaConversationUsage!.UserInitiatedPaidQuantity),
                transactionLogsWithinPeriod.Sum(x => x.WabaConversationUsage!.UserInitiatedFreeTierQuantity),
                transactionLogsWithinPeriod.Sum(x => x.WabaConversationUsage!.UserInitiatedFreeEntryPointQuantity),
                CloudApiUtils.SumConversationCategoryQuantities(
                    transactionLogsWithinPeriod
                        .Where(x => x.WabaConversationUsage?.ConversationCategoryQuantities != null)
                        .Select(c => c.WabaConversationUsage!.ConversationCategoryQuantities!)
                        .ToList()),
                totalUsed,
                CloudApiUtils.FormatDateTimeOffsetByGranularity(dayPoint, granularity),
                CloudApiUtils.FormatDateTimeOffsetByGranularity(nextDayPoint, granularity));

            if (isDetailed)
            {
                // Management facing number
                analytic.Used = usedSum;
                analytic.Markup = markupSum;
                analytic.TransactionHandlingFee = transactionHandlingFeeSum;
            }

            granularAnalytics.Add(analytic);
            dayPoint = nextDayPoint;
        }

        return new WhatsappCloudApiDetailedConversationUsageAnalyticDto(
            granularAnalytics,
            granularity,
            CloudApiUtils.FormatDateTimeOffsetByGranularity(timezoneOffsetDateTimeRange.Start, granularity),
            CloudApiUtils.FormatDateTimeOffsetByGranularity(timezoneOffsetDateTimeRange.End, granularity));
    }

    internal const long NewPricingUtcTimestamp = 1685577600;

    public async Task<List<ConversationAnalyticsResultData>?> GetConversationAnalyticsAsync(
        Waba waba,
        long lastConversationUsageInsertTimestamp,
        long nextConversationUsageInsertTimestamp,
        string granularity = WhatsappConversationAnalyticGranularityConst.HALF_HOUR,
        string? phoneNumber = null)
    {
        // Get Time zone
        var facebookTimezoneId = waba.FacebookWabaSnapshot?["timezone_id"]?.ToString();
        var facebookTimezone = CloudApiUtils.GetFacebookTimezone(facebookTimezoneId);

        // Get all Display Phone Numbers, ignore status
        var phoneNumbers = phoneNumber != null
            ?[phoneNumber]
            : waba.WabaPhoneNumbers
                .Select(wabaPhoneNumber => wabaPhoneNumber.FacebookPhoneNumberDetail)
                .Select(whatsappPhoneNumberDetail => whatsappPhoneNumberDetail.DisplayPhoneNumber)
                .Where(displayPhoneNumber => !string.IsNullOrEmpty(displayPhoneNumber))
                .Select(CloudApiUtils.NormalizePhoneNumber!)
                .Distinct()
                .ToList();

        var timePeriod = GetPeriodForDifferentPricing(
            facebookTimezone.TimezoneOffset,
            lastConversationUsageInsertTimestamp,
            nextConversationUsageInsertTimestamp);

        var conversationAnalytics = new List<ConversationAnalyticsResultData>();

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var whatsappCloudApiBspClient = hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
            ? new WhatsappCloudApiBspClient(
                decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken,
                _httpClient)
            : _whatsappCloudApiBspClient;

        if (timePeriod.DirectionBased.HasValue)
        {
            var directionBasedConversationAnalyticsResponse =
                await whatsappCloudApiBspClient.GetConversationAnalyticsAsync(
                    waba.FacebookWabaId,
                    timePeriod.DirectionBased.Value.Start,
                    timePeriod.DirectionBased.Value.End,
                    granularity,
                    metricTypes: new List<string>
                    {
                        WhatsappConversationAnalyticMetricTypeConst.COST,
                        WhatsappConversationAnalyticMetricTypeConst.CONVERSATION
                    },
                    dimensions: new List<string>
                    {
                        WhatsappConversationAnalyticDimensionsConst.conversation_type,
                        WhatsappConversationAnalyticDimensionsConst.conversation_direction
                    },
                    phoneNumbers: phoneNumbers);

            if (directionBasedConversationAnalyticsResponse.ConversationAnalyticsResult?.Data != null)
            {
                conversationAnalytics.AddRange(
                    directionBasedConversationAnalyticsResponse.ConversationAnalyticsResult.Data);
            }
        }

        if (timePeriod.CategoryBased.HasValue)
        {
            var categoryBasedConversationAnalyticsResponse =
                await whatsappCloudApiBspClient.GetConversationAnalyticsAsync(
                    waba.FacebookWabaId,
                    timePeriod.CategoryBased.Value.Start,
                    timePeriod.CategoryBased.Value.End,
                    granularity,
                    metricTypes: new List<string>
                    {
                        WhatsappConversationAnalyticMetricTypeConst.COST,
                        WhatsappConversationAnalyticMetricTypeConst.CONVERSATION
                    },
                    dimensions: new List<string>
                    {
                        WhatsappConversationAnalyticDimensionsConst.conversation_type,
                        WhatsappConversationAnalyticDimensionsConst.conversation_category,
                    },
                    phoneNumbers: phoneNumbers);

            if (categoryBasedConversationAnalyticsResponse.ConversationAnalyticsResult?.Data != null)
            {
                conversationAnalytics.AddRange(
                    categoryBasedConversationAnalyticsResponse.ConversationAnalyticsResult.Data);
            }
        }

        return conversationAnalytics;
    }

    internal ((long Start, long End)? DirectionBased, (long Start, long End)? CategoryBased)
        GetPeriodForDifferentPricing(
            TimeSpan timezoneOffset,
            long start,
            long end)
    {
        var newPricingTimezoneOffsetTimestamp = (long) (NewPricingUtcTimestamp - timezoneOffset.TotalSeconds);

        if (start < newPricingTimezoneOffsetTimestamp)
        {
            if (end < newPricingTimezoneOffsetTimestamp)
            {
                return ((start, end), null);
            }

            return ((start, newPricingTimezoneOffsetTimestamp), (newPricingTimezoneOffsetTimestamp, end));
        }

        return (null, (start, end));
    }

    private IEnumerable<IGrouping<long, WhatsappConversationAnalyticsResultDataPoint>> ExtractDataPoints(
        ConversationAnalyticsResultData conversationAnalyticsResultData)
    {
        var dataPoints = conversationAnalyticsResultData.DataPoints;
        _businessBalanceTransactionLogValidator.AssertValidBusinessBalanceTransactionLog(dataPoints);
        return dataPoints.OrderBy(dp => dp.Start).GroupBy(dp => dp.Start);
    }

    private async Task<List<int>?> CreateWithWabaConversationUsageAsync(
        Waba waba,
        ConversationAnalyticsResultData conversationAnalyticsResultData,
        MarkupProfile markupProfile)
    {
        var conversationStartToDataPointsEntries = ExtractDataPoints(conversationAnalyticsResultData);
        var createWithWabaConversationUsageResults = new List<int>();

        foreach (var conversationStartToDataPointsEntry in conversationStartToDataPointsEntries)
        {
            var conversationUsageTransactionItem = ConstructWabaConversationUsageTransactionItem(
                waba.FacebookWabaId,
                conversationStartToDataPointsEntry.Key,
                conversationStartToDataPointsEntry.ToList());

            var businessTransactionLogCreateState = await CreateBusinessBalanceTransactionLog(
                waba.FacebookWabaId,
                waba.FacebookBusinessId,
                waba?.FacebookWabaSnapshot?["timezone_id"]?.ToString(),
                markupProfile,
                conversationStartToDataPointsEntry.Key,
                conversationUsageTransactionItem);
            createWithWabaConversationUsageResults.Add(businessTransactionLogCreateState);
        }

        return createWithWabaConversationUsageResults;
    }

    private async Task<bool> ResynchronizeWithWabaConversationUsageAsync(
        string facebookWabaId,
        string facebookBusinessId,
        string? facebookTimezoneId,
        List<BusinessBalanceTransactionLog> businessBalanceTransactionLogs,
        MarkupProfile markupProfile,
        long lastConversationStartTimestamp,
        ConversationAnalyticsResultData conversationAnalyticsResultData)
    {
        var markupProfileInstanceState = markupProfile;
        var hasConversationAnalyticsRecordModified = false;
        var conversationStartToDataPointsEntries = ExtractDataPoints(conversationAnalyticsResultData);
        foreach (var conversationStartToDataPointsEntry in conversationStartToDataPointsEntries)
        {
            try
            {
                var conversationUsageTransactionItem = ConstructWabaConversationUsageTransactionItem(
                    facebookWabaId,
                    conversationStartToDataPointsEntry.Key,
                    conversationStartToDataPointsEntry.ToList());

                var businessBalanceTransactionLog =
                    businessBalanceTransactionLogs.Find(
                        b => b.UniqueId == conversationUsageTransactionItem.GetUniqueId);

                if (businessBalanceTransactionLog is not null)
                {
                    var (instanceModifiedStatus, instanceMarkupProfile) =
                        await ResynchronizeBusinessBalanceTransactionLog(
                            facebookWabaId,
                            facebookBusinessId,
                            facebookTimezoneId,
                            businessBalanceTransactionLog.Id,
                            lastConversationStartTimestamp,
                            conversationUsageTransactionItem);

                    if (instanceModifiedStatus)
                    {
                        hasConversationAnalyticsRecordModified = instanceModifiedStatus;
                    }

                    markupProfileInstanceState = instanceMarkupProfile;
                }
                else
                {
                    var createBusinessBalanceTransactionLog = await CreateBusinessBalanceTransactionLog(
                        facebookWabaId,
                        facebookBusinessId,
                        facebookTimezoneId,
                        markupProfileInstanceState,
                        lastConversationStartTimestamp,
                        conversationUsageTransactionItem,
                        true);

                    if (createBusinessBalanceTransactionLog != 0)
                    {
                        hasConversationAnalyticsRecordModified = true;
                    }
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "Exception occur during resynchronizeWithWabaConversationUsageAsync");
            }
        }

        return hasConversationAnalyticsRecordModified;
    }

    private async Task<(bool ModifiedStatus, MarkupProfile MarkupProfile)>
        ResynchronizeBusinessBalanceTransactionLog(
            string facebookWabaId,
            string facebookBusinessId,
            string? facebookTimezoneId,
            string businessBalanceTransactionLogId,
            long lastConversationStartTimestamp,
            WabaConversationUsageTransactionItem conversationUsageTransactionItem)
    {
        // Fetching Latest Instance Business Transaction Log
        var businessBalanceTransactionLog = await _businessBalanceTransactionLogRepository.GetAsync(
            businessBalanceTransactionLogId,
            facebookBusinessId);
        var businessBalanceTransactionLogSnapshot =
            JsonConvert.DeserializeObject<BusinessBalanceTransactionLog>(
                JsonConvert.SerializeObject(businessBalanceTransactionLog));

        var reconstructedTransactionLog = ConstructBusinessBalanceTransactionLog(
            facebookWabaId,
            facebookBusinessId,
            facebookTimezoneId,
            businessBalanceTransactionLog.MarkupProfileSnapshot,
            lastConversationStartTimestamp,
            conversationUsageTransactionItem);

        // Replacing business Transaction Log record source data with newly fetched data
        businessBalanceTransactionLog.Used = reconstructedTransactionLog.Used;
        businessBalanceTransactionLog.Markup = reconstructedTransactionLog.Markup;
        businessBalanceTransactionLog.TransactionHandlingFee =
            reconstructedTransactionLog.TransactionHandlingFee;
        businessBalanceTransactionLog.TransactionType = reconstructedTransactionLog.TransactionType;
        businessBalanceTransactionLog.WabaConversationUsage =
            reconstructedTransactionLog.WabaConversationUsage;

        var resynchronizeAtHistories =
            businessBalanceTransactionLog.ResynchronizeAtHistories ?? new List<DateTimeOffset>();
        resynchronizeAtHistories.Add(DateTimeOffset.UtcNow);
        businessBalanceTransactionLog.ResynchronizeAtHistories = resynchronizeAtHistories;
        businessBalanceTransactionLog.UpdatedAt = DateTimeOffset.UtcNow;

        var hasUsedAmountModified =
            businessBalanceTransactionLog.Used.Amount != businessBalanceTransactionLogSnapshot.Used.Amount;
        var hasMarkupAmountModified =
            businessBalanceTransactionLog.Markup.Amount != businessBalanceTransactionLogSnapshot.Markup.Amount;
        var hasTransactionLogAmountModified =
            businessBalanceTransactionLogSnapshot.TransactionHandlingFee is not null
            && businessBalanceTransactionLog.TransactionHandlingFee!.Amount !=
            businessBalanceTransactionLogSnapshot.TransactionHandlingFee.Amount;

        var hasWabaConversationUsageModified = HasWabaConversationUsageModified(
            businessBalanceTransactionLogSnapshot.WabaConversationUsage,
            conversationUsageTransactionItem);

        var instanceModifiedStatus =
            hasUsedAmountModified || hasMarkupAmountModified || hasTransactionLogAmountModified
            || hasWabaConversationUsageModified;

        if (!instanceModifiedStatus)
        {
            return (instanceModifiedStatus, businessBalanceTransactionLog.MarkupProfileSnapshot);
        }

        await _auditLogService.AuditBusinessBalanceTransactionLogAsync(
            businessBalanceTransactionLogSnapshot,
            businessBalanceTransactionLogSnapshot.FacebookBusinessId,
            AuditingOperation.ResetBusinessBalanceTransactionLogCalculatedState,
            new Dictionary<string, object?>
            {
                {
                    "change", businessBalanceTransactionLog
                },
            });
        await _businessBalanceTransactionLogRepository.UpsertAsync(
            businessBalanceTransactionLog,
            businessBalanceTransactionLogSnapshot.FacebookBusinessId,
            eTag: businessBalanceTransactionLog.ETag);

        return (instanceModifiedStatus, businessBalanceTransactionLog.MarkupProfileSnapshot);
    }

    public BusinessBalanceTransactionLog ConstructBusinessBalanceTransactionLog(
        string facebookWabaId,
        string facebookBusinessId,
        string? facebookTimezoneId,
        MarkupProfile markupProfile,
        long distinctConversationStart,
        WabaConversationUsageTransactionItem wabaConversationUsageTransactionItem)
    {
        var utcNow = DateTimeOffset.UtcNow;
        var totalCost = wabaConversationUsageTransactionItem.TotalCost;
        var actualTotalCost = new Money(Currencies.Usd, totalCost);
        var userInitiatedCost = wabaConversationUsageTransactionItem.UserInitiatedCost;
        var businessInitiatedCost = wabaConversationUsageTransactionItem.BusinessInitiatedCost;

        return new BusinessBalanceTransactionLog(
            _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
            facebookWabaId,
            facebookBusinessId,
            wabaConversationUsageTransactionItem.GetUniqueId,
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, wabaConversationUsageTransactionItem.TotalCost),
            TransactionLogUtils.GetCalculatedMarkup(
                markupProfile,
                userInitiatedCost,
                businessInitiatedCost,
                wabaConversationUsageTransactionItem.UserInitiatedPaidQuantity,
                wabaConversationUsageTransactionItem.BusinessInitiatedPaidQuantity),
            TransactionLogUtils.GetCalculatedTransactionHandlingFee(actualTotalCost.Amount, markupProfile),
            TransactionTypes.ConversationUsage,
            false,
            null,
            wabaConversationUsageTransactionItem,
            markupProfile,
            null,
            null,
            utcNow,
            utcNow);
    }

    private async Task<int> CreateBusinessBalanceTransactionLog(
        string facebookWabaId,
        string facebookBusinessId,
        string? facebookTimezoneId,
        MarkupProfile markupProfile,
        long lastConversationStartTimestamp,
        WabaConversationUsageTransactionItem conversationUsageTransactionItem,
        bool isCalculated = false)
    {
        var businessBalanceTransactionLog = ConstructBusinessBalanceTransactionLog(
            facebookWabaId,
            facebookBusinessId,
            facebookTimezoneId,
            markupProfile,
            lastConversationStartTimestamp,
            conversationUsageTransactionItem);

        businessBalanceTransactionLog.IsCalculated = isCalculated;

        if (await HasBusinessBalanceTransactionLogCreated(
                businessBalanceTransactionLog.FacebookBusinessId,
                conversationUsageTransactionItem))
        {
            _logger.LogInformation(
                "Whatsapp conversation analytics result data point already existed {FacebookBusinessId}/{UniqueId}",
                businessBalanceTransactionLog.FacebookBusinessId,
                conversationUsageTransactionItem.GetUniqueId);
            return 0;
        }

        // Note Assumption When obtaining it after an week the record would be stable
        var createdBusinessTransactionLog = await _businessBalanceTransactionLogRepository.CreateAsync(
            businessBalanceTransactionLog,
            facebookBusinessId);

        if (createdBusinessTransactionLog == 0)
        {
            _logger.LogError(
                "Unable to create business balance transaction log {BusinessBalanceTransactionLog}",
                JsonConvert.SerializeObject(businessBalanceTransactionLog));
        }

        return createdBusinessTransactionLog;
    }

    internal WabaConversationUsageTransactionItem ConstructWabaConversationUsageTransactionItem(
        string facebookWabaId,
        long distinctConversationStart,
        List<WhatsappConversationAnalyticsResultDataPoint> conversationStartDataPoints)
    {
        var businessInitiatedPaidQuantity = 0;
        var businessInitiatedFreeTierQuantity = 0;
        decimal businessInitiatedCost = 0;
        var userInitiatedPaidQuantity = 0;
        var userInitiatedFreeTierQuantity = 0;
        var userInitiatedFreeEntryPointQuantity = 0;

        // Apply to 2023-06-01 following data
        Dictionary<string, int>? conversationCategoryQuantities = null;
        Dictionary<string, decimal>? conversationCategoryCosts = null;
        if (conversationStartDataPoints.Any(x => !string.IsNullOrWhiteSpace(x.ConversationCategory)))
        {
            conversationCategoryQuantities = new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 0
                },
            };
            conversationCategoryCosts = new Dictionary<string, decimal>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 0
                },
            };
        }

        decimal userInitiatedCost = 0;

        var latestStartTime = 0L;
        var latestEndTime = 0L;

        foreach (var dp in conversationStartDataPoints)
        {
            var conversationType = dp.ConversationType;
            if (dp.End > latestEndTime)
            {
                latestStartTime = dp.Start;
                latestEndTime = dp.End;
            }

            if (conversationCategoryQuantities != null && conversationCategoryCosts != null &&
                !string.IsNullOrEmpty(dp.ConversationCategory))
            {
                var conversationCategory = dp.ConversationCategory.ToUpper();
                conversationCategoryQuantities.TryAdd(conversationCategory, 0);
                conversationCategoryCosts.TryAdd(conversationCategory, 0);

                conversationCategoryQuantities[conversationCategory] += dp.Conversation;
                conversationCategoryCosts[conversationCategory] += dp.Cost;
            }

            if (conversationType == ConversationTypes.FreeTier)
            {
                if (dp.ConversationDirection != null && dp.ConversationDirection is not "UNKNOWN")
                {
                    switch (dp.ConversationDirection)
                    {
                        case ConversationDirectionTypes.UserInitiated:
                            userInitiatedFreeTierQuantity += dp.Conversation;
                            break;
                        case ConversationDirectionTypes.BusinessInitiated:
                            businessInitiatedFreeTierQuantity += dp.Conversation;
                            break;
                    }
                }
                else
                {
                    if (dp.ConversationCategory is WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION
                        or WhatsappConversationAnalyticConversationCategoryConst.MARKETING
                        or WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE
                        or WhatsappConversationAnalyticConversationCategoryConst.UTILITY)
                    {
                        businessInitiatedFreeTierQuantity += dp.Conversation;
                    }

                    if (dp.ConversationCategory is WhatsappConversationAnalyticConversationCategoryConst.SERVICE)
                    {
                        userInitiatedFreeTierQuantity += dp.Conversation;
                    }
                }
            }
            else if (conversationType == ConversationTypes.FreeEntryPoint)
            {
                if (dp.ConversationDirection != null && dp.ConversationDirection is not "UNKNOWN")
                {
                    switch (dp.ConversationDirection)
                    {
                        case ConversationDirectionTypes.UserInitiated:
                            userInitiatedFreeEntryPointQuantity = dp.Conversation;
                            break;
                    }
                }
                else
                {
                    if (dp.ConversationCategory is WhatsappConversationAnalyticConversationCategoryConst.SERVICE)
                    {
                        userInitiatedFreeEntryPointQuantity += dp.Conversation;
                    }
                }
            }
            else if (conversationType == ConversationTypes.Regular)
            {
                if (dp.ConversationDirection != null && dp.ConversationDirection is not "UNKNOWN")
                {
                    switch (dp.ConversationDirection)
                    {
                        case ConversationDirectionTypes.UserInitiated:
                            userInitiatedPaidQuantity += dp.Conversation;
                            userInitiatedCost += dp.Cost;
                            break;
                        case ConversationDirectionTypes.BusinessInitiated:
                            businessInitiatedPaidQuantity += dp.Conversation;
                            businessInitiatedCost += dp.Cost;
                            break;
                    }
                }
                else
                {
                    if (dp.ConversationCategory is WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION
                        or WhatsappConversationAnalyticConversationCategoryConst.MARKETING
                        or WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE
                        or WhatsappConversationAnalyticConversationCategoryConst.UTILITY)
                    {
                        businessInitiatedPaidQuantity += dp.Conversation;
                        businessInitiatedCost += dp.Cost;
                    }

                    if (dp.ConversationCategory is WhatsappConversationAnalyticConversationCategoryConst.SERVICE)
                    {
                        userInitiatedPaidQuantity += dp.Conversation;
                        userInitiatedCost += dp.Cost;
                    }
                }
            }
            else
            {
                _logger.LogWarning(
                    "Unrecognized conversationType {ConversationType}",
                    conversationType);
            }
        }

        return new WabaConversationUsageTransactionItem(
            facebookWabaId,
            latestStartTime,
            latestEndTime,
            WhatsappConversationAnalyticGranularityConst.HALF_HOUR,
            businessInitiatedPaidQuantity,
            businessInitiatedFreeTierQuantity,
            businessInitiatedCost,
            userInitiatedPaidQuantity,
            userInitiatedFreeTierQuantity,
            userInitiatedFreeEntryPointQuantity,
            userInitiatedCost,
            conversationStartDataPoints,
            conversationCategoryQuantities,
            conversationCategoryCosts);
    }

    private async Task<bool> HasBusinessBalanceTransactionLogCreated(
        string facebookBusinessId,
        TransactionItem transactionItem)
    {
        return (await _businessBalanceTransactionLogRepository.GetObjectsAsync(
            b =>
                b.FacebookBusinessId == facebookBusinessId &&
                b.UniqueId == transactionItem.GetUniqueId)).Count != 0;
    }

    internal bool HasWabaConversationUsageModified(
        WabaConversationUsageTransactionItem transaction,
        WabaConversationUsageTransactionItem resynchronizedTransaction)
    {
        return
            transaction.Granularity != resynchronizedTransaction.Granularity
            || transaction.BusinessInitiatedPaidQuantity != resynchronizedTransaction.BusinessInitiatedPaidQuantity
            || transaction.BusinessInitiatedFreeTierQuantity !=
            resynchronizedTransaction.BusinessInitiatedFreeTierQuantity
            || transaction.BusinessInitiatedCost != resynchronizedTransaction.BusinessInitiatedCost
            || transaction.UserInitiatedPaidQuantity != resynchronizedTransaction.UserInitiatedPaidQuantity
            || transaction.UserInitiatedFreeTierQuantity != resynchronizedTransaction.UserInitiatedFreeTierQuantity
            || transaction.UserInitiatedFreeEntryPointQuantity !=
            resynchronizedTransaction.UserInitiatedFreeEntryPointQuantity
            || transaction.UserInitiatedCost != resynchronizedTransaction.UserInitiatedCost
            || transaction.ConversationAnalyticsDataPoints.Count !=
            resynchronizedTransaction.ConversationAnalyticsDataPoints.Count
            || !CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                transaction.ConversationCategoryQuantities,
                resynchronizedTransaction.ConversationCategoryQuantities)
            || !CloudApiUtils.IsConversationCategoryCostsEqual(
                transaction.ConversationCategoryCosts,
                resynchronizedTransaction.ConversationCategoryCosts);
    }
}