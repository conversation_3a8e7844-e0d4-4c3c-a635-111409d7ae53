using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Workers.Triggers.CloudApis;

public class OnCloudApiWabaResynchronizationTrigger
{
    private readonly IBus _bus;

    public OnCloudApiWabaResynchronizationTrigger(
        IBus bus)
    {
        _bus = bus;
    }

    /// <summary>
    /// Scheduling an daily recurring job for OnCloudApiWabaResynchronizationTrigger.
    /// </summary>
    /// <param name="timerInfo">Schedule Expression on "0 0 17 * * *" -> 1.00 AM UTC.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Function("OnCloudApiWabaResynchronizationTrigger")]
    public Task RunAsync(
        [TimerTrigger("0 0 17 * * *")]
        TimerInfo timerInfo)
    {
        return _bus.Publish(
            new OnCloudApiWabaResynchronizationTriggerEvent(DateTimeOffset.UtcNow));
    }
}