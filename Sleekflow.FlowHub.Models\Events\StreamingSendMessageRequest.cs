using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Models.Events;

public class StreamingSendMessageRequest
{
    [JsonProperty("id")]
    public Guid CorrelationId { get; set; }

    [JsonProperty("streaming_send_message_subscriptions")]
    public List<StreamingSendMessageSubscription> StreamingSendMessageSubscriptions { get; set; }

    [JsonProperty("partial_recommended_reply")]
    public string PartialRecommendedReply { get; set; }

    [JsonConstructor]
    public StreamingSendMessageRequest(
        Guid correlationId,
        List<StreamingSendMessageSubscription> streamingSendMessageSubscriptions,
        string partialRecommendedReply)
    {
        CorrelationId = correlationId;
        StreamingSendMessageSubscriptions = streamingSendMessageSubscriptions;
        PartialRecommendedReply = partialRecommendedReply;
    }
}