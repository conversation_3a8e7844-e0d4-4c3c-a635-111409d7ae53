using System.Text.Json;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Quality;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Sleekflow.IntelligentHub.Evaluator.Evaluators;

public class RagOutputScoringEvaluator : IEvaluator
{
    public const string RagOutputScoringMetricName = "RagOutputScoring";

    public const int FullScore = 5;

    public IReadOnlyCollection<string> EvaluationMetricNames { get; } =
    [
        RagOutputScoringMetricName
    ];

    // Define the JSON schema for the expected output
    private const string JsonSchemaInput =
        """
        {
          "$schema": "http://json-schema.org/draft-07/schema#",
          "strict": true,
          "type": "object",
          "required": ["accuracy", "coverage", "relevance", "stars", "key_issues", "recommendations"],
          "additionalProperties": false,
          "properties": {
            "accuracy": { "type": "string" },
            "coverage": { "type": "string" },
            "relevance": { "type": "string" },
            "stars": { "type": "integer" },
            "key_issues": {
              "type": "array",
              "items": { "type": "string" }
            },
            "recommendations": {
              "type": "array",
              "items": { "type": "string" }
            }
          }
        }
        """;

    // Set ChatOptions with the JSON response format based on the schema.
    private ChatOptions? ChatOptions { get; } = new ()
    {
        MaxOutputTokens = 8192,
        Temperature = 0.0f,
        TopP = 1f,
        PresencePenalty = 0.0f,
        FrequencyPenalty = 0.0f,
        ResponseFormat = ChatResponseFormat.ForJsonSchema(
            JsonSerializer.SerializeToElement(
                JsonDocument.Parse(JsonSchemaInput)))
    };

    // Implement the abstract property EvaluationMetricNames
    public async ValueTask<EvaluationResult> EvaluateAsync(
        IEnumerable<ChatMessage> messages,
        ChatResponse modelResponse,
        ChatConfiguration? chatConfiguration = null,
        IEnumerable<EvaluationContext>? additionalContext = null,
        CancellationToken cancellationToken = new CancellationToken())
    {
        var context = additionalContext?.OfType<RagOutputScoringEvaluationContext>().FirstOrDefault() ??
                      throw new Exception("Expected RagOutputScoringEvaluationContext in additionalContext.");

        var prompt =
            $$"""
              You are a specialized evaluator for comparing RAG (Retrieval-Augmented Generation) model outputs against their source materials. Your role is to perform a detailed quality assessment and provide structured feedback in JSON format.

              For this evaluation, please analyze the following aspects:

              1. ACCURACY ANALYSIS
                 - Evaluate factual consistency between the RAG output and the source material.
                 - Identify any hallucinations or unsupported claims.
                 - Flag any contradictions or misrepresentations of the source.

              2. COVERAGE ASSESSMENT
                 - Assess whether the output covers all key points from the source.
                 - Note any omissions or missing details.

              3. RELEVANCE EVALUATION
                 - Determine if the RAG output directly addresses the posed question.
                 - Recognize situations where the output provides analogous or similar information. For instance, if the request is for broadband services but the output recommends mobile service, consider this a beneficial alternative if it still helps address the user's underlying need.
                 - Identify any extraneous or irrelevant information.

              Additional Guidelines:
              - When the RAW source contains no data, note that this likely indicates an empty knowledge base; in such cases, please assign a score of 5.
              - If the RAG output offers similar or alternative recommendations that are still useful, even if not a direct hit from the source, factor this in as a positive outcome.

              4. SCORING CRITERIA (1-5):
                 (1): Major factual errors or omissions; content is largely irrelevant.
                 (2): Some inaccuracies and significant gaps in coverage.
                 (3): Generally accurate with minor issues.
                 (4): High accuracy and detail with very limited issues.
                 (5): Perfect accuracy, comprehensive coverage, and highly relevant.

              Please provide your evaluation as a JSON object in the following format:
              {
                 "accuracy": "Brief analysis of factual accuracy including any hallucinations",
                 "coverage": "Assessment of how completely the key information is covered",
                 "relevance": "Evaluation of whether the output addresses the question, including the value of recommended similar items if applicable",
                 "stars": [1-5],
                 "key_issues": [Array of key issues, if any in a string array],
                 "recommendations": [Array of suggestions for improvement in a string array]
              }

              Here are the details for this evaluation:

              ======Raw Source======
              {{context.RawSourceStr}}
              ======Raw Source======

              ======Question======
              {{messages.RenderText()}}
              ======Question======

              ======RAG Output======
              {{(string.IsNullOrWhiteSpace(context.RagSourceStr) ? "EMPTY" : context.RagSourceStr)}}
              ======RAG Output======

              Please analyze the RAG output thoroughly and return your answer in the specified JSON format.
              """;

        var evaluationResponse =
            await chatConfiguration.ChatClient.GetResponseAsync(
                new List<ChatMessage>
                {
                    new ChatMessage(ChatRole.System, prompt),
                },
                ChatOptions,
                cancellationToken: cancellationToken).ConfigureAwait(false);

        var modelResponseForEvaluationPrompt = evaluationResponse.Text.Trim();

        var scoringOutput = JsonConvert.DeserializeObject<RagOutputScoringOutput>(modelResponseForEvaluationPrompt)
                            ?? throw new Exception("Failed to deserialize the scoring output.");

        var result = new EvaluationResult(new NumericMetric(RagOutputScoringMetricName));
        var metric = result.Get<NumericMetric>(RagOutputScoringMetricName);

        // Use the "stars" value as the final score.
        metric.Value = scoringOutput.Stars;
        metric.Interpretation = scoringOutput.Stars switch
        {
            1 => new EvaluationMetricInterpretation(EvaluationRating.Unacceptable),
            2 => new EvaluationMetricInterpretation(EvaluationRating.Poor),
            3 => new EvaluationMetricInterpretation(EvaluationRating.Average),
            4 => new EvaluationMetricInterpretation(EvaluationRating.Good),
            5 => new EvaluationMetricInterpretation(EvaluationRating.Exceptional),
            _ => new EvaluationMetricInterpretation(EvaluationRating.Inconclusive)
        };

        // Attach the entire JSON for detailed reasoning in diagnostics.
        metric.Diagnostics = new List<EvaluationDiagnostic>
        {
            new EvaluationDiagnostic(
                EvaluationDiagnosticSeverity.Informational,
                JsonConvert.SerializeObject(scoringOutput))
        };

        return result;
    }
}

// Context for this evaluator, providing the raw source and the RAG output strings.
public class RagOutputScoringEvaluationContext : EvaluationContext
{
    public readonly string RawSourceStr;
    public readonly string RagSourceStr;

    public RagOutputScoringEvaluationContext(string rawSourceStr, string ragSourceStr)
        : base("RagOutputScoringEvaluationContext")
    {
        RawSourceStr = rawSourceStr;
        RagSourceStr = ragSourceStr;
    }
}

// POCO representing the expected JSON output from the evaluation.
public class RagOutputScoringOutput
{
    [JsonProperty("accuracy")]
    public string Accuracy { get; set; } = string.Empty;

    [JsonProperty("coverage")]
    public string Coverage { get; set; } = string.Empty;

    [JsonProperty("relevance")]
    public string Relevance { get; set; } = string.Empty;

    [JsonProperty("stars")]
    public int Stars { get; set; }

    [JsonProperty("key_issues")]
    public List<string> KeyIssues { get; set; } = new ();

    [JsonProperty("recommendations")]
    public List<string> Recommendations { get; set; } = new ();
}