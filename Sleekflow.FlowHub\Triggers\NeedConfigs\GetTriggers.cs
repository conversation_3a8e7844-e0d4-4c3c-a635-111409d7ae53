using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class GetTriggers : ITrigger<
    GetTriggers.GetTriggersInput,
    GetTriggers.GetTriggersOutput>
{
    private readonly INeedConfigService _needConfigService;

    public GetTriggers(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class GetTriggersInput
    {
        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public GetTriggersInput(string? version)
        {
            Version = version;
        }
    }

    public class GetTriggersOutput
    {
        [JsonProperty("triggers")]
        public List<TriggerConfigDto> Triggers { get; set; }

        [JsonConstructor]
        public GetTriggersOutput(List<TriggerConfigDto> triggers)
        {
            Triggers = triggers;
        }
    }

    public async Task<GetTriggersOutput> F(GetTriggersInput getTriggersInput)
    {
        var needConfigs = await _needConfigService.GetTriggersAsync(getTriggersInput.Version);

        return new GetTriggersOutput(
            needConfigs
                .Select(x => new TriggerConfigDto(x))
                .ToList());
    }
}