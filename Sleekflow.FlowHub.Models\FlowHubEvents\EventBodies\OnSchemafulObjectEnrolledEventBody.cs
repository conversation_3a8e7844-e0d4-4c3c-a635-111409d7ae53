﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnSchemafulObjectEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnSchemafulObjectEnrolled; }
    }

    [Required]
    [JsonProperty("schemaful_object_id")]
    public string SchemafulObjectId { get; set; }

    [Required]
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [Required]
    [JsonProperty("primary_property_value")]
    public string PrimaryPropertyValue { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; } // Not named as [SleekflowUserProfileId] to align with other event body

    [Required]
    [JsonProperty("property_values")]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnSchemafulObjectEnrolledEventBody(
        DateTimeOffset createdAt,
        string schemafulObjectId,
        string schemaId,
        string primaryPropertyValue,
        string contactId,
        Dictionary<string, object?> propertyValues,
        string workflowId,
        string workflowVersionedId)
        : base(createdAt)
    {
        CreatedAt = createdAt;
        SchemafulObjectId = schemafulObjectId;
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        ContactId = contactId;
        PropertyValues = propertyValues;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}