﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class CheckWorkflowContactEnrolmentConditionV2
    : ITrigger<
        CheckWorkflowContactEnrolmentConditionV2.CheckWorkflowContactEnrolmentConditionV2Input,
        CheckWorkflowContactEnrolmentConditionV2.CheckWorkflowContactEnrolmentConditionV2Output>
{
    private readonly IFlowHubEventEvaluator _flowHubEventEvaluator;
    public required ILogger<CheckWorkflowContactEnrolmentConditionV2> _logger;

    public CheckWorkflowContactEnrolmentConditionV2(
        IFlowHubEventEvaluator flowHubEventEvaluator,
        ILogger<CheckWorkflowContactEnrolmentConditionV2> logger)
    {
        _flowHubEventEvaluator = flowHubEventEvaluator;
        _logger = logger;
    }

    public class CheckWorkflowContactEnrolmentConditionV2Input : Models.Internals.CheckWorkflowContactEnrolmentConditionV2Input
    {
        [JsonConstructor]
        public CheckWorkflowContactEnrolmentConditionV2Input(
            OnDateAndTimeArrivedCommonEventBody eventBody,
            string workflowId,
            string workflowVersionedId,
            string sleekflowCompanyId,
            string contactId,
            ContactDetail contactDetail,
            string? condition,
            string? workflowName,
            string origin)
            : base(
                eventBody,
                workflowId,
                workflowVersionedId,
                sleekflowCompanyId,
                contactId,
                contactDetail,
                condition,
                workflowName,
                origin)
        {}

    }

    public class CheckWorkflowContactEnrolmentConditionV2Output : Models.Internals.CheckWorkflowContactEnrolmentConditionV2Output
    {
        [JsonConstructor]
        public CheckWorkflowContactEnrolmentConditionV2Output(bool enrolmentConditionSatisfied)
            : base(enrolmentConditionSatisfied)
        {
        }
    }


    public async Task<CheckWorkflowContactEnrolmentConditionV2Output> F(
        CheckWorkflowContactEnrolmentConditionV2Input input)
    {
        _logger.LogInformation(
            "Condition: {Condition}, EventBody: {EventBody}, WorkflowName: {WorkflowName}, SleekflowCompanyId: {SleekflowCompanyId}, FlowHubConfig: {FlowHubConfig}",
            input.Condition,
            input.EventBody,
            input.WorkflowName,
            input.SleekflowCompanyId,
            input.Origin);

        bool enrollmentConditionSatisfied = true;
        if (input is { Condition: not null, WorkflowName: not null })
        {
            enrollmentConditionSatisfied = await _flowHubEventEvaluator.EvaluateConditionAsync(
                input.Condition,
                input.EventBody,
                input.SleekflowCompanyId,
                input.Origin);
        }
        _logger.LogInformation("[CheckWorkflowContactEnrolmentConditionV2]: Evaluation result: {EnrollmentConditionSatisfied}", enrollmentConditionSatisfied);
        return new CheckWorkflowContactEnrolmentConditionV2Output(enrollmentConditionSatisfied);
    }
}