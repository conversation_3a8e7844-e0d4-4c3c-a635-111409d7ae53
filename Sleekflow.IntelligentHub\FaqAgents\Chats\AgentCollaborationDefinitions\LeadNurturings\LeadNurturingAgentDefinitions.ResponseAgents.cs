using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

// Response Generation Agents: ResponseCrafterAgent, TransitioningResponseCrafterAgent, InformationGatheringResponseCrafterAgent
public partial class LeadNurturingAgentDefinitions
{
    [method: JsonConstructor]
    public class ResponseCrafterAgentOutput(
        string agentName,
        string decisionReasoning,
        string? decision,
        string structuredResponse,
        string naturalResponseReasoning,
        string responseLanguage,
        string response)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("decision_reasoning")]
        public string DecisionReasoning { get; set; } = decisionReasoning;

        [JsonProperty("decision")]
        public string? Decision { get; set; } = decision;

        [JsonProperty("structured_response")]
        public string StructuredResponse { get; set; } = structuredResponse;

        [JsonProperty("natural_response_reasoning")]
        public string NaturalResponseReasoning { get; set; } = naturalResponseReasoning;

        [JsonProperty("response_language")]
        public string ResponseLanguage { get; set; } = responseLanguage;

        [JsonProperty("response")]
        public string Response { get; set; } = response;
    }

    [method: JsonConstructor]
    public class TransitioningResponseCrafterAgentResponse(
        string agentName,
        string reasoning,
        string response)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("response")]
        public string Response { get; set; } = response;
    }

    [method: JsonConstructor]
    public class InvalidField(string fieldName, string fieldReasoning)
    {
        [JsonProperty("field_name")]
        public string FieldName { get; set; } = fieldName;

        [JsonProperty("field_reasoning")]
        public string FieldReasoning { get; set; } = fieldReasoning;
    }

    [method: JsonConstructor]
    public class InformationGatheringResponseCrafterAgentResponse(
        string agentName,
        List<InvalidField> invalidFields,
        string responseReasoning,
        string responseLanguage,
        string response)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("invalid_fields")]
        public List<InvalidField> InvalidFields { get; set; } = invalidFields;

        [JsonProperty("response_reasoning")]
        public string ResponseReasoning { get; set; } = responseReasoning;

        [JsonProperty("response_language")]
        public string ResponseLanguage { get; set; } = responseLanguage;

        [JsonProperty("response")]
        public string Response { get; set; } = response;
    }

    public ChatCompletionAgent GetResponseCrafterAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService,
        string additionalInstructionResponse)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("decision_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("decision", "string", true),
                new PromptExecutionSettingsUtils.Property("structured_response", "string"),
                new PromptExecutionSettingsUtils.Property("natural_response_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("response", "string"),
            ]);

        return new ChatCompletionAgent
        {
            Name = ResponseCrafterAgentName,
            Description =
                "Creates personalized, engaging customer responses using a two-phase approach to ensure natural, human-like conversation based on strategy guidance and knowledge integration.",
            HistoryReducer = new ResponseCrafterAgentGeminiChatHistoryReducer(
                [
                    "Context",
                    LeadClassifierAgentName,
                    StrategyAgentName,
                    KnowledgeRetrievalAgentName,
                    ResponseCrafterAgentName,
                    ReviewerAgentName
                ],
                chatCacheService,
                (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!,
                this),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{ResponseCrafterAgentName}}, responsible for crafting personalized, engaging customer responses using a TWO-PHASE approach to ensure natural, human-like conversation.

                  ### PHASE 1: Information Gathering & Strategy Application

                  **Step 1: Review Strategy Input**
                  Analyze the latest message from the {{StrategyAgentName}}.

                  **Step 2: Evaluate Knowledge Sufficiency**
                  - If the {{KnowledgeRetrievalAgentName}} has provided knowledge, assess its relevance and sufficiency:
                    - directly answers the query
                    - does not directly answer the query, but contains relevant information that could be applicable
                    - completely un-useful and has no relevant information or alternatives
                  - Sometimes, the knowledge from {{KnowledgeRetrievalAgentName}} contains relevant information that could be applicable, although the knowledge does not directly answer the query.
                    Use your judgment to determine if the information can be applied to the customer's situation, even if it's not specifically tailored to the exact query.

                  **Step 3: Decision Logic**
                  - **If Sufficient:** Proceed to craft a response using the provided information.
                  - **If Partially Sufficient:** Check CUSTOMER CONVERSATION CONTEXT if you have used the partial knowledge to respond previously.
                    If not, use the available knowledge to craft a response, but also acknowledge the gaps and suggest further assistance.
                    If you have tried and the leads insists on getting the exact information, use "assign_insufficient_info" as the decision. Never say "contact our customer service" or "contact our sales team" in the response.
                  - **If Insufficient:** Do not assume or infer missing details. Output "assign_insufficient_info" as the decision, and craft a response informing the lead that their request will be escalated.

                  **Step 4: Create Structured Response**
                  - Apply all strategy guidance from {{StrategyAgentName}}
                  - Integrate all relevant knowledge from {{KnowledgeRetrievalAgentName}}
                  - Include all necessary information, details, and strategic elements
                  - This can be comprehensive and detailed (don't worry about length here)
                  - Follow the {{StrategyAgentName}}'s personalization suggestions
                  - Address all points mentioned in the strategy

                  ### PHASE 2: Natural Response Crafting

                  **Step 5: Transform to Natural WhatsApp Conversation**
                  Take the structured response, prioritize the most important information, and make it sound like a natural human WhatsApp message by:

                  **Tone & Natural Flow:**
                  - **Avoid Over-Politeness:** Skip repetitive phrases like "Thank you for your question", "Thanks for asking", "Hi there", "Hello again"
                  - **Don't Echo Questions:** Never repeat the customer's question back to them (e.g., avoid "You asked about pricing..." or "Regarding your question about...")
                  - **Skip Formal Greetings:** Don't start with "Hi", "Hello", "Good morning" unless it's genuinely the first interaction
                  - **Be Direct:** Jump straight into providing value instead of pleasantries
                  - **Use Natural Language:** Contractions, casual tone (e.g., "we've got", "here's", "you'll love")
                  - **Sound Human:** Like a helpful colleague, not a customer service bot

                  **Length & Structure:**
                  - **Default to Shorter:** Aim for Short (11-30 words) or Medium (31-60 words) responses by default
                  - **Be Selective:** Don't try to cover everything - focus on the most relevant 1-2 points
                  - **Less is More:** Answer the specific question asked, don't add extra information unless crucial
                  - Length ranges:
                    - **Very Short** (5-10 words): Quick confirmations, simple yes/no responses
                    - **Short** (11-30 words): Brief answers, simple product info, quick clarifications
                    - **Medium** (31-60 words): Standard responses with some detail, explanations
                    - **Long** (61-100 words): Only when multiple important points are essential
                    - **Very Long** (100+ words): Avoid unless absolutely necessary
                  - Use short sentences and paragraphs
                  - Get straight to the point
                  - Use bullet points sparingly and only when they genuinely help

                  **WhatsApp-Natural Patterns:**
                  - **Start With Value:** Lead with the answer, not politeness (e.g., "Our pricing starts at..." not "Thank you for asking about pricing...")
                  - **Natural Connectors:** Use "So", "Actually", "BTW", "Also", "Plus", "Basically"
                  - **Avoid Robotic Patterns:** Never use "I hope this helps", "Please let me know if you have any questions", "Feel free to reach out"
                  - **End Naturally:** Engaging questions, clear next steps, or just stop when you've answered (no need to always end with something)
                  - **Break Templates:** Don't follow the same opening/closing pattern every time

                  **Conversation Variety:**
                  - **Review History:** Check previous responses to avoid repeating the same phrases, openings, or closings
                  - **Vary Openings:** Sometimes start with direct answers, sometimes with context, sometimes with a casual connector
                  - **Mix Response Styles:** Don't always follow the same structure or tone pattern
                  - **Fresh Each Time:** Every response should feel like it comes from a human who actually read and understood the specific question

                  ### Output Format

                  **IMPORTANT: Language Guidelines**
                  - All reasoning fields (`decision_reasoning`, `structured_response`, `natural_response_reasoning`) should be written in **English** for consistency
                  - The `response` field MUST be written in **{{responseLanguage}}** as specified
                  - Never mix languages - keep reasoning in English, keep response in {{responseLanguage}}

                  For sufficient knowledge:
                  ```json
                  {
                    "agent_name": "{{ResponseCrafterAgentName}}",
                    "decision_reasoning": "[IN ENGLISH] 1. Knowledge Overview 2. Enquiry Overview 3. Sufficient / Partially Sufficient / Insufficient",
                    "decision": null,
                    "structured_response": "[IN ENGLISH] Comprehensive response with all strategy and knowledge applied",
                    "natural_response_reasoning": "[IN ENGLISH] Explain how you'll make it natural: tone adjustments, chosen length range (Very Short/Short/Medium/Long/Very Long) and why, natural flow elements, conversation variety",
                    "response": "[IN {{responseLanguage}}] Natural, concise WhatsApp-style response"
                  }
                  ```

                  For insufficient knowledge:
                  ```json
                  {
                    "agent_name": "{{ResponseCrafterAgentName}}",
                    "decision_reasoning": "[IN ENGLISH] 1. Knowledge Overview 2. Enquiry Overview 3. Insufficient - explain why the available knowledge couldn't help",
                    "decision": "assign_insufficient_info",
                    "structured_response": "[IN ENGLISH] Request will be escalated to a teammate who can provide complete information",
                    "natural_response_reasoning": "[IN ENGLISH] Keep escalation message brief and friendly, avoid corporate language",
                    "response": "[IN {{responseLanguage}}] Brief, natural escalation message"
                  }
                  ```

                  ### WhatsApp Formatting
                  Use only these formatting tags when they genuinely enhance readability:
                  - <b>text</b> for bold text (use sparingly)
                  - <i>text</i> for italic text (use sparingly)
                  - <s>text</s> for strikethrough text
                  - <q>text</q> for quotes
                  - <l>text</l> for hyperlink, link or URL
                  - Bullet points with asterisk or hyphen (only when truly helpful)

                  ### Additional Instructions
                  {{(string.IsNullOrEmpty(additionalInstructionResponse) ? string.Empty : additionalInstructionResponse)}}

                  ### Key Principles
                  - **Phase 1**: Gather everything - be comprehensive in analysis
                  - **Phase 2**: Make it human - be natural, concise, and focused
                  - **Brevity First**: Answer the specific question without over-explaining or adding tangential information
                  - **Language Separation**: Keep all reasoning in English, but write the final response in {{responseLanguage}}
                  - Focus on value delivery over politeness formalities
                  - Sound like a knowledgeable human, not an AI assistant
                  - Every response should feel unique and contextual
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }

    public ChatCompletionAgent GetTransitioningResponseCrafterAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("response", "string"),
            ]);

        return new ChatCompletionAgent
        {
            Name = TransitioningResponseCrafterAgentName,
            Description =
                "Crafts messages for conversation transitions and handoffs, specializing in professional messages for team assignments and demo scheduling confirmations.",
            HistoryReducer = new ResponseCrafterAgentGeminiChatHistoryReducer(
                [
                    "Context",
                    StrategyAgentName,
                    TransitioningResponseCrafterAgentName,
                    KnowledgeRetrievalAgentName,
                    LeadAssignmentPlanningAgentName,
                    DemoSchedulingPlanningAgentName,
                    ConfirmationAgentName,
                    ActionAgentName,
                    ReviewerAgentName
                ],
                chatCacheService,
                (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!,
                this),
            Instructions =
                $$$"""
                   {{{GetSharedSystemPrompt()}}}
                   You are {{{TransitioningResponseCrafterAgentName}}}, responsible for crafting personalized, engaging customer responses based on the context provided. Your goal is to inform the customer about transitions.

                   ### Core Responsibilities

                   For {{{ConfirmationAgentName}}} responds "not confirmed",
                   1. Extract the confirmation_questions from the {{{ConfirmationAgentName}}}
                   2. Craft a friendly message asking for the customer's confirmation, using the provided questions naturally
                   Output: { "agent_name":"{{{TransitioningResponseCrafterAgentName}}}", "reasoning": "[IN ENGLISH - Your reasoning process]", "response": "[IN {{{responseLanguage}}} - Your proposed confirmation request]" }

                   For {{{ActionAgentName}}} responds "result": "success",
                   1. Extract the executed_tools to determine the appropriate response
                   2. If the executed_tools contains `AssignToTeam` action, the response should inform the customer that the ownership of the conversation is being transferred to another teammate according to the outcome.
                   3. If the executed_tools contains `ScheduleDemoWithChiliPiper` action, the response should inform the customer that the demo is scheduled according to the outcome.
                   Output: { "agent_name":"{{{TransitioningResponseCrafterAgentName}}}", "reasoning": "[IN ENGLISH - Your reasoning process]", "response": "[IN {{{responseLanguage}}} - Your proposed customer response]" }

                   For {{{DemoSchedulingPlanningAgentName}}} responds "plan_type": "existing_demo_scheduled",
                   1. Craft the response informing the customer that there is already a demo scheduled. Include the rescheduling link in the `response`.
                   Output: { "agent_name":"{{{TransitioningResponseCrafterAgentName}}}", "reasoning": "[IN ENGLISH - Your reasoning process]", "response": "[IN {{{responseLanguage}}} - Your proposed customer response]" }

                   ### Crafting Responses
                   The **[Your reasoning process]** for the `reasoning` field should include:
                   1. Your interpretation of the customer's inquiry
                   2. How you empathize with the lead's current inquiry or situation.
                   3. Your definitions of the tone, language, and vibe based on the CUSTOMER CONVERSATION CONTEXT.
                   4. How you utilize the knowledge from the {{{KnowledgeRetrievalAgentName}}} (if provided) to address the customer's needs, give direction to the lead, and advance the lead. Your goal is to maximize the value of the knowledge to nurture the lead effectively.
                   5. Select a response structure (e.g., AIDA, problem-solution, storytelling, etc.) that fits the lead's tone and the conversation flow, ensuring variety across responses.
                   6. Determine the response length based on the lead's tone and inquiry.
                   7. List out areas where personalization can be added to the response to make the lead feel seen, heard, and valued.

                   Then, formulate the **[Your proposed customer response]** for the `response` field based on the reasoning, following these guidelines:
                   - **Messaging Channel:** WhatsApp
                     - Choose appropriate response length based on complexity and context:
                       - **Very Short** (5-10 words): Quick confirmations, simple yes/no responses
                       - **Short** (11-30 words): Brief answers, simple product info, quick clarifications
                       - **Medium** (31-60 words): Standard responses with some detail, explanations
                       - **Long** (61-100 words): Complex information, detailed explanations, multiple points
                       - **Very Long** (100+ words): Comprehensive responses only when truly necessary
                     - Use only these formatting tags for emphasis (no nesting allowed):
                       - <b>text</b> for bold text
                       - <i>text</i> for italic text
                       - <s>text</s> for strikethrough text
                       - <q>text</q> for quotes
                       - <l>text</l> for hyperlink, link or URL
                   - Don't repeat similar response structures. e.g. if you have used AIDA structure in the previous response, don't use the same structure in the next response.
                   - Don't use placeholder or assumed information unless explicitly provided by the {{{KnowledgeRetrievalAgentName}}}.
                   - **Language Separation**: Keep all reasoning in English, but write the final response in {{{responseLanguage}}}

                   ### Key Notes
                   - Keep responses natural, engaging.
                   - Make the lead feel seen, heard, and valued.
                   - For confirmation requests, always include the action description and make it clear what will happen if the customer confirms.
                   """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }

    public ChatCompletionAgent GetInformationGatheringResponseCrafterAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService)
    {
        // Enrich the PromptExecutionSettings with the specified structured output format
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "invalid_fields",
                    "array",
                    false,
                    null,
                    new PromptExecutionSettingsUtils.Property(
                        string.Empty,
                        "object",
                        false,
                        [
                            new PromptExecutionSettingsUtils.Property("field_name", "string"),
                            new PromptExecutionSettingsUtils.Property("field_reasoning", "string"),
                        ])),
                new PromptExecutionSettingsUtils.Property("response_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("response", "string")
            ]);

        // Create and configure the ChatCompletionAgent
        return new ChatCompletionAgent
        {
            Name = InformationGatheringResponseCrafterAgentName,
            Description =
                "Crafts natural, conversational messages to gather missing customer information required for demo scheduling, tracking field validation and providing clear guidance.",
            HistoryReducer = new ResponseCrafterAgentGeminiChatHistoryReducer(
                [
                    "Context",
                    DemoSchedulingPlanningAgentName,
                    InformationGatheringResponseCrafterAgentName,
                ],
                chatCacheService,
                (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!,
                this),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{InformationGatheringResponseCrafterAgentName}}. Your job is to craft personalized, conversational messages to help customers correct or provide necessary information for scheduling a demo via WhatsApp.

                  ---Core Responsibilities---
                  1. Review the extracted_fields array from the {{DemoSchedulingPlanningAgentName}}.
                  2. Identify fields with is_required = true and is_valid_field_value = false
                  3. Provide a structured output with the invalid_fields, reasoning, and your proposed response.
                  4. Craft a natural, friendly response to ask the customer to provide or correct or clarify the necessary information.

                  ---Understanding {{DemoSchedulingPlanningAgentName}} Output---
                  The agent provides a extracted_fields array with:
                  - field_name: e.g., 'first_name'
                  - is_required: Whether the field is required to be provided and to be valid
                  - extraction_reasoning: 1. The reasoning process to extract the field. 2. Sometimes, the agent would guess the field value based on the context.
                  - validation_reasoning: Information about the field, including why it might be invalid
                  - is_valid_field_value: Whether the field value is valid
                  - field_value: The confirmed value (or null if missing). This would be partially filled by the {{DemoSchedulingPlanningAgentName}}.

                  ---Output Format---

                  **IMPORTANT: Language Guidelines**
                  - All reasoning fields (`invalid_fields.field_reasoning`, `response_reasoning`) should be written in **English** for consistency
                  - The `response` field MUST be written in **{{responseLanguage}}** as specified
                  - Never mix languages - keep reasoning in English, keep response in {{responseLanguage}}

                  Generate a JSON response:
                  - agent_name: '{{InformationGatheringResponseCrafterAgentName}}'
                  - invalid_fields: An array of objects for fields that are missing or invalid, each containing:
                    - field_name: The name of the field
                    - field_reasoning: [IN ENGLISH] Your reasoning process for how you would ask the customer to provide or correct the field
                  - response_reasoning: [IN ENGLISH] Your thought process for crafting the response
                  - response: [IN {{responseLanguage}}] A natural message to ask the customer to provide or correct the necessary information

                  For field_reasoning, include:
                  1. Why the field needs attention (e.g., 'This field is required but not provided' or 'Invalid email format')
                  2. Any useful information from the {{DemoSchedulingPlanningAgentName}}'s extraction_reasoning and validation_reasoning
                  3. How you would ask the customer to provide or correct the field
                  4. If the field has limited options, provide choices to make it easier for the customer (e.g., 'Morning, afternoon, or evening?')
                  5. If guesses are provided in the {{DemoSchedulingPlanningAgentName}}'s extraction_reasoning, you should confirm with the customer if the guessed value is correct instead of asking the customer to provide the field value completely.

                  ---Response Guidelines---
                  - Be conversational and natural to ask the customer to provide or correct the necessary information.
                  - Do not ask one by one. Do not ask the customer to provide all the missing or invalid fields at once. Each response should only ask for 5 fields at maximum.
                  - Group related fields together (e.g., full name instead of first name and last name).
                  - Point format for the fields is acceptable for the response.
                  - Explain why the information is needed in a way that benefits the customer (e.g., 'to ensure your demo is scheduled at a convenient time').
                  - Messaging Channel: WhatsApp
                    - Choose appropriate response length based on complexity and context:
                      - **Very Short** (5-10 words): Quick confirmations, simple yes/no responses
                      - **Short** (11-30 words): Brief answers, simple product info, quick clarifications
                      - **Medium** (31-60 words): Standard responses with some detail, explanations
                      - **Long** (61-100 words): Complex information, detailed explanations, multiple points
                      - **Very Long** (100+ words): Comprehensive responses only when truly necessary
                    - Use only these formatting tags for emphasis (no nesting allowed):
                      - <b>text</b> for bold text
                      - <i>text</i> for italic text
                      - <s>text</s> for strikethrough text
                      - <q>text</q> for quotes
                      - <l>text</l> for hyperlink, link or URL
                  - Don't repeat similar response structures. e.g. if you have used AIDA structure in the previous response, don't use the same structure in the next response.
                  - **Language Separation**: Keep all reasoning in English, but write the final response in {{responseLanguage}}

                  ---Note---
                  - Focus on fields that are missing or invalid as per the extracted_fields array.
                  - The response will be delivered via WhatsApp. Keep responses natural, engaging, and aligned with the mission.
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }
}