using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts.ShopifyCarts;

[TriggerGroup(ControllerNames.ShopifyCarts)]
public class GetShopifyCartStatistics
    : ITrigger<
        GetShopifyCartStatistics.GetShopifyCartStatisticsInput,
        GetShopifyCartStatistics.GetShopifyCartStatisticsOutput>
{
    private readonly IShopifyCartService _shopifyCartService;

    public GetShopifyCartStatistics(
        IShopifyCartService shopifyCartService)
    {
        _shopifyCartService = shopifyCartService;
    }

    public class GetShopifyCartStatisticsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_staff_team_id")]
        public string? SleekflowStaffTeamId { get; set; }

        [JsonProperty("conversion_status")]
        public string? ConversionStatus { get; set; }

        [Required]
        [JsonProperty("from_date_time")]
        public DateTimeOffset FromDateTime { get; set; }

        [Required]
        [JsonProperty("to_date_time")]
        public DateTimeOffset ToDateTime { get; set; }

        [JsonConstructor]
        public GetShopifyCartStatisticsInput(
            string sleekflowCompanyId,
            string? sleekflowStaffId,
            string? sleekflowStaffTeamId,
            string? conversionStatus,
            DateTimeOffset fromDateTime,
            DateTimeOffset toDateTime)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamId = sleekflowStaffTeamId;
            ConversionStatus = conversionStatus;
            FromDateTime = fromDateTime;
            ToDateTime = toDateTime;
        }
    }

    public class GetShopifyCartStatisticsOutput : ShopifyCartStatistics
    {
        [JsonConstructor]
        public GetShopifyCartStatisticsOutput(
            long count,
            decimal totalPrice)
            : base(
                count,
                totalPrice)
        {
        }
    }

    public async Task<GetShopifyCartStatisticsOutput> F(
        GetShopifyCartStatisticsInput getShopifyCartStatisticsInput)
    {
        var shopifyCartStatistics = await _shopifyCartService.GetShopifyCartStatisticsAsync(
            getShopifyCartStatisticsInput.SleekflowCompanyId,
            getShopifyCartStatisticsInput.SleekflowStaffId,
            getShopifyCartStatisticsInput.SleekflowStaffTeamId,
            getShopifyCartStatisticsInput.ConversionStatus,
            getShopifyCartStatisticsInput.FromDateTime,
            getShopifyCartStatisticsInput.ToDateTime);

        return new GetShopifyCartStatisticsOutput(
            shopifyCartStatistics.Count,
            shopifyCartStatistics.TotalPrice);
    }
}