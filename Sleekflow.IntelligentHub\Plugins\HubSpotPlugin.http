### HubSpot Plugin API Test Requests
### Replace YOUR_HUBSPOT_API_KEY with your actual HubSpot API key
### Replace email/phone values with actual test data

@baseUrl = https://api.hubapi.com
@apiKey = ""
@testEmail = <EMAIL>
@testPhone = +85261096623
@testContactId = 117564869786

### 1. Search for Contact by Email (used by FindContactIdAsync)
POST {{baseUrl}}/crm/v3/objects/contacts/search
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
    "filterGroups": [
        {
            "filters": [
                {
                    "propertyName": "email",
                    "operator": "EQ",
                    "value": "{{testEmail}}"
                }
            ]
        }
    ],
    "properties": [
        "firstname",
        "lastname",
        "email",
        "phone",
        "hs_object_id"
    ],
    "limit": 1
}

### 2. Search for Contact by Phone (used by FindContactIdAsync)
POST {{baseUrl}}/crm/v3/objects/contacts/search
Authorization: Bearer {{api<PERSON><PERSON>}}
Content-Type: application/json

{
    "filterGroups": [
        {
            "filters": [
                {
                    "propertyName": "phone",
                    "operator": "EQ",
                    "value": "{{testPhone}}"
                }
            ]
        }
    ],
    "properties": [
        "firstname",
        "lastname",
        "email",
        "phone",
        "hs_object_id"
    ],
    "limit": 1
}

### 3. Get Contact Properties (GetContactPropertiesAsync)
GET {{baseUrl}}/crm/v3/objects/contacts/{{testContactId}}?properties=firstname,lastname,email,phone,company,website,lifecyclestage,hubspot_owner_id&archived=false
Authorization: Bearer {{apiKey}}
Accept: application/json

### 4. Get Contact Meeting Associations (GetContactMeetingsAsync - Step 1)
GET {{baseUrl}}/crm/v4/objects/contacts/{{testContactId}}/associations/meetings
Authorization: Bearer {{apiKey}}
Accept: application/json

### 5. Get Meeting Details (GetContactMeetingsAsync - Step 2)
### Replace MEETING_ID with actual meeting ID from associations response
GET {{baseUrl}}/crm/v3/objects/meetings/MEETING_ID?properties=hs_meeting_title,hs_meeting_body,hs_meeting_start_time,hs_meeting_end_time,hubspot_owner_id,hs_internal_meeting_notes
Authorization: Bearer {{apiKey}}
Accept: application/json

### 6. Update Contact Properties (UpdateContactPropertiesAsync)
PATCH {{baseUrl}}/crm/v3/objects/contacts/{{testContactId}}
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
    "properties": {
        "firstname": "Updated First Name",
        "lastname": "Updated Last Name",
        "phone": "+1987654321",
        "company": "Updated Company Name",
        "website": "https://updated-website.com",
        "lifecyclestage": "customer"
    }
}

### 7. Update Contact Properties - Single Property Example
PATCH {{baseUrl}}/crm/v3/objects/contacts/{{testContactId}}
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
  "properties": {
    "latest_lead_entry_point": "Book a Demo",
    "latest_lead_entry_point_drill_down": "AgentFlow"
  }
}

### 8. Update Contact Properties - Custom Properties Example
PATCH {{baseUrl}}/crm/v3/objects/contacts/{{testContactId}}
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
    "properties": {
        "hs_lead_status": "NEW",
        "notes_last_contacted": "Contact updated via API test",
        "hs_content_membership_status": "active"
    }
}