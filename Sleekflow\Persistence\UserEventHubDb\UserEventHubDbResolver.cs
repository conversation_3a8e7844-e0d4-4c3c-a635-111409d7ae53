using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.UserEventHubDb;

public interface IUserEventHubDbResolver : IContainerResolver
{
}

public class UserEventHubDbResolver : IUserEventHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public UserEventHubDbResolver(IUserEventHubDbConfig userEventHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            userEventHubDbConfig.Endpoint,
            userEventHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}