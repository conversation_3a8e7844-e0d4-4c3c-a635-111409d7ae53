﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Services;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class UpdateObjectV2 : ITrigger
{
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;

    public UpdateObjectV2(
        IGoogleSheetsObjectService googleSheetsObjectService,
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsConnectionService googleSheetsConnectionService)
    {
        _googleSheetsObjectService = googleSheetsObjectService;
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
    }

    public class UpdateObjectV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("typed_ids")]
        [Required]
        [ValidateArray]
        public List<TypedId> TypedIds { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonConstructor]
        public UpdateObjectV2Input(
            string sleekflowCompanyId,
            string connectionId,
            List<TypedId> typedIds,
            string entityTypeName,
            Dictionary<string, object?> dict)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            TypedIds = typedIds;
            EntityTypeName = entityTypeName;
            Dict = dict;
        }
    }

    public class UpdateObjectV2Output
    {
    }

    public async Task<UpdateObjectV2Output> F(UpdateObjectV2Input updateObjectV2Input)
    {
        var connection = await _googleSheetsConnectionService.GetByIdAsync(
            updateObjectV2Input.ConnectionId,
            updateObjectV2Input.SleekflowCompanyId);

        var authentication =
            await _googleSheetsAuthenticationService.GetAsync(
                connection.AuthenticationId,
                updateObjectV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        await _googleSheetsObjectService.UpdateObjectAsync(
            authentication,
            updateObjectV2Input.TypedIds,
            updateObjectV2Input.EntityTypeName,
            updateObjectV2Input.Dict);

        return new UpdateObjectV2Output();
    }
}