using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class GetAllManagementWhatsappCloudApiWabas
    : ITrigger<
        GetAllManagementWhatsappCloudApiWabas.GetAllManagementWhatsappCloudApiWabasInput,
        GetAllManagementWhatsappCloudApiWabas.GetAllManagementWhatsappCloudApiWabasOutput>
{
    private readonly IWabaService _wabaService;

    public GetAllManagementWhatsappCloudApiWabas(IWabaService wabaService)
    {
        _wabaService = wabaService;
    }

    public class GetAllManagementWhatsappCloudApiWabasInput
    {
    }

    public class GetAllManagementWhatsappCloudApiWabasOutput
    {
        [JsonProperty("wabas")]
        public List<ManagementWabaDto> Wabas { get; set; }

        [JsonConstructor]
        public GetAllManagementWhatsappCloudApiWabasOutput(List<ManagementWabaDto> wabas)
        {
            Wabas = wabas;
        }
    }

    public async Task<GetAllManagementWhatsappCloudApiWabasOutput> F(
        GetAllManagementWhatsappCloudApiWabasInput input)
    {
        var wabas = await _wabaService.GetAllAsync();

        return new GetAllManagementWhatsappCloudApiWabasOutput(
            wabas.Select(w => new ManagementWabaDto(w))
                .ToList());
    }
}