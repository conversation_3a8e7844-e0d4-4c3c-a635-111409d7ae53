using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.DurablePayloads;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class StartOrchestratorAndSaveDurablePayload
{
    private readonly ILogger<StartOrchestratorAndSaveDurablePayload> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly HttpClient _httpClient;
    private readonly IAppConfig _appConfig;

    public StartOrchestratorAndSaveDurablePayload(
        ILogger<StartOrchestratorAndSaveDurablePayload> logger,
        IHttpClientFactory httpClientFactory,
        IAsyncPolicy<HttpResponseMessage> retryPolicy,
        IAppConfig appConfig)
    {
        _logger = logger;
        _retryPolicy = retryPolicy;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _appConfig = appConfig;
    }

    public class StartOrchestratorAndSaveDurablePayloadInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("orchestrator_function_name")]
        [Required]
        public string OrchestratorFunctionName { get; set; }

        [JsonProperty("orchestrator_input")]
        [Required]
        public object OrchestratorInput { get; set; }

        [JsonConstructor]
        public StartOrchestratorAndSaveDurablePayloadInput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string orchestratorFunctionName,
            object orchestratorInput)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowVersionedId = workflowVersionedId;
            OrchestratorFunctionName = orchestratorFunctionName;
            OrchestratorInput = orchestratorInput;
        }
    }

    [Function("StartOrchestratorAndSaveDurablePayload")]
    public async Task RunAsync(
        [ActivityTrigger]
        StartOrchestratorAndSaveDurablePayloadInput startAndSaveDurablePayloadInput,
        [DurableClient]
        DurableTaskClient client)
    {
        _logger.LogInformation(
            "Activity: Attempting to schedule new orchestration {OrchestratorFunctionName} with input {OrchestratorInput}",
            startAndSaveDurablePayloadInput.OrchestratorFunctionName,
            JsonConvert.SerializeObject(startAndSaveDurablePayloadInput.OrchestratorInput));

        var instanceId = await client.ScheduleNewOrchestrationInstanceAsync(
            startAndSaveDurablePayloadInput.OrchestratorFunctionName,
            startAndSaveDurablePayloadInput.OrchestratorInput);

        _logger.LogInformation(
            "Activity: Successfully scheduled {OrchestratorFunctionName} with Instance ID {InstanceId}. Now creating payload.",
            startAndSaveDurablePayloadInput.OrchestratorFunctionName,
            instanceId);

        // Create management payload for the newly started orchestrator
        var httpManagementPayload = client.CreateHttpManagementPayload(instanceId);

        var pollyContext = new Context();
        pollyContext["logger"] = _logger;

        var updateWorkflowDurablePayloadInput = new UpdateWorkflowDurablePayloadInput(
            startAndSaveDurablePayloadInput.SleekflowCompanyId,
            startAndSaveDurablePayloadInput.WorkflowVersionedId,
            new DurablePayload(
                httpManagementPayload.Id!,
                httpManagementPayload.StatusQueryGetUri!,
                httpManagementPayload.SendEventPostUri!,
                httpManagementPayload.TerminatePostUri!,
                httpManagementPayload.RewindPostUri!,
                httpManagementPayload.PurgeHistoryDeleteUri!,
                httpManagementPayload.RestartPostUri!));

        var updateWorkflowDurablePayloadInputJsonStr = JsonConvert.SerializeObject(updateWorkflowDurablePayloadInput);

        var resMsg = await _retryPolicy.ExecuteAsync(
            async (context) =>
            {
                var reqMsg = new HttpRequestMessage
                {
                    Method = HttpMethod.Post,
                    Content = new StringContent(
                        updateWorkflowDurablePayloadInputJsonStr,
                        Encoding.UTF8,
                        "application/json"),
                    RequestUri = new Uri(
                        _appConfig.FlowHubInternalsEndpoint + "/UpdateWorkflowDurablePayload"),
                    Headers =
                    {
                        {
                            "X-Sleekflow-Key", _appConfig.InternalsKey
                        }
                    }
                };

                _logger.LogInformation("Sending request to FlowHub Internals: {RequestUri}", reqMsg.RequestUri);

                return await _httpClient.SendAsync(reqMsg);
            },
            pollyContext);

        resMsg.EnsureSuccessStatusCode();
        var resMsgContent = await resMsg.Content.ReadAsStringAsync();

        var updateWorkflowDurablePayloadOutput =
            JsonConvert.DeserializeObject<UpdateWorkflowDurablePayloadOutput>(resMsgContent)!;

        _logger.LogInformation(
            "Updated workflow {WorkflowVersionedId} durable payload with update result {IsUpdated}",
            updateWorkflowDurablePayloadOutput.WorkflowVersionedId,
            updateWorkflowDurablePayloadOutput.UpdateSuccess.ToString());
    }
}