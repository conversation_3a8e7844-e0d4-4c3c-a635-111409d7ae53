using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Products;

public interface IProductRepository : IDynamicFiltersRepository<Product>
{
    Task<int> GetCountAsync(
        string sleekflowCompanyId,
        int? limit = 10000,
        CancellationToken cancellationToken = default);
}

public class ProductRepository : DynamicFiltersBaseRepository<Product>, IProductRepository, IScopedService
{
    private readonly ILogger<ProductRepository> _logger;

    public ProductRepository(
        ILogger<ProductRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
        _logger = logger;
    }

    public async Task<int> GetCountAsync(
        string sleekflowCompanyId,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT VALUE COUNT(1)" +
                "FROM c " +
                "WHERE c.sleekflow_company_id = @sleekflowCompanyId " +
                "AND c.sys_type_name = @sys_type_name " +
                "AND ARRAY_CONTAINS(c.record_statuses, @record_status, true)")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@sys_type_name", SysTypeNames.Product)
            .WithParameter("@record_status", "Active");

        var container = GetContainer();
        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int>(
            qd,
            requestOptions: new QueryRequestOptions
            {
                MaxItemCount = limit > 1000 ? 2500 : limit,
                MaxBufferedItemCount = limit > 1000 ? 2500 : limit,
                MaxConcurrency = 4,
            });

        var count = 0;
        var objs = new List<int>();

        while (itemQueryIterator.HasMoreResults && count < limit)
        {
            var response = await itemQueryIterator.ReadNextAsync(cancellationToken);
            objs.AddRange(response);

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "GetCountAsync {QueryText} {RequestCharge}",
                    qd.QueryText,
                    response.RequestCharge);
            }

            count += response.Count;
        }

        return objs.Any() ? objs[0] : 0;
    }
}