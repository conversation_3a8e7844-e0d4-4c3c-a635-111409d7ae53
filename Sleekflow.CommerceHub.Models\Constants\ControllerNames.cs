namespace Sleekflow.CommerceHub.Models.Constants;

public static class ControllerNames
{
    public const string Blobs = "Blobs";
    public const string Categories = "Categories";
    public const string Carts = "Carts";
    public const string Currencies = "Currencies";
    public const string CustomCatalogs = "CustomCatalogs";
    public const string CustomCatalogConfigs = "CustomCatalogConfigs";
    public const string Internals = "Internals";
    public const string Languages = "Languages";
    public const string Orders = "Orders";
    public const string PaymentProviderConfigs = "PaymentProviderConfigs";
    public const string Payments = "Payments";
    public const string Products = "Products";
    public const string ProductVariants = "ProductVariants";
    public const string Stores = "Stores";
    public const string StripeWebhooks = "StripeWebhooks";
    public const string ShopifyStores = "ShopifyStores";
    public const string Shopify = "Shopify";
    public const string ShopifyOrders = "ShopifyOrders";
    public const string ShopifyCarts = "ShopifyCarts";
    public const string ShopifyProducts = "ShopifyProducts";
    public const string Vtex = "Vtex";
    public const string InflowActions = "InflowActions";
}