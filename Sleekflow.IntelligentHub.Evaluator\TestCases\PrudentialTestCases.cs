using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public class PrudentialTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetPrudentialTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Prudential/",
            SleekflowCompanyId: "577da285-4d3d-410a-a40b-5c1857d1516a");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢店舖保險中關於貨品存貨嘅保障限額及季節性調整",
            [
                new ChatMessageContent(AuthorRole.User, "請問你哋嘅店舖保險對貨品存貨有冇保障？保障限額係點計？季節性會唔會有調整？")
            ],
            "PRUChoice Shop Insurance 對店舖嘅貨品存貨提供保障，分為一般貨品同樣本及貿易存貨兩種。你可以分別為呢兩類貨品選擇投保金額。特別係喺每年12月1號至翌年3月1號嘅旺季，樣本及貿易存貨嘅索償限額會自動增加20%，而且呢個增加唔會額外收費，方便你應付旺季存貨增加嘅需要。",
            ["1eeea42b-d6e9-731f-b61a-d0a670bc5e31-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶想了解店舖招牌嘅保障範圍同賠償上限",
            [
                new ChatMessageContent(AuthorRole.User, "我想問下你哋嘅保險會唔會保障我嘅霓虹燈招牌？如果招牌壞咗或者損失，賠償有冇上限？")
            ],
            "係，PRUChoice Shop Insurance 會保障你嘅霓虹燈招牌或招牌嘅損失或損壞，賠償最高可達每年港幣10,000元，用於修理或更換招牌。另外，因為招牌引致嘅法律責任（例如第三者身體受傷或財物損失），保險亦會提供最高港幣1,000,000元嘅賠償保障。",
            ["1eeea42b-d6e9-731f-b61a-d0a670bc5e31-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢店舖保險嘅營業中斷保障及人身意外保障詳情",
            [
                new ChatMessageContent(AuthorRole.User, "如果我嘅店舖因意外要停業，保險會點保障我？另外，員工喺搶劫時受傷，保險又有冇保障？")
            ],
            "PRUChoice Shop Insurance 會保障因意外導致嘅營業中斷，賠償你恢復正常營業所需嘅相關開支，幫你減輕停業期間嘅損失。此外，保險亦提供人身意外保障，若你或員工喺搶劫或盜竊事件中受傷或致殘，會有相應嘅賠償，保障你嘅員工安全同生活。",
            ["1eeea42b-d6e9-731f-b61a-d0a670bc5e31-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "用戶想了解點樣喺myPrudential更改壽險保單嘅保費分配",
            [
                new ChatMessageContent(AuthorRole.User, "我想問下點樣喺myPrudential度更改我嘅壽險保單保費分配？有冇步驟講解？")
            ],
            "你可以喺myPrudential更改壽險保單嘅保費分配。步驟如下：\n" +
            "1. 登入myPrudential，揀選你想更改保費分配嘅壽險保單。\n" +
            "2. 喺功能選單揀「Change premium allocation」(更改保費分配)。\n" +
            "3. 如果你嘅風險評估問卷超過1年未更新，系統會提示你重新完成，或者用最新結果。\n" +
            "4. 選擇你想轉入或轉出嘅投資選擇，最多可以揀10個投資選擇。\n" +
            "5. 輸入新嘅保費分配百分比，總和必須係100%。\n" +
            "6. 確認資料無誤後，系統會發送一次性密碼(OTP)到你嘅手機，輸入後提交申請。\n" +
            "7. 申請成功後，你會收到確認通知。\n" +
            "例如：你可以將部分保費由Fidelity America Fund轉到Barings ASEAN Frontiers Fund，分配比例可以係20%同50%。",
            ["46b9bae9-84a4-7128-b1c6-d5d00cbb792b-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "用戶想知更改保費分配時，投資選擇同風險評估嘅關係",
            [
                new ChatMessageContent(AuthorRole.User, "更改保費分配時，如果我揀嘅投資選擇同我嘅風險評估唔啱，系統會點？")
            ],
            "當你揀嘅投資選擇同你嘅風險評估唔匹配，或者需要更多投資知識，系統會彈出提醒訊息，問你係咪確定繼續。如果你繼續，系統可能會要求你提供唔匹配嘅理由，特別係涉及衍生產品嘅時候。你亦可以選擇「Select Again」重新揀符合你風險評估嘅投資選擇。這樣做係為咗保障你嘅投資安全同合規。",
            ["46b9bae9-84a4-7128-b1c6-d5d00cbb792b-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "用戶查詢更改保費分配嘅處理時間同通知方式",
            [
                new ChatMessageContent(AuthorRole.User, "我想知如果我今日提交更改保費分配嘅指示，幾時會處理？完成後會點通知我？")
            ],
            "如果你喺香港時間工作日晚上7點前提交更改保費分配嘅指示，系統會喺當日處理。完成後，除咗會有確認信之外，你可以揀擇用電郵、SMS或者兩者都收到通知。例如電郵可以係 <EMAIL>，SMS會發送到你嘅手機號碼85262363679。咁你就可以即時知道申請狀態。",
            ["46b9bae9-84a4-7128-b1c6-d5d00cbb792b-0.md"]);
    }
}