﻿using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas.Dtos;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;

public interface IArrayObjectPropertyHook : IPropertyHook
{
}

public class ArrayObjectPropertyHook : IArrayObjectPropertyHook, ISingletonService
{
    private readonly IDataTypeInnerSchemaService _dataTypeInnerSchemaService;

    public ArrayObjectPropertyHook(IDataTypeInnerSchemaService dataTypeInnerSchemaService)
    {
        _dataTypeInnerSchemaService = dataTypeInnerSchemaService;
    }

    public (IDataType DataType, List<Option>? Options) PreConstruct(PropertyInput propertyInput)
    {
        var receivedInnerSchema = propertyInput.DataType.GetInnerSchema();
        var innerSchema = _dataTypeInnerSchemaService.Create(receivedInnerSchema.Properties);

        return (new ArrayObjectDataType(innerSchema), null);
    }

    public (IDataType DataType, List<Option>? Options) PreConstruct(Property property)
    {
        var receivedInnerSchema = property.DataType.GetInnerSchema();
        var innerSchema = _dataTypeInnerSchemaService.Create(receivedInnerSchema.Properties);

        return (new ArrayObjectDataType(innerSchema), null);
    }

    public UpdatePropertyChangeContext PreUpdate(Property originalProperty, Property receivedProperty)
    {
        var originalInnerSchema = originalProperty.DataType.GetInnerSchema();
        var receivedInnerSchema = receivedProperty.DataType.GetInnerSchema();

        _dataTypeInnerSchemaService.Update(originalInnerSchema, receivedInnerSchema);

        return new UpdatePropertyChangeContext(
            new List<string>(),
            NeedReindexPropertyValues(originalProperty, receivedProperty));
    }

    private static bool NeedReindexPropertyValues(Property originalProperty, Property receivedProperty)
        => originalProperty.IsSearchable != receivedProperty.IsSearchable;
}