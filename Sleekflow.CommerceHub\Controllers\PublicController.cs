﻿using Microsoft.AspNetCore.Mvc;
using Sleekflow.Mvc.Func;

namespace Sleekflow.CommerceHub.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private readonly IFuncService _funcService;

    public PublicController(IFuncService funcService)
    {
        _funcService = funcService;
    }

    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }
}