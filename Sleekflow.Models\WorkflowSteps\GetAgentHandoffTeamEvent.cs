using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Events;

namespace Sleekflow.Models.WorkflowSteps;

public class GetAgentHandoffTeamEvent : AgentEventBase
{
    public string SleekflowCompanyId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public string? TeamCategories { get; set; }

    public GetAgentHandoffTeamEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        List<SfChatEntry> conversationContext,
        string? teamCategories)
        : base(aggregateStepId, proxyStateId, stackEntries)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ConversationContext = conversationContext;
        TeamCategories = teamCategories;
    }

    public class Response
    {
        [JsonProperty("team_recommendation")]
        public string TeamRecommendation { get; set; }

        [JsonConstructor]
        public Response(string teamRecommendation)
        {
            TeamRecommendation = teamRecommendation;
        }
    }
}
