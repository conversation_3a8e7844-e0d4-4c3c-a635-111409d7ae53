using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.TenantHub.Models.Constants;

namespace Sleekflow.DataMigrator.Utils;

public class TravisBackendHttpClient
{
    private readonly HttpClient _httpClient;
    private readonly TravisBackendClientConfig? _clientConfig;
    private string _secretKey;

    public TravisBackendHttpClient(TravisBackendClientConfig? config, HttpClient httpClient, string selectedHub)
    {
        _clientConfig = config;
        _httpClient = httpClient;
        _secretKey = config!.SecretKey[selectedHub];
    }

    private static string CreateAuth0EventToken(string secretKey)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(),
            Expires = DateTime.UtcNow.AddMinutes(5),
            Issuer = string.Empty,
            Audience = string.Empty,
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
                SecurityAlgorithms.HmacSha256Signature)
        };
        return tokenHandler.WriteToken(tokenHandler.CreateToken(tokenDescriptor));
    }

    public async Task<string?> SendRequestAsync(
        string requestPath,
        object data,
        string? location = null,
        bool silentMode = true)
    {
        try
        {
            var request = new HttpRequestMessage(HttpMethod.Post, _clientConfig!.Endpoint + requestPath)
            {
                Method = HttpMethod.Post,
                Headers =
                {
                    {
                        // Environment config value:   TRAVIS_BACKEND_AUTH0_SECRET
                        "X-Tenant-Hub-Authorization", CreateAuth0EventToken(_secretKey)
                    },
                    {
                        "X-Sleekflow-Location", location ?? _clientConfig!.Location
                    }
                },
                Content = new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadAsStringAsync();
        }
        catch (Exception e)
        {
            if (!silentMode)
            {
                Console.WriteLine($"Request {data} not found.");
            }

            return null;
        }
    }
}