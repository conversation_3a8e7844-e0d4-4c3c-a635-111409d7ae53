using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Verifications;
using Sleekflow.CommerceHub.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs;

[TriggerGroup(ControllerNames.CustomCatalogs)]
public class VerifyCustomCatalogCsv
    : ITrigger<VerifyCustomCatalogCsv.VerifyCustomCatalogCsvInput, VerifyCustomCatalogCsv.VerifyCustomCatalogCsvOutput>
{
    private readonly IProductService _productService;
    private readonly ILogger<VerifyCustomCatalogCsv> _logger;
    private readonly ICustomCatalogFileService _customCatalogFileService;

    public VerifyCustomCatalogCsv(
        IProductService productService,
        ILogger<VerifyCustomCatalogCsv> logger,
        ICustomCatalogFileService customCatalogFileService)
    {
        _logger = logger;
        _productService = productService;
        _customCatalogFileService = customCatalogFileService;
    }

    public class VerifyCustomCatalogCsvInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonConstructor]
        public VerifyCustomCatalogCsvInput(string sleekflowCompanyId, string blobId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
        }
    }

    public class VerifyCustomCatalogCsvOutput
    {
        [JsonProperty("is_within_limitations")]
        public bool IsWithinLimitations { get; set; }

        [JsonProperty("csv_records")]
        public List<CustomCatalogCsvRecord> CsvRecords { get; set; }

        [JsonProperty("limitations")]
        public List<CustomCatalogLimitation> Limitations { get; set; }

        [JsonConstructor]
        public VerifyCustomCatalogCsvOutput(
            bool isWithinLimitations,
            List<CustomCatalogCsvRecord> csvRecords,
            List<CustomCatalogLimitation> limitations)
        {
            IsWithinLimitations = isWithinLimitations;
            CsvRecords = csvRecords;
            Limitations = limitations;
        }
    }

    // Note: Currently Only Support Product Count
    public async Task<VerifyCustomCatalogCsvOutput> F(VerifyCustomCatalogCsvInput verifyCustomCatalogCsvInput)
    {
        var blobId = verifyCustomCatalogCsvInput.BlobId;
        var sleekflowCompanyId = verifyCustomCatalogCsvInput.SleekflowCompanyId;
        var csvRecords = new List<CustomCatalogCsvRecord>();
        var limitations = new List<CustomCatalogLimitation>();
        var productCsvRecordCount = await _customCatalogFileService.ReadCustomCatalogCsvLineCountAsync(blobId);
        csvRecords.Add(new CustomCatalogCsvRecord(SysTypeNames.Product, productCsvRecordCount));

        var (productTotalQuota, productCurrentUsage) =
            await _productService.GetCustomCatalogProductQuotasAsync(sleekflowCompanyId);
        var productLimitations = new CustomCatalogLimitation(
            SysTypeNames.Product,
            productTotalQuota,
            productCurrentUsage);

        limitations.Add(productLimitations);
        var isWithinLimitations = productLimitations.RemainingQuota - productCsvRecordCount >= 0;

        if (!isWithinLimitations)
        {
            var hasError = await _customCatalogFileService.DeleteCustomCatalogCsvAsync(blobId);
            _logger.LogInformation("Delete Custom Catalog Csv {Status}", hasError);
        }

        return new VerifyCustomCatalogCsvOutput(isWithinLimitations, csvRecords, limitations);
    }
}