﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex;

public record VtexCredential
{
    private readonly string _domain;

    [JsonProperty("domain")]
    public string Domain => _domain.TrimEnd('/');

    [JsonProperty("app_key")]
    public string AppKey { get; }

    [JsonProperty("app_token")]
    public string AppToken { get; }

    [JsonConstructor]
    public VtexCredential(string domain, string appKey, string appToken)
    {
        _domain = domain;
        AppKey = appKey;
        AppToken = appToken;
    }

    public override string ToString()
    {
        return $"Domain: {Domain}, AppKey: {AppKey}, AppToken: {AppToken}";
    }
}