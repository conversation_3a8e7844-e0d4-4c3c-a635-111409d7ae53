﻿using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.CrmHubConfigs;

public interface ICrmHubConfigRepository : IRepository<CrmHubConfig>
{
}

public class CrmHubConfigRepository : BaseRepository<CrmHubConfig>, ICrmHubConfigRepository, IScopedService
{
    public CrmHubConfigRepository(
        ILogger<CrmHubConfigRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}