using System.Globalization;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using CsvHelper;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator;

[Parallelizable(ParallelScope.Children)]
public partial class ReActEvalTest
{
    // AsyncLocal to hold the TestId for the current execution context
    internal static readonly AsyncLocal<string?> CurrentTestId = new ();

    // Score calculation and result tracking
    private static readonly OverallScore OverallScoreCalculator = new ();

    private static readonly List<(string TestId, string Scenario, ReActEvalQuestion TestCase, ReActEvalResult Result)>
        AllTestResults = [];

    [TestCaseSource(nameof(GetTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task EvaluateReActPerformanceTest(ReActEvalQuestion testCase)
    {
        var testId = Guid.NewGuid().ToString();
        ReActEvalTest.CurrentTestId.Value = testId; // Set the test ID in AsyncLocal
        ChatEvalTest.CurrentTestId.Value = testId; // Also set in ChatEvalTest.CurrentTestId for mock tools

        try
        {
            // Run the ReAct agent with the test case
            var fixture = new ReActEvalFixture();
            var result = await fixture.RunTestAsync(testCase, testId);

            // Store the result for reporting
            AllTestResults.Add((testId, testCase.Scenario, testCase, result));
            OverallScoreCalculator.AddReActEvalResult(result);

            // Print the result to the console
            PrintResults(testCase, testId, result);

            // Verify tool calls
            AssertToolCalls(testCase, testId);
        }
        finally
        {
            // Clear test state
            ReActEvalTest.CurrentTestId.Value = null;
            ChatEvalTest.CurrentTestId.Value = null;
            MockSleekflowToolsPlugin.ClearTestState(testId);
            MockChiliPiperPlugin.ClearTestState(testId);
            MockHubspotPlugin.ClearTestState(testId);
            MockInformationGatheringPlugin.ClearTestState(testId);
        }
    }

    private void PrintResults(
        ReActEvalQuestion testCase,
        string testId,
        ReActEvalResult result)
    {
        var sb = new StringBuilder();

        // Header information
        sb.AppendLine($"Test Id: {testId}");
        sb.AppendLine($"Scenario: {testCase.Scenario}");
        sb.AppendLine();

        // Elapsed time & score information
        sb.AppendLine("----------------------- Performance ------------------------");
        sb.AppendLine($"Elapsed Time: {result.ElapsedMilliseconds / 1000d}s");
        sb.AppendLine($"Score: {Math.Round(result.Score, 2)}");
        sb.AppendLine();

        // Instruction section
        sb.AppendLine("----------------------- Instruction ------------------------");
        sb.AppendLine($"{testCase.AgentConfig!.PromptInstruction!.AdditionalInstructionCore!}");
        sb.AppendLine();


        // Question section
        sb.AppendLine("----------------------- Question ------------------------");
        if (testCase.QuestionContexts is not null)
        {

            foreach (var chatMessageContent in testCase.QuestionContexts)
            {
                sb.AppendLine($"{chatMessageContent.Role}: {chatMessageContent.Content}");
            }
        }

        if (testCase.SfChatEntriesQuestionContexts is not null)
        {
            foreach (var chatMessageContent in testCase.SfChatEntriesQuestionContexts)
            {
                if (chatMessageContent.Bot is not null)
                {
                    sb.AppendLine($"Bot: {chatMessageContent.Bot}");
                }

                if (chatMessageContent.User is not null)
                {
                    sb.AppendLine($"User: {chatMessageContent.User}");
                }

                if (chatMessageContent.Files is not null)
                {
                    sb.AppendLine($"Files: {string.Join(", ", chatMessageContent.Files.Select(f => f.Url))}");
                }
            }
        }

        // Response section
        sb.AppendLine("----------------------- Response ------------------------");
        sb.AppendLine(result.Response);
        sb.AppendLine();

        // Tool calls section
        sb.AppendLine("----------------------- Tool Calls ------------------------");

        if (testCase.ExpectExtractInformation)
        {
            sb.AppendLine("Expected ExtractInformation: Yes");
            var extractedFields = MockInformationGatheringPlugin.GetExtractedFields(testId);
            if (extractedFields != null && extractedFields.Count > 0)
            {
                sb.AppendLine("  Extracted Fields:");
                foreach (var field in extractedFields)
                {
                    sb.AppendLine($"    {field.FieldName}: {field.FieldValue}");
                }
            }

            var contactProperties = MockSleekflowToolsPlugin.GetUpdateContactPropertiesParams(testId);
            if (contactProperties != null && contactProperties.Properties.Count > 0)
            {
                sb.AppendLine("  Updated Contact Properties:");
                foreach (var property in contactProperties.Properties)
                {
                    sb.AppendLine($"    {property.PropertyId}: {property.PropertyValue}");
                }
            }
        }
        else
        {
            sb.AppendLine("Expected ExtractInformation: No");
        }

        // Internal notes section
        if (testCase.ShouldAddInternalNote)
        {
            sb.AppendLine("Expected AddInternalNote: Yes");
            if (MockSleekflowToolsPlugin.WasAddInternalNoteCalled(testId))
            {
                var internalNoteParams = MockSleekflowToolsPlugin.GetAddInternalNoteParams(testId);
                if (internalNoteParams != null)
                {
                    sb.AppendLine($"  Internal Note Content: {internalNoteParams.Content}");
                }
            }
        }
        else
        {
            sb.AppendLine("Expected AddInternalNote: No");
        }

        // Output the entire result
        Console.Write(sb.ToString());
    }

    private void AssertToolCalls(ReActEvalQuestion testCase, string testId)
    {
        // Check if this test should send a message
        if (testCase.ShouldSendMessage)
        {
            // For message sending tests, verify SendMessageAsync was called
            Assert.That(
                MockSleekflowToolsPlugin.WasSendMessageAsyncCalled(testId),
                Is.True,
                $"SendMessageAsync should be called for test {testId} because ShouldSendMessage is true");

            // Verify message parameters
            var messageParams = MockSleekflowToolsPlugin.GetSendMessageAsyncParams(testId);
            Assert.That(messageParams, Is.Not.Null, "Message parameters should not be null");

            // Verify message is not empty
            Assert.That(
                !string.IsNullOrEmpty(messageParams.Message),
                Is.True,
                "Message content should not be empty");
        }

        // Verification for Information Gathering
        if (testCase.ExpectExtractInformation)
        {
            // Check if information extraction was performed
            Assert.That(
                MockInformationGatheringPlugin.WasExtractFieldsCalled(testId),
                Is.True,
                $"ExtractFields should be called for test {testId}");
        }
        else if (testCase.ExpectExtractInformation == false)
        {
            // Check that information extraction was not performed
            Assert.That(
                MockInformationGatheringPlugin.WasExtractFieldsCalled(testId),
                Is.False,
                $"ExtractFields should not be called for test {testId}");
        }

        // Verify updated properties if expected properties are defined
        if (testCase.ExpectedContactProperties != null)
        {
            // Check if contact properties were updated in Sleekflow
            Assert.That(
                MockSleekflowToolsPlugin.WasUpdateContactPropertiesCalled(testId),
                Is.True,
                $"UpdateContactProperties should be called for test {testId}");

            var contactProperties = MockSleekflowToolsPlugin.GetUpdateContactPropertiesParams(testId);
            Assert.That(contactProperties, Is.Not.Null, "Updated contact properties should not be null");

            // Check that all expected properties were updated
            foreach (var expectedProperty in testCase.ExpectedContactProperties)
            {
                var propertyUpdated = contactProperties.Properties.First(p =>
                    p.PropertyId == expectedProperty.Key);

                Assert.That(
                    propertyUpdated.PropertyValue,
                    Is.EqualTo(expectedProperty.Value));
            }
        }

        // Conditional validation for Hubspot contact properties update
        if (testCase.ExpectHubspotUpdate)
        {
            // Check if contact properties were updated in Hubspot
            Assert.That(
                MockHubspotPlugin.WasUpdateContactPropertiesCalled(testId),
                Is.True,
                $"Hubspot UpdateContactProperties should be called for test {testId}");

            // Verify Hubspot properties were updated
            var hubspotProperties = MockHubspotPlugin.GetUpdateContactPropertiesParams(testId);
            Assert.That(hubspotProperties, Is.Not.Null, "Updated Hubspot contact properties should not be null");
            Assert.That(
                hubspotProperties.Properties,
                Is.Not.Null.And.Not.Empty,
                "Hubspot properties list should not be empty");
        }

        // Conditional validation for contact labels
        if (testCase.ShouldAddLabels && testCase.ExpectedLabels != null)
        {
            // Check if contact labels were added
            Assert.That(
                MockSleekflowToolsPlugin.WasAddContactLabelsCalled(testId),
                Is.True,
                $"AddContactLabels should be called for test {testId}");

            // Verify expected labels were added
            var addedLabels = MockSleekflowToolsPlugin.GetAddContactLabelsParams(testId);
            Assert.That(addedLabels, Is.Not.Null, "Added contact labels should not be null");
            Assert.That(addedLabels.Labels, Is.Not.Null, "Added labels list should not be null");

            // Check that all expected labels were added
            foreach (var expectedLabel in testCase.ExpectedLabels)
            {
                Assert.That(
                    addedLabels.Labels,
                    Does.Contain(expectedLabel),
                    $"Expected label '{expectedLabel}' should be added for test {testId}. Added labels: [{string.Join(", ", addedLabels.Labels)}]");
            }

            Console.WriteLine(
                $"Label verification passed for test {testId}. Expected: [{string.Join(", ", testCase.ExpectedLabels)}], Added: [{string.Join(", ", addedLabels.Labels)}]");
        }

        // Conditional validation for internal notes
        if (testCase.ShouldAddInternalNote)
        {
            // Check if internal note was added
            Assert.That(
                MockSleekflowToolsPlugin.WasAddInternalNoteCalled(testId),
                Is.True,
                $"AddInternalNote should be called for test {testId}");

            // Verify internal note content if expected content is provided
            if (!string.IsNullOrEmpty(testCase.ExpectedInternalNoteContent))
            {
                var internalNoteParams = MockSleekflowToolsPlugin.GetAddInternalNoteParams(testId);
                Assert.That(internalNoteParams, Is.Not.Null, "Internal note parameters should not be null");
                Assert.That(
                    internalNoteParams.Content,
                    Does.Contain(testCase.ExpectedInternalNoteContent),
                    $"Internal note content should contain '{testCase.ExpectedInternalNoteContent}' for test {testId}. Actual content: '{internalNoteParams.Content}'");
            }

            Console.WriteLine($"Internal note verification passed for test {testId}");
        }
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        Console.WriteLine("Total score:");
        Console.WriteLine(await OverallScoreCalculator.GetReportString());

        // Generate CSV output
        var csvPath = Path.Combine(
            GetRootPath(),
            $"react_eval_test_results_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

        await using var writer = new StreamWriter(csvPath);
        await using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // Write test results
        var records = AllTestResults.Select(result =>
        {
            var (testId, scenario, testCase, evalResult) = result;

            return new ReActEvalTestResult
            {
                TestId = testId,
                Scenario = scenario,
                Instruction = testCase.AgentConfig!.PromptInstruction!.AdditionalInstructionCore!,
                Response = FormatText(evalResult.Response),
                ElapsedMilliseconds = evalResult.ElapsedMilliseconds,
                Score = evalResult.Score,
                ExtractInformationCalled = MockInformationGatheringPlugin.WasExtractFieldsCalled(testId),
                UpdateContactPropertiesCalled = MockSleekflowToolsPlugin.WasUpdateContactPropertiesCalled(testId),
                HubspotUpdateContactPropertiesCalled = MockHubspotPlugin.WasUpdateContactPropertiesCalled(testId),
                SendMessageAsyncCalled = MockSleekflowToolsPlugin.WasSendMessageAsyncCalled(testId),
                AddInternalNoteCalled = MockSleekflowToolsPlugin.WasAddInternalNoteCalled(testId)
            };
        });

        await csv.WriteRecordsAsync(records);
        Console.WriteLine($"\nTest results exported to: {csvPath}");
    }

    private string GetRootPath()
    {
        var exePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var appPathMatcher = new Regex(@"(?<!file)[A-Za-z]:\\+[\S\s]*?(?=\\+bin)");
        return appPathMatcher.Match(exePath).Value;
    }

    private static string FormatText(string text)
    {
        return text?.Replace("\r\n", " ").Replace("\n", " ") ?? string.Empty;
    }

    // Add a new method to create default ToolsConfig
    private static ToolsConfig GetDefaultToolsConfig(
        SleekflowSendMessageTool? sleekflowSendMessageTool = null,
        HubspotTool? hubspotTool = null)
    {
        return new ToolsConfig(
            sleekflowSendMessageTool: sleekflowSendMessageTool,
            hubspotTool: hubspotTool);
    }
}

// Test case model class
public record ReActEvalQuestion : ChatEvalConfig
{
    public string Scenario { get; init; }

    public ChatMessageContent[]? QuestionContexts { get; init; }

    public ReplyGenerationContext ReplyGenerationContext { get; init; }

    public CompanyAgentConfig AgentConfig { get; init; }

    public bool ExpectExtractInformation { get; init; }

    public Dictionary<string, string>? ExpectedContactProperties { get; init; }

    // Input property to store webhook payloads or other input data
    public string? Input { get; init; }

    // Whether this test should verify message sending
    public bool ShouldSendMessage { get; init; }

    // Whether this test should verify Hubspot contact properties update
    public bool ExpectHubspotUpdate { get; init; }

    // Whether this test should verify label addition
    public bool ShouldAddLabels { get; init; }

    // Expected labels to be added to the contact
    public List<string>? ExpectedLabels { get; init; }

    // Support for file-type conversations
    public SfChatEntry[]? SfChatEntriesQuestionContexts { get; init; }

    // Whether this test should verify internal note addition
    public bool ShouldAddInternalNote { get; init; }

    // Expected content of the internal note
    public string? ExpectedInternalNoteContent { get; init; }

    public ReActEvalQuestion(
        string scenario,
        ChatMessageContent[]? questionContexts,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig,
        bool expectExtractInformation = false,
        Dictionary<string, string>? expectedContactProperties = null,
        bool shouldSendMessage = false,
        bool expectHubspotUpdate = false,
        bool shouldAddLabels = false,
        List<string>? expectedLabels = null,
        SfChatEntry[]? sfChatEntriesQuestionContexts = null,
        bool shouldAddInternalNote = false,
        string? expectedInternalNoteContent = null)
        : base(string.Empty, replyGenerationContext.SleekflowCompanyId)
    {
        Scenario = scenario;
        QuestionContexts = questionContexts;
        ReplyGenerationContext = replyGenerationContext;
        AgentConfig = agentConfig;
        ExpectExtractInformation = expectExtractInformation;
        ExpectedContactProperties = expectedContactProperties;
        ShouldSendMessage = shouldSendMessage;
        ExpectHubspotUpdate = expectHubspotUpdate;
        ShouldAddLabels = shouldAddLabels;
        ExpectedLabels = expectedLabels;
        SfChatEntriesQuestionContexts = sfChatEntriesQuestionContexts;
        ShouldAddInternalNote = shouldAddInternalNote;
        ExpectedInternalNoteContent = expectedInternalNoteContent;
    }
}

// Result class for ReAct evaluations
public class ReActEvalResult
{
    public string Response { get; set; } = string.Empty;

    public long ElapsedMilliseconds { get; set; }

    public double Score { get; set; }

    public ReActEvalResult(string response, long elapsedMilliseconds, double score)
    {
        Response = response;
        ElapsedMilliseconds = elapsedMilliseconds;
        Score = score;
    }
}

// Result class for CSV export
public class ReActEvalTestResult
{
    public string TestId { get; set; } = string.Empty;

    public string Scenario { get; set; } = string.Empty;

    public string Instruction { get; set; } = string.Empty;

    public string Response { get; set; } = string.Empty;

    public long ElapsedMilliseconds { get; set; }

    public double Score { get; set; }

    public bool ExtractInformationCalled { get; set; }

    public bool UpdateContactPropertiesCalled { get; set; }

    public bool HubspotUpdateContactPropertiesCalled { get; set; }

    public bool SendMessageAsyncCalled { get; set; }

    public bool AddInternalNoteCalled { get; set; }
}