﻿using Newtonsoft.Json;

namespace Sleekflow.Models.ActionEvents.CrmHub;

/// <summary>
/// For FlowHub Action - Create Schemaful Object
/// </summary>
public class CreateSchemafulObjectRequest
{
    public string SchemaId { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string PrimaryPropertyValue { get; set; }

    public Dictionary<string, object?> PropertyValues { get; set; }

    public string SleekflowUserProfileId { get; set; }

    public string CreatedVia { get; set; }

    [JsonConstructor]
    public CreateSchemafulObjectRequest(
        string schemaId,
        string sleekflowCompanyId,
        string primaryPropertyValue,
        Dictionary<string, object?> propertyValues,
        string sleekflowUserProfileId,
        string createdVia)
    {
        SchemaId = schemaId;
        SleekflowCompanyId = sleekflowCompanyId;
        PrimaryPropertyValue = primaryPropertyValue;
        PropertyValues = propertyValues;
        SleekflowUserProfileId = sleekflowUserProfileId;
        CreatedVia = createdVia;
    }
}