using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class UpdateWabaConnectedProductCatalog
    : ITrigger<UpdateWabaConnectedProductCatalog.UpdateWabaConnectedProductCatalogInput,
        UpdateWabaConnectedProductCatalog.UpdateWabaConnectedProductCatalogOutput>
{
    private readonly ILogger<UpdateWabaConnectedProductCatalog> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public UpdateWabaConnectedProductCatalog(
        ILogger<UpdateWabaConnectedProductCatalog> logger,
        IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class UpdateWabaConnectedProductCatalogInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [JsonProperty("facebook_product_catalog_id")]
        public string? FacebookProductCatalogId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateWabaConnectedProductCatalogInput(
            string sleekflowCompanyId,
            string wabaId,
            string? facebookProductCatalogId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            FacebookProductCatalogId = facebookProductCatalogId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateWabaConnectedProductCatalogOutput
    {
        [JsonProperty("waba")]
        public WabaDto Waba { get; set; }

        [JsonConstructor]
        public UpdateWabaConnectedProductCatalogOutput(WabaDto waba)
        {
            Waba = waba;
        }
    }

    public async Task<UpdateWabaConnectedProductCatalogOutput> F(
        UpdateWabaConnectedProductCatalogInput connectWabasProductCatalogsInput)
    {
        _logger.LogInformation(
            "updating waba {WabaIds} with facebook product catalog {FacebookProductCatalog}",
            connectWabasProductCatalogsInput.WabaId,
            connectWabasProductCatalogsInput.FacebookProductCatalogId);

        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            connectWabasProductCatalogsInput.SleekflowStaffId,
            connectWabasProductCatalogsInput.SleekflowStaffTeamIds);

        var waba = await _productCatalogService.UpdateWabaConnectedFacebookProductCatalogAsync(
            connectWabasProductCatalogsInput.SleekflowCompanyId,
            connectWabasProductCatalogsInput.WabaId,
            connectWabasProductCatalogsInput.FacebookProductCatalogId,
            sleekflowStaff);

        return new UpdateWabaConnectedProductCatalogOutput(new WabaDto(waba));
    }
}