using MimeKit;
using Sleekflow.EmailHub.Models.Communications;

namespace Sleekflow.EmailHub.Services;

public interface IEmailCommunicationService
{
    Task OnReceiveEmailAsync(
        List<string> sleekflowCompanyIds,
        string emailAddress,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default);

    Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default);

    Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default);

    Task SyncAllEmailsAsync(
        string emailAddress,
        CancellationToken cancellationToken = default);
}