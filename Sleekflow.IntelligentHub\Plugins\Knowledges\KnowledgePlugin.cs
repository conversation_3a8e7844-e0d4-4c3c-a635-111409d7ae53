using System.ComponentModel;
using System.Text.Json;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OpenAI.Chat;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Caches;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.LightRags;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.LightRag;
using Sleekflow.IntelligentHub.Plugins.Models;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class KnowledgePluginResponse : QueryKnowledgeResponse
{
    public KnowledgePluginResponse(string knowledge, string id)
        : base(knowledge, id, "KnowledgeBase")
    {
    }
}

public interface IKnowledgePlugin
{
    [KernelFunction("query_knowledge")]
    [Description(
        "Retrieves information from the knowledge base.")]
    [return: Description(
        "Tailored information from the knowledge base.")]
    Task<QueryKnowledgeResponse> QueryKnowledgeAsync(
        Kernel kernel,
        [Description(
            "Express your information needs naturally, whether as direct questions, scenario descriptions, or specific information requests. Provide clear context, specific requirements, and any relevant background details for the most accurate and useful results. ")]
        string query);
}

public class KnowledgePlugin : IKnowledgePlugin, IScopedService
{
    private readonly ILogger _logger;
    private readonly ILightRagClient _lightRagClient;
    private readonly IKnowledgeRetrievalCacheService _knowledgeRetrievalCacheService;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public KnowledgePlugin(
        ILogger<KnowledgePlugin> logger,
        ILightRagClient lightRagClient,
        IKnowledgeRetrievalCacheService knowledgeRetrievalCacheService,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _lightRagClient = lightRagClient;
        _knowledgeRetrievalCacheService = knowledgeRetrievalCacheService;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("query_knowledge")]
    [Description(
        "Retrieves information from the knowledge base.")]
    [return: Description(
        "Tailored information from the knowledge base.")]
    public async Task<QueryKnowledgeResponse> QueryKnowledgeAsync(
        Kernel kernel,
        [Description(
            "Express your information needs naturally, whether as direct questions, scenario descriptions, or specific information requests. Provide clear context, specific requirements, and any relevant background details for the most accurate and useful results. ")]
        string query)
    {
        var sleekflowCompanyId = (string) kernel.Data[KernelDataKeys.SLEEKFLOW_COMPANY_ID]!;
        var agentId = (string) kernel.Data[KernelDataKeys.AGENT_ID]!;
        var id = Guid.NewGuid().ToString();

        var embedding = await _knowledgeRetrievalCacheService.GenerateEmbeddingAsync(query);
        var cacheRecord = await _knowledgeRetrievalCacheService.GetCacheRecordAsync(embedding, sleekflowCompanyId);
        if (cacheRecord is not null)
        {
            var cachedConfirmedKnowledge = cacheRecord.Result;

            _logger.LogInformation(
                "Final Confirmed Knowledge:\n" +
                "Status: {Status}\n" +
                "Query: {Query}\n" +
                "Knowledge: {ConfirmedKnowledge}",
                "Cached",
                query,
                cachedConfirmedKnowledge);

            return new KnowledgePluginResponse(
                cachedConfirmedKnowledge,
                id);
        }

        var (status, confirmedKnowledge) = await GetConfirmedKnowledgeAsync(kernel, query, sleekflowCompanyId, agentId);

        _logger.LogInformation(
            "Final Confirmed Knowledge:\n" +
            "Status: {Status}\n" +
            "Query: {Query}\n" +
            "Knowledge: {ConfirmedKnowledge}",
            status == GetConfirmedKnowledgeStatus.Success ? "Success" : "Error",
            query,
            confirmedKnowledge);

        // Do not cache empty results
        if (string.IsNullOrWhiteSpace(confirmedKnowledge)
            || status == GetConfirmedKnowledgeStatus.Error)
        {
            return new KnowledgePluginResponse(
                confirmedKnowledge,
                id);
        }

        // Upsert the confirmed knowledge to the plugin internal cache
        await _knowledgeRetrievalCacheService.UpsertAsync(query, confirmedKnowledge, sleekflowCompanyId, embedding);

        return new KnowledgePluginResponse(
            confirmedKnowledge,
            id);
    }

    private enum GetConfirmedKnowledgeStatus
    {
        Success,
        Error
    }

    private async Task<(GetConfirmedKnowledgeStatus Status, string ConfirmedKnowledge)> GetConfirmedKnowledgeAsync(
        Kernel kernel,
        string query,
        string sleekflowCompanyId,
        string agentId)
    {
        try
        {
            var (highLevelKeywords, lowLevelKeywords) = await _lightRagClient.GenerateKeywordsAsync(kernel, query);
            var getLightRagSearchResultRequest = new GetLightRagSearchResultRequest(
                query,
                LightRagQueryMode.Mix,
                LightRagClient.GetAgentKnowledgeBaseId(sleekflowCompanyId, agentId),
                true,
                highLevelKeywords,
                lowLevelKeywords,
                10);
            var getLightRagSearchResultResponse =
                await _lightRagClient.ExecuteAsync<GetLightRagSearchResultResponse>(
                    LightRagPaths.Query,
                    sleekflowCompanyId,
                    agentId,
                    getLightRagSearchResultRequest);
            var data = getLightRagSearchResultResponse?.Response ?? string.Empty;

            var jsonObj = JsonConvert.DeserializeObject<JObject>(data)!;
            var kgContext = jsonObj.GetValue("kg_context")!.Value<string>()!;
            var kb2Context = jsonObj.GetValue("vector_context")!.Value<string>()!;

            var (_, _, kb1Sources) = LightRagSectionExtractor.ExtractSections(kgContext);

            var synthesizeAllSectionsResponse = await SynthesizeAllSectionsAsync(
                kernel,
                query,
                kb1Sources,
                kb2Context);

            var deserializeObject =
                JsonConvert.DeserializeObject<Dictionary<string, object>>(synthesizeAllSectionsResponse)!;
            var unifiedSummary = (string) deserializeObject["unified_summary"];
            var coverage = (string) deserializeObject["coverage"];
            var difficulty = (long) deserializeObject["difficulty"];

            return (
                GetConfirmedKnowledgeStatus.Success,
                $"""
                 <CONFIRMED_KNOWLEDGE>
                 <UNIFIED_SUMMARY>
                 {unifiedSummary}
                 </UNIFIED_SUMMARY>

                 <COVERAGE>
                 {coverage}
                 </COVERAGE>
                 </CONFIRMED_KNOWLEDGE>
                 """);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting confirmed knowledge: {Error}", e.Message);

            return (
                GetConfirmedKnowledgeStatus.Error,
                $"""
                 <CONFIRMED_KNOWLEDGE>
                 - No confirmed knowledge found for the query: {query}.
                 </CONFIRMED_KNOWLEDGE>
                 """);
        }
    }

    private async Task<string> SynthesizeAllSectionsAsync(
        Kernel kernel,
        string query,
        string kb1Sources,
        string kb2Context)
    {
        var composedContext =
            $"""
             ====KB1 CONTEXT====
             {kb1Sources}
             ====KB1 CONTEXT====

             ====KB2 CONTEXT====
             {kb2Context}
             ====KB2 CONTEXT====
             """;

        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

#pragma warning disable SKEXP0070
        if (promptExecutionSettings is GeminiPromptExecutionSettings geminiPromptExecutionSettings)
        {
            // Define the JSON schema for the structured output
            var outputSchema =
                """
                {
                    "type": "object",
                    "required": ["unified_summary", "coverage", "difficulty"],
                    "properties": {
                        "unified_summary": {
                            "type": "string"
                        },
                        "coverage": {
                            "type": "string"
                        },
                        "difficulty": {
                            "type": "number"
                        }
                    }
                }
                """;

            geminiPromptExecutionSettings.ResponseMimeType = "application/json";
            geminiPromptExecutionSettings.ResponseSchema = JsonDocument.Parse(outputSchema);
        }
#pragma warning restore SKEXP0070

        var finalOutput = await SummarizeAsync(
            kernel,
            "SynthesizeAllSections",
            $$$"""
               Synthesize information from **multiple knowledge sources** to generate a comprehensive, factually accurate summary that addresses the user query. If critical information is unavailable in the direct domain of the query, provide the best available alternative and clearly note the differences between the alternative and the actual query intent. For example, if the query is about broadband data but the knowledge base only covers networking data plans (e.g., mobile data plans), include the mobile data plans as an alternative while explicitly clarifying how they differ from broadband data.

               ### REQUIREMENTS
               - **Integrate Verified Information**: Combine content from **all provided sources (KB1, KB2)** into a cohesive and unified summary.
               - **Logical Consistency & Factual Accuracy**: Cross-check information across sources to validate consistency and factual accuracy. Avoid speculative or fabricated content.
               - **Clear Source Attribution**: Reference sources explicitly using the format `[Source KBN-ID]` (e.g., `[Source KB1-7]`). Distinguish between sources accurately (e.g., Source 1 in KB1 ≠ Source 1 in KB2).
               - **Alternative Suggestions When Data is Incomplete**:
                   - If the query's precise domain lacks adequate data (e.g., broadband data), identify and suggest the best available alternative (e.g., mobile data plans within networking).
                   - Clearly state the difference between the suggested alternative and the original query criteria, ensuring clarity on why the suggestion is made.
               - **Address Knowledge Gaps**: If the user's query cannot be fully resolved due to missing or incomplete information:
                  - Clearly state that the query cannot be resolved fully.
                  - List the **specific knowledge gaps** identified.
                  - Provide a summary of the topics and content covered by KB1 and KB2 to guide further exploration.
               - **Detailed and Comprehensive Output**: Prioritize depth and breadth of information when summarizing. Include **all relevant details and insights** within the available context.
               - **Context Preservation**: Deliver a precise **overview of the data context** rather than directly answering the query without complementary explanations.

               ### OUTPUT TEMPLATE
               A JSON contains two keys and each key has a string value:

               1. `unified_summary`
                  - Provide a cohesive, factually accurate synthesis of the information from KB1 and KB2.
                  - If the precise data required by the query is missing, include alternative suggestions (clearly indicating that these are alternatives) and explain the differences.
                  - Always reference sources with explicit source IDs (e.g., `[Source KB1-7]`).

               2. `coverage`
                  - Clearly state if the query cannot be fully resolved, elaborating on **why** (e.g., missing, incomplete, or conflicting information).
                  - If an alternative suggestion is provided, explain how it differs from the original query request.
                  - Explicitly **list the topics and content currently covered** by KB1 and KB2, preserving their scope and boundaries for further queries.

               3. `difficulty`
                  - Assign a `difficulty` score (integer 1-5) based on how directly and completely the provided contexts answer the query (1: Very direct and complete, 5: Very indirect, incomplete, or requires significant inference/alternatives).

               ### QUALITY STANDARDS
               1. **Validation & Accuracy**: Cross-reference all information for consistency and accuracy across sources.
               2. **Explicit Attribution**: Attribute all included information to its respective source with precise IDs.
               3. **Clarity in Limitations & Alternatives**: Transparently document any gaps, conflicts, limitations, and clearly note any alternative suggestions along with their distinctions from the queried data.
               4. **Comprehensive Coverage**: Include every relevant detail from KB1 and KB2 while avoiding redundancy or unnecessary elaboration.
               5. **Hallucination Prevention**: Refrain from generating content outside the scope of the provided knowledge bases. If no information is available, state it explicitly.
               6. **Temporal Relevance**: If the information mentions about a date, check if it is still relevant to the current date {{{DateTimeOffset.UtcNow:D}}}.

               ---

               ====QUERY====
               {{$QUERY}}
               ====QUERY====

               {{$DATA}}
               """,
            query,
            composedContext,
            promptExecutionSettings);

        return finalOutput;
    }

    private async Task<string> ThinkAndApplyAsync(
        Kernel kernel,
        string query,
        string coverage,
        string unifiedSummary)
    {
        var composedContext =
            $$"""
              ==== Original User Query ====
              {{query}}
              ==== End Query ====

              ==== Synthesized Knowledge Summary ====
              {{unifiedSummary}}
              ==== End Synthesized Knowledge Summary ====

              ==== Knowledge Coverage & Limitations ====
              {{coverage}}
              ==== End Knowledge Coverage & Limitations ====
              """;

#pragma warning disable SKEXP0070
        var promptExecutionSettings =
            (GeminiPromptExecutionSettings) _promptExecutionSettingsService.GetPromptExecutionSettings(
                SemanticKernelExtensions.S_FLASH_2_5);
#pragma warning restore SKEXP0070
        promptExecutionSettings.ExtensionData = new Dictionary<string, object>()
        {
            {
                "thinkingConfig", new Dictionary<string, object>()
                {
                    {
                        "thinkingBudget", 1024
                    }
                }
            }
        };

        var finalOutput = await SummarizeAsync(
            kernel,
            "ThinkAndApply",
            """
            **Role:** You are an expert strategist and problem-solver.

            **Task:**

            Analyze the user's `Original User Query` and the provided `Synthesized Knowledge Summary` (along with its `Knowledge Coverage & Limitations`).
            Your goal is to go beyond simple explanation and **brainstorm actionable insights, potential strategies, or illustrative scenarios** demonstrating how the knowledge can be *applied* to address the user's underlying goal or situation.

            **Rules:**

            1.  **Consider Limitations:** Pay attention to the `Knowledge Coverage & Limitations`. If the knowledge is incomplete or provides alternatives, factor this into your thinking. How do these limitations affect potential strategies?
            2.  **Structure and Clarity:** Organize your Thoughts logically. Use headings, bullet points, or numbered lists for clarity.
            3.  **Grounding:** Ensure your thinking is **directly inspired by and grounded in** the provided `Synthesized Knowledge Summary` and `Coverage`. Do **not** introduce external information or make unsupported claims.
            4.  **Focus:** Add value by showing *how* the knowledge can be used, not just repeating it.

            ---

            {{$DATA}}
            """,
            query,
            composedContext,
            promptExecutionSettings);

        return finalOutput;
    }

    private async Task<string> SummarizeAsync(
        Kernel kernel,
        string name,
        string prompt,
        string query,
        string data,
        PromptExecutionSettings promptExecutionSettings)
    {
        var summarizeFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "Summarize",
                Template = prompt,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "QUERY", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "DATA", IsRequired = true
                    }
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "A summarized result relevant to the query."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                },
            });

        // Invoke the function with the provided input
        try
        {
            var chatMessageContent = await summarizeFunction.InvokeAsync<ChatMessageContent>(
                kernel,
                new KernelArguments(promptExecutionSettings)
                {
                    {
                        "DATA", data
                    },
                    {
                        "QUERY", query
                    }
                });

            if (chatMessageContent is OpenAIChatMessageContent { InnerContent: ChatCompletion chatCompletion })
            {
                _logger.LogInformation(
                    """
                    ----------------------- Summarize ({Name}) ------------------------
                    TokenUsage: {InputTokenUsage}; {OutputTokenUsage}; {ReasoningTokenUsage}
                    """,
                    name,
                    chatCompletion.Usage.InputTokenCount,
                    chatCompletion.Usage.OutputTokenCount - chatCompletion.Usage.OutputTokenDetails.ReasoningTokenCount,
                    chatCompletion.Usage.OutputTokenDetails.ReasoningTokenCount);
            }

            return chatMessageContent?.Content ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing data");
            return string.Empty;
        }
    }
}