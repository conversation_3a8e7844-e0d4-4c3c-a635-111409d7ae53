using Microsoft.DurableTask.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.CommerceHub.Workers.Configs;
using Sleekflow.Mvc;

const string name = "SleekflowCommerceHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildDbServices(services);
        services.AddDurableTaskClient(
            _ =>
            {
            });

        services.AddSingleton<IAppConfig, AppConfig>();
        services.AddHttpContextAccessor();
        Modules.BuildServiceBusManager(services);
    });

hostBuilder.Build().Run();