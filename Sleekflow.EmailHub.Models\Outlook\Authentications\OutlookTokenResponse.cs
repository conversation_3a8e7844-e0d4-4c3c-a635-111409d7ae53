using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Outlook.Authentications;

public class OutlookTokenResponse
{
    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("scope")]
    public string Scope { get; set; }

    [JsonProperty("expires_in")]
    public long ExpiresIn { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonConstructor]
    public OutlookTokenResponse(
        string tokenType,
        string scope,
        long expiresIn,
        string accessToken,
        string refreshToken)
    {
        TokenType = tokenType;
        Scope = scope;
        ExpiresIn = expiresIn;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
    }
}