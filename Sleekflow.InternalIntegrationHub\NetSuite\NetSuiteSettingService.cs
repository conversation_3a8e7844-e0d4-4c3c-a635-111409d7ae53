using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.InternalIntegrationHub.Clients;
using Sleekflow.InternalIntegrationHub.Constants.NetSuite;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.NetSuite;

public interface INetSuiteSettingService
{
    public Task<(List<SubsidiaryDetailsResponse> SubsidiaryDetails, List<CurrencyDetailsResponse> CurrencyDetails,
            List<EmployeeDetailsResponse> LocalNetSuiteEmployeeList, List<TermDetailsResponse> TermDetails)>
        NetSuitePreparation(
            CancellationToken cancellationToken,
            bool showSubsidiary = true,
            bool showCurrency = true,
            bool showEmployee = true,
            bool forceRefreshEmployee = false,
            bool showTerm = true);

    public Task<string?> GetInvoiceItemIdAsync(string itemName);
}

public class NetSuiteSettingService : INetSuiteSettingService, IScopedService
{
    private readonly ILogger<NetSuiteSettingService> _logger;
    private readonly INetSuiteClient _client;
    private readonly ICacheService _cacheService;
    private readonly INetSuiteEmployeeService _employeeService;

    public NetSuiteSettingService(
        ILogger<NetSuiteSettingService> logger,
        INetSuiteClient client,
        ICacheService cacheService,
        INetSuiteEmployeeService employeeService)
    {
        _logger = logger;
        _client = client;
        _cacheService = cacheService;
        _employeeService = employeeService;
    }

    public async Task<(List<SubsidiaryDetailsResponse> SubsidiaryDetails, List<CurrencyDetailsResponse> CurrencyDetails,
            List<EmployeeDetailsResponse> LocalNetSuiteEmployeeList, List<TermDetailsResponse> TermDetails)>
        NetSuitePreparation(
            CancellationToken cancellationToken,
            bool showSubsidiary = true,
            bool showCurrency = true,
            bool showEmployee = true,
            bool forceRefreshEmployee = false,
            bool showTerm = true)
    {
        var subsidiaryDetails = new List<SubsidiaryDetailsResponse>();

        if (showSubsidiary)
        {
            // get subsidiary list from NetSuite
            _logger.LogDebug("Start to get subsidiary from NetSuite");
            var subsidiary = await GetSubsidiaryAsync();

            // get subsidiary details
            foreach (var id in subsidiary.Items.Select(sub => sub.Id))
            {
                _logger.LogDebug("Subsidiary ID: {Id}", id);
                var details = await GetSubsidiaryDetailsAsync(id);
                subsidiaryDetails.Add(details);
            }
        }

        var currencyDetails = new List<CurrencyDetailsResponse>();

        if (showCurrency)
        {
            // get currency list
            _logger.LogDebug("Start to get currency from NetSuite");
            var currency = await GetCurrencyAsync();

            // get currency details
            foreach (var id in currency.Items.Select(item => item.Id))
            {
                _logger.LogDebug("Currency ID: {Id}", id);
                var details = await GetCurrencyDetailsAsync(id);
                currencyDetails.Add(details);
            }
        }

        var localNetSuiteEmployeeList = new List<EmployeeDetailsResponse>();

        if (showEmployee)
        {
            // get employee list
            _logger.LogDebug("Start to get employee list from NetSuite");
            var netSuiteEmployeeList = await _employeeService.GetEmployeeAsync(forceRefreshEmployee);

            await Parallel.ForEachAsync(
                netSuiteEmployeeList.Items.Select(e => e.Id),
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = 3, CancellationToken = cancellationToken
                },
                async (id, _) =>
                {
                    _logger.LogDebug("NetSuite Employee ID: {Id}", id);
                    var netSuiteEmployee = await _employeeService.GetEmployeeDetailsAsync(id, forceRefreshEmployee);
                    localNetSuiteEmployeeList.Add(netSuiteEmployee);
                });
        }

        var termDetails = new List<TermDetailsResponse>();

        if (showTerm)
        {
            // get term list
            _logger.LogDebug("Start to get term from NetSuite");
            var term = await GetTermAsync();

            // get term details
            await Parallel.ForEachAsync(
                term.Items.Select(e => e.Id),
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = 3, CancellationToken = cancellationToken
                },
                async (id, _) =>
                {
                    _logger.LogDebug("Term ID: {Id}", id);
                    var details = await GetTermDetailsAsync(id);
                    termDetails.Add(details);
                });
        }

        return (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails);
    }

    private async Task<CommonListResponse> GetSubsidiaryAsync()
    {
        var subsidiary = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetSubsidiaryAsync)}:Subsidiary",
            async () =>
            {
                var response = await _client.GetAsync<CommonListResponse>(
                    Endpoints.GetSubsidiaryEndpoint,
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));
        return subsidiary;
    }

    private async Task<SubsidiaryDetailsResponse> GetSubsidiaryDetailsAsync(string subsidiaryId)
    {
        var subsidiaryDetails = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetSubsidiaryDetailsAsync)}:SubsidiaryDetails:{subsidiaryId}",
            async () =>
            {
                var response = await _client.GetAsync<SubsidiaryDetailsResponse>(
                    string.Format(Endpoints.GetSubsidiaryDetailsEndpoint, subsidiaryId),
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return subsidiaryDetails;
    }

    private async Task<CommonListResponse> GetCurrencyAsync()
    {
        var currencyList = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetCurrencyAsync)}:CurrencyList",
            async () =>
            {
                var response = await _client.GetAsync<CommonListResponse>(
                    Endpoints.GetCurrencyEndpoint,
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return currencyList;
    }

    private async Task<CurrencyDetailsResponse> GetCurrencyDetailsAsync(string currencyId)
    {
        var currency = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetCurrencyDetailsAsync)}:Currency:{currencyId}",
            async () =>
            {
                var response = await _client.GetAsync<CurrencyDetailsResponse>(
                    string.Format(Endpoints.GetCurrencyDetailsEndpoint, currencyId),
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return currency;
    }

    private async Task<CommonListResponse> GetTermAsync()
    {
        var termList = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetTermAsync)}:TermList",
            async () =>
            {
                var response = await _client.GetAsync<CommonListResponse>(
                    Endpoints.GetTermEndpoint,
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return termList;
    }

    private async Task<TermDetailsResponse> GetTermDetailsAsync(string termId)
    {
        var termDetails = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetTermDetailsAsync)}:Term:{termId}",
            async () =>
            {
                var response = await _client.GetAsync<TermDetailsResponse>(
                    string.Format(Endpoints.GetTermDetailsEndpoint, termId),
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return termDetails;
    }

    public async Task<string?> GetInvoiceItemIdAsync(string itemName)
    {
        var itemId = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetInvoiceItemIdAsync)}:InvoiceItemId:{itemName}",
            async () =>
            {
                var query = new Dictionary<string, string>
                {
                    ["q"] = $"itemId IS \"{itemName}\""
                };
                var response = await _client.GetAsync<CommonListResponse>(
                    Endpoints.NonInventorySaleItemEndpoint,
                    query);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data.Items.FirstOrDefault()?.Id;
            },
            TimeSpan.FromHours(1));

        return itemId;
    }
}