using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetIntelligentHubUsageFilterOutput
{
    [JsonProperty("intelligent_hub_usage_filter")]
    public IntelligentHubUsageFilterOutput? IntelligentHubUsageFilter { get; set; }

    [JsonConstructor]
    public GetIntelligentHubUsageFilterOutput(IntelligentHubUsageFilterOutput? intelligentHubUsageFilter)
    {
        IntelligentHubUsageFilter = intelligentHubUsageFilter;
    }

    public class IntelligentHubUsageFilterOutput
    {
        [JsonProperty("from_date_time")]
        public DateTimeOffset? FromDateTime { get; set; }

        [JsonProperty("to_date_time")]
        public DateTimeOffset? ToDateTime { get; set; }
    }
}