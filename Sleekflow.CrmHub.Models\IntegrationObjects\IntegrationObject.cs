﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.IntegrationObjects;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("integration_object")]
public class IntegrationObject
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("sys_type_name")]
    public string SysTypeName { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("integration_entity_type_name")]
    public string IntegrationEntityTypeName { get; set; }

    [JsonProperty("data")]
    public object? Data { get; set; }

    [JsonProperty("typed_ids")]
    public List<TypedId>? TypedIds { get; set; }

    [JsonProperty("last_modification_time")]
    public DateTimeOffset? LastModificationTime { get; set; }

    public IntegrationObject(
        string id,
        string sysTypeName,
        string sleekflowCompanyId,
        string providerName,
        string integrationEntityTypeName,
        object? data,
        List<TypedId>? typedIds,
        DateTimeOffset? lastModificationTime)
    {
        Id = id;
        SysTypeName = sysTypeName;
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderName = providerName;
        IntegrationEntityTypeName = integrationEntityTypeName;
        Data = data;
        TypedIds = typedIds;
        LastModificationTime = lastModificationTime;
    }
}