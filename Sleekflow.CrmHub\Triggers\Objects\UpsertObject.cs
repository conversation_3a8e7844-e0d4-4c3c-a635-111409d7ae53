﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class UpsertObject : ITrigger
{
    private readonly IBus _bus;

    public UpsertObject(
        IBus bus)
    {
        _bus = bus;
    }

    public class UpsertObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonConstructor]
        public UpsertObjectInput(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName,
            Dictionary<string, object?> dict)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            Dict = dict;
        }
    }

    public class UpsertObjectOutput
    {
    }

    public async Task<UpsertObjectOutput> F(
        UpsertObjectInput upsertObjectInput)
    {
        switch (upsertObjectInput.ProviderName)
        {
            case "sleekflow":
            {
                if (upsertObjectInput.EntityTypeName is "Contact" or "Lead" or "User")
                {
                    if (upsertObjectInput.Dict.ContainsKey("Id") == false || upsertObjectInput.Dict["Id"] == null)
                    {
                        throw new SfUserFriendlyException("Id must be provided");
                    }

                    var providerObjectId = (string) upsertObjectInput.Dict["Id"]!;

                    var onObjectOperationEvent = new OnObjectOperationEvent(
                        upsertObjectInput.Dict,
                        OnObjectOperationEvent.OperationCreateOrUpdateObject,
                        "sleekflow",
                        upsertObjectInput.SleekflowCompanyId,
                        providerObjectId,
                        upsertObjectInput.EntityTypeName,
                        null);

                    await _bus.Publish(
                        onObjectOperationEvent,
                        context => { context.ConversationId = Guid.Parse(upsertObjectInput.SleekflowCompanyId); });

                    return new UpsertObjectOutput();
                }

                break;
            }
        }

        throw new NotImplementedException();
    }
}