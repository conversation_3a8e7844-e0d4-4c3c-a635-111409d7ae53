using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.WebCrawlingSessions;

public interface IWebCrawlingSessionRepository : IDynamicFiltersRepository<WebCrawlingSession>
{
}

public class WebCrawlingSessionRepository
    : DynamicFiltersBaseRepository<WebCrawlingSession>,
        IWebCrawlingSessionRepository,
        IScopedService
{
    public WebCrawlingSessionRepository(
        ILogger<WebCrawlingSessionRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}