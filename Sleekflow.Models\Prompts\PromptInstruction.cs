using System.ComponentModel.DataAnnotations;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Constants;

namespace Sleekflow.Models.Prompts;

public class PromptInstruction
{
    [JsonProperty("objective")]
    public string? Objective { get; set; }

    [JsonProperty("tone")]
    [RegularExpression($"{TargetToneTypes.Casual}|{TargetToneTypes.Technical}|{TargetToneTypes.Professional}")]
    public string? Tone { get; set; }

    [JsonProperty("disclose_level")]
    [RegularExpression($"{DiscloseLevels.Always}|{DiscloseLevels.WhenAsked}")]
    public string? DiscloseLevel { get; set; }

    [JsonProperty("response_level")]
    [RegularExpression($"{ResponseLevels.Long}|{ResponseLevels.Short}|{ResponseLevels.Normal}")]
    public string? ResponseLevel { get; set; }

    [JsonProperty("restrictiveness_level")]
    [RegularExpression(
        $"{RestrictivenessLevels.Normal}|{RestrictivenessLevels.Relaxed}")]
    public string? RestrictivenessLevel { get; set; }

    [JsonProperty("greeting_message")]
    public string? GreetingMessage { get; set; }

    // This is additional instruction that could change the agent behaviour
    // This is affect the main goal of the agent
    [JsonProperty("additional_instruction_core")]
    public string? AdditionalInstructionCore { get; set; }

    [JsonProperty("additional_instruction_strategy")]
    public string? AdditionalInstructionStrategy { get; set; }

    [JsonProperty("additional_instruction_response")]
    public string? AdditionalInstructionResponse { get; set; }

    [JsonProperty("additional_instruction_knowledge_retrieval")]
    public string? AdditionalInstructionKnowledgeRetrieval { get; set; }

    [JsonProperty("guardrails")]
    public List<Guardrail>? Guardrails { get; set; }

    [JsonConstructor]
    public PromptInstruction(
        string? objective,
        string? tone,
        string? discloseLevel,
        string? responseLevel,
        string? restrictivenessLevel,
        string? greetingMessage,
        string? additionalInstructionCore,
        string? additionalInstructionStrategy,
        string? additionalInstructionResponse,
        string? additionalInstructionKnowledgeRetrieval,
        List<Guardrail>? guardrails)
    {
        Objective = objective;
        Tone = tone;
        DiscloseLevel = discloseLevel;
        ResponseLevel = responseLevel;
        RestrictivenessLevel = restrictivenessLevel;
        GreetingMessage = greetingMessage;
        AdditionalInstructionCore = additionalInstructionCore;
        AdditionalInstructionStrategy = additionalInstructionStrategy;
        AdditionalInstructionResponse = additionalInstructionResponse;
        AdditionalInstructionKnowledgeRetrieval = additionalInstructionKnowledgeRetrieval;
        Guardrails = guardrails;
    }

    public PromptInstruction()
    {
        Objective = string.Empty;
        Tone = TargetToneTypes.Professional;
        DiscloseLevel = DiscloseLevels.Always;
        ResponseLevel = ResponseLevels.Normal;
        RestrictivenessLevel = RestrictivenessLevels.Normal;
        GreetingMessage = string.Empty;
        AdditionalInstructionCore = null;
        AdditionalInstructionStrategy = null;
        AdditionalInstructionResponse = null;
        AdditionalInstructionKnowledgeRetrieval = null;
        Guardrails = null;
    }
}