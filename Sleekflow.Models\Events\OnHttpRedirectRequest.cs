﻿namespace Sleekflow.Models.Events;

public class OnHttpRedirectRequest
{
    public string Url { get; set; }

    public Dictionary<string, object?> HeaderDict { get; set; }

    public string? BodyStr { get; set; }

    public Dictionary<string, object?>? BodyDict { get; set; }

    public HttpMethod Method { get; set; }


    public OnHttpRedirectRequest(
        string url,
        Dictionary<string, object?> headerDict,
        string? bodyStr,
        Dictionary<string, object?>? bodyDict,
        HttpMethod method)
    {
        Url = url;
        HeaderDict = headerDict;
        BodyStr = bodyStr;
        BodyDict = bodyDict;
        Method = method;
    }
}