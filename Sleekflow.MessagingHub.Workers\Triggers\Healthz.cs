﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;

namespace Sleekflow.MessagingHub.Workers.Triggers;

public static class Healthz
{
    [Function("healthz")]
    public static IActionResult RunAsync(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)]
        HttpRequest req)
    {
        return new ContentResult
        {
            ContentType = "text/plain", Content = "HEALTH"
        };
    }
}