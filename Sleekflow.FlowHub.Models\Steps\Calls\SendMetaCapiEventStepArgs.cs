using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SendMetaCapiEventStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.send-meta-capi-event";

    [Required]
    [JsonProperty("whatsapp_business_account_id")]
    public string WhatsappBusinessAccountId { get; set; }

    [Required]
    [JsonProperty("event_name")]
    public string EventName { get; set; }

    [Required]
    [JsonProperty("action_source")]
    public string ActionSource { get; set; }

    [Required]
    [JsonProperty("messaging_channel")]
    public string MessagingChannel { get; set; }

    [Required]
    [JsonProperty("ctwa_clid__expr")]
    public string CtwaClidExpr { get; set; }

    [Required]
    [JsonProperty("custom_data_currency__expr")]
    public string CustomDataCurrencyExpr { get; set; }

    [Required]
    [JsonProperty("custom_data_value__expr")]
    public string CustomDataValueExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.MetaCapiIntegration;

    [JsonConstructor]
    public SendMetaCapiEventStepArgs(
        string whatsappBusinessAccountId,
        string eventName,
        string actionSource,
        string messagingChannel,
        string ctwaClidExpr,
        string customDataCurrencyExpr,
        string customDataValueExpr)
    {
        WhatsappBusinessAccountId = whatsappBusinessAccountId;
        EventName = eventName;
        ActionSource = actionSource;
        MessagingChannel = messagingChannel;
        CtwaClidExpr = ctwaClidExpr;
        CustomDataCurrencyExpr = customDataCurrencyExpr;
        CustomDataValueExpr = customDataValueExpr;
    }
}