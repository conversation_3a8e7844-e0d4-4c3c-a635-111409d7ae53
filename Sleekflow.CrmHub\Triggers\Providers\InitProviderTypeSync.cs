using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class InitProviderTypeSync : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public InitProviderTypeSync(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class InitProviderTypeSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_interval")]
        public int? SyncInterval { get; set; }

        [JsonProperty("is_flows_based")]
        public bool? IsFlowsBased { get; set; }

        [JsonProperty("typed_ids")]
        [ValidateArray]
        public List<TypedId>? TypedIds { get; set; }

        [JsonConstructor]
        public InitProviderTypeSyncInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            string entityTypeName,
            int? syncInterval,
            bool? isFlowsBased,
            List<TypedId>? typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            EntityTypeName = entityTypeName;
            SyncInterval = syncInterval;
            IsFlowsBased = isFlowsBased;
            TypedIds = typedIds;
        }
    }

    public class InitProviderTypeSyncOutput
    {
    }

    public async Task<InitProviderTypeSyncOutput> F(InitProviderTypeSyncInput input)
    {
        var providerService = _providerSelector.GetProviderService(input.ProviderName);

        await providerService.InitTypeSyncAsync(
            input.SleekflowCompanyId,
            input.ProviderConnectionId,
            input.EntityTypeName,
            input.SyncInterval,
            input.IsFlowsBased,
            input.TypedIds);

        return new InitProviderTypeSyncOutput();
    }
}