using MassTransit;
using MimeKit;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Models.OnPremise.Authentications;
using Sleekflow.EmailHub.Models.OnPremise.Events;
using Sleekflow.EmailHub.Models.OnPremise.Subscriptions;
using Sleekflow.EmailHub.Models.Outlook.Events;
using Sleekflow.EmailHub.Models.Providers;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Providers;

public interface IEmailService
{
    public Task<AuthenticateProviderOutput> AuthenticateProviderAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? extendedAuthInput,
        CancellationToken cancellationToken = default);

    public Task<SubscribeProviderOutput> SubscribeProviderAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default);

    public Task<SubscribeProviderOutput> UnsubscribeProviderAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default);

    public Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken = default);

    public Task ReceiveEmailAndStoreAsync(
        string emailAddress,
        Dictionary<string, string> emailMetadata,
        CancellationToken cancellationToken = default);

    Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default);

    Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default);

    Task SyncEmailsAsync(string sleekflowCompanyId, string emailAddress, CancellationToken cancellationToken = default);

    Task DeleteEmailAsync(string emailId, CancellationToken cancellationToken = default);
}

public abstract class EmailService : IEmailService
{
    private readonly IEmailAuthenticationService _emailAuthenticationService;
    private readonly IEmailSubscriptionService _emailSubscriptionService;
    private readonly IEmailCommunicationService _emailCommunicationService;
    private readonly IBus _bus;
    private readonly string _providerName;
    private readonly IEmailRepository _emailRepository;
    private readonly IProviderConfigService _providerConfigService;

    public EmailService(
        string providerName,
        IEmailAuthenticationService emailAuthenticationService,
        IEmailSubscriptionService emailSubscriptionService,
        IEmailCommunicationService emailCommunicationService,
        IEmailRepository emailRepository,
        IBus bus,
        IProviderConfigService providerConfigService)
    {
        _providerName = providerName;
        _emailAuthenticationService = emailAuthenticationService;
        _emailSubscriptionService = emailSubscriptionService;
        _emailCommunicationService = emailCommunicationService;
        _emailRepository = emailRepository;
        _bus = bus;
        _providerConfigService = providerConfigService;
    }

    public async Task<AuthenticateProviderOutput> AuthenticateProviderAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? extendedAuthInput,
        CancellationToken cancellationToken = default)
    {
        var context = await _emailAuthenticationService.AuthenticateAsync(
            sleekflowCompanyId,
            emailAddress,
            extendedAuthInput,
            cancellationToken);

        return new AuthenticateProviderOutput(_providerName, context, emailAddress);
    }

    public async Task<SubscribeProviderOutput> SubscribeProviderAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default)
    {
        await _emailSubscriptionService.SubscribeAtEmailAddressAsync(
            sleekflowCompanyId,
            emailAddress,
            subscriptionMetadata,
            cancellationToken);

        return new SubscribeProviderOutput(
            providerName: _providerName,
            emailAddress: emailAddress,
            sleekflowCompanyId: sleekflowCompanyId);
    }

    public async Task<SubscribeProviderOutput> UnsubscribeProviderAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        await _emailSubscriptionService.UnsubscribeAtEmailAddressAsync(sleekflowCompanyId, emailAddress, cancellationToken);

        return new SubscribeProviderOutput(
            providerName: _providerName,
            emailAddress: emailAddress,
            sleekflowCompanyId: sleekflowCompanyId);
    }

    public async Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken)
    {
        await _emailSubscriptionService.RenewEmailSubscriptionAsync(cancellationToken);
    }

    public async Task ReceiveEmailAndStoreAsync(
        string emailAddress,
        Dictionary<string, string> emailMetadata,
        CancellationToken cancellationToken = default)
    {
        var sleekflowCompanyIds = await _providerConfigService.GetCompanyIdsByEmailAddressAsync(emailAddress, cancellationToken);
        await _emailCommunicationService.OnReceiveEmailAsync(sleekflowCompanyIds, emailAddress, emailMetadata, cancellationToken);
    }

    public async Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _emailCommunicationService.HandleSendEmailEventAsync(
            sleekflowCompanyId,
            sender,
            subject,
            to,
            cc,
            bcc,
            replyTo,
            htmlBody,
            textBody,
            emailAttachments,
            emailMetadata,
            cancellationToken);
    }

    public async Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _emailCommunicationService.SendEmailAsync(
            sleekflowCompanyId,
            sender,
            subject,
            to,
            cc,
            bcc,
            replyTo,
            htmlBody,
            textBody,
            emailAttachments,
            emailMetadata,
            cancellationToken);
    }

    public async Task SyncEmailsAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        switch (_providerName)
        {
            case ProviderNames.Gmail:
                await _bus.Publish(
                    new OnGmailSyncAllEmailsTriggeredEvent(emailAddress),
                    cancellationToken);
                break;
            case ProviderNames.Outlook:
                await _bus.Publish(
                    new OnOutlookSyncAllTriggeredEvent(
                        emailAddress,
                        sleekflowCompanyId),
                    cancellationToken);
                break;
            case ProviderNames.OnPremise:
                var subscription =
                    await _emailSubscriptionService.GetSubscriptionAsync(sleekflowCompanyId, emailAddress, cancellationToken);

                var subscriptionMetadata =
                    subscription.EmailSubscriptionMetadata as OnPremiseSubscriptionMetadata ??
                    throw new SfInternalErrorException("cannot parse subscription metadata");

                var authentication =
                    await _emailAuthenticationService.GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken);

                var authenticationMetadata =
                    authentication.EmailAuthenticationMetadata as OnPremiseAuthenticationMetadata ??
                    throw new SfInternalErrorException("cannot parse onPremise authentication metadata");

                await _bus.Publish(
                    new OnOnPremiseSyncAllEmailsTriggeredEvent(
                        emailAddress),
                    cancellationToken);

                break;
            default:
                throw new InvalidOperationException("Sync is not supported to this provider");
        }
    }

    public async Task DeleteEmailAsync(string emailId, CancellationToken cancellationToken = default)
    {
        await _emailRepository.DeleteAsync(emailId, emailId, cancellationToken: cancellationToken);
    }
}