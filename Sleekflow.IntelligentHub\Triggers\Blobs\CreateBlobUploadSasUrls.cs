using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Blobs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Blobs;

[TriggerGroup(ControllerNames.Blobs)]
public class CreateBlobUploadSasUrls
    : ITrigger<
        CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsInput,
        CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsOutput>
{
    private readonly IBlobService _blobService;

    public CreateBlobUploadSasUrls(IBlobService blobService)
    {
        _blobService = blobService;
    }

    public class CreateBlobUploadSasUrlsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [Range(1, 16)]
        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs { get; set; }

        [Required]
        [JsonProperty("blob_type")]
        public string BlobType { get; set; }

        [JsonConstructor]
        public CreateBlobUploadSasUrlsInput(
            string sleekflowCompanyId,
            int numberOfBlobs,
            string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            NumberOfBlobs = numberOfBlobs;
            BlobType = blobType;
        }
    }

    public class CreateBlobUploadSasUrlsOutput
    {
        [JsonProperty("upload_blobs")]
        public List<PublicBlob> UploadBlobs { get; set; }

        [JsonConstructor]
        public CreateBlobUploadSasUrlsOutput(
            List<PublicBlob> uploadBlobs)
        {
            UploadBlobs = uploadBlobs;
        }
    }

    public async Task<CreateBlobUploadSasUrlsOutput> F(CreateBlobUploadSasUrlsInput createBlobUploadSasUrlsInput)
    {
        var uploadBlobs = await _blobService.CreateBlobUploadSasUrls(
            createBlobUploadSasUrlsInput.SleekflowCompanyId,
            createBlobUploadSasUrlsInput.NumberOfBlobs,
            createBlobUploadSasUrlsInput.BlobType);

        return new CreateBlobUploadSasUrlsOutput(
            uploadBlobs);
    }
}