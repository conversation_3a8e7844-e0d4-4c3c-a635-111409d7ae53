using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Models.Blobs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Blobs;

[TriggerGroup(ControllerNames.Blobs)]
public class CreateBlobDownloadSasUrls
    : ITrigger<
        CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsInput,
        CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsOutput>
{
    private readonly IRequestClient<CreateBlobDownloadSasUrlsRequest> _createBlobDownloadSasUrlsRequestClient;

    public CreateBlobDownloadSasUrls(
        IRequestClient<CreateBlobDownloadSasUrlsRequest> createBlobDownloadSasUrlsRequestClient)
    {
        _createBlobDownloadSasUrlsRequestClient = createBlobDownloadSasUrlsRequestClient;
    }

    public class CreateBlobDownloadSasUrlsInput : Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_names")]
        [Required]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        [Required]
        public string BlobType { get; set; }

        [JsonConstructor]
        public CreateBlobDownloadSasUrlsInput(string sleekflowCompanyId, List<string> blobNames, string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobNames = blobNames;
            BlobType = blobType;
        }
    }

    public class CreateBlobDownloadSasUrlsOutput
    {
        [JsonProperty("download_blobs")]
        public List<PublicBlob> DownloadBlobs { get; set; }

        [JsonConstructor]
        public CreateBlobDownloadSasUrlsOutput(
            List<PublicBlob> downloadBlobs)
        {
            DownloadBlobs = downloadBlobs;
        }
    }

    public async Task<CreateBlobDownloadSasUrlsOutput> F(
        CreateBlobDownloadSasUrlsInput createBlobDownloadSasUrlsInput)
    {
        var createBlobDownloadSasUrlsReplyResponse =
            await _createBlobDownloadSasUrlsRequestClient.GetResponse<CreateBlobDownloadSasUrlsReply>(
                new CreateBlobDownloadSasUrlsRequest(
                    createBlobDownloadSasUrlsInput.SleekflowCompanyId,
                    createBlobDownloadSasUrlsInput.BlobNames,
                    createBlobDownloadSasUrlsInput.BlobType,
                    new CompressProperties(70),
                    new ReformatProperties("png")));
        var createBlobDownloadSasUrlsReply = createBlobDownloadSasUrlsReplyResponse.Message;

        return new CreateBlobDownloadSasUrlsOutput(
            createBlobDownloadSasUrlsReply.DownloadBlobs);
    }
}