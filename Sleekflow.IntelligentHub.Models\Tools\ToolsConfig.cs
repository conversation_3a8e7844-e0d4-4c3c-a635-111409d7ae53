using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Tools;

/// <summary>
/// Configuration for ReAct agent group tools focused on executing actions.
/// </summary>
public class ToolsConfig
{
    /// <summary>
    /// Tool configuration for sending messages to leads.
    /// </summary>
    [JsonProperty("sleekflow_send_message_tool")]
    public SleekflowSendMessageTool? SleekflowSendMessageTool { get; set; }

    /// <summary>
    /// Tool configuration for HubSpot integration.
    /// </summary>
    [JsonProperty("hubspot_tool")]
    public HubspotTool? HubspotTool { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="ToolsConfig"/> class.
    /// </summary>
    /// <param name="sleekflowSendMessageTool">Tool configuration for sending messages to leads.</param>
    /// <param name="hubspotTool">Tool configuration for HubSpot integration.</param>
    [JsonConstructor]
    public ToolsConfig(SleekflowSendMessageTool? sleekflowSendMessageTool, HubspotTool? hubspotTool)
    {
        SleekflowSendMessageTool = sleekflowSendMessageTool;
        HubspotTool = hubspotTool;
    }
}

/// <summary>
/// Configuration for the tool that sends messages to leads.
/// </summary>
public class SleekflowSendMessageTool
{
    [JsonProperty("channel_identity_id__expr")]
    public string ChannelIdentityIdExpr { get; set; }

    [JsonProperty("channel__expr")]
    public string ChannelExpr { get; set; }

    [JsonProperty("impersonation_staff_id__expr")]
    public string? ImpersonationStaffIdExpr { get; set; }

    [JsonConstructor]
    public SleekflowSendMessageTool(string channelIdentityIdExpr, string channelExpr, string? impersonationStaffIdExpr)
    {
        ChannelIdentityIdExpr = channelIdentityIdExpr;
        ChannelExpr = channelExpr;
        ImpersonationStaffIdExpr = impersonationStaffIdExpr;
    }
}

/// <summary>
/// Configuration for the HubSpot integration tool.
/// </summary>
public class HubspotTool
{
    [JsonProperty("api_key")]
    public string ApiKey { get; set; }

    [JsonConstructor]
    public HubspotTool(string apiKey)
    {
        ApiKey = apiKey;
    }
}