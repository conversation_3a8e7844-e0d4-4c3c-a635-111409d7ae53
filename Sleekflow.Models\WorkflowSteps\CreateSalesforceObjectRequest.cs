﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class CreateSalesforceObjectRequest
{
    [JsonProperty("aggregate_step_id")]
    [Required]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    [Required]
    public string ProxyStateId { get; set; }

    [JsonProperty("stack_entries")]
    [Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    [Required]
    public string ConnectionId { get; set; }

    [JsonProperty("entity_type_name")]
    [Required]
    public string EntityTypeName { get; set; }

    [JsonProperty("is_set_owner_by_user_mapping_config")]
    [Required]
    public bool IsSetOwnerByUserMappingConfig { get; set; }

    [JsonProperty("sleekflow_user_id_for_mapping")]
    public string? SleekflowUserIdForMapping { get; set; }

    [JsonProperty("object_properties")]
    [Required]
    public Dictionary<string, object?> ObjectProperties { get; set; }

    [JsonConstructor]
    public CreateSalesforceObjectRequest(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName,
        bool isSetOwnerByUserMappingConfig,
        string? sleekflowUserIdForMapping,
        Dictionary<string, object?> objectProperties)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        IsSetOwnerByUserMappingConfig = isSetOwnerByUserMappingConfig;
        SleekflowUserIdForMapping = sleekflowUserIdForMapping;
        ObjectProperties = objectProperties;
    }
}