using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class UpdateDocumentAgentAssignmentsForAgent
    : ITrigger<UpdateDocumentAgentAssignmentsForAgent.UpdateDocumentAgentAssignmentsForAgentInput,
        UpdateDocumentAgentAssignmentsForAgent.UpdateDocumentAgentAssignmentsForAgentOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IKbDocumentService _kbDocumentService;

    public UpdateDocumentAgentAssignmentsForAgent(
        ISleekflowAuthorizationContext authorizationContext,
        IKbDocumentService kbDocumentService)
    {
        _authorizationContext = authorizationContext;
        _kbDocumentService = kbDocumentService;
    }

    public class UpdateDocumentAgentAssignmentsForAgentInput
    {
        [JsonProperty("agent_id")]
        [Required]
        public string AgentId { get; set; }

        [JsonProperty("document_ids")]
        [Required]
        [ValidateArray]
        public List<string> DocumentIds { get; set; }

        [JsonConstructor]
        public UpdateDocumentAgentAssignmentsForAgentInput(string agentId, List<string> documentIds)
        {
            AgentId = agentId;
            DocumentIds = documentIds;
        }
    }

    // Empty output class as per specification {}
    public class UpdateDocumentAgentAssignmentsForAgentOutput
    {
        [JsonConstructor]
        public UpdateDocumentAgentAssignmentsForAgentOutput()
        {
        }
    }

    public async Task<UpdateDocumentAgentAssignmentsForAgentOutput> F(
        UpdateDocumentAgentAssignmentsForAgentInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        await _kbDocumentService.PatchAgentAssignmentsForAgentAsync(
            sleekflowCompanyId,
            input.AgentId,
            input.DocumentIds);

        return new UpdateDocumentAgentAssignmentsForAgentOutput();
    }
}