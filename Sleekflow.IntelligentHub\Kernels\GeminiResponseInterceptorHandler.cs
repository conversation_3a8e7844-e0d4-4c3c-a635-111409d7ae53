namespace Sleekflow.IntelligentHub.Kernels;

public class GeminiResponseInterceptorHandler : DelegatingHandler
{
    public GeminiResponseInterceptorHandler()
        : base(new HttpClientHandler())
    {
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        var requestContent = await request.Content.ReadAsStringAsync();

        Console.WriteLine("POST " + request.RequestUri);
        foreach (var (key, value) in request.Headers)
        {
            Console.WriteLine($"{key}: {string.Join(", ", value)}");
        }

        Console.WriteLine("Content-Type: application/json");
        Console.WriteLine();

        request.Content = new StringContent(requestContent);

        // Send the original request
        var response = await base.SendAsync(request, cancellationToken);

        // Read the original response content
        var responseContent = await response.Content.ReadAsStringAsync();

        Console.WriteLine(requestContent);
        Console.WriteLine();
        Console.WriteLine(responseContent);

        Console.WriteLine("========================================");

        return response;
    }
}