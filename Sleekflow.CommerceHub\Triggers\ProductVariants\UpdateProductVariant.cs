using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.ProductVariants;

[TriggerGroup(ControllerNames.ProductVariants)]
public class UpdateProductVariant
    : ITrigger<
        UpdateProductVariant.UpdateProductVariantInput,
        UpdateProductVariant.UpdateProductVariantOutput>
{
    private readonly IProductVariantService _productVariantService;
    private readonly IImageService _imageService;

    public UpdateProductVariant(
        IProductVariantService productVariantService,
        IImageService imageService)
    {
        _productVariantService = productVariantService;
        _imageService = imageService;
    }

    public class UpdateProductVariantInput : ProductVariantInput, IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateProductVariantInput(
            string? sku,
            string? url,
            List<Price> prices,
            int position,
            List<ProductVariant.ProductVariantAttribute> attributes,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            PlatformData? platformData,
            string id,
            string sleekflowCompanyId,
            string storeId,
            string productId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                sku,
                url,
                prices,
                position,
                attributes,
                names,
                descriptions,
                images,
                platformData)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductId = productId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateProductVariantOutput
    {
        [JsonProperty("product_variant")]
        public ProductVariantDto ProductVariant { get; set; }

        [JsonConstructor]
        public UpdateProductVariantOutput(ProductVariant productVariant)
        {
            ProductVariant = new ProductVariantDto(productVariant);
        }
    }

    public async Task<UpdateProductVariantOutput> F(UpdateProductVariantInput updateProductVariantInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateProductVariantInput.SleekflowStaffId,
            updateProductVariantInput.SleekflowStaffTeamIds);

        var productVariant = await _productVariantService.GetProductVariantAsync(
            updateProductVariantInput.Id,
            updateProductVariantInput.StoreId,
            updateProductVariantInput.SleekflowCompanyId);
        if (productVariant.IsDefaultVariantProduct)
        {
            throw new SfUserFriendlyException("The default VariantProduct cannot be updated");
        }

        var images = await _imageService.GetImagesAsync(
            updateProductVariantInput.Images,
            updateProductVariantInput.SleekflowCompanyId,
            updateProductVariantInput.StoreId);

        var patchedProductVariant = await _productVariantService.PatchAndGetProductVariantAsync(
            updateProductVariantInput.Id,
            updateProductVariantInput.SleekflowCompanyId,
            updateProductVariantInput.StoreId,
            updateProductVariantInput.ProductId,
            updateProductVariantInput.Sku,
            updateProductVariantInput.Url,
            updateProductVariantInput.Prices,
            updateProductVariantInput.Attributes
                .Select(
                    a => new ProductVariant.ProductVariantAttribute(
                        a.Name.ToLowerInvariant(),
                        a.Value))
                .ToList(),
            updateProductVariantInput.Names,
            updateProductVariantInput.Descriptions,
            images,
            sleekflowStaff);

        return new UpdateProductVariantOutput(patchedProductVariant);
    }
}