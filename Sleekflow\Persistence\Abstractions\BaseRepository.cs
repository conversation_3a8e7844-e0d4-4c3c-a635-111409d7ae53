﻿using System.Linq.Expressions;
using System.Net;
using System.Runtime.CompilerServices;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Polly;
using Polly.Retry;
using Sleekflow.CancellationTokens;
using Sleekflow.Exceptions;
using Sleekflow.Utils;

namespace Sleekflow.Persistence.Abstractions;

public interface IRepository<TObj>
{
    public IAsyncEnumerable<TObj> GetObjectEnumerableAsync(
        QueryDefinition queryDefinition,
        CancellationToken cancellationToken = default);

    public IAsyncEnumerable<TObj> GetObjectEnumerableAsync(
        Expression<Func<TObj, bool>> predicate,
        CancellationToken cancellationToken = default);

    public Task<List<TObj>> GetObjectsAsync(
        QueryDefinition queryDefinition,
        int? limit = 10000,
        CancellationToken cancellationToken = default);

    public Task<List<T>> GetObjectsAsync<T>(
        QueryDefinition queryDefinition,
        int? limit = 10000,
        CancellationToken cancellationToken = default);

    public Task<List<TObj>> GetObjectsAsync(
        Expression<Func<TObj, bool>> predicate,
        int? limit = 10000,
        CancellationToken cancellationToken = default);

    public Task<List<TObj>> GetObjectsAsync<TOrderByKey>(
        Expression<Func<TObj, bool>> predicate,
        Expression<Func<TObj, TOrderByKey>> orderBy,
        bool orderByAscending,
        int? limit = 10000,
        CancellationToken cancellationToken = default);

    public Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync(
        QueryDefinition queryDefinition,
        string? encryptedContinuationToken,
        int limit,
        CancellationToken cancellationToken = default);

    public Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync(
        Expression<Func<TObj, bool>> predicate,
        string? encryptedContinuationToken,
        int limit,
        CancellationToken cancellationToken = default);

    public Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync<TOrderByKey>(
        Expression<Func<TObj, bool>> predicate,
        Expression<Func<TObj, TOrderByKey>> orderBy,
        bool orderByAscending,
        string? continuationToken,
        int limit,
        CancellationToken cancellationToken = default);

    public Task<TObj> GetAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default);

    public Task<TObj> GetAsync(
        string id,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default);

    public Task<TObj?> GetOrDefaultAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default);

    public Task<TObj?> GetOrDefaultAsync(
        string id,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default);

    public Task<int> CreateAsync(
        TObj obj,
        string partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default);

    public Task<int> CreateAsync(
        TObj obj,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default);

    public Task<TObj> CreateAndGetAsync(
        TObj obj,
        string partitionKey,
        CancellationToken cancellationToken = default);

    public Task<TObj> CreateAndGetAsync(
        TObj obj,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default);

    public Task<int> UpsertAsync(
        TObj obj,
        string partitionKey,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<int> UpsertAsync(
        TObj obj,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<TObj> UpsertAndGetAsync(
        TObj obj,
        string partitionKey,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<int> ReplaceAsync(
        string id,
        string partitionKey,
        TObj obj,
        TransactionalBatch? transactionalBatch = null,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<int> ReplaceAsync(
        string id,
        PartitionKey partitionKey,
        TObj obj,
        TransactionalBatch? transactionalBatch = null,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<TObj> ReplaceAndGetAsync(
        string id,
        string partitionKey,
        TObj obj,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<int> DeleteAsync(
        string id,
        string partitionKey,
        string? eTag = null,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default);

    public Task<int> DeleteAsync(
        string id,
        PartitionKey partitionKey,
        string? eTag = null,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default);

    public Task<int> DeleteAsync(
        List<string> ids,
        string partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default);

    public Task<int> DeleteAsync(
        List<string> ids,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default);

    public Task<int> PatchAsync(
        string id,
        string partitionKey,
        List<PatchOperation> patchOperations,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<int> PatchAsync(
        string id,
        PartitionKey partitionKey,
        List<PatchOperation> patchOperations,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<TObj> PatchAndGetAsync(
        string id,
        string partitionKey,
        List<PatchOperation> patchOperations,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<TransactionalBatchResponse> ExecuteTransactionalBatchAsync(
        string partitionKey,
        Func<TransactionalBatch, Task> func,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Task<TransactionalBatchResponse> ExecuteTransactionalBatchAsync(
        PartitionKey partitionKey,
        Func<TransactionalBatch, Task> func,
        string? eTag = null,
        CancellationToken cancellationToken = default);

    public Container GetContainer();

    public IOrderedQueryable<TObj> GetContainerOrderedQueryable();
}

public abstract class BaseRepository<TObj> : IRepository<TObj>
{
    private const string CryptoKey = "SleekflowContinuationToken";

    private readonly ILogger<BaseRepository<TObj>> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly Container _container;
    private readonly AsyncRetryPolicy _retryPolicy;

    protected BaseRepository(
        ILogger<BaseRepository<TObj>> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _container = InitContainer();

        var persistenceRetryPolicyService =
            (IPersistenceRetryPolicyService) _serviceProvider.GetRequiredService(
                typeof(IPersistenceRetryPolicyService));

        _retryPolicy = persistenceRetryPolicyService.GetAsyncRetryPolicy();
    }

    private CancellationToken GetCancellationToken()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var cancellationTokenService = scope.ServiceProvider.GetRequiredService<ICancellationTokenService>();

            return cancellationTokenService.GetCancellationToken();
        }
        catch (Exception e)
        {
            return CancellationToken.None;
        }
    }

    private Container InitContainer()
    {
        var attrs = Attribute.GetCustomAttributes(typeof(TObj));

        var resolverAttribute =
            (ResolverAttribute) attrs.First(attr => attr.GetType() == typeof(ResolverAttribute));
        var databaseIdAttribute =
            (DatabaseIdAttribute) attrs.First(attr => attr.GetType() == typeof(DatabaseIdAttribute));
        var containerIdAttribute =
            (ContainerIdAttribute) attrs.First(attr => attr.GetType() == typeof(ContainerIdAttribute));

        var resolverType = resolverAttribute.ResolverType;
        var databaseId = databaseIdAttribute.DatabaseId;
        var containerId = containerIdAttribute.ContainerId;

        var containerResolver = (IContainerResolver) _serviceProvider.GetRequiredService(resolverType);

        return containerResolver.Resolve(databaseId, containerId);
    }

    public virtual Container GetContainer()
    {
        return _container;
    }

    public virtual IOrderedQueryable<TObj> GetContainerOrderedQueryable()
    {
        return _container.GetItemLinqQueryable<TObj>();
    }

    protected virtual AsyncRetryPolicy GetRetryPolicy()
    {
        return _retryPolicy;
    }

    public virtual async IAsyncEnumerable<TObj> GetObjectEnumerableAsync(
        QueryDefinition queryDefinition,
        [EnumeratorCancellation]
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", _container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = _container.GetItemQueryIterator<JObject>(
            qd,
            null,
            new QueryRequestOptions
            {
                MaxConcurrency = 4,
            });
        while (itemQueryIterator.HasMoreResults && !cancellationToken.IsCancellationRequested)
        {
            var response = await itemQueryIterator.ReadNextAsync(cancellationToken);

            foreach (var entity in response)
            {
                yield return ToTargetType(entity);
            }
        }
    }

    public virtual IAsyncEnumerable<TObj> GetObjectEnumerableAsync(
        Expression<Func<TObj, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        var container = _container;
        var queryable = container
            .GetItemLinqQueryable<TObj>()
            .Where(predicate);
        var qd = queryable.ToQueryDefinition();

        return GetObjectEnumerableAsync(qd, cancellationToken);
    }

    public virtual async Task<List<TObj>> GetObjectsAsync(
        QueryDefinition queryDefinition,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;
        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (_) =>
            {
                using var itemQueryIterator = container.GetItemQueryIterator<JObject>(
                    qd,
                    requestOptions: new QueryRequestOptions
                    {
                        MaxItemCount = limit > 1000 ? 2500 : limit,
                        MaxBufferedItemCount = limit > 1000 ? 2500 : limit,
                        MaxConcurrency = 4,
                    });

                var count = 0;
                var objs = new List<TObj>();

                while (itemQueryIterator.HasMoreResults && count < limit)
                {
                    var response = await itemQueryIterator.ReadNextAsync(cancellationToken);
                    foreach (var entity in response)
                    {
                        objs.Add(ToTargetType(entity));
                    }

                    if (_logger.IsEnabled(LogLevel.Debug))
                    {
                        _logger.LogDebug(
                            "GetObjectsAsync {QueryText} {RequestCharge}",
                            qd.QueryText,
                            response.RequestCharge);
                    }

                    if (response.RequestCharge > 1000)
                    {
                        _logger.LogInformation(
                            "GetObjectsAsync {QueryText} {RequestCharge}",
                            qd.QueryText,
                            response.RequestCharge);
                    }

                    count += response.Count;
                }

                return objs;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        throw new SfQueryException(policyResult.FinalException, nameof(GetObjectsAsync));
    }

    public virtual async Task<List<T>> GetObjectsAsync<T>(
        QueryDefinition queryDefinition,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;
        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (_) =>
            {
                using var itemQueryIterator = container.GetItemQueryIterator<JObject>(
                    qd,
                    requestOptions: new QueryRequestOptions
                    {
                        MaxItemCount = limit > 1000 ? 2500 : limit,
                        MaxBufferedItemCount = limit > 1000 ? 2500 : limit,
                        MaxConcurrency = 4,
                    });

                var count = 0;
                var objs = new List<T>();

                while (itemQueryIterator.HasMoreResults && count < limit)
                {
                    var response = await itemQueryIterator.ReadNextAsync(cancellationToken);
                    foreach (var entity in response)
                    {
                        objs.Add(entity.ToObject<T>() ?? throw new Exception($"Unable to convert to type {typeof(T).Name}"));
                    }

                    if (_logger.IsEnabled(LogLevel.Debug))
                    {
                        _logger.LogDebug(
                            "GetObjectsAsync {QueryText} {RequestCharge}",
                            qd.QueryText,
                            response.RequestCharge);
                    }

                    if (response.RequestCharge > 1000)
                    {
                        _logger.LogInformation(
                            "GetObjectsAsync {QueryText} {RequestCharge}",
                            qd.QueryText,
                            response.RequestCharge);
                    }

                    count += response.Count;
                }

                return objs;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        throw new SfQueryException(policyResult.FinalException, nameof(GetObjectsAsync));
    }

    public virtual Task<List<TObj>> GetObjectsAsync(
        Expression<Func<TObj, bool>> predicate,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        var container = _container;
        var queryable = container
            .GetItemLinqQueryable<TObj>()
            .Where(predicate);
        var qd = queryable.ToQueryDefinition();

        return GetObjectsAsync(qd, limit, cancellationToken);
    }

    public virtual Task<List<TObj>> GetObjectsAsync<TOrderByKey>(
        Expression<Func<TObj, bool>> predicate,
        Expression<Func<TObj, TOrderByKey>> orderBy,
        bool orderByAscending,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        var container = _container;

        var queryable = container
            .GetItemLinqQueryable<TObj>()
            .Where(predicate);
        queryable = orderByAscending
            ? queryable.OrderBy(orderBy)
            : queryable.OrderByDescending(orderBy);
        var qd = queryable.ToQueryDefinition();

        return GetObjectsAsync(qd, limit, cancellationToken);
    }

    public virtual async Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync(
        QueryDefinition queryDefinition,
        string? encryptedContinuationToken,
        int limit,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;
        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        var continuationToken = encryptedContinuationToken switch
        {
            not null => encryptedContinuationToken.StartsWith('[')
                ? encryptedContinuationToken
                : AesUtils.AesDecryptBase64(encryptedContinuationToken, CryptoKey),
            _ => null
        };
        var nextContinuationToken = continuationToken;

        PolicyResult<(List<TObj> Objs, string? NextContinuationToken)> policyResult =
            await _retryPolicy.ExecuteAndCaptureAsync(
                async (_) =>
                {
                    var objs = new List<TObj>();

                    do
                    {
                        using var itemQueryIterator =
                            container
                                .GetItemQueryIterator<JObject>(
                                    qd,
                                    nextContinuationToken,
                                    new QueryRequestOptions
                                    {
                                        MaxItemCount = limit - objs.Count,
                                        MaxBufferedItemCount = limit - objs.Count,
                                        MaxConcurrency = 4,
                                    });
                        var response = await itemQueryIterator.ReadNextAsync(cancellationToken);

                        foreach (var entity in response)
                        {
                            objs.Add(ToTargetType(entity));
                        }

                        if (_logger.IsEnabled(LogLevel.Debug))
                        {
                            _logger.LogDebug(
                                "GetContinuationTokenizedObjectsAsync {QueryText} {NextContinuationToken} {RequestCharge}",
                                qd.QueryText,
                                nextContinuationToken,
                                response.RequestCharge);
                        }

                        if (response.RequestCharge > 1000)
                        {
                            _logger.LogInformation(
                                "GetContinuationTokenizedObjectsAsync {QueryText} {NextContinuationToken} {RequestCharge}",
                                qd.QueryText,
                                nextContinuationToken,
                                response.RequestCharge);
                        }

                        nextContinuationToken = response.ContinuationToken;
                    }
                    while (objs.Count < limit && nextContinuationToken != null);

                    return (
                        objs,
                        nextContinuationToken is not null
                            ? AesUtils.AesEncryptBase64(nextContinuationToken, CryptoKey)
                            : null
                    );
                },
                new Dictionary<string, object>
                {
                    {
                        "containerName", container.Id
                    }
                });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        throw new SfQueryException(policyResult.FinalException, nameof(GetContinuationTokenizedObjectsAsync));
    }

    public virtual Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync(
        Expression<Func<TObj, bool>> predicate,
        string? encryptedContinuationToken,
        int limit,
        CancellationToken cancellationToken = default)
    {
        var container = _container;
        var queryable = container
            .GetItemLinqQueryable<TObj>()
            .Where(predicate);
        var qd = queryable.ToQueryDefinition();

        return GetContinuationTokenizedObjectsAsync(qd, encryptedContinuationToken, limit, cancellationToken);
    }

    public virtual Task<(List<TObj> Objs, string? NextContinuationToken)>
        GetContinuationTokenizedObjectsAsync<TOrderByKey>(
            Expression<Func<TObj, bool>> predicate,
            Expression<Func<TObj, TOrderByKey>> orderBy,
            bool orderByAscending,
            string? continuationToken,
            int limit,
            CancellationToken cancellationToken = default)
    {
        var container = _container;
        var queryable = container
            .GetItemLinqQueryable<TObj>()
            .Where(predicate);
        queryable = orderByAscending ? queryable.OrderBy(orderBy) : queryable.OrderByDescending(orderBy);
        var qd = queryable.ToQueryDefinition();

        return GetContinuationTokenizedObjectsAsync(qd, continuationToken, limit, cancellationToken);
    }

    public virtual Task<TObj> GetAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default)
    {
        return GetAsync(id, new PartitionKey(partitionKey), cancellationToken);
    }

    public virtual async Task<TObj> GetAsync(
        string id,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (_) =>
            {
                var itemResponse =
                    await container.ReadItemAsync<JObject>(
                        id,
                        partitionKey,
                        cancellationToken: cancellationToken);

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "GetAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return ToTargetType(itemResponse.Resource);
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        throw new SfNotFoundObjectException(policyResult.FinalException, id, partitionKey);
    }

    public virtual Task<TObj?> GetOrDefaultAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default)
    {
        return GetOrDefaultAsync(id, new PartitionKey(partitionKey), cancellationToken);
    }

    public virtual async Task<TObj?> GetOrDefaultAsync(
        string id,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (_) =>
            {
                var itemResponse =
                    await container.ReadItemAsync<JObject>(
                        id,
                        partitionKey,
                        cancellationToken: cancellationToken);

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "GetOrDefaultAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return ToTargetType(itemResponse.Resource);
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        if (policyResult.FinalException is CosmosException { StatusCode: HttpStatusCode.NotFound })
        {
            return default;
        }

        throw new SfQueryException(policyResult.FinalException, nameof(GetOrDefaultAsync));
    }

    public virtual Task<int> CreateAsync(
        TObj obj,
        string partitionKey,
        TransactionalBatch? transactionalBatch = null,
        CancellationToken cancellationToken = default)
    {
        return CreateAsync(obj, new PartitionKey(partitionKey), transactionalBatch, cancellationToken);
    }

    public async Task<int> CreateAsync(
        TObj obj,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        if (transactionalBatch != null)
        {
            transactionalBatch.CreateItem(obj);

            return 1;
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.CreateItemAsync(
                        obj,
                        partitionKey,
                        cancellationToken: cancellationToken,
                        requestOptions: new ItemRequestOptions
                        {
                            EnableContentResponseOnWrite = false,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "CreateAsync {PartitionKey} {RequestCharge}",
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return 1;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(policyResult.FinalException, "Unable to create the item {Item}", obj);

        return 0;
    }

    public virtual Task<TObj> CreateAndGetAsync(
        TObj obj,
        string partitionKey,
        CancellationToken cancellationToken = default)
    {
        return CreateAndGetAsync(obj, new PartitionKey(partitionKey), cancellationToken);
    }

    public virtual async Task<TObj> CreateAndGetAsync(
        TObj obj,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var patchItemRequestOptions = new PatchItemRequestOptions
                {
                    EnableContentResponseOnWrite = true,
                };
                var itemResponse =
                    await container.CreateItemAsync<JObject>(
                        JObject.FromObject(obj),
                        partitionKey,
                        cancellationToken: cancellationToken,
                        requestOptions: patchItemRequestOptions);

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "CreateAndGetAsync {PartitionKey} {RequestCharge}",
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return ToTargetType(itemResponse.Resource);
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        throw new SfQueryException(policyResult.FinalException, nameof(CreateAndGetAsync));
    }

    public virtual Task<int> UpsertAsync(
        TObj obj,
        string partitionKey,
        TransactionalBatch? transactionalBatch = null,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        return UpsertAsync(
            obj,
            new PartitionKey(partitionKey),
            transactionalBatch,
            eTag,
            cancellationToken);
    }

    public virtual async Task<int> UpsertAsync(
        TObj obj,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        if (transactionalBatch != null)
        {
            transactionalBatch.UpsertItem(
                obj,
                new TransactionalBatchItemRequestOptions
                {
                    IfMatchEtag = eTag
                });

            return 1;
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.UpsertItemAsync(
                        obj,
                        partitionKey,
                        cancellationToken: cancellationToken,
                        requestOptions: new ItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = false,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "UpsertAsync {PartitionKey} {RequestCharge}",
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return 1;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to upsert the item {Item}",
            obj);

        return 0;
    }

    public async Task<TObj> UpsertAndGetAsync(
        TObj obj,
        string partitionKey,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.UpsertItemAsync<JObject>(
                        JObject.FromObject(obj),
                        new PartitionKey(partitionKey),
                        cancellationToken: cancellationToken,
                        requestOptions: new ItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = true,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "UpsertAsync {PartitionKey} {RequestCharge}",
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return ToTargetType(itemResponse.Resource);
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to upsert the item {Item}",
            obj);

        throw new SfQueryException(policyResult.FinalException, nameof(UpsertAndGetAsync));
    }

    public virtual Task<int> ReplaceAsync(
        string id,
        string partitionKey,
        TObj obj,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        return ReplaceAsync(
            id,
            new PartitionKey(partitionKey),
            obj,
            transactionalBatch,
            eTag,
            cancellationToken);
    }

    public virtual async Task<int> ReplaceAsync(
        string id,
        PartitionKey partitionKey,
        TObj obj,
        TransactionalBatch? transactionalBatch = null,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        if (transactionalBatch != null)
        {
            transactionalBatch.ReplaceItem(
                id,
                obj,
                new TransactionalBatchItemRequestOptions
                {
                    IfMatchEtag = eTag
                });

            return 1;
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.ReplaceItemAsync(
                        obj,
                        id,
                        partitionKey,
                        cancellationToken: cancellationToken,
                        requestOptions: new ItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = false,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "ReplaceAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return 1;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to upsert the item {Item}",
            obj);

        return 0;
    }

    public async Task<TObj> ReplaceAndGetAsync(
        string id,
        string partitionKey,
        TObj obj,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.ReplaceItemAsync<JObject>(
                        JObject.FromObject(obj),
                        id,
                        new PartitionKey(partitionKey),
                        cancellationToken: cancellationToken,
                        requestOptions: new ItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = true,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "ReplaceAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return ToTargetType(itemResponse.Resource);
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to replace the item {Item}",
            obj);

        throw new SfQueryException(policyResult.FinalException, nameof(ReplaceAndGetAsync));
    }

    public virtual Task<int> DeleteAsync(
        string id,
        string partitionKey,
        string? eTag = null,
        TransactionalBatch? transactionalBatch = null,
        CancellationToken cancellationToken = default)
    {
        return DeleteAsync(
            id,
            new PartitionKey(partitionKey),
            eTag,
            transactionalBatch,
            cancellationToken);
    }

    public virtual async Task<int> DeleteAsync(
        string id,
        PartitionKey partitionKey,
        string? eTag = null,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        if (transactionalBatch != null)
        {
            transactionalBatch.DeleteItem(
                id,
                new TransactionalBatchItemRequestOptions
                {
                    IfMatchEtag = eTag
                });

            return 1;
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.DeleteItemAsync<TObj>(
                        id,
                        partitionKey,
                        requestOptions: new ItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = false,
                        },
                        cancellationToken: cancellationToken);

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "DeleteAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return 1;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to delete the item {Id}",
            id);

        return 0;
    }

    public virtual Task<int> DeleteAsync(
        List<string> ids,
        string partitionKey,
        TransactionalBatch? transactionalBatch = null,
        CancellationToken cancellationToken = default)
    {
        return DeleteAsync(
            ids,
            new PartitionKey(partitionKey),
            transactionalBatch,
            cancellationToken);
    }

    public virtual async Task<int> DeleteAsync(
        List<string> ids,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default)
    {
        var deleteCount = 0;

        foreach (var id in ids)
        {
            if (await DeleteAsync(id, partitionKey, null, transactionalBatch, cancellationToken) == 1)
            {
                deleteCount += 1;
            }
        }

        return deleteCount;
    }

    public virtual Task<int> PatchAsync(
        string id,
        string partitionKey,
        List<PatchOperation> patchOperations,
        TransactionalBatch? transactionalBatch = null,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        return PatchAsync(
            id,
            new PartitionKey(partitionKey),
            patchOperations,
            transactionalBatch,
            eTag,
            cancellationToken);
    }

    public virtual async Task<int> PatchAsync(
        string id,
        PartitionKey partitionKey,
        List<PatchOperation> patchOperations,
        TransactionalBatch? transactionalBatch = default,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        if (transactionalBatch != null)
        {
            transactionalBatch.PatchItem(
                id,
                patchOperations,
                new TransactionalBatchPatchItemRequestOptions
                {
                    IfMatchEtag = eTag
                });

            return 1;
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.PatchItemAsync<TObj>(
                        id,
                        partitionKey,
                        patchOperations,
                        cancellationToken: cancellationToken,
                        requestOptions: new PatchItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = false,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "PatchAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return 1;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to patch the item {Id} {PartitionKey}",
            id,
            partitionKey);

        return 0;
    }

    public virtual Task<TransactionalBatchResponse> ExecuteTransactionalBatchAsync(
        string partitionKey,
        Func<TransactionalBatch, Task> func,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        return ExecuteTransactionalBatchAsync(
            new PartitionKey(partitionKey),
            func,
            eTag,
            cancellationToken);
    }

    public async Task<TObj> PatchAndGetAsync(
        string id,
        string partitionKey,
        List<PatchOperation> patchOperations,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (ctx) =>
            {
                var itemResponse =
                    await container.PatchItemAsync<JObject>(
                        id,
                        new PartitionKey(partitionKey),
                        patchOperations,
                        cancellationToken: cancellationToken,
                        requestOptions: new PatchItemRequestOptions
                        {
                            IfMatchEtag = eTag, EnableContentResponseOnWrite = true,
                        });

                if (_logger.IsEnabled(LogLevel.Debug))
                {
                    _logger.LogDebug(
                        "PatchAsync {Id} {PartitionKey} {RequestCharge}",
                        id,
                        partitionKey,
                        itemResponse.RequestCharge);
                }

                return ToTargetType(itemResponse.Resource);
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            return policyResult.Result;
        }

        _logger.LogWarning(
            policyResult.FinalException,
            "Unable to patch the item {Id} {PartitionKey}",
            id,
            partitionKey);

        throw new SfQueryException(policyResult.FinalException, nameof(PatchAndGetAsync));
    }

    public virtual async Task<TransactionalBatchResponse> ExecuteTransactionalBatchAsync(
        PartitionKey partitionKey,
        Func<TransactionalBatch, Task> func,
        string? eTag = null,
        CancellationToken cancellationToken = default)
    {
        if (cancellationToken == default)
        {
            cancellationToken = GetCancellationToken();
        }

        var container = _container;

        var transactionalBatch = container.CreateTransactionalBatch(partitionKey);

        await func.Invoke(transactionalBatch);

        var transactionalBatchResponse = await transactionalBatch.ExecuteAsync(
            new TransactionalBatchRequestOptions()
            {
                IfMatchEtag = eTag,
            },
            cancellationToken);

        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug(
                "ExecuteTransactionalBatchAsync {PartitionKey} {RequestCharge}",
                partitionKey,
                transactionalBatchResponse.RequestCharge);
        }

        if (transactionalBatchResponse.IsSuccessStatusCode)
        {
            return transactionalBatchResponse;
        }

        throw new Exception("Unable to patch or create the Entity.");
    }

    /**
     * A collection usually contains a single type of document, which is the default implementation.
     *
     * If a collection contains multiple types of documents, the corresponding repository needs to override this method
     * and resolve the correct type based on a distinguishing property in the JObject.
     */
    protected virtual TObj ToTargetType(JObject jObject)
    {
        return jObject.ToObject<TObj>() ??
               throw new Exception($"Unable to convert to document type. {typeof(TObj).Name}");
    }
}