using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class SubsidiaryDetailsResponse
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonProperty("country", NullValueHandling = NullValueHandling.Ignore)]
    public Country Country { get; set; }

    [JsonProperty("currency", NullValueHandling = NullValueHandling.Ignore)]
    public Currency Currency { get; set; }

    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty("isElimination", NullValueHandling = NullValueHandling.Ignore)]
    public bool IsElimination { get; set; }

    [JsonProperty("isInactive", NullValueHandling = NullValueHandling.Ignore)]
    public bool IsInactive { get; set; }

    [JsonProperty("lastModifiedDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime LastModifiedDate { get; set; }

    [JsonProperty("legalName", NullValueHandling = NullValueHandling.Ignore)]
    public string LegalName { get; set; }

    [JsonProperty("mainAddress", NullValueHandling = NullValueHandling.Ignore)]
    public MainAddress MainAddress { get; set; }

    [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
    public string Name { get; set; }

    [JsonProperty("parent", NullValueHandling = NullValueHandling.Ignore)]
    public Parent Parent { get; set; }

    [JsonProperty("returnAddress", NullValueHandling = NullValueHandling.Ignore)]
    public ReturnAddress ReturnAddress { get; set; }

    [JsonProperty("shippingAddress", NullValueHandling = NullValueHandling.Ignore)]
    public ShippingAddress ShippingAddress { get; set; }

    [JsonConstructor]
    public SubsidiaryDetailsResponse(
        List<Link> links,
        Country country,
        Currency currency,
        string id,
        bool isElimination,
        bool isInactive,
        DateTime lastModifiedDate,
        string legalName,
        MainAddress mainAddress,
        string name,
        Parent parent,
        ReturnAddress returnAddress,
        ShippingAddress shippingAddress)
    {
        Links = links;
        Country = country;
        Currency = currency;
        Id = id;
        IsElimination = isElimination;
        IsInactive = isInactive;
        LastModifiedDate = lastModifiedDate;
        LegalName = legalName;
        MainAddress = mainAddress;
        Name = name;
        Parent = parent;
        ReturnAddress = returnAddress;
        ShippingAddress = shippingAddress;
    }
}

public class Country
{
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty("refName", NullValueHandling = NullValueHandling.Ignore)]
    public string RefName { get; set; }

    [JsonConstructor]
    public Country(
        string id,
        string refName)
    {
        Id = id;
        RefName = refName;
    }
}

public class MainAddress
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonConstructor]
    public MainAddress(
        List<Link> links)
    {
        Links = links;
    }
}

public class Parent
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty("refName", NullValueHandling = NullValueHandling.Ignore)]
    public string RefName { get; set; }

    [JsonConstructor]
    public Parent(
        List<Link> links,
        string id,
        string refName)
    {
        Links = links;
        Id = id;
        RefName = refName;
    }
}

public class ReturnAddress
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonConstructor]
    public ReturnAddress(
        List<Link> links)
    {
        Links = links;
    }
}

public class ShippingAddress
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonConstructor]
    public ShippingAddress(
        List<Link> links)
    {
        Links = links;
    }
}