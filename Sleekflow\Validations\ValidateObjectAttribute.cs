using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Validations;

public class ValidateObjectAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null)
        {
            return null;
        }

        var results = new List<ValidationResult>();
        var context = new ValidationContext(value, null, null);

        Validator.TryValidateObject(value, context, results, true);

        if (results.Count != 0)
        {
            var compositeResults =
                new CompositeValidationResult(
                    $"Validations for {validationContext.MemberName} have failed.",
                    new[]
                    {
                        validationContext.MemberName!
                    });
            results.ForEach(compositeResults.AddResult);

            return compositeResults;
        }

        return ValidationResult.Success;
    }
}