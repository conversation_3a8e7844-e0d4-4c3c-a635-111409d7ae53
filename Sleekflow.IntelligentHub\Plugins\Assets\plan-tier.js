// Define the CSV data as a multiline string
const csvData = `Country,Region,Plan Tier 1,Plan Tier 2,Plan Tier 3,Plan Tier 4,Plan Tier 5,Local Currency
Afghanistan,ROW,,,,,Y,USD
Albania,ROW,,Y,,,,USD
Algeria,ROW,,,,,Y,USD
American Samoa,ROW,,Y,,,,USD
Andorra,ROW,,Y,,,,EUR
Angola,ROW,,,,,Y,USD
Anguilla,AMER,,,,,Y,USD
Antarctica,ROW,,Y,,,,USD
Antigua and Barbuda,AMER,,,,,Y,USD
Argentina,AMER,,,Y,,,USD
Armenia,ROW,,Y,,,,USD
Aruba,AMER,,Y,,,,USD
Australia,ROW,Y,,,,,AUD
Austria,ROW,Y,,,,,EUR
Azerbaijan,ROW,,Y,,,,USD
Bahamas,AMER,,Y,,,,USD
Bahrain,ROW,Y,,,,,USD
Bangladesh,ROW,,,,,Y,USD
Barbados,AMER,,,,,Y,USD
Belarus,ROW,,Y,,,,USD
Belgium,ROW,Y,,,,,EUR
Belize,AMER,,,Y,,,USD
Benin,ROW,,,,,Y,USD
Bermuda,AMER,Y,,,,,USD
Bhutan,ROW,,,,,Y,USD
Bolivia,AMER,,,Y,,,USD
Bosnia and Herzegovina,ROW,,Y,,,,USD
Botswana,ROW,,,,,Y,USD
Brazil,AMER,,,Y,,,BRL
British Indian Ocean Territory,ROW,Y,,,,,USD
British Virgin Islands,AMER,Y,,,,,USD
Brunei,ASEAN & India,,,,,Y,USD
Bulgaria,ROW,,Y,,,,USD
Burkina Faso,ROW,,,,,Y,USD
Burundi,ROW,,,,,Y,USD
Cambodia,ASEAN & India,,,,,Y,USD
Cameroon,ROW,,,,,Y,USD
Canada,AMER,Y,,,,,CAD
Cape Verde,ROW,,,,,Y,USD
Caribbean,ROW,,,,,Y,USD
Cayman Islands,AMER,Y,,,,,USD
Central African Republic,ROW,,,,,Y,USD
Chad,ROW,,,,,Y,USD
Chile,AMER,,,Y,,,USD
China,GCR & UAE,Y,,,,,CNY
Christmas Island,ROW,,Y,,,,USD
Cocos Islands,ROW,,Y,,,,USD
Colombia,AMER,,,Y,,,USD
Comoros,ROW,,,,,Y,USD
Congo (DRC),ROW,,,,,Y,USD
Cook Islands,ROW,,Y,,,,USD
Costa Rica,AMER,,,Y,,,USD
Côte d’Ivoire,ROW,,,,,Y,USD
Croatia,ROW,,Y,,,,EUR
Cuba,AMER,,,,,Y,USD
Curacao,ROW,,,,,Y,USD
Cyprus,ROW,Y,,,,,EUR
Czech Republic,ROW,Y,,,,,USD
Denmark,ROW,Y,,,,,USD
Djibouti,ROW,,,,,Y,USD
Dominica,AMER,,,,,Y,USD
Dominican Republic,AMER,,,,,Y,USD
East Timor,ROW,,,,,Y,USD
Ecuador,AMER,,,Y,,,USD
Egypt,ROW,,,,,Y,USD
El Salvador,AMER,,,Y,,,USD
Equatorial Guinea,ROW,,,,,Y,USD
Eritrea,ROW,,,,,Y,USD
Estonia,ROW,,Y,,,,EUR
Ethiopia,ROW,,,,,Y,USD
Falkland Islands,AMER,,,,,Y,USD
Faroe Islands,ROW,Y,,,,,USD
Fiji,ROW,,Y,,,,USD
Finland,ROW,Y,,,,,EUR
France,ROW,Y,,,,,EUR
French Polynesia,ROW,,Y,,,,USD
Gabon,ROW,,,,,Y,USD
Gambia,ROW,,,,,Y,USD
Georgia,ROW,,Y,,,,USD
Germany,ROW,Y,,,,,EUR
Ghana,ROW,,,,,Y,USD
Gibraltar,ROW,Y,,,,,USD
Greece,ROW,,Y,,,,EUR
Greenland,AMER,Y,,,,,USD
Grenada,AMER,,,,,Y,USD
Guam,ROW,Y,,,,,USD
Guatemala,AMER,,,Y,,,USD
Guernsey,ROW,Y,,,,,USD
Guinea,ROW,,,,,Y,USD
Guinea-Bissau,ROW,,,,,Y,USD
Guyana,AMER,,,Y,,,USD
Haiti,AMER,,,,,Y,USD
Honduras,AMER,,,Y,,,USD
Hong Kong SAR,GCR & UAE,Y,,,,,HKD
Hungary,ROW,,Y,,,,USD
Iceland,ROW,Y,,,,,USD
India,ASEAN & India,,,,,Y,INR
Indonesia,ASEAN & India,,,,,Y,IDR
Iran,ROW,,Y,,,,USD
Iraq,ROW,,Y,,,,USD
Ireland,ROW,Y,,,,,EUR
Isle of Man,ROW,Y,,,,,USD
Israel,ROW,Y,,,,,USD
Italy,ROW,Y,,,,,EUR
Jamaica,AMER,,,,,Y,USD
Japan,ROW,,Y,,,,USD
Jersey,ROW,Y,,,,,USD
Jordan,ROW,,Y,,,,USD
Kazakhstan,ROW,,Y,,,,USD
Kenya,ROW,,,,,Y,USD
Kiribati,ROW,,Y,,,,USD
South Korea,ROW,,Y,,,,USD
Kosovo,ROW,,Y,,,,USD
Kuwait,ROW,Y,,,,,USD
Kyrgyzstan,ROW,,,,,Y,USD
Laos,ASEAN & India,,,,,Y,USD
Latvia,ROW,,Y,,,,EUR
Lebanon,ROW,,,,,Y,USD
Lesotho,ROW,,,,,Y,USD
Liberia,ROW,,,,,Y,USD
Libya,ROW,,,,,Y,USD
Liechtenstein,ROW,Y,,,,,USD
Lithuania,ROW,,Y,,,,EUR
Luxembourg,ROW,Y,,,,,EUR
Macao SAR,GCR & UAE,Y,,,,,USD
"Macedonia, FYRO",ROW,,Y,,,,USD
Madagascar,ROW,,,,,Y,USD
Malawi,ROW,,,,,Y,USD
Malaysia,ASEAN & India,,,,Y,,MYR
Maldives,ROW,,Y,,,,USD
Mali,ROW,,,,,Y,USD
Malta,ROW,Y,,,,,EUR
Marshall Islands,ROW,,Y,,,,USD
Mauritania,ROW,,,,,Y,USD
Mauritius,ROW,,,,,Y,USD
Mayotte,ROW,Y,,,,,EUR
Mexico,AMER,,,Y,,,USD
Micronesia,ROW,,Y,,,,USD
Moldova,ROW,,,,,Y,USD
Monaco,ROW,Y,,,,,EUR
Mongolia,ROW,,,,,Y,USD
Montenegro,ROW,,Y,,,,USD
Montserrat,AMER,,,,,Y,USD
Morocco,ROW,,,,Y,,USD
Mozambique,ROW,,,,,Y,USD
Myanmar,ASEAN & India,,,,,Y,USD
Namibia,ROW,,,,,Y,USD
Nauru,ROW,,Y,,,,USD
Nepal,ROW,,,,,Y,USD
Netherlands,ROW,Y,,,,,EUR
Netherlands Antilles,ROW,,,,,Y,USD
New Caledonia,ROW,Y,,,,,USD
New Zealand,ROW,Y,,,,,USD
Nicaragua,AMER,,,Y,,,USD
Niger,ROW,,,,,Y,USD
Nigeria,ROW,,,,,Y,USD
Niue,ROW,Y,,,,,USD
North Korea,ROW,,,,,Y,USD
Northern Mariana Islands,ROW,,Y,,,,USD
Norway,ROW,Y,,,,,USD
Oman,ROW,Y,,,,,USD
Pakistan,ROW,,,,,Y,USD
Palau,ROW,,Y,,,,USD
Palestine,ROW,,,,,Y,USD
Panama,AMER,,,Y,,,USD
Papua New Guinea,ROW,,Y,,,,USD
Paraguay,AMER,,,Y,,,USD
Peru,AMER,,,Y,,,USD
Philippines,ASEAN & India,,,,,Y,USD
Pitcairn,ROW,Y,,,,,USD
Poland,ROW,,Y,,,,USD
Portugal,AMER,,Y,,,,EUR
Puerto Rico,AMER,,,,,Y,USD
Qatar,ROW,Y,,,,,USD
Republic of the Congo,ROW,,,,,Y,USD
Réunion,ROW,Y,,,,,EUR
Romania,ROW,,Y,,,,USD
Russia,ROW,,Y,,,,USD
Rwanda,ROW,,,,,Y,USD
Saint Barthélemy,ROW,,Y,,,,EUR
Saint Helena,ROW,,,,,Y,USD
Saint Kitts and Nevis,AMER,,,,,Y,USD
Saint Lucia,AMER,,,,,Y,USD
Saint Martin,AMER,,,,,Y,USD
Saint Pierre and Miquelon,AMER,,Y,,,,EUR
Saint Vincent and the Grenadines,AMER,,,,,Y,USD
Samoa,ROW,,Y,,,,USD
San Marino,ROW,Y,,,,,EUR
Sao Tome and Principe,ROW,,,,,Y,USD
Saudi Arabia,ROW,Y,,,,,USD
Senegal,ROW,,,,,Y,USD
Serbia,ROW,,Y,,,,USD
Seychelles,ROW,,,,,Y,USD
Sierra Leone,ROW,,,,,Y,USD
Singapore,ASEAN & India,,Y,,,,SGD
Sint Maarten,AMER,,,,,Y,USD
Slovakia,ROW,,Y,,,,EUR
Slovenia,ROW,Y,,,,,EUR
Solomon Islands,ROW,,Y,,,,USD
Somalia,ROW,,,,,Y,USD
South Africa,ROW,,,,Y,,USD
South Sudan,ROW,,,,,Y,USD
Spain,AMER,Y,,,,,EUR
Sri Lanka,ROW,,,,,Y,USD
Sudan,ROW,,,,,Y,USD
Suriname,AMER,,,Y,,,USD
Svalbard and Jan Mayen,ROW,Y,,,,,USD
Eswatini,ROW,,,,,Y,USD
Sweden,ROW,Y,,,,,USD
Switzerland,ROW,Y,,,,,USD
Syria,ROW,,,,,Y,USD
Taiwan,GCR & UAE,,Y,,,,USD
Tajikistan,ROW,,,,,Y,USD
Tanzania,ROW,,,,,Y,USD
Thailand,ASEAN & India,,,,Y,,USD
Togo,ROW,,,,,Y,USD
Tokelau,ROW,Y,,,,,USD
Tonga,ROW,,,,,Y,USD
Trinidad and Tobago,AMER,,,,,Y,USD
Tunisia,ROW,,,,,Y,USD
Turkey,ROW,,Y,,,,USD
Turkmenistan,ROW,,,,,Y,USD
Turks and Caicos Islands,AMER,,,,,Y,USD
Tuvalu,ROW,,Y,,,,USD
U.S. Virgin Islands,ROW,Y,,,,,USD
Uganda,ROW,,,,,Y,USD
Ukraine,ROW,,Y,,,,USD
United Arab Emirates,GCR & UAE,Y,,,,,AED
United Kingdom,ROW,Y,,,,,GBP
United States,AMER,Y,,,,,USD
Uruguay,AMER,,,Y,,,USD
Uzbekistan,ROW,,,,,Y,USD
Vanuatu,ROW,,,,,Y,USD
Vatican City,ROW,Y,,,,,EUR
Venezuela,AMER,,,Y,,,USD
Vietnam,ASEAN & India,,,,Y,,USD
Wallis and Futuna,ROW,Y,,,,,USD
Western Sahara,ROW,,,,,Y,USD
Yemen,ROW,,,,,Y,USD
Zambia,ROW,,,,,Y,USD
Zimbabwe,ROW,,,,,Y,USD`;

// Split the CSV data into rows and skip the header
const rows = csvData.split('\n').slice(1);

// Process each row
for (const row of rows) {
    // Split the row into columns
    const columns = row.split(',');

    // Extract fields
    const country = columns[0];
    const region = columns[1];
    const tiers = columns.slice(2, 7); // Tiers are in columns 2 to 6
    const tierIndex = tiers.indexOf('Y');
    const currency = columns[7];

    // Check if a tier is found and compute the tier number
    if (tierIndex !== -1) {
        const tier = tierIndex + 1; // Tier 1 is at index 0, Tier 2 at index 1, etc.
        // Output in the desired format
        console.log(`${country},${region},Plan Tier ${tier},${currency}`);
    }
}