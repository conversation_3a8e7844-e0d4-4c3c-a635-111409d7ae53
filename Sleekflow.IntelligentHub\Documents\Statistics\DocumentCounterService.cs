using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Tokens;

namespace Sleekflow.IntelligentHub.Documents.Statistics;

public interface IDocumentCounterService
{
    int CountWords(string content);

    int CountCharacters(string content);

    int CountTokens(string content);
}

public class DocumentCounterService : ISingletonService, IDocumentCounterService
{
    private readonly ITokenService _tokenService;

    public DocumentCounterService(ITokenService tokenService)
    {
        _tokenService = tokenService;
    }

    public int CountWords(string content)
    {
        var separators = new[]
        {
            ' ',
            '\t',
            '\r',
            '\n',
            ','
        };

        return content.Split(
            separators,
            StringSplitOptions.RemoveEmptyEntries).Count(item => item.All(ch => char.IsLetter(ch)));
    }

    public int CountCharacters(string content)
    {
        return content.Replace(" ", string.Empty).Length;
    }

    public int CountTokens(string content)
    {
        return _tokenService.Count(content);
    }
}