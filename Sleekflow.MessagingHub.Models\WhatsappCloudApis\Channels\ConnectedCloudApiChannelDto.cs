using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;

public class ConnectedCloudApiChannelDto
{
    [JsonProperty("connected_api_channels")]
    public List<WabaPhoneNumberDto>? ConnectedApiChannels { get; set; }

    [JsonConstructor]
    public ConnectedCloudApiChannelDto(List<WabaPhoneNumberDto>? connectedApiChannels)
    {
        ConnectedApiChannels = connectedApiChannels;
    }

    public ConnectedCloudApiChannelDto(Waba? waba)
        : this(
            waba?.WabaPhoneNumbers
                .Where(w => w.RecordStatus == WabaPhoneNumberStatuses.Active)
                .Select(w => new WabaPhoneNumberDto(w))
                .ToList())
    {
    }
}