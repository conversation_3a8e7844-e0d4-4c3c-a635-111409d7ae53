<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="Google.Apis.Drive.v3" Version="1.68.0.3608" />
        <PackageReference Include="Google.Apis.PeopleService.v1" Version="1.68.0.3359" />
        <PackageReference Include="Google.Apis.Script.v1" Version="1.68.0.3294" />
        <PackageReference Include="Google.Apis.Sheets.v4" Version="1.68.0.3568" />
        <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="5.2.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
        <ProjectReference Include="..\Sleekflow.CrmHub.Models\Sleekflow.CrmHub.Models.csproj" />
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>