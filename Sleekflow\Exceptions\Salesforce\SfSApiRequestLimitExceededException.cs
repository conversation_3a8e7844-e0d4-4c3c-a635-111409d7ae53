﻿namespace Sleekflow.Exceptions.Salesforce;

public class SfSApiRequestLimitExceededException : ErrorCodeException
{
    public SfSApiRequestLimitExceededException(string connectionId)
        : base(
            ErrorCodeConstant.SfSApiRequestLimitExceededException,
            $"The API request limit of this connection has been exceeded. connection {connectionId}",
            new Dictionary<string, object?>
            {
                {
                    "connectionId", connectionId
                }
            })
    {
    }
}