using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Events;
using Sleekflow.Models.Prompts;

namespace Sleekflow.Models.WorkflowSteps;

public class GetCalculateLeadScoreEvent : AgentEventBase
{
    public string AggregateStepId { get; set; }

    public string ProxyStateId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string? AgentId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public Dictionary<string, string>? ContactProperties { get; set; }

    public GetCalculateLeadScoreEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string? agentId,
        List<SfChatEntry> conversationContext,
        Dictionary<string, string>? contactProperties = null)
        : base(aggregateStepId, proxyStateId, stackEntries)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        AgentId = agentId;
        ConversationContext = conversationContext;
        ContactProperties = contactProperties;
    }

    public class Response
    {

        [JsonProperty("score")]
        public int Score { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }

        [JsonConstructor]
        public Response(int score, string reason)
        {
            Score = score;
            Reason = reason;
        }
    }
}