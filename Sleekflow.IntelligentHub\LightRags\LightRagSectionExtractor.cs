namespace Sleekflow.IntelligentHub.LightRags;

public static class LightRagSectionExtractor
{
    private const string EntitiesMarker = "-----Entities-----";
    private const string RelationshipsMarker = "-----Relationships-----";
    private const string SourcesMarker = "-----Sources-----";

    public static (string Entities, string Relationships, string Sources) ExtractSections(string input)
    {
        try
        {
            // Find the start index of each section
            int entitiesStart = input.IndexOf(EntitiesMarker, StringComparison.Ordinal);
            int relationshipsStart = input.IndexOf(RelationshipsMarker, StringComparison.Ordinal);
            int sourcesStart = input.IndexOf(SourcesMarker, StringComparison.Ordinal);

            if (entitiesStart == -1 || relationshipsStart == -1 || sourcesStart == -1)
            {
                throw new ArgumentException("One or more section markers not found in the input string.");
            }

            // Calculate the actual content start positions (after the markers)
            entitiesStart += EntitiesMarker.Length;
            relationshipsStart += RelationshipsMarker.Length;
            sourcesStart += SourcesMarker.Length;

            // Extract each section's content
            string entities = input
                .Substring(entitiesStart, relationshipsStart - EntitiesMarker.Length - entitiesStart)
                .Trim();

            string relationships = input
                .Substring(relationshipsStart, sourcesStart - RelationshipsMarker.Length - relationshipsStart)
                .Trim();

            string sources = input
                .Substring(sourcesStart)
                .Trim();

            return (entities, relationships, sources);
        }
        catch (Exception ex) when (ex is ArgumentOutOfRangeException || ex is ArgumentException)
        {
            throw new ArgumentException("Failed to parse sections. Please verify the input format.", ex);
        }
    }
}