using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Companies;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Ids;
using Sleekflow.Locks;
using Sleekflow.Models.Events;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.FlowHubConfigs;

public interface IFlowHubConfigService
{
    Task<bool> IsNotEnrolledAsync(string sleekflowCompanyId);

    Task<bool> IsStepLimitExceededAsync(string sleekflowCompanyId);

    Task<FlowHubConfig> GetFlowHubConfigAsync(string sleekflowCompanyId);

    Task<int> GetRemainingStepLimitAsync(string sleekflowCompanyId);

    Task<FlowHubConfig> UnenrollFlowHubAsync(string sleekflowCompanyId, AuditEntity.SleekflowStaff sleekflowStaff);

    Task<FlowHubConfig> EnrollFlowHubAsync(
        string sleekflowCompanyId,
        string origin,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<FlowHubConfig> UpdateFlowHubConfigAsync(
        string sleekflowCompanyId,
        UsageLimit usageLimit,
        UsageLimitOffset? usageLimitOffset,
        string? origin,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<FlowHubConfig> ToggleFlowUsageLimitAsync(
        string sleekflowCompanyId,
        bool isUsageLimitEnabled);

    Task<FlowHubConfig> EnableUsageLimitOffsetAsync(string sleekflowCompanyId, bool isEnableUsageLimitOffset);

    Task<FlowHubConfig> EnableUsageLimitAutoScalingAsync(string sleekflowCompanyId, bool isEnableUsageLimitAutoScaling);

    Task<(List<FlowHubConfig> FlowHubConfigs, string? NextContinuationToken)> GetEnrolledFlowHubConfigsAsync(
        string? continuationToken,
        int limit);
}

public class FlowHubConfigService : IFlowHubConfigService, IScopedService
{
    private readonly ICompanyUsageCycleService _companyUsageCycleService;
    private readonly IFlowHubConfigRepository _flowHubConfigRepository;
    private readonly ILockService _lockService;
    private readonly IIdService _idService;
    private readonly IBus _bus;

    public FlowHubConfigService(
        ICompanyUsageCycleService companyUsageCycleService,
        IFlowHubConfigRepository flowHubConfigRepository,
        ILockService lockService,
        IIdService idService,
        IBus bus)
    {
        _companyUsageCycleService = companyUsageCycleService;
        _flowHubConfigRepository = flowHubConfigRepository;
        _lockService = lockService;
        _idService = idService;
        _bus = bus;
    }

    public Task<bool> IsNotEnrolledAsync(string sleekflowCompanyId)
    {
        return Task.FromResult(false);
    }

    public Task<bool> IsStepLimitExceededAsync(string sleekflowCompanyId)
    {
        // TODO: Counter Grain
        return Task.FromResult(false);
    }

    public async Task<FlowHubConfig> GetFlowHubConfigAsync(string sleekflowCompanyId)
    {
        var flowHubConfigs =
            await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

        var flowHubConfig = flowHubConfigs.SingleOrDefault();

        if (flowHubConfig != null)
        {
            return flowHubConfig;
        }

        return new FlowHubConfig(
            false,
            null,
            null,
            null,
            "UNENROLLED",
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowCompanyId,
            null,
            null,
            true,
            false,
            false);
    }

    public Task<int> GetRemainingStepLimitAsync(string sleekflowCompanyId)
    {
        throw new NotImplementedException();
    }

    public async Task<FlowHubConfig> UnenrollFlowHubAsync(
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(FlowHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var flowHubConfigs =
                await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (flowHubConfigs.Count == 0)
            {
                return await _flowHubConfigRepository.CreateAndGetAsync(
                    new FlowHubConfig(
                        false,
                        null,
                        null,
                        null,
                        _idService.GetId("FlowHubConfig"),
                        string.Empty,
                        DateTimeOffset.UtcNow,
                        DateTimeOffset.UtcNow,
                        sleekflowCompanyId,
                        sleekflowStaff,
                        null,
                        false,
                        false,
                        false),
                    sleekflowCompanyId);
            }

            var flowHubConfig = flowHubConfigs.Single();

            var patchedFlowHubConfig = await _flowHubConfigRepository.PatchAndGetAsync(
                flowHubConfig.Id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/is_enrolled", false)
                },
                flowHubConfig.ETag);

            await _bus.Publish(
                new OnFlowHubUnenrolledEvent(
                    sleekflowCompanyId,
                    sleekflowStaff.SleekflowStaffId));

            return patchedFlowHubConfig;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<FlowHubConfig> EnrollFlowHubAsync(
        string sleekflowCompanyId,
        string origin,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(FlowHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var flowHubConfigs =
                await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (flowHubConfigs.Count == 0)
            {
                return await _flowHubConfigRepository.CreateAndGetAsync(
                    new FlowHubConfig(
                        true,
                        null,
                        null,
                        null,
                        _idService.GetId("FlowHubConfig"),
                        origin,
                        DateTimeOffset.UtcNow,
                        DateTimeOffset.UtcNow,
                        sleekflowCompanyId,
                        sleekflowStaff,
                        null,
                        true,
                        false,
                        false),
                    sleekflowCompanyId);
            }

            var flowHubConfig = flowHubConfigs.Single();

            var patchedFlowHubConfig = await _flowHubConfigRepository.PatchAndGetAsync(
                flowHubConfig.Id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/is_enrolled", true), PatchOperation.Set("/origin", origin)
                },
                flowHubConfig.ETag);

            await _bus.Publish(
                new OnFlowHubEnrolledEvent(
                    sleekflowCompanyId,
                    sleekflowStaff.SleekflowStaffId));

            return patchedFlowHubConfig;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<FlowHubConfig> UpdateFlowHubConfigAsync(
        string sleekflowCompanyId,
        UsageLimit usageLimit,
        UsageLimitOffset? usageLimitOffset,
        string? origin,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(FlowHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));
        try
        {
            var flowHubConfigs =
                await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (flowHubConfigs.Count == 0)
            {
                return await _flowHubConfigRepository.CreateAndGetAsync(
                    new FlowHubConfig(
                        true,
                        usageLimit,
                        usageLimitOffset,
                        null,
                        _idService.GetId("FlowHubConfig"),
                        origin,
                        DateTimeOffset.UtcNow,
                        DateTimeOffset.UtcNow,
                        sleekflowCompanyId,
                        sleekflowStaff,
                        sleekflowStaff,
                        true,
                        false,
                        false),
                    sleekflowCompanyId);
            }

            var flowHubConfig = flowHubConfigs.Single();

            var config = await _flowHubConfigRepository.PatchAndGetAsync(
                flowHubConfig.Id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/usage_limit", usageLimit),
                    PatchOperation.Set("/usage_limit_offset", usageLimitOffset ?? flowHubConfig.UsageLimitOffset),
                    PatchOperation.Set("/updated_at", DateTimeOffset.UtcNow),
                    PatchOperation.Set("/updated_by", sleekflowStaff ?? flowHubConfig.UpdatedBy)
                },
                flowHubConfig.ETag);

            //// Force Usage Cycle Update on FLowHubConfig updated.
            await _companyUsageCycleService.GetUsageCycleAsync(config, true);

            return config;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<FlowHubConfig> ToggleFlowUsageLimitAsync(string sleekflowCompanyId, bool isUsageLimitEnabled)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(FlowHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var flowHubConfigs =
                await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);
            if (flowHubConfigs.Count == 0)
            {
                throw new SfNotFoundObjectException(nameof(FlowHubConfig.Id), sleekflowCompanyId);
            }

            var flowHubConfig = flowHubConfigs.Single();

            return await _flowHubConfigRepository.PatchAndGetAsync(
                flowHubConfig.Id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/is_usage_limit_enabled", isUsageLimitEnabled),
                    PatchOperation.Set("/updated_at", DateTimeOffset.UtcNow)
                },
                flowHubConfig.ETag);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    /// <inheritdoc />
    public async Task<FlowHubConfig> EnableUsageLimitOffsetAsync(string sleekflowCompanyId, bool isEnableUsageLimitOffset)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            [ nameof(FlowHubConfigService), sleekflowCompanyId ],
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var flowHubConfigs = await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (flowHubConfigs.Count == 0)
            {
                throw new SfNotFoundObjectException(nameof(FlowHubConfig.Id), sleekflowCompanyId);
            }

            var flowHubConfig = flowHubConfigs.Single();

            return await _flowHubConfigRepository.PatchAndGetAsync(
                flowHubConfig.Id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/is_usage_limit_offset_enabled", isEnableUsageLimitOffset),
                    PatchOperation.Set("/updated_at", DateTimeOffset.UtcNow)
                },
                flowHubConfig.ETag);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    /// <inheritdoc />
    public async Task<FlowHubConfig> EnableUsageLimitAutoScalingAsync(string sleekflowCompanyId, bool isEnableUsageLimitAutoScaling)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            [ nameof(FlowHubConfigService), sleekflowCompanyId ],
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var flowHubConfigs = await _flowHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (flowHubConfigs.Count == 0)
            {
                throw new SfNotFoundObjectException(nameof(FlowHubConfig.Id), sleekflowCompanyId);
            }

            var flowHubConfig = flowHubConfigs.Single();

            return await _flowHubConfigRepository.PatchAndGetAsync(
                flowHubConfig.Id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/is_usage_limit_auto_scaling_enabled", isEnableUsageLimitAutoScaling),
                    PatchOperation.Set("/updated_at", DateTimeOffset.UtcNow)
                },
                flowHubConfig.ETag);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }

    }

    public async Task<(List<FlowHubConfig> FlowHubConfigs, string? NextContinuationToken)>
        GetEnrolledFlowHubConfigsAsync(
            string? continuationToken,
            int limit)
    {
        return await _flowHubConfigRepository.GetContinuationTokenizedObjectsAsync(
            x => x.IsEnrolled == true,
            continuationToken,
            limit);
    }
}