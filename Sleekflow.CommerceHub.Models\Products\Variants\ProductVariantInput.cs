using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Products.Variants;

public abstract class ProductVariantInput
{
    [StringLength(128, MinimumLength = 1)]
    [JsonProperty(ProductVariant.PropertyNameSku)]
    public string? Sku { get; set; }

    [StringLength(128, MinimumLength = 1)]
    [JsonProperty(ProductVariant.PropertyNameUrl)]
    public string? Url { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(ProductVariant.PropertyNamePrices)]
    public List<Price> Prices { get; set; }

    [Required]
    [JsonProperty(ProductVariant.PropertyNamePosition)]
    public int Position { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(ProductVariant.PropertyNameAttributes)]
    public List<ProductVariant.ProductVariantAttribute> Attributes { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(ProductVariant.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(ProductVariant.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [Required]
    [ValidateArray]
    [JsonProperty(ProductVariant.PropertyNameImages)]
    public List<ImageDto> Images { get; set; }

    [ValidateObject]
    [JsonProperty(ProductVariant.PropertyNamePlatformData)]
    public PlatformData? PlatformData { get; set; }

    [JsonConstructor]
    protected ProductVariantInput(
        string? sku,
        string? url,
        List<Price> prices,
        int position,
        List<ProductVariant.ProductVariantAttribute> attributes,
        List<Multilingual> names,
        List<Description> descriptions,
        List<ImageDto> images,
        PlatformData? platformData)
    {
        Sku = sku;
        Url = url;
        Prices = prices;
        Position = position;
        Attributes = attributes;
        Names = names;
        Descriptions = descriptions;
        Images = images;
        PlatformData = platformData;
    }
}