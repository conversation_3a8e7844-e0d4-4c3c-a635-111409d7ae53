<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.Integrator.Dynamics365" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.Integrator.Dynamics365/bin/Debug/net8.0/Sleekflow.Integrator.Dynamics365.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.Integrator.Dynamics365" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7088;http://localhost:7089" />
      <env name="CACHE_PREFIX" value="Sleekflow.Integrator.Dynamics365" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="COSMOS_CRM_HUB_DB_DATABASE_ID" value="crmhubdb" />
      <env name="COSMOS_CRM_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_CRM_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_CRM_HUB_INTEGRATION_DB_DATABASE_ID" value="crmhubintegrationdb" />
      <env name="COSMOS_CRM_HUB_INTEGRATION_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_CRM_HUB_INTEGRATION_DB_KEY" value="****************************************************************************************" />
      <env name="DYNAMICS365_CLIENT_ID" value="2295be6f-255c-41b6-9f4b-8e0a586665e6" />
      <env name="DYNAMICS365_CLIENT_SECRET" value="a99cad2b-49d5-4f80-90a5-b322ec493f82" />
      <env name="DYNAMICS365_OAUTH_CALLBACK_URL" value="https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/d365/AuthenticateCallback" />
      <env name="DYNAMICS365_OAUTH_STATE_ENCRYPTION_KEY" value="encrypt_key_20220509" />
      <env name="DYNAMICS365_WEBHOOK_CALLBACK_URL" value="https://webhook.site/696a5fcb-3597-474e-8b62-50bb83adfcef" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="WORKER_FUNCTIONS_KEY" value="PLACEHOLDER" />
      <env name="WORKER_HOSTNAME" value="http://localhost:7076" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.Integrator.Dynamics365/Sleekflow.Integrator.Dynamics365.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>