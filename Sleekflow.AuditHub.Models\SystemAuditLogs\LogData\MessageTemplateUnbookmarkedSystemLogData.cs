using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class MessageTemplateUnbookmarkedSystemLogData
{
    [JsonProperty("template_id")]
    public string TemplateId { get; set; }

    [JsonProperty("channel_type")]
    public string ChannelType { get; set; }

    [JsonProperty("channel_identity_id")]
    public string ChannelIdentityId { get; set; }

    [JsonProperty("template_name")]
    public string TemplateName { get; set; }

    [JsonConstructor]
    public MessageTemplateUnbookmarkedSystemLogData(string templateId, string channelType, string channelIdentityId,
        string templateName)
    {
        TemplateId = templateId;
        ChannelType = channelType;
        ChannelIdentityId = channelIdentityId;
        TemplateName = templateName;
    }
}