﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.ActionEvents.CrmHub;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateSchemafulObjectV2StepExecutor : IStepExecutor;

public class CreateSchemafulObjectV2StepExecutor
    : GeneralStepExecutor<CallStep<CreateSchemafulObjectV2StepArgs>>,
        ICreateSchemafulObjectV2StepExecutor,
        IScopedService
{
    private const string AuditSource = "flow_builder";

    private readonly IStateEvaluator _stateEvaluator;
    private readonly IRequestClient<CreateSchemafulObjectRequest> _createSchemafulObjectRequestClient;

    public CreateSchemafulObjectV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IRequestClient<CreateSchemafulObjectRequest> createSchemafulObjectRequestClient)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _createSchemafulObjectRequestClient = createSchemafulObjectRequestClient;
        _stateEvaluator = stateEvaluator;
        _createSchemafulObjectRequestClient = createSchemafulObjectRequestClient;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _createSchemafulObjectRequestClient.GetResponse<CreateSchemafulObjectReply>(
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<CreateSchemafulObjectRequest> GetArgs(
        CallStep<CreateSchemafulObjectV2StepArgs> callStep,
        ProxyState state)
    {
        var contactId = await GetContactIdAsync(callStep, state);

        var primaryPropertyValue =
            !string.IsNullOrWhiteSpace(callStep.Args.PrimaryPropertyValueExpr)
                ? (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.PrimaryPropertyValueExpr)
                            ?? callStep.Args.PrimaryPropertyValueExpr)
                : string.Empty;

        var schemafulObjectPropertyValues = new Dictionary<string, object?>();

        if (callStep.Args.MandatoryPropertiesIdExprDict is { Count: > 0 } mandatoryProperties)
        {
            foreach (var entry in mandatoryProperties)
            {
                schemafulObjectPropertyValues[entry.Key] =
                    await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, entry.Value);
            }
        }

        if (callStep.Args.OptionalPropertiesIdExprDict is { Count: > 0 } optionalProperties)
        {
            foreach (var entry in optionalProperties)
            {
                schemafulObjectPropertyValues[entry.Key] =
                    await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, entry.Value);
            }
        }

        return new CreateSchemafulObjectRequest(
            callStep.Args.SchemaId,
            state.Identity.SleekflowCompanyId,
            primaryPropertyValue,
            schemafulObjectPropertyValues,
            contactId,
            AuditSource);
    }

    private async Task<string?> GetContactIdAsync(CallStep<CreateSchemafulObjectV2StepArgs> callStep, ProxyState state)
    {
        var contactNumber = (string?) await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
            state,
            callStep.Args.ContactNumberExpr);

        if (string.IsNullOrWhiteSpace(contactNumber))
        {
            throw new InvalidOperationException(
                $"Contact number not specified from expression {callStep.Args.ContactNumberExpr}");
        }

        var locateContactIdExpression = $"{{{{ \"{contactNumber}\" | sleekflow.get_or_create_contact_id_by_phone_number }}}}";

        var contactId = (string?) await _stateEvaluator.EvaluateExpressionAsync(
            state,
            locateContactIdExpression);

        if (string.IsNullOrWhiteSpace(contactId))
        {
            throw new InvalidOperationException(
                $"Contact id not found by expression {locateContactIdExpression}");
        }

        return contactId;
    }
}