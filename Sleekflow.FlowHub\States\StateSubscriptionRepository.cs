using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.States;

public interface IStateSubscriptionRepository : IRepository<StateSubscription>
{
}

public class StateSubscriptionRepository
    : BaseRepository<StateSubscription>, IStateSubscriptionRepository, IScopedService
{
    public StateSubscriptionRepository(
        ILogger<StateSubscriptionRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}