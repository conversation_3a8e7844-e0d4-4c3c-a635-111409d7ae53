﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeleteObject : ITrigger
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public DeleteObject(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class DeleteObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public DeleteObjectInput(
            string sleekflowCompanyId,
            string objectId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            EntityTypeName = entityTypeName;
        }
    }

    public class DeleteObjectOutput
    {
    }

    public async Task<DeleteObjectOutput> F(DeleteObjectInput deleteObjectInput)
    {
        var authentication =
            await _hubspotAuthenticationService.GetAsync(deleteObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        await _hubspotObjectService.DeleteAsync(
            authentication,
            deleteObjectInput.ObjectId,
            deleteObjectInput.EntityTypeName);

        return new DeleteObjectOutput();
    }
}