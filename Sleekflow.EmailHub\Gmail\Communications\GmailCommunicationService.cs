using System.Net.Http.Headers;
using System.Text;
using Google.Apis.Gmail.v1.Data;
using MassTransit;
using MimeKit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Attachments;
using Sleekflow.EmailHub.Gmail.Authentications;
using Sleekflow.EmailHub.Gmail.Subscriptions;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Gmail.Authentications;
using Sleekflow.EmailHub.Models.Gmail.Communications;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Models.Gmail.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Convert = System.Convert;
using Message = Google.Apis.Gmail.v1.Data.Message;

namespace Sleekflow.EmailHub.Gmail.Communications;

public interface IGmailCommunicationService : IEmailCommunicationService
{
    Task TriggerPartialSyncEmailsAsync(
        string emailAddress,
        CancellationToken cancellationToken = default);
}

public class GmailCommunicationService : IScopedService, IGmailCommunicationService
{
    private readonly HttpClient _httpClient;
    private readonly IIdService _idService;
    private readonly IGmailAuthenticationService _gmailAuthenticationService;
    private readonly IGmailSubscriptionService _gmailSubscriptionService;
    private readonly IEmailRepository _emailRepository;
    private readonly ILogger<GmailCommunicationService> _logger;
    private readonly IBus _bus;
    private readonly IAttachmentService _attachmentService;
    private readonly IProviderConfigService _providerConfigService;

    public GmailCommunicationService(
        IHttpClientFactory httpClientFactory,
        IIdService idService,
        IGmailAuthenticationService gmailAuthenticationService,
        IGmailSubscriptionService gmailSubscriptionService,
        ILogger<GmailCommunicationService> logger,
        IEmailRepository emailRepository,
        IBus bus,
        IAttachmentService attachmentService,
        IProviderConfigService providerConfigService)
    {
        _idService = idService;
        _gmailAuthenticationService = gmailAuthenticationService;
        _gmailSubscriptionService = gmailSubscriptionService;
        _logger = logger;
        _emailRepository = emailRepository;
        _bus = bus;
        _attachmentService = attachmentService;
        _providerConfigService = providerConfigService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task OnReceiveEmailAsync(
        List<string> sleekflowCompanyIds,
        string emailAddress,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _bus.Publish(
            new OnGmailPartialSyncTriggeredEvent(emailAddress),
            cancellationToken);
    }

    private async Task StoreReceivedGmailMessages(
        List<Message> receivedGmailMessages,
        string emailAddress,
        string companyId,
        CancellationToken cancellationToken = default)
    {
        if (receivedGmailMessages.Count == 0)
        {
            _logger.LogError(
                "Cannot receive gmail from gmail's webhook: : emailAddress {emailAddress} of sleekflowCompanyIds {sleekflowCompanyId}",
                emailAddress,
                companyId);

            return;
        }

        _logger.LogInformation("[StoreReceivedGmailMessages] Started to store gmails to Db");

        foreach (var receivedGmail in receivedGmailMessages)
        {
            try
            {
                var emailId = _idService.GetId("Email");

                // Gmail does not provide standard base64, refer to this so answer https://stackoverflow.com/a/15114175/7761918
                var emailBytes = Convert.FromBase64String(receivedGmail.Raw.Replace("-", "+").Replace("_", "/"));

                using MemoryStream mm = new MemoryStream(emailBytes);
                var message = await MimeMessage.LoadAsync(mm, cancellationToken);

                var from = message.From.ToList().ConvertAll(x => (MailboxAddress) x)
                    .Select(x => new EmailContact(x.Address, x.Name)).ToList();

                var to = message.To.ToList().ConvertAll(x => (MailboxAddress) x)
                    .Select(x => new EmailContact(x.Address, x.Name)).ToList();

                var cc = message.Cc.ToList().ConvertAll(x => (MailboxAddress) x)
                    .Select(x => new EmailContact(x.Address, x.Name)).ToList();

                var bcc = message.Bcc.ToList().ConvertAll(x => (MailboxAddress) x)
                    .Select(x => new EmailContact(x.Address, x.Name)).ToList();

                var replyTo = message.ReplyTo.ToList().ConvertAll(x => (MailboxAddress) x)
                    .Select(x => new EmailContact(x.Address, x.Name)).ToList();

                var subject = message.Subject;

                var attachments = new List<EmailAttachment>();

                foreach (var attachment in message.Attachments)
                {
                    await _attachmentService.ProcessInboundAttachment(
                        companyId,
                        emailId,
                        (message.To.FirstOrDefault() ?? throw new SfNotFoundObjectException(string.Empty)) as
                        MailboxAddress ??
                        throw new SfInternalErrorException(string.Empty),
                        attachment,
                        attachments,
                        cancellationToken);
                }

                var gmailEmailMetadata = new GmailEmailMetadata(
                    receivedGmail.Id,
                    receivedGmail.ThreadId,
                    receivedGmail.LabelIds,
                    receivedGmail.Snippet,
                    receivedGmail.Payload,
                    receivedGmail.SizeEstimate,
                    receivedGmail.HistoryId,
                    receivedGmail.InternalDate,
                    null);

                var htmlBody = message.HtmlBody;

                var textBody = message.TextBody;

                var email = new Email(
                    emailId,
                    companyId,
                    from[0],
                    from,
                    to,
                    cc,
                    bcc,
                    replyTo,
                    subject,
                    htmlBody,
                    textBody,
                    false,
                    attachments,
                    gmailEmailMetadata);

                _logger.LogInformation(
                    $"Receive email from gmail webhook: emailAddress {emailAddress} of sleekflowCompanyId {companyId}",
                    emailAddress);
                await _emailRepository.UpsertAsync(email, email.Id, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Error while receiving gmails: emailAddress {emailAddress} of sleekflowCompanyIds {companyId}: Exception {ex}",
                    emailAddress,
                    companyId,
                    ex);
            }
        }
    }

    public async Task HandleSendEmailEventAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        var authentication =
            await _gmailAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                sender.EmailAddress,
                cancellationToken: cancellationToken);

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new NullReferenceException("Cannot parse gmailAuthenticationMetaData");
        ulong? historyId = null;

        if (emailMetadata != null &&
            emailMetadata.TryGetValue("historyId", out var historyIdString))
        {
            historyId = ulong.Parse(historyIdString);
        }

        var mimeMessage = new MimeMessage();

        mimeMessage.From.Add(new MailboxAddress(sender.Name, sender.EmailAddress));

        var toAddresses = to.ToList();

        foreach (var recipient in toAddresses)
        {
            mimeMessage.To.Add(new MailboxAddress(recipient.Name, recipient.EmailAddress));
        }

        var ccAddresses = cc.ToList();

        foreach (var ccAddress in ccAddresses)
        {
            mimeMessage.Cc.Add(new MailboxAddress(ccAddress.Name, ccAddress.EmailAddress));
        }

        var bccAddresses = bcc.ToList();

        foreach (var bccAddress in bccAddresses)
        {
            mimeMessage.Bcc.Add(new MailboxAddress(bccAddress.Name, bccAddress.EmailAddress));
        }

        mimeMessage.Subject = subject;

        var replyToAddresses = replyTo.ToList();

        foreach (var replyToAddress in replyToAddresses)
        {
            mimeMessage.ReplyTo.Add(new MailboxAddress(replyToAddress.Name, replyToAddress.EmailAddress));
        }

        var builder = new BodyBuilder
        {
            HtmlBody = htmlBody ?? string.Empty, TextBody = textBody ?? string.Empty
        };

        foreach (var emailAttachment in emailAttachments)
        {
            await _attachmentService.ProcessOutboundAttachment(emailAttachment, builder, cancellationToken);
        }

        mimeMessage.Body = builder.ToMessageBody();

        var raw = string.Empty;

        using (var memory = new MemoryStream())
        {
            await mimeMessage.WriteToAsync(memory, cancellationToken);

            var blob = memory.ToArray();
            raw = Convert.ToBase64String(blob);
        }

        var message = new Message
        {
            Raw = raw, HistoryId = historyId,
        };

        var sendEmailRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"https://gmail.googleapis.com/gmail/v1/users/{sender.EmailAddress}/messages/send"),
            Content = new StringContent(JsonConvert.SerializeObject(message), Encoding.UTF8, "application/json")
        };

        sendEmailRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            gmailAuthenticationMetadata.TokenType ?? "Bearer",
            gmailAuthenticationMetadata.AccessToken);

        var sendEmailResponse = await _httpClient.SendAsync(sendEmailRequestMessage, cancellationToken);

        var sendEmailResponseContent = await sendEmailResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!sendEmailResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(sendEmailResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} SendEmailAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                sendEmailResponse.StatusCode,
                errorMsg,
                sender,
                sleekflowCompanyId);

            throw new SfInternalErrorException($"HTTP[{sendEmailResponse.StatusCode}] {errorMsg}");
        }

        var receivedMessage = JsonConvert.DeserializeObject<Message>(sendEmailResponseContent);

        if (receivedMessage == null)
        {
            throw new SfInternalErrorException(
                $"Error on sending gmail message: emailAddress {sender.EmailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var emailId = _idService.GetId("Email");

        await _emailRepository.UpsertAsync(
            new Email(
                emailId,
                sleekflowCompanyId,
                sender,
                new List<EmailContact>
                {
                    sender
                },
                toAddresses,
                ccAddresses,
                bccAddresses,
                replyToAddresses,
                subject,
                null,
                textBody,
                true,
                emailAttachments.ToList(),
                new GmailEmailMetadata(
                    message.Id,
                    message.ThreadId,
                    message.LabelIds,
                    message.Snippet,
                    message.Payload,
                    message.SizeEstimate,
                    message.HistoryId,
                    message.InternalDate,
                    null)),
            emailId,
            cancellationToken: cancellationToken);
    }

    public async Task SendEmailAsync(
        string sleekflowCompanyId,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata,
        CancellationToken cancellationToken = default)
    {
        await _bus.Publish(
            new OnSendEmailTriggeredEvent(
                sleekflowCompanyId,
                ProviderNames.Gmail,
                sender,
                subject,
                to,
                cc,
                bcc,
                replyTo,
                htmlBody,
                textBody,
                emailAttachments,
                emailMetadata),
            cancellationToken);
    }

    public async Task SyncAllEmailsAsync(string emailAddress, CancellationToken cancellationToken = default)
    {
        var sleekflowCompanyIds = await _gmailSubscriptionService.FilterSubscribedCompanies(
            await _providerConfigService.GetCompanyIdsByEmailAddressAsync(emailAddress, cancellationToken),
            emailAddress,
            cancellationToken);

        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            var newMailCandidates =
                (await GetGmailInboxAllMessagesAsync(
                    emailAddress,
                    sleekflowCompanyId,
                    cancellationToken: cancellationToken))
                .OrderByDescending(x => x.InternalDate)
                .ToList();

            var allExistingEmails = (await _emailRepository.GetObjectsAsync(
                    x =>
                        x.SleekflowCompanyId == sleekflowCompanyId
                        && (x.To.Any(y => y.EmailAddress == emailAddress)
                            || x.Cc.Any(z => z.EmailAddress == emailAddress)
                            || x.Bcc.Any(w => w.EmailAddress == emailAddress)
                        )
                        && x.EmailMetadata.ProviderName == ProviderNames.Gmail,
                    cancellationToken: cancellationToken))
                .OrderByDescending(x => x.EmailMetadata.SentAt)
                .ToList();

            if (!allExistingEmails.Any())
            {
                await StoreReceivedGmailMessages(
                    newMailCandidates,
                    emailAddress,
                    sleekflowCompanyId,
                    cancellationToken);

                return;
            }

            var latestEmailStoredInSleekflow = allExistingEmails[0];

            var toBeAddedCandidates = newMailCandidates.Where(
                    x => x.InternalDate > latestEmailStoredInSleekflow.EmailMetadata.SentAt.ToUnixTimeMilliseconds())
                .ToList();

            var toBeUpdatedCandidates = newMailCandidates.Where(
                    x => x.InternalDate <= latestEmailStoredInSleekflow.EmailMetadata.SentAt.ToUnixTimeMilliseconds())
                .ToList();

            await UpdateEmailsFromGmailsFullSyncAsync(
                toBeUpdatedCandidates,
                allExistingEmails,
                cancellationToken);

            await StoreReceivedGmailMessages(
                toBeAddedCandidates,
                emailAddress,
                sleekflowCompanyId,
                cancellationToken);
        }
    }

    private async Task UpdateEmailsFromGmailsFullSyncAsync(
        List<Message> toBeUpdatedCandidates,
        List<Email> allExistingEmails,
        CancellationToken cancellationToken = default)
    {
        foreach (var candidate in toBeUpdatedCandidates)
        {
            var toBeUpdatedEmail = allExistingEmails.FirstOrDefault(
                x =>
                {
                    var metadata = x.EmailMetadata as GmailEmailMetadata ??
                                   throw new NullReferenceException("Cannot parse gmail metadata");

                    return metadata.MessageId == candidate.Id;
                });

            if (toBeUpdatedEmail == null)
            {
                continue;
            }

            var metadata = toBeUpdatedEmail.EmailMetadata as GmailEmailMetadata ??
                           throw new NullReferenceException("Cannot parse gmail metadata");

            if (metadata.LabelIds.SequenceEqual(candidate.LabelIds))
            {
                continue;
            }

            metadata.LabelIds = candidate.LabelIds;
            toBeUpdatedEmail.EmailMetadata = metadata;

            await _emailRepository.UpsertAsync(
                toBeUpdatedEmail,
                toBeUpdatedEmail.Id,
                cancellationToken: cancellationToken);
        }
    }

    public async Task TriggerPartialSyncEmailsAsync(string emailAddress, CancellationToken cancellationToken = default)
    {
        var sleekflowCompanyIds = await _gmailSubscriptionService.FilterSubscribedCompanies(
            await _providerConfigService.GetCompanyIdsByEmailAddressAsync(emailAddress, cancellationToken),
            emailAddress,
            cancellationToken);

        foreach (var sleekflowCompanyId in sleekflowCompanyIds)
        {
            var authentication =
                await _gmailAuthenticationService.GetAuthenticationAsync(
                    sleekflowCompanyId,
                    emailAddress,
                    cancellationToken: cancellationToken);

            var subscription =
                await _gmailSubscriptionService.GetSubscriptionAsync(
                    sleekflowCompanyId,
                    emailAddress,
                    cancellationToken);

            var gmailAuthenticationMetadata =
                authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                throw new SfInternalErrorException(
                    $"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

            var gmailSubscriptionMetadata =
                subscription.EmailSubscriptionMetadata as GmailSubscriptionMetadata ??
                throw new SfInternalErrorException(
                    $"Cannot parse gmailSubscriptionMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

            var historyRequestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(
                    $"https://gmail.googleapis.com/gmail/v1/users/me/history?startHistoryId={gmailSubscriptionMetadata.HistoryId}&maxResults=500")
            };

            historyRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
                gmailAuthenticationMetadata.TokenType ?? "Bearer",
                gmailAuthenticationMetadata.AccessToken);

            var historyResponse = await _httpClient.SendAsync(historyRequestMessage, cancellationToken);
            var historyResponseContent = await historyResponse.Content.ReadAsStringAsync(cancellationToken);

            if (!historyResponse.IsSuccessStatusCode)
            {
                var errorObject = JObject.Parse(historyResponseContent);
                var errorMsg = errorObject["error"]?.ToString();

                _logger.LogError(
                    "HTTP[{statusCode}] {errorMsg} GetGmailByLimitAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                    historyResponse.StatusCode,
                    errorMsg,
                    emailAddress,
                    sleekflowCompanyId);

                throw new SfInternalErrorException($"HTTP[{historyResponse.StatusCode}] {errorMsg}");
            }

            var historyResponseModel = JsonConvert.DeserializeObject<ListHistoryResponse>(historyResponseContent);

            await _gmailSubscriptionService.UpdateHistoryId(
                sleekflowCompanyId,
                emailAddress,
                historyResponseModel?.HistoryId ?? throw new SfInternalErrorException(
                    $"Invalid History Id: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}"),
                cancellationToken);

            var histories = historyResponseModel.History ?? new List<History>();

            var messageIds = new List<string>();

            foreach (var history in histories)
            {
                messageIds.AddRange(history.Messages.Select(x => x.Id).ToList());
            }

            messageIds = messageIds.Distinct().ToList();

            var result = new List<Message>();

            foreach (var msg in messageIds.Chunk(100))
            {
                result.AddRange(
                    await GetGmailMessagesByMessageIdsAsync(
                        emailAddress,
                        sleekflowCompanyId,
                        msg.ToList(),
                        cancellationToken: cancellationToken));
            }

            var allExistingEmails = (await _emailRepository.GetObjectsAsync(
                    x =>
                        messageIds.Contains(((GmailEmailMetadata) x.EmailMetadata).MessageId),
                    cancellationToken: cancellationToken))
                .OrderByDescending(x => x.EmailMetadata.SentAt)
                .ToList();

            var toBeAddedCandidates = result.Where(
                    x => !allExistingEmails.Select(y => (y.EmailMetadata as GmailEmailMetadata)!.MessageId)
                        .Contains(x.Id))
                .ToList();

            var toBeUpdatedCandidates = result.Where(
                    x => allExistingEmails.Select(y => (y.EmailMetadata as GmailEmailMetadata)!.MessageId)
                        .Contains(x.Id))
                .ToList();

            await UpdateEmailsFromGmailsFullSyncAsync(
                toBeUpdatedCandidates,
                allExistingEmails,
                cancellationToken);

            await StoreReceivedGmailMessages(
                toBeAddedCandidates,
                emailAddress,
                sleekflowCompanyId,
                cancellationToken);
        }
    }

    private async Task<List<Message>> GetGmailInboxAllMessagesAsync(
        string emailAddress,
        string sleekflowCompanyId,
        string format = "raw",
        CancellationToken cancellationToken = default)
    {
        var authentication = await _gmailAuthenticationService.GetAuthenticationAsync(
            sleekflowCompanyId,
            emailAddress,
            cancellationToken: cancellationToken);

        if (authentication is null)
        {
            _logger.LogError(
                "GetGmailByLimitAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId} is not authenticated yet",
                emailAddress,
                sleekflowCompanyId);

            throw new SfUnauthorizedException();
        }

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new NullReferenceException(
                                              $"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        ListMessagesResponse? messagesResponseModel = null;
        var messages = new List<Message>();

        do
        {
            var messagesRequestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(
                    $"https://gmail.googleapis.com/gmail/v1/users/{emailAddress}/messages" +
                    (messagesResponseModel == null
                        ? string.Empty
                        : $"?pageToken={messagesResponseModel.NextPageToken}&format={format}")),
            };

            messagesRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
                gmailAuthenticationMetadata.TokenType ?? "Bearer",
                gmailAuthenticationMetadata.AccessToken);

            var messagesResponse = await _httpClient.SendAsync(messagesRequestMessage, cancellationToken);
            var messagesResponseContent = await messagesResponse.Content.ReadAsStringAsync(cancellationToken);

            if (!messagesResponse.IsSuccessStatusCode)
            {
                var errorObject = JObject.Parse(messagesResponseContent);
                var errorMsg = errorObject["error"]?.ToString();

                _logger.LogError(
                    "HTTP[{statusCode}] {errorMsg} GetGmailByLimitAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                    messagesResponse.StatusCode,
                    errorMsg,
                    emailAddress,
                    sleekflowCompanyId);

                throw new SfInternalErrorException($"HTTP[{messagesResponse.StatusCode}] {errorMsg}");
            }

            messagesResponseModel = JsonConvert.DeserializeObject<ListMessagesResponse>(messagesResponseContent)!;
            messages.AddRange(messagesResponseModel.Messages);
        }
        while (!string.IsNullOrEmpty(messagesResponseModel.NextPageToken));

        var messagesResult = new List<Message>();

        foreach (var message in messages.Chunk(100))
        {
            messagesResult.AddRange(
                await GetGmailMessagesByMessageIdsAsync(
                    emailAddress,
                    sleekflowCompanyId,
                    message.Select(x => x.Id).ToList(),
                    cancellationToken: cancellationToken));
        }

        _logger.LogInformation(
            "GetGmailByLimitAsync successes: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        return messagesResult;
    }

    private async Task<List<Message>> GetGmailMessagesByMessageIdsAsync(
        string emailAddress,
        string sleekflowCompanyId,
        List<string> messageIds,
        string format = "raw",
        CancellationToken cancellationToken = default)
    {
        var authentication = await _gmailAuthenticationService.GetAuthenticationAsync(
            sleekflowCompanyId,
            emailAddress,
            cancellationToken: cancellationToken);

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new NullReferenceException(
                                              $"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var messagesMultipartContent = new MultipartContent("mixed", "sleekflow-emailhub");

        foreach (var messageId in messageIds)
        {
            var part = new List<string>
            {
                $"GET /gmail/v1/users/{emailAddress}/messages/{messageId}?format={format}",
                "Accept: application/json; charset=UTF-8"
            };
            messagesMultipartContent.Add(new StringContent(string.Join('\n', part), Encoding.UTF8));
        }

        var messagesRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"https://gmail.googleapis.com/batch/gmail/v1"),
            Content = messagesMultipartContent
        };

        messagesRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            gmailAuthenticationMetadata.TokenType ?? "Bearer",
            gmailAuthenticationMetadata.AccessToken);

        var messagesResponse = await _httpClient.SendAsync(messagesRequestMessage, cancellationToken);

        var messagesResponseContent = await messagesResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!messagesResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(messagesResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} GetGmailMessagesByMessageIdsAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                messagesResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"HTTP[{messagesResponse.StatusCode}] {errorMsg}: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        return ParseObjectsFromMultipartMixedResponse<Message>(messagesResponseContent, "--batch");
    }

    private List<TObj> ParseObjectsFromMultipartMixedResponse<TObj>(string raw, string boundary)
    {
        var result = new List<TObj>();
        var lines = raw.Split("\r\n");

        for (var i = 0; i < lines.Length; i++)
        {
            if (lines[i].StartsWith(boundary))
            {
                for (var j = i; j < lines.Length; j++)
                {
                    if (lines[j].StartsWith('{'))
                    {
                        var obj = JsonConvert.DeserializeObject<TObj>(lines[j]);

                        if (obj == null)
                        {
                            throw new NullReferenceException($"Cannot parse {nameof(TObj)}");
                        }

                        result.Add(obj);
                        i = j;

                        break;
                    }
                }
            }
        }

        return result;
    }
}