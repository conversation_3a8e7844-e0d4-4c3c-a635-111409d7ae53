﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

[TriggerGroup(ControllerNames.WebScraperRuns)]
public class GetWebScraperRun : ITrigger<GetWebScraperRun.GetWebScraperRunInput, GetWebScraperRun.GetWebScraperRunOutput>
{
    private readonly IWebScraperService _webScraperService;

    public GetWebScraperRun(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class GetWebScraperRunInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperRun.PropertyNameApifyRunId)]
        public string ApifyRunId { get; set; }

        [JsonConstructor]
        public GetWebScraperRunInput(string sleekflowCompanyId, string apifyRunId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyRunId = apifyRunId;
        }
    }

    public class GetWebScraperRunOutput
    {
        [JsonProperty(WebScraperRun.PropertyNameWebScraperRun)]
        public WebScraperRun WebScraperRun { get; set; }

        [JsonConstructor]
        public GetWebScraperRunOutput(WebScraperRun webScraperRun)
        {
            WebScraperRun = webScraperRun;
        }
    }

    public async Task<GetWebScraperRunOutput> F(GetWebScraperRunInput getWebScraperRunInput)
    {
        var run = await _webScraperService.GetRunAsync(
            getWebScraperRunInput.SleekflowCompanyId,
            getWebScraperRunInput.ApifyRunId);
        return new GetWebScraperRunOutput(run);
    }
}