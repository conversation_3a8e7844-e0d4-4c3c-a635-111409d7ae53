using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors;

public interface ISubFlowStepExecutor : IStepExecutor
{
}

public class SubFlowStepExecutor : GeneralStepExecutor<SubFlowStep>, ISubFlowStepExecutor, IScopedService
{
    private readonly IWorkflowStepLocator _workflowStepLocator;
    private readonly IStepOrchestrationService _stepOrchestrationService;
    private readonly IExecutorContext _executorContext;

    public SubFlowStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IStepOrchestrationService stepOrchestrationService,
        IServiceProvider serviceProvider,
        IExecutorContext executorContext)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _workflowStepLocator = workflowStepLocator;
        _stepOrchestrationService = stepOrchestrationService;
        _executorContext = executorContext;
    }

    public override bool IsMatched(Step step)
    {
        return step is SubFlowStep;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var subFlowStep = (SubFlowStep) step;

        var firstSubFlowStep = _workflowStepLocator.GetStep(workflow, subFlowStep.Substeps.First());

        var newStackEntries = new Stack<StackEntry>(stackEntries);
        newStackEntries.Push(new StackEntry(step.Id, _executorContext.WorkerInstanceId));

        await _stepOrchestrationService.ExecuteStepAsync(
            state.Id,
            firstSubFlowStep.Id,
            newStackEntries);

        // The actions is Delayed Complete
    }
}