﻿using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.CommerceHub.Tests.Vtex;

public class VtexOrderHookRegisterTests
{
    private IVtexOrderHookRegister _vtexOrderHookRegister;

    private VtexCredential _credential;

    [SetUp]
    public void Setup()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Vtex app key has a build-in expiration. Exclude from Github action.");
        }

        _credential = new VtexCredential(
            "https://sandboxsleekflo16.myvtex.com/",
            "vtexappkey-sandboxsleekflo16-JDTPBQ",
            "NYIGZILNLCJKHAUOIJRZIXFODNHZDYEXYUIILPEFQPNCWKJDUDKOYJDVZIXAOZTFDRDTLJATSZCOZUVHWZXSJEGEAHXNIYEXDNIHXJCTKFXYCQSCZRDSPWUGKTKFPRGB");

        _vtexOrderHookRegister = new VtexOrderHookRegister(
            new HttpClient(),
            new NullLogger<VtexOrderHookRegister>(),
            new MyAppConfig());
    }

    [Test]
    public async Task RegisterHookLifeCycle_WhenRegistrationIsValid_ShouldNotThrowError()
    {
        // register hook
        await _vtexOrderHookRegister.RegisterAsync(
            _credential,
            "vtexAuthenticationId",
            "sleekflowCompanyId");

        // validation
        var validationResult = await _vtexOrderHookRegister.ValidateRegistrationAsync(_credential);
        Assert.IsTrue(validationResult);

        // remove hook registration
        await Task.Delay(TimeSpan.FromSeconds(80));
        await _vtexOrderHookRegister.RemoveRegistrationAsync(_credential);

        // validation
        validationResult = await _vtexOrderHookRegister.ValidateRegistrationAsync(_credential);
        Assert.IsFalse(validationResult);
    }

    private sealed class MyAppConfig : IAppConfig
    {
        public string CommerceHubEndpoint => "https://webhook.site/bca335e9-34dd-410d-8045-630fb55195fa";
    }
}