﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows.Settings;

public class WorkflowEnrollmentSettings : IValidatableObject
{
    // Object cannot enroll again if it has enrolled in this flow before
    [JsonProperty("can_enroll_only_once")]
    public bool CanEnrollOnlyOnce { get; set; }

    // Object can enroll again only if its previous enrollment ended with failure
    [JsonProperty("can_enroll_again_on_failure_only")]
    public bool CanEnrollAgainOnFailureOnly { get; set; }

    // Object can enroll while it still has an active enrollment
    [JsonProperty("can_enroll_in_parallel")]
    public bool CanEnrollInParallel { get; set; }

    [JsonConstructor]
    public WorkflowEnrollmentSettings(
        bool canEnrollOnlyOnce,
        bool canEnrollAgainOnFailureOnly,
        bool canEnrollInParallel)
    {
        CanEnrollOnlyOnce = canEnrollOnlyOnce;
        CanEnrollAgainOnFailureOnly = canEnrollAgainOnFailureOnly;
        CanEnrollInParallel = canEnrollInParallel;
    }

    public static WorkflowEnrollmentSettings Default()
        => new (
            false,
            false,
            false);

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var toggles = new bool[] { CanEnrollOnlyOnce, CanEnrollAgainOnFailureOnly, CanEnrollInParallel };

        if (toggles.Count(v => v) > 1)
        {
            yield return new ValidationResult(
                "Only one of the following can be true: CanEnrollOnlyOnce, CanEnrollAgainOnFailureOnly, CanEnrollInParallel",
                new[]
                {
                    nameof(CanEnrollOnlyOnce),
                    nameof(CanEnrollAgainOnFailureOnly),
                    nameof(CanEnrollInParallel)
                });
        }
    }
}