using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Enriches chat context with plan tier.
/// </summary>
public class PlanTierEnricher : IChatHistoryEnricher
{
    private readonly ILogger<PlanTierEnricher> _logger;
    private readonly IPlanTieringPlugin _planTieringPlugin;
    private readonly string _phoneFieldName;
    private readonly string _countryFieldName;

    /// <summary>
    /// Initializes a new instance of the <see cref="PlanTierEnricher"/> class.
    /// </summary>
    /// <param name="logger">Logger for the enricher.</param>
    /// <param name="planTieringPlugin">The plan tier plugin.</param>
    /// <param name="parameters">Configuration parameters from EnricherConfig.</param>
    public PlanTierEnricher(
        ILogger<PlanTierEnricher> logger,
        IPlanTieringPlugin planTieringPlugin,
        IDictionary<string, string> parameters)
    {
        _logger = logger;
        _planTieringPlugin = planTieringPlugin;

        _phoneFieldName = parameters.TryGetValue(PlanTierEnricherConstants.PHONE_FIELD_KEY, out var phoneField) &&
                          !string.IsNullOrEmpty(phoneField)
            ? phoneField
            : "PhoneNumber";
        _countryFieldName = parameters.TryGetValue(PlanTierEnricherConstants.COUNTRY_FIELD_KEY, out var countryField) &&
                            !string.IsNullOrEmpty(countryField)
            ? countryField
            : "Country";
    }

    /// <summary>
    /// Gets the name of the section in the context message.
    /// </summary>
    public string GetContextSectionName() => EnricherSectionKeys.PLAN_TIER_SECTION;

    public string GetContextSectionExplanation()
    {
        return
            $"{GetContextSectionName()} is a section that contains information about the plan tier. You should never disclose this information to the user. This plan tier is used to determine the plan tier of the user.";
    }

    /// <summary>
    /// Enriches chat context with plan tier.
    /// </summary>
    public async Task<string> EnrichAsync(
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default)
    {
        if (contactProperties == null || !contactProperties.Any())
        {
            _logger.LogInformation("No plan tier available");
            return "No plan tier available";
        }

        if (!contactProperties.TryGetValue(_phoneFieldName, out var phoneNumber))
        {
            return "Phone number does not exists";
        }

        if (!contactProperties.TryGetValue(_countryFieldName, out var country))
        {
            return "Country does not exists";
        }

        try
        {
            var determinePlanTierResponse = await _planTieringPlugin.DeterminePlanTierAsync(
                kernel,
                phoneNumber,
                country,
                cancellationToken);

            EnrichKernelData(kernel, determinePlanTierResponse);

            return JsonConvert.SerializeObject(determinePlanTierResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enriching chat history with plan tier");
            return "Failed to process plan tier";
        }
    }

    private static void EnrichKernelData(Kernel kernel, DeterminePlanTierResponse determinePlanTierResponse)
    {
        kernel.Data[KernelDataKeys.DETERMINE_PLAN_TIER_RESPONSE] = determinePlanTierResponse;
    }
}