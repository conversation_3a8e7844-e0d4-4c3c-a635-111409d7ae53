using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps;

public class ThrowStep : Step
{
    [Required]
    [JsonProperty("error_code")]
    public int ErrorCode { get; set; }

    [Required]
    [JsonProperty("error_message")]
    public string ErrorMessage { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public ThrowStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        int errorCode,
        string errorMessage)
        : base(id, name, assign, nextStepId)
    {
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
    }
}