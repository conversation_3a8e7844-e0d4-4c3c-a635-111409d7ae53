using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.WabaBalanceAutoTopUpProfile)]
[Resolver(typeof(IMessagingHubDbResolver))]
public class WabaBalanceAutoTopUpProfile : AuditEntity, IHasETag, IHasRecordStatuses
{
    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("customer_id")]
    public string? CustomerId { get; set; }

    [JsonProperty("minimum_balance")]
    public Money MinimumBalance { get; set; }

    [JsonProperty("auto_top_up_plan")]
    public StripeWhatsAppCreditTopUpPlan AutoTopUpPlan { get; set; }

    [JsonProperty("is_auto_top_up_enabled")]
    public bool IsAutoTopUpEnabled { get; set; }

    [JsonProperty("record_statuses")]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public WabaBalanceAutoTopUpProfile(
        string id,
        string facebookWabaId,
        string facebookBusinessId,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        List<string> recordStatuses)
        : base(
            id,
            SysTypeNames.WabaBalanceAutoTopUpProfile,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        CustomerId = customerId;
        MinimumBalance = minimumBalance;
        AutoTopUpPlan = autoTopUpPlan;
        IsAutoTopUpEnabled = isAutoTopUpEnabled;
        RecordStatuses = recordStatuses;
    }
}