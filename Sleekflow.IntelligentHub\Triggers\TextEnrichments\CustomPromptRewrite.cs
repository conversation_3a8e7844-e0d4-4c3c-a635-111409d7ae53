using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TextEnrichments;

[TriggerGroup(ControllerNames.TextEnrichments)]
public class CustomPromptRewrite
    : ITrigger<CustomPromptRewrite.CustomPromptRewriteInput, CustomPromptRewrite.CustomPromptRewriteOutput>
{
    private readonly ITextCustomPromptRewriteService _textCustomPromptRewriteService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public CustomPromptRewrite(
        ITextCustomPromptRewriteService textCustomPromptRewriteService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
    {
        _textCustomPromptRewriteService = textCustomPromptRewriteService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    public class CustomPromptRewriteInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("message")]
        public string Message { get; set; }

        [Required]
        [JsonProperty("prompt")]
        public string Prompt { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [Validations.ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
        [Validations.ValidateObject]
        public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

        [JsonConstructor]
        public CustomPromptRewriteInput(
            string message,
            string prompt,
            string sleekflowCompanyId,
            IntelligentHubUsageFilter? intelligentHubUsageFilter,
            AuditEntity.SleekflowStaff? createdBy)
        {
            Message = message;
            Prompt = prompt;
            SleekflowCompanyId = sleekflowCompanyId;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
            CreatedBy = createdBy;
        }
    }

    public class CustomPromptRewriteOutput
    {
        [JsonProperty("output_message")]
        public string OutputMessage { get; set; }

        [JsonConstructor]
        public CustomPromptRewriteOutput(string outputMessage)
        {
            OutputMessage = outputMessage;
        }
    }

    public async Task<CustomPromptRewriteOutput> F(CustomPromptRewriteInput customPromptRewriteInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(
                customPromptRewriteInput.SleekflowCompanyId);

        var isUsageLimitExceeded =
            intelligentHubConfig == null
            || await _intelligentHubUsageService.IsUsageLimitExceeded(
                customPromptRewriteInput.SleekflowCompanyId,
                new Dictionary<string, int>
                {
                    {
                        PriceableFeatures.AiFeaturesTotalUsage,
                        _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                            intelligentHubConfig,
                            PriceableFeatures.AiFeaturesTotalUsage)
                    }
                },
                customPromptRewriteInput.IntelligentHubUsageFilter);

        if (isUsageLimitExceeded)
        {
            throw new SfUserFriendlyException("Cannot find IntelligentHubConfig or exceed max usage limit.");
        }

        var outputMessage = await _textCustomPromptRewriteService.RewriteAsync(
            customPromptRewriteInput.Message,
            customPromptRewriteInput.Prompt);

        await _intelligentHubUsageService.RecordUsageAsync(
            customPromptRewriteInput.SleekflowCompanyId,
            PriceableFeatures.CustomPromptRewrite,
            customPromptRewriteInput.CreatedBy,
            new CustomPromptRewriteSnapshot(
                customPromptRewriteInput.Message,
                customPromptRewriteInput.Prompt,
                outputMessage));

        return new CustomPromptRewriteOutput(outputMessage);
    }
}