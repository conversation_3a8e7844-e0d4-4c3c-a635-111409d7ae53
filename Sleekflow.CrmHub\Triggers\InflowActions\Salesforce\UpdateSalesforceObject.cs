using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class UpdateSalesforceObject : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public UpdateSalesforceObject(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class UpdateSalesforceObjectInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("dict")]
        [ValidateObject]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonConstructor]
        public UpdateSalesforceObjectInput(
            string sleekflowCompanyId,
            string salesforceConnectionId,
            string entityTypeName,
            string objectId,
            Dictionary<string, object?> dict)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SalesforceConnectionId = salesforceConnectionId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
            Dict = dict;
        }
    }

    public class UpdateSalesforceObjectOutput
    {
    }

    public async Task<UpdateSalesforceObjectOutput> F(
        UpdateSalesforceObjectInput updateSalesforceObjectInput)
    {
        var salesforceProviderService = _providerSelector.GetProviderService(
            "salesforce-integrator");

        await salesforceProviderService.UpdateObjectV2Async(
            updateSalesforceObjectInput.SleekflowCompanyId,
            updateSalesforceObjectInput.SalesforceConnectionId,
            updateSalesforceObjectInput.ObjectId,
            updateSalesforceObjectInput.Dict,
            updateSalesforceObjectInput.EntityTypeName);

        return new UpdateSalesforceObjectOutput();
    }
}