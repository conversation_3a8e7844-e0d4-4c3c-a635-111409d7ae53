using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.Gmail.Authentications;

public class GmailAuthenticationMetadata : EmailAuthenticationMetadata
{
    [JsonProperty("nonce")]
    public string Nonce { get; set; }

    [JsonProperty("token_type")]
    public string? TokenType { get; set; }

    [JsonProperty("access_token")]
    public string? AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string? RefreshToken { get; set; }

    [JsonProperty("expires_in")]
    public long? ExpiresIn { get; set; }

    [JsonProperty("raw")]
    public string? Raw { get; set; }

    [JsonProperty("issued_at")]
    public DateTimeOffset? IssuedAt { get; set; }

    [JsonConstructor]
    public GmailAuthenticationMetadata(
        string nonce,
        string? raw = null,
        string? tokenType = null,
        string? accessToken = null,
        string? refreshToken = null,
        long? expiresIn = null,
        DateTimeOffset? issuedAt = null)
        : base(ProviderNames.Gmail, ProviderNames.Gmail)
    {
        TokenType = tokenType;
        Raw = raw;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        ExpiresIn = expiresIn;
        IssuedAt = issuedAt;
        Nonce = nonce;
    }
}