﻿using System.Collections;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json.Linq;

namespace Sleekflow.Validations;

/// <summary>
/// Custom validation attribute to ensure that the values of a specified field within an enumerable collection are unique.
/// This attribute can be applied to fields or properties and performs uniqueness validation on the specified field's values.
/// </summary>
[AttributeUsage(AttributeTargets.Field | AttributeTargets.Property, AllowMultiple = true)]
public class EnumerableDistinctValuesAttribute : ValidationAttribute
{
    private readonly string _fieldName;
    private readonly bool _isIgnoreCase;

    public EnumerableDistinctValuesAttribute(string fieldName, bool isIgnoreCase = false)
    {
        _fieldName = fieldName;
        _isIgnoreCase = isIgnoreCase;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not IEnumerable
            || value is string)
        {
            return ValidationResult.Success;
        }

        var enumerable = JArray.FromObject(value);

        var elements = enumerable
            .Select(JObject.FromObject)
            .ToList();

        if (elements.Exists(e => e.GetValue(_fieldName, StringComparison.OrdinalIgnoreCase) is null))
        {
            return new ValidationResult(
                $"One or more elements do not have field {_fieldName}",
                new[]
                {
                    validationContext.MemberName!
                });
        }

        if (elements.Exists(e => e.GetValue(_fieldName, StringComparison.OrdinalIgnoreCase)!.Type is JTokenType.Object))
        {
            return new ValidationResult(
                $"One or more elements have invalid type for field {_fieldName}",
                new[]
                {
                    validationContext.MemberName!
                });
        }

        if (elements.Exists(
                e => string.IsNullOrWhiteSpace(
                    e.GetValue(_fieldName, StringComparison.OrdinalIgnoreCase)!.Value<string>())))
        {
            return new ValidationResult(
                $"One or more elements have missing value in field {_fieldName}",
                new[]
                {
                    validationContext.MemberName!
                });
        }

        var uniqueIdCount = elements
            .Select(e => e.GetValue(_fieldName, StringComparison.OrdinalIgnoreCase)!.Value<string>())
            .Distinct(_isIgnoreCase ? StringComparer.InvariantCultureIgnoreCase : null)
            .Count();

        if (uniqueIdCount != elements.Count)
        {
            return new ValidationResult(
                $"Two or more elements have duplicate values in field {_fieldName}",
                new[]
                {
                    validationContext.MemberName!
                });
        }

        return ValidationResult.Success;
    }
}