using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

public class InternalTopUpCreditDetail : IHasMetadata
{
    [JsonProperty("unique_id")]
    public string UniqueId { get; set; }

    [JsonProperty("credited_by")]
    public string? CreditedBy { get; set; }

    [JsonProperty("credited_by_user_name")]
    public string? CreditedByUserName { get; set; }

    [JsonProperty("credited_at")]
    public DateTimeOffset CreditedAt { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public InternalTopUpCreditDetail(
        string uniqueId,
        string? creditedBy,
        string? creditedByUserName,
        DateTimeOffset creditedAt,
        Dictionary<string, object?> metadata)
    {
        UniqueId = uniqueId;
        CreditedBy = creditedBy;
        CreditedByUserName = creditedByUserName;
        CreditedAt = creditedAt;
        Metadata = metadata;
    }
}