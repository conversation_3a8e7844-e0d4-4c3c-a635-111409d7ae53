﻿using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests.Schemas;

public class PropertyConstructorTests
{
    private const string SingleLineTextPropertyUniqueName = "single_line_text";
    private const string NumericPropertyUniqueName = "numeric";
    private const string DecimalPropertyUniqueName = "decimal";
    private const string SingleChoicePropertyUniqueName = "single_choice";
    private const string MultipleChoicePropertyUniqueName = "multiple_choice";
    private const string BooleanPropertyUniqueName = "boolean";
    private const string DatePropertyUniqueName = "date";
    private const string DateTimePropertyUniqueName = "datetime";
    private const string ArrayObjectPropertyUniqueName = "array_object";

    private const string OptionName1 = "option_name_1";
    private const string OptionName2 = "option_name_2";
    private const string OptionName3 = "option_name_3";

    private IPropertyConstructor _propertyConstructor = null!;

    [SetUp]
    public void SetUp()
    {
        var services = new ServiceCollection();

        var idService = GetIdService();

        services.AddSingleton<IIdService>(idService);
        services.AddSingleton<ISchemaRepository>(GetSchemaRepository());
        services.AddSingleton<ISchemaValidator, SchemaValidator>();

        services.AddSingleton<IOptionService, OptionService>();
        services.AddSingleton<IDataTypeInnerSchemaService, DataTypeInnerSchemaService>();
        services.AddSingleton<IDefaultPropertyHook, DefaultPropertyHook>();
        services.AddSingleton<ISingleChoicePropertyHook, SingleChoicePropertyHook>();
        services.AddSingleton<IMultipleChoicePropertyHook, MultipleChoicePropertyHook>();
        services.AddSingleton<IArrayObjectPropertyHook, ArrayObjectPropertyHook>();

        services.AddSingleton<IPropertyConstructor, PropertyConstructor>();

        var serviceProvider = services.BuildServiceProvider();

        _propertyConstructor = serviceProvider.GetRequiredService<IPropertyConstructor>();
    }

    [Test]
    public void GetPropertyHook_ShouldReturnCorrectPropertyHook()
    {
        // ISingleChoicePropertyHook
        var singleChoiceDataType = new SingleChoiceDataType();
        var res = InvokePrivateMethod("GetPropertyHook", singleChoiceDataType);

        Assert.That(res, Is.InstanceOf<ISingleChoicePropertyHook>());

        // IMultipleChoicePropertyHook
        var multipleChoiceDataType = new MultipleChoiceDataType();
        res = InvokePrivateMethod("GetPropertyHook", multipleChoiceDataType);

        Assert.That(res, Is.InstanceOf<IMultipleChoicePropertyHook>());

        // IArrayObjectPropertyHook
        var arrayObjectDataType = new ArrayObjectDataType(new InnerSchema(new List<Property>()));
        res = InvokePrivateMethod("GetPropertyHook", arrayObjectDataType);

        Assert.That(res, Is.InstanceOf<IArrayObjectPropertyHook>());

        // IDefaultPropertyHook
        var singleLineDataType = new SingleLineTextDataType();

        res = InvokePrivateMethod("GetPropertyHook", singleLineDataType);
        Assert.That(res, Is.InstanceOf<IDefaultPropertyHook>());

        var booleanDataType = new BooleanDataType();
        res = InvokePrivateMethod("GetPropertyHook", booleanDataType);
        Assert.That(res, Is.InstanceOf<IDefaultPropertyHook>());
    }

    [Test]
    public void ConstructByPropertyInput_BasicDataType_Tests()
    {
        var propertyInputs = GeneratePropertyInputs();

        // normal data types
        var singleLineTextPropertyInput = propertyInputs[SingleLineTextPropertyUniqueName];
        var singleLineProperty = _propertyConstructor
            .Construct(singleLineTextPropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(string.IsNullOrEmpty(singleLineProperty.Id), Is.False);
            Assert.That(singleLineProperty.DisplayName, Is.EqualTo("Test SingleLineText"));
            Assert.That(singleLineProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.SingleLineText));
            Assert.That(singleLineProperty.UniqueName, Is.EqualTo(SingleLineTextPropertyUniqueName));
            Assert.That(singleLineProperty.IsRequired, Is.EqualTo(singleLineTextPropertyInput.IsRequired));
            Assert.That(singleLineProperty.IsVisible, Is.EqualTo(singleLineTextPropertyInput.IsVisible));
            Assert.That(singleLineProperty.IsPinned, Is.EqualTo(singleLineTextPropertyInput.IsPinned));
            Assert.That(singleLineProperty.IsSearchable, Is.EqualTo(singleLineTextPropertyInput.IsSearchable));
            Assert.That(singleLineProperty.DisplayOrder, Is.EqualTo(singleLineTextPropertyInput.DisplayOrder));
            Assert.That(singleLineProperty.CreatedBy!.SleekflowStaffId, Is.EqualTo(singleLineTextPropertyInput.CreatedBy!.SleekflowStaffId));
        });

        var numericPropertyInput = propertyInputs[NumericPropertyUniqueName];
        var numericProperty = _propertyConstructor
            .Construct(numericPropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(numericProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Numeric));
            Assert.That(numericProperty.UniqueName, Is.EqualTo(NumericPropertyUniqueName));
        });

        var decimalPropertyInput = propertyInputs[DecimalPropertyUniqueName];
        var decimalProperty = _propertyConstructor
            .Construct(decimalPropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(decimalProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Decimal));
            Assert.That(decimalProperty.UniqueName, Is.EqualTo(DecimalPropertyUniqueName));
        });

        var booleanPropertyInput = propertyInputs[BooleanPropertyUniqueName];
        var booleanProperty = _propertyConstructor
            .Construct(booleanPropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(booleanProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Boolean));
            Assert.That(booleanProperty.UniqueName, Is.EqualTo(BooleanPropertyUniqueName));
        });

        var datePropertyInput = propertyInputs[DatePropertyUniqueName];
        var dateProperty = _propertyConstructor
            .Construct(datePropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(dateProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Date));
            Assert.That(dateProperty.UniqueName, Is.EqualTo(DatePropertyUniqueName));
        });

        var dateTimePropertyInput = propertyInputs[DateTimePropertyUniqueName];
        var dateTimeProperty = _propertyConstructor
            .Construct(dateTimePropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(dateTimeProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.DateTime));
            Assert.That(dateTimeProperty.UniqueName, Is.EqualTo(DateTimePropertyUniqueName));
        });

        // options included data types
        var singleChoicePropertyInput = propertyInputs[SingleChoicePropertyUniqueName];
        var singleChoiceProperty = _propertyConstructor
            .Construct(singleChoicePropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(singleChoiceProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.SingleChoice));
            Assert.That(singleChoiceProperty.UniqueName, Is.EqualTo(SingleChoicePropertyUniqueName));
            Assert.That(singleChoiceProperty.Options!.Count, Is.EqualTo(2));
            Assert.That(string.IsNullOrEmpty(singleChoiceProperty.Options[0].Id), Is.False);
            Assert.That(singleChoiceProperty.Options[0].Value, Is.EqualTo(OptionName1));
        });

        var multipleChoicePropertyInput = propertyInputs[MultipleChoicePropertyUniqueName];
        var multipleChoiceProperty = _propertyConstructor
            .Construct(multipleChoicePropertyInput);

        Assert.Multiple(() =>
        {
            Assert.That(multipleChoiceProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.MultipleChoice));
            Assert.That(multipleChoiceProperty.UniqueName, Is.EqualTo(MultipleChoicePropertyUniqueName));
            Assert.That(multipleChoiceProperty.Options!.Count, Is.EqualTo(3));
            Assert.That(string.IsNullOrEmpty(multipleChoiceProperty.Options[0].Id), Is.False);
            Assert.That(string.IsNullOrEmpty(multipleChoiceProperty.Options[1].Id), Is.False);
            Assert.That(string.IsNullOrEmpty(multipleChoiceProperty.Options[2].Id), Is.False);
            Assert.That(multipleChoiceProperty.Options[0].Value, Is.EqualTo(OptionName1));
            Assert.That(multipleChoiceProperty.Options[1].Value, Is.EqualTo(OptionName2));
            Assert.That(multipleChoiceProperty.Options[2].Value, Is.EqualTo(OptionName3));
        });
    }

    [Test]
    public void ConstructByPropertyInput_ArrayObject_Tests()
    {
        var propertyDictionary = GeneratePropertiesForSchemaCreation();
        var innerSchema = new InnerSchema(propertyDictionary.Values.ToList());

        var arrayObjectPropertyInput = new PropertyInput(
            "Test ArrayObject",
            ArrayObjectPropertyUniqueName,
            new ArrayObjectDataType(innerSchema),
            false,
            false,
            true,
            true,
            true,
            1,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            null);

        var arrayObjectProperty = _propertyConstructor
            .Construct(arrayObjectPropertyInput);

        Assert.Multiple(
            () =>
            {
                Assert.That(string.IsNullOrWhiteSpace(arrayObjectProperty.Id), Is.False);
                Assert.That(arrayObjectProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.ArrayObject));
                Assert.That(arrayObjectProperty.UniqueName, Is.EqualTo(ArrayObjectPropertyUniqueName));
                Assert.That(arrayObjectProperty.IsRequired, Is.EqualTo(arrayObjectPropertyInput.IsRequired));
                Assert.That(arrayObjectProperty.DataType.HasInnerSchema, Is.True);
            });

        var constructedInnerSchema = arrayObjectProperty.DataType.GetInnerSchema();
        var numericInnerProperty = constructedInnerSchema.Properties.First(p => p.UniqueName == NumericPropertyUniqueName);
        var multipleChoiceInnerProperty = constructedInnerSchema.Properties.First(p => p.UniqueName == MultipleChoicePropertyUniqueName);

        Assert.Multiple(
            () =>
            {
                Assert.That(constructedInnerSchema.Properties, Has.Count.EqualTo(innerSchema.Properties.Count));

                Assert.That(string.IsNullOrWhiteSpace(numericInnerProperty.Id), Is.False);
                Assert.That(numericInnerProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Numeric));
                Assert.That(numericInnerProperty.UniqueName, Is.EqualTo(NumericPropertyUniqueName));
                Assert.That(numericInnerProperty.IsRequired, Is.EqualTo(propertyDictionary[NumericPropertyUniqueName].IsRequired));
                Assert.That(numericInnerProperty.IsVisible, Is.EqualTo(propertyDictionary[NumericPropertyUniqueName].IsVisible));
                Assert.That(numericInnerProperty.DisplayOrder, Is.EqualTo(1));
                Assert.That(numericInnerProperty.CreatedBy!.SleekflowStaffId, Is.EqualTo("mocked-staff-id"));

                Assert.That(string.IsNullOrWhiteSpace(multipleChoiceInnerProperty.Id), Is.False);
                Assert.That(multipleChoiceInnerProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.MultipleChoice));
                Assert.That(multipleChoiceInnerProperty.UniqueName, Is.EqualTo(MultipleChoicePropertyUniqueName));
                Assert.That(multipleChoiceInnerProperty.IsRequired, Is.EqualTo(propertyDictionary[MultipleChoicePropertyUniqueName].IsRequired));
                Assert.That(multipleChoiceInnerProperty.IsVisible, Is.EqualTo(propertyDictionary[MultipleChoicePropertyUniqueName].IsRequired));
                Assert.That(multipleChoiceInnerProperty.Options!.Count, Is.EqualTo(3));
                Assert.That(string.IsNullOrEmpty(multipleChoiceInnerProperty.Options[0].Id), Is.False);
                Assert.That(multipleChoiceInnerProperty.Options[0].Value, Is.EqualTo(OptionName1));
            });
    }

    [Test]
    public void ConstructByProperty_BasicDataType_Tests()
    {
        var propertyDictionary = GeneratePropertiesForSchemaCreation();

        // normal data type
        var numericPropertyForSchemaCreation = propertyDictionary[NumericPropertyUniqueName];
        var numericProperty = _propertyConstructor
            .Construct(numericPropertyForSchemaCreation);

        Assert.Multiple(() =>
        {
            Assert.That(string.IsNullOrEmpty(numericProperty.Id), Is.False);
            Assert.That(numericProperty.DisplayName, Is.EqualTo("Test Numeric"));
            Assert.That(numericProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Numeric));
            Assert.That(numericProperty.UniqueName, Is.EqualTo(NumericPropertyUniqueName));
            Assert.That(numericProperty.IsRequired, Is.EqualTo(numericPropertyForSchemaCreation.IsRequired));
            Assert.That(numericProperty.IsVisible, Is.EqualTo(numericPropertyForSchemaCreation.IsVisible));
            Assert.That(numericProperty.IsPinned, Is.EqualTo(numericPropertyForSchemaCreation.IsPinned));
            Assert.That(numericProperty.IsSearchable, Is.EqualTo(numericPropertyForSchemaCreation.IsSearchable));
            Assert.That(numericProperty.DisplayOrder, Is.EqualTo(numericPropertyForSchemaCreation.DisplayOrder));
            Assert.That(numericProperty.CreatedBy!.SleekflowStaffId, Is.EqualTo(numericPropertyForSchemaCreation.CreatedBy!.SleekflowStaffId));
        });

        // options included data types
        var multipleChoicePropertyForSchemaCreation = propertyDictionary[MultipleChoicePropertyUniqueName];
        var multipleChoiceProperty = _propertyConstructor
            .Construct(multipleChoicePropertyForSchemaCreation);

        Assert.Multiple(() =>
        {
            Assert.That(multipleChoiceProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.MultipleChoice));
            Assert.That(multipleChoiceProperty.UniqueName, Is.EqualTo(MultipleChoicePropertyUniqueName));
            Assert.That(multipleChoiceProperty.Options!.Count, Is.EqualTo(3));
            Assert.That(string.IsNullOrEmpty(multipleChoiceProperty.Options[0].Id), Is.False);
            Assert.That(string.IsNullOrEmpty(multipleChoiceProperty.Options[1].Id), Is.False);
            Assert.That(string.IsNullOrEmpty(multipleChoiceProperty.Options[2].Id), Is.False);
            Assert.That(multipleChoiceProperty.Options[0].Value, Is.EqualTo(OptionName1));
            Assert.That(multipleChoiceProperty.Options[1].Value, Is.EqualTo(OptionName2));
            Assert.That(multipleChoiceProperty.Options[2].Value, Is.EqualTo(OptionName3));
        });
    }

    [Test]
    public void ConstructByProperty_ArrayObject_Tests()
    {
        var propertyDictionary = GeneratePropertiesForSchemaCreation();
        var innerSchema = new InnerSchema(propertyDictionary.Values.ToList());

        var arrayObjectPropertyForSchemaCreation = new Property(
            string.Empty,
            "Test ArrayObject",
            ArrayObjectPropertyUniqueName,
            new ArrayObjectDataType(innerSchema),
            false,
            false,
            true,
            true,
            1,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            DateTimeOffset.UtcNow,
            null);

        var arrayObjectProperty = _propertyConstructor
            .Construct(arrayObjectPropertyForSchemaCreation);

        Assert.Multiple(
            () =>
            {
                Assert.That(string.IsNullOrWhiteSpace(arrayObjectProperty.Id), Is.False);
                Assert.That(arrayObjectProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.ArrayObject));
                Assert.That(arrayObjectProperty.UniqueName, Is.EqualTo(ArrayObjectPropertyUniqueName));
                Assert.That(arrayObjectProperty.IsRequired, Is.EqualTo(arrayObjectPropertyForSchemaCreation.IsRequired));
                Assert.That(arrayObjectProperty.DataType.HasInnerSchema, Is.True);
            });

        var constructedInnerSchema = arrayObjectProperty.DataType.GetInnerSchema();
        var numericInnerProperty = constructedInnerSchema.Properties.First(p => p.UniqueName == NumericPropertyUniqueName);
        var multipleChoiceInnerProperty = constructedInnerSchema.Properties.First(p => p.UniqueName == MultipleChoicePropertyUniqueName);

        Assert.Multiple(
            () =>
            {
                Assert.That(constructedInnerSchema.Properties, Has.Count.EqualTo(innerSchema.Properties.Count));

                Assert.That(string.IsNullOrWhiteSpace(numericInnerProperty.Id), Is.False);
                Assert.That(numericInnerProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Numeric));
                Assert.That(numericInnerProperty.UniqueName, Is.EqualTo(NumericPropertyUniqueName));
                Assert.That(numericInnerProperty.IsRequired, Is.EqualTo(propertyDictionary[NumericPropertyUniqueName].IsRequired));
                Assert.That(numericInnerProperty.IsVisible, Is.EqualTo(propertyDictionary[NumericPropertyUniqueName].IsVisible));

                Assert.That(numericInnerProperty.DisplayOrder, Is.EqualTo(1));
                Assert.That(numericInnerProperty.CreatedBy!.SleekflowStaffId, Is.EqualTo("mocked-staff-id"));

                Assert.That(string.IsNullOrWhiteSpace(multipleChoiceInnerProperty.Id), Is.False);
                Assert.That(multipleChoiceInnerProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.MultipleChoice));
                Assert.That(multipleChoiceInnerProperty.UniqueName, Is.EqualTo(MultipleChoicePropertyUniqueName));
                Assert.That(multipleChoiceInnerProperty.IsRequired, Is.EqualTo(propertyDictionary[MultipleChoicePropertyUniqueName].IsRequired));
                Assert.That(multipleChoiceInnerProperty.IsVisible, Is.EqualTo(propertyDictionary[MultipleChoicePropertyUniqueName].IsRequired));
                Assert.That(multipleChoiceInnerProperty.Options!.Count, Is.EqualTo(3));
                Assert.That(string.IsNullOrEmpty(multipleChoiceInnerProperty.Options[0].Id), Is.False);
                Assert.That(multipleChoiceInnerProperty.Options[0].Value, Is.EqualTo(OptionName1));
            });
    }

    [Test]
    public void UpdateProperty_BasicDataType_Tests()
    {
        var propertyInputs = GeneratePropertyInputs();

        // normal data type
        var numericPropertyInput = propertyInputs[NumericPropertyUniqueName];
        var numericProperty = _propertyConstructor
            .Construct(numericPropertyInput);

        var numericPropertyForUpdate = new Property(
            numericProperty.Id,
            "Updated Numeric",
            NumericPropertyUniqueName,
            new NumericDataType(),
            false,
            false,
            true,
            false,
            999,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            DateTimeOffset.UtcNow,
            null);

        var updatePropertyChangeContext = _propertyConstructor
            .Update(numericProperty, numericPropertyForUpdate);

        Assert.Multiple(
            () =>
            {
                Assert.That(numericProperty.DisplayName, Is.EqualTo("Updated Numeric"));
                Assert.That(numericProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.Numeric));
                Assert.That(numericProperty.IsRequired, Is.EqualTo(numericPropertyForUpdate.IsRequired));
                Assert.That(numericProperty.IsVisible, Is.EqualTo(numericPropertyForUpdate.IsVisible));
                Assert.That(numericProperty.DisplayOrder, Is.EqualTo(numericPropertyForUpdate.DisplayOrder));
                Assert.That(updatePropertyChangeContext.NeedReindexPropertyValue, Is.True);
            });

        // options included data types
        var multipleChoicePropertyInput = propertyInputs[MultipleChoicePropertyUniqueName];
        var multipleChoiceProperty = _propertyConstructor
            .Construct(multipleChoicePropertyInput);

        var optionsForUpdate = DeepCopy(multipleChoiceProperty.Options)!;
        var toBeRemovedOptionIds = optionsForUpdate.Where(x => x.Value != OptionName1).Select(x => x.Id).ToList();
        var updatedOptionName1 = "Updated Option 1";
        var newOptionName = "Man!";

        optionsForUpdate[0].DisplayOrder = 99;
        optionsForUpdate[0].Value = updatedOptionName1;
        optionsForUpdate.RemoveAll(x => x.Value == OptionName2);
        optionsForUpdate.RemoveAll(x => x.Value == OptionName3);
        optionsForUpdate.Add(new Option(string.Empty, newOptionName, 10));

        var multipleChoicePropertyForUpdate = new Property(
            multipleChoiceProperty.Id,
            "Updated MultipleChoice",
            MultipleChoicePropertyUniqueName,
            new MultipleChoiceDataType(),
            false,
            false,
            false,
            true,
            9,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            DateTimeOffset.UtcNow,
            optionsForUpdate);

        var multipleChoicePropertyUpdateChangeContext = _propertyConstructor
            .Update(multipleChoiceProperty, multipleChoicePropertyForUpdate);

        Assert.Multiple(() =>
        {
            Assert.That(multipleChoiceProperty.DisplayName, Is.EqualTo("Updated MultipleChoice"));
            Assert.That(multipleChoiceProperty.DataType.Name, Is.EqualTo(SchemaPropertyDataTypes.MultipleChoice));
            Assert.That(multipleChoiceProperty.IsRequired, Is.EqualTo(multipleChoicePropertyForUpdate.IsRequired));
            Assert.That(multipleChoiceProperty.IsVisible, Is.EqualTo(multipleChoicePropertyForUpdate.IsVisible));
            Assert.That(multipleChoiceProperty.DisplayOrder, Is.EqualTo(multipleChoicePropertyForUpdate.DisplayOrder));
            Assert.That(multipleChoicePropertyUpdateChangeContext.NeedReindexPropertyValue, Is.False);
            Assert.That(multipleChoicePropertyUpdateChangeContext.ToBeDeletedOptionIds, Is.EqualTo(toBeRemovedOptionIds));
        });

        var updatedOptions = multipleChoiceProperty.Options!;
        Assert.Multiple(() =>
        {
            Assert.That(updatedOptions, Has.Count.EqualTo(2));
            Assert.That(string.IsNullOrWhiteSpace(updatedOptions[0].Id), Is.False);
            Assert.That(updatedOptions[0].Value, Is.EqualTo(newOptionName));
            Assert.That(updatedOptions[0].DisplayOrder, Is.EqualTo(0));
            Assert.That(updatedOptions[1].Value, Is.EqualTo(updatedOptionName1));
            Assert.That(updatedOptions[1].DisplayOrder, Is.EqualTo(1));
        });
    }

    [Test]
    public void UpdateProperty_ArrayObject_Tests()
    {
        var propertyDictionary = GeneratePropertiesForSchemaCreation();
        var innerSchema = new InnerSchema(propertyDictionary.Values.ToList());

        var arrayObjectPropertyInput = new PropertyInput(
            "Test ArrayObject",
            ArrayObjectPropertyUniqueName,
            new ArrayObjectDataType(innerSchema),
            false,
            false,
            true,
            true,
            true,
            1,
            new AuditEntity.SleekflowStaff("mocked-staff-id", null),
            null);

        var arrayObjectProperty = _propertyConstructor
            .Construct(arrayObjectPropertyInput);

        var arrayObjectPropertyForUpdate = DeepCopy(arrayObjectProperty);
        var innerSchemaForUpdate = arrayObjectPropertyForUpdate.DataType.GetInnerSchema();

        arrayObjectPropertyForUpdate.DisplayName = "Updated ArrayObject";
        arrayObjectPropertyForUpdate.IsRequired = true;
        arrayObjectPropertyForUpdate.IsVisible = false;
        arrayObjectPropertyForUpdate.DisplayOrder = 99;

        innerSchemaForUpdate.Properties.First(x => x.UniqueName == NumericPropertyUniqueName).IsRequired =
            true;
        innerSchemaForUpdate.Properties.First(x => x.UniqueName == NumericPropertyUniqueName).DisplayName =
            "Updated Inner Numeric";

        innerSchemaForUpdate.Properties.First(x => x.UniqueName == SingleChoicePropertyUniqueName)
            .Options![0]
            .Value = "Updated Option 1";
        innerSchemaForUpdate.Properties.First(x => x.UniqueName == SingleChoicePropertyUniqueName)
            .Options!
            .Add(new Option(string.Empty, "Man!", 10));

        innerSchemaForUpdate.Properties.RemoveAll(x => x.UniqueName == DatePropertyUniqueName);
        innerSchemaForUpdate.Properties.RemoveAll(x => x.UniqueName == MultipleChoicePropertyUniqueName);

        innerSchemaForUpdate.Properties.Add(
            new Property(
                string.Empty,
                "New Inner Boolean",
                BooleanPropertyUniqueName,
                new BooleanDataType(),
                false,
                true,
                true,
                true,
                5,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                null));

        var arrayObjectUpdateChangeContext = _propertyConstructor
            .Update(arrayObjectProperty, arrayObjectPropertyForUpdate);

        Assert.Multiple(
            () =>
            {
                Assert.That(arrayObjectProperty.DisplayName, Is.EqualTo("Updated ArrayObject"));
                Assert.That(arrayObjectProperty.DataType.HasInnerSchema, Is.True);
                Assert.That(arrayObjectProperty.IsRequired, Is.EqualTo(arrayObjectPropertyForUpdate.IsRequired));
                Assert.That(arrayObjectProperty.IsVisible, Is.EqualTo(arrayObjectPropertyForUpdate.IsVisible));
                Assert.That(arrayObjectProperty.DisplayOrder, Is.EqualTo(arrayObjectPropertyForUpdate.DisplayOrder));
                Assert.That(arrayObjectUpdateChangeContext.NeedReindexPropertyValue, Is.False);
            });

        var updatedInnerSchema = arrayObjectProperty.DataType.GetInnerSchema();

        Assert.Multiple(
            () =>
            {
                Assert.That(updatedInnerSchema.Properties, Has.Count.EqualTo(3));

                Assert.That(updatedInnerSchema.Properties.Count(x => x.UniqueName == DatePropertyUniqueName), Is.EqualTo(0));
                Assert.That(updatedInnerSchema.Properties.Count(x => x.UniqueName == MultipleChoicePropertyUniqueName), Is.EqualTo(0));
                Assert.That(updatedInnerSchema.Properties.Count(x => x.UniqueName == BooleanPropertyUniqueName), Is.EqualTo(1));
            });

        var updatedNumericProperty = updatedInnerSchema.Properties.First(x => x.UniqueName == NumericPropertyUniqueName);
        var updatedSingleChoiceProperty = updatedInnerSchema.Properties.First(x => x.UniqueName == SingleChoicePropertyUniqueName);
        var newBooleanProperty = updatedInnerSchema.Properties.First(x => x.UniqueName == BooleanPropertyUniqueName);
        Assert.Multiple(
            () =>
            {
                Assert.That(updatedNumericProperty.IsRequired, Is.True);
                Assert.That(updatedNumericProperty.DisplayName, Is.EqualTo("Updated Inner Numeric"));

                Assert.That(updatedSingleChoiceProperty.Options!, Has.Count.EqualTo(3));
                Assert.That(updatedSingleChoiceProperty.Options![0].Value, Is.EqualTo("Updated Option 1"));
                Assert.That(updatedSingleChoiceProperty.Options![2].Value, Is.EqualTo("Man!"));
                Assert.That(string.IsNullOrWhiteSpace(updatedSingleChoiceProperty.Options![2].Id), Is.False);

                Assert.That(string.IsNullOrWhiteSpace(newBooleanProperty.Id), Is.False);
                Assert.That(newBooleanProperty.IsRequired, Is.False);
                Assert.That(newBooleanProperty.DisplayOrder, Is.EqualTo(3));
            });
    }

    private Dictionary<string, Property> GeneratePropertiesForSchemaCreation()
    {
        var properties = new Dictionary<string, Property>();

        properties.Add(
            NumericPropertyUniqueName,
            new Property(
                string.Empty,
                "Test Numeric",
                NumericPropertyUniqueName,
                new NumericDataType(),
                false,
                false,
                true,
                true,
                2,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                null));

        properties.Add(
            DatePropertyUniqueName,
            new Property(
                string.Empty,
                "Test Date",
                DatePropertyUniqueName,
                new DateDataType(),
                false,
                true,
                true,
                true,
                5,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                null));

        properties.Add(
            SingleChoicePropertyUniqueName,
            new Property(
                string.Empty,
                "Test SingleChoice",
                SingleChoicePropertyUniqueName,
                new SingleChoiceDataType(),
                false,
                true,
                true,
                true,
                7,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(string.Empty, OptionName1, 0),
                    new Option(string.Empty, OptionName2, 1)
                }));

        properties.Add(
            MultipleChoicePropertyUniqueName,
            new Property(
                string.Empty,
                "Test MultipleChoice",
                MultipleChoicePropertyUniqueName,
                new MultipleChoiceDataType(),
                true,
                true,
                false,
                false,
                9,
                new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(string.Empty, OptionName1, 0),
                    new Option(string.Empty, OptionName2, 1),
                    new Option(string.Empty, OptionName3, 2)
                }));

        return properties;
    }

    private Dictionary<string, PropertyInput> GeneratePropertyInputs()
    {
        var mockSleekflowStaff = new AuditEntity.SleekflowStaff("mocked-staff-id", null);

        var propertyInputs = new Dictionary<string, PropertyInput>();

        propertyInputs.Add(
            SingleLineTextPropertyUniqueName,
            new PropertyInput(
                "Test SingleLineText",
                SingleLineTextPropertyUniqueName,
                new SingleLineTextDataType(),
                false,
                true,
                true,
                true,
                true,
                1,
                mockSleekflowStaff,
                null));

        propertyInputs.Add(
            NumericPropertyUniqueName,
            new PropertyInput(
                "Test Numeric",
                NumericPropertyUniqueName,
                new NumericDataType(),
                false,
                true,
                true,
                true,
                true,
                2,
                mockSleekflowStaff,
                null));

        propertyInputs.Add(
            DecimalPropertyUniqueName,
            new PropertyInput(
                "Test Decimal",
                DecimalPropertyUniqueName,
                new DecimalDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                3,
                mockSleekflowStaff,
                null));

        propertyInputs.Add(
            SingleChoicePropertyUniqueName,
            new PropertyInput(
                "Test SingleChoice",
                SingleChoicePropertyUniqueName,
                new SingleChoiceDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                4,
                null,
                new List<Option>
                {
                    new Option(string.Empty, OptionName1, 0),
                    new Option(string.Empty, OptionName2, 1)
                }));

        propertyInputs.Add(
            MultipleChoicePropertyUniqueName,
            new PropertyInput(
                "Test MultipleChoice",
                MultipleChoicePropertyUniqueName,
                new MultipleChoiceDataType(),
                false,
                true,
                true,
                false,
                true,
                5,
                null,
                new List<Option>
                {
                    new Option(string.Empty, OptionName1, 0),
                    new Option(string.Empty, OptionName2, 1),
                    new Option(string.Empty, OptionName3, 2)
                }));

        propertyInputs.Add(
            BooleanPropertyUniqueName,
            new PropertyInput(
                "Test Boolean",
                BooleanPropertyUniqueName,
                new BooleanDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                6,
                mockSleekflowStaff,
                null));

        propertyInputs.Add(
            DatePropertyUniqueName,
            new PropertyInput(
                "Test Date",
                DatePropertyUniqueName,
                new DateDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                7,
                mockSleekflowStaff,
                null));

        propertyInputs.Add(
            DateTimePropertyUniqueName,
            new PropertyInput(
                "Test DateTime",
                DateTimePropertyUniqueName,
                new DateTimeDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                8,
                mockSleekflowStaff,
                null));

        return propertyInputs;
    }

    private object? InvokePrivateMethod(string methodName, params object[] parameters)
    {
        var methodInfo = typeof(PropertyConstructor).GetMethod(methodName, BindingFlags.NonPublic | BindingFlags.Instance);
        return methodInfo!.Invoke(_propertyConstructor, parameters);
    }

    private static T DeepCopy<T>(T obj)
    {
        return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(obj))!;
    }

    private IdService GetIdService()
    {
        var dbContainerResolver = new DbContainerResolver(new MyCrmHubDbConfig());

        var idService = new IdService(
            NullLogger<IdService>.Instance,
            dbContainerResolver);

        return idService;
    }

    private SchemaRepository GetSchemaRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var schemaRepository = new SchemaRepository(
            NullLogger<SchemaRepository>.Instance,
            serviceProvider);

        return schemaRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig, IDbConfig
    {
        public string Endpoint => "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key => "****************************************************************************************";

        public string DatabaseId => "crmhubdb";
    }
}