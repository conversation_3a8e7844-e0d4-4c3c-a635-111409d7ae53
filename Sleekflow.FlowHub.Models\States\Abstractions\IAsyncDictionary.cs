namespace Sleekflow.FlowHub.Models.States.Abstractions;

public interface IAsyncDictionary<TKey, TValue>
{
    Task SetAsync(TKey key, TValue value);

    Task<long> IncrementAsync(TKey key, long increment = 1);

    Task<TValue> GetAsync(TKey key);

    Task ClearAsync();

    Task<bool> ContainsKeyAsync(TKey key);

    Task<bool> ContainsValueAsync(TValue value);

    Task<int> CountAsync();

    Task<bool> RemoveAsync(TKey key);

    Task<ICollection<TKey>> KeysAsync();

    Task<ICollection<TValue>> ValuesAsync();

    Task<Dictionary<TKey, TValue>> ToDictionaryAsync();
}