using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactOwnerRelationshipsV2StepExecutor : IStepExecutor
{
}

public class UpdateContactOwnerRelationshipsV2StepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactOwnerRelationshipsV2StepArgs>>,
        IUpdateContactOwnerRelationshipsV2StepExecutor,
        IScopedService
{
    private readonly IStateAggregator _stateAggregator;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactOwnerRelationshipsV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateAggregator stateAggregator,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateAggregator = stateAggregator;
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactOwnerRelationships",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput> GetArgs(
        CallStep<UpdateContactOwnerRelationshipsV2StepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, "{{ (trigger_event_body.contact_id | string.whitespace) ? usr_var_dict.contact.id : trigger_event_body.contact_id }}")
                      ?? throw new InvalidOperationException("No contact id found"));

        var assignmentStrategy = callStep.Args.Strategy;

        switch (assignmentStrategy)
        {
            case UpdateContactOwnerRelationshipsStepArgsStrategy.Unassigned:
            {
                return new UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    true,
                    null,
                    null,
                    null,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Company_RoundRobbin_All_Staffs:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_company_staffs");

                return new UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    null,
                    null,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.RoundRobbin_StaffOnly:
            {
                var staffIds = callStep.Args.StaffIds!;

                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_staff_id");
                var idx = Convert.ToInt32(increment % staffIds.Count);
                var staffId = Convert.ToString(staffIds[idx]);

                return new UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    null,
                    staffId,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team:
            {
                var teamId = callStep.Args.TeamId!;

                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_team_staff_id");

                return new UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    null,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_Specific_Staffs_In_Team:
            {
                var teamId = callStep.Args.TeamId!;
                var staffIds = callStep.Args.StaffIds!;

                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_team_specific_staff_id");

                var idx = Convert.ToInt32(increment % staffIds!.Count);
                var staffId = Convert.ToString(staffIds[idx]);

                return new UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    staffId,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_Unassigned_Staff:
            {
                var teamId = callStep.Args.TeamId!;

                return new UpdateContactOwnerRelationshipsStepExecutor.UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    null,
                    null,
                    assignmentStrategy);
            }
        }

#pragma warning disable S3928
        throw new ArgumentOutOfRangeException(nameof(callStep.Args.Strategy));
#pragma warning restore S3928
    }
}