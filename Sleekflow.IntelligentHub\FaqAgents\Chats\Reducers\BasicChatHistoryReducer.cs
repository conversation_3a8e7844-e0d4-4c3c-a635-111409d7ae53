using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

public class BasicChatHistoryReducer : IChatHistoryReducer
{
    public Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        return Task.FromResult<IEnumerable<ChatMessageContent>?>(
            chatHistory
                .Where(h =>
                    !(h.AuthorName == LongAgentDefinitions.ReviewerAgentName
                      && (
                          (h is OpenAIChatMessageContent openAiChatMessageContent &&
                           openAiChatMessageContent.ToolCalls.Any())
                          || h.Role == AuthorRole.Tool
                      )))
                .ToList());
    }
}