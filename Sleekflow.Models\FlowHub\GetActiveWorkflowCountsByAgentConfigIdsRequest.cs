using Newtonsoft.Json;

namespace Sleekflow.Models.FlowHub;

public class GetActiveWorkflowCountsByAgentConfigIdsRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("agent_config_ids")]
    public List<string> AgentConfigIds { get; set; }

    [JsonConstructor]
    public GetActiveWorkflowCountsByAgentConfigIdsRequest(
        string sleekflowCompanyId,
        List<string> agentConfigIds)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        AgentConfigIds = agentConfigIds;
    }
} 