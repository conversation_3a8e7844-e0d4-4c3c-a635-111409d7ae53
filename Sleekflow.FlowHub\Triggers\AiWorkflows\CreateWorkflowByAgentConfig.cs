using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowAgentConfigMappings;

[TriggerGroup(ControllerNames.AiWorkflows)]
public class CreateWorkflowByAgentConfig : ITrigger
{
    private readonly IAiWorkflowService _aiWorkflowService;

    public CreateWorkflowByAgentConfig(
        IAiWorkflowService aiWorkflowService)
    {
        _aiWorkflowService = aiWorkflowService;
    }

    public class CreateWorkflowByAgentConfigInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

    }

    public class CreateWorkflowByAgentConfigOutput
    {
        [JsonProperty("workflow")]
        public ProxyWorkflow Workflow { get; set; }

        [JsonConstructor]
        public CreateWorkflowByAgentConfigOutput(
            ProxyWorkflow workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<CreateWorkflowByAgentConfigOutput> F(CreateWorkflowByAgentConfigInput createWorkflowByAgentConfigInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
          createWorkflowByAgentConfigInput.SleekflowStaffId,
          createWorkflowByAgentConfigInput.SleekflowStaffTeamIds);

        var workflow = await _aiWorkflowService.CreateAiWorkflowTemplate(
            createWorkflowByAgentConfigInput.SleekflowCompanyId,
            createWorkflowByAgentConfigInput.AgentConfigId,
            sleekflowStaff);

        return new CreateWorkflowByAgentConfigOutput(workflow);
    }
}