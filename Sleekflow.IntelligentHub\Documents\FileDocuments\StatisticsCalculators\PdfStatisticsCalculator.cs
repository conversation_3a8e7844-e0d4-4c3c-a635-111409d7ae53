using System.Text;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using UglyToad.PdfPig;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class PdfStatisticsCalculator : IDocumentStatisticsCalculator
{
    private readonly IDocumentCounterService _documentCounterService;

    public PdfStatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        using var pdfDoc = PdfDocument.Open(stream);
        var textSb = new StringBuilder();
        var totalPageNumbers = pdfDoc.NumberOfPages;
        foreach (var page in pdfDoc.GetPages())
        {
            var currentText = page.Text;
            textSb.Append(currentText);
        }

        var content = textSb.ToString();
        var totalTokens = _documentCounterService.CountTokens(content);
        var totalWords = _documentCounterService.CountWords(content);
        var totalCharacters = _documentCounterService.CountCharacters(content);
        stream.Position = 0;

        return new DocumentStatistics(
            totalTokens,
            totalWords,
            totalCharacters,
            totalPageNumbers,
            (int) stream.Length);
    }
}