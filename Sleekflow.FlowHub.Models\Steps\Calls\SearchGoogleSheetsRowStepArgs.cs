﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SearchGoogleSheetsRowStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.search-google-sheets-row";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("spreadsheet_id")]
    public string SpreadsheetId { get; set; }

    [Required]
    [JsonProperty("worksheet_id")]
    public string WorksheetId { get; set; }

    [Required]
    [JsonProperty("header_row_id")]
    public string HeaderRowId { get; set; }

    [Required]
    [JsonProperty("conditions")]
    public List<SearchCondition> Conditions { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.GoogleSheetsIntegration;

    [JsonConstructor]
    public SearchGoogleSheetsRowStepArgs(
        string connectionId,
        string spreadsheetId,
        string worksheetId,
        string headerRowId,
        List<SearchCondition> conditions)
    {
        ConnectionId = connectionId;
        SpreadsheetId = spreadsheetId;
        WorksheetId = worksheetId;
        HeaderRowId = headerRowId;
        Conditions = conditions;
    }
}

public class SearchCondition
{
    [JsonProperty("field_id")]
    public string FieldId { get; set; }

    [JsonProperty("search_operator")]
    public string SearchOperator { get; set; }

    [JsonProperty("value__expr")]
    public string? ValueExpr { get; set; }

    [JsonConstructor]
    public SearchCondition(
        string fieldId,
        string searchOperator,
        string? valueExpr)
    {
        FieldId = fieldId;
        SearchOperator = searchOperator;
        ValueExpr = valueExpr;
    }
}