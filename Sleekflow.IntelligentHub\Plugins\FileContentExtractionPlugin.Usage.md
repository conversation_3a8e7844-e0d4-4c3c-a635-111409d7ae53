# FileContentExtractionPlugin Usage Guide

The `FileContentExtractionPlugin` is designed to extract text content from files attached to `SfChatEntry` objects using LLM processing. It supports various file types including text files, images with text content, and binary files.

## Features

- **Text File Processing**: Direct extraction of text content from text-based files (TXT, CSV, JSON, HTML, XML, Markdown, RTF)
- **Image Text Extraction**: Uses GPT-4o vision capabilities to extract text from images (JPEG, PNG, GIF, BMP, WebP)
- **Binary File Analysis**: LLM-based analysis of unknown file types using hex signatures
- **Smart Summarization**: Automatically summarizes large text content to keep responses manageable
- **Error Handling**: Graceful fallback when file processing fails

## Supported File Types

### Text Files (Direct Processing)
- `text/plain`
- `text/csv`
- `text/html`
- `text/xml`
- `application/json`
- `application/xml`
- `text/markdown`
- `text/rtf`

### Images (Vision Processing)
- `image/jpeg`
- `image/jpg`
- `image/png`
- `image/gif`
- `image/bmp`
- `image/webp`

### Other Files
- Binary files are analyzed using hex signatures and LLM analysis

## Basic Usage

### 1. Single File Processing

```csharp
// Inject the plugin
var fileContentPlugin = serviceProvider.GetService<IFileContentExtractionPlugin>();
var kernel = serviceProvider.GetService<Kernel>();

// Create a file object (usually from SfChatEntry)
var file = new SfChatEntryFile(
    url: "https://example.com/document.pdf",
    mimeType: "application/pdf",
    fileSize: 1024000
);

// Extract text content
var extractedText = await fileContentPlugin.ExtractTextFromFileAsync(kernel, file);
Console.WriteLine(extractedText);
```

### 2. Multiple Files Processing

```csharp
var files = new List<SfChatEntryFile>
{
    new("https://example.com/image.png", "image/png", 500000),
    new("https://example.com/document.txt", "text/plain", 2048)
};

var extractedTexts = await fileContentPlugin.ExtractTextFromFilesAsync(kernel, files);
foreach (var text in extractedTexts)
{
    Console.WriteLine($"Extracted: {text}");
}
```

## Integration with GroupChatHistoryUtils

The plugin integrates seamlessly with the enhanced `GroupChatHistoryUtils`:

```csharp
// Standard usage (basic file info only)
var basicResult = GroupChatHistoryUtils.GetSanitizedChatEntries(chatEntries);

// Enhanced usage (with file content extraction)
var fileContentPlugin = serviceProvider.GetService<IFileContentExtractionPlugin>();
var enhancedResult = await GroupChatHistoryUtils.GetSanitizedChatEntriesWithFileContentAsync(
    chatEntries,
    kernel,
    fileContentPlugin
);

// The enhanced result will include extracted file content in the EntryTexts
foreach (var entryText in enhancedResult.EntryTexts)
{
    Console.WriteLine(entryText);
}
```

## Usage in Agent Collaboration Definitions

When using in agent collaboration definitions, you can now process files attached to chat entries:

```csharp
public class MyAgentCollaborationDefinition : BaseAgentCollaborationDefinition
{
    private readonly IFileContentExtractionPlugin _fileContentPlugin;

    public MyAgentCollaborationDefinition(
        // ... other dependencies
        IFileContentExtractionPlugin fileContentPlugin)
    {
        _fileContentPlugin = fileContentPlugin;
    }

    public override async Task<string> InitializeChatHistoryAsync(
        AgentGroupChat agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig)
    {
        // Use enhanced chat history with file content
        var sanitizedChatResult = await GroupChatHistoryUtils
            .GetSanitizedChatEntriesWithFileContentAsync(
                chatEntries,
                _kernel,
                _fileContentPlugin);

        var chatHistoryStr = await _summaryPlugin.SummarizeConversationAsync(
            _kernel,
            sanitizedChatResult.EntryTexts);

        // Now your agents can work with file content as part of the conversation context
        return chatHistoryStr;
    }
}
```

## Output Examples

### Text File
```
[FILE CONTENT - text/plain]:
This is the content of the text file.
It contains multiple lines of text
that can be processed directly.
[END FILE CONTENT]
```

### Image with Text
```
[FILE CONTENT - image/png]:
The image contains the following text:
"Welcome to our company
Phone: (*************
Email: <EMAIL>"
[END FILE CONTENT]
```

### Binary File Analysis
```
[FILE CONTENT - application/pdf]:
This appears to be a PDF document based on the file signature.
The file contains structured document data that would require
specialized PDF processing tools to extract text content properly.
Recommended tools: PDF text extraction libraries or OCR processing.
[END FILE CONTENT]
```

### Large File (Summarized)
```
[FILE CONTENT - text/csv]:
This CSV file contains sales data with the following key information:
- 1,250 rows of customer transaction data
- Columns include: customer_id, product_name, quantity, price, date
- Date range: January 2024 to December 2024
- Total sales volume: $125,000
- Top products: Widget A (25%), Widget B (18%), Service X (15%)
[END FILE CONTENT]
```

## Error Handling

The plugin includes robust error handling:

```csharp
// If file extraction fails, you'll get a fallback message
[FILE ATTACHED: application/unknown, Size: 1024 bytes, URL: https://example.com/file.bin]
(Content extraction failed: Unable to download file or unsupported format)
```

## Performance Considerations

- **File Size Limits**: Large text files (>10,000 characters) are automatically summarized
- **Timeout Handling**: HTTP requests have a 30-second timeout
- **Memory Management**: Files are processed in memory streams to avoid disk I/O
- **Rate Limiting**: Consider implementing rate limiting for high-volume file processing

## Security Notes

- Files are downloaded and processed in memory only
- No files are stored permanently by the plugin
- All HTTP requests include standard user-agent headers
- Consider implementing additional security checks for file URLs and types before processing