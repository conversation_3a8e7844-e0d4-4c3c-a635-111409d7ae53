using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.EmailSentRecords;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.EmailSentRecord)]
[Resolver(typeof(IFlowHubDbResolver))]
public class EmailSentRecord : Entity
{
    [JsonProperty("data")]
    public object? Data { get; set; }

    [JsonConstructor]
    public EmailSentRecord(string id, object? data, int? ttl)
        : base(id, SysTypeNames.EmailSentRecord, ttl)
    {
        Data = data;
    }
}