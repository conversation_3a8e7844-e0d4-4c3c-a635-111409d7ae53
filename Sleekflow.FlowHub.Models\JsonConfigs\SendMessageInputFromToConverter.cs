using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Models.JsonConfigs;

public class SendMessageInputFromToConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        return objectType.IsSubclassOf(typeof(SendMessageInputFromTo));
    }

    public override object? ReadJson(
        JsonReader reader,
        Type objectType,
        object? existingValue,
        JsonSerializer serializer)
    {
        var jObject = JObject.Load(reader);
        switch (jObject["from_to_type"]!.Value<string>())
        {
            case "WhatsappCloudApiSendMessageInputFromTo":
                return JsonConvert.DeserializeObject<WhatsappCloudApiSendMessageInputFromTo>(jObject.ToString());
            case "FacebookPageMessengerSendMessageInputFromTo":
                return JsonConvert.DeserializeObject<FacebookPageMessengerSendMessageInputFromTo>(jObject.ToString());
            case "InstagramPageMessengerSendMessageInputFromTo":
                return JsonConvert.DeserializeObject<InstagramPageMessengerSendMessageInputFromTo>(jObject.ToString());
            default:
                throw new Exception();
        }
    }

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        var jObject = JObject.FromObject(value!);
        jObject.WriteTo(writer);
    }
}