using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Internals;

public class StreamingSendMessageSubscription
{
    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("send_message_input")]
    public SendMessageInput SendMessageInput { get; set; }

    [JsonProperty("send_message_step_id")]
    public string SendMessageStepId { get; set; }

    [JsonProperty("step_entries")]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public StreamingSendMessageSubscription(
        string proxyStateId,
        SendMessageInput sendMessageInput,
        string sendMessageStepId,
        Stack<StackEntry> stackEntries)
    {
        ProxyStateId = proxyStateId;
        SendMessageInput = sendMessageInput;
        SendMessageStepId = sendMessageStepId;
        StackEntries = stackEntries;
    }
}