using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Tools;

/// <summary>
/// Configuration for tools used by lead nurturing agents to interact with CRM systems and manage lead workflow.
/// </summary>
public class LeadNurturingTools
{
    /// <summary>
    /// Tool configuration for managing lead scores and classifications in custom objects.
    /// </summary>
    [JsonProperty("lead_score_custom_object_tool")]
    public LeadScoreCustomObjectTool? LeadScoreCustomObjectTool { get; set; }

    /// <summary>
    /// Tool configuration for marking leads as "hands off" in custom objects.
    /// </summary>
    [JsonProperty("hands_off_custom_object_tool")]
    public HandsOffCustomObjectTool? HandsOffCustomObjectTool { get; set; }

    /// <summary>
    /// Tool configuration for assigning leads to specific teams.
    /// </summary>
    [JsonProperty("assignment_tool")]
    public AssignmentTool? AssignmentTool { get; set; }

    /// <summary>
    /// Tool configuration for scheduling demos with leads.
    /// </summary>
    [JsonProperty("demo_tool")]
    public DemoTool? DemoTool { get; set; }

    /// <summary>
    /// Additional rules for classifying when to mark leads as "hands off".
    /// </summary>
    [JsonProperty("additional_hands_off_classification_rules")]
    public AdditionalHandsOffClassificationRules? AdditionalHandsOffClassificationRules { get; set; }

    /// <summary>
    /// Configuration for enabling the FollowUp agent to automatically send follow-up messages after successful interactions.
    /// When enabled, the FollowUp agent will be activated after the ReviewerAgent approves a response.
    /// </summary>
    [JsonProperty("is_followup_agent_enabled")]
    public bool IsFollowUpAgentEnabled { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="LeadNurturingTools"/> class.
    /// </summary>
    /// <param name="leadScoreCustomObjectTool">Tool configuration for managing lead scores in custom objects.</param>
    /// <param name="assignmentTool">Tool configuration for assigning leads to teams.</param>
    /// <param name="demoTool">Tool configuration for scheduling demos.</param>
    /// <param name="additionalHandsOffClassificationRules">Additional rules for hands-off classification.</param>
    /// <param name="isFollowUpAgentEnabled">Whether to enable the FollowUp agent for automatic follow-up messaging.</param>
    [JsonConstructor]
    public LeadNurturingTools(
        LeadScoreCustomObjectTool? leadScoreCustomObjectTool,
        AssignmentTool? assignmentTool,
        DemoTool? demoTool,
        AdditionalHandsOffClassificationRules? additionalHandsOffClassificationRules,
        bool isFollowUpAgentEnabled = false)
    {
        LeadScoreCustomObjectTool = leadScoreCustomObjectTool;
        AssignmentTool = assignmentTool;
        DemoTool = demoTool;
        AdditionalHandsOffClassificationRules = additionalHandsOffClassificationRules;
        IsFollowUpAgentEnabled = isFollowUpAgentEnabled;
    }
}

/// <summary>
/// Configuration for the tool that manages hands-off custom objects, used to mark leads that should not be contacted.
/// </summary>
public class HandsOffCustomObjectTool
{
    /// <summary>
    /// The schema ID for the hands-off custom object.
    /// </summary>
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    /// <summary>
    /// The field ID that stores the reason for assigning the lead as hands-off.
    /// </summary>
    [JsonProperty("assignment_reason_field_id")]
    public string AssignmentReasonFieldId { get; set; }

    /// <summary>
    /// The field ID that stores the lead score.
    /// </summary>
    [JsonProperty("lead_score_field_id")]
    public string LeadScoreFieldId { get; set; }

    /// <summary>
    /// The field ID that stores a short summary about the lead.
    /// </summary>
    [JsonProperty("short_summary_field_id")]
    public string ShortSummaryFieldId { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="HandsOffCustomObjectTool"/> class.
    /// </summary>
    /// <param name="schemaId">The schema ID for the custom object.</param>
    /// <param name="assignmentReasonFieldId">The field ID for assignment reason.</param>
    /// <param name="leadScoreFieldId">The field ID for lead score.</param>
    /// <param name="shortSummaryFieldId">The field ID for short summary.</param>
    [JsonConstructor]
    public HandsOffCustomObjectTool(
        string schemaId,
        string assignmentReasonFieldId,
        string leadScoreFieldId,
        string shortSummaryFieldId)
    {
        SchemaId = schemaId;
        AssignmentReasonFieldId = assignmentReasonFieldId;
        LeadScoreFieldId = leadScoreFieldId;
        ShortSummaryFieldId = shortSummaryFieldId;
    }
}

/// <summary>
/// Configuration for the tool that manages lead scoring custom objects, used to assign and track lead scores.
/// </summary>
public class LeadScoreCustomObjectTool
{
    /// <summary>
    /// The schema ID for the lead score custom object.
    /// </summary>
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    /// <summary>
    /// The field ID that stores the lead classification.
    /// </summary>
    [JsonProperty("classification_field_id")]
    public string ClassificationFieldId { get; set; }

    /// <summary>
    /// The field ID that stores the reasoning behind the lead score.
    /// </summary>
    [JsonProperty("reasoning_field_id")]
    public string ReasoningFieldId { get; set; }

    /// <summary>
    /// The field ID that stores the numerical lead score.
    /// </summary>
    [JsonProperty("score_field_id")]
    public string ScoreFieldId { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="LeadScoreCustomObjectTool"/> class.
    /// </summary>
    /// <param name="schemaId">The schema ID for the custom object.</param>
    /// <param name="classificationFieldId">The field ID for classification.</param>
    /// <param name="reasoningFieldId">The field ID for reasoning.</param>
    /// <param name="scoreFieldId">The field ID for score.</param>
    [JsonConstructor]
    public LeadScoreCustomObjectTool(
        string schemaId,
        string classificationFieldId,
        string reasoningFieldId,
        string scoreFieldId)
    {
        SchemaId = schemaId;
        ClassificationFieldId = classificationFieldId;
        ReasoningFieldId = reasoningFieldId;
        ScoreFieldId = scoreFieldId;
    }
}

/// <summary>
/// Configuration for the tool that assigns leads to specific teams based on rules.
/// </summary>
public class AssignmentTool
{
    /// <summary>
    /// List of assignment rule-team pairs that define when leads should be assigned to specific teams.
    /// </summary>
    [JsonProperty("assignments")]
    public List<AssignmentPair> Assignments { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="AssignmentTool"/> class.
    /// </summary>
    /// <param name="assignments">List of assignment rule-team pairs.</param>
    [JsonConstructor]
    public AssignmentTool(List<AssignmentPair> assignments)
    {
        Assignments = assignments;
    }
}

/// <summary>
/// Represents a pairing between an assignment rule and the team to assign leads to.
/// </summary>
public class AssignmentPair
{
    /// <summary>
    /// The rule that determines when to assign a lead to the specified team.
    /// </summary>
    [JsonProperty("rule")]
    public string Rule { get; set; }

    /// <summary>
    /// The name of the team to assign the lead to.
    /// </summary>
    [JsonProperty("team_name")]
    public string TeamName { get; set; }

    /// <summary>
    /// The unique identifier of the team.
    /// </summary>
    [JsonProperty("team_id")]
    public string? TeamId { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="AssignmentPair"/> class.
    /// </summary>
    /// <param name="rule">The assignment rule.</param>
    /// <param name="teamName">The team name.</param>
    /// <param name="teamId">The team ID.</param>
    [JsonConstructor]
    public AssignmentPair(string rule, string teamName, string? teamId)
    {
        Rule = rule;
        TeamName = teamName;
        TeamId = teamId;
    }
}

/// <summary>
/// Additional classification rules for determining when leads should be marked as hands-off.
/// </summary>
public class AdditionalHandsOffClassificationRules
{
    /// <summary>
    /// List of rules for hands-off classification.
    /// </summary>
    [JsonProperty("rules")]
    public List<AdditionalHandsOffClassificationRule> Rules { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="AdditionalHandsOffClassificationRules"/> class.
    /// </summary>
    /// <param name="rules">List of hands-off classification rules.</param>
    [JsonConstructor]
    public AdditionalHandsOffClassificationRules(List<AdditionalHandsOffClassificationRule> rules)
    {
        Rules = rules;
    }
}

/// <summary>
/// Represents a single rule for determining when a lead should be marked as hands-off.
/// </summary>
public class AdditionalHandsOffClassificationRule
{
    /// <summary>
    /// The rule text that defines when a lead should be marked as hands-off.
    /// </summary>
    [JsonProperty("rule")]
    public string Rule { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="AdditionalHandsOffClassificationRule"/> class.
    /// </summary>
    /// <param name="rule">The rule text.</param>
    [JsonConstructor]
    public AdditionalHandsOffClassificationRule(string rule)
    {
        Rule = rule;
    }
}

/// <summary>
/// Configuration for the tool that schedules demos with leads.
/// </summary>
public class DemoTool
{
    /// <summary>
    /// List of fields required for scheduling a demo.
    /// </summary>
    [JsonProperty("required_fields")]
    public List<RequiredField> RequiredFields { get; set; }

    /// <summary>
    /// Configuration for integration with ChiliPiper scheduling service.
    /// </summary>
    [JsonProperty("chili_piper_config")]
    public ChiliPiperConfig? ChiliPiperConfig { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="DemoTool"/> class.
    /// </summary>
    /// <param name="requiredFields">List of fields required for scheduling a demo.</param>
    /// <param name="chiliPiperConfig">Configuration for ChiliPiper integration.</param>
    [JsonConstructor]
    public DemoTool(List<RequiredField> requiredFields, ChiliPiperConfig? chiliPiperConfig = null)
    {
        RequiredFields = requiredFields;
        ChiliPiperConfig = chiliPiperConfig;
    }
}

/// <summary>
/// Represents a field required for scheduling a demo.
/// </summary>
public class RequiredField
{
    /// <summary>
    /// The name of the required field.
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// A description of the required field.
    /// </summary>
    [JsonProperty("description")]
    public string Description { get; set; }

    /// <summary>
    /// Indicates whether the field is mandatory.
    /// </summary>
    [JsonProperty("is_required")]
    public bool IsRequired { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="RequiredField"/> class.
    /// </summary>
    /// <param name="name">The field name.</param>
    /// <param name="description">The field description.</param>
    /// <param name="isRequired">Whether the field is mandatory.</param>
    [JsonConstructor]
    public RequiredField(string name, string description, bool isRequired)
    {
        Name = name;
        Description = description;
        IsRequired = isRequired;
    }
}

/// <summary>
/// Configuration for integration with ChiliPiper scheduling service.
/// </summary>
public class ChiliPiperConfig
{
    /// <summary>
    /// The API URL for the ChiliPiper service.
    /// </summary>
    [JsonProperty("api_url")]
    public string ApiUrl { get; set; }

    /// <summary>
    /// Mappings between local field names and ChiliPiper field names.
    /// </summary>
    [JsonProperty("field_mappings")]
    public Dictionary<string, string> FieldMappings { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="ChiliPiperConfig"/> class.
    /// </summary>
    /// <param name="apiUrl">The API URL for ChiliPiper.</param>
    /// <param name="fieldMappings">Mappings between local and ChiliPiper field names.</param>
    [JsonConstructor]
    public ChiliPiperConfig(string apiUrl, Dictionary<string, string> fieldMappings)
    {
        ApiUrl = apiUrl;
        FieldMappings = fieldMappings;
    }
}