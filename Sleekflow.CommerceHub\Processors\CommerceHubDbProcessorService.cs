using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Products;
using Sleekflow.Constants;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Processors;

public interface ICosmosProcessorService
{
}

public class CommerceHubDbProcessorService : BackgroundService, ICosmosProcessorService
{
    private readonly ILogger<CommerceHubDbProcessorService> _logger;
    private readonly ICommerceHubDbResolver _commerceHubDbResolver;
    private readonly ICommerceHubDbProcessorConfig _commerceHubDbProcessorConfig;
    private readonly IProductSearchService _productSearchService;
    private readonly ICategorySearchService _categorySearchService;
    private readonly List<ChangeFeedProcessor> _changeFeedProcessors = new List<ChangeFeedProcessor>();

    public CommerceHubDbProcessorService(
        ILogger<CommerceHubDbProcessorService> logger,
        ICommerceHubDbResolver commerceHubDbResolver,
        ICommerceHubDbProcessorConfig commerceHubDbProcessorConfig,
        IProductSearchService productSearchService,
        ICategorySearchService categorySearchService)
    {
        _logger = logger;
        _commerceHubDbResolver = commerceHubDbResolver;
        _commerceHubDbProcessorConfig = commerceHubDbProcessorConfig;
        _productSearchService = productSearchService;
        _categorySearchService = categorySearchService;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var productContainerName = ContainerNames.Product;
        _logger.LogInformation(
            "Starting Change Feed Processor for changeFeedHandler.GetEntityTypeName {EntityTypeName}",
            productContainerName);

        var productProcessorName =
            $"{productContainerName.ToLower()}-change-feed-processor-{_commerceHubDbProcessorConfig.CosmosChangeFeedEnvId}";
        var productChangeFeedProcessor = _commerceHubDbResolver
            .Resolve(ContainerNames.DatabaseId, productContainerName)
            .GetChangeFeedProcessorBuilder<Product>(
                processorName: productProcessorName,
                onChangesDelegate: async (context, changes, cancellationToken) =>
                {
                    var deletedChanges = changes
                        .Where(
                            p =>
                                p.SysTypeName == SysTypeNames.Product
                                && p.RecordStatuses.Contains(RecordStatuses.Deleted))
                        .ToList();
                    if (deletedChanges.Any())
                    {
                        await _productSearchService.DeleteProductsAsync(deletedChanges.ToList());
                    }

                    var updatedChanges = changes
                        .Where(
                            p =>
                                p.SysTypeName == SysTypeNames.Product
                                && p.RecordStatuses.Contains(RecordStatuses.Active))
                        .ToList();
                    if (updatedChanges.Any())
                    {
                        await _productSearchService.IndexProductsAsync(updatedChanges.ToList());
                    }
                })
            .WithMaxItems(50)
            .WithPollInterval(TimeSpan.FromSeconds(1))
            .WithInstanceName(Environment.MachineName)
            .WithLeaseContainer(_commerceHubDbResolver.Resolve(ContainerNames.DatabaseId, "sys_changefeed_lease"))
            .Build();

        var categoryContainerName = ContainerNames.Category;
        _logger.LogInformation(
            "Starting Change Feed Processor for changeFeedHandler.GetEntityTypeName {EntityTypeName}",
            categoryContainerName);

        var categoryProcessorName =
            $"{categoryContainerName.ToLower()}-change-feed-processor-{_commerceHubDbProcessorConfig.CosmosChangeFeedEnvId}";
        var categoryChangeFeedProcessor = _commerceHubDbResolver
            .Resolve(ContainerNames.DatabaseId, categoryContainerName)
            .GetChangeFeedProcessorBuilder<Category>(
                processorName: categoryProcessorName,
                onChangesDelegate: async (context, changes, cancellationToken) =>
                {
                    var deletedChanges = changes.Where(p => p.RecordStatuses.Contains(RecordStatuses.Deleted)).ToList();
                    if (deletedChanges.Any())
                    {
                        await _categorySearchService.DeleteCategoriesAsync(deletedChanges.ToList());
                    }

                    var updatedChanges = changes.Where(p => p.RecordStatuses.Contains(RecordStatuses.Active)).ToList();
                    if (updatedChanges.Any())
                    {
                        await _categorySearchService.IndexCategoriesAsync(updatedChanges.ToList());
                    }
                })
            .WithMaxItems(50)
            .WithInstanceName(Environment.MachineName)
            .WithLeaseContainer(_commerceHubDbResolver.Resolve(ContainerNames.DatabaseId, "sys_changefeed_lease"))
            .Build();

        try
        {
            await productChangeFeedProcessor.StartAsync();

            _logger.LogInformation(
                "Started Change Feed Processor for changeFeedHandler.GetTableName {TableName}",
                productContainerName);

            _changeFeedProcessors.Add(productChangeFeedProcessor);
        }
        catch (TaskCanceledException e)
        {
            // This is expected if the cancellation token is
            // signaled.
            _logger.LogError(e, "There is an exception thrown");
        }

        try
        {
            await categoryChangeFeedProcessor.StartAsync();

            _logger.LogInformation(
                "Started Change Feed Processor for changeFeedHandler.GetTableName {TableName}",
                categoryContainerName);

            _changeFeedProcessors.Add(categoryChangeFeedProcessor);
        }
        catch (TaskCanceledException e)
        {
            // This is expected if the cancellation token is
            // signaled.
            _logger.LogError(e, "There is an exception thrown");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        {
            foreach (var processor in _changeFeedProcessors)
            {
                try
                {
                    await processor.StopAsync();
                }
                catch
                {
                    // ignored
                }
            }
        }
    }
}