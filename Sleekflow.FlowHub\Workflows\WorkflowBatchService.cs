using Azure;
using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Polly;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.Ids;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowBatchService
{

    Task UpdateWorkflowAsync(
        List<ProxyWorkflow> workflows,
        string sleekflowCompanyId);

    Task<List<ProxyWorkflow>> GetProxyWorkflowsWithMetadataAsync(
        string sleekflowCompanyId,
        List<string> workflowIds);
}

public class WorkflowBatchService : IWorkflowBatchService, IScopedService
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowStepsService _workflowStepsService;
    private readonly ICacheService _cacheService;
    private readonly ILockService _lockService;
    private readonly IWorkflowMetadataService _workflowMetadataService;
    private readonly ILogger<WorkflowBatchService> _logger;
    private readonly IIdService _idService;

    public WorkflowBatchService(
        IWorkflowRepository workflowRepository,
        IWorkflowStepsService workflowStepsService,
        ICacheService cacheService,
        ILockService lockService,
        IWorkflowMetadataService workflowMetadataService,
        ILogger<WorkflowBatchService> logger,
        IIdService idService)
    {
        _workflowRepository = workflowRepository;
        _workflowStepsService = workflowStepsService;
        _cacheService = cacheService;
        _lockService = lockService;
        _workflowMetadataService = workflowMetadataService;
        _logger = logger;
        _idService = idService;
    }

    public async Task UpdateWorkflowAsync(
    List<ProxyWorkflow> workflows,
    string sleekflowCompanyId)
    {
        if (workflows is not { Count: > 0 })
        {
            return;
        }

        var @lock = await _lockService.WaitUnitLockAsync(
            new[] { nameof(WorkflowService), "batch_update" },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var currentWorkflows = await _workflowRepository.GetWorkflowsByIdsAsync(
                sleekflowCompanyId,
                workflows.Select(w => w.WorkflowVersionedId).ToList());

            var invalidWorkflows = currentWorkflows
                .Where(cw => workflows.Any(w =>
                    w.WorkflowVersionedId == cw.WorkflowVersionedId &&
                    w.ETag != cw.ETag))
                .ToList();

            if (invalidWorkflows.Any())
            {
                throw new Exception($"Batch update failed, some workflows are modified by other operations: {string.Join(", ", invalidWorkflows.Select(w => w.WorkflowVersionedId))}");
            }

            // var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflow.WorkflowId);

            var dbRetryPolicy = Policy
                .Handle<CosmosException>()
                .Or<TimeoutException>()
                .WaitAndRetryAsync(3, retryAttempt =>
                    TimeSpan.FromSeconds(5 * retryAttempt));

            var batchOperations = workflows.Select(w => (
                w.WorkflowVersionedId,
                w.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/activation_status", w.ActivationStatus),
                    PatchOperation.Replace("/updated_at", DateTime.UtcNow)
                },
                w.ETag
            )).ToList();

            var maxConcurrency = 10;
            var semaphore = new SemaphoreSlim(maxConcurrency);

            var blobRetryPolicy = Policy
                .Handle<RequestFailedException>()
                .Or<TimeoutException>()
                .WaitAndRetryAsync(3, retryAttempt =>
                    TimeSpan.FromSeconds(5 * retryAttempt));

            var uploadTasks = workflows.Select(async w =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var steps = w.Steps;
                    await blobRetryPolicy.ExecuteAsync(async () =>
                    {
                        await _workflowStepsService.SaveWorkflowStepsAsync(
                            w.SleekflowCompanyId,
                            w.WorkflowVersionedId,
                            steps);
                    });
                }
                finally
                {
                    semaphore.Release();
                }
            });

            try
            {
                // wait for all blob update
                await Task.WhenAll(uploadTasks);

                // update database after blob update
                await dbRetryPolicy.ExecuteAsync(async () =>
                    await _workflowRepository.BatchPatchAsync(batchOperations));

            }
            catch (Exception ex)
            {
                // if database update failed, need to rollback blob update
                var rollbackTasks = workflows.Select(async w =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        // get original steps
                        var originalSteps = await _workflowStepsService.GetWorkflowStepsAsync(
                            w.SleekflowCompanyId,
                            w.WorkflowVersionedId);

                        // rollback blob update
                        await blobRetryPolicy.ExecuteAsync(async () =>
                        {
                            await _workflowStepsService.SaveWorkflowStepsAsync(
                                w.SleekflowCompanyId,
                                w.WorkflowVersionedId,
                                originalSteps);
                        });
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                try
                {
                    await Task.WhenAll(rollbackTasks);
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "rollback blob update failed");
                }

                throw new Exception("update workflow failed, try to rollback blob update", ex);
            }
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<List<ProxyWorkflow>> GetProxyWorkflowsWithMetadataAsync(
        string sleekflowCompanyId,
        List<string> workflowIds)
    {
        if (string.IsNullOrEmpty(sleekflowCompanyId))
        {
            throw new ArgumentNullException(nameof(sleekflowCompanyId));
        }

        if (workflowIds == null || workflowIds.Count == 0)
        {
            throw new ArgumentException("Workflow IDs cannot be null or empty");
        }

        _logger.LogInformation("GetProxyWorkflowsWithMetadataAsync: {WorkflowIds}", workflowIds);

        var workflows = await _workflowRepository.GetWorkflowsByIdsAsync(
            sleekflowCompanyId,
            workflowIds);

        _logger.LogInformation("GetProxyWorkflowsWithMetadataAsync: {Workflows}", workflows);

        using var semaphore = new SemaphoreSlim(10);

        var stepsTasks = workflows.Select(async w =>
        {
            await semaphore.WaitAsync();
            try
            {
                return await _workflowStepsService.GetWorkflowStepsAsync(
                    w.SleekflowCompanyId,
                    w.WorkflowVersionedId);
            }
            finally
            {
                semaphore.Release();
            }
        });

        var metadataTasks = workflows.Select(async w =>
        {
            await semaphore.WaitAsync();
            try
            {
                return await _workflowMetadataService.GetWorkflowMetadataAsync(
                    w.SleekflowCompanyId,
                    w.WorkflowVersionedId);
            }
            finally
            {
                semaphore.Release();
            }
        });

        var allSteps = await Task.WhenAll(stepsTasks);
        var allMetadata = await Task.WhenAll(metadataTasks);

        return workflows.Select((w, i) =>
            new ProxyWorkflow(w, allSteps[i], allMetadata[i])).ToList();
    }
}