﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceAutoTopUpCharge;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopup;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopUpProfiles;


namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiWabaBalanceAutoTopUpEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiWabaBalanceAutoTopUpEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiWabaBalanceAutoTopUpEventConsumer> configurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiWabaBalanceAutoTopUpEventConsumer
    : IConsumer<
        OnCloudApiWabaBalanceAutoTopUpEvent>
{
    private readonly IWabaBalanceAutoTopUpService _wabaBalanceAutoTopUpService;
    private readonly IWabaBalanceAutoTopUpProfileService _wabaBalanceAutoTopUpProfileService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiWabaBalanceAutoTopUpEventConsumer> _logger;
    private readonly ILockService _lockService;
    private readonly IBalanceAutoTopUpChargeService _balanceAutoTopUpChargeService;

    public OnCloudApiWabaBalanceAutoTopUpEventConsumer(
        IWabaBalanceAutoTopUpService wabaBalanceAutoTopUpService,
        IWabaBalanceAutoTopUpProfileService wabaBalanceAutoTopUpProfileService,
        IBusinessBalanceService businessBalanceService,
        ILockService lockService,
        ILogger<OnCloudApiWabaBalanceAutoTopUpEventConsumer> logger,
        IBalanceAutoTopUpChargeService balanceAutoTopUpChargeService)
    {
        _wabaBalanceAutoTopUpService = wabaBalanceAutoTopUpService;
        _wabaBalanceAutoTopUpProfileService = wabaBalanceAutoTopUpProfileService;
        _businessBalanceService = businessBalanceService;
        _lockService = lockService;
        _logger = logger;
        _balanceAutoTopUpChargeService = balanceAutoTopUpChargeService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiWabaBalanceAutoTopUpEvent> context)
    {
        var onCloudApiWabaBalanceAutoTopUpEvent = context.Message;
        var retryCount = context.GetRedeliveryCount();

        if (retryCount > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiWabaBalanceAutoTopUpEvent}",
                JsonConvert.SerializeObject(onCloudApiWabaBalanceAutoTopUpEvent));

            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var cancellationToken = context.CancellationToken;

        var facebookBusinessId = onCloudApiWabaBalanceAutoTopUpEvent.FacebookBusinessId;
        var facebookWabaId = onCloudApiWabaBalanceAutoTopUpEvent.FacebookWabaId;

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId,
                "BusinessBalanceAutoTopUpLock"
            },
            TimeSpan.FromSeconds(
                10),
            cancellationToken);

        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));

            return;
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            _logger.LogWarning(
                "Unable to locate business account balance object with {FacebookBusinessId}",
                facebookBusinessId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);

            return;
        }

        var wabaBalance = businessBalance.WabaBalances?.Find(x => x.FacebookWabaId == facebookWabaId);
        if (wabaBalance == null)
        {
            _logger.LogError(
                "Unable to locate waba account balance object with {FacebookBusinessId} {FacebookWabaId}",
                facebookBusinessId,
                facebookWabaId);

            throw new SfInternalErrorException(
                $"Unable to locate waba account balance object with {facebookBusinessId} {facebookWabaId}");
        }

        var wabaBalanceAutoTopProfile =
            await _wabaBalanceAutoTopUpProfileService.GetWithFacebookWabaIdAsync(facebookWabaId);

        if (wabaBalanceAutoTopProfile is null)
        {
            _logger.LogWarning(
                "Unable to locate waba balance auto top up profile object with {FacebookWabaId}",
                facebookWabaId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);

            return;
        }

        var performAutoTopUp = _wabaBalanceAutoTopUpService.ShouldPerformAutoTopUp(
            facebookBusinessId,
            facebookWabaId,
            wabaBalance,
            wabaBalanceAutoTopProfile);

        if (performAutoTopUp)
        {
            await _balanceAutoTopUpChargeService.ChargeAutoTopUpFee(onCloudApiWabaBalanceAutoTopUpEvent);
        }

        await _lockService.ReleaseAsync(@lock, cancellationToken);
    }
}