﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Dynamics365.Objects;

public class Dynamics365GetObjectsEntityRecordCountOutput
{
    [JsonConstructor]
    public Dynamics365GetObjectsEntityRecordCountOutput(
        string odataContext,
        EntityRecordCountCollection entityRecordCountCollection)
    {
        OdataContext = odataContext;
        RecordCountCollection = entityRecordCountCollection;
    }

    [JsonProperty("@odata.context")]
    public string OdataContext { get; set; }

    [JsonProperty("entity_record_count_collection")]
    public EntityRecordCountCollection RecordCountCollection { get; set; }

    public class EntityRecordCountCollection
    {
        [JsonProperty("Count")]
        public int Count { get; set; }

        [JsonProperty("IsReadOnly")]
        public bool IsReadOnly { get; set; }

        [JsonProperty("Keys")]
        public List<string> Keys { get; set; }

        [JsonProperty("Values")]
        public List<long> Values { get; set; }

        [JsonConstructor]
        public EntityRecordCountCollection(int count, bool isReadOnly, List<string> keys, List<long> values)
        {
            Count = count;
            IsReadOnly = isReadOnly;
            Keys = keys;
            Values = values;
        }
    }
}