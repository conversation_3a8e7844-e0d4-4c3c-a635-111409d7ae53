﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.Blobs;

namespace Sleekflow.IntelligentHub.Blobs;

public interface IBlobService
{
    Task<List<PublicBlob>> CreateBlobDownloadSasUrls(
        string sleekflowCompanyId,
        List<string> blobNames,
        string blobType);

    Task<List<string>> DeleteBlobs(
        string sleekflowCompanyId,
        List<string> blobNames,
        string blobType);

    Task<List<PublicBlob>> CreateBlobUploadSasUrls(
        string sleekflowCompanyId,
        int numberOfBlobs,
        string blobType);
}

public class BlobService : IScopedService, IBlobService
{
    private readonly ILogger<BlobService> _logger;
    private readonly IRequestClient<CreateBlobDownloadSasUrlsRequest> _createBlobDownloadSasUrlsRequestClient;
    private readonly IRequestClient<CreateBlobUploadSasUrlsRequest> _createBlobUploadSasUrlsRequestClient;
    private readonly IRequestClient<DeleteBlobsRequest> _deleteBlobsRequestClient;

    public BlobService(
        ILogger<BlobService> logger,
        IRequestClient<CreateBlobDownloadSasUrlsRequest> createBlobDownloadSasUrlsRequestClient,
        IRequestClient<CreateBlobUploadSasUrlsRequest> createBlobUploadSasUrlsRequestClient,
        IRequestClient<DeleteBlobsRequest> deleteBlobsRequestClient)
    {
        _logger = logger;
        _createBlobDownloadSasUrlsRequestClient = createBlobDownloadSasUrlsRequestClient;
        _createBlobUploadSasUrlsRequestClient = createBlobUploadSasUrlsRequestClient;
        _deleteBlobsRequestClient = deleteBlobsRequestClient;
    }

    public async Task<List<PublicBlob>> CreateBlobDownloadSasUrls(
        string sleekflowCompanyId,
        List<string> blobNames,
        string blobType)
    {
        _logger.LogInformation(
            "CreateBlobDownloadSasUrls {SleekflowCompanyId} {BlobNames} {BlobType}",
            sleekflowCompanyId,
            blobNames,
            blobType);

        var createBlobDownloadSasUrlsReplyResponse =
            await _createBlobDownloadSasUrlsRequestClient.GetResponse<CreateBlobDownloadSasUrlsReply>(
                new CreateBlobDownloadSasUrlsRequest(
                    sleekflowCompanyId,
                    blobNames,
                    blobType,
                    null,
                    null));

        var createBlobDownloadSasUrlsReply = createBlobDownloadSasUrlsReplyResponse.Message;

        _logger.LogInformation(
            "CreateBlobDownloadSasUrls response {Message}",
            createBlobDownloadSasUrlsReply.DownloadBlobs);

        return createBlobDownloadSasUrlsReply.DownloadBlobs;
    }

    public async Task<List<string>> DeleteBlobs(
        string sleekflowCompanyId,
        List<string> blobNames,
        string blobType)
    {
        var deleteBlobsReplyResponse =
            await _deleteBlobsRequestClient.GetResponse<DeleteBlobsReply>(
                new DeleteBlobsRequest(
                    sleekflowCompanyId,
                    blobNames,
                    blobType));

        var deleteBlobReply = deleteBlobsReplyResponse.Message;

        return deleteBlobReply.BlobNames;
    }

    public async Task<List<PublicBlob>> CreateBlobUploadSasUrls(
        string sleekflowCompanyId,
        int numberOfBlobs,
        string blobType)
    {
        var createBlobUploadSasUrlsReplyResponse =
            await _createBlobUploadSasUrlsRequestClient.GetResponse<CreateBlobUploadSasUrlsReply>(
                new CreateBlobUploadSasUrlsRequest(
                    sleekflowCompanyId,
                    numberOfBlobs,
                    blobType));

        var createBlobUploadSasUrlsReply = createBlobUploadSasUrlsReplyResponse.Message;

        return createBlobUploadSasUrlsReply.UploadBlobs;
    }
}