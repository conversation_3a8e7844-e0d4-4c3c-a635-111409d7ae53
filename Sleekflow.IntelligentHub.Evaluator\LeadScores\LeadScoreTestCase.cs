﻿using Sleekflow.IntelligentHub.Models.Reviewers;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores;

public record LeadScoreTestCase(
    string Scenario,
    ChatMessageContent[] ChatMessageContents,
    EvaluatedScore ExpectedEvaluatedScore,
    List<LeadScoreConfig>? LeadScoreConfigs);

public record LeadScoreConfig(
    string Name,
    string AdditionalPrompt);