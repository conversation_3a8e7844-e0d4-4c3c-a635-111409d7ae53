﻿using System.Collections;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Sleekflow.Validations;

public class StringArrayElementValidationAttribute : ValidationAttribute
{
    private readonly int _minLength;
    private readonly int _maxLength;
    private readonly Regex? _regex;

    public StringArrayElementValidationAttribute(int minLength, int maxLength, string? regexPattern = null)
    {
        _minLength = minLength;
        _maxLength = maxLength;
        _regex = !string.IsNullOrEmpty(regexPattern) ? new Regex(regexPattern, RegexOptions.Compiled) : null;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null)
        {
            return ValidationResult.Success;
        }

        if (value is not IEnumerable
            || value is string)
        {
            return ValidationResult.Success;
        }

        if (value is IEnumerable<string> list)
        {
            foreach (var str in list)
            {
                if (str.Length < _minLength)
                {
                    return new ValidationResult(
                        $"{str} with {str.Length} characters in the list is less than {_minLength} characters long restriction.",
                        new[]
                        {
                            validationContext.MemberName!
                        });
                }

                if (str.Length > _maxLength)
                {
                    return new ValidationResult(
                        $"{str} with {str.Length} characters in the list is exceeding {_maxLength} characters long restriction.",
                        new[]
                        {
                            validationContext.MemberName!
                        });
                }

                if (_regex != null && !_regex.IsMatch(str))
                {
                    return new ValidationResult(
                        $"{str} in the list is not matching the regex pattern.",
                        new[]
                        {
                            validationContext.MemberName!
                        });
                }
            }
        }

        return ValidationResult.Success;
    }
}