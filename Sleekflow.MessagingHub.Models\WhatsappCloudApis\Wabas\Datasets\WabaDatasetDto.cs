﻿using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.Datasets;

public class WabaDatasetDto
{
    [JsonProperty("facebook_dataset_id")]
    public string FacebookDatasetId { get; set; }

    [JsonProperty("facebook_dataset_name")]
    public string? FacebookDatasetName { get; set; }

    [JsonConstructor]
    public WabaDatasetDto(string facebookDatasetId, string? facebookDatasetName)
    {
        FacebookDatasetId = facebookDatasetId;
        FacebookDatasetName = facebookDatasetName;
    }

    public WabaDatasetDto(WabaDataset wabaDataset)
        : this(wabaDataset.FacebookDatasetId, wabaDataset.FacebookDatasetName)
    {
    }
}