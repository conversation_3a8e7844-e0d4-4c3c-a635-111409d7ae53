﻿using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Providers.States;

public interface ISyncObjectsProgressStateRepository : IRepository<SyncObjectsProgressState>
{
}

public class SyncObjectsProgressStateRepository
    : BaseRepository<SyncObjectsProgressState>,
        ISyncObjectsProgressStateRepository,
        ISingletonService
{
    public SyncObjectsProgressStateRepository(
        ILogger<BaseRepository<SyncObjectsProgressState>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}