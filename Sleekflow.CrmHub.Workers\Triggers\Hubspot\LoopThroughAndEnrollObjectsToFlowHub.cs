﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Workers.Utils;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class LoopThroughAndEnrollObjectsToFlowHub
{
    private readonly ILogger<LoopThroughAndEnrollObjectsToFlowHub> _logger;

    public LoopThroughAndEnrollObjectsToFlowHub(
        ILogger<LoopThroughAndEnrollObjectsToFlowHub> logger)
    {
        _logger = logger;
    }

    public class LoopThroughAndEnrollObjectsToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            bool isCustomObject,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            IsCustomObject = isCustomObject;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    [Function("Hubspot_LoopThroughAndEnrollObjectsToFlowHub")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<LoopThroughAndEnrollObjectsToFlowHubInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (LoopThroughAndEnrollObjectsToFlowHubInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (loopThroughAndEnrollObjectsToFlowHubInput, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "Hubspot_LoopThroughAndEnrollObjectsToFlowHub_Orchestrator",
            loopThroughAndEnrollObjectsToFlowHubInput);

        logger.LogInformation($"Started Hubspot_LoopThroughAndEnrollObjectsToFlowHub_Orchestrator with ID = [{instanceId}]");

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get Hubspot_LoopThroughAndEnrollObjectsToFlowHub_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}