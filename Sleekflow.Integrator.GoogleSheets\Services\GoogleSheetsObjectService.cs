﻿using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.Drive.v3;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.GoogleSheets.Configs;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.GoogleSheets.Services;

public interface IGoogleSheetsObjectService
{
    Task<GoogleSheetsObjectService.GetFieldsOutput> GetFieldsAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName);

    Task<object> GetObjectAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName);

    Task CreateObjectAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName,
        Dictionary<string, object?> dict);

    Task UpdateObjectAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName,
        Dictionary<string, object?> dict);

    Task<List<Dictionary<string, object?>>> SearchObjectsAsync(
        GoogleSheetsAuthentication authentication,
        string entityTypeName,
        List<TypedId> typedIds,
        List<SearchObjectCondition>? conditions);

    Task<List<GoogleSheetsObjectService.SpreadsheetInfo>?> GetSpreadsheetInfosAsync(
        GoogleSheetsAuthentication authentication);
}

public class GoogleSheetsObjectService : ISingletonService, IGoogleSheetsObjectService
{
    private static class Constants
    {
        public const string RowEntityTypeName = "Row";
        public const string SpreadsheetEntityTypeName = "Spreadsheet";
        public const string WorksheetEntityTypeName = "Worksheet";
        public const string SpreadsheetType = "Spreadsheet";
        public const string WorksheetType = "Worksheet";
        public const string HeaderRowType = "HeaderRow";
        public const string DriveScope = "https://www.googleapis.com/auth/drive";
        public const string SpreadsheetsScope = "https://www.googleapis.com/auth/spreadsheets";
        public const string WorksheetIdKey = "id";
        public const string WorksheetTitleKey = "title";
        public const string SpreadsheetIdKey = "id";
        public const string SpreadsheetNameKey = "name";
        public const string ListDriveSpreadsheetFilesRequestQuery =
            "mimeType='application/vnd.google-apps.spreadsheet'";

        public static readonly string[] Weekdays =
        {
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"
        };

        public static readonly string[] DefaultDateFormats =
        {
            "MM/dd/yyyy",
            "M/d/yyyy",
            "yyyy-MM-dd",
            "yyyy-M-d",
            "yyyy/MM/dd",
            "yyyy/M/d",
            "dd-MM-yyyy",
            "d-M-yyyy"
        };

        public static readonly string[] DefaultDateTimeFormats =
        {
            "MM/dd/yyyy HH:mm:ss",
            "M/d/yyyy H:mm:ss",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-ddTHH:mm:sszzz",
            "yyyy-M-d H:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/M/d H:mm:ss",
            "dd-MM-yyyy HH:mm:ss",
            "d-M-yyyy H:mm:ss",
            "MM/dd/yyyy hh:mm tt",
            "M/d/yyyy h:mm tt",
            "yyyy-MM-dd hh:mm tt",
            "yyyy-M-d h:mm tt",
            "yyyy/MM/dd hh:mm tt",
            "yyyy/M/d h:mm tt",
            "dd-MM-yyyy hh:mm tt",
            "d-M-yyyy h:mm tt",
            "M/d/yyyy h:mm:ss tt zzz",
            "MM/dd/yyyy hh:mm:ss tt zzz"
        };
    }

    private static class Operators
    {
        public const string IsEqualTo = "isEqualTo";
        public const string IsNotEqualTo = "isNotEqualTo";
        public const string Contains = "contains";
        public const string DoesNotContain = "doesNotContain";
        public const string Exists = "exists";
        public const string DoesNotExist = "doesNotExist";
        public const string IsAfter = "isAfter";
        public const string IsBefore = "isBefore";
        public const string IsToday = "isToday";
        public const string IsOn = "isOn";
        public const string IsGreaterThan = "isGreaterThan";
        public const string IsLessThan = "isLessThan";
    }

    private readonly IGoogleSheetsConfig _googleSheetsConfig;
    private readonly ICacheService _cacheService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public GoogleSheetsObjectService(
        IGoogleSheetsConfig googleSheetsConfig,
        ICacheService cacheService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _googleSheetsConfig = googleSheetsConfig;
        _cacheService = cacheService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public class GetFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public class GoogleWorksheetRowRange
    {
        [JsonProperty("range_start")]
        public char RangeStart { get; set; }

        [JsonProperty("range_end")]
        public char RangeEnd { get; set; }

        [JsonConstructor]
        public GoogleWorksheetRowRange(char rangeStart, char rangeEnd)
        {
            RangeStart = rangeStart;
            RangeEnd = rangeEnd;
        }
    }

    public class SpreadsheetInfo
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("worksheets")]
        public List<WorksheetInfo> Worksheets { get; set; }
    }

    public class WorksheetInfo
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }
    }

    public async Task<GetFieldsOutput> GetFieldsAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName)
    {
        return entityTypeName switch
        {
            Constants.RowEntityTypeName => await GetRowFields(authentication, typedIds),
            _ => throw new NotSupportedException($"Entity type {entityTypeName} is not supported")
        };
    }

    public async Task<object> GetObjectAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName)
    {
        return entityTypeName switch
        {
            Constants.WorksheetEntityTypeName => await GetWorksheet(authentication, typedIds),
            _ => throw new NotSupportedException($"Entity type {entityTypeName} is not supported")
        };
    }

    public async Task CreateObjectAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName,
        Dictionary<string, object?> dict)
    {
        switch (entityTypeName)
        {
            case Constants.RowEntityTypeName:
                await CreateRow(authentication, typedIds, dict);
                break;
            default:
                throw new NotSupportedException($"Entity type {entityTypeName} is not supported");
        }
    }

    public async Task UpdateObjectAsync(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        string entityTypeName,
        Dictionary<string, object?> dict)
    {
        switch (entityTypeName)
        {
            case Constants.RowEntityTypeName:
                await UpdateRow(authentication, typedIds, dict);
                break;
            default:
                throw new NotSupportedException($"Entity type {entityTypeName} is not supported");
        }
    }

    public async Task<List<Dictionary<string, object?>>> SearchObjectsAsync(
        GoogleSheetsAuthentication authentication,
        string entityTypeName,
        List<TypedId> typedIds,
        List<SearchObjectCondition>? conditions)
    {
        switch (entityTypeName)
        {
            case Constants.RowEntityTypeName:
                return await SearchRows(authentication, typedIds, conditions);
            case Constants.WorksheetEntityTypeName:
                return await SearchWorksheets(authentication, typedIds);
            case Constants.SpreadsheetEntityTypeName:
                return await SearchSpreadsheets(authentication);
            default:
                throw new NotSupportedException($"Entity type {entityTypeName} is not supported");
        }
    }

    public async Task<List<SpreadsheetInfo>?> GetSpreadsheetInfosAsync(
        GoogleSheetsAuthentication authentication)
    {
        return await _cacheService.CacheAsync(
            $"{nameof(GoogleSheetsObjectService)}-{authentication.Id}-Spreadsheet",
            async () =>
            {
                var driveService = CreateDriveService(authentication);
                var listDriveFilesRequest = driveService.Files.List();
                listDriveFilesRequest.Q = Constants.ListDriveSpreadsheetFilesRequestQuery;
                var spreadsheetFilesResponse = await listDriveFilesRequest.ExecuteAsync();
                var spreadsheetFiles = spreadsheetFilesResponse.Files;

                if (spreadsheetFiles is null || !spreadsheetFiles.Any())
                {
                    return null;
                }

                var sheetsService = CreateSheetsService(authentication);
                var spreadsheetInfos = new List<SpreadsheetInfo>();

                // Create a list of tasks to fetch worksheets for all spreadsheets concurrently
                var readQuotaPerMinutePerUser = int.Parse(_googleSheetsConfig.GoogleSheetsReadQuotaPerMinutePerUser);
                var tasks = spreadsheetFiles.Take(readQuotaPerMinutePerUser / 2).Select(async spreadsheetFile =>
                {
                    var spreadsheetInfo = new SpreadsheetInfo
                    {
                        Id = spreadsheetFile.Id,
                        Name = spreadsheetFile.Name
                    };

                    var worksheetsResponse = await ExecuteWithExponentialBackoff(async () =>
                        (await sheetsService.Spreadsheets.Get(spreadsheetInfo.Id).ExecuteAsync()).Sheets);

                    spreadsheetInfo.Worksheets = worksheetsResponse.Select(
                        s => new WorksheetInfo
                        {
                            Id = s.Properties.Title,
                            Name = s.Properties.Title
                        }).ToList();

                    return spreadsheetInfo;
                }).ToList();

                // Wait for all tasks to complete
                var results = await Task.WhenAll(tasks);

                // Add the results to the final list
                spreadsheetInfos.AddRange(results);

                return spreadsheetInfos;
            });
    }

    private async Task<List<Dictionary<string, object?>>> GetWorksheet(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds)
    {
        var (spreadsheetId, worksheetId) = GetSpreadsheetAndWorksheetIds(typedIds);
        var rowRange = await GetRowRange(authentication, typedIds);

        var sheetsService = CreateSheetsService(authentication);
        var worksheetValuesResponse = await ExecuteWithExponentialBackoff(async () =>
            await sheetsService.Spreadsheets.Values.Get(
                spreadsheetId,
                $"{worksheetId}!{rowRange.RangeStart}:{rowRange.RangeEnd}").ExecuteAsync());
        var fields = (await GetRowFields(authentication, typedIds)).CreatableFields;
        var worksheetValues = worksheetValuesResponse.Values?.Skip(1);  // Skip headers
        if (worksheetValues is null)
        {
            return new List<Dictionary<string, object?>>();
        }

        return worksheetValues.Select(
            (x, index) =>
            {
                var rowDict = GetRowDictFromFieldsAndValues(
                    fields, x.Select(y => y?.ToString() ?? string.Empty).ToList());
                rowDict["Id"] = (index + 2).ToString();
                return rowDict;
            }).ToList();
    }

    private static string DetectColumnType(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return "string";
        }

        // Try parsing as bool
        if (bool.TryParse(value, out _))
        {
            return "boolean";
        }

        // Try parsing as number
        if (double.TryParse(value, out _))
        {
            return "number";
        }

        // Try parsing as DateTimeOffset without time component with common formats
        if (DateTimeOffset.TryParseExact(
                value,
                Constants.DefaultDateFormats,
                null,
                System.Globalization.DateTimeStyles.None,
                out _))
        {
            return "date";
        }

        // Try parsing as DateTimeOffset with common formats
        if (DateTimeOffset.TryParseExact(
                value,
                Constants.DefaultDateTimeFormats,
                null,
                System.Globalization.DateTimeStyles.None,
                out _))
        {
            return "datetime";
        }

        return "string";
    }

    private static object? TryParseValue(string? value, string fieldType)
    {
        if (value is null)
        {
            return null;
        }

        switch (fieldType)
        {
            case "boolean":
                if (bool.TryParse(value, out var boolValue))
                {
                    return boolValue;
                }

                break;
            case "number":
                if (double.TryParse(value, out var numValue))
                {
                    return numValue;
                }

                break;
            case "date":
            case "datetime":
                if (DateTimeOffset.TryParseExact(
                        value,
                        Constants.DefaultDateTimeFormats,
                        null,
                        System.Globalization.DateTimeStyles.None,
                        out var dateTimeValue))
                {
                    return dateTimeValue;
                }

                break;
            default:
                return value;
        }

        return null;
    }

    private async Task<GetFieldsOutput> GetRowFields(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds)
    {
        var sheetsService = CreateSheetsService(authentication);
        var (spreadsheetId, worksheetId) = GetSpreadsheetAndWorksheetIds(typedIds);
        var headerRowId = typedIds.Find(i => i.Type == Constants.HeaderRowType)?.Id ?? "1";

        // Get headers and first data row
        var worksheetValuesResponse = await ExecuteWithExponentialBackoff(async () =>
            await sheetsService.Spreadsheets.Values.Get(
                spreadsheetId,
                $"{worksheetId}!A{headerRowId}:ZZZ{int.Parse(headerRowId) + 1}").ExecuteAsync());

        if (worksheetValuesResponse.Values is null || !worksheetValuesResponse.Values.Any())
        {
            return new GetFieldsOutput(
                new List<GetTypeFieldsOutputFieldDto>(),
                new List<GetTypeFieldsOutputFieldDto>(),
                new List<GetTypeFieldsOutputFieldDto>());
        }

        var headers = worksheetValuesResponse.Values[0];
        var firstDataRow = worksheetValuesResponse.Values.Count > 1 ? worksheetValuesResponse.Values[1] : null;

        var rowFields = headers.Select((header, index) =>
        {
            var type = "string";
            if (firstDataRow != null && index < firstDataRow.Count && !header!.ToString()!.ToLower().Contains("phone"))
            {
                type = DetectColumnType(firstDataRow[index]?.ToString() ?? string.Empty);
            }

            return CreateFieldDto(header!.ToString()!, index, type);
        }).ToList();

        var creatableAndUpdatableFields = rowFields;
        var viewableFields = rowFields;
        viewableFields.Add(new GetTypeFieldsOutputFieldDto(
            calculated: false,
            compoundFieldName: string.Empty,
            createable: false,
            custom: false,
            encrypted: false,
            label: "Id",
            length: 500000,
            name: "Id",
            new List<GetTypeFieldsOutputPicklistValue>(),
            soapType: "string",
            type: "string",
            unique: true,
            updateable: false));

        return new GetFieldsOutput(creatableAndUpdatableFields, creatableAndUpdatableFields, viewableFields);
    }

    private static GetTypeFieldsOutputFieldDto CreateFieldDto(string label, int index, string type) =>
        new(
            calculated: false,
            compoundFieldName: string.Empty,
            createable: true,
            custom: false,
            encrypted: false,
            label: label,
            length: 50000,
            name: ((char)('A' + index)).ToString(),
            picklistValues: new List<GetTypeFieldsOutputPicklistValue>(),
            soapType: type,
            type: type,
            unique: false,
            updateable: true,
            mandatory: false);

    private async Task CreateRow(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        Dictionary<string, object?> dict)
    {
        var (spreadsheetId, worksheetId) = GetSpreadsheetAndWorksheetIds(typedIds);
        var rowRange = await GetRowRange(authentication, typedIds);
        var fields = (await GetRowFields(authentication, typedIds)).CreatableFields;

        var sheetsService = CreateSheetsService(authentication);
        var createRowRequest = sheetsService.Spreadsheets.Values.Append(
            new ValueRange { Values = new List<IList<object>> { ExtractRowValues(dict, fields, rowRange) } },
            spreadsheetId,
            $"{worksheetId}!{rowRange.RangeStart}:{rowRange.RangeEnd}");
        createRowRequest.ValueInputOption =
            SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOptionEnum.USERENTERED;
        await ExecuteWithExponentialBackoff(async () =>
            await createRowRequest.ExecuteAsync());
    }

    private async Task UpdateRow(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        Dictionary<string, object?> dict)
    {
        var (spreadsheetId, worksheetId, rowId) = GetSpreadsheetWorksheetAndRowIds(typedIds);
        var rowRange = await GetRowRange(authentication, typedIds);

        var sheetsService = CreateSheetsService(authentication);
        var currentRow = await GetRowValues(authentication, typedIds);
        var updateRowRequest = sheetsService.Spreadsheets.Values.Update(
            new ValueRange { Values = new List<IList<object>> { GetUpdatedRowValues(currentRow, dict, rowRange) } },
            spreadsheetId,
            $"{worksheetId}!{rowRange.RangeStart + rowId}:{rowRange.RangeEnd + rowId}");
        updateRowRequest.ValueInputOption =
            SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.USERENTERED;
        await ExecuteWithExponentialBackoff(async () =>
            await updateRowRequest.ExecuteAsync());
    }

    private async Task<List<Dictionary<string, object?>>> SearchRows(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds,
        List<SearchObjectCondition>? conditions)
    {
        var (spreadsheetId, worksheetId) = GetSpreadsheetAndWorksheetIds(typedIds);
        var rowRange = await GetRowRange(authentication, typedIds);

        var sheetsService = CreateSheetsService(authentication);
        var worksheetValuesResponse = await ExecuteWithExponentialBackoff(async () =>
            await sheetsService.Spreadsheets.Values.Get(
                spreadsheetId,
                $"{worksheetId}!{rowRange.RangeStart}:{rowRange.RangeEnd}").ExecuteAsync());

        var fields =
            (await GetFieldsAsync(authentication, typedIds, Constants.RowEntityTypeName)).ViewableFields;
        var rows = worksheetValuesResponse.Values?.Skip(1).Select(
            (x, index) =>
            {
                var rowDict = GetRowDictFromFieldsAndValues(
                    fields, x.Select(y => y?.ToString() ?? string.Empty).ToList());
                rowDict["Id"] = (index + 2).ToString();
                return rowDict;
            }).ToList();
        if (rows is null)
        {
            return new List<Dictionary<string, object?>>();
        }

        if (conditions is null)
        {
            return rows;
        }

        return GetFilteredRows(rows, fields, conditions);
    }

    private async Task<List<Dictionary<string, object?>>> SearchWorksheets(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds)
    {
        var spreadsheetId = GetSpreadsheetId(typedIds);

        var sheetsService = CreateSheetsService(authentication);
        var worksheetsResponse = await ExecuteWithExponentialBackoff(async () =>
            await sheetsService.Spreadsheets.Get(spreadsheetId).ExecuteAsync());
        var worksheets = worksheetsResponse.Sheets;
        return worksheets.Select(
            s => new Dictionary<string, object?>
            {
                { Constants.WorksheetIdKey, s.Properties.Title },
                { Constants.WorksheetTitleKey, s.Properties.Title }
            }).ToList();
    }

    private async Task<List<Dictionary<string, object?>>> SearchSpreadsheets(
        GoogleSheetsAuthentication authentication)
    {
        var driveService = CreateDriveService(authentication);
        var listDriveFilesRequest = driveService.Files.List();
        listDriveFilesRequest.Q = Constants.ListDriveSpreadsheetFilesRequestQuery;
        var spreadsheetFilesResponse = await listDriveFilesRequest.ExecuteAsync();
        return spreadsheetFilesResponse.Files.Select(
            f => new Dictionary<string, object?>
            {
                { Constants.SpreadsheetIdKey, f.Id },
                { Constants.SpreadsheetNameKey, f.Name }
            }).ToList();
    }

    private async Task<GoogleWorksheetRowRange> GetRowRange(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds)
    {
        // minus 1 to skip non-Google field "Id"
        var fieldCount = (await GetRowFields(authentication, typedIds)).CreatableFields.Count - 1;
        return new GoogleWorksheetRowRange('A', (char)('A' + fieldCount - 1));
    }

    private static IList<object> ExtractRowValues(
        Dictionary<string, object?> dict,
        List<GetTypeFieldsOutputFieldDto> fields,
        GoogleWorksheetRowRange rowRange)
    {
        var result = new List<object>();
        for (var c = rowRange.RangeStart; c <= rowRange.RangeEnd; c++)
        {
            var key = c.ToString();
            var field = fields.Find(f => f.Name == key);
            var value = string.Empty;

            if (dict.ContainsKey(key) && dict[key]?.ToString() is not null)
            {
                if (dict[key] is DateTimeOffset dto
                    && (field?.Type == "date" || field?.Type == "datetime"))
                {
                    value = field.Type == "date" ? dto.ToString("yyyy-MM-dd") : dto.ToString("yyyy-MM-ddTHH:mm:ssZ");
                }
                else
                {
                    value = dict[key]!.ToString();
                }
            }

            result.Add(value!);
        }

        return result;
    }

    private static IList<object> GetUpdatedRowValues(
        IList<string> currentRow,
        Dictionary<string, object?> dict,
        GoogleWorksheetRowRange rowRange)
    {
        var result = new List<object>();
        char c;
        int i;
        for (c = rowRange.RangeStart, i = 0; c <= rowRange.RangeEnd; c++, i++)
        {
            var key = c.ToString();
            if (dict.ContainsKey(key))
            {
                result.Add(dict[key]?.ToString() ?? string.Empty);
            }
            else if (currentRow.Count > i)
            {
                result.Add(currentRow[i]);
            }
            else
            {
                // If the current row doesn't have enough columns, it means the cell is empty
                result.Add(string.Empty);
            }
        }

        return result;
    }

    private static Dictionary<string, object?> GetRowDictFromFieldsAndValues(
        List<GetTypeFieldsOutputFieldDto> fields,
        List<string> values)
    {
        var result = new Dictionary<string, object?>();
        for (var i = 0; i < fields.Count; i++)
        {
            var value = i < values.Count ? values[i] : null;
            object? parsedValue = null;

            if (value != null)
            {
                switch (fields[i].Type)
                {
                    case "boolean":
                        if (bool.TryParse(value, out var boolValue))
                        {
                            parsedValue = boolValue;
                        }

                        break;
                    case "number":
                        if (double.TryParse(value, out var numValue))
                        {
                            parsedValue = numValue;
                        }

                        break;
                    case "date":
                    case "datetime":
                        if (DateTimeOffset.TryParseExact(
                                value,
                                fields[i].Type == "date" ? Constants.DefaultDateFormats : Constants.DefaultDateTimeFormats,
                                null,
                                System.Globalization.DateTimeStyles.None,
                                out var dateTimeValue))
                        {
                            parsedValue = dateTimeValue.ToString("yyyy-MM-ddTHH:mm:sszzz");
                        }

                        break;
                    default:
                        parsedValue = value;
                        break;
                }
            }

            result.Add(fields[i].Name, parsedValue);
        }

        return result;
    }

    private static List<Dictionary<string, object?>> GetFilteredRows(
        List<Dictionary<string, object?>> rows,
        List<GetTypeFieldsOutputFieldDto> fields,
        List<SearchObjectCondition> conditions)
    {
        return rows.Where(row => conditions.TrueForAll(condition =>
        {
            var fieldType = fields.Find(f => f.Name == condition.FieldName)?.Type;
            if (fieldType is null)
            {
                return false;
            }

            var fieldValue = TryParseValue(row[condition.FieldName]?.ToString(), fieldType);

            var conditionValue = condition.Value?.ToString();

            return EvaluateFieldCondition(fieldValue, fieldType, condition.Operator, conditionValue);
        })).ToList();
    }

    private static bool EvaluateFieldCondition(
        object? fieldValue,
        string fieldType,
        string conditionOperator,
        string? conditionValue)
    {
        if (conditionValue == null && fieldType != "datetime" && fieldType != "date")
        {
            return conditionOperator switch
            {
                Operators.Exists => fieldValue is not null,
                Operators.DoesNotExist => fieldValue is null,
                _ => false
            };
        }

        if (fieldValue is null)
        {
            return false;
        }

        switch (fieldType)
        {
            case "string":
                return EvaluateStringFieldCondition(fieldValue.ToString()!, conditionOperator, conditionValue!);
            case "number":
                return EvaluateNumberFieldCondition((double)fieldValue, conditionOperator, double.Parse(conditionValue!));
            case "date":
            case "datetime":
                DateTimeOffset? parsedConditionValue = null;
                if (DateTimeOffset.TryParseExact(
                        conditionValue,
                        Constants.DefaultDateFormats.Concat(Constants.DefaultDateTimeFormats).ToArray(),
                        null,
                        System.Globalization.DateTimeStyles.None,
                        out var dateTimeValue))
                {
                    parsedConditionValue = dateTimeValue;
                }
                else if (!Constants.Weekdays.Contains(conditionValue) && conditionOperator != Operators.IsToday)
                {
                    return false;
                }

                return EvaluateDateTimeFieldCondition(
                    (DateTimeOffset)fieldValue, conditionOperator, conditionValue == null ? null : parsedConditionValue == null ? conditionValue : parsedConditionValue);
            case "boolean":
                return EvaluateBooleanFieldCondition((bool)fieldValue, conditionOperator, bool.Parse(conditionValue!));
        }

        return false;
    }

    private static bool EvaluateStringFieldCondition(string value, string conditionOperator, string conditionValue) =>
        conditionOperator switch
        {
            Operators.IsEqualTo => value == conditionValue,
            Operators.IsNotEqualTo => value != conditionValue,
            Operators.Contains => value.Contains(conditionValue),
            Operators.DoesNotContain => !value.Contains(conditionValue),
            _ => false
        };

    private static bool EvaluateNumberFieldCondition(double value, string conditionOperator, double conditionValue) =>
        conditionOperator switch
        {
            Operators.IsEqualTo => AreNumbersEqual(value, conditionValue),
            Operators.IsNotEqualTo => !AreNumbersEqual(value, conditionValue),
            Operators.IsGreaterThan => value > conditionValue,
            Operators.IsLessThan => value < conditionValue,
            _ => false
        };

    private static bool EvaluateDateTimeFieldCondition(DateTimeOffset value, string conditionOperator, object? conditionValue) =>
        conditionOperator switch
        {
            Operators.IsEqualTo => value == (DateTimeOffset)conditionValue!,
            Operators.IsNotEqualTo => value != (DateTimeOffset)conditionValue!,
            Operators.IsAfter => value > (DateTimeOffset)conditionValue!,
            Operators.IsBefore => value < (DateTimeOffset)conditionValue!,
            Operators.IsToday => value.Date == DateTimeOffset.UtcNow.Date,
            Operators.IsOn => conditionValue is string day && Constants.Weekdays.Contains(day) && value.DayOfWeek.ToString() == day,
            _ => false
        };

    private static bool EvaluateBooleanFieldCondition(bool value, string conditionOperator, bool conditionValue) =>
        conditionOperator switch
        {
            Operators.IsEqualTo => value == conditionValue,
            Operators.IsNotEqualTo => value != conditionValue,
            _ => false
        };

    private static bool AreNumbersEqual(double a, double b, double tolerance = 1e-10)
    {
        return Math.Abs(a - b) < tolerance;
    }

    private async Task<List<string>> GetRowValues(
        GoogleSheetsAuthentication authentication,
        List<TypedId> typedIds)
    {
        var (spreadsheetId, worksheetId, rowId) = GetSpreadsheetWorksheetAndRowIds(typedIds);
        var rowRange = await GetRowRange(authentication, typedIds);

        var sheetsService = CreateSheetsService(authentication);
        var worksheetValuesResponse = await ExecuteWithExponentialBackoff(async () =>
            await sheetsService.Spreadsheets.Values.Get(
                spreadsheetId,
                $"{worksheetId}!{rowRange.RangeStart + rowId}:{rowRange.RangeEnd + rowId}").ExecuteAsync());
        return worksheetValuesResponse.Values?.FirstOrDefault()?.Select(x => x.ToString()).ToList() ?? new List<string>();
    }

    private SheetsService CreateSheetsService(GoogleSheetsAuthentication authentication)
    {
        return new SheetsService(new BaseClientService.Initializer
        {
            HttpClientInitializer = GetCredential(authentication.AccessToken)
        });
    }

    private DriveService CreateDriveService(GoogleSheetsAuthentication authentication)
    {
        return new DriveService(new BaseClientService.Initializer
        {
            HttpClientInitializer = GetCredential(authentication.AccessToken)
        });
    }

    private string GetSpreadsheetId(List<TypedId> typedIds)
    {
        var spreadsheetId = typedIds.Find(i => i.Type == Constants.SpreadsheetType)?.Id;
        if (spreadsheetId is null)
        {
            throw new Exception("Missing spreadsheet id");
        }

        return spreadsheetId;
    }

    private (string SpreadsheetId, string WorksheetId) GetSpreadsheetAndWorksheetIds(List<TypedId> typedIds)
    {
        var spreadsheetId = typedIds.Find(i => i.Type == Constants.SpreadsheetType)?.Id;
        var worksheetId = typedIds.Find(i => i.Type == Constants.WorksheetType)?.Id;
        if (spreadsheetId is null || worksheetId is null)
        {
            throw new Exception("Missing spreadsheet or worksheet id");
        }

        return (spreadsheetId, worksheetId);
    }

    private (string SpreadsheetId, string WorksheetId, string RowId) GetSpreadsheetWorksheetAndRowIds(List<TypedId> typedIds)
    {
        var (spreadsheetId, worksheetId) = GetSpreadsheetAndWorksheetIds(typedIds);
        var rowId = typedIds.Find(i => i.Type == Constants.RowEntityTypeName)?.Id;
        if (rowId is null)
        {
            throw new Exception("Missing row id");
        }

        return (spreadsheetId, worksheetId, rowId);
    }

    private UserCredential GetCredential(string accessToken)
    {
        var tokenResponse = new TokenResponse
        {
            AccessToken = accessToken,
            ExpiresInSeconds = 3600,
            IssuedUtc = DateTime.UtcNow
        };

        var initializer = new GoogleAuthorizationCodeFlow.Initializer
        {
            Scopes = new[]
            {
                Constants.DriveScope,
                Constants.SpreadsheetsScope
            },
            ClientSecrets = new ClientSecrets
            {
                ClientId = _googleSheetsConfig.GoogleSheetsClientId,
                ClientSecret = _googleSheetsConfig.GoogleSheetsClientSecret
            }
        };

        var flow = new GoogleAuthorizationCodeFlow(initializer);
        return new UserCredential(flow, "user", tokenResponse);
    }

    private async Task<T> ExecuteWithExponentialBackoff<T>(
        Func<Task<T>> action,
        int maxRetries = 5,
        int initialDelayMs = 1000)
    {
        var retryCount = 0;
        var delayMs = initialDelayMs;

        while (true)
        {
            try
            {
                return await action();
            }
            catch (Google.GoogleApiException ex) when (
                ex.HttpStatusCode == System.Net.HttpStatusCode.TooManyRequests ||
                ex.HttpStatusCode == System.Net.HttpStatusCode.GatewayTimeout ||
                ex.HttpStatusCode == System.Net.HttpStatusCode.ServiceUnavailable)
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.GoogleSheetsApiTooManyRequestsErrorReceived);

                if (retryCount >= maxRetries)
                {
                    throw; // Re-throw the exception if max retries reached
                }

                await Task.Delay(delayMs);
                delayMs *= 2; // Exponential backoff
                retryCount++;
            }
            catch (TaskCanceledException)
            {
                // Likely a timeout exception
                if (retryCount >= maxRetries)
                {
                    throw; // Re-throw the exception if max retries reached
                }

                await Task.Delay(delayMs);
                delayMs *= 2; // Exponential backoff
                retryCount++;
            }
        }
    }
}