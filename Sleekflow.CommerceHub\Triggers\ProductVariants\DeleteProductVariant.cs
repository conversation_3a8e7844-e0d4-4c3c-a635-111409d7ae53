using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.ProductVariants;

[TriggerGroup(ControllerNames.ProductVariants)]
public class DeleteProductVariant
    : ITrigger<
        DeleteProductVariant.DeleteProductVariantInput,
        DeleteProductVariant.DeleteProductVariantOutput>
{
    private readonly IProductVariantService _productVariantService;

    public DeleteProductVariant(
        IProductVariantService productVariantService)
    {
        _productVariantService = productVariantService;
    }

    public class DeleteProductVariantInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteProductVariantInput(
            string id,
            string storeId,
            string sleekflowCompanyId,
            string productId,
            string sleekflowStaffId,
            List<string>? sleekflowTeamIds)
        {
            Id = id;
            StoreId = storeId;
            SleekflowCompanyId = sleekflowCompanyId;
            ProductId = productId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowTeamIds;
        }
    }

    public class DeleteProductVariantOutput
    {
    }

    public async Task<DeleteProductVariantOutput> F(DeleteProductVariantInput deleteProductVariantInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteProductVariantInput.SleekflowStaffId,
            deleteProductVariantInput.SleekflowStaffTeamIds);

        await _productVariantService.DeleteProductVariantAsync(
            deleteProductVariantInput.Id,
            deleteProductVariantInput.StoreId,
            deleteProductVariantInput.SleekflowCompanyId,
            deleteProductVariantInput.ProductId,
            sleekflowStaff);

        return new DeleteProductVariantOutput();
    }
}