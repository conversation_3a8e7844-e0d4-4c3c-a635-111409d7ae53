﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;

namespace Sleekflow.CrmHub.Models.Schemas;

public class SchemaAccessibilitySettings
{
    public const string PropertyNameCategory = "category";

    [JsonProperty(PropertyNameCategory)]
    public string Category { get; set; }

    [JsonConstructor]
    public SchemaAccessibilitySettings(string category)
    {
        Category = category;
    }

    public static SchemaAccessibilitySettings Default()
    {
        return new SchemaAccessibilitySettings(SchemaCategories.Custom);
    }
}