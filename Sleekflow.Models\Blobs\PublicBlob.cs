using Newtonsoft.Json;

namespace Sleekflow.Models.Blobs;

public class PublicBlob
{
    [JsonProperty("container_name")]
    public string? ContainerName { get; set; }

    [JsonProperty("blob_name")]
    public string? BlobName { get; set; }

    [JsonProperty("blob_id")]
    public string? BlobId { get; set; }

    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("expires_on")]
    public DateTimeOffset ExpiresOn { get; set; }

    [JsonProperty("content_type")]
    public string ContentType { get; set; }

    [JsonConstructor]
    public PublicBlob(
        string? containerName,
        string? blobName,
        string? blobId,
        string url,
        DateTimeOffset expiresOn,
        string contentType)
    {
        ContainerName = containerName;
        BlobName = blobName;
        BlobId = blobId;
        Url = url;
        ExpiresOn = expiresOn;
        ContentType = contentType;
    }
}