using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.FileDocumentChunk)]
public class FileDocumentChunk : Chunk
{
    public const string PropertyNameBlobId = "blob_id";
    public const string PropertyNameDocumentId = "document_id";
    public const string PropertyNameUploadedBy = "uploaded_by";

    [JsonProperty(PropertyNameBlobId)]
    public string BlobId { get; set; }

    [JsonProperty(PropertyNameDocumentId)]
    public string DocumentId { get; set; }

    [JsonConstructor]
    public FileDocumentChunk(
        string id,
        string sleekflowCompanyId,
        string blobId,
        string documentId,
        string content,
        string contentEn,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<Category> categories,
        Dictionary<string, object?> metadata)
        : base(
            id,
            SysTypeNames.FileDocumentChunk,
            sleekflowCompanyId,
            content,
            contentEn,
            createdAt,
            updatedAt,
            categories,
            metadata)
    {
        BlobId = blobId;
        DocumentId = documentId;
    }
}