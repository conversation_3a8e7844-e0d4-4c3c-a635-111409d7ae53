using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;

public class PostgreSqlConfig : IConfig, IPostgreSqlConfig
{
    public string ConnectionString { get; }
    public string DatabaseName { get; }
    public string SchemaName { get; }
    public int MaxConnections { get; }

    public PostgreSqlConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ConnectionString = Environment.GetEnvironmentVariable("POSTGRES_CONNECTION_STRING", target) ??
                          throw new SfMissingEnvironmentVariableException("POSTGRES_CONNECTION_STRING");

        DatabaseName = Environment.GetEnvironmentVariable("POSTGRES_DATABASE_NAME", target) ??
                      throw new SfMissingEnvironmentVariableException("POSTGRES_DATABASE_NAME");

        SchemaName = Environment.GetEnvironmentVariable("POSTGRES_SCHEMA_NAME", target) ?? "public";

        MaxConnections = int.TryParse(Environment.GetEnvironmentVariable("POSTGRES_MAX_CONNECTIONS", target), out var maxConn)
            ? maxConn : 10;
    }
}