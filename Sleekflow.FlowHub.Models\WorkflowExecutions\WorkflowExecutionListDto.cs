using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionListDto : EntityDto, IHasCreatedAt, IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    public StateIdentityDto StateIdentity { get; set; }

    [JsonProperty("workflow_execution_status")]
    public string WorkflowExecutionStatus { get; set; }

    [Json<PERSON>roperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public WorkflowExecutionListDto(
        string id,
        string sleekflowCompanyId,
        string stateId,
        StateIdentityDto stateIdentity,
        string workflowExecutionStatus,
        DateTimeOffset createdAt)
        : base(id)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        StateIdentity = stateIdentity;
        WorkflowExecutionStatus = workflowExecutionStatus;
        CreatedAt = createdAt;
    }

    public WorkflowExecutionListDto(
        WorkflowExecution workflowExecution)
        : this(
            workflowExecution.Id,
            workflowExecution.SleekflowCompanyId,
            workflowExecution.StateId,
            new StateIdentityDto(workflowExecution.StateIdentity),
            workflowExecution.WorkflowExecutionStatus,
            workflowExecution.CreatedAt)
    {
    }
}