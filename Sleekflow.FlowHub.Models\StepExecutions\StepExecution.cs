using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.StepExecutions;

[ContainerId(ContainerNames.StepExecution)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class StepExecution : Entity, IHasCreatedAt, IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    public StateIdentity StateIdentity { get; set; }

    [JsonProperty("step_id")]
    public string StepId { get; set; }

    [JsonProperty("step_node_id")]
    public string? StepNodeId { get; set; }

    [JsonProperty("step_execution_status")]
    public string StepExecutionStatus { get; set; }

    [JsonProperty("worker_instance_id")]
    public string? WorkerInstanceId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("error")]
    public UserFriendlyError? Error { get; set; }

    [JsonConstructor]
    public StepExecution(
        string id,
        int? ttl,
        string sleekflowCompanyId,
        string stateId,
        StateIdentity stateIdentity,
        string stepId,
        string? stepNodeId,
        string stepExecutionStatus,
        string? workerInstanceId,
        DateTimeOffset createdAt,
        UserFriendlyError? error)
        : base(id, SysTypeNames.StepExecution, ttl)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        StateIdentity = stateIdentity;
        StepId = stepId;
        StepNodeId = stepNodeId;
        StepExecutionStatus = stepExecutionStatus;
        WorkerInstanceId = workerInstanceId;
        CreatedAt = createdAt;
        Error = error;
    }
}