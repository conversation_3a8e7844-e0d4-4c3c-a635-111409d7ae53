using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Tests.Workflows.Steps;

public class StepLocatorTests
{
    private WorkflowStepLocator _workflowStepLocator;

    [SetUp]
    public void Setup()
    {
        _workflowStepLocator = new WorkflowStepLocator(new WorkflowStepEntryProvider());
    }

    [Test]
    public void GetStepEntries()
    {
        // Arrange
        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(
            """
{
    "workflow_id": "GAeUYVAq2qv42J2",
    "workflow_versioned_id": "GAeUYVAq2qv42J2-BbqcG50gLg1bLZn",
    "name": "Test Flow",
    "triggers": {
        "conversation_status_changed": null,
        "contact_created": null,
        "contact_label_relationships_changed": null,
        "contact_list_relationships_changed": null,
        "contact_updated": null,
        "message_received": {
            "condition": "{{ [\"85298696051\"] | array.contains event_body.channel_id }}"
        },
        "message_sent": null,
        "is_webhook_enabled": false
    },
    "steps": [
        {
            "id": "setup-contact-and-conversation",
            "name": "setup",
            "assign": {
                "contact": "{{ contact_id | sleekflow.get_contact }}",
                "lists": "{{ contact_id | sleekflow.get_contact_lists }}",
                "labels": "{{ contact_id | sleekflow.get_contact_labels }}",
                "conversation": "{{ contact_id | sleekflow.get_contact_conversation }}"
            },
            "next_step_id": null
        },
        {
            "call": "sleekflow.v1.send-message",
            "args": {
                "channel__expr": "whatsappcloudapi",
                "from_to": {
                    "from_phone_number__expr": "{{ \"601125868915\" }}",
                    "to_phone_number__expr": null,
                    "to_contact_id__expr": "{{ trigger_event_body.contact_id }}"
                },
                "message_type__expr": "interactive",
                "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"What would you like to enquire?\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"a8244b07-0382-4dda-86cc-a1a65ed4352d\",\"title\":\"Test add label\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"6cdebc1e-64f0-4429-acfa-5a834fe4b67d\",\"title\":\"Test assign to \"}},{\"type\":\"reply\",\"reply\":{\"id\":\"ea51ea43-7398-4ebb-a1e3-da05087dd07c\",\"title\":\"Test add collaborator\"}}]}}} }}"
            },
            "id": "15d07170-da44-4453-9446-d3de00e5e4ca",
            "name": "action 1",
            "assign": null,
            "next_step_id": "eb767bf4-e4e2-44b0-a3d0-181cf241f2d5"
        },
        {
            "call": "sys.wait-for-event",
            "args": {
                "event_name": "OnMessageReceived",
                "condition__expr": null,
                "timeout_seconds__expr": null
            },
            "id": "eb767bf4-e4e2-44b0-a3d0-181cf241f2d5",
            "name": "wait-for-on-message-received",
            "assign": null,
            "next_step_id": "85eff181-fd5d-41d6-a08d-7cfe65db5969"
        },
        {
            "switch": [
                {
                    "condition": "{{ event_body.message.message_body.text_message == \"Test add label\" }}",
                    "next_step_id": "12d9eb34-f68b-46fc-b285-05dbf80f8486"
                },
                {
                    "condition": "{{ event_body.message.message_body.text_message == \"Test assign to \" }}",
                    "next_step_id": "e2f7f42a-c8a2-41c7-9340-39f05fa69d42"
                },
                {
                    "condition": "{{ event_body.message.message_body.text_message == \"Test add collaborator\" }}",
                    "next_step_id": "dca53e24-5fe8-4917-82fa-54b27ed0417b"
                },
                {
                    "condition": "{{ true }}",
                    "next_step_id": "7e0c5c25-f4f9-47fa-b56e-1b13413d3c77"
                }
            ],
            "id": "85eff181-fd5d-41d6-a08d-7cfe65db5969",
            "name": "answer-switch",
            "assign": null,
            "next_step_id": null
        },
        {
            "call": "sleekflow.v1.update-contact-label-relationships",
            "args": {
                "contact_id__expr": "{{ event_body.contact_id }}",
                "add_labels__expr": "{{ [\"VIP\"] }}",
                "remove_labels__expr": null,
                "set_labels__expr": null
            },
            "id": "12d9eb34-f68b-46fc-b285-05dbf80f8486",
            "name": "action 2",
            "assign": null,
            "next_step_id": "1cdd4d35-e2df-45d0-86a7-6b5d08beadb7"
        },
        {
            "call": "sleekflow.v1.update-contact-owner-relationships",
            "args": {
                "contact_id__expr": "{{ sys_var_dict.event_body.contact_id }}",
                "staff_ids__expr": "{{ [\"b94c4492-7e67-4647-8672-ce8e14fae69f\"] }}",
                "team_ids__expr": null,
                "strategy": "RoundRobbin_StaffOnly"
            },
            "id": "e2f7f42a-c8a2-41c7-9340-39f05fa69d42",
            "name": "action 3",
            "assign": null,
            "next_step_id": "ef5d7322-f4d2-4de5-991d-227c3c447e5c"
        },
        {
            "call": "sleekflow.v1.update-contact-collaborator-relationships",
            "args": {
                "contact_id__expr": "{{ sys_var_dict.event_body.contact_id }}",
                "add_staff_ids__expr": "{{ [\"1af73e6e-5166-4860-b2c4-72b24c138f67\"] }}",
                "remove_staff_ids__expr": null,
                "set_staff_ids__expr": null
            },
            "id": "dca53e24-5fe8-4917-82fa-54b27ed0417b",
            "name": "action 5",
            "assign": null,
            "next_step_id": "ec12dead-5fa2-4a11-9539-f4c0e4e7052c"
        },
        {
            "id": "7e0c5c25-f4f9-47fa-b56e-1b13413d3c77",
            "name": "end",
            "assign": null,
            "next_step_id": "c0d80914-2ee9-4a8c-bc3a-21c85c0f3805"
        },
        {
            "id": "1cdd4d35-e2df-45d0-86a7-6b5d08beadb7",
            "name": "end",
            "assign": null,
            "next_step_id": "c0d80914-2ee9-4a8c-bc3a-21c85c0f3805"
        },
        {
            "id": "ef5d7322-f4d2-4de5-991d-227c3c447e5c",
            "name": "end",
            "assign": null,
            "next_step_id": "c0d80914-2ee9-4a8c-bc3a-21c85c0f3805"
        },
        {
            "id": "ec12dead-5fa2-4a11-9539-f4c0e4e7052c",
            "name": "end",
            "assign": null,
            "next_step_id": "c0d80914-2ee9-4a8c-bc3a-21c85c0f3805"
        },
        {
            "id": "c0d80914-2ee9-4a8c-bc3a-21c85c0f3805",
            "name": "end",
            "assign": null,
            "next_step_id": null
        }
    ],
    "activation_status": "Active",
    "metadata": {
        "nodes": "[{\"width\":300,\"height\":148,\"id\":\"54d9e06d-aa71-4385-9bca-1604b29644d9\",\"selected\":false,\"position\":{\"x\":0,\"y\":0},\"data\":{\"category\":\"start\",\"formValues\":{\"triggerType\":\"incoming-messages\",\"channels\":[{\"id\":\"85298696051\",\"name\":\"Testing\",\"channel\":\"whatsappcloudapi\",\"messagingHubWabaId\":\"n2UJWnaQQv4Byg\"}],\"conversationStatus\":\"\",\"webhookUrl\":\"\",\"saveWebhookDataAsVariable\":\"\",\"contactProperties\":[]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":true,\"isValidating\":false}},\"type\":\"start\",\"positionAbsolute\":{\"x\":0,\"y\":0}},{\"width\":300,\"height\":327,\"id\":\"15d07170-da44-4453-9446-d3de00e5e4ca\",\"selected\":false,\"position\":{\"x\":0,\"y\":450},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"\",\"defaultTitle\":\"action 1\",\"channelType\":\"SPECIFIC_CHANNEL\",\"channel\":\"601125868915\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"What would you like to enquire?\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"QUICK_REPLY\",\"buttonList\":[{\"defaultTitle\":\"Title 1\",\"title\":\"Test add label\",\"nodeId\":\"a8244b07-0382-4dda-86cc-a1a65ed4352d\",\"description\":\"\",\"readOnly\":false},{\"defaultTitle\":\"flow-builder.send-message.quick-reply.title 1\",\"title\":\"Test assign to \",\"nodeId\":\"6cdebc1e-64f0-4429-acfa-5a834fe4b67d\",\"description\":\"\",\"readOnly\":false},{\"defaultTitle\":\"flow-builder.send-message.quick-reply.title 1\",\"title\":\"Test add collaborator\",\"nodeId\":\"ea51ea43-7398-4ebb-a1e3-da05087dd07c\",\"description\":\"\",\"readOnly\":false}],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":true,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":true,\"isValidating\":false}},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":0,\"y\":450}},{\"width\":300,\"height\":42,\"id\":\"a8244b07-0382-4dda-86cc-a1a65ed4352d\",\"position\":{\"x\":-600,\"y\":900},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 1\",\"title\":\"Test add label\",\"nodeId\":\"a8244b07-0382-4dda-86cc-a1a65ed4352d\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":-600,\"y\":900}},{\"width\":300,\"height\":42,\"id\":\"6cdebc1e-64f0-4429-acfa-5a834fe4b67d\",\"position\":{\"x\":-200,\"y\":900},\"data\":{\"formValues\":{\"defaultTitle\":\"flow-builder.send-message.quick-reply.title 1\",\"title\":\"Test assign to \",\"nodeId\":\"6cdebc1e-64f0-4429-acfa-5a834fe4b67d\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":-200,\"y\":900}},{\"width\":300,\"height\":42,\"id\":\"ea51ea43-7398-4ebb-a1e3-da05087dd07c\",\"selected\":false,\"position\":{\"x\":200,\"y\":900},\"data\":{\"formValues\":{\"defaultTitle\":\"flow-builder.send-message.quick-reply.title 1\",\"title\":\"Test add collaborator\",\"nodeId\":\"ea51ea43-7398-4ebb-a1e3-da05087dd07c\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":200,\"y\":900}},{\"width\":300,\"height\":42,\"id\":\"fca35681-66ca-40f8-a62a-d4d7519c94c5\",\"position\":{\"x\":600,\"y\":900},\"data\":{\"formValues\":{\"defaultTitle\":\"\",\"title\":\"other-answers\"},\"category\":\"option\"},\"type\":\"elseAnswer\",\"positionAbsolute\":{\"x\":600,\"y\":900}},{\"width\":300,\"height\":184,\"id\":\"12d9eb34-f68b-46fc-b285-05dbf80f8486\",\"selected\":false,\"position\":{\"x\":-600,\"y\":1350},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"\",\"defaultTitle\":\"action 2\",\"actionType\":\"addLabel\",\"labels\":[{\"id\":\"5388803a-90b6-4ad9-be60-738bf693402e\",\"hashtag\":\"VIP\",\"hashTagColor\":\"Blue\",\"count\":0,\"hashTagType\":\"Normal\",\"label\":\"VIP\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false}},\"type\":\"addLabel\",\"positionAbsolute\":{\"x\":-600,\"y\":1350}},{\"width\":300,\"height\":172,\"id\":\"e2f7f42a-c8a2-41c7-9340-39f05fa69d42\",\"selected\":false,\"position\":{\"x\":-200,\"y\":1350},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"\",\"defaultTitle\":\"action 3\",\"actionType\":\"assignTo\",\"assignTo\":\"specificUser\",\"assignees\":[{\"id\":\"b94c4492-7e67-4647-8672-ce8e14fae69f\",\"name\":\"Henson Tsai\",\"avatar\":\"https://sleekflow-prod-api-uat-auth0.azurewebsites.net/comapny/profilepicture/d343bd46-0949-456f-97b1-a881d3ba608e\"}],\"assignLogic\":\"\",\"team\":{\"id\":\"\",\"teamName\":\"\"}},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false}},\"type\":\"assignTo\",\"positionAbsolute\":{\"x\":-200,\"y\":1350}},{\"width\":300,\"height\":188,\"id\":\"dca53e24-5fe8-4917-82fa-54b27ed0417b\",\"selected\":false,\"position\":{\"x\":200,\"y\":1350},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"\",\"defaultTitle\":\"action 5\",\"actionType\":\"addCollaborator\",\"collaborators\":[{\"id\":\"1af73e6e-5166-4860-b2c4-72b24c138f67\",\"name\":\"Billy Chu Chu\",\"avatar\":\"https://sleekflow-prod-api-uat-auth0.azurewebsites.net/comapny/profilepicture/846ecea0-8a81-499d-91f7-cfbd5895d93f\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false}},\"type\":\"addCollaborator\",\"positionAbsolute\":{\"x\":200,\"y\":1350}},{\"width\":300,\"height\":116,\"id\":\"7e0c5c25-f4f9-47fa-b56e-1b13413d3c77\",\"position\":{\"x\":600,\"y\":1350},\"data\":{\"formValues\":{\"endType\":\"here\"},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":600,\"y\":1350}},{\"width\":300,\"height\":116,\"id\":\"1cdd4d35-e2df-45d0-86a7-6b5d08beadb7\",\"position\":{\"x\":-600,\"y\":1800},\"data\":{\"category\":\"end\",\"formValues\":{\"endType\":\"here\"}},\"type\":\"end\",\"positionAbsolute\":{\"x\":-600,\"y\":1800}},{\"width\":300,\"height\":116,\"id\":\"ef5d7322-f4d2-4de5-991d-227c3c447e5c\",\"position\":{\"x\":-200,\"y\":1800},\"data\":{\"formValues\":{\"endType\":\"here\"},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-200,\"y\":1800}},{\"width\":300,\"height\":116,\"id\":\"ec12dead-5fa2-4a11-9539-f4c0e4e7052c\",\"position\":{\"x\":200,\"y\":1800},\"data\":{\"formValues\":{\"endType\":\"here\"},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":200,\"y\":1800}}]",
        "edges": "[{\"type\":\"buttonEdge\",\"id\":\"0bac7adb-f65f-408f-af25-b443d4ea07fd\",\"source\":\"54d9e06d-aa71-4385-9bca-1604b29644d9\",\"target\":\"15d07170-da44-4453-9446-d3de00e5e4ca\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"9ffc732d-59b6-4117-b2bc-e96e0f0d189d\",\"source\":\"15d07170-da44-4453-9446-d3de00e5e4ca\",\"target\":\"a8244b07-0382-4dda-86cc-a1a65ed4352d\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"54cf8c8d-b0c0-48a2-8d3a-725e7a18c01e\",\"source\":\"15d07170-da44-4453-9446-d3de00e5e4ca\",\"target\":\"fca35681-66ca-40f8-a62a-d4d7519c94c5\"},{\"type\":\"buttonEdge\",\"id\":\"a77e3f9f-5964-40dd-b0cd-1c985cb17b9c\",\"source\":\"fca35681-66ca-40f8-a62a-d4d7519c94c5\",\"target\":\"7e0c5c25-f4f9-47fa-b56e-1b13413d3c77\"},{\"type\":\"buttonEdge\",\"id\":\"f49be7cb-b2ac-4129-951e-f0b564c207f1\",\"source\":\"15d07170-da44-4453-9446-d3de00e5e4ca\",\"target\":\"6cdebc1e-64f0-4429-acfa-5a834fe4b67d\"},{\"type\":\"buttonEdge\",\"id\":\"bd60e27e-aade-4993-a1e5-8d1bb12d90fe\",\"source\":\"15d07170-da44-4453-9446-d3de00e5e4ca\",\"target\":\"ea51ea43-7398-4ebb-a1e3-da05087dd07c\"},{\"type\":\"buttonEdge\",\"id\":\"829e77d7-207a-4fc9-b997-0f7c1378540a\",\"source\":\"a8244b07-0382-4dda-86cc-a1a65ed4352d\",\"target\":\"12d9eb34-f68b-46fc-b285-05dbf80f8486\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"74e2d391-e020-4091-aa6b-c4f18988960f\",\"source\":\"12d9eb34-f68b-46fc-b285-05dbf80f8486\",\"target\":\"1cdd4d35-e2df-45d0-86a7-6b5d08beadb7\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"9f780fbc-1709-4316-8ba3-43666a72609a\",\"source\":\"6cdebc1e-64f0-4429-acfa-5a834fe4b67d\",\"target\":\"e2f7f42a-c8a2-41c7-9340-39f05fa69d42\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"f3ddbef1-9a92-43b9-b5ca-a2d7643feb21\",\"source\":\"e2f7f42a-c8a2-41c7-9340-39f05fa69d42\",\"target\":\"ef5d7322-f4d2-4de5-991d-227c3c447e5c\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"501f5c66-95e8-483d-b6ca-ec83045988bf\",\"source\":\"ea51ea43-7398-4ebb-a1e3-da05087dd07c\",\"target\":\"dca53e24-5fe8-4917-82fa-54b27ed0417b\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"8d0522d4-3b1a-4402-98f7-35f5473793eb\",\"source\":\"dca53e24-5fe8-4917-82fa-54b27ed0417b\",\"target\":\"ec12dead-5fa2-4a11-9539-f4c0e4e7052c\",\"selected\":false}]"
    },
    "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
    "created_by": {
        "sleekflow_staff_id": "4216",
        "sleekflow_staff_team_ids": []
    },
    "updated_by": null,
    "created_at": "2023-06-17T03:52:28.048Z",
    "updated_at": "2023-06-17T03:52:45.963Z",
    "id": "GAeUYVAq2qv42J2-BbqcG50gLg1bLZn",
    "sys_type_name": "Workflow",
    "_rid": "A0BhAKWmEYCbAgAAAAAAAA==",
    "_self": "dbs/A0BhAA==/colls/A0BhAKWmEYA=/docs/A0BhAKWmEYCbAgAAAAAAAA==/",
    "_etag": "\"0300ca5c-0000-1900-0000-648d2e0d0000\"",
    "_attachments": "attachments/",
    "_ts": 1686973965
}
""")!;

        // Act
        var result = _workflowStepLocator.GetStep(workflow, "setup-contact-and-conversation");

        // Assert
        Assert.That(result.Id, Is.EqualTo("setup-contact-and-conversation"));
    }

    [Test]
    public void GetStep_ReturnsCorrectStepById()
    {
        // Arrange
        var workflow = new Workflow(
            "my-workflow-1",
            "my-workflow-1-1",
            "My Workflow 1",
            WorkflowType.Normal,
            workflowGroupId: null,
            new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            new List<Step>
            {
                new SimpleStep("step1", "Step 1", null, null),
                new SimpleStep("step2", "Step 2", null, null),
                new SimpleStep("step3", "Step 3", null, null),
            },
            WorkflowActivationStatuses.Active,
            "my-workflow-1-1",
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            "my-company-id-1",
            null,
            null,
            new Dictionary<string, object?>(),
            "v1",
            null);

        var proxyWorkflow = new ProxyWorkflow(
            workflow,
            workflow.Steps,
            workflow.Metadata);

        // Act
        var result = _workflowStepLocator.GetStep(proxyWorkflow, "step2");

        // Assert
        Assert.That(result.Id, Is.EqualTo("step2"));
    }

    [Test]
    public void GetNextStep_ReturnsCorrectNextStep()
    {
        // Arrange
        var workflow = new Workflow(
            "my-workflow-1",
            "my-workflow-1-1",
            "My Workflow 1",
            WorkflowType.Normal,
            workflowGroupId: null,
            new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            new List<Step>
            {
                new SimpleStep("step1", "Step 1", null, null),
                new SimpleStep("step2", "Step 2", null, null),
                new SimpleStep("step3", "Step 3", null, null),
            },
            WorkflowActivationStatuses.Active,
            "my-workflow-1-1",
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            "my-company-id-1",
            null,
            null,
            new Dictionary<string, object?>(),
            "v1",
            null);
        var currentStep = workflow.Steps.First(s => s.Id == "step1");

        var proxyWorkflow = new ProxyWorkflow(
            workflow,
            workflow.Steps,
            workflow.Metadata);

        // Act
        var result = _workflowStepLocator.GetNextStep(proxyWorkflow, currentStep, new Stack<StackEntry>());

        // Assert
        Assert.That(result!.Id, Is.EqualTo("step2"));
    }

    [Test]
    public void GetNextStep_ReturnsCorrectNextStep_Nested()
    {
        // Arrange
        var workflow = new Workflow(
            "my-workflow-1",
            "my-workflow-1-1",
            "My Workflow 1",
            WorkflowType.Normal,
            workflowGroupId: null,
            new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            new List<Step>
            {
                new SimpleStep("step1", "Step 1", null, null),
                new ParallelStep(
                    "step2",
                    "Step 2",
                    null,
                    null,
                    new List<ParallelStepBranch>
                    {
                        new ParallelStepBranch(
                            new SubFlowStep(
                                "step2.1",
                                "Step 2.1",
                                null,
                                null,
                                new List<Step>
                                {
                                    new SimpleStep("step2.1.1", "Step 2.1.1", null, null),
                                    new SimpleStep("step2.1.2", "Step 2.1.2", null, null),
                                })),
                        new ParallelStepBranch(
                            new SubFlowStep(
                                "step2.2",
                                "Step 2.2",
                                null,
                                null,
                                new List<Step>
                                {
                                    new SimpleStep("step2.2.1", "Step 2.2.1", null, null),
                                    new SimpleStep("step2.2.2", "Step 2.2.2", null, null),
                                }))
                    }),
                new SimpleStep("step3", "Step 3", null, null),
                new TryCatchStep(
                    "step4",
                    "Step 4",
                    null,
                    null,
                    new TryCatchStepTry(
                        new SubFlowStep(
                            "step4.1",
                            "Step 4.1",
                            null,
                            null,
                            new List<Step>
                            {
                                new SimpleStep("step4.1.1", "Step 4.1.1", null, null),
                                new ThrowStep("step4.1.2", "Step 4.1.2", null, null, 100, "message"),
                            })),
                    new TryCatchStepCatch(
                        "exception",
                        new SubFlowStep(
                            "step4.2",
                            "Step 4.2",
                            null,
                            null,
                            new List<Step>
                            {
                                new SimpleStep("step4.2.1", "Step 4.2.1", null, null),
                                new SimpleStep("step4.2.2", "Step 4.2.2", null, null),
                            }))),
                new SubFlowStep(
                    "step5",
                    "Step 5",
                    null,
                    "step6",
                    new List<Step>
                    {
                        new SimpleStep("step5.1", "Step 5.1", null, null),
                        new SimpleStep("step5.2", "Step 5.2", null, null),
                    }),
                new SimpleStep("step6", "Step 6", null, null),
            },
            WorkflowActivationStatuses.Active,
            "my-workflow-1-1",
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            "my-company-id-1",
            null,
            null,
            new Dictionary<string, object?>(),
            "v1",
            null);

        var asserts = new List<(string Source, string? Target)>
        {
            ("step1", "step2"),
            ("step2.1.1", "step2.1.2"),
            ("step2.1.2", null),
            ("step2.2.1", "step2.2.2"),
            ("step2.2.2", null),
            ("step2", "step3"),
            ("step3", "step4"),
            ("step4.1.1", "step4.1.2"),
            ("step4.1.2", null),
            ("step4.2.1", "step4.2.2"),
            ("step4.2.2", null),
            ("step4", "step5"),
            ("step5", "step6"),
            ("step5.1", "step5.2"),
            ("step5.2", null),
            ("step6", null)
        };

        foreach (var assert in asserts)
        {
            var proxyWorkflow = new ProxyWorkflow(
                workflow,
                workflow.Steps,
                workflow.Metadata);

            // Act
            var result = _workflowStepLocator.GetNextStep(proxyWorkflow, assert.Source, new Stack<StackEntry>());

            // Assert
            Assert.That(result?.Id, Is.EqualTo(assert.Target));
        }
    }

    [Test]
    public void GetNextStep_ReturnsNullWhenNoNextStepExists()
    {
        // Arrange
        var workflow = new Workflow(
            "my-workflow-1",
            "my-workflow-1-1",
            "My Workflow 1",
            WorkflowType.Normal,
            workflowGroupId: null,
            new WorkflowTriggers(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            new List<Step>
            {
                new SimpleStep("step1", "Step 1", null, null),
                new SimpleStep("step2", "Step 2", null, null),
                new SimpleStep("step3", "Step 3", null, null),
            },
            WorkflowActivationStatuses.Active,
            "my-workflow-1-1",
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
            "my-company-id-1",
            null,
            null,
            new Dictionary<string, object?>(),
            "v1",
            null);
        var currentStep = workflow.Steps.First(s => s.Id == "step3");

        var proxyWorkflow = new ProxyWorkflow(
            workflow,
            workflow.Steps,
            workflow.Metadata);

        // Act
        var result = _workflowStepLocator.GetNextStep(proxyWorkflow, currentStep, new Stack<StackEntry>());

        // Assert
        Assert.That(result, Is.Null);
    }
}