using Sleekflow.DependencyInjection;
using Sleekflow.**********************.Models.NetSuite.Integrations;
using Sleekflow.**********************.NetSuite;
using Sleekflow.**********************.TravisBackend;
using Sleekflow.**********************.Utils;

namespace Sleekflow.**********************.Integrations.SyncCompanyToNetSuiteIntegration;

public interface ISyncCompanyToNetSuiteIntegration : IExternalIntegration
{
}

public class SyncCompanyToNetSuiteIntegration : ISyncCompanyToNetSuiteIntegration, IScopedService
{
    private readonly INetSuiteSettingService _netSuiteSettingService;
    private readonly INetSuiteEmployeeService _netSuiteEmployeeService;
    private readonly INetSuiteCustomerService _netSuiteCustomerService;
    private readonly ITravisBackendService _travisBackendService;
    private readonly ILogger<SyncCompanyToNetSuiteIntegration> _logger;

    public SyncCompanyToNetSuiteIntegration(
        INetSuiteSettingService netSuiteSettingService,
        INetSuiteEmployeeService netSuiteEmployeeService,
        INetSuiteCustomerService netSuiteCustomerService,
        ITravisBackendService travisBackendService,
        ILogger<SyncCompanyToNetSuiteIntegration> logger)
    {
        _netSuiteSettingService = netSuiteSettingService;
        _netSuiteEmployeeService = netSuiteEmployeeService;
        _netSuiteCustomerService = netSuiteCustomerService;
        _travisBackendService = travisBackendService;
        _logger = logger;
    }

    public async Task Run(CancellationToken cancellationToken)
    {
        var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
            await _netSuiteSettingService.NetSuitePreparation(
                cancellationToken,
                showEmployee: false,
                showTerm: false);

        var companies = await _travisBackendService.GetAllCompanies();

        foreach (var company in companies)
        {
            var companyDetails = company.Company;
            var companyOwner = company.CompanyOwner;
            var salesRep = company.SalesRep;

            // create subsidiary
            var subsidiary = NetSuiteUtils.LocationConverter(companyDetails.CompanyCountry, subsidiaryDetails);

            var salesRepId = salesRep == null
                ? null
                : await _netSuiteEmployeeService.GetEmployeeIdByEmailAsync(salesRep.Email);

            // main currency is following the subsidiary
            // additional currency is hard coded to USD
            var currency = NetSuiteUtils.CurrencyConverter(companyDetails.CompanyCountry, currencyDetails);

            // create request
            var request = new CreateCustomerRequest(
                companyDetails.Id,
                companyDetails.CompanyName,
                new CreateCustomerRequestCommonId(subsidiary),
                companyOwner?.Email,
                companyOwner?.PhoneNumber,
                null,
                new CreateCustomerRequestCommonId(currency),
                currency == "1"
                    ? null
                    : new CurrencyList(
                    [
                        new CurrencyListElement("1")
                    ]),
                salesRepId == null ? null : new CreateCustomerRequestCommonId(salesRepId));
            var isSuccess = await _netSuiteCustomerService.CreateCustomerAsync(request);
            if (isSuccess)
            {
                _logger.LogInformation("Successfully created customer: {CompanyName}", companyDetails.CompanyName);
            }
            else
            {
                _logger.LogError("Failed to create customer: {CompanyName}", companyDetails.CompanyName);
            }
        }
    }
}