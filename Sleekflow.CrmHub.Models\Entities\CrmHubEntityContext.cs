﻿using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.Entities;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("entity")]
public class CrmHubEntityContext : IHasETag
{
    public const string PropertyNameId = "id";
    public const string PropertyNameSysEntityTypeName = "sys_entity_type_name";
    public const string PropertyNameSysSleekflowCompanyId = "sys_sleekflow_company_id";
    public const string PropertyNameSysTypeName = "sys_type_name";
    public const string PropertyNameCtxPhoneNumber = "ctx_phone_number";
    public const string PropertyNameCtxEmail = "ctx_email";
    public const string PropertyNameCtxExternalId = "ctx_external_id";
    public const string PropertyNameCtxExternalIds = "ctx_external_ids";
    public const string PropertyNameETag = "_etag";

    public const string QuerySelectStr =
        $"e.{PropertyNameId}, e.{PropertyNameSysEntityTypeName}, e.{PropertyNameSysSleekflowCompanyId}, e.{PropertyNameSysTypeName}, e.{PropertyNameCtxPhoneNumber}, e.{PropertyNameCtxEmail}, e.{PropertyNameCtxExternalId}, e.{PropertyNameCtxExternalIds}, e.{PropertyNameETag}";

    [JsonProperty(PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty(PropertyNameSysEntityTypeName)]
    public string SysEntityTypeName { get; set; }

    [JsonProperty(PropertyNameSysSleekflowCompanyId)]
    public string SysSleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameSysTypeName)]
    public string SysTypeName { get; set; }

    [JsonProperty(PropertyNameCtxPhoneNumber)]
    public string? CtxPhoneNumber { get; set; }

    [JsonProperty(PropertyNameCtxEmail)]
    public string? CtxEmail { get; set; }

    /// <summary>
    /// This stores the object id of the provider,
    /// if this CrmHubEntityContext is mapped to only one object in the provider.
    ///
    /// Either "CtxExternalId" or "CtxExternalIds" in one CrmHubEntityContext.
    ///
    /// e.g. "sleekflow:44acdaf9-302c-4e55-97f6-12eabef998f2".
    /// </summary>
    [JsonProperty(PropertyNameCtxExternalId)]
    public string? CtxExternalId { get; set; }

    /// <summary>
    /// This stores the merged ids of the objects of the provider.
    ///
    /// Either "CtxExternalId" or "CtxExternalIds" in one CrmHubEntityContext.
    ///
    /// e.g. ["sleekflow:44acdaf9-302c-4e55-97f6-12eabef998f2", "sleekflow:..."].
    /// </summary>
    [JsonProperty(PropertyNameCtxExternalIds)]
    public List<string>? CtxExternalIds { get; set; }

    [JsonProperty(PropertyName = PropertyNameETag, NullValueHandling = NullValueHandling.Ignore)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public CrmHubEntityContext(
        string id,
        string sysEntityTypeName,
        string sysSleekflowCompanyId,
        string? ctxPhoneNumber,
        string? ctxEmail,
        string? ctxExternalId,
        List<string>? ctxExternalIds,
        string sysTypeName,
        string? eTag)
    {
        Id = id;
        SysEntityTypeName = sysEntityTypeName;
        SysSleekflowCompanyId = sysSleekflowCompanyId;
        CtxPhoneNumber = ctxPhoneNumber;
        CtxEmail = ctxEmail;
        CtxExternalId = ctxExternalId;
        CtxExternalIds = ctxExternalIds;
        SysTypeName = sysTypeName;
        ETag = eTag;
    }

    public static CrmHubEntityContext GetExternalEntityContext(
        string id,
        string sysEntityTypeName,
        string sysSleekflowCompanyId,
        string ctxExternalId)
    {
        return new CrmHubEntityContext(
            id,
            sysEntityTypeName,
            sysSleekflowCompanyId,
            null,
            null,
            ctxExternalId,
            new List<string>(),
            "Entity",
            null);
    }

    public static CrmHubEntityContext GetMergedEntityContext(
        string id,
        string sysEntityTypeName,
        string sysSleekflowCompanyId,
        string ctxExternalId,
        string? ctxPhoneNumber,
        string? ctxEmail)
    {
        return new CrmHubEntityContext(
            id,
            sysEntityTypeName,
            sysSleekflowCompanyId,
            ctxPhoneNumber,
            ctxEmail,
            null,
            new List<string>
            {
                ctxExternalId
            },
            "Entity",
            null);
    }
}