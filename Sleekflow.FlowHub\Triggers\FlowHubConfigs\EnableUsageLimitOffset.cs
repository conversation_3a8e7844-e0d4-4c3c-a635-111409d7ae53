using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class EnableUsageLimitOffset : ITrigger<EnableUsageLimitOffset.EnableUsageLimitOffsetInput, EnableUsageLimitOffset.EnableUsageLimitOffsetOutput>
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public EnableUsageLimitOffset(IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    public class EnableUsageLimitOffsetInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("is_enable_usage_limit_offset")]
        [Required]
        public bool IsEnableUsageLimitOffset { get; set; }

        [JsonConstructor]
        public EnableUsageLimitOffsetInput(string sleekflowCompanyId, bool isEnableUsageLimitOffset)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            IsEnableUsageLimitOffset = isEnableUsageLimitOffset;
        }
    }

    public class EnableUsageLimitOffsetOutput
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; }

        [JsonConstructor]
        public EnableUsageLimitOffsetOutput(FlowHubConfig flowHubConfig)
        {
            FlowHubConfig = flowHubConfig;
        }
    }

    /// <inheritdoc />
    public async Task<EnableUsageLimitOffsetOutput> F(EnableUsageLimitOffsetInput input)
    {
        var flowHubConfig = await _flowHubConfigService.EnableUsageLimitOffsetAsync(input.SleekflowCompanyId, input.IsEnableUsageLimitOffset);
        return new EnableUsageLimitOffsetOutput(flowHubConfig);
    }
}