﻿namespace Sleekflow.Exceptions.Hubspot;

public class SfNotSupportedOperationException : ErrorCodeException
{
    public string EntityTypeName { get; }

    public SfNotSupportedOperationException(string entityTypeName)
        : base(
            ErrorCodeConstant.SfNotSupportedOperationException,
            $"The type is not supported. entityTypeName=[{entityTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "entityTypeName", entityTypeName
                }
            })
    {
        EntityTypeName = entityTypeName;
    }
}