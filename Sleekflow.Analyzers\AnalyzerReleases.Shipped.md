; Shipped analyzer releases
; https://github.com/dotnet/roslyn-analyzers/blob/master/src/Microsoft.CodeAnalysis.Analyzers/ReleaseTrackingAnalyzers.Help.md

## Release 1.0

### New Rules

Rule ID | Category | Severity | Notes
--------|----------|----------|--------------------
JA1001  | Usage    |  Warning | Property should have JsonProperty attribute
JA1002  | Usage    |  Warning | Constructor should have JsonConstructor attribute
SF1001  | Design   |  Warning | When a class has a Metadata field, it should implement the IHasMetadata interface.
SF1002  | Usage    |  Warning | Injection of IRepository implementation detected in class implementing ITrigger interface.
SF1003  | Design   |  Warning | If a class has a CreatedAt property, it should implement the IHasCreatedAt interface.
SF1004  | Design   |  Warning | If a class has a SleekflowCompanyId property, it should implement the IHasSleekflowCompanyId interface.
SF1005  | Design   |  Warning | If a class has a SleekflowUserProfileId property, it should implement the IHasSleekflowUserProfileId interface.
SF1006  | Design   |  Warning | If a class has a UpdatedAt property, it should implement the IHasUpdatedAt interface.
SF1101  | Design   |  Warning | Numeric properties in Input classes should have [Range] attribute.
SF1102  | Design   |  Warning | Non-nullable properties in Input classes should have [Required] attribute.
SF1103  | Design   |  Warning | Collection properties in Input classes should have [ValidateArray] attribute.
SF1104  | Design   |  Warning | Object properties in Input/Output classes should have [ValidateObject] attribute
