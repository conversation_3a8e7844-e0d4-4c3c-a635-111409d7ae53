using System.Text;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

public class CsvDocumentSplitter : IDocumentSplitter
{
    public Task<List<(Stream Stream, int StartPage, int EndPage)>> SplitDocumentIntoChunksAsync(
        Stream stream,
        int numberOfContentPerFile)
    {
        var outputStreams = new List<(Stream Stream, int StartPage, int EndPage)>();
        var reader = new StreamReader(stream);

        var firstLine = reader.ReadLine();

        while (!reader.EndOfStream)
        {
            var chunkStringBuilder = new StringBuilder();

            chunkStringBuilder.AppendLine(firstLine);
            for (var i = 0; i < numberOfContentPerFile; i++)
            {
                var line = reader.ReadLine();
                if (line == null)
                {
                    break;
                }

                chunkStringBuilder.AppendLine(line);
            }

            Stream currentStream = new MemoryStream();
            var buffer = Encoding.UTF8.GetBytes(chunkStringBuilder.ToString());
            currentStream.Write(buffer, 0, buffer.Length);

            // Should not dispose this stream.
            // To be returned to the caller.
            currentStream.Position = 0;

            outputStreams.Add((currentStream, 0, 0));
        }

        return Task.FromResult(outputStreams);
    }
}