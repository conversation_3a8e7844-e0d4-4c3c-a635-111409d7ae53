﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Blobs;
using Sleekflow.MessagingHub.Models.Constants;

namespace Sleekflow.MessagingHub.Blobs;

public interface IBlobService
{
    public GetBlobSasUrlOutput CreateBlobSasUrl(string storageType);

    public GetBlobSasUrlOutput GetBlobSasUrl(string blobId, string storageType);

    public Task<Response<BlobDownloadResult>> GetBlobDetailAsync(string blobId, string storageType);

    public record struct GetBlobSasUrlOutput(string BlobId, string Url, DateTimeOffset ExpiresOn);

    Task<string> UploadUrlToBlobAsync(string url, bool isTtlEnabled = true);

    Task<string> UploadUrlToBlobAsync(HttpRequestMessage httpRequestMessage, bool isTtlEnabled = true);
}

public class BlobService : IBlobService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly HttpClient _httpClient;
    private readonly IStorageConfig _storageConfig;

    public BlobService(
        IIdService idService,
        IStorageConfig storageConfig,
        IHttpClientFactory httpClientFactory)
    {
        _idService = idService;
        _storageConfig = storageConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public IBlobService.GetBlobSasUrlOutput CreateBlobSasUrl(string storageType)
    {
        var blobId = _idService.GetId(SysTypeNames.Blob);
        return GenerateBlobSasUrlOutput(blobId, GetBlobClient(blobId, storageType), BlobSasPermissions.Create);
    }

    public IBlobService.GetBlobSasUrlOutput GetBlobSasUrl(string blobId, string storageType)
    {
        return GenerateBlobSasUrlOutput(blobId, GetBlobClient(blobId, storageType), BlobSasPermissions.Read);
    }

    public async Task<Response<BlobDownloadResult>> GetBlobDetailAsync(string blobId, string storageType)
    {
        var blobClient = GetBlobClient(blobId, storageType);
        if (!await blobClient.ExistsAsync())
        {
            throw new SfNotFoundObjectException("Blob content not found");
        }

        return await blobClient.DownloadContentAsync();
    }

    public Task<string> UploadUrlToBlobAsync(string url, bool isTtlEnabled = true)
    {
        return UploadUrlToBlobAsync(new HttpRequestMessage(HttpMethod.Get, url), isTtlEnabled);
    }

    public async Task<string> UploadUrlToBlobAsync(HttpRequestMessage httpRequestMessage, bool isTtlEnabled = true)
    {
        var blobId = _idService.GetId(SysTypeNames.Blob);

        var blobServiceClient = new BlobServiceClient(_storageConfig.InternalStorageConnStr);
        var blobContainerClient = blobServiceClient.GetBlobContainerClient("internal-container");
        var blobClient = blobContainerClient.GetBlobClient(blobId);

        var resMsg = await _httpClient.SendAsync(httpRequestMessage);

        await using var stream = await resMsg.Content.ReadAsStreamAsync();
        await using var bufferedStream = new BufferedStream(stream, 1024 * 64);

        await blobClient.UploadAsync(bufferedStream, true);
        await blobClient.SetMetadataAsync(
            new Dictionary<string, string>
            {
                {
                    "IsTtlEnabled", string.Empty + isTtlEnabled
                }
            });

        var contentTypeHeaderValue = resMsg.Content.Headers.ContentType;
        if (contentTypeHeaderValue != null)
        {
            var contentType = contentTypeHeaderValue.ToString();

            await blobClient.SetHttpHeadersAsync(
                new BlobHttpHeaders()
                {
                    ContentType = contentType
                });
        }

        return blobId;
    }

    private BlobClient GetBlobClient(string blobId, string storageType)
    {
        var (storageConnStr, containerName) = GetStorageInformation(storageType);
        var blobServiceClient = new BlobServiceClient(storageConnStr);
        var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName);
        return blobContainerClient.GetBlobClient(blobId);
    }

    private (string StorageConnStr, string ContainerName) GetStorageInformation(string storageType)
    {
        return storageType switch
        {
            BlobStorageType.Internal => (_storageConfig.InternalStorageConnStr, "internal-container"),
            BlobStorageType.External => (
                _storageConfig.ExternalStorageConnStr,
                _storageConfig.ExternalStorageContainerName),
            _ => throw new SfInternalErrorException("Blob storage information")
        };
    }

    private static IBlobService.GetBlobSasUrlOutput GenerateBlobSasUrlOutput(
        string blobId,
        BlobBaseClient blobClient,
        BlobSasPermissions permissions)
    {
        var expiresOn = DateTimeOffset.UtcNow.AddDays(7);
        return new IBlobService.GetBlobSasUrlOutput(
            blobId,
            blobClient.GenerateSasUri(permissions, expiresOn).ToString(),
            expiresOn);
    }
}