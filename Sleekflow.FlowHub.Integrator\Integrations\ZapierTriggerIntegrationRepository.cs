﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Integrations;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Integrator.Integrations;

public interface IZapierTriggerIntegrationRepository : IIntegrationRepository<ZapierTriggerIntegration>
{
}

public class ZapierTriggerIntegrationRepository
    : BaseRepository<ZapierTriggerIntegration>, IZapierTriggerIntegrationRepository, IScopedService
{
    public ZapierTriggerIntegrationRepository(
        ILogger<ZapierTriggerIntegrationRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}