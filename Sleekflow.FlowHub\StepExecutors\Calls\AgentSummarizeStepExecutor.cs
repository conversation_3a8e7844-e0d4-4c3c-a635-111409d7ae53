using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Internals.ChatHistory;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Contacts;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IAgentSummarizeStepExecutor : IStepExecutor
{
}

public class AgentSummarizeStepExecutor
    : GeneralStepExecutor<CallStep<AgentSummarizeStepArgs>>, IAgentSummarizeStepExecutor, IScopedService
{
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IAgentConfigService _agentConfigService;
    private readonly IStateService _stateService;
    private readonly IChatHistoryService _chatHistoryService;
    private readonly ILogger _logger;

    public AgentSummarizeStepExecutor(
        IServiceBusManager serviceBusManager,
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator,
        IServiceProvider serviceProvider,
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IAgentConfigService agentConfigService,
        IStateService stateService,
        IChatHistoryService chatHistoryService,
        ILogger<AgentSummarizeStepExecutor> logger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _serviceBusManager = serviceBusManager;
        _coreCommander = coreCommander;
        _stateEvaluator = stateEvaluator;
        _agentConfigService = agentConfigService;
        _stateService = stateService;
        _chatHistoryService = chatHistoryService;
        _logger = logger;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);
        try
        {
            var contactId = await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.ContactIdExpr);

            var handoverReason =
                await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.HandoverReasonExpr);

            var config = await _agentConfigService.GetAsync(
                state,
                callStep.Args.CompanyAgentConfigIdExpr);
            var runningStates = await _stateService.GetRunningStatesAsync(
                                       state.Identity.ObjectId,
                                       state.Identity.ObjectType,
                                       state.Identity.SleekflowCompanyId,
                                       state.TriggerEventBody);
            var channelId = StateUtils.GetChannelIdFromParentState(runningStates, state.WorkflowContext.SnapshottedWorkflow.WorkflowId, _logger);

            // Obtain the conversation information from SleekflowCore
            var conversationMessages = await _chatHistoryService.GetConversationMessagesAsync(state, contactId, config, callStep, channelId);

            // Obtain the prompt context from the conversation messages
            var (contactProperties, languageIsoCode) = await GetPromptContextAsync(state, contactId, config, callStep);

            // Delay the completion until the agent has summarized the conversation - refer to OnAgentCompleteStepActivationEvent
            await _serviceBusManager.PublishAsync(
                new GetAgentSummarizeEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    state.Identity.SleekflowCompanyId,
                    config.Id,
                    conversationMessages,
                    handoverReason ?? string.Empty,
                    contactProperties,
                    languageIsoCode));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<(Dictionary<string, string>? ContactProperties, string? LanguageIsoCode)> GetPromptContextAsync(
        ProxyState state,
        string? contactId,
        AgentConfig config,
        CallStep<AgentSummarizeStepArgs> callStep)
    {
        var contactProperties = config.IsContactPropertiesEnabledAsContext == true
            ? (await _coreCommander.ExecuteAsync<ContactProperties.GetContactPropertiesOutput>(
                state.Origin,
                CommandNames.GetContactProperties,
                new ContactProperties.GetContactPropertiesInput(state.Identity, contactId!)))
            ?.ContactProperties
            : null;

        var languageIsoCode =
            await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.LanguageIsoCodeExpr);
        return (contactProperties, languageIsoCode);
    }
}