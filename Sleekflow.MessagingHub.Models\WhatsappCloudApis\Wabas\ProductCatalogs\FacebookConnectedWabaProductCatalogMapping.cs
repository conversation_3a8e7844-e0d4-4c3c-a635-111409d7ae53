using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;

public class FacebookConnectedWabaProductCatalogMappingDto
{
    [JsonProperty("waba", NullValueHandling = NullValueHandling.Ignore)]
    public WabaDto? Waba { get; set; }

    [JsonProperty("waba_product_catalog", NullValueHandling = NullValueHandling.Ignore)]
    public WabaProductCatalogDto? WabaProductCatalog { get; set; }

    [JsonConstructor]
    public FacebookConnectedWabaProductCatalogMappingDto(WabaDto? waba, WabaProductCatalogDto? wabaProductCatalog)
    {
        Waba = waba;
        WabaProductCatalog = wabaProductCatalog;
    }
}