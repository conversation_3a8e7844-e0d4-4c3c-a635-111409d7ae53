using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AgentRecommendReplyStepArgs : TypedCallStepArgs, IHasCompanyAgentConfigIdExpr
{
    public const string CallName = "sleekflow.v1.agent-recommend-reply";

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty(IHasCompanyAgentConfigIdExpr.PropertyNameCompanyAgentConfigIdExpr)]
    public string? CompanyAgentConfigIdExpr { get; set; }

    [JsonProperty("retrieval_window_timestamp__expr")]
    public string? RetrievalWindowTimestampExpr { get; set; }

    [JsonProperty("input__expr")]
    public string? InputExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonConstructor]
    public AgentRecommendReplyStepArgs(
        string contactIdExpr,
        string? companyAgentConfigIdExpr,
        string? retrievalWindowTimestampExpr = null,
        string? inputExpr = null)
    {
        ContactIdExpr = contactIdExpr;
        CompanyAgentConfigIdExpr = companyAgentConfigIdExpr;
        RetrievalWindowTimestampExpr = retrievalWindowTimestampExpr;
        InputExpr = inputExpr;
    }
}