using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Mvc.Configs;
using Sleekflow.Mvc.Requests;

namespace Sleekflow.Mvc.Func;

public interface IRequestService
{
    Task<Request> CreateLocalRequestAsync(
        HttpRequest httpRequest,
        PathString requestPath);

    Task<Request> CreateRequestAsync(
        HttpRequest httpRequest,
        PathString requestPath);

    Task PatchRequestOutputAsync(
        string id,
        long elapsedMilliseconds,
        bool success,
        object? input = null,
        object? output = null,
        Exception? exception = null);
}

public class RequestService : ISingletonService, IRequestService
{
    public const int MaxSize = 1024 * 512;

    private readonly IIdService _idService;
    private readonly IRequestConfig _requestConfig;
    private readonly IRequestRepository _requestRepository;

    public RequestService(
        IIdService idService,
        IRequestConfig requestConfig,
        IRequestRepository requestRepository)
    {
        _idService = idService;
        _requestConfig = requestConfig;
        _requestRepository = requestRepository;
    }

    public Task<Request> CreateLocalRequestAsync(
        HttpRequest httpRequest,
        PathString requestPath)
    {
        var request = new Request(
            _idService.GetId("Request"),
            httpRequest.HttpContext.TraceIdentifier,
            Environment.MachineName,
            DateTimeOffset.UtcNow,
            requestPath.ToUriComponent(),
            _requestConfig.AppName);

        return Task.FromResult(request);
    }

    public async Task<Request> CreateRequestAsync(
        HttpRequest httpRequest,
        PathString requestPath)
    {
        var request = new Request(
            _idService.GetId("Request"),
            httpRequest.HttpContext.TraceIdentifier,
            Environment.MachineName,
            DateTimeOffset.UtcNow,
            requestPath.ToUriComponent(),
            _requestConfig.AppName);

        if (BypassPathConfig.IsPathBypassed(requestPath))
        {
            return request;
        }

        return await _requestRepository.CreateAndGetAsync(
            request,
            request.Id);
    }

    public async Task PatchRequestOutputAsync(
        string id,
        long elapsedMilliseconds,
        bool success,
        object? input = null,
        object? output = null,
        Exception? exception = null)
    {
        var patchOperations = new List<PatchOperation>
        {
            PatchOperation.Replace("/elapsed_milliseconds", elapsedMilliseconds),
            PatchOperation.Replace("/success", success)
        };

        if (input != null)
        {
            var inputJson = JsonConvert.SerializeObject(
                input,
                new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });

            var isSmall = inputJson.Length < MaxSize;

            patchOperations.Add(
                PatchOperation.Replace(
                    "/input",
                    isSmall ? JsonConvert.DeserializeObject(inputJson) : inputJson[..MaxSize]));
        }

        if (output != null)
        {
            var outputJson = JsonConvert.SerializeObject(
                output,
                new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });

            var isSmall = outputJson.Length < MaxSize;

            patchOperations.Add(
                PatchOperation.Replace(
                    "/output",
                    isSmall ? JsonConvert.DeserializeObject(outputJson) : outputJson[..MaxSize]));
        }

        if (exception != null)
        {
            patchOperations.Add(PatchOperation.Replace("/exception_stack_trace", FlattenException(exception)));
        }

        await _requestRepository.PatchAsync(
            id,
            id,
            patchOperations);
    }

    private static string FlattenException(Exception exception)
    {
        var sb = new StringBuilder();

        Exception? ex = exception;
        while (ex != null)
        {
            sb.AppendLine(ex.Message);
            sb.AppendLine(ex.StackTrace);
            ex = ex.InnerException;
        }

        return sb.ToString();
    }
}