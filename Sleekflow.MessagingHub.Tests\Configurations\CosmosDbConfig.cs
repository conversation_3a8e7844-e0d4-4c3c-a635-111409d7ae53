using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.Persistence;

namespace Sleekflow.MessagingHub.Tests.Configurations;

public class CosmosDbConfig : IDbConfig
{
    public string Endpoint { get; }
        = "https://sleekflow2bd1537b.documents.azure.com:443/";

    public string Key { get; } =
        "****************************************************************************************";

    public string DatabaseId { get; } =
        ContainerNames.DatabaseId;
}