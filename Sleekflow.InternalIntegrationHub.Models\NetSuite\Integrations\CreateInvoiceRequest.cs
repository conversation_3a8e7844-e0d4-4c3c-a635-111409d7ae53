using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreateInvoiceRequest
{
    [JsonProperty("externalId", NullValueHandling = NullValueHandling.Ignore)]
    public string? ExternalId { get; set; }

    [JsonProperty("dueDate", NullValueHandling = NullValueHandling.Ignore)]
    public string? DueDate { get; set; }

    [JsonProperty("tranDate", NullValueHandling = NullValueHandling.Ignore)]
    public string? TranDate { get; set; }

    [JsonProperty("entity", NullValueHandling = NullValueHandling.Ignore)]
    public Entity? Entity { get; set; }

    [JsonProperty("item", NullValueHandling = NullValueHandling.Ignore)]
    public InvoiceItems? Items { get; set; }

    [JsonProperty("currency", NullValueHandling = NullValueHandling.Ignore)]
    public Currency? Currency { get; set; }

    [JsonProperty("terms", NullValueHandling = NullValueHandling.Ignore)]
    public Term? Terms { get; set; }

    [JsonProperty("salesRep", NullValueHandling = NullValueHandling.Ignore)]
    public SalesRep? SalesRep { get; set; }

    [JsonConstructor]
    public CreateInvoiceRequest(
        string? externalId,
        string? dueDate,
        string? tranDate,
        Entity? entity,
        InvoiceItems? items,
        Currency? currency,
        Term? terms,
        SalesRep? salesRep)
    {
        ExternalId = externalId;
        DueDate = dueDate;
        TranDate = tranDate;
        Entity = entity;
        Items = items;
        Currency = currency;
        Terms = terms;
        SalesRep = salesRep;
    }
}

public class InvoiceItems
{
    [JsonProperty("items")]
    public List<InvoiceItem>? Item { get; set; }

    [JsonConstructor]
    public InvoiceItems(List<InvoiceItem>? item)
    {
        Item = item;
    }
}

public class InvoiceItem
{
    [JsonProperty("item", NullValueHandling = NullValueHandling.Ignore)]
    public string? Item { get; set; }

    [JsonProperty("quantity", NullValueHandling = NullValueHandling.Ignore)]
    public int? Quantity { get; set; }

    [JsonProperty("amount", NullValueHandling = NullValueHandling.Ignore)]
    public double? Amount { get; set; }

    [JsonProperty("custcol_cv_rr_rev_end_date", NullValueHandling = NullValueHandling.Ignore)]
    public string? CustcolCvRrRevEndDate { get; set; }

    [JsonProperty("custcol_cv_rr_rev_start_date", NullValueHandling = NullValueHandling.Ignore)]
    public string? CustcolCvRrRevStartDate { get; set; }

    [JsonProperty("description", NullValueHandling = NullValueHandling.Ignore)]
    public string? Description { get; set; }

    [JsonConstructor]
    public InvoiceItem(
        string? item,
        int? quantity,
        double? amount,
        string? custcolCvRrRevEndDate,
        string? custcolCvRrRevStartDate,
        string? description)
    {
        Item = item;
        Quantity = quantity;
        Amount = amount;
        CustcolCvRrRevEndDate = custcolCvRrRevEndDate;
        CustcolCvRrRevStartDate = custcolCvRrRevStartDate;
        Description = description;
    }

    public InvoiceItem()
    {
    }
}

public class Term
{
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string? Id { get; set; }

    [JsonConstructor]
    public Term(string? id)
    {
        Id = id;
    }
}

public class SalesRep
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonConstructor]
    public SalesRep(string? id)
    {
        Id = id;
    }
}