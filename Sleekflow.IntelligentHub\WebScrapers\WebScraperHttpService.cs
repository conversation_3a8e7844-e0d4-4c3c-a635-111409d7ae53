﻿using System.Text;
using System.Web;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

namespace Sleekflow.IntelligentHub.WebScrapers;

/// <summary>
/// For external calls to Apify.
/// https://docs.apify.com/api/v2/.
/// </summary>
public interface IWebScraperHttpService
{
    #region Company Web Scraper
    public Task<ApifyRunResponse> StartActorRunAsync(WebScraperSetting webScraperSetting);
    #endregion

    #region Apify Run
    public Task<ApifyRunResponse> GetRunDetailAsync(string apifyRunId);

    public Task<ApifyRunResponse> AbortRunAsync(string apifyRunId);

    public Task<ApifyRunResponse> ResurrectRunAsync(string apifyRunId);

    public Task<ApifyDatasetResponse> GetDatasetDetailAsync(string apifyDatasetId);

    public Task<List<ApifyDatasetItemResponse>> GetDatasetItemsAsync(string apifyDatasetId, int offset = 0, int limit = 1000);
    #endregion

    #region Apify Task
    public Task<ApifyTaskResponse> GetApifyTaskDetailAsync(string apifyTaskId);

    public Task<ApifyTaskResponse> CreateApifyTaskAsync(string taskUniqueName, WebScraperSetting? scraperSetting);

    public Task<ApifyTaskResponse> UpdateApifyTaskSettingsAsync(string apifyTaskId, string taskName, WebScraperSetting? scraperSetting);

    public Task DeleteTaskAsync(string apifyTaskId);

    public Task<ApifyRunResponse> StartApifyTaskAsync(string apifyTaskId);

    public Task RegisterWebhookForApifyTaskAsync(string apifyTaskId);
    #endregion

    #region Apify Schedule
    public Task<ApifySchedulerResponse> GetSchedulerAsync(string apifySchedulerId);

    public Task<ApifySchedulerResponse> CreateSchedulerAsync(string schedulerUniqueName, string apifyTaskId, string cronExpression);

    public Task<ApifySchedulerResponse> UpdateSchedulerSettingsAsync(string apifySchedulerId, string cronExpression, bool isEnabled);

    public Task DeleteSchedulerAsync(string apifySchedulerId);
    #endregion
}

public class WebScraperHttpService : IWebScraperHttpService, IScopedService
{
    private readonly HttpClient _httpClient;
    private readonly IApifyConfig _apifyConfig;

    public WebScraperHttpService(
        IHttpClientFactory httpClientFactory,
        IApifyConfig apifyConfig)
    {
        _apifyConfig = apifyConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<ApifyRunResponse> StartActorRunAsync(WebScraperSetting webScraperSetting)
    {
        try
        {
            var requestBody = ConstructApifyScraperSettingRequest(webScraperSetting);
            var content = new StringContent(
                JsonConvert.SerializeObject(requestBody),
                Encoding.UTF8,
                "application/json");

            var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.ActorRunStartEndpoint);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);

            query["token"] = _apifyConfig.Token;
            uriBuilder.Query = query.ToString();

            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post, RequestUri = uriBuilder.Uri, Content = content
            };

            var response = await _httpClient.SendAsync(requestMessage);
            var responseContent = await response.Content.ReadAsStringAsync();
            var actorRunResponse = JsonConvert.DeserializeObject<ApifyRunResponse>(responseContent);

            if (!response.IsSuccessStatusCode || actorRunResponse == null)
            {
                var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
                throw new SfExternalCallException($"[ApifyIntegration] [StartActorRunAsync] failed! Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
            }

            return actorRunResponse;
        }
        catch (SfExternalCallException ece)
        {
            // Will implement retry logic later with https://app.clickup.com/t/9008009945/DEVS-1327
            throw;
        }
        catch (Exception e)
        {
            throw new SfInternalErrorException($"[ApifyIntegration] [StartActorRunAsync] parse ScraperSetting failed! {e.Message}");
        }
    }

    public async Task<ApifyTaskResponse> GetApifyTaskDetailAsync(string apifyTaskId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.TaskManageEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyTaskId", apifyTaskId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorTaskResponse = JsonConvert.DeserializeObject<ApifyTaskResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorTaskResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [GetApifyTaskDetailAsync] failed! apifyTaskId: {apifyTaskId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorTaskResponse;
    }

    public async Task<ApifyTaskResponse> CreateApifyTaskAsync(string taskUniqueName, WebScraperSetting? scraperSetting)
    {
        var requestBody = new JObject
        {
            ["actId"] = _apifyConfig.ActorId,
            ["title"] = taskUniqueName,
            ["name"] = taskUniqueName,
            ["input"] = scraperSetting != null
                ? JObject.FromObject(ConstructApifyScraperSettingRequest(scraperSetting))
                : null,
        };

        var content = new StringContent(
            JsonConvert.SerializeObject(requestBody),
            Encoding.UTF8,
            "application/json");

        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.TaskCreateEndpoint);
        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = uriBuilder.Uri,
            Content = content
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorTaskResponse = JsonConvert.DeserializeObject<ApifyTaskResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorTaskResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [CreateApifyTaskAsync] failed! taskUniqueName: {taskUniqueName}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorTaskResponse;
    }

    public async Task<ApifyTaskResponse> UpdateApifyTaskSettingsAsync(string apifyTaskId, string taskName, WebScraperSetting? scraperSetting)
    {
        var requestBody = new JObject
        {
            ["actId"] = _apifyConfig.ActorId,
            ["title"] = taskName,
            ["name"] = taskName,
            ["input"] = scraperSetting != null
                ? JObject.FromObject(ConstructApifyScraperSettingRequest(scraperSetting))
                : null,
        };

        var content = new StringContent(
            JsonConvert.SerializeObject(requestBody),
            Encoding.UTF8,
            "application/json");

        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.TaskManageEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyTaskId", apifyTaskId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Put,
            RequestUri = uriBuilder.Uri,
            Content = content
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorTaskResponse = JsonConvert.DeserializeObject<ApifyTaskResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorTaskResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [UpdateApifyTaskSettingsAsync] failed! taskName: {taskName}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorTaskResponse;
    }

    public async Task DeleteTaskAsync(string apifyTaskId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.TaskManageEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyTaskId", apifyTaskId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Delete,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);

        if (!response.IsSuccessStatusCode)
        {
            throw new SfExternalCallException(
                $"[ApifyIntegration] [DeleteTaskAsync] failed! apifyTaskId: {apifyTaskId} Response Code: {response.StatusCode}");
        }
    }

    public async Task<ApifyRunResponse> StartApifyTaskAsync(string apifyTaskId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.TaskStartEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyTaskId", apifyTaskId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = uriBuilder.Uri,
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var taskRunResponse = JsonConvert.DeserializeObject<ApifyRunResponse>(responseContent);

        if (!response.IsSuccessStatusCode || taskRunResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [StartApifyTaskAsync] failed! apifyTaskId: {apifyTaskId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return taskRunResponse;
    }

    public async Task<ApifySchedulerResponse> GetSchedulerAsync(string apifySchedulerId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.SchedulerManageEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifySchedulerId", apifySchedulerId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorSchedulerResponse = JsonConvert.DeserializeObject<ApifySchedulerResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorSchedulerResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [GetScheduleAsync] failed! apifySchedulerId: {apifySchedulerId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorSchedulerResponse;
    }

    public async Task<ApifySchedulerResponse> CreateSchedulerAsync(string schedulerUniqueName, string apifyTaskId, string cronExpression)
    {
        var requestBody = ConstructApifySchedulerRequest(schedulerUniqueName, apifyTaskId, cronExpression, true);

        var content = new StringContent(
            JsonConvert.SerializeObject(requestBody),
            Encoding.UTF8,
            "application/json");

        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.SchedulerCreateEndpoint);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = uriBuilder.Uri,
            Content = content
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorSchedulerResponse = JsonConvert.DeserializeObject<ApifySchedulerResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorSchedulerResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [CreateSchedulerAsync] create scheduler for taskId: {apifyTaskId} failed! Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorSchedulerResponse;
    }

    public async Task<ApifySchedulerResponse> UpdateSchedulerSettingsAsync(string apifySchedulerId, string cronExpression, bool isEnabled)
    {
        var requestBody = new JObject
        {
            ["isEnabled"] = isEnabled,
            ["cronExpression"] = cronExpression,
        };

        var content = new StringContent(
            JsonConvert.SerializeObject(requestBody),
            Encoding.UTF8,
            "application/json");

        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.SchedulerManageEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifySchedulerId", apifySchedulerId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Put,
            RequestUri = uriBuilder.Uri,
            Content = content
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorSchedulerResponse = JsonConvert.DeserializeObject<ApifySchedulerResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorSchedulerResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [UpdateTaskSettingsAsync] failed! Scheduler Id: {apifySchedulerId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorSchedulerResponse;
    }

    public async Task DeleteSchedulerAsync(string apifySchedulerId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.SchedulerManageEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifySchedulerId", apifySchedulerId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Delete,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [DeleteSchedulerAsync] failed! apifySchedulerId: {apifySchedulerId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }
    }

    public async Task RegisterWebhookForApifyTaskAsync(string apifyTaskId)
    {
        var requestBody = ConstructApifyWebhookIntegrationRequest(apifyTaskId, _apifyConfig.WebhookUri);
        var content = new StringContent(
            JsonConvert.SerializeObject(requestBody),
            Encoding.UTF8,
            "application/json");

        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.TaskWebhookCreateEndpoint);
        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = uriBuilder.Uri,
            Content = content
        };

        var response = await _httpClient.SendAsync(requestMessage);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [RegisterWebhookForApifyTaskAsync] failed! apifyTaskId: {apifyTaskId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }
    }

    public async Task<ApifyRunResponse> GetRunDetailAsync(string apifyRunId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.RunDetailGetEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyRunId", apifyRunId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorRunResponse = JsonConvert.DeserializeObject<ApifyRunResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorRunResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [GetRunDetailAsync] failed! apifyRunId: {apifyRunId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorRunResponse;
    }

    public async Task<ApifyRunResponse> AbortRunAsync(string apifyRunId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.RunAbortEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyRunId", apifyRunId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorRunResponse = JsonConvert.DeserializeObject<ApifyRunResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorRunResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [ApifyRunResponse] failed! apifyRunId: {apifyRunId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorRunResponse;
    }

    public async Task<ApifyRunResponse> ResurrectRunAsync(string apifyRunId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.RunResurrectEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyRunId", apifyRunId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var actorRunResponse = JsonConvert.DeserializeObject<ApifyRunResponse>(responseContent);

        if (!response.IsSuccessStatusCode || actorRunResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [ResurrectRunAsync] failed! apifyRunId: {apifyRunId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return actorRunResponse;
    }

    public async Task<ApifyDatasetResponse> GetDatasetDetailAsync(string apifyDatasetId)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.DatasetDetailGetEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyDatasetId", apifyDatasetId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var apifyDatasetResponse = JsonConvert.DeserializeObject<ApifyDatasetResponse>(responseContent);

        if (!response.IsSuccessStatusCode || apifyDatasetResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [GetDatasetDetailAsync] failed! DatasetId: {apifyDatasetId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return apifyDatasetResponse;
    }

    public async Task<List<ApifyDatasetItemResponse>> GetDatasetItemsAsync(string apifyDatasetId, int offset = 0, int limit = 1000)
    {
        var uriBuilder = new UriBuilder(ApifyIntegrationEndpoints.DatasetItemsGetEndpoint);
        uriBuilder.Path = uriBuilder.Path.Replace("apifyDatasetId", apifyDatasetId);

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["token"] = _apifyConfig.Token;
        query["offset"] = offset.ToString();
        query["limit"] = limit.ToString();
        uriBuilder.Query = query.ToString();

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = uriBuilder.Uri
        };

        var response = await _httpClient.SendAsync(requestMessage);
        var responseContent = await response.Content.ReadAsStringAsync();
        var apifyDatasetItemResponse = JsonConvert.DeserializeObject<List<ApifyDatasetItemResponse>>(responseContent);

        if (!response.IsSuccessStatusCode || apifyDatasetItemResponse == null)
        {
            var apifyError = JsonConvert.DeserializeObject<ApifyErrorResponse>(responseContent);
            throw new SfExternalCallException(
                $"[ApifyIntegration] [GetDatasetItemsAsync] failed! DatasetId: {apifyDatasetId}  Error Type: {apifyError?.Error.Type}, Error Message: {apifyError?.Error.Message}");
        }

        return apifyDatasetItemResponse;
    }

    #region private

    private static ApifyWebhookIntegrationRequest ConstructApifyWebhookIntegrationRequest(
        string apifyTaskId,
        string requestUrl)
    {
        return new ApifyWebhookIntegrationRequest(
            new List<string>
            {
                ApifyRunEventTypes.Succeeded,
                ApifyRunEventTypes.Failed,
                ApifyRunEventTypes.Aborted,
                ApifyRunEventTypes.TimedOut,
                ApifyRunEventTypes.Resurrected
            },
            apifyTaskId,
            requestUrl);
    }

    private static ApifyWebScraperSettingRequest ConstructApifyScraperSettingRequest(WebScraperSetting webScraperSetting)
    {
        return new ApifyWebScraperSettingRequest(
            dynamicContentWaitSecs: webScraperSetting.DynamicContentWaitSecs,
            maxCrawlDepth: webScraperSetting.MaxCrawlDepth,
            maxCrawlPages: webScraperSetting.MaxCrawlPages,
            maxConcurrentPages: webScraperSetting.MaxConcurrentPages,
            initialCookies: webScraperSetting.InitialCookies,
            startUrls: new[]
            {
                new StartUrl(webScraperSetting.StartUrl)
            },
            aggressivePrune: false,
            debugMode: false,
            proxyConfiguration: new ProxyConfiguration(true),
            removeCookieWarnings: true,
            clickElementsCssSelector: "[aria-expanded=\"false\"]",
            removeElementsCssSelector: "nav, footer, script, style, noscript, svg,\n[role=\"alert\"],\n[role=\"banner\"],\n[role=\"dialog\"],\n[role=\"alertdialog\"],\n[role=\"region\"][aria-label*=\"skip\" i],\n[aria-modal=\"true\"]",
            saveFiles: false,
            saveHtml: false,
            saveMarkdown: true,
            saveScreenshots: false,
            crawlerType: "playwright:firefox",
            excludeUrlGlobs: Array.Empty<string>(),
            initialConcurrency: 0,
            maxScrollHeightPixels: 5000,
            htmlTransformer: "readableText",
            readableTextCharThreshold: 100,
            maxResults: 99999);
    }

    private static ApifySchedulerRequest ConstructApifySchedulerRequest(
        string name,
        string apifyTaskId,
        string cronExpression,
        bool isEnabled)
    {
        return new ApifySchedulerRequest(
            name: name,
            title: name,
            isEnabled: isEnabled,
            isExclusive: true,
            cronExpression: cronExpression,
            timezone: "UTC",
            actions: new List<ApifySchedulerRequestAction>
            {
                new ApifySchedulerRequestAction("RUN_ACTOR_TASK", apifyTaskId)
            });
    }

    #endregion
}