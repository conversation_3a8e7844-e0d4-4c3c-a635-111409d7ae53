﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class HubspotObjectCreatedEventRequestConsumerDefinition : ConsumerDefinition<HubspotObjectCreatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<HubspotObjectCreatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class HubspotObjectCreatedEventRequestConsumer : IConsumer<HubspotObjectCreatedEventRequest>
{
    private readonly IBus _bus;

    public HubspotObjectCreatedEventRequestConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<HubspotObjectCreatedEventRequest> context)
    {
        var hubspotObjectCreatedEventRequest = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnHubspotObjectCreatedEventBody(
                hubspotObjectCreatedEventRequest.CreatedAt,
                hubspotObjectCreatedEventRequest.ConnectionId,
                hubspotObjectCreatedEventRequest.ObjectType,
                hubspotObjectCreatedEventRequest.ObjectDict),
            hubspotObjectCreatedEventRequest.ObjectId,
            hubspotObjectCreatedEventRequest.ObjectType,
            hubspotObjectCreatedEventRequest.SleekflowCompanyId));
    }
}