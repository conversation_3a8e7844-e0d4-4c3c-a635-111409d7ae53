using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Processors;

public interface ICrmHubDbProcessorConfig
{
    string CosmosChangeFeedEnvId { get; }
}

public class CrmHubDbProcessorConfig : IConfig, ICrmHubDbProcessorConfig
{
    public string CosmosChangeFeedEnvId { get; private set; }

    public CrmHubDbProcessorConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CosmosChangeFeedEnvId =
            Environment.GetEnvironmentVariable("COSMOS_CHANGE_FEED_ENV_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CHANGE_FEED_ENV_ID");
    }
}