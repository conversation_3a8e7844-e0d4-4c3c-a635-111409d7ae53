﻿using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Polly;
using Sharprompt;
using Sleekflow.Constants;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

class ConvertToSoftDelete : IExecutor
{
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private List<string>? _containerIds;

    public ConvertToSoftDelete(DbConfig dbConfig)
    {
        _cosmosClient = new CosmosClient(
            dbConfig.Endpoint,
            dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Convert To Soft Delete";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerIds = Prompt.MultiSelect(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerIds = selectedContainerIds.ToList();
    }

    public async Task ExecuteAsync()
    {
        foreach (var containerId in _containerIds!)
        {
            var count = await MigrateObjectsAsync(containerId);
            Console.WriteLine($"Completed {containerId} for {count} objects");
        }
    }

    private async Task<int> MigrateObjectsAsync(
        string containerName)
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                9,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(1.37) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], containerName=[{containerName}]");

                    return Task.CompletedTask;
                });

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerName);
        var i = 0;

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(container),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(async () => await MigrateObject(dict, container, token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private async Task<int> MigrateObject(
        Dictionary<string, object?> dict,
        Container container,
        CancellationToken token)
    {
        var newDict = new Dictionary<string, object?>(dict)
        {
            ["record_statuses"] = new List<string>()
            {
                RecordStatuses.Active
            }
        };

        var partitionKeyPaths =
            await CosmosUtils.GetContainerPartitionKeyPathsAsync(_cosmosClient, _databaseId!, container.Id);

        var partitionKeyBuilder = new PartitionKeyBuilder();
        foreach (var partitionKeyPath in partitionKeyPaths!)
        {
            partitionKeyBuilder.Add((string?) newDict[partitionKeyPath.TrimStart('/')]);
        }

        await container.ReplaceItemAsync(
            newDict,
            newDict["id"]!.ToString()!,
            partitionKeyBuilder.Build(),
            new ItemRequestOptions
            {
                IfMatchEtag = (string) newDict["_etag"]!, EnableContentResponseOnWrite = false,
            },
            token);

        return 1;
    }
}