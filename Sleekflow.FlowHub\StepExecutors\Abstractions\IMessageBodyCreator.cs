using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;

namespace Sleekflow.FlowHub.StepExecutors.Abstractions;

public interface IMessageBodyCreator
{
    bool CanHandle(string channelType);

    Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args);

    Task<MessageBody> CreateMediaMessageBodyAsync(string mediaUrl, string mediaType, string? mediaName, SendMessageV2StepArgs args);
}