﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class CreateProduct
    : ITrigger<
        CreateProduct.CreateProductInput,
        CreateProduct.CreateProductOutput>
{
    private readonly IProductService _productService;
    private readonly IImageService _imageService;
    private readonly IProductVariantService _productVariantService;
    private readonly IIdService _idService;

    public CreateProduct(
        IProductService productService,
        IImageService imageService,
        IProductVariantService productVariantService,
        IIdService idService)
    {
        _productService = productService;
        _imageService = imageService;
        _productVariantService = productVariantService;
        _idService = idService;
    }

    public class CreateProductInputProductVariant : ProductVariantInput
    {
        [JsonConstructor]
        public CreateProductInputProductVariant(
            string? sku,
            string? url,
            List<Price> prices,
            int position,
            List<ProductVariant.ProductVariantAttribute> attributes,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            PlatformData? platformData)
            : base(
                sku,
                url,
                prices,
                position,
                attributes,
                names,
                descriptions,
                images,
                platformData)
        {
        }
    }

    public class CreateProductInput : ProductInput, IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("product_variants")]
        public List<CreateProductInputProductVariant> ProductVariants { get; set; }

        [ValidateObject]
        [JsonProperty("platform_data")]
        public PlatformData? PlatformData { get; set; }

        [Required]
        [JsonProperty(Product.PropertyNameIsViewEnabled)]
        public bool IsViewEnabled { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateProductInput(
            List<string> categoryIds,
            string? sku,
            string? url,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            Dictionary<string, object?> metadata,
            string sleekflowCompanyId,
            string storeId,
            List<CreateProductInputProductVariant> productVariants,
            PlatformData? platformData,
            bool isViewEnabled,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(categoryIds, sku, url, names, descriptions, images, metadata)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductVariants = productVariants;
            PlatformData = platformData;
            IsViewEnabled = isViewEnabled;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateProductOutput
    {
        [JsonProperty("product")]
        public ProductDto Product { get; set; }

        [JsonConstructor]
        public CreateProductOutput(ProductDto product)
        {
            Product = product;
        }
    }

    public async Task<CreateProductOutput> F(CreateProductInput createProductInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createProductInput.SleekflowStaffId,
            createProductInput.SleekflowStaffTeamIds);

        var images = await _imageService.GetImagesAsync(
            createProductInput.Images,
            createProductInput.SleekflowCompanyId,
            createProductInput.StoreId);

        var productVariantNames = createProductInput.ProductVariants.SelectMany(pv => pv.Names).ToList();
        var productVariantDescriptions = createProductInput.ProductVariants.SelectMany(pv => pv.Descriptions).ToList();
        var productVariantAttributes = createProductInput.ProductVariants.SelectMany(pv => pv.Attributes)
            .GroupBy(a => a.Name)
            .Select(
                ag => new Product.ProductAttribute(
                    ag.Key,
                    ag.Select(a => new Product.ProductAttributeValue(a.Value)).ToList()))
            .ToList();
        var productVariantPrices = createProductInput.ProductVariants.SelectMany(pv => pv.Prices).ToList();

        var product = await _productService.CreateAndGetProductAsync(
            new Product(
                _idService.GetId(SysTypeNames.Product),
                createProductInput.SleekflowCompanyId,
                createProductInput.StoreId,
                createProductInput.CategoryIds,
                createProductInput.Sku,
                createProductInput.Url,
                createProductInput.Names,
                createProductInput.Descriptions,
                images,
                productVariantNames,
                productVariantDescriptions,
                productVariantAttributes,
                productVariantPrices,
                createProductInput.IsViewEnabled,
                new List<string>
                {
                    "Active"
                },
                PlatformData.CustomCatalog(),
                createProductInput.Metadata,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                createdBy: sleekflowStaff,
                updatedBy: sleekflowStaff),
            sleekflowStaff);

        var productVariants = new List<ProductVariant>
        {
            Capacity = Math.Min(createProductInput.ProductVariants.Count, 1)
        };

        // TODO Transactional
        foreach (var productVariantInput in createProductInput.ProductVariants)
        {
            var productVariantImages = await _imageService.GetImagesAsync(
                productVariantInput.Images,
                createProductInput.SleekflowCompanyId,
                createProductInput.StoreId);

            var productVariant = await _productVariantService.CreateAndGetProductVariantAsync(
                new ProductVariant(
                    _idService.GetId(SysTypeNames.ProductVariant),
                    createProductInput.SleekflowCompanyId,
                    createProductInput.StoreId,
                    product.Id,
                    productVariantInput.Sku,
                    productVariantInput.Url,
                    productVariantInput.Prices,
                    productVariantInput.Position,
                    false,
                    productVariantInput.Attributes,
                    productVariantInput.Names,
                    productVariantInput.Descriptions,
                    productVariantImages,
                    createProductInput.PlatformData ?? PlatformData.CustomCatalog(),
                    new List<string>
                    {
                        "Active"
                    },
                    new Dictionary<string, object?>(),
                    sleekflowStaff,
                    sleekflowStaff,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow),
                sleekflowStaff);

            productVariants.Add(productVariant);
        }

        return new CreateProductOutput(new ProductDto(product, productVariants));
    }
}