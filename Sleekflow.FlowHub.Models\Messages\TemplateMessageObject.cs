using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

namespace Sleekflow.FlowHub.Models.Messages;

public class TemplateMessageObject
{
    [JsonProperty("template_name")]
    public string TemplateName { get; set; }

    [JsonProperty("language")]
    public string Language { get; set; }

    [JsonProperty("components")]
    public List<TemplateMessageObjectComponent> Components { get; set; }

    [JsonConstructor]
    public TemplateMessageObject(
        string templateName,
        string language,
        List<TemplateMessageObjectComponent> components)
    {
        TemplateName = templateName;
        Language = language;
        Components = components;
    }
}

public class TemplateMessageObjectComponent
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("sub_type")]
    public string SubType { get; set; }

    [JsonProperty("index")]
    public int Index { get; set; }

    [JsonProperty("parameters")]
    public List<TemplateMessageObjectComponentParameter> Parameters { get; set; }

    [JsonConstructor]
    public TemplateMessageObjectComponent(
        string type,
        string subType,
        int index,
        List<TemplateMessageObjectComponentParameter> parameters)
    {
        Type = type;
        SubType = subType;
        Index = index;
        Parameters = parameters;
    }
}

public class TemplateMessageObjectComponentParameter
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonProperty("payload")]
    public string Payload { get; set; }

    [JsonProperty("image")]
    public ImageMessageObject? Image { get; set; }

    [JsonProperty("audio")]
    public AudioMessageObject? Audio { get; set; }

    [JsonProperty("document")]
    public DocumentMessageObject? Document { get; set; }

    [JsonProperty("video")]
    public VideoMessageObject? Video { get; set; }

    [JsonProperty("location")]
    public LocationMessageObject? Location { get; set; }

    [JsonProperty("currency")]
    public CurrencyMessageObject? Currency { get; set; }

    [JsonProperty("date_time")]
    public DateTimeMessageObject? DateTime { get; set; }

    [JsonProperty("action")]
    public ActionMessageObject? Action { get; set; }

    [JsonConstructor]
    public TemplateMessageObjectComponentParameter(
        string type,
        string text,
        string payload,
        ImageMessageObject? image,
        AudioMessageObject? audio,
        DocumentMessageObject? document,
        VideoMessageObject? video,
        LocationMessageObject? location,
        CurrencyMessageObject? currency,
        DateTimeMessageObject? dateTime,
        ActionMessageObject? action)
    {
        Type = type;
        Text = text;
        Payload = payload;
        Image = image;
        Audio = audio;
        Document = document;
        Video = video;
        Location = location;
        Currency = currency;
        DateTime = dateTime;
        Action = action;
    }
}