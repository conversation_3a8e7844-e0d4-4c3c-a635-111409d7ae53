using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.Datasets;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

[ContainerId(ContainerNames.Waba)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IMessagingHubDbResolver))]
public class Waba : Entity, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    public const string PropertyNameSleekflowCompanyIds = "sleekflow_company_ids";

    // TODO Need to update the property name to facebook business id when doing the data migration
    public const string PropertyNameFacebookWabaBusinessId = "facebook_waba_business_id";
    public const string PropertyNameFacebookBusinessId = "facebook_business_id";
    public const string PropertyNameFacebookWabaName = "facebook_waba_name";
    public const string PropertyNameFacebookWabaAccountReviewStatus = "facebook_waba_account_review_status";
    public const string PropertyNameFacebookWabaBusinessName = "facebook_waba_business_name";
    public const string PropertyNameFacebookWabaPrimaryFundingId = "facebook_waba_primary_funding_id";
    public const string PropertyNameFacebookWabaMessageTemplateNamespace = "facebook_waba_message_template_namespace";
    public const string PropertyNameFacebookWabaLongLivedAccessToken = "facebook_waba_long_lived_access_token";
    public const string PropertyNameFacebookBusinessIntegrationSystemUserAccessTokens = "facebook_business_integration_system_user_access_tokens";
    public const string PropertyNameFacebookWabaBusinessDetailSnapshot = "facebook_waba_business_detail_snapshot";

    // TODO Need to update the property name to facebook_waba_snapshot when doing the data migration.
    public const string PropertyNameFacebookWabaSnapshot = "facebook_waba_business_account_snapshot";
    public const string PropertyNameWabaPhoneNumbers = "waba_phone_numbers";
    public const string PropertyNameWabaActiveProductCatalog = "waba_active_product_catalog";
    public const string PropertyNameWabaProductCatalog = "waba_product_catalog";
    public const string PropertyNameWabaDataset = "waba_dataset";
    public const string PropertyNameUpdatedBy = "updated_by";
    public const string PropertyNameMarketingMessagesLiteApiStatus = "marketing_messages_lite_api_status";

    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty(PropertyNameFacebookWabaBusinessId)]
    public string FacebookWabaBusinessId { get; set; }

    [JsonProperty(PropertyNameFacebookBusinessId)]
    public string? FacebookBusinessId { get; set; }

    [JsonProperty(PropertyNameSleekflowCompanyIds)]
    public List<string> SleekflowCompanyIds { get; set; }

    [JsonProperty(PropertyNameFacebookWabaName)]
    public string? FacebookWabaName { get; set; }

    [JsonProperty(PropertyNameFacebookWabaAccountReviewStatus)]
    public string? FacebookWabaAccountReviewStatus { get; set; }

    [JsonProperty(PropertyNameFacebookWabaBusinessName)]
    public string? FacebookWabaBusinessName { get; set; }

    [JsonProperty(PropertyNameFacebookWabaPrimaryFundingId)]
    public string? FacebookWabaPrimaryFundingId { get; set; }

    [JsonProperty(PropertyNameFacebookWabaMessageTemplateNamespace)]
    public string? FacebookWabaMessageTemplateNamespace { get; set; }

    [JsonProperty(PropertyNameFacebookWabaLongLivedAccessToken)]
    public WabaLongLivedAccessToken? FacebookWabaUserLongLivedAccessToken { get; set; }

    [JsonProperty(PropertyNameFacebookBusinessIntegrationSystemUserAccessTokens)]
    public List<FacebookBusinessIntegrationSystemUserAccessToken>? FacebookBusinessIntegrationSystemUserAccessTokens { get; set; }

    [JsonProperty(PropertyNameFacebookWabaBusinessDetailSnapshot)]
    public Dictionary<string, object?>? FacebookWabaBusinessDetailSnapshot { get; set; }

    [JsonProperty(PropertyNameFacebookWabaSnapshot)]
    public Dictionary<string, object?>? FacebookWabaSnapshot { get; set; }

    [JsonProperty(PropertyNameWabaPhoneNumbers)]
    public HashSet<WabaPhoneNumber> WabaPhoneNumbers { get; set; }

    [JsonProperty("messaging_function_limitation")]
    public string? MessagingFunctionLimitation { get; set; }

    [JsonProperty(PropertyNameWabaActiveProductCatalog)]
    public WabaProductCatalog? WabaActiveProductCatalog { get; set; }

    [JsonProperty(PropertyNameWabaProductCatalog)]
    public WabaProductCatalog? WabaProductCatalog { get; set; }

    [JsonProperty(PropertyNameWabaDataset)]
    public WabaDataset? WabaDataset { get; set; }

    [JsonProperty("record_status")]
    public string? RecordStatus { get; set; }

    [JsonProperty("created_by")]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(PropertyName = IHasETag.PropertyNameETag, NullValueHandling = NullValueHandling.Ignore)]
    public string? ETag { get; set; }

    [JsonProperty(PropertyNameMarketingMessagesLiteApiStatus)]
    public string? MarketingMessagesLiteApiStatus { get; set; }

    [JsonConstructor]
    public Waba(
        string id,
        string facebookWabaId,
        string facebookWabaBusinessId,
        string? facebookBusinessId,
        List<string> sleekflowCompanyIds,
        string? facebookWabaName,
        string? facebookWabaAccountReviewStatus,
        string? facebookWabaBusinessName,
        string? facebookWabaPrimaryFundingId,
        string? facebookWabaMessageTemplateNamespace,
        WabaLongLivedAccessToken? facebookWabaUserLongLivedAccessToken,
        List<FacebookBusinessIntegrationSystemUserAccessToken>? facebookBusinessIntegrationSystemUserAccessTokens,
        Dictionary<string, object?>? facebookWabaBusinessDetailSnapshot,
        Dictionary<string, object?>? facebookWabaSnapshot,
        HashSet<WabaPhoneNumber> wabaPhoneNumbers,
        string? recordStatus,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string? eTag = null,
        string? marketingMessagesLiteApiStatus = null)
        : base(
            id,
            SysTypeNames.Waba)
    {
        FacebookWabaId = facebookWabaId;
        FacebookWabaBusinessId = facebookWabaBusinessId;
        FacebookBusinessId = facebookBusinessId ?? FacebookWabaBusinessId;
        SleekflowCompanyIds = sleekflowCompanyIds;
        FacebookWabaName = facebookWabaName;
        FacebookWabaAccountReviewStatus = facebookWabaAccountReviewStatus;
        FacebookWabaBusinessName = facebookWabaBusinessName;
        FacebookWabaPrimaryFundingId = facebookWabaPrimaryFundingId;
        FacebookWabaMessageTemplateNamespace = facebookWabaMessageTemplateNamespace;
        FacebookWabaUserLongLivedAccessToken = facebookWabaUserLongLivedAccessToken;
        FacebookBusinessIntegrationSystemUserAccessTokens = facebookBusinessIntegrationSystemUserAccessTokens;
        FacebookWabaBusinessDetailSnapshot = facebookWabaBusinessDetailSnapshot;
        FacebookWabaSnapshot = facebookWabaSnapshot;
        WabaPhoneNumbers = wabaPhoneNumbers;
        RecordStatus = recordStatus;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        ETag = eTag;
        MarketingMessagesLiteApiStatus = marketingMessagesLiteApiStatus;
    }
}