using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Workflows;

public class CompactWorkflow : IHasCreatedAt, IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Json<PERSON>roperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("triggers")]
    public WorkflowTriggers Triggers { get; set; }

    [JsonProperty("workflow_enrollment_settings")]
    public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

    [<PERSON><PERSON><PERSON><PERSON><PERSON>("workflow_schedule_settings")]
    public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public CompactWorkflow(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings? workflowEnrollmentSettings,
        WorkflowScheduleSettings? workflowScheduleSettings,
        DateTimeOffset createdAt)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        Triggers = triggers;
        WorkflowEnrollmentSettings = workflowEnrollmentSettings ?? WorkflowEnrollmentSettings.Default();
        WorkflowScheduleSettings = workflowScheduleSettings ?? WorkflowScheduleSettings.Default();
        CreatedAt = createdAt;
    }

    public CompactWorkflow(ProxyWorkflow workflow)
    {
        SleekflowCompanyId = workflow.SleekflowCompanyId;
        WorkflowId = workflow.WorkflowId;
        WorkflowVersionedId = workflow.WorkflowVersionedId;
        Triggers = workflow.Triggers;
        WorkflowEnrollmentSettings = workflow.WorkflowEnrollmentSettings;
        WorkflowScheduleSettings = workflow.WorkflowScheduleSettings;
        CreatedAt = workflow.CreatedAt;
    }
}