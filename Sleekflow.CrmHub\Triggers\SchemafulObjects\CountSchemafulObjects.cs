﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class CountSchemafulObjects : ITrigger<CountSchemafulObjects.CountSchemafulObjectsInput, CountSchemafulObjects.CountSchemafulObjectsOutput>
{
    private readonly ISchemafulObjectService _schemafulObjectService;

    public CountSchemafulObjects(ISchemafulObjectService schemafulObjectService)
    {
        _schemafulObjectService = schemafulObjectService;
    }

    public sealed class CountSchemafulObjectsFilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<SchemafulObjectQueryBuilder.SchemafulObjectFilter> Filters { get; set; }

        [JsonConstructor]
        public CountSchemafulObjectsFilterGroup(
            List<SchemafulObjectQueryBuilder.SchemafulObjectFilter> filters)
        {
            Filters = filters;
        }
    }

    public class CountSchemafulObjectsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [StringLength(1024)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty("is_search_indexed_property_values")]
        public bool IsSearchIndexedPropertyValues { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<CountSchemafulObjectsFilterGroup> FilterGroups { get; set; }

        [ValidateArray]
        [JsonProperty("array_exists")]
        public List<SchemafulObjectQueryBuilder.ArrayExist>? ArrayExists { get; set; }

        [JsonConstructor]
        public CountSchemafulObjectsInput(
            string sleekflowCompanyId,
            string schemaId,
            bool isSearchIndexedPropertyValues,
            List<CountSchemafulObjectsFilterGroup> filterGroups,
            List<SchemafulObjectQueryBuilder.ArrayExist>? arrayExists)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SchemaId = schemaId;
            IsSearchIndexedPropertyValues = isSearchIndexedPropertyValues;
            FilterGroups = filterGroups;
            ArrayExists = arrayExists;
        }
    }

    public class CountSchemafulObjectsOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public CountSchemafulObjectsOutput(long count)
        {
            Count = count;
        }
    }

    public async Task<CountSchemafulObjectsOutput> F(CountSchemafulObjectsInput countSchemafulObjectsInput)
    {
        var filterGroups = new List<SchemafulObjectQueryBuilder.FilterGroup>()
            .Concat(
                countSchemafulObjectsInput
                    .FilterGroups
                    .Select(
                        fg => new SchemafulObjectQueryBuilder.FilterGroup(
                            fg
                                .Filters
                                .Select(f => f)
                                .Cast<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>()
                                .ToList())))
            .ToList();

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>
            {
                new SchemafulObjectQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            filterGroups,
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            countSchemafulObjectsInput.SleekflowCompanyId,
            countSchemafulObjectsInput.SchemaId,
            countSchemafulObjectsInput.IsSearchIndexedPropertyValues,
            countSchemafulObjectsInput.ArrayExists);

        var rawRecords = await _schemafulObjectService.GetFilteredRawRecordsAsync(queryDefinition, 1);

        return new CountSchemafulObjectsOutput((long) rawRecords[0]["count"]!);
    }
}