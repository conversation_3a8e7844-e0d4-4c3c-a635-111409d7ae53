﻿using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using static Sleekflow.Persistence.AuditEntity;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubUsageService
{
    public Task RecordUsageAsync(
        string sleekflowCompanyId,
        string featureName,
        SleekflowStaff? sleekflowStaff,
        IntelligentHubUsageSnapshot? intelligentHubUsageSnapshot = null);

    public Task<Dictionary<string, int>> GetFeatureUsagesAsync(
        string sleekflowCompanyId,
        HashSet<string> featureNames,
        IntelligentHubUsageFilter? intelligentHubUsageFilter);

    public Task<bool> IsUsageLimitExceeded(
        string sleekflowCompanyId,
        Dictionary<string, int> featureUsageLimits,
        IntelligentHubUsageFilter? intelligentHubUsageFilter);

    public int GetFeatureTotalUsageLimit(IntelligentHubConfig intelligentHubConfig, string featureName);
}

public class IntelligentHubUsageService : IIntelligentHubUsageService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ICacheService _cacheService;
    private readonly IIntelligentHubUsageRepository _intelligentHubUsageRepository;
    private readonly ILogger<IntelligentHubUsageService> _logger;

    public IntelligentHubUsageService(
        IIdService idService,
        ICacheService cacheService,
        IIntelligentHubUsageRepository intelligentHubUsageRepository,
        ILogger<IntelligentHubUsageService> logger)
    {
        _idService = idService;
        _cacheService = cacheService;
        _intelligentHubUsageRepository = intelligentHubUsageRepository;
        _logger = logger;
    }

    public async Task RecordUsageAsync(
        string sleekflowCompanyId,
        string featureName,
        SleekflowStaff? sleekflowStaff,
        IntelligentHubUsageSnapshot? intelligentHubUsageSnapshot = null)
    {
        await _intelligentHubUsageRepository.UpsertAsync(
            new IntelligentHubUsage(
                featureName,
                intelligentHubUsageSnapshot,
                _idService.GetId(SysTypeNames.IntelligentHubUsage),
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowCompanyId,
                sleekflowStaff),
            sleekflowCompanyId);
    }

    public async Task<Dictionary<string, int>> GetFeatureUsagesAsync(
        string sleekflowCompanyId,
        HashSet<string> featureNames,
        IntelligentHubUsageFilter? intelligentHubUsageFilter)
    {
        var usageCountDict = new Dictionary<string, int>();
        foreach (var featureName in featureNames)
        {
            var count = featureName switch
            {
                PriceableFeatures.AiAgentsTotalUsage => await CountAiAgentsTotalUsageAsync(
                    sleekflowCompanyId,
                    intelligentHubUsageFilter),
                _ => await _intelligentHubUsageRepository.CountFeatureUsageAsync(
                    featureName,
                    sleekflowCompanyId,
                    intelligentHubUsageFilter)
            };

            // Just update the cache here
            var cacheKey = GetCacheKey(sleekflowCompanyId, featureName);

            await _cacheService.CacheAsync(
                cacheKey,
                () => Task.FromResult(count),
                TimeSpan.FromMinutes(5));

            usageCountDict.Add(featureName, count);
        }

        return usageCountDict;
    }

    public async Task<bool> IsUsageLimitExceeded(
        string sleekflowCompanyId,
        Dictionary<string, int> featureUsageLimits,
        IntelligentHubUsageFilter? intelligentHubUsageFilter)
    {
        _logger.LogInformation(
            "Checking usage limit for Company: {SleekflowCompanyId}, Feature Usage Limits: {UsageLimit}, Filter: {Filter}",
            sleekflowCompanyId,
            featureUsageLimits,
            JsonConvert.SerializeObject(intelligentHubUsageFilter));

        foreach (var featureUsageLimit in featureUsageLimits)
        {
            var featureName = featureUsageLimit.Key;
            var usageLimit = featureUsageLimit.Value;

            var usageCount = await _cacheService.CacheAsync(
                GetCacheKey(sleekflowCompanyId, featureName),
                async () =>
                {
                    return featureName switch
                    {
                        PriceableFeatures.AiAgentsTotalUsage => await CountAiAgentsTotalUsageAsync(
                            sleekflowCompanyId,
                            intelligentHubUsageFilter),
                        _ => await _intelligentHubUsageRepository.CountFeatureUsageAsync(
                            featureName,
                            sleekflowCompanyId,
                            intelligentHubUsageFilter)
                    };
                },
                TimeSpan.FromMinutes(5));

            _logger.LogInformation(
                "Feature: {FeatureName}, Usage Count: {UsageCount}, Usage Limit: {UsageLimit}",
                featureName,
                usageCount,
                usageLimit);

            if (usageCount < (int) (0.8 * usageLimit))
            {
                // No need to get accurate count if cached count not exceed 80%
                continue;
            }

            usageCount = featureName switch
            {
                PriceableFeatures.AiAgentsTotalUsage => await CountAiAgentsTotalUsageAsync(
                    sleekflowCompanyId,
                    intelligentHubUsageFilter),
                _ => await _intelligentHubUsageRepository.CountFeatureUsageAsync(
                    featureName,
                    sleekflowCompanyId,
                    intelligentHubUsageFilter)
            };

            if (usageCount > usageLimit)
            {
                return true;
            }
        }

        return false;
    }

    public int GetFeatureTotalUsageLimit(IntelligentHubConfig intelligentHubConfig, string featureName)
    {
        var totalUsageLimit = 0;

        if (intelligentHubConfig.UsageLimits.TryGetValue(featureName, out var featureUsageLimit))
        {
            totalUsageLimit += featureUsageLimit;
        }

        if (intelligentHubConfig.UsageLimitOffsets == null)
        {
            return totalUsageLimit;
        }

        if (intelligentHubConfig.UsageLimitOffsets.TryGetValue(featureName, out var featureUsageLimitOffset))
        {
            totalUsageLimit += featureUsageLimitOffset;
        }

        return totalUsageLimit;
    }

    private async Task<int> CountAiAgentsTotalUsageAsync(
        string sleekflowCompanyId,
        IntelligentHubUsageFilter? intelligentHubUsageFilter)
    {
        var totalUsage = 0;

        totalUsage += await CountAgentRecommendReplyFeatureTotalUsageAsync(
            sleekflowCompanyId,
            intelligentHubUsageFilter);

        return totalUsage;
    }

    private async Task<int> CountAgentRecommendReplyFeatureTotalUsageAsync(
        string sleekflowCompanyId,
        IntelligentHubUsageFilter? intelligentHubUsageFilter)
    {
        var totalUsage = 0;

        totalUsage += await _intelligentHubUsageRepository.CountAgentRecommendReplyFeatureUsageAsync(
            sleekflowCompanyId,
            [AgentCollaborationModes.LeadNurturing, AgentCollaborationModes.Long],
            intelligentHubUsageFilter) * 2;

        totalUsage += await _intelligentHubUsageRepository.CountAgentRecommendReplyFeatureUsageAsync(
            sleekflowCompanyId,
            [AgentCollaborationModes.Short],
            intelligentHubUsageFilter);

        return totalUsage;
    }

    private string GetCacheKey(string sleekflowCompanyId, string sObjectTypeName)
    {
        return $"{nameof(IntelligentHubUsageService)}-{sleekflowCompanyId}-{sObjectTypeName}";
    }
}