﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface IStorageConfig
{
    string ImageStorageConnStr { get; }

    string FileStorageConnStr { get; }
}

public class StorageConfig : IConfig, IStorageConfig
{
    public string ImageStorageConnStr { get; }

    public string FileStorageConnStr { get; }

    public StorageConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ImageStorageConnStr =
            Environment.GetEnvironmentVariable("IMAGE_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("IMAGE_STORAGE_CONN_STR");
        FileStorageConnStr =
            Environment.GetEnvironmentVariable("FILE_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("FILE_STORAGE_CONN_STR");
    }
}