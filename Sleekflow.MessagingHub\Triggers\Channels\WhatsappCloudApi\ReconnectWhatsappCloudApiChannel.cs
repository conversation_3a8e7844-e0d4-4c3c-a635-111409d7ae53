using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class ReconnectWhatsappCloudApiChannel
    : ITrigger<
        ReconnectWhatsappCloudApiChannel.ReconnectWhatsappCloudApiChannelInput,
        ReconnectWhatsappCloudApiChannel.ReconnectWhatsappCloudApiChannelOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<ReconnectWhatsappCloudApiChannel> _logger;

    public ReconnectWhatsappCloudApiChannel(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<ReconnectWhatsappCloudApiChannel> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class ReconnectWhatsappCloudApiChannelInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("webhook_url")]
        public string WebhookUrl { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ReconnectWhatsappCloudApiChannelInput(
            string sleekflowCompanyId,
            string wabaId,
            string wabaPhoneNumberId,
            string webhookUrl,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            WebhookUrl = webhookUrl;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ReconnectWhatsappCloudApiChannelOutput
    {
        [JsonProperty("connected_cloud_api_channel")]
        public ConnectedCloudApiChannelDto ConnectedCloudApiChannels { get; set; }

        [JsonConstructor]
        public ReconnectWhatsappCloudApiChannelOutput(ConnectedCloudApiChannelDto connectedCloudApiChannels)
        {
            ConnectedCloudApiChannels = connectedCloudApiChannels;
        }
    }

    public async Task<ReconnectWhatsappCloudApiChannelOutput> F(
        ReconnectWhatsappCloudApiChannelInput reconnectWhatsappCloudApiChannelInput)
    {
        var sleekflowCompanyId = reconnectWhatsappCloudApiChannelInput.SleekflowCompanyId;
        var wabaId = reconnectWhatsappCloudApiChannelInput.WabaId;
        var wabaPhoneNumberId = reconnectWhatsappCloudApiChannelInput.WabaPhoneNumberId;

        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            reconnectWhatsappCloudApiChannelInput.SleekflowStaffId,
            reconnectWhatsappCloudApiChannelInput.SleekflowStaffTeamIds);

        var waba = await _wabaService.GetActiveWabaWithIdAndWabaPhoneNumberIdAsync(
            wabaId,
            wabaPhoneNumberId,
            sleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("ReconnectCloudApiChannel");
        }

        var reconnectedWaba = await _channelService.ReconnectCloudApiChannel(
            waba,
            sleekflowCompanyId,
            wabaPhoneNumberId,
            reconnectWhatsappCloudApiChannelInput.WebhookUrl,
            sleekflowStaff);
        return new ReconnectWhatsappCloudApiChannelOutput(new ConnectedCloudApiChannelDto(reconnectedWaba));
    }
}