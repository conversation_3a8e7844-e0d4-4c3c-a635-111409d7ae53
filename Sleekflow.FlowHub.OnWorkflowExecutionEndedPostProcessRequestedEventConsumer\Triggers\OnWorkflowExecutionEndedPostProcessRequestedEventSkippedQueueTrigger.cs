﻿using Azure.Messaging.ServiceBus;
using MassTransit;
using Microsoft.Azure.Functions.Worker;

namespace Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.Triggers;

public class OnWorkflowExecutionEndedPostProcessRequestedEventSkippedQueueTrigger
{
    private readonly IMessageReceiver _messagereceiver;
    protected const string QueueName = "on-workflow-execution-ended-post-process-requested-event_skipped";

    public OnWorkflowExecutionEndedPostProcessRequestedEventSkippedQueueTrigger(
        IMessageReceiver messageReceiver)
    {
        _messagereceiver = messageReceiver;
    }

    [Function("OnWorkflowExecutionEndedPostProcessRequestedEventSkippedQueueTrigger")]
    public Task RunAsync(
        [ServiceBusTrigger(QueueName, Connection = "HIGH_TRAFFIC_SERVICE_BUS_CONN_STR")]
        ServiceBusReceivedMessage message,
        CancellationToken cancellationToken)
    {
        return _messagereceiver.HandleConsumer<Consumers.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer>(
            QueueName,
            message,
            cancellationToken);
    }
}