using Microsoft.Azure.Cosmos;
using ShopifySharp;
using ShopifySharp.Utilities;
using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Currencies;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.CommerceHub.Shopify;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Stores.ShopifyStores;

public interface IShopifyStoreService
{
    Task<Store> CreateAndGetOutOfBoxShopifyStoreAsync(
        string sleekflowCompanyId,
        string shopifyUrl,
        string shopAccessToken,
        bool isViewEnabled,
        bool isPaymentEnabled,
        StoreSubscriptionStatus? subscriptionStatus,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        ShopifySyncConfig shopifySyncConfig,
        ShopifyPaymentConfig shopifyPaymentConfig,
        ShopifySyncStatus? shopifySyncStatus,
        List<ShopifyMessageTemplate>? shopifyMessageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<Store> CreateAndGetPublicShopifyStoreAsync(
        string sleekflowCompanyId,
        string shopifyUrl,
        string authorizationCode,
        bool isViewEnabled,
        bool isPaymentEnabled,
        StoreSubscriptionStatus? subscriptionStatus,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        ShopifySyncConfig shopifySyncConfig,
        ShopifyPaymentConfig shopifyPaymentConfig,
        ShopifySyncStatus? shopifySyncStatus,
        List<ShopifyMessageTemplate>? shopifyMessageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<ShopifyStoreIntegrationExternalConfig> GetShopifyStoreIntegrationExternalConfigAsync(
        Store shopifyStore);

    Task<ShopifyStoreIntegrationExternalConfig> UpdateShopifyStoreIntegrationExternalConfigAsync(
        Store shopifyStore,
        string integrationStatus,
        ShopifySyncConfig syncConfig,
        ShopifyPaymentConfig paymentConfig,
        ShopifySyncStatus? syncStatus,
        List<ShopifyMessageTemplate>? messageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId);
}

public class ShopifyStoreService : IShopifyStoreService, IScopedService
{
    private readonly IStoreService _storeService;
    private readonly IIdService _idService;
    private readonly ICurrencyService _currencyService;
    private readonly IShopifySecretConfig _shopifySecretConfig;
    private readonly IStoreRepository _storeRepository;

    public ShopifyStoreService(
        IStoreService storeService,
        IIdService idService,
        ICurrencyService currencyService,
        IShopifySecretConfig shopifySecretConfig,
        IStoreRepository storeRepository)
    {
        _storeService = storeService;
        _idService = idService;
        _currencyService = currencyService;
        _shopifySecretConfig = shopifySecretConfig;
        _storeRepository = storeRepository;
    }

    public async Task<Store> CreateAndGetOutOfBoxShopifyStoreAsync(
        string sleekflowCompanyId,
        string shopifyUrl,
        string shopAccessToken,
        bool isViewEnabled,
        bool isPaymentEnabled,
        StoreSubscriptionStatus? subscriptionStatus,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        ShopifySyncConfig shopifySyncConfig,
        ShopifyPaymentConfig shopifyPaymentConfig,
        ShopifySyncStatus? shopifySyncStatus,
        List<ShopifyMessageTemplate>? shopifyMessageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        await ShopifyClientValidator.AssertValidShopifyCredentialAsync(
            shopifyUrl,
            shopAccessToken);

        return await CreateAndGetShopifyStoreAsync(
            sleekflowCompanyId,
            "OutOfBox",
            shopifyUrl,
            shopAccessToken,
            isViewEnabled,
            isPaymentEnabled,
            subscriptionStatus,
            metadata,
            templateDict,
            shopifySyncConfig,
            shopifyPaymentConfig,
            shopifySyncStatus,
            shopifyMessageTemplates,
            isShopifyBillingOwner,
            chargeUpdatedAt,
            chargeId,
            sleekflowStaffId,
            sleekflowStaffTeamIds);
    }

    public async Task<Store> CreateAndGetPublicShopifyStoreAsync(
        string sleekflowCompanyId,
        string shopifyUrl,
        string authorizationCode,
        bool isViewEnabled,
        bool isPaymentEnabled,
        StoreSubscriptionStatus? subscriptionStatus,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        ShopifySyncConfig shopifySyncConfig,
        ShopifyPaymentConfig shopifyPaymentConfig,
        ShopifySyncStatus? shopifySyncStatus,
        List<ShopifyMessageTemplate>? shopifyMessageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var clientId = GetClientId();
        var clientSecret = GetClientSecret();

        var shopifyOauthUtility = new ShopifyOauthUtility();

        var shopAccessToken = (await shopifyOauthUtility.AuthorizeAsync(
            authorizationCode,
            shopifyUrl,
            clientId,
            clientSecret)).AccessToken;

        await ShopifyClientValidator.AssertValidShopifyCredentialAsync(
            shopifyUrl,
            shopAccessToken);

        return await CreateAndGetShopifyStoreAsync(
            sleekflowCompanyId,
            "Public",
            shopifyUrl,
            shopAccessToken,
            isViewEnabled,
            isPaymentEnabled,
            subscriptionStatus,
            metadata,
            templateDict,
            shopifySyncConfig,
            shopifyPaymentConfig,
            shopifySyncStatus,
            shopifyMessageTemplates,
            isShopifyBillingOwner,
            chargeUpdatedAt,
            chargeId,
            sleekflowStaffId,
            sleekflowStaffTeamIds);
    }

    private async Task<Store> CreateAndGetShopifyStoreAsync(
        string sleekflowCompanyId,
        string integrationType,
        string shopifyUrl,
        string shopAccessToken,
        bool isViewEnabled,
        bool isPaymentEnabled,
        StoreSubscriptionStatus? subscriptionStatus,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        ShopifySyncConfig shopifySyncConfig,
        ShopifyPaymentConfig shopifyPaymentConfig,
        ShopifySyncStatus? shopifySyncStatus,
        List<ShopifyMessageTemplate>? shopifyMessageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        await ShopifyClientValidator.AssertValidShopifyCredentialAsync(
            shopifyUrl,
            shopAccessToken);

        var shopService = new ShopService(shopifyUrl, shopAccessToken);

        var shop = await shopService.GetAsync();

        var names = new List<Multilingual>
        {
            new ("en", shop.Name)
        };

        var descriptions = new List<Description>
        {
            new (DescriptionTypes.Text, new Multilingual(
                    "en", shop.Description),
                null,
                null)
        };

        // Shopify has no definition for shop languages, so we use the default language
        var languageCultureInfo = CultureUtils.GetCultureInfoByLanguageIsoCode("en")!;
        var languages = new List<Language>
        {
            new ("en", languageCultureInfo.EnglishName, languageCultureInfo.NativeName, true)
        };

        var currencies = _currencyService.GetCurrencies()
            .Where(c => c.CurrencyIsoCode == shop.Currency)
            .ToList();

        var shopifyStoreIntegrationExternalConfig = new ShopifyStoreIntegrationExternalConfig(
            shopifyUrl,
            shopAccessToken,
            integrationType,
            "Connected",
            shopifySyncConfig,
            shopifyPaymentConfig,
            shopifySyncStatus,
            shopifyMessageTemplates,
            isShopifyBillingOwner,
            chargeUpdatedAt,
            chargeId);

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            sleekflowStaffId,
            sleekflowStaffTeamIds);

        var shopifyStore = await _storeService.CreateStoreAsync(
            new Store(
                _idService.GetId(SysTypeNames.Store),
                sleekflowCompanyId,
                names,
                descriptions,
                shopifyUrl,
                PlatformData.Shopify(),
                isViewEnabled,
                isPaymentEnabled,
                languages,
                currencies,
                shopifyStoreIntegrationExternalConfig,
                subscriptionStatus,
                new List<string>
                {
                    "Active"
                },
                metadata,
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                templateDict),
            sleekflowCompanyId);

        return shopifyStore;
    }

    public async Task<ShopifyStoreIntegrationExternalConfig> UpdateShopifyStoreIntegrationExternalConfigAsync(
        Store shopifyStore,
        string integrationStatus,
        ShopifySyncConfig syncConfig,
        ShopifyPaymentConfig paymentConfig,
        ShopifySyncStatus? syncStatus,
        List<ShopifyMessageTemplate>? messageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId)
    {
        await ShopifyStoreValidator.AssertValidShopifyStoreExternalIntegrationConfigAsync(shopifyStore);

        var shopifyStoreIntegrationExternalConfig = (shopifyStore.StoreIntegrationExternalConfig as ShopifyStoreIntegrationExternalConfig)!;
        shopifyStoreIntegrationExternalConfig.IntegrationStatus = integrationStatus;
        shopifyStoreIntegrationExternalConfig.SyncConfig = syncConfig;
        shopifyStoreIntegrationExternalConfig.PaymentConfig = paymentConfig;
        shopifyStoreIntegrationExternalConfig.SyncStatus = syncStatus;
        shopifyStoreIntegrationExternalConfig.MessageTemplates = messageTemplates;
        shopifyStoreIntegrationExternalConfig.IsShopifyBillingOwner = isShopifyBillingOwner;
        shopifyStoreIntegrationExternalConfig.ChargeUpdatedAt = chargeUpdatedAt;
        shopifyStoreIntegrationExternalConfig.ChargeId = chargeId;

        var updatedShopifyStore = await _storeRepository.PatchAndGetAsync(
            shopifyStore.Id,
            shopifyStore.SleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/store_integration_external_config", shopifyStoreIntegrationExternalConfig)
            });

        await ShopifyStoreValidator.AssertValidShopifyStoreExternalIntegrationConfigAsync(updatedShopifyStore);

        return (updatedShopifyStore.StoreIntegrationExternalConfig as ShopifyStoreIntegrationExternalConfig)!;
    }

    public async Task<ShopifyStoreIntegrationExternalConfig> GetShopifyStoreIntegrationExternalConfigAsync(
        Store shopifyStore)
    {
        await ShopifyStoreValidator.AssertValidShopifyStoreExternalIntegrationConfigAsync(shopifyStore);

        return (shopifyStore.StoreIntegrationExternalConfig as ShopifyStoreIntegrationExternalConfig)!;
    }

    private string GetClientId() => _shopifySecretConfig.ShopifyClientId;

    private string GetClientSecret() => _shopifySecretConfig.ShopifyClientSecret;
}