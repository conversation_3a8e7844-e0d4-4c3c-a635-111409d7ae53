using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class ReplyInstagramMediaCommentStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.reply-instagram-media-comment";

    [Required]
    [JsonProperty("instagram_page_id__expr")]
    public string InstagramPageIdExpr { get; set; }

    [Required]
    [JsonProperty("instagram_comment_id__expr")]
    public string InstagramCommentIdExpr { get; set; }

    [Required]
    [JsonProperty("comment_body__expr")]
    public string CommentBodyExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.InstagramIntegration;

    [JsonConstructor]
    public ReplyInstagramMediaCommentStepArgs(string instagramPageIdExpr, string instagramCommentIdExpr, string commentBodyExpr)
    {
        InstagramPageIdExpr = instagramPageIdExpr;
        InstagramCommentIdExpr = instagramCommentIdExpr;
        CommentBodyExpr = commentBodyExpr;
    }
}