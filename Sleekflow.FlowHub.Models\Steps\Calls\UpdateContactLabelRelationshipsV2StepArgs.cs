﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactLabelRelationshipsV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-contact-label-relationships";

    [JsonProperty("labels")]
    public List<string>? Labels { get; set; }

    [JsonProperty("removal_action_type")]
    public string? RemovalActionType { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactLabelRelationshipsV2StepArgs(
        List<string>? labels,
        string? removalActionType)
    {
        Labels = labels;
        RemovalActionType = removalActionType;
    }
}