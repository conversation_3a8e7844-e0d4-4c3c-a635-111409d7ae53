using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Cores;

[SwaggerInclude]
public class GetUserProfileInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonConstructor]
    public GetUserProfileInput(string sleekflowCompanyId, string phoneNumber)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        PhoneNumber = phoneNumber;
    }
}

[SwaggerInclude]
public class GetUserProfileOutput : UserProfile
{
    [JsonConstructor]
    public GetUserProfileOutput(string id, string sleekflowCompanyId, string phoneNumber)
        : base(id, sleekflowCompanyId, phoneNumber)
    {
    }
}