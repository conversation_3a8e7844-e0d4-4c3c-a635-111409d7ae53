<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.1</TargetFramework>
        <LangVersion>10</LangVersion>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow\Sleekflow.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="GraphApi.Client">
        <HintPath>..\Sleekflow.MessagingHub\Binaries\GraphApi.Client\GraphApi.Client.dll</HintPath>
      </Reference>
      <Reference Include="GraphApi.Client.Const">
        <HintPath>..\Sleekflow.MessagingHub\Binaries\GraphApi.Client\GraphApi.Client.Const.dll</HintPath>
      </Reference>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Stripe.net" Version="41.27.0" />
    </ItemGroup>

</Project>
