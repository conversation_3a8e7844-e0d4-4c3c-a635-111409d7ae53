using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.PublicApiGatewayDb;

namespace Sleekflow;

#if SWAGGERGEN
using Moq;
#endif

public static class PublicApiGatewayModules
{
    public static void BuildMessagingHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON>d<PERSON><IPublicApiGatewayDbConfig>(new Mock<IPublicApiGatewayDbConfig>().Object);
        b.<PERSON>d<PERSON><IPublicApiGatewayDbResolver>(new Mock<IPublicApiGatewayDbResolver>().Object);

#else
        var publicApiGatewayDbConfig = new PublicApiGatewayDbConfig();

        b.<PERSON>d<PERSON><IPublicApiGatewayDbConfig>(publicApiGatewayDbConfig);
        b.<PERSON><PERSON><PERSON><IPublicApiGatewayDbResolver, PublicApiGatewayDbResolver>();
#endif
    }
}