using System.Collections.Immutable;
using Pulumi;
using Pulumi.AzureNative.DocumentDB.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.FlowHub;

public class FlowHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public FlowHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class FlowHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public FlowHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public FlowHubDbOutput InitFlowHubDb()
    {
        const string cosmosDbId = "flowhubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs()
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.FlowHubDb.IFlowHubDbService
        var containerParams = new ContainerParam[]
        {
            new (
                "state",
                "state",
                new List<string>
                {
                    "/id"
                },
                Ttl: -1,
                MaxThroughput: _myConfig.Name == "production" ? 100000 : 1000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/identity/sleekflow_company_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/identity/workflow_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/created_at"
                        }
                    }.ToImmutableArray()
                }
            ),
            new (
                "state_subscription",
                "state_subscription",
                new List<string>
                {
                    "/state_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),

            new (
                "workflow",
                "workflow",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 40000 : 1000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                    {
                        new()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Descending,
                            Path = "/created_at"
                        },
                        new()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/workflow_id"
                        }
                    }.ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[]
                    {
                        new()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/workflow_id"
                        },
                        new()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/created_at"
                        }
                    }.ToImmutableArray()
                }
            ),
            new (
                "workflow_execution",
                "workflow_execution",
                new List<string>
                {
                    "/state_identity/workflow_id", "/state_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 40000 : 1000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending,
                                Path = "/state_identity/workflow_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/workflow_execution_status"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/created_at"
                            }
                        }
                        .ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[] // For company monthly execution statistics
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/sleekflow_company_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/created_at"
                        }
                    }.ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[]
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/sleekflow_company_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/workflow_type"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/created_at"
                        }
                    }.ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[] // For workflow statistics in workflow listing view
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/state_identity/workflow_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/workflow_execution_status"
                        }
                    }.ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[] // For versioned workflow statistics in workflow listing view
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/state_identity/workflow_versioned_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/workflow_execution_status"
                        }
                    }.ToImmutableArray()
                }
            ),
            new (
                "workflow_webhook_trigger",
                "workflow_webhook_trigger",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),

            new (
                "step_execution",
                "step_execution",
                new List<string>
                {
                    "/state_identity/workflow_id", "/step_id", "/state_id"
                },
                Ttl: -1,
                MaxThroughput: _myConfig.Name == "production" ? 80000 : 1000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[] // Index for step execution update ttl
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/sleekflow_company_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/state_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/created_at"
                        }
                    }
                    .ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[] // Index for state step node execution count
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/sleekflow_company_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/state_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/step_id"
                        }
                    }
                    .ToImmutableArray()
                }
            ),

            new (
                "flow_hub_event",
                "flow_hub_event",
                new List<string>
                {
                    "/sleekflow_company_id", "/object_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "flow_hub_config",
                "flow_hub_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "workflow_group",
                "workflow_group",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new UniqueKeyArgs()
                    {
                        Paths = "/name",
                    }
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "workflow_step_category_statistics",
                "workflow_step_category_statistics",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "state_step_category_statistics",
                "state_step_category_statistics",
                new List<string>
                {
                    "/sleekflow_company_id",
                    "/state_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "email_sent_record",
                "email_sent_record",
                new List<string>
                {
                    "/id"
                },
                Ttl: -1,
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "aggregate_saga_instances",
                "aggregate_saga_instances",
                new List<string>
                {
                    "/id"
                },
                Ttl: -1,
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "streaming_recommended_reply_saga_instances",
                "streaming_recommended_reply_saga_instances",
                new List<string>
                {
                    "/id"
                },
                Ttl: -1,
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000
            ),
            new (
                "workflow_agent_config_mapping",
                "workflow_agent_config_mapping",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                    {
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/workflow_id"
                        },
                        new ()
                        {
                            Order = DocumentDB.CompositePathSortOrder.Ascending,
                            Path = "/agent_config_id"
                        }
                    }.ToImmutableArray()
                }
            ),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new FlowHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}