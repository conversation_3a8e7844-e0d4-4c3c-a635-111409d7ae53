using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows.Settings;

public class ContactPropertyDateTimeSettings : BaseDateTimeSettings
{
    [JsonConstructor]
    public ContactPropertyDateTimeSettings(
        string? contactPropertyId,
        string? triggerDateType,
        string[] triggerDateDuration,
        string? triggerTimeType,
        string? triggerCustomTime)

        : base(triggerDateType, triggerDateDuration, triggerTimeType, triggerCustomTime)
    {
        ContactPropertyId = contactPropertyId;
    }

    [Required]
    [JsonProperty("contact_property_id")]
    public string? ContactPropertyId { get; set; }
}

