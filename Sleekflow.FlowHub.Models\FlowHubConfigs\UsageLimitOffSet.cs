using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Models.FlowHubConfigs;

public class UsageLimitOffset
{
    [JsonProperty(UsageLimitOffsetFieldNames.PropertyNameMaximumNumOfActiveWorkflowsOffset)]
    public int? MaximumNumOfActiveWorkflowsOffset { get; set; }

    [JsonProperty(UsageLimitOffsetFieldNames.PropertyNameMaximumNumOfNodesPerWorkflowOffset)]
    public int? MaximumNumOfNodesPerWorkflowOffset { get; set; }

    [JsonProperty(UsageLimitOffsetFieldNames.PropertyNameMaximumNumOfMonthlyWorkflowExecutionsOffset)]
    public int? MaximumNumOfMonthlyWorkflowExecutionsOffset { get; set; }


    [JsonConstructor]
    public UsageLimitOffset(
        int? maximumNumOfActiveWorkflowsOffset,
        int? maximumNumOfNodesPerWorkflowOffset,
        int? maximumNumOfMonthlyWorkflowExecutionsOffset)
    {
        MaximumNumOfActiveWorkflowsOffset = maximumNumOfActiveWorkflowsOffset;
        MaximumNumOfNodesPerWorkflowOffset = maximumNumOfNodesPerWorkflowOffset;
        MaximumNumOfMonthlyWorkflowExecutionsOffset = maximumNumOfMonthlyWorkflowExecutionsOffset;
    }
}