using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AgentSummarizeStepArgs : TypedCallStepArgs, IHasCompanyAgentConfigIdExpr
{
    public const string CallName = "sleekflow.v1.agent-summarize";

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty("handover_reason__expr")]
    public string HandoverReasonExpr { get; set; }

    [JsonProperty("language_iso_code__expr")]
    public string LanguageIsoCodeExpr { get; set; }

    [JsonProperty(IHasCompanyAgentConfigIdExpr.PropertyNameCompanyAgentConfigIdExpr)]
    public string? CompanyAgentConfigIdExpr { get; set; }

    [JsonProperty("retrieval_window_timestamp__expr")]
    public string? RetrievalWindowTimestampExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonConstructor]
    public AgentSummarizeStepArgs(
        string contactIdExpr,
        string handoverReasonExpr,
        string languageIsoCodeExpr,
        string? companyAgentConfigIdExpr,
        string? retrievalWindowTimestampExpr = null)
    {
        ContactIdExpr = contactIdExpr;
        HandoverReasonExpr = handoverReasonExpr;
        LanguageIsoCodeExpr = languageIsoCodeExpr;
        CompanyAgentConfigIdExpr = companyAgentConfigIdExpr;
        RetrievalWindowTimestampExpr = retrievalWindowTimestampExpr;
    }
}