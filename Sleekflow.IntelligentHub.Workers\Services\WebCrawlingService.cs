using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using HtmlAgilityPack;
using Microsoft.Extensions.Logging;
// using Microsoft.Playwright; // Commented out due to deployment issues
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;

namespace Sleekflow.IntelligentHub.Workers.Services;

public interface IWebCrawlingService
{
    Task<CrawlingResult> ScrapeUrlAsync(string url);

    Task<WebCrawlingBatchResult> CrawlBatchAsync(
        string baseUrl,
        List<string> urlsToCrawl,
        HashSet<string> processedUrls,
        int batchSize);
}

public class WebCrawlingService : IScopedService, IWebCrawlingService
{
    private readonly ILogger<WebCrawlingService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IDocumentStatisticsCalculatorFactory _documentStatisticsCalculatorFactory;

    public WebCrawlingService(
        ILogger<WebCrawlingService> logger,
        IHttpClientFactory httpClientFactory,
        IDocumentStatisticsCalculatorFactory documentStatisticsCalculatorFactory)
    {
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("default-intelligent-hub-handler");
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "Sleekflow-WebCrawler/1.0 (Web Crawler)");
        _documentStatisticsCalculatorFactory = documentStatisticsCalculatorFactory;
    }

    public async Task<CrawlingResult> ScrapeUrlAsync(string url)
    {
        try
        {
            _logger.LogInformation("Scraping single URL: {Url}", url);

            var crawlPageResult = await CrawlPageAsync(url);

            _logger.LogInformation(
                "Successfully scraped URL: {Url}, title: {Title}, content length: {ContentLength}",
                url,
                crawlPageResult.CrawlingResult.WebpageTitle,
                crawlPageResult.CrawlingResult.CharacterCount);

            return crawlPageResult.CrawlingResult;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to scrape URL: {Url}", url);
            throw;
        }
    }

    /**
     * @param baseUrl The base url e.g. http://example.com
     * @param urlsToCrawl The list of urls where we haven't crawled yet. Initially it should contain baseUrl [http://example.com]
     * @param processedUrls The crawled urls
     * @param batchSize Number of links to crawl in this batch
     */
    public async Task<WebCrawlingBatchResult> CrawlBatchAsync(
        string baseUrl,
        List<string> urlsToCrawl,
        HashSet<string> processedUrls,
        int batchSize)
    {
        var decodedBaseUrl = Uri.UnescapeDataString(baseUrl);
        var crawlerResults = new ConcurrentBag<CrawlingResult>();

        // hardcode 1000 for now
        var remainingLimit = 1000 - processedUrls.Count;
        var currentBatchSize = Math.Min(batchSize, remainingLimit);
        var urlsToProcess = urlsToCrawl.Skip(currentBatchSize).ToList();

        // Take only the batch size or remaining URLs
        var batchUrls = urlsToCrawl.Take(currentBatchSize).ToList();

        _logger.LogInformation(
            "Starting web crawling batch for {UrlCount} URLs from base: {BaseUrl}",
            batchUrls.Count,
            decodedBaseUrl);

        await Parallel.ForEachAsync(
            batchUrls,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 8
            },
            async (urlToProcess, token) =>
            {
                try
                {
                    var crawlerResult = await CrawlPageAsync(urlToProcess);
                    crawlerResults.Add(crawlerResult.CrawlingResult);

                    // Process links from this page to find new URLs
                    foreach (var newUrl in crawlerResult.LinksInPage)
                    {
                        var absoluteUrl = new Uri(new Uri(decodedBaseUrl), newUrl).ToString();
                        var normalizedUrl = NormalizeUrl(absoluteUrl);

                        if (!normalizedUrl.StartsWith(decodedBaseUrl) ||
                            processedUrls.Contains(normalizedUrl) ||
                            batchUrls.Contains(normalizedUrl) ||
                            urlsToProcess.Contains(normalizedUrl))
                        {
                            continue;
                        }

                        lock (urlsToProcess)
                        {
                            if (!urlsToProcess.Contains(normalizedUrl))
                            {
                                urlsToProcess.Add(normalizedUrl);
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    // failure is not fatal, maybe the link is broken etc.
                    _logger.LogWarning("Failed to process URL {NewUrl}: {Error}", urlToProcess, e.Message);
                }
            });

        _logger.LogInformation(
            "Completed web crawling batch. Processed {ProcessedCount} URLs, found {NewUrlCount} new URLs",
            crawlerResults.Count,
            urlsToProcess.Count);

        return new WebCrawlingBatchResult(
            crawlerResults.ToList(),
            urlsToProcess);
    }

    private class CrawlPageResult
    {
        [JsonProperty("crawling_result")]
        public CrawlingResult CrawlingResult { get; set; }

        [JsonProperty(PropertyName = "links_in_page")]
        public string[] LinksInPage { get; set; }

        [JsonConstructor]
        public CrawlPageResult(CrawlingResult crawlingResult, string[] linksInPage)
        {
            CrawlingResult = crawlingResult;
            LinksInPage = linksInPage;
        }
    }

    private async Task<CrawlPageResult> CrawlPageAsync(string currentUrl)
    {
        _logger.LogInformation("Crawling page: {Url}", currentUrl);

        // Replaced Playwright with HttpClient
        var response = await _httpClient.GetAsync(currentUrl);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Failed to load page {currentUrl}, status: {response.StatusCode}");
        }

        var contentType = response.Content.Headers.ContentType?.MediaType ?? string.Empty;
        var contentLength = response.Content.Headers.ContentLength ?? 0;

        if (contentLength > 1024 * 1024 * 25)
        {
            throw new Exception($"Failed to load page {currentUrl}, content exceeds 25 MB ({contentLength} bytes)");
        }

        // Handle non-HTML content
        if (!contentType.Contains("text/html"))
        {
            var fallbackTitle = ExtractTitleFromUrl(currentUrl); // Fallback title from URL

            // Get the appropriate calculator based on content type
            var calculator =
                _documentStatisticsCalculatorFactory.CreateDocumentStatisticsCalculatorByMimeType(contentType);
            var contentStream = await response.Content.ReadAsStreamAsync();
            var documentStatistics = calculator.CalculateDocumentStatistics(contentStream);
            var fileCharacterCount = documentStatistics.TotalCharacters;

            return new CrawlPageResult(new CrawlingResult(currentUrl, fileCharacterCount, fallbackTitle), []);
        }

        var htmlContent = await response.Content.ReadAsStringAsync();

        // Extract page title using regex
        var title = ExtractTitleFromHtml(htmlContent) ?? ExtractTitleFromUrl(currentUrl);

        // Extract links from HTML using regex
        var links = ExtractLinksFromHtml(htmlContent);

        // Calculate character count from actual text content
        var textContent = ExtractTextContent(htmlContent);
        var characterCount = textContent.Length;

        _logger.LogInformation(
            "Successfully crawled page: {Url}, title: {Title}, found {LinkCount} links",
            currentUrl,
            title,
            links.Count);

        return new CrawlPageResult(new CrawlingResult(currentUrl, characterCount, title), links.ToArray());

        // Commented out Playwright-based implementation due to deployment issues
        /*
        using var playwright = await Playwright.CreateAsync();
        await using var browser = await playwright.Chromium.LaunchAsync(
            new BrowserTypeLaunchOptions
            {
                Headless = true,
            });

        var page = await browser.NewPageAsync(
            new BrowserNewPageOptions()
            {
                JavaScriptEnabled = true
            });

        var response = await page.GotoAsync(
            currentUrl,
            new PageGotoOptions()
            {
                Timeout = 30000
            });

        if (response == null || response.Status != 200)
        {
            throw new Exception($"Failed to load page {currentUrl}, status: {response?.Status}");
        }

        // Extract page title
        var title = await page.TitleAsync();

        if (!response.Headers["Content-Type"].Contains("text/html"))
        {
            var length = int.Parse(response.Headers["Content-Length"]);
            return new CrawlPageResult(new CrawlingResult(currentUrl, length, title), []);
        }

        var content = await page.ContentAsync();

        // Extract links from the page
        var linkElements = await page.QuerySelectorAllAsync("a");
        var links = new List<string>();

        foreach (var linkElement in linkElements)
        {
            var href = await linkElement.GetAttributeAsync("href");
            if (!string.IsNullOrEmpty(href))
            {
                links.Add(href);
            }
        }

        await page.CloseAsync();
        await browser.CloseAsync();

        _logger.LogInformation(
            "Successfully crawled page: {Url}, title: {Title}, found {LinkCount} links",
            currentUrl,
            title,
            links.Count);

        return new CrawlPageResult(new CrawlingResult(currentUrl, content.Length, title), links.ToArray());
        */
    }

    private string? ExtractTitleFromHtml(string htmlContent)
    {
        try
        {
            var titleMatch = Regex.Match(htmlContent, @"<title[^>]*>([^<]*)</title>", RegexOptions.IgnoreCase);
            if (titleMatch.Success)
            {
                return System.Net.WebUtility.HtmlDecode(titleMatch.Groups[1].Value.Trim());
            }
        }
        catch (Exception e)
        {
            _logger.LogWarning("Failed to extract title from HTML: {Error}", e.Message);
        }

        return null;
    }

    private string ExtractTitleFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host + uri.PathAndQuery;
        }
        catch
        {
            return url;
        }
    }

    private List<string> ExtractLinksFromHtml(string htmlContent)
    {
        var links = new List<string>();

        try
        {
            // Extract href attributes from anchor tags
            var linkMatches = Regex.Matches(
                htmlContent,
                @"<a[^>]+href\s*=\s*[""']([^""']+)[""'][^>]*>",
                RegexOptions.IgnoreCase);

            foreach (Match match in linkMatches)
            {
                var href = match.Groups[1].Value;
                if (!string.IsNullOrWhiteSpace(href))
                {
                    links.Add(System.Net.WebUtility.HtmlDecode(href));
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogWarning("Failed to extract links from HTML: {Error}", e.Message);
        }

        return links;
    }

    private string ExtractTextContent(string htmlContent)
    {
        try
        {
            var doc = new HtmlDocument();
            doc.LoadHtml(htmlContent);

            // Remove script, style, and other non-visible elements
            var nodesToRemove = doc.DocumentNode.SelectNodes(
                "//script | //style | //noscript | //iframe | //object | //embed | //applet | //canvas | //svg | //math | //template | //head | //meta | //link | //base");
            if (nodesToRemove != null)
            {
                foreach (var node in nodesToRemove)
                {
                    node.Remove();
                }
            }

            // Remove hidden elements
            var hiddenNodes = doc.DocumentNode.SelectNodes(
                "//*[@hidden or contains(@style, 'display: none') or contains(@style, 'visibility: hidden')]");
            if (hiddenNodes != null)
            {
                foreach (var node in hiddenNodes)
                {
                    node.Remove();
                }
            }

            // Get the inner text of the remaining content
            var text = doc.DocumentNode.InnerText;

            // Normalize whitespace
            return Regex.Replace(text, @"\s+", " ").Trim();
        }
        catch (Exception e)
        {
            _logger.LogWarning("Failed to extract text content from HTML: {Error}", e.Message);
            return string.Empty;
        }
    }

    private string NormalizeUrl(string url)
    {
        var normalizedUrl = url;

        // Remove language codes like /en-us/
        var languageCodeRegex = new Regex(@"/[a-z]{2}-[a-z]{2}/");
        var languageCodeMatch = languageCodeRegex.Match(normalizedUrl);
        if (languageCodeMatch.Success)
        {
            normalizedUrl = languageCodeRegex.Replace(normalizedUrl, "/");
        }

        // Remove fragment identifiers
        if (url.Contains('#'))
        {
            normalizedUrl = normalizedUrl[..normalizedUrl.IndexOf('#')];
        }

        return normalizedUrl;
    }
}

public class WebCrawlingBatchResult
{
    public List<CrawlingResult> NewCrawlingResults { get; }

    public List<string> UpdatedUrlsToProcess { get; }

    public WebCrawlingBatchResult(List<CrawlingResult> crawlingResults, List<string> newUrlsToProcess)
    {
        NewCrawlingResults = crawlingResults;
        UpdatedUrlsToProcess = newUrlsToProcess;
    }
}