using System.Globalization;

namespace Sleekflow.Utils;

public static class CultureUtils
{
    private static readonly Dictionary<string, CultureInfo> LanguageIsoCodeToCultureInfo = CultureInfo
        .GetCultures(CultureTypes.NeutralCultures)
        .GroupBy(c => c.Name)
        .Where(c => !string.IsNullOrWhiteSpace(c.Key))
        .ToDictionary(c => c.Key, c => c.Single());

    private static readonly Dictionary<string, RegionInfo> ThreeLetterIsoCurrencySymbolToRegionInfo = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(
            culture =>
            {
                try
                {
                    return new RegionInfo(culture.Name);
                }
                catch
                {
                    return null;
                }
            })
        .Where(r => r != null)
        .GroupBy(r => r!.ISOCurrencySymbol)
        .ToDictionary(r => r.Key, r => r.First()!);

    public static CultureInfo? GetCultureInfoByLanguageIsoCode(string languageIsoCode)
    {
        var cultureInfo = LanguageIsoCodeToCultureInfo.GetValueOrDefault(languageIsoCode);

        return cultureInfo;
    }

    public static IReadOnlyDictionary<string, CultureInfo> GetLanguageIsoCodeToCultureInfo()
    {
        return LanguageIsoCodeToCultureInfo;
    }

    public static RegionInfo? GetRegionInfoByCurrencyIsoCode(string currencyIsoCode)
    {
        var regionInfo = ThreeLetterIsoCurrencySymbolToRegionInfo.GetValueOrDefault(currencyIsoCode);

        return regionInfo;
    }

    public static IReadOnlyDictionary<string, RegionInfo> GetThreeLetterIsoCurrencySymbolToRegionInfo()
    {
        return ThreeLetterIsoCurrencySymbolToRegionInfo;
    }
}