using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Workers;

public class ExecuteSleepStepInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StepId { get; set; }

    [JsonProperty("total_seconds")]
    [System.ComponentModel.DataAnnotations.Required]
    public long TotalSeconds { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public ExecuteSleepStepInput(
        string stateId,
        string stepId,
        long totalSeconds,
        Stack<StackEntry> stackEntries)
    {
        StateId = stateId;
        StepId = stepId;
        TotalSeconds = totalSeconds;
        StackEntries = stackEntries;
    }
}