using System.Collections.Concurrent;
using Sleekflow.Integrator.Zoho.Services;

namespace Sleekflow.Integrator.Zoho.Utils;

public static class ZohoDateTimeUtils
{
    // Cache of datetime field names by entity type to avoid repeated lookups
    private static readonly ConcurrentDictionary<string, HashSet<string>> DateTimeFieldsCache = new();

    public static string ToZohoDateTimeString(DateTimeOffset dateTimeOffset)
    {
        // Convert UTC time to local time for Zoho
        return dateTimeOffset.ToLocalTime().ToString("yyyy-MM-ddTHH:mm:sszzz");
    }

    public static DateTimeOffset FromZohoDateTime(string dateTimeStr)
    {
        // Parse the local time and convert to DateTimeOffset, then to UTC
        var localDateTime = DateTime.Parse(dateTimeStr);
        var localOffset = TimeZoneInfo.Local.GetUtcOffset(localDateTime);
        return new DateTimeOffset(localDateTime, localOffset).ToUniversalTime();
    }

    public static DateTimeOffset? GetZohoDateTimeValue(object? value)
    {
        if (value == null)
        {
            return null;
        }

        // If it's already a DateTimeOffset, treat it as local time and convert to UTC
        if (value is DateTimeOffset dto)
        {
            // Convert to local time first (in case it's already UTC)
            var localTime = dto.ToLocalTime();

            // Then convert to UTC to ensure consistent handling
            return localTime.ToUniversalTime();
        }

        // If it's a string, parse it as a Zoho datetime (which will return UTC)
        if (value is string str)
        {
            return FromZohoDateTime(str);
        }

        return null;
    }

    /// <summary>
    /// Updates the cache of datetime fields for a given entity type using the fields output
    /// </summary>
    public static void UpdateDateTimeFieldsCache(string entityTypeName, ZohoObjectService.GetFieldsOutput fieldsOutput)
    {
        var dateTimeFields = new HashSet<string>();

        foreach (var field in fieldsOutput.CreatableFields.Concat(fieldsOutput.UpdatableFields).Concat(fieldsOutput.ViewableFields))
        {
            if (field.Type == "datetime")
            {
                dateTimeFields.Add(field.Name);
            }
        }

        DateTimeFieldsCache.AddOrUpdate(entityTypeName, dateTimeFields, (_, _) => dateTimeFields);
    }

    public static void ConvertDateTimeFields(Dictionary<string, object?> dict, string entityTypeName)
    {
        if (DateTimeFieldsCache.TryGetValue(entityTypeName, out var dateTimeFields))
        {
            foreach (var fieldName in dateTimeFields)
            {
                if (dict.TryGetValue(fieldName, out var value))
                {
                    dict[fieldName] = GetZohoDateTimeValue(value);
                }
            }
        }
    }

    public static void ConvertDateTimeFields(IEnumerable<Dictionary<string, object?>> dicts, string entityTypeName)
    {
        foreach (var dict in dicts)
        {
            ConvertDateTimeFields(dict, entityTypeName);
        }
    }
}