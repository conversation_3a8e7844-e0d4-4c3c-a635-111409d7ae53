using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.TravisBackend;

[SwaggerInclude]
public class GetCompanyUsageCycleOutput
{
    [JsonProperty("from_date_time")]
    public DateTimeOffset FromDateTime { get; set; }

    [JsonProperty("to_date_time")]
    public DateTimeOffset ToDateTime { get; set; }

    [JsonConstructor]
    public GetCompanyUsageCycleOutput(DateTimeOffset fromDateTime, DateTimeOffset toDateTime)
    {
        FromDateTime = fromDateTime;
        ToDateTime = toDateTime;
    }
}