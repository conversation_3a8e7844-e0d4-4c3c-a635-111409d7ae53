using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.AzureOpenAI;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using static Sleekflow.IntelligentHub.Evaluator.Constants.MethodNames;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;

public class NoRAGMethod : IMethod<ChatEvalConfig, ChatEvalOutput>
{
    private readonly Kernel _kernel;
    private readonly IChatCompletionService _chatCompletionService;

    public NoRAGMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_GPT_4_1_MINI);
    }

    public string MethodName => NoRAG;

    public async Task<ChatEvalOutput> CompleteAsync(
        ChatEvalConfig chatEvalConfig,
        ChatMessageContent[]? questionContexts,
        List<string>? sourceFilenames,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig,
        SfChatEntry[]? sfChatEntriesQuestionContexts)
    {
        var allFilesAsChatMessageContents = await ReadAllFilesAsChatMessageContents(chatEvalConfig, sourceFilenames);

        var chatHistory = new ChatHistory
        {
            new ChatMessageContent(
                AuthorRole.System,
                "You are not allowed to search the Internet. But you can use the following sources as knowledge base:")
        };
        chatHistory.AddRange(allFilesAsChatMessageContents);
        chatHistory.AddRange(questionContexts);

        var executionSettings = new AzureOpenAIPromptExecutionSettings
        {
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
        };

        var messageContent = await _chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            executionSettings: executionSettings,
            kernel: _kernel);
        if (messageContent.Content == null)
        {
            throw new Exception("Failed to generate answer.");
        }

        return new ChatEvalOutput(messageContent.Content, messageContent.Content);
    }

    private static async Task<List<ChatMessageContent>> ReadAllFilesAsChatMessageContents(
        ChatEvalConfig chatEvalConfig,
        List<string>? sourceFilenames)
    {
        // When there are less than 30 files in KB, we include all of them in the prompt
        // Exceeding that, and we only include the files in ChatEvalQuestion so we don't exceed the input context limit
        // Note that this will skew the RAG score
        var files = Directory.GetFiles(chatEvalConfig.SourceDir);
        if (files.Length >= 30 && sourceFilenames != null && sourceFilenames.Count > 0)
        {
            var contexts = new List<ChatMessageContent>();
            foreach (var sourceFilename in sourceFilenames)
            {
                var srcFilePath = Path.Combine(chatEvalConfig.SourceDir, sourceFilename);
                var mdString = await File.ReadAllTextAsync(srcFilePath);
                contexts.Add(new ChatMessageContent(AuthorRole.System, mdString));
            }

            return contexts;
        }
        else
        {
            var contexts = new List<ChatMessageContent>();

            foreach (var file in files)
            {
                var mdString = await File.ReadAllTextAsync(file);
                contexts.Add(new ChatMessageContent(AuthorRole.System, mdString));
            }

            return contexts;
        }
    }
}