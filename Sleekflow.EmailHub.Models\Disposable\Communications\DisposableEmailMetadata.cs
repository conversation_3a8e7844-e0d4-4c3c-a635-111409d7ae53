using System.Text.Json.Serialization;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.Disposable.Communications;

public class DisposableEmailMetadata : EmailMetadata
{
    [JsonConstructor]
    public DisposableEmailMetadata(DateTimeOffset sentAt)
        : base(ProviderNames.Disposable, ProviderNames.Disposable, sentAt)
    {
    }
}