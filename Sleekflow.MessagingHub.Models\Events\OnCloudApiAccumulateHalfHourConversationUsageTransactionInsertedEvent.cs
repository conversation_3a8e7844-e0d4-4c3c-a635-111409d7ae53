using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Models.Events;

public class OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent
{
    public long LastConversationUsageInsertTimestamp { get; set; }

    public string BusinessBalanceId { get; set; }

    public string FacebookBusinessId { get; set; }

    public bool IsConversationAnalyticsTransactionLogInserted { get; set; }

    public Dictionary<string, WabaConversationInsertionException> WabaConversationInsertionExceptions { get; set; }

    public OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent(
        long lastConversationUsageInsertTimestamp,
        string businessBalanceId,
        string facebookBusinessId,
        bool isConversationAnalyticsTransactionLogInserted,
        Dictionary<string, WabaConversationInsertionException> wabaConversationInsertionExceptions)
    {
        LastConversationUsageInsertTimestamp = lastConversationUsageInsertTimestamp;
        BusinessBalanceId = businessBalanceId;
        FacebookBusinessId = facebookBusinessId;
        IsConversationAnalyticsTransactionLogInserted = isConversationAnalyticsTransactionLogInserted;
        WabaConversationInsertionExceptions = wabaConversationInsertionExceptions;
    }
}