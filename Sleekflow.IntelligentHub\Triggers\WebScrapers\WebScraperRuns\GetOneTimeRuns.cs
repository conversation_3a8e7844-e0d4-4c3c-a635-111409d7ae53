﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

[TriggerGroup(ControllerNames.WebScraperRuns)]
public class GetOneTimeRuns : ITrigger<GetOneTimeRuns.GetOneTimeRunsInput, GetOneTimeRuns.GetOneTimeRunsOutput>
{
    private readonly IWebScraperService _webScraperService;

    public GetOneTimeRuns(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class GetOneTimeRunsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("offset")]
        public int Offset { get; set; }

        [Required]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetOneTimeRunsInput(string sleekflowCompanyId, int offset = 0, int limit = 10)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Offset = offset;
            Limit = limit;
        }
    }

    public class GetOneTimeRunsOutput
    {
        [JsonProperty("web_scraper_runs")]
        public List<WebScraperRun> WebScraperRuns { get; set; }

        [JsonConstructor]
        public GetOneTimeRunsOutput(List<WebScraperRun> webScraperRuns)
        {
            WebScraperRuns = webScraperRuns;
        }
    }

    public async Task<GetOneTimeRunsOutput> F(GetOneTimeRunsInput getOneTimeRunsInput)
    {
        var oneTimeRuns = await _webScraperService.GetOneTimeRunsAsync(
            getOneTimeRunsInput.SleekflowCompanyId,
            getOneTimeRunsInput.Offset,
            getOneTimeRunsInput.Limit);
        return new GetOneTimeRunsOutput(oneTimeRuns);
    }
}