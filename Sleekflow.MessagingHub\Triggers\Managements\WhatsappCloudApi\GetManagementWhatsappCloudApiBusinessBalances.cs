using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class GetManagementWhatsappCloudApiBusinessBalances
    : ITrigger<
        GetManagementWhatsappCloudApiBusinessBalances.GetManagementWhatsappCloudApiBusinessBalancesInput,
        GetManagementWhatsappCloudApiBusinessBalances.GetManagementWhatsappCloudApiBusinessBalancesOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetManagementWhatsappCloudApiBusinessBalances> _logger;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;

    public GetManagementWhatsappCloudApiBusinessBalances(
        IWabaService wabaService,
        ILogger<GetManagementWhatsappCloudApiBusinessBalances> logger,
        IBusinessBalanceService businessBalanceService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
    }

    public class GetManagementWhatsappCloudApiBusinessBalancesInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetManagementWhatsappCloudApiBusinessBalancesInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetManagementWhatsappCloudApiBusinessBalancesOutput
    {
        [JsonProperty("business_balances")]
        public List<ManagementBusinessBalanceDto> BusinessBalances { get; set; }

        [JsonConstructor]
        public GetManagementWhatsappCloudApiBusinessBalancesOutput(List<ManagementBusinessBalanceDto> businessBalances)
        {
            BusinessBalances = businessBalances;
        }
    }

    public async Task<GetManagementWhatsappCloudApiBusinessBalancesOutput> F(
        GetManagementWhatsappCloudApiBusinessBalancesInput getWhatsappCloudApiBusinessBalancesInput)
    {
        var sleekflowCompanyId = getWhatsappCloudApiBusinessBalancesInput.SleekflowCompanyId;

        var wabas = await _wabaService.GetWabasAsync(sleekflowCompanyId);

        if (wabas.Count == 0)
        {
            return new GetManagementWhatsappCloudApiBusinessBalancesOutput(new List<ManagementBusinessBalanceDto>());
        }

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w => w.FacebookBusinessId != null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        var managementBusinessBalances = new List<ManagementBusinessBalanceDto>();

        foreach (var (facebookBusinessId, relatedWabas) in facebookBusinessIdToWabasDict)
        {
            var businessBalance =
                await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

            if (businessBalance is not null)
            {
                var unCalculatedCreditTransferTransactionLogs =
                    await _wabaLevelCreditManagementService.GetUnCalculatedCreditTransferTransactionLogsAsync(
                        businessBalance);

                managementBusinessBalances.Add(
                    new ManagementBusinessBalanceDto(
                        businessBalance,
                        relatedWabas,
                        unCalculatedCreditTransferTransactionLogs));
            }
            else
            {
                _logger.LogWarning(
                    "Unable to locate BusinessBalance with {FacebookBusinessId}. {RelatedWabas}",
                    facebookBusinessId,
                    JsonConvert.SerializeObject(relatedWabas));
            }
        }

        return new GetManagementWhatsappCloudApiBusinessBalancesOutput(managementBusinessBalances);
    }
}