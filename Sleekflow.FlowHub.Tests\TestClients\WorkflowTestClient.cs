﻿using Newtonsoft.Json.Linq;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Triggers.Workflows;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.TestClients;

public static class WorkflowTestClient
{
    public static async Task<T?> CreateWorkflowAsync<T>(
        string sleekflowCompanyId,
        string workflowName,
        string staffId,
        string workflowType = WorkflowType.Normal,
        string? workflowGroupId = null,
        List<string>? staffTeamIds = null,
        WorkflowTriggers? workflowTriggers = null,
        WorkflowEnrollmentSettings? workflowEnrollmentSettings = null,
        WorkflowScheduleSettings? workflowScheduleSettings = null,
        List<Step>? steps = null,
        Dictionary<string, object?>? metadata = null)
        where T : class
    {
        workflowTriggers ??= new WorkflowTriggers(
            null,
            new WorkflowTrigger("{{ true }}"),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);

        steps ??= new List<Step>()
        {
            new LogStep("log", "Log", null, null, "Hello", "Information")
        };

        var createWorkflowInput = new CreateWorkflow.CreateWorkflowInput(
            sleekflowCompanyId,
            workflowTriggers,
            workflowEnrollmentSettings ?? WorkflowEnrollmentSettings.Default(),
            workflowScheduleSettings ?? WorkflowScheduleSettings.Default(),
            steps
                .Select(JObject.FromObject)
                .ToList(),
            workflowName,
            workflowType,
            workflowGroupId,
            metadata ?? new Dictionary<string, object?>(),
            null,
            staffId,
            staffTeamIds,
            null);

        var createWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(createWorkflowInput).ToUrl("/Workflows/CreateWorkflow");
            });

        var createWorkflowOutput = await createWorkflowScenarioResult
            .ReadAsJsonAsync<T>();

        return createWorkflowOutput;
    }

    public static Task<Output<CreateWorkflow.CreateWorkflowOutput>?> CreateWorkflowAsync(
        string sleekflowCompanyId,
        string workflowName,
        string staffId,
        string workflowType = WorkflowType.Normal,
        string? workflowGroupId = null,
        List<string>? staffTeamIds = null,
        WorkflowTriggers? workflowTriggers = null,
        WorkflowEnrollmentSettings? workflowEnrollmentSettings = null,
        WorkflowScheduleSettings? workflowScheduleSettings = null,
        List<Step>? steps = null,
        Dictionary<string, object?>? metadata = null)
    {
        return CreateWorkflowAsync<Output<CreateWorkflow.CreateWorkflowOutput>>(
            sleekflowCompanyId,
            workflowName,
            staffId,
            workflowType,
            workflowGroupId,
            staffTeamIds,
            workflowTriggers,
            workflowEnrollmentSettings,
            workflowScheduleSettings,
            steps,
            metadata);
    }

    public static async Task<Output<GetWorkflow.GetWorkflowOutput>?> GetWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        var getWorkflowInput = new GetWorkflow.GetWorkflowInput(
            sleekflowCompanyId,
            workflowId);

        var getWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getWorkflowInput).ToUrl("/Workflows/GetWorkflow");
            });

        var getWorkflowOutput = await getWorkflowScenarioResult
            .ReadAsJsonAsync<Output<GetWorkflow.GetWorkflowOutput>>();

        return getWorkflowOutput;
    }

    public static async Task<Output<GetVersionedWorkflow.GetVersionedWorkflowOutput>?> GetVersionedWorkflowAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        var getWorkflowInput = new GetVersionedWorkflow.GetVersionedWorkflowInput(
            sleekflowCompanyId,
            workflowVersionedId);

        var getVersionedWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getWorkflowInput).ToUrl("/Workflows/GetVersionedWorkflow");
            });

        var getVersionedWorkflowOutput = await getVersionedWorkflowScenarioResult
            .ReadAsJsonAsync<Output<GetVersionedWorkflow.GetVersionedWorkflowOutput>>();

        return getVersionedWorkflowOutput;
    }

    public static async Task<Output<GetWorkflows.GetWorkflowsOutput>?> GetWorkflowsAsync(
        string sleekflowCompanyId,
        int limit = 50,
        string? continuationToken = null,
        string? searchName = null,
        WorkflowFilters? workflowFilters = null,
        WorkflowExecutionStatisticsFilters? workflowExecutionStatisticsFilters = null)
    {
        var getWorkflowsInput = new GetWorkflows.GetWorkflowsInput(
            sleekflowCompanyId,
            continuationToken,
            limit,
            searchName,
            workflowFilters,
            workflowExecutionStatisticsFilters);

        var getWorkflowsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getWorkflowsInput).ToUrl("/Workflows/GetWorkflows");
            });

        var getWorkflowsOutput = await getWorkflowsScenarioResult
            .ReadAsJsonAsync<Output<GetWorkflows.GetWorkflowsOutput>>();

        return getWorkflowsOutput;
    }

    public static async Task<Output<GetLatestWorkflow.GetLatestWorkflowOutput>?> GetLatestWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        var getLatestWorkflowInput = new GetLatestWorkflow.GetLatestWorkflowInput(
            sleekflowCompanyId,
            workflowId);

        var getLatestWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getLatestWorkflowInput).ToUrl("/Workflows/GetLatestWorkflow");
            });

        var getLatestWorkflowOutput = await getLatestWorkflowScenarioResult
            .ReadAsJsonAsync<Output<GetLatestWorkflow.GetLatestWorkflowOutput>>();

        return getLatestWorkflowOutput;
    }

    public static async Task<Output<GetActiveWorkflow.GetActiveWorkflowOutput>?> GetActiveWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        var getActiveWorkflowInput = new GetActiveWorkflow.GetActiveWorkflowInput(
            sleekflowCompanyId,
            workflowId);

        var getActiveWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getActiveWorkflowInput).ToUrl("/Workflows/GetActiveWorkflow");
            });

        var getActiveWorkflowOutput = await getActiveWorkflowScenarioResult
            .ReadAsJsonAsync<Output<GetActiveWorkflow.GetActiveWorkflowOutput>>();

        return getActiveWorkflowOutput;
    }

    public static async Task<Output<CountWorkflows.CountWorkflowsOutput>?> CountWorkflowsAsync(
        string sleekflowCompanyId,
        string? workflowType = null)
    {
        var countWorkflowsInput = new CountWorkflows.CountWorkflowsInput(
            sleekflowCompanyId,
            workflowType);

        var countWorkflowsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(countWorkflowsInput).ToUrl("/Workflows/CountWorkflows");
            });

        var countWorkflowsOutput = await countWorkflowsScenarioResult
            .ReadAsJsonAsync<Output<CountWorkflows.CountWorkflowsOutput>>();

        return countWorkflowsOutput;
    }

    public static async Task<Output<UpdateWorkflow.UpdateWorkflowOutput>?> UpdateWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId,
        string workflowName,
        string staffId,
        string? workflowGroupId,
        WorkflowTriggers workflowTriggers,
        WorkflowEnrollmentSettings workflowEnrollmentSettings,
        WorkflowScheduleSettings workflowScheduleSettings,
        List<Step> steps,
        List<string>? staffTeamIds = null,
        Dictionary<string, object?>? metadata = null)
    {
        var updateWorkflowInput = new UpdateWorkflow.UpdateWorkflowInput(
            sleekflowCompanyId,
            workflowId,
            workflowTriggers,
            workflowEnrollmentSettings,
            workflowScheduleSettings,
            steps
                .Select(JObject.FromObject)
                .ToList(),
            workflowName,
            workflowGroupId,
            staffId,
            staffTeamIds,
            metadata ?? new Dictionary<string, object?>(),
            null);

        var updateWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(updateWorkflowInput).ToUrl("/Workflows/UpdateWorkflow");
            });

        var updateWorkflowOutput = await updateWorkflowScenarioResult
            .ReadAsJsonAsync<Output<UpdateWorkflow.UpdateWorkflowOutput>>();

        return updateWorkflowOutput;
    }

    public static async Task<Output<SaveWorkflowDraft.SaveWorkflowDraftOutput>?> SaveWorkflowDraftAsync(
        string sleekflowCompanyId,
        string workflowId,
        string workflowName,
        string staffId,
        string? workflowGroupId,
        WorkflowTriggers workflowTriggers,
        WorkflowEnrollmentSettings workflowEnrollmentSettings,
        WorkflowScheduleSettings workflowScheduleSettings,
        List<Step> steps,
        List<string>? staffTeamIds = null,
        Dictionary<string, object?>? metadata = null)
    {
        var saveWorkflowDraftInput = new SaveWorkflowDraft.SaveWorkflowDraftInput(
            sleekflowCompanyId,
            workflowId,
            workflowTriggers,
            workflowEnrollmentSettings,
            workflowScheduleSettings,
            steps
                .Select(JObject.FromObject)
                .ToList(),
            workflowName,
            workflowGroupId,
            staffId,
            staffTeamIds,
            metadata ?? new Dictionary<string, object?>());

        var saveWorkflowDraftScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(saveWorkflowDraftInput).ToUrl("/Workflows/SaveWorkflowDraft");
            });

        var saveWorkflowDraftOutput = await saveWorkflowDraftScenarioResult
            .ReadAsJsonAsync<Output<SaveWorkflowDraft.SaveWorkflowDraftOutput>>();

        return saveWorkflowDraftOutput;
    }

    public static async Task<Output<DeleteWorkflow.DeleteWorkflowOutput>?> DeleteWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var deleteWorkflowInput = new DeleteWorkflow.DeleteWorkflowInput(
            sleekflowCompanyId,
            workflowId,
            staffId,
            staffTeamIds);

        var deleteWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(deleteWorkflowInput).ToUrl("/Workflows/DeleteWorkflow");
            });

        var deleteWorkflowOutput = await deleteWorkflowScenarioResult
            .ReadAsJsonAsync<Output<DeleteWorkflow.DeleteWorkflowOutput>>();

        return deleteWorkflowOutput;
    }

    public static async Task<Output<ScheduleDeleteWorkflows.ScheduleDeleteWorkflowsOutput>?>
        ScheduleDeleteWorkflowsAsync(
            string sleekflowCompanyId,
            List<string> workflowIds,
            string staffId,
            List<string>? staffTeamIds = null)
    {
        var scheduleDeleteWorkflowsInput = new ScheduleDeleteWorkflows.ScheduleDeleteWorkflowsInput(
            sleekflowCompanyId,
            workflowIds,
            staffId,
            staffTeamIds);
        var scheduleDeleteWorkflowsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(scheduleDeleteWorkflowsInput).ToUrl("/Workflows/ScheduleDeleteWorkflows");
            });

        var scheduleDeleteWorkflowsOutput = await scheduleDeleteWorkflowsScenarioResult
            .ReadAsJsonAsync<Output<ScheduleDeleteWorkflows.ScheduleDeleteWorkflowsOutput>>();

        return scheduleDeleteWorkflowsOutput;
    }

    public static async Task<Output<DuplicateWorkflow.DuplicateWorkflowOutput>?> DuplicateWorkflowAsync(
        string sleekflowCompanyId,
        string workflowId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var duplicateWorkflowInput = new DuplicateWorkflow.DuplicateWorkflowInput(
            sleekflowCompanyId,
            workflowId,
            staffId,
            staffTeamIds);

        var duplicateWorkflow1ScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(duplicateWorkflowInput).ToUrl("/Workflows/DuplicateWorkflow");
            });

        var duplicateWorkflowOutput = await duplicateWorkflow1ScenarioResult
            .ReadAsJsonAsync<Output<DuplicateWorkflow.DuplicateWorkflowOutput>>();

        return duplicateWorkflowOutput;
    }

    public static async Task<Output<EnableWorkflow.EnableWorkflowOutput>?> EnableWorkflowAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var enableWorkflowInput = new EnableWorkflow.EnableWorkflowInput(
            sleekflowCompanyId,
            workflowVersionedId,
            staffId,
            staffTeamIds);

        var enableWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(enableWorkflowInput).ToUrl("/Workflows/EnableWorkflow");
            });

        var enableWorkflowOutput = await enableWorkflowScenarioResult
            .ReadAsJsonAsync<Output<EnableWorkflow.EnableWorkflowOutput>>();

        return enableWorkflowOutput;
    }

    public static async Task<Output<T>> EnableWorkflowDraftAsync<T>(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var enableWorkflowDraftInput = new EnableWorkflowDraft.EnableWorkflowDraftInput(
            sleekflowCompanyId,
            workflowVersionedId,
            staffId,
            staffTeamIds);

        var enableWorkflowDraftScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(enableWorkflowDraftInput).ToUrl("/Workflows/EnableWorkflowDraft");
            });

        var enableWorkflowDraftOutput = await enableWorkflowDraftScenarioResult
            .ReadAsJsonAsync<Output<T>>();

        return enableWorkflowDraftOutput;
    }

    public static async Task<Output<DisableWorkflow.DisableWorkflowOutput>?> DisableWorkflowAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var disableWorkflowInput = new DisableWorkflow.DisableWorkflowInput(
            sleekflowCompanyId,
            workflowVersionedId,
            staffId,
            staffTeamIds);

        var disableWorkflowScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(disableWorkflowInput).ToUrl("/Workflows/DisableWorkflow");
            });

        var disableWorkflowOutput = await disableWorkflowScenarioResult
            .ReadAsJsonAsync<Output<DisableWorkflow.DisableWorkflowOutput>>();

        return disableWorkflowOutput;
    }

    public static async Task<Output<SwapWorkflows.SwapWorkflowsOutput>?> SwapWorkflowsAsync(
        string sleekflowCompanyId,
        string sourceWorkflowVersionedId,
        string targetWorkflowVersionedId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var swapWorkflowsInput = new SwapWorkflows.SwapWorkflowsInput(
            sleekflowCompanyId,
            sourceWorkflowVersionedId,
            targetWorkflowVersionedId,
            staffId,
            staffTeamIds);

        var swapWorkflowsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(swapWorkflowsInput).ToUrl("/Workflows/SwapWorkflows");
            });

        var swapWorkflowsOutput = await swapWorkflowsScenarioResult
            .ReadAsJsonAsync<Output<SwapWorkflows.SwapWorkflowsOutput>>();

        return swapWorkflowsOutput;
    }
}