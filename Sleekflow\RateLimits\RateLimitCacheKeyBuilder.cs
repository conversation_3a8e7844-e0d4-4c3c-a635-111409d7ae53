﻿namespace Sleekflow.RateLimits;

public static class RateLimitCacheKeyBuilder<T>
{
    private const string KeySeparator = ":";
    private const string ValueSeparator = "-";
    private const string RateLimitPrefix = "rate-limit";
    private const int MaxKeyLength = 256;

    public static string BuildCacheKeyOnCompanyId(
        string companyId,
        params string[] additionalIdentifiers)
    {
        if (string.IsNullOrEmpty(companyId))
        {
            throw new ArgumentNullException(nameof(companyId));
        }

        return BuildKeyWithIdentifiers(
            new[]
            {
                companyId
            }.Concat(additionalIdentifiers ?? Array.Empty<string>()));
    }

    public static string BuildCacheKeyOnUserId(
        string userId,
        params string[] additionalIdentifiers)
    {
        if (string.IsNullOrEmpty(userId))
        {
            throw new ArgumentNullException(nameof(userId));
        }

        return BuildKeyWithIdentifiers(
            new[]
            {
                userId
            }.Concat(additionalIdentifiers ?? Array.Empty<string>()));
    }

    public static string BuildCacheKeyOnCustomIdentifiers(params string[] additionalIdentifiers)
    {
        if (additionalIdentifiers == null)
        {
            throw new ArgumentNullException(nameof(additionalIdentifiers));
        }

        return BuildKeyWithIdentifiers(additionalIdentifiers);
    }

    private static string BuildKeyWithIdentifiers(IEnumerable<string> identifiers)
    {
        var actionName = typeof(T).Name;
        var identifier = string.Join(
            ValueSeparator,
            new[]
            {
                actionName
            }.Concat(identifiers));
        return BuildKey(identifier);
    }

    private static string BuildKey(string identifier)
    {
        var key = string.Join(KeySeparator, RateLimitPrefix, identifier);
        if (key.Length > MaxKeyLength)
        {
            throw new ArgumentException($"Cache key length exceeds maximum allowed length of {MaxKeyLength}");
        }

        return key;
    }
}