using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;

public class AuthorNamesGeminiChatHistoryReducer(List<string> authorNames) : GeminiChatHistoryReducer
{
    private List<string> AuthorNames { get; } = authorNames;

    public override async Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        var chatMessageContents =
            chatHistory.ToList();

        var filteredMessageContents =
            chatMessageContents.Where(c => AuthorNames.Contains(c.AuthorName ?? string.Empty)).ToList();

        return await base.ReduceAsync(filteredMessageContents, cancellationToken);
    }
}