namespace Sleekflow.Models.NetSuite;

public class CreateNetSuiteInvoiceEvent
{
    public string? ExternalId { get; set; }

    public string SleekflowCompanyId { get; set; }

    public decimal SubscriptionFee { get; set; }

    public string? SubscriptionDescription { get; set; }

    public decimal OneTimeSetupFee { get; set; }

    public string? OneTimeSetupDescription { get; set; }

    public decimal WhatsappCreditAmount { get; set; }

    public string? WhatsappCreditDescription { get; set; }

    public DateTime? SubscriptionStartDate { get; set; }

    public DateTime? SubscriptionEndDate { get; set; }

    public int? PaymentTerm { get; set; }


    public string Currency { get; set; }

    public double? PaidAmount { get; set; }

    public DateTime? CreatedDate { get; set; }

    public CreateNetSuiteInvoiceEvent(
        string? externalId,
        string sleekflowCompanyId,
        decimal subscriptionFee,
        string? subscriptionDescription,
        decimal oneTimeSetupFee,
        string? oneTimeSetupDescription,
        decimal whatsappCreditAmount,
        string? whatsappCreditDescription,
        DateTime? subscriptionStartDate,
        DateTime? subscriptionEndDate,
        int? paymentTerm,
        string currency,
        double? paidAmount,
        DateTime? createdDate)
    {
        ExternalId = externalId;
        SleekflowCompanyId = sleekflowCompanyId;
        SubscriptionFee = subscriptionFee;
        SubscriptionDescription = subscriptionDescription;
        OneTimeSetupFee = oneTimeSetupFee;
        OneTimeSetupDescription = oneTimeSetupDescription;
        WhatsappCreditAmount = whatsappCreditAmount;
        WhatsappCreditDescription = whatsappCreditDescription;
        SubscriptionStartDate = subscriptionStartDate;
        SubscriptionEndDate = subscriptionEndDate;
        PaymentTerm = paymentTerm;
        Currency = currency;
        PaidAmount = paidAmount;
        CreatedDate = createdDate;
    }
}