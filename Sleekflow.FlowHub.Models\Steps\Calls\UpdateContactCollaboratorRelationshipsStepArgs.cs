using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactCollaboratorRelationshipsStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-collaborator-relationships";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty("add_staff_ids__expr")]
    public string? AddStaffIdsExpr { get; set; }

    [JsonProperty("remove_staff_ids__expr")]
    public string? RemoveStaffIdsExpr { get; set; }

    [JsonProperty("set_staff_ids__expr")]
    public string? SetStaffIdsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Conversation;

    [JsonConstructor]
    public UpdateContactCollaboratorRelationshipsStepArgs(
        string contactIdExpr,
        string? addStaffIdsExpr,
        string? removeStaffIdsExpr,
        string? setStaffIdsExpr)
    {
        ContactIdExpr = contactIdExpr;
        AddStaffIdsExpr = addStaffIdsExpr;
        RemoveStaffIdsExpr = removeStaffIdsExpr;
        SetStaffIdsExpr = setStaffIdsExpr;
    }
}