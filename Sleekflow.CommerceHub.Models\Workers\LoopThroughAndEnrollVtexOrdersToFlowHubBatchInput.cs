﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Workers;

public class LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("flow_hub_workflow_id")]
    public string FlowHubWorkflowId { get; set; }

    [Required]
    [JsonProperty("flow_hub_workflow_versioned_id")]
    public string FlowHubWorkflowVersionedId { get; set; }

    [Required]
    [JsonProperty("vtex_authentication_id")]
    public string VtexAuthenticationId { get; set; }

    [Required]
    [JsonProperty("page")]
    public int Page { get; set; }

    [Required]
    [JsonProperty("condition")]
    public VtexGetOrdersSearchCondition Condition { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput(
        string sleekflowCompanyId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string vtexAuthenticationId,
        int page,
        VtexGetOrdersSearchCondition condition)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        VtexAuthenticationId = vtexAuthenticationId;
        Page = page;
        Condition = condition;
    }
}