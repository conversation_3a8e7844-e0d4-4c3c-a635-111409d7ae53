﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Configs;
using Sleekflow.CrmHub.Models.ObjectIdResolvers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.States;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Providers;

public class HubspotIntegratorService : GenericProviderService
{
    public const string ProviderName = "hubspot-integrator";

    private readonly IAppConfig _appConfig;
    private readonly IHubspotObjectIdResolver _hubspotObjectIdResolver;
    private readonly HttpClient _httpClient;

    public HubspotIntegratorService(
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IProviderStateService providerStateService,
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        IHubspotObjectIdResolver hubspotObjectIdResolver,
        ICustomSyncConfigService customSyncConfigService,
        ILogger<HubspotIntegratorService> logger)
        : base(
            providerStateService,
            loopThroughObjectsProgressStateService,
            httpClientFactory.CreateClient("default-handler"),
            customSyncConfigService,
            ProviderName,
            appConfig.HubspotIntegratorEndpoint,
            logger)
    {
        _appConfig = appConfig;
        _hubspotObjectIdResolver = hubspotObjectIdResolver;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public override async Task InitTypeSyncAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        SyncConfig? syncConfig)
    {
        var inputJsonStr = JsonConvert.SerializeObject(
            new
            {
                sleekflow_company_id = sleekflowCompanyId, entity_type_name = entityTypeName, sync_config = syncConfig
            });

        var (_, _, _) = await PostAsync<object?>(
            _httpClient,
            inputJsonStr,
            _appConfig.HubspotIntegratorEndpoint + "/Integrations/InitTypeSync");
    }

    public override Task<string?> ResolveObjectIdAsync(Dictionary<string, object?> dict, string entityTypeName)
    {
        try
        {
            return Task.FromResult((string?) _hubspotObjectIdResolver.ResolveObjectId(dict));
        }
        catch (SfIdUnresolvableException)
        {
            return Task.FromResult((string?) null);
        }
    }
}