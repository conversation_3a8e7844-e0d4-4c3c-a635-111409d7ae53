﻿using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Events;
using Sleekflow.IntelligentHub.Models.Playgrounds;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.Playgrounds;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Events;

public class GeneratePlaygroundRecommendedReplyEventConsumerDefinition
    : ConsumerDefinition<GeneratePlaygroundRecommendedReplyEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GeneratePlaygroundRecommendedReplyEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GeneratePlaygroundRecommendedReplyEventConsumer : IConsumer<GeneratePlaygroundRecommendedReplyEvent>
{
    private readonly ILogger<GeneratePlaygroundRecommendedReplyEventConsumer> _logger;
    private readonly IChatService _chatService;
    private readonly IPlaygroundService _playgroundService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GeneratePlaygroundRecommendedReplyEventConsumer(
        ILogger<GeneratePlaygroundRecommendedReplyEventConsumer> logger,
        IChatService chatService,
        IPlaygroundService playgroundService,
        IIntelligentHubUsageService intelligentHubUsageService)
    {
        _logger = logger;
        _chatService = chatService;
        _playgroundService = playgroundService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    public async Task Consume(ConsumeContext<GeneratePlaygroundRecommendedReplyEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var sfChatEntries = message.ConversationContext;

        _logger.LogInformation(
            "Streaming Agent Recommended Reply {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(sfChatEntries, JsonConfig.DefaultLoggingJsonSerializerSettings));

        var playground = await _playgroundService.GetAsync(message.SessionId, message.SleekflowCompanyId);

        var agentConfigDto = playground.AgentConfig;
        var agentConfig = new CompanyAgentConfig(agentConfigDto);
        var (asyncEnumerable, sourcesStr, _) = await _chatService.StreamAgentAnswerAsync(
            sfChatEntries,
            sleekflowCompanyId,
            new ReplyGenerationContext(
                sleekflowCompanyId,
                playground.Id,
                new Dictionary<string, string>(),
                Guid.NewGuid().ToString(),
                null),
            agentConfig);

        var answerSb = new StringBuilder();
        await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
        {
            if (partialAnswer is null)
            {
                continue;
            }

            answerSb.Append(partialAnswer);
        }

        var recommendedReply = answerSb.ToString();
        recommendedReply = Regex.Replace(recommendedReply, @"\[source\]", string.Empty, RegexOptions.IgnoreCase);

        _logger.LogInformation("Agent Recommended reply: {RecommendedReply}", recommendedReply);

        await _intelligentHubUsageService.RecordUsageAsync(
            sleekflowCompanyId,
            PriceableFeatures.RecommendReply,
            null,
            new RecommendReplySnapshot(
                JsonConvert.SerializeObject(
                    sfChatEntries,
                    JsonConfig.DefaultLoggingJsonSerializerSettings),
                sourcesStr ?? string.Empty,
                recommendedReply));

        var playgroundRecommendedReply = new PlaygroundRecommendedReply(
            message.MessageId,
            sfChatEntries,
            recommendedReply);

        await _playgroundService.PatchAsync(
            message.SessionId,
            message.SleekflowCompanyId,
            playground.RecommendedReplies.Append(playgroundRecommendedReply).ToList(),
            playground.ETag);
    }
}