using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

public class UpdateSyncConfigDto
{
    [JsonConstructor]
    public UpdateSyncConfigDto(
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters,
        int interval,
        string entityTypeName,
        string? syncMode)
    {
        FilterGroups = filterGroups;
        FieldFilters = fieldFilters;
        Interval = interval;
        EntityTypeName = entityTypeName;
        SyncMode = syncMode;
    }

    [JsonProperty("filter_groups")]
    public List<SyncConfigFilterGroup> FilterGroups { get; set; }

    [JsonProperty("field_filters")]
    public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

    [JsonProperty("interval")]
    [Range(3600, 3600 * 24)]
    [Required]
    public int Interval { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("sync_mode")]
    public string? SyncMode { get; set; }
}