using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts;

[TriggerGroup(ControllerNames.Carts)]
public class UpdateCart
    : ITrigger<
        UpdateCart.UpdateCartInput,
        UpdateCart.UpdateCartOutput>
{
    private readonly ICartService _cartService;
    private readonly IProductVariantService _productVariantService;
    private readonly IProductService _productService;

    public UpdateCart(
        ICartService cartService,
        IProductVariantService productVariantService,
        IProductService productService)
    {
        _cartService = cartService;
        _productVariantService = productVariantService;
        _productService = productService;
    }

    public class UpdateCartInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("line_items")]
        public List<CartLineItem> LineItems { get; set; }

        [JsonProperty("cart_discount")]
        public Discount? CartDiscount { get; set; }

        [JsonConstructor]
        public UpdateCartInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            List<CartLineItem> lineItems,
            Discount? cartDiscount)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            LineItems = lineItems;
            CartDiscount = cartDiscount;
        }
    }

    public class UpdateCartOutput
    {
        [JsonProperty("cart")]
        public CartDto Cart { get; }

        [JsonConstructor]
        public UpdateCartOutput(CartDto cart)
        {
            Cart = cart;
        }
    }

    public async Task<UpdateCartOutput> F(UpdateCartInput updateCartInput)
    {
        var cart = await _cartService.PatchAndGetCartAsync(
            updateCartInput.SleekflowCompanyId,
            updateCartInput.StoreId,
            updateCartInput.SleekflowUserProfileId,
            updateCartInput.LineItems,
            updateCartInput.CartDiscount);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            cart.LineItems.Select(x => x.ProductVariantId).ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        return new UpdateCartOutput(
            new CartDto(
                cart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict));
    }
}