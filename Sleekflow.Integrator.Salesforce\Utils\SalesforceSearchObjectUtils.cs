﻿using System.Globalization;
using System.Text;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.InflowActions;
using FormatException = System.FormatException;

namespace Sleekflow.Integrator.Salesforce.Utils;

public static class SalesforceSearchObjectUtils
{
    public static string GenerateSoqlWhereClauseFromSearchObjectConditions(List<SearchObjectCondition> conditions)
    {
        var whereClause = new StringBuilder("WHERE ");

        for (var i = 0; i < conditions.Count; i++)
        {
            var condition = conditions[i];

            if (i > 0)
            {
                whereClause.Append(" AND ");
            }

            var conditionClause = GenerateSoqlWhereConditionFromSearchObjectCondition(condition);

            if (conditionClause.Contains(" AND ") || conditionClause.Contains(" OR "))
            {
                whereClause.Append('(');
                whereClause.Append(conditionClause);
                whereClause.Append(')');
            }
            else
            {
                whereClause.Append(conditionClause);
            }
        }

        return whereClause.ToString();
    }

    private static string GenerateSoqlWhereConditionFromSearchObjectCondition(SearchObjectCondition condition)
    {
        var fieldName = condition.FieldName;
        var @operator = condition.Operator;
        var value = condition.Value;

        try
        {
            if (value == null &&
                (@operator == SearchObjectConditionOperators.IsBetween
                || @operator == SearchObjectConditionOperators.IsNotBetween
                || @operator == SearchObjectConditionOperators.IsWithin
                || @operator == SearchObjectConditionOperators.IsNotWithin
                || @operator == SearchObjectConditionOperators.IsEqualTo
                || @operator == SearchObjectConditionOperators.IsAfter
                || @operator == SearchObjectConditionOperators.IsBefore
                || @operator == SearchObjectConditionOperators.Contains
                || @operator == SearchObjectConditionOperators.DoesNotContain
                || @operator == SearchObjectConditionOperators.IsGreaterThan
                || @operator == SearchObjectConditionOperators.IsLessThan))
            {
                throw new ArgumentException($"Value can't be null for the {@operator} search operator.");
            }

            switch (@operator)
            {
                case SearchObjectConditionOperators.IsToday:
                    return GenerateSoqlWhereConditionForIsTodayOperator(fieldName);
                case SearchObjectConditionOperators.IsBetween:
                    return GenerateSoqlWhereConditionForIsBetweenOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsNotBetween:
                    return GenerateSoqlWhereConditionForIsNotBetweenOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsWithin:
                    return GenerateSoqlWhereConditionForIsWithinOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsNotWithin:
                    return GenerateSoqlWhereConditionForIsNotWithinOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsOn:
                    return GenerateSoqlWhereConditionForIsOnOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsEqualTo:
                    return GenerateSoqlWhereConditionForIsEqualToOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsAfter:
                    return GenerateSoqlWhereConditionForIsAfterOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsBefore:
                    return GenerateSoqlWhereConditionForIsBeforeOperator(fieldName, value!);
                case SearchObjectConditionOperators.Exists:
                    return GenerateSoqlWhereConditionForExistsOperator(fieldName);
                case SearchObjectConditionOperators.DoesNotExist:
                    return GenerateSoqlWhereConditionForDoesNotExistOperator(fieldName);
                case SearchObjectConditionOperators.Contains:
                    return GenerateSoqlWhereConditionForContainsOperator(fieldName, value!);
                case SearchObjectConditionOperators.DoesNotContain:
                    return GenerateSoqlWhereConditionForDoesNotContainOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsGreaterThan:
                    return GenerateSoqlWhereConditionForIsGreaterThanOperator(fieldName, value!);
                case SearchObjectConditionOperators.IsLessThan:
                    return GenerateSoqlWhereConditionForIsLessThanOperator(fieldName, value!);
            }
        }
        catch (InvalidCastException)
        {
            throw new ArgumentException($"Unsupported value type for '{@operator}' search operator.");
        }
        catch (FormatException)
        {
            throw new ArgumentException($"Unsupported value type for '{@operator}' search operator.");
        }

        throw new NotImplementedException($"Search Operator '{@operator}' not implemented.");
    }

    private static string GenerateSoqlWhereConditionForIsTodayOperator(string fieldName)
    {
        return $"{fieldName} = TODAY";
    }

    private static string GenerateSoqlWhereConditionForIsBetweenOperator(string fieldName, object value)
    {
        var timeSpan = GetTimeSpanFromValueObject(value);

        return
            $"{fieldName} >= {FormatDateTimeOffset(timeSpan.StartDateTime)} AND {fieldName} <= {FormatDateTimeOffset(timeSpan.EndDateTime)}";
    }

    private static string GenerateSoqlWhereConditionForIsNotBetweenOperator(string fieldName, object value)
    {
        var timeSpan = GetTimeSpanFromValueObject(value);

        return
            $"{fieldName} < {FormatDateTimeOffset(timeSpan.StartDateTime)} OR {fieldName} > {FormatDateTimeOffset(timeSpan.EndDateTime)}";
    }

    private static string GenerateSoqlWhereConditionForIsWithinOperator(string fieldName, object value)
    {
        var duration = GetSearchObjectConditionDurationFromValueObject(value);

        var timeSpan = GetTimeSpanFromSearchObjectConditionDuration(duration);

        return
            $"{fieldName} >= {FormatDateTimeOffset(timeSpan.StartDateTime)} AND {fieldName} <= {FormatDateTimeOffset(timeSpan.EndDateTime)}";
    }

    private static string GenerateSoqlWhereConditionForIsNotWithinOperator(string fieldName, object value)
    {
        var duration = GetSearchObjectConditionDurationFromValueObject(value);

        var timeSpan = GetTimeSpanFromSearchObjectConditionDuration(duration);

        return $"{fieldName} < {FormatDateTimeOffset(timeSpan.StartDateTime)} OR {fieldName} > {FormatDateTimeOffset(timeSpan.EndDateTime)}";
    }

    private static string GenerateSoqlWhereConditionForIsOnOperator(string fieldName, object value)
    {
        var salesforceDayInWeekValue = GetSalesforceDayInWeekValue((string) value);

        return $"DAY_IN_WEEK({fieldName}) = {salesforceDayInWeekValue}";
    }

    private static string GenerateSoqlWhereConditionForIsEqualToOperator(string fieldName, object value)
    {
        try
        {
            var dateTimeOffsetValue = (DateTimeOffset)value;
            return $"{fieldName} = {FormatDateTimeOffset(dateTimeOffsetValue)}";
        }
        catch (InvalidCastException)
        {
            try
            {
                var boolValue = bool.Parse((string)value);
                return $"{fieldName} = {boolValue.ToString().ToLower()}";
            }
            catch (FormatException)
            {
                try
                {
                    // Check if the value is a numeric string
                    if (double.TryParse((string)value, out var doubleValue) && !IsNumericString((string)value))
                    {
                        return $"{fieldName} = {doubleValue.ToString(CultureInfo.InvariantCulture)}";
                    }
                    else
                    {
                        return $"{fieldName} = '{value}'";
                    }
                }
                catch (FormatException)
                {
                    return $"{fieldName} = '{value}'";
                }
            }
        }
    }

    private static string GenerateSoqlWhereConditionForIsAfterOperator(string fieldName, object value)
    {
        var dateTimeOffsetValue = (DateTimeOffset) value;

        return $"{fieldName} > {FormatDateTimeOffset(dateTimeOffsetValue)}";
    }

    private static string GenerateSoqlWhereConditionForIsBeforeOperator(string fieldName, object value)
    {
        var dateTimeOffsetValue = (DateTimeOffset) value;

        return $"{fieldName} < {FormatDateTimeOffset(dateTimeOffsetValue)}";
    }

    private static string GenerateSoqlWhereConditionForExistsOperator(string fieldName)
    {
        return $"{fieldName} != null";
    }

    private static string GenerateSoqlWhereConditionForDoesNotExistOperator(string fieldName)
    {
        return $"{fieldName} = null";
    }

    private static string GenerateSoqlWhereConditionForContainsOperator(string fieldName, object value)
    {
        return $"{fieldName} LIKE '%{"25" + value}%'";
    }

    private static string GenerateSoqlWhereConditionForDoesNotContainOperator(string fieldName, object value)
    {
        return $"NOT ({fieldName} LIKE '%{"25" + value}%')";
    }

    private static string GenerateSoqlWhereConditionForIsGreaterThanOperator(string fieldName, object value)
    {
        var doubleValue = double.Parse((string) value);

        return $"{fieldName} > {doubleValue.ToString(CultureInfo.InvariantCulture)}";
    }

    private static string GenerateSoqlWhereConditionForIsLessThanOperator(string fieldName, object value)
    {
        var doubleValue = double.Parse((string) value);

        return $"{fieldName} < {doubleValue.ToString(CultureInfo.InvariantCulture)}";
    }

    private static (DateTimeOffset StartDateTime, DateTimeOffset EndDateTime) GetTimeSpanFromValueObject(object value)
    {
        DateTimeOffset[]? dateTimeOffsets;

        try
        {
            dateTimeOffsets = (DateTimeOffset[]) value;
        }
        catch (InvalidCastException)
        {
            var jArray = (JArray) value;

            dateTimeOffsets = jArray.ToObject<DateTimeOffset[]>();
        }

        if (dateTimeOffsets?.Length != 2)
        {
            throw new ArgumentException(
                "Value must be an array of two elements for the 'isBetween' search operator.");
        }

        return (dateTimeOffsets[0], dateTimeOffsets[1]);
    }

    private static SearchObjectConditionDuration GetSearchObjectConditionDurationFromValueObject(object value)
    {
        SearchObjectConditionDuration? duration;

        try
        {
            duration = (SearchObjectConditionDuration) value;
        }
        catch (InvalidCastException)
        {
            var jObject = (JObject) value;

            duration = jObject.ToObject<SearchObjectConditionDuration>();
        }

        if (duration is null)
        {
            throw new InvalidCastException();
        }

        return duration;
    }

    private static string FormatDateTimeOffset(DateTimeOffset dateTimeOffset)
    {
        return dateTimeOffset.ToString("yyyy-MM-ddTHH:mm:ssZ");
    }

    private static (DateTimeOffset StartDateTime, DateTimeOffset EndDateTime)
        GetTimeSpanFromSearchObjectConditionDuration(SearchObjectConditionDuration duration)
    {
        var currentDateTime = DateTimeOffset.UtcNow;
        DateTimeOffset startDateTime;
        DateTimeOffset endDateTime;

        switch (duration.Unit)
        {
            case SearchObjectConditionDurationUnits.Second:
                startDateTime = currentDateTime.AddSeconds(-duration.Value);
                endDateTime = currentDateTime;
                break;
            case SearchObjectConditionDurationUnits.Minute:
                startDateTime = currentDateTime.AddMinutes(-duration.Value);
                endDateTime = currentDateTime;
                break;
            case SearchObjectConditionDurationUnits.Hour:
                startDateTime = currentDateTime.AddHours(-duration.Value);
                endDateTime = currentDateTime;
                break;
            case SearchObjectConditionDurationUnits.Day:
                startDateTime = currentDateTime.AddDays(-duration.Value);
                endDateTime = currentDateTime;
                break;
            case SearchObjectConditionDurationUnits.Month:
                startDateTime = currentDateTime.AddMonths(-(int)duration.Value);
                endDateTime = currentDateTime;
                break;
            default:
                throw new ArgumentException($"Unsupported search object condition duration unit: '{duration.Unit}'");
        }

        return (startDateTime, endDateTime);
    }

    private static int GetSalesforceDayInWeekValue(string weekday)
    {
        return weekday switch
        {
            SearchObjectConditionWeekdays.Sunday => 0,
            SearchObjectConditionWeekdays.Monday => 1,
            SearchObjectConditionWeekdays.Tuesday => 2,
            SearchObjectConditionWeekdays.Wednesday => 3,
            SearchObjectConditionWeekdays.Thursday => 4,
            SearchObjectConditionWeekdays.Friday => 5,
            SearchObjectConditionWeekdays.Saturday => 6,
            _ => throw new ArgumentException($"Unsupported search object condition weekday: '{weekday}'")
        };
    }

    private static bool IsNumericString(string value)
    {
        // Check if the string contains only digits
        return value.All(char.IsDigit);
    }
}