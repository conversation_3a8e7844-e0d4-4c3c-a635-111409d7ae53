﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyTaskResponse
{
    [JsonProperty(PropertyName = "data")]
    public ApifyTaskResponseData Data { get; set; }

    [JsonConstructor]
    public ApifyTaskResponse(ApifyTaskResponseData data)
    {
        Data = data;
    }
}

public sealed class ApifyTaskResponseData
{
    [JsonProperty(PropertyName = "id")]
    public string Id { get; set; }

    [JsonProperty(PropertyName = "userId")]
    public string UserId { get; set; }

    [JsonProperty(PropertyName = "actId")]
    public string ActId { get; set; }

    [JsonProperty(PropertyName = "name")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "title")]
    public string Title { get; set; }

    [JsonProperty(PropertyName = "username")]
    public string Username { get; set; }

    [JsonProperty(PropertyName = "createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty(PropertyName = "modifiedAt")]
    public DateTime? ModifiedAt { get; set; }

    [JsonProperty(PropertyName = "stats")]
    public ApifyTaskResponseDataStats Stats { get; set; }

    [JsonProperty(PropertyName = "input")]
    public ApifyTaskResponseDataInput Input { get; set; }

    [JsonConstructor]
    public ApifyTaskResponseData(
        string id,
        string userId,
        string actId,
        string name,
        string title,
        string username,
        DateTime? createdAt,
        DateTime? modifiedAt,
        ApifyTaskResponseDataStats stats,
        ApifyTaskResponseDataInput input)
    {
        Id = id;
        UserId = userId;
        ActId = actId;
        Name = name;
        Title = title;
        Username = username;
        CreatedAt = createdAt;
        ModifiedAt = modifiedAt;
        Stats = stats;
        Input = input;
    }
}

public sealed class ApifyTaskResponseDataStats
{
    [JsonProperty(PropertyName = "totalRuns")]
    public int TotalRuns { get; set; }

    [JsonProperty(PropertyName = "lastRunStartedAt")]
    public DateTime LastRunStartedAt { get; set; }

    [JsonConstructor]
    public ApifyTaskResponseDataStats(
        int totalRuns,
        DateTime lastRunStartedAt)
    {
        TotalRuns = totalRuns;
        LastRunStartedAt = lastRunStartedAt;
    }
}

public sealed class ApifyTaskResponseDataInput : IWebScraperSetting
{
    [JsonProperty(PropertyName = "dynamicContentWaitSecs")]
    public int DynamicContentWaitSecs { get; set; }

    [JsonProperty(PropertyName = "maxCrawlDepth")]
    public int MaxCrawlDepth { get; set; }

    [JsonProperty(PropertyName = "maxCrawlPages")]
    public int MaxCrawlPages { get; set; }

    [JsonProperty(PropertyName = "MaxConcurrentPages")]
    public int MaxConcurrentPages { get; set; }

    [JsonProperty(PropertyName = "startUrls")]
    public StartUrl[] StartUrls { get; set; }

    [JsonProperty(PropertyName = "initialCookies")]
    public dynamic InitialCookies { get; set; }

    [JsonConstructor]
    public ApifyTaskResponseDataInput(
        int dynamicContentWaitSecs,
        int maxCrawlDepth,
        int maxCrawlPages,
        int maxConcurrentPages,
        StartUrl[] startUrls,
        dynamic initialCookies)
    {
        DynamicContentWaitSecs = dynamicContentWaitSecs;
        MaxCrawlDepth = maxCrawlDepth;
        MaxCrawlPages = maxCrawlPages;
        MaxConcurrentPages = maxConcurrentPages;
        StartUrls = startUrls;
        InitialCookies = initialCookies;
    }
}