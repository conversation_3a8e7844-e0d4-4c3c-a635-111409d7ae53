using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaId
    : ITrigger<
        GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaId.GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput,
        GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaId.GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput>
{
    private readonly IMigrationService _migrationService;

    public GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaId(IMigrationService migrationService)
    {
        _migrationService = migrationService;
    }

    public class GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("user_access_token")]
        public string UserAccessToken { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput(
            string sleekflowCompanyId,
            string userAccessToken,
            string facebookWabaId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UserAccessToken = userAccessToken;
            FacebookWabaId = facebookWabaId;
        }
    }

    public class GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput
    {
        [JsonProperty("phone_numbers")]
        public List<WhatsappPhoneNumberDetail> PhoneNumbers { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput(List<WhatsappPhoneNumberDetail> phoneNumbers)
        {
            PhoneNumbers = phoneNumbers;
        }
    }

    public async Task<GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput> F(
        GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput getUserBusinessPhoneNumbersByWabaIdInput)
    {
        return new GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput(
            await _migrationService.GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdAsync(
                getUserBusinessPhoneNumbersByWabaIdInput.SleekflowCompanyId,
                getUserBusinessPhoneNumbersByWabaIdInput.UserAccessToken,
                getUserBusinessPhoneNumbersByWabaIdInput.FacebookWabaId));
    }
}