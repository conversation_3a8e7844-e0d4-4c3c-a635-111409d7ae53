using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class SummarizedSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonConstructor]
    public SummarizedSnapshot(List<SfChatEntry> conversationContext, string outputMessage)
    {
        ConversationContext = conversationContext;
        OutputMessage = outputMessage;
    }
}