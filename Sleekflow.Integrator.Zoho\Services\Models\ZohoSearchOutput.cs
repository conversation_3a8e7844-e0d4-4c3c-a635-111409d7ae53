using Newtonsoft.Json;

namespace Sleekflow.Integrator.Zoho.Services.Models;

public class ZohoSearchOutput
{
    [JsonProperty("data")]
    public List<Dictionary<string, object?>>? Data { get; set; }

    [JsonProperty("info")]
    public SearchInfo? Info { get; set; }

    public class SearchInfo
    {
        [JsonProperty("count")]
        public int? Count { get; set; }

        [JsonProperty("more_records")]
        public bool? MoreRecords { get; set; }

        [JsonProperty("page")]
        public int? Page { get; set; }
    }
}