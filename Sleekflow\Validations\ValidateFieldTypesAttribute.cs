using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Validations;

public class ValidateFieldTypesAttribute : ValidationAttribute
{
    private readonly string[] _searchFieldTypes;

    public ValidateFieldTypesAttribute(string[] searchFieldTypes)
    {
        _searchFieldTypes = searchFieldTypes;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null || !_searchFieldTypes.Contains(value))
        {
            throw new ArgumentException($"Invalid field input {value}");
        }

        return ValidationResult.Success;
    }
}