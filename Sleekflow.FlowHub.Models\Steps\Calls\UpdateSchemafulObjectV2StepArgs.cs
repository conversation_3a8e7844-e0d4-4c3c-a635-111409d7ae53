﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateSchemafulObjectV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-schemaful-object";

    [Required]
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [Required]
    [JsonProperty("record_source")]
    public string RecordSource { get; set; }

    [JsonProperty("primary_property_value__expr")]
    public string? PrimaryPropertyValueExpr { get; set; }

    [JsonProperty("custom_object_properties__id_expr_set")]
    public HashSet<CustomObjectPropertyIdValuePair> CustomObjectPropertiesIdExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.CustomObject;

    [JsonConstructor]
    public UpdateSchemafulObjectV2StepArgs(
        string schemaId,
        string recordSource,
        string? primaryPropertyValueExpr,
        HashSet<CustomObjectPropertyIdValuePair> customObjectPropertiesIdExprSet)
    {
        SchemaId = schemaId;
        RecordSource = recordSource;
        PrimaryPropertyValueExpr = primaryPropertyValueExpr;
        CustomObjectPropertiesIdExprSet = customObjectPropertiesIdExprSet;
    }
}