using MailKit.Security;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.OnPremise.Authentications;
using Sleekflow.EmailHub.OnPremise.Clients;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.EmailHub.OnPremise.Authentications;

public interface IOnPremiseAuthenticationService : IEmailAuthenticationService
{
}

public class OnPremiseAuthenticationService : IScopedService, IOnPremiseAuthenticationService
{
    private readonly ILogger<OnPremiseAuthenticationService> _logger;
    private readonly IEmailAuthenticationRepository _emailAuthenticationRepository;
    private readonly IIdService _idService;

    public OnPremiseAuthenticationService(
        ILogger<OnPremiseAuthenticationService> logger,
        IEmailAuthenticationRepository emailAuthenticationRepository,
        IIdService idService)
    {
        _logger = logger;
        _emailAuthenticationRepository = emailAuthenticationRepository;
        _idService = idService;
    }

    public async Task<EmailAuthentication> GetAuthenticationAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string? serverType = null,
        CancellationToken cancellationToken = default)
    {
        var authentication = (await _emailAuthenticationRepository.GetObjectsAsync(
            x => x.EmailAddress == emailAddress &&
                 x.SleekflowCompanyId == sleekflowCompanyId &&
                 x.ProviderName == ProviderNames.OnPremise &&
                 ((OnPremiseAuthenticationMetadata)x.EmailAuthenticationMetadata)
                 .ProtocolType == serverType,
            cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (authentication == null || !authentication.IsAuthenticated)
        {
            throw new SfUnauthorizedException();
        }

        return authentication;
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? extendedAuthMetadata,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Started OnPremise AuthenticationAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        if (extendedAuthMetadata == null ||
            !extendedAuthMetadata.ContainsKey("server_type") ||
            !extendedAuthMetadata.ContainsKey("server_name") ||
            !extendedAuthMetadata.ContainsKey("port_number") ||
            !extendedAuthMetadata.ContainsKey("username") ||
            !extendedAuthMetadata.ContainsKey("password"))
        {
            _logger.LogError(
                "OnPremise: Authentication fails - necessary information is missing, be sure to " +
                "supply server_type, server_name, port_number, username and password");

            throw new ArgumentNullException(nameof(extendedAuthMetadata));
        }

        if (!int.TryParse(extendedAuthMetadata["port_number"], out var portNumber))
        {
            throw new ArgumentException(nameof(portNumber));
        }

        var onPremiseAuthenticationMetadata = new OnPremiseAuthenticationMetadata(
            extendedAuthMetadata["server_type"],
            extendedAuthMetadata["server_name"],
            portNumber,
            extendedAuthMetadata["username"],
            extendedAuthMetadata["password"]);

        string authenticationId;

        try
        {
            authenticationId = (await GetAuthenticationAsync(sleekflowCompanyId, emailAddress, extendedAuthMetadata["server_type"], cancellationToken)).Id;
        }
        catch (SfUnauthorizedException)
        {
            authenticationId = _idService.GetId("EmailAuthentication");
        }

        var emailAuthentication = new EmailAuthentication(
            authenticationId,
            sleekflowCompanyId,
            emailAddress,
            true,
            onPremiseAuthenticationMetadata,
            ProviderNames.OnPremise);

        try
        {
            using var client = new OnPremiseAuthenticationClient(
                extendedAuthMetadata["server_type"],
                onPremiseAuthenticationMetadata.ServerName,
                onPremiseAuthenticationMetadata.Username,
                onPremiseAuthenticationMetadata.Password,
                onPremiseAuthenticationMetadata.PortNumber,
                SecureSocketOptions.Auto);
            await client.RunAsync(cancellationToken);
            client.Exit();
        }
        catch (Exception)
        {
            throw new SfUnauthorizedException();
        }

        await _emailAuthenticationRepository.UpsertAsync(emailAuthentication, emailAuthentication.Id, cancellationToken: cancellationToken);

        return "OnPremise Authentication Successes!";
    }
}