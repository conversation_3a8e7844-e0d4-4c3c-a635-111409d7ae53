using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.UserEventHubDb;

public interface IUserEventHubDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class UserEventHubDbConfig : IConfig, IUserEventHubDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public UserEventHubDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_USER_EVENT_HUB_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_USER_EVENT_HUB_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_USER_EVENT_HUB_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_USER_EVENT_HUB_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_USER_EVENT_HUB_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_USER_EVENT_HUB_DB_DATABASE_ID");
    }
}