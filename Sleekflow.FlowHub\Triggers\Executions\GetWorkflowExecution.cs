﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetWorkflowExecution : ITrigger
{
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IStateService _stateService;
    private readonly IStepExecutionService _stepExecutionService;

    public GetWorkflowExecution(
        IWorkflowExecutionService workflowExecutionService,
        IStateService stateService,
        IStepExecutionService stepExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
        _stateService = stateService;
        _stepExecutionService = stepExecutionService;
    }

    public class GetWorkflowExecutionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionInput(
            string sleekflowCompanyId,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StateId = stateId;
        }
    }

    public class GetWorkflowExecutionOutput
    {
        [JsonProperty("workflow_execution")]
        public WorkflowExecutionDto WorkflowExecution { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionOutput(
            WorkflowExecutionDto workflowExecution)
        {
            WorkflowExecution = workflowExecution;
        }
    }

    public async Task<GetWorkflowExecutionOutput> F(
        GetWorkflowExecutionInput getWorkflowExecutionInput)
    {
        var sleekflowCompanyId = getWorkflowExecutionInput.SleekflowCompanyId;
        var stateId = getWorkflowExecutionInput.StateId;

        var state = await _stateService.GetOrDefaultStateByIdAsync(
            sleekflowCompanyId,
            stateId);

        if (state is null)
        {
            throw new SfWorkflowStateNotFoundException(
                sleekflowCompanyId,
                stateId);
        }

        var workflowExecutions = await _workflowExecutionService.GetWorkflowExecutionsAsync(
            sleekflowCompanyId,
            new() { stateId });

        var stateFailureRemarks = await GetStateFailureRemarks(state, sleekflowCompanyId);

        var remarks = !string.IsNullOrWhiteSpace(state.StateReasonCode) || stateFailureRemarks.Count == 0
            ? GetRemarkFromStateReasonCode(state)
            : stateFailureRemarks;

        var workflowExecutionDto = new WorkflowExecutionDto(
            workflowExecutions
                .Where(x => x.WorkflowExecutionStatus is
                    WorkflowExecutionStatuses.Started
                    or WorkflowExecutionStatuses.Blocked
                    or WorkflowExecutionStatuses.Restricted)
                .MinBy(we => we.CreatedAt)!,
            workflowExecutions.MaxBy(we => we.CreatedAt)!,
            remarks);

        return new GetWorkflowExecutionOutput(workflowExecutionDto);
    }

    private static List<WorkflowExecutionDtoRemark> GetRemarkFromStateReasonCode(ProxyState? state)
    {
        var result = new List<WorkflowExecutionDtoRemark>();

        var workflowExecutionDtoRemark
            = state?.StateStatus switch
            {
                StateStatuses.Cancelled => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? StateReasonCodes.ManualCancellation,
                    string.Empty,
                    null),
                StateStatuses.Blocked => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? StateReasonCodes.EnrollmentRateLimited,
                    string.Empty,
                    null),
                StateStatuses.Restricted => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? StateReasonCodes.EnrollmentUsageLimitExceeded,
                    string.Empty,
                    null),
                StateStatuses.Failed => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? UserFriendlyErrorCodes.InternalError,
                    string.Empty,
                    null),
                _ => null
            };

        if (workflowExecutionDtoRemark is not null)
        {
            result.Add(workflowExecutionDtoRemark);
        }

        return result;
    }

    private async Task<List<WorkflowExecutionDtoRemark>> GetStateFailureRemarks(
        ProxyState state,
        string sleekflowCompanyId)
    {
        if (state.StateStatus != StateStatuses.Failed)
        {
            return new List<WorkflowExecutionDtoRemark>();
        }

        var failedStateStepExecutions = await _stepExecutionService.GetStateStepExecutionsAsync(
            sleekflowCompanyId,
            new List<string>() { state.Id },
            new List<string>()
            {
                StepExecutionStatuses.Failed, StepExecutionStatuses.Timeout,
            });

        var stateFailureRemarks = failedStateStepExecutions
            .Select(
                se => new WorkflowExecutionDtoRemark(
                    se.Error?.ErrorMessage ?? string.Empty,
                    se.Error?.ErrorCode ?? string.Empty,
                    se.StepId,
                    se.StepNodeId))
            .ToList();

        return stateFailureRemarks;
    }
}