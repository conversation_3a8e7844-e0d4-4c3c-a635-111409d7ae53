using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class GetStore
    : ITrigger<
        GetStore.GetStoreInput,
        GetStore.GetStoreOutput>
{
    private readonly IStoreService _storeService;

    public GetStore(
        IStoreService storeService)
    {
        _storeService = storeService;
    }

    public class GetStoreInput
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetStoreInput(
            string id,
            string sleekflowCompanyId)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetStoreOutput
    {
        [JsonProperty("store")]
        public StoreDto Store { get; set; }

        [JsonConstructor]
        public GetStoreOutput(StoreDto store)
        {
            Store = store;
        }
    }

    public async Task<GetStoreOutput> F(GetStoreInput getStoreInput)
    {
        var store = await _storeService.GetStoreAsync(getStoreInput.Id, getStoreInput.SleekflowCompanyId);

        return new GetStoreOutput(new StoreDto(store));
    }
}