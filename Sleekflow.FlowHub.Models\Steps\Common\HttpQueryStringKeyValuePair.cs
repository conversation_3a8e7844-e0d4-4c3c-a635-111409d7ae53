using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Steps.Common;

public class HttpQueryStringKeyValuePair
{
    [JsonProperty("query_string_key")]
    public string QueryStringKey { get; set; }

    [JsonProperty("query_string_value__expr")]
    public string? QueryStringValueExpr { get; set; }

    [JsonConstructor]
    public HttpQueryStringKeyValuePair(
        string queryStringKey,
        string? queryStringValueExpr)
    {
        QueryStringKey = queryStringKey;
        QueryStringValueExpr = queryStringValueExpr;
    }
}
