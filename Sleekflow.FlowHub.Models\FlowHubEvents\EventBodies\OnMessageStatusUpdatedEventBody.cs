using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnMessageStatusUpdatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnMessageStatusUpdated; }
    }

    [Required]
    [JsonProperty("message")]
    public OnMessageStatusUpdatedEventBodyMessage Message { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonProperty("channel")]
    public string Channel { get; set; }

    [Required]
    [JsonProperty("channel_id")]
    public string ChannelId { get; set; }

    [Required]
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [Required]
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("message_unique_id")]
    public string MessageUniqueId { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [JsonProperty("is_sent_from_sleekflow")]
    public bool IsSentFromSleekflow => true;

    [JsonConstructor]
    public OnMessageStatusUpdatedEventBody(
        DateTimeOffset createdAt,
        OnMessageStatusUpdatedEventBodyMessage message,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        string channel,
        string channelId,
        string conversationId,
        string messageId,
        string messageUniqueId,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId)
        : base(createdAt)
    {
        Message = message;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        Channel = channel;
        ChannelId = channelId;
        ConversationId = conversationId;
        MessageId = messageId;
        MessageUniqueId = messageUniqueId;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
    }
}

public class OnMessageStatusUpdatedEventBodyMessage : BaseMessage
{
    [JsonProperty("quoted_message")]
    public QuotedMessage? QuotedMessage { get; set; }

    [JsonProperty("analytic_tags")]
    public string? AnalyticTags { get; set; }

    [JsonConstructor]
    public OnMessageStatusUpdatedEventBodyMessage(
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        MessageBody messageBody,
        QuotedMessage? quotedMessage,
        string analyticTags,
        string messageType,
        string messageContent,
        string messageStatus,
        string messageDeliveryType)
        : base(
            createdAt,
            updatedAt,
            messageBody,
            messageType,
            messageContent,
            messageStatus,
            messageDeliveryType)
    {
        QuotedMessage = quotedMessage;
        AnalyticTags = analyticTags;
    }
}