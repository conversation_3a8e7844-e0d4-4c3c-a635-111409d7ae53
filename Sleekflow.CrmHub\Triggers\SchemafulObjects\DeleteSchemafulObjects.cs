﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class DeleteSchemafulObjects : ITrigger<DeleteSchemafulObjects.DeleteSchemafulObjectsInput, DeleteSchemafulObjects.DeleteSchemafulObjectsOutput>
{
    private readonly ISchemafulObjectService _schemafulObjectService;

    public DeleteSchemafulObjects(ISchemafulObjectService schemafulObjectService)
    {
        _schemafulObjectService = schemafulObjectService;
    }

    public class DeleteSchemafulObjectsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("ids")]
        [Validations.ValidateArray]
        public List<string> Ids { get; set; }

        [Required]
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public DeleteSchemafulObjectsInput(List<string> ids, string schemaId, string sleekflowCompanyId)
        {
            Ids = ids;
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class DeleteSchemafulObjectsOutput
    {
        [JsonProperty("deleted_count")]
        public int DeletedCount { get; set; }

        [JsonConstructor]
        public DeleteSchemafulObjectsOutput(int deletedCount)
        {
            DeletedCount = deletedCount;
        }
    }

    public async Task<DeleteSchemafulObjectsOutput> F(DeleteSchemafulObjectsInput deleteSchemafulObjectsInput)
    {
        return new DeleteSchemafulObjectsOutput(
            await _schemafulObjectService.DeleteSchemafulObjectsAsync(
                deleteSchemafulObjectsInput.Ids,
                deleteSchemafulObjectsInput.SchemaId,
                deleteSchemafulObjectsInput.SleekflowCompanyId));
    }
}