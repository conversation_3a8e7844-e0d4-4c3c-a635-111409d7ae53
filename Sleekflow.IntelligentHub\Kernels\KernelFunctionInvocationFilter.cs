﻿using System.Diagnostics;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using OpenAI.Chat;
using Serilog;
using Sleekflow.IntelligentHub.Models.Kernels;
using ILogger = Serilog.ILogger;

namespace Sleekflow.IntelligentHub.Kernels;

/**
 * Logs the execution time of a function and the number of tokens used.
 */
public class KernelFunctionInvocationFilter : IFunctionInvocationFilter
{
    private readonly ILogger _logger = Log.Logger.ForContext<KernelFunctionInvocationFilter>();

    public async Task OnFunctionInvocationAsync(
        FunctionInvocationContext context,
        Func<FunctionInvocationContext, Task> next)
    {
        var stopwatch = Stopwatch.StartNew();

        await next(context);
        stopwatch.Stop();

        var tokenUsage = GetTokenUsage(context.Result);

        _logger.Information(
            "{PluginName}.{FunctionName} executed in {ElapsedMilliseconds} ms | Tokens: {InputTokenCount} in, {OutputTokenCount} out, {TotalTokenCount} total",
            context.Function.PluginName,
            context.Function.Name,
            stopwatch.ElapsedMilliseconds,
            tokenUsage?.InputTokenCount,
            tokenUsage?.OutputTokenCount,
            tokenUsage?.TotalTokenCount);
    }

    private static KernelTokenUsage? GetTokenUsage(FunctionResult result)
    {
        try
        {
            if (result.Metadata?.TryGetValue("Usage", out var usage) == true && usage is ChatTokenUsage chatTokenUsage)
            {
                return new KernelTokenUsage
                {
                    InputTokenCount = chatTokenUsage.InputTokenCount,
                    OutputTokenCount = chatTokenUsage.OutputTokenCount,
                    TotalTokenCount = chatTokenUsage.TotalTokenCount,
                };
            }
#pragma warning disable SKEXP0070
            if (result.Metadata is GeminiMetadata metadata)
            {
                return new KernelTokenUsage
                {
                    InputTokenCount = metadata.PromptTokenCount,
                    OutputTokenCount = metadata.TotalTokenCount - metadata.PromptTokenCount,
                    TotalTokenCount = metadata.TotalTokenCount,
                };
            }
#pragma warning restore SKEXP0070
        }
        catch (Exception e)
        {
            Log.Logger.Error(e, "Error while getting token usage");
        }

        return null;
    }
}