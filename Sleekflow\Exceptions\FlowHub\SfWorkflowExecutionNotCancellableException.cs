﻿namespace Sleekflow.Exceptions.FlowHub;

public sealed class SfWorkflowExecutionNotCancellableException : ErrorCodeException
{
    public string StateId { get; }

    public string StateStatus { get; }

    public SfWorkflowExecutionNotCancellableException(
        string stateId,
        string stateStatus)
        : base(
            ErrorCodeConstant.SfWorkflowExecutionStatusNotCancellableException,
            $"Workflow execution of state id '{stateId}' cannot be cancelled with its current status '{stateStatus}'")
    {
        StateId = stateId;
        StateStatus = stateStatus;
    }
}