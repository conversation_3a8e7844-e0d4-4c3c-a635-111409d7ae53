using System.Net.Mime;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.TravisBackend;

namespace Sleekflow.FlowHub.TravisBackend;

public interface ITravisBackendHttpClient
{
    Task<GetCompanyUsageCycleOutput> GetCompanyUsageCycleAsync(string? domain, GetCompanyUsageCycleInput input);

    Task<ScaleWorkflowExecutionLimitOutput> ScaleWorkflowExecutionLimitAsync(string? domain, ScaleWorkflowExecutionLimitInput input);
}

public class TravisBackendHttpClient : ITravisBackendHttpClient, IScopedService
{
    private readonly HttpClient _httpClient;

    private static readonly IAppConfig AppConfig = new AppConfig();

    public TravisBackendHttpClient(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("travis-backend");
    }

    public Task<GetCompanyUsageCycleOutput> GetCompanyUsageCycleAsync(string? domain, GetCompanyUsageCycleInput input)
    {
        const string path = "ServicesCall/GetCompanyUsageCycle";
        return PostAsync<GetCompanyUsageCycleInput, GetCompanyUsageCycleOutput>(domain, path, input);
    }

    public Task<ScaleWorkflowExecutionLimitOutput> ScaleWorkflowExecutionLimitAsync(string? domain, ScaleWorkflowExecutionLimitInput input)
    {
        const string path = "ServicesCall/ScaleWorkflowExecutionLimit";
        return PostAsync<ScaleWorkflowExecutionLimitInput, ScaleWorkflowExecutionLimitOutput>(domain, path, input);
    }

    private async Task<TOutput> PostAsync<TInput, TOutput>(string? domain, string path, TInput input)
    {
        var basePath = !string.IsNullOrWhiteSpace(domain) ? $"{domain}/FlowHub/Internals" : AppConfig.CoreInternalsEndpoint;
        var url = $"{basePath}/{path}";

        var requestBody = JsonConvert.SerializeObject(input, JsonConfig.DefaultJsonSerializerSettings);
        var httpContent = new StringContent(requestBody, Encoding.UTF8, MediaTypeNames.Application.Json);

        var httpResponseMessage = await _httpClient.PostAsync(url, httpContent);
        httpResponseMessage.EnsureSuccessStatusCode();

        var responseStr = await httpResponseMessage.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<TOutput>(responseStr, JsonConfig.DefaultJsonSerializerSettings)!;
    }
}