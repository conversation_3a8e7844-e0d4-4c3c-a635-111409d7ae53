using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateContactStepExecutor : IStepExecutor
{
}

public class CreateContactStepExecutor
    : GeneralStepExecutor<CallStep<CreateContactStepArgs>>,
        ICreateContactStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public CreateContactStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "CreateContact",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class CreateContactInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("phone_number")]
        [Required]
        public string PhoneNumber { get; set; }

        [JsonProperty("contact_properties")]
        [Validations.ValidateObject]
        public Dictionary<string, object?>? ContactProperties { get; set; }

        [JsonProperty("workflow_trigger_event_body_event_name")]
        [Required]
        public string WorkflowTriggerEventBodyEventName { get; set; }

        [JsonConstructor]
        public CreateContactInput(
            string stateId,
            StateIdentity stateIdentity,
            string phoneNumber,
            Dictionary<string, object?>? contactProperties,
            string workflowTriggerEventBodyEventName)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            PhoneNumber = phoneNumber;
            ContactProperties = contactProperties;
            WorkflowTriggerEventBodyEventName = workflowTriggerEventBodyEventName;
        }
    }

    private async Task<CreateContactInput> GetArgs(
        CallStep<CreateContactStepArgs> callStep,
        ProxyState state)
    {
        var phoneNumber = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactPhoneNumberExpr)
                                  ?? callStep.Args.ContactPhoneNumberExpr);

        var contactProperties =
            callStep.Args.ContactPropertiesExprDict is null
                ? null
                : await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.ContactPropertiesExprDict);

        return new CreateContactInput(
            state.Id,
            state.Identity,
            phoneNumber,
            contactProperties,
            state.TriggerEventBody.EventName);
    }
}