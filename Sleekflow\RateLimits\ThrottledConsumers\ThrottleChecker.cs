﻿using Sleekflow.DependencyInjection;
using Sleekflow.RateLimits.LuaScripts;
using Sleekflow.RateLimits.RateLimiters;

namespace Sleekflow.RateLimits.ThrottledConsumers;

public interface IThrottleChecker
{
    Task<bool> CheckThrottleConditionAsync(string keyName, string rateLimitAlgorithm, ILuaScriptParam luaScriptParam);

    Task<(bool ShouldThrottle, int RemainLimit)> CheckThrottleConditionWithRemainLimitAsync(
        string keyName,
        string rateLimitAlgorithm,
        ILuaScriptParam luaScriptParam);
}

public class ThrottleChecker : ISingletonService, IThrottleChecker
{
    private readonly IRateLimiterFactory _rateLimiterFactory;

    public ThrottleChecker(IRateLimiterFactory rateLimiterFactory)
    {
        _rateLimiterFactory = rateLimiterFactory;
    }

    public async Task<bool> CheckThrottleConditionAsync(
        string keyName,
        string rateLimitAlgorithm,
        ILuaScriptParam luaScriptParam)
    {
        var rateLimiter = (IAllowanceRateLimiter) _rateLimiterFactory.CreateRateLimiter(rateLimitAlgorithm);
        var isAllowed = await rateLimiter.IsAllowedAsync(keyName, luaScriptParam);
        return !isAllowed;
    }

    public async Task<(bool ShouldThrottle, int RemainLimit)> CheckThrottleConditionWithRemainLimitAsync(
        string keyName,
        string rateLimitAlgorithm,
        ILuaScriptParam luaScriptParam)
    {
        var rateLimiter = (IAllowanceRateLimiter) _rateLimiterFactory.CreateRateLimiter(rateLimitAlgorithm);
        var (isAllowed, remaining) = await rateLimiter.GetRemainingAsync(keyName, luaScriptParam);
        return (!isAllowed, remaining);
    }
}