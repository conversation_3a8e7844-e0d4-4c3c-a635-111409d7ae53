﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Schemas;

public class SchemaDto : EntityDto, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(Schema.PropertyNameDisplayName)]
    public string DisplayName { get; set; }

    [JsonProperty(Schema.PropertyNameUniqueName)]
    public string UniqueName { get; set; }

    [JsonProperty(Schema.PropertyNameRelationshipType)]
    public string RelationshipType { get; }

    [JsonProperty(Schema.PropertyNameIsEnabled)]
    public bool IsEnabled { get; set; }

    [JsonProperty(Schema.PropertyNameSortingWeight)]
    public ushort SortingWeight { get; set; }

    [JsonProperty(Schema.PropertyNamePrimaryProperty)]
    public PrimaryProperty PrimaryProperty { get; set; }

    [JsonProperty(Schema.PropertyNameProperties)]
    public List<Property> Properties { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(Schema.PropertyNameSchemaAccessibilitySettings)]
    public SchemaAccessibilitySettings SchemaAccessibilitySettings { get; set; }

    [JsonConstructor]
    public SchemaDto(
        string id,
        string sleekflowCompanyId,
        string displayName,
        string uniqueName,
        string relationshipType,
        bool isEnabled,
        ushort sortingWeight,
        PrimaryProperty primaryProperty,
        List<Property> properties,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SchemaAccessibilitySettings schemaAccessibilitySettings)
        : base(id)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        DisplayName = displayName;
        UniqueName = uniqueName;
        RelationshipType = relationshipType;
        IsEnabled = isEnabled;
        SortingWeight = sortingWeight;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        SchemaAccessibilitySettings = schemaAccessibilitySettings;
        Properties = properties;
        PrimaryProperty = primaryProperty;
    }

    public SchemaDto(Schema schema)
        : this(
            schema.Id,
            schema.SleekflowCompanyId,
            schema.DisplayName,
            schema.UniqueName,
            schema.RelationshipType,
            schema.IsEnabled,
            schema.SortingWeight,
            schema.PrimaryProperty,
            schema.Properties,
            schema.CreatedAt,
            schema.UpdatedAt,
            schema.SchemaAccessibilitySettings)
    {
    }
}