using Newtonsoft.Json;

namespace Sleekflow.Models.Prompts;

public class ReplyGenerationContext
{
    [Json<PERSON>roperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [JsonProperty("contact_properties")]
    public Dictionary<string, string>? ContactProperties { get; set; }

    [JsonProperty("state_id")]
    public string? StateId { get; set; }

    [JsonProperty("input")]
    public string? Input { get; set; }

    [JsonIgnore]
#pragma warning disable JA1001
    public bool IsPlayground
#pragma warning restore JA1001
    {
        get
        {
            return StateId == null;
        }
    }

    [JsonConstructor]
    public ReplyGenerationContext(
        string sleekflowCompanyId,
        string contactId,
        Dictionary<string, string>? contactProperties,
        string? stateId,
        string? input)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ContactId = contactId;
        ContactProperties = contactProperties;
        StateId = stateId;
        Input = input;
    }
}