﻿using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetFacebookPageAccessTokenOutput
{
    [JsonProperty("facebook_page_access_token")]
    public string FacebookPageAccessToken { get; set; }

    [JsonConstructor]
    public GetFacebookPageAccessTokenOutput(string facebookPageAccessToken)
    {
        FacebookPageAccessToken = facebookPageAccessToken;
    }
}