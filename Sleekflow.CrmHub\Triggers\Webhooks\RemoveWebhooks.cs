﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Webhooks;

namespace Sleekflow.CrmHub.Triggers.Webhooks;

[TriggerGroup("Webhooks")]
public class RemoveWebhooks : ITrigger
{
    private readonly IWebhookService _webhookService;

    public RemoveWebhooks(
        IWebhookService webhookService)
    {
        _webhookService = webhookService;
    }

    public class RemoveWebhooksInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("event_type_name")]
        [Required]
        public string EventTypeName { get; set; }

        [JsonConstructor]
        public RemoveWebhooksInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string eventTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            EventTypeName = eventTypeName;
        }
    }

    public class RemoveWebhooksOutput
    {
        [JsonConstructor]
        public RemoveWebhooksOutput()
        {
        }
    }

    public async Task<RemoveWebhooksOutput> F(
        RemoveWebhooksInput removeWebhooksInput)
    {
        await _webhookService.RemoveWebhooksAsync(
            removeWebhooksInput.SleekflowCompanyId,
            removeWebhooksInput.EntityTypeName,
            removeWebhooksInput.EventTypeName,
            CancellationToken.None);

        return new RemoveWebhooksOutput();
    }
}