using System.Text;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Enriches chat context with data from Hubspot CRM.
/// </summary>
public class HubspotEnricher : IChatHistoryEnricher
{
    // Core dependencies
    private readonly IHubspotPlugin _hubspotPlugin;
    private readonly ILogger<HubspotEnricher> _logger;

    // Configuration properties
    private readonly string _emailFieldName;
    private readonly string _phoneFieldName;

    /// <summary>
    /// Initializes a new instance of the <see cref="HubspotEnricher"/> class.
    /// </summary>
    /// <param name="logger">Logger for the enricher.</param>
    /// <param name="hubspotPlugin">The Hubspot plugin for API interactions.</param>
    /// <param name="parameters">Configuration parameters from EnricherConfig.</param>
    public HubspotEnricher(
        ILogger<HubspotEnricher> logger,
        IHubspotPlugin hubspotPlugin,
        Dictionary<string, string> parameters)
    {
        _hubspotPlugin = hubspotPlugin;
        _logger = logger;

        // Get email field name (optional, with default)
        _emailFieldName = parameters.TryGetValue(HubspotEnricherConstants.EMAIL_FIELD_KEY, out var emailField) &&
                          !string.IsNullOrEmpty(emailField)
            ? emailField
            : "Email";

        // Get phone field name (optional, with default)
        _phoneFieldName = parameters.TryGetValue(HubspotEnricherConstants.PHONE_FIELD_KEY, out var phoneField) &&
                          !string.IsNullOrEmpty(phoneField)
            ? phoneField
            : "PhoneNumber";

        _logger.LogInformation(
            "Initialized HubspotEnricher with email field '{EmailField}' and phone field '{PhoneField}'",
            _emailFieldName,
            _phoneFieldName);
    }

    /// <summary>
    /// Gets the name of the section in the context message.
    /// </summary>
    public string GetContextSectionName() => EnricherSectionKeys.HUBSPOT_SECTION;

    public string GetContextSectionExplanation()
    {
        return
            $"{GetContextSectionName()} is a section that contains information about the contact's Hubspot data, including contact properties and scheduled meetings.";
    }

    /// <summary>
    /// Enriches chat context with Hubspot contact data and meeting history.
    /// </summary>
    public async Task<string> EnrichAsync(
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default)
    {
        if (contactProperties == null || !contactProperties.Any())
        {
            _logger.LogInformation("No contact properties available for Hubspot lookup");
            return "No contact properties available for Hubspot lookup";
        }

        try
        {
            // Extract search identifiers from contact properties
            var (email, phone) = ExtractContactIdentifiers(contactProperties);

            if (string.IsNullOrEmpty(email) && string.IsNullOrEmpty(phone))
            {
                _logger.LogInformation("No email or phone available for Hubspot lookup");
                return "No email or phone available for Hubspot lookup";
            }

            // Get contact properties and meetings
            var enrichedData = await RetrieveHubspotDataAsync(email, phone, kernel, cancellationToken);

            return enrichedData.Length > 0
                ? enrichedData.ToString()
                : "No Hubspot data found for this contact";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enriching chat history with Hubspot data");
            return "Failed to retrieve Hubspot data";
        }
    }

    /// <summary>
    /// Extracts email and phone from contact properties.
    /// </summary>
    private (string? Email, string? Phone) ExtractContactIdentifiers(Dictionary<string, string> contactProperties)
    {
        string? email = null;
        string? phone = null;

        // Extract email using configured field name
        if (contactProperties.TryGetValue(_emailFieldName, out var emailValue))
        {
            email = emailValue;
            _logger.LogDebug("Found email: {Email}", email);
        }

        // Extract phone using configured field name
        if (contactProperties.TryGetValue(_phoneFieldName, out var phoneValue))
        {
            phone = phoneValue;
            _logger.LogDebug("Found phone: {Phone}", phone);
        }

        return (email, phone);
    }

    /// <summary>
    /// Retrieves and formats data from Hubspot.
    /// </summary>
    private async Task<string> RetrieveHubspotDataAsync(
        string? email,
        string? phone,
        Kernel kernel,
        CancellationToken cancellationToken)
    {
        // Get contact properties
        _logger.LogInformation(
            "Retrieving Hubspot contact properties for email: {Email}, phone: {Phone}",
            email ?? "N/A",
            phone ?? "N/A");
        var contactPropertiesJson = await _hubspotPlugin.GetContactPropertiesAsync(
            kernel,
            email,
            phone,
            cancellationToken);

        // Get meetings
        _logger.LogInformation(
            "Retrieving Hubspot meetings for email: {Email}, phone: {Phone}",
            email ?? "N/A",
            phone ?? "N/A");
        var meetingsJson = await _hubspotPlugin.GetContactMeetingsAsync(
            kernel,
            email,
            phone,
            cancellationToken);

        var strBuilder = new StringBuilder();

        if (contactPropertiesJson is not null)
        {
            strBuilder.AppendLine("# Contact Properties");
            strBuilder.AppendLine(contactPropertiesJson);
        }

        if (meetingsJson is not null)
        {
            strBuilder.AppendLine("# Scheduled Meetings");
            strBuilder.AppendLine(meetingsJson);
        }

        if (contactPropertiesJson is null && meetingsJson is null)
        {
            return "No Hubspot data found for this contact.";
        }

        _logger.LogInformation(
            "Successfully retrieved Hubspot data for email: {Email}, phone: {Phone}",
            email ?? "N/A",
            phone ?? "N/A");

        return strBuilder.ToString();
    }
}