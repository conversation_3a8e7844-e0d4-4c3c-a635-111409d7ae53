using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetOrCreateWorkflowWebhookTrigger : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;
    private readonly IIdService _idService;

    public GetOrCreateWorkflowWebhookTrigger(
        IWorkflowService workflowService,
        IWorkflowWebhookTriggerService workflowWebhookTriggerService,
        IIdService idService)
    {
        _workflowService = workflowService;
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
        _idService = idService;
    }

    public class GetOrCreateWorkflowWebhookTriggerInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameWorkflowId)]
        [Required]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public GetOrCreateWorkflowWebhookTriggerInput(
            string sleekflowCompanyId,
            string workflowId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class GetOrCreateWorkflowWebhookTriggerOutput
    {
        [JsonProperty("webhook_trigger_id")]
        public string WebHookTriggerId { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameValidationToken)]
        public string ValidationToken { get; set; }

        [JsonProperty("object_type")]
        [AllowedStringValues(
            isIgnoreCase: false,
            "Contact",
            "Contact.Id",
            "Contact.PhoneNumber",
            "Contact.Email")]
        public string ObjectType { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameObjectIdExpression)]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("webhook_url")]
        public string WebhookUrl { get; set; }

        [JsonProperty("sample_post_request")]
        public string SamplePostRequest { get; set; }

        [JsonConstructor]
        public GetOrCreateWorkflowWebhookTriggerOutput(
            WorkflowWebhookTrigger workflowWebhookTrigger)
        {
            WebHookTriggerId = workflowWebhookTrigger.Id;
            ValidationToken = workflowWebhookTrigger.ValidationToken;
            ObjectType = workflowWebhookTrigger.ObjectType;
            ObjectIdExpression = workflowWebhookTrigger.ObjectIdExpression;

            var frontdoorHostname = Environment.GetEnvironmentVariable("FRONTDOOR_HOSTNAME") ?? string.Empty;
            WebhookUrl = $"https://{frontdoorHostname}/v1/flow-hub/Public/e";

            SamplePostRequest = $@"curl -X POST '{WebhookUrl}' \
                -H 'X-Sleekflow-Workflow-Webhook-Trigger-Id: {workflowWebhookTrigger.Id}' \
                -H 'X-Sleekflow-Workflow-Validation-Token: {workflowWebhookTrigger.ValidationToken}' \
                -H 'Content-Type: application/json' \
                -d '{{ Your JSON Data here }}'";

        }
    }

    public async Task<GetOrCreateWorkflowWebhookTriggerOutput> F(
        GetOrCreateWorkflowWebhookTriggerInput input)
    {

        var workflowWebHookTriggers =
            await _workflowWebhookTriggerService.GetWorkflowWebhookTriggersAsync(
                input.WorkflowId,
                input.SleekflowCompanyId);

        // In classic flow builder
        // Can create more than one workflow webhook trigger with different object types for the same workflow.
        // Reference: _workflowWebhookTriggerService.CreateWorkflowWebhookTriggerAsync
        // But actually we only use one workflow webhook trigger for the same workflow.
        // So just return the first one.
        if (workflowWebHookTriggers.Count > 0)
        {
            return new GetOrCreateWorkflowWebhookTriggerOutput(workflowWebHookTriggers[0]);
        }

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var workflowId = input.WorkflowId;
        var sleekflowCompanyId = input.SleekflowCompanyId;
        var objectIdExpression = "{{ (reqStr | json.deserialize).contact_id }}";
        var objectType = "Contact.Id";

        var (typeName, _) = _idService.DecodeId(workflowId);
        if (typeName != "Workflow")
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult(
                        "The workflow id is invalid.",
                        new[]
                        {
                            "WorkflowId"
                        })
                });
        }

        var (_, versionedWorkflows) = await _workflowService.GetWorkflowAsync(
            workflowId,
            sleekflowCompanyId);

        if (versionedWorkflows.Count == 0)
        {
            throw new SfNotFoundObjectException(workflowId);
        }

        var workflowWebhookTriggerId = _idService.GetId(
            "WorkflowWebhookTrigger",
            workflowId);

        var workflowWebhookTriggerbyCreate =
            await _workflowWebhookTriggerService.CreateWorkflowWebhookTriggerAsync(
                new WorkflowWebhookTrigger(
                    null,
                    workflowId,
                    RandomStringUtils.Gen(16),
                    objectIdExpression,
                    workflowWebhookTriggerId,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    sleekflowCompanyId,
                    sleekflowStaff,
                    sleekflowStaff,
                    objectType),
                sleekflowCompanyId);

        var output = new GetOrCreateWorkflowWebhookTriggerOutput(workflowWebhookTriggerbyCreate);


        return output;
    }
}