using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Dynamics365.Subscriptions;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeactivateTypeSync : ITrigger
{
    private readonly IDynamics365SubscriptionService _dynamics365SubscriptionService;

    public DeactivateTypeSync(
        IDynamics365SubscriptionService dynamics365SubscriptionService)
    {
        _dynamics365SubscriptionService = dynamics365SubscriptionService;
    }

    public class DeactivateTypeSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public DeactivateTypeSyncInput(
            string entityTypeName,
            string sleekflowCompanyId)
        {
            EntityTypeName = entityTypeName;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class DeactivateTypeSyncOutput
    {
        [JsonConstructor]
        public DeactivateTypeSyncOutput()
        {
        }
    }

    public async Task<DeactivateTypeSyncOutput> F(
        DeactivateTypeSyncInput deactivateTypeSyncInput)
    {
        await _dynamics365SubscriptionService.UpdateWithEmptyDurablePayloadAsync(
            deactivateTypeSyncInput.EntityTypeName,
            deactivateTypeSyncInput.SleekflowCompanyId);

        return new DeactivateTypeSyncOutput();
    }
}