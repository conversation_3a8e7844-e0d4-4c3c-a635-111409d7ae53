using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps.Stripe;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class TopUpWhatsappCloudApiBusinessBalance
    : ITrigger<
        TopUpWhatsappCloudApiBusinessBalance.TopUpWhatsappCloudApiBusinessBalanceInput,
        TopUpWhatsappCloudApiBusinessBalance.TopUpWhatsappCloudApiBusinessBalanceOutput>
{
    private readonly IBus _bus;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<TopUpWhatsappCloudApiBusinessBalance> _logger;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;

    public TopUpWhatsappCloudApiBusinessBalance(
        IBus bus,
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        ILogger<TopUpWhatsappCloudApiBusinessBalance> logger,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _bus = bus;
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public class TopUpWhatsappCloudApiBusinessBalanceInput : IHasMetadata
    {
        [Required]
        [JsonProperty("unique_id")]
        public string UniqueId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("top_up_method")]
        [Required]
        public string TopUpMethod { get; set; }

        [JsonProperty("credit")]
        [Required]
        public Money Credit { get; set; }

        [Required]
        [JsonProperty("credited_by")]
        public string CreditedBy { get; set; }

        [JsonProperty("credited_by_display_name")]
        public string? CreditedByDisplayName { get; set; }

        [JsonProperty("stripe_top_up_credit_detail")]
        public StripeTopUpCreditDetail? StripeTopUpCreditDetail { get; set; }

        [JsonProperty("metadata")]
        [Required]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonConstructor]
        public TopUpWhatsappCloudApiBusinessBalanceInput(
            string uniqueId,
            string facebookBusinessId,
            string topUpMethod,
            Money credit,
            string creditedBy,
            string? creditedByDisplayName,
            Dictionary<string, object?> metadata,
            StripeTopUpCreditDetail? stripeTopUpCreditDetail)
        {
            FacebookBusinessId = facebookBusinessId;
            TopUpMethod = topUpMethod;
            Credit = credit;
            CreditedBy = creditedBy;
            CreditedByDisplayName = creditedByDisplayName;
            Metadata = metadata;
            StripeTopUpCreditDetail = stripeTopUpCreditDetail;
            UniqueId = uniqueId;
        }
    }

    public class TopUpWhatsappCloudApiBusinessBalanceOutput
    {
        [JsonProperty("waba_balance_transaction_log")]
        public BusinessBalanceTransactionLog BusinessBalanceTransactionLog { get; set; }

        [JsonConstructor]
        public TopUpWhatsappCloudApiBusinessBalanceOutput(
            BusinessBalanceTransactionLog businessBalanceTransactionLog)
        {
            BusinessBalanceTransactionLog = businessBalanceTransactionLog;
        }
    }

    public async Task<TopUpWhatsappCloudApiBusinessBalanceOutput> F(
        TopUpWhatsappCloudApiBusinessBalanceInput topUpWhatsappCloudApiBusinessBalanceInput)
    {
        var facebookBusinessId = topUpWhatsappCloudApiBusinessBalanceInput.FacebookBusinessId;

        topUpWhatsappCloudApiBusinessBalanceInput.Metadata.TryGetValue("facebook_waba_id", out var facebookWabaId);

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

        if (wabas.Count == 0)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        var businessBalanceTransactionLog =
            await _businessBalanceTransactionLogService
                .CreateAndGetWithBusinessWabaTopUpAsync(
                    facebookBusinessId,
                    facebookWabaId?.ToString(),
                    businessBalance.MarkupProfile,

                    // For PowerFlow manual top up flow Travis_backend will provide the uniqueId else
                    // The unique Id should be using WabaTransactionItem.GetUniqueId
                    topUpWhatsappCloudApiBusinessBalanceInput.UniqueId,
                    topUpWhatsappCloudApiBusinessBalanceInput.TopUpMethod,
                    topUpWhatsappCloudApiBusinessBalanceInput.Credit,

                    // This is the staff Id from the Travis_Backend database AspNetUsers
                    topUpWhatsappCloudApiBusinessBalanceInput.CreditedBy,

                    // This is the display name of the staff from the Travis_Backend database UserProfile
                    topUpWhatsappCloudApiBusinessBalanceInput.CreditedByDisplayName,

                    // This provide a snapshot of the stripe checkout session
                    topUpWhatsappCloudApiBusinessBalanceInput.StripeTopUpCreditDetail,
                    topUpWhatsappCloudApiBusinessBalanceInput.Metadata);

        var onCloudApiBusinessBalancePendingTransactionLogCreatedEvent =
            new OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent(
                businessBalanceTransactionLog.FacebookBusinessId);
        await _bus.Publish(onCloudApiBusinessBalancePendingTransactionLogCreatedEvent);

        return new TopUpWhatsappCloudApiBusinessBalanceOutput(businessBalanceTransactionLog);
    }
}