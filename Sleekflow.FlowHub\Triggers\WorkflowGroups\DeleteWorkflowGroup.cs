﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.WorkflowGroups;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowGroups;

[TriggerGroup(ControllerNames.WorkflowGroups)]
public class DeleteWorkflowGroup
    : ITrigger<
        DeleteWorkflowGroup.DeleteWorkflowGroupInput,
        DeleteWorkflowGroup.DeleteWorkflowGroupOutput>
{
    private readonly IWorkflowGroupService _workflowGroupService;

    public DeleteWorkflowGroup(
        IWorkflowGroupService workflowGroupService)
    {
        _workflowGroupService = workflowGroupService;
    }

    public class DeleteWorkflowGroupInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteWorkflowGroupInput(
            string workflowGroupId,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            WorkflowGroupId = workflowGroupId;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DeleteWorkflowGroupOutput
    {
    }

    public async Task<DeleteWorkflowGroupOutput> F(DeleteWorkflowGroupInput input)
    {
        await _workflowGroupService.DeleteWorkflowGroupAsync(
            input.SleekflowCompanyId,
            input.WorkflowGroupId);

        return new DeleteWorkflowGroupOutput();
    }
}