using Newtonsoft.Json;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

namespace Sleekflow.FlowHub.Tests.States;

public class FlowHubEventEvaluatorTests
{
    [Test]
    public async Task EvaluateConditionAsync_Test()
    {
        var json =
            """
            {
                "$type": "Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies.OnMessageSentEventBody, Sleekflow.FlowHub.Models",
                "message": {
                    "quoted_message": null,
                    "created_at": "2025-05-06T10:14:50.902Z",
                    "updated_at": "2025-05-06T10:14:50.902Z",
                    "message_body": {
                        "audio_message": null,
                        "contacts_message": null,
                        "currency_message": null,
                        "document_message": null,
                        "image_message": null,
                        "location_message": null,
                        "reaction_message": null,
                        "text_message": {
                            "text": "*<PERSON>*\ntest"
                        },
                        "video_message": null,
                        "date_time_message": null,
                        "interactive_message": null,
                        "template_message": null,
                        "interactive_reply_message": null,
                        "order_message": null,
                        "facebook_messenger_message": null,
                        "instagram_messenger_message": null,
                        "telegram_messenger_message": null,
                        "wechat_messenger_message": null,
                        "live_chat_message": null
                    },
                    "message_type": "text",
                    "message_content": "*Leo Choi*\ntest",
                    "message_status": "Sending",
                    "message_timestamp": 1746526490,
                    "message_delivery_type": "Normal"
                },
                "sleekflow_staff_id": null,
                "sleekflow_staff_team_ids": null,
                "channel": "whatsappcloudapi",
                "channel_id": "85264522442",
                "conversation_id": "0b580994-dad2-406b-a29d-5b060750a1e6",
                "message_id": "2434618741",
                "message_unique_id": "wamid.HBgLODUyNjEwOTY2MjMVAgARGBIyMEYzRDZFQUM3MUZERkZBRTgA",
                "contact_id": "998981d6-3260-44b3-9721-26ace0581e13",
                "contact": {
                    "id": "998981d6-3260-44b3-9721-26ace0581e13",
                    "company_id": "471a6289-b9b7-43c3-b6ad-395a1992baea",
                    "first_name": "Leo",
                    "last_name": "Choi",
                    "picture_url": null,
                    "created_at": "2023-07-26T17:17:29.038Z",
                    "updated_at": "2025-03-03T08:47:09.637Z",
                    "last_contact": "2025-05-06T10:14:50.902Z",
                    "last_contact_from_customers": "2025-05-06T07:15:34.000Z",
                    "is_sandbox": false,
                    "is_shopify_profile": false,
                    "description": null,
                    "status": null,
                    "labels": [
                        {
                            "LabelValue": "Hk01 Ai day agentflow",
                            "LabelColor": "Green",
                            "LabelType": "Normal"
                        },
                        {
                            "LabelValue": "E2E AGENT",
                            "LabelColor": "Green",
                            "LabelType": "Normal"
                        }
                    ],
                    "LastContact": "2025-05-06T10:14:50.902Z",
                    "334932d4-eea0-4718-ad2c-2d4872d8211a": "2025-05-06T10:14:50.902Z",
                    "Email": "<EMAIL>",
                    "570f0995-6696-4467-b826-e58144d77d52": "<EMAIL>",
                    "secondary phone number testing": "",
                    "7ea62755-b3e3-4826-910d-704ef785e073": "",
                    "Patient Phone Number": "",
                    "20758974-b9d9-4f77-9717-aa3ed2a6969a": "",
                    "PhoneNumber": "85261096623",
                    "987dbbf1-5004-476a-a21f-62b16fe4bd96": "85261096623",
                    "Subscriber": "true",
                    "8e1d921e-5ca6-44e4-88e2-3f516238e545": "true",
                    "Country": "Hong Kong SAR",
                    "87097da9-17a0-4e47-8ed9-8544f4cddf49": "Hong Kong SAR",
                    "ContactOwner": "efc0fb18-0166-4fdc-9f2b-d751eed7e806",
                    "90e2e740-2c14-4f02-8f00-c54e11461afa": "efc0fb18-0166-4fdc-9f2b-d751eed7e806",
                    "AssignedTeam": "737",
                    "d325efe7-cb0d-4ef0-aa83-77f0c423b27a": "737",
                    "LastChannel": "whatsappcloudapi",
                    "3a01b2d6-7ef3-4115-bdd6-4b8cd39abeb1": "whatsappcloudapi",
                    "Company Name": "SleekFlow Limited",
                    "59f8cd2e-5edc-4f21-b650-30c155868d84": "SleekFlow Limited",
                    "Subscription Plan": "sleekflow_v10_enterprise",
                    "78857ac2-b596-41af-a5d0-4e77da499038": "sleekflow_v10_enterprise",
                    "LastContactFromCustomers": "2025-05-06T07:15:34.000Z",
                    "e12673c9-38a4-4f4e-a01e-170fbde6a2c2": "2025-05-06T07:15:34.000Z",
                    "Last Customers Language": "en",
                    "1d952140-10f6-4ba8-a829-33478ba1a196": "en",
                    "Job Title": "123",
                    "7c78eb51-0a14-462a-9141-345fb76d60dc": "123",
                    "leadquality": "Good",
                    "ac51af00a2a142579281e7d02086e469": "Good",
                    "Company Size": "500+",
                    "784f279a-2aa5-4eb2-a9c7-e29818973e02": "500+",
                    "conversation_id": "0b580994-dad2-406b-a29d-5b060750a1e6",
                    "PositionOfContactOwner": "Customer Support"
                },
                "sleekflow_staff_identity_id": "0f209c89-8cfc-4517-9939-a949d88edd4c",
                "is_sent_from_sleekflow": true,
                "created_at": "2025-05-06T10:14:51.787Z"
            }
            """;

        var onMessageSentEventBody = JsonConvert.DeserializeObject<OnMessageSentEventBody>(json)!;
        var flowHubEventEvaluator = new FlowHubEventEvaluator();

        var expr =
            "{{ ([\"85264522442\"] | array.contains event_body.channel_id) }}";

        var eval = await flowHubEventEvaluator.EvaluateConditionAsync(
            expr,
            onMessageSentEventBody,
            "471a6289-b9b7-43c3-b6ad-395a1992baea",
            null);

        Assert.That(eval, Is.EqualTo(true));

        var expr2 =
            "{{ (event_body.message.message_delivery_type | string.downcase) | string.contains \"normal\" }}";

        var eval2 = await flowHubEventEvaluator.EvaluateConditionAsync(
            expr2,
            onMessageSentEventBody,
            "471a6289-b9b7-43c3-b6ad-395a1992baea",
            null);

        Assert.That(eval2, Is.EqualTo(true));

        var expr3 =
            "{{ (event_body.contact.labels | array.map \"LabelValue\" | array.some @(do; ret $0 == \"E2E AGENT\"; end)) }}";

        var eval3 = await flowHubEventEvaluator.EvaluateConditionAsync(
            expr3,
            onMessageSentEventBody,
            "471a6289-b9b7-43c3-b6ad-395a1992baea",
            null);

        Assert.That(eval3, Is.EqualTo(true));

        var expr4 =
            "{{ ((event_body | array.get_string_values_by_path \"$.contact.labels[*].LabelValue\") | array.contains \"E2E AGENT\") }}";

        var eval4 = await flowHubEventEvaluator.EvaluateConditionAsync(
            expr4,
            onMessageSentEventBody,
            "471a6289-b9b7-43c3-b6ad-395a1992baea",
            null);

        Assert.That(eval4, Is.EqualTo(true));

        var expr5 =
            "{{ ([\"85264522442\",\"441268203710\",\"971508801048\",\"622150996720\",\"6531295202\",\"6564328333\"] | array.contains event_body.channel_id) && ((((event_body.message.message_delivery_type | string.downcase) | string.contains \"normal\")) && ((event_body | array.get_string_values_by_path \"$.contact.labels[*].LabelValue\") | array.contains \"E2E AGENT\")) }}";

        var eval5 = await flowHubEventEvaluator.EvaluateConditionAsync(
            expr5,
            onMessageSentEventBody,
            "471a6289-b9b7-43c3-b6ad-395a1992baea",
            null);

        Assert.That(eval5, Is.EqualTo(true));
    }
}