using System.Net;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.States.Functions;
using Sleekflow.FlowHub.StepExecutors.Calls;
using Sleekflow.FlowHub.Utils;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class TravisBackendAuthIntegrationTests
{
    private const string Host = "https://localhost:5000";
    private readonly IAppConfig _appConfig = new AppConfig();

    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Need FlowHub to run in the background.");
        }
    }

    [Test]
    public async Task CoreCommanderToTravisBackendNoAuthTest()
    {
        // Arrange
        const string mockCompanyId = "I LOVE SLEEKFLOW";
        const string mockContactId = "SLEEKFLOW LOVES ME";
        const string mockContent = "I LOVE CODING";
        var addInternalNoteToContactInput = new AddInternalNoteToContactStepExecutor.AddInternalNoteToContactInput(
            mockCompanyId,
            null,
            mockContactId,
            mockContent);
        var httpClient = new HttpClient();
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"{Host}/FlowHub/Internals/Commands/AddInternalNoteToContact"),
        };
        requestMessage.Content = new StringContent(
            JsonConvert.SerializeObject(addInternalNoteToContactInput),
            Encoding.UTF8,
            "application/json");
        requestMessage.Headers.Add("X-Sleekflow-Flow-Hub-Authorization", "This is not a valid token!!!");

        // Act
        var response = await httpClient.SendAsync(requestMessage);

        // Assert
        Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.NotFound));

        Console.WriteLine("### Test case successes for incorrect auth header scenario ###");
    }

    [Test]
    public async Task CoreCommanderToTravisBackendHasAuthTest()
    {
        // Arrange
        const string mockCompanyId = "I LOVE SLEEKFLOW";
        const string mockContactId = "SLEEKFLOW LOVES ME";
        const string mockContent = "I LOVE CODING";
        var addInternalNoteToContactInput = new AddInternalNoteToContactStepExecutor.AddInternalNoteToContactInput(
            mockCompanyId,
            null,
            mockContactId,
            mockContent);
        var httpClient = new HttpClient();
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"{Host}/FlowHub/Internals/Commands/AddInternalNoteToContact"),
        };
        requestMessage.Content = new StringContent(
            JsonConvert.SerializeObject(addInternalNoteToContactInput),
            Encoding.UTF8,
            "application/json");
        requestMessage.Headers.Add(
            "X-Sleekflow-Flow-Hub-Authorization",
            InternalsTokenUtils.CreateJwt(_appConfig.CoreInternalsKey));

        // Act
        var responseWithAuth = await httpClient.SendAsync(requestMessage);

        // Assert
        Assert.That(responseWithAuth.StatusCode, Is.Not.EqualTo(HttpStatusCode.Unauthorized));
        Console.WriteLine("### Test case successes for correct auth header scenario ###");
    }

    [Test]
    public async Task SleekflowFunctionsToTravisBackendNoAuthTest()
    {
        // Arrange
        const string mockCompanyId = "I LOVE SLEEKFLOW";
        const string mockContactId = "SLEEKFLOW LOVES ME";
        var getContactInput = new SleekflowFunctions.GetContactInput(mockCompanyId, mockContactId);
        var httpClient = new HttpClient();
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post, RequestUri = new Uri($"{Host}/FlowHub/Internals/Functions/GetContact"),
        };
        requestMessage.Content = new StringContent(
            JsonConvert.SerializeObject(getContactInput),
            Encoding.UTF8,
            "application/json");
        requestMessage.Headers.Add("X-Sleekflow-Flow-Hub-Authorization", "THIS IS NOT A VALID TOKEN");

        // Act
        var response = await httpClient.SendAsync(requestMessage);

        // Assert
        Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.NotFound));

        Console.WriteLine("### Test case successes for incorrect auth header scenario ###");
    }

    [Test]
    public async Task SleekflowFunctionsToTravisBackendAuthTest()
    {
        // Arrange
        const string mockCompanyId = "I LOVE SLEEKFLOW";
        const string mockContactId = "SLEEKFLOW LOVES ME";
        var getContactInput = new SleekflowFunctions.GetContactInput(mockCompanyId, mockContactId);
        var httpClient = new HttpClient();
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post, RequestUri = new Uri($"{Host}/FlowHub/Internals/Functions/GetContact"),
        };
        requestMessage.Content = new StringContent(
            JsonConvert.SerializeObject(getContactInput),
            Encoding.UTF8,
            "application/json");
        requestMessage.Headers.Add(
            "X-Sleekflow-Flow-Hub-Authorization",
            InternalsTokenUtils.CreateJwt(_appConfig.CoreInternalsKey));

        // Act
        var response = await httpClient.SendAsync(requestMessage);

        // Assert
        Assert.That(response.StatusCode, Is.Not.EqualTo(HttpStatusCode.Unauthorized));

        Console.WriteLine("### Test case successes for correct auth header scenario ###");
    }
}