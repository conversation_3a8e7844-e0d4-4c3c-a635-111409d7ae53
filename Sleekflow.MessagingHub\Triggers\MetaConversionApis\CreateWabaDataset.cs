﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.MetaConversionApis;

[TriggerGroup(ControllerNames.MetaConversionApis)]
public class CreateWabaDataset : ITrigger<CreateWabaDataset.CreateWabaDatasetInput, CreateWabaDataset.CreateWabaDatasetOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IWabaAssetsManager _wabaAssetsManager;

    public CreateWabaDataset(
        IWabaService wabaService,
        IWabaAssetsManager wabaAssetsManager)
    {
        _wabaService = wabaService;
        _wabaAssetsManager = wabaAssetsManager;
    }

    public class CreateWabaDatasetInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [JsonProperty("facebook_waba_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookWabaId { get; set; }

        [JsonProperty("facebook_dataset_name")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookDatasetName { get; set; }

        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateWabaDatasetInput(
            string facebookWabaId,
            string facebookDatasetName,
            string sleekflowCompanyId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            FacebookWabaId = facebookWabaId;
            FacebookDatasetName = facebookDatasetName;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateWabaDatasetOutput
    {
        [JsonProperty("waba")]
        public WabaDto Waba { get; set; }

        [JsonConstructor]
        public CreateWabaDatasetOutput(WabaDto waba)
        {
            Waba = waba;
        }
    }

    public async Task<CreateWabaDatasetOutput> F(CreateWabaDatasetInput createWabaDatasetInput)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(createWabaDatasetInput.FacebookWabaId);

        var wabaDataset = _wabaAssetsManager.GetExistingWabaDataset(waba);

        if (wabaDataset != null)
        {
            return new CreateWabaDatasetOutput(new WabaDto(waba));
        }

        await _wabaAssetsManager.SetupFacebookWabaDatasetAsync(
            createWabaDatasetInput.FacebookWabaId,
            createWabaDatasetInput.FacebookDatasetName);

        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            createWabaDatasetInput.SleekflowStaffId,
            createWabaDatasetInput.SleekflowStaffTeamIds);

        var refreshedWaba = await _wabaService.GetAndRefreshWabaAsync(
            waba.Id,
            createWabaDatasetInput.SleekflowCompanyId,
            true,
            sleekflowStaff);

        return new CreateWabaDatasetOutput(new WabaDto(refreshedWaba));
    }
}