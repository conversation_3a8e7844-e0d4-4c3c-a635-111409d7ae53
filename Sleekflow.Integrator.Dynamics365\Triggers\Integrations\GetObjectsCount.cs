using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetObjectsCount : ITrigger
{
    private readonly IDynamics365ObjectService _dynamics365ObjectService;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;

    public GetObjectsCount(
        IDynamics365ObjectService dynamics365ObjectService,
        IDynamics365AuthenticationService dynamics365AuthenticationService)
    {
        _dynamics365ObjectService = dynamics365ObjectService;
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
    }

    public class GetObjectsCountInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public GetObjectsCountInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class GetObjectsCountOutput
    {
        [JsonProperty("count")]
        [Required]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsCountOutput(
            long count)
        {
            Count = count;
        }
    }

    public async Task<GetObjectsCountOutput> F(
        GetObjectsCountInput getObjectsCountInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(getObjectsCountInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var count =
            await _dynamics365ObjectService.GetObjectsCountAsync(
                authentication,
                getObjectsCountInput.EntityTypeName,
                getObjectsCountInput.FilterGroups);

        return new GetObjectsCountOutput(count);
    }
}