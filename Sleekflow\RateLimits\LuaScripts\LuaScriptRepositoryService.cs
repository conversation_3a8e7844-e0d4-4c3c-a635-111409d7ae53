﻿using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using StackExchange.Redis;

namespace Sleekflow.RateLimits.LuaScripts;

public interface ILuaScriptRepositoryService
{
    LuaScript GetScript(string rateLimitAlgorithm);
}

public class LuaScriptRepositoryService : ISingletonService, ILuaScriptRepositoryService
{
    public LuaScript GetScript(string rateLimitAlgorithm)
    {
        switch (rateLimitAlgorithm)
        {
            case RateLimitAlgorithms.SlidingWindowAllowance:
                return new SlidingWindowAllowanceLuaScript().GetScript();
            case RateLimitAlgorithms.SlidingWindowNextAvailableSlot:
                return new SlidingWindowNextAvailableSlotLuaScript().GetScript();
            default:
                throw new SfRateLimitAlgorithmNotFoundException(rateLimitAlgorithm);
        }
    }
}