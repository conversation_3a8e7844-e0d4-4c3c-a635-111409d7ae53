using System.Collections.Immutable;

namespace Sleekflow.IntelligentHub.Models.Constants;

public static class TargetToneTypes
{
    public const string Professional = "Professional";
    public const string Friendly = "Friendly";
    public const string Empathetic = "Empathetic";
    public const string Authoritative = "Authoritative";
    public const string Urgent = "Urgent";
    public const string Instructional = "Instructional";
    public const string Casual = "Casual";
    public const string Technical = "Technical";

    public static readonly ImmutableList<string> AllToneTypes =
        new List<string>
            {
                Professional,
                Friendly,
                Empathetic,
                Authoritative,
                Urgent,
                Instructional
            }
            .ToImmutableList();
}