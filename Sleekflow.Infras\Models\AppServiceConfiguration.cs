﻿using Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Models;

public class AppServiceConfiguration
{
    public string Name { get; set; }

    public WebApp WebApp { get; set; }

    public AppServiceConfiguration(string name, WebApp webApp)
    {
        Name = name;
        WebApp = webApp;
    }

    public string GetDomainName()
    {
        return $"https://{Name}.azurewebsites.net";
    }
}