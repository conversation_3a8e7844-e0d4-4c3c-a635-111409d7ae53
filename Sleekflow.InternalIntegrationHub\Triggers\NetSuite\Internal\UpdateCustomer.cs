using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class UpdateCustomer
    : ITrigger<UpdateCustomer.UpdateCustomerInput,
        UpdateCustomer.UpdateCustomerOutput>
{
    private readonly INetSuiteCustomerService _netSuiteCustomerService;

    public UpdateCustomer(INetSuiteCustomerService netSuiteCustomerService)
    {
        _netSuiteCustomerService = netSuiteCustomerService;
    }

    public class UpdateCustomerInput
    {
        [Required]
        [JsonProperty("company_id", NullValueHandling = NullValueHandling.Ignore)]
        public string CompanyId { get; set; }

        [JsonProperty("company_name", NullValueHandling = NullValueHandling.Ignore)]
        public string? CompanyName { get; set; }

        [JsonProperty("company_country", NullValueHandling = NullValueHandling.Ignore)]
        public string? CompanyCountry { get; set; }

        [JsonProperty("company_owner_email", NullValueHandling = NullValueHandling.Ignore)]
        public string? CompanyOwnerEmail { get; set; }

        [JsonProperty("company_owner_phone", NullValueHandling = NullValueHandling.Ignore)]
        public string? CompanyOwnerPhone { get; set; }

        [JsonProperty("sales_rep_email", NullValueHandling = NullValueHandling.Ignore)]
        public string? SalesRepEmail { get; set; }

        [JsonConstructor]
        public UpdateCustomerInput(
            string companyId,
            string? companyName = null,
            string? companyCountry = null,
            string? companyOwnerEmail = null,
            string? companyOwnerPhone = null,
            string? salesRepEmail = null)
        {
            CompanyId = companyId;
            CompanyName = companyName;
            CompanyCountry = companyCountry;
            CompanyOwnerEmail = companyOwnerEmail;
            CompanyOwnerPhone = companyOwnerPhone;
            SalesRepEmail = salesRepEmail;
        }
    }

    public class UpdateCustomerOutput
    {
    }


    public async Task<UpdateCustomerOutput> F(UpdateCustomerInput input)
    {
        var response = await _netSuiteCustomerService.UpdateCustomerAsync(input);
        if (response == false)
        {
            throw new Exception("Failed to update customer");
        }

        return new UpdateCustomerOutput();
    }
}