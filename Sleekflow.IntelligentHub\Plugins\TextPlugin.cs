using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins;

public interface ITextPlugin
{
    Task<string> BulletizeText(Kernel kernel, string inputText);

    Task<string> EnumerateText(Kernel kernel, string inputText);

    Task<string> GrammarCorrectText(Kernel kernel, string inputText);

    Task<string> LengthenText(Kernel kernel, string inputText);

    Task<string> ShortenText(Kernel kernel, string inputText);

    Task<string> SimplifyText(Kernel kernel, string inputText);

    Task<string> RephraseIntoAuthoritativeText(Kernel kernel, string inputText);

    Task<string> RephraseIntoEmpatheticText(Kernel kernel, string inputText);

    Task<string> RephraseIntoFriendlyText(Kernel kernel, string inputText);

    Task<string> RephraseIntoInstructionalText(Kernel kernel, string inputText);

    Task<string> RephraseIntoProfessionalText(Kernel kernel, string inputText);

    Task<string> RephraseIntoUrgentText(Kernel kernel, string inputText);
}

public class TextPlugin
    : ITextPlugin, IScopedService
{
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public TextPlugin(
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("BulletizeText")]
    [Description("Bulletizes the input text.")]
    [return: Description("The bulletized text.")]
    public async Task<string> BulletizeText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var bulletizeTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "BulletizeText",
                Description =
                    "Bulletizes the input text.",
                Template =
                    """
                    <message role="system">
                    Please turn the input text into bullet points. The output MUST only contain the rephrased result.
                    You MUST follow the instruction belows:
                    - This target involves rephrasing the sentence as a set of bullet points, making it easier to read and understand.
                    - This could include breaking down a list of items or ideas into a series of bullet points or highlighting key points or takeaways in a more visually appealing format.
                    - You MUST follow the language of the input text.
                    </message>

                    <message role="user">
                    A healthy diet should include a variety of fruits and vegetables, whole grains, lean proteins, and a limited amount of saturated fats and sugars.
                    </message>
                    <message role="assistant">
                    - Include a variety of fruits and vegetables
                    - Incorporate whole grains
                    - Opt for lean proteins
                    - Limit saturated fats
                    - Reduce sugar intake
                    </message>

                    <message role="user">
                    健康飲食應該包括多種水果同蔬菜、全穀物、低脂蛋白質，同時要減少飽和脂肪同糖分嘅攝取。
                    </message>
                    <message role="assistant">
                    - 包括多種水果同蔬菜
                    - 食用全穀物
                    - 選擇低脂蛋白質
                    - 減少飽和脂肪攝取
                    - 控制糖分攝取
                    </message>

                    <message role="user">
                    Uma alimentação saudável deve conter uma variedade de frutas e vegetais, grãos integrais, proteínas magras e uma quantidade limitada de gorduras saturadas e açúcares.
                    </message>
                    <message role="assistant">
                    - Variedade de frutas e vegetais
                    - Grãos integrais
                    - Proteínas magras
                    - Limitar gorduras saturadas
                    - Reduzir a ingestão de açúcares
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The bulletized text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await bulletizeTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Bulletization failed";
    }

    [KernelFunction("EnumerateText")]
    [Description("Enumerates the input text.")]
    [return: Description("The enumerated text.")]
    public async Task<string> EnumerateText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var enumerateTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EnumerateText",
                Description =
                    "Enumerates the input text.",
                Template =
                    """
                    <message role="system">
                    Please turn the input text into numbered steps. The output MUST only contain the rephrased result.
                    You MUST follow the instructions below:
                    - This target involves rephrasing the sentence as a set of numbered steps or instructions, making it clearer and easier to follow.
                    - This could include breaking down a complex process into a series of simple steps or providing a step-by-step guide for completing a task.
                    - You MUST follow the language of the input text.
                    </message>

                    <message role="user">
                    To prepare a cup of tea, boil water, add tea leaves, let it steep, and then pour it into a cup.
                    </message>
                    <message role="assistant">
                    1. Boil water.
                    2. Add tea leaves to the water.
                    3. Allow the tea to steep.
                    4. Pour the steeped tea into a cup.
                    </message>

                    <message role="user">
                    要整個蛋糕，你要先攪拌麵粉、糖、牛奶同蛋，然後放入焗爐焗好。
                    </message>
                    <message role="assistant">
                    1. 攪拌麵粉、糖、牛奶同蛋。
                    2. 將混合物放入焗爐。
                    3. 焗到蛋糕成型。
                    </message>

                    <message role="user">
                    Para fazer um café, primeiro ferva água, depois adicione o pó de café, deixe filtrar e sirva em uma xícara.
                    </message>
                    <message role="assistant">
                    1. Ferva água.
                    2. Adicione o pó de café à água quente.
                    3. Deixe o café filtrar.
                    4. Sirva o café em uma xícara.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The enumerated text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await enumerateTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Enumeration failed";
    }

    [KernelFunction("GrammarCorrectText")]
    [Description("Helps fix grammatical mistakes and spelling errors of the original text.")]
    [return: Description("The grammar-correct text.")]
    public async Task<string> GrammarCorrectText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var grammarCorrectTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "GrammarCorrectText",
                Description =
                    "Helps fix grammatical mistakes and spelling errors of the original text",
                Template =
                    """
                    <message role="system">
                    Please correct spelling and grammar errors of the input text. The output MUST only contain the rephrased result.
                    You MUST follow the instructions below:
                    - This target involves identifying and correcting any spelling or grammar errors in the sentence.
                    - This could include fixing typos, correcting verb tense, or ensuring subject-verb agreement.
                    - You MUST follow the language of the input text.
                    - The grammar should be culturally relevant to the language.
                    - The corrected text MUST maintain the original text.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The grammar-correct text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await grammarCorrectTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Grammar correct failed";
    }

    [KernelFunction("LengthenText")]
    [Description("Lengthens the original text.")]
    [return: Description("The lengthened text.")]
    public async Task<string> LengthenText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var lengthenTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "LengthenText",
                Description =
                    "lengthens the original text",
                Template =
                    """
                    <message role="system">
                    Please lengthen the sentence base on the input text. The output MUST only contain the rephrased result.
                    You MUST follow the instructions below:
                    - This target involves expanding or elaborating on the existing sentence to make it longer and more detailed.
                    - This could include adding additional clauses, using more descriptive language or providing more context or examples.
                    - You MUST follow the language of the input text.
                    - The rephrased text should be culturally relevant to the language.
                    </message>

                    <message role="user">
                    她喜欢画画。
                    </message>
                    <message role="assistant">
                    她非常喜欢艺术，特别是画画，经常花很多时间在她的画室里创作各种风格的作品。
                    </message>

                    <message role="user">
                    Ele gosta de futebol.
                    </message>
                    <message role="assistant">
                    Ele é um apaixonado por futebol, passando horas assistindo jogos e discutindo estratégias e desempenho de jogadores com seus amigos.
                    </message>

                    <message role="user">
                    Dia suka membaca.
                    </message>
                    <message role="assistant">
                    Dia sangat suka membaca, menghabiskan waktu luangnya untuk menelusuri berbagai jenis buku, mulai dari fiksi hingga non-fiksi, untuk memperluas wawasan dan pengetahuannya.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The lengthened text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await lengthenTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Lengthening failed";
    }

    [KernelFunction("ShortenText")]
    [Description("Shortens the original text.")]
    [return: Description("The shortened text.")]
    public async Task<string> ShortenText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var shortenTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "ShortenText",
                Description =
                    "shortens the original text",
                Template =
                    """
                    <message role="system">
                    Please shorten the sentence base on the input text. The output MUST only contain the rephrased result.
                    You MUST follow the instructions below:
                    - This target involves condensing the existing sentence to make it shorter and more concise.
                    - This could include removing unnecessary words or phrases, simplifying complex language or rephrasing the sentence to use fewer words.
                    - You MUST follow the language of the input text.
                    - The rephrased text should be culturally relevant to the language.
                    </message>

                    <message role="user">
                    在香港，很多人喜歡在週末去郊外散步，享受大自然的美景。
                    </message>
                    <message role="assistant">
                    香港人週末愛郊遊賞景。
                    </message>

                    <message role="user">
                    No Brasil, é comum as pessoas saírem para jantar fora nos finais de semana e aproveitarem a companhia dos amigos.
                    </message>
                    <message role="assistant">
                    No Brasil, sair para jantar com amigos é comum nos fins de semana.
                    </message>

                    <message role="user">
                    Di Malaysia, ramai orang gemar berkumpul bersama keluarga pada hari minggu untuk makan malam bersama.
                    </message>
                    <message role="assistant">
                    Orang Malaysia biasa makan malam keluarga pada hari minggu.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The shortened text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await shortenTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Shortening failed";
    }

    [KernelFunction("SimplifyText")]
    [Description("Simplifies the original text.")]
    [return: Description("The simplified text.")]
    public async Task<string> SimplifyText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);
        ;

        var simplifyTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SimplifyText",
                Description =
                    "Simplifies the original text",
                Template =
                    """
                    <message role="system">
                    Please simplify the input text into concise sentence. The output MUST only contain the rephrased result.
                    You MUST follow the instructions below:
                    - This target involves making the language of the sentence easier to understand and more accessible to a wider audience.
                    - This could include using simpler words, avoiding jargon or breaking down complex ideas into simpler sentences.
                    - You MUST follow the language of the input text.
                    - The rephrased text should be culturally relevant to the language.
                    </message>

                    <message role="user">
                    廣州話嘅聲調系統好複雜，有九個聲調。
                    </message>
                    <message role="assistant">
                    廣州話有九個簡單嘅聲調。
                    </message>

                    <message role="user">
                    A fotossíntese é um processo complexo onde as plantas convertem luz solar em energia química.
                    </message>
                    <message role="assistant">
                    Na fotossíntese, as plantas usam luz do sol para fazer energia.
                    </message>

                    <message role="user">
                    Ekonomi makro merangkumi analisis terhadap keseluruhan prestasi ekonomi sebuah negara, termasuk KDNK dan kadar pengangguran.
                    </message>
                    <message role="assistant">
                    Ekonomi makro kaji prestasi ekonomi negara, seperti jumlah barang dihasilkan dan pengangguran.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The simplified text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await simplifyTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Simplifying failed";
    }

    [KernelFunction("RephraseIntoAuthoritativeText")]
    [Description("Rephrases the input text to an authoritative tone.")]
    [return: Description("The rephrased text.")]
    public async Task<string> RephraseIntoAuthoritativeText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var rephraseIntoAuthoritativeTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "RephraseIntoAuthoritativeText",
                Description =
                    "Rephrase the input text to a authoritative tone.",
                Template =
                    """
                    <message role="system">
                    Please rephrase the input text with an Authoritative Tone, the output MUST only contain the rephrased text.
                    You MUST follow the instructions below:
                    - An authoritative tone can be useful when providing guidance or directing customers to take specific actions.
                    - It conveys a sense of confidence and expertise, which can help to instill trust and credibility in your brand.
                    - You MUST follow the language of the input text.
                    - The tone should be culturally relevant to the language.
                    - The rephrased text MUST maintain the original meaning and context.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rephrased text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await rephraseIntoAuthoritativeTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Rephrasing failed";
    }

    [KernelFunction("RephraseIntoEmpatheticText")]
    [Description("Rephrases the input text to an empathetic tone.")]
    [return: Description("The rephrased text.")]
    public async Task<string> RephraseIntoEmpatheticText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var rephraseIntoEmpatheticTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "RephraseIntoEmpatheticText",
                Description =
                    "Rephrase the input text to a empathetic tone.",
                Template =
                    """
                    <message role="system">
                    Please rephrase the input text with an Empathetic Tone, the output MUST only contain the rephrased text.
                    You MUST follow the instructions below:
                    - An empathetic tone can be particularly effective when dealing with customer issues or complaints.
                    - It shows that you understand and care about their concerns, which can help to diffuse tense situations and build trust.
                    - You MUST follow the language of the input text.
                    - The tone should be culturally relevant to the language.
                    - The rephrased text MUST maintain the original meaning and context.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rephrased text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await rephraseIntoEmpatheticTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Rephrasing failed";
    }

    [KernelFunction("RephraseIntoFriendlyText")]
    [Description("Rephrases the input text to a friendly tone.")]
    [return: Description("The rephrased text.")]
    public async Task<string> RephraseIntoFriendlyText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var rephraseIntoFriendlyTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "RephraseIntoFriendlyText",
                Description =
                    "Rephrase the input text to a friendly tone.",
                Template =
                    """
                    <message role="system">
                    Please rephrase the input text with an Friendly Tone, the output MUST only contain the rephrased text.
                    You MUST follow the instructions below:
                    - A friendly tone should be Warm, approachable, and conversational style used to create positive and engaging experiences, often in customer service or marketing.
                    - You MUST follow the language of the input text.
                    - The tone should be culturally relevant to the language.
                    - The rephrased text MUST maintain the original meaning and context.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rephrased text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await rephraseIntoFriendlyTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Rephrasing failed";
    }

    [KernelFunction("RephraseIntoInstructionalText")]
    [Description("Rephrases the input text to a instructional tone.")]
    [return: Description("The rephrased text.")]
    public async Task<string> RephraseIntoInstructionalText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var rephraseIntoInstructionalTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "RephraseIntoInstructionalText",
                Description =
                    "Rephrase the input text to a instructional tone.",
                Template =
                    """
                    <message role="system">
                    Please rephrase the input text with an Instructional Tone, the output MUST only contain the rephrased text.
                    You MUST follow the instructions below:
                    - An instructional tone can be useful when providing step-by-step guidance or walking customers through a process.
                    - It involves being clear and concise in your communication, and providing the information customers need to take action.
                    - You MUST follow the language of the input text.
                    - The tone should be culturally relevant to the language.
                    - The rephrased text MUST maintain the original meaning and context.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rephrased text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await rephraseIntoInstructionalTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Rephrasing failed";
    }

    [KernelFunction("RephraseIntoProfessionalText")]
    [Description("Rephrases the input text to a professional tone.")]
    [return: Description("The rephrased text.")]
    public async Task<string> RephraseIntoProfessionalText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var rephraseIntoProfessionalTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "RephraseIntoProfessionalText",
                Description =
                    "Rephrase the input text to a professional tone.",
                Template =
                    """
                    <message role="system">
                    Please rephrase the input text with an Professional Tone, the output MUST only contain the rephrased text.
                    You MUST follow the instructions below:
                    - The sentence should be clear, concise, and authoritative language used in formal or business settings.
                    - You MUST follow the language of the input text.
                    - The tone should be culturally relevant to the language.
                    - The rephrased text MUST maintain the original meaning and context.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rephrased text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await rephraseIntoProfessionalTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Rephrasing failed";
    }

    [KernelFunction("RephraseIntoUrgentText")]
    [Description("Rephrases the input text to a urgent tone.")]
    [return: Description("The rephrased text.")]
    public async Task<string> RephraseIntoUrgentText(Kernel kernel, string inputText)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var rephraseIntoUrgentTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "RephraseIntoUrgentText",
                Description =
                    "Rephrase the input text to a urgent tone.",
                Template =
                    """
                    <message role="system">
                    Please rephrase the input text with an Urgent Tone, the output MUST only contain the rephrased text.
                    You MUST follow the instructions below:
                    - An urgent tone can be used to create a sense of urgency and encourage customers to take immediate action.
                    - This can be helpful in situations where prompt action is required, such as when dealing with a security issue or time-sensitive offer.
                    - You MUST follow the language of the input text.
                    - The tone should be culturally relevant to the language.
                    - The rephrased text MUST maintain the original meaning and context.
                    </message>

                    <message role="user">
                    {{$input}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "input"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rephrased text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await rephraseIntoUrgentTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "input", inputText
                }
            });

        return chatMessageContent?.Content ?? "Rephrasing failed";
    }
}