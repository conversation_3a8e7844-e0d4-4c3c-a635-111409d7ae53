using MassTransit;
using Sleekflow.FlowHub.Models.Events;

namespace Sleekflow.FlowHub.Consumers;

public class OnStateSubscriptionCreatedEventConsumerDefinition
    : ConsumerDefinition<OnStateSubscriptionCreatedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnStateSubscriptionCreatedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnStateSubscriptionCreatedEventConsumer : IConsumer<OnStateSubscriptionCreatedEvent>
{
    public Task Consume(ConsumeContext<OnStateSubscriptionCreatedEvent> context)
    {
        var @event = context.Message;

        return Task.CompletedTask;
    }
}