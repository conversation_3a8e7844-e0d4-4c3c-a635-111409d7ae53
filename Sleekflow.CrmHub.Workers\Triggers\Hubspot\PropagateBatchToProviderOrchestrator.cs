using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class PropagateBatchToProviderOrchestrator
{
    [Function("Hubspot_PropagateBatchToProvider_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var _ =
            await context.CallActivityAsync<PropagateBatchToProvider.PropagateBatchToProviderOutput>(
                "Hubspot_PropagateBatchToProvider",
                new PropagateBatchToProvider.PropagateBatchToProviderInput());

        var nextCleanup = context.CurrentUtcDateTime.AddSeconds(10);

        await context.CreateTimer(nextCleanup, CancellationToken.None);

        context.ContinueAsNew(null);
    }
}