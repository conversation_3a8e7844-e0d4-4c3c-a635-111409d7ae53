using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Orders;

public interface IOrderValidator
{
    Task AssertValidOrderAsync(Order order);
}

public class OrderValidator : IOrderValidator, IScopedService
{
    private readonly IStoreService _storeService;
    private readonly IProductVariantService _productVariantService;

    public OrderValidator(
        IStoreService storeService,
        IProductVariantService productVariantService)
    {
        _storeService = storeService;
        _productVariantService = productVariantService;
    }

    public async Task AssertValidOrderAsync(Order order)
    {
        var sleekflowCompanyId = order.SleekflowCompanyId;
        var storeId = order.StoreId;
        var lineItems = order.LineItems;

        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);

        // TODO check store.IsViewEnabled and store.IsPaymentEnabled
        var productVariants = new List<ProductVariant>();
        foreach (var pvId in lineItems.Select(li => li.ProductVariantId).Distinct().ToList())
        {
            productVariants.Add(
                await _productVariantService.GetProductVariantAsync(pvId, storeId, sleekflowCompanyId));
        }

        // TODO Check Total Price
        // foreach (var lineItem in lineItems)
        // {
        //     var productVariant = await _productVariantService.GetProductVariantAsync(
        //         lineItem.ProductVariantId,
        //         storeId,
        //         sleekflowCompanyId);
        //

        // var price = entity.Price;
        // var discounted = price.Amount - (lineItem.DiscountPerItem ?? 0);
        // if (variant.Price.Amount != discounted)
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new ($"Discounted price calculation error {variant.Price.Amount} not match {discounted}")
        //     });
        // }
        //
        // var total = (long) (discounted -

        //                     (discounted / 100 * (orderDiscount?.Rate ?? 0)));
        // lineItem.DiscountedPrice = total;
        // if (string.IsNullOrEmpty(totalPrice.CurrencyIsoCode))
        // {
        //     totalPrice.CurrencyIsoCode = entity.Price.CurrencyIsoCode;
        // }
        //
        // if (totalPrice.CurrencyIsoCode != entity.Price.CurrencyIsoCode)
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new ("Not distinct value - currency in order creation")
        //     });
        // }
        //
        // totalPrice.Amount = totalPrice.Amount += total;
        // productVariantsSnap.Add(entity);
        // }
        //
        // order.SnapshottedProductVariants = productVariantsSnap;
        // order.PaymentPrice = totalPrice;
    }
}