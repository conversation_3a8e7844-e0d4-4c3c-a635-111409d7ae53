POST https://localhost:7197/Authorized/UserWorkspaces/GetUserWorkspaces
Content-Type: application/json
X-Sleekflow-User-Id: 0f209c89-8cfc-4517-9939-a949d88edd4c
X-Sleekflow-Email: 0f209c89-8cfc-4517-9939-a949d88edd4c
X-Targeted-Company-Id:

{
}

###

POST https://localhost:7197/v1/user-event-hub/ReliableMessage/negotiate?data=eyJ1c2VyX2lkIjoiMGYyMDljODktOGNmYy00NTE3LTk5MzktYTk0OWQ4OGVkZDRjIiwiZ3JvdXBfaWRzIjpbIjQ3MWE2Mjg5LWI5YjctNDNjMy1iNmFkLTM5NWExOTkyYmFlYSJdfQ&negotiateVersion=1
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IlBybEhVTkNEV2h3a2hYYm8waENmYyJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tTt682cnyVOYtJpFtQS0a2P2NO_VMna-1z5NW-L5ymip1T_98pCWWgePbPUcQEJzMeVShzia3M2efrN6LhFK5LXWDTTzpem39DGjn2lQPputA4Fkatj1zlGY63ygg7QM8aNFPmuxFVx2CvpHi516rZdq2n40kyFByztz7UUoR5sD_aT_GyUlVCVD4GHl7NCixQYTv6MFM5CnJDkS7xP8SQfj7iepGQLJlYTeljQpLVQv94rCBdAkv7XlTgicfB9v2wotHkzppM_caENS0vh5a8YBfEwi1ndsDUowzL85nHVq7srFgRhdc0lGGhiEOYnutnOPB-OJBC6Gll27bzMHlg

{
}

### result = GetUserAuthenticationDetails.GetUserAuthenticationDetailsOutput
### SleekflowCompanyId = {string} "471a6289-b9b7-43c3-b6ad-395a1992baea"
### SleekflowImpersonator = {Impersonator} null
### SleekflowRoleIds = {List<string>} Count = 0
### SleekflowRoles = {List<string>} Count = 5
### SleekflowStaffId = {string} "11871"
### SleekflowTeamIds = {List<string>} Count = 1
### SleekflowUserId = {string} "0f209c89-8cfc-4517-9939-a949d88edd4c"