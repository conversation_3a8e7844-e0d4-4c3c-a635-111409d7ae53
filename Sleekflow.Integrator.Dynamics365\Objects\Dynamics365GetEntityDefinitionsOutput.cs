using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Sleekflow.Integrator.Dynamics365.Objects;

public class Dynamics365GetEntityDefinitionsOutput
{
    [JsonConstructor]
    public Dynamics365GetEntityDefinitionsOutput(string odataContext, List<EntityDefinition> value)
    {
        OdataContext = odataContext;
        Value = value;
    }

    [JsonProperty("@odata.context")]
    public string OdataContext { get; set; }

    [JsonProperty("value")]
    public List<EntityDefinition> Value { get; set; }
}

public class EntityDefinitionAttribute
{
    [JsonConstructor]
    public EntityDefinitionAttribute(
        string odataType,
        string metadataId,
        object hasChanged,
        string attributeOf,
        string attributeType,
        int columnNumber,
        object deprecatedVersion,
        string introducedVersion,
        string entityLogicalName,
        bool isCustomAttribute,
        bool isPrimaryId,
        bool isValidODataAttribute,
        bool isPrimaryName,
        bool isValidForCreate,
        bool isValidForRead,
        bool isValidForUpdate,
        bool canBeSecuredForRead,
        bool canBeSecuredForCreate,
        bool canBeSecuredForUpdate,
        bool isSecured,
        bool isRetrievable,
        bool isFilterable,
        bool isSearchable,
        bool isManaged,
        object linkedAttributeId,
        string logicalName,
        bool isValidForForm,
        bool isRequiredForForm,
        bool isValidForGrid,
        string schemaName,
        object externalName,
        bool isLogical,
        bool isDataSourceSecret,
        object inheritsFrom,
        DateTimeOffset createdOn,
        DateTimeOffset modifiedOn,
        int? sourceType,
        string autoNumberFormat,
        string format,
        string imeMode,
        int maxLength,
        string yomiOf,
        bool isLocalizable,
        int databaseLength,
        string formulaDefinition,
        int sourceTypeMask,
        AttributeTypeName attributeTypeName,
        Description description,
        DisplayName displayName,
        IsAuditEnabled isAuditEnabled,
        IsGlobalFilterEnabled isGlobalFilterEnabled,
        IsSortableEnabled isSortableEnabled,
        IsCustomizable isCustomizable,
        IsRenameable isRenameable,
        IsValidForAdvancedFind isValidForAdvancedFind,
        RequiredLevel requiredLevel,
        CanModifyAdditionalSettings canModifyAdditionalSettings,
        List<object> settings,
        FormatName formatName,
        double? maxValue,
        double? minValue,
        int? precision,
        DateTimeOffset? minSupportedValue,
        DateTimeOffset? maxSupportedValue,
        DateTimeBehavior dateTimeBehavior,
        CanChangeDateTimeBehavior canChangeDateTimeBehavior,
        int? defaultFormValue,
        object parentPicklistLogicalName,
        List<object> childPicklistLogicalNames,
        bool? defaultValue,
        bool? isEntityReferenceStored)
    {
        OdataType = odataType;
        MetadataId = metadataId;
        HasChanged = hasChanged;
        AttributeOf = attributeOf;
        AttributeType = attributeType;
        ColumnNumber = columnNumber;
        DeprecatedVersion = deprecatedVersion;
        IntroducedVersion = introducedVersion;
        EntityLogicalName = entityLogicalName;
        IsCustomAttribute = isCustomAttribute;
        IsPrimaryId = isPrimaryId;
        IsValidODataAttribute = isValidODataAttribute;
        IsPrimaryName = isPrimaryName;
        IsValidForCreate = isValidForCreate;
        IsValidForRead = isValidForRead;
        IsValidForUpdate = isValidForUpdate;
        CanBeSecuredForRead = canBeSecuredForRead;
        CanBeSecuredForCreate = canBeSecuredForCreate;
        CanBeSecuredForUpdate = canBeSecuredForUpdate;
        IsSecured = isSecured;
        IsRetrievable = isRetrievable;
        IsFilterable = isFilterable;
        IsSearchable = isSearchable;
        IsManaged = isManaged;
        LinkedAttributeId = linkedAttributeId;
        LogicalName = logicalName;
        IsValidForForm = isValidForForm;
        IsRequiredForForm = isRequiredForForm;
        IsValidForGrid = isValidForGrid;
        SchemaName = schemaName;
        ExternalName = externalName;
        IsLogical = isLogical;
        IsDataSourceSecret = isDataSourceSecret;
        InheritsFrom = inheritsFrom;
        CreatedOn = createdOn;
        ModifiedOn = modifiedOn;
        SourceType = sourceType;
        AutoNumberFormat = autoNumberFormat;
        Format = format;
        ImeMode = imeMode;
        MaxLength = maxLength;
        YomiOf = yomiOf;
        IsLocalizable = isLocalizable;
        DatabaseLength = databaseLength;
        FormulaDefinition = formulaDefinition;
        SourceTypeMask = sourceTypeMask;
        AttributeTypeName = attributeTypeName;
        Description = description;
        DisplayName = displayName;
        IsAuditEnabled = isAuditEnabled;
        IsGlobalFilterEnabled = isGlobalFilterEnabled;
        IsSortableEnabled = isSortableEnabled;
        IsCustomizable = isCustomizable;
        IsRenameable = isRenameable;
        IsValidForAdvancedFind = isValidForAdvancedFind;
        RequiredLevel = requiredLevel;
        CanModifyAdditionalSettings = canModifyAdditionalSettings;
        Settings = settings;
        FormatName = formatName;
        MaxValue = maxValue;
        MinValue = minValue;
        Precision = precision;
        MinSupportedValue = minSupportedValue;
        MaxSupportedValue = maxSupportedValue;
        DateTimeBehavior = dateTimeBehavior;
        CanChangeDateTimeBehavior = canChangeDateTimeBehavior;
        DefaultFormValue = defaultFormValue;
        ParentPicklistLogicalName = parentPicklistLogicalName;
        ChildPicklistLogicalNames = childPicklistLogicalNames;
        DefaultValue = defaultValue;
        IsEntityReferenceStored = isEntityReferenceStored;
    }

    [JsonProperty("@odata.type")]
    public string OdataType { get; set; }

    [JsonProperty("MetadataId")]
    public string MetadataId { get; set; }

    [JsonProperty("HasChanged")]
    public object HasChanged { get; set; }

    [JsonProperty("AttributeOf")]
    public string AttributeOf { get; set; }

    [JsonProperty("AttributeType")]
    public string AttributeType { get; set; }

    [JsonProperty("ColumnNumber")]
    public int ColumnNumber { get; set; }

    [JsonProperty("DeprecatedVersion")]
    public object DeprecatedVersion { get; set; }

    [JsonProperty("IntroducedVersion")]
    public string IntroducedVersion { get; set; }

    [JsonProperty("EntityLogicalName")]
    public string EntityLogicalName { get; set; }

    [JsonProperty("IsCustomAttribute")]
    public bool IsCustomAttribute { get; set; }

    [JsonProperty("IsPrimaryId")]
    public bool IsPrimaryId { get; set; }

    [JsonProperty("IsValidODataAttribute")]
    public bool IsValidODataAttribute { get; set; }

    [JsonProperty("IsPrimaryName")]
    public bool IsPrimaryName { get; set; }

    [JsonProperty("IsValidForCreate")]
    public bool IsValidForCreate { get; set; }

    [JsonProperty("IsValidForRead")]
    public bool IsValidForRead { get; set; }

    [JsonProperty("IsValidForUpdate")]
    public bool IsValidForUpdate { get; set; }

    [JsonProperty("CanBeSecuredForRead")]
    public bool CanBeSecuredForRead { get; set; }

    [JsonProperty("CanBeSecuredForCreate")]
    public bool CanBeSecuredForCreate { get; set; }

    [JsonProperty("CanBeSecuredForUpdate")]
    public bool CanBeSecuredForUpdate { get; set; }

    [JsonProperty("IsSecured")]
    public bool IsSecured { get; set; }

    [JsonProperty("IsRetrievable")]
    public bool IsRetrievable { get; set; }

    [JsonProperty("IsFilterable")]
    public bool IsFilterable { get; set; }

    [JsonProperty("IsSearchable")]
    public bool IsSearchable { get; set; }

    [JsonProperty("IsManaged")]
    public bool IsManaged { get; set; }

    [JsonProperty("LinkedAttributeId")]
    public object LinkedAttributeId { get; set; }

    [JsonProperty("LogicalName")]
    public string LogicalName { get; set; }

    [JsonProperty("IsValidForForm")]
    public bool IsValidForForm { get; set; }

    [JsonProperty("IsRequiredForForm")]
    public bool IsRequiredForForm { get; set; }

    [JsonProperty("IsValidForGrid")]
    public bool IsValidForGrid { get; set; }

    [JsonProperty("SchemaName")]
    public string SchemaName { get; set; }

    [JsonProperty("ExternalName")]
    public object ExternalName { get; set; }

    [JsonProperty("IsLogical")]
    public bool IsLogical { get; set; }

    [JsonProperty("IsDataSourceSecret")]
    public bool IsDataSourceSecret { get; set; }

    [JsonProperty("InheritsFrom")]
    public object InheritsFrom { get; set; }

    [JsonProperty("CreatedOn")]
    public DateTimeOffset CreatedOn { get; set; }

    [JsonProperty("ModifiedOn")]
    public DateTimeOffset ModifiedOn { get; set; }

    [JsonProperty("SourceType")]
    public int? SourceType { get; set; }

    [JsonProperty("AutoNumberFormat")]
    public string AutoNumberFormat { get; set; }

    [JsonProperty("Format")]
    public string Format { get; set; }

    [JsonProperty("ImeMode")]
    public string ImeMode { get; set; }

    [JsonProperty("MaxLength")]
    public int MaxLength { get; set; }

    [JsonProperty("YomiOf")]
    public string YomiOf { get; set; }

    [JsonProperty("IsLocalizable")]
    public bool IsLocalizable { get; set; }

    [JsonProperty("DatabaseLength")]
    public int DatabaseLength { get; set; }

    [JsonProperty("FormulaDefinition")]
    public string FormulaDefinition { get; set; }

    [JsonProperty("SourceTypeMask")]
    public int SourceTypeMask { get; set; }

    [JsonProperty("AttributeTypeName")]
    public AttributeTypeName AttributeTypeName { get; set; }

    [JsonProperty("Description")]
    public Description Description { get; set; }

    [JsonProperty("DisplayName")]
    public DisplayName DisplayName { get; set; }

    [JsonProperty("IsAuditEnabled")]
    public IsAuditEnabled IsAuditEnabled { get; set; }

    [JsonProperty("IsGlobalFilterEnabled")]
    public IsGlobalFilterEnabled IsGlobalFilterEnabled { get; set; }

    [JsonProperty("IsSortableEnabled")]
    public IsSortableEnabled IsSortableEnabled { get; set; }

    [JsonProperty("IsCustomizable")]
    public IsCustomizable IsCustomizable { get; set; }

    [JsonProperty("IsRenameable")]
    public IsRenameable IsRenameable { get; set; }

    [JsonProperty("IsValidForAdvancedFind")]
    public IsValidForAdvancedFind IsValidForAdvancedFind { get; set; }

    [JsonProperty("RequiredLevel")]
    public RequiredLevel RequiredLevel { get; set; }

    [JsonProperty("CanModifyAdditionalSettings")]
    public CanModifyAdditionalSettings CanModifyAdditionalSettings { get; set; }

    [JsonProperty("Settings")]
    public List<object> Settings { get; set; }

    [JsonProperty("FormatName")]
    public FormatName FormatName { get; set; }

    [JsonProperty("MaxValue")]
    public double? MaxValue { get; set; }

    [JsonProperty("MinValue")]
    public double? MinValue { get; set; }

    [JsonProperty("Precision")]
    public int? Precision { get; set; }

    [JsonProperty("MinSupportedValue")]
    public DateTimeOffset? MinSupportedValue { get; set; }

    [JsonProperty("MaxSupportedValue")]
    public DateTimeOffset? MaxSupportedValue { get; set; }

    [JsonProperty("DateTimeBehavior")]
    public DateTimeBehavior DateTimeBehavior { get; set; }

    [JsonProperty("CanChangeDateTimeBehavior")]
    public CanChangeDateTimeBehavior CanChangeDateTimeBehavior { get; set; }

    [JsonProperty("DefaultFormValue")]
    public int? DefaultFormValue { get; set; }

    [JsonProperty("ParentPicklistLogicalName")]
    public object ParentPicklistLogicalName { get; set; }

    [JsonProperty("ChildPicklistLogicalNames")]
    public List<object> ChildPicklistLogicalNames { get; set; }

    [JsonProperty("DefaultValue")]
    public bool? DefaultValue { get; set; }

    [JsonProperty("IsEntityReferenceStored")]
    public bool? IsEntityReferenceStored { get; set; }
}

public class AttributeTypeName
{
    [JsonConstructor]
    public AttributeTypeName(string value)
    {
        Value = value;
    }

    [JsonProperty("Value")]
    public string Value { get; set; }
}

public class CanChangeDateTimeBehavior
{
    [JsonConstructor]
    public CanChangeDateTimeBehavior(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class CanModifyAdditionalSettings
{
    [JsonConstructor]
    public CanModifyAdditionalSettings(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class DateTimeBehavior
{
    [JsonConstructor]
    public DateTimeBehavior(string value)
    {
        Value = value;
    }

    [JsonProperty("Value")]
    public string Value { get; set; }
}

public class Description
{
    [JsonConstructor]
    public Description(List<LocalizedLabel> localizedLabels, UserLocalizedLabel userLocalizedLabel)
    {
        LocalizedLabels = localizedLabels;
        UserLocalizedLabel = userLocalizedLabel;
    }

    [JsonProperty("LocalizedLabels")]
    public List<LocalizedLabel> LocalizedLabels { get; set; }

    [JsonProperty("UserLocalizedLabel")]
    public UserLocalizedLabel UserLocalizedLabel { get; set; }
}

public class DisplayName
{
    [JsonConstructor]
    public DisplayName(List<LocalizedLabel> localizedLabels, UserLocalizedLabel userLocalizedLabel)
    {
        LocalizedLabels = localizedLabels;
        UserLocalizedLabel = userLocalizedLabel;
    }

    [JsonProperty("LocalizedLabels")]
    public List<LocalizedLabel> LocalizedLabels { get; set; }

    [JsonProperty("UserLocalizedLabel")]
    public UserLocalizedLabel UserLocalizedLabel { get; set; }
}

public class FormatName
{
    [JsonConstructor]
    public FormatName(string value)
    {
        Value = value;
    }

    [JsonProperty("Value")]
    public string Value { get; set; }
}

public class IsAuditEnabled
{
    [JsonConstructor]
    public IsAuditEnabled(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class IsCustomizable
{
    [JsonConstructor]
    public IsCustomizable(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class IsGlobalFilterEnabled
{
    [JsonConstructor]
    public IsGlobalFilterEnabled(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class IsRenameable
{
    [JsonConstructor]
    public IsRenameable(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class IsSortableEnabled
{
    [JsonConstructor]
    public IsSortableEnabled(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class IsValidForAdvancedFind
{
    [JsonConstructor]
    public IsValidForAdvancedFind(bool value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public bool Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class LocalizedLabel
{
    [JsonConstructor]
    public LocalizedLabel(string label, int languageCode, bool isManaged, string metadataId, object hasChanged)
    {
        Label = label;
        LanguageCode = languageCode;
        IsManaged = isManaged;
        MetadataId = metadataId;
        HasChanged = hasChanged;
    }

    [JsonProperty("Label")]
    public string Label { get; set; }

    [JsonProperty("LanguageCode")]
    public int LanguageCode { get; set; }

    [JsonProperty("IsManaged")]
    public bool IsManaged { get; set; }

    [JsonProperty("MetadataId")]
    public string MetadataId { get; set; }

    [JsonProperty("HasChanged")]
    public object HasChanged { get; set; }
}

public class RequiredLevel
{
    [JsonConstructor]
    public RequiredLevel(string value, bool canBeChanged, string managedPropertyLogicalName)
    {
        Value = value;
        CanBeChanged = canBeChanged;
        ManagedPropertyLogicalName = managedPropertyLogicalName;
    }

    [JsonProperty("Value")]
    public string Value { get; set; }

    [JsonProperty("CanBeChanged")]
    public bool CanBeChanged { get; set; }

    [JsonProperty("ManagedPropertyLogicalName")]
    public string ManagedPropertyLogicalName { get; set; }
}

public class UserLocalizedLabel
{
    [JsonConstructor]
    public UserLocalizedLabel(string label, int languageCode, bool isManaged, string metadataId, object hasChanged)
    {
        Label = label;
        LanguageCode = languageCode;
        IsManaged = isManaged;
        MetadataId = metadataId;
        HasChanged = hasChanged;
    }

    [JsonProperty("Label")]
    public string Label { get; set; }

    [JsonProperty("LanguageCode")]
    public int LanguageCode { get; set; }

    [JsonProperty("IsManaged")]
    public bool IsManaged { get; set; }

    [JsonProperty("MetadataId")]
    public string MetadataId { get; set; }

    [JsonProperty("HasChanged")]
    public object HasChanged { get; set; }
}

public class EntityDefinition
{
    [JsonConstructor]
    public EntityDefinition(
        Dictionary<string, JToken> additionalProperties,
        string metadataId,
        string logicalName,
        int objectTypeCode,
        string ownershipType,
        string primaryNameAttribute,
        string primaryImageAttribute,
        string primaryIdAttribute,
        string schemaName,
        string logicalCollectionName,
        object externalCollectionName,
        string collectionSchemaName,
        string entitySetName,
        DateTimeOffset createdOn,
        DateTimeOffset modifiedOn,
        bool hasEmailAddresses,
        Description description,
        List<object> settings,
        List<EntityDefinitionAttribute> attributes)
    {
        AdditionalProperties = additionalProperties;
        MetadataId = metadataId;
        LogicalName = logicalName;
        ObjectTypeCode = objectTypeCode;
        OwnershipType = ownershipType;
        PrimaryNameAttribute = primaryNameAttribute;
        PrimaryImageAttribute = primaryImageAttribute;
        PrimaryIdAttribute = primaryIdAttribute;
        SchemaName = schemaName;
        LogicalCollectionName = logicalCollectionName;
        ExternalCollectionName = externalCollectionName;
        CollectionSchemaName = collectionSchemaName;
        EntitySetName = entitySetName;
        CreatedOn = createdOn;
        ModifiedOn = modifiedOn;
        HasEmailAddresses = hasEmailAddresses;
        Description = description;
        Settings = settings;
        Attributes = attributes;
    }

    [JsonProperty("MetadataId")]
    public string MetadataId { get; set; }

    [JsonProperty("LogicalName")]
    public string LogicalName { get; set; }

    [JsonProperty("ObjectTypeCode")]
    public int ObjectTypeCode { get; set; }

    [JsonProperty("OwnershipType")]
    public string OwnershipType { get; set; }

    [JsonProperty("PrimaryNameAttribute")]
    public string PrimaryNameAttribute { get; set; }

    [JsonProperty("PrimaryImageAttribute")]
    public string PrimaryImageAttribute { get; set; }

    [JsonProperty("PrimaryIdAttribute")]
    public string PrimaryIdAttribute { get; set; }

    [JsonProperty("SchemaName")]
    public string SchemaName { get; set; }

    [JsonProperty("LogicalCollectionName")]
    public string LogicalCollectionName { get; set; }

    [JsonProperty("ExternalCollectionName")]
    public object ExternalCollectionName { get; set; }

    [JsonProperty("CollectionSchemaName")]
    public string CollectionSchemaName { get; set; }

    [JsonProperty("EntitySetName")]
    public string EntitySetName { get; set; }

    [JsonProperty("CreatedOn")]
    public DateTimeOffset CreatedOn { get; set; }

    [JsonProperty("ModifiedOn")]
    public DateTimeOffset ModifiedOn { get; set; }

    [JsonProperty("HasEmailAddresses")]
    public bool HasEmailAddresses { get; set; }

    [JsonProperty("Description")]
    public Description Description { get; set; }

    [JsonProperty("Settings")]
    public List<object> Settings { get; set; }

    [JsonProperty("Attributes")]
    public List<EntityDefinitionAttribute> Attributes { get; set; }

    [JsonExtensionData]
    private Dictionary<string, JToken> AdditionalProperties { get; set; }
}