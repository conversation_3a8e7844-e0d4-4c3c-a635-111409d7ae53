using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workers;

public class NotifyStatusInput
{
    [JsonProperty("worker_instance_id")]
    public string? WorkerInstanceId { get; set; }

    [JsonProperty("status")]
    [System.ComponentModel.DataAnnotations.Required]
    public string Status { get; set; }

    [JsonProperty("error")]
    [Validations.ValidateObject]
    public UserFriendlyError? Error { get; set; }

    [JsonConstructor]
    public NotifyStatusInput(string? workerInstanceId, string status, UserFriendlyError? error)
    {
        WorkerInstanceId = workerInstanceId;
        Status = status;
        Error = error;
    }
}