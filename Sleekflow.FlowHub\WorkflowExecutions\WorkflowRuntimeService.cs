﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Persistence;
using Sleekflow.RateLimits;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Sleekflow.FlowHub.WorkflowExecutions;

public interface IWorkflowRuntimeService
{
    Task ExecuteWorkflowAsync(
        ProxyState state,
        string? workflowExecutionReasonCode);

    Task CompleteWorkflowAsync(ProxyState state);

    Task StartWorkflowAsync(
        string sleekflowCompanyId,
        string stateId);

    Task FailWorkflowAsync(
        ProxyState state,
        string? reasonCode = null,
        AuditEntity.SleekflowStaff? sleekflowStaff = null);

    Task CancelWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode,
        AuditEntity.SleekflowStaff? sleekflowStaff = null);

    Task AbandonWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode);

    Task BlockWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode);

    Task RestrictWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode);
}

public class WorkflowRuntimeService : IWorkflowRuntimeService, IScopedService
{
    private readonly IStateService _stateService;
    private readonly IStepRequester _stepRequester;
    private readonly IBus _bus;
    private readonly IServiceBusManager _serviceBusManager;
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;
    private readonly ILogger<WorkflowRuntimeService> _logger;
    private readonly IWorkflowRecurringSettingsParser _workflowRecurringSettingsParser;
    private readonly IWorkflowService _workflowService;
    private readonly IBurstyWorkflowService _burstyWorkflowService;

    public WorkflowRuntimeService(
        IStateService stateService,
        IStepRequester stepRequester,
        IBus bus,
        IServiceBusManager serviceBusManager,
        ILogger<WorkflowRuntimeService> logger,
        IWorkflowRecurringSettingsParser workflowRecurringSettingsParser,
        IWorkflowService workflowService,
        IBurstyWorkflowService burstyWorkflowService,
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _stateService = stateService;
        _stepRequester = stepRequester;
        _bus = bus;
        _serviceBusManager = serviceBusManager;
        _logger = logger;
        _workflowRecurringSettingsParser = workflowRecurringSettingsParser;
        _workflowService = workflowService;
        _burstyWorkflowService = burstyWorkflowService;
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public async Task ExecuteWorkflowAsync(
        ProxyState state,
        string? workflowExecutionReasonCode)
    {
        _logger.LogInformation(
            "state: {State}, workflowExecutionReasonCode: {WorkflowExecutionReasonCode}, ",
            state.StateStatus,
            workflowExecutionReasonCode);
        var workflow = state.WorkflowContext.SnapshottedWorkflow;

        if (state.StateStatus == StateStatuses.Running)
        {
            await _bus.Publish(
                new OnWorkflowExecutionStartedEvent(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    workflowExecutionReasonCode,
                    state.Identity,
                    workflow.WorkflowType,
                    null,
                    subWorkflowType: workflow.GetSubWorkflowType()));

            await PublishUserProfileEnrolledIntoWorkflowEventAsync(state);
        }
        else if (state.StateStatus == StateStatuses.Scheduled)
        {
            await _serviceBusManager.PublishAsync(
                new OnWorkflowExecutionScheduledEvent(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    workflowExecutionReasonCode,
                    state.Identity,
                    workflow.WorkflowType,
                    null));
        }

        Step? initialStep = null;
        if (workflow.WorkflowScheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted is not true)
        {
            initialStep = workflow.Steps.FirstOrDefault();
        }
        else // Old Scheduled Workflow Schema, we need to skip the first 4 steps (we have put the contact data into usr_var_dict in state already)
        {
            initialStep = workflow.Steps
                .SkipWhile(step => step is not CallStep<ScheduledTriggerConditionsCheckStepArgs>)
                .Skip(1)
                .FirstOrDefault();
            // 1. Uses `SkipWhile` to skip all steps until the first occurrence of `CallStep<ScheduledTriggerConditionsCheckStepArgs>`.
            // 2. Uses `Skip(1)` to move to the step immediately after the first occurrence.
            // 3. Retrieves the next step using `FirstOrDefault()`. If no such step exists, `initialStep` will be `null`.
        }

        _logger.LogInformation("initial step: {InitialStep}", JsonSerializer.Serialize(initialStep));

        if (initialStep == null)
        {
            await CompleteWorkflowAsync(state);

            return;
        }

        await _stepRequester.RequestAsync(
            state.Id,
            initialStep.Id,
            new Stack<StackEntry>(),
            null);
    }

    public async Task CompleteWorkflowAsync(ProxyState state)
    {
        var targetState = await _stateService.GetOrDefaultStateByIdAsync(
            state.Identity.SleekflowCompanyId,
            state.Id);

        if (targetState?.StateStatus is not StateStatuses.Running)
        {
            return;
        }

        await _stateService.CompleteStateAsync(state.Id);

        await _serviceBusManager.PublishAsync(
            new OnWorkflowExecutionEndedV2Event(
                state.Identity.SleekflowCompanyId,
                state.Id,
                WorkflowExecutionStatuses.Complete,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType,
                null,
                state.WorkflowContext.SnapshottedWorkflow.GetSubWorkflowType()));

        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true
            })
        {
            var (updatedProxyWorkflow, isUpdated) = await _workflowService.UpdateOldScheduledWorkflowSchemaFirstRecurringCompletedAsync(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId,
                state.WorkflowContext.SnapshottedWorkflow.SleekflowCompanyId);

            if (updatedProxyWorkflow is not null && isUpdated) // ensure only one workflow is updated and then trigger the recurrence for scheduled workflow in durable function once
            {
                await HandleWorkflowRecurrenceAsync(state, updatedProxyWorkflow);
            }

            // isUpdated: false -> for other enrolment also with old scheduled workflow schema with first recurrence completed, we dont need to trigger the recurrence
        }
        else
        {
            await HandleWorkflowRecurrenceAsync(state);
        }
    }

    public async Task StartWorkflowAsync(
        string sleekflowCompanyId,
        string stateId)
    {
        var targetState = await _stateService.GetOrDefaultStateByIdAsync(
            sleekflowCompanyId,
            stateId);

        if (targetState is null
            || targetState.StateStatus == StateStatuses.Running)
        {
            return;
        }

        await _stateService.StartStateAsync(stateId);

        await _bus.Publish(
            new OnWorkflowExecutionStartedEvent(
                targetState.Identity.SleekflowCompanyId,
                targetState.Id,
                null,
                targetState.Identity,
                targetState.WorkflowContext.SnapshottedWorkflow.WorkflowType,
                null));

        await PublishUserProfileEnrolledIntoWorkflowEventAsync(targetState);
    }

    public async Task FailWorkflowAsync(
        ProxyState state,
        string? reasonCode = null,
        AuditEntity.SleekflowStaff? sleekflowStaff = null)
    {
        var targetState = await _stateService.GetOrDefaultStateByIdAsync(
            state.Identity.SleekflowCompanyId,
            state.Id);

        if (targetState?.StateStatus is not StateStatuses.Running)
        {
            return;
        }

        await _stateService.FailStateAsync(
            state.Id,
            reasonCode);

        await _serviceBusManager.PublishAsync(
            new OnWorkflowExecutionEndedV2Event(
                state.Identity.SleekflowCompanyId,
                state.Id,
                WorkflowExecutionStatuses.Failed,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType,
                sleekflowStaff,
                state.WorkflowContext.SnapshottedWorkflow.GetSubWorkflowType()));

        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true
            })
        {
            var (updatedProxyWorkflow, isUpdated) = await _workflowService.UpdateOldScheduledWorkflowSchemaFirstRecurringCompletedAsync(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId,
                state.WorkflowContext.SnapshottedWorkflow.SleekflowCompanyId);

            if (updatedProxyWorkflow is not null && isUpdated) // ensure only one workflow is updated and then trigger the recurrence for scheduled workflow in durable function once
            {
                await HandleWorkflowRecurrenceAsync(state, updatedProxyWorkflow);
            }

            // isUpdated: false -> for other enrolment also with old scheduled workflow schema with first recurrence completed, we dont need to trigger the recurrence
        }
        else if (state.WorkflowContext.SnapshottedWorkflow.WorkflowType is WorkflowType.AIAgent)
        {
            var runningStates = await _stateService.GetRunningStatesAsync(
                state.Identity.ObjectId,
                state.Identity.ObjectType,
                state.Identity.SleekflowCompanyId,
                state.TriggerEventBody);

            var (parentState, targetStepInParent) = _stateService
                .GetParentState(runningStates, state.WorkflowContext.SnapshottedWorkflow.WorkflowId);

            await _serviceBusManager.PublishAsync(
                new OnAiAgentStepExitEvent(
                    targetStepInParent.Id,
                    parentState.Id,
                    new Stack<StackEntry>(),
                    parentState.WorkflowContext.SnapshottedWorkflow.WorkflowId,
                    AgentStepExitConditions.InternalException));
        }
        else
        {
            await HandleWorkflowRecurrenceAsync(state);
        }
    }

    public async Task CancelWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode,
        AuditEntity.SleekflowStaff? sleekflowStaff = null)
    {
        var state = await _stateService.GetOrDefaultStateByIdAsync(
            sleekflowCompanyId,
            stateId);

        if (state is null)
        {
            throw new SfNotFoundObjectException(
                stateId,
                partitionKey: stateId);
        }

        if (state.StateStatus != StateStatuses.Running)
        {
            throw new SfWorkflowExecutionNotCancellableException(
                state.Id,
                state.StateStatus);
        }

        await _stateService.CancelStateAsync(
            state.Id,
            reasonCode);

        await _serviceBusManager.PublishAsync(
            new OnWorkflowExecutionEndedV2Event(
                state.Identity.SleekflowCompanyId,
                state.Id,
                WorkflowExecutionStatuses.Cancelled,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType,
                sleekflowStaff,
                state.WorkflowContext.SnapshottedWorkflow.GetSubWorkflowType()));

        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true
            })
        {
            var (updatedProxyWorkflow, isUpdated) = await _workflowService.UpdateOldScheduledWorkflowSchemaFirstRecurringCompletedAsync(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId,
                state.WorkflowContext.SnapshottedWorkflow.SleekflowCompanyId);

            if (updatedProxyWorkflow is not null && isUpdated) // ensure only one workflow is updated and then trigger the recurrence for scheduled workflow in durable function once
            {
                if (reasonCode is not (
                    StateReasonCodes.VersionedWorkflowDeleted
                    or StateReasonCodes.VersionedWorkflowOutdated
                    or StateReasonCodes.VersionedWorkflowDisabled))
                {
                    await HandleWorkflowRecurrenceAsync(state, updatedProxyWorkflow);
                }
            }

            // isUpdated: false -> for other enrolment also with old scheduled workflow schema with first recurrence completed, we dont need to trigger the recurrence
        }
        else
        {
            if (reasonCode is not (
                StateReasonCodes.VersionedWorkflowDeleted
                or StateReasonCodes.VersionedWorkflowOutdated
                or StateReasonCodes.VersionedWorkflowDisabled))
            {
                await HandleWorkflowRecurrenceAsync(state);
            }
        }
    }

    public async Task AbandonWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode)
    {
        var state = await _stateService.GetOrDefaultStateByIdAsync(
            sleekflowCompanyId,
            stateId);

        if (state is null)
        {
            throw new SfNotFoundObjectException(
                stateId,
                partitionKey: stateId);
        }

        if (state.StateStatus is StateStatuses.Running or StateStatuses.Abandoned)
        {
            return;
        }

        await _stateService.AbandonStateAsync(
            state.Id,
            reasonCode);

        await _serviceBusManager.PublishAsync(
            new OnWorkflowExecutionEndedV2Event(
                state.Identity.SleekflowCompanyId,
                state.Id,
                WorkflowExecutionStatuses.Abandoned,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType,
                null,
                state.WorkflowContext.SnapshottedWorkflow.GetSubWorkflowType()));

        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true
            })
        {
           var (updatedProxyWorkflow, isUpdated) = await _workflowService.UpdateOldScheduledWorkflowSchemaFirstRecurringCompletedAsync(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId,
                state.WorkflowContext.SnapshottedWorkflow.SleekflowCompanyId);

           if (updatedProxyWorkflow is not null && isUpdated) // ensure only one workflow is updated and then trigger the recurrence for scheduled workflow in durable function once
           {
               await HandleWorkflowRecurrenceAsync(state, updatedProxyWorkflow);
           }

           // isUpdated: false -> for other enrolment also with old scheduled workflow schema with first recurrence completed, we dont need to trigger the recurrence
        }
        else
        {
            await HandleWorkflowRecurrenceAsync(state);
        }
    }

    public async Task BlockWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode)
    {
        var state = await _stateService.GetOrDefaultStateByIdAsync(
            sleekflowCompanyId,
            stateId);

        if (state is null)
        {
            throw new SfNotFoundObjectException(
                stateId,
                partitionKey: stateId);
        }

        await _stateService.BlockStateAsync(
            state.Id,
            reasonCode);

        await _bus.Publish(
            new OnWorkflowExecutionBlockedEvent(
                state.Identity.SleekflowCompanyId,
                state.Id,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType));

        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true
            })
        {
            var (updatedProxyWorkflow, isUpdated) = await _workflowService.UpdateOldScheduledWorkflowSchemaFirstRecurringCompletedAsync(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId,
                state.WorkflowContext.SnapshottedWorkflow.SleekflowCompanyId);

            if (updatedProxyWorkflow is not null && isUpdated) // ensure only one workflow is updated and then trigger the recurrence for scheduled workflow in durable function once
            {
                await HandleWorkflowRecurrenceAsync(state, updatedProxyWorkflow);
            }

            // isUpdated: false -> for other enrolment also with old scheduled workflow schema with first recurrence completed, we dont need to trigger the recurrence
        }
        else
        {
            await HandleWorkflowRecurrenceAsync(state);
        }
    }

    public async Task RestrictWorkflowAsync(
        string sleekflowCompanyId,
        string stateId,
        string reasonCode)
    {
        var state = await _stateService.GetOrDefaultStateByIdAsync(
            sleekflowCompanyId,
            stateId);

        if (state is null)
        {
            throw new SfNotFoundObjectException(
                stateId,
                partitionKey: stateId);
        }

        if (state.StateStatus is StateStatuses.Restricted)
        {
            return;
        }

        await _stateService.RestrictStateAsync(
            state.Id,
            reasonCode);

        await _bus.Publish(
            new OnWorkflowExecutionRestrictedEvent(
                state.Identity.SleekflowCompanyId,
                state.Id,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType));

        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true
            })
        {
            var (updatedProxyWorkflow, isUpdated) = await _workflowService.UpdateOldScheduledWorkflowSchemaFirstRecurringCompletedAsync(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId,
                state.WorkflowContext.SnapshottedWorkflow.SleekflowCompanyId);

            if (updatedProxyWorkflow is not null && isUpdated) // ensure only one workflow is updated and then trigger the recurrence for scheduled workflow in durable function once
            {
                await HandleWorkflowRecurrenceAsync(state, updatedProxyWorkflow);
            }

            // isUpdated: false -> for other enrolment also with old scheduled workflow schema with first recurrence completed, we dont need to trigger the recurrence
        }
        else
        {
            await HandleWorkflowRecurrenceAsync(state);
        }
    }

    private async Task PublishUserProfileEnrolledIntoWorkflowEventAsync(ProxyState state)
    {
        if (state.Identity.ObjectType is "Contact" or "Contact.Id")
        {
            try
            {
                await _bus.Publish(
                    new OnUserProfileEnrolledIntoFlowHubWorkflowEvent(
                        state.Identity.SleekflowCompanyId,
                        state.Identity.WorkflowId,
                        state.Identity.WorkflowVersionedId,
                        state.WorkflowContext.SnapshottedWorkflow.Name,
                        state.Identity.ObjectId,
                        state.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to publish OnUserProfileEnrolledIntoFlowHubWorkflowEvent for state {StateId} in workflow {WorkflowVersionedId}",
                    state.Id,
                    state.Identity.WorkflowVersionedId);
            }
        }
    }

    private async Task HandleWorkflowRecurrenceAsync(ProxyState state, ProxyWorkflow? updatedProxyWorkflow = null)
    {
        var workflowScheduleSettings = state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings;

        if (!workflowScheduleSettings.ScheduledAt.HasValue
            || workflowScheduleSettings.RecurringSettings is null
            || state.IsReenrolllment)
        {
            return;
        }

        // route the old scheduled workflow schema to use the new scheduled workflow durable function
        if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings is {
                ScheduleType: WorkflowScheduleTypes.PredefinedDateTime,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: not true }

            && updatedProxyWorkflow is { WorkflowScheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted: true })
        {
            try
            {
                _logger.LogInformation(
                    "Routing old scheduled workflow {WorkflowVersionedId} schema to use the new scheduled workflow durable function",
                    updatedProxyWorkflow.WorkflowVersionedId);
                await _burstyWorkflowService.TriggerScheduledWorkflowAsync(
                    updatedProxyWorkflow.SleekflowCompanyId,
                    updatedProxyWorkflow.WorkflowId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to trigger scheduled workflow for state {StateId} in {WorkflowVersionedId}",
                    state.Id,
                    state.Identity.WorkflowVersionedId);
            }
        }
        else if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema is not true
                 && state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings.IsOldScheduledWorkflowSchemaFirstRecurringCompleted is not true)
        {
            var nextOccurrence = _workflowRecurringSettingsParser.GetNextOccurrence(
                workflowScheduleSettings.ScheduledAt!.Value,
                workflowScheduleSettings.RecurringSettings);

            try
            {
                await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
                    new OnTriggerEventRequestedEvent(
                        new OnContactRecurrentlyEnrolledEventBody(
                            DateTimeOffset.UtcNow,
                            state.Identity.ObjectId,
                            state.Identity.WorkflowId,
                            state.Identity.WorkflowVersionedId,
                            nextOccurrence),
                        state.Identity.ObjectId,
                        state.Identity.ObjectType,
                        state.Identity.SleekflowCompanyId),
                    RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                        state.Identity.SleekflowCompanyId));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to publish {EventName} for state {StateId} in {WorkflowVersionedId}",
                    nameof(OnContactRecurrentlyEnrolledEventBody),
                    state.Id,
                    state.Identity.WorkflowVersionedId);
            }
        }
    }
}