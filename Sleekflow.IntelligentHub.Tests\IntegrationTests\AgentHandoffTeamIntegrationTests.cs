using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.IntelligentHub.Events;
using Sleekflow.Models.Chats;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class AgentHandoffTeamIntegrationTests
{
    private ITestHarness _harness;

    [SetUp]
    public void Setup()
    {
        var provider = Application.InMemoryBusHost.Server.Services;
        _harness = provider.GetRequiredService<ITestHarness>();
    }

    private const string SleekflowCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";

    [Test]
    [TestCase(
        "Computer: Answer questions related to computer\nTaxation: Answer questions related to Taxation\nUnknown: cannot decide which product the customer is interested in",
        "[]")]
    [TestCase(
        "Computer: Answer questions related to computer\nTaxation: Answer questions related to Taxation\nUnknown: cannot decide which product the customer is interested in",
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"我想咨询下税务问题\"},{\"bot\":\"您好，您想咨询什么税务问题\"},{\"user\":\"算了，你们还卖电脑是么\"},{\"bot\":\"是的，我们和很多电脑品牌进行合作，请问您想咨询游戏电脑还是办公电脑\"},{\"user\":\"游戏笔记本电脑\"}]")]
    [TestCase(
        "MedicalBeauty: Answer questions related to medical beauty\nTaxation: Answer questions related to Taxation\nUnknown: cannot decide which product the customer is interested in",
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"我想咨询下水光针\"},{\"bot\":\"您好，我们这里有很多美容套餐，请问您主要想了解价格还是别的\"},{\"user\":\"我想问下价格\"}]")]
    [TestCase(
        "Mobile: responsible for HKBN mobile plan product\nHealthcare: responsible for Botiwe health care product\nUnknown: cannot decide which product the customer is interested in",
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"我们公司需要升级手机计划，大约有50名员工需要新的资费套餐。\"},{\"bot\":\"您好，感谢您联系我们关于企业手机计划。我们有专门针对企业客户的优惠方案，可以为您的50名员工提供灵活的数据和通话套餐。请问您对数据使用量有什么特别的要求吗？\"},{\"user\":\"我们主要需要大量的本地数据，至少每人每月10GB，以及一些中国内地漫游数据。\"},{\"bot\":\"我们的企业优惠计划正好符合您的需求。我们可以提供每人每月15GB本地数据和2GB中国内地漫游数据的方案，还附带无限本地通话。企业客户还可享受额外9折优惠。您希望安排一次详细咨询吗？\"},{\"user\":\"听起来不错。我想了解一下具体的价格和合同期限。\"}]")]
    [TestCase(
        "Mobile: responsible for HKBN mobile plan product\nHealthcare: responsible for Botiwe health care product\nUnknown: cannot decide which product the customer is interested in",
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"我想了解一下醫療服務計劃。\"},{\"bot\":\"香港寬頻市務部與Bowtie將於2024年6月27日推出全新「四合一醫療服務計劃」,免診金、免排隊、免藥費及包醫生紙,讓客戶以超值價格獲得全面的醫療保障。低至每月$99 就可享12個月計劃期內無限次普通科西醫視像會診、洗牙及全面牙科檢查、全身檢查及流感疫苗接種*,新客戶及現有客戶同樣可享計劃內高達$3,500醫療保健服務。\"},{\"user\":\"服务时间呢\"},{\"bot\":\"遙距視像會診的服務時間為週一至週五，上午9時至下午7時，和週六上午9時至下午1時，但不涵蓋公眾假期以及在黑色暴雨警告或八號或以上颱風信號生效期間。\"},{\"user\":\"我想订购。\"}]")]
    [TestCase(
        "Mobile: responsible for HKBN mobile plan product\nHealthcare: responsible for Botiwe health care product\nUnknown: cannot decide which product the customer is interested in",
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"I would like to learn about your mobile services.\"},{\"bot\":\"To meet the growing demand for 'Asia-Pacific and Greater Bay Area' roaming data and 'China mobile phone numbers,' the marketing department will launch the 'N Mobile Local + Asia-Pacific Data Mobile Communication Plan with China Mobile Number' on April 24, 2024. The plan includes: Asia-Pacific data plans and Asia-Pacific data plans with a China Mainland number.\"},{\"user\":\"I heard you also have a home care plan.\"},{\"bot\":\"HKBN x Evercare 'Stay at Home' Home Care Plan. For as low as $399 per month, enjoy attentive home care services, making life easier for you and your family. Online booking available for a hassle-free process.\"},{\"user\":\"I would like to subscribe.\"}]")]
    [TestCase(
        "Mobile: responsible for HKBN mobile plan product\nHealthcare: responsible for Botiwe health care product\nUnknown: cannot decide which product the customer is interested in",
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"/clear\"},{\"user\":\"I'm looking for a good Italian restaurant nearby.\"}]")]
    public async Task AgentHandoffTeamTest(string teamCategories, string chatEntries)
    {
        // Arrange
        var consumer = _harness.GetConsumerHarness<GetAgentHandoffTeamEventConsumer>();

        // Act
        await _harness.Bus.Publish(
            new GetAgentHandoffTeamEvent(
                string.Empty,
                string.Empty,
                new Stack<StackEntry>(),
                SleekflowCompanyId,
                JsonConvert.DeserializeObject<List<SfChatEntry>>(chatEntries)!,
                teamCategories));

        // Assert
        Assert.That(await _harness.Consumed.Any<GetAgentHandoffTeamEvent>());
        Assert.That(await consumer.Consumed.Any<GetAgentHandoffTeamEvent>());
    }
}