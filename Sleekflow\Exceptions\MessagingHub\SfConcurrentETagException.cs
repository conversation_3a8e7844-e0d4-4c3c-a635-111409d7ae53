namespace Sleekflow.Exceptions.MessagingHub;

public class SfConcurrentETagException : ErrorCodeException
{
    public string TargetEntity { get; }

    public string TargetEntityId { get; }

    public string TargetETag { get; }

    public string CurrentETag { get; }

    public SfConcurrentETagException(string targetEntity, string targetEntityId, string targetETag, string currentETag)
        : base(
            ErrorCodeConstant.SfConcurrentETagException,
            $"The {targetEntity} id {targetEntityId} eTag is incorrect. Someone has made the change to the record already. Please check. your target eTag=[{targetETag}] while current eTag=[{currentETag}]",
            new Dictionary<string, object?>
            {
                {
                    "targetEntity", targetEntity
                },
                {
                    "targetEntityId", targetEntityId
                },
                {
                    "targetETag", targetETag
                },
                {
                    "currentETag", currentETag
                }
            })
    {
        TargetEntity = targetEntity;
        TargetEntityId = targetEntityId;
        TargetETag = targetETag;
        CurrentETag = currentETag;
    }
}