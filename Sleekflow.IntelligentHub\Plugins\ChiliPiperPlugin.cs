using System.ComponentModel;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Links;

namespace Sleekflow.IntelligentHub.Plugins;

using Microsoft.SemanticKernel;

/// <summary>
/// Represents the response from Chili Piper's Concierge Router API
/// </summary>
public class ChiliPiperResponse
{
    [JsonProperty("routeId")]
    public string? RouteId { get; set; }

    [JsonProperty("routingLink")]
    public string? RoutingLink { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("distributionId")]
    public string? DistributionId { get; set; }

    [JsonProperty("timeoutInMS")]
    public int? TimeoutInMS { get; set; }

    [JsonProperty("timeoutRedirectUrl")]
    public string? TimeoutRedirectUrl { get; set; }
}

/// <summary>
/// Represents the client response for Chili Piper scheduling (without internal routing fields)
/// </summary>
public class ChiliPiperClientResponse
{
    [JsonProperty("routeId")]
    public string? RouteId { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonProperty("distributionId")]
    public string? DistributionId { get; set; }

    [JsonProperty("timeoutInMS")]
    public int? TimeoutInMS { get; set; }
}

/// <summary>
/// Chili Piper Integration Plugin
///
/// This plugin handles integration with Chili Piper's Concierge Router REST API to schedule
/// sales demos from lead nurturing conversations. When all required fields are gathered from
/// the customer conversation, the DemoSchedulingPlanningAgent will use this plugin to create
/// a booking through Chili Piper.
///
/// ## Dependency Injection
///
/// The Chili Piper integration is implemented as a separate plugin that is automatically
/// registered in the dependency injection container via the IScopedService interface.
/// The system will automatically detect and inject this plugin when it's needed.
///
/// The LeadNurturingAgentActionsDefinitions class takes this plugin as a dependency:
///
/// ```csharp
/// public LeadNurturingAgentActionsDefinitions(
///     ILogger.<LeadNurturingAgentActionsDefinitions> logger,
///     ISleekflowToolsPlugin sleekflowToolsPlugin,
///     IChiliPiperPlugin chiliPiperPlugin)
/// {
///     _logger = logger;
///     _sleekflowToolsPlugin = sleekflowToolsPlugin;
///     _chiliPiperPlugin = chiliPiperPlugin;
/// }
/// ```
///
/// ## Configuration
///
/// To enable Chili Piper integration, add a ChiliPiperConfig to the DemoTool configuration:
///
/// ```json
/// {
///   "demo_tool": {
///     "required_fields": [
///       {
///         "name": "first_name",
///         "description": "Customer's first name",
///         "is_required": true
///       },
///       {
///         "name": "last_name",
///         "description": "Customer's last name",
///         "is_required": true
///       },
///       {
///         "name": "email",
///         "description": "Customer's email address",
///         "is_required": true
///       },
///       {
///         "name": "phone_number",
///         "description": "Customer's phone number",
///         "is_required": true
///       },
///       {
///         "name": "company_name",
///         "description": "Customer's company name",
///         "is_required": true
///       },
///       {
///         "name": "employees_count",
///         "description": "Number of employees at customer's company",
///         "is_required": false
///       },
///       {
///         "name": "country",
///         "description": "Customer's country",
///         "is_required": false
///       },
///       {
///         "name": "message",
///         "description": "Additional message from customer",
///         "is_required": false
///       },
///       {
///         "name": "job_title",
///         "description": "Customer's job title",
///         "is_required": false
///       },
///       {
///         "name": "website",
///         "description": "Customer's company website",
///         "is_required": false
///       }
///     ],
///     "chili_piper_config": {
///       "api_url": "https://sleekflow.chilipiper.com/concierge-router/inbound_router/rest",
///       "field_mappings": {
///         "first_name": "PersonFirstName",
///         "last_name": "PersonLastName",
///         "email": "PersonEmail",
///         "phone_number": "bd87fc44-7fdd-40f0-8a3a-c4635ec2066a",
///         "company_name": "CompanyName",
///         "employees_count": "134d6b7e-edda-465f-bde4-d14826719a2c",
///         "country": "ef26de54-ca30-4a90-b233-69088e5741dc",
///         "country_region": "4b39f91e-f426-4ba3-aa3b-12f301734f97",
///         "message": "6333efc1-81ca-44c6-ae28-df0ff0e3ec42",
///         "ecommerce_platform": "e2f82b15-4a2a-4438-b068-34742b8acf97",
///         "job_title": "0e613939-d851-4b4c-8c24-b59d02dd7992",
///         "website": "f8d47804-05e5-4a11-a2b6-d733a96e06d4"
///       }
///     }
///   }
/// }
/// ```
///
/// ## Field Mapping
///
/// The field_mappings dictionary maps the internal field names used by the DemoSchedulingPlanningAgent
/// to the corresponding field names expected by Chili Piper's API. This allows for flexibility
/// in how fields are named and processed internally while ensuring correct mapping to Chili Piper's
/// required format.
///
/// ## API Flow
///
/// When a demo is requested and all required fields are gathered:
///
/// 1. The DemoSchedulingPlanningAgent generates a response with action_type set to "schedule_demo"
/// 2. The HandleDemoSchedulingPlanningAgentAction method in LeadNurturingAgentActionsDefinitions checks that all required fields are present
/// 3. If Chili Piper is configured, it calls the ScheduleDemoWithChiliPiper method in this plugin
/// 4. This method maps the fields according to the configuration and sends a request to the Chili Piper API
/// 5. Upon successful booking, a confirmation message is sent to the customer through the TransitioningResponseCrafterAgent
///
/// This integration allows for seamless scheduling of sales demos directly from customer
/// conversations without manual intervention.
/// </summary>
public interface IChiliPiperPlugin
{
    [KernelFunction("schedule_demo_with_chili_piper")]
    [Description("Schedules a demo using Chili Piper API.")]
    Task<string> ScheduleDemoWithChiliPiper(
        Kernel kernel,
        [Description("List of fields with names and values to send to Chili Piper")]
        List<LeadNurturingAgentDefinitions.FieldInfo> fields);
}

public class ChiliPiperPlugin : IChiliPiperPlugin, IScopedService
{
    private readonly ILogger<ChiliPiperPlugin> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IRequestClient<ShortenLinkRequest> _shortenLinkRequestClient;

    public ChiliPiperPlugin(
        ILogger<ChiliPiperPlugin> logger,
        IHttpClientFactory httpClientFactory,
        IRequestClient<ShortenLinkRequest> shortenLinkRequestClient)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _shortenLinkRequestClient = shortenLinkRequestClient;
    }

    private async Task<string> GetShortenedUrl(
        string url,
        string sleekflowCompanyId,
        string title)
    {
        var shortenLinkReplyResponse = await _shortenLinkRequestClient.GetResponse<ShortenLinkReply>(
            new ShortenLinkRequest(
                url,
                sleekflowCompanyId,
                title,
                "meeting.sleekflow.io"));
        var shortenLinkReply = shortenLinkReplyResponse.Message;

        return shortenLinkReply.ShortUrl;
    }

    /// <summary>
    /// Schedules a demo using Chili Piper's Concierge Router REST API.
    /// This method handles mapping fields from the internal format to Chili Piper's expected format,
    /// constructing the request payload, and making the API call.
    ///
    /// Required fields typically include:
    /// - first_name
    /// - last_name
    /// - email
    /// - company_name
    /// - phone_number
    ///
    /// Additional optional fields may include:
    /// - employees_count
    /// - country
    /// - message
    /// - job_title
    /// - website
    ///
    /// The field names above are mapped to Chili Piper field IDs through the configuration.
    /// </summary>
    /// <param name="kernel">Semantic Kernel instance containing contact and configuration data.</param>
    /// <param name="fields">List of fields with names and values to send to Chili Piper.</param>
    /// <returns>Status message indicating success or failure of the scheduling operation.</returns>
    [KernelFunction("schedule_demo_with_chili_piper")]
    [Description("Schedules a demo using Chili Piper API.")]
    public async Task<string> ScheduleDemoWithChiliPiper(
        Kernel kernel,
        [Description("List of fields with names and values to send to Chili Piper")]
        List<LeadNurturingAgentDefinitions.FieldInfo> fields)
    {
        var contactId = kernel.Data[KernelDataKeys.CONTACT_ID] as string;
        var stateId = kernel.Data[KernelDataKeys.STATE_ID] as string;
        var tools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;
        var demoTool = tools?.DemoTool;

        if (stateId == null || contactId == null || tools == null || demoTool == null ||
            demoTool.ChiliPiperConfig == null)
        {
            Console.WriteLine("Simulating demo scheduling with Chili Piper...");
            Console.WriteLine($"Action - Scheduled demo for Lead ({contactId})");
            Console.WriteLine($"Fields - {JsonConvert.SerializeObject(fields)}");
            Console.WriteLine();

            _logger.LogInformation("Simulating demo scheduling with Chili Piper...");
            _logger.LogInformation(
                "Action - Scheduled demo for Lead ({ContactId})",
                contactId);
            _logger.LogInformation("Fields - {Fields}", JsonConvert.SerializeObject(fields));

            return "Demo Scheduled (Simulated)";
        }

        try
        {
            // Create the request body for Chili Piper API
            var formData = new Dictionary<string, string>();

            // Convert list of fields to dictionary
            var fieldsDictionary = fields.ToDictionary(f => f.FieldName, f => f.FieldValue);

            // Map the fields based on configuration
            foreach (var mapping in demoTool.ChiliPiperConfig.FieldMappings)
            {
                if (fieldsDictionary.TryGetValue(mapping.Key, out var value))
                {
                    formData[mapping.Value] = value;
                }
            }

            // Create the payload structure as required by Chili Piper
            var payload = new
            {
                form = formData,
                options = new
                {
                    dynamicRedirectLink = "https://sleekflow.io/thank-you/booking-confirmed",
                }
            };

            // Make the API call to Chili Piper
            using var httpClient = _httpClientFactory.CreateClient("ChiliPiper");

            var content = new StringContent(
                JsonConvert.SerializeObject(payload),
                System.Text.Encoding.UTF8,
                "application/json");

            var response = await httpClient.PostAsync(demoTool.ChiliPiperConfig.ApiUrl, content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation(
                    "Successfully scheduled demo with Chili Piper for contact {ContactId}. Response: {Response}",
                    contactId,
                    responseContent);

                // Parse the response using the ChiliPiperResponse class
                var chiliPiperResponse = JsonConvert.DeserializeObject<ChiliPiperResponse>(responseContent);

                // Create client response without internal routing fields
                ChiliPiperClientResponse? clientResponse = null;
                if (chiliPiperResponse != null)
                {
                    clientResponse = new ChiliPiperClientResponse
                    {
                        RouteId = chiliPiperResponse.RouteId,
                        Url = chiliPiperResponse.Url,
                        DistributionId = chiliPiperResponse.DistributionId,
                        TimeoutInMS = chiliPiperResponse.TimeoutInMS
                    };

                    // Shorten the URL in the client response
                    if (!string.IsNullOrEmpty(clientResponse.Url))
                    {
                        clientResponse.Url = await GetShortenedUrl(
                            clientResponse.Url,
                            kernel.Data[KernelDataKeys.SLEEKFLOW_COMPANY_ID] as string ?? "",
                            "Chili Piper Meeting Link");
                    }
                }

                return "The information has been sent to Chili Piper. Use the link to choose a time for the demo. Response: " + JsonConvert.SerializeObject(clientResponse);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError(
                    "Failed to schedule demo with Chili Piper for contact {ContactId}. Status: {StatusCode}, Error: {Error}",
                    contactId,
                    response.StatusCode,
                    errorContent);
                return $"Failed to schedule demo: {response.StatusCode}";

            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Exception occurred while scheduling demo with Chili Piper for contact {ContactId}",
                contactId);
            return $"Error scheduling demo: {ex.Message}";
        }
    }
}