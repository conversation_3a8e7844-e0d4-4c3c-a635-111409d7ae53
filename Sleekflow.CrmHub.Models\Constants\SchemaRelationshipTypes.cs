﻿using System.Collections.Immutable;

namespace Sleekflow.CrmHub.Models.Constants;

public class SchemaRelationshipTypes
{
    public const string OneToOne = "one-to-one";
    public const string OneToMany = "one-to-many";
    public const string ManyToMany = "many-to-many";

    public static readonly ImmutableList<string> SupportedRelationships =
        new List<string>
            {
                OneToOne, OneToMany, ManyToMany,
            }
            .ToImmutableList();
}