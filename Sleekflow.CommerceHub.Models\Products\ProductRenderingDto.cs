using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Renderings;

namespace Sleekflow.CommerceHub.Models.Products;

public class ProductRenderingDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty("categories")]
    public List<CategoryRenderingDto> Categories { get; set; }

    [JsonProperty(Product.PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(Product.PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty(Product.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty("images")]
    public List<ImageDto> Images { get; set; }

    [JsonConstructor]
    public ProductRenderingDto(
        string id,
        string storeId,
        List<CategoryRenderingDto> categories,
        string? sku,
        string? url,
        string name,
        List<Description> descriptions,
        List<ImageDto> images)
    {
        Id = id;
        StoreId = storeId;
        Categories = categories;
        Sku = sku;
        Url = url;
        Name = name;
        Descriptions = descriptions;
        Images = images;
    }

    public ProductRenderingDto(
        Product product,
        List<Category> categories,
        LanguageOption languageOption)
        : this(
            product.Id,
            product.StoreId,
            categories.Select(c => new CategoryRenderingDto(c, languageOption)).ToList(),
            product.Sku,
            product.Url,
            product.Names.Find(n => n.LanguageIsoCode == languageOption.LanguageIsoCode)?.Value
            ?? product.Names.First(n => n.LanguageIsoCode == languageOption.DefaultLanguageIsoCode).Value,
            product
                .Descriptions
                .Where(
                    d =>
                        d.Image != null
                        || (d.Text != null && d.Text.LanguageIsoCode == languageOption.LanguageIsoCode))
                .ToList(),
            product.Images.Select(i => new ImageDto(i)).ToList())
    {
        var productCategoryIds = product.CategoryIds;
        if (productCategoryIds.Count > 0)
        {
            var categoryIds = categories.Select(c => c.Id).ToList();
            if (!productCategoryIds.SequenceEqual(categoryIds))
            {
                throw new ArgumentException(
                    $"Product {product.Id} has CategoryIds {string.Join(", ", productCategoryIds)} but Categories {string.Join(", ", categories.Select(c => c.Id))} were passed in");
            }
        }
    }

    public static ProductRenderingDto Sample()
    {
        return new ProductRenderingDto(
            "myProductId",
            "myStoreId",
            new List<CategoryRenderingDto>
            {
                CategoryRenderingDto.Sample()
            },
            "mySku",
            "https://google.com",
            "My Product Name",
            new List<Description>
            {
                new Description(
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a sample product description"),
                    null,
                    null)
            },
            new List<ImageDto>
            {
                new ImageDto(
                    "https://insideretail.asia/wp-content/uploads/2022/11/bigstock-Icon-Of-The-Whatsapp-App-On-T-413646935.jpg",
                    null)
            });
    }
}