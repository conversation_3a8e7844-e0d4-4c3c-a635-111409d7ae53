﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Providers.Models;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Providers.States;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("sys_state_loop_through_objects_progress")]
public class LoopThroughObjectsProgressState : Entity, IHasSleekflowCompanyId
{
    public const string PropertyNameStateObj = "state_obj";

    public class LoopThroughObjectsProgressStateObj
    {
        [JsonProperty("durable_payload")]
        public DurablePayload DurablePayload { get; set; }

        [JsonProperty("query_output")]
        public StatusQueryGetOutput<
            StatusQueryGetOutputInput,
            StatusQueryGetOutputCustomStatus,
            StatusQueryGetOutputOutput>? QueryOutput { get; set; }

        [JsonConstructor]
        public LoopThroughObjectsProgressStateObj(
            DurablePayload durablePayload,
            StatusQueryGetOutput<
                    StatusQueryGetOutputInput,
                    StatusQueryGetOutputCustomStatus,
                    StatusQueryGetOutputOutput>?
                queryOutput)
        {
            DurablePayload = durablePayload;
            QueryOutput = queryOutput;
        }
    }

    [JsonProperty("state_name")]
    public string StateName { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("flow_hub_workflow_id")]
    public string FlowHubWorkflowId { get; set; }

    [JsonProperty("flow_hub_workflow_versioned_id")]
    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameStateObj)]
    public LoopThroughObjectsProgressStateObj StateObj { get; set; }

    public LoopThroughObjectsProgressState(
        string id,
        string stateName,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        LoopThroughObjectsProgressStateObj stateObj,
        string sleekflowCompanyId)
        : base(id, "LoopThroughObjectsProgressState")
    {
        StateName = stateName;
        ProviderName = providerName;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        StateObj = stateObj;
        SleekflowCompanyId = sleekflowCompanyId;
    }
}