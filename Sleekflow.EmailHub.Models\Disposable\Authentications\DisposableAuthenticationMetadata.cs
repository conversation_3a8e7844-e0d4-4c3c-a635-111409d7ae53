using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.Disposable.Authentications;

public class DisposableAuthenticationMetadata : EmailAuthenticationMetadata
{
    [Json<PERSON>roperty("server_name")]
    public string ServerName { get; set; }

    [Json<PERSON>roperty("port_number")]
    public int PortNumber { get; set; }

    [JsonProperty("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }

    [JsonConstructor]
    public DisposableAuthenticationMetadata(
        string serverName,
        int portNumber,
        string username,
        string password)
        : base(ProviderNames.Disposable, ProviderNames.Disposable)
    {
        ServerName = serverName;
        PortNumber = portNumber;
        Username = username;
        Password = password;
    }
}