using MassTransit;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;

namespace Sleekflow.CrmHub.Events;

public class OnProviderInitializedEventConsumerDefinition : ConsumerDefinition<OnProviderInitializedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnProviderInitializedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnProviderInitializedEventConsumer : IConsumer<OnProviderInitializedEvent>
{
    private readonly IProviderConfigService _providerConfigService;

    public OnProviderInitializedEventConsumer(
        IProviderConfigService providerConfigService)
    {
        _providerConfigService = providerConfigService;
    }

    public async Task Consume(ConsumeContext<OnProviderInitializedEvent> context)
    {
        var @event = context.Message;

        var providerConfig =
            await _providerConfigService.GetProviderConfigAsync(@event.SleekflowCompanyId, @event.ProviderName);

        await _providerConfigService.UpdateIsAuthenticatedAsync(providerConfig.Id, @event.SleekflowCompanyId, true);
    }
}