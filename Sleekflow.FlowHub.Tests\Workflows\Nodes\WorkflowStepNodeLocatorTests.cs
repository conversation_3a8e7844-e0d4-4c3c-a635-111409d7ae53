﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Tests.Workflows.Nodes;

public class WorkflowStepNodeLocatorTests
{
    private readonly IWorkflowStepNodeLocator _workflowStepNodeLocator = new WorkflowStepNodeLocator();

    [Test]
    public void GetStepNodeId_GivenValidWorkflow_ShouldReturnTheCorrectStepNodeIdForSpecifiedStep()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "AmKUmnDlq5YkMJx",
                "workflow_versioned_id": "AmKUmnDlq5YkMJx-Np4cR2R6mplWqdz2",
                "name": "DEVS-3885 List Message Reply",
                "triggers": {
                    "message_received": {
                        "condition": "{{ ([\"***********\"] | array.contains event_body.channel_id) && ((((event_body.message.message_content | regex.replace \"\\\\*[^*]+\\\\*\\\\n\" '' | string.strip ) == \"DEVS-3885\")))  }}"
                    }
                },
                "steps": [
                    {
                        "id": "setup-contact-and-conversation",
                        "step_node_id": null,
                        "name": "setup",
                        "assign": {
                            "contact": "{{ trigger_event_body.contact_id | sleekflow.get_contact }}",
                            "lists": "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}",
                            "conversation": "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        "next_step_id": null
                    },
                    {
                        "id": "setup-contact-owner",
                        "step_node_id": null,
                        "name": "setup-contact-owner",
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "next_step_id": null
                    },
                    {
                        "call": "sys.sleep",
                        "args": {
                            "seconds__expr": "5"
                        },
                        "id": "aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb",
                        "step_node_id": "aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb",
                        "name": "Time delay 1",
                        "assign": null,
                        "next_step_id": "d1af52c2-842e-45da-b574-7e8977f540f5"
                    },
                    {
                        "switch": [
                            {
                                "condition": "{{ (usr_var_dict.contact[\"1824e844-348d-48f9-9746-4945dc380fff\"] | string.contains \"189736147\") }}",
                                "next_step_id": "39947d70-117d-4307-97ae-6754a9059b14"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                            }
                        ],
                        "id": "d1af52c2-842e-45da-b574-7e8977f540f5",
                        "step_node_id": "d1af52c2-842e-45da-b574-7e8977f540f5",
                        "name": "condition-branching",
                        "assign": null,
                        "next_step_id": null
                    },
                    {
                        "parallel_branches": [
                            {
                                "step": {
                                    "try": {
                                        "step": {
                                            "call": "sys.wait-for-event",
                                            "args": {
                                                "event_name": "OnMessageReceived",
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "timeout_seconds__expr": "45"
                                            },
                                            "id": "527ac07d-c6a7-4589-bb02-d95e3b16dfc6",
                                            "step_node_id": null,
                                            "name": "wait-for-message",
                                            "assign": {
                                                "message_527ac07d-c6a7-4589-bb02-d95e3b16dfc6": "{{ event_body.message.message_content }}",
                                                "error_527ac07d-c6a7-4589-bb02-d95e3b16dfc6": "{{ null }}"
                                            },
                                            "next_step_id": null
                                        }
                                    },
                                    "catch": {
                                        "as": "e",
                                        "step": {
                                            "id": "7b833e0a-7c81-46c2-a5a4-86a0779f5410",
                                            "step_node_id": null,
                                            "name": "timeout",
                                            "assign": {
                                                "error_527ac07d-c6a7-4589-bb02-d95e3b16dfc6": "{{ sys_var_dict.e.error_message }}"
                                            },
                                            "next_step_id": null
                                        }
                                    },
                                    "id": "a4a31ca7-5253-4960-a49f-e5bed0df6617",
                                    "step_node_id": null,
                                    "name": "try-catch-timeout",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            },
                            {
                                "step": {
                                    "call": "sleekflow.v1.send-message",
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_phone_number__expr": null,
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "to_facebook_comment_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "to_instagram_comment_id__expr": null
                                        },
                                        "message_type__expr": "interactive",
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"list\",\"body\":{\"text\":\"How do you wish to proceed?\",\"type\":\"text\"},\"action\":{\"button\":\"Options\",\"sections\":[{\"title\":\"\",\"rows\":[{\"id\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"title\":\"Option 1\",\"description\":\"Assign me to random people\"},{\"id\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"title\":\"Option 2\",\"description\":\"End flow\"}]}]}}} }}"
                                    },
                                    "id": "50447763-defd-4be6-a110-fe03fb54cd99",
                                    "step_node_id": null,
                                    "name": "Action 1",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            }
                        ],
                        "id": "39947d70-117d-4307-97ae-6754a9059b14",
                        "step_node_id": "39947d70-117d-4307-97ae-6754a9059b14",
                        "name": "send-message-and-wait-for-event",
                        "assign": null,
                        "next_step_id": "3abcc08a-77a9-4370-a120-4709405ea74c"
                    },
                    {
                        "switch": [
                            {
                                "condition": "{{ usr_var_dict[\"error_527ac07d-c6a7-4589-bb02-d95e3b16dfc6\"] != null }}",
                                "next_step_id": "79eb6ad9-b0b8-4406-b8aa-edf393ad771e"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_527ac07d-c6a7-4589-bb02-d95e3b16dfc6\"] ?? \" \" | string.strip) == (\"Option 1\" | string.strip) }}",
                                "next_step_id": "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_527ac07d-c6a7-4589-bb02-d95e3b16dfc6\"] ?? \" \" | string.strip) == (\"Option 2\" | string.strip) }}",
                                "next_step_id": "5bc611f0-ae94-42b4-9e6b-4610c37e8622"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2"
                            }
                        ],
                        "id": "3abcc08a-77a9-4370-a120-4709405ea74c",
                        "step_node_id": null,
                        "name": "timeout-with-answers-switch",
                        "assign": null,
                        "next_step_id": null
                    },
                    {
                        "call": "sleekflow.v1.send-message",
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_phone_number__expr": null,
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "to_facebook_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "to_facebook_comment_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "to_instagram_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "to_instagram_comment_id__expr": null
                            },
                            "message_type__expr": "text",
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Done assignment (assignment is not gonna happen, this is just for testing branching)\"}} }}"
                        },
                        "id": "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a",
                        "step_node_id": "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a",
                        "name": "Action 2",
                        "assign": null,
                        "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                    },
                    {
                        "call": "sleekflow.v1.send-message",
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_phone_number__expr": null,
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "to_facebook_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "to_facebook_comment_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "to_instagram_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "to_instagram_comment_id__expr": null
                            },
                            "message_type__expr": "text",
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"As you wish. Bye~\"}} }}"
                        },
                        "id": "5bc611f0-ae94-42b4-9e6b-4610c37e8622",
                        "step_node_id": "5bc611f0-ae94-42b4-9e6b-4610c37e8622",
                        "name": "Action 3",
                        "assign": null,
                        "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                    },
                    {
                        "call": "sleekflow.v1.send-message",
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_phone_number__expr": null,
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "to_facebook_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "to_facebook_comment_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "to_instagram_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "to_instagram_comment_id__expr": null
                            },
                            "message_type__expr": "text",
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"What did you say?\"}} }}"
                        },
                        "id": "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2",
                        "step_node_id": "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2",
                        "name": "Action 4",
                        "assign": null,
                        "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                    },
                    {
                        "parallel_branches": [
                            {
                                "step": {
                                    "substeps": [
                                        {
                                            "call": "sys.wait-for-event",
                                            "args": {
                                                "event_name": "OnMessageReceived",
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "timeout_seconds__expr": null
                                            },
                                            "id": "5ab610d5-7d61-49f3-a637-6272348b4cd9",
                                            "step_node_id": null,
                                            "name": "wait-for-on-message-received",
                                            "assign": {
                                                "message_5ab610d5-7d61-49f3-a637-6272348b4cd9": "{{ event_body.message.message_content }}"
                                            },
                                            "next_step_id": null
                                        }
                                    ],
                                    "id": "bdb524fe-4561-4a99-916e-45369c03005e",
                                    "step_node_id": null,
                                    "name": "wait-for-message",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            },
                            {
                                "step": {
                                    "call": "sleekflow.v1.send-message",
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_phone_number__expr": null,
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "to_facebook_comment_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "to_instagram_comment_id__expr": null
                                        },
                                        "message_type__expr": "interactive",
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"No reply after 45 seconds\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"title\":\"Again\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"title\":\"End\"}}]}}} }}"
                                    },
                                    "id": "da3cb93b-8d1e-4fab-a5ee-84ed1193a7ef",
                                    "step_node_id": null,
                                    "name": "Action 5",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            }
                        ],
                        "id": "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
                        "step_node_id": "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
                        "name": "send-message-and-wait-for-event",
                        "assign": null,
                        "next_step_id": "48eb342d-3f17-42c0-981b-e58da348dd25"
                    },
                    {
                        "switch": [
                            {
                                "condition": "{{ (usr_var_dict[\"message_5ab610d5-7d61-49f3-a637-6272348b4cd9\"] ?? \" \" | string.strip) == (\"Again\" | string.strip) }}",
                                "next_step_id": "96499ba3-22a4-4506-837b-66e101f6231f"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_5ab610d5-7d61-49f3-a637-6272348b4cd9\"] ?? \" \" | string.strip) == (\"End\" | string.strip) }}",
                                "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                            }
                        ],
                        "id": "48eb342d-3f17-42c0-981b-e58da348dd25",
                        "step_node_id": null,
                        "name": "answer-switch",
                        "assign": null,
                        "next_step_id": null
                    },
                    {
                        "id": "96499ba3-22a4-4506-837b-66e101f6231f",
                        "step_node_id": null,
                        "name": "jumpTo_39947d70-117d-4307-97ae-6754a9059b14",
                        "assign": null,
                        "next_step_id": "39947d70-117d-4307-97ae-6754a9059b14"
                    },
                    {
                        "id": "6301a06a-383e-4aee-acef-1a6a645d5e74",
                        "step_node_id": null,
                        "name": "end",
                        "assign": null,
                        "next_step_id": null
                    }
                ],
                "activation_status": "Active",
                "metadata": {
                    "v2": "{\"nodes\":[{\"width\":300,\"height\":216,\"id\":\"61b28ce1-4d15-4af0-b3e8-41cb88d18469\",\"selected\":false,\"position\":{\"x\":0,\"y\":0},\"data\":{\"category\":\"start\",\"formValues\":{\"triggerType\":\"incoming-messages\",\"channels\":[{\"id\":\"***********\",\"subId\":154,\"name\":\"Flow Builder Test UAT\",\"channel\":\"whatsappcloudapi\",\"messagingHubWabaId\":\"amUx8pKxvn051X\",\"facebookWabaBusinessId\":\"3305169899744267\"}],\"conversationStatus\":\"\",\"saveWebhookDataAsVariables\":[],\"identifierType\":\"\",\"identifierExpression\":\"\",\"enableAutoCreateContact\":false,\"contactProperties\":[],\"addedLists\":[],\"removedLists\":[],\"isFilterByCondition\":true,\"conditions\":[{\"conditionType\":\"keyword\",\"operator\":\"containsExactly\",\"keywords\":[{\"value\":\"DEVS-3885\"}]}],\"title\":\" \"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"start\",\"positionAbsolute\":{\"x\":0,\"y\":0}},{\"width\":300,\"height\":96,\"id\":\"aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb\",\"selected\":false,\"position\":{\"x\":0,\"y\":336},\"data\":{\"category\":\"timeDelay\",\"formValues\":{\"title\":\"Time delay 1\",\"type\":\"elapsed\",\"timeUnit\":\"second\",\"timeDuration\":\"5\",\"dateTime\":null},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"timeDelay\",\"positionAbsolute\":{\"x\":0,\"y\":336}},{\"width\":300,\"height\":36,\"id\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"position\":{\"x\":0,\"y\":552},\"data\":{\"category\":\"condition\",\"formValues\":{\"title\":\"branching\"}},\"type\":\"branching\",\"positionAbsolute\":{\"x\":0,\"y\":552}},{\"width\":300,\"height\":148,\"id\":\"6293f764-63c6-415f-a173-1b6ea491fe94\",\"selected\":false,\"position\":{\"x\":-192.5,\"y\":708},\"data\":{\"category\":\"condition\",\"formValues\":{\"title\":\"Condition 1\",\"conditions\":[{\"conditionType\":\"contactProperty\",\"contactProperty\":{\"id\":\"1824e844-348d-48f9-9746-4945dc380fff\",\"label\":\"Phone Number\",\"type\":\"PhoneNumber\"},\"operator\":\"contains\",\"value\":\"189736147\",\"keywords\":[]}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"condition\",\"positionAbsolute\":{\"x\":-192.5,\"y\":708}},{\"width\":300,\"height\":96,\"id\":\"8e7786ce-35a4-48b7-8722-e699abafba75\",\"position\":{\"x\":192.5,\"y\":708},\"data\":{\"category\":\"condition\",\"formValues\":{\"title\":\"else\"}},\"type\":\"elseCondition\",\"positionAbsolute\":{\"x\":192.5,\"y\":708}},{\"width\":300,\"height\":239,\"id\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"selected\":false,\"position\":{\"x\":-192.5,\"y\":976},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 1\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"How do you wish to proceed?\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"LIST_MESSAGE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"Options\",\"sectionList\":[{\"sectionTitle\":\"\",\"options\":[{\"defaultTitle\":\"Title 1\",\"title\":\"Option 1\",\"nodeId\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"description\":\"Assign me to random people\",\"readOnly\":false},{\"defaultTitle\":\"Title 2\",\"title\":\"Option 2\",\"nodeId\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"description\":\"End flow\"}]}],\"isButtonAsBranch\":true,\"isSaveReply\":false,\"isMessageTimeout\":true,\"timeoutDuration\":\"45\",\"timeoutUnit\":\"second\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-192.5,\"y\":976}},{\"width\":300,\"height\":116,\"id\":\"fadf0d7a-6b77-41da-8225-adc6d8b40ac3\",\"position\":{\"x\":192.5,\"y\":924},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":192.5,\"y\":924}},{\"width\":300,\"height\":44,\"id\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"selected\":false,\"position\":{\"x\":-965.5,\"y\":1335},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 1\",\"title\":\"Option 1\",\"nodeId\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"description\":\"Assign me to random people\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"listMessage\",\"positionAbsolute\":{\"x\":-965.5,\"y\":1335}},{\"width\":300,\"height\":44,\"id\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"position\":{\"x\":-578.5,\"y\":1335},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 2\",\"title\":\"Option 2\",\"nodeId\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"description\":\"End flow\"},\"category\":\"option\"},\"type\":\"listMessage\",\"positionAbsolute\":{\"x\":-578.5,\"y\":1335}},{\"width\":300,\"height\":44,\"id\":\"952a6b14-f9fb-4075-899f-ae55e2cdc8dc\",\"position\":{\"x\":-191.5,\"y\":1335},\"data\":{\"formValues\":{\"title\":\"Other answers\"},\"category\":\"option\"},\"type\":\"elseAnswer\",\"positionAbsolute\":{\"x\":-191.5,\"y\":1335}},{\"width\":300,\"height\":44,\"id\":\"7b833e0a-7c81-46c2-a5a4-86a0779f5410\",\"selected\":false,\"position\":{\"x\":580.5,\"y\":1335},\"data\":{\"category\":\"option\",\"formValues\":{\"duration\":\"45\",\"unit\":\"second\"}},\"type\":\"timeout\",\"positionAbsolute\":{\"x\":580.5,\"y\":1335}},{\"width\":300,\"height\":242,\"id\":\"d438be3d-bf8d-4fbc-8568-ca98aecd3b3a\",\"selected\":false,\"position\":{\"x\":-965.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 2\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"Done assignment (assignment is not gonna happen, this is just for testing branching)\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-965.5,\"y\":1499}},{\"width\":300,\"height\":202,\"id\":\"5bc611f0-ae94-42b4-9e6b-4610c37e8622\",\"selected\":false,\"position\":{\"x\":-578.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 3\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"As you wish. Bye~\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-578.5,\"y\":1499}},{\"width\":300,\"height\":202,\"id\":\"4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2\",\"selected\":false,\"position\":{\"x\":-191.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 4\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"What did you say?\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-191.5,\"y\":1499}},{\"width\":300,\"height\":282,\"id\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"selected\":false,\"position\":{\"x\":580.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 5\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"No reply after 45 seconds\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"QUICK_REPLY\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[{\"defaultTitle\":\"Title 1\",\"title\":\"Again\",\"nodeId\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"description\":\"\",\"readOnly\":false},{\"defaultTitle\":\"Quick reply 3\",\"title\":\"End\",\"nodeId\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"description\":\"\",\"readOnly\":false}],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":true,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":580.5,\"y\":1499}},{\"width\":300,\"height\":116,\"id\":\"239c0a45-163d-4653-ae01-3eaacab5d475\",\"selected\":false,\"position\":{\"x\":-965.5,\"y\":1861},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-965.5,\"y\":1861}},{\"width\":300,\"height\":116,\"id\":\"1f2e3c64-8f01-42c1-8d6a-d02510a34cac\",\"position\":{\"x\":-578.5,\"y\":1821},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-578.5,\"y\":1821}},{\"width\":300,\"height\":116,\"id\":\"b42c9deb-dd3f-40c6-8fa8-f04b978e04f4\",\"position\":{\"x\":-191.5,\"y\":1821},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-191.5,\"y\":1821}},{\"width\":300,\"height\":44,\"id\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"position\":{\"x\":195.5,\"y\":1901},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 1\",\"title\":\"Again\",\"nodeId\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":195.5,\"y\":1901}},{\"width\":300,\"height\":44,\"id\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"position\":{\"x\":580.5,\"y\":1901},\"data\":{\"formValues\":{\"defaultTitle\":\"Quick reply 3\",\"title\":\"End\",\"nodeId\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":580.5,\"y\":1901}},{\"width\":300,\"height\":44,\"id\":\"244c206d-0df5-4a2b-aa25-dc60555f81e5\",\"position\":{\"x\":965.5,\"y\":1901},\"data\":{\"formValues\":{\"title\":\"Other answers\"},\"category\":\"option\"},\"type\":\"elseAnswer\",\"positionAbsolute\":{\"x\":965.5,\"y\":1901}},{\"width\":300,\"height\":128,\"id\":\"96499ba3-22a4-4506-837b-66e101f6231f\",\"selected\":false,\"position\":{\"x\":195.5,\"y\":2065},\"data\":{\"formValues\":{\"endType\":\"jumpTo\",\"targetNode\":{\"id\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"title\":\"Action 1\",\"category\":\"action\"}},\"category\":\"end\",\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"end\",\"positionAbsolute\":{\"x\":195.5,\"y\":2065}},{\"width\":300,\"height\":116,\"id\":\"ec3dfaef-beb4-4194-9373-6711795fc08b\",\"selected\":false,\"position\":{\"x\":580.5,\"y\":2065},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":580.5,\"y\":2065}},{\"width\":300,\"height\":116,\"id\":\"da6181fc-2d1d-4985-bd0c-3f526c62455b\",\"position\":{\"x\":965.5,\"y\":2065},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":965.5,\"y\":2065}}],\"edges\":[{\"type\":\"buttonEdge\",\"id\":\"addcb0a9-c191-4f7c-b15b-a470a88aead9\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"1d45cfd7-12c6-44c3-b168-607c2968b305\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"952a6b14-f9fb-4075-899f-ae55e2cdc8dc\"},{\"type\":\"buttonEdge\",\"id\":\"5a453df0-1567-45bb-aa9b-f282b9549862\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"7b833e0a-7c81-46c2-a5a4-86a0779f5410\"},{\"type\":\"buttonEdge\",\"id\":\"ac0c4b5f-be13-498e-b004-e9304d44a322\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\"},{\"type\":\"buttonEdge\",\"id\":\"2573cd04-4821-4244-bc5b-b063b8789539\",\"source\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"target\":\"d438be3d-bf8d-4fbc-8568-ca98aecd3b3a\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"90a3fb25-36c2-48b4-b232-0dede4e96b38\",\"source\":\"d438be3d-bf8d-4fbc-8568-ca98aecd3b3a\",\"target\":\"239c0a45-163d-4653-ae01-3eaacab5d475\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"cb2f506d-d5ed-4d9a-aaa4-bb9ed2f981d6\",\"source\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"target\":\"5bc611f0-ae94-42b4-9e6b-4610c37e8622\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"c0550740-4c22-491e-9646-92f9960e1c5b\",\"source\":\"5bc611f0-ae94-42b4-9e6b-4610c37e8622\",\"target\":\"1f2e3c64-8f01-42c1-8d6a-d02510a34cac\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"c3989241-4f6b-4aed-bd29-14e3c3f96559\",\"source\":\"952a6b14-f9fb-4075-899f-ae55e2cdc8dc\",\"target\":\"4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"9624002a-916d-48af-8a9a-7d70795dca12\",\"source\":\"4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2\",\"target\":\"b42c9deb-dd3f-40c6-8fa8-f04b978e04f4\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"ceba1db9-5356-4273-ab43-aea8ebd6ad8e\",\"source\":\"7b833e0a-7c81-46c2-a5a4-86a0779f5410\",\"target\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"37c3d5d0-e5a3-4961-b61d-98049e9bd6ea\",\"source\":\"61b28ce1-4d15-4af0-b3e8-41cb88d18469\",\"target\":\"aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"d5bcecfa-97b6-4acf-af5f-77622987d759\",\"source\":\"aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb\",\"target\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"ca4c76a2-68eb-4543-b672-c916b40777b0\",\"source\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"target\":\"6293f764-63c6-415f-a173-1b6ea491fe94\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"ceea8217-b86a-40d0-b479-d199aeb2a80c\",\"source\":\"6293f764-63c6-415f-a173-1b6ea491fe94\",\"target\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"4b7974b0-18ff-4053-88c6-71b8224a1a1a\",\"source\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"target\":\"8e7786ce-35a4-48b7-8722-e699abafba75\"},{\"type\":\"buttonEdge\",\"id\":\"4f309f47-eb07-408a-a816-c9c852dfb640\",\"source\":\"8e7786ce-35a4-48b7-8722-e699abafba75\",\"target\":\"fadf0d7a-6b77-41da-8225-adc6d8b40ac3\"},{\"type\":\"buttonEdge\",\"id\":\"280fa284-e294-4688-bc09-9bc93a8a0f1a\",\"source\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"target\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"737c22e4-a15d-4393-86ba-69fbb0d4eeb5\",\"source\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"target\":\"96499ba3-22a4-4506-837b-66e101f6231f\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"3084cf94-57dc-456a-af6f-432869cbfaba\",\"source\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"target\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\"},{\"type\":\"buttonEdge\",\"id\":\"a684d83b-0224-4515-9679-f91367aca677\",\"source\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"target\":\"ec3dfaef-beb4-4194-9373-6711795fc08b\"},{\"type\":\"buttonEdge\",\"id\":\"9cbb097e-c253-479b-95e4-150cf01b15c6\",\"source\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"target\":\"244c206d-0df5-4a2b-aa25-dc60555f81e5\"},{\"type\":\"buttonEdge\",\"id\":\"4d0580b3-1cec-4589-836c-28bbe2e03325\",\"source\":\"244c206d-0df5-4a2b-aa25-dc60555f81e5\",\"target\":\"da6181fc-2d1d-4985-bd0c-3f526c62455b\"}]}"
                },
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-01-19T04:32:29.115+00:00",
                "updated_at": "2024-01-19T04:32:31.685+00:00",
                "id": "AmKUmnDlq5YkMJx-Np4cR2R6mplWqdz2"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData);

        var expected = new Dictionary<string, string?>()
        {
            ["aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb"] = "aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb",
            ["d1af52c2-842e-45da-b574-7e8977f540f5"] = "d1af52c2-842e-45da-b574-7e8977f540f5",
            ["39947d70-117d-4307-97ae-6754a9059b14"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["527ac07d-c6a7-4589-bb02-d95e3b16dfc6"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["a4a31ca7-5253-4960-a49f-e5bed0df6617"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["7b833e0a-7c81-46c2-a5a4-86a0779f5410"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["50447763-defd-4be6-a110-fe03fb54cd99"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["d438be3d-bf8d-4fbc-8568-ca98aecd3b3a"] = "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a",
            ["5bc611f0-ae94-42b4-9e6b-4610c37e8622"] = "5bc611f0-ae94-42b4-9e6b-4610c37e8622",
            ["4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2"] = "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2",
            ["79eb6ad9-b0b8-4406-b8aa-edf393ad771e"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            ["bdb524fe-4561-4a99-916e-45369c03005e"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            ["5ab610d5-7d61-49f3-a637-6272348b4cd9"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            ["da3cb93b-8d1e-4fab-a5ee-84ed1193a7ef"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            [string.Empty] = null
        };

        // Act
        foreach (var stepId in expected.Keys)
        {
            var stepNodeId = _workflowStepNodeLocator.GetStepNodeId(
                workflow,
                stepId);

            // Assert
            Assert.That(stepNodeId, Is.EqualTo(expected[stepId]));
        }
    }

    [Test]
    public void GetStepNodeId_GivenWorkflowIsNull_ShouldReturnNull()
    {
        // Arrange
        ProxyWorkflow? workflow = null;

        // Act
        var stepNodeId = _workflowStepNodeLocator.GetStepNodeId(
            workflow,
            Guid.NewGuid().ToString());

        Assert.That(stepNodeId, Is.Null);
    }

    [Test]
    public void GetStepNodeIds_GivenValidWorkflow_ShouldReturnTheCorrectStepNodeIds()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "AmKUmnDlq5YkMJx",
                "workflow_versioned_id": "AmKUmnDlq5YkMJx-Np4cR2R6mplWqdz2",
                "name": "DEVS-3885 List Message Reply",
                "triggers": {
                    "message_received": {
                        "condition": "{{ ([\"***********\"] | array.contains event_body.channel_id) && ((((event_body.message.message_content | regex.replace \"\\\\*[^*]+\\\\*\\\\n\" '' | string.strip ) == \"DEVS-3885\")))  }}"
                    }
                },
                "steps": [
                    {
                        "id": "setup-contact-and-conversation",
                        "step_node_id": null,
                        "name": "setup",
                        "assign": {
                            "contact": "{{ trigger_event_body.contact_id | sleekflow.get_contact }}",
                            "lists": "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}",
                            "conversation": "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        "next_step_id": null
                    },
                    {
                        "id": "setup-contact-owner",
                        "step_node_id": null,
                        "name": "setup-contact-owner",
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "next_step_id": null
                    },
                    {
                        "call": "sys.sleep",
                        "args": {
                            "seconds__expr": "5"
                        },
                        "id": "aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb",
                        "step_node_id": "aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb",
                        "name": "Time delay 1",
                        "assign": null,
                        "next_step_id": "d1af52c2-842e-45da-b574-7e8977f540f5"
                    },
                    {
                        "switch": [
                            {
                                "condition": "{{ (usr_var_dict.contact[\"1824e844-348d-48f9-9746-4945dc380fff\"] | string.contains \"189736147\") }}",
                                "next_step_id": "39947d70-117d-4307-97ae-6754a9059b14"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                            }
                        ],
                        "id": "d1af52c2-842e-45da-b574-7e8977f540f5",
                        "step_node_id": "d1af52c2-842e-45da-b574-7e8977f540f5",
                        "name": "condition-branching",
                        "assign": null,
                        "next_step_id": null
                    },
                    {
                        "parallel_branches": [
                            {
                                "step": {
                                    "try": {
                                        "step": {
                                            "call": "sys.wait-for-event",
                                            "args": {
                                                "event_name": "OnMessageReceived",
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "timeout_seconds__expr": "45"
                                            },
                                            "id": "527ac07d-c6a7-4589-bb02-d95e3b16dfc6",
                                            "step_node_id": null,
                                            "name": "wait-for-message",
                                            "assign": {
                                                "message_527ac07d-c6a7-4589-bb02-d95e3b16dfc6": "{{ event_body.message.message_content }}",
                                                "error_527ac07d-c6a7-4589-bb02-d95e3b16dfc6": "{{ null }}"
                                            },
                                            "next_step_id": null
                                        }
                                    },
                                    "catch": {
                                        "as": "e",
                                        "step": {
                                            "id": "7b833e0a-7c81-46c2-a5a4-86a0779f5410",
                                            "step_node_id": null,
                                            "name": "timeout",
                                            "assign": {
                                                "error_527ac07d-c6a7-4589-bb02-d95e3b16dfc6": "{{ sys_var_dict.e.error_message }}"
                                            },
                                            "next_step_id": null
                                        }
                                    },
                                    "id": "a4a31ca7-5253-4960-a49f-e5bed0df6617",
                                    "step_node_id": null,
                                    "name": "try-catch-timeout",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            },
                            {
                                "step": {
                                    "call": "sleekflow.v1.send-message",
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_phone_number__expr": null,
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "to_facebook_comment_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "to_instagram_comment_id__expr": null
                                        },
                                        "message_type__expr": "interactive",
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"list\",\"body\":{\"text\":\"How do you wish to proceed?\",\"type\":\"text\"},\"action\":{\"button\":\"Options\",\"sections\":[{\"title\":\"\",\"rows\":[{\"id\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"title\":\"Option 1\",\"description\":\"Assign me to random people\"},{\"id\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"title\":\"Option 2\",\"description\":\"End flow\"}]}]}}} }}"
                                    },
                                    "id": "50447763-defd-4be6-a110-fe03fb54cd99",
                                    "step_node_id": null,
                                    "name": "Action 1",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            }
                        ],
                        "id": "39947d70-117d-4307-97ae-6754a9059b14",
                        "step_node_id": "39947d70-117d-4307-97ae-6754a9059b14",
                        "name": "send-message-and-wait-for-event",
                        "assign": null,
                        "next_step_id": "3abcc08a-77a9-4370-a120-4709405ea74c"
                    },
                    {
                        "switch": [
                            {
                                "condition": "{{ usr_var_dict[\"error_527ac07d-c6a7-4589-bb02-d95e3b16dfc6\"] != null }}",
                                "next_step_id": "79eb6ad9-b0b8-4406-b8aa-edf393ad771e"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_527ac07d-c6a7-4589-bb02-d95e3b16dfc6\"] ?? \" \" | string.strip) == (\"Option 1\" | string.strip) }}",
                                "next_step_id": "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_527ac07d-c6a7-4589-bb02-d95e3b16dfc6\"] ?? \" \" | string.strip) == (\"Option 2\" | string.strip) }}",
                                "next_step_id": "5bc611f0-ae94-42b4-9e6b-4610c37e8622"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2"
                            }
                        ],
                        "id": "3abcc08a-77a9-4370-a120-4709405ea74c",
                        "step_node_id": null,
                        "name": "timeout-with-answers-switch",
                        "assign": null,
                        "next_step_id": null
                    },
                    {
                        "call": "sleekflow.v1.send-message",
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_phone_number__expr": null,
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "to_facebook_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "to_facebook_comment_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "to_instagram_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "to_instagram_comment_id__expr": null
                            },
                            "message_type__expr": "text",
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Done assignment (assignment is not gonna happen, this is just for testing branching)\"}} }}"
                        },
                        "id": "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a",
                        "step_node_id": "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a",
                        "name": "Action 2",
                        "assign": null,
                        "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                    },
                    {
                        "call": "sleekflow.v1.send-message",
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_phone_number__expr": null,
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "to_facebook_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "to_facebook_comment_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "to_instagram_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "to_instagram_comment_id__expr": null
                            },
                            "message_type__expr": "text",
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"As you wish. Bye~\"}} }}"
                        },
                        "id": "5bc611f0-ae94-42b4-9e6b-4610c37e8622",
                        "step_node_id": "5bc611f0-ae94-42b4-9e6b-4610c37e8622",
                        "name": "Action 3",
                        "assign": null,
                        "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                    },
                    {
                        "call": "sleekflow.v1.send-message",
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_phone_number__expr": null,
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "to_facebook_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "to_facebook_comment_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "to_instagram_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "to_instagram_comment_id__expr": null
                            },
                            "message_type__expr": "text",
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"What did you say?\"}} }}"
                        },
                        "id": "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2",
                        "step_node_id": "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2",
                        "name": "Action 4",
                        "assign": null,
                        "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                    },
                    {
                        "parallel_branches": [
                            {
                                "step": {
                                    "substeps": [
                                        {
                                            "call": "sys.wait-for-event",
                                            "args": {
                                                "event_name": "OnMessageReceived",
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "timeout_seconds__expr": null
                                            },
                                            "id": "5ab610d5-7d61-49f3-a637-6272348b4cd9",
                                            "step_node_id": null,
                                            "name": "wait-for-on-message-received",
                                            "assign": {
                                                "message_5ab610d5-7d61-49f3-a637-6272348b4cd9": "{{ event_body.message.message_content }}"
                                            },
                                            "next_step_id": null
                                        }
                                    ],
                                    "id": "bdb524fe-4561-4a99-916e-45369c03005e",
                                    "step_node_id": null,
                                    "name": "wait-for-message",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            },
                            {
                                "step": {
                                    "call": "sleekflow.v1.send-message",
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_phone_number__expr": null,
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "to_facebook_comment_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "to_instagram_comment_id__expr": null
                                        },
                                        "message_type__expr": "interactive",
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"No reply after 45 seconds\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"title\":\"Again\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"title\":\"End\"}}]}}} }}"
                                    },
                                    "id": "da3cb93b-8d1e-4fab-a5ee-84ed1193a7ef",
                                    "step_node_id": null,
                                    "name": "Action 5",
                                    "assign": null,
                                    "next_step_id": null
                                }
                            }
                        ],
                        "id": "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
                        "step_node_id": "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
                        "name": "send-message-and-wait-for-event",
                        "assign": null,
                        "next_step_id": "48eb342d-3f17-42c0-981b-e58da348dd25"
                    },
                    {
                        "switch": [
                            {
                                "condition": "{{ (usr_var_dict[\"message_5ab610d5-7d61-49f3-a637-6272348b4cd9\"] ?? \" \" | string.strip) == (\"Again\" | string.strip) }}",
                                "next_step_id": "96499ba3-22a4-4506-837b-66e101f6231f"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_5ab610d5-7d61-49f3-a637-6272348b4cd9\"] ?? \" \" | string.strip) == (\"End\" | string.strip) }}",
                                "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "6301a06a-383e-4aee-acef-1a6a645d5e74"
                            }
                        ],
                        "id": "48eb342d-3f17-42c0-981b-e58da348dd25",
                        "step_node_id": null,
                        "name": "answer-switch",
                        "assign": null,
                        "next_step_id": null
                    },
                    {
                        "id": "96499ba3-22a4-4506-837b-66e101f6231f",
                        "step_node_id": null,
                        "name": "jumpTo_39947d70-117d-4307-97ae-6754a9059b14",
                        "assign": null,
                        "next_step_id": "39947d70-117d-4307-97ae-6754a9059b14"
                    },
                    {
                        "id": "6301a06a-383e-4aee-acef-1a6a645d5e74",
                        "step_node_id": null,
                        "name": "end",
                        "assign": null,
                        "next_step_id": null
                    }
                ],
                "activation_status": "Active",
                "metadata": {
                    "v2": "{\"nodes\":[{\"width\":300,\"height\":216,\"id\":\"61b28ce1-4d15-4af0-b3e8-41cb88d18469\",\"selected\":false,\"position\":{\"x\":0,\"y\":0},\"data\":{\"category\":\"start\",\"formValues\":{\"triggerType\":\"incoming-messages\",\"channels\":[{\"id\":\"***********\",\"subId\":154,\"name\":\"Flow Builder Test UAT\",\"channel\":\"whatsappcloudapi\",\"messagingHubWabaId\":\"amUx8pKxvn051X\",\"facebookWabaBusinessId\":\"3305169899744267\"}],\"conversationStatus\":\"\",\"saveWebhookDataAsVariables\":[],\"identifierType\":\"\",\"identifierExpression\":\"\",\"enableAutoCreateContact\":false,\"contactProperties\":[],\"addedLists\":[],\"removedLists\":[],\"isFilterByCondition\":true,\"conditions\":[{\"conditionType\":\"keyword\",\"operator\":\"containsExactly\",\"keywords\":[{\"value\":\"DEVS-3885\"}]}],\"title\":\" \"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"start\",\"positionAbsolute\":{\"x\":0,\"y\":0}},{\"width\":300,\"height\":96,\"id\":\"aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb\",\"selected\":false,\"position\":{\"x\":0,\"y\":336},\"data\":{\"category\":\"timeDelay\",\"formValues\":{\"title\":\"Time delay 1\",\"type\":\"elapsed\",\"timeUnit\":\"second\",\"timeDuration\":\"5\",\"dateTime\":null},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"timeDelay\",\"positionAbsolute\":{\"x\":0,\"y\":336}},{\"width\":300,\"height\":36,\"id\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"position\":{\"x\":0,\"y\":552},\"data\":{\"category\":\"condition\",\"formValues\":{\"title\":\"branching\"}},\"type\":\"branching\",\"positionAbsolute\":{\"x\":0,\"y\":552}},{\"width\":300,\"height\":148,\"id\":\"6293f764-63c6-415f-a173-1b6ea491fe94\",\"selected\":false,\"position\":{\"x\":-192.5,\"y\":708},\"data\":{\"category\":\"condition\",\"formValues\":{\"title\":\"Condition 1\",\"conditions\":[{\"conditionType\":\"contactProperty\",\"contactProperty\":{\"id\":\"1824e844-348d-48f9-9746-4945dc380fff\",\"label\":\"Phone Number\",\"type\":\"PhoneNumber\"},\"operator\":\"contains\",\"value\":\"189736147\",\"keywords\":[]}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"condition\",\"positionAbsolute\":{\"x\":-192.5,\"y\":708}},{\"width\":300,\"height\":96,\"id\":\"8e7786ce-35a4-48b7-8722-e699abafba75\",\"position\":{\"x\":192.5,\"y\":708},\"data\":{\"category\":\"condition\",\"formValues\":{\"title\":\"else\"}},\"type\":\"elseCondition\",\"positionAbsolute\":{\"x\":192.5,\"y\":708}},{\"width\":300,\"height\":239,\"id\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"selected\":false,\"position\":{\"x\":-192.5,\"y\":976},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 1\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"How do you wish to proceed?\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"LIST_MESSAGE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"Options\",\"sectionList\":[{\"sectionTitle\":\"\",\"options\":[{\"defaultTitle\":\"Title 1\",\"title\":\"Option 1\",\"nodeId\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"description\":\"Assign me to random people\",\"readOnly\":false},{\"defaultTitle\":\"Title 2\",\"title\":\"Option 2\",\"nodeId\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"description\":\"End flow\"}]}],\"isButtonAsBranch\":true,\"isSaveReply\":false,\"isMessageTimeout\":true,\"timeoutDuration\":\"45\",\"timeoutUnit\":\"second\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-192.5,\"y\":976}},{\"width\":300,\"height\":116,\"id\":\"fadf0d7a-6b77-41da-8225-adc6d8b40ac3\",\"position\":{\"x\":192.5,\"y\":924},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":192.5,\"y\":924}},{\"width\":300,\"height\":44,\"id\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"selected\":false,\"position\":{\"x\":-965.5,\"y\":1335},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 1\",\"title\":\"Option 1\",\"nodeId\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"description\":\"Assign me to random people\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"listMessage\",\"positionAbsolute\":{\"x\":-965.5,\"y\":1335}},{\"width\":300,\"height\":44,\"id\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"position\":{\"x\":-578.5,\"y\":1335},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 2\",\"title\":\"Option 2\",\"nodeId\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"description\":\"End flow\"},\"category\":\"option\"},\"type\":\"listMessage\",\"positionAbsolute\":{\"x\":-578.5,\"y\":1335}},{\"width\":300,\"height\":44,\"id\":\"952a6b14-f9fb-4075-899f-ae55e2cdc8dc\",\"position\":{\"x\":-191.5,\"y\":1335},\"data\":{\"formValues\":{\"title\":\"Other answers\"},\"category\":\"option\"},\"type\":\"elseAnswer\",\"positionAbsolute\":{\"x\":-191.5,\"y\":1335}},{\"width\":300,\"height\":44,\"id\":\"7b833e0a-7c81-46c2-a5a4-86a0779f5410\",\"selected\":false,\"position\":{\"x\":580.5,\"y\":1335},\"data\":{\"category\":\"option\",\"formValues\":{\"duration\":\"45\",\"unit\":\"second\"}},\"type\":\"timeout\",\"positionAbsolute\":{\"x\":580.5,\"y\":1335}},{\"width\":300,\"height\":242,\"id\":\"d438be3d-bf8d-4fbc-8568-ca98aecd3b3a\",\"selected\":false,\"position\":{\"x\":-965.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 2\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"Done assignment (assignment is not gonna happen, this is just for testing branching)\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-965.5,\"y\":1499}},{\"width\":300,\"height\":202,\"id\":\"5bc611f0-ae94-42b4-9e6b-4610c37e8622\",\"selected\":false,\"position\":{\"x\":-578.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 3\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"As you wish. Bye~\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-578.5,\"y\":1499}},{\"width\":300,\"height\":202,\"id\":\"4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2\",\"selected\":false,\"position\":{\"x\":-191.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 4\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"What did you say?\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":-191.5,\"y\":1499}},{\"width\":300,\"height\":282,\"id\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"selected\":false,\"position\":{\"x\":580.5,\"y\":1499},\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 5\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"No reply after 45 seconds\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"QUICK_REPLY\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[{\"defaultTitle\":\"Title 1\",\"title\":\"Again\",\"nodeId\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"description\":\"\",\"readOnly\":false},{\"defaultTitle\":\"Quick reply 3\",\"title\":\"End\",\"nodeId\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"description\":\"\",\"readOnly\":false}],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":true,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"sendMessage\",\"positionAbsolute\":{\"x\":580.5,\"y\":1499}},{\"width\":300,\"height\":116,\"id\":\"239c0a45-163d-4653-ae01-3eaacab5d475\",\"selected\":false,\"position\":{\"x\":-965.5,\"y\":1861},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-965.5,\"y\":1861}},{\"width\":300,\"height\":116,\"id\":\"1f2e3c64-8f01-42c1-8d6a-d02510a34cac\",\"position\":{\"x\":-578.5,\"y\":1821},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-578.5,\"y\":1821}},{\"width\":300,\"height\":116,\"id\":\"b42c9deb-dd3f-40c6-8fa8-f04b978e04f4\",\"position\":{\"x\":-191.5,\"y\":1821},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":-191.5,\"y\":1821}},{\"width\":300,\"height\":44,\"id\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"position\":{\"x\":195.5,\"y\":1901},\"data\":{\"formValues\":{\"defaultTitle\":\"Title 1\",\"title\":\"Again\",\"nodeId\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":195.5,\"y\":1901}},{\"width\":300,\"height\":44,\"id\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"position\":{\"x\":580.5,\"y\":1901},\"data\":{\"formValues\":{\"defaultTitle\":\"Quick reply 3\",\"title\":\"End\",\"nodeId\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"description\":\"\",\"readOnly\":false},\"category\":\"option\"},\"type\":\"quickReply\",\"positionAbsolute\":{\"x\":580.5,\"y\":1901}},{\"width\":300,\"height\":44,\"id\":\"244c206d-0df5-4a2b-aa25-dc60555f81e5\",\"position\":{\"x\":965.5,\"y\":1901},\"data\":{\"formValues\":{\"title\":\"Other answers\"},\"category\":\"option\"},\"type\":\"elseAnswer\",\"positionAbsolute\":{\"x\":965.5,\"y\":1901}},{\"width\":300,\"height\":128,\"id\":\"96499ba3-22a4-4506-837b-66e101f6231f\",\"selected\":false,\"position\":{\"x\":195.5,\"y\":2065},\"data\":{\"formValues\":{\"endType\":\"jumpTo\",\"targetNode\":{\"id\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"title\":\"Action 1\",\"category\":\"action\"}},\"category\":\"end\",\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"end\",\"positionAbsolute\":{\"x\":195.5,\"y\":2065}},{\"width\":300,\"height\":116,\"id\":\"ec3dfaef-beb4-4194-9373-6711795fc08b\",\"selected\":false,\"position\":{\"x\":580.5,\"y\":2065},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":580.5,\"y\":2065}},{\"width\":300,\"height\":116,\"id\":\"da6181fc-2d1d-4985-bd0c-3f526c62455b\",\"position\":{\"x\":965.5,\"y\":2065},\"data\":{\"formValues\":{\"endType\":\"here\",\"targetNode\":null},\"category\":\"end\"},\"type\":\"end\",\"positionAbsolute\":{\"x\":965.5,\"y\":2065}}],\"edges\":[{\"type\":\"buttonEdge\",\"id\":\"addcb0a9-c191-4f7c-b15b-a470a88aead9\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"1d45cfd7-12c6-44c3-b168-607c2968b305\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"952a6b14-f9fb-4075-899f-ae55e2cdc8dc\"},{\"type\":\"buttonEdge\",\"id\":\"5a453df0-1567-45bb-aa9b-f282b9549862\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"7b833e0a-7c81-46c2-a5a4-86a0779f5410\"},{\"type\":\"buttonEdge\",\"id\":\"ac0c4b5f-be13-498e-b004-e9304d44a322\",\"source\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"target\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\"},{\"type\":\"buttonEdge\",\"id\":\"2573cd04-4821-4244-bc5b-b063b8789539\",\"source\":\"6f7571bb-951d-482f-b293-6aded5bc0b16\",\"target\":\"d438be3d-bf8d-4fbc-8568-ca98aecd3b3a\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"90a3fb25-36c2-48b4-b232-0dede4e96b38\",\"source\":\"d438be3d-bf8d-4fbc-8568-ca98aecd3b3a\",\"target\":\"239c0a45-163d-4653-ae01-3eaacab5d475\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"cb2f506d-d5ed-4d9a-aaa4-bb9ed2f981d6\",\"source\":\"1d3bf17a-723b-438e-a9a3-0f249c5c2591\",\"target\":\"5bc611f0-ae94-42b4-9e6b-4610c37e8622\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"c0550740-4c22-491e-9646-92f9960e1c5b\",\"source\":\"5bc611f0-ae94-42b4-9e6b-4610c37e8622\",\"target\":\"1f2e3c64-8f01-42c1-8d6a-d02510a34cac\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"c3989241-4f6b-4aed-bd29-14e3c3f96559\",\"source\":\"952a6b14-f9fb-4075-899f-ae55e2cdc8dc\",\"target\":\"4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"9624002a-916d-48af-8a9a-7d70795dca12\",\"source\":\"4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2\",\"target\":\"b42c9deb-dd3f-40c6-8fa8-f04b978e04f4\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"ceba1db9-5356-4273-ab43-aea8ebd6ad8e\",\"source\":\"7b833e0a-7c81-46c2-a5a4-86a0779f5410\",\"target\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"37c3d5d0-e5a3-4961-b61d-98049e9bd6ea\",\"source\":\"61b28ce1-4d15-4af0-b3e8-41cb88d18469\",\"target\":\"aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"d5bcecfa-97b6-4acf-af5f-77622987d759\",\"source\":\"aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb\",\"target\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"ca4c76a2-68eb-4543-b672-c916b40777b0\",\"source\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"target\":\"6293f764-63c6-415f-a173-1b6ea491fe94\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"ceea8217-b86a-40d0-b479-d199aeb2a80c\",\"source\":\"6293f764-63c6-415f-a173-1b6ea491fe94\",\"target\":\"39947d70-117d-4307-97ae-6754a9059b14\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"4b7974b0-18ff-4053-88c6-71b8224a1a1a\",\"source\":\"d1af52c2-842e-45da-b574-7e8977f540f5\",\"target\":\"8e7786ce-35a4-48b7-8722-e699abafba75\"},{\"type\":\"buttonEdge\",\"id\":\"4f309f47-eb07-408a-a816-c9c852dfb640\",\"source\":\"8e7786ce-35a4-48b7-8722-e699abafba75\",\"target\":\"fadf0d7a-6b77-41da-8225-adc6d8b40ac3\"},{\"type\":\"buttonEdge\",\"id\":\"280fa284-e294-4688-bc09-9bc93a8a0f1a\",\"source\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"target\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"737c22e4-a15d-4393-86ba-69fbb0d4eeb5\",\"source\":\"6ac43e7c-d159-4690-b0f1-cc577e490eb4\",\"target\":\"96499ba3-22a4-4506-837b-66e101f6231f\",\"edgeType\":\"buttonType\"},{\"type\":\"buttonEdge\",\"id\":\"3084cf94-57dc-456a-af6f-432869cbfaba\",\"source\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"target\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\"},{\"type\":\"buttonEdge\",\"id\":\"a684d83b-0224-4515-9679-f91367aca677\",\"source\":\"5242b8b2-c69c-4ec6-8798-91660d6cf61e\",\"target\":\"ec3dfaef-beb4-4194-9373-6711795fc08b\"},{\"type\":\"buttonEdge\",\"id\":\"9cbb097e-c253-479b-95e4-150cf01b15c6\",\"source\":\"79eb6ad9-b0b8-4406-b8aa-edf393ad771e\",\"target\":\"244c206d-0df5-4a2b-aa25-dc60555f81e5\"},{\"type\":\"buttonEdge\",\"id\":\"4d0580b3-1cec-4589-836c-28bbe2e03325\",\"source\":\"244c206d-0df5-4a2b-aa25-dc60555f81e5\",\"target\":\"da6181fc-2d1d-4985-bd0c-3f526c62455b\"}]}"
                },
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-01-19T04:32:29.115+00:00",
                "updated_at": "2024-01-19T04:32:31.685+00:00",
                "id": "AmKUmnDlq5YkMJx-Np4cR2R6mplWqdz2"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;

        var expected = new Dictionary<string, string>()
        {
            ["aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb"] = "aed69586-a5a2-4a8f-9a69-cd1d6c6a58cb",
            ["d1af52c2-842e-45da-b574-7e8977f540f5"] = "d1af52c2-842e-45da-b574-7e8977f540f5",
            ["39947d70-117d-4307-97ae-6754a9059b14"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["527ac07d-c6a7-4589-bb02-d95e3b16dfc6"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["a4a31ca7-5253-4960-a49f-e5bed0df6617"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["7b833e0a-7c81-46c2-a5a4-86a0779f5410"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["50447763-defd-4be6-a110-fe03fb54cd99"] = "39947d70-117d-4307-97ae-6754a9059b14",
            ["d438be3d-bf8d-4fbc-8568-ca98aecd3b3a"] = "d438be3d-bf8d-4fbc-8568-ca98aecd3b3a",
            ["5bc611f0-ae94-42b4-9e6b-4610c37e8622"] = "5bc611f0-ae94-42b4-9e6b-4610c37e8622",
            ["4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2"] = "4ceb2a3f-9dd3-4157-824c-b1ca854d3ac2",
            ["79eb6ad9-b0b8-4406-b8aa-edf393ad771e"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            ["bdb524fe-4561-4a99-916e-45369c03005e"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            ["5ab610d5-7d61-49f3-a637-6272348b4cd9"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
            ["da3cb93b-8d1e-4fab-a5ee-84ed1193a7ef"] = "79eb6ad9-b0b8-4406-b8aa-edf393ad771e",
        };

        // Act
        var results = _workflowStepNodeLocator.GetStepNodeIds(
            workflow.Steps,
            workflow.Metadata);

        // Assert
        CollectionAssert.AreEquivalent(results, expected);
    }

    [Test]
    public void GetStepNodeIds_GivenWorkflowIsNull_ShouldReturnEmptyDictionary()
    {
        // Act
        var stepNodeIds = _workflowStepNodeLocator.GetStepNodeIds(
            null,
            null);

        // Assert
        Assert.That(stepNodeIds, Has.Count.EqualTo(0));
    }

    [Test]
    public void GetStepNodeIds_GivenWorkflowMetadataIsNull_ShouldReturnEmptyDictionary()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "7glUk05r4XAGyyy",
                "workflow_versioned_id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z",
                "name": "Problematic flow",
                "triggers": {
                    "contact_updated": {
                        "condition": "{{ ((event_body.post_updated_contact[\"9f5ea004-867c-40de-a22b-1e470b6c1897\"] == \"false\")) }}"
                    }
                },
                "steps": [
                    {
                        "assign": {
                            "contact": "{{ trigger_event_body.contact_id | sleekflow.get_contact }}",
                            "conversation": "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}",
                            "lists": "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        },
                        "id": "setup-contact-and-conversation",
                        "name": "setup",
                        "next_step_id": null
                    },
                    {
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "id": "setup-contact-owner",
                        "name": "setup-contact-owner",
                        "next_step_id": null
                    },
                    {
                        "assign": null,
                        "id": "cb6dead9-58dd-4020-9900-5a9ca281dcaa",
                        "name": "send-message-and-wait-for-event",
                        "next_step_id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "parallel_branches": [
                            {
                                "step": {
                                    "assign": null,
                                    "catch": {
                                        "as": "e",
                                        "step": {
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ sys_var_dict.e.error_message }}"
                                            },
                                            "id": "141482c2-38e9-4de2-9e41-7f48fb4900ca",
                                            "name": "timeout",
                                            "next_step_id": null
                                        }
                                    },
                                    "id": "daebfeb5-80f3-471c-be98-dd6f9df166c6",
                                    "name": "try-catch-timeout",
                                    "next_step_id": null,
                                    "try": {
                                        "step": {
                                            "args": {
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "event_name": "OnMessageReceived",
                                                "timeout_seconds__expr": "300"
                                            },
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ null }}",
                                                "message_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ event_body.message.message_content }}"
                                            },
                                            "call": "sys.wait-for-event",
                                            "id": "43c2c09b-1f12-4737-8494-82b4ed9ac058",
                                            "name": "wait-for-message",
                                            "next_step_id": null
                                        }
                                    }
                                }
                            },
                            {
                                "step": {
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "to_facebook_comment_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "to_instagram_comment_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "to_phone_number__expr": null
                                        },
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"Pick something\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"ab4481a0-c693-43d6-b89a-47bc5034bf5b\",\"title\":\"Something  1\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"bae4586e-474c-4b54-9860-8ad01cfeafb6\",\"title\":\"Something 2\"}}]}}} }}",
                                        "message_type__expr": "interactive"
                                    },
                                    "assign": null,
                                    "call": "sleekflow.v1.send-message",
                                    "id": "5dabd486-5870-4c88-b478-6a02f5fc0b94",
                                    "name": "Action 1",
                                    "next_step_id": null
                                }
                            }
                        ]
                    },
                    {
                        "assign": null,
                        "id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "name": "timeout-with-answers-switch",
                        "next_step_id": null,
                        "switch": [
                            {
                                "condition": "{{ usr_var_dict[\"error_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] != null }}",
                                "next_step_id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something  1\" | string.strip) }}",
                                "next_step_id": "0dcc85cd-0845-41d3-a44b-cd1b448318af"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something 2\" | string.strip) }}",
                                "next_step_id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "1c03b5c8-bba2-465f-8819-278c7081d4ac"
                            }
                        ]
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 1\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "0dcc85cd-0845-41d3-a44b-cd1b448318af",
                        "name": "Action 2",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 2\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f",
                        "name": "Action 3",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Bye\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "1c03b5c8-bba2-465f-8819-278c7081d4ac",
                        "name": "Action 4",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ \"***********\" }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Oops, timed out\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016",
                        "name": "Action 5",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "assign": null,
                        "id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda",
                        "name": "end",
                        "next_step_id": null
                    }
                ],
                "activation_status": "Active",
                "metadata": null,
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-01-15T03:58:59.477+00:00",
                "updated_at": "2024-01-15T03:59:01.373+00:00",
                "id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;

        // Act
        var stepNodeIds = _workflowStepNodeLocator.GetStepNodeIds(
            workflow.Steps,
            workflow.Metadata);

        // Assert
        Assert.That(stepNodeIds, Has.Count.EqualTo(0));
    }

    [Test]
    public void GetStepNodeIds_GivenWorkflowMetadataIsEmpty_ShouldReturnEmptyDictionary()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "7glUk05r4XAGyyy",
                "workflow_versioned_id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z",
                "name": "Problematic flow",
                "triggers": {
                    "contact_updated": {
                        "condition": "{{ ((event_body.post_updated_contact[\"9f5ea004-867c-40de-a22b-1e470b6c1897\"] == \"false\")) }}"
                    }
                },
                "steps": [
                    {
                        "assign": {
                            "contact": "{{ trigger_event_body.contact_id | sleekflow.get_contact }}",
                            "conversation": "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}",
                            "lists": "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        },
                        "id": "setup-contact-and-conversation",
                        "name": "setup",
                        "next_step_id": null
                    },
                    {
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "id": "setup-contact-owner",
                        "name": "setup-contact-owner",
                        "next_step_id": null
                    },
                    {
                        "assign": null,
                        "id": "cb6dead9-58dd-4020-9900-5a9ca281dcaa",
                        "name": "send-message-and-wait-for-event",
                        "next_step_id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "parallel_branches": [
                            {
                                "step": {
                                    "assign": null,
                                    "catch": {
                                        "as": "e",
                                        "step": {
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ sys_var_dict.e.error_message }}"
                                            },
                                            "id": "141482c2-38e9-4de2-9e41-7f48fb4900ca",
                                            "name": "timeout",
                                            "next_step_id": null
                                        }
                                    },
                                    "id": "daebfeb5-80f3-471c-be98-dd6f9df166c6",
                                    "name": "try-catch-timeout",
                                    "next_step_id": null,
                                    "try": {
                                        "step": {
                                            "args": {
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "event_name": "OnMessageReceived",
                                                "timeout_seconds__expr": "300"
                                            },
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ null }}",
                                                "message_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ event_body.message.message_content }}"
                                            },
                                            "call": "sys.wait-for-event",
                                            "id": "43c2c09b-1f12-4737-8494-82b4ed9ac058",
                                            "name": "wait-for-message",
                                            "next_step_id": null
                                        }
                                    }
                                }
                            },
                            {
                                "step": {
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "to_facebook_comment_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "to_instagram_comment_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "to_phone_number__expr": null
                                        },
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"Pick something\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"ab4481a0-c693-43d6-b89a-47bc5034bf5b\",\"title\":\"Something  1\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"bae4586e-474c-4b54-9860-8ad01cfeafb6\",\"title\":\"Something 2\"}}]}}} }}",
                                        "message_type__expr": "interactive"
                                    },
                                    "assign": null,
                                    "call": "sleekflow.v1.send-message",
                                    "id": "5dabd486-5870-4c88-b478-6a02f5fc0b94",
                                    "name": "Action 1",
                                    "next_step_id": null
                                }
                            }
                        ]
                    },
                    {
                        "assign": null,
                        "id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "name": "timeout-with-answers-switch",
                        "next_step_id": null,
                        "switch": [
                            {
                                "condition": "{{ usr_var_dict[\"error_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] != null }}",
                                "next_step_id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something  1\" | string.strip) }}",
                                "next_step_id": "0dcc85cd-0845-41d3-a44b-cd1b448318af"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something 2\" | string.strip) }}",
                                "next_step_id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "1c03b5c8-bba2-465f-8819-278c7081d4ac"
                            }
                        ]
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 1\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "0dcc85cd-0845-41d3-a44b-cd1b448318af",
                        "name": "Action 2",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 2\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f",
                        "name": "Action 3",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Bye\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "1c03b5c8-bba2-465f-8819-278c7081d4ac",
                        "name": "Action 4",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ \"***********\" }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Oops, timed out\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016",
                        "name": "Action 5",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "assign": null,
                        "id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda",
                        "name": "end",
                        "next_step_id": null
                    }
                ],
                "activation_status": "Active",
                "metadata": {},
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-01-15T03:58:59.477+00:00",
                "updated_at": "2024-01-15T03:59:01.373+00:00",
                "id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;

        // Act
        var stepNodeIds = _workflowStepNodeLocator.GetStepNodeIds(
            workflow.Steps,
            workflow.Metadata);

        // Assert
        Assert.That(stepNodeIds, Has.Count.EqualTo(0));
    }

    [Test]
    public void GetStepNodeIds_GivenWorkflowMetadataEntryIsEmpty_ShouldReturnEmptyDictionary()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "7glUk05r4XAGyyy",
                "workflow_versioned_id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z",
                "name": "Problematic flow",
                "triggers": {
                    "contact_updated": {
                        "condition": "{{ ((event_body.post_updated_contact[\"9f5ea004-867c-40de-a22b-1e470b6c1897\"] == \"false\")) }}"
                    }
                },
                "steps": [
                    {
                        "assign": {
                            "contact": "{{ trigger_event_body.contact_id | sleekflow.get_contact }}",
                            "conversation": "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}",
                            "lists": "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        },
                        "id": "setup-contact-and-conversation",
                        "name": "setup",
                        "next_step_id": null
                    },
                    {
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "id": "setup-contact-owner",
                        "name": "setup-contact-owner",
                        "next_step_id": null
                    },
                    {
                        "assign": null,
                        "id": "cb6dead9-58dd-4020-9900-5a9ca281dcaa",
                        "name": "send-message-and-wait-for-event",
                        "next_step_id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "parallel_branches": [
                            {
                                "step": {
                                    "assign": null,
                                    "catch": {
                                        "as": "e",
                                        "step": {
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ sys_var_dict.e.error_message }}"
                                            },
                                            "id": "141482c2-38e9-4de2-9e41-7f48fb4900ca",
                                            "name": "timeout",
                                            "next_step_id": null
                                        }
                                    },
                                    "id": "daebfeb5-80f3-471c-be98-dd6f9df166c6",
                                    "name": "try-catch-timeout",
                                    "next_step_id": null,
                                    "try": {
                                        "step": {
                                            "args": {
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "event_name": "OnMessageReceived",
                                                "timeout_seconds__expr": "300"
                                            },
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ null }}",
                                                "message_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ event_body.message.message_content }}"
                                            },
                                            "call": "sys.wait-for-event",
                                            "id": "43c2c09b-1f12-4737-8494-82b4ed9ac058",
                                            "name": "wait-for-message",
                                            "next_step_id": null
                                        }
                                    }
                                }
                            },
                            {
                                "step": {
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "to_facebook_comment_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "to_instagram_comment_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "to_phone_number__expr": null
                                        },
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"Pick something\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"ab4481a0-c693-43d6-b89a-47bc5034bf5b\",\"title\":\"Something  1\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"bae4586e-474c-4b54-9860-8ad01cfeafb6\",\"title\":\"Something 2\"}}]}}} }}",
                                        "message_type__expr": "interactive"
                                    },
                                    "assign": null,
                                    "call": "sleekflow.v1.send-message",
                                    "id": "5dabd486-5870-4c88-b478-6a02f5fc0b94",
                                    "name": "Action 1",
                                    "next_step_id": null
                                }
                            }
                        ]
                    },
                    {
                        "assign": null,
                        "id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "name": "timeout-with-answers-switch",
                        "next_step_id": null,
                        "switch": [
                            {
                                "condition": "{{ usr_var_dict[\"error_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] != null }}",
                                "next_step_id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something  1\" | string.strip) }}",
                                "next_step_id": "0dcc85cd-0845-41d3-a44b-cd1b448318af"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something 2\" | string.strip) }}",
                                "next_step_id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "1c03b5c8-bba2-465f-8819-278c7081d4ac"
                            }
                        ]
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 1\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "0dcc85cd-0845-41d3-a44b-cd1b448318af",
                        "name": "Action 2",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 2\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f",
                        "name": "Action 3",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Bye\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "1c03b5c8-bba2-465f-8819-278c7081d4ac",
                        "name": "Action 4",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ \"***********\" }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Oops, timed out\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016",
                        "name": "Action 5",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "assign": null,
                        "id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda",
                        "name": "end",
                        "next_step_id": null
                    }
                ],
                "activation_status": "Active",
                "metadata": {
                    "v2": ""
                },
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-01-15T03:58:59.477+00:00",
                "updated_at": "2024-01-15T03:59:01.373+00:00",
                "id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;

        // Act
        var stepNodeIds = _workflowStepNodeLocator.GetStepNodeIds(
            workflow.Steps,
            workflow.Metadata);

        // Assert
        Assert.That(stepNodeIds, Has.Count.EqualTo(0));
    }

    [Test]
    public void GetStepNodeIds_GivenWorkflowMetadataEntryIsNotValid_ShouldReturnEmptyDictionary()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "7glUk05r4XAGyyy",
                "workflow_versioned_id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z",
                "name": "Problematic flow",
                "triggers": {
                    "contact_updated": {
                        "condition": "{{ ((event_body.post_updated_contact[\"9f5ea004-867c-40de-a22b-1e470b6c1897\"] == \"false\")) }}"
                    }
                },
                "steps": [
                    {
                        "assign": {
                            "contact": "{{ trigger_event_body.contact_id | sleekflow.get_contact }}",
                            "conversation": "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}",
                            "lists": "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        },
                        "id": "setup-contact-and-conversation",
                        "name": "setup",
                        "next_step_id": null
                    },
                    {
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "id": "setup-contact-owner",
                        "name": "setup-contact-owner",
                        "next_step_id": null
                    },
                    {
                        "assign": null,
                        "id": "cb6dead9-58dd-4020-9900-5a9ca281dcaa",
                        "name": "send-message-and-wait-for-event",
                        "next_step_id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "parallel_branches": [
                            {
                                "step": {
                                    "assign": null,
                                    "catch": {
                                        "as": "e",
                                        "step": {
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ sys_var_dict.e.error_message }}"
                                            },
                                            "id": "141482c2-38e9-4de2-9e41-7f48fb4900ca",
                                            "name": "timeout",
                                            "next_step_id": null
                                        }
                                    },
                                    "id": "daebfeb5-80f3-471c-be98-dd6f9df166c6",
                                    "name": "try-catch-timeout",
                                    "next_step_id": null,
                                    "try": {
                                        "step": {
                                            "args": {
                                                "condition__expr": "{{ event_body.channel_id == trigger_event_body.channel_id }}",
                                                "event_name": "OnMessageReceived",
                                                "timeout_seconds__expr": "300"
                                            },
                                            "assign": {
                                                "error_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ null }}",
                                                "message_43c2c09b-1f12-4737-8494-82b4ed9ac058": "{{ event_body.message.message_content }}"
                                            },
                                            "call": "sys.wait-for-event",
                                            "id": "43c2c09b-1f12-4737-8494-82b4ed9ac058",
                                            "name": "wait-for-message",
                                            "next_step_id": null
                                        }
                                    }
                                }
                            },
                            {
                                "step": {
                                    "args": {
                                        "channel__expr": "whatsappcloudapi",
                                        "from_to": {
                                            "from_channel_id__expr": null,
                                            "from_facebook_page_id__expr": null,
                                            "from_facebook_post_id__expr": null,
                                            "from_instagram_media_id__expr": null,
                                            "from_instagram_page_id__expr": null,
                                            "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                            "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                            "to_facebook_comment_id__expr": null,
                                            "to_facebook_id__expr": null,
                                            "to_instagram_comment_id__expr": null,
                                            "to_instagram_id__expr": null,
                                            "to_phone_number__expr": null
                                        },
                                        "message_body__expr": "{{ {\"interactive_message\":{\"type\":\"button\",\"body\":{\"text\":\"Pick something\",\"type\":\"text\"},\"action\":{\"buttons\":[{\"type\":\"reply\",\"reply\":{\"id\":\"ab4481a0-c693-43d6-b89a-47bc5034bf5b\",\"title\":\"Something  1\"}},{\"type\":\"reply\",\"reply\":{\"id\":\"bae4586e-474c-4b54-9860-8ad01cfeafb6\",\"title\":\"Something 2\"}}]}}} }}",
                                        "message_type__expr": "interactive"
                                    },
                                    "assign": null,
                                    "call": "sleekflow.v1.send-message",
                                    "id": "5dabd486-5870-4c88-b478-6a02f5fc0b94",
                                    "name": "Action 1",
                                    "next_step_id": null
                                }
                            }
                        ]
                    },
                    {
                        "assign": null,
                        "id": "cb083903-8357-4728-9ee3-0f82aeac3308",
                        "name": "timeout-with-answers-switch",
                        "next_step_id": null,
                        "switch": [
                            {
                                "condition": "{{ usr_var_dict[\"error_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] != null }}",
                                "next_step_id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something  1\" | string.strip) }}",
                                "next_step_id": "0dcc85cd-0845-41d3-a44b-cd1b448318af"
                            },
                            {
                                "condition": "{{ (usr_var_dict[\"message_43c2c09b-1f12-4737-8494-82b4ed9ac058\"] ?? \" \" | string.strip) == (\"Something 2\" | string.strip) }}",
                                "next_step_id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f"
                            },
                            {
                                "condition": "{{ true }}",
                                "next_step_id": "1c03b5c8-bba2-465f-8819-278c7081d4ac"
                            }
                        ]
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 1\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "0dcc85cd-0845-41d3-a44b-cd1b448318af",
                        "name": "Action 2",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"You picked something 2\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "88ad6faa-ded3-44c3-9a93-f3c8c715093f",
                        "name": "Action 3",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Bye\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "1c03b5c8-bba2-465f-8819-278c7081d4ac",
                        "name": "Action 4",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ \"***********\" }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Oops, timed out\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "6a3d58d8-7f63-47fc-84a0-384bc70c4016",
                        "name": "Action 5",
                        "next_step_id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda"
                    },
                    {
                        "assign": null,
                        "id": "5f0a852f-ed0a-47dc-bf56-0966b0890dda",
                        "name": "end",
                        "next_step_id": null
                    }
                ],
                "activation_status": "Active",
                "metadata": {
                    "v2": "{\"text_message\":{\"text\":\"Oops, timed out\"}}"
                },
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-01-15T03:58:59.477+00:00",
                "updated_at": "2024-01-15T03:59:01.373+00:00",
                "id": "7glUk05r4XAGyyy-rNLcLLyx8lYxM1Z"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;

        // Act
        var stepNodeIds = _workflowStepNodeLocator.GetStepNodeIds(
            workflow.Steps,
            workflow.Metadata);

        // Assert
        Assert.That(stepNodeIds, Has.Count.EqualTo(0));
    }
}