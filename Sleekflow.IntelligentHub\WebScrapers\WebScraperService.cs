﻿using System.Text;
using System.Text.RegularExpressions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

namespace Sleekflow.IntelligentHub.WebScrapers;

public interface IWebScraperService
{
    #region Company Web Scraper
    public Task<WebScraper> GetWebScraperAsync(string sleekflowCompanyId);

    public Task<WebScraperRun> StartOneTimeRunAsync(string sleekflowCompanyId, string oneTimeRunName, WebScraperSetting webScraperSetting);

    public Task<List<WebScraperRun>> GetOneTimeRunsAsync(string sleekflowCompanyId, int offset = 0, int limit = 10);
    #endregion

    #region Run
    public Task<WebScraperRun> GetRunAsync(string sleekflowCompanyId, string apifyRunId);

    public Task DeleteRunAsync(string sleekflowCompanyId, string apifyRunId);

    public Task UpdateRunStatusAsync(ApifyWebhook apifyWebhook);

    public Task<WebScraperRun> UpdateRunStatusAsync(string sleekflowCompanyId, string apifyRunId);

    public Task<WebScraperRun> AbortRunAsync(string sleekflowCompanyId, string apifyRunId);

    public Task<WebScraperRun> ResurrectRunAsync(string sleekflowCompanyId, string apifyRunId);
    #endregion

    #region Web Page
    public Task<string> GetWebPageContentAsync(string sleekflowCompanyId, string apifyRunId, string webPageId);

    public Task<WebScraperRun> StoreWebPageContentAsync(string apifyRunId);
    #endregion

    #region Web Scraper Task
    public Task<WebScraperTask> GetWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId);

    public Task<WebScraperTask> CreateWebScraperTaskAsync(string sleekflowCompanyId, string webScraperTaskName, WebScraperSetting webScraperSetting);

    public Task<WebScraperTask> UpdateWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId, string webScraperTaskName, WebScraperSetting webScraperSetting);

    public Task DeleteWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId);

    public Task<WebScraperRun> StartWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId);

    public Task<List<WebScraperRun>> GetWebScraperTaskRunsAsync(string sleekflowCompanyId, string apifyTaskId, int offset = 0, int limit = 10);

    public Task<WebScraperTask> CreateScheduleForWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId, string cronExpression);

    public Task<WebScraperTask> UpdateScheduleForWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId, string cronExpression, bool isEnabled);

    public Task<WebScraperTask> DeleteScheduleForWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId);
    #endregion
}

public class WebScraperService : IWebScraperService, IScopedService
{
    private readonly IWebScraperHttpService _webScraperHttpService;
    private readonly IWebScraperRepository _webScraperRepository;
    private readonly IWebScraperRunRepository _webScraperRunRepository;
    private readonly IIdService _idService;
    private readonly ILogger<WebScraperService> _logger;
    private readonly IBlobService _blobService;
    private readonly IAzureBlobClientFactory _azureBlobClientFactory;

    public WebScraperService(
        IWebScraperHttpService webScraperHttpService,
        IWebScraperRepository webScraperRepository,
        IWebScraperRunRepository webScraperRunRepository,
        IIdService idService,
        ILogger<WebScraperService> logger,
        IBlobService blobService,
        IAzureBlobClientFactory azureBlobClientFactory)
    {
        _webScraperHttpService = webScraperHttpService;
        _webScraperRepository = webScraperRepository;
        _webScraperRunRepository = webScraperRunRepository;
        _idService = idService;
        _logger = logger;
        _blobService = blobService;
        _azureBlobClientFactory = azureBlobClientFactory;
    }

    /// <summary>
    /// Get Web Scraper for Company. Will create a new one if not exist.
    /// </summary>
    /// <param name="sleekflowCompanyId">SleekFlow Company Id.</param>
    /// <returns>Company Web Scraper.</returns>
    public async Task<WebScraper> GetWebScraperAsync(string sleekflowCompanyId)
    {
        try
        {
            return await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);
        }
        catch (ArgumentException ae)
        {
            var webScraperCode = sleekflowCompanyId.Length >= 8
                ? sleekflowCompanyId[^8..]
                : sleekflowCompanyId.PadLeft(8, '0');
            var webScraper = new WebScraper(
                _idService.GetId(SysTypeNames.WebScraper),
                sleekflowCompanyId,
                webScraperCode,
                null);

            return await _webScraperRepository.CreateAndGetAsync(
                webScraper,
                webScraper.SleekflowCompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError("[WebScraper] Cannot get company web scraper! {CompanyId} {Exception}", sleekflowCompanyId, e.Message);
            throw;
        }
    }

    /// <summary>
    /// Create and Start a new one time run.
    /// Will save to database only when the run is started successfully.
    /// </summary>
    /// <param name="sleekflowCompanyId">SleekFlow Company Id.</param>
    /// <param name="oneTimeRunName">Name of One Time Run.</param>
    /// <param name="webScraperSetting">Apify Actor settings of this Run.</param>
    /// <returns>Run object.</returns>
    public async Task<WebScraperRun> StartOneTimeRunAsync(string sleekflowCompanyId, string oneTimeRunName, WebScraperSetting webScraperSetting)
    {
        try
        {
            var formattedName = await FormatOneTimeRunNameAsync(oneTimeRunName, sleekflowCompanyId);

            var apifyRunResponse = await _webScraperHttpService.StartActorRunAsync(webScraperSetting);

            WebScraperRun oneTimeWebScraperRun = new WebScraperRun(
                _idService.GetId(SysTypeNames.WebScraperRun),
                apifyRunId: apifyRunResponse.Data.ApifyRunId,
                sleekflowCompanyId: sleekflowCompanyId,
                apifyTaskId: null,
                isOneTimeRun: true,
                oneTimeRunName: formattedName,
                createdAt: DateTimeOffset.Now,
                startedAt: apifyRunResponse.Data.StartedAt != null
                    ? new DateTimeOffset(apifyRunResponse.Data.StartedAt.GetValueOrDefault())
                    : null,
                finishedAt: apifyRunResponse.Data.FinishedAt != null
                    ? new DateTimeOffset(apifyRunResponse.Data.FinishedAt.GetValueOrDefault())
                    : null,
                status: apifyRunResponse.Data.Status,
                statusMessage: apifyRunResponse.Data.StatusMessage,
                apifyKeyValueStoreId: apifyRunResponse.Data.DefaultKeyValueStoreId,
                apifyDatasetId: apifyRunResponse.Data.DefaultDatasetId,
                apifyRequestQueueId: apifyRunResponse.Data.DefaultRequestQueueId,
                webScraperSetting: webScraperSetting,
                webPages: null);

            return await _webScraperRunRepository.CreateAndGetAsync(
                oneTimeWebScraperRun,
                oneTimeWebScraperRun.SleekflowCompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError("[WebScraper] Failed to start one time run! {CompanyId} {Exception}", sleekflowCompanyId, e.Message);
            throw;
        }
    }

    /// <summary>
    /// Get One Time Runs of Company.
    /// Limit the number of runs to 10 because each run may have huge amount of webpages.
    /// </summary>
    /// <param name="sleekflowCompanyId">SleekFlow Company Id.</param>
    /// <param name="offset">offset.</param>
    /// <param name="limit">limit.</param>
    /// <returns>A list of One Time Runs.</returns>
    public async Task<List<WebScraperRun>> GetOneTimeRunsAsync(string sleekflowCompanyId, int offset = 0, int limit = 10)
    {
        var oneTimeRuns = await _webScraperRunRepository.GetObjectsAsync(
            r =>
                r.IsOneTimeRun &&
                r.SleekflowCompanyId == sleekflowCompanyId,
            offset + limit);

        return oneTimeRuns.Skip(offset).Take(limit).ToList();
    }

    public async Task<WebScraperRun> GetRunAsync(string sleekflowCompanyId, string apifyRunId)
    {
        return await _webScraperRunRepository.GetRunAsync(sleekflowCompanyId, apifyRunId);
    }

    public async Task DeleteRunAsync(string sleekflowCompanyId, string apifyRunId)
    {
        var run = await _webScraperRunRepository.GetRunAsync(sleekflowCompanyId, apifyRunId);

        try
        {
            // Delete previous webpages blobs.
            if (run.WebPages != null)
            {
                await _blobService.DeleteBlobs(
                    run.SleekflowCompanyId,
                    run.WebPages.Select(w => w.WebPageId).ToList(),
                    "File");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to delete web page blobs for run {RunId}", apifyRunId);
        }

        var deleteRunAsync = await _webScraperRunRepository.DeleteAsync(
            run.Id,
            run.SleekflowCompanyId);

        if (deleteRunAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete Run: apifyRunId {apifyRunId}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }

    /// <summary>
    /// For WebScraperWebhookService to update Run status.
    /// </summary>
    /// <param name="apifyWebhook">Webhook from Apify.</param>
    /// <returns>Task.</returns>
    public async Task UpdateRunStatusAsync(ApifyWebhook apifyWebhook)
    {
        var runs = await _webScraperRunRepository.GetObjectsAsync(
            r =>
                r.ApifyRunId == apifyWebhook.Data.ActorRunId);

        if (runs.Count != 1)
        {
            throw new ArgumentException("Invalid apifyRunId.", apifyWebhook.Data.ActorRunId);
        }

        var run = runs.First();

        run.StartedAt = apifyWebhook.Resource.StartedAt != null
            ? new DateTimeOffset(apifyWebhook.Resource.StartedAt.GetValueOrDefault())
            : null;
        run.FinishedAt = apifyWebhook.Resource.FinishedAt != null
            ? new DateTimeOffset(apifyWebhook.Resource.FinishedAt.GetValueOrDefault())
            : null;
        run.Status = apifyWebhook.Resource.Status;
        run.StatusMessage = apifyWebhook.Resource.StatusMessage;
        run.ApifyKeyValueStoreId = apifyWebhook.Resource.DefaultKeyValueStoreId;
        run.ApifyDatasetId = apifyWebhook.Resource.DefaultDatasetId;
        run.ApifyRequestQueueId = apifyWebhook.Resource.DefaultRequestQueueId;

        await _webScraperRunRepository.UpsertAsync(run, run.SleekflowCompanyId);
    }

    /// <summary>
    /// For manually update Run status.
    /// </summary>
    /// <param name="sleekflowCompanyId">SleekFlow Company Id.</param>
    /// <param name="apifyRunId">Run Id.</param>
    /// <returns>Task.</returns>
    public async Task<WebScraperRun> UpdateRunStatusAsync(string sleekflowCompanyId, string apifyRunId)
    {
        var run = await _webScraperRunRepository.GetRunAsync(sleekflowCompanyId, apifyRunId);

        var apifyRunResponse = await _webScraperHttpService.GetRunDetailAsync(run.ApifyRunId);
        var updatedRun = UpdateRunMetadata(run, apifyRunResponse);

        return await _webScraperRunRepository.UpsertAndGetAsync(
            updatedRun,
            updatedRun.SleekflowCompanyId);
    }

    public async Task<WebScraperRun> AbortRunAsync(string sleekflowCompanyId, string apifyRunId)
    {
        var run = await _webScraperRunRepository.GetRunAsync(sleekflowCompanyId, apifyRunId);

        if (!ApifyRunStatuses.TransitionalStatuses.Contains(run.Status))
        {
            throw new ArgumentException($"Cannot abort the Run {run.ApifyRunId} because it is not at Transitional Status.", run.Status);
        }

        var apifyRunResponse = await _webScraperHttpService.AbortRunAsync(run.ApifyRunId);
        var updatedRun = UpdateRunMetadata(run, apifyRunResponse);

        return await _webScraperRunRepository.UpsertAndGetAsync(
            updatedRun,
            updatedRun.SleekflowCompanyId);
    }

    public async Task<WebScraperRun> ResurrectRunAsync(string sleekflowCompanyId, string apifyRunId)
    {
        var run = await _webScraperRunRepository.GetRunAsync(sleekflowCompanyId, apifyRunId);

        if (!ApifyRunStatuses.TerminalStatuses.Contains(run.Status))
        {
            throw new ArgumentException($"Cannot resurrect the Run {run.ApifyRunId} because it is not at Terminal Status.", run.Status);
        }

        var apifyRunResponse = await _webScraperHttpService.ResurrectRunAsync(run.ApifyRunId);
        var updatedRun = UpdateRunMetadata(run, apifyRunResponse);

        return await _webScraperRunRepository.UpsertAndGetAsync(
            updatedRun,
            updatedRun.SleekflowCompanyId);
    }

    public async Task<string> GetWebPageContentAsync(string sleekflowCompanyId, string apifyRunId, string webPageId)
    {
        try
        {
            string blobDownloadSasUrl;

            try
            {
                blobDownloadSasUrl = _blobService.CreateBlobDownloadSasUrls(
                        sleekflowCompanyId,
                        new List<string> { webPageId },
                        "" +
                        "File")
                    .Result.First()
                    .Url;
            }
            catch (Exception e)
            {
                throw new SfInternalErrorException($"Failed to get blob download sas url. WebPageId: {webPageId}");
            }

            var blobClient = _azureBlobClientFactory.GetBlobClient(blobDownloadSasUrl);

            var memoryStream = new MemoryStream();
            await blobClient.DownloadToAsync(memoryStream);

            memoryStream.Position = 0;
            using var reader = new StreamReader(memoryStream, Encoding.UTF8);
            return await reader.ReadToEndAsync();
        }
        catch (Exception e)
        {
            _logger.LogError("Failed to get web page content. {RunId}, {WebPageId}, {ErrorMessage}", apifyRunId, webPageId, e.Message);
            throw;
        }
    }

    public async Task<WebScraperRun> StoreWebPageContentAsync(string apifyRunId)
    {
        var runs = await _webScraperRunRepository.GetObjectsAsync(
            r =>
                r.ApifyRunId == apifyRunId);

        if (runs.Count != 1)
        {
            throw new ArgumentException("Invalid apifyRunId.", apifyRunId);
        }

        var run = runs.First();

        if (run.Status != ApifyRunStatuses.Succeeded)
        {
            throw new ArgumentException(
                $"Cannot store web page content because the Run {run.ApifyRunId} is not at Succeeded Status.",
                run.Status);
        }

        var apifyDatasetResponse = await _webScraperHttpService.GetDatasetDetailAsync(run.ApifyDatasetId);
        var totalCount = apifyDatasetResponse.Data.ItemCount;

        // Reset webpages for each dataset sync.
        var webPagesToDelete = run.WebPages?.Select(wp => wp.WebPageId).ToList();
        run.WebPages = new List<WebPage>();

        try
        {
            for (int i = 0; i < totalCount; i += 1000)
            {
                var datasetItemResponses =
                    await _webScraperHttpService.GetDatasetItemsAsync(run.ApifyDatasetId, i, 1000);

                foreach (var datasetItemResponse in datasetItemResponses)
                {
                    // Upload web page content to blob storage.
                    byte[] byteArray = Encoding.UTF8.GetBytes(datasetItemResponse.Text);
                    using (MemoryStream stream = new MemoryStream(byteArray))
                    {
                        var blobUploadSasUrl = _blobService.CreateBlobUploadSasUrls(
                                run.SleekflowCompanyId,
                                1,
                                "File")
                            .Result.First()
                            .Url;

                        var blobClient = _azureBlobClientFactory.GetBlobClient(blobUploadSasUrl);
                        await blobClient.UploadAsync(stream, overwrite: true);

                        // Store web page metadata to DB
                        var webpageId = blobClient.Name.Split("/")[1];
                        var webPage = new WebPage(
                            webPageId: webpageId,
                            apifyRunId: apifyRunId,
                            webPageUri: datasetItemResponse.Url,
                            scrapedTime: new DateTimeOffset(datasetItemResponse.Crawl.LoadedTime),
                            languageIsoCode: datasetItemResponse.Metadata.LanguageCode,
                            title: datasetItemResponse.Metadata.Title,
                            description: datasetItemResponse.Metadata.Description,
                            keywords: datasetItemResponse.Metadata.Keywords,
                            author: datasetItemResponse.Metadata.Author);

                        run.WebPages.Add(webPage);
                    }
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to store web page content for run {RunId}", apifyRunId);
            throw;
        }

        run = await _webScraperRunRepository.UpsertAndGetAsync(run, run.SleekflowCompanyId);

        // Delete previous webpages blobs.
        try
        {
            if (webPagesToDelete != null)
            {
                await _blobService.DeleteBlobs(
                    run.SleekflowCompanyId,
                    webPagesToDelete,
                    "File");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to delete web page blobs for Run {ApifyRunId}", apifyRunId);
        }

        return run;
    }

    public async Task<WebScraperTask> GetWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId)
    {
        return await _webScraperRepository.GetWebScraperTaskAsync(sleekflowCompanyId, apifyTaskId);
    }

    public async Task<WebScraperTask> CreateWebScraperTaskAsync(
        string sleekflowCompanyId,
        string webScraperTaskName,
        WebScraperSetting webScraperSetting)
    {
        try
        {
            var webScraper = await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);

            var webScraperTaskCode = GenerateWebScraperTaskCodeAsync(webScraperTaskName, webScraper);
            var trimmedWebScraperTaskName = webScraperTaskName.Trim();

            var apifyTaskResponse = await _webScraperHttpService.CreateApifyTaskAsync(webScraperTaskCode, webScraperSetting);
            await _webScraperHttpService.RegisterWebhookForApifyTaskAsync(apifyTaskResponse.Data.Id);

            var webScraperTask = new WebScraperTask(
                apifyTaskResponse.Data.Id,
                trimmedWebScraperTaskName,
                webScraperTaskCode,
                DateTimeOffset.Now,
                DateTimeOffset.Now,
                webScraperSetting,
                null);

            webScraper.WebScraperTasks ??= new List<WebScraperTask>();
            webScraper.WebScraperTasks.Add(webScraperTask);

            await _webScraperRepository.UpsertAndGetAsync(webScraper, sleekflowCompanyId);

            return webScraperTask;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to create web scraper task {WebScraperTaskName} for company {SleekflowCompanyId}: {ErrorMessage}",
                webScraperTaskName,
                sleekflowCompanyId,
                e.Message);
            throw;
        }
    }

    public async Task<WebScraperTask> UpdateWebScraperTaskAsync(
        string sleekflowCompanyId,
        string apifyTaskId,
        string webScraperTaskName,
        WebScraperSetting webScraperSetting)
    {
        try
        {
            var webScraper = await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);
            var webScraperTask = webScraper.WebScraperTasks?.First(t => t.ApifyTaskId == apifyTaskId);
            if (webScraperTask == null)
            {
                throw new ArgumentException($"Cannot find Web scraper task {apifyTaskId} for company {sleekflowCompanyId}");
            }

            // If display name changed
            if (webScraperTask.Code != FormatWebScraperTaskCode(webScraper.Code, webScraperTaskName))
            {
                webScraperTask.Code = GenerateWebScraperTaskCodeAsync(webScraperTaskName, webScraper);
                webScraperTask.DisplayName = webScraperTaskName.Trim();
            }

            await _webScraperHttpService.UpdateApifyTaskSettingsAsync(
                apifyTaskId,
                webScraperTask.Code,
                webScraperSetting);

            // Update task meta data
            webScraperTask.UpdatedAt = DateTimeOffset.Now;
            webScraperTask.WebScraperSetting = webScraperSetting;

            await _webScraperRepository.UpsertAndGetAsync(webScraper, sleekflowCompanyId);

            return webScraperTask;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to update web scraper task {ApifyTaskId} for company {SleekflowCompanyId}: {ErrorMessage}",
                apifyTaskId,
                sleekflowCompanyId,
                e.Message);
            throw;
        }
    }

    public async Task DeleteWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId)
    {
        try
        {
            var webScraper = await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);
            var webScraperTask = webScraper.WebScraperTasks?.First(t => t.ApifyTaskId == apifyTaskId);
            if (webScraperTask == null)
            {
                throw new ArgumentException($"Cannot find Web scraper task {apifyTaskId} for company {sleekflowCompanyId}");
            }

            if (webScraperTask.WebScraperTaskScheduler != null)
            {
                await _webScraperHttpService.DeleteSchedulerAsync(webScraperTask.WebScraperTaskScheduler.ApifySchedulerId);
            }

            await _webScraperHttpService.DeleteTaskAsync(apifyTaskId);

            var taskRuns = await _webScraperRunRepository.GetObjectsAsync(
                r => r.SleekflowCompanyId == sleekflowCompanyId && r.ApifyTaskId == apifyTaskId);
            foreach (var taskRun in taskRuns)
            {
                await DeleteRunAsync(sleekflowCompanyId, taskRun.ApifyRunId);
            }

            if (webScraper.WebScraperTasks == null || !webScraper.WebScraperTasks.Remove(webScraperTask))
            {
                throw new Exception($"Failed to remove web scraper task {apifyTaskId} from company {sleekflowCompanyId}");
            }

            await _webScraperRepository.UpsertAndGetAsync(webScraper, sleekflowCompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to delete web scraper task {ApifyTaskId} from company {SleekflowCompanyId}: {ErrorMessage}",
                apifyTaskId,
                sleekflowCompanyId,
                e.Message);
            throw;
        }
    }

    public async Task<WebScraperRun> StartWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId)
    {
        try
        {
            var webScraperTask = await _webScraperRepository.GetWebScraperTaskAsync(sleekflowCompanyId, apifyTaskId);

            var taskRuns = await _webScraperRunRepository.GetObjectsAsync(r => r.ApifyTaskId == apifyTaskId);

            if (taskRuns.Exists(r => ApifyRunStatuses.TransitionalStatuses.Contains(r.Status)))
            {
                throw new Exception("Task is already running!");
            }

            var apifyRunResponse = await _webScraperHttpService.StartApifyTaskAsync(apifyTaskId);
            var taskRun = new WebScraperRun(
                _idService.GetId(SysTypeNames.WebScraperRun),
                apifyRunId: apifyRunResponse.Data.ApifyRunId,
                sleekflowCompanyId: sleekflowCompanyId,
                apifyTaskId: apifyTaskId,
                isOneTimeRun: false,
                oneTimeRunName: null,
                createdAt: DateTimeOffset.Now,
                startedAt: apifyRunResponse.Data.StartedAt != null
                    ? new DateTimeOffset(apifyRunResponse.Data.StartedAt.GetValueOrDefault())
                    : null,
                finishedAt: apifyRunResponse.Data.FinishedAt != null
                    ? new DateTimeOffset(apifyRunResponse.Data.FinishedAt.GetValueOrDefault())
                    : null,
                status: apifyRunResponse.Data.Status,
                statusMessage: apifyRunResponse.Data.StatusMessage,
                apifyKeyValueStoreId: apifyRunResponse.Data.DefaultKeyValueStoreId,
                apifyDatasetId: apifyRunResponse.Data.DefaultDatasetId,
                apifyRequestQueueId: apifyRunResponse.Data.DefaultRequestQueueId,
                webScraperSetting: webScraperTask.WebScraperSetting,
                webPages: null);

            return await _webScraperRunRepository.CreateAndGetAsync(
                taskRun,
                taskRun.SleekflowCompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to start web scraper task {ApifyTaskId} for company {SleekflowCompanyId}: {ErrorMessage}",
                apifyTaskId,
                sleekflowCompanyId,
                e.Message);
            throw;
        }
    }

    public async Task<List<WebScraperRun>> GetWebScraperTaskRunsAsync(string sleekflowCompanyId, string apifyTaskId, int offset = 0, int limit = 10)
    {
        try
        {
            var taskRuns = await _webScraperRunRepository.GetObjectsAsync(
                r => r.ApifyTaskId == apifyTaskId && r.SleekflowCompanyId == sleekflowCompanyId,
                offset + limit);
            return taskRuns.Skip(offset).Take(limit).ToList();
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to get web scraper task runs for company {SleekflowCompanyId}: {ErrorMessage}",
                sleekflowCompanyId,
                e.Message);
            throw;
        }
    }

    public async Task<WebScraperTask> CreateScheduleForWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId, string cronExpression)
    {
        try
        {
            var webScraper = await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);
            var webScraperTask = webScraper.WebScraperTasks?.First(t => t.ApifyTaskId == apifyTaskId);
            if (webScraperTask == null)
            {
                throw new ArgumentException($"Cannot find Web scraper task {apifyTaskId} for company {sleekflowCompanyId}");
            }

            if (webScraperTask.WebScraperTaskScheduler != null)
            {
                throw new ArgumentException($"Web scraper task {apifyTaskId} already has a scheduler!");
            }

            var schedulerName = $"{webScraper.Code}--{apifyTaskId}";
            var apifySchedulerResponse = await _webScraperHttpService.CreateSchedulerAsync(
                apifyTaskId,
                apifyTaskId,
                cronExpression);

            var scheduler = new WebScraperTaskScheduler(
                schedulerName,
                apifySchedulerResponse.Data.Id,
                cronExpression,
                true);

            webScraperTask.WebScraperTaskScheduler = scheduler;
            await _webScraperRepository.UpsertAndGetAsync(webScraper, sleekflowCompanyId);

            return webScraperTask;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to create schedule for task: {ApifyTaskId}: {ErrorMessage}",
                apifyTaskId,
                e.Message);
            throw;
        }
    }

    public async Task<WebScraperTask> UpdateScheduleForWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId, string cronExpression, bool isEnabled)
    {
        try
        {
            var webScraper = await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);
            var webScraperTask = webScraper.WebScraperTasks?.First(t => t.ApifyTaskId == apifyTaskId);
            if (webScraperTask == null)
            {
                throw new ArgumentException($"Cannot find Web scraper task {apifyTaskId} for company {sleekflowCompanyId}");
            }

            if (webScraperTask.WebScraperTaskScheduler == null)
            {
                throw new ArgumentException($"Web scraper task {apifyTaskId} doesn't have a scheduler!");
            }

            await _webScraperHttpService.UpdateSchedulerSettingsAsync(
                webScraperTask.WebScraperTaskScheduler.ApifySchedulerId,
                cronExpression,
                isEnabled);

            webScraperTask.WebScraperTaskScheduler.CronExpression = cronExpression;
            webScraperTask.WebScraperTaskScheduler.IsEnabled = isEnabled;
            webScraperTask.UpdatedAt = DateTimeOffset.Now;

            await _webScraperRepository.UpsertAndGetAsync(webScraper, sleekflowCompanyId);
            return webScraperTask;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to create schedule for task: {ApifyTaskId}: {ErrorMessage}",
                apifyTaskId,
                e.Message);
            throw;
        }
    }

    public async Task<WebScraperTask> DeleteScheduleForWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId)
    {
        try
        {
            var webScraper = await _webScraperRepository.GetWebScraperAsync(sleekflowCompanyId);
            var webScraperTask = webScraper.WebScraperTasks?.First(t => t.ApifyTaskId == apifyTaskId);
            if (webScraperTask == null)
            {
                throw new ArgumentException($"Cannot find Web scraper task {apifyTaskId} for company {sleekflowCompanyId}");
            }

            if (webScraperTask.WebScraperTaskScheduler == null)
            {
                throw new ArgumentException($"Web scraper task {apifyTaskId} doesn't have a scheduler!");
            }

            await _webScraperHttpService.DeleteSchedulerAsync(webScraperTask.WebScraperTaskScheduler.ApifySchedulerId);

            webScraperTask.WebScraperTaskScheduler = null;
            webScraperTask.UpdatedAt = DateTimeOffset.Now;

            await _webScraperRepository.UpsertAndGetAsync(webScraper, sleekflowCompanyId);
            return webScraperTask;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[WebScraper] Failed to delete schedule for task: {ApifyTaskId}: {ErrorMessage}",
                apifyTaskId,
                e.Message);
            throw;
        }
    }

    #region private methods

    private static WebScraperRun UpdateRunMetadata(WebScraperRun webScraperRun, ApifyRunResponse apifyRunResponse)
    {
        webScraperRun.StartedAt = apifyRunResponse.Data.StartedAt != null
            ? new DateTimeOffset(apifyRunResponse.Data.StartedAt.GetValueOrDefault())
            : null;
        webScraperRun.FinishedAt = apifyRunResponse.Data.FinishedAt != null
            ? new DateTimeOffset(apifyRunResponse.Data.FinishedAt.GetValueOrDefault())
            : null;
        webScraperRun.Status = apifyRunResponse.Data.Status;
        webScraperRun.StatusMessage = apifyRunResponse.Data.StatusMessage;
        webScraperRun.ApifyKeyValueStoreId = apifyRunResponse.Data.DefaultKeyValueStoreId;
        webScraperRun.ApifyDatasetId = apifyRunResponse.Data.DefaultDatasetId;
        webScraperRun.ApifyRequestQueueId = apifyRunResponse.Data.DefaultRequestQueueId;

        return webScraperRun;
    }

    private async Task<string> FormatOneTimeRunNameAsync(string oneTimeRunName, string companyId)
    {
        if (!ValidateName(oneTimeRunName, isOneTimeRun: true))
        {
            throw new InvalidDataException($"Invalid oneTimeRunName: {oneTimeRunName}. Only alphanumeric characters, hyphens, and underscores are allowed.");
        }

        var trimmedName = oneTimeRunName.Trim();

        var oneTimeRuns = await _webScraperRunRepository.GetObjectsAsync(
            r =>
                r.IsOneTimeRun &&
                r.SleekflowCompanyId == companyId &&
                r.OneTimeRunName == trimmedName);

        if (oneTimeRuns.Any())
        {
            throw new InvalidDataException($"OneTimeRunName already exists: {oneTimeRunName}.");
        }

        return trimmedName;
    }

    private string GenerateWebScraperTaskCodeAsync(string webScraperTaskName, WebScraper webScraper)
    {
        if (!ValidateName(webScraperTaskName, isOneTimeRun: false))
        {
            throw new InvalidDataException($"Invalid Web Scraper Task Name: {webScraperTaskName}. Only alphanumeric characters and hyphens are allowed.");
        }

        var webScraperTaskCode = FormatWebScraperTaskCode(webScraper.Code, webScraperTaskName);

        var isTaskCodeDuplicate = webScraper.WebScraperTasks != null &&
                                  webScraper.WebScraperTasks.Exists(t => t.Code == webScraperTaskCode);

        if (isTaskCodeDuplicate)
        {
            throw new InvalidDataException($"Web Scraper Task Code already exists: {webScraperTaskCode}.");
        }

        return webScraperTaskCode;
    }

    private static string FormatWebScraperTaskCode(string webScraperCode, string webScraperTaskName)
    {
        // Remove multiple spaces, turn to lower case and replace spaces with hyphens.
        var webScraperTaskCode = Regex.Replace(webScraperTaskName, @"\s+", " ")
            .Replace(" ", "-")
            .ToLower();

        return $"{webScraperCode}--{webScraperTaskCode}";
    }

    /// <summary>
    /// Validate name for OneTimeRun and Task.
    /// Only allowed alphanumeric, space and hyphen.
    /// Length limitation: OneTimeRun 100, Task 30.
    /// </summary>
    /// <param name="name">name.</param>
    /// <param name="isOneTimeRun">If is OneTimeRun.</param>
    /// <returns>isNameValid.</returns>
    private static bool ValidateName(string name, bool isOneTimeRun)
    {
        string pattern = @"^[a-zA-Z0-9-\s]+$";
        Regex regex = new Regex(pattern);
        return isOneTimeRun
            ? name.Length <= 100 && regex.IsMatch(name)
            : name.Length <= 30 && regex.IsMatch(name);
    }

    #endregion
}