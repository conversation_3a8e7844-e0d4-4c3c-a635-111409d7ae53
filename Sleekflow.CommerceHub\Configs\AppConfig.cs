﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface IAppConfig
{
    string CommerceHubEndpoint { get; }
}

public class AppConfig : IAppConfig, IConfig
{
    public string CommerceHubEndpoint { get; }

    public AppConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CommerceHubEndpoint =
            Environment.GetEnvironmentVariable("COMMERCE_HUB_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("COMMERCE_HUB_ENDPOINT");
    }
}