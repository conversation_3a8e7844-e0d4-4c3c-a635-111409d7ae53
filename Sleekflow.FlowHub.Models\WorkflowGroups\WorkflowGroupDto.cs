﻿using Newtonsoft.Json;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Models.WorkflowGroups;

public class WorkflowGroupDto : AuditEntityDto
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonConstructor]
    public WorkflowGroupDto(
        string name,
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(
            id,
            sleekflowCompanyId,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt)
    {
        Name = name;
    }

    public WorkflowGroupDto(WorkflowGroup workflowGroup)
        : this(
            workflowGroup.Name,
            workflowGroup.Id,
            workflowGroup.SleekflowCompanyId,
            workflowGroup.CreatedBy,
            workflowGroup.UpdatedBy,
            workflowGroup.CreatedAt,
            workflowGroup.UpdatedAt)
    {
    }
}