﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Workers.Triggers.Dynamics365;

public class SubscriptionsCheckOrchestrator
{
    public class SubscriptionsCheckOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTimeOffset LastUpdateTime { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorCustomStatusOutput(
            long count,
            DateTimeOffset lastUpdateTime,
            DateTimeOffset? lastObjectModificationTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Dynamics365_SubscriptionsCheck_Orchestrator")]
    public async Task<SubscriptionsCheckOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var startTime = context.CurrentUtcDateTime;
        var subscriptionsCheckInput = context.GetInput<SubscriptionsCheck.SubscriptionsCheckInput>();
        var dynamics365Subscription = subscriptionsCheckInput.Subscription;

        context.SetCustomStatus(new SubscriptionsCheckOrchestratorCustomStatusOutput(0, startTime, null));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;

        // Dynamics365 uses `>=`
        // The first lastObjectModificationTime should use `+1` to get newer objects
        var lastObjectModificationTime =
            dynamics365Subscription.LastObjectModificationTime?.AddSeconds(1)
            ?? dynamics365Subscription.LastExecutionStartTime;
        var nextLastObjectModificationTime =
            dynamics365Subscription.LastObjectModificationTime
            ?? dynamics365Subscription.LastExecutionStartTime;
        var nextRecordsUrl = null as string;
        while (true)
        {
            var subscriptionsCheckBatchOutput =
                await context.CallActivityAsync<SubscriptionsCheckBatch.SubscriptionsCheckBatchOutput>(
                    "Dynamics365_SubscriptionsCheck_Batch",
                    new SubscriptionsCheckBatch.SubscriptionsCheckBatchInput(
                        subscriptionsCheckInput.SleekflowCompanyId,
                        dynamics365Subscription,
                        subscriptionsCheckInput.EntityTypeName,
                        subscriptionsCheckInput.FilterGroups,
                        subscriptionsCheckInput.FieldFilters,
                        lastObjectModificationTime,
                        nextRecordsUrl),
                    taskOptions);

            totalCount += subscriptionsCheckBatchOutput.Count;
            nextLastObjectModificationTime =
                subscriptionsCheckBatchOutput.NextLastObjectModificationTime > nextLastObjectModificationTime
                    ? subscriptionsCheckBatchOutput.NextLastObjectModificationTime
                    : nextLastObjectModificationTime;
            nextRecordsUrl = subscriptionsCheckBatchOutput.NextRecordsUrl;

            context.SetCustomStatus(
                new SubscriptionsCheckOrchestratorCustomStatusOutput(
                    totalCount,
                    context.CurrentUtcDateTime,
                    nextLastObjectModificationTime));

            if (nextRecordsUrl == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        await context.CallActivityAsync(
            "Dynamics365_SubscriptionsCheck_End",
            new SubscriptionsCheckEnd.SubscriptionsCheckEndInput(
                dynamics365Subscription,
                nextLastObjectModificationTime,
                startTime));

        return new SubscriptionsCheckOrchestratorOutput(totalCount);
    }
}