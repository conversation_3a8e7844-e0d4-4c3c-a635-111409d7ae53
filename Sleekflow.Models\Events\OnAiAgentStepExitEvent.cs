using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.Events;

public class OnAiAgentStepExitEvent
{
    public string StepId { get; }

    public string StateId { get; }

    public Stack<StackEntry> StackEntries { get; }

    public string WorkflowId { get; }

    public string ExitCondition { get; set; }

    public OnAiAgentStepExitEvent(
        string stepId,
        string stateId,
        Stack<StackEntry> stackEntries,
        string workflowId,
        string exitCondition)
    {
        StepId = stepId;
        StateId = stateId;
        StackEntries = stackEntries;
        WorkflowId = workflowId;
        ExitCondition = exitCondition;
    }
}