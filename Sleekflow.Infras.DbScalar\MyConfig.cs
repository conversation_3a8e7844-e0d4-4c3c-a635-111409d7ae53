namespace Sleekflow.Infras.DbScalar;

public class MyConfig
{
    public string TenantId { get; set; }

    public string ClientId { get; set; }

    public string ClientSecret { get; set; }

    public string SubscriptionId { get; set; }

    public string ResourceGroupName { get; set; }

    public string ServerName { get; set; }

    public string DatabaseName { get; set; }

    public int DesiredCapacityUp { get; set; }

    public int DesiredHighAvailabilityReplicaCountUp { get; set; }

    public int DesiredCapacityDown { get; set; }

    public int DesiredHighAvailabilityReplicaCountDown { get; set; }

    public MyConfig()
    {
        TenantId = "d66fa1cc-347d-42e9-9444-19c5fd0bbcce";
        ClientId = "5b6084af-2bd6-44bc-a224-83f655624bb3";
        ClientSecret = "****************************************";

        SubscriptionId = "c19c9b56-93e9-4d4c-bc81-838bd3f72ad6";
        ResourceGroupName = "sleekflow-core-rg-eas-productionb94f12b1";
        ServerName = "sleekflow-core-sql-server-eas-productiond2a4d949";
        DatabaseName = "sleekflow-crm-prod";

        DesiredCapacityUp = 128;
        DesiredHighAvailabilityReplicaCountUp = 0;
        DesiredCapacityDown = 128;
        DesiredHighAvailabilityReplicaCountDown = 0;
    }
}