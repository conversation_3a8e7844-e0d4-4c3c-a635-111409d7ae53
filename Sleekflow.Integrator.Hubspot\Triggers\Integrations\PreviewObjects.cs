using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class PreviewObjects : ITrigger
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public PreviewObjects(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class PreviewObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public PreviewObjectsInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class PreviewObjectsOutput
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public PreviewObjectsOutput(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<PreviewObjectsOutput> F(
        PreviewObjectsInput previewObjectsInput)
    {
        var authentication =
            await _hubspotAuthenticationService.GetAsync(previewObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (objects, _) = await _hubspotObjectService.GetObjectsAsync(
            authentication,
            100,
            null,
            previewObjectsInput.EntityTypeName,
            previewObjectsInput.FilterGroups,
            null);

        return new PreviewObjectsOutput(objects);
    }
}