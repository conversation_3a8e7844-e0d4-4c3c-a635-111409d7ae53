using AutoMapper;
using Sleekflow.CrmHub.Models.Providers;

namespace Sleekflow.Integrator.Dynamics365.Objects;

public class EntityDefinitionAttributeProfile : Profile
{
    public EntityDefinitionAttributeProfile()
    {
        // TODO soaptype
        CreateMap<EntityDefinitionAttribute, GetTypeFieldsOutputFieldDto>()
            .ForMember(f => f.Calculated, e => e.MapFrom(p => false))
            .ForMember(f => f.CompoundFieldName, e => e.MapFrom(p => string.Empty))
            .ForMember(f => f.Createable, e => e.MapFrom(p => p.IsValidForCreate))
            .ForMember(f => f.Custom, e => e.MapFrom(p => p.IsCustomAttribute))
            .ForMember(f => f.Encrypted, e => e.MapFrom(p => p.IsSecured))
            .ForMember(
                f => f.Label,
                e => e.MapFrom(p => p.DisplayName.LocalizedLabels.Select(ll => ll.Label).FirstOrDefault()))
            .ForMember(f => f.Length, e => e.MapFrom(p => p.MaxLength))
            .ForMember(f => f.Name, e => e.MapFrom(p => p.LogicalName))

            // TODO Picklist
            .ForMember(f => f.PicklistValues, e => e.MapFrom(p => new List<GetTypeFieldsOutputPicklistValue>()))
            .ForMember(f => f.SoapType, e => e.MapFrom(p => p.OdataType))
            .ForMember(f => f.Type, e => e.MapFrom(p => p.AttributeType))
            .ForMember(f => f.Unique, e => e.MapFrom(p => false))
            .ForMember(f => f.Updateable, e => e.MapFrom(p => p.IsValidForUpdate))
            .ForCtorParam("calculated", e => e.MapFrom(p => false))
            .ForCtorParam("compoundFieldName", e => e.MapFrom(p => string.Empty))
            .ForCtorParam("createable", e => e.MapFrom(p => p.IsValidForCreate))
            .ForCtorParam("custom", e => e.MapFrom(p => p.IsCustomAttribute))
            .ForCtorParam("encrypted", e => e.MapFrom(p => p.IsSecured))
            .ForCtorParam(
                "label",
                e => e.MapFrom(p => p.DisplayName.LocalizedLabels.Select(ll => ll.Label).FirstOrDefault()))
            .ForCtorParam("length", e => e.MapFrom(p => p.MaxLength))
            .ForCtorParam("name", e => e.MapFrom(p => p.LogicalName))

            // TODO Picklist
            .ForCtorParam("picklistValues", e => e.MapFrom(p => new List<GetTypeFieldsOutputPicklistValue>()))
            .ForCtorParam("soapType", e => e.MapFrom(p => p.OdataType))
            .ForCtorParam("type", e => e.MapFrom(p => p.AttributeType))
            .ForCtorParam("unique", e => e.MapFrom(p => false))
            .ForCtorParam("updateable", e => e.MapFrom(p => p.IsValidForUpdate));
    }
}