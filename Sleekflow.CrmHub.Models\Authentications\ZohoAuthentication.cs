﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Authentications;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("zoho_authentication")]
public class ZohoAuthentication : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty("api_domain")]
    public string ApiDomain { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("expires_in")]
    public string ExpiresIn { get; set; }

    [JsonProperty("issued_at")]
    public string IssuedAt { get; set; }

    [JsonProperty("raw_res")]
    public object? RawRes { get; set; }

    [JsonProperty("refresh_res")]
    public object? RefreshRes { get; set; }

    [JsonConstructor]
    public ZohoAuthentication(
        string id,
        string sleekflowCompanyId,
        string accessToken,
        string refreshToken,
        string apiDomain,
        string tokenType,
        string expiresIn,
        string issuedAt,
        object? rawRes,
        object? refreshRes)
        : base(id, "Authentication")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        ApiDomain = apiDomain;
        TokenType = tokenType;
        ExpiresIn = expiresIn;
        IssuedAt = issuedAt;
        RawRes = rawRes;
        RefreshRes = refreshRes;
    }
}