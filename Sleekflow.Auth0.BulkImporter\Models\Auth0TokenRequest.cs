using Newtonsoft.Json;

namespace Sleekflow.Auth0.BulkImporter.Models;

public class Auth0TokenRequest
{
    public Auth0TokenRequest(string clientId, string clientSecret, string audience, string grantType)
    {
        ClientId = clientId;
        ClientSecret = clientSecret;
        Audience = audience;
        GrantType = grantType;
    }

    [JsonProperty("client_id")]
    public string ClientId { get; set; }

    [JsonProperty("client_secret")]
    public string ClientSecret { get; set; }

    [JsonProperty("audience")]
    public string Audience { get; set; }

    [JsonProperty("grant_type")]
    public string GrantType { get; set; }
}