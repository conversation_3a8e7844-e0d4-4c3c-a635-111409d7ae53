using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class GetPaymentProviderConfigs
    : ITrigger<
        GetPaymentProviderConfigs.GetPaymentProviderConfigsInput,
        GetPaymentProviderConfigs.GetPaymentProviderConfigsOutput>
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;

    public GetPaymentProviderConfigs(
        IPaymentProviderConfigService paymentProviderConfigService)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
    }

    public class GetPaymentProviderConfigsInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public GetPaymentProviderConfigsInput(
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class GetPaymentProviderConfigsOutput
    {
        [JsonProperty("payment_provider_configs")]
        public List<PaymentProviderConfigDto> PaymentProviderConfigs { get; set; }

        [JsonConstructor]
        public GetPaymentProviderConfigsOutput(
            List<PaymentProviderConfigDto> paymentProviderConfigs)
        {
            PaymentProviderConfigs = paymentProviderConfigs;
        }
    }

    public async Task<GetPaymentProviderConfigsOutput> F(
        GetPaymentProviderConfigsInput getPaymentProviderConfigsInput)
    {
        var paymentProviderConfigs = await _paymentProviderConfigService.GetPaymentProviderConfigsAsync(
            getPaymentProviderConfigsInput.SleekflowCompanyId);

        return new GetPaymentProviderConfigsOutput(
            paymentProviderConfigs.Select(ppc => new PaymentProviderConfigDto(ppc)).ToList());
    }
}