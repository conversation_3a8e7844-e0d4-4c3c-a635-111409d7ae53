﻿using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex.ViewModels;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Vtex;

[TriggerGroup(ControllerNames.Vtex)]
public class GetVtexAuthentications
    : ITrigger<
        GetVtexAuthentications.GetVtexAuthenticationsInput,
        GetVtexAuthentications.GetVtexAuthenticationsOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;
    private readonly IVtexOrderHookRegister _vtexOrderHookRegister;

    public GetVtexAuthentications(IVtexAuthenticationService vtexAuthenticationService, IVtexOrderHookRegister vtexOrderHookRegister)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
        _vtexOrderHookRegister = vtexOrderHookRegister;
    }

    public class GetVtexAuthenticationsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetVtexAuthenticationsInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetVtexAuthenticationsOutput
    {
        [JsonProperty("vtex_authentications")]
        public List<VtexAuthenticationViewModel> VtexAuthentications { get; set; }

        [JsonConstructor]
        public GetVtexAuthenticationsOutput(List<VtexAuthenticationViewModel> vtexAuthentications)
        {
            VtexAuthentications = vtexAuthentications;
        }
    }

    public async Task<GetVtexAuthenticationsOutput> F(
        GetVtexAuthenticationsInput input)
    {
        var vtexAuthentications = await _vtexAuthenticationService.GetVtexAuthenticationsAsync(
            input.SleekflowCompanyId);

        var vtexAuthenticationViewModels = new ConcurrentBag<VtexAuthenticationViewModel>();

        await Parallel.ForEachAsync(
            vtexAuthentications,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3
            },
            async (vtexAuthentication, cancellationToken) =>
            {
                var isActive = await _vtexOrderHookRegister.ValidateRegistrationAsync(
                    vtexAuthentication.Credential,
                    cancellationToken);

                vtexAuthenticationViewModels.Add(new VtexAuthenticationViewModel(vtexAuthentication, isActive));
            });

        return new GetVtexAuthenticationsOutput(vtexAuthenticationViewModels.ToList());
    }
}