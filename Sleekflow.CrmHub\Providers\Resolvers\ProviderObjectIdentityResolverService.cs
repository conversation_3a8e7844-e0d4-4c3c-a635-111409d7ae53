﻿using System.Globalization;
using Newtonsoft.Json.Linq;
using PhoneNumbers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Providers.Resolvers;

public interface IProviderObjectIdentityResolverService
{
    ProviderObjectIdentity Resolve(
        Dictionary<string, object?> dict,
        string entityTypeName,
        string providerName,
        ProviderConfig? providerConfig);

    public class ProviderObjectIdentity
    {
        public string? PhoneNumber { get; set; }

        public string? Email { get; set; }

        public ProviderObjectIdentity(
            string? phoneNumber,
            string? email)
        {
            PhoneNumber = phoneNumber;
            Email = email;
        }
    }
}

public class ProviderObjectIdentityResolverService : ISingletonService, IProviderObjectIdentityResolverService
{
    private readonly ILogger<ProviderObjectIdentityResolverService> _logger;

    public ProviderObjectIdentityResolverService(ILogger<ProviderObjectIdentityResolverService> logger)
    {
        _logger = logger;
    }

    public IProviderObjectIdentityResolverService.ProviderObjectIdentity Resolve(
        Dictionary<string, object?> dict,
        string entityTypeName,
        string providerName,
        ProviderConfig? providerConfig)
    {
        if (providerName == "salesforce-integrator"
            && (entityTypeName == "Contact" || entityTypeName == "Lead" || entityTypeName == "User"))
        {
            var countryCodes = new List<string?>
            {
                (string?) dict.GetValueOrDefault("salesforce-integrator:CountryCode"),
                GetTwoLetterIsoRegionNameByCountryName(
                    (string?) dict.GetValueOrDefault("salesforce-integrator:Country")),
                (string?) dict.GetValueOrDefault("salesforce-integrator:MailingCountryCode"),
                GetTwoLetterIsoRegionNameByIsoCurrencySymbol(
                    (string?) dict.GetValueOrDefault("salesforce-integrator:DefaultCurrencyIsoCode")),
                GetTwoLetterIsoRegionNameByCountryName(
                    (string?) dict.GetValueOrDefault("salesforce-integrator:MailingCountry")),
                GetTwoLetterIsoRegionNameByIsoCurrencySymbol(
                    (string?) dict.GetValueOrDefault("salesforce-integrator:CurrencyIsoCode")),

                // VITEL International Hong Kong Limited
                (string?) dict.GetValueOrDefault("salesforce-integrator:Locale__c"),
            };
            var countryCode = countryCodes.Find(l => !string.IsNullOrWhiteSpace(l));

            var phoneNumbers = new List<string?>
            {
                (string?) dict.GetValueOrDefault("salesforce-integrator:MobilePhone"),
                (string?) dict.GetValueOrDefault("salesforce-integrator:OtherPhone"),
                (string?) dict.GetValueOrDefault("salesforce-integrator:Phone"),
                (string?) dict.GetValueOrDefault("salesforce-integrator:HomePhone"),
                (string?) dict.GetValueOrDefault("salesforce-integrator:AssistantPhone"),

                // VITEL International Hong Kong Limited
                (string?) dict.GetValueOrDefault("salesforce-integrator:MobileHK__c"),
                (string?) dict.GetValueOrDefault("salesforce-integrator:MobileCN__c"),
                (string?) dict.GetValueOrDefault("salesforce-integrator:MobileTW__c"),
            };
            var phoneNumber = GetPhoneNumber(providerConfig, phoneNumbers, countryCode);

            var emails = new List<string?>
            {
                (string?) dict.GetValueOrDefault("salesforce-integrator:Email")
            };
            var email = emails.Find(l => !string.IsNullOrWhiteSpace(l));

            return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(phoneNumber, email);
        }
        else if (providerName == "hubspot-integrator"
                 && entityTypeName == "Contact")
        {
            var phoneNumbers = new List<string?>
            {
                (string?) dict.GetValueOrDefault("hubspot-integrator:mobilephone"),
                (string?) dict.GetValueOrDefault("hubspot-integrator:phone"),

                // Jetour phone number DEVS-5104
                (string?) dict.GetValueOrDefault("hubspot-integrator:passenger_phone_1"),
            };
            var phoneNumber = GetPhoneNumber(providerConfig, phoneNumbers, null);

            var emails = new List<string?>
            {
                (string?) dict.GetValueOrDefault("hubspot-integrator:email"),
                (string?) dict.GetValueOrDefault("hubspot-integrator:work_email"),

                // Jetour phone number DEVS-5104
                (string?) dict.GetValueOrDefault("hubspot-integrator:custom_email"),
            };
            var email = emails.Find(l => !string.IsNullOrWhiteSpace(l));

            return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(phoneNumber, email);
        }
        else if (providerName == "hubspot-integrator" && entityTypeName == "User")
        {
            var emails = new List<string?>
            {
                (string?) dict.GetValueOrDefault("hubspot-integrator:email"),
            };
            var email = emails.Find(l => !string.IsNullOrWhiteSpace(l));

            return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(null, email);
        }
        else if (providerName == "sleekflow"
                 && (entityTypeName == "Contact" || entityTypeName == "Lead" || entityTypeName == "User"))
        {
            // Phone Numbers from Sleekflow have been parsed and in the correct format
            // We just need prefix with "+"
            var phoneNumbers = new List<string?>
            {
                (string?) dict.GetValueOrDefault("sleekflow:PhoneNumber"),
                (string?) dict.GetValueOrDefault("sleekflow:MobilePhone"),
                (string?) dict.GetValueOrDefault("sleekflow:OtherPhone"),
                (string?) dict.GetValueOrDefault("sleekflow:Phone"),
                (string?) dict.GetValueOrDefault("sleekflow:HomePhone"),
                (string?) dict.GetValueOrDefault("sleekflow:AssistantPhone"),
            };
            var phoneNumber = GetPhoneNumber(
                providerConfig,
                phoneNumbers.Select(pn => pn == null ? null : "+" + pn),
                null);

            var emails = new List<string?>
            {
                (string?) dict.GetValueOrDefault("sleekflow:Email"),
                (string?) dict.GetValueOrDefault("sleekflow:EmailAddress"),
            };
            var email = emails.Find(l => !string.IsNullOrWhiteSpace(l));

            return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(phoneNumber, email);
        }
        else if (providerName == Dynamics365IntegratorService.ProviderName
                 && entityTypeName == "Contact")
        {
            var countryCodes = new List<string?>
            {
                GetTwoLetterIsoRegionNameByCountryName(
                    (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:thk_country")),
                GetTwoLetterIsoRegionNameByCountryName(
                    (dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:new_APMCountryId") as JObject)
                    ?.GetValue("new_code")?.ToObject<string>()),
            };
            var countryCode = countryCodes.Find(l => !string.IsNullOrWhiteSpace(l));

            var phoneNumbers = new List<string?>
            {
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:mobilephone"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:telephone1"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:telephone2"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:telephone3"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address1_telephone1"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address1_telephone2"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address1_telephone3"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address2_telephone1"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address2_telephone2"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address2_telephone3"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address3_telephone1"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address3_telephone2"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:address3_telephone3"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:assistantphone"),
            };
            var phoneNumber = GetPhoneNumber(providerConfig, phoneNumbers, countryCode);

            var emails = new List<string?>
            {
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:emailaddress1"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:emailaddress2"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:emailaddress3"),
            };
            var email = emails.Find(l => !string.IsNullOrWhiteSpace(l));

            return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(phoneNumber, email);
        }
        else if (providerName == Dynamics365IntegratorService.ProviderName
                 && entityTypeName == "User")
        {
            var emails = new List<string?>
            {
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:internalemailaddress"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:domainname"),
                (string?) dict.GetValueOrDefault($"{Dynamics365IntegratorService.ProviderName}:windowsliveid"),
            };
            var email = emails.Find(l => !string.IsNullOrWhiteSpace(l));

            return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(null, email);
        }

        return new IProviderObjectIdentityResolverService.ProviderObjectIdentity(null, null);
    }

    private string? GetPhoneNumber(
        ProviderConfig? providerConfig,
        IEnumerable<string?> phoneNumbers,
        string? resolvedCountryCode)
    {
        var phoneNumberStr = phoneNumbers.FirstOrDefault(l => !string.IsNullOrWhiteSpace(l));
        if (phoneNumberStr == null)
        {
            return null;
        }

        try
        {
            var phoneNumberUtil = PhoneNumberUtil.GetInstance();

            if (phoneNumberStr.StartsWith("+"))
            {
                var phoneNumber = phoneNumberUtil.Parse(phoneNumberStr, null);
                var formattedPhoneNumberStr = $"{phoneNumber.CountryCode}{phoneNumber.NationalNumber}";

                return formattedPhoneNumberStr;
            }

            var defaultRegionCode = providerConfig == null ? "ZZ" : providerConfig.DefaultRegionCode;
            if (defaultRegionCode == "ZZ" && resolvedCountryCode == null)
            {
                var phoneNumber = phoneNumberUtil.Parse("+" + phoneNumberStr, null);
                var formattedPhoneNumberStr = $"{phoneNumber.CountryCode}{phoneNumber.NationalNumber}";

                return formattedPhoneNumberStr;
            }

            if (defaultRegionCode == "ZZ" && resolvedCountryCode != null)
            {
                var phoneNumber = phoneNumberUtil.Parse(phoneNumberStr, resolvedCountryCode);
                var formattedPhoneNumberStr = $"{phoneNumber.CountryCode}{phoneNumber.NationalNumber}";

                return formattedPhoneNumberStr;
            }
            else
            {
                var phoneNumber = phoneNumberUtil.Parse(phoneNumberStr, defaultRegionCode);
                var formattedPhoneNumberStr = $"{phoneNumber.CountryCode}{phoneNumber.NationalNumber}";

                return formattedPhoneNumberStr;
            }
        }
        catch (NumberParseException numberParseException)
        {
            _logger.LogWarning(
                numberParseException,
                "Caught numberParseException for {PhoneNumberStr}",
                phoneNumberStr);

            throw;
        }
    }

    private string? GetTwoLetterIsoRegionNameByIsoCurrencySymbol(string? isoCurrencySymbol)
    {
        if (isoCurrencySymbol == null)
        {
            return null;
        }

        var symbol = CultureInfo
            .GetCultures(CultureTypes.AllCultures)
            .Where(c => !c.IsNeutralCulture)
            .Select(
                culture =>
                {
                    try
                    {
                        return new RegionInfo(culture.Name);
                    }
                    catch
                    {
                        return null;
                    }
                })
            .Where(ri => ri != null && ri.ISOCurrencySymbol == isoCurrencySymbol)
            .Select(ri => ri!.TwoLetterISORegionName)
            .FirstOrDefault();

        return symbol;
    }

    private string? GetTwoLetterIsoRegionNameByCountryName(string? countryName)
    {
        if (countryName == null)
        {
            return null;
        }

        var symbol = CultureInfo
            .GetCultures(CultureTypes.AllCultures)
            .Where(c => !c.IsNeutralCulture)
            .Select(
                culture =>
                {
                    try
                    {
                        return new RegionInfo(culture.Name);
                    }
                    catch
                    {
                        return null;
                    }
                })
            .Where(
                ri => ri != null && (
                    string.Equals(ri.ThreeLetterISORegionName, countryName, StringComparison.InvariantCultureIgnoreCase)
                    || string.Equals(
                        ri.TwoLetterISORegionName,
                        countryName,
                        StringComparison.InvariantCultureIgnoreCase)
                    || ri.EnglishName.ToLowerInvariant().Contains(countryName.ToLowerInvariant())))
            .Select(ri => ri!.TwoLetterISORegionName)
            .FirstOrDefault();

        return symbol;
    }
}