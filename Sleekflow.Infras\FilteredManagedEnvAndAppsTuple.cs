using Pulumi;
using Sleekflow.Infras.Components;
using Sleekflow.Infras.Constants;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using Insights = Pulumi.AzureNative.Insights;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras;

public sealed class FilteredManagedEnvAndAppsTuple : ManagedEnvAndAppsTuple
{
    /// <summary>
    /// Only services in this list will be deployed to the global managed environment
    /// </summary>
    private List<string> ServiceNameFilters { get; set; }

    public FilteredManagedEnvAndAppsTuple(
        App.ManagedEnvironment managedEnvironment,
        Dictionary<string, App.ContainerApp> containerApps,
        Dictionary<string, Web.WebApp> workerApps,
        Insights.Component insightsComponent,
        OperationalInsights.Workspace logAnalyticsWorkspace,
        string name,
        string locationName,
        MyServiceBus.ServiceBusOutput serviceBus,
        MyServiceBus.ServiceBusOutput? highTrafficServiceBus,
        Cache.Redis redis,
        Cache.Redis schedulerRedis,
        MassTransitBlobStorage.MassTransitBlobStorageOutput massTransitBlobStorage,
        MyEventHub.EventHubOutput eventHub,
        List<string> serviceNameFilters)
        : base(
            managedEnvironment,
            containerApps,
            workerApps,
            insightsComponent,
            logAnalyticsWorkspace,
            name,
            locationName,
            serviceBus,
            highTrafficServiceBus,
            redis,
            schedulerRedis,
            massTransitBlobStorage,
            eventHub)
    {
        ServiceNameFilters = serviceNameFilters;
    }

    /// <summary>
    /// Determine if a service is part of this managed env or not.
    /// </summary>
    /// <param name="serviceName">The service name being tested for the filters.</param>
    /// <returns>Is part of this managed env or not.</returns>
    public override bool IsPartOfManagedEnv(string serviceName)
    {
        return ServiceNameFilters.Contains(serviceName);
    }

    /// <summary>
    /// Determine if a service is excluded from this managed env or not.
    /// </summary>
    /// <param name="serviceName">The service name being tested.</param>
    /// <returns>Is excluded from this managed env or not.</returns>
    public override bool IsExcludedFromManagedEnv(string serviceName)
    {
        return !IsPartOfManagedEnv(serviceName);
    }

    public override bool AreAllExcludedFromManagedEnv(params string[] serviceNames)
    {
        return serviceNames.All(IsExcludedFromManagedEnv);
    }

    /// <summary>
    /// Obtain the container app name for a service in this managed env.
    /// </summary>
    /// <param name="appName">The service app name.</param>
    /// <returns>Container app name for given environment.</returns>
    public override string FormatContainerAppName(string appName)
    {
        var locationShortName = LocationNames.GetShortName(LocationName);
        var suffix =
            Name == locationShortName || Name == "pri"
                ? string.Empty
                : "-" + Name;

        // e.g. sleekflow-ch-app-eus
        var containerAppName = $"sleekflow-{appName}-app-{locationShortName}";

        // e.g. sleekflow-ch-app-eus-sec
        return $"{containerAppName}{suffix}";
    }

    public override string FormatResourceName(string resourceName)
    {
        var locationShortName = LocationNames.GetShortName(LocationName);
        var suffix =
            Name == locationShortName || Name == "pri"
                ? string.Empty
                : "-" + Name;

        // e.g. sleekflow-ch-app-eus
        var nationalizedResourceName = $"{resourceName}-{locationShortName}";

        // e.g. sleekflow-ch-app-eus-sec
        return $"{nationalizedResourceName}{suffix}";
    }

    /// <summary>
    /// Format the managed env location for SF_ENVIRONMENT
    /// </summary>
    /// <param name="locationName">The location where the container app are hosted.</param>
    /// <returns>FormatSfEnvironment.</returns>
    protected override Output<string> FormatSfEnvironment(Output<string> locationName)
    {
        return base.FormatSfEnvironment(Output.Create(LocationName));
    }
}