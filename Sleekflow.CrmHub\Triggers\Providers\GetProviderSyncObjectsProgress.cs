﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderSyncObjectsProgress : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderSyncObjectsProgress(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderSyncObjectsProgressInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_state_id")]
        [Required]
        public string ProviderStateId { get; set; }

        [JsonConstructor]
        public GetProviderSyncObjectsProgressInput(
            string sleekflowCompanyId,
            string providerName,
            string providerStateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderStateId = providerStateId;
        }
    }

    public class GetProviderSyncObjectsProgressOutput
    {
        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public GetProviderSyncObjectsProgressOutput(
            int count,
            DateTime lastUpdateTime,
            string status)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            Status = status;
        }
    }

    public async Task<GetProviderSyncObjectsProgressOutput> F(
        GetProviderSyncObjectsProgressInput getProviderSyncObjectsProgressInput)
    {
        var providerService = _providerSelector.GetProviderService(getProviderSyncObjectsProgressInput.ProviderName);

        var getSyncObjectsProgressOutput = await providerService.GetSyncObjectsProgressAsync(
            getProviderSyncObjectsProgressInput.ProviderStateId,
            getProviderSyncObjectsProgressInput.SleekflowCompanyId);

        return new GetProviderSyncObjectsProgressOutput(
            getSyncObjectsProgressOutput.Count,
            getSyncObjectsProgressOutput.LastUpdateTime,
            getSyncObjectsProgressOutput.Status);
    }
}