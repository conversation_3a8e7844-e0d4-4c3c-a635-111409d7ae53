using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    null,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnContactLabelRelationshipsChangedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnContactLabelRelationshipsChangedEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnContactLabelRelationshipsChangedEventInput
        : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnContactLabelRelationshipsChangedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnContactLabelRelationshipsChangedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnContactLabelRelationshipsChangedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnContactLabelRelationshipsChangedEventOutput
    {
    }

    public async Task<OnContactLabelRelationshipsChangedEventOutput> F(
        OnContactLabelRelationshipsChangedEventInput onContactLabelRelationshipsChangedEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onContactLabelRelationshipsChangedEventInput.EventBody,
                onContactLabelRelationshipsChangedEventInput.ContactId,
                "Contact",
                onContactLabelRelationshipsChangedEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onContactLabelRelationshipsChangedEventInput.SleekflowCompanyId));

        return new OnContactLabelRelationshipsChangedEventOutput();
    }
}