using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopUpProfiles;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class UpsertWabaBalanceAutoTopUpProfile
    : ITrigger<UpsertWabaBalanceAutoTopUpProfile.UpsertWabaBalanceAutoTopUpProfileInput,
        UpsertWabaBalanceAutoTopUpProfile.UpsertWabaBalanceAutoTopUpProfileOutput>
{
    private readonly IWabaBalanceAutoTopUpProfileService _wabaBalanceAutoTopUpProfileService;

    public UpsertWabaBalanceAutoTopUpProfile(IWabaBalanceAutoTopUpProfileService wabaBalanceAutoTopUpProfileService)
    {
        _wabaBalanceAutoTopUpProfileService = wabaBalanceAutoTopUpProfileService;
    }

    public class UpsertWabaBalanceAutoTopUpProfileInput : IHasSleekflowCompanyId
    {
        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("waba_balance_auto_top_up_profile")]
        [Validations.ValidateObject]
        public WabaBalanceAutoTopUpProfileDto WabaBalanceAutoTopUpProfile { get; set; }

        [JsonProperty("customer_id")]
        public string? CustomerId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("credited_by")]
        public string CreditedBy { get; set; }

        [JsonProperty("credited_by_display_name")]
        public string? CreditedByDisplayName { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("redirect_to_url")]
        public string RedirectToUrl { get; set; }

        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpsertWabaBalanceAutoTopUpProfileInput(
            WabaBalanceAutoTopUpProfileDto wabaBalanceAutoTopUpProfile,
            string? customerId,
            string creditedBy,
            string? creditedByDisplayName,
            string redirectToUrl,
            string? phoneNumber,
            string sleekflowCompanyId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            WabaBalanceAutoTopUpProfile = wabaBalanceAutoTopUpProfile;
            CustomerId = customerId;
            CreditedBy = creditedBy;
            CreditedByDisplayName = creditedByDisplayName;
            RedirectToUrl = redirectToUrl;
            PhoneNumber = phoneNumber;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpsertWabaBalanceAutoTopUpProfileOutput
    {
        [JsonProperty("waba_balance_auto_top_up_profile")]
        public WabaBalanceAutoTopUpProfileDto WabaBalanceAutoTopUpProfile { get; set; }

        [JsonProperty("payment_url", NullValueHandling = NullValueHandling.Ignore)]
        public string? PaymentUrl { get; set; }

        [JsonConstructor]
        public UpsertWabaBalanceAutoTopUpProfileOutput(
            WabaBalanceAutoTopUpProfileDto wabaBalanceAutoTopUpProfile,
            string? paymentUrl)
        {
            WabaBalanceAutoTopUpProfile = wabaBalanceAutoTopUpProfile;
            PaymentUrl = paymentUrl;
        }
    }

    public async Task<UpsertWabaBalanceAutoTopUpProfileOutput> F(UpsertWabaBalanceAutoTopUpProfileInput input)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var result = await _wabaBalanceAutoTopUpProfileService.UpsertWabaBalanceAutoTopUpProfileAsync(
            input.WabaBalanceAutoTopUpProfile.FacebookWabaId,
            input.WabaBalanceAutoTopUpProfile.FacebookBusinessId,
            input.CustomerId,
            input.WabaBalanceAutoTopUpProfile.MinimumBalance,
            input.WabaBalanceAutoTopUpProfile.AutoTopUpPlan,
            input.WabaBalanceAutoTopUpProfile.IsAutoTopUpEnabled,
            input.SleekflowCompanyId,
            sleekflowStaff);

        string? paymentUrl = null;

        if (result.CustomerId is null)
        {
            paymentUrl = await _wabaBalanceAutoTopUpProfileService.GenerateWabaBalanceAutoTopUpProfilePaymentLinkAsync(
                input.SleekflowCompanyId,
                input.WabaBalanceAutoTopUpProfile.FacebookWabaId,
                input.WabaBalanceAutoTopUpProfile.FacebookBusinessId,
                input.CreditedBy,
                input.CreditedByDisplayName,
                input.RedirectToUrl,
                input.PhoneNumber,
                input.WabaBalanceAutoTopUpProfile.AutoTopUpPlan);
        }

        return new UpsertWabaBalanceAutoTopUpProfileOutput(
            new WabaBalanceAutoTopUpProfileDto(result),
            paymentUrl);
    }
}