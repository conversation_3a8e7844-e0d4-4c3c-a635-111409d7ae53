using System.Text;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.InternalIntegrationHub.Clients;
using Sleekflow.InternalIntegrationHub.Configs.OmniHr;
using Sleekflow.InternalIntegrationHub.Constants.OmniHr;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;
using Sleekflow.InternalIntegrationHub.Utils;

namespace Sleekflow.InternalIntegrationHub.OmniHr;

public interface IOmniHrIntegrationService
{
    public Task<List<OmniHrEmployee>> GetEmployeesAsync();

    public Task<CompensationCurrency?> GetCompensationCurrencyAsync(int id);

    public Task<EmployeeDetailsResponse> GetEmployeeDetailsAsync(int systemId);

    public Task<ManagerHistoryResponse> GetManagerHistoryAsync(int employeeId, int managerId);

    public Task<(StringBuilder, int)> GenerateImportEmployeeCsvAsync();
}

public class OmniHrIntegrationService : IOmniHrIntegrationService, IScopedService
{
    private readonly IOmniHrConfig _config;
    private readonly ILogger<OmniHrIntegrationService> _logger;
    private readonly ICacheService _cacheService;
    private readonly IExternalClient _client;

    private readonly Dictionary<string, string?> _defaultHeaders;

    public OmniHrIntegrationService(
        IOmniHrConfig config,
        ILogger<OmniHrIntegrationService> logger,
        ICacheService cacheService,
        IExternalClient client)
    {
        _logger = logger;
        _config = config;
        _cacheService = cacheService;
        _defaultHeaders = new Dictionary<string, string?>
        {
            {
                "x-subdomain", "sleekflow"
            }
        };
        _client = client;
    }

    private async Task<string> LoginAsync()
    {
        try
        {
            var accessToken = await _cacheService.CacheAsync(
                $"{nameof(OmniHrIntegrationService)}:{nameof(LoginAsync)}:accessToken",
                async () =>
                {
                    var username = _config.Username;
                    var password = _config.Password;
                    var requestBody = new LoginRequest(username, password);
                    var responseContent = await _client.PostAsync<LoginResponse>(
                        _config.BaseUrl,
                        Endpoints.LoginEndpoint,
                        requestBody,
                        headers: _defaultHeaders);
                    if (responseContent is null)
                    {
                        throw new SfUserFriendlyException("Failed to login to OmniHr");
                    }

                    if (_defaultHeaders.ContainsKey("Authorization") == false)
                    {
                        _defaultHeaders.Add("Authorization", $"Bearer {responseContent.Access}");
                    }

                    _logger.LogInformation("Login response: {0}", JsonConvert.SerializeObject(responseContent));
                    return responseContent.Access;
                },
                TimeSpan.FromSeconds(30));

            return accessToken;
        }
        catch (Exception ex)
        {
            throw new Exception("Failed to login to OmniHr", ex);
        }
    }

    public async Task<List<OmniHrEmployee>> GetEmployeesAsync()
    {
        await LoginAsync();
        var page = 1;
        var queryParams = new Dictionary<string, object>
        {
            {
                "employment_status", "1,2"
            },
            {
                "ordering", "display_first_name"
            },
            {
                "limit", 10
            },
            {
                "page", page
            }
        };
        if (!_defaultHeaders.ContainsKey("Authorization"))
        {
            var access = await LoginAsync();
            _defaultHeaders.Add("Authorization", $"Bearer {access}");
        }

        List<OmniHrEmployee> employees = [];
        while (true)
        {
            _logger.LogDebug("Getting employees from OmniHr, page: {0}", page * 10);
            var responseContent = await _client.GetAsync<EmployeeListResponse>(
                _config.BaseUrl,
                Endpoints.GetEmployeeListEndpoint,
                queryParams,
                headers: _defaultHeaders);

            if (responseContent is null)
            {
                throw new SfUserFriendlyException("Failed to get employee list from OmniHr");
            }

            employees.AddRange(responseContent.Results);
            if (responseContent.Next is null)
            {
                break;
            }

            queryParams["page"] = ++page;
        }

        return employees;
    }

    public async Task<CompensationCurrency?> GetCompensationCurrencyAsync(int id)
    {
        await LoginAsync();
        if (!_defaultHeaders.ContainsKey("Authorization"))
        {
            var access = await LoginAsync();
            _defaultHeaders.Add("Authorization", $"Bearer {access}");
        }

        var responseContent = await _client.GetAsync<List<UserDataSectionResponse>>(
            _config.BaseUrl,
            string.Format(Endpoints.GetUserDataSectionEndpoint, id),
            null,
            headers: _defaultHeaders);
        if (responseContent is null)
        {
            throw new SfUserFriendlyException("Failed to get user data section from OmniHr");
        }

        var currencySection = responseContent.Find(x => x.Name == "Compensation Currency");
        if (currencySection is null)
        {
            throw new SfUserFriendlyException("Failed to get compensation currency from OmniHr");
        }

        var currencyAttribute =
            JsonConvert.DeserializeObject<CompensationCurrencyAttribute>(currencySection.Attributes[0].ToString());

        return currencyAttribute?.ValueDisplay;
    }

    public async Task<EmployeeDetailsResponse> GetEmployeeDetailsAsync(int systemId)
    {
        await LoginAsync();
        if (!_defaultHeaders.ContainsKey("Authorization"))
        {
            var access = await LoginAsync();
            _defaultHeaders.Add("Authorization", $"Bearer {access}");
        }

        var responseContent = await _client.GetAsync<EmployeeDetailsResponse>(
            _config.BaseUrl,
            string.Format(Endpoints.GetEmployeeDetailsEndpoint, systemId),
            null,
            headers: _defaultHeaders);
        if (responseContent is null)
        {
            throw new SfUserFriendlyException("Failed to get employee details from OmniHr");
        }

        return responseContent;
    }

    public async Task<ManagerHistoryResponse> GetManagerHistoryAsync(int employeeId, int managerId)
    {
        await LoginAsync();
        if (!_defaultHeaders.ContainsKey("Authorization"))
        {
            var access = await LoginAsync();
            _defaultHeaders.Add("Authorization", $"Bearer {access}");
        }

        var responseContent = await _client.GetAsync<ManagerHistoryResponse>(
            _config.BaseUrl,
            string.Format(Endpoints.GetEmployeeManagerHistoryEndpoint, employeeId, managerId),
            null,
            headers: _defaultHeaders);
        if (responseContent is null)
        {
            throw new SfUserFriendlyException("Failed to get employee details from OmniHr");
        }

        return responseContent;
    }

    public async Task<(StringBuilder, int)> GenerateImportEmployeeCsvAsync()
    {
        var omniEmployees = await GetEmployeesAsync();
        var csv = new StringBuilder();
        csv.AppendLine("External ID,First Name,Last Name,Supervisor,Subsidiary,Is Sales Rep");

        foreach (var staff in omniEmployees)
        {
            var employeeDetail = await GetEmployeeDetailsAsync(staff.SystemId);
            var locationMap = OmniHrUtil.GetLocationMappings();
            var location = locationMap.FirstOrDefault(l => string.Equals(l.Key, staff.LocationName));

            if (employeeDetail.CurrentManagerHistorical.HasValue)
            {
                var manager = await GetManagerHistoryAsync(
                    staff.Id,
                    employeeDetail.CurrentManagerHistorical.Value);

                csv.AppendLine(
                    $"{staff.Id},{staff.FirstName},{staff.LastName},{manager.ManagerData?.FirstName} {manager.ManagerData?.LastName},Parent Company : {location.Value},{(employeeDetail.Department == "Sales" ? "Yes" : "No")}");
                continue;
            }

            csv.AppendLine(
                $"{staff.Id},{staff.FirstName},{staff.LastName},null,Parent Company : {location.Value},{(employeeDetail.Department == "Sales" ? "Yes" : "No")}");
        }

        return (csv, omniEmployees.Count);
    }
}