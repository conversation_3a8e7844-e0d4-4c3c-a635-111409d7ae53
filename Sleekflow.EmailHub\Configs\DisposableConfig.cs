using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Configs;

public interface IDisposableConfig
{
    string ServerName { get; }

    int Port { get; }

    string Username { get; }

    string Password { get; }
}

public class DisposableConfig : IDisposableConfig, IConfig
{
    public DisposableConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;
        ServerName = Environment.GetEnvironmentVariable("DISPOSABLE_SERVER_NAME", target)
            ?? throw new SfMissingEnvironmentVariableException("DISPOSABLE_SERVER_NAME");
        try
        {
            Port = int.Parse(Environment.GetEnvironmentVariable("DISPOSABLE_PORT", target));
        }
        catch (Exception)
        {
            throw new SfMissingEnvironmentVariableException("DISPOSABLE_PORT");
        }

        Username = Environment.GetEnvironmentVariable("DISPOSABLE_USERNAME", target)
                   ?? throw new SfMissingEnvironmentVariableException("DISPOSABLE_USERNAME");
        Password = Environment.GetEnvironmentVariable("DISPOSABLE_PASSWORD", target)
            ?? throw new SfMissingEnvironmentVariableException("DISPOSABLE_PASSWORD");
    }

    public string ServerName { get; }

    public int Port { get; }

    public string Username { get; }

    public string Password { get; }
}