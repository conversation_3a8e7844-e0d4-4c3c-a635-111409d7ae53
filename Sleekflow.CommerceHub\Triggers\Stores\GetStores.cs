using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class GetStores
    : ITrigger<
        GetStores.GetStoresInput,
        GetStores.GetStoresOutput>
{
    private readonly IStoreService _storeService;

    public GetStores(
        IStoreService storeService)
    {
        _storeService = storeService;
    }

    public class GetStoresInput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [ValidateObject]
        [JsonProperty(Store.PropertyNameIsViewEnabled)]
        public bool? IsViewEnabled { get; set; }

        [ValidateObject]
        [JsonProperty(Store.PropertyNameIsPaymentEnabled)]
        public bool? IsPaymentEnabled { get; set; }

        [ValidateObject]
        [JsonProperty("provider_name")]
        public string? ProviderName { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetStoresInput(
            string? continuationToken,
            string sleekflowCompanyId,
            bool? isViewEnabled,
            bool? isPaymentEnabled,
            string? providerName,
            int limit)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            IsViewEnabled = isViewEnabled;
            IsPaymentEnabled = isPaymentEnabled;
            ProviderName = providerName;
            Limit = limit;
        }
    }

    public class GetStoresOutput
    {
        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonProperty("stores")]
        public List<StoreDto> Stores { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonConstructor]
        public GetStoresOutput(
            string? nextContinuationToken,
            List<StoreDto> stores,
            int count)
        {
            NextContinuationToken = nextContinuationToken;
            Stores = stores;
            Count = count;
        }
    }

    public async Task<GetStoresOutput> F(GetStoresInput getCommerceStoresInput)
    {
        var (stores, nextContinuationToken) = await _storeService.GetStoresAsync(
            getCommerceStoresInput.ContinuationToken,
            getCommerceStoresInput.SleekflowCompanyId,
            getCommerceStoresInput.Limit,
            getCommerceStoresInput.IsViewEnabled,
            getCommerceStoresInput.IsPaymentEnabled,
            getCommerceStoresInput.ProviderName);

        return new GetStoresOutput(
            nextContinuationToken,
            stores.Select(x => new StoreDto(x)).ToList(),
            stores.Count);
    }
}