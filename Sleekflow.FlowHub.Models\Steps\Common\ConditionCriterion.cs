﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Steps.Common;

public class ConditionCriterion
{
    [Required]
    [JsonProperty("field_path")]
    public string FieldPath { get; set; }

    [Required]
    [JsonProperty("operator")]
    public string Operator { get; set; }

    [JsonProperty("values")]
    public List<object>? Values { get; set; }

    [JsonConstructor]
    public ConditionCriterion(
        string fieldPath,
        string @operator,
        List<object>? values)
    {
        FieldPath = fieldPath;
        Operator = @operator;
        Values = values;
    }
}