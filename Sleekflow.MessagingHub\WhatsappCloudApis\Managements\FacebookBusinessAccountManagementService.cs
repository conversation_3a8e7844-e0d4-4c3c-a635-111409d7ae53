using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Managements;

public interface IFacebookBusinessAccountManagementService
{
    Task<List<ManagementBusinessBalanceDto>> DisassociateFacebookBusinessAccountFromCompanyAsync(
        string facebookBusinessId,
        string sleekflowCompanyId,
        string sleekflowStaffId);
}

public class FacebookBusinessAccountManagementService : IFacebookBusinessAccountManagementService, IScopedService
{
    private readonly IWabaRepository _wabaRepository;
    private readonly ILogger<FacebookBusinessAccountManagementService> _logger;
    private readonly IAuditLogService _auditLogService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;

    public FacebookBusinessAccountManagementService(
        IWabaRepository wabaRepository,
        ILogger<FacebookBusinessAccountManagementService> logger,
        IAuditLogService auditLogService,
        IBusinessBalanceService businessBalanceService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService)
    {
        _wabaRepository = wabaRepository;
        _logger = logger;
        _auditLogService = auditLogService;
        _businessBalanceService = businessBalanceService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
    }

    public async Task<List<ManagementBusinessBalanceDto>> DisassociateFacebookBusinessAccountFromCompanyAsync(
        string facebookBusinessId,
        string sleekflowCompanyId,
        string sleekflowStaffId)
    {
        var wabas = await _wabaRepository.GetObjectsAsync(
            waba => waba.FacebookBusinessId == facebookBusinessId &&
                    waba.SleekflowCompanyIds.Contains(sleekflowCompanyId));

        return await DisassociateFacebookBusinessAccountWithWabas(wabas, facebookBusinessId, sleekflowCompanyId, sleekflowStaffId);
    }

    private async Task<List<ManagementBusinessBalanceDto>> DisassociateFacebookBusinessAccountWithWabas(
        List<Waba> wabas,
        string facebookBusinessId,
        string sleekflowCompanyId,
        string sleekflowStaffId)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance?.Balance.Amount < 0)
        {
            throw new SfFacebookBusinessNegativeBalanceException(
                $"Cannot disassociate the Facebook business account=[{facebookBusinessId}] from the company=[{sleekflowCompanyId}] because the balance is negative.",
                new Dictionary<string, object?>
                {
                    {
                        "facebookBusinessId", facebookBusinessId
                    },
                    {
                        "sleekflowCompanyId", sleekflowCompanyId
                    }
                });
        }

        var wabaPhoneNumberAssociatedCompanyIds = wabas.SelectMany(waba => waba.WabaPhoneNumbers).Select(number => number.SleekflowCompanyId).ToList();

        // if any of the phone number in wabas within the facebook business still has the company id, that means the company still connecting the channel under that waba
        if (wabaPhoneNumberAssociatedCompanyIds.Contains(sleekflowCompanyId))
        {
            throw new SfWabaPhoneNumberStillAssociatedException(
                $"Cannot disassociate the Facebook business account=[{facebookBusinessId}] from the company=[{sleekflowCompanyId}] because the waba phone number is associated.",
                new Dictionary<string, object?>
                {
                    {
                        "facebookBusinessId", facebookBusinessId
                    },
                    {
                        "sleekflowCompanyId", sleekflowCompanyId
                    }
                });
        }

        foreach (var waba in wabas)
        {
            var oldWaba = JsonConvert.DeserializeObject<Waba>(JsonConvert.SerializeObject(waba));

            if (oldWaba == null)
            {
                continue;
            }

            waba.SleekflowCompanyIds.Remove(sleekflowCompanyId);

            // If no waba phone number (channel) within the facebook business account is connected in the company, then we can remove the company id from the waba_product_catalog
            if (waba.WabaProductCatalog?.SleekflowCompanyId == sleekflowCompanyId)
            {
                waba.WabaProductCatalog.SleekflowCompanyId = null;
            }

            var isReplaced = await _wabaRepository.ReplaceWabaAsync(oldWaba, waba);

            if (isReplaced)
            {
                await _auditLogService.AuditWabaAsync(
                    waba,
                    waba.FacebookWabaId,
                    sleekflowCompanyId,
                    AuditingOperation.DisassociateFacebookBusinessAccountFromCompany,
                    JsonConvertExtensions.ToDictionary(
                        new Dictionary<string, object>
                        {
                            {
                                "changes", wabas
                            },
                            {
                                "sleekflow_staff_id", sleekflowStaffId
                            }
                        }));
            }
        }

        var businessBalanceAndWabasTuples =
            await _businessBalanceService.GetManagementBusinessBalancesFromCompanyAsync(sleekflowCompanyId);

        var managementBusinessBalances = (await Task.WhenAll(
            businessBalanceAndWabasTuples
                .Select(
                    async b =>
                    {
                        var (retrievedBusinessBalance, retrievedWabas) = b;

                        var unCalculatedCreditTransferTransactionLogs =
                            await _wabaLevelCreditManagementService.GetUnCalculatedCreditTransferTransactionLogsAsync(
                                retrievedBusinessBalance);

                        var businessBalanceDto = new ManagementBusinessBalanceDto(
                            retrievedBusinessBalance,
                            retrievedWabas,
                            unCalculatedCreditTransferTransactionLogs);

                        return businessBalanceDto;
                    }))).ToList();

        return managementBusinessBalances;
    }
}