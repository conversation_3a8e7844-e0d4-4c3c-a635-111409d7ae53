using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps.Actions;

public class ExitCondition
{
    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }
}

public class ExitConversationAction : BaseAction
{
    [JsonProperty("conditions")]
    public List<ExitCondition> Conditions { get; set; }
}
