﻿using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.Subscriptions;

public interface IZohoSubscriptionRepository : IRepository<ZohoSubscription>
{
}

public class ZohoSubscriptionRepository
    : BaseRepository<ZohoSubscription>,
        IZohoSubscriptionRepository
{
    public ZohoSubscriptionRepository(
        ILogger<BaseRepository<ZohoSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}