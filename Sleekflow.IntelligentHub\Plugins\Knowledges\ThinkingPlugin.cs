using System.ComponentModel;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Models;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class ThinkingPluginResponse : QueryKnowledgeResponse
{
    public ThinkingPluginResponse(string knowledge, string id)
        : base(knowledge, id, "Thinking")
    {
    }
}

public interface IThinkingPlugin
{
    [KernelFunction("think_and_apply")]
    [Description(
        "Think how to utilize the information from the knowledge base.")]
    [return: Description(
        "Synthesized information from the knowledge base.")]
    Task<ThinkingPluginResponse> ThinkAndApplyAsync(
        Kernel kernel,
        [Description("The topic to think about.")]
        string topic);
}

public class ThinkingPlugin : IThinkingPlugin, IScopedService
{
    private readonly ILogger _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly IAgenticKnowledgePluginStorageService _agenticKnowledgePluginStorageService;

    public ThinkingPlugin(
        ILogger<ThinkingPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgenticKnowledgePluginStorageService agenticKnowledgePluginStorageService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _agenticKnowledgePluginStorageService = agenticKnowledgePluginStorageService;
    }

    [KernelFunction("think_and_apply")]
    [Description(
        "Think how to utilize the information from the knowledge base.")]
    [return: Description(
        "Synthesized information from the knowledge base.")]
    public async Task<ThinkingPluginResponse> ThinkAndApplyAsync(
        Kernel kernel,
        [Description("The topic to think about.")]
        string topic)
    {
        var result = await InternalThinkAndApplyAsync(kernel, topic);

        return new ThinkingPluginResponse(
            $"<CONFIRMED_KNOWLEDGE>{result}</CONFIRMED_KNOWLEDGE>",
            Guid.NewGuid().ToString());
    }

    private async Task<string> InternalThinkAndApplyAsync(
        Kernel kernel,
        string topic)
    {
        var composedContext =
            $$"""
              ==== Original User Query ====
              {{topic}}
              ==== End Query ====

              ==== Source Knowledge ====
              {{_agenticKnowledgePluginStorageService.GetAllToolResults()}}
              ==== End Source Knowledge ====
              """;

#pragma warning disable SKEXP0070
        var promptExecutionSettings =
            (GeminiPromptExecutionSettings) _promptExecutionSettingsService.GetPromptExecutionSettings(
                SemanticKernelExtensions.S_FLASH_2_5);
        promptExecutionSettings.ThinkingConfig = new GeminiThinkingConfig
        {
            ThinkingBudget = 1024,
        };
#pragma warning restore SKEXP0070

        var finalOutput = await SummarizeAsync(
            kernel,
            "ThinkAndApply",
            """
            **Role:** You are an expert strategist and problem-solver.

            **Task:**

            Analyze the user's `Original User Query` and the provided `Synthesized Knowledge Summary` (along with its `Knowledge Coverage & Limitations`).
            Your goal is to go beyond simple explanation and **brainstorm actionable insights, potential strategies, or illustrative scenarios** demonstrating how the knowledge can be *applied* to address the user's underlying goal or situation.

            **Rules:**

            1.  **Consider Limitations:** Pay attention to the `Knowledge Coverage & Limitations`. If the knowledge is incomplete or provides alternatives, factor this into your thinking. How do these limitations affect potential strategies?
            2.  **Structure and Clarity:** Organize your Thoughts logically. Use headings, bullet points, or numbered lists for clarity.
            3.  **Grounding:** Ensure your thinking is **directly inspired by and grounded in** the provided `Synthesized Knowledge Summary` and `Coverage`. Do **not** introduce external information or make unsupported claims.
            4.  **Focus:** Add value by showing *how* the knowledge can be used, not just repeating it.

            ---

            {{$DATA}}
            """,
            topic,
            composedContext,
            promptExecutionSettings);

        return finalOutput;
    }

    private async Task<string> SummarizeAsync(
        Kernel kernel,
        string name,
        string prompt,
        string query,
        string data,
        PromptExecutionSettings promptExecutionSettings)
    {
        var summarizeFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = name,
                Template = prompt,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "QUERY", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "DATA", IsRequired = true
                    }
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "A summarized result relevant to the query."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                },
            });

        // Invoke the function with the provided input
        try
        {
            var chatMessageContent = await summarizeFunction.InvokeAsync<ChatMessageContent>(
                kernel,
                new KernelArguments(promptExecutionSettings)
                {
                    {
                        "DATA", data
                    },
                    {
                        "QUERY", query
                    }
                });

            if (chatMessageContent is OpenAIChatMessageContent { InnerContent: ChatCompletion chatCompletion })
            {
                _logger.LogInformation(
                    """
                    ----------------------- Summarize ({Name}) ------------------------
                    TokenUsage: {InputTokenUsage}; {OutputTokenUsage}; {ReasoningTokenUsage}
                    """,
                    name,
                    chatCompletion.Usage.InputTokenCount,
                    chatCompletion.Usage.OutputTokenCount - chatCompletion.Usage.OutputTokenDetails.ReasoningTokenCount,
                    chatCompletion.Usage.OutputTokenDetails.ReasoningTokenCount);
            }

            return chatMessageContent?.Content ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing data");
            return string.Empty;
        }
    }
}