﻿using Sleekflow.CommerceHub.Models.Vtex.Dtos;
using Sleekflow.Events;

namespace Sleekflow.CommerceHub.Models.Events;

public class OnVtexOrderHookReceivedEvent : IEvent
{
    public string SleekflowCompanyId { get; set; }

    public string VtexAuthenticationId { get; set; }

    public VtexOrderHookDto VtexOrderHook { get; set; }

    public OnVtexOrderHookReceivedEvent(string sleekflowCompanyId, string vtexAuthenticationId, VtexOrderHookDto vtexOrderHook)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        VtexAuthenticationId = vtexAuthenticationId;
        VtexOrderHook = vtexOrderHook;
    }
}