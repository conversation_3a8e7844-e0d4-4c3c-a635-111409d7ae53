using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class StaffAddedAsCollaboratorSystemLogData
{
    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [JsonProperty("user_profile_id")]
    public string UserProfileId { get; set; }

    [JsonProperty("user_profile_first_name")]
    public string UserProfileFirstName { get; set; }

    [JsonProperty("user_profile_last_name")]
    public string UserProfileLastName { get; set; }

    [JsonConstructor]
    public StaffAddedAsCollaboratorSystemLogData(string staffId, string conversationId, string userProfileId,
        string userProfileFirstName, string userProfileLastName)
    {
        StaffId = staffId;
        ConversationId = conversationId;
        UserProfileId = userProfileId;
        UserProfileFirstName = userProfileFirstName;
        UserProfileLastName = userProfileLastName;
    }
}