﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class TriggerConfigDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("trigger_name")]
    public string TriggerName { get; set; }

    [JsonProperty("trigger_group")]
    public string TriggerGroup { get; set; }

    [JsonProperty("trigger_description")]
    public string TriggerDescription { get; set; }

    [JsonProperty("is_external_integration")]
    public bool IsExternalIntegration { get; set; }

    [JsonProperty("condition_arg_name")]
    public string ConditionArgName { get; set; }

    [JsonConstructor]
    public TriggerConfigDto(
        string id,
        string triggerName,
        string triggerGroup,
        string triggerDescription,
        bool isExternalIntegration,
        string conditionArgName)
    {
        Id = id;
        TriggerName = triggerName;
        TriggerGroup = triggerGroup;
        TriggerDescription = triggerDescription;
        IsExternalIntegration = isExternalIntegration;
        ConditionArgName = conditionArgName;
    }

    public TriggerConfigDto(TriggerConfig config)
    {
        Id = config.Id;
        TriggerName = config.TriggerName;
        TriggerGroup = config.TriggerGroup;
        TriggerDescription = config.TriggerDescription;
        IsExternalIntegration = config.IsExternalIntegration;
        ConditionArgName = config.ConditionArgName;
    }
}