using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using OpenAI.Chat;
using Polly;
using Sleekflow.IntelligentHub.Models.Constants;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Kernels;

public class MyChatCompletionService : IChatCompletionService
{
    private readonly ILogger<MyChatCompletionService> _logger;
    private readonly IChatCompletionService _chatCompletionService;
    private readonly ITokenCountingService _tokenCountingService;

    public MyChatCompletionService(
        ILogger<MyChatCompletionService> logger,
        IChatCompletionService chatCompletionService,
        ITokenCountingService tokenCountingService)
    {
        _logger = logger;
        _chatCompletionService = chatCompletionService;
        _tokenCountingService = tokenCountingService;
    }

    public async Task<IReadOnlyList<ChatMessageContent>> GetChatMessageContentsAsync(
        ChatHistory chatHistory,
        PromptExecutionSettings? executionSettings = null,
        Kernel? kernel = null,
        CancellationToken cancellationToken = default)
    {
        var retryPolicy = Policy
            .Handle<Exception>(exception =>
            {
                try
                {
                    var exceptionStr = exception.ToString();

                    return exceptionStr.Contains("System.Net.Sockets.SocketException")
                           || exceptionStr.Contains("System.Net.Http.HttpRequestException")
                           || exceptionStr.Contains("System.IO.IOException");
                }
                catch (Exception e)
                {
                    _logger.LogWarning(e, "Unable to determine exception type");
                }

                return false;
            })
            .WaitAndRetryAsync(
                2,
                sleepDurationProvider: (retryCount, _, _) =>
                    TimeSpan.FromSeconds(2.37) * retryCount * new Random().Next(1, 3),
                onRetryAsync: (e, timeSpan, retryCount, _) =>
                {
                    _logger.LogWarning(
                        e,
                        "Retrying {Times} after {TimeSpan} due to {ExceptionType}: {ExceptionMessage}",
                        retryCount,
                        timeSpan,
                        e.GetType().Name,
                        e.Message);

                    return Task.CompletedTask;
                });

        var myChatHistory = GetMyChatHistory(chatHistory, executionSettings);

        var policyResult =
            await retryPolicy.ExecuteAndCaptureAsync(async () =>
                await _chatCompletionService.GetChatMessageContentsAsync(
                    myChatHistory,
                    executionSettings,
                    kernel,
                    cancellationToken));

        if (policyResult.FinalException != null)
        {
            throw policyResult.FinalException;
        }

        var chatMessageContents = policyResult.Result;

        var chatMessageContent = chatMessageContents.LastOrDefault();

        var groupChatIdStr = kernel!.Data.TryGetValue(KernelDataKeys.GROUP_CHAT_ID, out var value)
            ? (string?) value
            : null;

        if (groupChatIdStr is not null && executionSettings is not null)
        {
            HandleGeminiChatCompletion(groupChatIdStr, executionSettings, chatMessageContent);
            HandleOpenAiChatCompletion(groupChatIdStr, executionSettings, chatMessageContent);
        }

        return chatMessageContents;
    }

    // Gemini does not support function calling from OpenAI
    // Gemini treats function calling as a normal assistant message
    private static ChatHistory GetMyChatHistory(ChatHistory chatHistory, PromptExecutionSettings? executionSettings)
    {
#pragma warning disable SKEXP0070
        if (executionSettings is GeminiPromptExecutionSettings geminiPromptExecutionSettings)
#pragma warning restore SKEXP0070
        {
            var myChatHistory = chatHistory;

            return new ChatHistory(
                myChatHistory
                    .Select(c =>
                    {
                        if (c is ChatMessageContent chatMessageContent
                            && c.Items.Any(i => i is FunctionResultContent))
                        {
                            var chatMessageContentItemCollection = new ChatMessageContentItemCollection();

                            foreach (var item in c.Items)
                            {
                                if (item is FunctionResultContent)
                                {
                                    // FunctionResultContent is not supported in gemini
                                }
                                else
                                {
                                    chatMessageContentItemCollection.Add(item);
                                }
                            }

                            return new ChatMessageContent(
                                AuthorRole.Assistant,
                                chatMessageContentItemCollection);
                        }

                        return c;
                    })
                    .Where(c =>
                    {
                        if (c is ChatMessageContent chatMessageContent
                            && string.IsNullOrEmpty(chatMessageContent.Content)
                            && chatMessageContent.Items.Count == 0)
                        {
                            return false;
                        }

                        if (c is OpenAIChatMessageContent openAiChatMessageContent
                            && openAiChatMessageContent.Items.Any(i => i is FunctionCallContent))
                        {
                            return false;
                        }

                        return true;
                    })
                    .ToList());
        }

        return chatHistory;
    }

    private void HandleGeminiChatCompletion(
        string groupChatIdStr,
        PromptExecutionSettings promptExecutionSettings,
        ChatMessageContent? chatMessageContent)
    {
#pragma warning disable SKEXP0070
        if (chatMessageContent is GeminiChatMessageContent geminiChatMessageContent)
        {
            var name = promptExecutionSettings.ServiceId ?? "gemini";

            var totalTokenCount = geminiChatMessageContent.Metadata?.TotalTokenCount ?? 0;
            var promptTokenCount = geminiChatMessageContent.Metadata?.PromptTokenCount ?? 0;
            var outputTokenCount = totalTokenCount - promptTokenCount;

            // TODO print(response.text))
            // print(response.usage_metadata.thoughts_token_count) #Output thought token count
            // print(response.usage_metadata.total_token_count) #Output thought and response token count

            _tokenCountingService.AddToken(groupChatIdStr, name + "_Input", promptTokenCount);
            _tokenCountingService.AddToken(groupChatIdStr, name + "_Output", outputTokenCount);
        }
#pragma warning restore SKEXP0070
    }

    private void HandleOpenAiChatCompletion(
        string groupChatIdStr,
        PromptExecutionSettings promptExecutionSettings,
        ChatMessageContent? chatMessageContent)
    {
        if (chatMessageContent is OpenAIChatMessageContent
            {
                InnerContent: ChatCompletion chatCompletion
            })
        {
            var name = promptExecutionSettings.ServiceId ?? "openai";

            _tokenCountingService.AddToken(
                groupChatIdStr,
                name + "_Input",
                chatCompletion.Usage.InputTokenCount - chatCompletion.Usage.InputTokenDetails.CachedTokenCount);
            _tokenCountingService.AddToken(
                groupChatIdStr,
                name + "_Input_Cached",
                chatCompletion.Usage.InputTokenDetails.CachedTokenCount);
            _tokenCountingService.AddToken(
                groupChatIdStr,
                name + "_Output",
                chatCompletion.Usage.OutputTokenCount
                - chatCompletion.Usage.OutputTokenDetails?.ReasoningTokenCount ?? 0);
            _tokenCountingService.AddToken(
                groupChatIdStr,
                name + "_ReasoningOutput",
                chatCompletion.Usage.OutputTokenDetails?.ReasoningTokenCount ?? 0);
        }
    }

    public IAsyncEnumerable<StreamingChatMessageContent> GetStreamingChatMessageContentsAsync(
        ChatHistory chatHistory,
        PromptExecutionSettings? executionSettings = null,
        Kernel? kernel = null,
        CancellationToken cancellationToken = default)
    {
        return _chatCompletionService.GetStreamingChatMessageContentsAsync(
            chatHistory,
            executionSettings,
            kernel,
            cancellationToken);
    }

    public IReadOnlyDictionary<string, object?> Attributes => _chatCompletionService.Attributes;
}