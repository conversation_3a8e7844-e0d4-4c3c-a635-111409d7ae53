using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class OnRecommendedReplyGenerationFinishedEvent
{
    [JsonProperty("recommended_reply_step_id")]
    public string RecommendReplyStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("final_sequence_number")]
    public int FinalSequenceNumber { get; set; }

    [JsonProperty("confidence_scoring")]
    public int ConfidenceScoring { get; set; }

    [JsonConstructor]
    public OnRecommendedReplyGenerationFinishedEvent(
        string recommendReplyStepId,
        string proxyStateId,
        int finalSequenceNumber,
        int confidenceScoring)
    {
        RecommendReplyStepId = recommendReplyStepId;
        ProxyStateId = proxyStateId;
        FinalSequenceNumber = finalSequenceNumber;
        ConfidenceScoring = confidenceScoring;
    }
}