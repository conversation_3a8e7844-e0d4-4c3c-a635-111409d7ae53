# Chat History Enricher for Agent Collaboration

## Overview

This proposal outlines a design for a flexible ChatHistoryEnricher system that enhances the context provided to AI agents during conversation initialization. The system will allow dynamic enrichment of chat history with external data sources (like CRM data, meeting records, etc.) to provide agents with more comprehensive context before they generate responses.

## Goals

- Create a flexible enrichment framework that can be configured per company
- Support multiple enrichers that can be enabled/disabled independently
- Implement a factory pattern for easy creation of enrichers
- Add Hubspot as the first enricher integration
- Ensure data privacy by allowing configuration of what data can be exposed
- Minimize latency impact on response generation

## Considerations

- **Performance**: Enrichers should operate in parallel where possible to minimize latency
- **Privacy**: Only specified contact properties should be exposed to LLMs
- **Extensibility**: Design must make it easy to add new enrichers
- **Configuration**: Companies should be able to customize which enrichers are active
- **Failure Handling**: System should gracefully handle failures of individual enrichers

## High-Level Architecture

```mermaid
classDiagram
    class IChatHistoryEnricher {
        <<interface>>
        +EnrichAsync(context, contactProperties, kernel) Task~string~
        +GetContextSectionName() string
    }

    class ChatHistoryEnricherFactory {
        <<interface>>
        +CreateEnrichers(companyConfig, kernel) List~IChatHistoryEnricher~
    }

    class ChatHistoryEnricherFactoryImpl {
        +CreateEnrichers(companyConfig, kernel) List~IChatHistoryEnricher~
    }

    class BaseAgentCollaborationDefinition {
        +InitializeChatHistoryAsync(...) Task~string~
    }

    class HubspotEnricher {
        -_hubspotPlugin HubspotPlugin
        +EnrichAsync(context, contactProperties, kernel) Task~string~
        +GetContextSectionName() string
    }

    class CompanyAgentConfig {
        +EnricherConfigs List~EnricherConfig~
    }

    class EnricherConfig {
        +Type string
        +IsEnabled bool
        +Parameters Dictionary~string,string~
    }

    ChatHistoryEnricherFactory <|.. ChatHistoryEnricherFactoryImpl : implements
    BaseAgentCollaborationDefinition --> ChatHistoryEnricherFactory : uses
    ChatHistoryEnricherFactoryImpl --> IChatHistoryEnricher : creates
    HubspotEnricher ..|> IChatHistoryEnricher : implements
    ChatHistoryEnricherFactoryImpl --> CompanyAgentConfig : reads config from
    CompanyAgentConfig *-- EnricherConfig : contains
```

## Detailed Design

### 1. Core Interfaces and Models

#### File: `Sleekflow.IntelligentHub.Models/Enrichers/IChatHistoryEnricher.cs`

```csharp
using Microsoft.SemanticKernel;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Enrichers;

/// <summary>
/// Interface for chat history enrichers that add external data to agent conversations
/// </summary>
public interface IChatHistoryEnricher
{
    /// <summary>
    /// Enriches the chat context with external data
    /// </summary>
    /// <param name="context">The reply generation context</param>
    /// <param name="contactProperties">Contact properties from the chat</param>
    /// <param name="kernel">The semantic kernel instance</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Formatted string with enriched data or error message</returns>
    Task<string> EnrichAsync(
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the name of the section in the context message
    /// </summary>
    /// <returns>Section name to be used in the context message</returns>
    string GetContextSectionName();
}
```

#### File: `Sleekflow.IntelligentHub.Models/Companies/CompanyAgentConfigs/EnricherConfig.cs`

```csharp
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

/// <summary>
/// Configuration for a chat history enricher
/// </summary>
public class EnricherConfig
{
    /// <summary>
    /// The type of enricher (e.g., "Hubspot", "Salesforce")
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Whether this enricher is enabled
    /// </summary>
    [JsonProperty("is_enabled")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Parameters specific to this enricher type (e.g., API keys, endpoint URLs)
    /// </summary>
    [JsonProperty("parameters")]
    public Dictionary<string, string> Parameters { get; set; } = new();
}
```

#### File: `Sleekflow.IntelligentHub.Models/Constants/EnricherDataKeys.cs`

```csharp
namespace Sleekflow.IntelligentHub.Models.Constants;

/// <summary>
/// Constants for enricher-specific kernel data dictionary keys
/// </summary>
public static class EnricherDataKeys
{
    /// <summary>
    /// Root key for all enricher-related data
    /// </summary>
    public const string ENRICHER_ROOT = "ENRICHER_DATA";

    /// <summary>
    /// Hubspot API key and settings
    /// </summary>
    public const string HUBSPOT_SETTINGS = "HUBSPOT_SETTINGS";
}
```

### 2. Updates to CompanyAgentConfig

#### File: `Sleekflow.IntelligentHub.Models/Companies/CompanyAgentConfigs/CompanyAgentConfig.cs`

Extend the CompanyAgentConfig class to include enricher configurations:

```csharp
public class CompanyAgentConfig
{
    // Existing properties...

    /// <summary>
    /// Configurations for chat history enrichers
    /// </summary>
    [JsonProperty("enricher_configs")]
    public List<EnricherConfig> EnricherConfigs { get; set; } = new();
}
```

### 3. Factory Implementation

#### File: `Sleekflow.IntelligentHub/FaqAgents/Chats/Enrichers/IChatHistoryEnricherFactory.cs`

```csharp
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Enrichers;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Factory interface for creating chat history enrichers
/// </summary>
public interface IChatHistoryEnricherFactory
{
    /// <summary>
    /// Creates enrichers based on company configuration
    /// </summary>
    /// <param name="companyConfig">The company agent configuration</param>
    /// <param name="kernel">The semantic kernel instance</param>
    /// <returns>List of chat history enrichers</returns>
    List<IChatHistoryEnricher> CreateEnrichers(
        CompanyAgentConfig companyConfig,
        Kernel kernel);
}
```

#### File: `Sleekflow.IntelligentHub/FaqAgents/Chats/Enrichers/ChatHistoryEnricherFactory.cs`

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Enrichers;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Factory for creating chat history enrichers based on configuration
/// </summary>
public class ChatHistoryEnricherFactory : IChatHistoryEnricherFactory, ISingletonService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ChatHistoryEnricherFactory> _logger;

    public ChatHistoryEnricherFactory(
        IServiceProvider serviceProvider,
        ILogger<ChatHistoryEnricherFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// Creates enrichers based on company configuration
    /// </summary>
    /// <param name="companyConfig">The company agent configuration</param>
    /// <param name="kernel">The semantic kernel instance</param>
    /// <returns>List of chat history enrichers</returns>
    public List<IChatHistoryEnricher> CreateEnrichers(
        CompanyAgentConfig companyConfig,
        Kernel kernel)
    {
        var enrichers = new List<IChatHistoryEnricher>();

        if (companyConfig.EnricherConfigs == null || !companyConfig.EnricherConfigs.Any())
        {
            _logger.LogInformation("No enricher configs found in company config");
            return enrichers;
        }

        // Initialize enricher data dictionary if not exists
        if (!kernel.Data.ContainsKey(EnricherDataKeys.ENRICHER_ROOT))
        {
            kernel.Data[EnricherDataKeys.ENRICHER_ROOT] = new Dictionary<string, object>();
        }

        var enricherData = kernel.Data[EnricherDataKeys.ENRICHER_ROOT] as Dictionary<string, object>
            ?? new Dictionary<string, object>();

        foreach (var config in companyConfig.EnricherConfigs.Where(c => c.IsEnabled))
        {
            try
            {
                switch (config.Type.ToLowerInvariant())
                {
                    case "hubspot":
                        // Get or create HubspotPlugin
                        var hubspotPlugin = _serviceProvider.GetRequiredService<HubspotPlugin>();

                        // If there's an API key in the config, store it in the kernel data
                        if (config.Parameters.TryGetValue("ApiKey", out var apiKey))
                        {
                            // Create HubspotSettings
                            var hubspotSettings = new HubspotSettings { HubspotApiKey = apiKey };

                            // Store in enricher data dictionary
                            enricherData[EnricherDataKeys.HUBSPOT_SETTINGS] = hubspotSettings;

                            // Also store in the original TOOLS key for compatibility with existing code
                            kernel.Data[KernelDataKeys.TOOLS] = hubspotSettings;
                        }

                        var hubspotLogger = _serviceProvider.GetRequiredService<ILogger<HubspotEnricher>>();
                        enrichers.Add(new HubspotEnricher(hubspotPlugin, hubspotLogger));
                        _logger.LogInformation("Created Hubspot enricher");
                        break;

                    // Add cases for other enricher types
                    default:
                        _logger.LogWarning("Unknown enricher type: {Type}", config.Type);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating enricher of type {Type}", config.Type);
            }
        }

        // Update the kernel data with any changes
        kernel.Data[EnricherDataKeys.ENRICHER_ROOT] = enricherData;

        return enrichers;
    }
}
```

### 4. Hubspot Enricher Implementation

#### File: `Sleekflow.IntelligentHub/FaqAgents/Chats/Enrichers/HubspotEnricher.cs`

```csharp
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Enrichers;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Enriches chat context with data from Hubspot CRM
/// </summary>
public class HubspotEnricher : IChatHistoryEnricher
{
    private readonly HubspotPlugin _hubspotPlugin;
    private readonly ILogger<HubspotEnricher> _logger;

    public HubspotEnricher(
        HubspotPlugin hubspotPlugin,
        ILogger<HubspotEnricher>? logger = null)
    {
        _hubspotPlugin = hubspotPlugin;
        _logger = logger ?? NullLoggerFactory.Instance.CreateLogger<HubspotEnricher>();
    }

    /// <summary>
    /// Gets the name of the section in the context message
    /// </summary>
    public string GetContextSectionName() => "HUBSPOT DATA";

    /// <summary>
    /// Enriches chat context with Hubspot contact data and meeting history
    /// </summary>
    public async Task<string> EnrichAsync(
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default)
    {
        if (contactProperties == null || !contactProperties.Any())
        {
            _logger.LogInformation("No contact properties available for Hubspot lookup");
            return "No contact properties available for Hubspot lookup";
        }

        try
        {
            string? email = null;
            string? phone = null;

            // Extract email and phone from contact properties
            if (contactProperties.TryGetValue("email", out var emailValue))
            {
                email = emailValue;
                _logger.LogDebug("Found email: {Email}", email);
            }

            if (contactProperties.TryGetValue("phone", out var phoneValue))
            {
                phone = phoneValue;
                _logger.LogDebug("Found phone: {Phone}", phone);
            }

            if (string.IsNullOrEmpty(email) && string.IsNullOrEmpty(phone))
            {
                _logger.LogInformation("No email or phone available for Hubspot lookup");
                return "No email or phone available for Hubspot lookup";
            }

            // Ensure Hubspot settings are available in kernel data
            if (!EnsureHubspotSettings(kernel))
            {
                return "Hubspot API key not configured";
            }

            // Get contact properties
            _logger.LogInformation("Retrieving Hubspot contact properties for email: {Email}, phone: {Phone}",
                email ?? "N/A", phone ?? "N/A");
            var contactPropertiesJson = await _hubspotPlugin.GetContactPropertiesAsync(
                kernel, email, phone, cancellationToken);

            // Get meetings
            _logger.LogInformation("Retrieving Hubspot meetings for email: {Email}, phone: {Phone}",
                email ?? "N/A", phone ?? "N/A");
            var meetingsJson = await _hubspotPlugin.GetContactMeetingsAsync(
                kernel, email, phone, cancellationToken);

            // Format the response
            var response = new StringBuilder();

            if (!string.IsNullOrEmpty(contactPropertiesJson))
            {
                // Parse and format contact properties for better readability
                try
                {
                    var contactObject = JObject.Parse(contactPropertiesJson);
                    var properties = contactObject["properties"] as JObject;

                    response.AppendLine("CONTACT INFORMATION:");
                    if (properties != null)
                    {
                        // Format selected important properties
                        var formattedProperties = new Dictionary<string, string>();
                        foreach (var prop in properties)
                        {
                            formattedProperties[prop.Key] = prop.Value?["value"]?.ToString() ?? string.Empty;
                        }

                        response.AppendLine(JsonConvert.SerializeObject(formattedProperties, Formatting.Indented));
                    }
                    else
                    {
                        response.AppendLine(contactPropertiesJson);
                    }
                }
                catch (JsonException)
                {
                    // Fall back to raw JSON if parsing fails
                    response.AppendLine(contactPropertiesJson);
                }

                response.AppendLine();
            }

            if (meetingsJson != "[]")
            {
                // Parse and format meetings for better readability
                try
                {
                    var meetings = JArray.Parse(meetingsJson);

                    response.AppendLine("MEETING HISTORY:");
                    foreach (var meeting in meetings)
                    {
                        var properties = meeting["properties"] as JObject;
                        if (properties != null)
                        {
                            response.AppendLine("* Meeting: " +
                                (properties["hs_meeting_title"]?["value"]?.ToString() ?? "Untitled Meeting"));

                            // Format date if available
                            if (properties["hs_meeting_start_time"]?["value"] != null)
                            {
                                var timestamp = properties["hs_meeting_start_time"]["value"].ToString();
                                if (long.TryParse(timestamp, out var epochMs))
                                {
                                    var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(epochMs).DateTime;
                                    response.AppendLine("  Date: " + dateTime.ToString("yyyy-MM-dd HH:mm"));
                                }
                            }

                            // Add meeting notes if available
                            if (properties["hs_meeting_body"]?["value"] != null)
                            {
                                response.AppendLine("  Description: " + properties["hs_meeting_body"]["value"].ToString());
                            }

                            // Add internal notes if available
                            if (properties["hs_internal_meeting_notes"]?["value"] != null)
                            {
                                response.AppendLine("  Internal Notes: " + properties["hs_internal_meeting_notes"]["value"].ToString());
                            }

                            response.AppendLine();
                        }
                    }
                }
                catch (JsonException)
                {
                    // Fall back to raw JSON if parsing fails
                    response.AppendLine(meetingsJson);
                }
            }

            _logger.LogInformation("Successfully retrieved Hubspot data");
            return response.Length > 0
                ? response.ToString()
                : "No Hubspot data found for this contact";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enriching chat history with Hubspot data");
            return "Failed to retrieve Hubspot data";
        }
    }

    /// <summary>
    /// Ensures that Hubspot settings are properly configured in the kernel
    /// </summary>
    private bool EnsureHubspotSettings(Kernel kernel)
    {
        // First check if settings are in the enricher data dictionary
        if (kernel.Data.TryGetValue(EnricherDataKeys.ENRICHER_ROOT, out var enricherDataObj) &&
            enricherDataObj is Dictionary<string, object> enricherData &&
            enricherData.TryGetValue(EnricherDataKeys.HUBSPOT_SETTINGS, out var settingsObj) &&
            settingsObj is HubspotSettings settings &&
            !string.IsNullOrEmpty(settings.HubspotApiKey))
        {
            // Ensure the settings are also in TOOLS for compatibility
            kernel.Data[KernelDataKeys.TOOLS] = settings;
            return true;
        }

        // Check if TOOLS already contains valid HubspotSettings
        if (kernel.Data.TryGetValue(KernelDataKeys.TOOLS, out var toolsObj) &&
            toolsObj is HubspotSettings existingSettings &&
            !string.IsNullOrEmpty(existingSettings.HubspotApiKey))
        {
            return true;
        }

        _logger.LogError("Hubspot API key not found in kernel data");
        return false;
    }
}
```

### 5. Updates to BaseAgentCollaborationDefinition

Modify the InitializeChatHistoryAsync method to incorporate enrichers:

```csharp
// Add new dependencies to constructor
private readonly IChatHistoryEnricherFactory _enricherFactory;

public BaseAgentCollaborationDefinition(
    ILogger<BaseAgentCollaborationDefinition> logger,
    Kernel kernel,
    ISummaryPlugin summaryPlugin,
    ILanguagePlugin languagePlugin,
    IAgentDurationTracker agentDurationTracker,
    ICompanyConfigService companyConfigService,
    IChatHistoryEnricherFactory enricherFactory)
{
    _logger = logger;
    _kernel = kernel;
    _summaryPlugin = summaryPlugin;
    _languagePlugin = languagePlugin;
    _agentDurationTracker = agentDurationTracker;
    _companyConfigService = companyConfigService;
    _enricherFactory = enricherFactory;
}

public virtual async Task<string> InitializeChatHistoryAsync(
    AgentGroupChat agentGroupChat,
    string groupChatIdStr,
    List<SfChatEntry> chatEntries,
    ReplyGenerationContext replyGenerationContext,
    AgentCollaborationConfig agentCollaborationConfig)
{
    var sanitizedChatEntries = GroupChatHistoryUtils.GetSanitizedChatEntries(chatEntries);

    var summaryTask = _summaryPlugin.SummarizeConversationAsync(_kernel, sanitizedChatEntries);
    var configTask = _companyConfigService.GetConfigAsync(replyGenerationContext.SleekflowCompanyId);
    var languageTask = _languagePlugin.DetectAppropriateResponseLanguageAsync(_kernel, string.Join("--\n", sanitizedChatEntries));

    await Task.WhenAll(summaryTask, configTask, languageTask);

    var chatHistoryStr = await summaryTask;
    var companyConfig = await configTask;
    var detectAppropriateResponseLanguageResponse = await languageTask;

    agentCollaborationConfig.DetectedResponseLanguage = detectAppropriateResponseLanguageResponse.ResponseLanguageName;

    _logger.LogInformation(
        "Chat history: {ChatHistoryStr}",
        chatHistoryStr);

    var contactProperties = replyGenerationContext.ContactProperties;
    var allContactProperties = contactProperties is null
        ? "No contact properties"
        : JsonConvert.SerializeObject(contactProperties);
    var leadNurturingTools = _kernel.Data.TryGetValue(KernelDataKeys.TOOLS, out var value)
        ? value as LeadNurturingTools
        : null;
    var permittedContactProperties = new Dictionary<string, string>();

    if (contactProperties != null && leadNurturingTools?.PermittedContactProperties != null)
    {
        var permitted = leadNurturingTools.PermittedContactProperties;

        permittedContactProperties = contactProperties
            .Where(cp => permitted.Exists(p => p.Name == cp.Key))
            .ToDictionary(cp => cp.Key, cp => cp.Value);
    }

    // log both contact properties and all contact properties in one message
    _logger.LogInformation(
        "All Contact Properties: {AllContactProperties}; Permitted Contact Properties: {PermittedContactProperties}",
        allContactProperties,
        permittedContactProperties.Count == 0
            ? "No contact properties"
            : JsonConvert.SerializeObject(permittedContactProperties));

    // Create enrichers based on company config
    var enrichers = _enricherFactory.CreateEnrichers(companyConfig, _kernel);
    _logger.LogInformation("Created {Count} enrichers", enrichers.Count);

    // Run all enrichers in parallel
    var enrichmentTasks = enrichers.Select(enricher =>
        RunEnricherSafelyAsync(enricher, replyGenerationContext, permittedContactProperties, _kernel)
    );

    var enrichmentResults = await Task.WhenAll(enrichmentTasks);

    // Build additional sections for the chat message
    var enrichmentSections = new StringBuilder();
    for (int i = 0; i < enrichers.Count; i++)
    {
        var enricher = enrichers[i];
        var result = enrichmentResults[i];

        if (!string.IsNullOrEmpty(result))
        {
            enrichmentSections.AppendLine($"===={enricher.GetContextSectionName()}====");
            enrichmentSections.AppendLine(result);
            enrichmentSections.AppendLine($"===={enricher.GetContextSectionName()}====");
            enrichmentSections.AppendLine();
        }
    }

    // Prepare the explanation of each section for the agent
    var sectionExplanations = new List<string>
    {
        "BACKGROUND is a configuration specifying our company's background information.",
        "REQUESTED TONE is a configuration specifying the desired tone of the response.",
        "RESPONSE LANGUAGE is the language specifying the appropriate language for the final response.",
        "CONTACT PROPERTIES is the contact properties of the customer stored in our system."
    };

    // Add explanations for all active enrichers
    foreach (var enricher in enrichers)
    {
        sectionExplanations.Add($"{enricher.GetContextSectionName()} contains enriched data from {enricher.GetContextSectionName().ToLower()}.");
    }

    sectionExplanations.Add($"CONVERSATION CONTEXT is the historical messages between our company and the customer. {(sanitizedChatEntries.Count > 10 ? "Please note that the conversation is very long, it is summarized." : string.Empty)}");
    sectionExplanations.Add("ADDITIONAL INSTRUCTION is a configuration specifying the additional instruction.");

    // Modify the chat message to include enrichment sections
    agentGroupChat.AddChatMessage(
        new ChatMessageContent(
            AuthorRole.User,
            $"""
             ====BACKGROUND====
             {companyConfig.BackgroundInformation}
             ====BACKGROUND====

             ====REQUESTED TONE====
             {GetRequestedToneContext(agentCollaborationConfig.Tone)}
             ====REQUESTED TONE====

             ====RESPONSE LANGUAGE====
             {agentCollaborationConfig.DetectedResponseLanguage}
             ====RESPONSE LANGUAGE====

             ====CONTACT PROPERTIES====
             {(permittedContactProperties.Count == 0 ? "No contact properties" : JsonConvert.SerializeObject(permittedContactProperties))}
             ====CONTACT PROPERTIES====

             {enrichmentSections}

             ====CONVERSATION CONTEXT====
             {chatHistoryStr}
             ====CONVERSATION CONTEXT====

             ====ADDITIONAL INSTRUCTION====
             {agentCollaborationConfig.AdditionalInstructionCore ?? "No additional instructions."}
             ====ADDITIONAL INSTRUCTION====

             {string.Join("\n", sectionExplanations)}
             """)
        {
            AuthorName = "Context"
        });

    return chatHistoryStr;
}

private async Task<string> RunEnricherSafelyAsync(
    IChatHistoryEnricher enricher,
    ReplyGenerationContext context,
    Dictionary<string, string>? contactProperties,
    Kernel kernel,
    CancellationToken cancellationToken = default)
{
    try
    {
        _logger.LogInformation("Running enricher {EnricherType}", enricher.GetType().Name);
        var startTime = DateTimeOffset.UtcNow;
        var result = await enricher.EnrichAsync(context, contactProperties, kernel, cancellationToken);
        var duration = DateTimeOffset.UtcNow - startTime;

        _logger.LogInformation(
            "Enricher {EnricherType} completed in {DurationMs}ms",
            enricher.GetType().Name,
            duration.TotalMilliseconds);

        return result;
    }
    catch (Exception ex)
    {
        _logger.LogError(
            ex,
            "Error running enricher {EnricherType}",
            enricher.GetType().Name);
        return $"Error retrieving data from {enricher.GetContextSectionName().ToLower()}";
    }
}
```

## Implementation Plan

### Phase 1: Core Infrastructure

- [ ] Create IChatHistoryEnricher interface
- [ ] Create EnricherConfig model
- [ ] Create EnricherDataKeys constants class
- [ ] Create IChatHistoryEnricherFactory interface
- [ ] Update CompanyAgentConfig to include EnricherConfigs
- [ ] Implement ChatHistoryEnricherFactory with ISingletonService marker
- [ ] Update BaseAgentCollaborationDefinition to use enrichers

### Phase 2: Hubspot Enricher

- [ ] Implement HubspotEnricher
- [ ] Add unit tests for HubspotEnricher
- [ ] Add integration tests with mock Hubspot API

### Phase 3: Additional Enrichers

- [ ] Implement additional enrichers as needed (e.g., Salesforce, Google Calendar)
- [ ] Create admin UI for configuring enrichers

### Phase 4: Monitoring and Improvements

- [ ] Add telemetry for enricher performance
- [ ] Implement caching for frequently accessed data
- [ ] Add controls for data privacy and filtering

## Expected Outcomes

- Enhanced context for AI agents leading to more personalized and relevant responses
- Flexible configuration allowing companies to choose which external data to include
- Improved user experience through more informed AI responses
- Maintainable codebase with clear separation of concerns

## References

- [BaseAgentCollaborationDefinition.cs](../Sleekflow.IntelligentHub/FaqAgents/Chats/AgentCollaborationDefinitions/BaseAgentCollaborationDefinition.cs) - The class being enhanced
- [HubspotPlugin.cs](../Sleekflow.IntelligentHub/Plugins/HubspotPlugin.cs) - Plugin for Hubspot integration
- [CompanyAgentConfig.cs](../Sleekflow.IntelligentHub.Models/Companies/CompanyAgentConfigs/CompanyAgentConfig.cs) - Configuration class to be extended
- [KernelDataKeys.cs](../Sleekflow.IntelligentHub/Models/Constants/KernelDataKeys.cs) - Constants for kernel data keys