﻿using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Evaluations;

[SwaggerInclude]
public class ConversationChannelLastMessage
{
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("message_unique_id")]
    public string? MessageUniqueId { get; set; }

    [JsonProperty("message_status")]
    public string MessageStatus { get; set; }

    [JsonProperty("message_type")]
    public string MessageType { get; set; }

    [JsonProperty("message_delivery_type")]
    public string MessageDeliveryType { get; set; }

    [JsonProperty("channel")]
    public string Channel { get; set; }

    [JsonProperty("channel_id")]
    public string ChannelId { get; set; }

    [JsonProperty("message_content")]
    public string? MessageContent { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public ConversationChannelLastMessage(
        string conversationId,
        string messageId,
        string? messageUniqueId,
        string messageStatus,
        string messageType,
        string messageDeliveryType,
        string channel,
        string channelId,
        string? messageContent,
        DateTimeOffset createdAt)
    {
        ConversationId = conversationId;
        MessageId = messageId;
        MessageUniqueId = messageUniqueId;
        MessageStatus = messageStatus;
        MessageType = messageType;
        MessageDeliveryType = messageDeliveryType;
        Channel = channel;
        ChannelId = channelId;
        MessageContent = messageContent;
        CreatedAt = createdAt;
    }
}