﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class RearrangeSchemaOrder : ITrigger<RearrangeSchemaOrder.RearrangeSchemaOrderInput, RearrangeSchemaOrder.RearrangeSchemaOrderOutput>
{
    private readonly ISchemaService _schemaService;

    public RearrangeSchemaOrder(
        ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public class RearrangeSchemaOrderInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty("prior_to_schema_id")]
        public string PriorToSchemaId { get; set; }

        [JsonConstructor]
        public RearrangeSchemaOrderInput(string sleekflowCompanyId, string id, string priorToSchemaId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Id = id;
            PriorToSchemaId = priorToSchemaId;
        }
    }

    public class RearrangeSchemaOrderOutput
    {
    }

    /// <summary>
    ///  Move the target schema to the next position of the preSchema, pass an empty PreSchemaId if move to first.
    /// </summary>
    /// <param name="rearrangeSchemaOrderInput">RearrangeSchemaOrderInput.</param>
    /// <returns>Is operation succeed.</returns>
    public async Task<RearrangeSchemaOrderOutput> F(RearrangeSchemaOrderInput rearrangeSchemaOrderInput)
    {
        await _schemaService.RearrangeDisplayOrderAsync(
            rearrangeSchemaOrderInput.Id,
            rearrangeSchemaOrderInput.PriorToSchemaId,
            rearrangeSchemaOrderInput.SleekflowCompanyId);

        return new RearrangeSchemaOrderOutput();
    }
}