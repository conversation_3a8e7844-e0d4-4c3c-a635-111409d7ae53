﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.AuditHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class AuditHubModules
{
    public static void BuildAuditHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON><PERSON><PERSON><PERSON><PERSON><IAuditHubDbConfig>(new Mock<IAuditHubDbConfig>().Object);
        b.<PERSON><PERSON><IAuditHubDbResolver>(new Mock<IAuditHubDbResolver>().Object);

#else
        var auditHubDbConfig = new AuditHubDbConfig();

        b.<PERSON><PERSON><PERSON><IAuditHubDbConfig>(auditHubDbConfig);
        b.<PERSON><PERSON><PERSON><PERSON><IAuditHubDbResolver, AuditHubDbResolver>();
#endif
    }
}