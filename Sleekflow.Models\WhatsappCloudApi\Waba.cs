using Newtonsoft.Json;

namespace Sleekflow.Models.WhatsappCloudApi;

public class Waba
{
    // Facebook Login For Business Flow specific - if this is null implies this channel continues to use Facebook Login Flow
    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("sleekflow_company_ids")]
    public List<string> SleekflowCompanyIds { get; set; }

    [JsonConstructor]
    public Waba(string facebookBusinessId, List<string> sleekflowCompanyIds)
    {
        FacebookBusinessId = facebookBusinessId;
        SleekflowCompanyIds = sleekflowCompanyIds;
    }
}