using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.WhatsappCloudApi;

namespace Sleekflow.InternalIntegrationHub.WhatsappCloudApi;

public interface IWabaService
{
    public Task<List<Waba>> GetWabas(string companyId);
}

public class WabaService : IScopedService, IWabaService
{
    private readonly IRequestClient<GetWabasRequest>
        _getWabasRequestClient;

    public WabaService(
        IRequestClient<GetWabasRequest> getWabasRequestClient)
    {
        _getWabasRequestClient = getWabasRequestClient;
    }

    public async Task<List<Waba>> GetWabas(string companyId)
    {
        var getWabasResponse =
            await _getWabasRequestClient.GetResponse<GetWabasReply>(
                new GetWabasRequest(companyId));

        return getWabasResponse.Message.Wabas;
    }
}