﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Services;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetTypeFieldsV2 : ITrigger
{
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;

    public GetTypeFieldsV2(
        IGoogleSheetsObjectService googleSheetsObjectService,
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService)
    {
        _googleSheetsObjectService = googleSheetsObjectService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
    }

    public class GetTypeFieldsV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("typed_ids")]
        [Required]
        [ValidateArray]
        public List<TypedId> TypedIds { get; set; }

        [JsonConstructor]
        public GetTypeFieldsV2Input(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<TypedId> typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            TypedIds = typedIds;
        }
    }

    public class GetTypeFieldsV2Output
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetTypeFieldsV2Output(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<GetTypeFieldsV2Output> F(GetTypeFieldsV2Input getTypeFieldsV2Input)
    {
        var connection =
            await _googleSheetsConnectionService.GetByIdAsync(
                getTypeFieldsV2Input.ConnectionId,
                getTypeFieldsV2Input.SleekflowCompanyId);

        var authentication =
            await _googleSheetsAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getTypeFieldsV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var output = await _googleSheetsObjectService.GetFieldsAsync(
            authentication,
            getTypeFieldsV2Input.TypedIds,
            getTypeFieldsV2Input.EntityTypeName);

        return new GetTypeFieldsV2Output(
            output.UpdatableFields,
            output.CreatableFields,
            output.ViewableFields);
    }
}