using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Models.Events;

public class OnCloudApiConversationUsageExceptionEvent
{
    public string BusinessBalanceId { get; set; }

    public string FacebookBusinessId { get; set; }

    public Dictionary<string, WabaConversationInsertionException> WabaConversationInsertionExceptions { get; set; }

    public OnCloudApiConversationUsageExceptionEvent(
        string businessBalanceId,
        string facebookBusinessId,
        Dictionary<string, WabaConversationInsertionException> wabaConversationInsertionExceptions)
    {
        BusinessBalanceId = businessBalanceId;
        FacebookBusinessId = facebookBusinessId;
        WabaConversationInsertionExceptions = wabaConversationInsertionExceptions;
    }
}