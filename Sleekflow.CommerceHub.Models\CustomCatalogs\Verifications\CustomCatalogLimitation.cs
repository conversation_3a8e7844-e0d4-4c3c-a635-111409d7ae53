using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs.Verifications;

public class CustomCatalogLimitation
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("total_quota")]
    public long TotalQuota { get; set; }

    [JsonProperty("current_usage")]
    public long CurrentUsage { get; set; }

    [JsonProperty("remaining_quota")]
    public long RemainingQuota { get; set; }

    [JsonConstructor]
    public CustomCatalogLimitation(string type, long totalQuota, long currentUsage, long remainingQuota)
    {
        Type = type;
        TotalQuota = totalQuota;
        CurrentUsage = currentUsage;
        RemainingQuota = remainingQuota;
    }

    public CustomCatalogLimitation(string type, long totalQuota, int currentUsage)
        : this(
            type,
            totalQuota,
            currentUsage,
            totalQuota - currentUsage)
    {
    }
}