﻿using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using MimeDetective;
using MimeDetective.Definitions;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISendMessageV2StepExecutor : IStepExecutor;

public class SendMessageV2StepExecutor
    : GeneralStepExecutor<CallStep<SendMessageV2StepArgs>>,
        ISendMessageV2StepExecutor,
        IScopedService
{
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly HttpClient _httpClient;
    private readonly IMessageBodyCreatorMatcher _messageBodyCreatorMatcher;

    public SendMessageV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator,
        IHttpClientFactory httpClientFactory,
        IMessageBodyCreatorMatcher messageBodyCreatorMatcher)
        : base(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider)
    {
        _coreCommander = coreCommander;
        _stateEvaluator = stateEvaluator;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _messageBodyCreatorMatcher = messageBodyCreatorMatcher;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "v2/SendMessage",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    public class SendMessageInputFromToV2
    {
        [Required]
        [JsonProperty("channel_identity_id")]
        public string ChannelIdentityId { get; set; }

        [Required]
        [JsonProperty("to_contact_id")]
        public string ToContactId { get; set; }

        [JsonConstructor]
        public SendMessageInputFromToV2(
            string channelIdentityId,
            string toContactId)
        {
            ChannelIdentityId = channelIdentityId;
            ToContactId = toContactId;
        }
    }

    [SwaggerInclude]
    public class SendMessageInputV2
    {
        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [Required]
        [JsonProperty("state_identity")]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [Required]
        [JsonProperty("channel")]
        public string ChannelType { get; set; }

        [Required]
        [JsonProperty("from_to")]
        [Validations.ValidateObject]
        public SendMessageInputFromToV2 FromTo { get; set; }

        [JsonProperty("message_type")]
        [Required]
        public string MessageType { get; set; }

        [JsonProperty("message_body")]
        [Required]
        [Validations.ValidateObject]
        public MessageBody MessageBody { get; set; }

        [JsonProperty("delivery_type")]
        public int? DeliveryType { get; set; }

        [JsonProperty("staff_id")]
        public string? StaffId { get; set; }

        [JsonConstructor]
        public SendMessageInputV2(
            string stateId,
            StateIdentity stateIdentity,
            string channelType,
            SendMessageInputFromToV2 fromTo,
            string messageType,
            MessageBody messageBody,
            int? deliveryType,
            string? staffId)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ChannelType = channelType;
            FromTo = fromTo;
            MessageType = messageType;
            MessageBody = messageBody;
            DeliveryType = deliveryType;
            StaffId = staffId;
        }
    }

    private async Task<SendMessageInputV2> GetArgs(
        CallStep<SendMessageV2StepArgs> callStep,
        ProxyState state)
    {
        var channelType = callStep.Args.ChannelType;
        var channelIdentityId = callStep.Args.ChannelIdentityId;

        var messageStr = (string?) await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
            state,
            callStep.Args.MessageExpr ?? string.Empty) ?? callStep.Args.MessageExpr;

        var toContactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(
                          state,
                          "{{ (trigger_event_body.contact_id | string.whitespace) ? usr_var_dict.contact.id : trigger_event_body.contact_id }}")
                      ?? throw new InvalidOperationException("No contact id found"));

        var (messageBody, messageType) = await GetMessageBodyAndMessageTypeAsync(
            state,
            channelType,
            messageStr,
            callStep.Args);

        var deliveryType = !string.IsNullOrWhiteSpace(callStep.Args.DeliveryTypeExpr)
            ? (int?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                callStep.Args.DeliveryTypeExpr!)
            : null;

        // This is to impersonate a staff when sending a message.
        var staffId = !string.IsNullOrWhiteSpace(callStep.Args.StaffIdExpr)
            ? (string?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                callStep.Args.StaffIdExpr!)
            : null;

        // This is to specify the delivery type for AI Agent recommended replies.
        if (deliveryType == null
            && callStep.Args.MessageExpr != null
            && callStep.Args.MessageExpr.Contains("recommended_reply"))
        {
            deliveryType = 6;
        }

        return new SendMessageInputV2(
            state.Id,
            state.Identity,
            channelType,
            new SendMessageInputFromToV2(
                channelIdentityId,
                toContactId),
            messageType,
            messageBody,
            deliveryType,
            staffId);
    }

    private async Task<(MessageBody MessageBody, string MessageType)> GetMessageBodyAndMessageTypeAsync(
        ProxyState state,
        string channelType,
        string? messageStr,
        SendMessageV2StepArgs callStepArgs)
    {
        var messageBodyCreator = _messageBodyCreatorMatcher.MatchCreator(channelType);

        if (!string.IsNullOrWhiteSpace(callStepArgs.MediaParameters?.MediaUrlExpr))
        {
            var mediaUrl = (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
                                         state,
                                         callStepArgs.MediaParameters!.MediaUrlExpr!)
                                     ?? throw new InvalidOperationException(
                                         $"No media url evaluated from expression {callStepArgs.MediaParameters.MediaUrlExpr}"));

            var mediaResponse = await _httpClient.GetAsync(
                mediaUrl,
                HttpCompletionOption.ResponseHeadersRead);

            if (!mediaResponse.IsSuccessStatusCode)
            {
                throw new InvalidOperationException(
                    $"Couldn't read media from url {mediaUrl}. Status code: {mediaResponse.StatusCode}");
            }

            await using var mediaStream = await mediaResponse.Content.ReadAsStreamAsync();

            var inspector = new ContentInspectorBuilder
                {
                    Definitions = DefaultDefinitions.All()
                }
                .Build();

            var fileType = inspector.Inspect(mediaStream)
                .Select(x => x.Definition.File)
                .FirstOrDefault();

            var mediaType = fileType?.MimeType ?? MediaTypeNames.Application.Octet;

            var mediaMessageType = mediaType switch
            {
                _ when mediaType.StartsWith("image") => "image",
                _ when mediaType.StartsWith("video") => "video",
                _ when mediaType.StartsWith("audio") => "audio",
                _ when mediaType.StartsWith("application") => "document",
                _ => throw new NotSupportedException($"Unsupported media type {mediaType}")
            };

            var mediaName = (string?) await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
                state,
                callStepArgs.MediaParameters.MediaNameExpr ?? string.Empty);

            if (string.IsNullOrWhiteSpace(mediaName))
            {
                mediaName = Guid.NewGuid().ToString();
            }

            if (!string.IsNullOrWhiteSpace(fileType?.MimeType))
            {
                mediaName = $"{Path.GetFileNameWithoutExtension(mediaName)}.{fileType.Extensions.First()}";
            }

            var mediaMessageBody = await messageBodyCreator.CreateMediaMessageBodyAsync(
                mediaUrl,
                mediaMessageType,
                mediaName,
                callStepArgs);

            return (mediaMessageBody, mediaMessageType);
        }

        var (messageBody, messageType) =
            await messageBodyCreator.CreateMessageBodyAndMessageTypeAsync(messageStr!, callStepArgs);

        return (messageBody, messageType);
    }
}