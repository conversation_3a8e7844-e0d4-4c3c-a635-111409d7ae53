using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class GetProductCatalogProductItems
    : ITrigger<
        GetProductCatalogProductItems.GetProductCatalogProductItemsInput,
        GetProductCatalogProductItems.GetProductCatalogProductItemsOutput>
{
    private readonly IProductCatalogService _productCatalogService;
    private readonly ILogger<GetProductCatalogProductItems> _logger;

    public GetProductCatalogProductItems(
        IProductCatalogService productCatalogService,
        ILogger<GetProductCatalogProductItems> logger)
    {
        _productCatalogService = productCatalogService;
        _logger = logger;
    }

    public class GetProductCatalogProductItemsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("facebook_product_catalog_id")]
        public string FacebookProductCatalogId { get; set; }

        [JsonConstructor]
        public GetProductCatalogProductItemsInput(
            string sleekflowCompanyId,
            string wabaId,
            string facebookProductCatalogId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            FacebookProductCatalogId = facebookProductCatalogId;
        }
    }

    public class GetProductCatalogProductItemsOutput
    {
        [JsonProperty("product_items")]
        public List<ProductItemDto> ProductItems { get; set; }

        [JsonConstructor]
        public GetProductCatalogProductItemsOutput(List<ProductItemDto> productItems)
        {
            ProductItems = productItems;
        }
    }

    public async Task<GetProductCatalogProductItemsOutput> F(
        GetProductCatalogProductItemsInput getProductCatalogProductItemsInput)
    {
        _logger.LogInformation("getting waba id {WabaId} product catalog id {ProductCatalogId}", getProductCatalogProductItemsInput.WabaId, getProductCatalogProductItemsInput.FacebookProductCatalogId);

        return new GetProductCatalogProductItemsOutput(
            await _productCatalogService.GetProductItemsAsync(
                getProductCatalogProductItemsInput.SleekflowCompanyId,
                getProductCatalogProductItemsInput.WabaId,
                getProductCatalogProductItemsInput.FacebookProductCatalogId));
    }
}