using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Communications;

public abstract class EmailMetadata
{
    [Required]
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [Required]
    [JsonProperty("provider_id")]
    public string ProviderId { get; set; }

    [Required]
    [JsonProperty("sent_at")]
    public DateTimeOffset SentAt { get; set; }

    [JsonConstructor]
    protected EmailMetadata(
        string providerName,
        string providerId,
        DateTimeOffset sentAt)
    {
        ProviderName = providerName;
        ProviderId = providerId;
        SentAt = sentAt;
    }
}