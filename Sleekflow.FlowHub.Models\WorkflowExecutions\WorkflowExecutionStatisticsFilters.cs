using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionStatisticsFilters : IValidatableObject
{
    [JsonProperty("from_date_time")]
    public DateTimeOffset? FromDateTime { get; set; }

    [JsonProperty("to_date_time")]
    public DateTimeOffset? ToDateTime { get; set; }

    [JsonProperty("workflow_type")]
    public string? WorkflowType { get; set; }

    [JsonConstructor]
    public WorkflowExecutionStatisticsFilters(
        DateTimeOffset? fromDateTime,
        DateTimeOffset? toDateTime,
        string? workflowType)
    {
        FromDateTime = fromDateTime;
        ToDateTime = toDateTime;
        WorkflowType = workflowType;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (FromDateTime.HasValue && !ToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both FromDateTime and ToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(FromDateTime)
                });
        }

        if (!FromDateTime.HasValue && ToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both FromDateTime and ToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(ToDateTime)
                });
        }

        if (FromDateTime.HasValue && ToDateTime.HasValue)
        {
            if (FromDateTime.Value > ToDateTime.Value)
            {
                yield return new ValidationResult(
                    "FromDateTime can't be greater than ToDateTime",
                    new[]
                    {
                        nameof(FromDateTime),
                        nameof(ToDateTime)
                    });
            }

            if ((ToDateTime.Value - FromDateTime.Value).TotalDays > 31)
            {
                yield return new ValidationResult(
                    "The duration between FromDateTime and ToDateTime can't be more than 31 days",
                    new[]
                    {
                        nameof(FromDateTime),
                        nameof(ToDateTime)
                    });
            }
        }

        if (WorkflowType is not null && WorkflowType != Constants.WorkflowType.Normal &&
            WorkflowType != Constants.WorkflowType.InternalForZapierIntegration)
        {
            yield return new ValidationResult(
                $"WorkflowType must be either {Constants.WorkflowType.Normal} or {Constants.WorkflowType.InternalForZapierIntegration}",
                new[]
                {
                    nameof(WorkflowType)
                });
        }
    }
}