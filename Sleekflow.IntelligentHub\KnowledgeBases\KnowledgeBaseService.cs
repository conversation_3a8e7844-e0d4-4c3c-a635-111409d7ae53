using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Chats;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.KnowledgeBases;

public interface IKnowledgeBaseService
{
    Task<List<(string Sourcepage, string Content, string ContentEn)>> GetSearchResultsAsync(
        string sleekflowCompanyId,
        string searchText,
        List<SfChatEntry>? sfChatEntries = null);
}

public class KnowledgeBaseService : IKnowledgeBaseService, IScopedService
{
    private readonly ILogger _logger;
    private readonly ISearchPlugin _searchPlugin;

    public KnowledgeBaseService(
        ILogger<KnowledgeBaseService> logger,
        ISearchPlugin searchPlugin)
    {
        _logger = logger;
        _searchPlugin = searchPlugin;
    }

    public async Task<List<(string Sourcepage, string Content, string ContentEn)>> GetSearchResultsAsync(
        string sleekflowCompanyId,
        string searchText,
        List<SfChatEntry>? sfChatEntries = null)
    {
        _logger.LogInformation(
            "Get search results for {SleekflowCompanyId} with text {SearchText} and chat entries {SfChatEntries}",
            sleekflowCompanyId,
            searchText,
            JsonConvert.SerializeObject(sfChatEntries));
        if (sfChatEntries is null || sfChatEntries.Count <= 0)
        {
            return await _searchPlugin.GetSearchResultsAsync(sleekflowCompanyId, searchText, string.Empty);
        }

        var chatEntries = sfChatEntries;
        chatEntries.Add(
            new SfChatEntry
            {
                User = searchText
            });

        var (userQuestion, chatHistoryInStr) = SfChatEntryUtils.ToQuestionAndChatHistoryStr(chatEntries);
        return await _searchPlugin.GetSearchResultsAsync(sleekflowCompanyId, userQuestion, chatHistoryInStr);
    }
}