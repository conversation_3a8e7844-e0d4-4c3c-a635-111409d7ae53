﻿using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.Connections;

public interface IGoogleSheetsConnectionRepository : IRepository<GoogleSheetsConnection>
{
}

public class GoogleSheetsConnectionRepository
    : BaseRepository<GoogleSheetsConnection>,
        IGoogleSheetsConnectionRepository,
        ISingletonService
{
    public GoogleSheetsConnectionRepository(
        ILogger<BaseRepository<GoogleSheetsConnection>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}