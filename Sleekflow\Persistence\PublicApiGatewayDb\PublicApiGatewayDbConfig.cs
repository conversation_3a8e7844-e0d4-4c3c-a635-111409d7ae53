using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.PublicApiGatewayDb;

public interface IPublicApiGatewayDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class PublicApiGatewayDbConfig : IPublicApiGatewayDbConfig, IConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public PublicApiGatewayDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_PUBLIC_API_GATEWAY_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_PUBLIC_API_GATEWAY_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_PUBLIC_API_GATEWAY_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_PUBLIC_API_GATEWAY_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_PUBLIC_API_GATEWAY_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_PUBLIC_API_GATEWAY_DB_DATABASE_ID");
    }
}