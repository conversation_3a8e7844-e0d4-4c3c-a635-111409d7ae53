<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Auth0.AuthenticationApi" Version="7.26.2" />
      <PackageReference Include="Auth0.Core" Version="7.26.2" />
      <PackageReference Include="Auth0.ManagementApi" Version="7.26.2" />
      <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
      <PackageReference Include="Microsoft.AspNetCore.Cryptography.KeyDerivation" Version="8.0.13" />
      <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Dockerfile" />
    </ItemGroup>
</Project>
