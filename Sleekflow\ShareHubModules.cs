﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.ShareHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class ShareHubModules
{
    public static void BuildShareHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON>d<PERSON><PERSON><PERSON><IShareHubDbConfig>(new Mock<IShareHubDbConfig>().Object);
        b.<PERSON><PERSON><PERSON><PERSON><IShareHubDbResolver>(new Mock<IShareHubDbResolver>().Object);

#else
        var shareHubDbConfig = new ShareHubDbConfig();

        b.<PERSON>d<PERSON><PERSON><IShareHubDbConfig>(shareHubDbConfig);
        b.<PERSON><PERSON><PERSON><IShareHubDbResolver, ShareHubDbResolver>();
#endif
    }
}