using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class SalesforceObjectCreatedEventRequestConsumerDefinition : ConsumerDefinition<SalesforceObjectCreatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SalesforceObjectCreatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SalesforceObjectCreatedEventRequestConsumer : IConsumer<SalesforceObjectCreatedEventRequest>
{
    private readonly IServiceBusManager _serviceBusManager;

    public SalesforceObjectCreatedEventRequestConsumer(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<SalesforceObjectCreatedEventRequest> context)
    {
        var salesforceObjectCreatedEventRequest = context.Message;

        await _serviceBusManager.PublishAsync(
            new OnTriggerEventRequestedEvent(
                new OnSalesforceObjectCreatedEventBody(
                    salesforceObjectCreatedEventRequest.CreatedAt,
                    salesforceObjectCreatedEventRequest.ConnectionId,
                    salesforceObjectCreatedEventRequest.ConnectionId,
                    salesforceObjectCreatedEventRequest.ObjectType,
                    salesforceObjectCreatedEventRequest.ObjectType,
                    salesforceObjectCreatedEventRequest.IsCustomObject,
                    salesforceObjectCreatedEventRequest.ObjectDict),
                salesforceObjectCreatedEventRequest.ObjectId,
                salesforceObjectCreatedEventRequest.ObjectType,
                salesforceObjectCreatedEventRequest.SleekflowCompanyId));
    }
}