﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.AuditHubDb;

namespace Sleekflow.AuditHub.Models.AuditLogs;

[Resolver(typeof(IAuditHubDbResolver))]
[DatabaseId("audithubdb")]
[ContainerId("audit_log")]
public class AuditLog : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("details")]
    public Dictionary<string, object?> Details { get; set; }

    [JsonProperty("created_time")]
    public DateTimeOffset CreatedTime { get; set; }

    [JsonConstructor]
    public AuditLog(
        string id,
        string sleekflowCompanyId,
        string type,
        string description,
        Dictionary<string, object?> details,
        DateTimeOffset createdTime)
        : base(id, "AuditLog")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        Type = type;
        Description = description;
        Details = details;
        CreatedTime = createdTime;
    }
}