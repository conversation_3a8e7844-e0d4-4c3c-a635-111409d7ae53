using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetWorkflowExecutionUsages : ITrigger<
    GetWorkflowExecutionUsages.GetWorkflowExecutionUsagesInput,
    GetWorkflowExecutionUsages.GetWorkflowExecutionUsagesOutput>
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public class GetWorkflowExecutionUsagesInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("filters")]
        [ValidateObject]
        public WorkflowExecutionUsageFilters? Filters { get; set; }

        [JsonProperty("limit")]
        [Required]
        [Range(1, 15)]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionUsagesInput(
            string sleekflowCompanyId,
            WorkflowExecutionUsageFilters? filters,
            int limit,
            string? continuationToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Filters = filters;
            Limit = limit;
            ContinuationToken = continuationToken;
        }
    }

    public class GetWorkflowExecutionUsagesOutput
    {
        [JsonProperty("workflow_execution_usages")]
        public List<WorkflowExecutionUsageListDto> WorkflowExecutionUsages { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionUsagesOutput(
            List<WorkflowExecutionUsageListDto> workflowExecutionUsages,
            string? continuationToken)
        {
            WorkflowExecutionUsages = workflowExecutionUsages;
            ContinuationToken = continuationToken;
        }
    }

    public GetWorkflowExecutionUsages(
        IWorkflowService workflowService,
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowService = workflowService;
        _workflowExecutionService = workflowExecutionService;
    }

    public async Task<GetWorkflowExecutionUsagesOutput> F(GetWorkflowExecutionUsagesInput input)
    {
        var (workflows, nextContinuationToken) = await _workflowService.GetAllLatestWorkflowAndStatusTuplesAsync(
            input.SleekflowCompanyId,
            input.ContinuationToken,
            input.Limit,
            input.Filters?.WorkflowName,
            new WorkflowFilters(
                input.Filters?.WorkflowStatus,
                null,
                null,
                null,
                null,
                input.Filters?.WorkflowType,
                null),
            includeDeleted: true);

        var workflowIds = workflows
            .Select(w => w.WorkflowId)
            .ToList();

        var executionUsagesByWorkflow = await _workflowExecutionService.GetExecutionUsagesByWorkflowsAsync(
                input.SleekflowCompanyId,
                workflowIds,
                input.Filters?.ExecutionFromDateTime,
                input.Filters?.ExecutionToDateTime);

        var workflowEnrollmentUsages = new List<WorkflowExecutionUsageListDto>();

        foreach (var workflow in workflows)
        {
            var executionUsage = executionUsagesByWorkflow
                .Find(e => e.WorkflowId == workflow.WorkflowId);

            workflowEnrollmentUsages.Add(
                new WorkflowExecutionUsageListDto(
                    new LightweightWorkflowDto(workflow),
                    executionUsage?.TotalExecutionCount ?? 0,
                    executionUsage?.FailedExecutionCount ?? 0,
                    executionUsage?.LastEnrolledAt ?? null));
        }

        return new GetWorkflowExecutionUsagesOutput(
            workflowEnrollmentUsages,
            nextContinuationToken);
    }
}