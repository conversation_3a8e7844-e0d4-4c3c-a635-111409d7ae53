﻿using System.Web;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Configs;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.GoogleSheets.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly ILogger<PublicController> _logger;

    public PublicController(
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        ILogger<PublicController> logger)
    {
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _logger = logger;
    }

    [Route("healthz")]
    [HttpGet]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }

    [SwaggerQuery(
    new[]
    {
        "code",
        "state"
    })]
    [Route("AuthenticateCallback")]
    [HttpGet]
    public async Task<IActionResult> AuthenticateCallback()
    {
        var code = HttpContext.Request.Query["code"];
        var encryptedState = HttpContext.Request.Query["state"];

        var (authentication, email, successUrl, failureUrl) =
            await _googleSheetsAuthenticationService.HandleAuthenticateCallbackAndStoreAsync(
                code!,
                encryptedState!);

        try
        {
            if (authentication is null)
            {
                throw new Exception("Unable to handle the authentication callback");
            }

            var connection = await _googleSheetsConnectionService.GetByAuthenticationIdAsync(
                authentication.SleekflowCompanyId,
                authentication.Id);

            if (connection is null)
            {
                connection = await _googleSheetsConnectionService.CreateAndGetAsync(
                    authentication.SleekflowCompanyId,
                    authentication.Id,
                    email,
                    email,
                    true);
            }
            else
            {
                await _googleSheetsConnectionService.PatchAsync(
                    connection.Id,
                    authentication.SleekflowCompanyId,
                    connection.Name,
                    true);
            }

            string encodedSuccessUrl;

            var successUri = new Uri(successUrl!);

            var existingQueryStrings = HttpUtility.ParseQueryString(successUri.Query);
            if (existingQueryStrings.Count == 0)
            {
                encodedSuccessUrl = successUrl + $"?connection_id={HttpUtility.HtmlEncode(connection.Id)}";
            }
            else
            {
                var queryStrings = existingQueryStrings;
                queryStrings["connection_id"] = HttpUtility.HtmlEncode(connection.Id);

                encodedSuccessUrl = successUri.GetLeftPart(UriPartial.Path) + "?" + queryStrings;
            }

            return new ContentResult
            {
                ContentType = "text/html",
                Content = $"<meta http-equiv=\"refresh\" content=\"0;URL='{encodedSuccessUrl}'\" />"
            };
        }
        catch (Exception exception)
        {
            var gen = RandomStringUtils.Gen(10);

            _logger.LogError(exception, "Caught an exception. requestId {RequestId}", gen);

            return new ContentResult
            {
                ContentType = "text/html",
                Content = "<meta http-equiv=\"refresh\" content=\"0;URL='" + failureUrl + "'\" />"
            };
        }
    }
}