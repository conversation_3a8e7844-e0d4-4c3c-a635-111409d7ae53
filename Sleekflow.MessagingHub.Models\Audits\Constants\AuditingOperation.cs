namespace Sleekflow.MessagingHub.Models.Audits.Constants;

public static class AuditingOperation
{
    // Internal changes
    public const string WabaUpsertSnapshots = "waba_upsert_snapshots";

    public const string WabaRefreshSnapshots = "waba_refresh_snapshots";

    // External changes through graph apis
    public const string SendMessage = "send_message";

    public const string ConnectWabaAddWabaAssignedUser = "connect_waba_add_waba_assigned_user";

    public const string ConnectWabaCreateWabaSubscription = "connect_waba_create_waba_subscription";

    public const string AttachWabaCreditLine = "attach_waba_credit_line";

    public const string FacebookWebhook = "facebook_webhook";

    public const string WabaConnectCloudApiChannel = "waba_connect_cloud_api_channel";

    public const string WabaUpdate = "waba_update";

    public const string WabaDisconnectCloudApiChannel = "waba_disconnect_cloud_api_channel";

    public const string RegisterWabaPhoneNumber = "register_waba_phone_number";

    public const string OnboardPartnersToMMLite = "onboard_partners_to_mm_lite";

    public const string UpdatePhoneNumberSettings = "update_phone_number_settings";

    public const string OnCloudApiWabaBusinessConnectedEvent = "on_cloud_api_waba_business_connected_event";

    public const string OnCloudApiWabaBalanceConnectedEvent = "on_cloud_api_waba_balance_connected_event";

    public const string OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent =
        "on_cloud_api_accumulate_half_hour_conversation_usage_transaction_inserted_event";

    public const string OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent =
        "on_cloud_api_business_balance_pending_transaction_log_created_event";

    public const string UnblockOrBlockWabaSnapshot = "unblock_or_block_waba_snapshot";

    public const string InitiatePhoneNumberWabaMigration = "initiate_phone_number_waba_migration";

    public const string RequestPhoneNumberVerificationCode = "request_phone_number_verification_code";

    public const string VerifyPhoneNumberOwnership = "verify_phone_number_ownership";

    public const string MigratePhoneNumberToCloudApi = "migrate_phone_number_to_cloud_api";

    public const string UpdateWhatsappCloudApiBusinessBalanceMarkupProfile =
        "update_whatsapp_cloud_api_business_balance_markup_profile";

    public const string WabaPhoneNumberEnableProductCatalog = "waba_phone_number_enable_product_catalog";

    public const string RefreshWabaProductCatalog = "refresh_waba_product_catalog";

    public const string ResetBusinessBalanceTransactionLogCalculatedState =
        "reset_business_balance_transaction_log_calculated_state";

    public const string OnCloudApiRevalidateHalfHourConversationUsageTransactionEventConsumer =
        "on_cloud_api_revalidate_half_hour_conversation_usage_transaction_event_consumer";

    public const string OnCloudApiBusinessBalanceResynchronizationEvent =
        "on_cloud_api_business_balance_resynchronization_event";

    public const string ConnectProductCatalogAddProductCatalogAssignedUser =
        "connect_product_catalog_add_product_catalog_assigned_user";

    public const string DisconnectFacebookWabaProductCatalog = "disconnect_facebook_waba_product_catalog";

    public const string ConnectFacebookWabaProductCatalog = "connect_facebook_waba_product_catalog";

    public const string UpdateFacebookPhoneNumberCommerceSetting = "update_facebook_phone_number_commerce_setting";

    public const string RefreshWabasPhoneNumbersProductCatalogs = "refresh_wabass_phone_numbers_product_catalogs";

    public const string OnCloudApiWebhookStatusUpdateEvent = "on_cloud_api_webhook_status_update_event";

    public const string DisassociateFacebookBusinessAccountFromCompany = "disassociate_facebook_business_account_from_company";

    public const string UpdateWabaPhoneNumberWhatsappBusinessProfile = "update_waba_phone_number_whatsapp_business_profile";

    public const string SwitchWabaLevelCreditManagement = "switch_waba_level_credit_management";

    public const string SwitchBusinessLevelCreditManagement = "switch_business_level_credit_management";

    public const string CreditTransferFromBusinessToWaba = "credit_transfer_from_business_to_waba";

    public const string CreditTransferFromWabaToBusiness = "credit_transfer_from_waba_to_business";

    public const string OnCloudApiWabaDeletionWebhookEvent = "on_cloud_api_waba_deletion_webhook_event";
}