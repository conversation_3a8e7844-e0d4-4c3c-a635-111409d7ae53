using Sleekflow.DurablePayloads;
using Sleekflow.FlowHub.Models.Workflows.Settings;

namespace Sleekflow.FlowHub.Workers.Services;

public interface IPropertyOrchestrationService
{
    Task<DurablePayload> TriggerContactDateTimeWorkflowAsync<T>(
        string sleekflowCompanyId,
        string origin,
        string workflowId,
        string workflowVersionedId,
        ContactPropertyDateTimeSettings contactPropertyDateTimeSettings,
        CustomObjectDateTimeSettings customObjectDateTimeSettings,
        string workflowVersion,
        string scheduledType,
        WorkflowRecurringSettings recurringSettings);
}