﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class LoopThroughAndEnrollObjectsToFlowHubBatch : ITrigger
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public LoopThroughAndEnrollObjectsToFlowHubBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            bool isCustomObject,
            string? nextRecordsUrl,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            IsCustomObject = isCustomObject;
            NextRecordsUrl = nextRecordsUrl;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchOutput(long count, string? nextRecordsUrl)
        {
            Count = count;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    [Function("Salesforce_LoopThroughAndEnrollObjectsToFlowHub_Batch")]
    public async Task<LoopThroughAndEnrollObjectsToFlowHubBatchOutput> Batch(
        [ActivityTrigger]
        LoopThroughAndEnrollObjectsToFlowHubBatchInput loopThroughAndEnrollObjectsToFlowHubBatchInput)
    {
        var inputJsonStr = JsonConvert.SerializeObject(loopThroughAndEnrollObjectsToFlowHubBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.SalesforceIntegratorInternalsEndpoint + "/LoopThroughAndEnrollObjectsToFlowHubBatch"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<LoopThroughAndEnrollObjectsToFlowHubBatchOutput>()!;
    }
}