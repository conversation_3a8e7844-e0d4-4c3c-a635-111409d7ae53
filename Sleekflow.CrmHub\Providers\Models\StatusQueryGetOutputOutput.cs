﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Providers.Models;

public class StatusQueryGetOutputCustomStatus
{
    [JsonProperty("count")]
    public int Count { get; set; }

    [JsonProperty("last_update_time")]
    public DateTime LastUpdateTime { get; set; }

    [JsonConstructor]
    public StatusQueryGetOutputCustomStatus(int count, DateTime lastUpdateTime)
    {
        Count = count;
        LastUpdateTime = lastUpdateTime;
    }
}

public class StatusQueryGetOutputInput
{
    [JsonProperty("sleekflow_company_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    [System.ComponentModel.DataAnnotations.Required]
    public string TypeName { get; set; }

    [JsonConstructor]
    public StatusQueryGetOutputInput(string sleekflowCompanyId, string typeName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        TypeName = typeName;
    }
}

public class StatusQueryGetOutputOutput
{
    [JsonProperty("total_count")]
    public int TotalCount { get; set; }

    [JsonConstructor]
    public StatusQueryGetOutputOutput(int totalCount)
    {
        TotalCount = totalCount;
    }
}