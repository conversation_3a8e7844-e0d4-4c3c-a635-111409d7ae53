using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances.SQL;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiBusinessBalanceResynchronizationEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiBusinessBalanceResynchronizationEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiBusinessBalanceResynchronizationEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiBusinessBalanceResynchronizationEventConsumer
    : IConsumer<OnCloudApiBusinessBalanceResynchronizationEvent>
{
    private readonly ILockService _lockService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiBusinessBalanceResynchronizationEventConsumer> _logger;
    private readonly ITransactionLogCalculatedBusinessBalanceService _transactionLogCalculatedBusinessBalanceService;

    public OnCloudApiBusinessBalanceResynchronizationEventConsumer(
        ILockService lockService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiBusinessBalanceResynchronizationEventConsumer> logger,
        ITransactionLogCalculatedBusinessBalanceService transactionLogCalculatedBusinessBalanceService)
    {
        _logger = logger;
        _lockService = lockService;
        _businessBalanceService = businessBalanceService;
        _transactionLogCalculatedBusinessBalanceService = transactionLogCalculatedBusinessBalanceService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiBusinessBalanceResynchronizationEvent> context)
    {
        var cancellationToken = context.CancellationToken;
        var onCloudApiBusinessBalanceResynchronizationEvent = context.Message;
        var facebookBusinessId = onCloudApiBusinessBalanceResynchronizationEvent.FacebookBusinessId;

        var retryCount = context.GetRedeliveryCount();
        if (retryCount > 10)
        {
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId
            },
            TimeSpan.FromSeconds(
                60 * (OnCloudApiBusinessBalanceResynchronizationEventConsumerDefinition.LockDuration +
                      OnCloudApiBusinessBalanceResynchronizationEventConsumerDefinition.MaxAutoRenewDuration)),
            cancellationToken);
        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));
            return;
        }

        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
            _logger.LogWarning(
                "Unable To Locate Business Balance Information {FacebookBusinessId}",
                facebookBusinessId);
            return;
        }

        try
        {
            var transactionLogCalculatedBusinessBalance =
                await _transactionLogCalculatedBusinessBalanceService.AccumulateBusinessBalanceTransactionLogAsync(
                    businessBalance.FacebookBusinessId);

            if (transactionLogCalculatedBusinessBalance is null)
            {
                await _lockService.ReleaseAsync(@lock, cancellationToken);
                throw new SfInternalErrorException(
                    $"Unable To Locate Transaction Log Calculated Business Balance {facebookBusinessId}");
            }

            var businessBalanceSnapshot =
                JsonConvert.DeserializeObject<BusinessBalance>(JsonConvert.SerializeObject(businessBalance));

            businessBalance.Credit.Amount = transactionLogCalculatedBusinessBalance.Credit;
            businessBalance.Used.Amount = transactionLogCalculatedBusinessBalance.Used;
            businessBalance.Markup.Amount = transactionLogCalculatedBusinessBalance.Markup;

            if (transactionLogCalculatedBusinessBalance.TransactionHandlingFee != 0
                && businessBalance.TransactionHandlingFee == null)
            {
                businessBalance.TransactionHandlingFee = new Money(Currencies.Usd, 0);
            }

            if (businessBalance.TransactionHandlingFee is not null)
            {
                businessBalance.TransactionHandlingFee.Amount =
                    transactionLogCalculatedBusinessBalance.TransactionHandlingFee;
            }

            // By Waba Billing resynchronization
            if (businessBalance.WabaBalances is not null)
            {
                foreach (var wabaBalance in businessBalance.WabaBalances)
                {
                    try
                    {
                        var transactionLogCalculatedBusinessBalanceByWaba =
                            await _transactionLogCalculatedBusinessBalanceService
                                .AccumulateBusinessBalanceTransactionLogByFacebookWabaIdAsync(
                                    businessBalance.FacebookBusinessId,
                                    wabaBalance.FacebookWabaId);

                        if (transactionLogCalculatedBusinessBalanceByWaba is not null)
                        {
                            wabaBalance.Credit.Amount = transactionLogCalculatedBusinessBalanceByWaba.Credit;
                            wabaBalance.Used.Amount = transactionLogCalculatedBusinessBalanceByWaba.Used;
                            wabaBalance.Markup.Amount = transactionLogCalculatedBusinessBalanceByWaba.Markup;
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "[{Method}] Unable to resynchronize WabaBalance for Facebook Business Id {FacebookBusinessId}, Waba Id {WabaId}",
                            nameof(OnCloudApiBusinessBalanceResynchronizationEventConsumer),
                            businessBalance.FacebookBusinessId,
                            wabaBalance.FacebookWabaId);
                    }
                }
            }

            businessBalance.UpdatedAt = DateTimeOffset.UtcNow;

            var upsertBusinessBalanceState = await _businessBalanceService.UpsertBusinessBalanceAsync(
                AuditingOperation.OnCloudApiBusinessBalanceResynchronizationEvent,
                businessBalanceSnapshot,
                businessBalance,
                new Dictionary<string, object>
                {
                    {
                        "TransactionLogCalculatedBusinessBalance", transactionLogCalculatedBusinessBalance
                    }
                });

            _logger.LogInformation("{UpsertBusinessBalanceState}", upsertBusinessBalanceState);

            if (upsertBusinessBalanceState == 0)
            {
                await context.Redeliver(TimeSpan.FromMinutes(30));
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "[{Method}] Unexpected Error Occurs during resynchronizing business balance with Facebook Business Id {FacebookBusinessId}",
                nameof(OnCloudApiBusinessBalanceResynchronizationEventConsumer),
                onCloudApiBusinessBalanceResynchronizationEvent.FacebookBusinessId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);
            await context.Redeliver(TimeSpan.FromMinutes(10));
            return;
        }

        await _lockService.ReleaseAsync(@lock, cancellationToken);
    }
}