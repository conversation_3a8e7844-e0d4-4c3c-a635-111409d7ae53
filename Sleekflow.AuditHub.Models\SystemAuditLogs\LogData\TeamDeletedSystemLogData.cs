using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class TeamDeletedSystemLogData
{
    [JsonProperty("team_id")]
    public string TeamId { get; set; }

    [JsonProperty("team_name")]
    public string TeamName { get; set; }

    [JsonConstructor]
    public TeamDeletedSystemLogData(string teamId, string teamName)
    {
        TeamId = teamId;
        TeamName = teamName;
    }
}