﻿using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Subscriptions;

public interface IZohoSubscriptionRepository : IRepository<ZohoSubscription>
{
}

public class ZohoSubscriptionRepository
    : BaseRepository<ZohoSubscription>,
        IZohoSubscriptionRepository,
        ISingletonService
{
    public ZohoSubscriptionRepository(
        ILogger<BaseRepository<ZohoSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}