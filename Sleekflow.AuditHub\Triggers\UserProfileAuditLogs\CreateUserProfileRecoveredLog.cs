﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileRecoveredLog
    : ITrigger<
        CreateUserProfileRecoveredLog.CreateUserProfileRecoveredLogInput,
        CreateUserProfileRecoveredLog.CreateUserProfileRecoveredLogOutput>
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateUserProfileRecoveredLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateUserProfileRecoveredLogInput : IHasSleekflowUserProfileId
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public UserProfileRecoveredLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileRecoveredLogInput(
            string? sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            UserProfileRecoveredLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateUserProfileRecoveredLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateUserProfileRecoveredLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateUserProfileRecoveredLogOutput> F(CreateUserProfileRecoveredLogInput input)
    {
        var dataStr = JsonConvert.SerializeObject(input.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
            new UserProfileAuditLog(
                id,
                (distributedInvocationContext?.SleekflowCompanyId
                 ?? input.SleekflowCompanyId)
                ?? throw new InvalidOperationException(),
                distributedInvocationContext?.SleekflowStaffId
                ?? input.SleekflowStaffId,
                input.SleekflowUserProfileId,
                UserProfileAuditLogTypes.UserProfileRecovered,
                input.AuditLogText,
                data,
                DateTimeOffset.UtcNow,
                null));

        return new CreateUserProfileRecoveredLogOutput(id);
    }
}