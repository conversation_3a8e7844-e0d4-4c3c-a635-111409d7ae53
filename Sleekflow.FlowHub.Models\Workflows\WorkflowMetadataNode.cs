﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows;

public class WorkflowMetadataNode
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("data")]
    public WorkflowMetadataNodeData Data { get; set; }

    [JsonConstructor]
    public WorkflowMetadataNode(
        string id,
        WorkflowMetadataNodeData data)
    {
        Id = id;
        Data = data;
    }

    public bool IsNode =>
        string.Equals(Data.Category, "action", StringComparison.OrdinalIgnoreCase)
        || string.Equals(Data.Category, "timeDelay", StringComparison.OrdinalIgnoreCase)
        || string.Equals(Data.Category, "condition", StringComparison.OrdinalIgnoreCase);
}