using System.Collections.Concurrent;
using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.Kernels;

public interface ITokenCountingService
{
    void AddToken(string groupChatIdStr, string model, int count);

    Dictionary<string, int> GetTokenCounts(string groupChatIdStr);
}

public class TokenCountingService : ITokenCountingService, ISingletonService
{
    public ConcurrentDictionary<string, ConcurrentDictionary<string, int>> GroupChatIdToModelToTokens { get; set; } = new ();

    public void AddToken(string groupChatIdStr, string model, int count)
    {
        if (string.IsNullOrEmpty(groupChatIdStr))
        {
            return;
        }

        GroupChatIdToModelToTokens.AddOrUpdate(groupChatIdStr, new ConcurrentDictionary<string, int>(), (key, oldValue) => oldValue);
        GroupChatIdToModelToTokens[groupChatIdStr].AddOrUpdate(model, count, (key, oldValue) => oldValue + count);
    }

    public Dictionary<string, int> GetTokenCounts(string groupChatIdStr)
    {
        return GroupChatIdToModelToTokens
            .GetValueOrDefault(groupChatIdStr, new ConcurrentDictionary<string, int>())
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }
}