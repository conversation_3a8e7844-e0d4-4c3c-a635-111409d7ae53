﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

[TriggerGroup(ControllerNames.WebScraperRuns)]
public class AbortWebScraperRun : ITrigger<AbortWebScraperRun.AbortWebScraperRunInput, AbortWebScraperRun.AbortWebScraperRunOutput>
{
    private readonly IWebScraperService _webScraperService;

    public AbortWebScraperRun(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class AbortWebScraperRunInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperRun.PropertyNameApifyRunId)]
        public string ApifyRunId { get; set; }

        [JsonConstructor]
        public AbortWebScraperRunInput(string sleekflowCompanyId, string apifyRunId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyRunId = apifyRunId;
        }
    }

    public class AbortWebScraperRunOutput
    {
        [JsonProperty(WebScraperRun.PropertyNameWebScraperRun)]
        public WebScraperRun WebScraperRun { get; set; }

        [JsonConstructor]
        public AbortWebScraperRunOutput(WebScraperRun webScraperRun)
        {
            WebScraperRun = webScraperRun;
        }
    }

    public async Task<AbortWebScraperRunOutput> F(AbortWebScraperRunInput abortWebScraperRunInput)
    {
        var run = await _webScraperService.AbortRunAsync(abortWebScraperRunInput.SleekflowCompanyId, abortWebScraperRunInput.ApifyRunId);
        return new AbortWebScraperRunOutput(run);
    }
}