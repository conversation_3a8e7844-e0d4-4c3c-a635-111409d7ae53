using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Mvc.Authorizations;

public class Impersonator : IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_user_id")]
    public string? SleekflowUserId { get; set; } = string.Empty;

    [JsonProperty("sleekflow_tenanthub_user_id")]
    public string? SleekflowTenantHubUserId { get; set; } = string.Empty;

    [JsonProperty("sleekflow_staff_id")]
    public string? SleekflowStaffId { get; set; } = string.Empty;

    [JsonProperty("sleekflow_company_id")]
    public string? SleekflowCompanyId { get; set; } = string.Empty;
}