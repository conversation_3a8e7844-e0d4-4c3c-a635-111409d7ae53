using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries.Indexing;
using Sleekflow.IntelligentHub.Models.FaqAgents;
using Sleekflow.IntelligentHub.TextEnrichments;

namespace Sleekflow.IntelligentHub.FaqAgents;

public interface IFaqAgentSearchIndexer
{
    Task CreateSearchIndexAsync(string sleekflowCompanyId);

    Task IndexDocumentsAsync(IEnumerable<FaqAgentDocument> documents, string sleekflowCompanyId);
}

public class FaqAgentSearchIndexer : IFaqAgentSearchIndexer, IScopedService
{
    private readonly ILogger<FaqAgentSearchIndexer> _logger;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IKnowledgeBaseEntryIndexingService _knowledgeBaseEntryIndexingService;

    public FaqAgentSearchIndexer(
        ILogger<FaqAgentSearchIndexer> logger,
        ITextTranslationService textTranslationService,
        IKnowledgeBaseEntryIndexingService knowledgeBaseEntryIndexingService)
    {
        _logger = logger;
        _textTranslationService = textTranslationService;
        _knowledgeBaseEntryIndexingService = knowledgeBaseEntryIndexingService;
    }

    public async Task CreateSearchIndexAsync(string sleekflowCompanyId)
    {
        var indexClient = _knowledgeBaseEntryIndexingService.CreateIndexClient(sleekflowCompanyId);

        var indexNames = indexClient.GetIndexNamesAsync();
        await foreach (var page in indexNames.AsPages())
        {
            if (page.Values.Any(indexName => indexName == sleekflowCompanyId))
            {
                return;
            }
        }

        var index = new SearchIndex(sleekflowCompanyId)
        {
            Fields =
            {
                new SimpleField("id", SearchFieldDataType.String)
                {
                    IsKey = true
                },
                new SearchableField("content"),
                new SearchableField("content_en")
                {
                    AnalyzerName = "en.microsoft"
                },
                new SimpleField("sourcepage", SearchFieldDataType.String)
                {
                    IsFacetable = true
                },
                new SimpleField("sourcefile", SearchFieldDataType.String)
                {
                    IsFacetable = true
                },
            }
        };

        await indexClient.CreateIndexAsync(index);
    }

    public async Task IndexDocumentsAsync(
        IEnumerable<FaqAgentDocument> documents,
        string sleekflowCompanyId)
    {
        var searchClient = _knowledgeBaseEntryIndexingService.CreateSearchClient(sleekflowCompanyId);

        var iteration = 0;
        var batch = new IndexDocumentsBatch<SearchDocument>();
        foreach (var document in documents)
        {
            _logger.LogInformation($"Indexing document {document.Id}.");

            var contentInEnglish =
                await _textTranslationService.TranslateByAzureTranslationServiceAsync("en", document.Content);
            batch.Actions.Add(
                new IndexDocumentsAction<SearchDocument>(
                    IndexActionType.MergeOrUpload,
                    new SearchDocument
                    {
                        ["id"] = document.Id,
                        ["content"] = document.Content,
                        ["content_en"] = contentInEnglish,
                        ["sourcepage"] = document.SourcePage,
                        ["sourcefile"] = document.SourceFile
                    }));

            iteration++;
            if (iteration % 1_000 is 0)
            {
                // Every one thousand documents, batch create.
                IndexDocumentsResult result = await searchClient.IndexDocumentsAsync(batch);
                var succeeded = result.Results.Count(r => r.Succeeded);

                batch = new ();
            }

            _logger.LogInformation($"Indexed document {document.Id}.");
        }

        if (batch is { Actions.Count: > 0 })
        {
            // Any remaining documents, batch create.
            var index = new SearchIndex($"index-{batch.Actions.Count}");
            IndexDocumentsResult result = await searchClient.IndexDocumentsAsync(batch);
            var succeeded = result.Results.Count(r => r.Succeeded);
        }
    }
}