﻿using System.Globalization;

namespace Sleekflow.Extensions;

public static class ObjectExtensions
{
    public static bool IsFutureDateTimeString(
        this object? @object)
    {
        if (@object is null)
        {
            return false;
        }

        return @object is string objectStringValue
               && DateTime.TryParse(
                   objectStringValue,
                   DateTimeFormatInfo.InvariantInfo,
                   DateTimeStyles.AdjustToUniversal,
                   out var objectDateTimeValue)
               && objectDateTimeValue >= DateTime.UtcNow;
    }
}