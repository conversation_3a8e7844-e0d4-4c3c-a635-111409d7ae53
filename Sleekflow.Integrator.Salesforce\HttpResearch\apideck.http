POST https://unify.apideck.com/vault/sessions HTTP/1.1
Content-Type: application/json
Authorization: Bearer sk_live_e0caa731-dee3-4a2e-b34a-4769ab1b85e5-1f1NjNyCZzWyCgkUfTye-ce73adc7-795b-43c7-a1f8-7136960f6ba4
x-apideck-app-id: gtDDv4VcYVFpwmIsfTydvSF9DPS6pVPnENj4fTyd
x-apideck-consumer-id: <EMAIL>

###

GET https://unify.apideck.com/vault/connections HTTP/1.1
Authorization: Bearer sk_live_e0caa731-dee3-4a2e-b34a-4769ab1b85e5-1f1NjNyCZzWyCgkUfTye-ce73adc7-795b-43c7-a1f8-7136960f6ba4
x-apideck-app-id: gtDDv4VcYVFpwmIsfTydvSF9DPS6pVPnENj4fTyd
x-apideck-consumer-id: <EMAIL>

###

POST https://unify.apideck.com/vault/connections/crm/salesforce HTTP/1.1
Content-Type: application/json
Authorization: Bearer sk_live_e0caa731-dee3-4a2e-b34a-4769ab1b85e5-1f1NjNyCZzWyCgkUfTye-ce73adc7-795b-43c7-a1f8-7136960f6ba4
x-apideck-app-id: gtDDv4VcYVFpwmIsfTydvSF9DPS6pVPnENj4fTyd
x-apideck-consumer-id: <EMAIL>

{
  "path": {
    "service_id": "salesforce",
    "unified_api": "crm"
  }
}

###
GET https://unify.apideck.com/crm/contacts HTTP/1.1
Content-Type: application/json
Authorization: Bearer sk_live_e0caa731-dee3-4a2e-b34a-4769ab1b85e5-1f1NjNyCZzWyCgkUfTye-ce73adc7-795b-43c7-a1f8-7136960f6ba4
x-apideck-app-id: gtDDv4VcYVFpwmIsfTydvSF9DPS6pVPnENj4fTyd
x-apideck-consumer-id: <EMAIL>
x-apideck-service-id: salesforce