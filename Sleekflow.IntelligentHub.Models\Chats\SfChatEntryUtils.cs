using System.Text;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Chats;

public static class SfChatEntryUtils
{
    public static (string UserQuestion, string ChatHistoryStr) ToQuestionAndChatHistoryStr(
        List<SfChatEntry> sfChatEntries)
    {
        var lastUserChatEntries = sfChatEntries
            .TakeLast(sfChatEntries.Count - sfChatEntries.FindLastIndex(e => e.<PERSON><PERSON> is not null))
            .Where(e => e.User is not null)
            .Select(x => x.User)
            .ToList();
        if (lastUserChatEntries.Count == 0)
        {
            lastUserChatEntries = sfChatEntries.Where(e => e.User is not null).Select(x => x.User).ToList();
        }

        var userQuestion = string.Join("\n", lastUserChatEntries);
        var chatHistorySb = ToChatHistoryStr(sfChatEntries);
        return (userQuestion!, chatHistorySb.ToString());
    }

    public static string ToChatHistoryStr(List<SfChatEntry> sfChatEntries, List<string>? clearCommands = null)
    {
        var chatHistorySb = new StringBuilder();
        foreach (var sfChatEntry in sfChatEntries)
        {
            var user = sfChatEntry.User?.Replace("\n", " ");
            var bot = sfChatEntry.Bot?.Replace("\n", " ");
            if (user is not null && bot is not null)
            {
                chatHistorySb.Append($"user:{user}\nassistant:{bot}\n");
            }
            else if (user is not null)
            {
                chatHistorySb.Append($"user:{user}\n");

                // If the user sends a clear command, clear the chat history
                if (clearCommands is not null && clearCommands.Exists(c => user.Contains(c)))
                {
                    chatHistorySb.Clear();
                }
            }
            else if (bot is not null)
            {
                chatHistorySb.Append($"assistant:{bot}\n");
            }
        }

        return chatHistorySb.ToString();
    }
}