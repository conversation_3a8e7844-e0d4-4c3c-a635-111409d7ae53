using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.PaymentProviderConfigs;

public interface IPaymentProviderConfigValidator
{
    Task AssertValidPaymentProviderConfigAsync(
        string sleekflowCompanyId,
        List<string> storeIds,
        Dictionary<string, List<string>> storeIdToCurrencyIsoCodesDict,
        List<string> supportedCurrencyIsoCodes);
}

public class PaymentProviderConfigValidator : IPaymentProviderConfigValidator, IScopedService
{
    public Task AssertValidPaymentProviderConfigAsync(
        string sleekflowCompanyId,
        List<string> storeIds,
        Dictionary<string, List<string>> storeIdToCurrencyIsoCodesDict,
        List<string> supportedCurrencyIsoCodes)
    {
        // TODO
        // if (paymentProviderCurrencyMapping.StoreIds != null &&
        //     paymentProviderCurrencyMapping.StoreIds.Contains(storeId))
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new (
        //             $"store {storeId} is already linked to currency {currencyIsoCode} and provider {paymentProviderName}")
        //     });
        // }
        return Task.CompletedTask;
    }
}