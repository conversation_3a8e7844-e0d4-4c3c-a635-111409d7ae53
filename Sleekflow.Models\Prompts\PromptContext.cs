using Newtonsoft.Json;

namespace Sleekflow.Models.Prompts;

public class PromptContext
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("additional_prompt")]
    public string? AdditionalPrompt { get; set; }

    [JsonConstructor]
    public PromptContext(
        string name,
        string? additionalPrompt)
    {
        Name = name;
        AdditionalPrompt = additionalPrompt;
    }
}