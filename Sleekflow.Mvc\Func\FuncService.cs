using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog.Context;
using Sleekflow.DistributedInvocations;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Mvc.Configs;
using Sleekflow.Mvc.Func.Abstractions;
using Sleekflow.Outputs;
using Sleekflow.Persistence.Abstractions;
using PathString = Microsoft.AspNetCore.Http.PathString;

namespace Sleekflow.Mvc.Func;

public interface IFuncService
{
    Task<ActionResult<Output<TOutput>>> Run1Async<TInput, TOutput>(
        (HttpRequest HttpRequest, TInput Input) req,
        Func<TInput, Task<TOutput>> f,
        PathString requestPath,
        List<IFuncFilter>? funcFilters = null);
}

public class FuncService : IFuncService
{
    private const int MaxSize = 8192;

    private readonly ILogger<FuncService> _logger;
    private readonly IRequestService _requestService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IDynamicFiltersRepositoryContext _dynamicFiltersRepositoryContext;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public FuncService(
        ILogger<FuncService> logger,
        IRequestService requestService,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _logger = logger;
        _requestService = requestService;
        _serviceProvider = serviceProvider;
        _dynamicFiltersRepositoryContext = dynamicFiltersRepositoryContext;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    private void EnsureInputIsValidated<T>(T input)
    {
        if (input == null)
        {
            throw new SfValidationException(new List<ValidationResult>());
        }

        var validationResults = new List<ValidationResult>();
        var isValid = Validator.TryValidateObject(
            input,
            new ValidationContext(input, _serviceProvider, null),
            validationResults,
            validateAllProperties: true);
        if (isValid == false)
        {
            throw new SfValidationException(validationResults);
        }
    }

    public async Task<ActionResult<Output<TOutput>>> Run1Async<TInput, TOutput>(
        (HttpRequest HttpRequest, TInput Input) req,
        Func<TInput, Task<TOutput>> f,
        PathString requestPath,
        List<IFuncFilter>? funcFilters = null)
    {
        var stopWatch = Stopwatch.StartNew();

        var shouldRecordRequest =
            !BypassPathConfig.IsPathBypassed(requestPath);

        var shouldLogRequest =
            requestPath != "/Providers/GetProviderConfigs"
            && requestPath != "/Objects/GetObjectsByIdentities"
            && shouldRecordRequest;

        var httpRequest = req.HttpRequest;
        var request = shouldRecordRequest
            ? await _requestService.CreateRequestAsync(httpRequest, requestPath)
            : await _requestService.CreateLocalRequestAsync(httpRequest, requestPath);

        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;
        var environment = Environment.GetEnvironmentVariable("SF_ENVIRONMENT", target) ?? "Local";

        httpRequest.Headers.TryGetValue(
            IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
            out var encodedDistributedInvocationContext);

        if (!string.IsNullOrEmpty(encodedDistributedInvocationContext))
        {
            _distributedInvocationContextService.SetIncomingContext(encodedDistributedInvocationContext!);
        }

        using (LogContext.PushProperty("SfRequestId", request.Id))
        using (LogContext.PushProperty("SfEnvironment", environment))
        {
            try
            {
                EnsureInputIsValidated(req.Input);

                if (funcFilters != null && funcFilters.Any())
                {
                    foreach (var funcFilter in funcFilters)
                    {
                        await funcFilter.FilterAsync(httpRequest);
                    }
                }

                _dynamicFiltersRepositoryContext.SleekflowCompanyId =
                    httpRequest.Headers.TryGetValue("X-SLEEKFLOW-COMPANY-ID", out var sleekflowCompanyIds)
                        ? sleekflowCompanyIds.FirstOrDefault(id => !string.IsNullOrEmpty(id))
                        : null;

                var inputJson = JsonConvert.SerializeObject(
                    req.Input,
                    JsonConfig.DefaultJsonSerializerSettings);

                if (shouldLogRequest)
                {
                    _logger.LogInformation(
                        "RequestId {RequestId} RequestPath {RequestPath} Input {Input}",
                        request.Id,
                        request.Path,
                        inputJson.Length < MaxSize ? inputJson : inputJson[..MaxSize]);
                }

                var data = await f.Invoke(req.Input);
                var output = Ok(data, request.Id);

                if (shouldRecordRequest)
                {
                    await _requestService.PatchRequestOutputAsync(
                        request.Id,
                        stopWatch.ElapsedMilliseconds,
                        success: true);
                }

                var outputJson = JsonConvert.SerializeObject(
                    output,
                    JsonConfig.DefaultJsonSerializerSettings);

                if (shouldLogRequest)
                {
                    _logger.LogInformation(
                        "RequestId {RequestId} RequestPath {RequestPath} Output {Output}",
                        request.Id,
                        request.Path,
                        outputJson.Length < MaxSize ? outputJson : outputJson[..MaxSize]);
                }

                return new OkObjectResult(output);
            }
            catch (SfValidationException e)
            {
                _logger.LogError(e, "Caught an SfValidationException");

                var output = BadRequest(e.ValidationResults, request.Id);

                if (shouldRecordRequest)
                {
                    await _requestService.PatchRequestOutputAsync(
                        request.Id,
                        stopWatch.ElapsedMilliseconds,
                        success: false,
                        input: req.Input,
                        output: output,
                        exception: e);
                }

                var outputJson = JsonConvert.SerializeObject(
                    output,
                    JsonConfig.DefaultJsonSerializerSettings);
                _logger.LogInformation(
                    "RequestId {RequestId} RequestPath {RequestPath} Output {Output}",
                    request.Id,
                    request.Path,
                    outputJson.Length < MaxSize ? outputJson : outputJson[..MaxSize]);

                return new OkObjectResult(output);
            }
            catch (SfUnauthorizedException e)
            {
                _logger.LogError(e, "Caught an SfUnauthorizedException");

                var output = Error(401, "Unauthorized", request.Id);

                if (shouldRecordRequest)
                {
                    await _requestService.PatchRequestOutputAsync(
                        request.Id,
                        stopWatch.ElapsedMilliseconds,
                        success: false,
                        input: req.Input,
                        output: output,
                        exception: e);
                }

                var outputJson = JsonConvert.SerializeObject(
                    output,
                    JsonConfig.DefaultJsonSerializerSettings);
                _logger.LogInformation(
                    "RequestId {RequestId} RequestPath {RequestPath} Output {Output}",
                    request.Id,
                    request.Path,
                    outputJson.Length < MaxSize ? outputJson : outputJson[..MaxSize]);

                return new OkObjectResult(output);
            }
            catch (SfAccessDeniedException e)
            {
                _logger.LogError(e, "Caught an SfAccessDeniedException");

                var output = Error(403, "Access Denied", request.Id);

                if (shouldRecordRequest)
                {
                    await _requestService.PatchRequestOutputAsync(
                        request.Id,
                        stopWatch.ElapsedMilliseconds,
                        success: false,
                        input: req.Input,
                        output: output,
                        exception: e);
                }

                var outputJson = JsonConvert.SerializeObject(
                    output,
                    JsonConfig.DefaultJsonSerializerSettings);
                _logger.LogInformation(
                    "RequestId {RequestId} RequestPath {RequestPath} Output {Output}",
                    request.Id,
                    request.Path,
                    outputJson.Length < MaxSize ? outputJson : outputJson[..MaxSize]);

                return new OkObjectResult(output);
            }
            catch (ErrorCodeException e)
            {
                _logger.LogError(e, "Caught an ErrorCodeException");

                var output = Error(
                    500,
                    e.Message,
                    request.Id,
                    e.ErrorCode,
                    JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                        e.SerializedContext,
                        JsonConfig.DefaultJsonSerializerSettings));

                if (shouldRecordRequest)
                {
                    await _requestService.PatchRequestOutputAsync(
                        request.Id,
                        stopWatch.ElapsedMilliseconds,
                        success: false,
                        input: req.Input,
                        output: output,
                        exception: e);
                }

                var outputJson = JsonConvert.SerializeObject(
                    output,
                    JsonConfig.DefaultJsonSerializerSettings);
                _logger.LogInformation(
                    "RequestId {RequestId} RequestPath {RequestPath} Output {Output}",
                    request.Id,
                    request.Path,
                    outputJson.Length < MaxSize ? outputJson : outputJson[..MaxSize]);

                return new OkObjectResult(output);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Caught an exception");

                var output = Error(500, "Internal exception", request.Id, ErrorCodeConstant.SfInternalException);

                if (shouldRecordRequest)
                {
                    await _requestService.PatchRequestOutputAsync(
                        request.Id,
                        stopWatch.ElapsedMilliseconds,
                        success: false,
                        input: req.Input,
                        output: output,
                        exception: e);
                }

                var outputJson = JsonConvert.SerializeObject(
                    output,
                    JsonConfig.DefaultJsonSerializerSettings);
                _logger.LogInformation(
                    "RequestId {RequestId} RequestPath {RequestPath} Output {Output}",
                    request.Id,
                    request.Path,
                    outputJson.Length < MaxSize ? outputJson : outputJson[..MaxSize]);

                return new OkObjectResult(output);
            }
            finally
            {
                stopWatch.Stop();
            }
        }
    }

    private static Output<T> Ok<T>(T data, string requestId)
    {
        return new Output<T>(
            true,
            200,
            data,
            message: null,
            errorCode: null,
            errorContext: null,
            requestId);
    }

    private static Output<object?> Error(
        int statusCode,
        string message,
        string requestId,
        int? errorCode = null,
        Dictionary<string, object?>? errorContext = null)
    {
        return new Output<object?>(
            false,
            statusCode,
            data: null,
            message: message,
            errorCode: errorCode,
            errorContext: errorContext,
            requestId);
    }

    private static Output<object?> BadRequest(
        List<ValidationResult> validationResults,
        string requestId,
        int? errorCode = null,
        Dictionary<string, object?>? errorContext = null)
    {
        return new Output<object?>(
            false,
            400,
            data: validationResults,
            message: "The input is incorrect.",
            errorCode: errorCode,
            errorContext: errorContext,
            requestId);
    }
}