using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Events;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Events;

namespace Sleekflow.IntelligentHub.Consumers;

public class RecommendReplyStreamingEndpointEventConsumerDefinition
    : ConsumerDefinition<RecommendReplyStreamingEndpointEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<RecommendReplyStreamingEndpointEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public partial class RecommendReplyStreamingEndpointEventConsumer : IConsumer<RecommendReplyStreamingEndpointEvent>
{
    private readonly IChatService _chatService;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubConcurrentUsageService _intelligentHubConcurrentUsageService;

    public RecommendReplyStreamingEndpointEventConsumer(
        IChatService chatService,
        ITextTranslationService textTranslationService,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubConcurrentUsageService intelligentHubConcurrentUsageService)
    {
        _chatService = chatService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _intelligentHubConcurrentUsageService = intelligentHubConcurrentUsageService;
        _textTranslationService = textTranslationService;
    }

    public async Task Consume(ConsumeContext<RecommendReplyStreamingEndpointEvent> context)
    {
        try
        {
            var message = context.Message.Input;
            var sleekflowCompanyId = message.SleekflowCompanyId;
            var conversationContext = message.ConversationContext;

            var (asyncEnumerable, _, sourcesStr) = await _chatService.StreamAnswerAsync(
                conversationContext,
                sleekflowCompanyId);

            // the correlation id just need to be unique for this stream
            var correlationId = NewId.NextGuid();
            var nextSequenceNumber = 0;

            var fullAnswerSb = new StringBuilder();
            await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
            {
                if (partialAnswer.Length == 0)
                {
                    continue;
                }

                fullAnswerSb.Append(partialAnswer);

                var partialRecommendedReply = CleanAnswer(partialAnswer);
                await context.Publish(
                    new OnRecommendedReplyStreamingEndpointEmittedEvent(
                        correlationId,
                        message.SessionId,
                        nextSequenceNumber++,
                        partialRecommendedReply,
                        message.ClientRequestId));
            }

            await context.Publish(
                new OnRecommendedReplyStreamingEndpointFinishedEvent(
                    correlationId,
                    message.SessionId,
                    nextSequenceNumber,
                    message.ClientRequestId));

            var recommendedReply = CleanAnswer(fullAnswerSb.ToString());

            await _intelligentHubUsageService.RecordUsageAsync(
                sleekflowCompanyId,
                PriceableFeatures.RecommendReply,
                null,
                new RecommendReplySnapshot(
                    JsonConvert.SerializeObject(
                        conversationContext,
                        JsonConfig.DefaultLoggingJsonSerializerSettings),
                    sourcesStr ?? string.Empty,
                    recommendedReply));
        }
        finally
        {
            await _intelligentHubConcurrentUsageService.DecrementConcurrentUsageAsync(
                context.Message.Input.SleekflowCompanyId);
        }
    }

    private static string CleanAnswer(string answer)
    {
        return MyRegex().Replace(answer, string.Empty);
    }

    [GeneratedRegex(@"\[source\]", RegexOptions.IgnoreCase, "en-HK")]
    private static partial Regex MyRegex();
}