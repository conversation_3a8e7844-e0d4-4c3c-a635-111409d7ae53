﻿using Newtonsoft.Json;

namespace Sleekflow.Models.Links;

public class ShortenLinkRequest
{
    public string LongUrl { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string? Title { get; set; }

    public string? Domain { get; set; }

    [JsonConstructor]
    public ShortenLinkRequest(
        string longUrl,
        string sleekflowCompanyId,
        string? title,
        string? domain = null)
    {
        LongUrl = longUrl;
        SleekflowCompanyId = sleekflowCompanyId;
        Title = title;
        Domain = domain;
    }
}