using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class GetFlowHubConfig : ITrigger
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public GetFlowHubConfig(
        IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    public class GetFlowHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetFlowHubConfigInput(
            string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetFlowHubConfigOutput
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; }

        [JsonConstructor]
        public GetFlowHubConfigOutput(
            FlowHubConfig flowHubConfig)
        {
            FlowHubConfig = flowHubConfig;
        }
    }

    public async Task<GetFlowHubConfigOutput> F(GetFlowHubConfigInput getFlowHubConfigInput)
    {
        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(
            getFlowHubConfigInput.SleekflowCompanyId);

        return new GetFlowHubConfigOutput(flowHubConfig);
    }
}