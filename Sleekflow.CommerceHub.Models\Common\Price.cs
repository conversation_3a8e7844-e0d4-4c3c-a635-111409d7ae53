using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Models.Common;

public class Price : IValidatableObject
{
    [JsonConstructor]
    public Price(
        string currencyIsoCode,
        decimal amount)
    {
        CurrencyIsoCode = currencyIsoCode;
        Amount = amount;
    }

    [Required]
    [StringLength(3, MinimumLength = 3)]
    [JsonProperty("currency_iso_code")]
    public string CurrencyIsoCode { get; set; }

    [Required]
    [JsonProperty("amount")]
    public decimal Amount { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var validationResults = new List<ValidationResult>();

        if (Amount < 0)
        {
            validationResults.Add(
                new ValidationResult(
                    "Amount cannot be negative",
                    new[]
                    {
                        nameof(Amount)
                    }));
        }

        var regionInfo = CultureUtils.GetRegionInfoByCurrencyIsoCode(CurrencyIsoCode);
        if (regionInfo == null)
        {
            validationResults.Add(
                new ValidationResult(
                    "CurrencyIsoCode is invalid",
                    new[]
                    {
                        nameof(CurrencyIsoCode)
                    }));
        }

        return validationResults;
    }
}