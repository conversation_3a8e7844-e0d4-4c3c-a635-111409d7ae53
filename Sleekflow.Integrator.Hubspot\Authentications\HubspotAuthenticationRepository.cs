﻿using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Authentications;

public interface IHubspotAuthenticationRepository : IRepository<HubspotAuthentication>
{
}

public class HubspotAuthenticationRepository
    : BaseRepository<HubspotAuthentication>,
        IHubspotAuthenticationRepository,
        ISingletonService
{
    public HubspotAuthenticationRepository(
        ILogger<BaseRepository<HubspotAuthentication>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}