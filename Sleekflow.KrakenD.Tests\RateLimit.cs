using System.Collections.Concurrent;
using System.Net;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.KrakenD.Tests;

public class RateLimit
{
    [SetUp]
    public void Setup()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Result counts vary overtime");
        }

    }

    [Test]
    public void Test1()
    {
        var httpClient = new HttpClient();
        var concurrentBag = new ConcurrentBag<HttpStatusCode>();

        var cts = new CancellationTokenSource(new TimeSpan(0, 0, 0, 500));
        Parallel.For(
            0L,
            10L,
            index =>
            {
                var requestMessage = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri =
                        new Uri(
                            $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/Healthz"),
                };
                var httpResponseMsg = httpClient.Send(requestMessage, cts.Token);

                concurrentBag.Add(httpResponseMsg.StatusCode);
            });
        cts.Dispose();

        var okCount = concurrentBag.Count(cb => cb == HttpStatusCode.OK);
        var tooManyRequestsCount = concurrentBag.Count(cb => cb == HttpStatusCode.TooManyRequests);

        // Health counts vary overtime
        Assert.That(okCount, !Is.Zero);
        Assert.That(tooManyRequestsCount, !Is.Zero);
    }
}