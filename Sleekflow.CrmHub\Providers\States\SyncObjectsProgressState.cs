using Newtonsoft.Json;
using Sleekflow.CrmHub.Providers.Models;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Providers.States;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("sys_state_sync_objects_progress")]
public class SyncObjectsProgressState : Entity
{
    public const string PropertyNameStateObj = "state_obj";

    public class SyncObjectsProgressStateObj
    {
        [JsonProperty("durable_payload")]
        public DurablePayload DurablePayload { get; set; }

        [JsonProperty("query_output")]
        public StatusQueryGetOutput<
            StatusQueryGetOutputInput,
            StatusQueryGetOutputCustomStatus,
            StatusQueryGetOutputOutput>? QueryOutput { get; set; }

        [JsonConstructor]
        public SyncObjectsProgressStateObj(
            DurablePayload durablePayload,
            StatusQueryGetOutput<
                    StatusQueryGetOutputInput,
                    StatusQueryGetOutputCustomStatus,
                    StatusQueryGetOutputOutput>?
                queryOutput)
        {
            DurablePayload = durablePayload;
            QueryOutput = queryOutput;
        }
    }

    [JsonProperty("state_name")]
    public string StateName { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameStateObj)]
    public SyncObjectsProgressStateObj StateObj { get; set; }

    public SyncObjectsProgressState(
        string id,
        string stateName,
        string providerName,
        SyncObjectsProgressStateObj stateObj,
        string sleekflowCompanyId)
        : base(id, "SyncObjectsProgressState")
    {
        StateName = stateName;
        ProviderName = providerName;
        StateObj = stateObj;
        SleekflowCompanyId = sleekflowCompanyId;
    }
}