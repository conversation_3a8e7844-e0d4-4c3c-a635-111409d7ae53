﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.Text;
using Scriban;
using Sleekflow.Analyzers.Triggers;

namespace Sleekflow.Analyzers;

[Generator]
public class TriggerGroupGenerator : ISourceGenerator
{
    public void Initialize(GeneratorInitializationContext context)
    {
        context.RegisterForSyntaxNotifications(() => new AttributeSyntaxReceiver<TriggerGroupAttribute>());
    }

    public void Execute(GeneratorExecutionContext context)
    {
        if (context.SyntaxReceiver is not AttributeSyntaxReceiver<TriggerGroupAttribute> syntaxReceiver)
        {
            return;
        }

        var endpoints = new List<Endpoint>();
        foreach (var classSyntax in syntaxReceiver.Classes)
        {
            try
            {
                // Converting the class to semantic model to access much more meaningful data.
                var model = context.Compilation.GetSemanticModel(classSyntax.SyntaxTree);

                // Parse to declared symbol, so you can access each part of code separately, such as interfaces, methods, members, constructor parameters etc.
                var symbol = ModelExtensions.GetDeclaredSymbol(model, classSyntax);

                // Finding my GenerateServiceAttribute over it. I'm sure this attribute is placed, because my syntax receiver already checked before.
                // So, I can surely execute following query.
                var triggerGroupAttribute = classSyntax.AttributeLists
                    .SelectMany(sm => sm.Attributes)
                    .First(
                        x => x.Name.ToString()
                            .EnsureEndsWith("Attribute")
                            .Equals(nameof(TriggerGroupAttribute)));

                // Arguments
                // 1. GroupName
                // 2. BaseRoute
                // 3. FilterNames
                var arguments = triggerGroupAttribute.ArgumentList!.Arguments;

                var groupName =
                    (string) model.GetConstantValue(arguments[0].Expression).Value!;
                var baseRoute =
                    arguments.Count > 1
                    && (arguments[1].Expression.IsKind(SyntaxKind.InterpolatedStringExpression) ||
                        arguments[1].Expression.IsKind(SyntaxKind.StringLiteralExpression))
                        ? (string) model.GetConstantValue(arguments[1].Expression).Value!
                        : null;
                var filterNamesTokens =
                    arguments.Count > 2
                        ? arguments[2].DescendantTokens()
                            .Where(f => model.GetConstantValue(f.Parent!).HasValue)
                            .Select(f => (string) model.GetConstantValue(f.Parent!).Value!)
                            .Distinct()
                            .ToList()
                        : null;

                var namespaceName = GetNamespaceRecursively(symbol!.ContainingNamespace);

                var filters = filterNamesTokens == null
                    ? new List<Filter>()
                    : filterNamesTokens
                        .Select(
                            f =>
                                new Filter(
                                    f,
                                    f.Split('.').Last().Remove(0, 1).ToLowerFirstChar()))
                        .ToList();

                endpoints.Add(
                    new Endpoint(
                        new Trigger(
                            baseRoute,
                            groupName,
                            namespaceName,
                            symbol.Name,
                            symbol.Name.ToLowerFirstChar()),
                        filters,
                        baseRoute));
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        var controllerNameToSourceCodeStrings = endpoints
            .GroupBy(t => t.Trigger.ControllerName)
            .Select(
                tg => new
                {
                    ControllerName = tg.Key,
                    SourceCodeStr = GetSourceCodeStrFor(tg.OrderBy(t => t.Trigger.ClassName).ToList(), tg.Key)
                })
            .ToList();

        foreach (var controllerNameToSourceCodeString in controllerNameToSourceCodeStrings)
        {
            context.AddSource(
                $"{controllerNameToSourceCodeString.ControllerName}Controller.g.cs",
                SourceText.From(controllerNameToSourceCodeString.SourceCodeStr, Encoding.UTF8));
        }
    }

    private string GetSourceCodeStrFor(List<Endpoint> endpoints, string controllerName)
    {
        var templateStr = GetEmbeddedResource("Sleekflow.Analyzers.Templates.controller.cs.liquid");

        var template = Template.ParseLiquid(templateStr);

        // Since we have grouped by an controller name it would be assuming that all the controller route
        // and base name should be the same through out the controller functions
        var (trigger, _, baseRoute) = endpoints[0];
        var result = template.Render(
            new
            {
                ControllerRoute =
                    baseRoute != null
                        ? baseRoute + "/" + trigger.GroupName
                        : "[Controller]",
                ControllerNamespaceName =
                    endpoints[0].Trigger.NamespaceName.Replace("Triggers", "Controllers"),
                ControllerName =
                    controllerName,
                Endpoints =
                    endpoints,
                Filters = endpoints.Select(c => c.Filters)
                    .SelectMany(f => f).GroupBy(f => f.FullyQualifiedName)
                    .Select(f => f.First()).ToList()
            });

        return SyntaxFactory.ParseCompilationUnit(result).NormalizeWhitespace().GetText().ToString();
    }

    private string GetEmbeddedResource(string path)
    {
        using var stream = typeof(TriggerGroupGenerator).Assembly.GetManifestResourceStream(path);
        using var streamReader = new StreamReader(stream!);

        return streamReader.ReadToEnd();
    }

    private string GetNamespaceRecursively(INamespaceSymbol symbol)
    {
        if (symbol.ContainingNamespace == null)
        {
            return symbol.Name;
        }

        return (GetNamespaceRecursively(symbol.ContainingNamespace) + "." + symbol.Name).Trim('.');
    }
}