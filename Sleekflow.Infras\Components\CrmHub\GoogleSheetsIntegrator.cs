﻿using Pulumi;
using Pulumi.AzureNative.Cache;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using AppConfiguration = Pulumi.AzureNative.AppConfiguration;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.CrmHub;

public class GoogleSheetsIntegrator
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly CrmHubDb.CrmHubDbOutput _crmHubDbOutput;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly AppConfiguration.ConfigurationStore _appConfig;
    private readonly Db.DbOutput _dbOutput;
    private readonly MyConfig _myConfig;
    private readonly CrmHubIntegrationDb.CrmHubIntegrationDbOutput _crmHubIntegrationDbOutput;
    private readonly GcpConfig _gcpConfig;

    public GoogleSheetsIntegrator(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        CrmHubDb.CrmHubDbOutput crmHubDbOutput,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        AppConfiguration.ConfigurationStore appConfig,
        Db.DbOutput dbOutput,
        MyConfig myConfig,
        CrmHubIntegrationDb.CrmHubIntegrationDbOutput crmHubIntegrationDbOutput,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _crmHubDbOutput = crmHubDbOutput;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _appConfig = appConfig;
        _dbOutput = dbOutput;
        _myConfig = myConfig;
        _crmHubIntegrationDbOutput = crmHubIntegrationDbOutput;
        _gcpConfig = gcpConfig;
    }

    public List<App.ContainerApp> InitGoogleSheetsIntegrator()
    {
        var primaryAppConfigReadOnlyConnStr = Output
            .Tuple(_resourceGroup.Name, _appConfig.Name)
            .Apply(
                items => AppConfiguration.ListConfigurationStoreKeys.Invoke(
                    new AppConfiguration.ListConfigurationStoreKeysInvokeArgs
                    {
                        ResourceGroupName = items.Item1, ConfigStoreName = items.Item2
                    }))
            .Apply(o => o.Value.First(v => v.Name == "Primary Read Only").ConnectionString);

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.GoogleSheetsIntegrator),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.GoogleSheetsIntegrator))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => ListRedisKeys.InvokeAsync(
                        new ListRedisKeysArgs()
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));
            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));
            var worker = managedEnvAndAppsTuple.GetWorkerApp(ServiceNames.GoogleSheetsIntegrator);

            var listWorkerHostKeysResult = Output
                .Tuple(worker.Name, _resourceGroup.Name, worker.Id)
                .Apply(
                    items => Web.ListWebAppHostKeys.Invoke(
                        new Web.ListWebAppHostKeysInvokeArgs
                        {
                            Name = items.Item1, ResourceGroupName = items.Item2,
                        }));

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.GoogleSheetsIntegrator));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = 1,
                            MaxReplicas = 20,
                            Rules = new App.Inputs.ScaleRuleArgs
                            {
                                Name = $"http",
                                Http = new App.Inputs.HttpScaleRuleArgs()
                                {
                                    Metadata = new InputMap<string>()
                                    {
                                        {
                                            "concurrentRequests", "40"
                                        }
                                    }
                                }
                            },
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-gs-in-app",
                                Image = myImage.BaseImageName,
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 0.25, Memory = ".5Gi"
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APP_CONFIGURATION_CONN_STR",
                                            Value = primaryAppConfigReadOnlyConnStr,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.Integrator.GoogleSheets",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region CrmHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CRM_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_crmHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CRM_HUB_DB_KEY", Value = _crmHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CRM_HUB_DB_DATABASE_ID", Value = _crmHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region CrmHubIntegrationDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CRM_HUB_INTEGRATION_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_crmHubIntegrationDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CRM_HUB_INTEGRATION_DB_KEY",
                                            Value = _crmHubIntegrationDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_CRM_HUB_INTEGRATION_DB_DATABASE_ID",
                                            Value = _crmHubIntegrationDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region WorkerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_HOSTNAME",
                                            Value = worker.DefaultHostName.Apply(hn => "https://" + hn),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_FUNCTIONS_KEY",
                                            Value = listWorkerHostKeysResult
                                                .Apply(l => l.FunctionKeys!["default"]),
                                        },

                                        #endregion

                                        #region GoogleSheetsConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_OAUTH_STATE_ENCRYPTION_KEY",
                                            Value = "encrypt_key_20241104",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_CLIENT_ID",
                                            Value =
                                                "1094012517897-s35ob15i9veimj5265pa424i3dfpmop1.apps.googleusercontent.com",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_CLIENT_SECRET",
                                            Value =
                                                "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",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_READ_QUOTA_PER_MINUTE",
                                            Value = "1500",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_WRITE_QUOTA_PER_MINUTE",
                                            Value = "600"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_READ_QUOTA_PER_MINUTE_PER_USER",
                                            Value = "120",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "GOOGLE_SHEETS_WRITE_QUOTA_PER_MINUTE_PER_USER",
                                            Value = "120",
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.GoogleSheetsIntegrator, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }
}