﻿using System.Collections.Immutable;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Webhooks;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Webhooks;

namespace Sleekflow.MessagingHub.Events;

public class OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertConsumerDefinition
    : ConsumerDefinition<OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEventConsumer>
{
    private const string EastAsiaProdEnv = "https://sleekflow-core-app-eas-production.azurewebsites.net";
    private const string EastAsiaStagingEnv = "https://sleekflow-core-app-eas-staging.azurewebsites.net";
    private const string EastAsiaDevEnv = "https://sleekflow-core-app-eas-dev.azurewebsites.net";
    private const string UatEnv = "https://sleekflow-prod-api-uat.azurewebsites.net";
    private const string EastUsProdEnv = "https://sleekflow-core-app-eus-production.azurewebsites.net";
    private const string EastUsStagingEnv = "https://sleekflow-core-app-eus-staging.azurewebsites.net";
    private const string EastUsDevEnv = "https://sleekflow-core-app-eus-dev.azurewebsites.net";
    private const string SouthEastAsiaProdEnv = "https://sleekflow-core-app-seas-production.azurewebsites.net";
    private const string UnitedArabEmiratesProdEnv = "https://sleekflow-core-app-uaen-production.azurewebsites.net";


    public static readonly List<string> AllUrls = new ()
    {
        EastAsiaProdEnv,
        EastAsiaStagingEnv,
        EastAsiaDevEnv,
        UatEnv,
        EastUsProdEnv,
        EastUsStagingEnv,
        EastUsDevEnv,
        SouthEastAsiaProdEnv,
        UnitedArabEmiratesProdEnv
    };

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEventConsumer
    : IConsumer<OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEvent>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEventConsumer> _logger;
    private readonly IWhatsappCloudApiBusinessBalanceWebhookService _whatsappCloudApiBusinessBalanceWebhookService;
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;

    public OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEventConsumer(
        IWabaService wabaService,
        ILogger<OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEventConsumer> logger,
        IWhatsappCloudApiBusinessBalanceWebhookService whatsappCloudApiBusinessBalanceWebhookService,
        IMessagingHubWebhookService messagingHubWebhookService)
    {
        _wabaService = wabaService;
        _logger = logger;
        _whatsappCloudApiBusinessBalanceWebhookService = whatsappCloudApiBusinessBalanceWebhookService;
        _messagingHubWebhookService = messagingHubWebhookService;
    }

    public async Task Consume(
        ConsumeContext<OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEvent> context)
    {
        var retryCount = context.GetRedeliveryCount();

        if (retryCount > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCompaniesBusinessBalanceChangedEventWebhooksUpsertEvent}",
                JsonConvert.SerializeObject(context.Message));

            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var cancellationToken = context.CancellationToken;
        var wabas = await _wabaService.GetAllAsync();


        var facebookBusinessIdSleekflowCompanyIdsDict = wabas.GroupBy(x => x.FacebookBusinessId)
            .ToDictionary(x => x.Key!, x => x.SelectMany(y => y.SleekflowCompanyIds).ToHashSet());

        foreach (var (facebookBusinessId, sleekflowCompanyIds) in facebookBusinessIdSleekflowCompanyIdsDict)
        {
            foreach (var sleekflowCompanyId in sleekflowCompanyIds)
            {
                try
                {
                    var existingSleekflowCompanyWabaPhoneNumberWebhooks =
                        await _messagingHubWebhookService.GetWebhooksWithSleekflowCompanyIdAsync(
                            sleekflowCompanyId,
                            WebhookEntityTypeNames.WabaPhoneNumber,
                            WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                            cancellationToken);

                    if (!existingSleekflowCompanyWabaPhoneNumberWebhooks.Any())
                    {
                        continue;
                    }

                    var sleekflowCoreDomainUrls = new HashSet<string>();
                    foreach (var existingWabaPhoneNumberWebhook in existingSleekflowCompanyWabaPhoneNumberWebhooks)
                    {
                        if (string.IsNullOrEmpty(existingWabaPhoneNumberWebhook.Url))
                        {
                            continue;
                        }

                        if (!Uri.TryCreate(existingWabaPhoneNumberWebhook.Url, UriKind.Absolute, out var domainUri))
                        {
                            continue;
                        }

                        // uri.Scheme : https
                        // uri.Host : sleekflow-core-app-eas-production.azurewebsites.net
                        var receivingMessagingWebhookDomainUrl = domainUri.Scheme + "://" + domainUri.Host;

                        if (OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertConsumerDefinition.AllUrls.Contains(receivingMessagingWebhookDomainUrl))
                        {
                            sleekflowCoreDomainUrls.Add(receivingMessagingWebhookDomainUrl);
                        }
                    }

                    foreach (var sleekfowCoreDomainUrl in sleekflowCoreDomainUrls)
                    {
                        var webhookUrl = sleekfowCoreDomainUrl + "/whatsapp/cloudapi/businessbalance/webhook";
                        await _whatsappCloudApiBusinessBalanceWebhookService.UpsertWebhookAsync(
                            sleekflowCompanyId,
                            facebookBusinessId,
                            webhookUrl,
                            cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error occurred while processing OnCompaniesBusinessBalanceChangedEventWebhooksUpsertEventConsumer for company {CompanyId} facebook business {FacebookBusinessId}",
                        sleekflowCompanyId,
                        facebookBusinessId);
                    await context.Redeliver(TimeSpan.FromSeconds(8));
                }
            }
        }
    }
}