using Newtonsoft.Json;
using Sleekflow.TenantHub.Models.Constants;

namespace Sleekflow.DataMigrator.Configs;

public class TravisBackendClientConfig
{
    [JsonConstructor]
    public TravisBackendClientConfig(
        string endpoint,
        Dictionary<string, string> secretKey,
        string location = ServerLocations.EastAsia)
    {
        Endpoint = endpoint;
        SecretKey = secretKey;
        Location = location;
    }

    public string Endpoint { get; set; }

    public Dictionary<string, string> SecretKey { get; set; }

    public string? Location { get; set; }
}