using System.Diagnostics.CodeAnalysis;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using OpenAI.Chat;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Documents;

public class TokenUsageTracker
{
    // Provider-specific token counts
    public int GeminiInputTokenCount { get; private set; } = 0;

    public int GeminiOutputTokenCount { get; private set; } = 0;

    public int OpenAiInputTokenCount { get; private set; } = 0;

    public int OpenAiOutputTokenCount { get; private set; } = 0;

    public TokenUsageTracker()
    {
    }

    [Experimental("SKEXP0070")]
    public void RecordTokenUsage(ChatMessageContent content)
    {
        switch (content)
        {
            case GeminiChatMessageContent geminiChatMessageContent:
            {
                if (geminiChatMessageContent.Metadata == null)
                {
                    throw new Exception("No token usage data found.");
                }

                var inputTokenCount = (int) (geminiChatMessageContent.Metadata["PromptTokenCount"] ??
                                             throw new Exception("Missing PromptTokenCount."));
                var outputTokenCount = (int) (geminiChatMessageContent.Metadata["CandidatesTokenCount"] ??
                                              throw new Exception("Missing CandidatesTokenCount."));

                // Update Gemini-specific counts
                GeminiInputTokenCount += inputTokenCount;
                GeminiOutputTokenCount += outputTokenCount;

                Console.WriteLine(
                    $"Gemini token Usage: {inputTokenCount + outputTokenCount} Total: {GeminiInputTokenCount + GeminiOutputTokenCount}");
                break;
            }

            case OpenAIChatMessageContent openAiChatMessageContent:
            {
                if (openAiChatMessageContent.InnerContent is not ChatCompletion completion)
                {
                    throw new Exception("No token usage data found.");
                }

                var inputTokenCount = completion.Usage.InputTokenCount;
                var outputTokenCount = completion.Usage.OutputTokenCount;

                // Update OpenAI-specific counts
                OpenAiInputTokenCount += inputTokenCount;
                OpenAiOutputTokenCount += outputTokenCount;

                Console.WriteLine(
                    $"AzureOpenAI token Usage: {inputTokenCount + outputTokenCount} Total: {OpenAiInputTokenCount + OpenAiOutputTokenCount}");
                break;
            }

            default:
                throw new Exception($"Unexpected ChatMessageContent type: {content.GetType()}");
        }
    }
}