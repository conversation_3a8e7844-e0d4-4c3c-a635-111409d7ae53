using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Carts;

public class CartDto : AuditEntityDto
{
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(Cart.PropertyNameLineItems)]
    public List<CartLineItemDto> LineItems { get; set; }

    [JsonProperty(Cart.PropertyNameCartDiscount)]
    public Discount? CartDiscount { get; set; }

    [JsonProperty(Cart.PropertyNameCartStatus)]
    public string CartStatus { get; set; }

    [JsonConstructor]
    public CartDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        List<CartLineItemDto> lineItems,
        Discount? cartDiscount,
        string cartStatus)
        : base(
            id,
            sleekflowCompanyId,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt)
    {
        StoreId = storeId;
        LineItems = lineItems;
        CartDiscount = cartDiscount;
        CartStatus = cartStatus;
    }

    public CartDto(
        Cart cart,
        Dictionary<string, ProductVariant> idToProductVariantDict,
        Dictionary<string, Product> idToProductDict)
        : this(
            cart.Id,
            cart.SleekflowCompanyId,
            cart.CreatedBy,
            cart.UpdatedBy,
            cart.CreatedAt,
            cart.UpdatedAt,
            cart.StoreId,
            cart.LineItems
                .Where(
                    li =>
                        idToProductVariantDict.ContainsKey(li.ProductVariantId)
                        && idToProductDict.ContainsKey(idToProductVariantDict[li.ProductVariantId].ProductId))
                .Select(
                    li =>
                        new CartLineItemDto(
                            li,
                            new ProductVariantDto(
                                idToProductVariantDict[li.ProductVariantId],
                                idToProductDict[idToProductVariantDict[li.ProductVariantId].ProductId]),
                            idToProductDict[li.ProductId] is not null
                                ? new ProductDto(idToProductDict[li.ProductId], new List<ProductVariant>())
                                : null))
                .ToList(),
            cart.CartDiscount,
            cart.CartStatus)
    {
    }
}