using Newtonsoft.Json;

namespace Sleekflow.Models.ActionEvents.CrmHub;

public class CreatePropertyDrivenSchemafulObjectEvent
{
    [JsonProperty("schema_unique_name")]
    public string SchemaUniqueName { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_user_profile_id")]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty("property_values")]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [JsonProperty("created_via")]
    public string CreatedVia { get; set; }

    [JsonConstructor]
    public CreatePropertyDrivenSchemafulObjectEvent(
        string schemaUniqueName,
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        Dictionary<string, object?> propertyValues,
        string createdVia)
    {
        SchemaUniqueName = schemaUniqueName;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        PropertyValues = propertyValues;
        CreatedVia = createdVia;
    }
}