using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Events.EventHub;

public interface IEventHubConfig
{
    string EventHubConnStr { get; }
}

public class EventHubConfig : IConfig, IEventHubConfig
{
    public string EventHubConnStr { get; private set; }

    public EventHubConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        EventHubConnStr =
            Environment.GetEnvironmentVariable("EVENT_HUB_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("EVENT_HUB_CONN_STR");
    }
}