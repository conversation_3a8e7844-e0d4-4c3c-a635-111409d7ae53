using System.Collections.Concurrent;
using System.ComponentModel;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Plugins;
using Description = System.ComponentModel.DescriptionAttribute;

namespace Sleekflow.IntelligentHub.Evaluator;

public class MockInformationGatheringPlugin : IInformationGatheringPlugin
{
    private readonly ILogger<MockInformationGatheringPlugin> _logger;
    private readonly InformationGatheringPlugin _realPlugin;

    // Store calls by test ID
    private static readonly ConcurrentDictionary<string, InformationGatheringCallInfo> CallInfo = new ();

    public MockInformationGatheringPlugin(
        ILogger<MockInformationGatheringPlugin> logger,
        InformationGatheringPlugin realPlugin)
    {
        _logger = logger;

        // Get the real plugin from the service provider
        _realPlugin = realPlugin;
    }

    [KernelFunction]
    [Description("Extracts field information from conversation context based on required fields")]
    [return: Description("A JSON string containing the extracted field information")]
    public async Task<InformationGatheringPlugin.ExtractFieldsOutput> ExtractFieldsAsync(
        Kernel kernel,
        [Description("The conversation context to extract information from")]
        string conversationContext,
        [Description("The list of required fields to extract information for")]
        List<RequiredField>? requiredFields = null)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return await _realPlugin.ExtractFieldsAsync(kernel, conversationContext);
        }

        // Record the start time
        var startTime = DateTime.UtcNow;

        // Call the real plugin to extract the fields
        var result = await _realPlugin.ExtractFieldsAsync(kernel, conversationContext, requiredFields);

        // Calculate processing time
        var processingTime = DateTime.UtcNow - startTime;

        var callInfo = CallInfo.GetOrAdd(testId, _ => new InformationGatheringCallInfo());
        callInfo.ExtractFieldsCalled = true;
        callInfo.ExtractedFields = result.GatheredFields;
        callInfo.ProcessingTimeMs = processingTime.TotalMilliseconds;

        _logger.LogInformation(
            "Information extraction for test {TestId}: Extracted {FieldCount} fields in {ProcessingTime}ms",
            testId,
            result.GatheredFields.Count,
            processingTime.TotalMilliseconds);

        return result;
    }

    /// <summary>
    /// Checks if ExtractFields was called for a specific test.
    /// </summary>
    public static bool WasExtractFieldsCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.ExtractFieldsCalled;
    }

    /// <summary>
    /// Get the fields extracted for a specific test.
    /// </summary>
    public static List<InformationGatheringPlugin.ExtractedField>? GetExtractedFields(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.ExtractedFields : null;
    }

    /// <summary>
    /// Calculates a time efficiency bonus based on processing time (0.0 to 1.0).
    /// </summary>
    public static double GetProcessingTimeBonus(string testId)
    {
        if (!CallInfo.TryGetValue(testId, out var info) || !info.ExtractFieldsCalled)
        {
            return 0.0;
        }

        // Define thresholds for processing time
        const double fastThreshold = 500.0; // ms
        const double slowThreshold = 2000.0; // ms

        if (info.ProcessingTimeMs <= fastThreshold)
        {
            return 1.0; // Full bonus for fast processing
        }

        if (info.ProcessingTimeMs >= slowThreshold)
        {
            return 0.0; // No bonus for slow processing
        }

        // Linear scale between thresholds
        return 1.0 - ((info.ProcessingTimeMs - fastThreshold) / (slowThreshold - fastThreshold));
    }

    /// <summary>
    /// Clears the stored call info for a specific test.
    /// </summary>
    public static void ClearTestState(string testId)
    {
        CallInfo.TryRemove(testId, out _);
    }
}

public class InformationGatheringCallInfo
{
    public bool ExtractFieldsCalled { get; set; }

    public List<InformationGatheringPlugin.ExtractedField>? ExtractedFields { get; set; }

    public double ProcessingTimeMs { get; set; }
}