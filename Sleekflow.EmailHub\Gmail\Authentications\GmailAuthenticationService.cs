using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.Gmail.v1;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Configs;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Gmail.Authentications;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.EmailHub.Gmail.Authentications;

public interface IGmailAuthenticationService : IEmailAuthenticationService, IEmailOAuthService
{
}

public class GmailAuthenticationService : IScopedService, IGmailAuthenticationService
{
    private readonly IGmailConfig _gmailConfig;
    private readonly ILogger<GmailAuthenticationService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IEmailAuthenticationRepository _emailAuthenticationRepository;
    private readonly IIdService _idService;

    public GmailAuthenticationService(
        IHttpClientFactory httpClientFactory,
        ILogger<GmailAuthenticationService> logger,
        IGmailConfig gmailConfig,
        IEmailAuthenticationRepository emailAuthenticationRepository,
        IIdService idService)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _logger = logger;
        _gmailConfig = gmailConfig;
        _emailAuthenticationRepository = emailAuthenticationRepository;
        _idService = idService;
    }

    public async Task<EmailAuthentication> GetAuthenticationAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string? serverType = null,
        CancellationToken cancellationToken = default)
    {
        var authentication = (await _emailAuthenticationRepository.GetObjectsAsync(
                x=>
                    x.SleekflowCompanyId == sleekflowCompanyId &&
                    x.EmailAddress == emailAddress &&
                    x.ProviderName == ProviderNames.Gmail,
                cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new SfInternalErrorException(
                                              $"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        if (gmailAuthenticationMetadata.ExpiresIn != null &&
            DateTimeOffset.UtcNow - TimeSpan.FromSeconds(30) - gmailAuthenticationMetadata.IssuedAt <
            TimeSpan.FromSeconds((long) gmailAuthenticationMetadata.ExpiresIn))
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(sleekflowCompanyId, emailAddress, cancellationToken);
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? extendedAuthMetadata,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Started AuthenticationAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
        var nonce = Guid.NewGuid().ToString();
        var clientId = _gmailConfig.ClientId;
        var redirectUri = _gmailConfig.RedirectUri;
        var scope = GmailService.Scope.MailGoogleCom;
        const string responseType = "code";
        const string accessType = "offline";
        const string prompt = "consent";

        var queryParams = new Dictionary<string, string>
        {
            {
                "client_id", clientId
            },
            {
                "redirect_uri", redirectUri
            },
            {
                "response_type", responseType
            },
            {
                "scope", scope
            },
            {
                "access_type", accessType
            },
            {
                "prompt", prompt
            },
            {
                "state", nonce
            }
        };

        var gmailRedirectToGoogleServerUrl = $"https://accounts.google.com/o/oauth2/v2/auth?" +
                                             $"{string.Join(
                                                 "&",
                                                 queryParams
                                                     .Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"))}";

        _logger.LogInformation(
            "AuthenticateAsync: redirecting to google's OAuth server: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        string authenticationId;

        try
        {
            authenticationId = (await GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken)).Id;
        }
        catch (SfUnauthorizedException)
        {
            authenticationId = _idService.GetId("EmailAuthentication");
        }

        await _emailAuthenticationRepository.UpsertAsync(
            new EmailAuthentication(
                id: authenticationId,
                sleekflowCompanyId: sleekflowCompanyId,
                emailAddress: emailAddress,
                isAuthenticated: false,
                new GmailAuthenticationMetadata(
                    nonce: nonce),
                ProviderNames.Gmail),
            authenticationId,
            cancellationToken: cancellationToken);

        return gmailRedirectToGoogleServerUrl;
    }

    public async Task<EmailAuthentication> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var authentication = (await _emailAuthenticationRepository.GetObjectsAsync(
                x=>
                    x.SleekflowCompanyId == sleekflowCompanyId &&
                    x.EmailAddress == emailAddress &&
                    x.ProviderName == ProviderNames.Gmail,
                cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        if (!authentication.IsAuthenticated)
        {
            return authentication;
        }

        _logger.LogInformation(
            "Started Gmail ReAuthenticateAndStoreAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new SfInternalErrorException(
                                              $"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        var clientId = _gmailConfig.ClientId;
        var clientSecret = _gmailConfig.ClientSecret;
        var refreshToken = gmailAuthenticationMetadata.RefreshToken ?? throw new SfUnauthorizedException();

        var getTokenRequestBody = new FormUrlEncodedContent(
            new Dictionary<string, string>
            {
                ["client_id"] = clientId,
                ["client_secret"] = clientSecret,
                ["refresh_token"] = refreshToken,
                ["grant_type"] = "refresh_token",
            });
        getTokenRequestBody.Headers.Clear();
        getTokenRequestBody.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
        var getTokenResponse = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", getTokenRequestBody, cancellationToken);
        var getTokenResponseStr = await getTokenResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!getTokenResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(getTokenResponseStr);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} Gmail ReAuthenticateAndStoreAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                getTokenResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException($"HTTP[{getTokenResponse.StatusCode}] {errorMsg}");
        }

        var token = JsonConvert.DeserializeObject<TokenResponse>(getTokenResponseStr);

        _logger.LogInformation(
            "Completed Gmail ReAuthenticateAndStoreAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        gmailAuthenticationMetadata.AccessToken = token!.AccessToken;
        gmailAuthenticationMetadata.RefreshToken = token.RefreshToken ?? gmailAuthenticationMetadata.RefreshToken;
        gmailAuthenticationMetadata.IssuedAt = DateTimeOffset.UtcNow - TimeSpan.FromSeconds(30);
        gmailAuthenticationMetadata.ExpiresIn = token.ExpiresInSeconds;
        gmailAuthenticationMetadata.TokenType = token.TokenType;
        await _emailAuthenticationRepository.UpsertAsync(authentication, authentication.Id, cancellationToken: cancellationToken);

        return authentication;
    }

    public async Task HandleAuthCallbackAndStoreAsync(
        string code,
        string nonce,
        CancellationToken cancellationToken = default)
    {
        var pendingAuthentication = (await _emailAuthenticationRepository.GetObjectsAsync(
            x =>
                x.ProviderName == ProviderNames.Gmail &&
                ((GmailAuthenticationMetadata)x.EmailAuthenticationMetadata).Nonce == nonce,
            cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (pendingAuthentication == null)
        {
            return;
        }

        var clientId = _gmailConfig.ClientId;
        var clientSecret = _gmailConfig.ClientSecret;
        var redirectUri = _gmailConfig.RedirectUri;

        var getTokenRequestBody = new FormUrlEncodedContent(
            new Dictionary<string, string>
            {
                ["client_id"] = clientId,
                ["client_secret"] = clientSecret,
                ["code"] = code,
                ["grant_type"] = "authorization_code",
                ["redirect_uri"] = redirectUri,
            });
        getTokenRequestBody.Headers.Clear();
        getTokenRequestBody.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
        var getTokenResponse = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", getTokenRequestBody, cancellationToken);
        var getTokenResponseStr = await getTokenResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!getTokenResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(getTokenResponseStr);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} HandleAuthCallbackAndStoreAsync fails: cannot exchange for token",
                getTokenResponse.StatusCode,
                errorMsg);

            throw new SfInternalErrorException($"HTTP[{getTokenResponse.StatusCode}] {errorMsg}");
        }

        var token = JsonConvert.DeserializeObject<TokenResponse>(getTokenResponseStr);

        var gmailAuthenticationMetadata =
            pendingAuthentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
            throw new NullReferenceException("Cannot parse gmailAuthenticationMetaData");
        gmailAuthenticationMetadata.AccessToken = token!.AccessToken;
        gmailAuthenticationMetadata.TokenType = token.TokenType;
        gmailAuthenticationMetadata.RefreshToken = token.RefreshToken;
        gmailAuthenticationMetadata.ExpiresIn = token.ExpiresInSeconds;
        gmailAuthenticationMetadata.IssuedAt = DateTimeOffset.UtcNow - TimeSpan.FromSeconds(30);
        pendingAuthentication.IsAuthenticated = true;
        gmailAuthenticationMetadata.Raw = getTokenResponseStr;
        await _emailAuthenticationRepository.UpsertAsync(pendingAuthentication, pendingAuthentication.Id, cancellationToken: cancellationToken);

        _logger.LogInformation(
            "HandleAuthCallbackAndStoreAsync successes for nonce: {nonce}",
            nonce);
    }
}