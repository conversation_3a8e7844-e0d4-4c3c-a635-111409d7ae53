config:
  auth0:client_id:
    secure: AAABAMePtn8J8CqCxu9LwRLJoUONNJjJjOC5SRFlV1ZmUjNzdcBtm9V8Sly+PqZ6tIu7lKrItjYzzlgYj1DoUQ==
  auth0:client_secret:
    secure: AAABALKlS99llnR6O9RcM+HtfI+x9yRZ7z1Jz8IMpGQtBNmFsKkIKWr3lqKrIt5uWR4+h4u7osTgJmpPKuMTr0QwL6pv9YoM0+QxGZ81U5ps+2ZnPIKlp/ONSmWZBjiL
  auth0:domain: sleekflow.eu.auth0.com
  core_sql_db:connection_string_eas:
    secure: AAABAP/cy3Mk5UqxSC4VVzsdyve6k4WIYdBB9EZWyPg1Gl2WRULWVfSu4YCEw5rLiExC/QgsbkksK7DLb2yMAM5UTKrgEUd3Ad0CJA3W4DnHSNvsH4qge+****************************************************************************************************************+lmGaIzJYmJ70cFDpYeQa0gM/I38nIGMCpinSmyGVUcO+8P4GXo7MxIDUxWTdaij5KgzEvygBVmpv/L3mOgYflHTrTTN1KzAZcxspX5JL8O5GEOTr6RTfm0RqILtWeLetjaraBpLOXp3+99P2R2mJavq5/uizYo4WBgt51chrLq+2IKsx2tBVfJd+EpJHt4qLFKLLyuODmwhE27jfwOni4/8Ifis4TLzgIYNDinuzKWatNYni+3lvoz1hbH5uquDYBGL0Z7+CXX7PRhsHbJx6J2/grxnxfRMb7T4bdXyY/4WWsU+aYcn1EfwkzvkYqs8eOyYUqXcGQRXZY0qG4IH8XQD4+oxrip6Y9+VOcYJnke2o=
  core_sql_db:connection_string_eus:
    secure: AAABAEeMXiYHGF1a/bqWWVRn8pGHJ5RTvFi82pYSyKquKQyWq1waPI+nwNmFeJQqPQvm+hDyMGCJx3qbmGSgPFiqtI70Vf9bcimwiZ9yuzswj/gwDC/j3/PJjQGpGsLZxVh/dW8MRKKF4kXZxXm/f2HALB0WGNQGUbDUyrkOICKesYcB5NhecRJ0+Z5VyYvQuc12Jra6X9yP3/UPcpe9GN4fmdxuUed52v/************************************************************************+TOhvRgZSOy7r+LidJGb+1LciYPfPppAt0E3NcNaEJ21gNU+tJ/Z/n9pgE601llZifWTPvfZCG58lAq+jm8GTQkotrGX/lthwS1Wd0dmvM7OMLmZIY6A+arOesHJahUaznbrTqHDUyoQRXmWfGz/KtGdRvf42zYE7ZdUveyJhtK5aaBHlIQ/s402TsvTT4YMWRENHG0PvPTbwNifElOrK+PPBIRLJcOgy+LXy/JayYQreFLgTxwZVyOHifq9IBPF3V2mo7vJnLZFwZnBqxYBuAw2l485pWMIuMahUr6ZAv7MSBpd9eKW3vEaf3R0EYOU=
  core_sql_db:connection_string_seas:
    secure: AAABAI3KdMU9jmRB1IPIUwLJJvXt7lUmWKEkb/DGoXaSNvv6sWDI0s1yAP3Gb5AklCztYbNELsT5I2hK7N88phdhqEFP7leIieeNNMZ643Uqnz6dSTOAgIxCXRUiWh9APFY/Oro0f+s5atlHoC8Rwv6wdiw4JK4hzi/K75jOie9cQb6eBKn+JG72dapbRcMEsrTlR6YKfZmAi5fPkfC8kp/3VBr5mW1o630REby2rWY+Iso5FemiUc4Jv+SecQAILyWKzr+x/aGZghqLN4RC8xowd/zJJHt+ItYQprVmt5ur9b/az9HKfEYUB86GakBmGtI613RD6JpkL3yh4as5QOQG1ydxIUUjQZsJM/lx1x1jxQ3GzzLXGDOKN5QCaO9Fgs3rDTfgiW7veNbZDG5bIl9jCAIpc23qYmKariWutUeaK4V4fDlWTD9e+R4gCLEDkeb+ApzwWlbWozHBBJxB0M/H5/ITU3cOWoXqKA09TxNCM3I1RO0fBG/zYeixgNXx2JLSBH8X0kADyiDhBlSh6126f0rJVWH+EARkbaXN9z4Dy4PfwTN3u5mmRnbrxgkrdfl5ZaxFNwvYTGkyGti89eCmj5xRzZ91YLBFqAxK88sI/w==
  core_sql_db:connection_string_uaen:
    secure: AAABAJiQJOKVXzb69MW01HkEQmJHXtRmjlZH+OqXgCYJwgTUj060CiHHm6QI/tu5J8LmTw//LM60kzNPr3CHbEiTxOETYRtv6ZI7deJexRJVSFGKW76dhDaE2NR9uv3fSeS2iLnUd9xSVHYnCyTiPj871wILP+Iat5uO+OAh+3kn09e8RFrGlEz3fgCZBREMmcz6pOaS63SyRYoj9hR4XG3tJgNRpqjUtgGVdDx0vTFFHsB0a/pCBg3QxGRuSssuUjY/2HRHSpA1Et7bGN9FmkkCJHrE0NNV1v6aLEXkNlZOKO2JWBgVa80Ha6KBRnDw3IHRq7SYM2VfwsArKcL1k0BYNGoqw+zxDZ/20GNua21KYUqCz7Q8MuoFB7NqwUblCEhBLZTlWbShSTRSPEE3Q2FHIXekgiUy1Dsl+MsY+qFO2bff070hv4PaXFYUOQ6RbN1B2jGr6ADKu41oPRVO/k0Hy5+WPC+xRGemfiNF+X0OaxzxU827HmNsz8Dpu9TAZc9leiYjv+j4ExFdym5OZzhvGhoCXuytwLvP5LU6xftUrvslNaSGTm2Sebl5rCZY9gggT5rR7hQQwEg+zsyJnyfl4CYawz1XJ9dTaBVDlNYqEg==
  core_sql_db:connection_string_weu:
    secure: AAABAOMyXufuZ5Dk7ZqBKf0LCKH0y1mlRjvOopcqpmXSe+7iLhZXbTJo/aozE++aIJeOP5ddgB2K1/h2zsJbuGcY2g+bWTWqr7utsSfgCIaNRZ/vvQa2BlNHvCkAjNZXJdNI2xAKnUMplvE3mpqNOV5BK1+UrJgEn6rnrR/s8sf+uJm21ir5fK9cUCqZ5fJBSU/axSlAV/3kl7ROqkhl01Wi/6vc4CG9e64YzY/nllEAWf1CpMjGzeTkvBP4mGo3GvsdjujI02gA+sgxw9bQokt4py4sFhMbl8VnhFNcw0Zghy8IErjffqDTS7We35YPTcWVZ7Qr3Khi8tpWPjvSrrv6ezdTIqLY6mP+R+1Uc1OnDnkOdArQe+2lZadfEhRtruCIjSJdmX+0eksjYAbzf8Z/jJU0y8WLu3WlAi3lK8LEGMjrRHNHi5M4ksjLz6GtmVEcv3vk1GP+d/LMIqhiQHDPPhAX60fJjkePsxxMEIpbqNDXCElNhNLESIPfD6ub/GpJYSwQPi/zvNDUBwr4fm8neSW4cuZJ89hJLPcO25ZJyTLSo8gPnAhwPHWxCGm68xb/DCDG0vckgjhISYsVAGITZbxx2lnTRoPJZiljjys=
  azure-native:location: EastAsia
  pulumi:template: azure-csharp
  gcp:project-id: my-production-project-405815
  gcp:credential-json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  sleekflow:name: production
  sleekflow:origins:
    - https://app.sleekflow.io
    - https://cn.sleekflow.io
    - https://sleekflow-preproduction.azureedge.net
    - https://preprod-us.sleekflow.io
    - https://staging.sleekflow.io
    - https://beta.sleekflow.io
    - https://preprod-us-revamp.sleekflow.io
    - https://staging-revamp.sleekflow.io
    - https://next.sleekflow.io
    - https://v1.sleekflow.io
  sleekflow:clients:
    - name: sleekflow-api
      allowed_logout_urls: [ ]
      allowed_origins:
        - https://api.sleekflow.io
        - https://sleekflow-prod-api.azurewebsites.net
        - https://sleekflow-prod-api-auth0.azurewebsites.net
      app_type: non_interactive
      callbacks:
        - https://api.sleekflow.io
        - https://sleekflow-prod-api.azurewebsites.net
        - https://sleekflow-prod-api-auth0.azurewebsites.net
      grant_types:
        - client_credentials
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      token_endpoint_auth_method: client_secret_post
      web_origins:
        - https://api.sleekflow.io
        - https://sleekflow-prod-api.azurewebsites.net
        - https://sleekflow-prod-api-auth0.azurewebsites.net

    - name: sleekflow-api-health-check
      allowed_logout_urls: [ ]
      allowed_origins:
        - https://api.sleekflow.io
        - https://sleekflow-prod-api.azurewebsites.net
        - https://sleekflow-prod-api-auth0.azurewebsites.net
        - https://sleekflow-core-app-eas-production.azurewebsites.net
        - https://sleekflow-core-app-eus-production.azurewebsites.net
        - https://sleekflow-core-app-seas-production.azurewebsites.net
        - https://sleekflow-prod-api-preproduction.azurewebsites.net
      app_type: non_interactive
      callbacks:
        - https://api.sleekflow.io
        - https://sleekflow-prod-api.azurewebsites.net
        - https://sleekflow-prod-api-auth0.azurewebsites.net
        - https://sleekflow-core-app-eas-production.azurewebsites.net
        - https://sleekflow-core-app-eus-production.azurewebsites.net
        - https://sleekflow-core-app-seas-production.azurewebsites.net
        - https://sleekflow-prod-api-preproduction.azurewebsites.net
      grant_types:
        - client_credentials
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      token_endpoint_auth_method: client_secret_post
      web_origins:
        - https://api.sleekflow.io
        - https://sleekflow-prod-api.azurewebsites.net
        - https://sleekflow-prod-api-auth0.azurewebsites.net
        - https://sleekflow-core-app-eas-production.azurewebsites.net
        - https://sleekflow-core-app-eus-production.azurewebsites.net
        - https://sleekflow-core-app-seas-production.azurewebsites.net
        - https://sleekflow-prod-api-preproduction.azurewebsites.net

    - name: sleekflow-client-powerflow-app
      initiate_login_uri: https://powerflow.sleekflow.io
      allowed_logout_urls:
        - https://powerflow.sleekflow.io
        - https://powerflow.sleekflow.io/logout
        - https://powerflowdev.z7.web.core.windows.net
        - https://powerflowdev.z7.web.core.windows.net/logout
      allowed_origins:
        - https://powerflow.sleekflow.io
        - https://powerflowdev.z7.web.core.windows.net
      app_type: spa
      callbacks:
        - https://powerflow.sleekflow.io
        - https://powerflow.sleekflow.io/callback
        - https://powerflowdev.z7.web.core.windows.net
        - https://powerflowdev.z7.web.core.windows.net/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      token_endpoint_auth_method: none
      web_origins:
        - https://powerflow.sleekflow.io
        - https://powerflowdev.z7.web.core.windows.net

    - name: sleekflow-client-auth0-actions
      allowed_logout_urls: [ ]
      allowed_origins: [ ]
      app_type: non_interactive
      callbacks: [ ]
      grant_types:
        - client_credentials
      token_endpoint_auth_method: client_secret_post
      web_origins: [ ]

    - name: sleekflow-client-web-app
      initiate_login_uri: https://v1.sleekflow.io
      allowed_logout_urls:
        - https://app.sleekflow.io
        - https://cn.sleekflow.io
        - https://sleekflow-preproduction.azureedge.net
        - https://preprod-us.sleekflow.io
        - https://staging.sleekflow.io
        - https://perf.sleekflow.io/
        - https://adfs.shkp.com/adfs/ls?wa=wsignoutcleanup1.0
        - https://sso-uat1.hongyip.com/auth/realms/hy-sso-uat1/protocol/openid-connect/logout
        - https://v1.sleekflow.io
      allowed_origins:
        - https://app.sleekflow.io
        - https://cn.sleekflow.io
        - https://sleekflow-preproduction.azureedge.net
        - https://preprod-us.sleekflow.io
        - https://staging.sleekflow.io
        - https://perf.sleekflow.io/
        - https://v1.sleekflow.io
      app_type: spa
      callbacks:
        - https://app.sleekflow.io
        - https://cn.sleekflow.io
        - https://sleekflow-preproduction.azureedge.net
        - https://preprod-us.sleekflow.io
        - https://staging.sleekflow.io
        - https://app.sleekflow.io/settings/opt-in
        - https://app.sleekflow.io/settings/templates
        - https://perf.sleekflow.io/
        - https://v1.sleekflow.io
        - https://v1.sleekflow.io/settings/templates
        - https://v1.sleekflow.io/settings/opt-in
        - https://v1.sleekflow.io/settings/inbox
        - https://v1.sleekflow.io/channels
        - https://v1.sleekflow.io/en-US/settings/templates
        - https://v1.sleekflow.io/zh-HK/settings/templates
        - https://v1.sleekflow.io/zh-CN/settings/templates
        - https://v1.sleekflow.io/pt-BR/settings/templates
        - https://v1.sleekflow.io/it-IT/settings/templates
        - https://v1.sleekflow.io/id-ID/settings/templates
        - https://v1.sleekflow.io/de-DE/settings/templates
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - https://app.sleekflow.io
        - https://cn.sleekflow.io
        - https://sleekflow-preproduction.azureedge.net
        - https://preprod-us.sleekflow.io
        - https://staging.sleekflow.io
        - https://perf.sleekflow.io/
        - https://v1.sleekflow.io

    - name: sleekflow-client-mobile-app
      allowed_logout_urls:
        - https://sso.sleekflow.io/logout/callback
        - https://app.sleekflow.io
      allowed_origins: [ ]
      app_type: native
      callbacks:
        - sleekflowauth0://sleekflow.eu.auth0.com/android/io.sleekflow.sleekflow/callback
        - io.sleekflow.sleekflow://sleekflow.eu.auth0.com/ios/io.sleekflow.sleekflow/callback
        - sleekflowauth0://sso.sleekflow.io/android/io.sleekflow.sleekflow/callback
        - io.sleekflow.sleekflow://sso.sleekflow.io/ios/io.sleekflow.sleekflow/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      web_origins: [ ]
      native_client_options:
        ios:
          team_id: JNXJD4KQ2C
          app_bundle_identifier: io.sleekflow.app
        android:
          app_package_name: io.sleekflow.sleekflow
          key_hashes:
            - 4B:7A:FA:CF:5F:56:1E:88:C1:0C:01:7A:EA:5F:61:6C:31:0D:AF:18:59:50:C4:9E:FF:32:8E:31:17:0C:4E:86
            - C8:44:11:BE:74:24:10:92:66:74:36:AE:D3:83:47:FC:44:AE:C5:76:0D:5E:2E:5C:E2:4F:9E:1F:7B:EB:5C:4D

    - name: sleekflow-client-mobile-v2-app
      allowed_logout_urls:
        - https://sso.sleekflow.io/logout/callback
        - https://app.sleekflow.io
      allowed_origins: [ ]
      app_type: native
      callbacks:
        - sleekflowauth0://sso.sleekflow.io/android/io.sleekflow.v2/callback
        - io.sleekflow.v2://sso.sleekflow.io/ios/io.sleekflow.v2/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      web_origins: [ ]
      native_client_options:
        ios:
          team_id: JNXJD4KQ2C
          app_bundle_identifier: io.sleekflow.app
        android:
          app_package_name: io.sleekflow.v2
          key_hashes:
            - 4B:7A:FA:CF:5F:56:1E:88:C1:0C:01:7A:EA:5F:61:6C:31:0D:AF:18:59:50:C4:9E:FF:32:8E:31:17:0C:4E:86
            - C8:44:11:BE:74:24:10:92:66:74:36:AE:D3:83:47:FC:44:AE:C5:76:0D:5E:2E:5C:E2:4F:9E:1F:7B:EB:5C:4D

    - name: sleekflow-client-reseller-portal-app
      initiate_login_uri: https://partner.sleekflow.io
      app_type: spa
      allowed_logout_urls:
        - https://partner.sleekflow.io
        - https://partner-uat.sleekflow.io
      allowed_origins:
        - https://partner.sleekflow.io
        - https://partner-uat.sleekflow.io
      callbacks:
        - https://partner.sleekflow.io
        - https://partner-uat.sleekflow.io
      web_origins:
        - https://partner.sleekflow.io
        - https://partner-uat.sleekflow.io
      grant_types:
        - authorization_code
        - implicit
        - refresh_token

    - name: sleekflow-client-web-v2-app
      initiate_login_uri: https://app.sleekflow.io
      allowed_logout_urls:
        - https://beta.sleekflow.io
        - https://preprod-us-revamp.sleekflow.io
        - https://staging-revamp.sleekflow.io
        - https://perf-revamp.sleekflow.io/
        - https://next.sleekflow.io
        - https://adfs.shkp.com/adfs/ls?wa=wsignoutcleanup1.0
        - https://sso-uat1.hongyip.com/auth/realms/hy-sso-uat1/protocol/openid-connect/logout
        - https://app.sleekflow.io
      allowed_origins:
        - https://beta.sleekflow.io
        - https://preprod-us-revamp.sleekflow.io
        - https://staging-revamp.sleekflow.io
        - https://perf-revamp.sleekflow.io/
        - https://next.sleekflow.io
        - https://app.sleekflow.io
      app_type: spa
      callbacks:
        - https://beta.sleekflow.io
        - https://preprod-us-revamp.sleekflow.io
        - https://staging-revamp.sleekflow.io
        - https://perf-revamp.sleekflow.io/
        - https://next.sleekflow.io
        - https://app.sleekflow.io
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - https://beta.sleekflow.io
        - https://preprod-us-revamp.sleekflow.io
        - https://staging-revamp.sleekflow.io
        - https://perf-revamp.sleekflow.io/
        - https://next.sleekflow.io
        - https://app.sleekflow.io

  sleekflow:connections:
    - name: sleekflow-connection-google-oauth2
      strategy: google-oauth2
      force_replace: false
      enabled_clients:
        - sleekflow-client-web-app
        - sleekflow-client-mobile-app
      options:
        scopes: [ email ]
        client_id: 99474478348-u0dtmseh4qoge36e661o92qovlkul6sc.apps.googleusercontent.com
        client_secret: 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
    - name: sleekflow-connection-apple
      strategy: apple
      enabled_clients:
        - sleekflow-client-web-app
        - sleekflow-client-mobile-app
      is_domain_connection: false
      force_replace: false
      options:
        kid: JGDF63GM34
        scopes:
          - name
          - email
        team_id: JNXJD4KQ2C
        client_id: io.sleekflow.app
        client_secret: |-
*****************************************************************************************************************************************************************************************************************************************************************************************************************************
  sleekflow:custom_domain:
    is_enabled: true
    domain: sso.sleekflow.io
    type: auth0_managed_certs
  sleekflow:post_login_webhook: https://gw.sleekflow.io/v1/tenant-hub/webhooks/auth0/postUserLogin
  sleekflow:pre_user_registration_webhook: https://gw.sleekflow.io/v1/tenant-hub/webhooks/auth0/preUserRegistration
  sleekflow:post_change_password_webhook: https://gw.sleekflow.io/v1/tenant-hub/webhooks/auth0/postUserChangePassword
  sleekflow:requires_mfa_webhook: https://gw.sleekflow.io/v1/tenant-hub/webhooks/auth0/requiresMFA
  sleekflow:action_issuer: https://sso.sleekflow.io/
  sleekflow:action_audience: https://api.sleekflow.io/
  sleekflow:jwk_url: https://sso.sleekflow.io/.well-known/jwks.json
  sleekflow:enable_custom_domain_in_emails: true
  sleekflow:travis_backend_base_url: https://sleekflow-core-production-hac3h0azhvcub0aq.z01.azurefd.net
  sleekflow:auth0_client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
  sleekflow:auth0_client_secret: ****************************************************************
  sleekflow:auth0_domain: sleekflow.eu.auth0.com
  sleekflow:user_email_check_secret_key: eYx1z5CTaCXkgHWObGRbLM14oo1fnVJL
  sleekflow:whatsapp_cloud_api_override_webhook:
