using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class GetTriggerNeeds : ITrigger<
    GetTriggerNeeds.GetTriggerNeedsInput,
    GetTriggerNeeds.GetTriggerNeedsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public GetTriggerNeeds(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class GetTriggerNeedsInput
    {
        [Required]
        [JsonProperty("trigger_id")]
        public string TriggerId { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonProperty("parameters")]
        public Dictionary<string, object?>? Parameters { get; set; }

        [JsonConstructor]
        public GetTriggerNeedsInput(
            string triggerId,
            string? version,
            Dictionary<string, object?>? parameters)
        {
            TriggerId = triggerId;
            Version = version;
            Parameters = parameters;
        }
    }

    public class GetTriggerNeedsOutput
    {
        [JsonProperty("needs")]
        public List<TriggerNeedConfigDto> Needs { get; set; }

        [JsonConstructor]
        public GetTriggerNeedsOutput(List<TriggerNeedConfigDto> needs)
        {
            Needs = needs;
        }
    }

    public async Task<GetTriggerNeedsOutput> F(GetTriggerNeedsInput input)
    {
        var needConfigs = await _needConfigService.GetTriggerNeedsAsync(
            input.Version,
            input.TriggerId,
            input.Parameters);

        return new GetTriggerNeedsOutput(
            needConfigs
                .Select(x => new TriggerNeedConfigDto(x))
                .ToList());
    }
}