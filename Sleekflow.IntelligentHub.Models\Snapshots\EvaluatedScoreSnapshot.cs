using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class EvaluatedScoreSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("lead_scoring")]
    public EvaluatedScore? EvaluatedScore { get; set; }

    [JsonConstructor]
    public EvaluatedScoreSnapshot(List<SfChatEntry> conversationContext, EvaluatedScore? evaluatedScore)
    {
        ConversationContext = conversationContext;
        EvaluatedScore = evaluatedScore;
    }
}