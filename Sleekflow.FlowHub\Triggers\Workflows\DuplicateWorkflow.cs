using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class DuplicateWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IIdService _idService;
    private readonly string _aiAgentStepCall = EnterAiAgentStepArgs.CallName;

    public DuplicateWorkflow(
        IWorkflowService workflowService,
        IWorkflowWebhookTriggerService workflowWebhookTriggerService,
        IFlowHubConfigService flowConfigService,
        IIdService idService)
    {
        _workflowService = workflowService;
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
        _flowHubConfigService = flowConfigService;
        _idService = idService;
    }

    public class DuplicateWorkflowInput : IHasSleekflowStaff, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DuplicateWorkflowInput(
            string sleekflowCompanyId,
            string workflowId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DuplicateWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public DuplicateWorkflowOutput(
            WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<DuplicateWorkflowOutput> F(DuplicateWorkflowInput duplicateWorkflowInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            duplicateWorkflowInput.SleekflowStaffId,
            duplicateWorkflowInput.SleekflowStaffTeamIds);

        var workflow = await _workflowService.GetLatestWorkflowAsync(
            duplicateWorkflowInput.WorkflowId,
            duplicateWorkflowInput.SleekflowCompanyId);

        var workflowId = _idService.GetId("Workflow");
        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflowId);

        var numOfWorkflows = await _workflowService.CountWorkflowsAsync(
            duplicateWorkflowInput.SleekflowCompanyId,
            workflowType: WorkflowType.Normal);

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(duplicateWorkflowInput.SleekflowCompanyId);

        var usageLimit = flowHubConfig.UsageLimit;

        if (usageLimit != null)
        {
            var numOfMaximumWorkflows = usageLimit.MaximumNumOfWorkflows;

            if (numOfWorkflows >= numOfMaximumWorkflows)
            {
                throw new SfFlowHubExceedUsageException(UsageLimitFieldNames.PropertyNameMaximumNumOfWorkflows);
            }
        }

        foreach (var step in workflow.Steps)
        {
            if (step is CallStep<EnterAiAgentStepArgs> callStep && callStep.Call == _aiAgentStepCall)
            {
                callStep.Args.AiAgentWorkflowId = null;
            }
        }

        var duplicatedWorkflow = await _workflowService.CreateWorkflowAsync(
            new Workflow(
                workflowId,
                workflowVersionedId,
                workflow.Name,
                workflow.WorkflowType,
                workflow.WorkflowGroupId,
                workflow.Triggers,
                workflow.WorkflowEnrollmentSettings,
                workflow.WorkflowScheduleSettings,
                workflow.Steps,
                WorkflowActivationStatuses.Draft,
                workflowVersionedId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                workflow.SleekflowCompanyId,
                sleekflowStaff,
                null,
                workflow.Metadata,
                workflow.Version,
                manualEnrollmentSource: workflow.ManualEnrollmentSource),
            duplicateWorkflowInput.SleekflowCompanyId);

        var triggers = await _workflowWebhookTriggerService.GetWorkflowWebhookTriggersAsync(
            duplicateWorkflowInput.WorkflowId,
            duplicateWorkflowInput.SleekflowCompanyId);

        foreach (var trigger in triggers.OrderBy(t => t.UpdatedAt))
        {
            var workflowWebhookTriggerId = _idService.GetId(
                "WorkflowWebhookTrigger",
                duplicatedWorkflow.WorkflowId);

            await _workflowWebhookTriggerService.CreateWorkflowWebhookTriggerAsync(
                new WorkflowWebhookTrigger(
                    null,
                    duplicatedWorkflow.WorkflowId,
                    RandomStringUtils.Gen(16),
                    trigger.ObjectIdExpression,
                    workflowWebhookTriggerId,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    duplicatedWorkflow.SleekflowCompanyId,
                    sleekflowStaff,
                    sleekflowStaff,
                    trigger.ObjectType),
                duplicatedWorkflow.SleekflowCompanyId);
        }

        return new DuplicateWorkflowOutput(new WorkflowDto(duplicatedWorkflow));
    }
}