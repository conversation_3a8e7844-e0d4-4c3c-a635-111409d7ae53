﻿using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas.Dtos;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;

public interface IMultipleChoicePropertyHook : IPropertyHook
{
}

public class MultipleChoicePropertyHook : IMultipleChoicePropertyHook, ISingletonService
{
    private readonly IOptionService _optionService;

    public MultipleChoicePropertyHook(IOptionService optionService)
    {
        _optionService = optionService;
    }

    public (IDataType DataType, List<Option>? Options) PreConstruct(PropertyInput propertyInput)
    {
        var (sanitizedOptions, _) = _optionService.SanitizeAndSortOptions(propertyInput.Options, null);

        return (propertyInput.DataType, sanitizedOptions);
    }

    public (IDataType DataType, List<Option>? Options) PreConstruct(Property property)
    {
        var (sanitizedOptions, _) = _optionService.SanitizeAndSortOptions(property.Options, null);

        return (property.DataType, sanitizedOptions);
    }

    public UpdatePropertyChangeContext PreUpdate(Property originalProperty, Property receivedProperty)
    {
        var (sanitizedOptions, toBeDeletedOptionIds) = _optionService.SanitizeAndSortOptions(
            receivedProperty.Options,
            originalProperty.Options);

        originalProperty.Options = sanitizedOptions;

        return new UpdatePropertyChangeContext(
            toBeDeletedOptionIds,
            NeedReindexPropertyValues(originalProperty, receivedProperty));
    }

    private static bool NeedReindexPropertyValues(Property originalProperty, Property receivedProperty)
        => originalProperty.IsSearchable != receivedProperty.IsSearchable;
}