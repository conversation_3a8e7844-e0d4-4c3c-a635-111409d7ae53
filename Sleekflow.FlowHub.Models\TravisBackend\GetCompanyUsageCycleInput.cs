using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.TravisBackend;

[SwaggerInclude]
public class GetCompanyUsageCycleInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonConstructor]
    public GetCompanyUsageCycleInput(string sleekflowCompanyId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
    }
}