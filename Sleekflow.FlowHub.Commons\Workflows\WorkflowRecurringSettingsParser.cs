using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.TimeProviders;

namespace Sleekflow.FlowHub.Commons.Workflows;

public interface IWorkflowRecurringSettingsParser
{
    DateTimeOffset GetNextOccurrence(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings recurringSettings,
        int offset = 0,
        bool futureOccurrenceOnly = true);

    // calculate the first occurrence that falls after the provided current time
    DateTimeOffset GetNextOccurrenceAfter(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings recurringSettings,
        DateTimeOffset currentTimeUtc);
}

public class WorkflowRecurringSettingsParser : IWorkflowRecurringSettingsParser, ISingletonService
{
    private readonly ITimeProvider _timeProvider;

    public WorkflowRecurringSettingsParser(ITimeProvider timeProvider)
    {
        _timeProvider = timeProvider;
    }

    public DateTimeOffset GetNextOccurrence(
        DateTimeOffset firstOccurrence,
        WorkflowRecurringSettings recurringSettings,
        int offset = 0,
        bool futureOccurrenceOnly = true)
        => recurringSettings.RecurringType switch
        {
            WorkflowRecurringTypes.Weekly => GetNextOccurrence(
                firstOccurrence,
                futureOccurrenceOnly ? _timeProvider.UtcNow : firstOccurrence,
                offset,
                repeatInWeeks: 1),
            WorkflowRecurringTypes.Monthly => GetNextOccurrence(
                firstOccurrence,
                futureOccurrenceOnly ? _timeProvider.UtcNow : firstOccurrence,
                offset,
                repeatInMonths: 1),
            WorkflowRecurringTypes.Yearly => GetNextOccurrence(
                firstOccurrence,
                futureOccurrenceOnly ? _timeProvider.UtcNow : firstOccurrence,
                offset,
                repeatInYears: 1),
            WorkflowRecurringTypes.Custom => GetNextOccurrence(
                firstOccurrence,
                futureOccurrenceOnly ? _timeProvider.UtcNow : firstOccurrence,
                offset,
                repeatInDays: recurringSettings.Day,
                repeatInWeeks: recurringSettings.Week,
                repeatInMonths: recurringSettings.Month,
                repeatInYears: recurringSettings.Year),
            _ => throw new ArgumentOutOfRangeException(
                nameof(WorkflowRecurringSettings.RecurringType),
                $"Unrecognized recurring type {recurringSettings.RecurringType}.")
        };

    private DateTimeOffset GetNextOccurrence(
        DateTimeOffset firstOccurrence,
        DateTimeOffset afterUtcDateTime,
        int offset = 0,
        int? repeatInDays = null,
        int? repeatInWeeks = null,
        int? repeatInMonths = null,
        int? repeatInYears = null)
    {
        var nextOccurrence = afterUtcDateTime;

        if (offset < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(offset), $"Offset cannot be less than 0. Received offset: {offset}");
        }

        if (firstOccurrence > afterUtcDateTime)
        {
            throw new InvalidOperationException(
                $"First occurrence cannot be after the afterUtcDateTime. First occurrence: {firstOccurrence:o}, afterUtcDateTime: {afterUtcDateTime:o}");
        }

        if (repeatInDays is > 0)
        {
            var pastIntervals = Math.Floor((afterUtcDateTime - firstOccurrence).TotalDays / repeatInDays.Value);
            nextOccurrence = firstOccurrence.AddDays(repeatInDays.Value * (pastIntervals + 1 + offset));
        }
        else if (repeatInWeeks is > 0)
        {
            var pastIntervals = (int)((afterUtcDateTime - firstOccurrence).TotalDays / (repeatInWeeks.Value * 7));
            nextOccurrence = firstOccurrence.AddDays(repeatInWeeks.Value * 7 * (pastIntervals + 1 + offset));
        }
        else if (repeatInMonths is > 0)
        {
            /*
             * Next occurrence by month is slightly special. We cannot simply use difference between month of
             * two datetime object to get the intervals.
             *
             * Example:
             *   Given we have first occurrence at 2024-06-30T12:34:56+00:00 and current time is 2024-07-04T13:56:30+00:00.
             *   If we take the month difference, it would be 1.
             *   If given the recurring interval is 1 month, it would actually skip the next occurrence 2024-07-30T12:34:56+00:00
             *   and return 2024-08-30T12:34:56+00:00 instead.
             *
             * Can refer to test case:
             * - GetNextOccurrence_GivenCustomMonthsRecurringSettings_EdgeCaseOne_ShouldReturnCorrectNextOccurrence
             */

            var pastIntervals = 0;

            while (true)
            {
                nextOccurrence = firstOccurrence.AddMonths(repeatInMonths.Value * (pastIntervals + 1 + offset));

                if (nextOccurrence > afterUtcDateTime)
                {
                    break;
                }

                pastIntervals++;
            }

            nextOccurrence = firstOccurrence.AddMonths(repeatInMonths.Value * (pastIntervals + 1 + offset));
        }
        else if (repeatInYears > 0)
        {
            /*
             * Next occurrence by year is similar to month. We cannot simply use difference between month of
             * two datetime object to get the intervals.
             *
             * Example:
             *   Given we have first occurrence at 2024-06-15T12:34:56+00:00 and current time is 2025-06-12T13:56:30+00:00.
             *   If we take the month difference, it would be 12.
             *   If given the recurring interval is 1 year, it would actually skip the next occurrence 2025-06-15T12:34:56+00:00
             *   and return 2026-08-30T12:34:56+00:00 instead.
             */

            var pastIntervals = 0;

            while (true)
            {
                nextOccurrence = firstOccurrence.AddMonths(repeatInYears.Value * 12 * (pastIntervals + 1 + offset));

                if (nextOccurrence > afterUtcDateTime)
                {
                    break;
                }

                pastIntervals++;
            }

            nextOccurrence = firstOccurrence.AddMonths(repeatInYears.Value * 12 * (pastIntervals + 1 + offset));
        }

        return nextOccurrence;
    }

    // Replace the GetNextOccurrenceAfter method with this corrected version

public DateTimeOffset GetNextOccurrenceAfter(
    DateTimeOffset firstOccurrence,
    WorkflowRecurringSettings recurringSettings,
    DateTimeOffset currentTimeUtc)
{
    // Optimization: If the reference time is strictly before the very first possible occurrence,
    // then the next occurrence *is* the first occurrence itself.
    if (currentTimeUtc < firstOccurrence)
    {
        return firstOccurrence;
    }

    int? repeatInDays = null;
    int? repeatInWeeks = null;
    int? repeatInMonths = null;
    int? repeatInYears = null;

    // Determine the interval settings based on the type
    switch (recurringSettings.RecurringType)
    {
        case WorkflowRecurringTypes.Weekly:
            repeatInWeeks = 1;
            break;
        case WorkflowRecurringTypes.Monthly:
            repeatInMonths = 1;
            break;
        case WorkflowRecurringTypes.Yearly:
            repeatInYears = 1;
            break;
        case WorkflowRecurringTypes.Custom:
            repeatInDays = recurringSettings.Day;
            repeatInWeeks = recurringSettings.Week;
            repeatInMonths = recurringSettings.Month;
            repeatInYears = recurringSettings.Year;
            // Add precedence logic here if needed, e.g., prefer Day if multiple are set
            if (repeatInDays > 0) { repeatInWeeks = repeatInMonths = repeatInYears = null; }
            else if (repeatInWeeks > 0) { repeatInMonths = repeatInYears = null; }
            else if (repeatInMonths > 0) { repeatInYears = null; }
            break;
        default:
            throw new ArgumentOutOfRangeException(
                nameof(recurringSettings.RecurringType),
                $"Unrecognized recurring type {recurringSettings.RecurringType}.");
    }

    // --- Calculation ---
    DateTimeOffset calculatedNextOccurrence;
    var multiplier = 0; // Counter for intervals from firstOccurrence

    // Optimized calculation for Day/Week based intervals (already correct)
    if (repeatInDays.HasValue && repeatInDays > 0)
    {
        var timeElapsed = currentTimeUtc - firstOccurrence;

        if (timeElapsed.Ticks < 0)
        {
            timeElapsed = TimeSpan.Zero;
        }

        var intervalsPassed = (long)Math.Floor(timeElapsed.TotalDays / repeatInDays.Value);
        // Calculate based on the number of intervals *after* the ones that have passed
        calculatedNextOccurrence = firstOccurrence.AddDays(repeatInDays.Value * (intervalsPassed + 1));
    }
    else if (repeatInWeeks.HasValue && repeatInWeeks > 0)
    {
        var timeElapsed = currentTimeUtc - firstOccurrence;

        if (timeElapsed.Ticks < 0)
        {
            timeElapsed = TimeSpan.Zero;
        }

        var daysInInterval = repeatInWeeks.Value * 7;
        var intervalsPassed = (long)Math.Floor(timeElapsed.TotalDays / daysInInterval);
        // Calculate based on the number of intervals *after* the ones that have passed
        calculatedNextOccurrence = firstOccurrence.AddDays(daysInInterval * (intervalsPassed + 1));
    }
    // Iterative calculation for Month/Year based intervals
    // Corrected to calculate from firstOccurrence each time
    else if (repeatInMonths.HasValue && repeatInMonths > 0)
    {
        while (true)
        {
            // Calculate based on original firstOccurrence + multiplier * interval
            var potentialNext = firstOccurrence.AddMonths(repeatInMonths.Value * multiplier);
            if (potentialNext > currentTimeUtc)
            {
                calculatedNextOccurrence = potentialNext;
                break;
            }

            multiplier++;
            if (multiplier > 12000) // Safety break (e.g., 1000 years)
            {
                throw new InvalidOperationException("GetNextOccurrenceAfter month calculation exceeded limit.");
            }
        }
    }
    else if (repeatInYears.HasValue && repeatInYears > 0)
    {
        while (true)
        {
            // Calculate based on original firstOccurrence + multiplier * interval
            var potentialNext = firstOccurrence.AddYears(repeatInYears.Value * multiplier);
            if (potentialNext > currentTimeUtc)
            {
                calculatedNextOccurrence = potentialNext;
                break;
            }

            multiplier++;
            if (multiplier > 1000) // Safety break (e.g., 1000 years)
            {
                throw new InvalidOperationException("GetNextOccurrenceAfter year calculation exceeded limit.");
            }
        }
    }
    else
    {
         throw new ArgumentException("Invalid recurring settings: No valid interval found.", nameof(recurringSettings));
    }

    return calculatedNextOccurrence;
}
}