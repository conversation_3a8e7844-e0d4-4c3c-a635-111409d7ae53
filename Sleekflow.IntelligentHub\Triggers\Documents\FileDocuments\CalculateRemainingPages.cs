using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class CalculateRemainingPages
    : ITrigger<
        CalculateRemainingPages.CalculateRemainingPagesInput,
        CalculateRemainingPages.CalculateRemainingPagesOutput>
{
    private readonly IFileDocumentService _fileDocumentService;

    public CalculateRemainingPages(IFileDocumentService fileDocumentService)
    {
        _fileDocumentService = fileDocumentService;
    }

    public class CalculateRemainingPagesInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("page_limit")]
        [Required]
        [Range(1, 1000)]
        public int PageLimit { get; set; }

        [JsonConstructor]
        public CalculateRemainingPagesInput(string sleekflowCompanyId, int pageLimit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PageLimit = pageLimit;
        }
    }

    public class CalculateRemainingPagesOutput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("page_limit")]
        [Required]
        public int PageLimit { get; set; }

        [JsonProperty("current_usage")]
        [Required]
        public int CurrentUsage { get; set; }

        [JsonProperty("remaining_page")]
        [Required]
        public int RemainingPage { get; set; }

        [JsonConstructor]
        public CalculateRemainingPagesOutput(
            string sleekflowCompanyId,
            int pageLimit,
            int currentUsage,
            int remainingPage)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PageLimit = pageLimit;
            CurrentUsage = currentUsage;
            RemainingPage = remainingPage;
        }
    }

    public async Task<CalculateRemainingPagesOutput> F(
        CalculateRemainingPagesInput calculateRemainingPagesInput)
    {
        var pageUsed = await _fileDocumentService.GetUsedPagesAsync(
            calculateRemainingPagesInput.SleekflowCompanyId);
        var pageRemain = calculateRemainingPagesInput.PageLimit - pageUsed;

        return new CalculateRemainingPagesOutput(
            calculateRemainingPagesInput.SleekflowCompanyId,
            calculateRemainingPagesInput.PageLimit,
            pageUsed,
            pageRemain);
    }
}