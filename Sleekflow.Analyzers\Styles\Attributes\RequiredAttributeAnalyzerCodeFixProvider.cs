using System.Collections.Immutable;
using System.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CodeActions;
using Microsoft.CodeAnalysis.CodeFixes;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Editing;

namespace Sleekflow.Analyzers.Styles;

[ExportCodeFixProvider(LanguageNames.CSharp, Name = nameof(RequiredAttributeAnalyzerCodeFixProvider))]
[Shared]
public class RequiredAttributeAnalyzerCodeFixProvider : CodeFixProvider
{
    private const string Title = "Add [Required] attribute";

    public sealed override ImmutableArray<string> FixableDiagnosticIds
    {
        get { return ImmutableArray.Create(RequiredAttributeAnalyzer.DiagnosticId); }
    }

    public sealed override FixAllProvider GetFixAllProvider()
    {
        return WellKnownFixAllProviders.BatchFixer;
    }

    public sealed override async Task RegisterCodeFixesAsync(CodeFixContext context)
    {
        var root = await context.Document.GetSyntaxRootAsync(context.CancellationToken).ConfigureAwait(false);

        var diagnostic = context.Diagnostics.First();
        var diagnosticSpan = diagnostic.Location.SourceSpan;

        var declaration = root!.FindToken(diagnosticSpan.Start).Parent!.AncestorsAndSelf()
            .OfType<PropertyDeclarationSyntax>().First();

        context.RegisterCodeFix(
            CodeAction.Create(
                title: Title,
                createChangedDocument: c => AddRequiredAttributeAsync(context.Document, declaration, c),
                equivalenceKey: Title),
            diagnostic);
    }

    private async Task<Document> AddRequiredAttributeAsync(
        Document document,
        PropertyDeclarationSyntax propertyDeclaration,
        CancellationToken cancellationToken)
    {
        var editor = await DocumentEditor.CreateAsync(document, cancellationToken).ConfigureAwait(false);
        var generator = editor.Generator;

        var requiredAttribute = generator.Attribute("System.ComponentModel.DataAnnotations.RequiredAttribute");
        editor.AddAttribute(propertyDeclaration, requiredAttribute);

        return editor.GetChangedDocument();
    }
}