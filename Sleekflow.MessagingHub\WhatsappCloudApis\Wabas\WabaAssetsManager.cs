using GraphApi.Client.ApiClients.Exceptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.Datasets;
using Sleekflow.MessagingHub.WhatsappCloudApis.MetaConversionApis;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

public interface IWabaAssetsManager
{
    // Facebook Waba Dataset
    Task<WabaDataset?> RefreshWabaDatasetAsync(Waba waba, string? businessIntegrationSystemUserAccessToken = null);

    Task SetupFacebookWabaDatasetAsync(
        string facebookWabaId,
        string facebookDatasetName,
        string? businessIntegrationSystemUserAccessToken = null);

    WabaDataset? GetExistingWabaDataset(Waba waba);

    Task<WabaDatasetDto?> GetFacebookWabaDatasetAsync(
        string facebookWabaId,
        string? businessIntegrationSystemUserAccessToken = null);
}

public class WabaAssetsManager : IWabaAssetsManager, ISingletonService
{
    private readonly IMetaConversionApiService _metaConversionApiService;
    private readonly ILogger<WabaAssetsManager> _logger;

    public WabaAssetsManager(
        IMetaConversionApiService metaConversionApiService,
        ILogger<WabaAssetsManager> logger)
    {
        _metaConversionApiService = metaConversionApiService;
        _logger = logger;
    }

    public async Task<WabaDataset?> RefreshWabaDatasetAsync(Waba waba, string? businessIntegrationSystemUserAccessToken = null)
    {
        var wabaDatasetDto = await GetFacebookWabaDatasetAsync(waba.FacebookWabaId, businessIntegrationSystemUserAccessToken);

        if (wabaDatasetDto != null)
        {
            waba.WabaDataset = new WabaDataset(
                wabaDatasetDto.FacebookDatasetId,
                wabaDatasetDto.FacebookDatasetName,
                waba.WabaDataset?.CreatedAt ?? DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow);

            return waba.WabaDataset;
        }

        return null;
    }

    public WabaDataset? GetExistingWabaDataset(Waba waba)
    {
        return waba.WabaDataset;
    }

    public async Task SetupFacebookWabaDatasetAsync(string facebookWabaId, string facebookDatasetName, string? businessIntegrationSystemUserAccessToken = null)
    {
        try
        {
            await _metaConversionApiService.CreateWabaDatasetAsync(
                facebookWabaId,
                facebookDatasetName,
                businessIntegrationSystemUserAccessToken);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    $"Graph Error occurred while creating Facebook Waba {facebookWabaId} Dataset",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }

            throw;
        }
    }

    public async Task<WabaDatasetDto?> GetFacebookWabaDatasetAsync(string facebookWabaId, string? businessIntegrationSystemUserAccessToken = null)
    {
        try
        {
            var getWabaDatasetResponse = await _metaConversionApiService.GetWabaDatasetAsync(
                facebookWabaId,
                businessIntegrationSystemUserAccessToken);

            if (getWabaDatasetResponse.Data.Any())
            {
                var dataset = getWabaDatasetResponse.Data.First();

                return new WabaDatasetDto(dataset.Id, dataset.Name);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred while getting Facebook Waba {FacebookWabaId} Dataset", facebookWabaId);
        }

        return null;
    }
}