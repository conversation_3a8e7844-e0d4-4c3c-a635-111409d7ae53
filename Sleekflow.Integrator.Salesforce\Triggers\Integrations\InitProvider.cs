﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Authentications;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProvider : ITrigger
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public InitProvider(ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class InitProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("return_to_url")]
        [Required]
        public string ReturnToUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderInput(
            string sleekflowCompanyId,
            string returnToUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ReturnToUrl = returnToUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderOutput
    {
        [JsonProperty("salesforce_authentication_url")]
        public string SalesforceAuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderOutput(string salesforceAuthenticationUrl)
        {
            SalesforceAuthenticationUrl = salesforceAuthenticationUrl;
        }
    }

    public async Task<InitProviderOutput> F(
        InitProviderInput initProviderInput)
    {
        var redirectUrl =
            await _salesforceAuthenticationService.AuthenticateAsync(
                initProviderInput.SleekflowCompanyId,
                initProviderInput.ReturnToUrl,
                initProviderInput.AdditionalDetails);

        return new InitProviderOutput(redirectUrl);
    }
}