﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Sleekflow.Exceptions;
using Sleekflow.Mvc.Authorizations;

namespace Sleekflow.Mvc.Func.Abstractions;

public interface IHeadersAuthorizationFuncFilter : IFuncFilter
{
}

public class HeadersAuthorizationFuncFilter : IHeadersAuthorizationFuncFilter
{
    private const string XSleekflowTenantHubUserId = "X-Sleekflow-TenantHub-User-Id";
    private const string XSleekflowUserId = "X-Sleekflow-User-Id";
    private const string XSleekflowStaffId = "X-Sleekflow-Staff-Id";
    private const string XSleekflowCompanyId = "X-Sleekflow-Company-Id";
    private const string XSleekflowRoles = "X-Sleekflow-Roles";
    private const string XSleekflowRoleIds = "X-Sleekflow-Role-Ids";
    private const string XSleekflowTeamIds = "X-Sleekflow-Team-Ids";
    private const string XSleekflowIsRbacEnabled = "X-Sleekflow-Is-Rbac-Enabled";
    private const string XSleekflowImpersonatorUserId = "X-Sleekflow-Impersonator-User-Id";
    private const string XSleekflowImpersonatorCompanyId = "X-Sleekflow-Impersonator-Company-Id";
    private const string XSleekflowImpersonatorStaffId = "X-Sleekflow-Impersonator-Staff-Id";
    private const string XSleekflowImpersonatorTenantUserId = "X-Sleekflow-Impersonator-TenantHub-User-Id";

    private readonly ILogger<HeadersAuthorizationFuncFilter> _logger;
    private readonly ISleekflowAuthorizationContext _sleekflowAuthorizationContext;

    public HeadersAuthorizationFuncFilter(
        ILogger<HeadersAuthorizationFuncFilter> logger,
        ISleekflowAuthorizationContext sleekflowAuthorizationContext)
    {
        _logger = logger;
        _sleekflowAuthorizationContext = sleekflowAuthorizationContext;
    }

    public Task FilterAsync(HttpRequest httpRequest)
    {
        var headers = httpRequest.Headers;
        try
        {
            headers.TryGetValue(XSleekflowTenantHubUserId, out var sleekflowTenantHubUserId);
            headers.TryGetValue(XSleekflowUserId, out var sleekflowUserId);
            headers.TryGetValue(XSleekflowStaffId, out var sleekflowStaffId);
            headers.TryGetValue(XSleekflowCompanyId, out var sleekflowCompanyId);
            headers.TryGetValue(XSleekflowRoles, out var sleekflowRoles);
            headers.TryGetValue(XSleekflowRoleIds, out var sleekflowRoleIds);
            headers.TryGetValue(XSleekflowTeamIds, out var sleekflowTeamIds);
            headers.TryGetValue(XSleekflowIsRbacEnabled, out var sleekflowIsRbacEnabled);
            headers.TryGetValue(XSleekflowImpersonatorUserId, out var sleekflowImpersonatorUserId);
            headers.TryGetValue(XSleekflowImpersonatorCompanyId, out var sleekflowImpersonatorCompanyId);
            headers.TryGetValue(XSleekflowImpersonatorStaffId, out var sleekflowImpersonatorStaffId);
            headers.TryGetValue(XSleekflowImpersonatorTenantUserId, out var sleekflowImpersonatorTenantUserId);
            if (string.IsNullOrEmpty(sleekflowStaffId)
                || string.IsNullOrEmpty(sleekflowCompanyId))
            {
                throw new SfUnauthorizedException();
            }

            if (!string.IsNullOrEmpty(sleekflowImpersonatorUserId) &&
                !string.IsNullOrEmpty(sleekflowImpersonatorCompanyId) &&
                !string.IsNullOrEmpty(sleekflowImpersonatorStaffId))
            {
                _logger.LogInformation(
                    "Sleekflow {SleekflowStaffId} is impersonating {SleekflowImpersonatorStaffId}",
                    sleekflowStaffId,
                    sleekflowImpersonatorStaffId);
                _sleekflowAuthorizationContext.SleekflowImpersonator = new Impersonator
                {
                    SleekflowUserId = sleekflowImpersonatorUserId,
                    SleekflowCompanyId = sleekflowImpersonatorCompanyId,
                    SleekflowStaffId = sleekflowImpersonatorStaffId,
                    SleekflowTenantHubUserId = sleekflowImpersonatorTenantUserId
                };
            }

            _sleekflowAuthorizationContext.SleekflowTenantHubUserId = sleekflowTenantHubUserId;
            _sleekflowAuthorizationContext.SleekflowUserId = sleekflowUserId!;
            _sleekflowAuthorizationContext.SleekflowStaffId = sleekflowStaffId!;
            _sleekflowAuthorizationContext.SleekflowCompanyId = sleekflowCompanyId!;
            _sleekflowAuthorizationContext.IsRbacEnabled =
                !string.IsNullOrEmpty(sleekflowIsRbacEnabled) && bool.Parse(sleekflowIsRbacEnabled!);
            _sleekflowAuthorizationContext.SleekflowRoles = ExtractValues(sleekflowRoles);
            _sleekflowAuthorizationContext.SleekflowRoleIds = ExtractValues(sleekflowRoleIds);
            _sleekflowAuthorizationContext.SleekflowTeamIds = ExtractValues(sleekflowTeamIds);
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "Unable to authenticate Auth0Token");
            throw;
        }

        return Task.CompletedTask;
    }

    private static List<string> ExtractValues(string? values)
    {
        return string.IsNullOrEmpty(values)
            ? new List<string>()
            : values.Replace("\"", string.Empty).Split(',').Where(v => !string.IsNullOrEmpty(v)).ToList();
    }
}