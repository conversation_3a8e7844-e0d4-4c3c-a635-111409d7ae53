using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Validations;

public class ValidateNotEqualToAnyStringAttribute : ValidationAttribute
{
    private readonly string[] _strings;

    public ValidateNotEqualToAnyStringAttribute(string[] strings)
    {
        _strings = strings;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is string str && _strings.Contains(str, StringComparer.InvariantCulture))
        {
            return new ValidationResult(
                $"The {validationContext.MemberName} {str} is restricted to use.",
                new[]
                {
                    validationContext.MemberName!
                });
        }

        return ValidationResult.Success;
    }
}