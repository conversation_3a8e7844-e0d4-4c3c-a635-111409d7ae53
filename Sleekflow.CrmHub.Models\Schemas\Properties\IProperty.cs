﻿using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public interface IProperty : IHasCreatedBy
{
    public const string PropertyNameDisplayName = "display_name";
    public const string PropertyNameUniqueName = "unique_name";
    public const string PropertyNameDataType = "data_type";
    public const string PropertyNameIsRequired = "is_required";
    public const string PropertyNameIsVisible = "is_visible";
    public const string PropertyNameIsPinned = "is_pinned";
    public const string PropertyNameIsSearchable = "is_searchable";
    public const string PropertyNameDisplayOrder = "display_order";
    public const string PropertyNameOptions = "options";

    public string DisplayName { get; set; }

    public string UniqueName { get; set; }

    public IDataType DataType { get; }

    public bool IsVisible { get; set; }

    public bool IsPinned { get; set; }

    public bool IsSearchable { get; set; }
}