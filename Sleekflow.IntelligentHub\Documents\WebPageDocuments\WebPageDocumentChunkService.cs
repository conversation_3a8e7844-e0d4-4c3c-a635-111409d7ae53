﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.TextEnrichments;

namespace Sleekflow.IntelligentHub.Documents.WebPageDocuments;

public interface IWebPageDocumentChunkService
{
    Task<WebPageDocumentChunk> PatchAndGetWebPageDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        EditingChunkDto editingChunk);

    Task<WebPageDocumentChunk> GetWebPageDocumentChunkAsync(
        string sleekflowCompanyId,
        string webPageDocumentChunkId);

    Task<(
            List<WebPageDocumentChunk> WebPageDocumentChunks,
            string? NextContinuationToken)>
        GetWebPageDocumentChunksAsync(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit);

    Task<WebPageDocumentChunk> CreateWebPageDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        string pageUri,
        string content,
        string contentEn,
        List<Category> categories);

    Task DeleteWebPageDocumentChunkAsync(List<string> webPageDocumentChunkIds, string sleekflowCompanyId);
}

public class WebPageDocumentChunkService : IWebPageDocumentChunkService, IScopedService
{
    private readonly IWebPageDocumentChunkRepository _webPageDocumentChunkRepository;
    private readonly IIdService _idService;
    private readonly IWebPageDocumentService _webPageDocumentService;
    private readonly ITextTranslationService _textTranslationService;

    public WebPageDocumentChunkService(
        IWebPageDocumentChunkRepository webPageDocumentChunkRepository,
        IIdService idService,
        IWebPageDocumentService webPageDocumentService,
        ITextTranslationService textTranslationService)
    {
        _webPageDocumentChunkRepository = webPageDocumentChunkRepository;
        _idService = idService;
        _webPageDocumentService = webPageDocumentService;
        _textTranslationService = textTranslationService;
    }

    public async Task<WebPageDocumentChunk> PatchAndGetWebPageDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        EditingChunkDto editingChunk)
    {
        var webPageDocumentChunk = await _webPageDocumentChunkRepository.GetWebPageDocumentChunkOrDefaultAsync(
            editingChunk.ChunkId,
            sleekflowCompanyId,
            documentId);
        if (webPageDocumentChunk is null)
        {
            throw new SfNotFoundObjectException(editingChunk.ChunkId, sleekflowCompanyId);
        }

        var contentEn = await _textTranslationService.TranslateByAzureTranslationServiceAsync(
            TranslationSupportedLanguages.English,
            editingChunk.Content);
        var patchedWebPageDocumentChunk = await _webPageDocumentChunkRepository.PatchAndGetWebPageDocumentChunkAsync(
            webPageDocumentChunk,
            editingChunk.Content,
            contentEn,
            editingChunk.Categories,
            editingChunk.Metadata);

        return patchedWebPageDocumentChunk;
    }

    public async Task<WebPageDocumentChunk> GetWebPageDocumentChunkAsync(
        string sleekflowCompanyId,
        string webPageDocumentChunkId)
    {
        var webPageDocumentChunk =
            await _webPageDocumentChunkRepository.GetAsync(webPageDocumentChunkId, sleekflowCompanyId);
        if (webPageDocumentChunk == null)
        {
            throw new SfNotFoundObjectException(webPageDocumentChunkId, sleekflowCompanyId);
        }

        return webPageDocumentChunk;
    }

    public async Task<(List<WebPageDocumentChunk> WebPageDocumentChunks, string? NextContinuationToken)>
        GetWebPageDocumentChunksAsync(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit)
    {
        var document = await _webPageDocumentService.GetDocumentAsync(sleekflowCompanyId, documentId);

        return await _webPageDocumentChunkRepository.GetWebPageDocumentChunksAsync(
            sleekflowCompanyId,
            document.Id,
            continuationToken,
            limit);
    }

    public async Task<WebPageDocumentChunk> CreateWebPageDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        string pageUri,
        string content,
        string contentEn,
        List<Category> categories)
    {
        var createdWebPageDocumentChunk = await _webPageDocumentChunkRepository.CreateAndGetAsync(
            new WebPageDocumentChunk(
                _idService.GetId(SysTypeNames.WebPageDocumentChunk),
                sleekflowCompanyId,
                documentId,
                pageUri,
                content,
                contentEn,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                categories,
                new Dictionary<string, object?>()),
            sleekflowCompanyId);

        return createdWebPageDocumentChunk;
    }

    public async Task DeleteWebPageDocumentChunkAsync(List<string> webPageDocumentChunkIds, string sleekflowCompanyId)
    {
        var numOfDeletedChunks =
            await _webPageDocumentChunkRepository.DeleteAsync(webPageDocumentChunkIds, sleekflowCompanyId);
        if (numOfDeletedChunks == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete chunks, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}