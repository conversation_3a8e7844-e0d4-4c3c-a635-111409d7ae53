using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.Stores.ShopifyStores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Stores.ShopifyStores;

[TriggerGroup(ControllerNames.ShopifyStores)]
public class UpdateShopifyStoreIntegrationExternalConfig
    : ITrigger<
        UpdateShopifyStoreIntegrationExternalConfig.UpdateShopifyStoreIntegrationExternalConfigInput,
        UpdateShopifyStoreIntegrationExternalConfig.UpdateShopifyStoreIntegrationExternalConfigOutput>
{
    private readonly IShopifyStoreService _shopifyStoreService;
    private readonly IStoreService _storeService;

    public UpdateShopifyStoreIntegrationExternalConfig(
        IShopifyStoreService shopifyStoreService,
        IStoreService storeService)
    {
        _shopifyStoreService = shopifyStoreService;
        _storeService = storeService;
    }

    public class UpdateShopifyStoreIntegrationExternalConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("store_integration_external_config")]
        public StoreIntegrationExternalConfigInput StoreIntegrationExternalConfig { get; set; }

        [JsonConstructor]
        public UpdateShopifyStoreIntegrationExternalConfigInput(
            string sleekflowCompanyId,
            string storeId,
            StoreIntegrationExternalConfigInput storeIntegrationExternalConfig)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            StoreIntegrationExternalConfig = storeIntegrationExternalConfig;
        }
    }

    public class UpdateShopifyStoreIntegrationExternalConfigOutput
    {
        [JsonProperty("shopify_store_integration_external_config")]
        public ShopifyStoreIntegrationExternalConfig ShopifyStoreIntegrationExternalConfig { get; set; }

        [JsonConstructor]
        public UpdateShopifyStoreIntegrationExternalConfigOutput(
            ShopifyStoreIntegrationExternalConfig shopifyStoreIntegrationExternalConfig)
        {
            ShopifyStoreIntegrationExternalConfig = shopifyStoreIntegrationExternalConfig;
        }
    }

    public async Task<UpdateShopifyStoreIntegrationExternalConfigOutput> F(
        UpdateShopifyStoreIntegrationExternalConfigInput updateShopifyStoreIntegrationExternalConfigInput)
    {
        var shopifyStore = await _storeService.GetStoreAsync(
            updateShopifyStoreIntegrationExternalConfigInput.StoreId,
            updateShopifyStoreIntegrationExternalConfigInput.SleekflowCompanyId);

        var shopifyStoreIntegrationExternalConfig =
            (updateShopifyStoreIntegrationExternalConfigInput.StoreIntegrationExternalConfig.GetConfig() as ShopifyStoreIntegrationExternalConfig)!;

        return new UpdateShopifyStoreIntegrationExternalConfigOutput(
            await _shopifyStoreService.UpdateShopifyStoreIntegrationExternalConfigAsync(
                shopifyStore,
                shopifyStoreIntegrationExternalConfig.IntegrationStatus,
                shopifyStoreIntegrationExternalConfig.SyncConfig,
                shopifyStoreIntegrationExternalConfig.PaymentConfig,
                shopifyStoreIntegrationExternalConfig.SyncStatus,
                shopifyStoreIntegrationExternalConfig.MessageTemplates,
                shopifyStoreIntegrationExternalConfig.IsShopifyBillingOwner,
                shopifyStoreIntegrationExternalConfig.ChargeUpdatedAt,
                shopifyStoreIntegrationExternalConfig.ChargeId));
    }
}