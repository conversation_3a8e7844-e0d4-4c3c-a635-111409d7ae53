name: <PERSON>ulumi Preview

on:
  workflow_dispatch:
  pull_request:
    branches:
      - master

env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages
  REGISTRY: ghcr.io

jobs:
  set-env:
    name: Set Environment Variables
    runs-on: ubuntu-latest
    outputs:
      image_prefix: ${{ steps.set-prefix.outputs.prefix }}
      build_time: ${{ steps.set-buildtime.outputs.time }}
      branch_name: ${{ steps.set-branch.outputs.name }}
    steps:
      - id: set-prefix
        run: echo "prefix=$(echo ${{ github.repository_owner }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT
      - id: set-buildtime
        run: echo "time=$(date -u '+%Y%m%d%H%M%S')" >> $GITHUB_OUTPUT
      - id: set-branch
        run: |
          # Get the branch name and replace '/' with '-'
          branch="${{ github.event.pull_request.head.ref }}"
          if [ -z "$branch" ]; then
            echo "name=latest" >> $GITHUB_OUTPUT
          else
            echo "name=${branch//\//-}" >> $GITHUB_OUTPUT
          fi

  build-common:
    needs: set-env
    name: Build Common Image
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      image_tags: ${{ steps.meta.outputs.tags }}
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow
          tags: |
            type=sha,prefix=
            type=ref,event=branch
            type=raw,value=latest,enable={{is_default_branch}}

      # Build and cache common base image
      - name: Build common base image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.branch_name }}
          cache-from: type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:buildcache-${{ needs.set-env.outputs.branch_name }}
          cache-to: type=registry,ref=${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:buildcache-${{ needs.set-env.outputs.branch_name }},mode=max

  # Build services using normal runners
  build-services-normal:
    name: Build Normal Services
    needs: [ build-common, set-env ]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        # Services that can be built with normal runners
        service: [
          AuditHub,
          CommerceHub,
          CrmHub,
          EmailHub,
          Dynamics365Integrator,
          HubspotIntegrator,
          SalesforceIntegrator,
          ApiGateway,
          GlobalApiGateway,
          InternalApiGateway,
          MessagingHub,
          WebhookHub,
          ShareHub,
          PublicApiGateway,
          FlowHub,
          FlowHubExecutor,
          FlowHubIntegrator,
          TenantHub,
          IntelligentHub,
          SfmcJourneyBuilderCustomActivity,
          UserEventHub,
          UserEventAnalyticsHub,
          TicketingHub,
          SupportHub,
          Scheduler,
          InternalIntegrationHub,
          OpenPolicyAgent,
          OpenPolicyAdministrationLayer,
          GoogleSheetsIntegrator,
          ZohoIntegrator
        ]
      # Allow all matrix jobs to run even if one fails
      fail-fast: false
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Build service
        uses: ./.github/actions/build-service
        with:
          service_name: ${{ matrix.service }}
          branch_name: ${{ needs.set-env.outputs.branch_name }}
          build_time: ${{ needs.set-env.outputs.build_time }}
          image_prefix: ${{ needs.set-env.outputs.image_prefix }}
          registry: ${{ env.REGISTRY }}
          github_sha: ${{ github.sha }}
          common_image: ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.branch_name }}

  # Build services that need larger runners
  build-services-large:
    name: Build Large Services
    needs: [ build-common, set-env ]
    runs-on:
      group: "Default Middle Runners"  # Using a middle runner group for preview
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        # Services that need large runners
        service: [
          IntelligentHubLightRag
        ]
      # Allow all matrix jobs to run even if one fails
      fail-fast: false
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Build service
        uses: ./.github/actions/build-service
        with:
          service_name: ${{ matrix.service }}
          branch_name: ${{ needs.set-env.outputs.branch_name }}
          build_time: ${{ needs.set-env.outputs.build_time }}
          image_prefix: ${{ needs.set-env.outputs.image_prefix }}
          registry: ${{ env.REGISTRY }}
          github_sha: ${{ github.sha }}
          common_image: ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.branch_name }}

  preview:
      name: Preview
      needs: [ build-services-normal, build-services-large, set-env ]
      runs-on:
        group: "Default Middle Runners"
      # Serialize Pulumi deployments
      concurrency:
        group: pulumi-deploy
        cancel-in-progress: false
      steps:
        - uses: actions/checkout@v3
          with:
            submodules: true
            token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
            fetch-depth: 0

        - name: Set up .NET Core
          uses: actions/setup-dotnet@v3
          with:
            dotnet-version: '8.0.303'

        - uses: actions/cache@v3
          with:
            path: ${{ github.workspace }}/.nuget/packages
            key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
            restore-keys: |
              ${{ runner.os }}-nuget-

        - name: Build required projects
          run: |
            # Build worker projects needed by Sleekflow.Infras by searching for files containing "bin/Release/net8.0/publish"
            dotnet publish Sleekflow.FlowHub.Workers -c Release
            dotnet publish Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer -c Release
            dotnet publish Sleekflow.MessagingHub.Workers -c Release
            dotnet publish Sleekflow.CrmHub.Workers -c Release
            dotnet publish Sleekflow.InternalIntegrationHub.Workers -c Release
            dotnet publish Sleekflow.Infras.DbScalar -c Release
            dotnet publish Sleekflow.CommerceHub.Workers -c Release
            dotnet publish Sleekflow.EmailHub.Workers -c Release
            dotnet publish Sleekflow.IntelligentHub.Workers -c Release

        - name: Log in to GitHub Container Registry
          uses: docker/login-action@v3
          with:
            registry: ${{ env.REGISTRY }}
            username: ${{ github.actor }}
            password: ${{ secrets.GITHUB_TOKEN }}

        - name: Restore Pulumi packages
          working-directory: ./Sleekflow.Infras/
          run: dotnet restore

        - name: Set Pulumi stack name
          id: set-stack
          run: |
            if [[ "${{ github.event.pull_request.base.ref }}" == "master" ]]; then
              echo "stack=production" >> $GITHUB_OUTPUT
            elif [[ "${{ github.event.pull_request.base.ref }}" == "staging" ]]; then
              echo "stack=staging" >> $GITHUB_OUTPUT
            else
              echo "stack=dev" >> $GITHUB_OUTPUT
            fi

        - name: Pull Images Concurrently
          run: |
            # Create an array of pull commands
            pull_commands=()

            # Add common image
            pull_commands+=("docker pull ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-common:${{ needs.set-env.outputs.build_time }}")

            # Get service mappings and extract all services
            mappings=$(cat .github/workflows/service-mappings.json)

            # Generate pull commands for each service directly from mappings
            while IFS= read -r service; do
              # Extract image name from mappings
              image=$(echo "$mappings" | jq -r --arg svc "$service" '.[$svc].image')
              if [ "$image" != "null" ]; then
                pull_commands+=("docker pull ${{ env.REGISTRY }}/${{ needs.set-env.outputs.image_prefix }}/sleekflow-$image:${{ needs.set-env.outputs.build_time }}")
              fi
            done < <(echo "$mappings" | jq -r 'keys[]')

            # Execute all pull commands in parallel and wait for completion
            for cmd in "${pull_commands[@]}"; do
              echo "Starting: $cmd"
              eval "$cmd" &
            done

            # Wait for all background processes to complete
            wait

            echo "All images pulled successfully"

        - name: Preview Everything
          uses: pulumi/actions@v4
          with:
            command: preview
            stack-name: ${{ steps.set-stack.outputs.stack }}
            work-dir: ./Sleekflow.Infras/
          env:
            PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
            ARM_CLIENT_ID: ${{ secrets.ARM_CLIENT_ID }}
            ARM_CLIENT_SECRET: ${{ secrets.ARM_CLIENT_SECRET }}
            ARM_SUBSCRIPTION_ID: ${{ secrets.ARM_SUBSCRIPTION_ID }}
            ARM_TENANT_ID: ${{ secrets.ARM_TENANT_ID }}
            BUILD_TIME: ${{ needs.set-env.outputs.build_time }}

  test:
    name: Run Tests
    needs: [build-services-normal, build-services-large, preview]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test: [
          AuditHub,
          CommerceHub,
          CrmHub,
          IntelligentHub,
          FlowHub,
          KrakenD,
          MessagingHub,
          PublicApiGateway,
          ShareHub,
          TenantHub,
          UserEventHub
        ]
      fail-fast: false
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          token: ${{ secrets.SLEEFLOW_PRIVATE_TOKEN }}
          fetch-depth: 0

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Running Tests
        run: |
          set -o pipefail
          dotnet test Sleekflow.${{matrix.test}}.Tests/Sleekflow.${{matrix.test}}.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage/${{matrix.test}} 2>&1 | grep -v "warning"

  generate-report:
    name: Generate Report
    needs: [ test ]
    runs-on: ubuntu-latest
    steps:
      - name: Generate Report
        run: |
          dotnet tool install -g dotnet-reportgenerator-globaltool
          reportgenerator -reports:coverage/**/**/coverage.cobertura.xml -targetdir:coverage -reporttypes:Cobertura

      - name: Generate Coverage Report
        uses: clearlyip/code-coverage-report-action@v5
        id: code_coverage_report_action
        with:
          filename: "./coverage/Cobertura.xml"
          artifact_download_workflow_names: 'master-coverage-snapshot'
          only_list_changed_files: true

      - name: Add Coverage PR Comment
        uses: marocchino/sticky-pull-request-comment@v2
        if: github.event_name == 'pull_request'
        with:
          recreate: true
          path: code-coverage-results.md