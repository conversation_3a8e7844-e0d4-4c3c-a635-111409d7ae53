using System.Reflection;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.Channels
{
    public class ChannelServiceTests
    {
        private Mock<ILogger<ChannelService>> _loggerMock;
        private ChannelService _channelService;

        [SetUp]
        public void Setup()
        {
            _loggerMock = new Mock<ILogger<ChannelService>>();
            var httpClientFactoryMock = new Mock<IHttpClientFactory>();

            // Create minimal ChannelService instance with just the logger
            // We're using reflection to access private methods, so we don't need to mock all dependencies
            _channelService = new ChannelService(
                null, // IIdService
                null, // IWabaService
                _loggerMock.Object,
                null, // IWabaRepository
                null, // IAuditLogService
                new Mock<ICloudApiClients>().Object, // IMessagingHubWebhookService
                null, // ICommonRetryPolicyService
                null, // IWhatsappCloudApiBspClient
                httpClientFactoryMock.Object // HttpClient
            );
        }

        [Test]
        public void IsUpdateDataLocalizationRequiredForRegisterPhoneNumber_WhenErrorContainsDataLocalization_ReturnsTrue()
        {
            // Arrange
            string errorResponseBody =
                @"{""error"":{""message"":""(#100) Invalid parameter"",""type"":""OAuthException"",""code"":100,""error_data"":{""messaging_product"":""whatsapp"",""details"":""Migrated phone number should be first registered with the same data localization region used on the source WABA. Data localization region: 'DE'.""},""fbtrace_id"":""AdPrXdHfO5YPB3bSvo79SEm""}}";

            var errorResponse = JsonConvert.DeserializeObject<GraphApiErrorResponse>(errorResponseBody);
            var exception = new GraphApiClientException(
                errorResponse,
                HttpMethod.Post,
                "phonenumberid/register",
                400,
                "{\"messaging_product\":\"whatsapp\",\"pin\":\"000000\"}",
                errorResponseBody);

            // Get the private method using reflection
            MethodInfo methodInfo = typeof(ChannelService).GetMethod(
                "IsUpdateDataLocalizationRequiredForRegisterPhoneNumber",
                BindingFlags.NonPublic | BindingFlags.Instance);

            // Act
            var result = methodInfo.Invoke(
                _channelService,
                new object[]
                {
                    exception
                });

            // Extract tuple values using reflection
            var isUpdateRequired = (bool) ((dynamic) result).Item1;
            var region = (string) ((dynamic) result).Item2;

            // Assert
            Assert.IsTrue(isUpdateRequired);
            Assert.AreEqual("DE", region);
        }

        [Test]
        public void IsUpdateDataLocalizationRequiredForRegisterPhoneNumber_WhenErrorDoesNotContainDataLocalization_ReturnsFalse()
        {
            // Arrange
            string errorResponseBody =
                @"{""error"":{""message"":""(#100) Invalid parameter"",""type"":""OAuthException"",""code"":100,""error_data"":{""messaging_product"":""whatsapp"",""details"":""Some other error message without data localization.""},""fbtrace_id"":""AdPrXdHfO5YPB3bSvo79SEm""}}";

            var errorResponse = JsonConvert.DeserializeObject<GraphApiErrorResponse>(errorResponseBody);
            var exception = new GraphApiClientException(
                errorResponse,
                HttpMethod.Post,
                "phonenumberid/register",
                400,
                "{\"messaging_product\":\"whatsapp\",\"pin\":\"000000\"}",
                errorResponseBody);


            // Get the private method using reflection
            MethodInfo methodInfo = typeof(ChannelService).GetMethod(
                "IsUpdateDataLocalizationRequiredForRegisterPhoneNumber",
                BindingFlags.NonPublic | BindingFlags.Instance);

            // Act
            var result = methodInfo.Invoke(
                _channelService,
                new object[]
                {
                    exception
                });

            // Extract tuple values using reflection
            var isUpdateRequired = (bool) ((dynamic) result).Item1;
            var region = (string) ((dynamic) result).Item2;

            // Assert
            Assert.IsFalse(isUpdateRequired);
            Assert.IsNull(region);
        }

        [Test]
        public void IsUpdateDataLocalizationRequiredForRegisterPhoneNumber_WhenExceptionOccursDuringParsing_ReturnsFalse()
        {
            // Arrange
            string errorResponseBody =
                @"{""error"":{""message"":""(#100) Invalid parameter"",""type"":""OAuthException"",""code"":100,""error_data"":null,""fbtrace_id"":""AdPrXdHfO5YPB3bSvo79SEm""}}";

            var errorResponse = JsonConvert.DeserializeObject<GraphApiErrorResponse>(errorResponseBody);
            var exception = new GraphApiClientException(
                errorResponse,
                HttpMethod.Post,
                "phonenumberid/register",
                400,
                "{\"messaging_product\":\"whatsapp\",\"pin\":\"000000\"}",
                errorResponseBody);


            // Get the private method using reflection
            MethodInfo methodInfo = typeof(ChannelService).GetMethod(
                "IsUpdateDataLocalizationRequiredForRegisterPhoneNumber",
                BindingFlags.NonPublic | BindingFlags.Instance);

            // Act
            var result = methodInfo.Invoke(
                _channelService,
                new object[]
                {
                    exception
                });

            // Extract tuple values using reflection
            var isUpdateRequired = (bool) ((dynamic) result).Item1;
            var region = (string) ((dynamic) result).Item2;

            // Assert
            Assert.IsFalse(isUpdateRequired);
            Assert.IsNull(region);
        }
    }
}