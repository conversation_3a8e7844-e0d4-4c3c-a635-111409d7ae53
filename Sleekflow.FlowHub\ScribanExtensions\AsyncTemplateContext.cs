using Scriban;
using Scriban.Syntax;

namespace Sleekflow.FlowHub.ScribanExtensions;

public class AsyncTemplateContext : TemplateContext
{
    public override async ValueTask<object> EvaluateAsync(ScriptNode scriptNode, bool aliasReturnedFunction)
    {
        var value = await base.EvaluateAsync(scriptNode, aliasReturnedFunction);

        if (value is Task valueTask)
        {
            var result = await (dynamic) valueTask;

            return new ValueTask<object>(result);
        }

        return value;
    }
}