﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.Unifies;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.UnifyRules;

[TriggerGroup("UnifyRules")]
public class GetUnifyRules : ITrigger
{
    private readonly IUnifyService _unifyService;

    public GetUnifyRules(
        IUnifyService unifyService)
    {
        _unifyService = unifyService;
    }

    public class GetUnifyRulesInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public GetUnifyRulesInput(
            string sleekflowCompanyId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
        }
    }

    public class GetUnifyRulesOutput
    {
        [JsonProperty("unify_rules")]
        public List<UnifyRule> UnifyRules { get; set; }

        [JsonConstructor]
        public GetUnifyRulesOutput(List<UnifyRule> unifyRules)
        {
            this.UnifyRules = unifyRules;
        }
    }

    public async Task<GetUnifyRulesOutput> F(
        GetUnifyRulesInput getUnifyRulesInput)
    {
        var unifyRules = await _unifyService.GetUnifyRulesAsync(
            getUnifyRulesInput.SleekflowCompanyId,
            getUnifyRulesInput.EntityTypeName);

        return new GetUnifyRulesOutput(unifyRules);
    }
}