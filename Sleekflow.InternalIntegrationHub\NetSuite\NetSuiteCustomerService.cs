using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.InternalIntegrationHub.Clients;
using Sleekflow.InternalIntegrationHub.Constants.NetSuite;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;
using Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;
using Sleekflow.InternalIntegrationHub.Utils;
using Currency = Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common.Currency;
using Entity = Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common.Entity;

namespace Sleekflow.InternalIntegrationHub.NetSuite;

public interface INetSuiteCustomerService
{
    public Task<bool> CreateCustomerAsync(CreateCustomerRequest request);

    public Task<bool> CreateCustomerAsync(CreateCustomer.CreateCustomerInput createCustomerInput);

    public Task<bool> UpdateCustomerAsync(UpdateCustomer.UpdateCustomerInput updateCustomerInput);

    public Task<bool> CreatePaymentAsync(
        string billRecordId,
        double totalAmount,
        string companyId,
        DateTime? createdDate);

    public Task<bool> CreatePaymentFromStripeAsync(
        CreatePaymentFromStripe.CreatePaymentFromStripeInput createPaymentFromStripeInput);

    public Task<string?> CreateCreditMemo(CreateCreditMemo.CreateCreditMemoInput input);

    public Task<bool> CreateRefundAsync(CreateRefund.CreateRefundInput input);
}

public class NetSuiteCustomerService : INetSuiteCustomerService, IScopedService
{
    private readonly ILogger<NetSuiteCustomerService> _logger;
    private readonly INetSuiteClient _client;
    private readonly ICacheService _cacheService;
    private readonly INetSuiteSettingService _netSuiteSettingService;
    private readonly INetSuiteEmployeeService _netSuiteEmployeeService;

    public NetSuiteCustomerService(
        ILogger<NetSuiteCustomerService> logger,
        INetSuiteClient client,
        ICacheService cacheService,
        INetSuiteSettingService netSuiteSettingService,
        INetSuiteEmployeeService netSuiteEmployeeService)
    {
        _logger = logger;
        _client = client;
        _cacheService = cacheService;
        _netSuiteSettingService = netSuiteSettingService;
        _netSuiteEmployeeService = netSuiteEmployeeService;
    }

    private async Task<string> GetCustomerInternalIdAsync(string companyId)
    {
        var customerId = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetCustomerInternalIdAsync)}:{companyId}",
            async () =>
            {
                var customerSearch = await _client.GetAsync<CommonSearchResponse>(
                    Endpoints.CreateCustomerEndpoint,
                    new Dictionary<string, string>
                    {
                        {
                            "q", "externalId IS " + companyId
                        }
                    });
                if (customerSearch.Data?.Items.Count == 0)
                {
                    throw new SfUserFriendlyException("Customer not found");
                }

                return customerSearch.Data?.Items[0].Id!;
            },
            TimeSpan.FromHours(1));

        return customerId;
    }

    public async Task<bool> CreateCustomerAsync(CreateCustomerRequest request)
    {
        var response = await _client.PostAsync<object>(
            Endpoints.CreateCustomerEndpoint,
            request);
        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to create customer to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }

    public async Task<bool> CreateCustomerAsync(CreateCustomer.CreateCustomerInput createCustomerInput)
    {
        var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
            await _netSuiteSettingService.NetSuitePreparation(
                CancellationToken.None,
                showEmployee: false,
                showTerm: false);
        var subsidiary = NetSuiteUtils.LocationConverter(createCustomerInput.CompanyCountry, subsidiaryDetails);
        var currency = NetSuiteUtils.CurrencyConverter(createCustomerInput.CompanyCountry, currencyDetails);
        var salesRepId = createCustomerInput.SalesRepEmail == null
            ? null
            : await _netSuiteEmployeeService.GetEmployeeIdByEmailAsync(createCustomerInput.SalesRepEmail);
        var request = new CreateCustomerRequest(
            createCustomerInput.CompanyId,
            createCustomerInput.CompanyName,
            new CreateCustomerRequestCommonId(subsidiary),
            createCustomerInput.CompanyOwnerEmail,
            createCustomerInput.CompanyOwnerPhone,
            null,
            new CreateCustomerRequestCommonId(currency),
            currency == "1"
                ? null
                : new CurrencyList(
                [
                    new CurrencyListElement("1")
                ]),
            salesRepId == null ? null : new CreateCustomerRequestCommonId(salesRepId));

        return await CreateCustomerAsync(request);
    }

    public async Task<bool> UpdateCustomerAsync(UpdateCustomer.UpdateCustomerInput updateCustomerInput)
    {
        var customerInternalId = await GetCustomerInternalIdAsync(updateCustomerInput.CompanyId);

        string? subsidiary = null;
        string? currency = null;
        string? salesRep = null;

        if (updateCustomerInput.CompanyCountry != null)
        {
            var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
                await _netSuiteSettingService.NetSuitePreparation(
                    CancellationToken.None,
                    showEmployee: false,
                    showTerm: false);
            if (updateCustomerInput.CompanyCountry != null)
            {
                subsidiary = NetSuiteUtils.LocationConverter(updateCustomerInput.CompanyCountry, subsidiaryDetails);
                currency = NetSuiteUtils.CurrencyConverter(updateCustomerInput.CompanyCountry, currencyDetails);
            }
        }

        if (updateCustomerInput.SalesRepEmail != null)
        {
            var salesRepId =
                await _netSuiteEmployeeService.GetEmployeeIdByEmailAsync(updateCustomerInput.SalesRepEmail);
            salesRep = salesRepId;
        }

        var request = new CreateCustomerRequest(
            updateCustomerInput.CompanyId,
            updateCustomerInput.CompanyName,
            subsidiary == null ? null : new CreateCustomerRequestCommonId(subsidiary),
            updateCustomerInput.CompanyOwnerEmail,
            updateCustomerInput.CompanyOwnerPhone,
            null,
            currency == null ? null : new CreateCustomerRequestCommonId(currency),
            currency == "1"
                ? null
                : new CurrencyList(
                [
                    new CurrencyListElement("1")
                ]),
            salesRep == null ? null : new CreateCustomerRequestCommonId(salesRep));

        var response = await _client.PatchAsync<object>(
            string.Format(Endpoints.UpdateCustomerEndpoint, customerInternalId),
            request);
        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to update customer to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }

    public async Task<bool> CreatePaymentAsync(
        string billRecordId,
        double totalAmount,
        string companyId,
        DateTime? createdDate)
    {
        var dateFormat = "yyyy-MM-dd";
        var tranDate = createdDate == null
            ? DateTime.Now.ToString(dateFormat)
            : createdDate.Value.ToString(dateFormat);
        var paymentItems = new List<ApplyElement>();
        var invoiceSearch = await _client.GetAsync<CommonSearchResponse>(
            Endpoints.CreateInvoiceEndpoint,
            new Dictionary<string, string>
            {
                {
                    "q", $"externalId IS \"{billRecordId}\""
                }
            });
        var isInvoicesFound = invoiceSearch.Data?.Items.Count > 0;
        if (isInvoicesFound == false)
        {
            throw new SfNotFoundObjectException("Invoice not found");
        }

        var invoiceInternalId = invoiceSearch.Data?.Items[0].Id;

        var invoiceDetails = await _client.GetAsync<InvoiceDetailsResponse>(
            string.Format(Endpoints.GetInvoiceEndpoint, invoiceInternalId),
            null);

        if (invoiceDetails.Data == null)
        {
            throw new SfNotFoundObjectException("Invoice not found");
        }

        paymentItems.Add(
            new ApplyElement(totalAmount, true, new Doc(billRecordId)));

        var request = new CreatePaymentFromStripeRequest(
            true,
            new Collection<ApplyElement>(paymentItems),
            new Currency(invoiceDetails.Data.Currency.Id),
            new Customer(companyId),
            tranDate);

        var response = await _client.PostAsync<object>(
            Endpoints.CreatePaymentEndpoint,
            request);

        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to create payment to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }

    public async Task<bool> CreatePaymentFromStripeAsync(
        CreatePaymentFromStripe.CreatePaymentFromStripeInput createPaymentFromStripeInput)
    {
        const string dateFormat = "yyyy-MM-dd";
        var tranDate = DateTime.Now.ToString(dateFormat);
        var paymentItems = new List<ApplyElement>();
        var invoiceSearch = await _client.GetAsync<CommonSearchResponse>(
            Endpoints.CreateInvoiceEndpoint,
            new Dictionary<string, string>
            {
                {
                    "q", $"externalId IS \"{createPaymentFromStripeInput.ExternalId}\""
                }
            });

        var isInvoicesFound = invoiceSearch.Data?.Items.Count > 0;
        if (isInvoicesFound == false)
        {
            throw new SfNotFoundObjectException("Invoice not found");
        }

        var totalAmount = createPaymentFromStripeInput.Items.Sum(e => e.Amount);

        var invoiceInternalId = invoiceSearch.Data?.Items[0].Id;

        var invoiceDetails = await _client.GetAsync<InvoiceDetailsResponse>(
            string.Format(Endpoints.GetInvoiceEndpoint, invoiceInternalId),
            null);

        if (invoiceDetails.Data == null)
        {
            throw new SfNotFoundObjectException("Invoice not found");
        }

        paymentItems.Add(
            new ApplyElement(decimal.ToDouble(totalAmount), true, new Doc(createPaymentFromStripeInput.ExternalId)));

        var request = new CreatePaymentFromStripeRequest(
            true,
            new Collection<ApplyElement>(paymentItems),
            new Currency(invoiceDetails.Data.Currency.Id),
            new Customer(createPaymentFromStripeInput.CompanyId),
            tranDate);

        var response = await _client.PostAsync<object>(
            Endpoints.CreatePaymentEndpoint,
            request);

        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to create payment to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }

    public async Task<string?> CreateCreditMemo(CreateCreditMemo.CreateCreditMemoInput input)
    {
        var itemId = await _netSuiteSettingService.GetInvoiceItemIdAsync(input.ItemName);
        if (itemId == null)
        {
            throw new SfNotFoundObjectException($"{input.ItemName} Item Id not found");
        }

        var itemElement = new Element<Item>(input.Amount, new Item(itemId));
        var creditMemoExternalId = $"cm-{input.InvoiceExternalId}";
        var request = new CreateCreditMemoRequest(
            new Entity(input.CompanyId),
            creditMemoExternalId,
            new Collection<Element<Item>>([itemElement]));
        var response = await _client.PostAsync<object>(
            Endpoints.CreditMemoEndpoint,
            request);

        if (response.IsSuccessful)
        {
            return creditMemoExternalId;
        }

        _logger.LogError(
            "Failed to create credit memo to NetSuite: {Content} Request: {Request}",
            response.Content,
            request.ToString());
        return null;
    }

    public async Task<bool> CreateRefundAsync(CreateRefund.CreateRefundInput input)
    {
        var request = new CreateRefundRequest(
            new Customer(input.CompanyId),
            new Collection<ApplyElement>(
                [new ApplyElement((double) input.Amount, true, new Doc(input.CreditMemoExternalId))]));
        var response = await _client.PostAsync<object>(Endpoints.CustomerRefundEndpoint, request);

        if (!response.IsSuccessful)
        {
            _logger.LogError(
                "Failed to create credit refund to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }
}