using Azure.Monitor.OpenTelemetry.Exporter;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.OpenTelemetry.Meters;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class ModuleUtils
{
    public static void BuildOpenTelemetryServices(IServiceCollection b)
    {
#if !SWAGGERGEN
        var applicationInsightConnectionString =
            Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING");
        if (applicationInsightConnectionString != null && !string.IsNullOrEmpty(applicationInsightConnectionString))
        {
            b.AddOpenTelemetry()
                .WithMetrics(
                    metrics =>
                    {
                        metrics.AddMeter(BaseMeters.SleekflowMeters);
                        metrics.AddAzureMonitorMetricExporter(
                            credential =>
                            {
                                credential.ConnectionString = applicationInsightConnectionString;
                            });
                    });
        }
#endif
    }
}