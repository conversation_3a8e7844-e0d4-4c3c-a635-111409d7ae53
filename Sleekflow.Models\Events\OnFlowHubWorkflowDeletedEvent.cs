namespace Sleekflow.Models.Events;

public class OnFlowHubWorkflowDeletedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string? SleekflowStaffId { get; set; }

    public string WorkflowId { get; set; }

    public OnFlowHubWorkflowDeletedEvent(string sleekflowCompanyId, string? sleekflowStaffId, string workflowId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowStaffId = sleekflowStaffId;
        WorkflowId = workflowId;
    }
}