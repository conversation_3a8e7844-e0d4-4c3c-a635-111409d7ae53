using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Sleekflow.InternalIntegrationHub.Models.Events;

namespace Sleekflow.InternalIntegrationHub.Workers.Triggers.Integrations;

public class SyncCompanyToNetSuiteIntegrationTrigger
{
    private readonly IBus _bus;

    public SyncCompanyToNetSuiteIntegrationTrigger(IBus bus)
    {
        _bus = bus;
    }

    [Function("SyncCompanyToNetSuiteIntegrationTrigger")]
    public Task RunAsync(
        [TimerTrigger("0 0 20 * * *")]
        TimerInfo timerInfo)
    {
        return _bus.Publish(
            new SyncCompanyToNetSuiteIntegrationEvent());
    }
}