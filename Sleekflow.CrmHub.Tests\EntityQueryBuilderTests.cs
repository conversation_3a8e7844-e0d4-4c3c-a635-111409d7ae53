using Sleekflow.CrmHub.Entities;

namespace Sleekflow.CrmHub.Tests;

public class EntityQueryBuilderTests
{
    [SetUp]
    public void Setup()
    {
        // Method intentionally left empty.
    }

    [Test]
    public void QueryTest()
    {
        var queryDefinition = EntityQueryBuilder.BuildQueryDef(
            new List<EntityQueryBuilder.ISelect>(),
            "Contact",
            new List<EntityQueryBuilder.FilterGroup>
            {
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("name", "=", "<PERSON>"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("phone_number", "=", "61096623"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                    })
            },
            new List<EntityQueryBuilder.Sort>
            {
                new EntityQueryBuilder.Sort("phone_number", "DESC", false)
            },
            new List<EntityQueryBuilder.GroupBy>(),
            "comp id");

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT *\n" +
                "FROM Contact m\n" +
                "WHERE (m[\"sys_sleekflow_company_id\"] = @sys_sleekflow_company_id_0) AND (m[\"sys_entity_type_name\"] = @sys_entity_type_name_1) AND (m[\"sys_type_name\"] = @sys_type_name_2) AND (m[\"name\"][\"v\"] = @name_3) AND (m[\"phone_number\"][\"v\"] = @phone_number_4) AND (m[\"email\"][\"v\"] = @email_5 OR m[\"email\"][\"v\"] = @email_6)\n" +
                "ORDER BY m[\"phone_number\"][\"i\"] DESC"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@sys_sleekflow_company_id_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("comp id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@sys_entity_type_name_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("Contact"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@sys_type_name_2"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo("Entity"));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@name_3"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo("Leo Choi"));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@phone_number_4"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo("61096623"));

        Assert.That(queryDefinition.GetQueryParameters()[5].Name, Is.EqualTo("@email_5"));
        Assert.That(queryDefinition.GetQueryParameters()[5].Value, Is.EqualTo("<EMAIL>"));

        Assert.That(queryDefinition.GetQueryParameters()[6].Name, Is.EqualTo("@email_6"));
        Assert.That(queryDefinition.GetQueryParameters()[6].Value, Is.EqualTo("<EMAIL>"));
    }

    [Test]
    public void CountCaseInsensitiveTest()
    {
        var queryDefinition = EntityQueryBuilder.BuildQueryDef(
            new List<EntityQueryBuilder.ISelect>
            {
                new EntityQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            "Contact",
            new List<EntityQueryBuilder.FilterGroup>
            {
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("name", "=", "Leo Choi"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("phone_number", "=", "61096623"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                    })
            },
            new List<EntityQueryBuilder.Sort>
            {
                // Diff here false
                new EntityQueryBuilder.Sort("phone_number", "DESC", isCaseSensitive: false)
            },
            new List<EntityQueryBuilder.GroupBy>(),
            "comp id");

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT COUNT(1) count\n" +
                "FROM Contact m\n" +
                "WHERE (m[\"sys_sleekflow_company_id\"] = @sys_sleekflow_company_id_0) AND (m[\"sys_entity_type_name\"] = @sys_entity_type_name_1) AND (m[\"sys_type_name\"] = @sys_type_name_2) AND (m[\"name\"][\"v\"] = @name_3) AND (m[\"phone_number\"][\"v\"] = @phone_number_4) AND (m[\"email\"][\"v\"] = @email_5 OR m[\"email\"][\"v\"] = @email_6)\n" +
                "ORDER BY m[\"phone_number\"][\"i\"] DESC"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@sys_sleekflow_company_id_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("comp id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@sys_entity_type_name_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("Contact"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@sys_type_name_2"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo("Entity"));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@name_3"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo("Leo Choi"));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@phone_number_4"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo("61096623"));

        Assert.That(queryDefinition.GetQueryParameters()[5].Name, Is.EqualTo("@email_5"));
        Assert.That(queryDefinition.GetQueryParameters()[5].Value, Is.EqualTo("<EMAIL>"));

        Assert.That(queryDefinition.GetQueryParameters()[6].Name, Is.EqualTo("@email_6"));
        Assert.That(queryDefinition.GetQueryParameters()[6].Value, Is.EqualTo("<EMAIL>"));
    }

    [Test]
    public void CountCaseSensitiveTest()
    {
        var queryDefinition = EntityQueryBuilder.BuildQueryDef(
            new List<EntityQueryBuilder.ISelect>
            {
                new EntityQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            "Contact",
            new List<EntityQueryBuilder.FilterGroup>
            {
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("name", "=", "Leo Choi"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("phone_number", "=", "61096623"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                    })
            },
            new List<EntityQueryBuilder.Sort>
            {
                // Diff here true
                new EntityQueryBuilder.Sort("phone_number", "DESC", isCaseSensitive: true)
            },
            new List<EntityQueryBuilder.GroupBy>(),
            "comp id");

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT COUNT(1) count\n" +
                "FROM Contact m\n" +
                "WHERE (m[\"sys_sleekflow_company_id\"] = @sys_sleekflow_company_id_0) AND (m[\"sys_entity_type_name\"] = @sys_entity_type_name_1) AND (m[\"sys_type_name\"] = @sys_type_name_2) AND (m[\"name\"][\"v\"] = @name_3) AND (m[\"phone_number\"][\"v\"] = @phone_number_4) AND (m[\"email\"][\"v\"] = @email_5 OR m[\"email\"][\"v\"] = @email_6)\n" +
                "ORDER BY m[\"phone_number\"][\"v\"] DESC"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@sys_sleekflow_company_id_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("comp id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@sys_entity_type_name_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("Contact"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@sys_type_name_2"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo("Entity"));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@name_3"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo("Leo Choi"));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@phone_number_4"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo("61096623"));

        Assert.That(queryDefinition.GetQueryParameters()[5].Name, Is.EqualTo("@email_5"));
        Assert.That(queryDefinition.GetQueryParameters()[5].Value, Is.EqualTo("<EMAIL>"));

        Assert.That(queryDefinition.GetQueryParameters()[6].Name, Is.EqualTo("@email_6"));
        Assert.That(queryDefinition.GetQueryParameters()[6].Value, Is.EqualTo("<EMAIL>"));
    }

    [Test]
    public void CountCaseSensitiveGroupByTest()
    {
        var queryDefinition = EntityQueryBuilder.BuildQueryDef(
            new List<EntityQueryBuilder.ISelect>
            {
                new EntityQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            "Contact",
            new List<EntityQueryBuilder.FilterGroup>
            {
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("name", "=", "Leo Choi"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("phone_number", "=", "61096623"),
                    }),
                new EntityQueryBuilder.FilterGroup(
                    new List<EntityQueryBuilder.IFilter>
                    {
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                        new EntityQueryBuilder.Filter("email", "=", "<EMAIL>"),
                    })
            },
            new List<EntityQueryBuilder.Sort>(),
            new List<EntityQueryBuilder.GroupBy>
            {
                new EntityQueryBuilder.GroupBy("campaign_id", isCaseSensitive: true)
            },
            "comp id");

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT COUNT(1) count, m[\"campaign_id\"][\"v\"] as \"campaign_id\"\n" +
                "FROM Contact m\n" +
                "WHERE (m[\"sys_sleekflow_company_id\"] = @sys_sleekflow_company_id_0) AND (m[\"sys_entity_type_name\"] = @sys_entity_type_name_1) AND (m[\"sys_type_name\"] = @sys_type_name_2) AND (m[\"name\"][\"v\"] = @name_3) AND (m[\"phone_number\"][\"v\"] = @phone_number_4) AND (m[\"email\"][\"v\"] = @email_5 OR m[\"email\"][\"v\"] = @email_6)\n" +
                "GROUP BY m[\"campaign_id\"][\"v\"]"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@sys_sleekflow_company_id_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("comp id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@sys_entity_type_name_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("Contact"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@sys_type_name_2"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo("Entity"));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@name_3"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo("Leo Choi"));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@phone_number_4"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo("61096623"));

        Assert.That(queryDefinition.GetQueryParameters()[5].Name, Is.EqualTo("@email_5"));
        Assert.That(queryDefinition.GetQueryParameters()[5].Value, Is.EqualTo("<EMAIL>"));

        Assert.That(queryDefinition.GetQueryParameters()[6].Name, Is.EqualTo("@email_6"));
        Assert.That(queryDefinition.GetQueryParameters()[6].Value, Is.EqualTo("<EMAIL>"));
    }
}