using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetStateStepExecutions : ITrigger
{
    private readonly IStepExecutionService _stepExecutionService;
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowStepNodeLocator _workflowStepNodeLocator;

    public GetStateStepExecutions(
        IStepExecutionService stepExecutionService,
        IWorkflowService workflowService,
        IWorkflowStepNodeLocator workflowStepNodeLocator)
    {
        _stepExecutionService = stepExecutionService;
        _workflowService = workflowService;
        _workflowStepNodeLocator = workflowStepNodeLocator;
    }

    public class GetStateStepExecutionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("limit")]
        [Required]
        [Range(1, 1000)]
        public int Limit { get; set; }

        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonConstructor]
        public GetStateStepExecutionsInput(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            Limit = limit;
            StateId = stateId;
        }
    }

    public class GetStateStepExecutionsOutput
    {
        [JsonProperty("step_executions")]
        public List<StepExecutionDto> StepExecutions { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetStateStepExecutionsOutput(
            List<StepExecutionDto> stepExecutions,
            string? nextContinuationToken)
        {
            StepExecutions = stepExecutions;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetStateStepExecutionsOutput> F(GetStateStepExecutionsInput getStateStepExecutionsInput)
    {
        var (stepExecutions, nextContinuationToken) = await _stepExecutionService.GetStateStepExecutionsAsync(
            getStateStepExecutionsInput.SleekflowCompanyId,
            getStateStepExecutionsInput.ContinuationToken,
            getStateStepExecutionsInput.Limit,
            getStateStepExecutionsInput.StateId);

        if (stepExecutions is not { Count: > 0 })
        {
            return MapResult(
                new List<StepExecution>(),
                null);
        }

        var versionedWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            getStateStepExecutionsInput.SleekflowCompanyId,
            stepExecutions[0].StateIdentity.WorkflowVersionedId);

        // We won't map step_node_id if any of the following is true:
        // 1. Versioned workflow is deleted (should not happen), but just precaution
        // 2. Step executions are already mapped with step node ids (this means the enrollment happened after introduction of step_node_id in step_execution)
        if (versionedWorkflow is null
            || versionedWorkflow is { ActivationStatus: WorkflowActivationStatuses.Deleted }
            || stepExecutions.Exists(x => !string.IsNullOrWhiteSpace(x.StepNodeId)))
        {
            return MapResult(
                stepExecutions,
                nextContinuationToken);
        }

        var stepNodeEntries = _workflowStepNodeLocator.GetStepNodeIds(
            versionedWorkflow.Steps,
            versionedWorkflow.Metadata);

        var stepsWithoutNodeId = stepExecutions
            .Where(x => string.IsNullOrWhiteSpace(x.StepNodeId))
            .ToList();

        foreach (var step in stepsWithoutNodeId)
        {
            step.StepNodeId = stepNodeEntries.GetValueOrDefault(step.StepId);
        }

        return MapResult(
            stepExecutions,
            nextContinuationToken);
    }

    private static GetStateStepExecutionsOutput MapResult(
        List<StepExecution> stepExecutions,
        string? nextContinuationToken)
        => new GetStateStepExecutionsOutput(
            stepExecutions
                .Select(se => new StepExecutionDto(se))
                .ToList(),
            nextContinuationToken);
}