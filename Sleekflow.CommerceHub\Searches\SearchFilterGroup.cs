using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Searches;

public class SearchFilterGroup
{
    [Required]
    [ValidateArray]
    [JsonProperty("filters")]
    public List<SearchFilter> Filters { get; set; }

    [JsonConstructor]
    public SearchFilterGroup(
        List<SearchFilter> filters)
    {
        Filters = filters;
    }
}