using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.FlowHub.Triggers.Authorized.AiWorkflows;

[TriggerGroup(
    ControllerNames.AiWorkflows,
    $"{BasePaths.Authorized}",
    [
        AuthorizationFilterNames.HeadersAuthorizationFuncFilter
    ])]
public class GetStepsAndMetadata(
    ISleekflowAuthorizationContext sleekflowAuthorizationContext,
    IAiWorkflowService aiWorkflowService,
    IAgentConfigService agentConfigService)
    : ITrigger<GetStepsAndMetadata.GetStepsAndMetadataInput, GetStepsAndMetadata.GetStepsAndMetadataOutput>
{
    public class GetStepsAndMetadataInput(string agentConfigId, string enterAiAgentStepId)
    {
        [Required]
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; } = agentConfigId;

        [Required]
        [JsonProperty("enter_ai_agent_step_id")]
        public string EnterAiAgentStepId { get; set; } = enterAiAgentStepId;
    }

    [method: JsonConstructor]
    public class GetStepsAndMetadataOutput(List<Step> steps, Dictionary<string, object> metadata)
    {
        [JsonProperty("steps")]
        public List<Step> Steps { get; set; } = steps;

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; } = metadata;
    }

    public async Task<GetStepsAndMetadataOutput> F(GetStepsAndMetadataInput input)
    {
        var sleekflowCompanyId = sleekflowAuthorizationContext.SleekflowCompanyId!;

        var agentConfig = await agentConfigService.GetAsync(sleekflowCompanyId, input.AgentConfigId);
        if (agentConfig == null)
        {
            throw new Exception($"Agent config with ID {input.AgentConfigId} not found.");
        }

        var steps = aiWorkflowService.generateStepsByConditions(
            agentConfig,
            input.EnterAiAgentStepId,
            Guid.NewGuid().ToString());

        var metadata = aiWorkflowService.generateMetadataByConditions(
            agentConfig,
            input.EnterAiAgentStepId);

        return new GetStepsAndMetadataOutput(steps, metadata);
    }
}