using GraphApi.Client.ApiClients;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Newtonsoft.Json;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Tests.Configurations;
using Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;
using Sleekflow.Webhooks;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.WabaLevelCreditManagement;

public class WabaLevelCreditManagementTest
{
    private const string StaffId = "staffId1";
    private const string CompanyId = "test_companyId1";

    private IIdService _idService;
    private IBus _bus;
    private IWabaRepository _wabaRepository;
    private IWabaService _wabaService;
    private IBusinessBalanceRepository _businessBalanceRepository;
    private IBusinessBalanceTransactionLogRepository _businessBalanceTransactionLogRepository;
    private IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private IWhatsappCloudApiBusinessBalanceWebhookService _whatsappCloudApiBusinessBalanceWebhookService;
    private IWabaLevelCreditManagementService _service;

    private Mock<IAuditLogService> _auditLogService;
    private Mock<ILogger<WabaLevelCreditManagementService>> _logger;
    private Mock<ILogger<BusinessBalanceTransactionLogService>> _businessBalanceTransactionLogLogger;
    private Mock<ICloudApiClients> _cloudApiClientsMock;
    private Mock<IBusinessBalanceTransactionLogValidator> _businessBalanceTransactionLogValidatorMock;
    private Mock<IHttpClientFactory> _httpClientFactoryMock;
    private Mock<IWebhookRepository> _webhookRepositoryMock;
    private Mock<IWabaAssetsManager> _wabaAssetsManagerMock;

    private static TRepository GetMessagingHubRepository<TRepository, TEntity>(ServiceProvider? serviceProvider)
        where TRepository : BaseRepository<TEntity>, IRepository<TEntity>
        where TEntity : Entity
    {
        var messagingHubRepository = (TRepository) Activator.CreateInstance(
            typeof(TRepository),
            [
                NullLogger<TRepository>.Instance,
                serviceProvider
            ])!;

        return messagingHubRepository;
    }

    [OneTimeSetUp]
    public void BusinessBalanceAutoTopUpServiceUnitTestsSetUp()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<IBusinessBalanceService, BusinessBalanceService>();
        serviceCollection.AddSingleton<IMessagingHubDbResolver>(
            new MessagingHubDbResolver(new MessagingHubDbTestConfig()));
        Modules.BuildServiceBusServices(serviceCollection);
        var serviceProvider = serviceCollection.BuildServiceProvider();

        _idService = new IdService(
            new Mock<ILogger<IdService>>().Object,
            new DbContainerResolver(new CosmosDbConfig()));
        _logger = new Mock<ILogger<WabaLevelCreditManagementService>>();
        _auditLogService = new Mock<IAuditLogService>();
        _businessBalanceTransactionLogLogger = new Mock<ILogger<BusinessBalanceTransactionLogService>>();
        _bus = serviceProvider.GetRequiredService<IBus>();

        _cloudApiClientsMock = new Mock<ICloudApiClients>();
        _cloudApiClientsMock.Setup(x => x.WhatsappCloudApiBspClient)
            .Returns(new Mock<IWhatsappCloudApiBspClient>().Object);
        _cloudApiClientsMock.Setup(x => x.WhatsappCloudApiAuthenticationClient)
            .Returns(new Mock<IWhatsappCloudApiAuthenticationClient>().Object);
        _cloudApiClientsMock.Setup(x => x.WhatsappCloudApiMessagingClient)
            .Returns(new Mock<IWhatsappCloudApiMessagingClient>().Object);
        _cloudApiClientsMock.Setup(x => x.FacebookCommerceClient).Returns(new Mock<IFacebookCommerceClient>().Object);
        _businessBalanceTransactionLogValidatorMock = new Mock<IBusinessBalanceTransactionLogValidator>();
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();

        _webhookRepositoryMock = new Mock<IWebhookRepository>();
        _wabaRepository = GetMessagingHubRepository<WabaRepository, Waba>(serviceProvider);
        _businessBalanceRepository = GetMessagingHubRepository<BusinessBalanceRepository, BusinessBalance>(serviceProvider);
        _businessBalanceTransactionLogRepository = GetMessagingHubRepository<BusinessBalanceTransactionLogRepository, BusinessBalanceTransactionLog>(serviceProvider);
        _wabaAssetsManagerMock = new Mock<IWabaAssetsManager>();

        _wabaService = new WabaService(
            _bus,
            _idService,
            new Mock<SecretConfig>().Object,
            new Mock<ILogger<WabaService>>().Object,
            _wabaRepository,
            new Mock<IAuditLogService>().Object,
            new Mock<ICloudApiClients>().Object,
            new Mock<ICommonRetryPolicyService>().Object,
            new Mock<IWhatsappCloudApiWebhookConfig>().Object,
            new Mock<IAppConfig>().Object,
            _httpClientFactoryMock.Object,
            _wabaAssetsManagerMock.Object);

        _businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idService,
            _auditLogService.Object,
            _cloudApiClientsMock.Object,
            _businessBalanceTransactionLogLogger.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepository,
            _wabaService,
            _httpClientFactoryMock.Object);

        _whatsappCloudApiBusinessBalanceWebhookService = new WhatsappCloudApiBusinessBalanceWebhookService(
            _webhookRepositoryMock.Object,
            _bus,
            _idService);

        _service = new WabaLevelCreditManagementService(
            _idService,
            _auditLogService.Object,
            _logger.Object,
            new BusinessBalanceService(
                _idService,
                _auditLogService.Object,
                new Mock<ILogger<BusinessBalanceService>>().Object,
                _businessBalanceRepository,
                _wabaRepository),
            _wabaService,
            _businessBalanceTransactionLogRepository,
            _bus,
            _businessBalanceTransactionLogService,
            _whatsappCloudApiBusinessBalanceWebhookService);
    }

    [Test]
    public async Task Test_1_Two_Users_Switch_To_Waba_Level()
    {
        #region set up data
        var businessBalanceString = """
                            {
                                "facebook_business_id": "****************",
                                "credit": {
                                    "currency_iso_code": "USD",
                                    "amount": 120
                                },
                                "used": {
                                    "currency_iso_code": "USD",
                                    "amount": 19.153799999999997
                                },
                                "markup": {
                                    "currency_iso_code": "USD",
                                    "amount": 2.298456
                                },
                                "transaction_handling_fee": {
                                    "currency_iso_code": "USD",
                                    "amount": 0.574614
                                },
                                "markup_profile": {
                                    "per_paid_business_initiated_conversation_fee_markup": {
                                        "currency_iso_code": "USD",
                                        "amount": 0
                                    },
                                    "per_paid_user_initiated_conversation_fee_markup": {
                                        "currency_iso_code": "USD",
                                        "amount": 0
                                    },
                                    "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                    "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                    "transaction_handling_fee_rate": 0.03
                                },
                                "conversation_usage_insert_state": {
                                    "last_conversation_usage_insert_timestamp": 1727074800,
                                    "waba_conversation_insertion_exception": {},
                                    "updated_at": "2024-09-23T07:05:02.423Z"
                                },
                                "record_statuses": [],
                                "created_at": "2023-11-14T04:34:40.391Z",
                                "updated_at": "2024-09-23T07:05:02.507Z",
                                "_etag": "\"550006ca-0000-1900-0000-66f1131e0100\"",
                                "id": "aJiDAGEzy5aeNe2",
                                "sys_type_name": "BusinessBalance",
                                "_rid": "x18TAKoTxS2nAAAAAAAAAA==",
                                "_self": "dbs/x18TAA==/colls/x18TAKoTxS0=/docs/x18TAKoTxS2nAAAAAAAAAA==/",
                                "_attachments": "attachments/",
                                "_ts": 1727075102
                            }
                            """;
        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString)!;

        const string wabaString = """
                                  {
                                      "facebook_waba_id": "166367663226917",
                                      "facebook_waba_business_id": "****************",
                                      "facebook_business_id": "****************",
                                      "sleekflow_company_ids": [
                                          "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                          "81ab95d5-bb44-4678-9765-f63640a305d8"
                                      ],
                                      "facebook_waba_name": "SleekFlow App WABA",
                                      "facebook_waba_account_review_status": "APPROVED",
                                      "facebook_waba_business_name": "sfv2testing",
                                      "facebook_waba_primary_funding_id": "****************",
                                      "facebook_waba_message_template_namespace": "e4450b53_d977_49da_b1a3_0d41bf527e66",
                                      "facebook_waba_long_lived_access_token": {
                                          "encrypted_token": "n8XQZJqH0Md/n1QZT04Xo1WvdJdGTJD1NKIIvrwOApt+ofYQPCUeEkuQHYlBkB/x8ZDImtvxr3AJoGUZfa8zi7IkDkkG3m8tinXjTTaiNm4B6AHIyER9kSa54B8ALooIyJV6ZljbHGpOfZtM4HRRFqbEsSqhvE+Ffboi1DVK9Dr9U0AC9kJXtf5jP3lojkCcTBeqEC/wm+WxPOyrHaCcMuNfl3E0NCX/iaC171GoJMZMoiH3rj9YZXwDAZ1lyeVgJTiMWSgSQ1mNWbIE",
                                          "token_type": "bearer",
                                          "expiry_datetime": null,
                                          "is_valid": true
                                      },
                                      "facebook_business_integration_system_user_access_tokens": [
                                          {
                                              "encrypted_token": "laTjYelMgUHHg1285gENv+bXbB+6XuXFOfEhwskFGaEB9UgD1DUj/bNKpuAyPe8uYjvXvPn+r4lK8ebeLdVfGz32IZMj6tTrPA4z0VfUZXbsVNzzNn6Xi0AFn+gJhFdf4xlSkHsKzm+KR+BrP2uUOZ4+P7zj1WVG3+AzDfXdFPeBoE4uJJfsa53aWXwGBN4IBxmcaPHmvqix4buj3NqphK3nPbnidwTdE6h9g6J0qzhoTUSvQFm7BFyhiwRm3x+WbJ6LexezKtFG1ZseV/ZVaJwMjr72OPp7Bpn+BHGDf9QUnT2INDPwPOUmXIDnUwCQ+EW5u14ex4HpW3CDplwfPXnvtIay5SybRSyUszbj860=t2ICFwFtvG4bfv4H",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "whatsapp_business_management",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122094662954371790",
                                              "created_at": "2024-06-13T10:40:39.867Z"
                                          },
                                          {
                                              "encrypted_token": "tx1zQ2MD5TquPe5hh3G0UlK4yVjKx9v1JqQ+M9mgSq4JOO4nM5EtRzbG+wqU60Uxiey+DqrMf3qd+75jrvymR7Ts4v9HTsPtr+OYswUKbc2uIJVP1UFxDYcx8jHT96jQYiShOG898cm+kzIRPDmjdjO9ggSkqYnN7Mw912ZNzXPnsOOnHmEa7AlXqDXK6dgsW1FYfq8SeddzaELKRIJGmkCFapozg9oc9Tw35gDbGiCb44+jRJVbgdEu7b5A3ce94D805FtkwN1Qjvw+ifw4OFJR1G5aeoysi/0zlELkAuqr+yLWnX9768KZJROdHfWDvGae+3DARSNUfwtiZNG2MVh+S3Q6VAV2UdBBVSs1sTQ=ENY3h0ubcd7M965B",
                                              "scopes": [
                                                  "pages_show_list",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "whatsapp_business_management",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122094662954371790",
                                              "created_at": "2024-06-13T06:54:23.345Z"
                                          },
                                          {
                                              "encrypted_token": "iWzMTg9o5o7PYyTCyPNZLZ0+cq1weCoX1yop9aMtbwiBV0nTbc0QgeSrDmW/mPhf0q4R/NXu+cRm8F7nbYKwCP7Fz5xFizM1eYuTwSi/FaOu31SHM6IDRLN+M1CUqA6V9sb4S1ayxxa61XKyheQsOLKxtcYrH+dxFomUaKtVddPvPvIJX42s7hV+h4m2hs7qz+7ujLL7/GNQuJ6aS0W92h5I7HUCqlwJ/GHawr4dyERkk+e8r+07LAFvtkM4H1MjNFgXtZlqsBShjfxUzmcnzHH8/JYIXUmZMBlyxHNc/9KgxhUwMyYm4Q+f4po2ai+GtJ5lZkNfk+URMmqxOsyxsP0mxJDA4AgzMH68mc6XXqY=0kgTcpKICd7d=RC0",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "ads_management",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "instagram_basic",
                                                  "instagram_manage_comments",
                                                  "leads_retrieval",
                                                  "whatsapp_business_management",
                                                  "instagram_manage_messages",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "ads_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_basic",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_manage_comments",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "leads_retrieval",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "instagram_manage_messages",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122124012134119638",
                                              "created_at": "2024-04-24T02:40:08.643Z"
                                          },
                                          {
                                              "encrypted_token": "4cZ7ZP4Zfsjo6Ba909FT0O9PVGxeUekWluphlJ3weOYg3zjpifXM5ETZiPPu5U5ENNSt32pcacQ1fzb0JdveKZ62SoCibUPKyQtWgU0tZEjO/qR0dL2TuQrMyBcKR5WiWeyAUQL+2uPOecWSQX5flt6bQ7iG89KSMzqwiqReIMoxSFTSkr7opAjCcbY1/UjERAjXbhmWwCIldrrLu6pcQ5sy4RTOqvxXIlqYwq5xEpa5pg/1femU96goQIMfeRRNkbLrO95q5Yzyx5mxg6vEMzmORLF3idWwxX+5uRe1L0jsEf6E4hK76xJO4tFfLHFl2hjg07zfpFJuSaRcjTsPHD+TpBL306Lf9l8CZpi4hlQ=OddJcvLnvgVl1Zrj",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "ads_management",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "instagram_basic",
                                                  "instagram_manage_comments",
                                                  "leads_retrieval",
                                                  "whatsapp_business_management",
                                                  "instagram_manage_messages",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "ads_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_basic",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_manage_comments",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "leads_retrieval",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "instagram_manage_messages",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122124012134119638",
                                              "created_at": "2024-04-15T09:22:51.618Z"
                                          },
                                          {
                                              "encrypted_token": "W1Ah09XayX6A/nha4KM86CYE5087UTwSQSN6LcEG2odDfHreq15M1IfKjrWc63M9uNEt8o0YD1cZgtH3dn4B3/ODZ/N8fIuuuFWKR8fn9z/5rpEybwPnb1XaqZUsPmybpXlFwydWTGeyhdLvRHUq6vgEVU7QXWmOndkRJee1Yl8dMKq3iZt/Q+6BxXSBLCjMN1wbruyz4TiFOtZO48WF5SD8XMn4KuPQGnREXmRm+PJ+YL/zMMp7ZB01Vx3i3YfTFnh5soccy9bjUA2GL0ulDG8NtAbI1zrsEUrZw4gO1IKdo5nLLNVO2E97QUTfiqzvo1wfIkjStxHV4QTTtJKNncXbZ8bzHCT5NCdAByowTP4=sO5kS8MOavbDeEAd",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "ads_management",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "instagram_basic",
                                                  "instagram_manage_comments",
                                                  "leads_retrieval",
                                                  "whatsapp_business_management",
                                                  "instagram_manage_messages",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "ads_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_basic",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_manage_comments",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "leads_retrieval",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "instagram_manage_messages",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122124012134119638",
                                              "created_at": "2024-04-15T06:19:29.982Z"
                                          }
                                      ],
                                      "facebook_waba_business_detail_snapshot": {
                                          "id": "****************",
                                          "block_offline_analytics": true,
                                          "timezone_id": 0,
                                          "name": "sfv2testing",
                                          "profile_picture_uri": "https://scontent-hkg4-2.xx.fbcdn.net/v/t39.30808-1/442501518_122043044824997259_1021025782936074954_n.png?stp=dst-png_s100x100&_nc_cat=111&ccb=1-7&_nc_sid=727b99&_nc_ohc=xQF6iPDpkcgQ7kNvgGG5Dvz&_nc_ht=scontent-hkg4-2.xx&edm=AHdzj10EAAAA&_nc_gid=A_gLeCbaYwhTZIVteOkfK_m&oh=00_AYCSQO8sr_sH6Zrin-pNYPre6AaXbnr2PyOQ--ckOOyvOg&oe=66F6B309",
                                          "vertical": "NOT_SET",
                                          "verification_status": "pending_submission",
                                          "link": "https://business.facebook.com/business/****************",
                                          "is_hidden": false
                                      },
                                      "facebook_waba_business_account_snapshot": {
                                          "id": "***************",
                                          "name": "SleekFlow App WABA",
                                          "account_review_status": "APPROVED",
                                          "on_behalf_of_business_info": {
                                              "name": "sfv2testing",
                                              "id": "****************",
                                              "status": "APPROVED",
                                              "type": "SELF"
                                          },
                                          "message_template_namespace": "e4450b53_d977_49da_b1a3_0d41bf527e66",
                                          "timezone_id": "42",
                                          "primary_funding_id": "****************",
                                          "currency": "USD",
                                          "purchase_order_number": null
                                      },
                                      "waba_phone_numbers": [
                                          {
                                              "facebook_phone_number_id": "***************",
                                              "facebook_phone_number_detail": {
                                                  "id": "***************",
                                                  "quality_rating": "GREEN",
                                                  "new_certificate": null,
                                                  "name_status": "AVAILABLE_WITHOUT_REVIEW",
                                                  "new_name_status": "NONE",
                                                  "account_mode": "LIVE",
                                                  "certificate": null,
                                                  "code_verification_status": "EXPIRED",
                                                  "display_phone_number": "******-406-9890",
                                                  "verified_name": "Stable Phone Number",
                                                  "is_pin_enabled": false,
                                                  "is_official_business_account": false,
                                                  "messaging_limit_tier": "TIER_250",
                                                  "quality_score": {
                                                      "date": null,
                                                      "reasons": null,
                                                      "score": "GREEN"
                                                  },
                                                  "status": "CONNECTED",
                                                  "whatsapp_commerce_settings": {
                                                      "data": [
                                                          {
                                                              "id": "***************",
                                                              "is_catalog_visible": true,
                                                              "is_cart_enabled": true
                                                          }
                                                      ]
                                                  }
                                              },
                                              "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                              "webhook_url": "https://sleekflow-core-app-eas-dev.azurewebsites.net/whatsapp/cloudapi/webhook/b6d7e442-38ae-4b9a-b100-2951729768bc/YxiVJZ4Z2v6zb2?verify_code=653803478cea4ae7bed7153f94903c329691c4d3dc98222e92b20fa20a95a262",
                                              "record_status": "active",
                                              "whatsapp_commerce_setting": {
                                                  "facebook_whatsapp_commerce_setting_id": "***************",
                                                  "is_catalog_visible": true,
                                                  "is_cart_enabled": true
                                              },
                                              "created_by": {
                                                  "sleekflow_staff_id": "827",
                                                  "sleekflow_staff_team_ids": null
                                              },
                                              "updated_by": null,
                                              "created_at": "2023-11-14T04:34:36.490Z",
                                              "updated_at": "2024-09-23T02:44:37.417Z",
                                              "id": "YxiVJZ4Z2v6zb2",
                                              "sys_type_name": "WabaPhoneNumber"
                                          },
                                          {
                                              "facebook_phone_number_id": "***************",
                                              "facebook_phone_number_detail": {
                                                  "id": "***************",
                                                  "quality_rating": "GREEN",
                                                  "new_certificate": null,
                                                  "name_status": "AVAILABLE_WITHOUT_REVIEW",
                                                  "new_name_status": "NONE",
                                                  "account_mode": "LIVE",
                                                  "certificate": "CmMKHwilqP2nlsv6AxIGZW50OndhIgZOZXcgRVNQ1vT4qwYaQM7w3FHgMqMhRoEQ8PbUFSOr8Z2NfI8zL5Wo92OvyTEXn6YSklDqQuOCKTaCZKwBgBzRwh38FfQZYmXENrVx7Q8SLW06M+mhrOIU4EOPsJmlbi6cX+bnW+0W6b5nh2iN3wq2m+cYdSyUPLETeLmvGg==",
                                                  "code_verification_status": "VERIFIED",
                                                  "display_phone_number": "******-379-5227",
                                                  "verified_name": "New ES",
                                                  "is_pin_enabled": true,
                                                  "is_official_business_account": false,
                                                  "messaging_limit_tier": "TIER_250",
                                                  "quality_score": {
                                                      "date": null,
                                                      "reasons": null,
                                                      "score": "GREEN"
                                                  },
                                                  "status": "CONNECTED",
                                                  "whatsapp_commerce_settings": null
                                              },
                                              "sleekflow_company_id": null,
                                              "webhook_url": null,
                                              "record_status": "in_active",
                                              "whatsapp_commerce_setting": null,
                                              "created_by": {
                                                  "sleekflow_staff_id": "827",
                                                  "sleekflow_staff_team_ids": null
                                              },
                                              "updated_by": null,
                                              "created_at": "2023-12-04T03:41:38.892Z",
                                              "updated_at": "2024-09-23T02:44:37.417Z",
                                              "id": "ZaiQP7K1qM24k2",
                                              "sys_type_name": "WabaPhoneNumber"
                                          }
                                      ],
                                      "messaging_function_limitation": null,
                                      "waba_active_product_catalog": null,
                                      "waba_product_catalog": {
                                          "facebook_product_catalog_id": "282558374777594",
                                          "facebook_product_catalog_name": "SF testing for facebook login",
                                          "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                          "default_image_url": "",
                                          "product_count": 2,
                                          "vertical": "commerce",
                                          "status": "active",
                                          "created_by": {
                                              "sleekflow_staff_id": "2f9840b7-28e2-4c30-b5f4-8faba152de43",
                                              "sleekflow_staff_team_ids": null
                                          },
                                          "updated_by": {
                                              "sleekflow_staff_id": "b31e3a4d-1e2e-4853-9385-fdf06668194e",
                                              "sleekflow_staff_team_ids": null
                                          },
                                          "created_at": "2024-01-26T05:14:47.128Z",
                                          "updated_at": "2024-09-23T05:38:47.300Z",
                                          "id": "ElYC1AP0bevDZYy2",
                                          "sys_type_name": "WabaProductCatalog"
                                      },
                                      "created_by": {
                                          "sleekflow_staff_id": "827",
                                          "sleekflow_staff_team_ids": null
                                      },
                                      "updated_by": {
                                          "sleekflow_staff_id": "827",
                                          "sleekflow_staff_team_ids": null
                                      },
                                      "created_at": "2023-11-14T04:34:36.490Z",
                                      "updated_at": "2024-09-23T05:38:47.300Z",
                                      "_etag": "\"550050be-0000-1900-0000-66f0fee70000\"",
                                      "id": "n2UmK4v4bk8pde1",
                                      "sys_type_name": "Waba",
                                      "_rid": "x18TAKNRhw2EAAAAAAAAAA==",
                                      "_self": "dbs/x18TAA==/colls/x18TAKNRhw0=/docs/x18TAKNRhw2EAAAAAAAAAA==/",
                                      "_attachments": "attachments/",
                                      "_ts": 1727069927
                                  }
                                  """;
        var waba = JsonConvert.DeserializeObject<Waba>(wabaString)!;
        const string facebookWabaId = "166367663226917";
        var facebookBusinessId = businessBalance.FacebookBusinessId;
        businessBalance = await _businessBalanceRepository.UpsertAndGetAsync(businessBalance, facebookBusinessId);
        waba = await _wabaRepository.UpsertAndGetAsync(waba, facebookWabaId);
        var id = businessBalance.Id;
        var wabaId = waba.Id;


        #endregion

        await _service.SwitchFromBusinessLevelToWabaLevelCreditManagementAsync(
            businessBalance.FacebookBusinessId,
            businessBalance.ETag!,
            new CreditAllocationObject(
                [
                    new CreditTransferFromTo(
                        new CreditTransferTargetObject(businessBalance.FacebookBusinessId, null, "facebook_business"),
                        new CreditTransferTargetObject(null, facebookWabaId, "facebook_waba"),
                        new Money("USD", 10),
                        "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA")
                ]),
            CompanyId,
            StaffId,
            new List<string>());

        Assert.ThrowsAsync(Is.TypeOf<SfConcurrentETagException>(), Code);
        Assert.That(await _businessBalanceRepository.DeleteAsync(id, facebookBusinessId), Is.EqualTo(1));
        Assert.That(await _wabaRepository.DeleteAsync(wabaId, facebookWabaId), Is.EqualTo(1));

        return;

        async Task Code() =>
            await _service.SwitchFromBusinessLevelToWabaLevelCreditManagementAsync(
                businessBalance.FacebookBusinessId, businessBalance.ETag!,
                new CreditAllocationObject(
                [
                    new CreditTransferFromTo(
                        new CreditTransferTargetObject(businessBalance.FacebookBusinessId, null, "facebook_business"),
                        new CreditTransferTargetObject(null, facebookWabaId, "facebook_waba"),
                        new Money("USD", 5),
                        "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA")
                ]),
                CompanyId,
                StaffId,
                new List<string>());
    }

    [Test]
    public async Task Test_2_Two_Users_Switch_To_Business_Level()
    {
        #region set up data
        const string facebookWabaId = "166367663226918";
        var businessBalanceString = """
                            {
                                "facebook_business_id": "****************",
                                "credit": {
                                    "currency_iso_code": "USD",
                                    "amount": 120
                                },
                                "used": {
                                    "currency_iso_code": "USD",
                                    "amount": 19.153799999999997
                                },
                                "markup": {
                                    "currency_iso_code": "USD",
                                    "amount": 2.298456
                                },
                                "transaction_handling_fee": {
                                    "currency_iso_code": "USD",
                                    "amount": 0.574614
                                },
                                "markup_profile": {
                                    "per_paid_business_initiated_conversation_fee_markup": {
                                        "currency_iso_code": "USD",
                                        "amount": 0
                                    },
                                    "per_paid_user_initiated_conversation_fee_markup": {
                                        "currency_iso_code": "USD",
                                        "amount": 0
                                    },
                                    "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                    "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                    "transaction_handling_fee_rate": 0.03
                                },
                                "conversation_usage_insert_state": {
                                    "last_conversation_usage_insert_timestamp": 1727074800,
                                    "waba_conversation_insertion_exception": {},
                                    "updated_at": "2024-09-23T07:05:02.423Z"
                                },
                                "record_statuses": [],
                                "created_at": "2023-11-14T04:34:40.391Z",
                                "updated_at": "2024-09-23T07:05:02.507Z",
                                "_etag": "\"550006ca-0000-1900-0000-66f1131e0100\"",
                                "id": "aJiDAGEzy5aeNe2",
                                "sys_type_name": "BusinessBalance",
                                "_rid": "x18TAKoTxS2nAAAAAAAAAA==",
                                "_self": "dbs/x18TAA==/colls/x18TAKoTxS0=/docs/x18TAKoTxS2nAAAAAAAAAA==/",
                                "_attachments": "attachments/",
                                "_ts": 1727075102
                            }
                            """;
        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString)!;

        const string wabaString = """
                                  {
                                      "facebook_waba_id": "166367663226918",
                                      "facebook_waba_business_id": "****************",
                                      "facebook_business_id": "****************",
                                      "sleekflow_company_ids": [
                                          "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                          "81ab95d5-bb44-4678-9765-f63640a305d8"
                                      ],
                                      "facebook_waba_name": "SleekFlow App WABA",
                                      "facebook_waba_account_review_status": "APPROVED",
                                      "facebook_waba_business_name": "sfv2testing",
                                      "facebook_waba_primary_funding_id": "****************",
                                      "facebook_waba_message_template_namespace": "e4450b53_d977_49da_b1a3_0d41bf527e66",
                                      "facebook_waba_long_lived_access_token": {
                                          "encrypted_token": "n8XQZJqH0Md/n1QZT04Xo1WvdJdGTJD1NKIIvrwOApt+ofYQPCUeEkuQHYlBkB/x8ZDImtvxr3AJoGUZfa8zi7IkDkkG3m8tinXjTTaiNm4B6AHIyER9kSa54B8ALooIyJV6ZljbHGpOfZtM4HRRFqbEsSqhvE+Ffboi1DVK9Dr9U0AC9kJXtf5jP3lojkCcTBeqEC/wm+WxPOyrHaCcMuNfl3E0NCX/iaC171GoJMZMoiH3rj9YZXwDAZ1lyeVgJTiMWSgSQ1mNWbIE",
                                          "token_type": "bearer",
                                          "expiry_datetime": null,
                                          "is_valid": true
                                      },
                                      "facebook_business_integration_system_user_access_tokens": [
                                          {
                                              "encrypted_token": "laTjYelMgUHHg1285gENv+bXbB+6XuXFOfEhwskFGaEB9UgD1DUj/bNKpuAyPe8uYjvXvPn+r4lK8ebeLdVfGz32IZMj6tTrPA4z0VfUZXbsVNzzNn6Xi0AFn+gJhFdf4xlSkHsKzm+KR+BrP2uUOZ4+P7zj1WVG3+AzDfXdFPeBoE4uJJfsa53aWXwGBN4IBxmcaPHmvqix4buj3NqphK3nPbnidwTdE6h9g6J0qzhoTUSvQFm7BFyhiwRm3x+WbJ6LexezKtFG1ZseV/ZVaJwMjr72OPp7Bpn+BHGDf9QUnT2INDPwPOUmXIDnUwCQ+EW5u14ex4HpW3CDplwfPXnvtIay5SybRSyUszbj860=t2ICFwFtvG4bfv4H",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "whatsapp_business_management",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122094662954371790",
                                              "created_at": "2024-06-13T10:40:39.867Z"
                                          },
                                          {
                                              "encrypted_token": "tx1zQ2MD5TquPe5hh3G0UlK4yVjKx9v1JqQ+M9mgSq4JOO4nM5EtRzbG+wqU60Uxiey+DqrMf3qd+75jrvymR7Ts4v9HTsPtr+OYswUKbc2uIJVP1UFxDYcx8jHT96jQYiShOG898cm+kzIRPDmjdjO9ggSkqYnN7Mw912ZNzXPnsOOnHmEa7AlXqDXK6dgsW1FYfq8SeddzaELKRIJGmkCFapozg9oc9Tw35gDbGiCb44+jRJVbgdEu7b5A3ce94D805FtkwN1Qjvw+ifw4OFJR1G5aeoysi/0zlELkAuqr+yLWnX9768KZJROdHfWDvGae+3DARSNUfwtiZNG2MVh+S3Q6VAV2UdBBVSs1sTQ=ENY3h0ubcd7M965B",
                                              "scopes": [
                                                  "pages_show_list",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "whatsapp_business_management",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122094662954371790",
                                              "created_at": "2024-06-13T06:54:23.345Z"
                                          },
                                          {
                                              "encrypted_token": "iWzMTg9o5o7PYyTCyPNZLZ0+cq1weCoX1yop9aMtbwiBV0nTbc0QgeSrDmW/mPhf0q4R/NXu+cRm8F7nbYKwCP7Fz5xFizM1eYuTwSi/FaOu31SHM6IDRLN+M1CUqA6V9sb4S1ayxxa61XKyheQsOLKxtcYrH+dxFomUaKtVddPvPvIJX42s7hV+h4m2hs7qz+7ujLL7/GNQuJ6aS0W92h5I7HUCqlwJ/GHawr4dyERkk+e8r+07LAFvtkM4H1MjNFgXtZlqsBShjfxUzmcnzHH8/JYIXUmZMBlyxHNc/9KgxhUwMyYm4Q+f4po2ai+GtJ5lZkNfk+URMmqxOsyxsP0mxJDA4AgzMH68mc6XXqY=0kgTcpKICd7d=RC0",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "ads_management",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "instagram_basic",
                                                  "instagram_manage_comments",
                                                  "leads_retrieval",
                                                  "whatsapp_business_management",
                                                  "instagram_manage_messages",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "ads_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_basic",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_manage_comments",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "leads_retrieval",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "instagram_manage_messages",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122124012134119638",
                                              "created_at": "2024-04-24T02:40:08.643Z"
                                          },
                                          {
                                              "encrypted_token": "4cZ7ZP4Zfsjo6Ba909FT0O9PVGxeUekWluphlJ3weOYg3zjpifXM5ETZiPPu5U5ENNSt32pcacQ1fzb0JdveKZ62SoCibUPKyQtWgU0tZEjO/qR0dL2TuQrMyBcKR5WiWeyAUQL+2uPOecWSQX5flt6bQ7iG89KSMzqwiqReIMoxSFTSkr7opAjCcbY1/UjERAjXbhmWwCIldrrLu6pcQ5sy4RTOqvxXIlqYwq5xEpa5pg/1femU96goQIMfeRRNkbLrO95q5Yzyx5mxg6vEMzmORLF3idWwxX+5uRe1L0jsEf6E4hK76xJO4tFfLHFl2hjg07zfpFJuSaRcjTsPHD+TpBL306Lf9l8CZpi4hlQ=OddJcvLnvgVl1Zrj",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "ads_management",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "instagram_basic",
                                                  "instagram_manage_comments",
                                                  "leads_retrieval",
                                                  "whatsapp_business_management",
                                                  "instagram_manage_messages",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "ads_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_basic",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_manage_comments",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "leads_retrieval",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "instagram_manage_messages",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122124012134119638",
                                              "created_at": "2024-04-15T09:22:51.618Z"
                                          },
                                          {
                                              "encrypted_token": "W1Ah09XayX6A/nha4KM86CYE5087UTwSQSN6LcEG2odDfHreq15M1IfKjrWc63M9uNEt8o0YD1cZgtH3dn4B3/ODZ/N8fIuuuFWKR8fn9z/5rpEybwPnb1XaqZUsPmybpXlFwydWTGeyhdLvRHUq6vgEVU7QXWmOndkRJee1Yl8dMKq3iZt/Q+6BxXSBLCjMN1wbruyz4TiFOtZO48WF5SD8XMn4KuPQGnREXmRm+PJ+YL/zMMp7ZB01Vx3i3YfTFnh5soccy9bjUA2GL0ulDG8NtAbI1zrsEUrZw4gO1IKdo5nLLNVO2E97QUTfiqzvo1wfIkjStxHV4QTTtJKNncXbZ8bzHCT5NCdAByowTP4=sO5kS8MOavbDeEAd",
                                              "scopes": [
                                                  "catalog_management",
                                                  "pages_show_list",
                                                  "ads_management",
                                                  "business_management",
                                                  "pages_messaging",
                                                  "instagram_basic",
                                                  "instagram_manage_comments",
                                                  "leads_retrieval",
                                                  "whatsapp_business_management",
                                                  "instagram_manage_messages",
                                                  "pages_read_engagement",
                                                  "pages_manage_metadata",
                                                  "pages_read_user_content",
                                                  "pages_manage_ads",
                                                  "pages_manage_posts",
                                                  "pages_manage_engagement",
                                                  "whatsapp_business_messaging",
                                                  "public_profile"
                                              ],
                                              "granular_scopes": [
                                                  {
                                                      "scope": "pages_show_list",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "ads_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "business_management",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_messaging",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_basic",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "instagram_manage_comments",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "leads_retrieval",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_management",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  },
                                                  {
                                                      "scope": "instagram_manage_messages",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_metadata",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_read_user_content",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_ads",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_posts",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "pages_manage_engagement",
                                                      "target_ids": null
                                                  },
                                                  {
                                                      "scope": "whatsapp_business_messaging",
                                                      "target_ids": [
                                                          "***************",
                                                          "169233172937026"
                                                      ]
                                                  }
                                              ],
                                              "facebook_app_id": "812364635796464",
                                              "facebook_application": "Sleekflow",
                                              "facebook_business_system_user_id": "122124012134119638",
                                              "created_at": "2024-04-15T06:19:29.982Z"
                                          }
                                      ],
                                      "facebook_waba_business_detail_snapshot": {
                                          "id": "****************",
                                          "block_offline_analytics": true,
                                          "timezone_id": 0,
                                          "name": "sfv2testing",
                                          "profile_picture_uri": "https://scontent-hkg4-2.xx.fbcdn.net/v/t39.30808-1/442501518_122043044824997259_1021025782936074954_n.png?stp=dst-png_s100x100&_nc_cat=111&ccb=1-7&_nc_sid=727b99&_nc_ohc=xQF6iPDpkcgQ7kNvgGG5Dvz&_nc_ht=scontent-hkg4-2.xx&edm=AHdzj10EAAAA&_nc_gid=A_gLeCbaYwhTZIVteOkfK_m&oh=00_AYCSQO8sr_sH6Zrin-pNYPre6AaXbnr2PyOQ--ckOOyvOg&oe=66F6B309",
                                          "vertical": "NOT_SET",
                                          "verification_status": "pending_submission",
                                          "link": "https://business.facebook.com/business/****************",
                                          "is_hidden": false
                                      },
                                      "facebook_waba_business_account_snapshot": {
                                          "id": "***************",
                                          "name": "SleekFlow App WABA",
                                          "account_review_status": "APPROVED",
                                          "on_behalf_of_business_info": {
                                              "name": "sfv2testing",
                                              "id": "****************",
                                              "status": "APPROVED",
                                              "type": "SELF"
                                          },
                                          "message_template_namespace": "e4450b53_d977_49da_b1a3_0d41bf527e66",
                                          "timezone_id": "42",
                                          "primary_funding_id": "****************",
                                          "currency": "USD",
                                          "purchase_order_number": null
                                      },
                                      "waba_phone_numbers": [
                                          {
                                              "facebook_phone_number_id": "***************",
                                              "facebook_phone_number_detail": {
                                                  "id": "***************",
                                                  "quality_rating": "GREEN",
                                                  "new_certificate": null,
                                                  "name_status": "AVAILABLE_WITHOUT_REVIEW",
                                                  "new_name_status": "NONE",
                                                  "account_mode": "LIVE",
                                                  "certificate": null,
                                                  "code_verification_status": "EXPIRED",
                                                  "display_phone_number": "******-406-9890",
                                                  "verified_name": "Stable Phone Number",
                                                  "is_pin_enabled": false,
                                                  "is_official_business_account": false,
                                                  "messaging_limit_tier": "TIER_250",
                                                  "quality_score": {
                                                      "date": null,
                                                      "reasons": null,
                                                      "score": "GREEN"
                                                  },
                                                  "status": "CONNECTED",
                                                  "whatsapp_commerce_settings": {
                                                      "data": [
                                                          {
                                                              "id": "***************",
                                                              "is_catalog_visible": true,
                                                              "is_cart_enabled": true
                                                          }
                                                      ]
                                                  }
                                              },
                                              "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                              "webhook_url": "https://sleekflow-core-app-eas-dev.azurewebsites.net/whatsapp/cloudapi/webhook/b6d7e442-38ae-4b9a-b100-2951729768bc/YxiVJZ4Z2v6zb2?verify_code=653803478cea4ae7bed7153f94903c329691c4d3dc98222e92b20fa20a95a262",
                                              "record_status": "active",
                                              "whatsapp_commerce_setting": {
                                                  "facebook_whatsapp_commerce_setting_id": "***************",
                                                  "is_catalog_visible": true,
                                                  "is_cart_enabled": true
                                              },
                                              "created_by": {
                                                  "sleekflow_staff_id": "827",
                                                  "sleekflow_staff_team_ids": null
                                              },
                                              "updated_by": null,
                                              "created_at": "2023-11-14T04:34:36.490Z",
                                              "updated_at": "2024-09-23T02:44:37.417Z",
                                              "id": "YxiVJZ4Z2v6zb2",
                                              "sys_type_name": "WabaPhoneNumber"
                                          },
                                          {
                                              "facebook_phone_number_id": "***************",
                                              "facebook_phone_number_detail": {
                                                  "id": "***************",
                                                  "quality_rating": "GREEN",
                                                  "new_certificate": null,
                                                  "name_status": "AVAILABLE_WITHOUT_REVIEW",
                                                  "new_name_status": "NONE",
                                                  "account_mode": "LIVE",
                                                  "certificate": "CmMKHwilqP2nlsv6AxIGZW50OndhIgZOZXcgRVNQ1vT4qwYaQM7w3FHgMqMhRoEQ8PbUFSOr8Z2NfI8zL5Wo92OvyTEXn6YSklDqQuOCKTaCZKwBgBzRwh38FfQZYmXENrVx7Q8SLW06M+mhrOIU4EOPsJmlbi6cX+bnW+0W6b5nh2iN3wq2m+cYdSyUPLETeLmvGg==",
                                                  "code_verification_status": "VERIFIED",
                                                  "display_phone_number": "******-379-5227",
                                                  "verified_name": "New ES",
                                                  "is_pin_enabled": true,
                                                  "is_official_business_account": false,
                                                  "messaging_limit_tier": "TIER_250",
                                                  "quality_score": {
                                                      "date": null,
                                                      "reasons": null,
                                                      "score": "GREEN"
                                                  },
                                                  "status": "CONNECTED",
                                                  "whatsapp_commerce_settings": null
                                              },
                                              "sleekflow_company_id": null,
                                              "webhook_url": null,
                                              "record_status": "in_active",
                                              "whatsapp_commerce_setting": null,
                                              "created_by": {
                                                  "sleekflow_staff_id": "827",
                                                  "sleekflow_staff_team_ids": null
                                              },
                                              "updated_by": null,
                                              "created_at": "2023-12-04T03:41:38.892Z",
                                              "updated_at": "2024-09-23T02:44:37.417Z",
                                              "id": "ZaiQP7K1qM24k2",
                                              "sys_type_name": "WabaPhoneNumber"
                                          }
                                      ],
                                      "messaging_function_limitation": null,
                                      "waba_active_product_catalog": null,
                                      "waba_product_catalog": {
                                          "facebook_product_catalog_id": "282558374777594",
                                          "facebook_product_catalog_name": "SF testing for facebook login",
                                          "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                          "default_image_url": "",
                                          "product_count": 2,
                                          "vertical": "commerce",
                                          "status": "active",
                                          "created_by": {
                                              "sleekflow_staff_id": "2f9840b7-28e2-4c30-b5f4-8faba152de43",
                                              "sleekflow_staff_team_ids": null
                                          },
                                          "updated_by": {
                                              "sleekflow_staff_id": "b31e3a4d-1e2e-4853-9385-fdf06668194e",
                                              "sleekflow_staff_team_ids": null
                                          },
                                          "created_at": "2024-01-26T05:14:47.128Z",
                                          "updated_at": "2024-09-23T05:38:47.300Z",
                                          "id": "ElYC1AP0bevDZYy2",
                                          "sys_type_name": "WabaProductCatalog"
                                      },
                                      "created_by": {
                                          "sleekflow_staff_id": "827",
                                          "sleekflow_staff_team_ids": null
                                      },
                                      "updated_by": {
                                          "sleekflow_staff_id": "827",
                                          "sleekflow_staff_team_ids": null
                                      },
                                      "created_at": "2023-11-14T04:34:36.490Z",
                                      "updated_at": "2024-09-23T05:38:47.300Z",
                                      "_etag": "\"550050be-0000-1900-0000-66f0fee70000\"",
                                      "id": "n2UmK4v4bk8pde2",
                                      "sys_type_name": "Waba",
                                      "_rid": "x18TAKNRhw2EAAAAAAAAAA==",
                                      "_self": "dbs/x18TAA==/colls/x18TAKNRhw0=/docs/x18TAKNRhw2EAAAAAAAAAA==/",
                                      "_attachments": "attachments/",
                                      "_ts": 1727069927
                                  }
                                  """;
        var waba = JsonConvert.DeserializeObject<Waba>(wabaString)!;

        var facebookBusinessId = businessBalance.FacebookBusinessId;
        businessBalance = await _businessBalanceRepository.UpsertAndGetAsync(businessBalance, facebookBusinessId);
        waba = await _wabaRepository.UpsertAndGetAsync(waba, facebookWabaId);
        var id = businessBalance.Id;
        var wabaId = waba.Id;
        #endregion

        await _service.SwitchFromBusinessLevelToWabaLevelCreditManagementAsync(
            businessBalance.FacebookBusinessId,
            businessBalance.ETag!,
            new CreditAllocationObject(
            [
                new CreditTransferFromTo(
                    new CreditTransferTargetObject(businessBalance.FacebookBusinessId, null, "facebook_business"),
                    new CreditTransferTargetObject(null, facebookWabaId, "facebook_waba"),
                    new Money("USD", 10),
                    "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA")
            ]),
            CompanyId,
            StaffId,
            new List<string>());

        Thread.Sleep(1000);
        businessBalance = await _businessBalanceRepository.GetAsync(businessBalance.Id, facebookBusinessId);
        await _service.SwitchFromWabaLevelToBusinessLevelCreditManagementAsync(
            businessBalance.FacebookBusinessId,
            businessBalance.ETag!,
            CompanyId,
            StaffId,
            new List<string>());

        Assert.ThrowsAsync(Is.TypeOf<SfConcurrentETagException>(), Code);
        Assert.That(await _businessBalanceRepository.DeleteAsync(id, facebookBusinessId), Is.EqualTo(1));
        Assert.That(await _wabaRepository.DeleteAsync(wabaId, facebookWabaId), Is.EqualTo(1));
        return;

        async Task Code() =>
            await _service.SwitchFromWabaLevelToBusinessLevelCreditManagementAsync(
                businessBalance.FacebookBusinessId,
                businessBalance.ETag!,
                CompanyId,
                StaffId,
                new List<string>());
    }

    [Test]
    public async Task Test_3_Init_Waba_Balance_With_Previous_Logs()
    {
        var localBusinessBalanceTransactionLogService = Mock.Of<IBusinessBalanceTransactionLogService>(
            s => s.GetResynchronizationBusinessTransactionLogAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<long>(),
                It.IsAny<long>()) == Task.FromResult(new List<BusinessBalanceTransactionLog>()
            {
                new (
                    _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                    "facebookWabaId",
                    "facebookBusinessId",
                    "uniqueId",
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, 5),
                    new Money(CurrencyIsoCodes.USD, 0.5m),
                    new Money(CurrencyIsoCodes.USD, 0.05m),
                    TransactionTypes.CreditTransfer,
                    default,
                    default,
                    default,
                    default,
                    default,
                    default,
                    DateTimeOffset.Now,
                    DateTimeOffset.Now,
                    "success"),
                new (
                    _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                    "facebookWabaId",
                    "facebookBusinessId",
                    "uniqueId",
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, 5),
                    new Money(CurrencyIsoCodes.USD, 0.5m),
                    new Money(CurrencyIsoCodes.USD, 0.05m),
                    TransactionTypes.CreditTransfer,
                    default,
                    default,
                    default,
                    default,
                    default,
                    default,
                    DateTimeOffset.Now,
                    DateTimeOffset.Now,
                    "success"),
                new (
                    _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                    "facebookWabaId",
                    "facebookBusinessId",
                    "uniqueId",
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, -1),
                    new Money(CurrencyIsoCodes.USD, -0.1m),
                    new Money(CurrencyIsoCodes.USD, -0.01m),
                    TransactionTypes.CreditTransfer,
                    default,
                    default,
                    default,
                    default,
                    default,
                    default,
                    DateTimeOffset.Now,
                    DateTimeOffset.Now,
                    "success")
            }));

        _service = new WabaLevelCreditManagementService(
            _idService,
            _auditLogService.Object,
            _logger.Object,
            new BusinessBalanceService(
                _idService,
                _auditLogService.Object,
                new Mock<ILogger<BusinessBalanceService>>().Object,
                _businessBalanceRepository,
                _wabaRepository),
            _wabaService,
            _businessBalanceTransactionLogRepository,
            _bus,
            localBusinessBalanceTransactionLogService,
            _whatsappCloudApiBusinessBalanceWebhookService);

        var balance = await _service.InitWabaBalancesInBusinessBalanceAsync(
            ["test_waba_id_1"],
            new BusinessBalance(
                "test_id",
                "test_fb_id",
                null!,
                null!,
                null!,
                null!,
                DateTimeOffset.UtcNow.AddDays(-1),
                DateTimeOffset.UtcNow.AddDays(-1)));

        Assert.That(balance.WabaBalances, Is.Not.Null.And.Count.GreaterThanOrEqualTo(1));
        Assert.That(balance.WabaBalances.First().FacebookWabaId, Is.EqualTo("test_waba_id_1"));
        Assert.That(balance.WabaBalances.First().Credit.Amount, Is.EqualTo(9.99m));
        Assert.That(balance.WabaBalances.First().Credit.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().Used.Amount, Is.EqualTo(9m));
        Assert.That(balance.WabaBalances.First().Used.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().Markup.Amount, Is.EqualTo(0.9m));
        Assert.That(balance.WabaBalances.First().Markup.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().TransactionHandlingFee?.Amount, Is.Null.Or.EqualTo(0.09m));
        Assert.That(balance.WabaBalances.First().TransactionHandlingFee?.CurrencyIsoCode, Is.Null.Or.EqualTo("USD"));
    }

    [Test]
    public async Task Test_4_Init_Waba_Balance_With_Empty_Logs()
    {
        var localBusinessBalanceTransactionLogService = Mock.Of<IBusinessBalanceTransactionLogService>(
            s => s.GetResynchronizationBusinessTransactionLogAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<long>(),
                It.IsAny<long>()) == Task.FromResult(new List<BusinessBalanceTransactionLog>
            {
                Capacity = 0
            }));
        _service = new WabaLevelCreditManagementService(
            _idService,
            _auditLogService.Object,
            _logger.Object,
            new BusinessBalanceService(
                _idService,
                _auditLogService.Object,
                new Mock<ILogger<BusinessBalanceService>>().Object,
                _businessBalanceRepository,
                _wabaRepository),
            _wabaService,
            _businessBalanceTransactionLogRepository,
            _bus,
            localBusinessBalanceTransactionLogService,
            _whatsappCloudApiBusinessBalanceWebhookService);

        var balance = await _service.InitWabaBalancesInBusinessBalanceAsync(
           ["test_waba_id_1"],
           new BusinessBalance(
                "test_id",
                "test_fb_id",
                null!,
                null!,
                null!,
                null!,
                DateTimeOffset.UtcNow.AddDays(-1),
                DateTimeOffset.UtcNow.AddDays(-1)));

        Assert.That(balance.WabaBalances, Is.Not.Null.And.Count.GreaterThanOrEqualTo(1));
        Assert.That(balance.WabaBalances.First().FacebookWabaId, Is.EqualTo("test_waba_id_1"));
        Assert.That(balance.WabaBalances.First().Credit.Amount, Is.EqualTo(0));
        Assert.That(balance.WabaBalances.First().Credit.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().Balance.Amount, Is.EqualTo(0));
        Assert.That(balance.WabaBalances.First().Balance.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().Markup.Amount, Is.EqualTo(0));
        Assert.That(balance.WabaBalances.First().Markup.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().Used.Amount, Is.EqualTo(0));
        Assert.That(balance.WabaBalances.First().Used.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(balance.WabaBalances.First().TransactionHandlingFee?.Amount, Is.Null.Or.EqualTo(0));
        Assert.That(balance.WabaBalances.First().TransactionHandlingFee?.CurrencyIsoCode, Is.Null.Or.EqualTo("USD"));
    }

    [Test]
    public async Task Test_5_Init_Waba_Balance_With_Exiting_Waba()
    {
        var localBusinessBalanceTransactionLogService = Mock.Of<IBusinessBalanceTransactionLogService>(
            s => s.GetResynchronizationBusinessTransactionLogAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<long>(),
                It.IsAny<long>()) == Task.FromResult(new List<BusinessBalanceTransactionLog>
            {
                new (
                    _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                    "facebookWabaId",
                    "facebookBusinessId",
                    "uniqueId",
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, 5),
                    new Money(CurrencyIsoCodes.USD, 0.5m),
                    new Money(CurrencyIsoCodes.USD, 0.05m),
                    TransactionTypes.CreditTransfer,
                    default,
                    default,
                    default,
                    default,
                    default,
                    default,
                    DateTimeOffset.Now,
                    DateTimeOffset.Now,
                    "success"),
            }));

        _service = new WabaLevelCreditManagementService(
            _idService,
            _auditLogService.Object,
            _logger.Object,
            new BusinessBalanceService(
                _idService,
                _auditLogService.Object,
                new Mock<ILogger<BusinessBalanceService>>().Object,
                _businessBalanceRepository,
                _wabaRepository),
            _wabaService,
            _businessBalanceTransactionLogRepository,
            _bus,
            localBusinessBalanceTransactionLogService,
            _whatsappCloudApiBusinessBalanceWebhookService);

        var balance = await _service.InitWabaBalancesInBusinessBalanceAsync(
           ["test_waba_id_1", "test_unique_id_1"],
           new BusinessBalance(
                "test_id",
                "test_fb_id",
                null!,
                new List<WabaBalance>
                {
                    new ("waba_balance_id", "test_waba_id_1", default, default)
                },
                null!,
                null!,
                DateTimeOffset.UtcNow.AddDays(-1),
                DateTimeOffset.UtcNow.AddDays(-1)));

        Assert.That(balance.WabaBalances, Is.Not.Null.And.Count.GreaterThanOrEqualTo(2));

        var newWabaBalance = balance.WabaBalances.FirstOrDefault(x => x.FacebookWabaId == "test_unique_id_1");
        Assert.That(newWabaBalance, Is.Not.Null);
        Assert.That(newWabaBalance.Credit.Amount, Is.EqualTo(5.55m));
        Assert.That(newWabaBalance.Credit.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(newWabaBalance.Markup.Amount, Is.EqualTo(0.5m));
        Assert.That(newWabaBalance.Markup.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(newWabaBalance.Used.Amount, Is.EqualTo(5));
        Assert.That(newWabaBalance.Used.CurrencyIsoCode, Is.EqualTo("USD"));
        Assert.That(newWabaBalance.TransactionHandlingFee?.Amount, Is.Null.Or.EqualTo(0.05m));
        Assert.That(newWabaBalance.TransactionHandlingFee?.CurrencyIsoCode, Is.Null.Or.EqualTo("USD"));
    }
}