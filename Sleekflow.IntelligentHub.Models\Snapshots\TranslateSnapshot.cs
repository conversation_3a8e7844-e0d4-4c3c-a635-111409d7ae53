﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class TranslateSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("input_message")]
    public string InputMessage { get; set; }

    [JsonProperty("target_language_code")]
    public string TargetLanguageCode { get; set; }

    [JsonProperty( "output_message")]
    public string OutputMessage { get; set; }

    [JsonConstructor]
    public TranslateSnapshot(string inputMessage, string targetLanguageCode, string outputMessage)
    {
        InputMessage = inputMessage;
        TargetLanguageCode = targetLanguageCode;
        OutputMessage = outputMessage;
    }
}