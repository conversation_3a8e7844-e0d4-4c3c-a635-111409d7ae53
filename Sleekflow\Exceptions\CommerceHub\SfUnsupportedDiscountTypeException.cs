namespace Sleekflow.Exceptions.CommerceHub;

public class SfUnsupportedDiscountTypeException : ErrorCodeException
{
    public string AppliedDiscountType { get; }

    public SfUnsupportedDiscountTypeException(string appliedDiscountType)
        : base(ErrorCodeConstant.SfUnsupportedDiscountTypeException, "The discount type is not supported.")
    {
        AppliedDiscountType = appliedDiscountType;
    }
}