﻿using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowExecutionStartedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StateId { get; set; }

    public string? WorkflowExecutionReasonCode { get; set; }

    public StateIdentity StateIdentity { get; set; }

    public string? WorkflowType { get; set; }

    public AuditEntity.SleekflowStaff? StartedBy { get; set; }

    public DateTimeOffset StartedAt { get; set; } = DateTimeOffset.UtcNow;

    public string? SubWorkflowType { get; set; }

    public OnWorkflowExecutionStartedEvent(
        string sleekflowCompanyId,
        string stateId,
        string? workflowExecutionReasonCode,
        StateIdentity stateIdentity,
        string? workflowType,
        AuditEntity.SleekflowStaff? startedBy,
        string? subWorkflowType = null)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        WorkflowExecutionReasonCode = workflowExecutionReasonCode;
        StateIdentity = stateIdentity;
        WorkflowType = workflowType;
        StartedBy = startedBy;
        SubWorkflowType = subWorkflowType;
    }
}