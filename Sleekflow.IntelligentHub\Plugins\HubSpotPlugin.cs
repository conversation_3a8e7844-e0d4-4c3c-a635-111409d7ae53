using System.ComponentModel;
using System.Net.Http.Headers;
using System.Text;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;

namespace Sleekflow.IntelligentHub.Plugins;

public interface IHubspotPlugin
{
    [KernelFunction("get_contact_properties")]
    [Description(
        "Gets all available Hubspot contact properties for a contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        Description(
            "A JSON string containing all the contact's properties, or null if the contact is not found or an error occurs.")]
    Task<string?> GetContactPropertiesAsync(
        Kernel kernel,
        [Description("The email address of the contact")]
        string? email = null,
        [Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default);

    [KernelFunction("get_contact_meetings")]
    [Description(
        "Gets details of all meetings associated with a Hubspot contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        Description(
            "A JSON string representing a list of meeting objects. Returns null if the contact is not found, has no meetings, or an error occurs.")]
    Task<string?> GetContactMeetingsAsync(
        Kernel kernel,
        [Description("The email address of the contact")]
        string? email = null,
        [Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default);

    [KernelFunction("update_contact_properties")]
    [Description(
        "Updates properties of a Hubspot contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        Description(
            "A JSON string containing the updated contact's properties, or null if the contact is not found or an error occurs.")]
    Task<string?> UpdateContactPropertiesAsync(
        Kernel kernel,
        [Description("A list of HubspotProperty objects containing the properties to update")]
        List<HubspotProperty> properties,
        [Description("The email address of the contact")]
        string? email = null,
        [Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default);
}

public class HubspotPlugin : IHubspotPlugin, IScopedService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger _logger;
    private static readonly Uri HubspotBaseUri = new Uri("https://api.hubapi.com"); // Set Base API URL here

    public HubspotPlugin(
        HttpClient httpClient,
        ILoggerFactory? loggerFactory = null)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = loggerFactory?.CreateLogger(typeof(HubspotPlugin)) ??
                  NullLoggerFactory.Instance.CreateLogger(typeof(HubspotPlugin));
    }

    private bool TryConfigureRequest(
        Kernel kernel,
        HttpMethod method,
        string relativeUri,
        out HttpRequestMessage request)
    {
        request = new HttpRequestMessage();

        if (!kernel.Data.TryGetValue(KernelDataKeys.TOOLS_CONFIG, out var toolsConfigObj))
        {
            _logger.LogError("Kernel.Data does not contain 'TOOLS_CONFIG' entry.");
            return false;
        }

        string? apiKey;

        // Try casting to the expected interface or type
        if (toolsConfigObj is ToolsConfig { HubspotTool: not null } toolsConfig)
        {
            apiKey = toolsConfig.HubspotTool.ApiKey;
        }
        else
        {
            return false;
        }

        if (string.IsNullOrWhiteSpace(apiKey))
        {
            _logger.LogError(
                "Hubspot API key not found or empty in Kernel.Data[KernelDataKeys.HUBSPOT_PLUGIN_CONFIG].HubspotApiKey.");
            return false;
        }

        try
        {
            request.Method = method;
            request.RequestUri = new Uri(HubspotBaseUri, relativeUri); // Combine base and relative URI
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", apiKey); // Set Auth per request
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create or configure HttpRequestMessage.");
            request.Dispose(); // Dispose if created but failed during configuration
            request = null!; // Ensure request is null on failure exit
            return false;
        }
    }

    private async Task<string?> FindContactIdAsync(
        Kernel kernel,
        string? email,
        string? phone,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(email) && string.IsNullOrWhiteSpace(phone))
        {
            _logger.LogWarning("Both email and phone are null or empty. Cannot search for contact.");
            return null;
        }

        // Configure the request for searching contacts
        if (!TryConfigureRequest(kernel, HttpMethod.Post, "/crm/v3/objects/contacts/search", out var request))
        {
            return null;
        }

        try
        {
            // Create search filter based on available contact info (email or phone)
            var filterProperty = !string.IsNullOrWhiteSpace(email) ? "email" : "phone";
            var filterValue = !string.IsNullOrWhiteSpace(email) ? email : phone;

            var searchRequest = new
            {
                filterGroups = new[]
                {
                    new
                    {
                        filters = new[]
                        {
                            new
                            {
                                propertyName = filterProperty, @operator = "EQ", value = filterValue
                            }
                        }
                    }
                },
                properties = new[]
                {
                    "firstname",
                    "lastname",
                    "email",
                    "phone",
                    "hs_object_id"
                },
                limit = 1
            };

            // Serialize request to JSON
            var jsonContent = JsonConvert.SerializeObject(searchRequest);
            request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Send request
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            // Parse response
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var jsonResponse = JObject.Parse(responseContent);

            // Extract contact ID if available
            var results = jsonResponse["results"] as JArray;
            if (results != null && results.Count > 0)
            {
                return results[0]["id"]?.ToString();
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error searching for contact by {FilterType}: {FilterValue}",
                !string.IsNullOrWhiteSpace(email) ? "email" : "phone",
                !string.IsNullOrWhiteSpace(email) ? email : phone);
            return null;
        }
    }

    [KernelFunction("get_contact_properties")]
    [Description(
        "Gets all available Hubspot contact properties for a contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        Description(
            "A JSON string containing all the contact's properties, or null if the contact is not found or an error occurs.")]
    public async Task<string?> GetContactPropertiesAsync(
        Kernel kernel, // Kernel passed to access context/data
        [Description("The email address of the contact")]
        string? email = null,
        [Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default)
    {
        // First find the contact ID using the provided email or phone
        var contactId = await FindContactIdAsync(kernel, email, phone, cancellationToken);
        if (string.IsNullOrWhiteSpace(contactId))
        {
            _logger.LogWarning("Contact not found with email: {Email} or phone: {Phone}", email, phone);
            return null;
        }

        // Create request to get all contact properties
        if (!TryConfigureRequest(
                kernel,
                HttpMethod.Get,
                $"/crm/v3/objects/contacts/{contactId}?properties=firstname,lastname,email,phone,company,website,lifecyclestage,hubspot_owner_id&archived=false",
                out var request))
        {
            return null;
        }

        try
        {
            // Send request
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            // Return the JSON response as-is
            return await response.Content.ReadAsStringAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting contact properties for contactId: {ContactId}", contactId);
            return null;
        }
    }

    [KernelFunction("get_contact_meetings")]
    [Description(
        "Gets details of all meetings associated with a Hubspot contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        Description(
            "A JSON string representing a list of meeting objects. Returns null if the contact is not found, has no meetings, or an error occurs.")]
    public async Task<string?> GetContactMeetingsAsync(
        Kernel kernel, // Kernel passed to access context/data
        [Description("The email address of the contact")]
        string? email = null,
        [Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default)
    {
        // First find the contact ID using the provided email or phone
        var contactId = await FindContactIdAsync(kernel, email, phone, cancellationToken);
        if (string.IsNullOrWhiteSpace(contactId))
        {
            _logger.LogWarning("Contact not found with email: {Email} or phone: {Phone}", email, phone);
            return null; // Return empty array as specified in the function description
        }

        // Get meeting associations
        if (!TryConfigureRequest(
                kernel,
                HttpMethod.Get,
                $"/crm/v4/objects/contacts/{contactId}/associations/meetings",
                out var associationsRequest))
        {
            return null;
        }

        try
        {
            // Get meeting IDs
            var associationsResponse = await _httpClient.SendAsync(associationsRequest, cancellationToken);
            associationsResponse.EnsureSuccessStatusCode();

            var associationsContent = await associationsResponse.Content.ReadAsStringAsync(cancellationToken);
            var associationsJson = JObject.Parse(associationsContent);

            // Extract meeting IDs
            var meetingIds = new List<string>();
            var results = associationsJson["results"] as JArray;
            if (results == null || results.Count == 0)
            {
                return "[]"; // No meetings found
            }

            foreach (var result in results)
            {
                var meetingId = result["toObjectId"]?.ToString();
                if (!string.IsNullOrWhiteSpace(meetingId))
                {
                    meetingIds.Add(meetingId);
                }
            }

            // Get details for each meeting
            var meetings = new JArray();
            foreach (var meetingId in meetingIds)
            {
                if (!TryConfigureRequest(
                        kernel,
                        HttpMethod.Get,
                        $"/crm/v3/objects/meetings/{meetingId}?properties=hs_meeting_title,hs_meeting_body,hs_meeting_start_time,hs_meeting_end_time,hubspot_owner_id,hs_internal_meeting_notes",
                        out var meetingRequest))
                {
                    continue;
                }

                var meetingResponse = await _httpClient.SendAsync(meetingRequest, cancellationToken);
                if (meetingResponse.IsSuccessStatusCode)
                {
                    var meetingContent = await meetingResponse.Content.ReadAsStringAsync(cancellationToken);
                    var meetingJson = JObject.Parse(meetingContent);
                    meetings.Add(meetingJson);
                }
            }

            return meetings.Count == 0
                ? null
                : meetings.ToString(Formatting.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meetings for contactId: {ContactId}", contactId);
            return null;
        }
    }

    [KernelFunction("update_contact_properties")]
    [Description(
        "Updates properties of a Hubspot contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        Description(
            "A JSON string containing the updated contact's properties, or null if the contact is not found or an error occurs.")]
    public async Task<string?> UpdateContactPropertiesAsync(
        Kernel kernel,
        [Description("A list of HubspotProperty objects containing the properties to update")]
        List<HubspotProperty> properties,
        [Description("The email address of the contact")]
        string? email = null,
        [Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default)
    {
        if (properties == null || properties.Count == 0)
        {
            _logger.LogWarning("Properties JSON is null or empty. Cannot update contact.");
            return null;
        }

        // First find the contact ID using the provided email or phone
        var contactId = await FindContactIdAsync(kernel, email, phone, cancellationToken);
        if (string.IsNullOrWhiteSpace(contactId))
        {
            _logger.LogWarning("Contact not found with email: {Email} or phone: {Phone}", email, phone);
            return null;
        }

        // Configure the request for updating contact properties
        if (!TryConfigureRequest(
                kernel,
                HttpMethod.Patch,
                $"/crm/v3/objects/contacts/{contactId}",
                out var request))
        {
            return null;
        }

        try
        {
            // Parse the input properties JSON to validate it
            var propertiesObject = properties.ToDictionary(p => p.PropertyName, p => p.PropertyValue);

            // Create the update request payload
            var updateRequest = new
            {
                properties = propertiesObject
            };

            // Serialize request to JSON
            var jsonContent = JsonConvert.SerializeObject(updateRequest);
            request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Send request
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            // Return the JSON response containing the updated contact
            return await response.Content.ReadAsStringAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating contact properties for contactId: {ContactId}", contactId);
            return null;
        }
    }
}

public class HubspotProperty
{
    public string PropertyName { get; set; }

    public string PropertyValue { get; set; }

    [JsonConstructor]
    public HubspotProperty(string propertyName, string propertyValue)
    {
        PropertyName = propertyName;
        PropertyValue = propertyValue;
    }
}