using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class LanguagePluginTest
{
    private Kernel _kernel;
    private ILanguagePlugin _languagePlugin;

    [SetUp]
    public void SetUp()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _languagePlugin = scope.ServiceProvider.GetRequiredService<ILanguagePlugin>();
    }

    public class LanguageDetectionTestCase
    {
        public string InputText { get; set; } = string.Empty;

        public string ExpectedLanguageCode { get; set; } = string.Empty;

        public string ExpectedLanguageName { get; set; } = string.Empty;
    }

    private static IEnumerable<LanguageDetectionTestCase> LanguageDetectionTestCases
    {
        get
        {
            yield return new LanguageDetectionTestCase
            {
                InputText = "Hello, how are you?", ExpectedLanguageCode = "en", ExpectedLanguageName = "English"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "我在台灣生活",
                ExpectedLanguageCode = "zh-Hant-TW",
                ExpectedLanguageName = "Traditional Chinese - Taiwan"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "我喺香港住",
                ExpectedLanguageCode = "zh-Hant-HK",
                ExpectedLanguageName = "Traditional Chinese - Hong Kong"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "Bonjour, je m'appelle Pierre", ExpectedLanguageCode = "fr", ExpectedLanguageName = "French"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "こんにちは、私はジョンです", ExpectedLanguageCode = "ja", ExpectedLanguageName = "Japanese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "안녕하세요, 제 이름은 미나입니다", ExpectedLanguageCode = "ko", ExpectedLanguageName = "Korean"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "Hallo, mein Name ist Hans", ExpectedLanguageCode = "de", ExpectedLanguageName = "German"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "客戶以古文詢問美容療程",
                ExpectedLanguageCode = "zh-Hant",
                ExpectedLanguageName = "Traditional Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "¡Hola! ¿Cómo estás?", ExpectedLanguageCode = "es", ExpectedLanguageName = "Spanish"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "我哋嘅價錢係 $888.88",
                ExpectedLanguageCode = "zh-Hant-HK",
                ExpectedLanguageName = "Traditional Chinese - Hong Kong"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "Здравствуйте, как дела?", ExpectedLanguageCode = "ru", ExpectedLanguageName = "Russian"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "สวัสดีค่ะ สบายดีไหม", ExpectedLanguageCode = "th", ExpectedLanguageName = "Thai"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "你好，早上好", ExpectedLanguageCode = "zh-Hant", ExpectedLanguageName = "Traditional Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "今天天氣十分不錯", ExpectedLanguageCode = "zh-Hant", ExpectedLanguageName = "Traditional Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "我要 book 一個會議室",
                ExpectedLanguageCode = "zh-Hant-HK",
                ExpectedLanguageName = "Traditional Chinese - Hong Kong"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "您好 👋，請問有什麼可以幫您？ 😊",
                ExpectedLanguageCode = "zh-Hant",
                ExpectedLanguageName = "Traditional Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "下午好！☀️ 欢迎光临我们的商店 🏪",
                ExpectedLanguageCode = "zh-Hans",
                ExpectedLanguageName = "Simplified Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "感謝您的訂單 🛍️，預計30分鐘內送達 🚚",
                ExpectedLanguageCode = "zh-Hant",
                ExpectedLanguageName = "Traditional Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "唔該借畀我本 notes check 下個 presentation materials，我想 review 一次先去開會。",
                ExpectedLanguageCode = "zh-Hant-HK",
                ExpectedLanguageName = "Traditional Chinese - Hong Kong"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "产品编号 SKU-12345 的智能手机现已上市，搭载 Snapdragon 8 Gen 2 处理器，支持 5G 网络，售价 RMB 3999。",
                ExpectedLanguageCode = "zh-Hans",
                ExpectedLanguageName = "Simplified Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "本次優惠活動包含多款人氣商品：寿司セット、抹茶ラテ、和風料理，活動期間至本月底，歡迎把握機會！",
                ExpectedLanguageCode = "zh-Hant",
                ExpectedLanguageName = "Traditional Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText = "系统配置要求：Windows 10 或以上，RAM 8GB，需要安装 Python 3.8+，并确保 GPU driver 已更新到最新版本。",
                ExpectedLanguageCode = "zh-Hans",
                ExpectedLanguageName = "Simplified Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText =
                    "user: 你好，最近过得怎么样？\nbot: 最近还不错，你呢？\nuser: 生活挺忙的，每天都在工作。\nbot: 要记得适当休息啊，工作虽然重要但是健康更重要。\nuser: Can you reply in English?",
                ExpectedLanguageCode = "en",
                ExpectedLanguageName = "English"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText =
                    "user: 你好，最近过得怎么样？\nbot: 最近还不错，你呢？\nuser: 生活挺忙的，每天都在工作。\nbot: 要记得适当休息啊，工作虽然重要但是健康更重要。\nuser: Can you reply in English?\nbot: Sure! From now on I'll respond in English.\nuser: Let's switch back to Sim Chinese",
                ExpectedLanguageCode = "zh-Hans",
                ExpectedLanguageName = "Simplified Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText =
                    "user: 你好，我最近在学习一门新的编程语言\nbot: 很好啊！是在学什么编程语言呢？\nuser: 我在学Python，感觉还挺有趣的\nbot: Python确实是个不错的选择，特别适合初学者。你主要用它来做什么项目？\nuser: 主要是数据分析和机器学习相关的项目\nbot: 这些都是Python的强项，有遇到什么困难吗？\nuser: Can you help me with Python in English?\nbot: Of course! What specific aspects of Python would you like help with?\nuser: Let's discuss machine learning\nbot: Sure! Machine learning in Python typically starts with libraries like scikit-learn and TensorFlow. What's your experience level?\nuser: 还是用中文讨论吧，这样更容易理解\nbot: 好的，那我们继续用中文。机器学习确实需要循序渐进地学习，我们可以从基础概念开始聊起。",
                ExpectedLanguageCode = "zh-Hans",
                ExpectedLanguageName = "Simplified Chinese"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText =
                    "user: 最近香港嘅天氣點呀？\nbot: 依家天氣幾好，唔係咁熱。你最近去咗邊度行街？\nuser: 我上個週末去咗旺角行街\nbot: 旺角依家仲係咁多人嘅，你有冇買到啲咩嘢？\nuser: Can we continue in English?\nbot: Sure! How was your shopping experience in Mong Kok?\nuser: It was quite crowded but fun\nbot: Yes, Mong Kok is always bustling with energy! Did you try any street food?\nuser: 不如轉返中文啦\nbot: 好，我哋改返中文傾。你有冇食到街頭小食？\nuser: 係呀，買咗魚蛋同煎餅\nbot: 香港嘅街頭小食真係好正！你仲鍾意食咩架？\nuser: 雞蛋仔同格仔餅都幾好食\nbot: 冇錯！尤其係熱辣辣嘅雞蛋仔，香港特色小食來㗎！",
                ExpectedLanguageCode = "zh-Hant-HK",
                ExpectedLanguageName = "Traditional Chinese - Hong Kong"
            };

            yield return new LanguageDetectionTestCase
            {
                InputText =
                    "Customer: Hi HKBN, could you tell me about your products? Highest Quality Response\n--\nOur Company: Hello! Thank you for reaching out to HKBN. We offer a range of services designed to meet diverse needs:\n\n- *Home Care Plans*: In partnership with Evercare, we provide services like personal hygiene care, wound care, and medical escort services. These plans are tailored for individuals requiring extra care at home, with options starting at HKD $399/month.\n\n- *5G Mobile Services*: Our plans include high-speed local data, global travel data, and even a Chinese mainland number. For example, our 5G 20GB plan starts at just HKD $98/month.\n\nMany of our customers have shared how these services have made their lives easier, such as professional wound care or seamless mobile connectivity.\n\nFeel free to let us know if you’re looking for specific solutions, and we’d be happy to assist further!\n\n(Highest Quality Response Bot)\n--\nCustomer: I would like to know about the 5G Mobile Services. But, your prices seem a bit high. I’ve seen cheaper options elsewhere.",
                ExpectedLanguageCode = "en",
                ExpectedLanguageName = "English"
            };
        }
    }

    [TestCaseSource(nameof(LanguageDetectionTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task DetectTextLanguageIsoCodeTest(LanguageDetectionTestCase testCase)
    {
        // Act
        var result = await _languagePlugin.DetectAppropriateResponseLanguageAsync(_kernel, testCase.InputText);

        // Assert
        TestContext.WriteLine($"Input Text: {testCase.InputText}");
        TestContext.WriteLine($"Reasoning: {result.Reasoning}");
        TestContext.WriteLine($"Expected Language Code: {testCase.ExpectedLanguageCode}");
        TestContext.WriteLine($"Actual Language Code: {result.ResponseLanguageCode}");
        TestContext.WriteLine($"Expected Language Name: {testCase.ExpectedLanguageName}");
        TestContext.WriteLine($"Actual Language Name: {result.ResponseLanguageName}\n");

        Assert.Multiple(
            () =>
            {
                Assert.That(result.ResponseLanguageCode, Contains.Substring(testCase.ExpectedLanguageCode));
                Assert.That(result.ResponseLanguageName, Contains.Substring(testCase.ExpectedLanguageName));
            });
    }

    public class TranslationTestCase
    {
        public string InputText { get; set; } = string.Empty;

        public string TargetLanguageCode { get; set; } = string.Empty;
    }

    private static IEnumerable<TranslationTestCase> TranslationTestCases
    {
        get
        {
            yield return new TranslationTestCase
            {
                InputText = "新商品上架啦！🆕 欢迎选购 🛒", TargetLanguageCode = "en"
            };

            yield return new TranslationTestCase
            {
                InputText =
                    "Dear valued customers,\n\nI hope this email finds you well. We are excited to announce our upcoming system maintenance scheduled for next weekend. During this time, we will be implementing several improvements to enhance your experience with our platform. The maintenance window will be from Saturday 8 PM to Sunday 4 AM (GMT+8).\n\nPlease note that during this period, some services may be temporarily unavailable. We apologize for any inconvenience this may cause and appreciate your understanding. Our support team will remain available 24/7 to assist you with any urgent matters.\n\nBest regards,\nTechnical Support Team",
                TargetLanguageCode = "zh-Hant"
            };

            yield return new TranslationTestCase
            {
                InputText =
                    "親愛的用戶您好，\n\n感謝您一直以來對我們產品的支持。我們很高興地向您介紹我們最新推出的人工智能客戶服務系統。這個系統採用了最先進的自然語言處理技術，能夠精確理解客戶需求，提供24小時不間斷的專業服務。系統具備多語言支援、智能路由、自動回覆等功能，大大提升了客戶服務效率。\n\n為了確保您能夠順利使用新系統，我們特別準備了詳細的使用指南和視頻教程。如果您在使用過程中遇到任何問題，歡迎隨時聯繫我們的技術支援團隊。\n\n期待您的寶貴意見！",
                TargetLanguageCode = "zh-Hans"
            };

            yield return new TranslationTestCase
            {
                InputText =
                    "尊敬的产品团队：\n\n我是一名连续使用贵公司客服系统超过两年的企业客户。首先，我要肯定系统在过去几个月的显著改进。新增的自动分类功能和批量处理工具极大地提高了我们的工作效率。特别是在处理大量客户咨询时，智能分流系统表现出色，使我们能够更快地响应重要请求。\n\n不过，我也想提出一些建议：1. 报表统计功能可以更加灵活，允许自定义时间段；2. 移动端界面还需优化，某些按钮太小不易点击；3. 希望能添加更多自动化工作流程模板。\n\n我相信有了这些改进，系统会变得更加完善。感谢您们一直以来的努力！",
                TargetLanguageCode = "en"
            };

            yield return new TranslationTestCase
            {
                InputText = "The Apple iPhone supports WeChat and LINE apps", TargetLanguageCode = "fr"
            };

            yield return new TranslationTestCase
            {
                InputText = "I am writing code in JavaScript and Python", TargetLanguageCode = "zh-Hans"
            };

            yield return new TranslationTestCase
            {
                InputText = "你好，請問客戶服務時間是幾點？", TargetLanguageCode = "zh-Hans"
            };

            yield return new TranslationTestCase
            {
                InputText = "How can I help you today?", TargetLanguageCode = "zh-Hant"
            };

            yield return new TranslationTestCase
            {
                InputText = "我想预订下周的会议", TargetLanguageCode = "en"
            };

            yield return new TranslationTestCase
            {
                InputText = "Hello! 👋 I am happy to help you today 😊", TargetLanguageCode = "zh-Hant"
            };

            yield return new TranslationTestCase
            {
                InputText = "親愛的顧客 ❤️，您的訂單已確認 ✅", TargetLanguageCode = "zh-Hans"
            };

            yield return new TranslationTestCase
            {
                InputText = "新商品上架啦！🆕 欢迎选购 🛒", TargetLanguageCode = "en"
            };
        }
    }

    [TestCaseSource(nameof(TranslationTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task TranslateTextWithIsoCodeTest(TranslationTestCase testCase)
    {
        // Act
        var translatedText = await _languagePlugin.TranslateTextWithIsoCodeAsync(
            _kernel,
            testCase.InputText,
            testCase.TargetLanguageCode);
        var detectAppropriateResponseLanguageResponse =
            await _languagePlugin.DetectAppropriateResponseLanguageAsync(_kernel, translatedText);

        // Assert
        TestContext.WriteLine($"Input Text: {testCase.InputText}");
        TestContext.WriteLine($"Target Language Code: {testCase.TargetLanguageCode}");
        TestContext.WriteLine($"Translated Text: {translatedText}");
        TestContext.WriteLine(
            $"Detected Language Code: {detectAppropriateResponseLanguageResponse.ResponseLanguageCode}\n");

        // Verify the translated text is in the target language
        Assert.That(
            detectAppropriateResponseLanguageResponse.ResponseLanguageCode,
            Does.StartWith(testCase.TargetLanguageCode.Split('-')[0]));
    }

    [Test]
    public async Task DetectTextLanguageIsoCode_WithEmptyText_ShouldReturnUnknown()
    {
        // Act
        var result = await _languagePlugin.DetectAppropriateResponseLanguageAsync(_kernel, string.Empty);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result.ResponseLanguageCode, Is.EqualTo("en"));
                Assert.That(result.ResponseLanguageName, Is.EqualTo("English"));
            });
    }

    [Test]
    public async Task TranslateTextWithIsoCode_WithEmptyText_ShouldReturnEmpty()
    {
        // Act
        var result = await _languagePlugin.TranslateTextWithIsoCodeAsync(_kernel, string.Empty, "en");

        // Assert
        Assert.That(result, Is.Empty.Or.EqualTo("Translation failed"));
    }
}