using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ResolveObjectId : ITrigger
{
    private readonly IHubspotObjectService _hubspotObjectService;

    public ResolveObjectId(IHubspotObjectService hubspotObjectService)
    {
        _hubspotObjectService = hubspotObjectService;
    }

    public class ResolveObjectIdInput
    {
        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonConstructor]
        public ResolveObjectIdInput(
            Dictionary<string, object?> dict)
        {
            Dict = dict;
        }
    }

    public class ResolveObjectIdOutput
    {
        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonConstructor]
        public ResolveObjectIdOutput(
            string? id)
        {
            Id = id;
        }
    }

    public Task<ResolveObjectIdOutput> F(
        ResolveObjectIdInput resolveObjectIdInput)
    {
        try
        {
            var id = _hubspotObjectService.ResolveObjectId(
                resolveObjectIdInput.Dict);

            return Task.FromResult(new ResolveObjectIdOutput(id));
        }
        catch (SfIdUnresolvableException)
        {
            return Task.FromResult(new ResolveObjectIdOutput(null));
        }
    }
}