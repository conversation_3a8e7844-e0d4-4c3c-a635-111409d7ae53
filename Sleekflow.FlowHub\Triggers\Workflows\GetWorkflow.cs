using Sleekflow.Persistence.Abstractions;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public GetWorkflow(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class GetWorkflowInput : Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonConstructor]
        public GetWorkflowInput(
            string sleekflowCompanyId,
            string workflowId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
        }
    }

    public class GetWorkflowOutput
    {
        [JsonProperty("active_workflow")]
        public WorkflowDto? ActiveWorkflow { get; set; }

        [JsonProperty("versioned_workflows")]
        public List<WorkflowDto> VersionedWorkflows { get; set; }

        [JsonConstructor]
        public GetWorkflowOutput(WorkflowDto? activeWorkflow, List<WorkflowDto> versionedWorkflows)
        {
            ActiveWorkflow = activeWorkflow;
            VersionedWorkflows = versionedWorkflows;
        }
    }

    public async Task<GetWorkflowOutput> F(GetWorkflowInput getWorkflowInput)
    {
        var (activeWorkflow, historicalWorkflows) = await _workflowService.GetWorkflowAsync(
            getWorkflowInput.WorkflowId,
            getWorkflowInput.SleekflowCompanyId);

        return new GetWorkflowOutput(
            activeWorkflow == null ? null : new WorkflowDto(activeWorkflow),
            historicalWorkflows.Select(w => new WorkflowDto(w)).ToList());
    }
}