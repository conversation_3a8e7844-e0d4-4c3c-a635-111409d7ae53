using MassTransit;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Internals.ChatHistory;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Contacts;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Chats;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IEvaluateExitConditionsStepExecutor : IStepExecutor { }

public class EvaluateExitConditionsStepExecutor
    : GeneralStepExecutor<CallStep<EvaluateExitConditionsStepArgs>>,
        IEvaluateExitConditionsStepExecutor,
        IScopedService
{
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ILogger _logger;
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IAgentConfigService _agentConfigService;
    private readonly IStateService _stateService;
    private readonly IChatHistoryService _chatHistoryService;

    public EvaluateExitConditionsStepExecutor(
        IServiceBusManager serviceBusManager,
        IServiceProvider serviceProvider,
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator,
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        ILogger<EvaluateExitConditionsStepExecutor> logger,
        IAgentConfigService agentConfigService,
        IStateService stateService,
        IChatHistoryService chatHistoryService
    )
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _serviceBusManager = serviceBusManager;
        _logger = logger;
        _coreCommander = coreCommander;
        _stateEvaluator = stateEvaluator;
        _agentConfigService = agentConfigService;
        _stateService = stateService;
        _chatHistoryService = chatHistoryService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync
    )
    {
        try
        {
            var callStep = ToConcreteStep(step);
            var config = await _agentConfigService.GetAsync(state, callStep.Args.CompanyAgentConfigIdExpr);
            var contactId = await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.ContactIdExpr);

            var runningStates = await _stateService.GetRunningStatesAsync(
                                       state.Identity.ObjectId,
                                       state.Identity.ObjectType,
                                       state.Identity.SleekflowCompanyId,
                                       state.TriggerEventBody);
            var channelId = StateUtils
                    .GetChannelIdFromParentState(runningStates, state.WorkflowContext.SnapshottedWorkflow.WorkflowId, _logger);
            // Obtain the conversation information from SleekflowCore
            var conversationMessages = await _chatHistoryService.GetConversationMessagesAsync(state, contactId, config, callStep, channelId);
            int? scoreInt = null;
            var score = await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.ScoreExpr);
            if (score != null && score.Trim() != string.Empty)
            {
                scoreInt = int.Parse(score);
            }

            var confidenceScore = await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.ConfidenceScoreExpr);
            var confidenceScoreInt = int.Parse(confidenceScore);

            // Delay the completion until the agent has evaluated the exit condition - refer to OnAgentCompleteStepActivationEvent
            await _serviceBusManager.PublishAsync(
                new GetAgentEvaluateExitConditionsEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    state.Identity.SleekflowCompanyId,
                    config.Id,
                    conversationMessages,
                    confidenceScoreInt,
                    scoreInt
                )
            );
        }
        catch (Exception ex)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                ex
            );
        }
    }
}
