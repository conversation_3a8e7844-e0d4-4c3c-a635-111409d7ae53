﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.InternalIntegrationHubDb;

public interface IInternalIntegrationHubDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class InternalIntegrationHubDbConfig : IConfig, IInternalIntegrationHubDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public InternalIntegrationHubDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_INTERNAL_INTEGRATION_HUB_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_INTERNAL_INTEGRATION_HUB_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_INTERNAL_INTEGRATION_HUB_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_INTERNAL_INTEGRATION_HUB_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_INTERNAL_INTEGRATION_HUB_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_INTERNAL_INTEGRATION_HUB_DB_DATABASE_ID");
    }
}