using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileRemovedFromListLogData
{
    [JsonProperty("user_profile_removed_from_list")]
    public UserProfileListLogData UserProfileRemovedFromList { get; set; }

    [JsonConstructor]
    public UserProfileRemovedFromListLogData(UserProfileListLogData userProfileRemovedFromList)
    {
        UserProfileRemovedFromList = userProfileRemovedFromList;
    }
}