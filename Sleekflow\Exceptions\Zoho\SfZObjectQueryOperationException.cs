﻿namespace Sleekflow.Exceptions.Zoho;

public class SfZObjectQueryOperationException : ErrorCodeException
{
    public string ResponseStr { get; }

    public string ZObjectTypeName { get; }

    public SfZObjectQueryOperationException(string responseStr, string zObjectTypeName)
        : base(
            ErrorCodeConstant.SfZObjectQueryOperationException,
            $"The query request has failed. zObjectTypeName=[{zObjectTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "zObjectTypeName", zObjectTypeName
                }
            })
    {
        ResponseStr = responseStr;
        ZObjectTypeName = zObjectTypeName;
    }
}