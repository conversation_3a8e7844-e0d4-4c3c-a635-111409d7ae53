﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

public record VtexItemMetadataDto
{
    [JsonProperty("Items")]
    public List<VtexItemMetadataItemDto> Items { get; set; }

    [JsonConstructor]
    public VtexItemMetadataDto(List<VtexItemMetadataItemDto> items)
    {
        Items = items;
    }
}

public record VtexItemMetadataItemDto
{
    [JsonProperty("Id")]
    public string Id { get; set; }

    [JsonProperty("Seller")]
    public string Seller { get; set; }

    [JsonProperty("Name")]
    public string Name { get; set; }

    [JsonProperty("SkuName")]
    public string SkuName { get; set; }

    [JsonProperty("ProductId")]
    public string ProductId { get; set; }

    [JsonProperty("RefId")]
    public string RefId { get; set; }

    [JsonProperty("Ean")]
    public string Ean { get; set; }

    [JsonProperty("ImageUrl")]
    public string ImageUrl { get; set; }

    [JsonProperty("DetailUrl")]
    public string DetailUrl { get; set; }

    [JsonConstructor]
    public VtexItemMetadataItemDto(
        string id,
        string seller,
        string name,
        string skuName,
        string productId,
        string refId,
        string ean,
        string imageUrl,
        string detailUrl)
    {
        Id = id;
        Seller = seller;
        Name = name;
        SkuName = skuName;
        ProductId = productId;
        RefId = refId;
        Ean = ean;
        ImageUrl = imageUrl;
        DetailUrl = detailUrl;
    }
}