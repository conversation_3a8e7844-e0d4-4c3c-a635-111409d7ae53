using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;

namespace Sleekflow.FlowHub.Utils;

public static class StateUtils
{
    public static (ProxyState ParentState, CallStep<EnterAiAgentStepArgs> TargetStep) GetParentState(
        List<ProxyState> runningStates,
        string childrenWorkflowId,
        ILogger logger)
    {
        var matchingParentStates = runningStates
            .Where(x => x.StateStatus == StateStatuses.Running)
            .Where(s =>
                s.WorkflowContext.SnapshottedWorkflow.Steps
                    .OfType<CallStep<EnterAiAgentStepArgs>>()
                    .Where(stepArgs => stepArgs.Call == EnterAiAgentStepArgs.CallName)
                    .Any(stepArgs => stepArgs.Args.AiAgentWorkflowId == childrenWorkflowId))
            .ToList();

        if (matchingParentStates.Count == 0)
        {
            var errorMessage = $"No parent state found for childrenWorkflowId: '{childrenWorkflowId}' that is configured to call this workflow via EnterAiAgentStep.";
            logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        if (matchingParentStates.Count > 1)
        {
            var errorMessage = $"Multiple parent states ({matchingParentStates.Count}) found for childrenWorkflowId: '{childrenWorkflowId}' that are configured to call this workflow via EnterAiAgentStep. Expected exactly one.";
            logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        var uniqueParentState = matchingParentStates.Single();

        var targetStepsInParent = uniqueParentState.WorkflowContext.SnapshottedWorkflow.Steps
            .OfType<CallStep<EnterAiAgentStepArgs>>()
            .Where(s => s.Call == EnterAiAgentStepArgs.CallName && s.Args.AiAgentWorkflowId == childrenWorkflowId)
            .ToList();

        if (targetStepsInParent.Count == 0)
        {
            var errorMessage = $"Parent state '{uniqueParentState.Id}' was matched, but no specific EnterAiAgentStep pointing to child workflow '{childrenWorkflowId}' could be uniquely identified within it.";
            logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        if (targetStepsInParent.Count > 1)
        {
            var errorMessage = $"Parent state '{uniqueParentState.Id}' was matched, but it contains multiple ({targetStepsInParent.Count}) EnterAiAgentStep entries pointing to child workflow '{childrenWorkflowId}'. Expected exactly one such step in the parent.";
            logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        return (uniqueParentState, targetStepsInParent.Single());
    }

    public static string? GetChannelIdFromParentState(List<ProxyState> runningStates, string childrenWorkflowId, ILogger logger)
    {
        string? channelId = null;
        try
        {
            var (parentState, targetStepInParent) = GetParentState(runningStates, childrenWorkflowId, logger);
            channelId = targetStepInParent.Args.ChannelIdentityId;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting parent state for workflow {WorkflowId}", childrenWorkflowId);
        }
        return channelId;
    }
}