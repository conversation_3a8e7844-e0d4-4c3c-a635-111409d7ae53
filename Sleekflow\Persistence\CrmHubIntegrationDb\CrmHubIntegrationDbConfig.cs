using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.CrmHubIntegrationDb;

public interface ICrmHubIntegrationDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class CrmHubIntegrationDbConfig : IConfig, ICrmHubIntegrationDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public CrmHubIntegrationDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_CRM_HUB_INTEGRATION_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CRM_HUB_INTEGRATION_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_CRM_HUB_INTEGRATION_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CRM_HUB_INTEGRATION_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_CRM_HUB_INTEGRATION_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CRM_HUB_INTEGRATION_DB_DATABASE_ID");
    }
}