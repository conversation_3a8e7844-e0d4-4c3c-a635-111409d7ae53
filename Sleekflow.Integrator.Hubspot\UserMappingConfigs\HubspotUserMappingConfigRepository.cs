﻿using Sleekflow.CrmHub.Models.UserMappingConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.UserMappingConfigs;

public interface IHubspotUserMappingConfigRepository : IRepository<HubspotUserMappingConfig>
{
}

public class HubspotUserMappingConfigRepository
    : BaseRepository<HubspotUserMappingConfig>,
        IHubspotUserMappingConfigRepository,
        ISingletonService
{
    public HubspotUserMappingConfigRepository(
        ILogger<BaseRepository<HubspotUserMappingConfig>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}