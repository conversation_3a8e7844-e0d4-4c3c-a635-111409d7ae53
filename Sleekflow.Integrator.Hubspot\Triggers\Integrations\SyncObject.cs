using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SyncObject : ITrigger
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IBus _bus;

    public SyncObject(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IBus bus)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _bus = bus;
    }

    public class SyncObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public SyncObjectInput(
            string sleekflowCompanyId,
            string objectId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            EntityTypeName = entityTypeName;
        }
    }

    public class SyncObjectOutput
    {
    }

    public async Task<SyncObjectOutput> F(
        SyncObjectInput syncObjectInput)
    {
        var authentication =
            await _hubspotAuthenticationService.GetAsync(syncObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var @object = await _hubspotObjectService.GetObjectAsync(
            authentication,
            syncObjectInput.ObjectId,
            syncObjectInput.EntityTypeName);
        if (@object == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        await _bus.Publish(
            new OnObjectOperationEvent(
                @object,
                OnObjectOperationEvent.OperationCreateOrUpdateObject,
                "hubspot-integrator",
                syncObjectInput.SleekflowCompanyId,
                _hubspotObjectService.ResolveObjectId(@object),
                syncObjectInput.EntityTypeName,
                null),
            context => { context.ConversationId = Guid.Parse(syncObjectInput.SleekflowCompanyId); });

        return new SyncObjectOutput();
    }
}