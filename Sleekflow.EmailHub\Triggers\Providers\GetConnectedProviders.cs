using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Companys;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Providers;

[TriggerGroup(ControllerNames.Providers)]
public class GetConnectedProviders : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;

    public GetConnectedProviders(IProviderConfigService providerConfigService)
    {
        _providerConfigService = providerConfigService;
    }

    public class GetConnectedProvidersInput
    {
        [JsonConstructor]
        public GetConnectedProvidersInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }
    }

    public class GetConnectedProvidersOutput
    {
        [JsonConstructor]
        public GetConnectedProvidersOutput(List<CompanyProviderWithEmails> companyProviderWithEmailList)
        {
            CompanyProviderWithEmailList = companyProviderWithEmailList;
        }

        [JsonProperty("company_provider_with_email_list")]
        public List<CompanyProviderWithEmails> CompanyProviderWithEmailList { get; set; }
    }

    public async Task<GetConnectedProvidersOutput> F(GetConnectedProvidersInput getConnectedProvidersInput)
    {
        var companyProviderWithEmailList = await _providerConfigService
            .GetConnectedProviderAsync(getConnectedProvidersInput.SleekflowCompanyId);

        return new GetConnectedProvidersOutput(companyProviderWithEmailList);
    }
}