using Sleekflow.DependencyInjection;

namespace Sleekflow.FlowHub.Silos;

public interface ISiloReadinessIndicator
{
    bool IsReady { get; }

    Task WaitUntilReadyAsync(CancellationToken cancellationToken = default);

    void SignalReady(); // Method to be called by the lifecycle observer
}

public class SiloReadinessIndicator : ISiloReadinessIndicator, ISingletonService
{
    private readonly TaskCompletionSource<bool> _readinessTcs =
        new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);

    private readonly ILogger<SiloReadinessIndicator> _logger;

    public SiloReadinessIndicator(ILogger<SiloReadinessIndicator> logger)
    {
        _logger = logger;
    }

    public bool IsReady => _readinessTcs.Task.IsCompletedSuccessfully;

    public Task WaitUntilReadyAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Waiting for silo readiness signal...");

        // Register cancellation if token is valid
        if (cancellationToken.CanBeCanceled)
        {
            return _readinessTcs.Task.WaitAsync(cancellationToken); // Use WaitAsync for cancellation
        }

        return _readinessTcs.Task;
    }

    public void SignalReady()
    {
        _logger.LogInformation("Silo readiness signal received. Completing task.");
        // TrySetResult ensures this only completes once
        _readinessTcs.TrySetResult(true);
    }
}