using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class EnterAiAgentStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.enter-ai-agent";

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonProperty("ai_agent_id")]
    [Required]
    public string AiAgentId { get; set; }

    [JsonProperty("channel_type")]
    [Required]
    public string ChannelType { get; set; }

    [JsonProperty("channel_identity_id")]
    [Required]
    public string ChannelIdentityId { get; set; }

    [JsonProperty("ai_agent_workflow_id")]
    public string? AiAgentWorkflowId { get; set; }

    [JsonConstructor]
    public EnterAiAgentStepArgs(
        string aiAgentId,
        string channelType,
        string channelIdentityId,
        string? aiAgentWorkflowId)
    {
        AiAgentId = aiAgentId;
        ChannelType = channelType;
        ChannelIdentityId = channelIdentityId;
        AiAgentWorkflowId = aiAgentWorkflowId;
    }
}