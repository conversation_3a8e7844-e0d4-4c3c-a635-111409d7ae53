using System.ComponentModel.DataAnnotations;
using ShopifySharp;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Shopify;

public static class ShopifyClientValidator
{
    public static async Task AssertValidShopifyCredentialAsync(
        string shopifyUrl,
        string shopAccessToken)
    {
        if (!shopifyUrl.Contains("myshopify.com"))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "This Shopify URL is invalid.",
                        new List<string>
                        {
                            nameof(shopifyUrl)
                        })
                });
        }

        try
        {
            var shopService = new ShopService(shopifyUrl, shopAccessToken);

            var shop = await shopService.GetAsync();

            if (shop is null)
            {
                throw new Exception();
            }
        }
        catch (HttpRequestException)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Failed to authenticate with the host with this Shopify URL and shop access token.",
                        new List<string>
                        {
                            nameof(shopifyUrl), nameof(shopAccessToken)
                        })
                });
        }
        catch (Exception)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "This Shopify URL or shop access token is invalid.",
                        new List<string>
                        {
                            nameof(shopifyUrl), nameof(shopAccessToken)
                        })
                });
        }
    }
}