using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.TenantHubDb;

public interface ITenantHubDbResolver : IContainerResolver
{
}

public class TenantHubDbResolver : ITenantHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public TenantHubDbResolver(ITenantHubDbConfig tenantHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            tenantHubDbConfig.Endpoint,
            tenantHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                ApplicationRegion = (tenantHubDbConfig.SfLocation == "local")
                    ? "eastasia"
                    : tenantHubDbConfig.SfLocation,
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}