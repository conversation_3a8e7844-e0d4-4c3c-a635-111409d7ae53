﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IWorkflowDeletionConfig
{
    public int WorkflowVersionDeletionCleanupDelayDays { get; }
}

public class WorkflowDeletionConfig : IConfig, IWorkflowDeletionConfig
{
    public int WorkflowVersionDeletionCleanupDelayDays { get; }

    public WorkflowDeletionConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        WorkflowVersionDeletionCleanupDelayDays =
            int.TryParse(
                Environment.GetEnvironmentVariable(
                    "WORKFLOW_VERSION_DELETION_CLEANUP_DELAY_DAYS",
                    target),
                out var workflowVersionDeletionCleanupDelayDays)
                ? workflowVersionDeletionCleanupDelayDays
                : throw new SfMissingEnvironmentVariableException(
                    "WORKFLOW_VERSION_DELETION_CLEANUP_DELAY_DAYS");
    }
}