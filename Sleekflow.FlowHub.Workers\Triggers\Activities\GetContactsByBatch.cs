﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class GetContactsByBatch
{
    private readonly ISleekflowCoreConfig _sleekflowCoreConfig;
    private readonly HttpClient _httpClient;
    private readonly ILogger<GetContactsByBatch> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;

    public GetContactsByBatch(
        ISleekflowCoreConfig sleekflowCoreConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<GetContactsByBatch> logger,
        IAsyncPolicy<HttpResponseMessage> retryPolicy)
    {
        _sleekflowCoreConfig = sleekflowCoreConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _logger = logger;
        _retryPolicy = retryPolicy;
    }

    public class GetContactsByBatchFunctionInput : GetContactsByBatchInput
    {
        [Required]
        [JsonProperty("origin")]
        public string Origin { get; set; }

        public GetContactsByBatchFunctionInput(
            string origin,
            string sleekflowCompanyId,
            DateTimeOffset? lastContactCreatedAt,
            string? lastContactId,
            int? batchSize)
            : base(sleekflowCompanyId, lastContactCreatedAt, lastContactId, batchSize)
        {
            Origin = origin;
        }
    }

    [Function("GetContactsByBatch")]
    public async Task<GetContactsByBatchOutput> RunAsync(
        [ActivityTrigger]
        GetContactsByBatchFunctionInput getContactsByBatchFunctionInput)
    {
        _logger.LogInformation(
            "Get contacts by batch for company {CompanyId} with last contact created at {LastContactCreatedAt} and last contact id {LastContactId}",
            getContactsByBatchFunctionInput.SleekflowCompanyId,
            getContactsByBatchFunctionInput.LastContactCreatedAt,
            getContactsByBatchFunctionInput.LastContactId);

        var getContactsByBatchInput = new GetContactsByBatchInput(
            getContactsByBatchFunctionInput.SleekflowCompanyId,
            getContactsByBatchFunctionInput.LastContactCreatedAt,
            getContactsByBatchFunctionInput.LastContactId,
            getContactsByBatchFunctionInput.BatchSize);

        var inputJsonStr = JsonConvert.SerializeObject(getContactsByBatchInput);
        var targetUri = new Uri(getContactsByBatchFunctionInput.Origin + "/FlowHub/Internals/Functions/GetContactsByBatch");

        var pollyContext = new Context();
        pollyContext["logger"] = _logger; // Key "logger" must match what onRetryAsync expects

        var resMsg = await _retryPolicy.ExecuteAsync(
            async (context) =>
        {
            var reqMsg = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
                RequestUri = targetUri,
                Headers = { { "X-Sleekflow-Flow-Hub-Authorization", InternalsTokenUtils.CreateJwt(_sleekflowCoreConfig.CoreInternalsKey) } },
            };

            // Log attempt using the instance logger
            _logger.LogInformation(
                "Attempting HTTP POST to {Uri} for {Company}",
                targetUri,
                getContactsByBatchFunctionInput.SleekflowCompanyId);
            return await _httpClient.SendAsync(reqMsg);
        }, pollyContext); // Pass the context to ExecuteAsync
        resMsg.EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        return JsonConvert.DeserializeObject<GetContactsByBatchOutput>(resStr)!;
    }
}