using System.ComponentModel;
using AngleSharp.Html;
using AngleSharp.Html.Parser;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class WebPageContentPluginFetchResponse : QueryKnowledgeResponse
{
    public WebPageContentPluginFetchResponse(string knowledge, string id)
        : base(knowledge, id, "FetchWebsite")
    {
    }
}

public interface IWebPageContentPlugin
{
    [KernelFunction("get_page_content")]
    [Description("Fetches and extracts the text content from a given URL.")]
    Task<WebPageContentPluginFetchResponse> GetPageContentAsync(
        Kernel kernel,
        [Description("The URL of the page to fetch content from")]
        string url,
        [Description("The topic to summarize the content for")]
        string? topic);
}

public class WebPageContentPlugin
    : IWebPageContentPlugin, IScopedService
{
    private readonly ILogger<WebPageContentPlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly IHttpClientFactory _httpClientFactory;

    public WebPageContentPlugin(
        ILogger<WebPageContentPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _httpClientFactory = httpClientFactory;
    }

    [KernelFunction("get_page_content")]
    [Description("Fetches and extracts the text content from a given URL.")]
    public async Task<WebPageContentPluginFetchResponse> GetPageContentAsync(
        Kernel kernel,
        [Description("The URL of the page to fetch content from")]
        string url,
        [Description("The topic to summarize the content for")]
        string? topic)
    {
        _logger.LogInformation("Fetching page content for URL: {Url}", url);

        var id = Guid.NewGuid().ToString();

        // Configure HttpClient for the request
        // TODO: Use a named client for better configuration management
        using var client = _httpClientFactory.CreateClient("default-handler");
        client.Timeout = TimeSpan.FromSeconds(10); // Set a reasonable timeout
        client.DefaultRequestHeaders.Add(
            "User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3");

        try
        {
            // Fetch the page content
            var response = await client.GetAsync(url);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to fetch page: {StatusCode}", response.StatusCode);

                return new WebPageContentPluginFetchResponse(
                    $"Failed to fetch page: {response.StatusCode}, {url}",
                    id);
            }

            var html = await response.Content.ReadAsStringAsync();

            // Parse HTML with HtmlAgilityPack
            var textContent = ExtractTextContent(html);

            var content = await SummarizeWebsiteContentAsync(kernel, topic ?? "general", textContent);

            _logger.LogInformation(
                "Successfully fetched and extracted content for URL: {Url}, {Content}",
                url,
                content);

            return new WebPageContentPluginFetchResponse(
                $"<CONFIRMED_KNOWLEDGE><URL>{url}</URL><SUMMARY>{content}</SUMMARY></CONFIRMED_KNOWLEDGE>",
                id); // Limit to 2000 characters
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching page content for URL: {Url}", url);

            return new WebPageContentPluginFetchResponse(
                $"Failed to fetch page: {url}",
                id);
        }
    }

    private async Task<string> SummarizeWebsiteContentAsync(
        Kernel kernel,
        string query,
        string data)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        var summarizeFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SummarizeWebsiteContent",
                Template =
                    """
                    To effectively address the query, start by conducting a thorough and systematic review of the provided data, paying close attention to all details that might relate to the question at hand. Identify and isolate the pieces of information, facts, or insights that are most directly applicable to the query, setting aside any extraneous or unrelated material. After gathering this critical information, take a moment to reflect on how it aligns with the query’s intent, ensuring you fully understand its implications. Then, craft your response by organizing the extracted information into a series of concise bullet points. Each bullet point should focus on a single, distinct idea or piece of evidence, expressed clearly and succinctly to maintain readability. Arrange these bullet points in a deliberate, logical sequence—starting with foundational information and progressing naturally to more specific or conclusive points—so that the reader can follow your reasoning effortlessly. This structured approach should culminate in a comprehensive and well-supported answer that fully resolves the query while remaining engaging and easy to digest.

                    ====DATA====
                    {{$DATA}}
                    ====DATA====

                    ====QUERY====
                    {{$QUERY}}
                    ====QUERY====
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "QUERY", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "DATA", IsRequired = true
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "A summarized result relevant to the query."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                },
            });

        // Invoke the function with the provided input
        try
        {
            var chatMessageContent = await summarizeFunction.InvokeAsync<ChatMessageContent>(
                kernel,
                new KernelArguments(promptExecutionSettings)
                {
                    {
                        "DATA", data
                    },
                    {
                        "QUERY", query
                    }
                });

            return chatMessageContent?.Content ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing data");
            return string.Empty;
        }
    }

    public static string ExtractTextContent(string html)
    {
        // Create an HTML parser
        var parser = new HtmlParser();

        // Parse the HTML string into a document
        var document = parser.ParseDocument(html);

        // Create a formatter for pretty-printing
        var formatter = new PrettyMarkupFormatter();

        // Serialize the document to formatted HTML
        var sw = new StringWriter();
        document.ToHtml(sw, formatter);
        var prettyHtml = sw.ToString();

        // Parse the HTML string into a document
        document = parser.ParseDocument(prettyHtml);

        // Remove all <script> elements
        var scripts = document.QuerySelectorAll("script");
        foreach (var script in scripts)
        {
            script.Remove();
        }

        // Remove all <style> elements
        var styles = document.QuerySelectorAll("style");
        foreach (var style in styles)
        {
            style.Remove();
        }

        // Get the body element and its text content
        var body = document.Body;
        string text = body?.TextContent ?? string.Empty;

        // Normalize whitespace: replace sequences of whitespace with a single space and trim
        string cleaned = CleanText(text);

        return cleaned;
    }

    private static string CleanText(string inputText)
    {
        // Split the text into lines
        var lines = inputText.Split(
            new[]
            {
                '\r',
                '\n'
            },
            StringSplitOptions.None);

        // Trim each line and remove empty lines
        var cleanedLines = lines
            .Select(line => line.Trim()) // Trim leading/trailing whitespace
            .Where(line => !string.IsNullOrWhiteSpace(line)); // Remove empty or whitespace-only lines

        // Join the lines back with newlines to preserve paragraphs
        return string.Join(Environment.NewLine, cleanedLines);
    }
}