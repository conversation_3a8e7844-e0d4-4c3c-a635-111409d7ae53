﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.FlowHubIntegrationDb;

public interface IFlowHubIntegrationDbResolver : IContainerResolver
{
}

public class FlowHubIntegrationDbResolver : IFlowHubIntegrationDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public FlowHubIntegrationDbResolver(IFlowHubIntegrationDbConfig flowHubIntegrationDbConfig)
    {
        _cosmosClient = new CosmosClient(
            flowHubIntegrationDbConfig.Endpoint,
            flowHubIntegrationDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}