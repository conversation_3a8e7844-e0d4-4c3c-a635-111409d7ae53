﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.CrmHub.Schemas;

public interface ISchemaRepository : IRepository<Schema>
{
    public Task<Schema> PatchAndGetSchemaAsync(Schema schema);

    Task<List<Schema>> GetUsageLimitCountableSchemasAsync(string sleekflowCompanyId);

    Task<int> GetUsageLimitCountableSchemasCountAsync(string sleekflowCompanyId);

    Task<List<string>> GetUsageLimitCountableSchemaIdsAsync(string sleekflowCompanyId);

    Task<bool> IsSchemaUniqueNameExistsAsync(string sleekflowCompanyId, string uniqueName);
}

public class SchemaRepository
    : BaseRepository<Schema>, ISchemaRepository, ISingletonService
{
    public SchemaRepository(
        ILogger<SchemaRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<Schema> PatchAndGetSchemaAsync(Schema schema)
    {
        return await PatchAndGetAsync(
            schema.Id,
            schema.SleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace(Schema.PropertyNameDisplayName, schema.DisplayName),
                Replace(Schema.PropertyNameIsEnabled, schema.IsEnabled),
                Replace(Schema.PropertyNameIsDeleted, schema.IsDeleted),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, schema.UpdatedAt),
                Replace(Schema.PropertyNameSortingWeight, schema.SortingWeight),
                Replace(Schema.PropertyNameProperties, schema.Properties),
                Replace(
                    new List<string>
                    {
                        Schema.PropertyNamePrimaryProperty, IProperty.PropertyNameDisplayName
                    },
                    schema.PrimaryProperty.DisplayName)
            });
    }

    public async Task<List<Schema>> GetUsageLimitCountableSchemasAsync(string sleekflowCompanyId)
    {
        return await GetObjectsAsync(
            s =>
                s.SleekflowCompanyId == sleekflowCompanyId &&
                s.IsDeleted == false &&
                s.SchemaAccessibilitySettings.Category == SchemaCategories.Custom,
            s => s.SortingWeight,
            true);
    }

    public async Task<int> GetUsageLimitCountableSchemasCountAsync(string sleekflowCompanyId)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
            $"""
             SELECT
              VALUE COUNT(1)
             FROM
              %%CONTAINER_NAME%% r
             WHERE
              r.{IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId} = @sleekflowCompanyId
             AND
              r.{Schema.PropertyNameIsDeleted} = false
              AND
              r.{Schema.PropertyNameSchemaAccessibilitySettings}.{SchemaAccessibilitySettings.PropertyNameCategory} = "{SchemaCategories.Custom}"
             """)
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int?>(qd);
        var queryResult = await itemQueryIterator.ReadNextAsync();

        var count = queryResult.FirstOrDefault() ?? 0;

        return count;
    }

    public async Task<List<string>> GetUsageLimitCountableSchemaIdsAsync(string sleekflowCompanyId)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                $"""
             SELECT
              VALUE r.id
             FROM
              %%CONTAINER_NAME%% r
             WHERE
              r.{IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId} = @sleekflowCompanyId
             AND
              r.{Schema.PropertyNameIsDeleted} = false
             AND
              r.{Schema.PropertyNameSchemaAccessibilitySettings}.{SchemaAccessibilitySettings.PropertyNameCategory} = "{SchemaCategories.Custom}"
             """)
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        var results = new List<string>();

        using var itemQueryIterator = container.GetItemQueryIterator<string>(qd);
        while (itemQueryIterator.HasMoreResults)
        {
            var queryResult = await itemQueryIterator.ReadNextAsync();
            results.AddRange(queryResult);
        }

        return results;
    }

    public async Task<bool> IsSchemaUniqueNameExistsAsync(string sleekflowCompanyId, string uniqueName)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                $"""
             SELECT
              VALUE COUNT(1)
             FROM
              %%CONTAINER_NAME%% r
             WHERE
              r.{IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId} = @sleekflowCompanyId
             AND
              r.{Schema.PropertyNameIsDeleted} = false
              AND
              r.{Schema.PropertyNameUniqueName} = @uniqueName
             """)
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@uniqueName", uniqueName);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int?>(qd);
        var queryResult = await itemQueryIterator.ReadNextAsync();

        var count = queryResult.FirstOrDefault() ?? 0;

        return count > 0;
    }
}