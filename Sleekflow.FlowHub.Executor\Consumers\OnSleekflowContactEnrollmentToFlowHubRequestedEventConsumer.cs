using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnSleekflowContactEnrollmentToFlowHubRequestedEventConsumerDefinition : ConsumerDefinition<OnSleekflowContactEnrollmentToFlowHubRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnSleekflowContactEnrollmentToFlowHubRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnSleekflowContactEnrollmentToFlowHubRequestedEventConsumer : IConsumer<OnSleekflowContactEnrollmentToFlowHubRequestedEvent>
{
    private readonly IServiceBusManager _serviceBusManager;

    public OnSleekflowContactEnrollmentToFlowHubRequestedEventConsumer(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<OnSleekflowContactEnrollmentToFlowHubRequestedEvent> context)
    {
        var onSleekflowContactEnrollmentToFlowHubRequestedEvent = context.Message;

        await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
            new OnContactEnrolledEventBody(
                onSleekflowContactEnrollmentToFlowHubRequestedEvent.CreatedAt,
                onSleekflowContactEnrollmentToFlowHubRequestedEvent.ContactId,
                onSleekflowContactEnrollmentToFlowHubRequestedEvent.Contact,
                onSleekflowContactEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowId,
                onSleekflowContactEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowVersionedId),
            onSleekflowContactEnrollmentToFlowHubRequestedEvent.ContactId,
            "Contact",
            onSleekflowContactEnrollmentToFlowHubRequestedEvent.SleekflowCompanyId));
    }
}