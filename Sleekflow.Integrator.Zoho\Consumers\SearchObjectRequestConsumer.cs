﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.Zoho.Consumers;

public class SearchZohoObjectRequestConsumerDefinition : ConsumerDefinition<SearchZohoObjectRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SearchZohoObjectRequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SearchZohoObjectRequestConsumer : IConsumer<SearchZohoObjectRequest>
{
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly ILogger<SearchZohoObjectRequestConsumer> _logger;

    public SearchZohoObjectRequestConsumer(
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService,
        IBus bus,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        ILogger<SearchZohoObjectRequestConsumer> logger)
    {
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SearchZohoObjectRequest> context)
    {
        var request = context.Message;

        var consumeId = Guid.NewGuid().ToString();

        try
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoSearchObjectRequestReceived,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            var connection = await _zohoConnectionService.GetByIdAsync(
                request.ConnectionId,
                request.SleekflowCompanyId);

            var authentication =
                await _zohoAuthenticationService.GetAsync(
                    connection.AuthenticationId,
                    request.SleekflowCompanyId);
            if (authentication == null)
            {
                throw new SfUnauthorizedException();
            }

            var objects = await _zohoObjectService.SearchObjectsAsync(
                authentication,
                request.EntityTypeName,
                request.Conditions.Select(
                    c =>
                        new Sleekflow.CrmHub.Models.InflowActions.SearchObjectCondition(
                            c.FieldName,
                            c.Operator,
                            c.Value)).ToList());

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoSearchObjectRequestHandled,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            var result = objects.Count == 0 ? new Dictionary<string, object?>() : objects[0];
            await _bus.Publish(
                new OnZohoCompleteStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    JsonConvert.SerializeObject(result)));
        }
        catch (Exception ex)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoSearchObjectRequestFailed,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            _logger.LogError(
                ex,
                "SearchZohoObjectRequest failed,"
                + " ConsumeId: {ConsumeId},"
                + " SleekflowCompanyId: {SleekflowCompanyId},"
                + " ConnectionId: {ConnectionId},"
                + " EntityTypeName: {EntityTypeName},"
                + " Conditions: {Conditions},"
                + " AggregateStepId: {AggregateStepId},"
                + " ProxyStateId: {ProxyStateId},"
                + " StackEntries: {StackEntries},",
                consumeId,
                request.SleekflowCompanyId,
                request.ConnectionId,
                request.EntityTypeName,
                JsonConvert.SerializeObject(request.Conditions),
                request.AggregateStepId,
                request.ProxyStateId,
                request.StackEntries);

            await _bus.Publish(
                new OnZohoFailStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null,
                    ex));
        }
    }
}