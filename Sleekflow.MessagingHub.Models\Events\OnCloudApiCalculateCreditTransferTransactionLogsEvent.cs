﻿using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

namespace Sleekflow.MessagingHub.Models.Events;

public class OnCloudApiCalculateCreditTransferTransactionLogsEvent
{
    public BusinessBalance BusinessBalance { get; set; }

    public List<BusinessBalanceTransactionLog> CreditTransferTransactionLogs { get; set; }

    public List<CreditTransferFromTo> CreditTransfers { get; set; }

    public string? SleekflowCompanyId { get; set; }

    public string? SleekflowStaffId { get; set; }

    public List<string>? SleekflowStaffTeamIds { get; set; }

    public OnCloudApiCalculateCreditTransferTransactionLogsEvent(
        BusinessBalance businessBalance,
        List<BusinessBalanceTransactionLog> creditTransferTransactionLogs,
        List<CreditTransferFromTo> creditTransfers,
        string? sleekflowCompanyId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        BusinessBalance = businessBalance;
        CreditTransferTransactionLogs = creditTransferTransactionLogs;
        CreditTransfers = creditTransfers;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
    }
}