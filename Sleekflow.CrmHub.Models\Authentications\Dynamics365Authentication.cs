using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Authentications;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("dynamics365_authentication")]
public class Dynamics365Authentication : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("scope")]
    public string Scope { get; set; }

    [JsonProperty("expires_in")]
    public long ExpiresIn { get; set; }

    [JsonProperty("ext_expires_in")]
    public long ExtExpiresIn { get; set; }

    [JsonProperty("expires_on")]
    public long ExpiresOn { get; set; }

    [JsonProperty("not_before")]
    public long NotBefore { get; set; }

    [JsonProperty("resource")]
    public string Resource { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty("raw_res")]
    public Dictionary<string, object?> RawRes { get; set; }

    [JsonProperty("refresh_res")]
    public Dictionary<string, object?>? RefreshRes { get; set; }

    [JsonConstructor]
    public Dynamics365Authentication(
        string id,
        string sleekflowCompanyId,
        string tokenType,
        string scope,
        long expiresIn,
        long extExpiresIn,
        long expiresOn,
        long notBefore,
        string resource,
        string accessToken,
        string refreshToken,
        Dictionary<string, object?> rawRes,
        Dictionary<string, object?>? refreshRes)
        : base(id, "Dynamics365Authentication")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        TokenType = tokenType;
        Scope = scope;
        ExpiresIn = expiresIn;
        ExtExpiresIn = extExpiresIn;
        ExpiresOn = expiresOn;
        NotBefore = notBefore;
        Resource = resource;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        RawRes = rawRes;
        RefreshRes = refreshRes;
    }
}