using Azure.Search.Documents.Models;

namespace Sleekflow.Exceptions.CognitiveSearch;

public class SfCognitiveSearchException : ErrorCodeException
{
    public List<IndexingResult> FailedIndexResults { get; }

    public SfCognitiveSearchException(List<IndexingResult> failedIndexResults)
        : base(ErrorCodeConstant.SfCognitiveSearchException)
    {
        FailedIndexResults = failedIndexResults;
    }
}