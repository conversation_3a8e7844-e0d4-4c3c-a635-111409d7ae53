using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowRepository : IRepository<Workflow>
{
    public record WorkflowIdAndMaxCreatedAtTuple(string WorkflowId, DateTimeOffset MaxCreatedAt);

    Task<List<WorkflowIdAndMaxCreatedAtTuple>> GetLatestWorkflowIdCreatedAtTuplesAsync(
        string sleekflowCompanyId);

    Task<int> CountWorkflowsAsync(
        string sleekflowCompanyId,
        string? workflowId,
        string? activationStatus,
        string? workflowType);

    Task<List<Workflow>> GetWorkflowsByIdsAsync(
        string sleekflowCompanyId,
        List<string> workflowIds);

    Task BatchPatchAsync(List<(string WorkflowVersionedId, string SleekflowCompanyId, List<PatchOperation> Operations, string ETag)> batchOperations);
}

public class WorkflowRepository : BaseRepository<Workflow>, IWorkflowRepository, IScopedService
{
    public WorkflowRepository(
        ILogger<WorkflowRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<List<IWorkflowRepository.WorkflowIdAndMaxCreatedAtTuple>> GetLatestWorkflowIdCreatedAtTuplesAsync(
        string sleekflowCompanyId)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                """
                SELECT r.workflow_id WorkflowId, MAX(r.created_at) MaxCreatedAt
                FROM %%CONTAINER_NAME%% r
                WHERE r.sleekflow_company_id = @sleekflow_company_id
                GROUP BY r.workflow_id
                """)
            .WithParameter("@sleekflow_company_id", sleekflowCompanyId);
        var limit = 1000;

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator =
            container.GetItemQueryIterator<IWorkflowRepository.WorkflowIdAndMaxCreatedAtTuple>(
                qd,
                requestOptions: new QueryRequestOptions
                {
                    MaxItemCount = limit > 1000 ? 2500 : limit,
                    MaxBufferedItemCount = limit > 1000 ? 2500 : limit,
                    MaxConcurrency = 4,
                });

        var count = 0;
        var objs = new List<IWorkflowRepository.WorkflowIdAndMaxCreatedAtTuple>();

        while (itemQueryIterator.HasMoreResults && count < limit)
        {
            var response = await itemQueryIterator.ReadNextAsync();
            objs.AddRange(response);

            count += response.Count;
        }

        return objs;
    }

    public async Task<int> CountWorkflowsAsync(
        string sleekflowCompanyId,
        string? workflowId,
        string? activationStatus,
        string? workflowType)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                $"""
                 SELECT VALUE COUNT(1) FROM
                 (SELECT DISTINCT(r.workflow_id)
                 FROM %%CONTAINER_NAME%% r
                 WHERE r.sleekflow_company_id = @sleekflow_company_id
                 AND r.activation_status != '{WorkflowActivationStatuses.Deleted}'
                 {(string.IsNullOrWhiteSpace(workflowId) ? string.Empty : "AND r.workflow_id = @workflow_id")}
                 {(string.IsNullOrWhiteSpace(activationStatus) ? string.Empty : "AND r.activation_status = @activation_status")}
                 {(string.IsNullOrWhiteSpace(workflowType) ? string.Empty : "AND r.workflow_type = @workflow_type")})
                 """)
            .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
            .WithParameter("@workflow_id", workflowId)
            .WithParameter("@activation_status", activationStatus)
            .WithParameter("@workflow_type", workflowType);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int?>(qd);
        var queryResult = await itemQueryIterator.ReadNextAsync();

        return queryResult.FirstOrDefault() ?? 0;
    }

    public async Task<List<Workflow>> GetWorkflowsByIdsAsync(
        string sleekflowCompanyId,
        List<string> workflowIds)
    {
        if (string.IsNullOrEmpty(sleekflowCompanyId))
        {
            throw new ArgumentNullException(nameof(sleekflowCompanyId));
        }

        if (workflowIds == null || !workflowIds.Any())
        {
            return new List<Workflow>();
        }

        const int maxBatchSize = 100;
        if (workflowIds.Count > maxBatchSize)
        {
            throw new ArgumentException($"The number of workflow IDs exceeds the maximum batch size of {maxBatchSize}.");
        }

        //TODO: This is a temporary solution to get the latest workflow for each workflow ID.
        //We need to find a better solution to get the latest workflow for each workflow ID.
        var queryDefinition = new QueryDefinition(
        """
            SELECT * FROM %%CONTAINER_NAME%% w
            WHERE w.sleekflow_company_id = @sleekflow_company_id
            AND ARRAY_CONTAINS(@workflow_ids, w.workflow_id)
            AND w.activation_status != @activation_status
            """)
        .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
        .WithParameter("@workflow_ids", workflowIds)
        .WithParameter("@activation_status", WorkflowActivationStatuses.Deleted);

        var workflows = await GetObjectsAsync(queryDefinition);

        var latestWorkflows = workflows
            .GroupBy(w => w.WorkflowId)
            .Select(g => g.OrderByDescending(w => w.CreatedAt).First())
            .ToList();

        return latestWorkflows;
    }

    public async Task BatchPatchAsync(List<(string WorkflowVersionedId, string SleekflowCompanyId, List<PatchOperation> Operations, string ETag)> batchOperations)
    {
        var groupedOperations = batchOperations
            .GroupBy(op => op.SleekflowCompanyId)
            .ToList();

        foreach (var group in groupedOperations)
        {
            var partitionKey = group.Key;
            var operations = group.ToList();

            await ExecuteTransactionalBatchAsync(
                      partitionKey,
                      (Func<TransactionalBatch, Task>) (async batch =>
                      {
                          foreach (var operation in operations)
                          {
                              batch.PatchItem(
                                  operation.WorkflowVersionedId,
                                  operation.Operations,
                                  new TransactionalBatchPatchItemRequestOptions
                                  {
                                      IfMatchEtag = operation.ETag
                                  });
                          }
                      }));
        }
    }

}