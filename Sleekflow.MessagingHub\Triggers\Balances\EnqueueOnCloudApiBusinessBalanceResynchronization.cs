using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Triggers.Balances;

[TriggerGroup(ControllerNames.Balances)]
public class EnqueueOnCloudApiBusinessBalanceResynchronization
    : ITrigger<
        EnqueueOnCloudApiBusinessBalanceResynchronization.EnqueueOnCloudApiBusinessBalanceResynchronizationInput,
        EnqueueOnCloudApiBusinessBalanceResynchronization.EnqueueOnCloudApiBusinessBalanceResynchronizationOutput>
{
    private readonly IBus _bus;

    public EnqueueOnCloudApiBusinessBalanceResynchronization(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueOnCloudApiBusinessBalanceResynchronizationInput
    {
        [JsonProperty("facebook_business_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public EnqueueOnCloudApiBusinessBalanceResynchronizationInput(string facebookBusinessId)
        {
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class EnqueueOnCloudApiBusinessBalanceResynchronizationOutput
    {
    }

    public async Task<EnqueueOnCloudApiBusinessBalanceResynchronizationOutput> F(
        EnqueueOnCloudApiBusinessBalanceResynchronizationInput
            enqueueOnCloudApiBusinessBalanceResynchronizationInput)
    {
        await _bus.Publish(
            new OnCloudApiBusinessBalanceResynchronizationEvent(
                enqueueOnCloudApiBusinessBalanceResynchronizationInput.FacebookBusinessId));
        return new EnqueueOnCloudApiBusinessBalanceResynchronizationOutput();
    }
}