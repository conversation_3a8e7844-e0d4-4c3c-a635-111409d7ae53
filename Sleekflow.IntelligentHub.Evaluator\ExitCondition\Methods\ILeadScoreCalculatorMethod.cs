using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

namespace Sleekflow.IntelligentHub.Evaluator.ExitCondition.Methods;

public interface IExitConditionMethod<TOutput>
{
    string MethodName { get; }

    Task<TOutput> CompleteAsync(
        ChatMessageContent[] questionContexts,
        List<Models.Companies.CompanyAgentConfigs.Actions.ExitCondition> exitConditions);
}