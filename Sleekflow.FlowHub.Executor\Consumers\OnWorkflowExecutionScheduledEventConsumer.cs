﻿using MassTransit;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowExecutionScheduledEventConsumerDefinition : ConsumerDefinition<OnWorkflowExecutionScheduledEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowExecutionScheduledEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowExecutionScheduledEventConsumer
    : IConsumer<OnWorkflowExecutionScheduledEvent>, IHighTrafficConsumer<OnWorkflowExecutionScheduledEvent>
{
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public OnWorkflowExecutionScheduledEventConsumer(
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
    }

    public async Task Consume(ConsumeContext<OnWorkflowExecutionScheduledEvent> context)
    {
        var @event = context.Message;

        var stateId = @event.StateId;
        var stateIdentity = @event.StateIdentity;
        var workflowExecutionReasonCode = @event.WorkflowExecutionReasonCode;
        var workflowType = @event.WorkflowType;
        var scheduledBy = @event.ScheduledBy;
        var scheduledAt = @event.ScheduledAt;

        await _workflowExecutionService.CreateWorkflowExecutionAsync(
            stateId,
            stateIdentity,
            WorkflowExecutionStatuses.Scheduled,
            workflowExecutionReasonCode,
            0,
            workflowType,
            scheduledBy,
            scheduledAt);
    }
}