﻿using Pulumi;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Storage;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;

namespace Sleekflow.Infras.Components.ShareHub;

public class ShareHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly ShareHubDb.ShareHubDbOutput _shareHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly MySynapse.SynapseOutput _synapseOutput;
    private readonly GcpConfig _gcpConfig;

    public ShareHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        ShareHubDb.ShareHubDbOutput shareHubDbOutput,
        MyConfig myConfig,
        MySynapse.SynapseOutput synapseOutput,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _shareHubDbOutput = shareHubDbOutput;
        _myConfig = myConfig;
        _synapseOutput = synapseOutput;
        _gcpConfig = gcpConfig;
    }

    public List<App.ContainerApp> InitShareHub()
    {
        var imageStorageAccount = InitImageStorageAccount();
        var fileStorageAccount = InitFileStorageAccount();
        var qrImageStorageAccount = InitQrImageStorageAccount();

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.ShareHub),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.ShareHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.ShareHub));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 3 : 1,
                            MaxReplicas = 20,
                            Rules = new List<string>
                                {
                                    "create-blob-download-sas-urls",
                                    "create-blob-upload-sas-urls",
                                    "shorten-link",
                                }
                                .Select(
                                    queueName => new App.Inputs.ScaleRuleArgs
                                    {
                                        Name = $"azure-servicebus-{queueName}",
                                        Custom = new App.Inputs.CustomScaleRuleArgs
                                        {
                                            Type = "azure-servicebus",
                                            Metadata = new InputMap<string>
                                            {
                                                {
                                                    "queueName", queueName
                                                },
                                                {
                                                    "messageCount", "8"
                                                },
                                            },
                                            Auth = new InputList<App.Inputs.ScaleRuleAuthArgs>
                                            {
                                                new App.Inputs.ScaleRuleAuthArgs
                                                {
                                                    TriggerParameter = "connection",
                                                    SecretRef = "service-bus-keda-conn-str-secret"
                                                },
                                            }
                                        }
                                    })
                                .Concat(
                                    new List<App.Inputs.ScaleRuleArgs>()
                                    {
                                        new App.Inputs.ScaleRuleArgs
                                        {
                                            Name = "http",
                                            Http = new App.Inputs.HttpScaleRuleArgs
                                            {
                                                Metadata = new InputMap<string>
                                                {
                                                    {
                                                        "concurrentRequests", "80"
                                                    }
                                                }
                                            }
                                        }
                                    })
                                .ToList(),
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-sh-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 1, Memory = "2.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80
                                        },
                                        InitialDelaySeconds = 8,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80
                                        },
                                        InitialDelaySeconds = 8,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },

                                        #region ShareHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_SHARE_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_shareHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_SHARE_HUB_DB_KEY", Value = _shareHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_SHARE_HUB_DB_DATABASE_ID",
                                            Value = _shareHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.ShareHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "QR_CODE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                qrImageStorageAccount.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "IMAGE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                imageStorageAccount.Name),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FILE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                fileStorageAccount.Name),
                                        },

                                        #endregion

                                        #region DefaultDomainConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DEFAULT_DOMAIN_PROVIDER_ID",
                                            Value = _myConfig.Name.ToLower() switch
                                            {
                                                "dev" => "dev.sf.chat",
                                                "staging" => "staging.sf.chat",
                                                "production" => "l.sf.chat",
                                            }
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DEFAULT_DOMAIN_PROFILE_NAME", Value = "sleekflow"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DEFAULT_DOMAIN_RESOURCE_GROUP_NAME",
                                            Value = _myConfig.Name.ToLower() switch
                                            {
                                                "dev" => "sleekflow-resource-group-devf9af1d41",
                                                "staging" => "sleekflow-resource-group-staging7417fc06",
                                                "production" => "sleekflow-resource-group-production853b96c8",
                                            }
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DEFAULT_DOMAIN_SUBSCRIPTION_ID",
                                            Value = "c19c9b56-93e9-4d4c-bc81-838bd3f72ad6"
                                        },

                                        #endregion

                                        #region AzureFrontDoorConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURE_AD_TENANT_ID", Value = "d66fa1cc-347d-42e9-9444-19c5fd0bbcce"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            // https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Credentials/appId/19956950-83e2-4f21-98fc-0cc3942416f4
                                            Name = "AZURE_AD_CLIENT_ID", Value = "19956950-83e2-4f21-98fc-0cc3942416f4"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AZURE_AD_CLIENT_SECRET",
                                            Value = "****************************************"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AFD_SUBSCRIPTION_ID", Value = "c19c9b56-93e9-4d4c-bc81-838bd3f72ad6"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AFD_RESOURCE_GROUP_NAME", Value = _resourceGroup.Name
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "AFD_PROFILE_NAME", Value = "sleekflow"
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region AnalyticsConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ANALYTICS_STORAGE_TYPE", Value = "synapse"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SYNAPSE_SERVERLESS_SQL_POOL_CONN_STR",
                                            Value = Output
                                                .Tuple(
                                                    _synapseOutput.WorkspaceName,
                                                    _synapseOutput.SqlAdministratorLogin,
                                                    _synapseOutput.SqlAdministratorLoginPassword)
                                                .Apply(
                                                    t =>
                                                        $"Server=tcp:{t.Item1}-ondemand.sql.azuresynapse.net,1433;Initial Catalog=master;Persist Security Info=False;User ID={t.Item2};Password={t.Item3};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;")
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SYNAPSE_LINK_COSMOS_DB_CONN_STR",
                                            Value = Output
                                                .Tuple(
                                                    _shareHubDbOutput.AccountName,
                                                    _resourceGroup.Location,
                                                    _shareHubDbOutput.AccountKey)
                                                .Apply(
                                                    t =>
                                                        $"account={t.Item1};database={_shareHubDbOutput.DatabaseId};region={t.Item2};key={t.Item3};")
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.ShareHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }

    private Storage.StorageAccount InitQrImageStorageAccount()
    {
        var qrImageRandomId = new Random.RandomId(
            "sleekflow-qr-code-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "share hub qr code storage"
                    }
                },
            });
        var qrImageStorageAccount = new Storage.StorageAccount(
            "sleekflow-qr-image-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-share-hub-qr-image-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = qrImageRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var _ = new Storage.BlobContainer(
            "sleekflow-qr-image-container",
            new Storage.BlobContainerArgs
            {
                AccountName = qrImageStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "qr-image-container"
            },
            new CustomResourceOptions
            {
                Parent = qrImageStorageAccount
            });
        return qrImageStorageAccount;
    }

    private Storage.StorageAccount InitFileStorageAccount()
    {
        var fileRandomId = new Random.RandomId(
            "sleekflow-sh-file-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "share hub file storage"
                    }
                },
            });
        var fileStorageAccount = new Storage.StorageAccount(
            "sleekflow-sh-file-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-share-hub-file-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = fileRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var fileStorageAccountBlobServiceProperties = new Storage.BlobServiceProperties(
            "sleekflow-sh-file-storage-account-table-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = fileStorageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*"
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.GET),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.HEAD),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.POST),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.OPTIONS),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.PUT)
                            },
                            AllowedOrigins = new[]
                            {
                                "*",
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                    },
                },
                BlobServicesName = "default",
            });

        var __ = new Storage.BlobContainer(
            "sleekflow-sh-file-container",
            new Storage.BlobContainerArgs
            {
                AccountName = fileStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "file-container"
            },
            new CustomResourceOptions
            {
                Parent = fileStorageAccount
            });
        return fileStorageAccount;
    }

    private Storage.StorageAccount InitImageStorageAccount()
    {
        var imageRandomId = new Random.RandomId(
            "sleekflow-sh-image-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "share hub image storage"
                    }
                },
            });
        var imageStorageAccount = new Storage.StorageAccount(
            "sleekflow-sh-image-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-share-hub-image-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = imageRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var imageStorageAccountBlobServiceProperties = new Storage.BlobServiceProperties(
            "sleekflow-sh-image-storage-account-table-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = imageStorageAccount.Name,
                Cors = new Storage.Inputs.CorsRulesArgs
                {
                    CorsRules = new[]
                    {
                        new Storage.Inputs.CorsRuleArgs
                        {
                            AllowedHeaders = new[]
                            {
                                "*",
                            },
                            AllowedMethods = new[]
                            {
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.GET),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.HEAD),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.POST),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.OPTIONS),
                                Union<string, AllowedMethods>.FromT1(AllowedMethods.PUT)
                            },
                            AllowedOrigins = new[]
                            {
                                "*",
                            },
                            ExposedHeaders = new[]
                            {
                                "x-ms-meta-*",
                            },
                            MaxAgeInSeconds = 100,
                        },
                    },
                },
                BlobServicesName = "default",
            });

        var _ = new Storage.BlobContainer(
            "sleekflow-sh-image-container",
            new Storage.BlobContainerArgs
            {
                AccountName = imageStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "image-container"
            },
            new CustomResourceOptions
            {
                Parent = imageStorageAccount
            });
        return imageStorageAccount;
    }
}
