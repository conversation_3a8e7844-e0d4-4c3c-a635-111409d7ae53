﻿using System.Text;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

internal class BackUp : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private List<string>? _containerIds;

    public BackUp(
        DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Back Up";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerIds = Prompt.MultiSelect(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerIds = selectedContainerIds.ToList();
    }

    public async Task ExecuteAsync()
    {
        foreach (var containerId in _containerIds!)
        {
            var count = await BackUpObjectsAsync(containerId);
            Console.WriteLine($"Completed {containerId} for {count} objects");
        }
    }

    private async Task<int> BackUpObjectsAsync(
        string containerId)
    {
        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerId);

        var i = 0;

        await using TextWriter textWriter = new StreamWriter(container.Id + ".json", false, Encoding.UTF8, 1024 * 32);
        await foreach (var dictionary in CosmosUtils.GetObjectsAsync(container))
        {
            var json = JsonConvert.SerializeObject(dictionary, JsonConfig.DefaultJsonSerializerSettings);

            await textWriter.WriteLineAsync(json);

            Interlocked.Increment(ref i);

            if (i % 1000 == 0)
            {
                Console.WriteLine("In Progress " + i);
            }
        }

        return i;
    }
}