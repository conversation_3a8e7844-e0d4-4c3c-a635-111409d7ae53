using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;

public class ShopifyCartDto : CartDto
{
    [JsonProperty("shopify_cart_external_integration_info")]
    public ShopifyCartExternalIntegrationInfo ShopifyCartExternalIntegrationInfo { get; set; }

    [JsonConstructor]
    public ShopifyCartDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        List<CartLineItemDto> lineItems,
        Discount? cartDiscount,
        string cartStatus,
        ShopifyCartExternalIntegrationInfo shopifyCartExternalIntegrationInfo)
        : base(
            id,
            sleekflowCompanyId,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt,
            storeId,
            lineItems,
            cartDiscount,
            cartStatus)
    {
        ShopifyCartExternalIntegrationInfo = shopifyCartExternalIntegrationInfo;
    }

    public ShopifyCartDto(
        Cart cart,
        Dictionary<string, ProductVariant> idToProductVariantDict,
        Dictionary<string, Product> idToProductDict)
        : this(
            cart.Id,
            cart.SleekflowCompanyId,
            cart.CreatedBy,
            cart.UpdatedBy,
            cart.CreatedAt,
            cart.UpdatedAt,
            cart.StoreId,
            cart.LineItems
                .Where(
                    li =>
                        idToProductVariantDict.ContainsKey(li.ProductVariantId)
                        && idToProductDict.ContainsKey(idToProductVariantDict[li.ProductVariantId].ProductId))
                .Select(
                    li =>
                        new CartLineItemDto(
                            li,
                            new ProductVariantDto(
                                idToProductVariantDict[li.ProductVariantId],
                                idToProductDict[idToProductVariantDict[li.ProductVariantId].ProductId])))
                .ToList(),
            cart.CartDiscount,
            cart.CartStatus,
            (cart.CartExternalIntegrationInfo as ShopifyCartExternalIntegrationInfo)!)
    {
    }
}