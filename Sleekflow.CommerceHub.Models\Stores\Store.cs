﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Stores;

[ContainerId(ContainerNames.Store)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class Store : AuditEntity, IHasRecordStatuses, IHasMetadata
{
    public const string PropertyNameNames = "names";
    public const string PropertyNameDescriptions = "descriptions";
    public const string PropertyNameUrl = "url";
    public const string PropertyNamePlatformData = "platform_data";
    public const string PropertyNameIsViewEnabled = "is_view_enabled";
    public const string PropertyNameIsPaymentEnabled = "is_payment_enabled";
    public const string PropertyNameLanguages = "languages";
    public const string PropertyNameCurrencies = "currencies";
    public const string PropertyNameTemplateDict = "template_dict";
    public const string PropertyNameStoreIntegrationExternalConfig = "store_integration_external_config";
    public const string PropertyNameSubscriptionStatus = "subscription_status";

    [JsonProperty(PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(PropertyNameIsViewEnabled)]
    public bool IsViewEnabled { get; set; }

    [JsonProperty(PropertyNameIsPaymentEnabled)]
    public bool IsPaymentEnabled { get; set; }

    [JsonProperty(PropertyNameLanguages)]
    public List<Language> Languages { get; set; }

    [JsonProperty(PropertyNameCurrencies)]
    public List<Currency> Currencies { get; set; }

    [JsonProperty(PropertyNameTemplateDict)]
    public StoreTemplateDict TemplateDict { get; set; }

    [JsonProperty(PropertyNameStoreIntegrationExternalConfig, TypeNameHandling = TypeNameHandling.Objects)]
    public StoreIntegrationExternalConfig? StoreIntegrationExternalConfig { get; set; }

    [JsonProperty(PropertyNameSubscriptionStatus, TypeNameHandling = TypeNameHandling.Objects)]
    public StoreSubscriptionStatus? SubscriptionStatus { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public Store(
        string id,
        string sleekflowCompanyId,
        List<Multilingual> names,
        List<Description> descriptions,
        string? url,
        PlatformData platformData,
        bool isViewEnabled,
        bool isPaymentEnabled,
        List<Language> languages,
        List<Currency> currencies,
        StoreIntegrationExternalConfig? storeIntegrationExternalConfig,
        StoreSubscriptionStatus? subscriptionStatus,
        List<string> recordStatuses,
        Dictionary<string, object?> metadata,
        SleekflowStaff createdBy,
        SleekflowStaff updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        StoreTemplateDict templateDict)
        : base(
            id,
            sysTypeName: SysTypeNames.Store,
            createdAt: createdAt,
            updatedAt: updatedAt,
            sleekflowCompanyId: sleekflowCompanyId,
            createdBy: createdBy,
            updatedBy: updatedBy)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Names = names;
        Descriptions = descriptions;
        Url = url;
        PlatformData = platformData;
        IsViewEnabled = isViewEnabled;
        IsPaymentEnabled = isPaymentEnabled;
        Languages = languages;
        Currencies = currencies;
        StoreIntegrationExternalConfig = storeIntegrationExternalConfig;
        SubscriptionStatus = subscriptionStatus;
        RecordStatuses = recordStatuses;
        Metadata = metadata;
        TemplateDict = templateDict;
    }
}

public class StoreTemplateDict
{
    [Required]
    [ValidateArray]
    [JsonProperty("message_preview_templates")]
    public List<Multilingual> MessagePreviewTemplates { get; set; }

    [JsonConstructor]
    public StoreTemplateDict(List<Multilingual> messagePreviewTemplates)
    {
        MessagePreviewTemplates = messagePreviewTemplates;
    }
}