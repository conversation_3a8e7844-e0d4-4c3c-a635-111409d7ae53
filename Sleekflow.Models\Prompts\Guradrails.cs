using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.Prompts;

public class Guardrail
{
    [JsonProperty("title")]
    [Required]
    public string Title { get; set; }

    [JsonProperty("observe_for")]
    [Required]
    public string ObserveFor { get; set; }

    [JsonProperty("how_to_react")]
    [Required]
    public string HowToReact { get; set; }

    [JsonConstructor]
    public Guardrail(
        string title,
        string observeFor,
        string howToReact)
    {
        Title = title;
        ObserveFor = observeFor;
        HowToReact = howToReact;
    }
}