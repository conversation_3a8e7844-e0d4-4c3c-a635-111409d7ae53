using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Events;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.RecommendedReplies;

[TriggerGroup(ControllerNames.RecommendedReplies)]
public partial class RecommendReplyStreaming
    : ITrigger<RecommendReplyStreaming.RecommendReplyStreamingInput,
        RecommendReplyStreaming.RecommendReplyStreamingOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubConcurrentUsageService _intelligentHubConcurrentUsageService;
    private readonly IBus _bus;

    public RecommendReplyStreaming(
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubConcurrentUsageService intelligentHubConcurrentUsageService,
        IBus bus)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _intelligentHubConcurrentUsageService = intelligentHubConcurrentUsageService;
        _bus = bus;
    }

    public class RecommendReplyStreamingInput : IHasSleekflowCompanyId
    {
        [Required]
        [ValidateArray]
        [JsonProperty("conversation_context")]
        public List<SfChatEntry> ConversationContext { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
        [ValidateObject]
        public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

        [Required]
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [Required]
        [JsonProperty("client_request_id")]
        public string ClientRequestId { get; set; }

        [JsonConstructor]
        public RecommendReplyStreamingInput(
            List<SfChatEntry> conversationContext,
            string sleekflowCompanyId,
            IntelligentHubUsageFilter? intelligentHubUsageFilter,
            AuditEntity.SleekflowStaff? createdBy,
            string sessionId,
            string clientRequestId)
        {
            ConversationContext = conversationContext;
            SleekflowCompanyId = sleekflowCompanyId;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
            CreatedBy = createdBy;
            SessionId = sessionId;
            ClientRequestId = clientRequestId;
        }
    }

    public class RecommendReplyStreamingOutput
    {
        [JsonConstructor]
        public RecommendReplyStreamingOutput()
        {
        }
    }

    public async Task<RecommendReplyStreamingOutput> F(RecommendReplyStreamingInput recommendReplyStreamingInput)
    {
        await ValidateUsageLimit(recommendReplyStreamingInput);
        await IncrementConcurrentUsageCount(recommendReplyStreamingInput);

        await _bus.Publish(new RecommendReplyStreamingEndpointEvent(recommendReplyStreamingInput));

        return new RecommendReplyStreamingOutput();
    }

    private async Task ValidateUsageLimit(RecommendReplyStreamingInput recommendReplyStreamingInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(
                recommendReplyStreamingInput.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null ||
                                   await _intelligentHubUsageService.IsUsageLimitExceeded(
                                       recommendReplyStreamingInput.SleekflowCompanyId,
                                       new Dictionary<string, int>
                                       {
                                           {
                                               PriceableFeatures.AiFeaturesTotalUsage,
                                               _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                                                   intelligentHubConfig,
                                                   PriceableFeatures.AiFeaturesTotalUsage)
                                           }
                                       },
                                       recommendReplyStreamingInput.IntelligentHubUsageFilter);

        if (isUsageLimitExceeded)
        {
            throw new SfUserFriendlyException("Cannot find IntelligentHubConfig or exceed max usage limit.");
        }
    }

    private async Task IncrementConcurrentUsageCount(RecommendReplyStreamingInput recommendReplyStreamingInput)
    {
        await _intelligentHubConcurrentUsageService.TryIncrementConcurrentUsageAsync(
            recommendReplyStreamingInput.SleekflowCompanyId);
    }

    private static string CleanAnswer(string answer)
    {
        return MyRegex().Replace(answer, string.Empty);
    }

    [GeneratedRegex(@"\[source\]", RegexOptions.IgnoreCase, "en-HK")]
    private static partial Regex MyRegex();
}