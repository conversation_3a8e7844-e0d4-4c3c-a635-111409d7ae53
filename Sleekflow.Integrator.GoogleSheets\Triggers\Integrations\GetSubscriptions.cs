﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.GoogleSheets.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetSubscriptions : ITrigger
{
    private readonly IGoogleSheetsSubscriptionService _googleSheetsSubscriptionService;

    public GetSubscriptions(
        IGoogleSheetsSubscriptionService googleSheetsSubscriptionService)
    {
        _googleSheetsSubscriptionService = googleSheetsSubscriptionService;
    }

    public class GetSubscriptionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("typed_ids")]
        public List<TypedId> TypedIds { get; set; }

        [JsonConstructor]
        public GetSubscriptionsInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<TypedId> typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            TypedIds = typedIds;
        }
    }

    public class GoogleSheetsSubscriptionDto : IHasSleekflowCompanyId
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonProperty("interval")]
        public int Interval { get; set; }

        [JsonProperty("is_flows_based")]
        public bool? IsFlowsBased { get; set; }

        [JsonProperty("connection_id")]
        public string? ConnectionId { get; set; }

        [JsonProperty("typed_ids")]
        public List<TypedId>? TypedIds { get; set; }

        [JsonConstructor]
        public GoogleSheetsSubscriptionDto(
            GoogleSheetsSubscription subscription)
        {
            Id = subscription.Id;
            SleekflowCompanyId = subscription.SleekflowCompanyId;
            EntityTypeName = subscription.EntityTypeName;
            Interval = subscription.Interval;
            IsFlowsBased = subscription.IsFlowsBased;
            ConnectionId = subscription.ConnectionId;
            TypedIds = subscription.TypedIds;
        }
    }

    public class GetSubscriptionsOutput
    {
        [JsonProperty("subscriptions")]
        [Required]
        public List<GoogleSheetsSubscriptionDto> Subscriptions { get; set; }

        [JsonConstructor]
        public GetSubscriptionsOutput(
            List<GoogleSheetsSubscriptionDto> subscriptions)
        {
            Subscriptions = subscriptions;
        }
    }

    public async Task<GetSubscriptionsOutput> F(
        GetSubscriptionsInput getSubscriptionsInput)
    {
        var subscriptions =
            await _googleSheetsSubscriptionService.GetSubscriptionsAsync(
                getSubscriptionsInput.SleekflowCompanyId,
                getSubscriptionsInput.ConnectionId,
                getSubscriptionsInput.EntityTypeName,
                getSubscriptionsInput.TypedIds);

        return new GetSubscriptionsOutput(subscriptions
            .Select(o => new GoogleSheetsSubscriptionDto(o))
            .ToList());
    }
}