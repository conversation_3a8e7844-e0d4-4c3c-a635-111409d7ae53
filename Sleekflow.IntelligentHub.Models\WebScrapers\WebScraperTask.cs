﻿using System.ComponentModel;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.WebScrapers;

public interface IWebScraperTask
{
    public string ApifyTaskId { get; set; }

    public string Code { get; set; }

    public WebScraperSetting WebScraperSetting { get; set; }
}

public class WebScraperTask : IWebScraperTask, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameWebScraperTask = "web_scraper_task";
    public const string PropertyNameApifyTaskId = "apify_task_id";
    public const string PropertyNameDisplayName = "display_name";

    [JsonProperty(PropertyNameApifyTaskId)]
    public string ApifyTaskId { get; set; }

    [JsonProperty(PropertyName = PropertyNameDisplayName)]
    [Description("Display name in Sleekflow")]
    public string DisplayName { get; set; }

    [JsonProperty(PropertyName = "code")]
    [Description("WebScraperId + TaskName lower case. Mapping to Apify Task Code and Apify Task Name")]
    public string Code { get; set; }

    [JsonProperty(PropertyName = "created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(PropertyName = "updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(PropertyName = WebScraperSetting.PropertyNameWebScraperSetting)]
    public WebScraperSetting WebScraperSetting { get; set; }

    [JsonProperty(PropertyName = WebScraperTaskScheduler.PropertyNameWebScraperTaskScheduler)]
    public WebScraperTaskScheduler? WebScraperTaskScheduler { get; set; }

    [JsonConstructor]
    public WebScraperTask(
        string apifyTaskId,
        string displayName,
        string code,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        WebScraperSetting webScraperSetting,
        WebScraperTaskScheduler? webScraperWebScraperTaskScheduler)
    {
        ApifyTaskId = apifyTaskId;
        DisplayName = displayName;
        Code = code;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        WebScraperSetting = webScraperSetting;
        WebScraperTaskScheduler = webScraperWebScraperTaskScheduler;
    }
}