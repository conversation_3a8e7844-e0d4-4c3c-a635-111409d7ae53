using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class UpdateIntelligentHubConfigUsageLimitOffsets
    : ITrigger<UpdateIntelligentHubConfigUsageLimitOffsets.UpdateIntelligentHubConfigUsageLimitOffsetsInput,
        UpdateIntelligentHubConfigUsageLimitOffsets.UpdateIntelligentHubConfigUsageLimitOffsetsOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public UpdateIntelligentHubConfigUsageLimitOffsets(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class UpdateIntelligentHubConfigUsageLimitOffsetsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IntelligentHubConfig.PropertyNameUsageLimitOffsets)]
        [Validations.ValidateObject]
        public Dictionary<string, int> UsageLimitOffsets { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateIntelligentHubConfigUsageLimitOffsetsInput(
            string sleekflowCompanyId,
            Dictionary<string, int> usageLimitOffsets,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UsageLimitOffsets = usageLimitOffsets;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateIntelligentHubConfigUsageLimitOffsetsOutput
    {
        [JsonProperty("intelligent_hub_config")]
        public IntelligentHubConfig IntelligentHubConfig { get; set; }

        [JsonConstructor]
        public UpdateIntelligentHubConfigUsageLimitOffsetsOutput(IntelligentHubConfig intelligentHubConfig)
        {
            IntelligentHubConfig = intelligentHubConfig;
        }
    }

    public async Task<UpdateIntelligentHubConfigUsageLimitOffsetsOutput> F(
        UpdateIntelligentHubConfigUsageLimitOffsetsInput updateIntelligentHubConfigUsageLimitOffsetsInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(
                updateIntelligentHubConfigUsageLimitOffsetsInput.SleekflowCompanyId);

        if (intelligentHubConfig is null)
        {
            throw new SfUserFriendlyException("IntelligentHub not enrolled yet.");
        }

        var updatedBy = string.IsNullOrWhiteSpace(updateIntelligentHubConfigUsageLimitOffsetsInput.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                updateIntelligentHubConfigUsageLimitOffsetsInput.SleekflowStaffId,
                updateIntelligentHubConfigUsageLimitOffsetsInput.SleekflowStaffTeamIds);

        intelligentHubConfig = await _intelligentHubConfigService.UpdateIntelligentHubConfigUsageLimitOffsetsAsync(
            intelligentHubConfig.Id,
            intelligentHubConfig.SleekflowCompanyId,
            updateIntelligentHubConfigUsageLimitOffsetsInput.UsageLimitOffsets,
            updatedBy);

        return new UpdateIntelligentHubConfigUsageLimitOffsetsOutput(intelligentHubConfig);
    }
}