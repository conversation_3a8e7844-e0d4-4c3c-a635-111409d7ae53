using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;

namespace Sleekflow.Integrator.Hubspot.HubspotQueues;

public interface IHubspotQueueService
{
    Task<List<(string SleekflowCompanyId, string EntityTypeName)>> GetDistinctCompanyIdAndEntityTypeNameTuples();

    Task EnqueueItemAsync(
        string sleekflowCompanyId,
        HubspotQueueItem queueItem);

    Task<List<HubspotQueueItem>> PeekItemsAsync(
        int limit,
        string sleekflowCompanyId,
        string objectOperation,
        string entityTypeName);

    Task DeleteItemsAsync(List<string> ids, string sleekflowCompanyId);
}

public class HubspotQueueService : IHubspotQueueService, ISingletonService
{
    private readonly IHubspotQueueItemRepository _hubspotQueueItemRepository;

    public HubspotQueueService(
        IHubspotQueueItemRepository hubspotQueueItemRepository)
    {
        _hubspotQueueItemRepository = hubspotQueueItemRepository;
    }

    public async Task EnqueueItemAsync(
        string sleekflowCompanyId,
        HubspotQueueItem queueItem)
    {
        await _hubspotQueueItemRepository.UpsertAsync(
            queueItem,
            sleekflowCompanyId);
    }

    public async Task DeleteItemsAsync(List<string> ids, string sleekflowCompanyId)
    {
        await _hubspotQueueItemRepository.DeleteAsync(ids, sleekflowCompanyId);
    }

    public async Task<List<HubspotQueueItem>> PeekItemsAsync(
        int limit,
        string sleekflowCompanyId,
        string objectOperation,
        string entityTypeName)
    {
        var hubspotQueueItems = await _hubspotQueueItemRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId AND c.object_operation = @objectOperation AND c.entity_type_name = @entityTypeName " +
                    "ORDER BY c.created_at " +
                    "OFFSET 0 LIMIT @limit")
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@objectOperation", objectOperation)
                .WithParameter("@entityTypeName", entityTypeName)
                .WithParameter("@limit", limit),
            limit);

        return hubspotQueueItems;
    }

    public async Task<List<(string SleekflowCompanyId, string EntityTypeName)>>
        GetDistinctCompanyIdAndEntityTypeNameTuples()
    {
        var tuples = await _hubspotQueueItemRepository.GetDistinctCompanyIdAndEntityTypeNameTuplesAsync();

        return tuples.ToList();
    }
}