/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {

    const clientWebApps = ['sleekflow-client-web-app', 'sleekflow-client-web-v2-app'];

    const domain = event.secrets.domain;
    const clientId = event.secrets.client_id;
    const isUserRequireMFAWebhookUrl = event.secrets.requires_mfa_webhook;
    const issuer = event.secrets.issuer;
    const audience = event.secrets.audience;

    const axios = require("axios").default;

    const getIsMFARequired = async (user, client_name) => {
        const jwt = require('jwt-encode');

        const data = {
            iss: issuer,
            aud: audience,
            domain: domain,
            client_id: clientId
        }

        const tokenSecret = issuer + audience + "+wsadz4gI_3DUXI8P";
        const token = jwt(data, tokenSecret);

        const options = {
            method: 'POST',
            url: isUserRequireMFAWebhookUrl,
            params: {connection: event.connection.strategy, token: token},
            headers: {'content-type': 'application/json'},
            data: {
                auth0_event_user: user,
                auth0_client_name: client_name,
                connection_strategy: event.connection.strategy,
                connection_name: event.connection.name
            }
        };

        const response = await axios.request(options);

        return response;
    };

    if (
        event
        && event.transaction
        && event.transaction.protocol == 'oauth2-refresh-token') {
        return;
    }

    if (
        event
        && event.client
        && event.client.name == "sleekflow-api") {
        return;
    }

    const authMethods = event.authentication?.methods || [];
    const completedMfa = !!authMethods.find((method) => method.name === 'mfa');

    if (
        completedMfa === true
        && clientWebApps.some(x => x === event.client.name) === true) {
        // Skip 2FA when the MFA is done, and the application is one of client web apps.
        return;
    }

    if (
        event
        && event.client
        && event.client.name == "sleekflow-client-powerflow-app") {
        api.multifactor.enable('any', {allowRememberBrowser: true});
    }

    const getIsMfaRequiredResponse = await getIsMFARequired(event.user, event.client.name);

    if (
        getIsMfaRequiredResponse
        && getIsMfaRequiredResponse.status === 200
        && getIsMfaRequiredResponse.data
        && getIsMfaRequiredResponse.data.is_mfa_required) {
        var allow_remember_browser =
            getIsMfaRequiredResponse.data?.allow_remember_browser ?? true;
        api.multifactor.enable('any', {allowRememberBrowser: allow_remember_browser});
    }
};


/**
 * Handler that will be invoked when this action is resuming after an external redirect. If your
 * onExecutePostLogin function does not perform a redirect, this function can be safely ignored.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
// exports.onContinuePostLogin = async (event, api) => {
// };
