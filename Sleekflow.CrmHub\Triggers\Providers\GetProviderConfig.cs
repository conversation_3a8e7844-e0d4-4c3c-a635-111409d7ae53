﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderConfig : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;

    public GetProviderConfig(
        IProviderConfigService providerConfigService)
    {
        _providerConfigService = providerConfigService;
    }

    public class GetProviderConfigInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public GetProviderConfigInput(
            string sleekflowCompanyId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
        }
    }

    public class GetProviderConfigOutput
    {
        [JsonProperty("provider_config")]
        public ProviderConfig ProviderConfig { get; set; }

        [JsonConstructor]
        public GetProviderConfigOutput(ProviderConfig providerConfig)
        {
            ProviderConfig = providerConfig;
        }
    }

    public async Task<GetProviderConfigOutput> F(
        GetProviderConfigInput getProviderConfigInput)
    {
        var providerConfig = await _providerConfigService.GetProviderConfigAsync(
            getProviderConfigInput.SleekflowCompanyId,
            getProviderConfigInput.ProviderName);

        return new GetProviderConfigOutput(providerConfig);
    }
}