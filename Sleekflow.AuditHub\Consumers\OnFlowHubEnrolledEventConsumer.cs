using MassTransit;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;
using Sleekflow.AuditHub.SystemAuditLogs;
using Sleekflow.Ids;
using Sleekflow.Models.Events;

namespace Sleekflow.AuditHub.Consumers;

public class OnFlowHubEnrolledEventConsumerDefinition
    : ConsumerDefinition<OnFlowHubEnrolledEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnFlowHubEnrolledEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnFlowHubEnrolledEventConsumer : IConsumer<OnFlowHubEnrolledEvent>
{
    private readonly IIdService _idService;
    private readonly ISystemAuditLogService _systemAuditLogService;

    public OnFlowHubEnrolledEventConsumer(
        IIdService idService,
        ISystemAuditLogService systemAuditLogService)
    {
        _idService = idService;
        _systemAuditLogService = systemAuditLogService;
    }

    public async Task Consume(ConsumeContext<OnFlowHubEnrolledEvent> context)
    {
        var @event = context.Message;

        var id = _idService.GetId("SystemAuditLog");
        await _systemAuditLogService.CreateSystemAuditLogAsync(
            new SystemAuditLog(
                id,
                @event.SleekflowCompanyId,
                @event.SleekflowStaffId,
                null,
                SystemAuditLogTypes.FlowBuilderEnrolled,
                JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                    JsonConvert.SerializeObject(
                        new FlowBuilderEnrolledSystemLogData())),
                DateTimeOffset.UtcNow,
                null));
    }
}