using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class CreateWebsiteDocument
    : ITrigger<CreateWebsiteDocument.CreateWebsiteDocumentInput,
        CreateWebsiteDocument.CreateWebsiteDocumentOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IWebsiteDocumentService _websiteDocumentService;

    public CreateWebsiteDocument(
        ISleekflowAuthorizationContext authorizationContext,
        IWebsiteDocumentService websiteDocumentService)
    {
        _authorizationContext = authorizationContext;
        _websiteDocumentService = websiteDocumentService;
    }

    public class CreateWebsiteDocumentInput
    {
        [JsonProperty("web_crawling_session_id")]
        [Required]
        public string WebCrawlingSessionId { get; set; }

        [JsonProperty("base_url")]
        [Required]
        public string BaseUrl { get; set; }

        [JsonProperty("base_url_title")]
        public string? BaseUrlTitle { get; set; }

        [JsonProperty("selected_urls")]
        [Required]
        [ValidateArray]
        public List<string> SelectedUrls { get; set; }

        [JsonProperty("agent_ids")]
        [Required]
        [ValidateArray]
        public List<string> AgentIds { get; set; }

        [JsonConstructor]
        public CreateWebsiteDocumentInput(
            string webCrawlingSessionId,
            string baseUrl,
            string? baseUrlTitle,
            List<string> selectedUrls,
            List<string> agentIds)
        {
            WebCrawlingSessionId = webCrawlingSessionId;
            BaseUrl = baseUrl;
            BaseUrlTitle = baseUrlTitle;
            SelectedUrls = selectedUrls;
            AgentIds = agentIds;
        }
    }

    // Empty output class as per specification {}
    public class CreateWebsiteDocumentOutput
    {
        [JsonProperty("id")]
        [Required]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateWebsiteDocumentOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateWebsiteDocumentOutput> F(
        CreateWebsiteDocumentInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;
        var staffId = _authorizationContext.SleekflowStaffId!;

        var documentId = await _websiteDocumentService.CreateWebsiteDocumentAsync(
            sleekflowCompanyId,
            input.WebCrawlingSessionId,
            input.BaseUrl,
            input.BaseUrlTitle,
            input.SelectedUrls,
            input.AgentIds,
            staffId);

        return new CreateWebsiteDocumentOutput(documentId);
    }
}