﻿using Sleekflow.Events;

namespace Sleekflow.CrmHub.Models.Events;

public class OnObjectPersistedEvent : IEvent
{
    /// <summary>
    /// The property stores the latest snapshot of an object.
    /// </summary>
    public Dictionary<string, object?> Object { get; set; }

    /// <summary>
    /// The property stores the event belongs to which sleekflow company.
    /// </summary>
    public string SleekflowCompanyId { get; set; }

    public string ObjectId { get; set; }

    public string EntityTypeName { get; set; }

    public OnObjectPersistedEvent(
        Dictionary<string, object?> @object,
        string sleekflowCompanyId,
        string objectId,
        string entityTypeName)
    {
        Object = @object;
        SleekflowCompanyId = sleekflowCompanyId;
        ObjectId = objectId;
        EntityTypeName = entityTypeName;
    }
}