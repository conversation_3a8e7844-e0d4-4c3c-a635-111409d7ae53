using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Sleekflow.InternalIntegrationHub.Models.Events;

namespace Sleekflow.InternalIntegrationHub.Workers.Triggers.Integrations;

public class OmniHrEmployeeInfoToNetSuiteIntegrationTrigger
{
    private readonly IBus _bus;

    public OmniHrEmployeeInfoToNetSuiteIntegrationTrigger(IBus bus)
    {
        _bus = bus;
    }

    [Function("OmniHrEmployeeInfoToNetSuiteIntegrationTrigger")]
    public Task RunAsync(
        [TimerTrigger("0 0 0 1 * *")]
        TimerInfo timerInfo)
    {
        return _bus.Publish(
            new OmniHrEmployeeInfoToNetSuiteIntegrationEvent());
    }
}