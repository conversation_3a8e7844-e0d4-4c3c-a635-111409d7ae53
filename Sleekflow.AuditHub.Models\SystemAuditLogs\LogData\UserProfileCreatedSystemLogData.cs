using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class UserProfileCreatedSystemLogData
{
    [JsonProperty("user_profile_id")]
    public string UserProfileId { get; set; }

    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonConstructor]
    public UserProfileCreatedSystemLogData(string userProfileId, string firstName, string lastName, string email)
    {
        UserProfileId = userProfileId;
        FirstName = firstName;
        LastName = lastName;
        Email = email;
    }
}