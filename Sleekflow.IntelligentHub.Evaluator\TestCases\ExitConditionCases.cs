using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ExitCondition;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator;

public partial class ExitConditionEvaluatorTest
{
    private static readonly List<Models.Companies.CompanyAgentConfigs.Actions.ExitCondition> ExitConditions = [
        new Models.Companies.CompanyAgentConfigs.Actions.ExitCondition(
            "Human",
            "custom",
            "User explicitly requests to speak to a human agent."
        ),
        new Models.Companies.CompanyAgentConfigs.Actions.ExitCondition(
            "Drop Off",
            "custom",
            "No interest in the plan and wants to drop off."
        )
    ];

    public static IEnumerable<ExitConditionTestCase> GetTestCases()
    {
        return GetHKBNHandoffTestCasesFromKB();
    }

    private static IEnumerable<ExitConditionTestCase> GetDefaultTestCases()
    {

        yield return new ExitConditionTestCase(
            "Human - Request for Human Agent",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "This is too complicated. Can I speak to someone directly about this plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Of course! I can connect you with a customer service representative who can explain the plan in detail. Please wait while I transfer you.")
            ],
            ExitConditions,
            new ExitConditionResult(
                isMatchExitCondition: true,
                exitConditionTitle: "Human",
                reason: "The customer is requesting to speak to a human agent.")
            );

        yield return new ExitConditionTestCase(
            "Drop Off - Customer indicates no interest",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "No thanks, I am not interested"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your time.")
            ],
            ExitConditions,
            new ExitConditionResult(
                isMatchExitCondition: true,
                exitConditionTitle: "Drop Off",
                reason: "The customer is not interested in the plan and wants to drop off.")
            );

        yield return new ExitConditionTestCase(
            "Drop off Lead - Already have other plan, explicitly not interested",
            [
                new ChatMessageContent(AuthorRole.User, "I already have insurance plan, thank you"),
                new ChatMessageContent(AuthorRole.Assistant, "Thank you for your time")
            ],
            ExitConditions,
            new ExitConditionResult(
                isMatchExitCondition: true,
                exitConditionTitle: "Drop Off",
                reason: "Drop off, customer has insurance and not interested"));

    }

    private static IEnumerable<ExitConditionTestCase> GetHKBNHandoffTestCasesFromKB()
    {

        // Human (Requesting Agent)
        for (int i = 0; i < 3; i++)
        {
            yield return new ExitConditionTestCase(
                "Human - Direct Request for Agent",
                [
                    new ChatMessageContent(
                            AuthorRole.User,
                            "I'd like to discuss this in detail with someone. Can you connect me to an agent?"),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "Of course! I'll transfer you to an agent immediately"),
                        new ChatMessageContent(
                            AuthorRole.User,
                            "Thanks.")
                ],
                ExitConditions,
                new ExitConditionResult(
                    isMatchExitCondition: true,
                    exitConditionTitle: "Human",
                    reason: "Explicitly requests to speak with a human agent, indicating potential interest but need for personal assistance.")
            );
        }

        for (int i = 0; i < 3; i++)
        {
            yield return new ExitConditionTestCase(
                "Human - Dissatisfaction with AI",
                [
                    new ChatMessageContent(
                            AuthorRole.User,
                            "This conversation isn't helpful. I need to speak to a human agent."),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "My apologies. Let me connect you to a specialist who can assist further"),
                        new ChatMessageContent(
                            AuthorRole.User,
                            "Thank you.")
                ],
                ExitConditions,
                new ExitConditionResult(
                    isMatchExitCondition: true,
                    exitConditionTitle: "Human",
                    reason: "Expresses dissatisfaction with the AI conversation and requests human assistance, with unclear level of interest in products.")
            );
        }

        for (int i = 0; i < 3; i++)
        {
            yield return new ExitConditionTestCase(
                "Human - Specific Inquiry Requiring Human",
                [
                    new ChatMessageContent(
                            AuthorRole.User,
                            "I have very specific questions about your Tri-Area Plan. Can I talk to someone?"),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "Sure! I'll connect you to a representative who can provide detailed answers"),
                        new ChatMessageContent(
                            AuthorRole.User,
                            "That's great.")
                ],
                ExitConditions,
                new ExitConditionResult(
                    isMatchExitCondition: true,
                    exitConditionTitle: "Human",
                    reason: "Shows interest in a specific plan and requests human assistance for detailed information, indicating moderate to high interest.")
            );
        }

        // Drop Off (Customer Ending Due to Disinterest)
        for (int i = 0; i < 3; i++)
        {
            yield return new ExitConditionTestCase(
                "Drop Off - INDICAID Testing Plan Disinterest",
                [
                    new ChatMessageContent(
                            AuthorRole.User,
                            "Can you tell me about your INDICAID monthly testing plans?"),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "We offer customizable plans starting at $48 per month for two kits"),
                        new ChatMessageContent(
                            AuthorRole.User,
                            "Actually, I'm not interested anymore."),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "No problem. Feel free to contact us if you change your mind. Goodbye!")
                ],
                ExitConditions,
                new ExitConditionResult(
                    isMatchExitCondition: true,
                    exitConditionTitle: "Drop Off",
                    reason: "Initially shows interest but explicitly ends conversation due to disinterest after learning about offerings.")
            );
        }

        for (int i = 0; i < 3; i++)
        {
            yield return new ExitConditionTestCase(
                "Drop Off - Evercare Wound Care Plan",
                [
                    new ChatMessageContent(
                            AuthorRole.User,
                            "What's the cost of your Evercare wound care plan?"),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "It's $499 per month and includes home visits by professional nurses"),
                        new ChatMessageContent(
                            AuthorRole.User,
                            "That's too expensive. I'll pass."),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "Understood. Thank you for considering us.")
                ],
                ExitConditions,
                new ExitConditionResult(
                    isMatchExitCondition: true,
                    exitConditionTitle: "Drop Off",
                    reason: "Shows initial interest but explicitly rejects offering due to price and ends conversation.")
            );
        }

        for (int i = 0; i < 3; i++)
        {
            yield return new ExitConditionTestCase(
                "Drop Off - Global SIM Plan",
                [
                    new ChatMessageContent(
                            AuthorRole.User,
                            "I'm curious about your global travel data plans."),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "Our Global SIM plans cover 50+ destinations starting at $168"),
                        new ChatMessageContent(
                            AuthorRole.User,
                            "I'll think about it. Bye."),
                        new ChatMessageContent(
                            AuthorRole.Assistant,
                            "Thanks for your time! Have a nice day.")
                ],
                ExitConditions,
                new ExitConditionResult(
                    isMatchExitCondition: true,
                    exitConditionTitle: "Drop Off",
                    reason: "Shows initial curiosity but ends conversation without commitment, though leaves possibility open for future interest.")
            );
        }
    }

}