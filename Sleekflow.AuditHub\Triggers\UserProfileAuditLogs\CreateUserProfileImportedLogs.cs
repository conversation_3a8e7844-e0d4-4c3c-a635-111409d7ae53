﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileImportedLogs : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;

    public CreateUserProfileImportedLogs(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
    }

    public class CreateUserProfileImportedLogsInput
    {
        [JsonConstructor]
        public CreateUserProfileImportedLogsInput(List<UserProfileImportedLog> userProfileImportedLogs)
        {
            UserProfileImportedLogs = userProfileImportedLogs;
        }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("user_profile_imported_logs")]
        public List<UserProfileImportedLog> UserProfileImportedLogs { get; set; }
    }

    public class UserProfileImportedLog : IHasSleekflowCompanyId, IHasSleekflowUserProfileId
    {
        [JsonConstructor]
        public UserProfileImportedLog(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            UserProfileImportedLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public UserProfileImportedLogData Data { get; set; }
    }

    public class CreateUserProfileImportedLogsOutput
    {
    }

    public async Task<CreateUserProfileImportedLogsOutput> F(
        CreateUserProfileImportedLogsInput createUserProfileImportedLogsInput)
    {
        await Parallel.ForEachAsync(
            createUserProfileImportedLogsInput.UserProfileImportedLogs,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (userProfileImportedLog, cancellationToken) =>
            {
                var dataStr = JsonConvert.SerializeObject(userProfileImportedLog.Data);
                var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

                await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
                    new UserProfileAuditLog(
                        _idService.GetId("UserProfileAuditLog"),
                        userProfileImportedLog.SleekflowCompanyId,
                        userProfileImportedLog.SleekflowStaffId,
                        userProfileImportedLog.SleekflowUserProfileId,
                        UserProfileAuditLogTypes.UserProfileImported,
                        userProfileImportedLog.AuditLogText,
                        data,
                        DateTimeOffset.UtcNow,
                        null),
                    cancellationToken);
            });

        return new CreateUserProfileImportedLogsOutput();
    }
}