using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Payments.Configuration;

public abstract class PaymentProviderExternalConfig
{
    [Required]
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [Required]
    [JsonProperty("provider_id")]
    public string ProviderId { get; set; }

    [JsonConstructor]
    protected PaymentProviderExternalConfig(
        string providerName,
        string providerId)
    {
        ProviderName = providerName;
        ProviderId = providerId;
    }
}