using GraphApi.Client.Const.WhatsappCloudApi;
using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

public class WhatsappCloudApiDetailedConversationUsageAnalyticDto : WhatsappCloudApiConversationUsageAnalyticDto
{
    [JsonConstructor]
    public WhatsappCloudApiDetailedConversationUsageAnalyticDto(
        List<WhatsappCloudApiGranularConversationUsageAnalyticDto> granularConversationUsageAnalytics,
        int totalBusinessInitiatedPaidQuantity,
        int totalBusinessInitiatedFreeTierQuantity,
        int totalUserInitiatedPaidQuantity,
        int totalUserInitiatedFreeTierQuantity,
        int totalUserInitiatedFreeEntryPointQuantity,
        Dictionary<string, int>? conversationCategoryQuantities,
        Money totalUsed,
        Money? totalMarkup,
        Money? totalTransactionHandlingFee,
        string granularity,
        string start,
        string end)
        : base(
            totalBusinessInitiatedPaidQuantity,
            totalBusinessInitiatedFreeTierQuantity,
            totalUserInitiatedPaidQuantity,
            totalUserInitiatedFreeTierQuantity,
            totalUserInitiatedFreeEntryPointQuantity,
            conversationCategoryQuantities,
            totalUsed,
            totalMarkup,
            totalTransactionHandlingFee,
            granularity,
            start,
            end)
    {
        GranularConversationUsageAnalytics = granularConversationUsageAnalytics;
    }

    public WhatsappCloudApiDetailedConversationUsageAnalyticDto(
        List<WhatsappCloudApiGranularConversationUsageAnalyticDto> granularConversationUsageAnalytics,
        int totalBusinessInitiatedPaidQuantity,
        int totalBusinessInitiatedFreeTierQuantity,
        int totalUserInitiatedPaidQuantity,
        int totalUserInitiatedFreeTierQuantity,
        int totalUserInitiatedFreeEntryPointQuantity,
        Dictionary<string, int>? conversationCategoryQuantities,
        Money totalUsed,
        string granularity,
        string start,
        string end)
        : base(
            totalBusinessInitiatedPaidQuantity,
            totalBusinessInitiatedFreeTierQuantity,
            totalUserInitiatedPaidQuantity,
            totalUserInitiatedFreeTierQuantity,
            totalUserInitiatedFreeEntryPointQuantity,
            conversationCategoryQuantities,
            totalUsed,
            granularity,
            start,
            end)
    {
        GranularConversationUsageAnalytics = granularConversationUsageAnalytics;
    }

    public WhatsappCloudApiDetailedConversationUsageAnalyticDto(
        List<WhatsappCloudApiGranularConversationUsageAnalyticDto> granularConversationUsageAnalytics,
        string granularity,
        string start,
        string end)
        : base(
            granularConversationUsageAnalytics,
            granularity,
            start,
            end)
    {
        GranularConversationUsageAnalytics = granularConversationUsageAnalytics;
    }

    [JsonProperty("granular_conversation_usage_analytics")]
    public List<WhatsappCloudApiGranularConversationUsageAnalyticDto> GranularConversationUsageAnalytics { get; set; }
}

public class WhatsappCloudApiConversationUsageAnalyticDto
{
    [JsonConstructor]
    public WhatsappCloudApiConversationUsageAnalyticDto(
        int totalBusinessInitiatedPaidQuantity,
        int totalBusinessInitiatedFreeTierQuantity,
        int totalUserInitiatedPaidQuantity,
        int totalUserInitiatedFreeTierQuantity,
        int totalUserInitiatedFreeEntryPointQuantity,
        Dictionary<string, int>? conversationCategoryQuantities,
        Money totalUsed,
        Money? totalMarkup,
        Money? totalTransactionHandlingFee,
        string granularity,
        string start,
        string end)
    {
        TotalBusinessInitiatedPaidQuantity = totalBusinessInitiatedPaidQuantity;
        TotalBusinessInitiatedFreeTierQuantity = totalBusinessInitiatedFreeTierQuantity;
        TotalUserInitiatedPaidQuantity = totalUserInitiatedPaidQuantity;
        TotalUserInitiatedFreeTierQuantity = totalUserInitiatedFreeTierQuantity;
        TotalUserInitiatedFreeEntryPointQuantity = totalUserInitiatedFreeEntryPointQuantity;
        ConversationCategoryQuantities = conversationCategoryQuantities;
        TotalUsed = totalUsed;
        TotalMarkup = totalMarkup;
        TotalTransactionHandlingFee = totalTransactionHandlingFee;
        Granularity = granularity;
        Start = start;
        End = end;
    }

    public WhatsappCloudApiConversationUsageAnalyticDto(
        int totalBusinessInitiatedPaidQuantity,
        int totalBusinessInitiatedFreeTierQuantity,
        int totalUserInitiatedPaidQuantity,
        int totalUserInitiatedFreeTierQuantity,
        int totalUserInitiatedFreeEntryPointQuantity,
        Dictionary<string, int>? conversationCategoryQuantities,
        Money totalUsed,
        string granularity,
        string start,
        string end)
    {
        TotalBusinessInitiatedPaidQuantity = totalBusinessInitiatedPaidQuantity;
        TotalBusinessInitiatedFreeTierQuantity = totalBusinessInitiatedFreeTierQuantity;
        TotalUserInitiatedPaidQuantity = totalUserInitiatedPaidQuantity;
        TotalUserInitiatedFreeTierQuantity = totalUserInitiatedFreeTierQuantity;
        TotalUserInitiatedFreeEntryPointQuantity = totalUserInitiatedFreeEntryPointQuantity;
        ConversationCategoryQuantities = conversationCategoryQuantities;
        TotalUsed = totalUsed;
        Granularity = granularity;
        Start = start;
        End = end;
    }

    public WhatsappCloudApiConversationUsageAnalyticDto(
        List<WhatsappCloudApiGranularConversationUsageAnalyticDto> granularConversationUsageAnalytics,
        string granularity,
        string start,
        string end)
    {
        TotalBusinessInitiatedPaidQuantity =
            granularConversationUsageAnalytics.Sum(c => c.BusinessInitiatedPaidQuantity);

        TotalBusinessInitiatedFreeTierQuantity =
            granularConversationUsageAnalytics.Sum(c => c.BusinessInitiatedFreeTierQuantity);
        TotalUserInitiatedPaidQuantity = granularConversationUsageAnalytics.Sum(c => c.UserInitiatedPaidQuantity);

        TotalUserInitiatedFreeTierQuantity =
            granularConversationUsageAnalytics.Sum(c => c.UserInitiatedFreeTierQuantity);

        TotalUserInitiatedFreeEntryPointQuantity =
            granularConversationUsageAnalytics.Sum(c => c.UserInitiatedFreeEntryPointQuantity);

        TotalUsed = granularConversationUsageAnalytics.Select(y => y.Used)
            .Aggregate(
                new Money(CurrencyIsoCodes.USD, 0),
                MoneyExtensions.Add);

        if (granularConversationUsageAnalytics.Any(g => g.Markup != null))
        {
            TotalMarkup = granularConversationUsageAnalytics
                .Where(y => y.Markup != null)
                .Select(y => y.Markup!)
                .Aggregate(new Money(CurrencyIsoCodes.USD, 0), MoneyExtensions.Add);
        }

        if (granularConversationUsageAnalytics.Any(g => g.TransactionHandlingFee != null))
        {
            TotalTransactionHandlingFee = granularConversationUsageAnalytics
                .Where(y => y.TransactionHandlingFee != null)
                .Select(y => y.TransactionHandlingFee!)
                .Aggregate(new Money(CurrencyIsoCodes.USD, 0), MoneyExtensions.Add);
        }

        var sumConversationCategoryQuantities = new Dictionary<string, int>();
        foreach (var conversationCategoryQuantities in granularConversationUsageAnalytics
                     .Where(y => y.ConversationCategoryQuantities != null)
                     .Select(y => y.ConversationCategoryQuantities!))
        {
            foreach (var (key, value) in conversationCategoryQuantities)
            {
                sumConversationCategoryQuantities.TryAdd(key, 0);
                sumConversationCategoryQuantities[key] += value;
            }
        }

        ConversationCategoryQuantities = sumConversationCategoryQuantities;

        Granularity = granularity;
        Start = start;
        End = end;
    }

    [JsonProperty("total_business_initiated_paid_quantity")]
    public int TotalBusinessInitiatedPaidQuantity { get; set; }

    [JsonProperty("total_business_initiated_free_tier_quantity")]
    public int TotalBusinessInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("total_user_initiated_paid_quantity")]
    public int TotalUserInitiatedPaidQuantity { get; set; }

    [JsonProperty("total_user_initiated_free_tier_quantity")]
    public int TotalUserInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("total_user_initiated_free_entry_point_quantity")]
    public int TotalUserInitiatedFreeEntryPointQuantity { get; set; }

    [JsonProperty("conversation_category_quantities", NullValueHandling = NullValueHandling.Ignore)]
    public Dictionary<string, int>? ConversationCategoryQuantities { get; set; }

    [JsonProperty("total_used")]
    public Money TotalUsed { get; set; }

    [JsonProperty("total_markup", NullValueHandling = NullValueHandling.Ignore)]
    public Money? TotalMarkup { get; set; }

    [JsonProperty("total_transaction_handling_fee", NullValueHandling = NullValueHandling.Ignore)]
    public Money? TotalTransactionHandlingFee { get; set; }

    [JsonProperty("granularity")]
    public string Granularity { get; set; }

    [JsonProperty("start")]
    public string Start { get; set; }

    [JsonProperty("end")]
    public string End { get; set; }
}

public class WhatsappCloudApiGranularConversationUsageAnalyticDto
{
    [JsonConstructor]
    public WhatsappCloudApiGranularConversationUsageAnalyticDto(
        int businessInitiatedPaidQuantity,
        int businessInitiatedFreeTierQuantity,
        int userInitiatedPaidQuantity,
        int userInitiatedFreeTierQuantity,
        int userInitiatedFreeEntryPointQuantity,
        Dictionary<string, int>? conversationCategoryQuantities,
        Money used,
        Money? markup,
        Money? transactionHandlingFee,
        string start,
        string end)
    {
        BusinessInitiatedPaidQuantity = businessInitiatedPaidQuantity;
        BusinessInitiatedFreeTierQuantity = businessInitiatedFreeTierQuantity;
        UserInitiatedPaidQuantity = userInitiatedPaidQuantity;
        UserInitiatedFreeTierQuantity = userInitiatedFreeTierQuantity;
        UserInitiatedFreeEntryPointQuantity = userInitiatedFreeEntryPointQuantity;
        ConversationCategoryQuantities = conversationCategoryQuantities;
        Used = used;
        Markup = markup;
        TransactionHandlingFee = transactionHandlingFee;
        Start = start;
        End = end;
    }

    public WhatsappCloudApiGranularConversationUsageAnalyticDto(
        int businessInitiatedPaidQuantity,
        int businessInitiatedFreeTierQuantity,
        int userInitiatedPaidQuantity,
        int userInitiatedFreeTierQuantity,
        int userInitiatedFreeEntryPointQuantity,
        Dictionary<string, int>? conversationCategoryQuantities,
        Money used,
        string start,
        string end)
    {
        BusinessInitiatedPaidQuantity = businessInitiatedPaidQuantity;
        BusinessInitiatedFreeTierQuantity = businessInitiatedFreeTierQuantity;
        UserInitiatedPaidQuantity = userInitiatedPaidQuantity;
        UserInitiatedFreeTierQuantity = userInitiatedFreeTierQuantity;
        UserInitiatedFreeEntryPointQuantity = userInitiatedFreeEntryPointQuantity;
        ConversationCategoryQuantities = conversationCategoryQuantities;
        Used = used;
        Start = start;
        End = end;
    }

    [JsonProperty("business_initiated_paid_quantity")]
    public int BusinessInitiatedPaidQuantity { get; set; }

    [JsonProperty("business_initiated_free_tier_quantity")]
    public int BusinessInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("user_initiated_paid_quantity")]
    public int UserInitiatedPaidQuantity { get; set; }

    [JsonProperty("user_initiated_free_tier_quantity")]
    public int UserInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("user_initiated_free_entry_point_quantity")]
    public int UserInitiatedFreeEntryPointQuantity { get; set; }

    [JsonProperty("conversation_category_quantities", NullValueHandling = NullValueHandling.Ignore)]
    public Dictionary<string, int>? ConversationCategoryQuantities { get; set; }

    [JsonProperty("used")]
    public Money Used { get; set; }

    [JsonProperty("markup", NullValueHandling = NullValueHandling.Ignore)]
    public Money? Markup { get; set; }

    [JsonProperty("transaction_handling_fee", NullValueHandling = NullValueHandling.Ignore)]
    public Money? TransactionHandlingFee { get; set; }

    [JsonProperty("start")]
    public string Start { get; set; }

    [JsonProperty("end")]
    public string End { get; set; }
}

public class FacebookBusinessWabaDto
{
    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("facebook_waba_name")]
    public string? FacebookWabaName { get; set; }

    [JsonProperty("facebook_phone_numbers")]
    public List<string?> FacebookPhoneNumbers { get; set; }

    [JsonProperty("facebook_waba_timezone")]
    public FacebookTimezone? FacebookWabaTimezone { get; set; }

    public FacebookBusinessWabaDto(
        Waba waba,
        FacebookTimezone? facebookWabaTimezone)
    {
        FacebookWabaId = waba.FacebookWabaId;
        FacebookWabaName = waba.FacebookWabaName;

        FacebookPhoneNumbers = waba.WabaPhoneNumbers
            .Select(w => w.FacebookPhoneNumberDetail.DisplayPhoneNumber)
            .ToList();
        FacebookWabaTimezone = facebookWabaTimezone;
    }

    [JsonConstructor]
    public FacebookBusinessWabaDto(
        string facebookWabaId,
        string facebookWabaName,
        List<string?> facebookPhoneNumbers,
        FacebookTimezone? facebookWabaTimezone)
    {
        FacebookWabaId = facebookWabaId;
        FacebookWabaName = facebookWabaName;
        FacebookPhoneNumbers = facebookPhoneNumbers;
        FacebookWabaTimezone = facebookWabaTimezone;
    }
}

public class WhatsappCloudApiWabaConversationUsageAnalytic
{
    [JsonProperty("conversation_usage_analytic")]
    public WhatsappCloudApiConversationUsageAnalyticDto ConversationUsageAnalytic { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("facebook_business_name")]
    public string? FacebookBusinessName { get; set; }

    [JsonProperty("facebook_business_waba")]
    public FacebookBusinessWabaDto FacebookBusinessWaba { get; set; }

    [JsonConstructor]
    public WhatsappCloudApiWabaConversationUsageAnalytic(
        WhatsappCloudApiConversationUsageAnalyticDto conversationUsageAnalytic,
        string facebookBusinessId,
        string? facebookBusinessName,
        FacebookBusinessWabaDto facebookBusinessWaba)
    {
        ConversationUsageAnalytic = conversationUsageAnalytic;
        FacebookBusinessId = facebookBusinessId;
        FacebookBusinessName = facebookBusinessName;
        FacebookBusinessWaba = facebookBusinessWaba;
    }
}