using System.ComponentModel;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.FlowHubConfigs;

[ContainerId(ContainerNames.FlowHubConfig)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class FlowHubConfig : AuditEntity, IHasETag
{
    [JsonProperty("is_enrolled")]
    public bool IsEnrolled { get; set; }

    [JsonProperty("is_usage_limit_enabled")]
    public bool IsUsageLimitEnabled { get; set; }

    [JsonProperty("is_usage_limit_offset_enabled")]
    public bool IsUsageLimitOffsetEnabled { get; set; }

    [JsonProperty("is_usage_limit_auto_scaling_enabled")]
    public bool IsUsageLimitAutoScalingEnabled { get; set; }

    [JsonProperty("usage_limit")]
    public UsageLimit? UsageLimit { get; set; }

    [JsonProperty("usage_limit_offset")]
    public UsageLimitOffset? UsageLimitOffset { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty("origin")]
    public string? Origin { get; set; }

    [JsonConstructor]
    public FlowHubConfig(
        bool isEnrolled,
        UsageLimit? usageLimit,
        UsageLimitOffset? usageLimitOffset,
        string? eTag,
        string id,
        string? origin,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        bool? isUsageLimitEnabled,
        bool? isUsageLimitOffsetEnabled,
        bool? isUsageLimitAutoScalingEnabled)
        : base(id, SysTypeNames.FlowHubConfig, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        IsEnrolled = isEnrolled;
        UsageLimit = usageLimit;
        UsageLimitOffset = usageLimitOffset;
        IsUsageLimitEnabled = isUsageLimitEnabled.GetValueOrDefault(false);
        IsUsageLimitOffsetEnabled = isUsageLimitOffsetEnabled.GetValueOrDefault(true);
        IsUsageLimitAutoScalingEnabled = isUsageLimitAutoScalingEnabled.GetValueOrDefault(false);
        Origin = origin;
        ETag = eTag;
    }
}