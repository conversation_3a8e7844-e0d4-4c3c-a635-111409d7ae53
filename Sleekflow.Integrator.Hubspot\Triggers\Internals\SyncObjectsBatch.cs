﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Internals;

[TriggerGroup("Internals")]
public class SyncObjectsBatch : ITrigger
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IBus _bus;
    private readonly ILogger<SyncObjectsBatch> _logger;

    public SyncObjectsBatch(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IBus bus,
        ILogger<SyncObjectsBatch> logger)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _bus = bus;
        _logger = logger;
    }

    public class SyncObjectsBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("limit")]
        [Required]
        public long Limit { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SyncObjectsBatchInput(
            string sleekflowCompanyId,
            long limit,
            string? after,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Limit = limit;
            After = after;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class SyncObjectsBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonConstructor]
        public SyncObjectsBatchOutput(
            long count,
            string? after)
        {
            Count = count;
            After = after;
        }
    }

    public async Task<SyncObjectsBatchOutput> F(
        SyncObjectsBatchInput syncObjectsBatchInput)
    {
        var after = syncObjectsBatchInput.After;
        var limit = syncObjectsBatchInput.Limit;
        var sleekflowCompanyId = syncObjectsBatchInput.SleekflowCompanyId;
        var entityTypeName = syncObjectsBatchInput.EntityTypeName;

        var authentication =
            await _hubspotAuthenticationService.GetAsync(sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Start after {After}, limit {Limit}, sleekflowCompanyId {SleekflowCompanyId}",
            after,
            limit,
            sleekflowCompanyId);

        var (objects, nextAfter) =
            await _hubspotObjectService.GetObjectsAsync(
                authentication,
                limit,
                after,
                entityTypeName,
                syncObjectsBatchInput.FilterGroups,
                syncObjectsBatchInput.FieldFilters);

        _logger.LogInformation(
            "End after {After}, limit {Limit}, sleekflowCompanyId {SleekflowCompanyId}, count {Count}, nextAfter {NextAfter}",
            after,
            limit,
            sleekflowCompanyId,
            objects.Count,
            nextAfter);

        var events = objects
            .Select(
                dict =>
                {
                    var onObjectOperationEvent = new OnObjectOperationEvent(
                        dict,
                        OnObjectOperationEvent.OperationCreateOrUpdateObject,
                        "hubspot-integrator",
                        sleekflowCompanyId,
                        _hubspotObjectService.ResolveObjectId(dict),
                        entityTypeName,
                        null);

                    return onObjectOperationEvent;
                })
            .ToList();

        foreach (var onObjectOperationEvents in events.Chunk(30))
        {
            await _bus.PublishBatch(
                onObjectOperationEvents,
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
        }

        _logger.LogInformation(
            "Flushed after {After}, limit {Limit}, sleekflowCompanyId {SleekflowCompanyId}, count {Count}, nextAfter {NextAfter}",
            after,
            limit,
            sleekflowCompanyId,
            objects.Count,
            nextAfter);

        return new SyncObjectsBatchOutput(objects.Count, nextAfter);
    }
}