using System.Collections.Immutable;
using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.AuditHub;

public class AuditHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public AuditHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class AuditHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public AuditHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public AuditHubDbOutput InitAuditHubDb()
    {
        const string cosmosDbId = "audithubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 20000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.AuditHubDb.IAuditHubDbService
        var containerParams = new ContainerParam[]
        {
            new (
                "audit_log",
                "audit_log",
                new List<string>()
                {
                    "/sleekflow_company_id"
                },
                3600 * 24 * 21),
            new (
                "user_profile_audit_log",
                "user_profile_audit_log",
                new List<string>
                {
                    "/sleekflow_user_profile_id"
                },
                3600 * 24 * 30 * 6,
                ExcludedIndexingPathsList: new List<DocumentDB.Inputs.ExcludedPathArgs>()
                {
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/_etag/?"
                    },
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/data/*"
                    }
                },
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_user_profile_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_company_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Descending, Path = "/created_time"
                            }
                        }
                        .ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_user_profile_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_company_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/type"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_staff_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Descending, Path = "/created_time"
                            }
                        }
                        .ToImmutableArray()
                }),
            new (
                "system_audit_log",
                "system_audit_log",
                new List<string>
                {
                    "/id"
                },
                3600 * 24 * 30 * 6,
                MaxThroughput: _myConfig.Name == "production" ? 20000 : 1000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_company_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Descending, Path = "/created_time"
                            }
                        }
                        .ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/sleekflow_company_id"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Ascending, Path = "/type"
                            },
                            new ()
                            {
                                Order = DocumentDB.CompositePathSortOrder.Descending, Path = "/created_time"
                            }
                        }
                        .ToImmutableArray()
                })
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new AuditHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}