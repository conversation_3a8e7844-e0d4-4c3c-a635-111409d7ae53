﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public sealed class PrimaryPropertyInput : IProperty
{
    public const string PropertyNamePrimaryPropertyConfig = "primary_property_config";

    [Required]
    [JsonProperty(IProperty.PropertyNameDisplayName)]
    public string DisplayName { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameUniqueName)]
    public string UniqueName { get; set; }

    [Required]
    [Validations.ValidateObject]
    [JsonProperty(IProperty.PropertyNameDataType)]
    public IDataType DataType { get; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsVisible)]
    public bool IsVisible { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsPinned)]
    public bool IsPinned { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsSearchable)]
    public bool IsSearchable { get; set; }

    [Required]
    [Validations.ValidateObject]
    [JsonProperty(PropertyNamePrimaryPropertyConfig)]
    public PrimaryPropertyConfig PrimaryPropertyConfig { get; set; }

    [Validations.ValidateObject]
    [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonConstructor]
    public PrimaryPropertyInput(
        string displayName,
        string uniqueName,
        IDataType dataType,
        bool isVisible,
        bool isPinned,
        bool isSearchable,
        PrimaryPropertyConfig primaryPropertyConfig,
        AuditEntity.SleekflowStaff? createdBy)
    {
        DisplayName = displayName;
        UniqueName = uniqueName;
        DataType = dataType;
        IsVisible = isVisible;
        IsPinned = isPinned;
        IsSearchable = isSearchable;
        PrimaryPropertyConfig = primaryPropertyConfig;
        CreatedBy = createdBy;
    }
}