using System.Text.Json;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.JsonConfigs;

namespace Sleekflow.IntelligentHub.Plugins;

public interface IReviewerPlugin
{
    Task<int> GetConfidenceScoringAsync(Kernel kernel, string chatHistory, string messageToEvaluate);

    Task<EvaluatedScore?> GetEvaluateScoreAsync(Kernel kernel, string chatHistory, string? additionalPrompt = null);

    Task<HandoffTeam?> GetHandOffTeamAsync(Kernel kernel, string chatHistory, string teamCategories);

    Task<ExitConditionResult?> GetExitConditionResultAsync(
        Kernel kernel,
        string chatHistory,
        int? score,
        int confidenceScore,
        List<ExitCondition> exitConditions
    );

    Task<CalculateLeadScoreResult?> GetCalculateLeadScoreAsync(
        Kernel kernel,
        string chatHistory,
        List<LeadScoreCriterion> criteria
    );

    Task<AddLabelResult?> GetAddLabelAsync(
        Kernel kernel,
        string chatHistory,
        List<Label> labels,
        string instructions
    );
}

public class ReviewerPlugin : IReviewerPlugin, IScopedService
{
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly ILogger<ReviewerPlugin> _logger;

    public ReviewerPlugin(
        IPromptExecutionSettingsService promptExecutionSettingsService,
        ILogger<ReviewerPlugin> logger
    )
    {
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _logger = logger;
    }

    public async Task<int> GetConfidenceScoringAsync(Kernel kernel, string chatHistory, string messageToEvaluate)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_FLASH
        );

#pragma warning disable SKEXP0070
        if (promptExecutionSettings is GeminiPromptExecutionSettings geminiPromptExecutionSettings)
        {
            geminiPromptExecutionSettings.ResponseMimeType = "application/json";
            geminiPromptExecutionSettings.ResponseSchema = JsonDocument.Parse(
                """
                {
                  "type": "object",
                  "required": ["message_consistency", "ambiguity_detection", "tone_and_style_mismatch", "relevancy", "temporal_relevance", "contradiction", "escalation_detection"],
                  "properties": {
                    "message_consistency": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    },
                    "ambiguity_detection": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    },
                    "tone_and_style_mismatch": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    },
                    "relevancy": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    },
                    "temporal_relevance": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    },
                    "contradiction": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    },
                    "escalation_detection": {
                      "type": "object",
                      "properties": {
                        "score": {
                          "type": "integer"
                        },
                        "reason": {
                          "type": "string"
                        }
                      },
                      "required": ["reason", "score"],
                      "propertyOrdering": ["reason", "score"]
                    }
                  }
                }
                """
            );
        }
#pragma warning restore SKEXP0070

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluateConfidenceScoring",
                Description =
                    "Review the context and evaluate the reply to provide a score on the content, structure, and clarity.",
                Template = """
                           <message role="system">
                           You are tasked with evaluating a given message for various types of conflicts against the prior context and scoring it accordingly. A higher score indicates better alignment (less conflict), while a lower score indicates more conflict.

                           ### Evaluation Criteria:

                           1. **Message Consistency Scoring**
                               - **Similarity Check**: Ensure the new message aligns with the prior context.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (severe conflict with prior context)
                                 - 2 = "bad but acceptable" (minor conflict)
                                 - 3 = "average" (neutral alignment)
                                 - 4 = "good" (mostly aligned)
                                 - 5 = "perfect" (complete alignment)

                           2. **Ambiguity Detection Scoring**
                               - **Clarity of Terms**: If the message contains ambiguous language, assign a lower score.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (highly ambiguous)
                                 - 2 = "bad but acceptable" (somewhat ambiguous)
                                 - 3 = "average" (moderately clear)
                                 - 4 = "good" (mostly clear)
                                 - 5 = "perfect" (completely clear)

                           3. **Tone and Style Mismatch Scoring**
                               - **Tone Alignment**: If the new message's tone is consistent with the preferred tone, it should get a higher score.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (completely mismatched tone)
                                 - 2 = "bad but acceptable" (slightly mismatched)
                                 - 3 = "average" (neutral tone alignment)
                                 - 4 = "good" (mostly aligned)
                                 - 5 = "perfect" (perfectly aligned)

                           4. **Relevancy Scoring**
                               - **Context Relevance**: A message that is on-topic and relevant to the previous conversation should get a higher score.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (completely irrelevant)
                                 - 2 = "bad but acceptable" (somewhat relevant)
                                 - 3 = "average" (moderately relevant)
                                 - 4 = "good" (mostly relevant)
                                 - 5 = "perfect" (completely relevant)

                           5. **Temporal Relevance Scoring**
                               - **Time Sensitivity**: If a message refers to relevant and timely information, it should get a higher score.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (outdated or irrelevant information)
                                 - 2 = "bad but acceptable" (slightly outdated)
                                 - 3 = "average" (moderately timely)
                                 - 4 = "good" (mostly timely)
                                 - 5 = "perfect" (completely timely)

                           6. **Contradiction Scoring**
                               - **Explicit Contradictions**: If a new message does not contradict previous messages, assign a higher score.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (severe contradiction)
                                 - 2 = "bad but acceptable" (minor contradiction)
                                 - 3 = "average" (no significant contradiction)
                                 - 4 = "good" (mostly consistent)
                                 - 5 = "perfect" (completely consistent)

                           7. **Escalation Detection Scoring**
                               - **Escalation or Delay Identification**: If the message indicates an escalation (e.g., "I will get back to our manager") or a delayed response (e.g., "I will provide results later"), assign a lower score and flag the response.
                               - **Score**: Rate from 1 to 5, where:
                                 - 1 = "totally bad" (escalation or delay detected)
                                 - 2 = "bad but acceptable" (minor delay with a clear timeline)
                                 - 3 = "average" (no escalation or delay)
                                 - 4 = "good" (immediate response with minor clarification needed)
                                 - 5 = "perfect" (immediate, relevant, and complete response)

                           **Note**: Even if the bot confidently states it will escalate or provide information later, this should still be considered a negative aspect and scored accordingly, as our group does not support escalation or delayed responses.

                           Please evaluate the provided message according to these criteria and respond in JSON format, ensuring that each property name is in lowercase and includes 'score' and 'reason'.
                           </message>
                           <message role="user">
                           ### Chat History:
                           ```
                           {{$chat_history}}
                           ```
                           ### Message to be Evaluated:
                           ```
                           Bot: {{$message_to_evaluate}}
                           ```
                           """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
                    new InputVariable
                    {
                        Name = "message_to_evaluate"
                    },
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation of the message in JSON format."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    },
                },
            }
        );

        var chatMessageContent = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "chat_history", chatHistory
                },
                {
                    "message_to_evaluate", messageToEvaluate
                },
            }
        );

        var content = chatMessageContent?.Content ?? "{}";

        var confidenceScoringResult = JsonConvert.DeserializeObject<ConfidenceScoringResult>(content)!;

        var (
            messageConsistencyScore,
            ambiguityDetection,
            toneAndStyleMismatch,
            relevancy,
            temporalRelevance,
            contradiction,
            escalationDetection
            ) = confidenceScoringResult;

        var weights = new Dictionary<string, int>
        {
            {
                "messageConsistency", 2
            },
            {
                "ambiguityDetection", 1
            },
            {
                "toneAndStyleMismatch", 1
            },
            {
                "relevancy", 3
            },
            {
                "temporalRelevance", 1
            },
        };

        // Calculate the weighted sum of scores
        var sumWeightedScores =
            (messageConsistencyScore.Score * weights["messageConsistency"])
            + (ambiguityDetection.Score * weights["ambiguityDetection"])
            + (toneAndStyleMismatch.Score * weights["toneAndStyleMismatch"])
            + (relevancy.Score * weights["relevancy"])
            + (temporalRelevance.Score * weights["temporalRelevance"]);

        // Calculate the sum of weights
        var sumWeights = weights.Values.Sum();

        // Calculate totalScore by subtracting the minimum possible weighted sum
        var totalScore = sumWeightedScores - sumWeights; // Minimum score is 1 per criterion

        if (contradiction.Score < 3)
        {
            totalScore /= 2;
        }

        if (escalationDetection.Score < 3)
        {
            totalScore /= 5 - escalationDetection.Score;
        }

        // Calculate the percentage
        var percentage = (int) (1.0 * totalScore / (sumWeights * 4.0) * 100);

        _logger.LogInformation(
            "Evaluated the confidence score {ConfidenceScoringResult} for a recommended reply {MessageToEvaluate}. Total Score is {TotalScore}.",
            JsonConvert.SerializeObject(confidenceScoringResult, JsonConfig.DefaultLoggingJsonSerializerSettings),
            messageToEvaluate,
            percentage
        );

        return percentage;
    }

    public async Task<EvaluatedScore?> GetEvaluateScoreAsync(
        Kernel kernel,
        string chatHistory,
        string? additionalPrompt = null
    )
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_GPT_4_1
        );

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "EvaluateScoring",
                Description = "Review the original text and provide feedback on the content, structure, and clarity.",
                Template = """
                           <message role="system">
                           You are an expert lead-scoring AI agent tasked with evaluating leads based on their conversational interactions. Your goal is to analyze the provided chat history and contact properties to assign a lead score and category, while providing concise and actionable feedback.
                           {{$additional_prompt}}
                           </message>
                           ### Interaction 1:
                           <message role="user">
                           ### Chat History:
                           ```
                           User: 我在尋找一個經濟實惠的手機計劃，有什麼選擇？
                           Bot: 你好John，我們有幾個手機計劃，最經濟實惠的是「42Mbps本地數據+中國內地及澳門數據/+中國流動電話號碼流動通訊計劃」。月費僅需$78。
                           User: 這個計劃還有哪些細節？
                           Bot: 包含每月10GB本地數據及1GB中國內地及澳門數據，並附帶中國流動電話號碼。
                           User: 這個計劃對我來說正合適，我要辦理。
                           ```
                           </message>
                           <message role="assistant">
                           { "category": "High", "scores": {
                               "intent_interest": 98,
                               "buying_signals": 100,
                               "depth_specificity": 42,
                               "sentiment_tone": 78
                           }, "reason": "The user asked detailed questions about pricing and features, showing high intent. Their inquiry about the sign-up process suggests readiness to purchase." }
                           </message>
                           ### Interaction 2:
                           <message role="user">
                           ### Chat History:
                           ```
                           User: I wanted to speak to a human agent.
                           Bot: Certainly! The $78 China Data Roaming plan offers 5GB of high-speed data in China with 42mbps speeds. After 5GB, the speed is reduced, but you can still access data. Is there something specific you'd like to clarify?
                           User: Yes, I want to know if there are hidden fees or restrictions. I've had bad experiences with other companies before.
                           Bot: That's a valid concern! There are no hidden fees as long as you stay within the plan's limits. Additional data beyond the included 5GB or usage outside China may incur extra charges, which are clearly listed in our terms. I can also provide a detailed breakdown if you'd like.
                           User: I'm still not entirely convinced. I'd prefer to speak with someone or a manager directly who can guarantee there won't be any surprises.
                           ```
                           </message>
                           <message role="assistant">
                           { "category": "Human", "scores": {
                               "intent_interest": 49,
                               "buying_signals": 51,
                               "depth_specificity": 38,
                               "sentiment_tone": 48
                           }, "reason": "The user explicitly requested a human agent, indicating a preference for human interaction." }
                           </message>
                           ### Interaction 3:
                           <message role="user">
                           ### Chat History:
                           ```
                           User: 我在尋找一個經濟實惠的手機計劃，有什麼選擇？
                           Bot: 你好John，我們有幾個手機計劃，最經濟實惠月費僅需$78。
                           User: 不再感興趣。
                           ```
                           </message>
                           <message role="assistant">
                           { "category": "Drop Off", "scores": {
                               "intent_interest": 0,
                               "buying_signals": 0,
                               "depth_specificity": 12,
                               "sentiment_tone": 0
                           }, "reason": "The user indicated they were no longer interested, ending the conversation." }
                           </message>
                           ### Interaction 4:
                           <message role="user">
                           ### Chat History:
                           ```
                           User: Can you speak in English?
                           ```
                           </message>
                           <message role="assistant">
                           { "category": "Low", "scores": {
                               "intent_interest": 0,
                               "buying_signals": 0,
                               "depth_specificity": 0,
                               "sentiment_tone": 0
                           }, "reason": "The user asked to speak in English, indicating a preference for English." }
                           </message>

                           ### Interaction 5:
                           <message role="user">
                           ### Chat History:
                           ```
                           User: Hi, I'm an existing HKBN broadband customer. I saw something about a healthcare plan you offer, but I'm a bit confused. What's included, and how does it work?
                           Bot: Hi! Great to hear you're interested. As an HKBN customer, you can add the Bowtie 4-in-1 Healthcare Service for $99/month. It includes unlimited video consultations, two dental cleanings a year at CMER Dental clinics, one full body check at Bowtie & JP Health, and one flu vaccine. Would you like me to break down each service?
                           User: Yes, please. Start with the video consultations. How do I actually *use* them?
                           Bot: Okay! First, after you sign up and activate the plan through My HKBN, you'll receive a separate email with instructions on how to activate your Bowtie account. Then, you log in to the Bowtie platform and select 'Video Consultation.' This will redirect you to the MyDoc platform, where you can book a consultation with a registered doctor. They can provide medical advice, prescriptions (with free delivery), and even sick leave certificates if needed. The service are designed for people age 18-80 years old.The service hours are Mon-Fri 9am-7pm, Sat 9am-1pm, excluding holidays and storm warnings. Any questions so far?
                           User: Okay, that makes sense. And what about the dental cleaning? Where can I go, and what's included?
                           Bot: For dental cleanings, you can visit designated CMER Dental clinics in Central, Diamond Hill, Kwun Tong, and Sha Tin. The cleaning includes a checkup, scaling, polishing, and it *might* include X-rays, depending on the dentist's assessment. To book, you'll need to do it through the Bowtie platform. Also, for new HKBN customer, each customer(PPS) can only register this service for maximum 5 times, would you like me to check on your registration status first?
                           ```
                           </message>
                           <message role="assistant">
                           { "category": "Medium", "scores": {
                               "intent_interest": 72,
                               "buying_signals": 43,
                               "depth_specificity": 53,
                               "sentiment_tone": 67
                           }, "reason": "The customer shows strong interest in the healthcare plan, asks specific questions and actively engages in conversation, but has not yet expressed clear purchase intention or inquired about pricing details." }
                           </message>

                           ### Interaction 6:
                           <message role="user">
                           ### Chat History:
                           ```
                           User: Can I use the Bowtie flu shot for my chihuahua, Princess Fluffybutt? She's been sneezing a lot lately, and I'm worried she'll miss the dog show.
                           Bot: I understand your concern for Princess Fluffybutt, but the Bowtie 4-in-1 Healthcare Service is designed for *human* use only. It's not suitable for pets, the service are designed for people age 18-80 years old. I recommend consulting with a veterinarian for Princess Fluffybutt's healthcare needs. Is there any other medical services that I can provide assistance with?
                           User: But she *identifies* as a human! She wears tiny hats and watches K-dramas all day. Surely there's an exception?
                           Bot: I apologize, but there are no exceptions. The video consultations are also design for people with human age 18-80 years old, you might want to consider bringing Princess Fluffybutt to register an veterinary. Is there any other services I can help you with?
                           ```
                           </message>
                           <message role="assistant">
                           { "category": "Low", "scores": {
                               "intent_interest": 23,
                               "buying_signals": 27,
                               "depth_specificity": 23,
                               "sentiment_tone": 21
                           }, "reason": "The user asked about a pet-related service, which is not relevant to the healthcare plan." }
                           </message>
                           ### Dynamic Input Fields:
                           <message role="user">
                           ### Chat History:
                           ```
                           {{$chat_history}}
                           ```
                           </message>
                           """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "additional_prompt"
                    },
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "The review feedbacks of the scoring in JSON format.",
                    JsonSchema = """
                                 {
                                     "type": "object",
                                     "properties": {
                                         "category": {
                                             "type": "string"
                                         },
                                         "scores": {
                                             "type": "object",
                                             "properties": {
                                                 "intent_interest": {
                                                     "type": "integer"
                                                 },
                                                 "buying_signals": {
                                                     "type": "integer"
                                                 },
                                                 "depth_specificity": {
                                                     "type": "integer"
                                                 },
                                                 "sentiment_tone": {
                                                     "type": "integer"
                                                 }
                                             }
                                         },
                                         "reason": {
                                             "type": "string"
                                         }
                                     }
                                 }
                                 """,
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    },
                },
            }
        );

        var chatMessageContent = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "additional_prompt", additionalPrompt
                },
                {
                    "chat_history", chatHistory
                },
            }
        );

        var content = chatMessageContent?.Content ?? "{}";

        var evaluatedScore = JsonConvert.DeserializeObject<EvaluatedScore>(content);
        if (evaluatedScore is null)
        {
            throw new Exception("Failed to deserialize evaluated score");
        }

        int score = 0;
        if (evaluatedScore.Scores != null)
        {
            score += (int) (evaluatedScore.Scores.IntentInterest * 0.3);
            score += (int) (evaluatedScore.Scores.BuyingSignals * 0.4);
            score += (int) (evaluatedScore.Scores.DepthSpecificity * 0.25);
            score += (int) (evaluatedScore.Scores.SentimentTone * 0.05);
        }

        evaluatedScore.Score = score;

        if (evaluatedScore.Category == "Human" || evaluatedScore.Category == "Drop Off")
        {
            return evaluatedScore;
        }

        if (score >= 80)
        {
            evaluatedScore.Category = "High";
        }
        else if (score <= 40)
        {
            evaluatedScore.Category = "Low";
        }
        else
        {
            evaluatedScore.Category = "Medium";
        }

        return evaluatedScore;
    }

    public async Task<HandoffTeam?> GetHandOffTeamAsync(Kernel kernel, string chatHistory, string teamCategories)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_GPT_4_1
        );

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "HandOffTeam",
                Description =
                    "Analyze conversations to determine which human team the conversation should be transferred to.",
                Template = """
                           <message role="system">
                           You are a specialized agent responsible for analyzing conversations between users and AI assistants to determine which human team the conversation should be transferred to based on the business needs identified in the chat.

                           ### Goal:
                           Goal: Determine the most appropriate human team to handle the conversation based on business needs.

                           ---Primary Function---
                           - Analyze the entire chat history between the user and AI assistant
                           - Identify the primary business need or issue being discussed
                           - Determine the most appropriate human team to handle the conversation

                           ---Analysis Approach---
                           - Focus on the core issue or request in the conversation, not just keywords
                           - Consider the user's intent and the context of the entire conversation
                           - Look for explicit statements of need as well as implicit indicators
                           - Pay attention to technical terminology or industry-specific language that might indicate specialized needs
                           - Consider the recency if the customer has questions about multiple products or services.

                           ---Team Categories---
                           The following are team descriptions:
                           {{$team_categories}}

                           </message>

                           ### Example 1:
                           ---Team Categories---
                           The following are team descriptions:
                           Mobile: responsible for HKBN mobile plan product
                           Healthcare: responsible for Botiwe health care product
                           Unknown: cannot decide which product the customer is interested in

                           <message role="user">
                           ### Chat History:
                           ```
                           User: 我们公司需要升级手机计划，大约有50名员工需要新的资费套餐。
                           Bot: 您好，感谢您联系我们关于企业手机计划。我们有专门针对企业客户的优惠方案，可以为您的50名员工提供灵活的数据和通话套餐。请问您对数据使用量有什么特别的要求吗？
                           User: 我们主要需要大量的本地数据，至少每人每月10GB，以及一些中国内地漫游数据。
                           Bot: 我们的企业优惠计划正好符合您的需求。我们可以提供每人每月15GB本地数据和2GB中国内地漫游数据的方案，还附带无限本地通话。企业客户还可享受额外9折优惠。您希望安排一次详细咨询吗？
                           User: 听起来不错。我想了解一下具体的价格和合同期限。
                           ```
                           </message>

                           <message role="assistant">
                           { "team_recommendation": "Mobile" }
                           </message>

                           ### Example 2:
                           ---Team Categories---
                           The following are team descriptions:
                           Mobile: responsible for HKBN mobile plan product
                           Healthcare: responsible for Botiwe health care product
                           Unknown: cannot decide which product the customer is interested in

                           <message role="user">
                           ### Chat History:
                           ```
                           User: 我想了解一下醫療服務計劃。
                           Bot: 香港寬頻市務部與Bowtie將於2024年6月27日推出全新「四合一醫療服務計劃」,免診 金、免排隊、免藥費及包醫生紙,讓客戶以超值價格獲得全面的醫療保障。低至每月$99 就可享12個月計劃期內無限次普通科西醫視像會診、洗牙及全面牙科檢查、全身檢查 及流感疫苗接種*,新客戶及現有客戶同樣可享計劃內高達$3,500醫療保健服務。
                           User: 服务时间呢
                           Bot: 遙距視像會診的服務時間為週一至週五，上午9時至下午7時，和週六上午9時至下午1時，但不涵蓋公眾假期以及在黑色暴雨警告或八號或以上颱風信號生效期間。
                           User: 我想订购。
                           ```
                           </message>

                           <message role="assistant">
                           { "team_recommendation": "HealthCare"}
                           </message>

                           ### Example 3:
                           ---Team Categories---
                           The following are team descriptions:
                           Mobile: responsible for HKBN mobile plan product
                           Healthcare: responsible for Botiwe health care product
                           Unknown: cannot decide which product the customer is interested in

                           <message role="user">
                           ### Chat History:
                           ```
                           User: I would like to learn about your mobile services.
                           Bot: To meet the growing demand for "Asia-Pacific and Greater Bay Area" roaming data and "China mobile phone numbers," the marketing department will launch the "N Mobile Local + Asia-Pacific Data Mobile Communication Plan with China Mobile Number" on April 24, 2024. The plan includes: Asia-Pacific data plans and Asia-Pacific data plans with a China Mainland number.

                           User: I heard you also have a home care plan.
                           Bot: HKBN x Evercare "Stay at Home" Home Care Plan
                           For as low as $399 per month, enjoy attentive home care services, making life easier for you and your family.
                           Online booking available for a hassle-free process.

                           User: I would like to subscribe.
                           ```
                           </message>

                           <message role="assistant">
                           { "team_recommendation": "HealthCare"}
                           </message>

                           ### Dynamic Input:
                           <message role="user">
                           ### Chat History:
                           ```
                           {{$chat_history}}
                           ```
                           </message>
                           """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
                    new InputVariable
                    {
                        Name = "team_categories"
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description =
                        "The analysis of which team the conversation should be transferred to in JSON format.",
                    JsonSchema = """
                                 {
                                     "type": "object",
                                     "properties": {
                                         "team_recommendation": {
                                             "type": "string"
                                         }
                                     }
                                 }
                                 """,
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    },
                },
            }
        );

        var chatMessageContent = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "chat_history", chatHistory
                },
                {
                    "team_categories", teamCategories
                },
            }
        );

        var content = chatMessageContent?.Content ?? "{}";

        return JsonConvert.DeserializeObject<HandoffTeam>(content);
    }

    public async Task<ExitConditionResult?> GetExitConditionResultAsync(
        Kernel kernel,
        string chatHistory,
        int? score,
        int confidenceScore,
        List<ExitCondition> exitConditions
    )
    {
        if (exitConditions.Count == 0)
        {
            throw new Exception("Exit conditions are empty");
        }
        // Early exit check for specific condition types
        var earlyExitResult = CheckEarlyExitConditions(exitConditions, score, confidenceScore);
        if (earlyExitResult != null)
        {
            return earlyExitResult;
        }

        var remainingConditions = exitConditions
        .Where(c => c.Type != "low_confidence" && c.Type != "lead_score")
        .ToList();

        if (remainingConditions.Count == 0)
        {
            return new ExitConditionResult(false, string.Empty, "No exit conditions met");
        }

        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_GPT_4_1
        );

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "ExitCondition",
                Description = "Evaluate the exit condition based on the chat history and score.",
                Template = """
                            <message role="system">
                                You are an exit condition evaluator for a customer service AI system. Your job is to analyze chat conversations and determine if any exit conditions are met.

                                ## Your Task:
                                1. Carefully review the entire chat history between user and assistant
                                2. Evaluate each custom exit condition against the conversation context
                                3. Consider both explicit requests and implicit behavioral signals
                                4. Return your evaluation in the specified JSON format

                                ## CRITICAL: Title Validation Rules:
                                - The "exit_condition_title" field MUST be exactly one of the titles from the provided exit conditions list
                                - If no exit condition is met, return an empty string ("") for "exit_condition_title"
                                - DO NOT create new titles or modify existing ones
                                - You must validate your response before finalizing it
                                - If your initial assessment results in a title that doesn't match any provided condition, reconsider your evaluation

                                ## Output Format:
                                Return a JSON object with:
                                - "is_match_exit_condition": boolean (true if any condition is met)
                                - "exit_condition_title": string (title of the matched condition, or empty if none)
                                - "reason": string (brief explanation of your decision)

                                ## Analysis Guidelines:
                                - For custom conditions, analyze conversation context and user behavior
                                - Look for explicit user requests for human agents
                                - Consider implicit signals (frustration, repeated questions, complex requests)
                                - Prioritize recent messages and clear behavioral patterns
                                - Be conservative - only trigger when conditions are clearly met
                                - Consider conversation flow and user satisfaction level
                                - Focus on immediate intent rather than hypothetical scenarios
                            </message>

                            ### Example 1 - Custom Type (Human Agent Requested) - English:
                            <message role="user">
                            Exit Conditions:
                            [{"title": "Human agent requested", "type": "custom", "description": "Exit the conversation when the user shows intent to speak with a sales representative."}]

                            Chat History:
                            user: I'm interested in your 5G plans
                            assistant: We have various 5G plans starting from $99 per month. What price range are you looking for?
                            user: I'd like to speak with a sales representative for more details
                            assistant: I can certainly help you with that! Let me provide you with some basic information first.
                            user: No thanks, just connect me with sales directly please

                            </message>

                            <message role="assistant">
                            {"is_match_exit_condition": true, "exit_condition_title": "Human agent requested", "reason": "User explicitly requested to speak with sales representative twice, showing clear intent for human interaction"}
                            </message>

                            ### Example 2 - Custom Type (Frustrated User Seeking Human Help):
                            <message role="user">
                            Exit Conditions:
                            [{"title": "Customer frustration escalation", "type": "custom", "description": "Exit when customer shows signs of frustration and requests human assistance or supervisor."}]

                            Chat History:
                            user: I've been trying to cancel my subscription for weeks
                            assistant: I can help you with that cancellation. Let me look up your account details.
                            user: I already tried this with three different agents. Nothing gets resolved.
                            assistant: I apologize for the previous experience. Let me make sure we get this sorted out today.
                            user: This is ridiculous. Can I speak to a supervisor or someone who can actually help?
                            assistant: I understand your frustration. I have the authority to process your cancellation right now.
                            user: No, I want to speak to a human supervisor immediately. This automated system isn't working.
                            </message>

                            <message role="assistant">
                            {"is_match_exit_condition": true, "exit_condition_title": "Customer frustration escalation", "reason": "User expressed clear frustration with previous failed attempts and explicitly requested to speak with a human supervisor, showing escalation pattern."}
                            </message>

                            ### Example 3 - Custom Type (Human Agent Requested Traditional Chinese):
                            <message role="user">
                            Exit Conditions:
                            [{"title": "Human agent requested", "type": "custom", "description": "Exit when user requests human interaction."}]

                            Chat History:
                            user: 我想了解一下醫療服務計劃
                            assistant: 香港寬頻與Bowtie推出「四合一醫療服務計劃」，低至每月$99就可享無限次視像會診。
                            user: 服務時間是什麼？
                            assistant: 遙距視像會診的服務時間為週一至週五上午9時至下午7時，週六上午9時至下午1時。
                            user: 我想直接跟人工客服詳談
                            </message>

                            <message role="assistant">
                            {"is_match_exit_condition": true, "exit_condition_title": "Human agent requested", "reason": "User explicitly requested to speak with human customer service for detailed discussion"}
                            </message>

                            ### Example 4 - Custom Type (No Exit Condition Met):
                            <message role="user">
                            Exit Conditions:
                            [{"title": "Technical issue unresolved", "type": "custom", "description": "Exit when user expresses frustration with unresolved technical problems after multiple attempts."}]

                            Chat History:
                            user: My internet keeps disconnecting every few minutes
                            assistant: I understand how frustrating that must be. Let's troubleshoot this step by step. Can you try restarting your modem?
                            user: Okay, I restarted it. Let me test for a few minutes.
                            user: Great! That seems to have fixed it. The connection is stable now.
                            assistant: Excellent! I'm glad we could resolve that quickly. Is there anything else I can help you with today?
                            user: No, that's all. Thanks for your help!
                            </message>

                            <message role="assistant">
                            {"is_match_exit_condition": false, "exit_condition_title": "", "reason": "Issue was successfully resolved and user expressed satisfaction. No frustration or unresolved problems detected."}
                            </message>

                            ### Actual Input:
                            <message role="user">
                            Exit Conditions:
                            {{$exit_conditions}}

                            Chat History:
                            {{$chat_history}}
                            </message>
                           """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "exit_conditions"
                    },
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The evaluation of the exit condition in JSON format.",
                    JsonSchema = """
                                 {
                                     "type": "object",
                                     "properties": {
                                         "is_match_exit_condition": {
                                             "type": "boolean"
                                         },
                                         "exit_condition_title": {
                                             "type": "string"
                                         },
                                         "reason": {
                                             "type": "string"
                                         }
                                     }
                                 }
                                 """,
                },
            }
        );

        var chatMessageContent = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "exit_conditions", JsonConvert.SerializeObject(remainingConditions, new JsonSerializerSettings
                    {
                        StringEscapeHandling = StringEscapeHandling.Default
                    })
                },
                {
                    "chat_history", chatHistory
                }
            }
        );

        var content = chatMessageContent?.Content ?? "{}";

        return JsonConvert.DeserializeObject<ExitConditionResult>(content);
    }

    public async Task<CalculateLeadScoreResult?> GetCalculateLeadScoreAsync(
        Kernel kernel,
        string chatHistory,
        List<LeadScoreCriterion> leadScoreCriteria
    )
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.S_GPT_4_1
        );

        var function = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "CalculateLeadScore",
                Description = "Evaluate lead scoring based on custom criteria and chat history",
                Template = """
                           <message role="system">
                           You are a professional lead scoring expert. Your task is to analyze the provided chat history against custom scoring criteria and assign a score of 0-100 for each criterion with detailed reasoning.

                           ## Scoring Principles
                           - Carefully analyze customer behavior, language, and intent in the chat history
                           - Score each criterion independently with a range of 0-100 points
                           - 0 points means completely fails to meet the criterion
                           - 100 points means perfectly meets the criterion
                           - Base scores on actual customer performance, avoid overly high or low scores
                           - Provide specific and objective reasoning for each score

                           ## Scoring Criteria Information
                           You will receive a list of scoring criteria, each containing:
                           - weight: Indicates the importance of this criterion in the final score calculation
                           - description: The specific content to evaluate for this criterion

                           ## Output Requirements
                           - Must provide independent scoring (0-100 points) for each criterion
                           - Each score must have specific reasoning
                           - Provide overall comprehensive analysis
                           - Use the index position to identify scoring results

                           ## Important Notes
                           - Focus on actual customer performance demonstrated in the chat
                           - Distinguish different levels of buying signal strength
                           - Consider customer engagement and interaction quality
                           - Avoid scoring based on assumptions or speculation
                           </message>

                           ### Example 1 - High Purchase Intent:
                           <message role="user">
                           Please score the sales lead based on the following chat history and scoring criteria:

                           ### Chat History:
                           ```
                           User: Can I switch to your $118 unlimited mobile data plan?
                           Assistant: Sure! Let me check if you're eligible. Could you share your account number, please?
                           User: It's ********.
                           Assistant: Thank you. You're eligible to switch, and there's no additional charge for the upgrade. Would you like me to proceed?
                           User: Yes, please.
                           ```

                           ### Scoring Criteria:
                           [{"weight": 40, "description": "Intent & Interest Level - Does the lead explicitly ask about features, specifications, availability, or compatibility?"}, {"weight": 35, "description": "Buying Signals - Does the lead express urgency, mention timeframes, inquire about pricing, or show readiness to purchase?"}, {"weight": 25, "description": "Engagement Quality - Does the lead ask follow-up questions, provide requested information, and actively participate in the conversation?"}]
                           </message>

                           <message role="assistant">
                           {
                             "criterion_scores": [
                               {
                                 "criterion_index": 0,
                                 "score": 95,
                                 "reason": "Customer directly asks about a specific plan ($118 unlimited mobile data plan), showing clear intent and interest in a particular product offering."
                               },
                               {
                                 "criterion_index": 1,
                                 "score": 100,
                                 "reason": "Customer provides account information when requested, agrees to proceed immediately, and shows clear willingness to complete the transaction without hesitation."
                               },
                               {
                                 "criterion_index": 2,
                                 "score": 90,
                                 "reason": "Customer actively provides requested information (account number) and gives clear confirmation to proceed, demonstrating high engagement and cooperation."
                               }
                             ],
                             "overall_reason": "This lead demonstrates exceptional purchase intent with specific product inquiry, immediate willingness to provide personal information, and clear agreement to proceed with the service change. All indicators point to a ready-to-convert customer."
                           }
                           </message>

                           ### Example 2 - Medium Interest Level:
                           <message role="user">
                           Please score the sales lead based on the following chat history and scoring criteria:

                           ### Chat History:
                           ```
                           User: Do you have any internet plans cheaper than $100?
                           Assistant: Currently, our best offer is $109 for 1000M broadband. It's a great deal for the speed and reliability. Would you like more details?
                           User: I'll think about it.
                           Assistant: No problem. Let me know if you have any questions!
                           ```

                           ### Scoring Criteria:
                           [{"weight": 40, "description": "Intent & Interest Level - Does the lead explicitly ask about features, specifications, availability, or compatibility?"}, {"weight": 35, "description": "Buying Signals - Does the lead express urgency, mention timeframes, inquire about pricing, or show readiness to purchase?"}, {"weight": 25, "description": "Engagement Quality - Does the lead ask follow-up questions, provide requested information, and actively participate in the conversation?"}]
                           </message>

                           <message role="assistant">
                           {
                             "criterion_scores": [
                               {
                                 "criterion_index": 0,
                                 "score": 60,
                                 "reason": "Customer shows some interest by asking about plans within a specific price range, but doesn't inquire about features, specifications, or specific requirements."
                               },
                               {
                                 "criterion_index": 1,
                                 "score": 30,
                                 "reason": "Customer mentions price preference but shows no urgency, no specific timeline, and defers decision with 'I'll think about it', indicating weak buying signals."
                               },
                               {
                                 "criterion_index": 2,
                                 "score": 40,
                                 "reason": "Customer asks initial question but doesn't follow up with additional questions, doesn't request more details when offered, showing limited engagement."
                               }
                             ],
                             "overall_reason": "This lead shows initial price-conscious interest but lacks strong buying signals or deep engagement. The 'I'll think about it' response suggests they are still in early consideration phase without immediate purchase intent."
                           }
                           </message>

                           ### Example 3 - Low Interest/Just Browsing:
                           <message role="user">
                           Please score the sales lead based on the following chat history and scoring criteria:

                           ### Chat History:
                           ```
                           User: I'm just browsing options right now. Do you have any brochures?
                           Assistant: We don't have physical brochures, but I can share all the plan details and pricing with you via WhatsApp. Let me know if that works for you!
                           User: Sure, just send it over.
                           Assistant: Done! Let me know if you'd like help choosing the right plan.
                           ```

                           ### Scoring Criteria:
                           [{"weight": 40, "description": "Intent & Interest Level - Does the lead explicitly ask about features, specifications, availability, or compatibility?"}, {"weight": 35, "description": "Buying Signals - Does the lead express urgency, mention timeframes, inquire about pricing, or show readiness to purchase?"}, {"weight": 25, "description": "Engagement Quality - Does the lead ask follow-up questions, provide requested information, and actively participate in the conversation?"}]
                           </message>

                           <message role="assistant">
                           {
                             "criterion_scores": [
                               {
                                 "criterion_index": 0,
                                 "score": 25,
                                 "reason": "Customer explicitly states they are 'just browsing' and only requests general information materials, showing minimal specific intent or interest in particular products."
                               },
                               {
                                 "criterion_index": 1,
                                 "score": 10,
                                 "reason": "No buying signals present - customer clarifies they are browsing, requests only informational materials, and shows no urgency or purchase readiness."
                               },
                               {
                                 "criterion_index": 2,
                                 "score": 45,
                                 "reason": "Customer does respond to assistant's suggestions and agrees to receive information, showing some level of engagement, but doesn't ask follow-up questions or seek clarification."
                               }
                             ],
                             "overall_reason": "This lead is in very early information-gathering stage with no immediate purchase intent. The explicit 'just browsing' statement and request for only general materials indicates low conversion potential in the near term."
                           }
                           </message>

                           ### Example 4 - Custom Criteria Example:
                           <message role="user">
                           Please score the sales lead based on the following chat history and scoring criteria:

                           ### Chat History:
                           ```
                           User: Hi, I'm an existing HKBN broadband customer. I saw something about a healthcare plan you offer, but I'm a bit confused. What's included?
                           Assistant: Hi! Great to hear you're interested. As an HKBN customer, you can add the Bowtie 4-in-1 Healthcare Service for $99/month. It includes unlimited video consultations, two dental cleanings a year, one full body check, and one flu vaccine.
                           User: That sounds comprehensive. How do I actually use the video consultations?
                           Assistant: After you sign up, you'll receive instructions to activate your Bowtie account. You can then book consultations with registered doctors who can provide medical advice, prescriptions, and sick leave certificates.
                           User: What about the dental cleaning? Where can I go?
                           ```

                           ### Scoring Criteria:
                           [{"weight": 30, "description": "Product Understanding - Does the customer ask detailed questions about how the service works and its specific features?"}, {"weight": 25, "description": "Value Recognition - Does the customer acknowledge or show appreciation for the product benefits?"}, {"weight": 20, "description": "Implementation Interest - Does the customer inquire about how to use or access the services?"}, {"weight": 25, "description": "Follow-up Engagement - Does the customer continue asking relevant questions and show sustained interest?"}]
                           </message>

                           <message role="assistant">
                           {
                             "criterion_scores": [
                               {
                                 "criterion_index": 0,
                                 "score": 85,
                                 "reason": "Customer asks specific, detailed questions about service functionality ('How do I actually use the video consultations?') and location details ('Where can I go?'), demonstrating desire to understand the product thoroughly."
                               },
                               {
                                 "criterion_index": 1,
                                 "score": 75,
                                 "reason": "Customer responds positively with 'That sounds comprehensive' when hearing about the service features, indicating recognition of the value proposition."
                               },
                               {
                                 "criterion_index": 2,
                                 "score": 80,
                                 "reason": "Customer specifically asks about implementation details - how to use video consultations and where to access dental services, showing practical interest in actually utilizing the services."
                               },
                               {
                                 "criterion_index": 3,
                                 "score": 90,
                                 "reason": "Customer maintains sustained engagement by asking multiple follow-up questions across different service components, demonstrating genuine continued interest."
                               }
                             ],
                             "overall_reason": "This lead shows strong engagement with detailed product inquiries and positive value recognition. The customer's focus on practical implementation details and sustained questioning pattern indicates genuine interest and potential for conversion."
                           }
                           </message>

                           ### Actual Input:
                           <message role="user">
                           Please score the sales lead based on the following chat history and scoring criteria:

                           ### Chat History:
                           ```
                           {{$chat_history}}
                           ```

                           ### Scoring Criteria:
                           {{$lead_score_criteria}}

                           Please return the scoring results in the specified JSON format, ensuring accurate scores and reasoning for each criterion.
                           </message>
                           """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
                    new InputVariable
                    {
                        Name = "lead_score_criteria"
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description =
                        "Lead scoring results based on given criteria, including detailed scores for each criterion",
                    JsonSchema = """
                                 {
                                     "type": "object",
                                     "properties": {
                                         "criterion_scores": {
                                             "type": "array",
                                             "items": {
                                                 "type": "object",
                                                 "properties": {
                                                     "criterion_index": {
                                                         "type": "integer",
                                                         "description": "Index position of the scoring criterion in the input array (starting from 0)"
                                                     },
                                                     "score": {
                                                         "type": "integer",
                                                         "minimum": 0,
                                                         "maximum": 100,
                                                         "description": "Score for this criterion (0-100 points)"
                                                     },
                                                     "reason": {
                                                         "type": "string",
                                                         "description": "Specific reasoning for this criterion's score"
                                                     }
                                                 },
                                                 "required": ["criterion_index", "score", "reason"]
                                             }
                                         },
                                         "overall_reason": {
                                             "type": "string",
                                             "description": "Comprehensive analysis and reasoning for the overall scoring"
                                         }
                                     },
                                     "required": ["criterion_scores", "overall_reason"]
                                 }
                                 """,
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    },
                },
            }
        );

        var chatMessageContent = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "lead_score_criteria", JsonConvert.SerializeObject(leadScoreCriteria, new JsonSerializerSettings
                    {
                        StringEscapeHandling = StringEscapeHandling.Default
                    })
                },
                {
                    "chat_history", chatHistory
                },
            }
        );

        var content = chatMessageContent?.Content ?? "{}";

        var result = JsonConvert.DeserializeObject<LeadScoreCriterionEvaluationResult>(content);
        if (result is null)
        {
            throw new Exception("Failed to deserialize lead score evaluation result");
        }

        int totalWeightedScore = 0;
        int totalWeight = 0;

        for (int i = 0; i < result.CriterionScores.Count && i < leadScoreCriteria.Count; i++)
        {
            var criterionScore = result.CriterionScores[i];
            if (criterionScore.CriterionIndex >= 0 && criterionScore.CriterionIndex < leadScoreCriteria.Count)
            {
                var criterion = leadScoreCriteria[criterionScore.CriterionIndex];
                totalWeightedScore += criterionScore.Score * criterion.Weight;
                totalWeight += criterion.Weight;
            }
        }

        var finalScore = totalWeight > 0 ? (totalWeightedScore / totalWeight) : 0;

        return new CalculateLeadScoreResult(finalScore, result.OverallReason);
    }

    public ExitConditionResult? CheckEarlyExitConditions(
        List<ExitCondition> exitConditions,
        int? score,
        int confidenceScore)
    {
        var orderedExitConditions = exitConditions
            .Where(c => c.Type is "low_confidence" or "lead_score")
            .OrderBy(c => c.Type == "lead_score" ? 0 : 1)
            .ToList();

        foreach (var condition in orderedExitConditions)
        {
            // Skip if no operator or values are defined
            if (string.IsNullOrEmpty(condition.Operator) || condition.Values == null || condition.Values.Count == 0)
            {
                continue;
            }

            var conditionMet = false;
            var reason = string.Empty;
            if (condition.Type == "low_confidence")
            {
                conditionMet = EvaluateCondition(confidenceScore, condition.Operator, condition.Values);
                reason =
                    $"Confidence score of {confidenceScore} meets the {condition.Operator} condition with threshold {string.Join(", ", condition.Values)}";
            }
            else if (condition.Type == "lead_score" && score.HasValue)
            {
                conditionMet = EvaluateCondition(score.Value, condition.Operator, condition.Values);
                reason =
                    $"Score of {score} meets the {condition.Operator} condition with threshold {string.Join(", ", condition.Values)}";
            }

            if (!conditionMet)
            {
                continue;
            }

            _logger.LogInformation(
                "Early exit condition met: {ConditionTitle}. {Reason}",
                condition.Title,
                reason);

            return new ExitConditionResult(true, condition.Title, reason);
        }

        return null;
    }

    private bool EvaluateCondition(int value, string @operator, List<object> values)
    {
        try
        {
            switch (@operator.ToLowerInvariant())
            {
                case "larger_than":
                    if (values.Count > 0 && int.TryParse(values[0].ToString(), out int threshold1))
                    {
                        return value > threshold1;
                    }

                    break;

                case "smaller_than":
                    if (values.Count > 0 && int.TryParse(values[0].ToString(), out int threshold2))
                    {
                        return value < threshold2;
                    }

                    break;

                case "is_between":
                    if (values.Count >= 2 &&
                        int.TryParse(values[0].ToString(), out int lowerBound) &&
                        int.TryParse(values[1].ToString(), out int upperBound))
                    {
                        return value >= lowerBound && value <= upperBound;
                    }

                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Error evaluating condition with operator {Operator} and values {Values}",
                @operator,
                string.Join(", ", values));
        }

        return false;
    }

    public async Task<AddLabelResult?> GetAddLabelAsync(
        Kernel kernel,
        string chatHistory,
        List<Label> labels,
        string instructions
    )
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(
           SemanticKernelExtensions.S_GPT_4_1
       );

        var function = kernel.CreateFunctionFromPrompt(
          new PromptTemplateConfig
          {
              Name = "AddLabel",
              Description = "Add label based on the chat history and available labels.",
              Template = """
                        <message role="system">
                        You are a specialized AI assistant responsible for analyzing customer conversations and assigning appropriate labels based on customer behavior, intent, and interaction patterns.

                        ### Goal:
                        Analyze the provided chat history and assign the most appropriate label from the available options based on the given instructions.

                        ### Analysis Approach:
                        - Focus on the customer's intent, behavior, and explicit statements
                        - Consider the overall context and conversation flow
                        - Look for clear indicators that match the available labels
                        - Prioritize recent messages and overall conversation tone
                        - Consider customer satisfaction, interest level, and purchase intent

                        ### Instructions:
                        {{$instructions}}

                        ### Output Requirements:
                        - Select the most appropriate hashtag from the available labels
                        - Provide a clear reason for your selection
                        - If no label is clearly applicable, select the most suitable one or provide reasoning for no match and set the hashtag to empty string
                        </message>

                        ### Example 1 - Mobile Service Inquiry:
                        <message role="user">
                        Available Labels: [{"hashtag": "mobile"}, {"hashtag": "healthcare"}, {"hashtag": "broadband"}]

                        Chat History:
                        User: 我想了解一下你们的手机计划，有什么优惠吗？
                        Bot: 我们目前有多种手机计划，包括42Mbps本地数据计划，月费只需$78。
                        User: 这个计划包含多少数据？
                        Bot: 包含每月10GB本地数据及1GB中国内地及澳门数据。
                        User: 听起来不错，我想了解更多详情。
                        </message>

                        <message role="assistant">
                        {"hashtag": "mobile", "reason": "Customer explicitly inquired about mobile plans, and the entire conversation revolves around mobile services, including data packages, pricing, and other mobile service-related content."}
                        </message>

                        ### Example 2 - Healthcare Service Discussion:
                        <message role="user">
                        Available Labels: [{"hashtag": "mobile"}, {"hashtag": "healthcare"}, {"hashtag": "broadband"}]

                        Chat History:
                        User: 我看到你们有医疗保健服务，能详细介绍一下吗？
                        Bot: 我们提供Bowtie四合一医疗服务计划，月费$99，包含无限视像会诊、牙科清洁等。
                        User: 视像会诊的时间是什么时候？
                        Bot: 服务时间为周一至周五上午9时至下午7时，周六上午9时至下午1时。
                        User: 这个服务很有用，我想订购。
                        </message>

                        <message role="assistant">
                        {"hashtag": "healthcare", "reason": "Customer inquired about healthcare services, and the conversation involves medical service plans, video consultation schedules, and other healthcare-related topics."}
                        </message>

                        ### Example 3 - Broadband Internet Inquiry:
                        <message role="user">
                        Available Labels: [{"hashtag": "mobile"}, {"hashtag": "healthcare"}, {"hashtag": "broadband"}]

                        Chat History:
                        User: Can I upgrade my home internet plan to faster speed?
                        Bot: Yes, we have several broadband options. Our 1000M plan offers excellent speed for $109/month.
                        User: What's the installation process like?
                        Bot: Installation typically takes 2-3 hours and includes router setup and testing.
                        User: Perfect, I'd like to proceed with the upgrade.
                        </message>

                        <message role="assistant">
                        {"hashtag": "broadband", "reason": "Customer is inquiring about home internet upgrade, discussing broadband speeds, installation process, which are all related to broadband services."}
                        </message>

                        ### Example 4 - Mixed Inquiry with Mobile Focus:
                        <message role="user">
                        Available Labels: [{"hashtag": "mobile"}, {"hashtag": "healthcare"}, {"hashtag": "broadband"}]

                        Chat History:
                        User: 我想了解一下你们的服务，有手机和宽带套餐吗？
                        Bot: 我们有手机和宽带的组合套餐，可以为您节省费用。
                        User: 手机计划的数据够用吗？我主要关心手机流量。
                        Bot: 我们的手机计划提供充足的数据流量，还有中国漫游数据。
                        User: 太好了，我主要需要手机服务，宽带暂时不用。
                        </message>

                        <message role="assistant">
                        {"hashtag": "mobile", "reason": "Although the customer initially inquired about both mobile and broadband packages, the conversation focus and main concern were on mobile services, particularly data usage. The customer ultimately clarified that they primarily need mobile services."}
                        </message>

                        ### Example 5 - Unclear Intent:
                        <message role="user">
                        Available Labels: [{"hashtag": "mobile"}, {"hashtag": "healthcare"}, {"hashtag": "support"}]

                        Chat History:
                        User: 你好，我想咨询一下。
                        Bot: 您好，很高兴为您服务，请问有什么可以帮助您的？
                        User: 我想了解一下你们的服务。
                        Bot: 我们提供多种服务，包括手机计划、医疗保健服务等，您对哪方面比较感兴趣？
                        User: 我再想想，谢谢。
                        </message>

                        <message role="assistant">
                        {"hashtag": "", "reason": "Customer's inquiry was too general without expressing clear interest in any specific service. The conversation ended without determining specific needs, making it impossible to accurately assign a label."}
                        </message>

                        ### Actual Input:
                        <message role="user">
                        Available Labels: {{$labels}}

                        Chat History:
                        {{$chat_history}}
                        </message>
                       """,
              InputVariables = new List<InputVariable>
              {
                    new InputVariable
                    {
                        Name = "labels"
                    },
                    new InputVariable
                    {
                        Name = "instructions"
                    },
                    new InputVariable
                    {
                        Name = "chat_history"
                    },
              },
              OutputVariable = new OutputVariable
              {
                  Description = "The selected label and reason in JSON format.",
                  JsonSchema = """
                                 {
                                     "type": "object",
                                     "properties": {
                                         "hashtag": {
                                             "type": "string"
                                         },
                                         "reason": {
                                             "type": "string"
                                         }
                                     }
                                 }
                                 """,
              },
              ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
              {
                  {
                      promptExecutionSettings.ServiceId!, promptExecutionSettings
                  },
              },
          }
      );

        var chatMessageContent = await function.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "chat_history", chatHistory
                },
                {
                    "labels", JsonConvert.SerializeObject(labels.Select(l => new { hashtag = l.Hashtag }).ToList())
                },
                {
                    "instructions", instructions
                },
            }
        );

        var content = chatMessageContent?.Content ?? "{}";

        var result = JsonConvert.DeserializeObject<AddLabelResult>(content);
        if (result is null)
        {
            throw new Exception("Failed to deserialize add label result");
        }

        return result;
    }
}
