﻿using System.ComponentModel.DataAnnotations;
using Sleekflow.Caches;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.SchemafulObjects.Utils;

public interface ISchemafulObjectValidator
{
    Task ValidateIsExceedMaximumSchemafulObjectNumPerCompanyAsync(
        CrmHubConfig crmHubConfig,
        string sleekflowCompanyId);

    Task ValidateSleekflowUserProfileIdOccupancyAsync(
        Schema schema,
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string? excludedSchemafulObjectId = null);

    Task ValidatePrimaryPropertyValueAsync(
        Schema schema,
        string sleekflowCompanyId,
        string primaryPropertyValue);

    /// <summary>
    /// Get number of schema that counted for usage limitation.
    /// </summary>
    /// <param name="sleekflowCompanyId">Sleekflow Company Id.</param>
    /// <returns>Total count.</returns>
    Task<int> GetSchemaNumForUsageLimit(string sleekflowCompanyId);

    /// <summary>
    /// Get number of schemaful object that counted for usage limitation.
    ///
    /// Count schemas which are not deleted
    /// </summary>
    /// <param name="sleekflowCompanyId">Sleekflow Company Id.</param>
    /// <returns>Total count.</returns>
    Task<int> GetSchemafulObjectNumPerCompanyForUsageLimitAsync(string sleekflowCompanyId);
}

public class SchemafulObjectValidator : ISchemafulObjectValidator, IScopedService
{
    private readonly ISchemaRepository _schemaRepository;
    private readonly ISchemafulObjectRepository _schemafulObjectRepository;
    private readonly ICacheService _cacheService;

    public SchemafulObjectValidator(
        ISchemaRepository schemaRepository,
        ISchemafulObjectRepository schemafulObjectRepository,
        ICacheService cacheService)
    {
        _schemaRepository = schemaRepository;
        _schemafulObjectRepository = schemafulObjectRepository;
        _cacheService = cacheService;
    }

    public async Task ValidateSleekflowUserProfileIdOccupancyAsync(
        Schema schema,
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string? excludedSchemafulObjectId = null)
    {
        if (string.IsNullOrEmpty(sleekflowUserProfileId))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult("SleekflowUserProfileId is required")
                });
        }

        // if relationship is one-to-one, check if user profile is occupied
        if (schema.RelationshipType == SchemaRelationshipTypes.OneToOne &&
            await IsSleekflowUserProfileOccupiedAsync(
                sleekflowCompanyId,
                schema.Id,
                sleekflowUserProfileId,
                excludedSchemafulObjectId))
        {
            throw new SfCustomObjectSleekflowUserProfileIdOccupiedException(sleekflowUserProfileId);
        }
    }

    public async Task ValidatePrimaryPropertyValueAsync(
        Schema schema,
        string sleekflowCompanyId,
        string primaryPropertyValue)
    {
        if (schema.PrimaryProperty.PrimaryPropertyConfig.IsAutoGenerated)
        {
            return;
        }

        if (string.IsNullOrEmpty(primaryPropertyValue))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult("Primary property value must be provided for this schema")
                });
        }

        if (await IsPrimaryPropertyValueOccupiedAsync(
                sleekflowCompanyId,
                schema.Id,
                primaryPropertyValue))
        {
            throw new SfCustomObjectPrimaryPropertyValueOccupiedException(primaryPropertyValue);
        }
    }

    public async Task<int> GetSchemaNumForUsageLimit(string sleekflowCompanyId)
    {
        return await _schemaRepository.GetUsageLimitCountableSchemasCountAsync(sleekflowCompanyId);
    }

    public async Task<int> GetSchemafulObjectNumPerCompanyForUsageLimitAsync(string sleekflowCompanyId)
    {
        var cacheKey = $"{nameof(_schemaRepository.GetUsageLimitCountableSchemaIdsAsync)}-{sleekflowCompanyId}";
        var schemaIds = await _cacheService.CacheAsync(
            cacheKey,
            async () => await _schemaRepository.GetUsageLimitCountableSchemaIdsAsync(sleekflowCompanyId),
            TimeSpan.FromMinutes(5));

        var count = await _schemafulObjectRepository.GetSchemafulObjectCountBySchemasAsync(
            sleekflowCompanyId,
            schemaIds);

        return count;
    }

    private async Task<bool> IsSleekflowUserProfileOccupiedAsync(
        string sleekflowCompanyId,
        string schemaId,
        string sleekflowUserProfileId,
        string? excludedSchemafulObjectId = null)
    {
        var filterGroups = new List<SchemafulObjectQueryBuilder.FilterGroup>()
        {
            new SchemafulObjectQueryBuilder.FilterGroup(
                new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                {
                    new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                        IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                        "=",
                        sleekflowUserProfileId,
                        false)
                })
        };

        // if exclude itself
        if (!string.IsNullOrEmpty(excludedSchemafulObjectId))
        {
            filterGroups.Add(
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            "id",
                            "!=",
                            excludedSchemafulObjectId,
                            false)
                    }));
        }

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>
            {
                new SchemafulObjectQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            filterGroups,
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            sleekflowCompanyId,
            schemaId,
            true);

        var rawRecords =
            await _schemafulObjectRepository.GetObjectsAsync<Dictionary<string, object?>>(queryDefinition, 1);

        return (long) rawRecords[0]["count"]! > 0;
    }

    private async Task<bool> IsPrimaryPropertyValueOccupiedAsync(
        string sleekflowCompanyId,
        string schemaId,
        string primaryPropertyValue)
    {
        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>
            {
                new SchemafulObjectQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            SchemafulObject.PropertyNamePrimaryPropertyValue,
                            "=",
                            primaryPropertyValue,
                            false)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            sleekflowCompanyId,
            schemaId,
            true);

        var rawRecords =
            await _schemafulObjectRepository.GetObjectsAsync<Dictionary<string, object?>>(queryDefinition, 1);

        return (long) rawRecords[0]["count"]! > 0;
    }

    [Obsolete("Temporarily deprecated https://app.clickup.com/t/9008009945/DEVS-7787")]
    private async Task ValidateIsExceedMaximumSchemafulObjectNumPerSchemaAsync(
        CrmHubConfig crmHubConfig,
        string schemaId,
        string sleekflowCompanyId)
    {
        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>
            {
                new SchemafulObjectQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            new List<SchemafulObjectQueryBuilder.FilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            sleekflowCompanyId,
            schemaId,
            true);

        var rawRecords =
            await _schemafulObjectRepository.GetObjectsAsync<Dictionary<string, object?>>(queryDefinition, 1);

        var schemafulObjectCount = (long) rawRecords[0]["count"]!;

        if (schemafulObjectCount >= crmHubConfig.GetOffsetAppliedUsageLimit().CustomObjectMaximumSchemafulObjectNumPerSchema)
        {
            throw new SfCrmHubExceedUsageException(UsageLimit.PropertyNameCustomObjectMaximumSchemafulObjectNumPerSchema);
        }
    }

    public async Task ValidateIsExceedMaximumSchemafulObjectNumPerCompanyAsync(
        CrmHubConfig crmHubConfig,
        string sleekflowCompanyId)
    {
        var cacheKey = $"{nameof(GetSchemafulObjectNumPerCompanyForUsageLimitAsync)}-{sleekflowCompanyId}";
        var count = await _cacheService.CacheAsync(
            cacheKey,
            async () => await GetSchemafulObjectNumPerCompanyForUsageLimitAsync(sleekflowCompanyId),
            TimeSpan.FromMinutes(5));

        if (count >= crmHubConfig.GetOffsetAppliedUsageLimit().CustomObjectMaximumSchemafulObjectNumPerCompany)
        {
            throw new SfCrmHubExceedUsageException(UsageLimit.PropertyNameCustomObjectMaximumSchemafulObjectNumPerCompany);
        }
    }
}