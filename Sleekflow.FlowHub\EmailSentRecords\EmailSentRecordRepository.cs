using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.EmailSentRecords;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.EmailSentRecords;

public interface IEmailSentRecordRepository : IRepository<EmailSentRecord>
{
}

public class EmailSentRecordRepository : BaseRepository<EmailSentRecord>, IEmailSentRecordRepository, IScopedService
{
    public EmailSentRecordRepository(
        ILogger<EmailSentRecordRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}