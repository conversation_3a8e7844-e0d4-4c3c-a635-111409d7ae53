using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetTicketExportDataOutputTicket
{
    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("created_by")]
    public string CreatedBy { get; set; }

    [JsonProperty("created_by_email")]
    public string CreatedByEmail { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("channel")]
    public GetTicketExportDataOutputChannel Channel { get; set; }

    [JsonProperty("status_id")]
    public string StatusId { get; set; }

    [JsonProperty("status_name")]
    public string StatusName { get; set; }

    [JsonProperty("priority_id")]
    public string PriorityId { get; set; }

    [JsonProperty("priority_name")]
    public string PriorityName { get; set; }

    [JsonProperty("due_date")]
    public DateTimeOffset? DueDate { get; set; }

    [JsonProperty("type_id")]
    public string TypeId { get; set; }

    [JsonProperty("type_name")]
    public string TypeName { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("first_assignee")]
    public string FirstAssignee { get; set; }

    [JsonProperty("first_assignee_email")]
    public string FirstAssigneeEmail { get; set; }

    [JsonProperty("current_assignee")]
    public string CurrentAssignee { get; set; }

    [JsonProperty("current_assignee_email")]
    public string CurrentAssigneeEmail { get; set; }

    [JsonProperty("contact_name")]
    public string ContactName { get; set; }

    [JsonProperty("contact_phone_number")]
    public string ContactPhoneNumber { get; set; }

    [JsonProperty("contact_email")]
    public string ContactEmail { get; set; }

    [JsonProperty("first_respondent")]
    public string FirstRespondent { get; set; }

    [JsonProperty("first_respondent_email")]
    public string FirstRespondentEmail { get; set; }

    [JsonProperty("first_response_timestamp")]
    public DateTimeOffset? FirstResponseTimeStamp { get; set; }

    [JsonProperty("resolution_agent")]
    public string ResolutionAgent { get; set; }

    [JsonProperty("resolution_agent_email")]
    public string ResolutionAgentEmail { get; set; }

    [JsonProperty("resolution_timestamp")]
    public DateTimeOffset? ResolutionTimeStamp { get; set; }

    [JsonProperty("ticket_reassigned_before_resolution")]
    public bool TicketReassignedBeforeResolution { get; set; }

    [JsonConstructor]
    public GetTicketExportDataOutputTicket(
        DateTimeOffset createdAt,
        string createdBy,
        string createdByEmail,
        string id,
        string title,
        GetTicketExportDataOutputChannel channel,
        string statusId,
        string statusName,
        string priorityId,
        string priorityName,
        DateTimeOffset? dueDate,
        string typeId,
        string typeName,
        string description,
        string firstAssignee,
        string firstAssigneeEmail,
        string currentAssignee,
        string currentAssigneeEmail,
        string contactName,
        string contactPhoneNumber,
        string contactEmail,
        string firstRespondent,
        string firstRespondentEmail,
        DateTimeOffset? firstResponseTimeStamp,
        string resolutionAgent,
        string resolutionAgentEmail,
        DateTimeOffset? resolutionTimeStamp,
        bool ticketReassignedBeforeResolution)
    {
        CreatedAt = createdAt;
        CreatedBy = createdBy;
        CreatedByEmail = createdByEmail;
        Id = id;
        Title = title;
        Channel = channel;
        StatusId = statusId;
        StatusName = statusName;
        PriorityId = priorityId;
        PriorityName = priorityName;
        DueDate = dueDate;
        TypeId = typeId;
        TypeName = typeName;
        Description = description;
        FirstAssignee = firstAssignee;
        FirstAssigneeEmail = firstAssigneeEmail;
        CurrentAssignee = currentAssignee;
        CurrentAssigneeEmail = currentAssigneeEmail;
        ContactName = contactName;
        ContactPhoneNumber = contactPhoneNumber;
        ContactEmail = contactEmail;
        FirstRespondent = firstRespondent;
        FirstRespondentEmail = firstRespondentEmail;
        FirstResponseTimeStamp = firstResponseTimeStamp;
        ResolutionAgent = resolutionAgent;
        ResolutionAgentEmail = resolutionAgentEmail;
        ResolutionTimeStamp = resolutionTimeStamp;
        TicketReassignedBeforeResolution = ticketReassignedBeforeResolution;
    }
}

[SwaggerInclude]
public class GetTicketExportDataOutput
{
    [JsonProperty("ticket")]
    public GetTicketExportDataOutputTicket Ticket { get; set; }

    [JsonConstructor]
    public GetTicketExportDataOutput(GetTicketExportDataOutputTicket ticket)
    {
        Ticket = ticket;
    }
}

public class GetTicketExportDataOutputChannel
{
    [JsonProperty("channel_type")]
    public string ChannelType { get; set; }

    [JsonProperty("channel_identity_id")]
    public string ChannelIdentityId { get; set; }

    [JsonProperty("channel_name")]
    public string ChannelName { get; set; }


    [JsonConstructor]
    public GetTicketExportDataOutputChannel(
        string channelType,
        string channelIdentityId,
        string channelName)
    {
        ChannelType = channelType;
        ChannelIdentityId = channelIdentityId;
        ChannelName = channelName;
    }
}