﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Sleekflow.Caches;
using Sleekflow.FlowHub.Limiters;
using StackExchange.Redis;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class RequestRateLimiterIntegrationTests
{
    public const string OperationName = "integration-test";
    public const string RequestKey = "rate-limiter";
    public const int WindowSizeSeconds = 60;

    [Test]
    [TestCase(1)]
    [TestCase(5)]
    [TestCase(10)]
    public async Task IsHitLimitAsync_GivenHitLimit_ShouldReturnTrue(int limit)
    {
        // Arrange
        var cacheConfig = new MyCacheConfig();
        var connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(cacheConfig.RedisConnStr);
        var loggerMock = new Mock<ILogger<RequestRateLimiter>>();

        var rateLimiter = new RequestRateLimiter(
            connectionMultiplexer,
            cacheConfig,
            loggerMock.Object);

        var tasks = Enumerable.Range(1, limit)
            .Select(_ => rateLimiter.IsHitLimitAsync(
                OperationName,
                RequestKey,
                WindowSizeSeconds,
                limit));

        await Task.WhenAll(tasks);

        // Act
        var isHitLimit = await rateLimiter.IsHitLimitAsync(
            OperationName,
            RequestKey,
            WindowSizeSeconds,
            limit);

        // Assert
        Assert.That(isHitLimit, Is.True);
        loggerMock.Verify(
            x =>
                x.Log(
                    It.Is<LogLevel>(level => level == LogLevel.Warning),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()),
            Times.Once);

        // Clean up
        connectionMultiplexer.GetDatabase()
            .KeyDelete(new RedisKey($"{cacheConfig.CachePrefix}-{RequestKey}"));
    }

    [Test]
    [TestCase(1)]
    [TestCase(5)]
    [TestCase(10)]
    public async Task IsHitLimitAsync_GivenNotHitLimit_ShouldReturnFalse(int limit)
    {
        // Arrange
        var cacheConfig = new MyCacheConfig();
        var connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(cacheConfig.RedisConnStr);

        var rateLimiter = new RequestRateLimiter(
            connectionMultiplexer,
            cacheConfig,
            NullLogger<RequestRateLimiter>.Instance);

        var tasks = Enumerable.Range(1, limit - 1)
            .Select(_ => rateLimiter.IsHitLimitAsync(
                OperationName,
                RequestKey,
                WindowSizeSeconds,
                limit));

        await Task.WhenAll(tasks);

        // Act
        var isHitLimit = await rateLimiter.IsHitLimitAsync(
            OperationName,
            RequestKey,
            WindowSizeSeconds,
            limit);

        // Assert
        Assert.That(isHitLimit, Is.False);

        // Clean up
        connectionMultiplexer.GetDatabase()
            .KeyDelete(new RedisKey($"{cacheConfig.CachePrefix}-{RequestKey}"));
    }

    [Test]
    [TestCase(1)]
    [TestCase(5)]
    [TestCase(10)]
    public async Task IsHitLimitAsync_GivenNotHitLimitAfterTheWindow_ShouldReturnFalse(int limit)
    {
        // Arrange
        const int windowSizeSeconds = 3;
        var cacheConfig = new MyCacheConfig();
        var connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(cacheConfig.RedisConnStr);

        var rateLimiter = new RequestRateLimiter(
            connectionMultiplexer,
            cacheConfig,
            NullLogger<RequestRateLimiter>.Instance);

        var tasks = Enumerable.Range(1, limit)
            .Select(_ => rateLimiter.IsHitLimitAsync(
                OperationName,
                RequestKey,
                windowSizeSeconds,
                limit));

        await Task.WhenAll(tasks);

        await Task.Delay((windowSizeSeconds * 1000) + 1);

        // Act
        var isHitLimit = await rateLimiter.IsHitLimitAsync(
            OperationName,
            RequestKey,
            WindowSizeSeconds,
            limit);

        // Assert
        Assert.That(isHitLimit, Is.False);

        // Clean up
        connectionMultiplexer.GetDatabase()
            .KeyDelete(new RedisKey($"{cacheConfig.CachePrefix}-{RequestKey}"));
    }

    private class MyCacheConfig : ICacheConfig
    {
        public string CachePrefix => "INTEGRATION-TEST-RATE-LIMITER";

        public string RedisConnStr => "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False";
    }
}