using GraphApi.Client.ApiClients.Exceptions;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumer
    : IConsumer<OnCloudApiAccumulateHalfHourConversationUsageTransactionEvent>
{
    private readonly IBus _bus;
    private readonly ILockService _lockService;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly ILogger<OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumer> _logger;

    public OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumer(
        IBus bus,
        ILockService lockService,
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        ILogger<OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumer> logger)
    {
        _bus = bus;
        _logger = logger;
        _lockService = lockService;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public async Task Consume(
        ConsumeContext<OnCloudApiAccumulateHalfHourConversationUsageTransactionEvent> context)
    {
        var onCloudApiHalfHourConversationUsageTransactionLogInsertEvent = context.Message;
        var facebookBusinessId =
            onCloudApiHalfHourConversationUsageTransactionLogInsertEvent.FacebookBusinessId;

        var isAnyConversationAnalyticsTransactionLogInserted = false;
        var cancellationToken = context.CancellationToken;

        var retryCount = context.GetRedeliveryCount();
        if (context.GetRedeliveryCount() > 10)
        {
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId,
                "ConversationUsageTransactionLock"
            },
            TimeSpan.FromSeconds(
                60 *
                (OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumerDefinition
                     .LockDuration +
                 OnCloudApiAccumulateHalfHourConversationUsageTransactionEventConsumerDefinition
                     .MaxAutoRenewDuration)),
            cancellationToken);
        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));
            return;
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);
        if (businessBalance is null)
        {
            _logger.LogWarning(
                "Unable to locate business account balance object with {FacebookBusinessId}",
                facebookBusinessId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);
            return;
        }

        var lastConversationUsageInsertTimestamp =
            businessBalance.ConversationUsageInsertState.LastConversationUsageInsertTimestamp;
        var currentDateTimeOffset = DateTimeOffset.UtcNow;
        var wabaConversationInsertionExceptions =
            businessBalance.ConversationUsageInsertState.WabaConversationInsertionExceptions;

        // Remove the offset of the NextConversationUsageInsertTimestamp to keep it either x.00 or x.30
        var nextConversationUsageInsertTimestamp =
            currentDateTimeOffset.ToUnixTimeSeconds() - (currentDateTimeOffset.ToUnixTimeSeconds() % 1800L);

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);
        foreach (var waba in wabas)
        {
            var isWabaContainsInsertionException = wabaConversationInsertionExceptions.ContainsKey(waba.FacebookWabaId);
            try
            {
                var actualLastConversationUsageInsertTimestamp = lastConversationUsageInsertTimestamp;

                if (isWabaContainsInsertionException)
                {
                    actualLastConversationUsageInsertTimestamp =
                        wabaConversationInsertionExceptions[waba.FacebookWabaId].LastConversationUsageInsertTimestamp;
                }

                foreach (var (
                             actualConversationUsageInsertStartTimestamp,
                             actualConversationUsageInsertEndTimestamp) in CloudApiUtils.ExtractDateTimes(
                             actualLastConversationUsageInsertTimestamp,
                             nextConversationUsageInsertTimestamp))
                {
                    try
                    {
                        var isConversationAnalyticsTransactionLogInserted =
                            await _businessBalanceTransactionLogService.ConversationAnalyticsInsertionAsync(
                                waba,
                                businessBalance,
                                actualConversationUsageInsertStartTimestamp,
                                actualConversationUsageInsertEndTimestamp);

                        isAnyConversationAnalyticsTransactionLogInserted = isConversationAnalyticsTransactionLogInserted
                            ? isConversationAnalyticsTransactionLogInserted
                            : isAnyConversationAnalyticsTransactionLogInserted;
                    }
                    catch (Exception exception)
                    {
                        // Catch Error Message Else throw or update conversation insertion exception list
                        _logger.LogError(exception, "An unknown error has occurred");

                        if (exception is GraphApiClientException graphApiClientException)
                        {
                            var graphApiError = graphApiClientException?.ErrorApiResponse?.Error;
                            if (graphApiError is { Code: 100, ErrorSubcode: 2593027 })
                            {
                                _logger.LogWarning(
                                    "You can query for insights once your have started sending messages from this WhatsApp Account. {Exception}",
                                    JsonConvert.SerializeObject(exception));
                                continue;
                            }
                        }

                        if (!isWabaContainsInsertionException)
                        {
                            wabaConversationInsertionExceptions.Add(
                                waba.FacebookWabaId,
                                new WabaConversationInsertionException(actualConversationUsageInsertStartTimestamp));
                        }
                        else
                        {
                            wabaConversationInsertionExceptions[waba.FacebookWabaId]
                                .LastConversationUsageInsertTimestamp = actualConversationUsageInsertStartTimestamp;
                        }

                        throw;
                    }
                }

                if (isWabaContainsInsertionException)
                {
                    wabaConversationInsertionExceptions.Remove(waba.FacebookWabaId);
                }
            }
            catch (Exception exception)
            {
                if (exception is GraphApiClientException graphApiClientException)
                {
                    var graphApiError = graphApiClientException?.ErrorApiResponse?.Error;
                    if (graphApiError is { Code: 100, ErrorSubcode: 33 })
                    {
                        _logger.LogWarning(
                            "Waba is no longer available. {Exception}",
                            JsonConvert.SerializeObject(exception));
                    }
                    else if (graphApiError is { Code: 1 } && waba.WabaPhoneNumbers.Count == 0)
                    {
                        _logger.LogWarning(
                            "An unknown error has occurred. OAuthException {Waba}/{Exception}",
                            JsonConvert.SerializeObject(waba),
                            JsonConvert.SerializeObject(exception));
                    }
                    else
                    {
                        _logger.LogError(
                            "Un-captured graph api client exception occur during ConversationAnalyticsInsertion {Waba}/{Exception}",
                            JsonConvert.SerializeObject(waba),
                            JsonConvert.SerializeObject(exception));
                    }
                }
                else
                {
                    _logger.LogError(
                        "Error occur during ConversationAnalyticsInsertion {Waba}/{Exception}",
                        JsonConvert.SerializeObject(waba),
                        JsonConvert.SerializeObject(exception));
                }
            }
        }

        var hasConversationUsagePeriodPassedSevenDay =
            (DateTimeOffset.FromUnixTimeSeconds(nextConversationUsageInsertTimestamp) -
             DateTimeOffset.FromUnixTimeSeconds(lastConversationUsageInsertTimestamp)).TotalDays >= 7;

        if (isAnyConversationAnalyticsTransactionLogInserted || hasConversationUsagePeriodPassedSevenDay)
        {
            if (hasConversationUsagePeriodPassedSevenDay)
            {
                nextConversationUsageInsertTimestamp = currentDateTimeOffset.AddHours(-1).ToUnixTimeSeconds() -
                                                       (currentDateTimeOffset.ToUnixTimeSeconds() % 1800L);
            }

            // send event Update Business balance last with event
            var onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent =
                new OnCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent(
                    nextConversationUsageInsertTimestamp,
                    businessBalance.Id,
                    facebookBusinessId,
                    isAnyConversationAnalyticsTransactionLogInserted,
                    wabaConversationInsertionExceptions);

            await _bus.Publish(
                onCloudApiAccumulateHalfHourConversationUsageTransactionInsertedEvent,
                cancellationToken);
        }

        if (!isAnyConversationAnalyticsTransactionLogInserted &&
            businessBalance.ConversationUsageInsertState
                .WabaConversationInsertionExceptions.Except(wabaConversationInsertionExceptions).Any())
        {
            var onCloudApiConversationUsageExceptionEvent =
                new OnCloudApiConversationUsageExceptionEvent(
                    businessBalance.Id,
                    facebookBusinessId,
                    wabaConversationInsertionExceptions);
            await _bus.Publish(onCloudApiConversationUsageExceptionEvent, cancellationToken);
        }

        await _lockService.ReleaseAsync(@lock, cancellationToken);
    }
}