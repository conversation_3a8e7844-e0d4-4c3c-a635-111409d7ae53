﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class GetSchemafulObjectSample : ITrigger<GetSchemafulObjectSample.GetSchemafulObjectSampleInput, GetSchemafulObjectSample.GetSchemafulObjectSampleOutput>
{
    private readonly ISchemaService _schemaService;

    public GetSchemafulObjectSample(ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public class GetSchemafulObjectSampleInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectSampleInput(string schemaId, string sleekflowCompanyId)
        {
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetSchemafulObjectSampleOutput
    {
        [JsonProperty("schemaful_object")]
        public SchemafulObjectDto SchemafulObject { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectSampleOutput(SchemafulObjectDto schemafulObject)
        {
            SchemafulObject = schemafulObject;
        }
    }

    public async Task<GetSchemafulObjectSampleOutput> F(GetSchemafulObjectSampleInput getSchemafulObjectSampleInput)
    {
        var schema = await _schemaService.GetAsync(
            getSchemafulObjectSampleInput.SchemaId,
            getSchemafulObjectSampleInput.SleekflowCompanyId);

        return new GetSchemafulObjectSampleOutput(
            new SchemafulObjectDto(SampleDataUtils.GetSampleSchemafulObject(schema)));
    }
}