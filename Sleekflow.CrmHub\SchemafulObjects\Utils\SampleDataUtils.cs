﻿using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;

namespace Sleekflow.CrmHub.SchemafulObjects.Utils;

public static class SampleDataUtils
{
    public static SchemafulObject GetSampleSchemafulObject(Schema schema)
    {
        var defaultPrimaryPropertyValue = GetPrimaryPropertyDefaultValue(schema.PrimaryProperty);
        var defaultDateTimeOffset = new DateTimeOffset(2001, 1, 1, 0, 0, 0, TimeSpan.Zero);

        var fullPropertyValues = schema.Properties.ToDictionary(
            p => p.UniqueName,
            GetPropertyDefaultValue);

        return new SchemafulObject(
            "sample_schemaful_object_id",
            schema.Id,
            defaultPrimaryPropertyValue,
            "sample_company_id",
            "sample-user-profile-id",
            fullPropertyValues,
            fullPropertyValues,
            null,
            defaultDateTimeOffset,
            defaultDateTimeOffset,
            null,
            null);
    }

    public static object? GetPropertyDefaultValue(Property property)
    {
        return property.DataType.Name switch
        {
            SchemaPropertyDataTypes.SingleLineText => "sample text",
            SchemaPropertyDataTypes.Numeric => 123,
            SchemaPropertyDataTypes.Decimal => 123.45,
            SchemaPropertyDataTypes.SingleChoice => property.Options![0].Value,
            SchemaPropertyDataTypes.MultipleChoice => property.Options!.Select(o => o.Value).ToList(), // i.e. a string of "option1,option2"
            SchemaPropertyDataTypes.Boolean => true,
            SchemaPropertyDataTypes.Date => "2001-01-01T16:00:00.000Z",
            SchemaPropertyDataTypes.DateTime => "2001-01-01T16:00:00.000Z",
            _ => throw new NotImplementedException()
        };
    }

    public static object? GetPropertyDefaultValueForCsv(Property property)
    {
        return property.DataType.Name switch
        {
            SchemaPropertyDataTypes.SingleLineText => "sample text",
            SchemaPropertyDataTypes.Numeric => 123,
            SchemaPropertyDataTypes.Decimal => 123.45,
            SchemaPropertyDataTypes.SingleChoice => property.Options![0].Value,
            SchemaPropertyDataTypes.MultipleChoice => new JArray(property.Options!.Select(o => o.Value)).ToString(),
            SchemaPropertyDataTypes.Boolean => true,
            SchemaPropertyDataTypes.Date => "2001-01-01T16:00:00.000Z",
            SchemaPropertyDataTypes.DateTime => "2001-01-01T16:00:00.000Z",
            _ => throw new NotImplementedException()
        };
    }

    public static string GetPrimaryPropertyDefaultValue(PrimaryProperty primaryProperty)
    {
        return primaryProperty.PrimaryPropertyConfig.Sequential is null
            ? "default_primary_property_value"
            : primaryProperty.PrimaryPropertyConfig.Sequential.SequencePrefix + "0000001";
    }
}