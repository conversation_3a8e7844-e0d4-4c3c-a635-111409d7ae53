using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Searches;

public class SearchFilter
{
    [Required]
    [StringLength(128, MinimumLength = 1)]
    [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [Required]
    [RegularExpression("^(=|>|<|>=|<=|!=|contains|startsWith)$")]
    [JsonProperty("operator")]
    public string Operator { get; set; }

    [JsonProperty("value")]
    public object? Value { get; set; }

    [JsonConstructor]
    public SearchFilter(string fieldName, string @operator, object? value)
    {
        FieldName = fieldName;
        Operator = @operator;
        Value = value;
    }
}