using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps;

public class ParallelStep : Step
{
    [Required]
    [JsonProperty("parallel_branches")]
    public List<ParallelStepBranch> ParallelBranches { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public ParallelStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        List<ParallelStepBranch> parallelBranches)
        : base(id, name, assign, nextStepId)
    {
        ParallelBranches = parallelBranches;
    }
}

public class ParallelStepBranch
{
    [JsonProperty("step")]
    public Step Step { get; set; }

    [JsonConstructor]
    public ParallelStepBranch(Step step)
    {
        Step = step;
    }
}