﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.Exceptions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Entities;

public static class EntityQueryBuilder
{
    public class FilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<IFilter> Filters { get; set; }

        [JsonConstructor]
        public FilterGroup(
            List<IFilter> filters)
        {
            Filters = filters;
        }
    }

    public interface IFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }
    }

    public class Filter : IFilter
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                "sleekflow_company_id",
                "sys_type_name",
                "sys_entity_type_name"
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(=|>|<|>=|<=|!=|contains|in|startsWith)$")]
        [JsonProperty("operator")]
        public string Operator { get; set; }

        [JsonProperty("value")]
        public object? Value { get; set; }

        [JsonConstructor]
        public Filter(string fieldName, string @operator, object? value)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
        }
    }

    public class PlainFilter : IFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }

        public PlainFilter(string fieldName, string @operator, object? value)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
        }
    }

    public class Sort
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                "sleekflow_company_id",
                "sys_type_name",
                "sys_entity_type_name"
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(asc|desc|ASC|DESC)$")]
        [JsonProperty("direction")]
        public string Direction { get; set; }

        [Required]
        [JsonProperty("is_case_sensitive")]
        public bool IsCaseSensitive { get; set; }

        [JsonConstructor]
        public Sort(string fieldName, string direction, bool isCaseSensitive)
        {
            FieldName = fieldName;
            Direction = direction;
            IsCaseSensitive = isCaseSensitive;
        }
    }

    public class GroupBy
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                "sleekflow_company_id",
                "sys_type_name",
                "sys_entity_type_name"
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [JsonProperty("is_case_sensitive")]
        public bool IsCaseSensitive { get; set; }

        [JsonConstructor]
        public GroupBy(string fieldName, bool isCaseSensitive)
        {
            FieldName = fieldName;
            IsCaseSensitive = isCaseSensitive;
        }
    }

    private sealed class Param
    {
        public string Name { get; set; }

        public object? Value { get; set; }

        public Param(string name, object? value)
        {
            Name = name;
            Value = value;
        }
    }

    public interface ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }
    }

    public class Select : ISelect
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                "sleekflow_company_id",
                "sys_type_name",
                "sys_entity_type_name"
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonProperty("as")]
        public string? As { get; set; }

        [JsonConstructor]
        public Select(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public class PlainSelect : ISelect
    {
        [Required]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonProperty("as")]
        public string? As { get; set; }

        [JsonConstructor]
        public PlainSelect(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public static QueryDefinition BuildQueryDef(
        List<ISelect> selects,
        string entityTypeName,
        List<FilterGroup> filterGroups,
        List<Sort> sorts,
        List<GroupBy> groupBys,
        string sysSleekflowCompanyId,
        string sysTypeName = "Entity")
    {
        var selectExpressions = new List<string>()
            {
                Capacity = selects.Count + groupBys.Count
            }
            .Concat(
                selects.Select(
                    f =>
                    {
                        if (f is PlainSelect)
                        {
                            return $"{f.FieldName} {f.As}";
                        }

                        return $"m[\"{f.FieldName}\"] {f.As}";
                    }))
            .Concat(
                groupBys.Select(
                    f =>
                    {
                        if (f.IsCaseSensitive)
                        {
                            return
                                $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"] as \"{f.FieldName}\"";
                        }

                        return $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValueCi}\"] as \"{f.FieldName}\"";
                    }))
            .ToList();

        var selectClause = selectExpressions.Any()
            ? "SELECT "
              + string.Join(", ", selectExpressions)
            : "SELECT *";

        var defaultFilterGroups = new List<FilterGroup>
        {
            new EntityQueryBuilder.FilterGroup(
                new List<IFilter>
                {
                    new PlainFilter(
                        CrmHubEntityContext.PropertyNameSysSleekflowCompanyId,
                        "=",
                        sysSleekflowCompanyId)
                }),
            new EntityQueryBuilder.FilterGroup(
                new List<IFilter>
                {
                    new PlainFilter(
                        CrmHubEntityContext.PropertyNameSysEntityTypeName,
                        "=",
                        entityTypeName)
                }),
            new EntityQueryBuilder.FilterGroup(
                new List<IFilter>
                {
                    new PlainFilter(
                        CrmHubEntityContext.PropertyNameSysTypeName,
                        "=",
                        sysTypeName)
                }),
        };

        var (@params, whereClause) = GetWhereClause(
            defaultFilterGroups
                .Concat(filterGroups)
                .ToList());

        var sortClause = sorts.Count == 0
            ? string.Empty
            : "ORDER BY "
              + string.Join(
                  ", ",
                  sorts.Select(
                      f =>
                      {
                          if (f.IsCaseSensitive)
                          {
                              return $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"] {f.Direction}";
                          }

                          return $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValueCi}\"] {f.Direction}";
                      }));

        var groupByClause = groupBys.Count == 0
            ? string.Empty
            : "GROUP BY "
              + string.Join(
                  ", ",
                  groupBys.Select(
                      f =>
                      {
                          if (f.IsCaseSensitive)
                          {
                              return $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"]";
                          }

                          return $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValueCi}\"]";
                      }));

        var clauses = new List<string>
            {
                selectClause,
                $"FROM {entityTypeName} m",
                whereClause,
                sortClause,
                groupByClause
            }
            .Where(l => !string.IsNullOrWhiteSpace(l))
            .ToList();

        var queryDefinition = @params
            .Aggregate(
                new QueryDefinition(string.Join("\n", clauses)),
                (qd, param) =>
                {
                    if (param.Value is JArray jArray)
                    {
                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<string>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<long>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<double>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        throw new SfValidationException(
                            new List<ValidationResult>()
                            {
                                new ValidationResult(
                                    "Invalid parameter value",
                                    new[]
                                    {
                                        param.Name
                                    })
                            });
                    }

                    return qd.WithParameter(param.Name, param.Value);
                });

        return queryDefinition;
    }

    private static (List<Param> Params, string Where) GetWhereClause(
        IReadOnlyCollection<FilterGroup> filterGroups)
    {
        var @params = new List<Param>();

        if (filterGroups.Count <= 0)
        {
            return (@params, string.Empty);
        }

        var stringBuilder = new StringBuilder();
        var i = 0;

        stringBuilder.Append("WHERE ");
        stringBuilder.AppendJoin(
            " AND ",
            filterGroups
                .Where(fg => fg.Filters.Any())
                .Select(
                    fg =>
                    {
                        var filterClauseStrs = fg.Filters.Select(
                            f =>
                            {
                                if (f is PlainFilter)
                                {
                                    return f.Operator switch
                                    {
                                        "contains" =>
                                            $"CONTAINS(m[\"{f.FieldName}\"], {GetSanitizedParamName(f.FieldName, i++)}, true)",
                                        "startsWith" =>
                                            $"STARTSWITH(m[\"{f.FieldName}\"], {GetSanitizedParamName(f.FieldName, i++)}, false)",
                                        "in" =>
                                            $"ARRAY_CONTAINS({GetSanitizedParamName(f.FieldName, i++)}, m[\"{f.FieldName}\"], false)",
                                        _ =>
                                            $"m[\"{f.FieldName}\"] {f.Operator} {GetSanitizedParamName(f.FieldName, i++)}"
                                    };
                                }

                                return f.Operator switch
                                {
                                    "contains" =>
                                        $"CONTAINS(m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"], {GetSanitizedParamName(f.FieldName, i++)}, true)",
                                    "startsWith" =>
                                        $"STARTSWITH(m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"], {GetSanitizedParamName(f.FieldName, i++)}, false)",
                                    "in" =>
                                        $"ARRAY_CONTAINS({GetSanitizedParamName(f.FieldName, i++)}, m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"], false)",
                                    _ =>
                                        $"m[\"{f.FieldName}\"][\"{SnapshottedValue.PropertyNameValue}\"] {f.Operator} {GetSanitizedParamName(f.FieldName, i++)}"
                                };
                            });

                        var sb = new StringBuilder();
                        sb.Append('(');
                        sb.AppendJoin(" OR ", filterClauseStrs);
                        sb.Append(')');

                        return sb.ToString();
                    }));

        i = 0;

        @params.AddRange(
            filterGroups
                .SelectMany(fg => fg.Filters)
                .Select(f => new Param(GetSanitizedParamName(f.FieldName, i++), f.Value)));

        return (@params, stringBuilder.ToString());
    }

    private static string GetSanitizedParamName(string name, int index)
    {
        return "@"
               + name
                   .Replace("[", "_")
                   .Replace("]", "_")
                   .Replace(".", "_")
                   .Replace(":", "_")
               + "_" + index;
    }
}