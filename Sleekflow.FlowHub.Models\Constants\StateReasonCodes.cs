﻿namespace Sleekflow.FlowHub.Models.Constants;

public static class StateReasonCodes
{
    // Cancelled
    public const string ManualCancellation = "manual_cancellation";
    public const string VersionedWorkflowOutdated = "versioned_workflow_outdated";
    public const string VersionedWorkflowDisabled = "versioned_workflow_disabled";
    public const string VersionedWorkflowDeleted = "versioned_workflow_deleted";

    // Blocked
    public const string EnrollmentRateLimited = "enrollment_rate_limited";
    public const string EnrollOnlyOncePolicy = "enroll_only_once_policy";
    public const string EnrollAgainOnFailureOnlyPolicy = "enroll_again_on_failure_only_policy";

    // Restricted
    public const string EnrollmentUsageLimitExceeded = "enrollment_usage_limit_exceeded";

    // Abandoned
    public const string ContactPropertyChanged = "contact_property_changed";
    public const string ScheduledEnrollmentInternalError = "scheduled_enrollment_internal_error";
    public const string ScheduledEnrollmentConditionNotFulfilled = "scheduled_enrollment_condition_not_fulfilled";
    public const string ScheduledEnrollmentFlowHubDisabled = "scheduled_enrollment_flowhub_disabled";
    public const string ScheduledEnrollmentWorkflowExecutionLimitExceeded = "scheduled_enrollment_workflow_execution_limit_exceeded";
    public const string SchemafulObjectPropertyChanged = "schemaful_object_property_changed";
}