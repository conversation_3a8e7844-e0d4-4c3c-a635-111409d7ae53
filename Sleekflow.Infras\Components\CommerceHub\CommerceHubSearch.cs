using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Newtonsoft.Json;
using Pulumi.AzureNative.Resources;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Products;
using Search = Pulumi.AzureNative.Search;

namespace Sleekflow.Infras.Components.CommerceHub;

public class CommerceHubSearch
{
    private readonly ResourceGroup _resourceGroup;

    private const string ConnectionString =
        "AccountEndpoint=https://sleekflow2bd1537b.documents.azure.com:443/;AccountKey=****************************************************************************************;Database=commercehubdb;";

    public CommerceHubSearch(
        ResourceGroup resourceGroup)
    {
        _resourceGroup = resourceGroup;
    }

    public CommerceHubSearch()
    {
        _resourceGroup = null!;
    }

    public Search.Service InitCommerceHubSearch()
    {
        var commerceHubSearchService = new Search.Service(
            "sleekflow-commh-search-service",
            new ()
            {
                ResourceGroupName = _resourceGroup.Name,
                HostingMode = Search.HostingMode.Default,
                PartitionCount = 1,
                ReplicaCount = 1,
                Sku = new Search.Inputs.SkuArgs
                {
                    Name = Search.SkuName.Standard,
                },
            });

        return commerceHubSearchService;
    }

    public async Task InitAzureSearchIndex()
    {
        // https://github.com/Azure-Samples/search-dotnet-getting-started/blob/master/DotNetHowToIndexers/Program.cs
        var searchServiceEndpoint = "https://sleekflow-commh-search-service.search.windows.net";
        var searchServiceAdminApiKey = "sKa3Y5E4IzPhrsWsYTChzFMykA0RB9lG20hhHN36zKAzSeAX8HN2";
        // var searchServiceEndpoint = "https://sleekflow-commh-search-service2fc49330.search.windows.net";
        // var searchServiceAdminApiKey = "UwDa0UlpFllroi2WpyAoElkBgbx2fc8RbtMplYbEyCAzSeDbpC0y";

        var jsonSerializerSettings = NewtonsoftJsonObjectSerializer.CreateJsonSerializerSettings();

        var indexClient = new SearchIndexClient(
            new Uri(searchServiceEndpoint),
            new AzureKeyCredential(searchServiceAdminApiKey),
            new SearchClientOptions
            {
                Serializer = new NewtonsoftJsonObjectSerializer(jsonSerializerSettings)
            });
        var indexerClient = new SearchIndexerClient(
            new Uri(searchServiceEndpoint),
            new AzureKeyCredential(searchServiceAdminApiKey),
            new SearchClientOptions
            {
                Serializer = new NewtonsoftJsonObjectSerializer(jsonSerializerSettings)
            });

        var cosmosQuery =
            "SELECT c.id, c.store_id, c.names, c.descriptions, c.platform_data, c.record_statuses, c.sleekflow_company_id, c.created_at, c.updated_at, c._ts, ARRAY_CONTAINS(c.record_statuses, 'Deleted') as is_deleted FROM c WHERE c._ts >= @HighWaterMark AND c.sys_type_name = 'Category' ORDER BY c._ts";

        await InitIndex(
            jsonSerializerSettings,
            typeof(CategoryIndexDto),
            indexClient,
            "commercehubdb-category-index",
            "commercehubdb-category-data-source",
            "category",
            cosmosQuery,
            indexerClient,
            "commercehubdb-category-indexer",
            suggesterSourceFields: new[]
            {
                "names/value",
                "descriptions/text/value"
            });

        cosmosQuery =
            "SELECT c.id, c.store_id, c.category_ids, c.sku, c.names, c.descriptions, c.attributes, c.prices, c.images, c.record_statuses, c.platform_data, c.sleekflow_company_id, c.created_at, c.updated_at, c._ts, ARRAY_CONTAINS(c.record_statuses, 'Deleted') as is_deleted FROM c WHERE c._ts >= @HighWaterMark AND c.sys_type_name = 'Product' ORDER BY c._ts";

        await InitIndex(
            jsonSerializerSettings,
            typeof(ProductIndexDto),
            indexClient,
            "commercehubdb-product-index",
            "commercehubdb-product-data-source",
            "product",
            cosmosQuery,
            indexerClient,
            "commercehubdb-product-indexer",
            new[]
            {
                "sku",
                "names/value",
                "descriptions/text/value"
            });
    }

    private static async Task InitIndex(
        JsonSerializerSettings jsonSerializerSettings,
        Type modelType,
        SearchIndexClient indexClient,
        string indexName,
        string dataSourceName,
        string cosmosContainerName,
        string cosmosQuery,
        SearchIndexerClient indexerClient,
        string indexerName,
        string[]? suggesterSourceFields = null,
        bool isIndexerEnabled = false)
    {
        var fieldBuilder = new FieldBuilder
        {
            Serializer = new NewtonsoftJsonObjectSerializer(jsonSerializerSettings)
        };
        var searchFields = fieldBuilder.Build(modelType);

        if (indexName == "commercehubdb-product-index")
        {
            var complexField = new ComplexField("product_variant_prices", true);
            complexField.Fields.Add(
                new SearchableField("currency_iso_code")
                {
                    IsFilterable = true, IsFacetable = true
                });
            complexField.Fields.Add(
                new SimpleField("amount", SearchFieldDataType.Double)
                {
                    IsFilterable = true, IsFacetable = true
                });
            searchFields.Add(complexField);
        }

        var searchIndex = new SearchIndex(indexName)
        {
            Fields = searchFields,
            Suggesters =
            {
                new SearchSuggester("sg", suggesterSourceFields)
            }
        };

        await indexClient.CreateOrUpdateIndexAsync(searchIndex);

        if (isIndexerEnabled)
        {
            var dataSource =
                new SearchIndexerDataSourceConnection(
                    dataSourceName,
                    SearchIndexerDataSourceType.CosmosDb,
                    ConnectionString,
                    new SearchIndexerDataContainer(cosmosContainerName)
                    {
                        Query = cosmosQuery,
                    })
                {
                    DataChangeDetectionPolicy = new HighWaterMarkChangeDetectionPolicy("_ts"),
                    DataDeletionDetectionPolicy = new SoftDeleteColumnDeletionDetectionPolicy
                    {
                        SoftDeleteColumnName = "is_deleted", SoftDeleteMarkerValue = "true"
                    }
                };

            await indexerClient.CreateOrUpdateDataSourceConnectionAsync(dataSource);

            var indexer = new SearchIndexer(indexerName, dataSource.Name, searchIndex.Name)
            {
                Schedule = new IndexingSchedule(TimeSpan.FromMinutes(5))
                {
                    StartTime = DateTimeOffset.Now
                },
                Parameters = new IndexingParameters
                {
                    BatchSize = 1000, MaxFailedItems = 0, MaxFailedItemsPerBatch = 0
                },
            };

            await indexerClient.CreateOrUpdateIndexerAsync(indexer);
        }
    }
}