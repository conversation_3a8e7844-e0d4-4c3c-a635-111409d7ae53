﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowStepValidator
{
    Task AssertAllStepsAreValidAsync(
        List<Step>? steps,
        string? workflowScheduleType,
        bool? isNewScheduledWorkflowSchema);

    Task AssertStepIdsUniquenessAsync(List<Step> steps);

    Task AssertValidNextStepAsync(List<Step> steps);
}

public sealed class WorkflowStepValidator : IWorkflowStepValidator, ISingletonService
{
    private readonly IWorkflowStepEntryProvider _workflowStepEntryProvider;

    public WorkflowStepValidator(IWorkflowStepEntryProvider workflowStepEntryProvider)
    {
        _workflowStepEntryProvider = workflowStepEntryProvider;
    }

    public async Task AssertAllStepsAreValidAsync(List<Step>? steps, string? workflowScheduleType, bool? isNewScheduledWorkflowSchema)
    {
        if (steps is null
            || steps.Count == 0)
        {
            return;
        }

        var stepEntries = _workflowStepEntryProvider.GetStepEntries(steps);

        await AssertStepIdsUniquenessAsync(stepEntries);
        await AssertValidNextStepAsync(stepEntries);

        AssertRequiredStepExistence(steps, workflowScheduleType, isNewScheduledWorkflowSchema);
    }

    public Task AssertStepIdsUniquenessAsync(List<Step>? steps)
    {
        if (steps is null
            || steps.Count == 0)
        {
            return Task.CompletedTask;
        }

        var stepEntries = _workflowStepEntryProvider.GetStepEntries(steps);

        return AssertStepIdsUniquenessAsync(stepEntries);
    }

    public Task AssertValidNextStepAsync(List<Step>? steps)
    {
        if (steps is null
            || steps.Count == 0)
        {
            return Task.CompletedTask;
        }

        var stepEntries = _workflowStepEntryProvider.GetStepEntries(steps);

        return AssertValidNextStepAsync(stepEntries);
    }

    private static Task AssertStepIdsUniquenessAsync(IReadOnlyList<WorkflowStepEntry> stepEntries)
    {
        var stepIds = stepEntries
            .Select(entry => entry.Step.Id)
            .ToList();

        if (stepIds.Exists(string.IsNullOrWhiteSpace))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ($"One or more steps have empty '{nameof(Step.Id)}' field")
                });
        }

        var duplicateIds = stepIds
            .GroupBy(id => id)
            .ToDictionary(g => g.Key, g => g.Count())
            .Where(kv => kv.Value > 1)
            .Select(kv => kv.Key)
            .ToList();

        if (duplicateIds.Count > 0)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ($"Two or more steps have duplicate values for '{nameof(Step.Id)}'. Duplicate step ids: {JsonConvert.SerializeObject(duplicateIds)}")
                });
        }

        return Task.CompletedTask;
    }

    private static Task AssertValidNextStepAsync(IReadOnlyList<WorkflowStepEntry> stepEntries)
    {
        var invalidStepIds = new HashSet<string>();

        foreach (var stepEntry in stepEntries)
        {
            if (stepEntry.Step is SwitchStep switchStep)
            {
                foreach (var switchCase in switchStep.SwitchCases)
                {
                    ValidateNextStepDepth(stepEntries, stepEntry, invalidStepIds, switchCase.NextStepId);
                }
            }

            ValidateNextStepDepth(stepEntries, stepEntry, invalidStepIds, stepEntry.Step.NextStepId);
        }

        if (invalidStepIds.Count > 0)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new($"One or more steps have invalid next step that is either non-existent or not in the same depth. " +
                        $"Problematic step ids: {JsonConvert.SerializeObject(invalidStepIds)}")
                });
        }

        return Task.CompletedTask;
    }

    private static void ValidateNextStepDepth(
        IReadOnlyList<WorkflowStepEntry> stepEntries,
        WorkflowStepEntry stepEntry,
        ISet<string> invalidStepIds,
        string? nextStepId)
    {
        if (string.IsNullOrWhiteSpace(nextStepId))
        {
            return;
        }

        var nextStep = stepEntries.FirstOrDefault(entry => entry.Step.Id == nextStepId);

        if (nextStep is null
            || nextStep.Depth != stepEntry.Depth)
        {
            invalidStepIds.Add(stepEntry.Step.Id);
        }
    }

    private static void AssertRequiredStepExistence(IReadOnlyList<Step> steps, string? workflowScheduleType, bool? isNewScheduledWorkflowSchema)
    {
        if (!string.IsNullOrWhiteSpace(workflowScheduleType)
            && workflowScheduleType is not WorkflowScheduleTypes.None
            && steps.Count(s => s is CallStep<ScheduledTriggerConditionsCheckStepArgs>) != 1
            && isNewScheduledWorkflowSchema is not true)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new($"Schedule based workflow must have exactly one call step that invokes '{ScheduledTriggerConditionsCheckStepArgs.CallName}'")
                });
        }
    }
}