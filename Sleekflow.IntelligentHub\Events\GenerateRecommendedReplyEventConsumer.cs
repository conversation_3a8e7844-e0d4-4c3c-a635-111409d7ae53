using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Events;

public class GenerateRecommendedReplyEventConsumerDefinition : ConsumerDefinition<GenerateRecommendedReplyEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GenerateRecommendedReplyEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GenerateRecommendedReplyEventConsumer : IConsumer<GenerateRecommendedReplyEvent>
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly IChatService _chatService;
    private readonly IReviewerService _reviewerService;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public GenerateRecommendedReplyEventConsumer(
        Kernel kernel,
        IChatService chatService,
        IReviewerService reviewerService,
        ITextTranslationService textTranslationService,
        ILogger<GenerateRecommendedReplyEventConsumer> logger,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubConfigService intelligentHubConfigService)
    {
        _logger = logger;
        _kernel = kernel;
        _chatService = chatService;
        _reviewerService = reviewerService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _textTranslationService = textTranslationService;
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public async Task Consume(ConsumeContext<GenerateRecommendedReplyEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;

        // Check if the usage limit is reached
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(message.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null ||
                                   await _intelligentHubUsageService.IsUsageLimitExceeded(
                                       message.SleekflowCompanyId,
                                       new Dictionary<string, int>
                                       {
                                           {
                                               PriceableFeatures.AiAgentsTotalUsage, _intelligentHubUsageService
                                                   .GetFeatureTotalUsageLimit(
                                                       intelligentHubConfig,
                                                       PriceableFeatures.AiAgentsTotalUsage)
                                           }
                                       },
                                       new IntelligentHubUsageFilter(
                                           message.IntelligentHubUsageFilterFromDateTime,
                                           message.IntelligentHubUsageFilterToDateTime));

        if (isUsageLimitExceeded)
        {
            _logger.LogWarning(
                "Cannot find IntelligentHubConfig or exceed max usage limit for SleekflowCompanyId {SleekflowCompanyId}.",
                sleekflowCompanyId);

            await context.Publish(
                new OnRecommendedReplyGenerationEmitEvent(
                    context.Message.RecommendReplyStepId,
                    context.Message.ProxyStateId,
                    0,
                    "Cannot find IntelligentHubConfig or exceed max usage limit"));

            await context.Publish(
                new OnRecommendedReplyGenerationFinishedEvent(
                    context.Message.RecommendReplyStepId,
                    context.Message.ProxyStateId,
                    0,
                    -1));

            return;
        }

        var (asyncEnumerable, _, sourcesStr) = await _chatService.StreamAnswerAsync(
            conversationContext,
            sleekflowCompanyId);

        var streamSequenceNumber = 0;

        var fullAnswerSb = new StringBuilder();
        var partialAnswerSb = new StringBuilder();
        await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
        {
            if (partialAnswer.Length == 0)
            {
                continue;
            }

            fullAnswerSb.Append(partialAnswer);
            partialAnswerSb.Append(partialAnswer);
            if (partialAnswer.Contains('\n'))
            {
                var answer = partialAnswerSb.ToString();
                var newlineIndex = answer.LastIndexOf('\n');

                // send everything before the last newline
                var partialRecommendedReply = CleanRecommendedReply(answer.Substring(0, newlineIndex));
                await context.Publish(
                    new OnRecommendedReplyGenerationEmitEvent(
                        context.Message.RecommendReplyStepId,
                        context.Message.ProxyStateId,
                        streamSequenceNumber++,
                        partialRecommendedReply));

                if (newlineIndex != answer.Length - 1)
                {
                    // the remaining text becomes part of the next message
                    var remainingText = answer.Substring(newlineIndex + 1);
                    partialAnswerSb.Clear();
                    partialAnswerSb.Append(remainingText);
                }
                else
                {
                    partialAnswerSb.Clear();
                }
            }
        }

        // flush the remaining text
        if (partialAnswerSb.Length > 0)
        {
            var partialRecommendedReply = CleanRecommendedReply(partialAnswerSb.ToString());
            await context.Publish(
                new OnRecommendedReplyGenerationEmitEvent(
                    context.Message.RecommendReplyStepId,
                    context.Message.ProxyStateId,
                    streamSequenceNumber++,
                    partialRecommendedReply));
        }

        // confidence scoring is calculated after the entire answer is streamed
        var confidenceScoring = await _chatService.GetConfidenceScoring(conversationContext, fullAnswerSb.ToString());
        await context.Publish(
            new OnRecommendedReplyGenerationFinishedEvent(
                context.Message.RecommendReplyStepId,
                context.Message.ProxyStateId,
                streamSequenceNumber - 1,
                confidenceScoring));

        await _intelligentHubUsageService.RecordUsageAsync(
            sleekflowCompanyId,
            PriceableFeatures.RecommendReply,
            null,
            new RecommendReplySnapshot(
                JsonConvert.SerializeObject(
                    conversationContext,
                    JsonConfig.DefaultLoggingJsonSerializerSettings),
                sourcesStr ?? string.Empty,
                fullAnswerSb.ToString()));
    }

    private string CleanRecommendedReply(string recommendedReply)
    {
        return Regex.Replace(recommendedReply, @"\[source\]", string.Empty, RegexOptions.IgnoreCase);
    }
}