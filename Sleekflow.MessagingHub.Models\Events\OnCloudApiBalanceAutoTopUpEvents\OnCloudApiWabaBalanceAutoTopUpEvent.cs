﻿using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

namespace Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;

public class OnCloudApiWabaBalanceAutoTopUpEvent
{
    public string BusinessBalanceId { get; set; }

    public string FacebookWabaId { get; set; }

    public string FacebookBusinessId { get; set; }

    public string CustomerId { get; set; }

    public StripeWhatsAppCreditTopUpPlan AutoTopUpPlan { get; set; }


    public OnCloudApiWabaBalanceAutoTopUpEvent(
        string businessBalanceId,
        string facebookWabaId,
        string facebookBusinessId,
        string customerId,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan)
    {
        BusinessBalanceId = businessBalanceId;
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        CustomerId = customerId;
        AutoTopUpPlan = autoTopUpPlan;
    }
}