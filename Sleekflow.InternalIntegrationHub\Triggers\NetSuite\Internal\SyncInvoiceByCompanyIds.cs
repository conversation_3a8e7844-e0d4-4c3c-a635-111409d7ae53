using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.Validations;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class SyncInvoiceByCompanyIds
    : ITrigger<SyncInvoiceByCompanyIds.SyncInvoiceByCompanyIdsInput,
        SyncInvoiceByCompanyIds.SyncInvoiceByCompanyIdsOutput>
{
    private readonly INetSuiteInvoiceService _netSuiteInvoiceService;

    public SyncInvoiceByCompanyIds(INetSuiteInvoiceService netSuiteInvoiceService)
    {
        _netSuiteInvoiceService = netSuiteInvoiceService;
    }

    [method: JsonConstructor]
    public class SyncInvoiceByCompanyIdsInput(
        string[] companyIds,
        bool? isEnableCheckBillRecord,
        bool? isEnableCheckWhatsappCredit)
    {
        [Required]
        [JsonProperty("company_ids")]
        [ValidateObject]
        public string[] CompanyIds { get; set; } = companyIds;

        [Required]
        [JsonProperty("check_bill_record")]
        [ValidateObject]
        public bool IsEnableCheckBillRecord { get; set; } = isEnableCheckBillRecord ?? false;

        [Required]
        [JsonProperty("check_whatsapp_credit")]
        [ValidateObject]
        public bool IsEnableCheckWhatsappCredit { get; set; } = isEnableCheckWhatsappCredit ?? false;
    }

    [method: JsonConstructor]
    public class SyncInvoiceByCompanyIdsOutput(bool isSuccess)
    {
        [JsonProperty("is_success")]
        public bool IsSuccess { get; set; } = isSuccess;
    }

    public async Task<SyncInvoiceByCompanyIdsOutput> F(SyncInvoiceByCompanyIdsInput input)
    {
        var response = await _netSuiteInvoiceService.SyncInvoiceByCompanyIds(input);

        return response > 0 ? new SyncInvoiceByCompanyIdsOutput(true) : new SyncInvoiceByCompanyIdsOutput(false);
    }
}