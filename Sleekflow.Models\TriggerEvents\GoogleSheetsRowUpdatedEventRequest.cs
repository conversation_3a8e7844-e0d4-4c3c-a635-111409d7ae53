﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class GoogleSheetsRowUpdatedEventRequest
{
    public DateTimeOffset CreatedAt { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string ConnectionId { get; set; }

    public string SpreadsheetId { get; set; }

    public string WorksheetId { get; set; }

    public string RowId { get; set; }

    public Dictionary<string, object?> Row { get; set; }

    [JsonConstructor]
    public GoogleSheetsRowUpdatedEventRequest(
        DateTimeOffset createdAt,
        string sleekflowCompanyId,
        string connectionId,
        string spreadsheetId,
        string worksheetId,
        string rowId,
        Dictionary<string, object?> row)
    {
        CreatedAt = createdAt;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        SpreadsheetId = spreadsheetId;
        WorksheetId = worksheetId;
        RowId = rowId;
        Row = row;
    }
}