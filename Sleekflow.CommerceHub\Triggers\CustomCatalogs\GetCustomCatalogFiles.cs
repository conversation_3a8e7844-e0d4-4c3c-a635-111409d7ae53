using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Blobs;
using Sleekflow.CommerceHub.CustomCatalogs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs;

[TriggerGroup(ControllerNames.CustomCatalogs)]
public class GetCustomCatalogFiles
    : ITrigger<
        GetCustomCatalogFiles.GetCustomCatalogFilesInput,
        GetCustomCatalogFiles.GetCustomCatalogFilesOutput>
{
    private readonly ICustomCatalogFileService _customCatalogFileService;
    private readonly IBlobService _blobService;

    public GetCustomCatalogFiles(
        ICustomCatalogFileService customCatalogFileService,
        IBlobService blobService)
    {
        _customCatalogFileService = customCatalogFileService;
        _blobService = blobService;
    }

    public class GetCustomCatalogFilesInput
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string StoreId { get; set; }

        [JsonConstructor]
        public GetCustomCatalogFilesInput(
            string sleekflowCompanyId,
            string storeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
        }
    }

    public class GetCustomCatalogFilesOutput
    {
        [JsonProperty("custom_catalog_files")]
        public List<CustomCatalogFileDto> CustomCatalogFiles { get; set; }

        [JsonConstructor]
        public GetCustomCatalogFilesOutput(List<CustomCatalogFileDto> customCatalogFiles)
        {
            CustomCatalogFiles = customCatalogFiles;
        }
    }

    public async Task<GetCustomCatalogFilesOutput> F(
        GetCustomCatalogFilesInput getCustomCatalogFilesInput)
    {
        var customCatalogFiles = await _customCatalogFileService.GetCustomCatalogFilesAsync(
            getCustomCatalogFilesInput.SleekflowCompanyId,
            getCustomCatalogFilesInput.StoreId);

        var entries = new List<(CustomCatalogFile CustomCatalogFile, int Progress, string? LogSasUrl)>();

        foreach (var customCatalogFile in customCatalogFiles)
        {
            var progress = customCatalogFile.FileProcessStatus switch
            {
                CustomCatalogFileProcessStatuses.Processing => 20,
                CustomCatalogFileProcessStatuses.Completed => 100,
                _ => 10
            };

            if (customCatalogFile.FileProcessStatus == CustomCatalogFileProcessStatuses.Failed)
            {
                var customCatalogFileProcessLogger = await new CustomCatalogFileProcessLogger(
                        _blobService,
                        customCatalogFile.SleekflowCompanyId,
                        customCatalogFile.StoreId,
                        customCatalogFile.Id)
                    .InitAsync();

                var logSasUrl = customCatalogFileProcessLogger.GetSasUrl();

                entries.Add((customCatalogFile, progress, logSasUrl));

                continue;
            }

            entries.Add((customCatalogFile, progress, null));
        }

        return new GetCustomCatalogFilesOutput(
            entries
                .Select(
                    f => new CustomCatalogFileDto(
                        f.CustomCatalogFile,
                        f.Progress,
                        f.LogSasUrl))
                .ToList());
    }
}