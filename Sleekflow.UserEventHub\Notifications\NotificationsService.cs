using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.UserEventHub;
using Sleekflow.Locks;
using Sleekflow.UserEventHub.Models.Constants;
using Sleekflow.UserEventHub.Models.Notifications;

namespace Sleekflow.UserEventHub.Notifications;

public interface INotificationService
{
    Task<NotificationSettings> RegisterAndEnableMobilePushNotification(
        string handle,
        string[] tags,
        string userId,
        string sleekflowCompanyId,
        string? platform,
        string? deviceId,
        string? hubName);

    Task<NotificationSettings> GetNotificationSettingsByUserIdAsync(
        string userId,
        string sleekflowCompanyId);

    Task<NotificationSettings> UpdateNotificationSettingsByPlatformAsync(
        string platform,
        string userId,
        string sleekflowCompanyId,
        bool? isNotificationEnabled,
        bool? banner,
        string? androidSound,
        string? iosSound,
        bool? badge,
        List<string>? notificationPolicies);

    Task SendNotificationAsync(
        string notificationEvent,
        string[] tags,
        string? title,
        string? body,
        int? badge,
        string? conversationId,
        string? companyId,
        string? userId);

    Task<bool> UnregisterMobilePushNotification(
        string userId,
        string sleekflowCompanyId);

    /// <summary>
    /// Delete a device installation from Azure Notification Hub.
    /// </summary>
    /// <param name="deviceId">The device ID (installation ID) to delete</param>
    /// <returns>True if the deletion was successful</returns>
    Task<bool> DeleteDeviceInstallationAsync(string deviceId);
}

public class NotificationService
    : INotificationService, IScopedService
{
    private readonly ILogger<NotificationService> _logger;

    private readonly ILockService _lockService;

    private readonly ICacheService _cacheService;

    private readonly INotificationHubService _notificationsHubService;

    private readonly INotificationSettingsRepository _notificationSettingsRepository;

    public NotificationService(
        ILogger<NotificationService> logger,
        ILockService lockService,
        ICacheService cacheService,
        INotificationHubService notificationsHubService,
        INotificationSettingsRepository notificationSettingsRepository)
    {
        _logger = logger;
        _lockService = lockService;
        _cacheService = cacheService;
        _notificationsHubService = notificationsHubService;
        _notificationSettingsRepository = notificationSettingsRepository;
    }

    // Register and enable mobile push notification for a specific device
    public async Task<NotificationSettings> RegisterAndEnableMobilePushNotification(
        string handle,
        string[] tags,
        string userId,
        string sleekflowCompanyId,
        string? platform,
        string? deviceId,
        string? hubName)
    {
        var notificationSettings =
            await _notificationSettingsRepository.GetNotificationSettingsByUserIdAsync(
                userId,
                sleekflowCompanyId);

        // Create a device registration with the handle (token) and tags
        var deviceRegistration = new DeviceRegistration(handle, tags, platform, hubName);

        string registrationId = string.Empty;

        if (!string.IsNullOrEmpty(deviceId))
        {
            await _notificationsHubService.RemoveAllRegistrationsByTagAsync(sleekflowCompanyId, userId);

            // If deviceId is provided, use installation mode
            var hubResponse = await _notificationsHubService.RegisterDeviceInstallationAsync(
                deviceId,
                deviceRegistration);

            if (hubResponse.ErrorMessages.Any())
            {
                _logger.LogError(
                    "Failed to register device installation. UserId: {UserId}, DeviceId: {DeviceId}, Errors: {Errors}",
                    userId,
                    deviceId,
                    string.Join(", ", hubResponse.ErrorMessages));
                throw new SfSendNotificationFailException("Failed to register device for push notifications");
            }

            registrationId = deviceId;
        }

        if (notificationSettings == null)
        {
            _logger.LogInformation("Notifications settings not found, creating new settings.");
            var newNotificationSettings =
                await _notificationSettingsRepository.CreateDefaultNotificationSettingsWithMobileRegistrationAsync(
                    handle,
                    registrationId,
                    userId,
                    sleekflowCompanyId,
                    platform,
                    hubName);
            return newNotificationSettings;
        }

        if (notificationSettings.Mobile == null)
        {
            _logger.LogInformation("Mobile notification settings not found, creating new settings.");
            notificationSettings.Mobile =
                MobileNotificationSettings.CreateDefaultWithRegister(
                    handle,
                    registrationId,
                    platform,
                    hubName);
            await _notificationSettingsRepository.PatchAndGetNotificationSettingsAsync(notificationSettings);
            return notificationSettings;
        }
        else
        {
            _logger.LogInformation("Mobile notification settings found, updating settings.");
            notificationSettings.Mobile.Handle = handle;
            notificationSettings.Mobile.RegistrationId = registrationId;
            notificationSettings.Mobile.Platform = platform;
            notificationSettings.Mobile.HubName = hubName;
            notificationSettings.UpdatedAt = DateTimeOffset.UtcNow;
            var updatedNotificationSettings =
                await _notificationSettingsRepository.PatchAndGetNotificationSettingsAsync(
                    notificationSettings);

            // Remove cache
            await _cacheService.RemoveCacheAsync(
                $"{nameof(NotificationHubService)}:{nameof(SendNotificationAsync)}:{sleekflowCompanyId}:{userId}");

            return updatedNotificationSettings;
        }
    }

    public async Task<NotificationSettings> GetNotificationSettingsByUserIdAsync(
        string userId,
        string sleekflowCompanyId)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(NotificationService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(20));

        try
        {
            var notificationSettings =
                await _notificationSettingsRepository.GetNotificationSettingsByUserIdAsync(
                    userId,
                    sleekflowCompanyId);

            if (notificationSettings == null)
            {
                throw new SfNotFoundObjectException($"Notifications settings with userId = {userId}");
            }

            return notificationSettings;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<NotificationSettings> UpdateNotificationSettingsByPlatformAsync(
        string platform,
        string userId,
        string sleekflowCompanyId,
        bool? isNotificationEnabled,
        bool? banner,
        string? androidSound,
        string? iosSound,
        bool? badge,
        List<string>? notificationPolicies)
    {
        if (platform != NotificationPlatform.Mobile && platform != NotificationPlatform.Web)
        {
            throw new SfNotificationInvalidPlatformException();
        }

        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(NotificationService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(20));

        try
        {
            var notificationSettings =
                await _notificationSettingsRepository.GetNotificationSettingsByUserIdAsync(
                    userId,
                    sleekflowCompanyId);

            if (notificationSettings == null)
            {
                throw new SfNotFoundObjectException($"Notifications settings with userId = {userId}");
            }

            switch (platform)
            {
                case NotificationPlatform.Web:
                    break;
                case NotificationPlatform.Mobile when notificationSettings.Mobile == null:
                    throw new SfNotFoundObjectException(
                        $"Mobile notification settings not found. UserId = {userId}");
                case NotificationPlatform.Mobile:
                    notificationSettings.Mobile = notificationSettings.Mobile.Update(
                        isNotificationEnabled,
                        notificationPolicies,
                        banner,
                        iosSound,
                        androidSound,
                        badge);
                    break;
            }

            notificationSettings.UpdatedAt = DateTimeOffset.UtcNow;

            var updatedNotificationSettings =
                await _notificationSettingsRepository.PatchAndGetNotificationSettingsAsync(
                    notificationSettings);

            // Remove cache
            await _cacheService.RemoveCacheAsync(
                $"{nameof(NotificationHubService)}:{nameof(SendNotificationAsync)}:{sleekflowCompanyId}:{userId}");

            return updatedNotificationSettings;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task SendNotificationAsync(
        string notificationEvent,
        string[] tags,
        string? title,
        string? body,
        int? badge,
        string? conversationId,
        string? companyId,
        string? userId)
    {
        try
        {
            var notification = new Notification(
                tags,
                notificationEvent,
                title,
                body,
                badge,
                conversationId,
                companyId,
                userId);
            await _notificationsHubService.SendNotificationAsync(notification);
        }
        catch (SfNotFoundObjectException ex)
        {
            _logger.LogError(
                ex,
                "SendNotificationAsync error. Cannot find notification settings. {ExceptionMessage}, Company Id {CompanyId}, User Id {UserId}",
                ex.Message,
                companyId,
                userId);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "SendNotificationAsync error. {ExceptionMessage}, Company Id {CompanyId}",
                ex.Message,
                companyId);
            throw;
        }
    }

    public async Task<bool> UnregisterMobilePushNotification(string userId, string sleekflowCompanyId)
    {
        var notificationSettings =
            await _notificationSettingsRepository.GetNotificationSettingsByUserIdAsync(
                userId,
                sleekflowCompanyId);

        if (notificationSettings?.Mobile == null)
        {
            throw new SfNotFoundObjectException($"Notifications settings not found. UserId = {userId}");
        }

        var deviceId = notificationSettings.Mobile.RegistrationId;
        if (deviceId == null)
        {
            throw new SfNotFoundObjectException($"Device ID not found. UserId = {userId}");
        }

        try
        {
            // Using the new DeleteInstallationAsync method which is specifically designed for installation mode
            await _notificationsHubService.DeleteInstallationAsync(deviceId);
            _logger.LogInformation(
                "Successfully unregistered device installation. UserId: {UserId}, DeviceId: {DeviceId}",
                userId,
                deviceId);
        }
        catch (Exception e)
        {
            _logger.LogInformation(
                e,
                "Failed to delete installation from Azure, seems unregistered already. UserId: {UserId}, DeviceId: {DeviceId}",
                userId,
                deviceId);
        }

        notificationSettings.Mobile.Handle = null;
        notificationSettings.Mobile.RegistrationId = null;
        notificationSettings.UpdatedAt = DateTimeOffset.UtcNow;
        await _notificationSettingsRepository.PatchAndGetNotificationSettingsAsync(notificationSettings);

        return true;
    }

    /// <summary>
    /// Delete a device installation from Azure Notification Hub.
    /// </summary>
    /// <param name="deviceId">The device ID (installation ID) to delete</param>
    /// <returns>True if the deletion was successful</returns>
    public async Task<bool> DeleteDeviceInstallationAsync(string deviceId)
    {
        try
        {
            _logger.LogInformation(
                "Deleting device installation. DeviceId: {DeviceId}",
                deviceId);

            await _notificationsHubService.DeleteInstallationAsync(deviceId);

            _logger.LogInformation(
                "Successfully deleted device installation. DeviceId: {DeviceId}",
                deviceId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to delete device installation. DeviceId: {DeviceId}",
                deviceId);

            // Return false instead of throwing to allow callers to handle the failure gracefully
            return false;
        }
    }
}