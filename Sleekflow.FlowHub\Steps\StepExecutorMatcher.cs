using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.StepExecutors;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.StepExecutors.Calls;

namespace Sleekflow.FlowHub.Steps;

public interface IStepExecutorMatcher
{
    List<IStepExecutor> GetStepExecutors();

    IStepExecutor MatchExecutor(Step step);
}

public class StepExecutorMatcher : IScopedService, IStepExecutorMatcher
{
    private readonly List<IStepExecutor> _stepExecutors;

    public StepExecutorMatcher(
        IAddInternalNoteToContactStepExecutor addInternalNoteToContactStepExecutor,
        ICreateOrUpdateContactStepExecutor createOrUpdateContactStepExecutor,
        IGenerateQrCodeStepExecutor generateQrCodeStepExecutor,
        IHttpV2StepExecutor httpV2StepExecutor,
        IHttpGetStepExecutor httpGetStepExecutor,
        IHttpPostStepExecutor httpPostStepExecutor,
        IHttpDeleteStepExecutor httpDeleteStepExecutor,
        IHttpPutStepExecutor httpPutStepExecutor,
        IHttpPatchStepExecutor httpPatchStepExecutor,
        IJumpToStepExecutor jumpToStepExecutor,
        IReplyFacebookInstagramPostCommentStepExecutor replyFacebookInstagramPostCommentStepExecutor,
        ISendMessageStepExecutor sendMessageStepExecutor,
        ISendMessageV2StepExecutor sendMessageV2StepExecutor,
        IDmFbIgPostCommentStepExecutor dmFbIgPostCommentStepExecutor,
        ISleepStepExecutor sleepStepExecutor,
        IUpdateContactCollaboratorRelationshipsStepExecutor updateContactCollaboratorRelationshipsStepExecutor,
        IUpdateContactConversationStatusStepExecutor updateContactConversationStatusStepExecutor,
        IUpdateContactLabelRelationshipsStepExecutor updateContactLabelRelationshipsStepExecutor,
        IUpdateContactListRelationshipsStepExecutor updateContactListRelationshipsStepExecutor,
        IUpdateContactOwnerRelationshipsStepExecutor updateContactOwnerRelationshipsStepExecutor,
        IUpdateContactPropertiesStepExecutor updateContactPropertiesStepExecutor,
        IUpdateContactPropertiesByPropertyKeyStepExecutor updateContactPropertiesByPropertyKeyStepExecutor,
        ICreateSalesforceObjectStepExecutor createSalesforceObjectStepExecutor,
        ISearchSalesforceObjectStepExecutor searchSalesforceObjectStepExecutor,
        IUpdateSalesforceObjectStepExecutor updateSalesforceObjectStepExecutor,
        ICreateContactStepExecutor createContactStepExecutor,
        ICreateContactV2StepExecutor createContactV2StepExecutor,
        ICreateContactWithSalesforceUserMappingStepExecutor createContactWithSalesforceUserMappingStepExecutor,
        ICreateSchemafulObjectStepExecutor createSchemafulObjectStepExecutor,
        ICreateSchemafulObjectV2StepExecutor createSchemafulObjectV2StepExecutor,
        IUpdateSchemafulObjectStepExecutor updateSchemafulObjectStepExecutor,
        IScheduledTriggerConditionsCheckStepExecutor scheduledTriggerConditionsCheckStepExecutor,
        IWaitForEventStepExecutor waitForEventStepExecutor,
        ILogStepExecutor logStepExecutor,
        IParallelStepExecutor parallelStepExecutor,
        ISimpleStepExecutor simpleStepExecutor,
        ISubFlowStepExecutor subFlowStepExecutor,
        ISwitchStepExecutor switchStepExecutor,
        IThrowStepExecutor throwStepExecutor,
        ITryCatchStepExecutor tryCatchStepExecutor,
        IRecommendReplyStepExecutor recommendReplyStepExecutor,
        IRecommendReplyStreamingStepExecutor recommendReplyStreamingStepExecutor,
        ICaptureUserEventStepExecutor captureUserEventStepExecutor,
        IAggregateStepExecutor aggregateStepExecutor,
        IAgentEvaluateIntentionStepExecutor agentEvaluateIntentionStepExecutor,
        IAgentEvaluateScoreStepExecutor agentEvaluateScoreStepExecutor,
        IAgentRecommendReplyStepExecutor agentRecommendReplyStepExecutor,
        IAgentSummarizeStepExecutor agentSummarizeStepExecutor,
        IUpdateContactCollaboratorRelationshipsV2StepExecutor updateContactCollaboratorRelationshipsV2StepExecutor,
        IUpdateContactOwnerRelationshipsV2StepExecutor updateContactOwnerRelationshipsV2StepExecutor,
        IUpdateContactListRelationshipsV2StepExecutor updateContactListRelationshipsV2StepExecutor,
        IUpdateContactLabelRelationshipsV2StepExecutor updateContactLabelRelationshipsV2StepExecutor,
        IUpdateContactPropertiesV2StepExecutor updateContactPropertiesV2StepExecutor,
        IUpdateContactConversationStatusV2StepExecutor updateContactConversationStatusV2StepExecutor,
        IAddInternalNoteToContactV2StepExecutor addInternalNoteToContactV2StepExecutor,
        ICreateTicketStepExecutor createTicketStepExecutor,
        IUpdateSchemafulObjectV2StepExecutor updateSchemafulObjectV2StepExecutor,
        ISleepV2StepExecutor sleepV2StepExecutor,
        IWaitForEventV2StepExecutor waitForEventV2StepExecutor,
        IEndStepExecutor endStepExecutor,
        ICreateGoogleSheetsRowStepExecutor createGoogleSheetsRowStepExecutor,
        IUpdateGoogleSheetsRowStepExecutor updateGoogleSheetsRowStepExecutor,
        ISearchGoogleSheetsRowStepExecutor searchGoogleSheetsRowStepExecutor,
        ICreateHubspotObjectStepExecutor createHubspotObjectStepExecutor,
        ISearchHubspotObjectStepExecutor searchHubspotObjectStepExecutor,
        IUpdateHubspotObjectStepExecutor updateHubspotObjectStepExecutor,
        ICreateZohoObjectStepExecutor createZohoObjectStepExecutor,
        ISearchZohoObjectStepExecutor searchZohoObjectStepExecutor,
        IUpdateZohoObjectStepExecutor updateZohoObjectStepExecutor,
        ISendMetaCapiEventStepExecutor sendMetaCapiEventStepExecutor,
        ICreateSalesforceObjectV2StepExecutor createSalesforceObjectV2StepExecutor,
        ISearchSalesforceObjectV2StepExecutor searchSalesforceObjectV2StepExecutor,
        IUpdateSalesforceObjectV2StepExecutor updateSalesforceObjectV2StepExecutor,
        IUpdateContactPropertiesWithRecordSourceStepExecutor updateContactPropertiesWithRecordSourceStepExecutor,
        IEnterAiAgentStepExecutor enterAiAgentStepExecutor,
        ISendVariableToParentWorkflowExecutor sendVariableToParentWorkflowExecutor,
        IEvaluateExitConditionsStepExecutor evaluateExitConditionsStepExecutor,
        IAgentCalculateLeadScoreStepExecutor agentCalculateLeadScoreStepExecutor,
        IAgentAddLabelStepExecutor agentAddLabelStepExecutor)
    {
        _stepExecutors = new List<IStepExecutor>
        {
            addInternalNoteToContactStepExecutor,
            createOrUpdateContactStepExecutor,
            generateQrCodeStepExecutor,
            httpV2StepExecutor,
            httpGetStepExecutor,
            httpPostStepExecutor,
            httpDeleteStepExecutor,
            httpPutStepExecutor,
            httpPatchStepExecutor,
            jumpToStepExecutor,
            replyFacebookInstagramPostCommentStepExecutor,
            sendMessageStepExecutor,
            sendMessageV2StepExecutor,
            dmFbIgPostCommentStepExecutor,
            sleepStepExecutor,
            updateContactCollaboratorRelationshipsStepExecutor,
            updateContactConversationStatusStepExecutor,
            updateContactLabelRelationshipsStepExecutor,
            updateContactListRelationshipsStepExecutor,
            updateContactOwnerRelationshipsStepExecutor,
            updateContactPropertiesStepExecutor,
            updateContactPropertiesByPropertyKeyStepExecutor,
            createSalesforceObjectStepExecutor,
            searchSalesforceObjectStepExecutor,
            updateSalesforceObjectStepExecutor,
            createContactStepExecutor,
            createContactV2StepExecutor,
            createContactWithSalesforceUserMappingStepExecutor,
            createSchemafulObjectStepExecutor,
            createSchemafulObjectV2StepExecutor,
            updateSchemafulObjectStepExecutor,
            scheduledTriggerConditionsCheckStepExecutor,
            waitForEventStepExecutor,
            logStepExecutor,
            parallelStepExecutor,
            simpleStepExecutor,
            subFlowStepExecutor,
            switchStepExecutor,
            throwStepExecutor,
            tryCatchStepExecutor,
            recommendReplyStepExecutor,
            recommendReplyStreamingStepExecutor,
            captureUserEventStepExecutor,
            aggregateStepExecutor,
            agentEvaluateScoreStepExecutor,
            agentEvaluateIntentionStepExecutor,
            agentRecommendReplyStepExecutor,
            agentSummarizeStepExecutor,
            updateContactCollaboratorRelationshipsV2StepExecutor,
            updateContactOwnerRelationshipsV2StepExecutor,
            updateContactListRelationshipsV2StepExecutor,
            updateContactLabelRelationshipsV2StepExecutor,
            updateContactPropertiesV2StepExecutor,
            updateContactConversationStatusV2StepExecutor,
            addInternalNoteToContactV2StepExecutor,
            createTicketStepExecutor,
            updateSchemafulObjectV2StepExecutor,
            sleepV2StepExecutor,
            waitForEventV2StepExecutor,
            endStepExecutor,
            createGoogleSheetsRowStepExecutor,
            updateGoogleSheetsRowStepExecutor,
            searchGoogleSheetsRowStepExecutor,
            createHubspotObjectStepExecutor,
            searchHubspotObjectStepExecutor,
            updateHubspotObjectStepExecutor,
            createZohoObjectStepExecutor,
            searchZohoObjectStepExecutor,
            updateZohoObjectStepExecutor,
            sendMetaCapiEventStepExecutor,
            createSalesforceObjectV2StepExecutor,
            searchSalesforceObjectV2StepExecutor,
            updateSalesforceObjectV2StepExecutor,
            updateContactPropertiesWithRecordSourceStepExecutor,
            enterAiAgentStepExecutor,
            sendVariableToParentWorkflowExecutor,
            evaluateExitConditionsStepExecutor,
            agentCalculateLeadScoreStepExecutor,
            agentAddLabelStepExecutor
        };
    }

    public List<IStepExecutor> GetStepExecutors()
    {
        return _stepExecutors;
    }

    public IStepExecutor MatchExecutor(Step step)
    {
        var stepExecutor = _stepExecutors.Find(se => se.IsMatched(step));
        if (stepExecutor != null)
        {
            return stepExecutor;
        }

        throw new ArgumentOutOfRangeException(nameof(step));
    }
}