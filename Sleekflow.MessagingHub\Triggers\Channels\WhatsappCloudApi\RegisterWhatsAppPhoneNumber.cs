using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class RegisterWhatsAppPhoneNumber
    : ITrigger<
        RegisterWhatsAppPhoneNumber.RegisterWhatsAppPhoneNumberInput,
        RegisterWhatsAppPhoneNumber.RegisterWhatsAppPhoneNumberOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<RegisterWhatsAppPhoneNumber> _logger;

    public RegisterWhatsAppPhoneNumber(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<RegisterWhatsAppPhoneNumber> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class RegisterWhatsAppPhoneNumberInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_phone_number_id")]
        public string FacebookPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("pin")]
        public string Pin { get; set; }

        [JsonConstructor]
        public RegisterWhatsAppPhoneNumberInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string facebookPhoneNumberId,
            string pin)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            FacebookPhoneNumberId = facebookPhoneNumberId;
            Pin = pin;
        }
    }

    public class RegisterWhatsAppPhoneNumberOutput
    {
        [JsonProperty("register_phone_number_response")]
        public RegisterPhoneNumberResponse RegisterPhoneNumberResponse { get; set; }

        [JsonConstructor]
        public RegisterWhatsAppPhoneNumberOutput(RegisterPhoneNumberResponse registerPhoneNumberResponse)
        {
            RegisterPhoneNumberResponse = registerPhoneNumberResponse;
        }
    }

    public async Task<RegisterWhatsAppPhoneNumberOutput> F(
        RegisterWhatsAppPhoneNumberInput registerWhatsAppPhoneNumberInput)
    {
        var facebookWabaId = registerWhatsAppPhoneNumberInput.FacebookWabaId;
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new RegisterWhatsAppPhoneNumberOutput(
            await _channelService.RegisterPhoneNumber(
                facebookWabaId,
                registerWhatsAppPhoneNumberInput.SleekflowCompanyId,
                registerWhatsAppPhoneNumberInput.FacebookPhoneNumberId,
                registerWhatsAppPhoneNumberInput.Pin,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}