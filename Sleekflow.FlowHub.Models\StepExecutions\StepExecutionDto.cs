using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.StepExecutions;

public class StepExecutionDto : IHasSleekflowCompanyId, IHasCreatedAt
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    public StateIdentityDto StateIdentity { get; set; }

    [JsonProperty("step_id")]
    public string StepId { get; set; }

    [JsonProperty("step_node_id")]
    public string? StepNodeId { get; set; }

    [JsonProperty("step_execution_status")]
    public string StepExecutionStatus { get; set; }

    [JsonProperty("worker_instance_id")]
    public string? WorkerInstanceId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("error")]
    public UserFriendlyError? Error { get; set; }

    [JsonConstructor]
    public StepExecutionDto(
        string sleekflowCompanyId,
        string stateId,
        StateIdentityDto stateIdentity,
        string stepId,
        string? stepNodeId,
        string stepExecutionStatus,
        string? workerInstanceId,
        DateTimeOffset createdAt,
        UserFriendlyError? error)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        StateIdentity = stateIdentity;
        StepId = stepId;
        StepNodeId = stepNodeId;
        StepExecutionStatus = stepExecutionStatus;
        WorkerInstanceId = workerInstanceId;
        CreatedAt = createdAt;
        Error = error;
    }

    public StepExecutionDto(StepExecution stepExecution)
        : this(
            stepExecution.SleekflowCompanyId,
            stepExecution.StateId,
            new StateIdentityDto(stepExecution.StateIdentity),
            stepExecution.StepId,
            stepExecution.StepNodeId,
            stepExecution.StepExecutionStatus,
            stepExecution.WorkerInstanceId,
            stepExecution.CreatedAt,
            stepExecution.Error)
    {
    }
}