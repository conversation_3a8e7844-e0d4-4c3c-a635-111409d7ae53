using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.Utils;

namespace Sleekflow.FlowHub.TravisBackend.Handlers;

public class FlowHubTravisBackendAuthorizationHandler : DelegatingHandler
{
    private static readonly IAppConfig AppConfig = new AppConfig();

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var token = InternalsTokenUtils.CreateJwt(AppConfig.CoreInternalsKey);
        request.Headers.Add("X-Sleekflow-Flow-Hub-Authorization", token);

        return await base.SendAsync(request, cancellationToken);
    }
}