using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.Plugins;

[method: JsonConstructor]
public class DeterminePlanTierResponse(
    string explanation,
    string country,
    string region,
    string tier,
    string localCurrency)
{
    [JsonProperty("explanation")]
    public string Explanation { get; set; } = explanation;

    [JsonProperty("country")]
    public string Country { get; set; } = country;

    [JsonProperty("region")]
    public string Region { get; set; } = region;

    [JsonProperty("tier")]
    public string Tier { get; set; } = tier;

    [JsonProperty("local_currency")]
    public string LocalCurrency { get; set; } = localCurrency;
}

public interface IPlanTieringPlugin
{
    [KernelFunction("determine_plan_tier")]
    [Description(
        "Determines the plan tier based on a phone number by identifying the country and looking up the appropriate tier.")]
    [return: Description("A structured response with the country, region, plan tier, and local currency.")]
    Task<DeterminePlanTierResponse> DeterminePlanTierAsync(
        Kernel kernel,
        [Description("The phone number to analyze")]
        string phoneNumber,
        [Description("The country of the phone number")]
        string country,
        CancellationToken cancellationToken = default);
}

public class PlanTieringPlugin : IPlanTieringPlugin, IScopedService
{
    private static readonly DeterminePlanTierResponse DefaultResponse = new (
        "Unable to determine.",
        "Unknown",
        "GCR & UAE",
        "Plan Tier 1",
        "HKD");

    private readonly ILogger<PlanTieringPlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public PlanTieringPlugin(
        ILogger<PlanTieringPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("determine_plan_tier")]
    [Description(
        "Determines the plan tier based on a phone number by identifying the country and looking up the appropriate tier.")]
    [return: Description("A structured response with the country, region, plan tier, and local currency.")]
    public async Task<DeterminePlanTierResponse> DeterminePlanTierAsync(
        Kernel kernel,
        [Description("The phone number to analyze")]
        string phoneNumber,
        [Description("The country of the phone number")]
        string country,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var determinePlanTierResponse =
                await DeterminePlanTierByPhoneNumberAndCountryAsync(kernel, phoneNumber, country);

            return determinePlanTierResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining plan tier from phone number: {PhoneNumber}", phoneNumber);
            return DefaultResponse;
        }
    }

    private async Task<DeterminePlanTierResponse> DeterminePlanTierByPhoneNumberAndCountryAsync(
        Kernel kernel,
        string phoneNumber,
        string country)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            promptExecutionSettings,
            new List<PromptExecutionSettingsUtils.Property>
            {
                new PromptExecutionSettingsUtils.Property(
                    "explanation",
                    "string"),
                new PromptExecutionSettingsUtils.Property(
                    "country",
                    "string"),
                new PromptExecutionSettingsUtils.Property(
                    "region",
                    "string"),
                new PromptExecutionSettingsUtils.Property(
                    "tier",
                    "string"),
                new PromptExecutionSettingsUtils.Property(
                    "local_currency",
                    "string")
            });

        var summarizeFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "DeterminePlanTier",
                Template =

                    #region Prompt

                    """
                    Determine the plan tier based on the provided phone number and country.

                    The response should be structured in JSON format with the following fields:
                    - explanation: A brief explanation of the plan tier determination.
                    - country: The country name.
                    - region: The region name.
                    - tier: The plan tier.
                    - local_currency: The local currency.

                    ### Here is the dataset to follow for determining the plan tier:

                    ```
                    Country,Region,Plan Tier,Local Currency
                    Afghanistan,ROW,Plan Tier 5,USD
                    Albania,ROW,Plan Tier 2,USD
                    Algeria,ROW,Plan Tier 5,USD
                    American Samoa,ROW,Plan Tier 2,USD
                    Andorra,ROW,Plan Tier 2,EUR
                    Angola,ROW,Plan Tier 5,USD
                    Anguilla,AMER,Plan Tier 5,USD
                    Antarctica,ROW,Plan Tier 2,USD
                    Antigua and Barbuda,AMER,Plan Tier 5,USD
                    Argentina,AMER,Plan Tier 3,USD
                    Armenia,ROW,Plan Tier 2,USD
                    Aruba,AMER,Plan Tier 2,USD
                    Australia,ROW,Plan Tier 1,AUD
                    Austria,ROW,Plan Tier 1,EUR
                    Azerbaijan,ROW,Plan Tier 2,USD
                    Bahamas,AMER,Plan Tier 2,USD
                    Bahrain,ROW,Plan Tier 1,USD
                    Bangladesh,ROW,Plan Tier 5,USD
                    Barbados,AMER,Plan Tier 5,USD
                    Belarus,ROW,Plan Tier 2,USD
                    Belgium,ROW,Plan Tier 1,EUR
                    Belize,AMER,Plan Tier 3,USD
                    Benin,ROW,Plan Tier 5,USD
                    Bermuda,AMER,Plan Tier 1,USD
                    Bhutan,ROW,Plan Tier 5,USD
                    Bolivia,AMER,Plan Tier 3,USD
                    Bosnia and Herzegovina,ROW,Plan Tier 2,USD
                    Botswana,ROW,Plan Tier 5,USD
                    Brazil,AMER,Plan Tier 3,BRL
                    British Indian Ocean Territory,ROW,Plan Tier 1,USD
                    British Virgin Islands,AMER,Plan Tier 1,USD
                    Brunei,ASEAN & India,Plan Tier 5,USD
                    Bulgaria,ROW,Plan Tier 2,USD
                    Burkina Faso,ROW,Plan Tier 5,USD
                    Burundi,ROW,Plan Tier 5,USD
                    Cambodia,ASEAN & India,Plan Tier 5,USD
                    Cameroon,ROW,Plan Tier 5,USD
                    Canada,AMER,Plan Tier 1,CAD
                    Cape Verde,ROW,Plan Tier 5,USD
                    Caribbean,ROW,Plan Tier 5,USD
                    Cayman Islands,AMER,Plan Tier 1,USD
                    Central African Republic,ROW,Plan Tier 5,USD
                    Chad,ROW,Plan Tier 5,USD
                    Chile,AMER,Plan Tier 3,USD
                    China,GCR & UAE,Plan Tier 1,CNY
                    Christmas Island,ROW,Plan Tier 2,USD
                    Cocos Islands,ROW,Plan Tier 2,USD
                    Colombia,AMER,Plan Tier 3,USD
                    Comoros,ROW,Plan Tier 5,USD
                    Congo (DRC),ROW,Plan Tier 5,USD
                    Cook Islands,ROW,Plan Tier 2,USD
                    Costa Rica,AMER,Plan Tier 3,USD
                    Côte d’Ivoire,ROW,Plan Tier 5,USD
                    Croatia,ROW,Plan Tier 2,EUR
                    Cuba,AMER,Plan Tier 5,USD
                    Curacao,ROW,Plan Tier 5,USD
                    Cyprus,ROW,Plan Tier 1,EUR
                    Czech Republic,ROW,Plan Tier 1,USD
                    Denmark,ROW,Plan Tier 1,USD
                    Djibouti,ROW,Plan Tier 5,USD
                    Dominica,AMER,Plan Tier 5,USD
                    Dominican Republic,AMER,Plan Tier 5,USD
                    East Timor,ROW,Plan Tier 5,USD
                    Ecuador,AMER,Plan Tier 3,USD
                    Egypt,ROW,Plan Tier 5,USD
                    El Salvador,AMER,Plan Tier 3,USD
                    Equatorial Guinea,ROW,Plan Tier 5,USD
                    Eritrea,ROW,Plan Tier 5,USD
                    Estonia,ROW,Plan Tier 2,EUR
                    Ethiopia,ROW,Plan Tier 5,USD
                    Falkland Islands,AMER,Plan Tier 5,USD
                    Faroe Islands,ROW,Plan Tier 1,USD
                    Fiji,ROW,Plan Tier 2,USD
                    Finland,ROW,Plan Tier 1,EUR
                    France,ROW,Plan Tier 1,EUR
                    French Polynesia,ROW,Plan Tier 2,USD
                    Gabon,ROW,Plan Tier 5,USD
                    Gambia,ROW,Plan Tier 5,USD
                    Georgia,ROW,Plan Tier 2,USD
                    Germany,ROW,Plan Tier 1,EUR
                    Ghana,ROW,Plan Tier 5,USD
                    Gibraltar,ROW,Plan Tier 1,USD
                    Greece,ROW,Plan Tier 2,EUR
                    Greenland,AMER,Plan Tier 1,USD
                    Grenada,AMER,Plan Tier 5,USD
                    Guam,ROW,Plan Tier 1,USD
                    Guatemala,AMER,Plan Tier 3,USD
                    Guernsey,ROW,Plan Tier 1,USD
                    Guinea,ROW,Plan Tier 5,USD
                    Guinea-Bissau,ROW,Plan Tier 5,USD
                    Guyana,AMER,Plan Tier 3,USD
                    Haiti,AMER,Plan Tier 5,USD
                    Honduras,AMER,Plan Tier 3,USD
                    Hong Kong SAR,GCR & UAE,Plan Tier 1,HKD
                    Hungary,ROW,Plan Tier 2,USD
                    Iceland,ROW,Plan Tier 1,USD
                    India,ASEAN & India,Plan Tier 5,INR
                    Indonesia,ASEAN & India,Plan Tier 5,IDR
                    Iran,ROW,Plan Tier 2,USD
                    Iraq,ROW,Plan Tier 2,USD
                    Ireland,ROW,Plan Tier 1,EUR
                    Isle of Man,ROW,Plan Tier 1,USD
                    Israel,ROW,Plan Tier 1,USD
                    Italy,ROW,Plan Tier 1,EUR
                    Jamaica,AMER,Plan Tier 5,USD
                    Japan,ROW,Plan Tier 2,USD
                    Jersey,ROW,Plan Tier 1,USD
                    Jordan,ROW,Plan Tier 2,USD
                    Kazakhstan,ROW,Plan Tier 2,USD
                    Kenya,ROW,Plan Tier 5,USD
                    Kiribati,ROW,Plan Tier 2,USD
                    South Korea,ROW,Plan Tier 2,USD
                    Kosovo,ROW,Plan Tier 2,USD
                    Kuwait,ROW,Plan Tier 1,USD
                    Kyrgyzstan,ROW,Plan Tier 5,USD
                    Laos,ASEAN & India,Plan Tier 5,USD
                    Latvia,ROW,Plan Tier 2,EUR
                    Lebanon,ROW,Plan Tier 5,USD
                    Lesotho,ROW,Plan Tier 5,USD
                    Liberia,ROW,Plan Tier 5,USD
                    Libya,ROW,Plan Tier 5,USD
                    Liechtenstein,ROW,Plan Tier 1,USD
                    Lithuania,ROW,Plan Tier 2,EUR
                    Luxembourg,ROW,Plan Tier 1,EUR
                    Macao SAR,GCR & UAE,Plan Tier 1,USD
                    "Macedonia, FYRO",Plan Tier 3,
                    Madagascar,ROW,Plan Tier 5,USD
                    Malawi,ROW,Plan Tier 5,USD
                    Malaysia,ASEAN & India,Plan Tier 4,MYR
                    Maldives,ROW,Plan Tier 2,USD
                    Mali,ROW,Plan Tier 5,USD
                    Malta,ROW,Plan Tier 1,EUR
                    Marshall Islands,ROW,Plan Tier 2,USD
                    Mauritania,ROW,Plan Tier 5,USD
                    Mauritius,ROW,Plan Tier 5,USD
                    Mayotte,ROW,Plan Tier 1,EUR
                    Mexico,AMER,Plan Tier 3,USD
                    Micronesia,ROW,Plan Tier 2,USD
                    Moldova,ROW,Plan Tier 5,USD
                    Monaco,ROW,Plan Tier 1,EUR
                    Mongolia,ROW,Plan Tier 5,USD
                    Montenegro,ROW,Plan Tier 2,USD
                    Montserrat,AMER,Plan Tier 5,USD
                    Morocco,ROW,Plan Tier 4,USD
                    Mozambique,ROW,Plan Tier 5,USD
                    Myanmar,ASEAN & India,Plan Tier 5,USD
                    Namibia,ROW,Plan Tier 5,USD
                    Nauru,ROW,Plan Tier 2,USD
                    Nepal,ROW,Plan Tier 5,USD
                    Netherlands,ROW,Plan Tier 1,EUR
                    Netherlands Antilles,ROW,Plan Tier 5,USD
                    New Caledonia,ROW,Plan Tier 1,USD
                    New Zealand,ROW,Plan Tier 1,USD
                    Nicaragua,AMER,Plan Tier 3,USD
                    Niger,ROW,Plan Tier 5,USD
                    Nigeria,ROW,Plan Tier 5,USD
                    Niue,ROW,Plan Tier 1,USD
                    North Korea,ROW,Plan Tier 5,USD
                    Northern Mariana Islands,ROW,Plan Tier 2,USD
                    Norway,ROW,Plan Tier 1,USD
                    Oman,ROW,Plan Tier 1,USD
                    Pakistan,ROW,Plan Tier 5,USD
                    Palau,ROW,Plan Tier 2,USD
                    Palestine,ROW,Plan Tier 5,USD
                    Panama,AMER,Plan Tier 3,USD
                    Papua New Guinea,ROW,Plan Tier 2,USD
                    Paraguay,AMER,Plan Tier 3,USD
                    Peru,AMER,Plan Tier 3,USD
                    Philippines,ASEAN & India,Plan Tier 5,USD
                    Pitcairn,ROW,Plan Tier 1,USD
                    Poland,ROW,Plan Tier 2,USD
                    Portugal,AMER,Plan Tier 2,EUR
                    Puerto Rico,AMER,Plan Tier 5,USD
                    Qatar,ROW,Plan Tier 1,USD
                    Republic of the Congo,ROW,Plan Tier 5,USD
                    Réunion,ROW,Plan Tier 1,EUR
                    Romania,ROW,Plan Tier 2,USD
                    Russia,ROW,Plan Tier 2,USD
                    Rwanda,ROW,Plan Tier 5,USD
                    Saint Barthélemy,ROW,Plan Tier 2,EUR
                    Saint Helena,ROW,Plan Tier 5,USD
                    Saint Kitts and Nevis,AMER,Plan Tier 5,USD
                    Saint Lucia,AMER,Plan Tier 5,USD
                    Saint Martin,AMER,Plan Tier 5,USD
                    Saint Pierre and Miquelon,AMER,Plan Tier 2,EUR
                    Saint Vincent and the Grenadines,AMER,Plan Tier 5,USD
                    Samoa,ROW,Plan Tier 2,USD
                    San Marino,ROW,Plan Tier 1,EUR
                    Sao Tome and Principe,ROW,Plan Tier 5,USD
                    Saudi Arabia,ROW,Plan Tier 1,USD
                    Senegal,ROW,Plan Tier 5,USD
                    Serbia,ROW,Plan Tier 2,USD
                    Seychelles,ROW,Plan Tier 5,USD
                    Sierra Leone,ROW,Plan Tier 5,USD
                    Singapore,ASEAN & India,Plan Tier 2,SGD
                    Sint Maarten,AMER,Plan Tier 5,USD
                    Slovakia,ROW,Plan Tier 2,EUR
                    Slovenia,ROW,Plan Tier 1,EUR
                    Solomon Islands,ROW,Plan Tier 2,USD
                    Somalia,ROW,Plan Tier 5,USD
                    South Africa,ROW,Plan Tier 4,USD
                    South Sudan,ROW,Plan Tier 5,USD
                    Spain,AMER,Plan Tier 1,EUR
                    Sri Lanka,ROW,Plan Tier 5,USD
                    Sudan,ROW,Plan Tier 5,USD
                    Suriname,AMER,Plan Tier 3,USD
                    Svalbard and Jan Mayen,ROW,Plan Tier 1,USD
                    Eswatini,ROW,Plan Tier 5,USD
                    Sweden,ROW,Plan Tier 1,USD
                    Switzerland,ROW,Plan Tier 1,USD
                    Syria,ROW,Plan Tier 5,USD
                    Taiwan,GCR & UAE,Plan Tier 2,USD
                    Tajikistan,ROW,Plan Tier 5,USD
                    Tanzania,ROW,Plan Tier 5,USD
                    Thailand,ASEAN & India,Plan Tier 4,USD
                    Togo,ROW,Plan Tier 5,USD
                    Tokelau,ROW,Plan Tier 1,USD
                    Tonga,ROW,Plan Tier 5,USD
                    Trinidad and Tobago,AMER,Plan Tier 5,USD
                    Tunisia,ROW,Plan Tier 5,USD
                    Turkey,ROW,Plan Tier 2,USD
                    Turkmenistan,ROW,Plan Tier 5,USD
                    Turks and Caicos Islands,AMER,Plan Tier 5,USD
                    Tuvalu,ROW,Plan Tier 2,USD
                    U.S. Virgin Islands,ROW,Plan Tier 1,USD
                    Uganda,ROW,Plan Tier 5,USD
                    Ukraine,ROW,Plan Tier 2,USD
                    United Arab Emirates,GCR & UAE,Plan Tier 1,AED
                    United Kingdom,ROW,Plan Tier 1,GBP
                    United States,AMER,Plan Tier 1,USD
                    Uruguay,AMER,Plan Tier 3,USD
                    Uzbekistan,ROW,Plan Tier 5,USD
                    Vanuatu,ROW,Plan Tier 5,USD
                    Vatican City,ROW,Plan Tier 1,EUR
                    Venezuela,AMER,Plan Tier 3,USD
                    Vietnam,ASEAN & India,Plan Tier 4,USD
                    Wallis and Futuna,ROW,Plan Tier 1,USD
                    Western Sahara,ROW,Plan Tier 5,USD
                    Yemen,ROW,Plan Tier 5,USD
                    Zambia,ROW,Plan Tier 5,USD
                    Zimbabwe,ROW,Plan Tier 5,USD
                    ```

                    ### Example

                    Phone Number: "+852 9123 4567"
                    Reference Country (libphonenumber): "Hong Kong SAR China"
                    Output:
                    {
                        "explanation": "The closest match of `Hong Kong SAR China` is `Hong Kong SAR`. `Hong Kong SAR,GCR & UAE,Plan Tier 1,HKD` is filtered.",
                        "country": "Hong Kong SAR",
                        "region": "GCR & UAE",
                        "tier": "Plan Tier 1",
                        "local_currency": "HKD"
                    }

                    ### Here is the Phone Number and Country you need to determine the plan tier for:

                    Phone Number: {{$PHONE_NUMBER}}
                    Reference Country (libphonenumber): {{$COUNTRY}}
                    """,

                #endregion

                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "PHONE_NUMBER", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "COUNTRY", IsRequired = true
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "A json"
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                },
            });

        // Invoke the function with the provided input
        try
        {
            var chatMessageContent = await summarizeFunction.InvokeAsync<ChatMessageContent>(
                kernel,
                new KernelArguments(promptExecutionSettings)
                {
                    {
                        "PHONE_NUMBER", phoneNumber
                    },
                    {
                        "COUNTRY", country
                    }
                });

            var determinePlanTierResponse =
                JsonConvert.DeserializeObject<DeterminePlanTierResponse>(chatMessageContent!.Content!)!;

            determinePlanTierResponse.Tier = determinePlanTierResponse.Tier.Replace("Plan Tier ", string.Empty);

            _logger.LogInformation(
                "Plan tier determined: {PlanTier}, {Region}, {Country}, {Currency}, Explanation: {Explanation}",
                determinePlanTierResponse.Tier,
                determinePlanTierResponse.Region,
                determinePlanTierResponse.Country,
                determinePlanTierResponse.LocalCurrency,
                determinePlanTierResponse.Explanation);

            return determinePlanTierResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing data");

            return new DeterminePlanTierResponse("Unable to determine.", "Unknown", "GCR & UAE", "1", "HKD");
        }
    }
}