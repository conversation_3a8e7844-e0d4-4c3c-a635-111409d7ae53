name: 'Check Service Changes'
description: 'Determines if common files or service-specific files have changed'

inputs:
  service_name:
    description: 'Name of the service to check'
    required: true

outputs:
  common_changed:
    description: 'True if common files have changed'
    value: ${{ steps.check-changes.outputs.common_changed }}
  service_changed:
    description: 'True if service-specific files have changed'
    value: ${{ steps.check-changes.outputs.service_changed }}

runs:
  using: 'composite'
  steps:
    - id: check-changes
      shell: bash
      run: |
        echo "::group::Debug - Git Info"
        echo "Current commit: ${{ github.sha }}"
        
        # Determine base commit based on event type
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          echo "Base commit: ${{ github.event.pull_request.base.sha }}"
          base_commit="${{ github.event.pull_request.base.sha }}"
          git fetch origin ${{ github.event.pull_request.base.ref }}
        else
          echo "Before commit: ${{ github.event.before }}"
          if [[ -n "${{ github.event.before }}" ]] && git rev-parse --quiet --verify "${{ github.event.before }}^{commit}" > /dev/null; then
            base_commit="${{ github.event.before }}"
            echo "Using before commit as base: $base_commit"
          else
            echo "Before commit not available or invalid, trying merge base..."
            # Fallback: try to get the merge base with the default branch
            git fetch origin ${{ github.event.repository.default_branch }}
            if git merge-base --is-ancestor HEAD origin/${{ github.event.repository.default_branch }}; then
              base_commit=$(git merge-base HEAD origin/${{ github.event.repository.default_branch }})
              echo "Using merge-base as base: $base_commit"
            else
              echo "No valid base commit found, marking everything as changed"
              echo "common_changed=true" >> $GITHUB_OUTPUT
              echo "service_changed=true" >> $GITHUB_OUTPUT
              exit 0
            fi
          fi
        fi
        echo "::endgroup::"

        echo "::group::Debug - Changed Files"
        # Get the list of changed files
        if ! git diff --name-only $base_commit ${{ github.sha }} > changed_files.txt; then
          echo "Git diff failed, marking everything as changed"
          echo "common_changed=true" >> $GITHUB_OUTPUT
          echo "service_changed=true" >> $GITHUB_OUTPUT
          exit 0
        fi
        echo "Changed files:"
        cat changed_files.txt
        echo "::endgroup::"

        echo "::group::Debug - Common Files Check"
        # Load common projects configuration
        common_config=$(jq -r '.' .github/workflows/common-projects.json)

        # Create regex pattern from configuration
        common_files_pattern=$(echo "$common_config" | jq -r '.commonFiles | join("|")' | sed 's/\./\\./g')
        common_projects_pattern=$(echo "$common_config" | jq -r '.commonProjects | map(. + "/.*") | join("|")' | sed 's/\./\\./g')

        # Combine patterns and check for changes
        if grep -q -E "^($common_files_pattern|$common_projects_pattern)" changed_files.txt; then
          echo "Common files changed"
          echo "common_changed=true" >> $GITHUB_OUTPUT
        else
          echo "No common files changed"
          echo "common_changed=false" >> $GITHUB_OUTPUT
        fi

        # Debug output
        echo "Common files pattern: $common_files_pattern"
        echo "Common projects pattern: $common_projects_pattern"
        echo "::endgroup::"

        echo "::group::Debug - Service Files Check"
        # Get service info from mappings file
        service_info=$(jq -r --arg svc "${{ inputs.service_name }}" '.[$svc]' .github/workflows/service-mappings.json)
        path=$(echo "$service_info" | jq -r '.path')
        echo "Checking service: ${{ inputs.service_name }}"
        echo "Service path: $path"

        # Get dependencies from service info
        dependencies=$(echo "$service_info" | jq -r '.dependencies // [] | join("|")')

        # Create pattern for service path and its dependencies
        if [ -n "$dependencies" ]; then
          check_pattern="^($path|$dependencies)(/|$)"
        else
          check_pattern="^$path(/|$)"
        fi

        echo "Checking pattern: $check_pattern"

        # Check if service-specific files or its dependencies have changed
        if grep -q -E "$check_pattern" changed_files.txt; then
          echo "Service files or dependencies changed"
          echo "service_changed=true" >> $GITHUB_OUTPUT
        else
          echo "No service files or dependencies changed"
          echo "service_changed=false" >> $GITHUB_OUTPUT
        fi
        echo "::endgroup::" 