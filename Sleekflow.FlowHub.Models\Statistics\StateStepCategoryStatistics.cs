﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.Statistics;

[ContainerId(ContainerNames.StateStepCategoryStatistics)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class StateStepCategoryStatistics : Entity, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    public StateIdentity StateIdentity { get; set; }

    [JsonProperty("state_trigger_step_category")]
    public string StateTriggerStepCategory { get; set; }

    [JsonProperty("step_category_count_dict")]
    public Dictionary<string, int> StepCategoryCountDict { get; set; }

    [JsonProperty("enrolled_at")]
    public DateTimeOffset EnrolledAt { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public StateStepCategoryStatistics(
        string id,
        string sleekflowCompanyId,
        string stateId,
        StateIdentity stateIdentity,
        string stateTriggerStepCategory,
        Dictionary<string, int> stepCategoryCountDict,
        DateTimeOffset enrolledAt,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(
            id,
            SysTypeNames.StateStepCategoryStatistics)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        StateIdentity = stateIdentity;
        StateTriggerStepCategory = stateTriggerStepCategory;
        StepCategoryCountDict = stepCategoryCountDict;
        EnrolledAt = enrolledAt;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}