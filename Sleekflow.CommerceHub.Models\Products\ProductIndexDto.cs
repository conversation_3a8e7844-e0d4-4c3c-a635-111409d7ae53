using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Products;

public class ProductIndexDto
{
    [SimpleField(IsKey = true, IsHidden = false)]
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [SimpleField(IsFilterable = true, IsHidden = false)]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [SimpleField(IsHidden = true)]
    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [SimpleField(IsHidden = true)]
    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [SearchableField(IsFilterable = true, IsFacetable = true, IsSortable = true)]
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [SearchableField(IsFacetable = true, IsFilterable = true)]
    [JsonProperty(Product.PropertyNameCategoryIds)]
    public List<string> CategoryIds { get; set; }

    [SearchableField(IsFilterable = true)]
    [JsonProperty(Product.PropertyNameSku)]
    public string? Sku { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameUrl)]
    public string? Url { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameImages)]
    public List<Image> Images { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameProductVariantNames)]
    public List<Multilingual> ProductVariantNames { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameProductVariantDescriptions)]
    public List<Description> ProductVariantDescriptions { get; set; }

    [SimpleField]
    [JsonProperty(Product.PropertyNameProductVariantAttributes)]
    public List<Product.ProductAttribute> ProductVariantAttributes { get; set; }

    [FieldBuilderIgnore]
    [JsonProperty(Product.PropertyNameProductVariantPrices)]
    public List<Price> ProductVariantPrices { get; set; }

    [SimpleField(IsFilterable = true)]
    [JsonProperty(Product.PropertyNameIsViewEnabled)]
    public bool IsViewEnabled { get; set; }

    [SearchableField(IsFilterable = true)]
    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [SimpleField]
    [JsonProperty(CommonFieldNames.PropertyNamePlatformData)]
    public PlatformDataIndexDto PlatformData { get; set; }

    [JsonConstructor]
    public ProductIndexDto(
        string id,
        string sleekflowCompanyId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        List<Multilingual> productVariantNames,
        List<Description> productVariantDescriptions,
        List<Product.ProductAttribute> productVariantAttributes,
        List<Price> productVariantPrices,
        bool isViewEnabled,
        List<string> recordStatuses,
        PlatformDataIndexDto platformData)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        StoreId = storeId;
        CategoryIds = categoryIds;
        Sku = sku;
        Url = url;
        Names = names;
        Descriptions = descriptions;
        Images = images;
        ProductVariantNames = productVariantNames;
        ProductVariantDescriptions = productVariantDescriptions;
        ProductVariantAttributes = productVariantAttributes;
        ProductVariantPrices = productVariantPrices;
        IsViewEnabled = isViewEnabled;
        RecordStatuses = recordStatuses;
        PlatformData = platformData;
    }

    public ProductIndexDto(Product product)
    {
        Id = product.Id;
        SleekflowCompanyId = product.SleekflowCompanyId;
        CreatedAt = product.CreatedAt;
        UpdatedAt = product.UpdatedAt;
        StoreId = product.StoreId;
        CategoryIds = product.CategoryIds;
        Sku = product.Sku;
        Url = product.Url;
        Names = product.Names;
        Descriptions = product.Descriptions;
        Images = product.Images;
        ProductVariantNames = product.ProductVariantNames;
        ProductVariantDescriptions = product.ProductVariantDescriptions;
        ProductVariantAttributes = product.ProductVariantAttributes;
        ProductVariantPrices = product.ProductVariantPrices;
        IsViewEnabled = product.IsViewEnabled;
        RecordStatuses = product.RecordStatuses;
        PlatformData = new PlatformDataIndexDto(product.PlatformData);
    }
}