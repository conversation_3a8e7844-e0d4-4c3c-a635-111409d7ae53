using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class HttpV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.http-request";

    public enum HttpMethod
    {
        Get,
        Post,
        Put,
        Delete
    }

    [Required]
    [JsonProperty("method")]
    [JsonConverter(typeof(StringEnumConverter))]
    public HttpMethod Method { get; set; }

    [Required]
    [JsonProperty("url__expr")]
    public string UrlExpr { get; set; }

    [JsonProperty("headers__key_expr_set")]
    public HashSet<HttpHeaderKeyValuePair>? HeadersKeyExprSet { get; set; }

    [JsonProperty("query_string__expr_set")]
    public HashSet<HttpQueryStringKeyValuePair>? QueryStringExprSet { get; set; }

    [JsonProperty("body__expr")]
    public string? BodyExpr { get; set; }

    [JsonProperty("body_type")]
    public string? BodyType { get; set; }

    [JsonProperty("content_type")]
    public string? ContentType { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.ExternalIntegration;

    [JsonConstructor]
    public HttpV2StepArgs(
        HttpMethod method,
        string urlExpr,
        HashSet<HttpHeaderKeyValuePair>? headersKeyExprSet = null,
        HashSet<HttpQueryStringKeyValuePair>? queryStringExprSet = null,
        string? bodyExpr = null,
        string? bodyType = null,
        string? contentType = null)
    {
        Method = method;
        UrlExpr = urlExpr;
        HeadersKeyExprSet = headersKeyExprSet;
        QueryStringExprSet = queryStringExprSet;
        BodyExpr = bodyExpr;
        BodyType = bodyType;
        ContentType = contentType;
    }
}
