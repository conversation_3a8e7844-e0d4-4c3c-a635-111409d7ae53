function upsertProductProcedure() {

    // Request entity field types 
    const STORE_ID = "store_id";
    const SLEEKFLOW_COMPANY_ID = "sleekflow_company_id";
    const PRODUCT_ID = "product_id";
    const ATTRIBUTES = "attributes";
    const PRICE = "price"
    const SYS_TYPE_NAME = "sys_type_name";
    const NAME = "name";
    // Record status type
    const PRODUCT = "Product";
    const PRODUCT_VARIANT = "ProductVariant";
    const ACTIVE = "Active"

    // Extract request information
    var context = getContext();
    var container = context.getCollection();
    var response = context.getResponse();
    
    // Obtain the response body
    var entity = response.getBody();

    // Proceedings the trigger logic if SYS_TYPE_NAME is PRODUCT_VARIANT
    if ((SYS_TYPE_NAME in entity) && entity[SYS_TYPE_NAME] !== PRODUCT_VARIANT) return

    // Query for both product and all the related product variants
    const query = {
        query: `SELECT *
                    FROM root r
                    WHERE (r.id = @productId or r.product_id = @productId)
                       AND r.store_id = @storeId
                       AND r.sleekflow_company_id = @sleekflowCompanyId
                       AND ARRAY_CONTAINS(r.record_statuses, @active)`,
        parameters: [
            { name: "@productId", value: entity[PRODUCT_ID]},
            { name: "@storeId", value: entity[STORE_ID] },
            { name: "@sleekflowCompanyId", value: entity[SLEEKFLOW_COMPANY_ID]},
            { name: "@active", value: ACTIVE }
        ]
    };

    var accept = container.queryDocuments(container.getSelfLink(), query, upsertProductCallBack);
    if (!accept) throw "Unable to upsert product, abort";

    function upsertProductCallBack(err, collections, responseOptions) {
        if (err) throw new Error("Error in upsert product " + err.message);
        if (collections.length === 0) throw new Error("Collection is empty ", collections);
        
        var product = collections.find(c => c[SYS_TYPE_NAME] === PRODUCT);
        var variant = collections.filter(c => c[SYS_TYPE_NAME] === PRODUCT_VARIANT);
        
        // Pushing updated variant
        variant.push(entity);
        
        // Hard replace the object list with the collections
        var attributes = variant.filter(v => v[ATTRIBUTES] !== null).map(v => v[ATTRIBUTES]);
        attributes = DistinctCollection([].concat.apply([], attributes), AttributeFunc);
        var keys = DistinctKeys(attributes, NAME)
        product.attributes = keys.map(k => ProductAttribute(k,
            attributes.filter(c => c.name === k).map(m => AttributeValue(m.value))))
        
        // Hard replace the object list with the collections
        var prices = variant.filter(v => v[PRICE] !== null).map(v => v[PRICE])
        product.prices = DistinctCollection(prices, PriceFunc);
        
        // Trigger upsert action 
        var accept = container.replaceDocument(product._self, product, (err, _) => {
            if (err) throw "Unable to update product , abort";
        });

        // Validate Update state
        if (!accept) throw "Unable to update product, abort";
        return;
    }
    
    // HELPER FUNCTIONS
    // Remove duplication of the object
    const DistinctCollection = (collections, action) => [...collections.reduce(action, new Map()).values()]
    // Collect the distinct keys of the objects i.e. name
    const DistinctKeys = (collections, type) => [...new Set(collections.map(c => c[type]))]
    
    // Attribute distinct functions
    const AttributeFunc = (map, { name, value }) => {
        return (map.set(`${name}-${value}`, { name, value }));
    }
    // Price distinct functions
    const PriceFunc = (map, { currency_iso_code, amount }) => {
        return (map.set(`${currency_iso_code}-${amount}`, { currency_iso_code, amount }));
    }
    
    // Model
    // Product attribute model
    const ProductAttribute = (name, value) => {
        return {
            "name" : name,
            "values" : value
        }
    }
    // Attribute value model
    const AttributeValue = (value) => {
        return {
            "value" : value
        }
    }

}