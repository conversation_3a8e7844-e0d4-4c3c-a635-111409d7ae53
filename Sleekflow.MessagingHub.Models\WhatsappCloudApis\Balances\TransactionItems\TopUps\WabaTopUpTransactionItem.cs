using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps.Stripe;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

public class WabaTopUpTransactionItem : TransactionItem
{
    [JsonProperty("pay_amount")]
    public Money PayAmount { get; set; }

    [JsonProperty("payment_method")]
    public string PaymentMethod { get; set; }

    [JsonProperty("stripe_top_up_credit_detail")]
    public StripeTopUpCreditDetail? StripeTopUpCreditDetail { get; set; }

    [JsonProperty("internal_top_up_credit_detail")]
    public InternalTopUpCreditDetail? InternalTopUpCreditDetail { get; set; }

    [JsonConstructor]
    public WabaTopUpTransactionItem(
        Money payAmount,
        string topUpPaymentMethod,
        StripeTopUpCreditDetail? stripeTopUpCreditDetail,
        InternalTopUpCreditDetail? internalTopUpCreditDetail)
    {
        PayAmount = payAmount;
        PaymentMethod = topUpPaymentMethod;
        StripeTopUpCreditDetail = stripeTopUpCreditDetail;
        InternalTopUpCreditDetail = internalTopUpCreditDetail;
    }

    // Avoid the Azure Service Bus created a duplicate of the message and we consumer the credit balance twice.
    [JsonIgnore]
    public override string GetUniqueId
    {
        get
        {
            switch (PaymentMethod)
            {
                case TopUpPaymentMethods.Internal:
                    // e.g. The unique id pattern: facebookBusinessId/staffId/timestamp
                    // 688872929128013/8a77de3f-6ae6-4839-90f3-c6feebe8a284 <- (Travis_backend - AspNetUsers - Id)/1672367279
                    return InternalTopUpCreditDetail!.UniqueId;
                case TopUpPaymentMethods.Stripe:
                    return StripeTopUpCreditDetail!.CheckoutSessionId ?? StripeTopUpCreditDetail!.InvoiceId;
                default:
                    return Guid.NewGuid().ToString();
            }
        }
    }
}