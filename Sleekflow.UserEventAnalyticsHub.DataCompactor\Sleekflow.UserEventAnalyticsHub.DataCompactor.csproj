﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="DuckDB.NET.Data.Full" Version="1.3.0" />
        <PackageReference Include="Azure.Storage.Files.DataLake" Version="12.21.0" />
        <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="Dapper" Version="2.1.66" />
        <PackageReference Include="Npgsql" Version="8.0.3" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
        <PackageReference Include="Polly" Version="8.5.2" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="../Sleekflow/Sleekflow.csproj" />
        <ProjectReference Include="../Sleekflow.Models/Sleekflow.Models.csproj" />
        <ProjectReference Include="../Sleekflow.UserEventHub.Models/Sleekflow.UserEventHub.Models.csproj" />
    </ItemGroup>

</Project>