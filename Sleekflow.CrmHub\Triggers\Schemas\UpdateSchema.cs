﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class UpdateSchema : ITrigger<UpdateSchema.UpdateSchemaInput, UpdateSchema.UpdateSchemaOutput>
{
    private readonly ISchemaService _schemaService;

    public UpdateSchema(
        ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public class UpdateSchemaInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [Required]
        [JsonProperty(Schema.PropertyNameDisplayName)]
        public string DisplayName { get; set; }

        [Required]
        [JsonProperty("primary_property_display_name")]
        public string PrimaryPropertyDisplayName { get; set; }

        [Required]
        [JsonProperty(Schema.PropertyNameIsEnabled)]
        public bool IsEnabled { get; set; }

        [Required]
        [JsonProperty(Schema.PropertyNameProperties)]
        [Validations.ValidateArray]
        public List<Property> Properties { get; set; }

        [JsonConstructor]
        public UpdateSchemaInput(
            string sleekflowCompanyId,
            string id,
            string displayName,
            string primaryPropertyDisplayName,
            bool isEnabled,
            List<Property> properties)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Id = id;
            DisplayName = displayName;
            PrimaryPropertyDisplayName = primaryPropertyDisplayName;
            IsEnabled = isEnabled;
            Properties = properties;
        }
    }

    public class UpdateSchemaOutput
    {
        [JsonProperty("schema")]
        public SchemaDto Schema { get; set; }

        [JsonConstructor]
        public UpdateSchemaOutput(
            SchemaDto schema)
        {
            Schema = schema;
        }
    }

    public async Task<UpdateSchemaOutput> F(UpdateSchemaInput updateSchemaInput)
    {
        var schema = await _schemaService.UpdateAndGetAsync(
            updateSchemaInput.Id,
            updateSchemaInput.SleekflowCompanyId,
            updateSchemaInput.DisplayName,
            updateSchemaInput.PrimaryPropertyDisplayName,
            updateSchemaInput.IsEnabled,
            updateSchemaInput.Properties);

        return new UpdateSchemaOutput(new SchemaDto(schema));
    }
}