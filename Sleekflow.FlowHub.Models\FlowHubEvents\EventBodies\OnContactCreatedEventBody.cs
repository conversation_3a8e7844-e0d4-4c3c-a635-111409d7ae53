using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnContactCreatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactCreated; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [Required]
    [JsonProperty("created_contact")]
    public Dictionary<string, object?> CreatedContact { get; set; }

    [Required]
    [Json<PERSON>roperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [JsonConstructor]
    public OnContactCreatedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        Dictionary<string, object?> createdContact,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        CreatedContact = createdContact;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
    }
}