﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;

public class Input
{
    [JsonProperty("sleekflow_company_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    [System.ComponentModel.DataAnnotations.Required]
    public string TypeName { get; set; }

    [JsonConstructor]
    public Input(string sleekflowCompanyId, string typeName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        TypeName = typeName;
    }
}