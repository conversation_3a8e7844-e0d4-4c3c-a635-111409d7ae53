﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class GetWorkflowStatus
    : ITrigger<GetWorkflowStatus.GetWorkflowStatusInput, GetWorkflowStatus.GetWorkflowStatusOutput>
{
    private readonly IWorkflowService _workflowService;

    public GetWorkflowStatus(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class GetWorkflowStatusInput
        : Models.Internals.GetWorkflowStatusInput
    {
        [JsonConstructor]
        public GetWorkflowStatusInput(string sleekflowCompanyId, string workflowVersionedId)
            : base(sleekflowCompanyId, workflowVersionedId)
        {
        }
    }

    public class GetWorkflowStatusOutput
        : Models.Internals.GetWorkflowStatusOutput
    {
        [JsonConstructor]
        public GetWorkflowStatusOutput(
            string workflowStatus)
            : base(workflowStatus)
        {
        }
    }

    public async Task<GetWorkflowStatusOutput> F(
        GetWorkflowStatusInput input)
    {
        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionId);

        return new GetWorkflowStatusOutput(proxyWorkflow == null ? string.Empty : proxyWorkflow.ActivationStatus);
    }
}