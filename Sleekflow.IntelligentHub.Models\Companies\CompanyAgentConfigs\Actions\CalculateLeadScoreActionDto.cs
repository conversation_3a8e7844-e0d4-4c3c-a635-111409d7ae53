using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class CalculateLeadScoreActionDto : BaseActionDto
{
    [JsonProperty("criteria")]
    public List<LeadScoreCriterionDto> Criteria { get; set; }

    [JsonConstructor]
    public CalculateLeadScoreActionDto(bool enabled, List<LeadScoreCriterionDto> criteria)
        : base(enabled)
    {
        Criteria = criteria;
    }

    public CalculateLeadScoreActionDto(CalculateLeadScoreAction action)
        : base(action)
    {
        Criteria = action.Criteria.Select(c => c.ToDto()).ToList();
    }
} 