﻿namespace Sleekflow.Exceptions.MessagingHub;

public class SfIncorrectStateIdException : ErrorCodeException
{
    public string StateId { get; }

    public SfIncorrectStateIdException(string stateId)
        : base(
            ErrorCodeConstant.SfIncorrectStateIdException,
            $"The StateId is incorrect. Please check. stateId=[{stateId}]",
            new Dictionary<string, object?>
            {
                {
                    "stateId", stateId
                },
            })
    {
        StateId = stateId;
    }
}