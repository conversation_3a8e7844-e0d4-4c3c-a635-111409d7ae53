﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Configs;

public interface IStorageConfig
{
    string InternalStorageConnStr { get; }

    string ExternalStorageConnStr { get; set; }

    string ExternalStorageContainerName { get; set; }
}

public class StorageConfig : IConfig, IStorageConfig
{
    public string InternalStorageConnStr { get; }

    public string ExternalStorageConnStr { get; set; }

    public string ExternalStorageContainerName { get; set; }

    public StorageConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        InternalStorageConnStr =
            Environment.GetEnvironmentVariable("INTERNAL_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("INTERNAL_STORAGE_CONN_STR");
        ExternalStorageConnStr =
            Environment.GetEnvironmentVariable("EXTERNAL_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("EXTERNAL_STORAGE_CONN_STR");
        ExternalStorageContainerName =
            Environment.GetEnvironmentVariable("EXTERNAL_STORAGE_CONTAINER_NAME", target)
            ?? throw new SfMissingEnvironmentVariableException("EXTERNAL_STORAGE_CONTAINER_NAME");
    }
}