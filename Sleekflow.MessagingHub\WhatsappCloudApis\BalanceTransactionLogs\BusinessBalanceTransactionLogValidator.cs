using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.Models;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;

public interface IBusinessBalanceTransactionLogValidator
{
    void AssertValidBusinessBalanceTransactionLog(
        List<WhatsappConversationAnalyticsResultDataPoint> dataPoints);
}

public class BusinessBalanceTransactionLogValidator
    : IBusinessBalanceTransactionLogValidator, ISingletonService
{
    public void AssertValidBusinessBalanceTransactionLog(
        List<WhatsappConversationAnalyticsResultDataPoint> dataPoints)
    {
        if (dataPoints == null)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult(
                        "Data points cannot be null",
                        new[]
                        {
                            nameof(dataPoints)
                        })
                });
        }

        if (dataPoints.Count == 0)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult(
                        "Data points have no items",
                        new[]
                        {
                            nameof(dataPoints)
                        })
                });
        }

        // Direction based or Category based
        if (dataPoints.All(x => x.ConversationDirection == null || x.ConversationType == null)
            &&
            dataPoints.All(x => x.ConversationCategory == null || x.ConversationType == null))
        {
            throw new ArgumentException(
                "Conversation Analytic DataPoints missing ConversationDirection or ConversationType or ConversationCategory");
        }
    }
}