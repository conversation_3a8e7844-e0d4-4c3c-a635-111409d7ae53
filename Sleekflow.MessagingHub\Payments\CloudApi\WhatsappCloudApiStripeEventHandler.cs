using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

namespace Sleekflow.MessagingHub.Payments.CloudApi;

public static class WhatsappCloudApiStripeEventHandler
{
    public static bool IsWhatsappCloudApiManualTopUpEvent(IReadOnlyDictionary<string, string> metadata)
    {
        var isWhatsappCloudApiManualTopUpEvent = metadata.TryGetValue("type", out var type);

        if (isWhatsappCloudApiManualTopUpEvent is false)
        {
            return false;
        }

        return type == TopUpTypes.WhatsappCloudApiManualTopUp;
    }

    public static bool IsWhatsappCloudApiAutoTopUpEvent(IReadOnlyDictionary<string, string> metadata)
    {
        var isWhatsappCloudApiAutoTopUpEvent = metadata.TryGetValue("type", out var type);

        if (isWhatsappCloudApiAutoTopUpEvent is false)
        {
            return false;
        }

        return type == TopUpTypes.WhatsappCloudApiAutoTopUp;
    }
}