using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.StatisticsCalculators;

[TestFixture]
[TestOf(typeof(ExcelStatisticsCalculator))]
public class ExcelStatisticsCalculatorTest
{
    const string ExcelFilePath = "../../../Binaries/clinic open hour.xlsx";
    const string ExcelFilePath2 = "../../../Binaries/100-excel-rows.xlsx";

    [Test]
    public void CalculateDocumentStatisticsTest()
    {
        var statisticsCalculator = new ExcelStatisticsCalculator(new Mock<IDocumentCounterService>().Object);

        var fileStream = new FileStream(ExcelFilePath2, FileMode.Open, FileAccess.Read, FileShare.Read);
        var documentStatistics = statisticsCalculator.CalculateDocumentStatistics(fileStream);

        Assert.That(documentStatistics.TotalPages, Is.EqualTo(3));
    }
}