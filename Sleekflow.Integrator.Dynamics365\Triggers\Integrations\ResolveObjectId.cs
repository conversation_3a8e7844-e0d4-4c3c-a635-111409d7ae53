using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ResolveObjectId : ITrigger
{
    private readonly IDynamics365ObjectService _dynamics365ObjectService;

    public ResolveObjectId(
        IDynamics365ObjectService dynamics365ObjectService)
    {
        _dynamics365ObjectService = dynamics365ObjectService;
    }

    public class ResolveObjectIdInput
    {
        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public ResolveObjectIdInput(
            Dictionary<string, object?> dict,
            string entityTypeName)
        {
            Dict = dict;
            EntityTypeName = entityTypeName;
        }
    }

    public class ResolveObjectIdOutput
    {
        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonConstructor]
        public ResolveObjectIdOutput(
            string? id)
        {
            Id = id;
        }
    }

    public Task<ResolveObjectIdOutput> F(
        ResolveObjectIdInput resolveObjectIdInput)
    {
        try
        {
            var id = _dynamics365ObjectService.ResolveObjectId(
                resolveObjectIdInput.Dict,
                resolveObjectIdInput.EntityTypeName);

            return Task.FromResult(new ResolveObjectIdOutput(id));
        }
        catch (SfIdUnresolvableException)
        {
            return Task.FromResult(new ResolveObjectIdOutput(null));
        }
    }
}