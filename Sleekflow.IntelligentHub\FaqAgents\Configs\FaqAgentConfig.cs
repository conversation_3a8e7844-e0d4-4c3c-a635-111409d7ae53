namespace Sleekflow.IntelligentHub.FaqAgents.Configs;

public class FaqAgentConfig
{
    public string CompanyName { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string AzureSearchIndexName { get; set; }

    public string HeadlessApiKey { get; set; }

    public List<string> AdditionalPromptsForOptimizedSearchQuery { get; set; }

    public List<string> AdditionalPromptsForChat { get; set; }

    public bool ShouldShowCitation { get; set; }

    public int MaxNumOfSearchResults { get; set; }

    public FaqAgentConfig(
        string companyName,
        string sleekflowCompanyId,
        string headlessApiKey,
        string azureSearchIndexName,
        List<string> additionalPromptsForOptimizedSearchQuery,
        List<string> additionalPromptsForChat,
        bool shouldShowCitation,
        int maxNumOfSearchResults)
    {
        CompanyName = companyName;
        SleekflowCompanyId = sleekflowCompanyId;
        HeadlessApiKey = headlessApiKey;
        AzureSearchIndexName = azureSearchIndexName;
        AdditionalPromptsForOptimizedSearchQuery = additionalPromptsForOptimizedSearchQuery;
        AdditionalPromptsForChat = additionalPromptsForChat;
        ShouldShowCitation = shouldShowCitation;
        MaxNumOfSearchResults = maxNumOfSearchResults;
    }
}