/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  const domain = event.secrets.domain;
  const clientId = event.secrets.client_id;
  const clientSecret = event.secrets.client_secret;
  const postLoginWebhookUrl = event.secrets.post_login_webhook;
  const issuer = event.secrets.issuer;
  const audience = event.secrets.audience;

  const axios = require("axios").default;

  const sendPostLoginWebhook = async (user) => {
    const jwt = require('jwt-encode');

    const data = {
      iss: issuer,
      aud: audience,
      domain: domain,
      client_id: clientId
    }

    const tokenSecret = issuer + audience + "+wsadz4gI_3DUXI8P";
    const token = jwt(data, tokenSecret);

    const options = {
      method: 'POST',
      url: postLoginWebhookUrl,
      params: {connection_strategy: event.connection.strategy, token: token, client_id: event.client.client_id},
      headers: {'content-type': 'application/json'},
      data: user
    };

    const response = await axios.request(options);

    return response;
  };

  const res = await sendPostLoginWebhook(event.user);
  if (res.status !== 200) {
    api.access.deny('Internal Server Error.')
  }

  // public static class PostUserLoginResponseStatus
  // {
  //   public const string Unchanged = "unchanged";
  //   public const string MergedByAuth0UserId = "merged_by_auth0_user_id";
  //   public const string MergedByEmail = "merged_by_email";
  //   public const string ErrorConflictUsername = "error_conflict_username";
  //   public const string ErrorConflictEmailAndNotVerified = "error_conflict_email_and_not_verified";
  //   public const string ErrorEmailAndNotVerified = "error_email_and_not_verified";
  //   public const string ErrorUserMustCompleteInvitation = "error_user_must_complete_invitation";
  //   public const string NewUser = "new_user";
  //
  //   public static readonly string[] AllStatuses =
  //   {
  //     Unchanged,
  //     MergedByAuth0UserId,
  //     MergedByEmail,
  //     ErrorConflictUsername,
  //     ErrorConflictEmailAndNotVerified,
  //     ErrorEmailAndNotVerified,
  //     ErrorUserMustCompleteInvitation,
  //     NewUser
  //   };
  // }

  if (res.data.status === "error_conflict_username") {
    api.access.deny('Username already exists.')
  } else if (res.data.status === "error_conflict_email_and_not_verified" || res.data.status === "error_email_and_not_verified") {
    api.access.deny('You need to first verify the email.')
  } else if (res.data.status === 'error_user_must_complete_invitation') {
    api.access.deny('Invited user must complete the invitation before login. Please check your email and follow the instruction to complete the invitation.')
  } else if (res.data.status === "error_not_valid_enterprise_user") {
      api.access.deny(`Not a valid enterprise user connection strategy: ${event.connection.strategy}`)
  } else if (
    res.data.status === "merged_by_auth0_user_id"
    || res.data.status === "merged_by_email" || res.data.status === "merged_by_enterprise_imported_user"
    || res.data.status === "new_user") {
    api.user.setAppMetadata("sleekflow_id", res.data.app_metadata.sleekflow_id);
    api.user.setAppMetadata("roles", res.data.app_metadata.roles);
    api.user.setAppMetadata("phone_number", res.data.app_metadata.phone_number);
    api.user.setAppMetadata("login_requires_email_verification", res.data.app_metadata.login_requires_email_verification);
  } else if (res.data.status === "unchanged") {
    // do nothing
  } else {
    api.access.deny('Internal Server Error.')
  }
};


/**
 * Handler that will be invoked when this action is resuming after an external redirect. If your
 * onExecutePostLogin function does not perform a redirect, this function can be safely ignored.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
// exports.onContinuePostLogin = async (event, api) => {
// };
