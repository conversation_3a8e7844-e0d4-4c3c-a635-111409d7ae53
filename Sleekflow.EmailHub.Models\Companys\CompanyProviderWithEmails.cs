using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Companys;

public class CompanyProviderWithEmails
{
    [JsonConstructor]
    public CompanyProviderWithEmails(string providerName, List<string> emailAddresses)
    {
        ProviderName = providerName;
        EmailAddresses = emailAddresses;
    }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("email_address")]
    public List<string> EmailAddresses { get; set; }
}