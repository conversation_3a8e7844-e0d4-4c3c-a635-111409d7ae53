﻿using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Blobs;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowMetadataService
{
    Task<Dictionary<string, object?>> SaveWorkflowMetadataAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        Dictionary<string, object?> workflowMetadata);

    Task<Dictionary<string, object?>> GetWorkflowMetadataAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);

    Task<bool> DeleteWorkflowMetadataAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);
}

public class WorkflowMetadataService : IWorkflowMetadataService, IScopedService
{
    public const string WorkflowMetadataContainerName = "workflow-metadata";

    private readonly IBlobService _blobService;
    private readonly ICacheService _cacheService;
    private readonly ISpecializedMemoryCacheService _specializedMemoryCache;

    public WorkflowMetadataService(
        IBlobService blobService,
        ICacheService cacheService,
        IEnumerable<ISpecializedMemoryCacheService> specializedMemoryCacheServices)
    {
        _blobService = blobService;
        _cacheService = cacheService;
        _specializedMemoryCache = specializedMemoryCacheServices
            .First(x => x.CacheName == WorkflowMetadataContainerName);
    }

    public async Task<Dictionary<string, object?>> SaveWorkflowMetadataAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        Dictionary<string, object?> workflowMetadata)
    {
        await _blobService.UploadAsJsonAsync(
            WorkflowMetadataContainerName,
            GetWorkflowMetadataBlobName(
                sleekflowCompanyId,
                workflowVersionedId),
            workflowMetadata);

        return workflowMetadata;
    }

    public async Task<Dictionary<string, object?>> GetWorkflowMetadataAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        var cacheKey = $"WorkflowMetadata:{sleekflowCompanyId}:{workflowVersionedId}";

        var metadata = await _specializedMemoryCache.GetOrCreateAsync(
            cacheKey,
            () => _cacheService.CacheAsync(
                cacheKey,
                () => _blobService.DownloadJsonAsAsync<Dictionary<string, object?>>(
                    WorkflowMetadataContainerName,
                    GetWorkflowMetadataBlobName(
                        sleekflowCompanyId,
                        workflowVersionedId)),
                TimeSpan.FromMinutes(10)));

        return metadata ?? new();
    }

    public Task<bool> DeleteWorkflowMetadataAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        return _blobService.RemoveIfExistsAsync(
            WorkflowMetadataContainerName,
            GetWorkflowMetadataBlobName(
                sleekflowCompanyId,
                workflowVersionedId));
    }

    private static string GetWorkflowMetadataBlobName(
        string sleekflowCompanyId,
        string workflowVersionedId)
        => Path.Combine(sleekflowCompanyId, workflowVersionedId) + ".json";
}