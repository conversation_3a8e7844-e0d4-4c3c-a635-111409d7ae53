using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Filters;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.Validations;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs
    : ITrigger<
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs.
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput,
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs.
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput>
{
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;

    public GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public class GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput
    {
        [JsonConstructor]
        public GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput(
            BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
            int limit,
            string? continuationToken)
        {
            BusinessBalanceTransactionLogFilter = businessBalanceTransactionLogFilter;
            Limit = limit;
            ContinuationToken = continuationToken;
        }

        [Required]
        [ValidateObject]
        [JsonProperty("business_balance_transaction_log_filter")]
        public BusinessBalanceTransactionLogFilter BusinessBalanceTransactionLogFilter { get; set; }

        [JsonProperty("limit")]
        [Range(1, 10000)]
        [Required]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    public class GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput
    {
        [JsonConstructor]
        public GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput(
            List<BusinessBalanceTransactionLog> businessBalanceTransactionLogs,
            string? nextContinuationToken,
            int count)
        {
            BusinessBalanceTransactionLogs = businessBalanceTransactionLogs;
            NextContinuationToken = nextContinuationToken;
            Count = count;
        }

        [JsonProperty("business_balance_transaction_logs")]
        public List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }
    }

    public async Task<GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput> F(
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput input)
    {
        var (transactionLogs, nextContinuationToken) =
            await _businessBalanceTransactionLogService.GetFilteredConversationUsageTransactionLogsAsync(
                input.BusinessBalanceTransactionLogFilter,
                input.Limit,
                input.ContinuationToken);

        return new GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput(
            transactionLogs,
            nextContinuationToken,
            transactionLogs.Count);
    }
}