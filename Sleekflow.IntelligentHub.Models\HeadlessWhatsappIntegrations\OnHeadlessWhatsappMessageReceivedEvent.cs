using Sleekflow.Events;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.HeadlessWhatsappIntegrations;

public class OnHeadlessWhatsappMessageReceivedEvent : IEvent, IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string ContextId { get; set; }

    public string PhoneNumber { get; set; }

    public string User { get; set; }

    public DateTimeOffset SentTime { get; set; }

    public OnHeadlessWhatsappMessageReceivedEvent(
        string sleekflowCompanyId,
        string contextId,
        string phoneNumber,
        string user,
        DateTimeOffset sentTime)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ContextId = contextId;
        PhoneNumber = phoneNumber;
        User = user;
        SentTime = sentTime;
    }
}