using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class RecommendReplyStreamingStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.recommend-reply-streaming";

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonConstructor]
    public RecommendReplyStreamingStepArgs(string contactIdExpr)
    {
        ContactIdExpr = contactIdExpr;
    }
}