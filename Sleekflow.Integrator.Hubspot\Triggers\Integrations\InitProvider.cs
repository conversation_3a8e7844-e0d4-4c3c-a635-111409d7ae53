using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Authentications;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProvider : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public InitProvider(IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class InitProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("return_to_url")]
        [Required]
        public string ReturnToUrl { get; set; }

        [JsonConstructor]
        public InitProviderInput(
            string sleekflowCompanyId,
            string returnToUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ReturnToUrl = returnToUrl;
        }
    }

    public class InitProviderOutput
    {
        [JsonProperty("hubspot_authentication_url")]
        public string HubspotAuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderOutput(string hubspotAuthenticationUrl)
        {
            HubspotAuthenticationUrl = hubspotAuthenticationUrl;
        }
    }

    public async Task<InitProviderOutput> F(
        InitProviderInput initProviderInput)
    {
        var redirectUrl =
            await _hubspotAuthenticationService.AuthenticateAsync(
                initProviderInput.SleekflowCompanyId,
                initProviderInput.ReturnToUrl);

        return new InitProviderOutput(redirectUrl);
    }
}