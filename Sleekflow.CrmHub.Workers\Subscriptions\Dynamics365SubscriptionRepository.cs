﻿using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.Subscriptions;

public interface IDynamics365SubscriptionRepository : IRepository<Dynamics365Subscription>
{
}

public class Dynamics365SubscriptionRepository
    : BaseRepository<Dynamics365Subscription>,
        IDynamics365SubscriptionRepository
{
    public Dynamics365SubscriptionRepository(
        ILogger<BaseRepository<Dynamics365Subscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}