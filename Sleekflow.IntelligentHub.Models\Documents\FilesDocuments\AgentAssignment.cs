using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

public static class RagStatus
{
    public const string Pending = "pending";
    public const string InProgress = "in-progress";
    public const string Completed = "completed";
    public const string Failed = "failed";
}

public class AgentAssignment
{
    public const string PropertyNameAgentId = "agent_id";
    public const string PropertyNameRagStatus = "rag_status";
    public const string PropertyNameRagUploadPercentage = "rag_status_upload_percentage";

    [JsonProperty(PropertyNameAgentId)]
    public string AgentId { get; set; }

    [JsonProperty(PropertyNameRagStatus)]
    [RegularExpression(
        $"{FilesDocuments.RagStatus.Pending}|{FilesDocuments.RagStatus.InProgress}|{FilesDocuments.RagStatus.Completed}|{FilesDocuments.RagStatus.Failed}")]
    public string RagStatus { get; set; }

    [JsonProperty(PropertyNameRagUploadPercentage)]
    public double RagStatusUploadPercentage { get; set; }

    [JsonConstructor]
    public AgentAssignment(string agentId, string ragStatus, double? ragStatusUploadPercentage)
    {
        AgentId = agentId;
        RagStatus = ragStatus;
        RagStatusUploadPercentage = ragStatusUploadPercentage ?? 0.0;
    }
}