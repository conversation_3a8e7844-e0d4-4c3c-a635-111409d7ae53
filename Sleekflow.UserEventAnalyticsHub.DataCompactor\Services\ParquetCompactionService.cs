using DuckDB.NET.Data;
using Microsoft.Extensions.Logging;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Extensions;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public class ParquetCompactionService : IParquetCompactionService
{
    private readonly ILogger<ParquetCompactionService> _logger;
    private readonly ICompactorConfig _compactorConfig;
    private readonly IPostgreSqlConfig _postgresConfig;
    private readonly IBlobDiscoveryService _blobDiscoveryService;
    private readonly IPostgreSqlSchemaService _schemaService;
    private readonly IFileTrackingService _fileTrackingService;

    public ParquetCompactionService(
        ILogger<ParquetCompactionService> logger,
        ICompactorConfig compactorConfig,
        IPostgreSqlConfig postgresConfig,
        IBlobDiscoveryService blobDiscoveryService,
        IPostgreSqlSchemaService schemaService,
        IFileTrackingService fileTrackingService)
    {
        _logger = logger;
        _compactorConfig = compactorConfig;
        _postgresConfig = postgresConfig;
        _blobDiscoveryService = blobDiscoveryService;
        _schemaService = schemaService;
        _fileTrackingService = fileTrackingService;
    }

    public async Task<ProcessingResult> CompactAllCompaniesAsync()
    {
        var result = new ProcessingResult();

        try
        {
            _logger.LogInformation("Starting compaction process for all companies");

            // Get all companies
            var companyIds = await _blobDiscoveryService.GetAllCompanyIdsAsync();
            _logger.LogInformation("Found {CompanyCount} companies to process", companyIds.Count);

            // Process companies with limited concurrency
            var semaphore = new SemaphoreSlim(_compactorConfig.MaxConcurrentCompanies);
            var tasks = companyIds.Select(async companyId =>
            {
                await semaphore.WaitAsync();
                try
                {
                    var companyResult = await CompactCompanyAsync(companyId);
                    lock (result)
                    {
                        result.TotalFilesProcessed += companyResult.TotalFilesProcessed;
                        result.TotalRecordsMigrated += companyResult.TotalRecordsMigrated;
                        result.CompaniesProcessed++;
                        if (companyResult.Success)
                        {
                            result.CompaniesSuccessful++;
                        }
                        else
                        {
                            result.CompaniesWithErrors++;
                        }
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            result.Success = result.CompaniesWithErrors == 0;
            result.EndTime = DateTime.UtcNow;

            _logger.LogInformation("Compaction process completed. Companies: {Total}, Successful: {Success}, Errors: {Errors}",
                result.CompaniesProcessed, result.CompaniesSuccessful, result.CompaniesWithErrors);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during compaction process");
            result.Success = false;
            result.EndTime = DateTime.UtcNow;
            return result;
        }
    }

    public async Task<ProcessingResult> CompactCompanyAsync(string sleekflowCompanyId)
    {
        var result = new ProcessingResult();

        try
        {
            _logger.LogInformation("Starting compaction for company: {CompanyId}", sleekflowCompanyId);

            // Ensure PostgreSQL schema exists
            await _schemaService.EnsureCompanySchemaAsync(sleekflowCompanyId);

            // Get all files for company
            var allFiles = await _blobDiscoveryService.DiscoverCompanyFilesAsync(sleekflowCompanyId);
            _logger.LogInformation("Found {FileCount} files for company: {CompanyId}", allFiles.Count, sleekflowCompanyId);

            // Filter out already migrated files
            var unmigratedFiles = await _fileTrackingService.GetUnmigratedFilesAsync(sleekflowCompanyId, allFiles);
            _logger.LogInformation("Found {UnmigratedCount} unmigrated files for company: {CompanyId}", unmigratedFiles.Count, sleekflowCompanyId);

            if (unmigratedFiles.Count == 0)
            {
                _logger.LogInformation("No unmigrated files found for company: {CompanyId}", sleekflowCompanyId);
                result.Success = true;
                return result;
            }

            // Process files in batches
            var batches = CreateBatches(unmigratedFiles, _compactorConfig.FileBatchSize);
            _logger.LogInformation("Processing {BatchCount} batches for company: {CompanyId}", batches.Count, sleekflowCompanyId);

            foreach (var batch in batches)
            {
                var batchResult = await ProcessBatchAsync(sleekflowCompanyId, batch);
                result.TotalFilesProcessed += batchResult.TotalFilesProcessed;
                result.TotalRecordsMigrated += batchResult.TotalRecordsMigrated;

                if (!batchResult.Success)
                {
                    _logger.LogWarning("Batch processing failed for company: {CompanyId}", sleekflowCompanyId);
                    result.Success = false;
                }
            }

            if (result.Success)
            {
                _logger.LogInformation("Successfully completed compaction for company: {CompanyId}. Files: {Files}, Records: {Records}",
                    sleekflowCompanyId, result.TotalFilesProcessed, result.TotalRecordsMigrated);
            }
            else
            {
                _logger.LogWarning("Compaction completed with errors for company: {CompanyId}", sleekflowCompanyId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during compaction for company: {CompanyId}", sleekflowCompanyId);
            result.Success = false;
            return result;
        }
    }

    private async Task<ProcessingResult> ProcessBatchAsync(string sleekflowCompanyId, List<BlobFileInfo> batch)
    {
        var result = new ProcessingResult();

        try
        {
            _logger.LogInformation("Processing batch of {BatchSize} files for company: {CompanyId}", batch.Count, sleekflowCompanyId);

            // Mark files as started
            foreach (var file in batch)
            {
                await _fileTrackingService.MarkFileAsStartedAsync(sleekflowCompanyId, file);
            }

            // Perform the actual migration using DuckDB
            var recordsCount = await MigrateBatchToDuckDbAsync(sleekflowCompanyId, batch);

            // Mark files as completed
            foreach (var file in batch)
            {
                await _fileTrackingService.MarkFileAsCompletedAsync(sleekflowCompanyId, file, recordsCount / batch.Count);
            }

            result.TotalFilesProcessed = batch.Count;
            result.TotalRecordsMigrated = recordsCount;
            result.Success = true;

            _logger.LogInformation("Successfully processed batch for company: {CompanyId}. Files: {Files}, Records: {Records}",
                sleekflowCompanyId, batch.Count, recordsCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing batch for company: {CompanyId}", sleekflowCompanyId);

            // Mark files as failed
            foreach (var file in batch)
            {
                try
                {
                    await _fileTrackingService.MarkFileAsFailedAsync(sleekflowCompanyId, file, ex.Message);
                }
                catch (Exception trackingEx)
                {
                    _logger.LogError(trackingEx, "Error marking file as failed: {BlobPath}", file.BlobPath);
                }
            }

            result.Success = false;
            return result;
        }
    }

    private async Task<int> MigrateBatchToDuckDbAsync(string sleekflowCompanyId, List<BlobFileInfo> batch)
    {
        try
        {
            await using var connection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await connection.OpenAsync();

            // Configure DuckDB with Azure and PostgreSQL for data migration
            await DuckDbExtensions.ConfigureDuckDbAsync(connection, _compactorConfig, _postgresConfig, _logger);

            // Build the migration query
            var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);
            var migrateQuery = BuildMigrationQuery(batch, eventsTableName);

            _logger.LogDebug("Executing migration query for company: {CompanyId}", sleekflowCompanyId);

            using var command = new DuckDBCommand(migrateQuery, connection);

            // Set command timeout
            command.CommandTimeout = _compactorConfig.ProcessingTimeoutMinutes * 60;

            var recordsAffected = await command.ExecuteNonQueryAsync();

            _logger.LogDebug("Migration query completed for company: {CompanyId}. Records affected: {RecordsAffected}",
                sleekflowCompanyId, recordsAffected);

            return recordsAffected;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing DuckDB migration for company: {CompanyId}", sleekflowCompanyId);
            throw;
        }
    }

    private string BuildMigrationQuery(List<BlobFileInfo> batch, string eventsTableName)
    {
        var blobPaths = batch.Select(file =>
            $"abfss://{_compactorConfig.StorageAccountName}.dfs.core.windows.net/{_compactorConfig.EventsContainerName}/{file.BlobPath}")
            .ToList();

        // Build UNION ALL query with individual parquet_scan calls
        var unionQueries = blobPaths.Select(path =>
            $@"SELECT
                id,
                eventType,
                sleekflowCompanyId,
                objectId,
                objectType,
                source,
                properties::JSON AS properties,
                metadata::JSON AS metadata,
                timestamp,
                CAST(year AS INTEGER) AS year,
                CAST(month AS INTEGER) AS month,
                CAST(day AS INTEGER) AS day,
                CAST(hour AS INTEGER) AS hour
            FROM '{path}'");

        var unionQuery = string.Join(" UNION ALL ", unionQueries);

        return $@"
            CREATE VIEW batch_events AS
            {unionQuery};

            INSERT INTO postgres_db.{eventsTableName}
            (id, eventType, sleekflowCompanyId, objectId, objectType, source, properties, metadata, timestamp, year, month, day, hour)
            SELECT
                id,
                eventType,
                sleekflowCompanyId,
                objectId,
                objectType,
                source,
                properties,
                metadata,
                timestamp,
                year,
                month,
                day,
                hour
            FROM batch_events;";
    }

    private List<List<BlobFileInfo>> CreateBatches(List<BlobFileInfo> files, int batchSize)
    {
        var batches = new List<List<BlobFileInfo>>();

        for (int i = 0; i < files.Count; i += batchSize)
        {
            var batch = files.Skip(i).Take(batchSize).ToList();
            batches.Add(batch);
        }

        return batches;
    }
}