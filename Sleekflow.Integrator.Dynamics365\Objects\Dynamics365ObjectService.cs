using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.ObjectIdResolvers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Dynamics365;
using Sleekflow.JsonConfigs;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Dynamics365.Objects;

public interface IDynamics365ObjectService
{
    Task<Dynamics365ObjectService.GetFieldsOutput> GetFieldsAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName);

    Task<Dictionary<string, object?>> GetObjectAsync(
        Dynamics365Authentication dynamics365Authentication,
        string objectId,
        string entityTypeName);

    Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> GetObjectsAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters = null,
        string? nextRecordsUrl = null);

    Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> GetRecentlyUpdatedObjectsAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        DateTimeOffset lastModificationTime,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters = null,
        string? nextRecordsUrl = null);

    Task<long> GetObjectsCountAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups);

    Task DeleteAsync(
        Dynamics365Authentication dynamics365Authentication,
        string objectId,
        string entityTypeName);

    Task CreateAsync(
        Dynamics365Authentication dynamics365Authentication,
        Dictionary<string, object?> dict,
        string entityTypeName);

    Task UpdateAsync(
        Dynamics365Authentication dynamics365Authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string entityTypeName);

    string ResolveObjectId(Dictionary<string, object?> dict, string entityTypeName);

    Task<string> GetObjectDirectRefUrlAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        string objectId);
}

public class Dynamics365ObjectService : ISingletonService, IDynamics365ObjectService
{
    private readonly IMapper _mapper;
    private readonly ICacheService _cacheService;
    private readonly IDynamics365ObjectIdResolver _dynamics365ObjectIdResolver;
    private readonly HttpClient _httpClient;

    public Dynamics365ObjectService(
        IHttpClientFactory httpClientFactory,
        IMapper mapper,
        ICacheService cacheService,
        IDynamics365ObjectIdResolver dynamics365ObjectIdResolver)
    {
        _mapper = mapper;
        _cacheService = cacheService;
        _dynamics365ObjectIdResolver = dynamics365ObjectIdResolver;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    private static string GetObjectCodeName(string entityTypeName)
    {
        switch (entityTypeName)
        {
            case "Contact":
                return "contacts";
            case "User":
                return "systemusers";
            case "SalesOrder":
                return "salesorders";
            case "SalesOrderItem":
                return "salesorderdetails";
            case "Store":
                return "thk_stores";
            case "Language":
                return "new_apmlangs";
            case "Product":
                return "products";
            case "ProductVariant":
                return "thk_productvariants";
            case "Currency":
                return "transactioncurrencies";
            case "Salesperson":
                return "thk_salespersons";
            case "AwarenessSource":
                return "thk_howdoyoukonwuses";
            default:
                break;
        }

        throw new Exception($"Unexpected entityTypeName {entityTypeName}");
    }

    private static string GetEntityName(string entityTypeName)
    {
        switch (entityTypeName)
        {
            case "Contact":
                return "contact";
            case "User":
                return "systemuser";
            case "SalesOrder":
                return "salesorder";
            case "SalesOrderItem":
                return "salesorderdetail";
            case "Store":
                return "thk_store";
            case "Language":
                return "new_apmlang";
            case "Product":
                return "product";
            case "ProductVariant":
                return "thk_productvariant";
            case "Currency":
                return "transactioncurrency";
            case "Salesperson":
                return "thk_salesperson";
            case "AwarenessSource":
                return "thk_howdoyoukonwus";
            default:
                break;
        }

        throw new Exception($"Unexpected entityTypeName {entityTypeName}");
    }

    public class GetFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            this.UpdatableFields = updatableFields;
            this.CreatableFields = creatableFields;
            this.ViewableFields = viewableFields;
        }
    }

    public async Task<GetFieldsOutput> GetFieldsAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName)
    {
        return await _cacheService.CacheAsync(
            $"{nameof(Dynamics365ObjectService)}-{dynamics365Authentication.SleekflowCompanyId}-{entityTypeName}",
            func: async () =>
            {
                var objectCodeName = GetObjectCodeName(entityTypeName);

                var reqMsg = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri =
                        new Uri(
                            $"{dynamics365Authentication.Resource}/api/data/v9.0/EntityDefinitions?$filter=LogicalCollectionName eq '{objectCodeName}'&$expand=Attributes"),
                };
                reqMsg.Headers.Authorization =
                    new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

                var resMsg = await _httpClient.SendAsync(reqMsg);
                var str = await resMsg.Content.ReadAsStringAsync();
                if (resMsg.IsSuccessStatusCode == false)
                {
                    throw new Exception(
                        $"Unable to execute {nameof(GetFieldsAsync)}, resMsg {resMsg}, str {str}, objectCodeName {objectCodeName}");
                }

                var dynamics365GetEntityDefinitionsOutput = str.ToObject<Dynamics365GetEntityDefinitionsOutput>();
                if (dynamics365GetEntityDefinitionsOutput == null)
                {
                    throw new Exception(
                        $"Unable to execute {nameof(GetFieldsAsync)}, resMsg {resMsg}, str {str}, objectCodeName {objectCodeName}");
                }

                var entityDefinition = dynamics365GetEntityDefinitionsOutput.Value.First();

                var updatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var creatableFields = new List<GetTypeFieldsOutputFieldDto>();
                var viewableFields = new List<GetTypeFieldsOutputFieldDto>();
                foreach (var getTypeFieldsOutputFieldDto in entityDefinition
                             .Attributes
                             .Select(field => _mapper.Map<GetTypeFieldsOutputFieldDto>(field)))
                {
                    if (getTypeFieldsOutputFieldDto.Updateable)
                    {
                        updatableFields.Add(getTypeFieldsOutputFieldDto);
                    }
                    else if (getTypeFieldsOutputFieldDto.Createable)
                    {
                        creatableFields.Add(getTypeFieldsOutputFieldDto);
                    }
                    else
                    {
                        viewableFields.Add(getTypeFieldsOutputFieldDto);
                    }
                }

                viewableFields.Add(
                    new GetTypeFieldsOutputFieldDto(
                        true,
                        string.Empty,
                        false,
                        true,
                        false,
                        "_new_apmcountryid_value",
                        1024,
                        "_new_apmcountryid_value",
                        new List<GetTypeFieldsOutputPicklistValue>(),
                        "String",
                        "String",
                        false,
                        false));

                return new GetFieldsOutput(updatableFields, creatableFields, viewableFields);
            },
            CancellationToken.None);
    }

    public async Task<Dictionary<string, object?>> GetObjectAsync(
        Dynamics365Authentication dynamics365Authentication,
        string objectId,
        string entityTypeName)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri =
                new Uri(
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}({objectId})"),
        };
        reqMsg.Headers.Authorization =
            new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

        var resMsg = await _httpClient.SendAsync(reqMsg);
        if (resMsg.IsSuccessStatusCode == false)
        {
            throw new SfIntegratorOperationException(nameof(GetObjectAsync), null, objectCodeName, resMsg);
        }

        string? str = null;
        Dynamics365GetObjectOutput? obj;
        try
        {
            str = await resMsg.Content.ReadAsStringAsync();

            obj = JsonConvert.DeserializeObject<Dynamics365GetObjectOutput>(
                str,
                JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception e)
        {
            throw new SfIntegratorOperationException(e, nameof(GetObjectAsync), str, objectCodeName, resMsg);
        }

        if (obj == null)
        {
            throw new SfIntegratorOperationException(nameof(GetObjectAsync), str, objectCodeName, resMsg);
        }

        return obj;
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)> GetObjectsAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        List<SyncConfigFieldFilter>? fieldFilters = null,
        string? nextRecordsUrl = null)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);

        if (nextRecordsUrl != null)
        {
            var dynamics365GetObjectsOutput = await Dynamics365GetObjectsAsync(
                dynamics365Authentication,
                objectCodeName,
                nextRecordsUrl,
                nameof(GetObjectsAsync));

            return (dynamics365GetObjectsOutput.Value, dynamics365GetObjectsOutput.OdataNextLink);
        }
        else
        {
            var getFieldsOutput = await GetFieldsAsync(dynamics365Authentication, entityTypeName);
            var creatableFields = getFieldsOutput.CreatableFields;
            var updatableFields = getFieldsOutput.UpdatableFields;
            var viewableFields = getFieldsOutput.ViewableFields;

            var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
            var fieldNames = fields.Select(f => f.Name).ToHashSet();

            EnsureCorrectFieldNames(filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName), fieldNames);

            var where =
                filterGroups.Any()
                    ? "&$filter="
                      + string.Join(
                          "+and+",
                          filterGroups.Select(
                              fg =>
                                  "(" + string.Join("+or+", fg.Filters.Select(f => f.FieldName + "+eq+" + f.Value)) +
                                  ")"))
                    : string.Empty;

            var expand = objectCodeName switch
            {
                "salesorderdetails" => "&$expand=thk_variant_code,transactioncurrencyid",
                "contacts" => "&$expand=new_APMCountryId($select=new_code)",
                _ => string.Empty
            };

            string url;
            if (fieldFilters == null)
            {
                url =
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}?$orderby=createdon+asc{where}{expand}";
            }
            else
            {
                EnsureCorrectFieldNames(fieldFilters.Select(f => f.Name), fieldNames);

                var selectedFieldNames = fields
                    .Select(f => f.Name)
                    .Where(f => fieldFilters.Any(ff => ff.Name == f))
                    .ToList();
                url =
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}?$orderby=createdon+asc&$select={string.Join(",", selectedFieldNames)}{where}{expand}";
            }

            var dynamics365GetObjectsOutput = await Dynamics365GetObjectsAsync(
                dynamics365Authentication,
                objectCodeName,
                url,
                nameof(GetObjectsAsync));

            return (dynamics365GetObjectsOutput.Value, dynamics365GetObjectsOutput.OdataNextLink);
        }
    }

    public async Task<(List<Dictionary<string, object?>> Objects, string? NextRecordsUrl)>
        GetRecentlyUpdatedObjectsAsync(
            Dynamics365Authentication dynamics365Authentication,
            string entityTypeName,
            DateTimeOffset lastModificationTime,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters = null,
            string? nextRecordsUrl = null)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);

        if (nextRecordsUrl != null)
        {
            var dynamics365GetObjectsOutput = await Dynamics365GetObjectsAsync(
                dynamics365Authentication,
                objectCodeName,
                nextRecordsUrl,
                nameof(GetRecentlyUpdatedObjectsAsync));

            return (dynamics365GetObjectsOutput.Value, dynamics365GetObjectsOutput.OdataNextLink);
        }
        else
        {
            var getFieldsOutput = await GetFieldsAsync(dynamics365Authentication, entityTypeName);
            var creatableFields = getFieldsOutput.CreatableFields;
            var updatableFields = getFieldsOutput.UpdatableFields;
            var viewableFields = getFieldsOutput.ViewableFields;

            var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
            var fieldNames = fields.Select(f => f.Name).ToHashSet();

            EnsureCorrectFieldNames(filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName), fieldNames);

            var lastModificationTimeStr =
                JsonConvert
                    .SerializeObject(lastModificationTime, JsonConfig.DefaultJsonSerializerSettings)
                    .Replace("\"", string.Empty);
            var where =
                filterGroups.Any()
                    ? "&$filter="
                      + string.Join(
                          "+and+",
                          filterGroups.Select(
                              fg =>
                                  "(" + string.Join("+or+", fg.Filters.Select(f => f.FieldName + "+eq+" + f.Value)) +
                                  ")"))
                      + "+and+modifiedon+ge+" + lastModificationTimeStr
                    : "&$filter=" + "modifiedon+ge+" + lastModificationTimeStr;

            var expand = objectCodeName switch
            {
                "salesorderdetails" => "&$expand=thk_variant_code,transactioncurrencyid",
                "contacts" => "&$expand=new_APMCountryId($select=new_code)",
                _ => string.Empty
            };

            string url;
            if (fieldFilters == null)
            {
                url =
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}?$orderby=createdon+asc{where}{expand}";
            }
            else
            {
                EnsureCorrectFieldNames(fieldFilters.Select(f => f.Name), fieldNames);

                var selectedFieldNames = fields
                    .Select(f => f.Name)
                    .Where(f => fieldFilters.Any(ff => ff.Name == f))
                    .ToList();
                url =
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}?$filter={where}&$orderby=createdon+asc&$select={string.Join(",", selectedFieldNames)}{expand}";
            }

            var dynamics365GetObjectsOutput = await Dynamics365GetObjectsAsync(
                dynamics365Authentication,
                objectCodeName,
                url,
                nameof(GetRecentlyUpdatedObjectsAsync));

            return (dynamics365GetObjectsOutput.Value, dynamics365GetObjectsOutput.OdataNextLink);
        }
    }

    private async Task<Dynamics365GetObjectsOutput> Dynamics365GetObjectsAsync(
        Dynamics365Authentication dynamics365Authentication,
        string objectCodeName,
        string url,
        string operationName)
    {
        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(url),
        };
        reqMsg.Headers.Authorization = new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

        if (objectCodeName == "salesorders")
        {
            reqMsg.Headers.Add("Prefer", "odata.maxpagesize=300");
        }
        else if (objectCodeName == "salesorderdetails")
        {
            reqMsg.Headers.Add("Prefer", "odata.maxpagesize=400");
        }
        else
        {
            reqMsg.Headers.Add("Prefer", "odata.maxpagesize=200");
        }

        var resMsg = await _httpClient.SendAsync(reqMsg);

        string? str = null;
        Dynamics365GetObjectsOutput? obj;
        try
        {
            str = await resMsg.Content.ReadAsStringAsync();

            obj = JsonConvert.DeserializeObject<Dynamics365GetObjectsOutput>(
                str,
                JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception e)
        {
            throw new SfIntegratorOperationException(e, operationName, str, objectCodeName, resMsg);
        }

        if (resMsg.IsSuccessStatusCode == false)
        {
            throw new SfIntegratorOperationException(operationName, str, objectCodeName, resMsg);
        }

        if (obj == null)
        {
            throw new SfIntegratorOperationException(operationName, str, objectCodeName, resMsg);
        }

        return obj;
    }

    public async Task<long> GetObjectsCountAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);
        var entityName = GetEntityName(entityTypeName);

        var getFieldsOutput = await GetFieldsAsync(dynamics365Authentication, entityTypeName);

        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        var fields = updatableFields.Concat(creatableFields).Concat(viewableFields).ToList();
        var fieldNames = fields.Select(f => f.Name).ToHashSet();

        EnsureCorrectFieldNames(filterGroups.SelectMany(fg => fg.Filters).Select(f => f.FieldName), fieldNames);

        var where =
            filterGroups.Any()
                ? "&$filter="
                  + string.Join(
                      "+and+",
                      filterGroups.Select(
                          fg =>
                              "(" + string.Join("+or+", fg.Filters.Select(f => f.FieldName + "+eq+" + f.Value)) + ")"))
                : string.Empty;

        var reqMsgForFilteredCount = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri =
                new Uri(
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}?$count=true&$top=1{where}"),
        };
        reqMsgForFilteredCount.Headers.Authorization =
            new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);
        reqMsgForFilteredCount.Headers.Add("Prefer", "odata.maxpagesize=1");

        var resMsgForFilteredCount = await _httpClient.SendAsync(reqMsgForFilteredCount);
        var strForFilteredCount = await resMsgForFilteredCount.Content.ReadAsStringAsync();
        if (resMsgForFilteredCount.IsSuccessStatusCode == false)
        {
            throw new Exception(
                $"Unable to execute {nameof(GetObjectsCountAsync)}, resMsg {resMsgForFilteredCount}, str {strForFilteredCount}, objectCodeName {objectCodeName}");
        }

        var dynamics365GetObjectsOdataCountOutput =
            strForFilteredCount.ToObject<Dynamics365GetObjectsODataCountOutput>();
        if (dynamics365GetObjectsOdataCountOutput == null)
        {
            throw new Exception(
                $"Unable to execute {nameof(GetObjectsCountAsync)}, resMsg {resMsgForFilteredCount}, str {strForFilteredCount}, objectCodeName {objectCodeName}");
        }

        if (dynamics365GetObjectsOdataCountOutput.OdataCount != 5000)
        {
            return dynamics365GetObjectsOdataCountOutput.OdataCount;
        }

        // This is a special handling in Dynamics365 because Dynamics365 doesn't support $count=true for more than 5000 records
        return await GetObjectsTotalCountAsync(dynamics365Authentication, entityName, objectCodeName);
    }

    private async Task<long> GetObjectsTotalCountAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityName,
        string objectCodeName)
    {
        var reqMsgForTotalCount = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri =
                new Uri(
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/RetrieveTotalRecordCount(EntityNames=['{entityName}'])")
        };
        reqMsgForTotalCount.Headers.Authorization =
            new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

        var resMsgForTotalCount = await _httpClient.SendAsync(reqMsgForTotalCount);
        var strForTotalCount = await resMsgForTotalCount.Content.ReadAsStringAsync();
        if (resMsgForTotalCount.IsSuccessStatusCode == false)
        {
            throw new Exception(
                $"Unable to execute {nameof(GetObjectsCountAsync)}, resMsg {resMsgForTotalCount}, str {strForTotalCount}, objectCodeName {objectCodeName}");
        }

        var dynamics365GetObjectsEntityRecordCountOutput =
            strForTotalCount.ToObject<Dynamics365GetObjectsEntityRecordCountOutput>();
        if (dynamics365GetObjectsEntityRecordCountOutput == null)
        {
            throw new Exception(
                $"Unable to execute {nameof(GetObjectsCountAsync)}, resMsg {resMsgForTotalCount}, str {strForTotalCount}, objectCodeName {objectCodeName}");
        }

        return dynamics365GetObjectsEntityRecordCountOutput.RecordCountCollection.Values.FirstOrDefault();
    }

    public async Task DeleteAsync(
        Dynamics365Authentication dynamics365Authentication,
        string objectId,
        string entityTypeName)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Delete,
            RequestUri =
                new Uri(
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}({objectId})"),
        };
        reqMsg.Headers.Authorization =
            new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

        var resMsg = await _httpClient.SendAsync(reqMsg);
        var str = await resMsg.Content.ReadAsStringAsync();
        if (!resMsg.IsSuccessStatusCode)
        {
            throw new Exception(
                $"The httpResponseMsg=[{resMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public async Task CreateAsync(
        Dynamics365Authentication dynamics365Authentication,
        Dictionary<string, object?> dict,
        string entityTypeName)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);
        var objStr = JsonConvert.SerializeObject(
            dict,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
            });

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                objStr,
                Encoding.UTF8,
                "application/json"),
            RequestUri =
                new Uri(
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}"),
        };
        reqMsg.Headers.Authorization =
            new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

        var resMsg = await _httpClient.SendAsync(reqMsg);
        var str = await resMsg.Content.ReadAsStringAsync();
        if (!resMsg.IsSuccessStatusCode)
        {
            throw new Exception(
                $"The httpResponseMsg=[{resMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public async Task UpdateAsync(
        Dynamics365Authentication dynamics365Authentication,
        Dictionary<string, object?> dict,
        string objectId,
        string entityTypeName)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);
        var objStr = JsonConvert.SerializeObject(
            dict,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
            });

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Patch,
            Content = new StringContent(
                objStr,
                Encoding.UTF8,
                "application/json"),
            RequestUri =
                new Uri(
                    $"{dynamics365Authentication.Resource}/api/data/v9.0/{objectCodeName}({objectId})"),
        };
        reqMsg.Headers.Authorization =
            new AuthenticationHeaderValue("Bearer", dynamics365Authentication.AccessToken);

        var resMsg = await _httpClient.SendAsync(reqMsg);
        var str = await resMsg.Content.ReadAsStringAsync();
        if (!resMsg.IsSuccessStatusCode)
        {
            throw new Exception(
                $"The httpResponseMsg=[{resMsg}], str=[{str}], objectCodeName=[{GetObjectCodeName(entityTypeName)}] is not working");
        }
    }

    public string ResolveObjectId(Dictionary<string, object?> dict, string entityTypeName)
    {
        var objectCodeName = GetObjectCodeName(entityTypeName);

        return _dynamics365ObjectIdResolver.ResolveObjectId(dict, objectCodeName);
    }

    public async Task<string> GetObjectDirectRefUrlAsync(
        Dynamics365Authentication dynamics365Authentication,
        string entityTypeName,
        string objectId)
    {
        throw new NotImplementedException();
    }

    private static void EnsureCorrectFieldNames(IEnumerable<string> fieldNames, IReadOnlySet<string> allFieldNames)
    {
        var invalidFieldNamesInFilters = fieldNames
            .Where(fn => allFieldNames.Contains(fn) == false)
            .ToList();
        if (invalidFieldNamesInFilters.Any())
        {
            throw new SfUserFriendlyException(
                $"Some filters are invalid. invalidFieldNames {invalidFieldNamesInFilters}");
        }
    }
}