using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Orders.ShopifyOrders;

public class ShopifyOrderDto : OrderDto
{
    [JsonProperty("shopify_order_external_integration_info")]
    public ShopifyOrderExternalIntegrationInfo ShopifyOrderExternalIntegrationInfo { get; set; }

    [JsonConstructor]
    public ShopifyOrderDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        string sleekflowUserProfileId,
        List<OrderLineItem> lineItems,
        string currencyIsoCode,
        decimal totalPrice,
        decimal subtotalPrice,
        Discount? orderDiscount,
        string orderStatus,
        string paymentStatus,
        DateTimeOffset? paymentLinkSentAt,
        DateTimeOffset? completedAt,
        Dictionary<string, object?> metadata,
        ShopifyOrderExternalIntegrationInfo shopifyOrderExternalIntegrationInfo)
        : base(
            id,
            sleekflowCompanyId,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt,
            storeId,
            sleekflowUserProfileId,
            lineItems,
            currencyIsoCode,
            totalPrice,
            subtotalPrice,
            orderDiscount,
            orderStatus,
            paymentStatus,
            paymentLinkSentAt,
            completedAt,
            metadata)
    {
        ShopifyOrderExternalIntegrationInfo = shopifyOrderExternalIntegrationInfo;
    }

    public ShopifyOrderDto(Order order)
        : this(
            order.Id,
            order.SleekflowCompanyId,
            order.CreatedBy,
            order.UpdatedBy,
            order.CreatedAt,
            order.UpdatedAt,
            order.StoreId,
            order.SleekflowUserProfileId,
            order.LineItems,
            order.CurrencyIsoCode,
            order.TotalPrice,
            order.SubtotalPrice,
            order.OrderDiscount,
            order.OrderStatus,
            order.PaymentStatus,
            order.PaymentLinkSentAt,
            order.CompletedAt,
            order.Metadata,
            (order.OrderExternalIntegrationInfo as ShopifyOrderExternalIntegrationInfo)!)
    {
    }
}