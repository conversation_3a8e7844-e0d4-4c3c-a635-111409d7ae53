﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class DuplicateProducts
    : ITrigger<
        DuplicateProducts.DuplicateProductsInput,
        DuplicateProducts.DuplicateProductsOutput>
{
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;
    private readonly IIdService _idService;

    public DuplicateProducts(
        IProductService productService,
        IProductVariantService productVariantService,
        IIdService idService)
    {
        _productService = productService;
        _productVariantService = productVariantService;
        _idService = idService;
    }

    public class DuplicateProductsInput : IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [MinLength(1)]
        [MaxLength(32)]
        [JsonProperty("product_ids")]
        public List<string> ProductIds { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DuplicateProductsInput(
            string sleekflowCompanyId,
            string storeId,
            List<string> productIds,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductIds = productIds;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DuplicateProductsOutput
    {
        [JsonProperty("products")]
        public List<ProductDto> Products { get; set; }

        [JsonConstructor]
        public DuplicateProductsOutput(List<ProductDto> products)
        {
            Products = products;
        }
    }

    public async Task<DuplicateProductsOutput> F(DuplicateProductsInput duplicateProductsInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            duplicateProductsInput.SleekflowStaffId,
            duplicateProductsInput.SleekflowStaffTeamIds);

        var products = await _productService.GetProductsAsync(
            duplicateProductsInput.ProductIds,
            duplicateProductsInput.SleekflowCompanyId,
            duplicateProductsInput.StoreId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            duplicateProductsInput.SleekflowCompanyId,
            duplicateProductsInput.StoreId,
            products.Select(p => p.Id).ToList());

        var productIdToProductVariantsDict = productVariants
            .GroupBy(pv => pv.ProductId)
            .ToDictionary(e => e.Key, e => e.ToList());

        // TODO Transactional
        var duplicatedProductAndProductVariantsTuples = new List<(Product, List<ProductVariant>)>();
        foreach (var product in products)
        {
            var productVariantsForProduct = productIdToProductVariantsDict[product.Id];

            if (product.PlatformData.Type != PlatformDataTypes.CustomCatalog)
            {
                continue;
            }

            var duplicatedProduct = await _productService.CreateAndGetProductAsync(
                new Product(
                    _idService.GetId(SysTypeNames.Product),
                    product.SleekflowCompanyId,
                    product.StoreId,
                    product.CategoryIds,
                    product.Sku != null
                        ? "DUPLICATE-" + product.Sku
                        : product.Sku,
                    product.Url,
                    product.Names
                        .Select(n => new Multilingual(n.LanguageIsoCode, $"DUPLICATE - {n.Value}"))
                        .ToList(),
                    product.Descriptions,
                    product.Images,
                    product.ProductVariantNames
                        .Select(pvn => new Multilingual(pvn.LanguageIsoCode, $"DUPLICATE - {pvn.Value}"))
                        .ToList(),
                    product.ProductVariantDescriptions,
                    product.ProductVariantAttributes,
                    product.ProductVariantPrices,
                    false,
                    new List<string>
                    {
                        "Active"
                    },
                    PlatformData.CustomCatalog(),
                    product.Metadata,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    createdBy: sleekflowStaff,
                    updatedBy: sleekflowStaff),
                sleekflowStaff);

            var duplicatedProductVariants = new List<ProductVariant>();

            // TODO Transactional
            foreach (var productVariant in productVariantsForProduct)
            {
                if (productVariant.PlatformData.Type != PlatformDataTypes.CustomCatalog)
                {
                    continue;
                }

                var duplicatedProductVariant = await _productVariantService.CreateAndGetProductVariantAsync(
                    new ProductVariant(
                        _idService.GetId(SysTypeNames.ProductVariant),
                        product.SleekflowCompanyId,
                        product.StoreId,
                        duplicatedProduct.Id,
                        productVariant.Sku == null
                            ? null
                            : "DUPLICATE-" + productVariant.Sku,
                        null,
                        productVariant.Prices,
                        productVariant.Position,
                        productVariant.IsDefaultVariantProduct,
                        productVariant.Attributes,
                        productVariant.Names
                            .Select(n => new Multilingual(n.LanguageIsoCode, $"DUPLICATE - {n.Value}"))
                            .ToList(),
                        productVariant.Descriptions,
                        productVariant.Images,
                        PlatformData.CustomCatalog(),
                        new List<string>
                        {
                            "Active"
                        },
                        new Dictionary<string, object?>(),
                        sleekflowStaff,
                        sleekflowStaff,
                        DateTimeOffset.UtcNow,
                        DateTimeOffset.UtcNow),
                    sleekflowStaff);

                duplicatedProductVariants.Add(duplicatedProductVariant);
            }

            duplicatedProductAndProductVariantsTuples.Add((duplicatedProduct, duplicatedProductVariants));
        }

        return new DuplicateProductsOutput(
            duplicatedProductAndProductVariantsTuples
                .Select(t => new ProductDto(t.Item1, t.Item2))
                .ToList());
    }
}