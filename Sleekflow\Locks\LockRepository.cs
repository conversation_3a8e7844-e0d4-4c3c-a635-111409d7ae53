﻿using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Locks;

public interface ILockRepository : IRepository<Lock>
{
}

public class LockRepository : BaseRepository<Lock>, ILockRepository, ISingletonService
{
    public LockRepository(
        ILogger<BaseRepository<Lock>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}