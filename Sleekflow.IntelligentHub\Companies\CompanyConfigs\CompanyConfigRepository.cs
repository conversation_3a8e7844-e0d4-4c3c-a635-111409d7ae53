using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Companies.CompanyConfigs;

public interface ICompanyConfigRepository : IRepository<CompanyConfig>
{
    Task<CompanyConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string eTag,
        AuditEntity.SleekflowStaff sleekflowStaff,
        string? name = null,
        string? backgroundInformation = null,
        string? preferredLanguage = null,
        string? collaborationMode = null);
}

public class CompanyConfigRepository : BaseRepository<CompanyConfig>, ICompanyConfigRepository, IScopedService
{
    public CompanyConfigRepository(ILogger<CompanyConfigRepository> logger, IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<CompanyConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string eTag,
        AuditEntity.SleekflowStaff sleekflowStaff,
        string? name = null,
        string? backgroundInformation = null,
        string? preferredLanguage = null,
        string? collaborationMode = null)
    {
        var operations = new List<PatchOperation>
        {
            PatchOperation.Set($"/{AuditEntity.PropertyNameUpdatedBy}", sleekflowStaff),
            PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
        };

        if (name != null)
        {
            operations.Add(PatchOperation.Set($"/{CompanyConfig.PropertyNameName}", name));
        }

        if (backgroundInformation != null)
        {
            operations.Add(
                PatchOperation.Set($"/{CompanyConfig.PropertyNameBackgroundInformation}", backgroundInformation));
        }

        if (preferredLanguage != null)
        {
            operations.Add(PatchOperation.Set($"/{CompanyConfig.PropertyNamePreferredLanguage}", preferredLanguage));
        }

        return await PatchAndGetAsync(id, sleekflowCompanyId, operations, eTag: eTag);
    }
}