using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using Newtonsoft.Json;
using PDFtoImage;
using Polly;
using SkiaSharp;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IPdfKnowledgeSource : IKnowledgeSource
{
}

public class PdfKnowledgeSource : IPdfKnowledgeSource, IScopedService
{
    private readonly ILogger<PdfKnowledgeSource> _logger;
    private readonly Kernel _kernel;
    private readonly IDocumentStatisticsCalculatorFactory _documentStatisticsCalculatorFactory;

    public PdfKnowledgeSource(
        ILogger<PdfKnowledgeSource> logger,
        Kernel kernel,
        IDocumentStatisticsCalculatorFactory documentStatisticsCalculatorFactory)
    {
        _logger = logger;
        _kernel = kernel;
        _documentStatisticsCalculatorFactory = documentStatisticsCalculatorFactory;
    }

#pragma warning disable SKEXP0070
#pragma warning disable SKEXP0001
    public async Task<IKnowledgeSource.IngestionResult> Ingest(
        Stream blobStream,
        object? fileIngestionProgress)
    {
        const int batchCount = 10;

        // use the statistics calculator to get page count
        var statisticsCalculator =
            _documentStatisticsCalculatorFactory.CreateDocumentStatisticsCalculator(DocumentTypes.Pdf);
        var pageCount = statisticsCalculator.CalculateDocumentStatistics(blobStream).TotalPages;

        PdfFileIngestionProgress pdfFileIngestionProgress;

        if (fileIngestionProgress == null)
        {
            // Initialize a new progress object if none is provided
            pdfFileIngestionProgress = new PdfFileIngestionProgress([], 0, pageCount);
        }
        else if (fileIngestionProgress is PdfFileIngestionProgress progress)
        {
            // Direct instance of PdfFileIngestionProgress
            pdfFileIngestionProgress = progress;
        }
        else
        {
            try
            {
                // Try to deserialize as JSON
                var jsonString = JsonConvert.SerializeObject(fileIngestionProgress);
                pdfFileIngestionProgress = JsonConvert.DeserializeObject<PdfFileIngestionProgress>(jsonString)
                                           ?? throw new Exception(
                                               "Failed to deserialize fileIngestionProgress to PdfFileIngestionProgress");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to interpret fileIngestionProgress as PdfFileIngestionProgress");
                throw new Exception("FileIngestionProgress could not be interpreted as PdfFileIngestionProgress", ex);
            }
        }

        var startPageIndex = pdfFileIngestionProgress.ProcessedPages;
        var endPageIndex = Math.Min(startPageIndex + batchCount - 1, pageCount - 1);

        if (startPageIndex > endPageIndex)
        {
            throw new Exception($"FileIngestionProgress start page index is greater than end page index {startPageIndex} - {endPageIndex}");
        }

        // PDF pages may lack context if evaluated one at a time, hence we generate a description for every page so they can be self-containing

        blobStream.Position = 0;
        var pdfPageImageStreams = await ConvertPdfToImagesAsync(blobStream, startPageIndex, endPageIndex);

        var newPageDescriptions = await GeneratePageDescriptions(
            pdfFileIngestionProgress.PageDescriptionDictionary,
            pdfPageImageStreams);

        var options = new ParallelOptions
        {
            MaxDegreeOfParallelism = 30
        };
        var pageMarkdowns = new ConcurrentDictionary<int, string>();

        var retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                3,
                attempt => TimeSpan.FromSeconds(5),
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        "Retry {RetryCount} after {TotalSeconds}s due to {ExceptionMessage}",
                        retryCount,
                        timeSpan.TotalSeconds,
                        exception.Message);
                });

        await Parallel.ForEachAsync(
            newPageDescriptions.ToArray(),
            options,
            async (kv, token) =>
            {
                await retryPolicy.ExecuteAsync(
                    async (ct) =>
                    {
                        var pageNumber = kv.Key;
                        var pageDescription = kv.Value;

                        var pageImageStream = pdfPageImageStreams[pageNumber];
                        var markdown = await ProcessPdfPage(pageImageStream, pageDescription);

                        pageMarkdowns[pageNumber] = markdown;
                    },
                    token);
            });

        // update progress
        foreach (var kv in newPageDescriptions)
        {
            pdfFileIngestionProgress.PageDescriptionDictionary.Add(
                kv.Key,
                kv.Value);
        }

        pdfFileIngestionProgress.ProcessedPages += pageMarkdowns.Count;

        return new IKnowledgeSource.IngestionResult(
            pageMarkdowns.OrderBy(kv => kv.Key).Select(pageMarkdown => pageMarkdown.Value).ToArray(),
            pdfFileIngestionProgress);
    }

    // returns page number to image stream
    private async Task<Dictionary<int, MemoryStream>> ConvertPdfToImagesAsync(
        Stream pdfStream,
        int startPageIndex,
        int endPageIndex)
    {
        var pdfPageImageStreams = new Dictionary<int, MemoryStream>();

        var currentPage = startPageIndex;
        await foreach (
            var skBitmap in Conversion.ToImagesAsync(
                pdfStream,
                startPageIndex..(endPageIndex + 1),
                options: new RenderOptions(Dpi: 300, Width: 2048, WithAspectRatio: true)))
        {
            var encoded = skBitmap.Encode(SKEncodedImageFormat.Jpeg, 100);

            var memoryStream = new MemoryStream();
            encoded.SaveTo(memoryStream);
            memoryStream.Position = 0; // Reset position to beginning for reading
            pdfPageImageStreams.Add(currentPage + 1, memoryStream);
            currentPage++;
        }

        return pdfPageImageStreams;
    }

    private class GeneratePageDescriptionsOutput
    {
        [JsonProperty("page_number")]
        public int PageNumber { get; set; }

        [JsonProperty("page_description")]
        public string PageDescription { get; set; }

        [JsonConstructor]
        public GeneratePageDescriptionsOutput(
            int pageNumber,
            string pageDescription)
        {
            PageNumber = pageNumber;
            PageDescription = pageDescription;
        }
    }

    private async Task<Dictionary<int, string>> GeneratePageDescriptions(
        Dictionary<int, string> pageDescriptionDictionary,
        Dictionary<int, MemoryStream> pdfPageImageStreams)
    {
        var chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);

        var retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                3,
                attempt => TimeSpan.FromSeconds(5),
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        "Retry GeneratePageDescriptions {RetryCount} after {TotalSeconds}s due to {ExceptionMessage}",
                        retryCount,
                        timeSpan.TotalSeconds,
                        exception.Message);
                });

        var newPageDescriptions = new Dictionary<int, string>();

        await retryPolicy.ExecuteAsync(
            async () =>
            {
                var chatHistory = new ChatHistory();

                // System message to explain the task
                chatHistory.AddSystemMessage(
                    """
                    The user wants to split a PDF document such that each page becomes a standalone PDF document.
                    You are tasked to generate a description for selected pages in the PDF such that each page contains enough context to be a self-contained document.

                    Instructions:
                    - Generate a detailed description for every page to give full context for understanding that page on its own.
                    - You should understand and respect the entire document in generating a fitting description for each page.
                    - The page descriptions for the previous pages are supplied to you, use them to ensure context completeness.
                    - If a table is split across multiple pages, make sure all headers and necessary details are included to make each page self-contained.

                    Input format:
                    A PDF where every page is supplied as images.

                    Output format:
                    [
                        {
                            "page_number": int,
                            "page_description": string
                        }
                    ]

                    Output language:
                    - The output page_description should use the original language of the PDF document.

                    The output array should be sorted by ascending page number.
                    """);

                // First, add context from all previously processed pages
                if (pageDescriptionDictionary.Count > 0)
                {
                    chatHistory.AddUserMessage(
                        "Here are the descriptions for previous pages to provide context:");

                    var previousPageDescriptions = pageDescriptionDictionary.Select(
                        kv => new GeneratePageDescriptionsOutput(kv.Key, kv.Value));

                    chatHistory.AddUserMessage(
                        JsonConvert.SerializeObject(
                            previousPageDescriptions.ToArray(),
                            Formatting.Indented));
                }

                // Include info about the total document
                chatHistory.AddUserMessage(
                    $"The document has {pageDescriptionDictionary.Count + pdfPageImageStreams.Count} total pages.");

                // Add the current batch of images
                foreach (var (pageNumber, imageStream) in pdfPageImageStreams)
                {
                    chatHistory.AddUserMessage(
                    [
                        new TextContent($"Here is page {pageNumber}"),
                        new ImageContent(pdfPageImageStreams[pageNumber].ToArray(), "image/jpeg"),
                    ]);
                }

                chatHistory.AddUserMessage(
                [
                    new TextContent(
                        $"Please generate descriptions for pages {pageDescriptionDictionary.Count + 1} to {pageDescriptionDictionary.Count + pdfPageImageStreams.Count}"),
                ]);

                var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
                    chatHistory,
                    new GeminiPromptExecutionSettings()
                    {
                        Temperature = 1.0f, ResponseMimeType = "application/json",
                    },
                    _kernel);

                // tokenUsageTracker.RecordTokenUsage(completeMarkdownOutput);

                if (completeMarkdownOutput.Content == null)
                {
                    throw new Exception("Failed chat completion in pdf page verification.");
                }

                var generatePageDescriptionsOutputs =
                    JsonConvert.DeserializeObject<GeneratePageDescriptionsOutput[]>(
                        Regex.Replace(
                            completeMarkdownOutput.Content,
                            "```(json)?",
                            ""));

                if (generatePageDescriptionsOutputs == null)
                {
                    throw new Exception("GeneratePageDescriptions failed.");
                }

                if (generatePageDescriptionsOutputs.Length != pdfPageImageStreams.Count)
                {
                    throw new Exception(
                        $"GetPageContext expected {pdfPageImageStreams.Count} pages but got {generatePageDescriptionsOutputs.Length}.");
                }

                // Add batch results to the full list
                foreach (var generatePageDescriptionsOutput in generatePageDescriptionsOutputs)
                {
                    newPageDescriptions.Add(
                        generatePageDescriptionsOutput.PageNumber,
                        generatePageDescriptionsOutput.PageDescription);
                }
            });

        return newPageDescriptions;
    }

    private async Task<string> ProcessPdfPage(
        MemoryStream imageStream,
        string pageDescription)
    {
        var chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);

        var retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                6,
                attempt => TimeSpan.FromSeconds(5),
                onRetry: (exception, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        "Retry ProcessPdfPage {RetryCount} after {TotalSeconds}s due to {ExceptionMessage}",
                        retryCount,
                        timeSpan.TotalSeconds,
                        exception.Message);
                });

        return await retryPolicy.ExecuteAsync(
            async () =>
            {
                var chatHistory = new ChatHistory();
                chatHistory.AddSystemMessage(
                    """
                    You are tasked to convert a document image into a markdown document.

                    You are given an image of a document and also a description of the document. Use the description to further your understanding of the document.

                    1. Text extraction instructions:
                       - Extract all textual information from the provided page.
                       - Maintain the original document's logical flow and hierarchy.

                    2. Table extraction instructions:
                      - Convert each table into a markdown table.
                      - If the table contains images, interpret them and convert into text so they can fit nicely in the markdown table.
                      - Ensure all table data is fully represented in the text without loss of information.
                      - If there are multiple tables, each table should be converted to separate markdown tables.

                    3. Image extraction instructions:
                      - Some images may be illustrative rather than essential to the main content. Handle them as follows:
                        -- If an image contains textual information integral to the document's purpose, include it in the output and specify its relevance in the text.
                        -- If an image is purely illustrative and adds no unique textual information, just briefly describe its purpose (e.g., "This section includes an illustrative screenshot of a diagram.").
                      - Ensure they can be easily identified and distinguished from the main text output.

                    **Special Considerations**:
                       - Preserve exact spelling and formatting of proper nouns, technical terms, and specialized vocabulary.
                       - Retain any specific formatting (e.g., bold or italic text) that carries semantic meaning.
                       - Include footnotes, references, or supplementary information in appropriate contexts, linking them to the relevant text.
                       - Ensure the transformed content reads naturally and logically, as if originally written in text form.
                       - Ensure that the response is provided in the same language as the input. Additionally, if the input language is not English, include an English translation alongside the original terms where feasible. e.g., "Spanish term (English term)." This is not required for proper nouns or technical terms. Do not include the pinyin for Chinese characters.
                       - Preserve all numerical data, measurements, and specific values exactly as they appear.
                       - Preserve all special terms, entity names, and technical nomenclature exactly as they appear in the original document.

                    The final output should be a good conversion of the page's content in markdown.

                    Output format:
                    ```markdown
                        {The markdown output}
                    ```

                    Output language:
                    - The markdown should use the same language as the document.
                    """);

                chatHistory.AddUserMessage(
                [
                    new ImageContent(imageStream.ToArray(), "image/jpeg"),
                    new TextContent($"Description: {pageDescription}")
                ]);

                var completeMarkdownOutput = await chatCompletionService.GetChatMessageContentAsync(
                    chatHistory,
                    new GeminiPromptExecutionSettings()
                    {
                        Temperature = 1.0f
                    },
                    _kernel);

                // tokenUsageTracker.RecordTokenUsage(completeMarkdownOutput);

                if (completeMarkdownOutput.Content == null)
                {
                    throw new Exception("Failed chat completion in pdf page.");
                }

                _logger.LogInformation("ProcessPdfPage output {Output}", completeMarkdownOutput.Content);

                var unwrappedOutput = Regex.Replace(
                    completeMarkdownOutput.Content,
                    "```(markdown)?",
                    "");

                if (unwrappedOutput == null)
                {
                    throw new Exception("ProcessPdfPage failed.");
                }

                await VerifyPdfPageCompletion(
                    imageStream,
                    pageDescription,
                    unwrappedOutput);

                return unwrappedOutput;
            });
    }

    private class VerifyPdfPageCompletionResults
    {
        [JsonProperty("score")]
        public int Score { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }

        [JsonConstructor]
        public VerifyPdfPageCompletionResults(int score, string reason)
        {
            Score = score;
            Reason = reason;
        }
    }

    private async Task VerifyPdfPageCompletion(
        MemoryStream imageStream,
        string pageDescription,
        string pageMarkdown)
    {
        var chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);

        if (pageMarkdown.Contains(new string(' ', 1000)))
        {
            throw new Exception(
                $"The markdown contains abnormally large amount of spaces. It is likely incorrectly generated. {pageMarkdown}");
        }

        if (pageMarkdown.Contains(new string('-', 1000)))
        {
            throw new Exception(
                $"The markdown contains abnormally large amount of dashes. It is likely incorrectly generated. {pageMarkdown}");
        }

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are given a document image and a markdown document. Your task is to verify the markdown document is an accurate representation for the original document.

            Input:
            - The document image
            - A description for the document image
            - A markdown that you need to verify

            Criteria - Textual content completeness:
            - The markdown should contain important and useful textual information in the original document
            - Unimportant information can be omitted
            - It is not necessary to use the exact wordings, paraphrase is acceptable

            Criteria - Table content completeness:
            - The markdown should contain all the table information in the original document with no information loss

            Criteria - Image content completeness:
            - You do not need to verify the presence nor the accuracy for images.
            - It is possible for a pdf page to be image only, in that case just pass the verification.

            Criteria - Structural correctness:
            - The markdown must be valid
            - The markdown should be self-containing and meaningful
            - The markdown should have a coherent document structure

            Criteria - Language:
            - The markdown should use the same language as the document.

            Output format:
            {
                score: int
                reason: string
            }

            Output explanation:
            score: A score of 0-100 for grading whether the markdown is a fairly accurate representation of the original document
            reason: List all reasons in detail for the verdict. If something is missing, you should say exactly what was missing and including it should pass the verification. Check and make sure your reasons are actually valid.
            """);

        chatHistory.AddUserMessage(
        [
            new ImageContent(imageStream.ToArray(), "image/jpeg"),
            new TextContent($"Description: {pageDescription}"),
            new TextContent($"Markdown:\n {pageMarkdown}")
        ]);

        var completeOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            new GeminiPromptExecutionSettings()
            {
                Temperature = 0.1f, ResponseMimeType = "application/json",
            },
            _kernel);

        // tokenUsageTracker.RecordTokenUsage(completeOutput);

        if (completeOutput.Content == null)
        {
            throw new Exception("Chat completion failed.");
        }

        var unwrappedOutput = Regex.Replace(completeOutput.Content, "```(json)?", "");
        var results = JsonConvert.DeserializeObject<VerifyPdfPageCompletionResults>(unwrappedOutput);
        if (results == null)
        {
            throw new Exception("Chat completion failed.");
        }

        if (results.Score < 70)
        {
            throw new Exception($"PDF verification failed. Score: {results.Score} Reason: {results.Reason}");
        }
    }
#pragma warning restore SKEXP0070
#pragma warning restore SKEXP0001
}