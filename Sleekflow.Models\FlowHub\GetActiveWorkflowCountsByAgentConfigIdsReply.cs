using Newtonsoft.Json;

namespace Sleekflow.Models.FlowHub;

public class GetActiveWorkflowCountsByAgentConfigIdsReply
{
    [JsonProperty("workflow_counts")]
    public Dictionary<string, int> WorkflowCounts { get; set; }

    [JsonConstructor]
    public GetActiveWorkflowCountsByAgentConfigIdsReply(
        Dictionary<string, int> workflowCounts)
    {
        WorkflowCounts = workflowCounts;
    }
} 