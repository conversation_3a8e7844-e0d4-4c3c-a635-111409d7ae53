using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

/// <summary>
/// Configuration for a chat history enricher.
/// </summary>
public class EnricherConfig
{
    /// <summary>
    /// The type of enricher (e.g., "Hubspot", "Salesforce").
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Whether this enricher is enabled.
    /// </summary>
    [JsonProperty("is_enabled")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Parameters specific to this enricher type (e.g., API keys, endpoint URLs).
    /// </summary>
    [JsonProperty("parameters")]
    public Dictionary<string, string> Parameters { get; set; } = new();
}