﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class SearchObjectCondition
{
    [JsonProperty("field_name")]
    [Required]
    public string FieldName { get; set; }

    [JsonProperty("operator")]
    [Required]
    public string Operator { get; set; }

    [JsonProperty("value")]
    public object? Value { get; set; }

    [JsonConstructor]
    public SearchObjectCondition(string fieldName, string @operator, object? value)
    {
        FieldName = fieldName;
        Operator = @operator;
        Value = value;
    }
}