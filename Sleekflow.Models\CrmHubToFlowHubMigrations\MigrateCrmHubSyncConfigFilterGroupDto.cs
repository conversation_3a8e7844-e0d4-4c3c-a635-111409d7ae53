﻿using Newtonsoft.Json;

namespace Sleekflow.Models.CrmHubToFlowHubMigrations;

public class MigrateCrmHubSyncConfigFilterGroupDto
{
    [JsonProperty("filters")]
    public List<MigrateCrmHubSyncConfigFilterDto> Filters { get; set; }

    [JsonConstructor]
    public MigrateCrmHubSyncConfigFilterGroupDto(
        List<MigrateCrmHubSyncConfigFilterDto> filters)
    {
        Filters = filters;
    }
}