using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public abstract class BaseMediaMessageObject : BaseMessageObject
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("link")]
    public string Link { get; set; }

    [JsonProperty("caption")]
    public string Caption { get; set; }

    [JsonProperty("filename")]
    public string Filename { get; set; }

    [JsonProperty("provider")]
    public MediaMessageObjectProvider Provider { get; set; }

    [JsonConstructor]
    protected BaseMediaMessageObject(
        string id,
        string link,
        string caption,
        string filename,
        MediaMessageObjectProvider provider)
    {
        Id = id;
        Link = link;
        Caption = caption;
        Filename = filename;
        Provider = provider;
    }
}

public class MediaMessageObjectProvider
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("config")]
    public MediaMessageObjectProviderConfig Config { get; set; }

    [JsonConstructor]
    public MediaMessageObjectProvider(string name, string type, MediaMessageObjectProviderConfig config)
    {
        Name = name;
        Type = type;
        Config = config;
    }
}

public class MediaMessageObjectProviderConfig
{
    [JsonProperty("basic")]
    public MediaMessageObjectProviderConfigBasic? Basic { get; set; }

    [JsonProperty("bearer")]
    public MediaMessageObjectProviderConfigBearer? Bearer { get; set; }

    [JsonConstructor]
    public MediaMessageObjectProviderConfig(
        MediaMessageObjectProviderConfigBasic? basic,
        MediaMessageObjectProviderConfigBearer? bearer)
    {
        Basic = basic;
        Bearer = bearer;
    }
}

public class MediaMessageObjectProviderConfigBasic
{
    [JsonProperty("username")]
    public string Username { get; set; }

    [JsonProperty("password")]
    public string Password { get; set; }

    [JsonConstructor]
    public MediaMessageObjectProviderConfigBasic(string username, string password)
    {
        Username = username;
        Password = password;
    }
}

public class MediaMessageObjectProviderConfigBearer
{
    [JsonProperty("bearer")]
    public string Bearer { get; set; }

    [JsonConstructor]
    public MediaMessageObjectProviderConfigBearer(string bearer)
    {
        Bearer = bearer;
    }
}