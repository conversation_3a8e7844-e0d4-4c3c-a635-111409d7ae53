using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateContactStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-contact";

    [Required]
    [J<PERSON><PERSON>roper<PERSON>("contact_phone_number__expr")]
    public string ContactPhoneNumberExpr { get; set; }

    [JsonProperty("contact_properties__expr_dict")]
    public Dictionary<string, string?>? ContactPropertiesExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public CreateContactStepArgs(
        string contactPhoneNumberExpr,
        Dictionary<string, string?>? contactPropertiesExprDict)
    {
        ContactPhoneNumberExpr = contactPhoneNumberExpr;
        ContactPropertiesExprDict = contactPropertiesExprDict;
    }
}