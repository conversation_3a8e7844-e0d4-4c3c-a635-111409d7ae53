using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public partial class ChatEvalTest
{
    private static IEnumerable<ChatEvalQuestion> GetTestCases()
    {
#pragma warning disable SA1515
        List<IEnumerable<ChatEvalQuestion>> list =
        [
            // BeameTestCases.GetBeameTestCases(),
            // BowtieTestCases.GetBowtieTestCases(),
            // BowtieTestCases.GetBowtieDifficultTestCases(),
            // ChatEvalTest.GetDifficultTestCases(),
            // EcoWorldTestCases.GetEcoWorldTestCases(),
            // EGLTourTestCases.GetEGLTourTestCases(),
            // HelmetKingTestCases.GetHelmetKingTestCases(),
            // HkbnTestCases.GetHkbnTestCases(),
            // HkbnTestCases.GetHkbnHandsOffTestCases(),
            // LanguageTestCases.GetLanguageTestCases(),
            // LinkedinTestCases.GetLinkedinTestCases(),
            // OTandPTestCases.GetOTandPTestCases(),
            SleekflowTestCases.GetSleekflowTestCases(),
            SleekflowTestCases.GetSleekflowChiliPiperTestCases(),
            SleekflowTestCases.GetSleekflowDifficultTestCases(),
            SleekflowTestCases.GetSleekflowHandsOffTestCases(),
            SleekflowTestCases.GetSleekflowRealTestCases(),
            SleekflowTestCases.GetSleekflowMetaTestCases(),
            SleekflowTestCases.GetSleekflowPricingTestCases(),
            SleekflowTestCases.GetSleekflowAudioTestCases(),
            // TaylorUniversityTestCases.GetTaylorUniversityTestCases(),
            // ToneTestCases.GetToneTestCases(),
            // VeryLongTestCases.GetVeryLongTestCases(),
            // WeirdoBeautyTestCases.GetWeirdoBeautyTestCases(),
            // WeirdoBeautyTestCases.GetWeirdoBeautyDifficultTestCases(),
            // WeirdoBeautyTestCases.GetWeirdoBeautyHandsOffTestCases(),
            // GetGreetingBehaviorTestCases()
        ];
#pragma warning restore SA1515

        // Use the Chili Piper test cases to validate field values
        return list.SelectMany(e => e).ToList();
    }

    private static IEnumerable<ChatEvalQuestion> GetDifficultTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HKBN/",
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc");

        // Selected Test Cases
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer inquiring about INDICAID products without prior knowledge of plans",
            [
                new ChatMessageContent(AuthorRole.User, "你好, 最近身邊朋友都在用INDICAID快測, 想問下有什麼檢測試劑可以選擇?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！我很樂意為你介紹INDICAID的檢測產品。INDICAID提供多款快速檢測試劑，主要分為以下幾大類：\n1. 呼吸道健康系列，例如6合1快速抗原檢測試劑盒，可同時檢測6種病毒包括新冠、流感等\n2. 腸胃健康系列，如腸道3合1快速檢測\n3. 泌尿生殖健康系列，包括HPV尿液檢測\n請問你對哪個系列比較感興趣？"),
                new ChatMessageContent(AuthorRole.User, "呼吸道那個6合1聽起來不錯，可以講詳細點嗎？")
            ],
            "呼吸道6合1檢測試劑盒是我們的熱門產品，特點包括：\n- 一次測試即可同時檢測6種病毒：新冠病毒、甲型及乙型流感、呼吸道合胞病毒、呼吸道腺病毒和肺炎支原體\n- 準確度高達99%，是最多香港私家醫院採用的本地快測品牌\n- 適合成人及小童使用\n- 15分鐘就能得到結果\n而且，我們現在有特別優惠計劃，讓你可以更實惠的價格定期購買，需要我為你介紹計劃詳情嗎？",
            SourceFilenames: ["INDICAID「月月測」產品小冊子.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer seeking guidance on plan activation process",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I just subscribed to the HKBN x Bowtie medical plan. How do I activate it?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Welcome! To activate your HKBN x Bowtie medical plan, you'll need to follow these steps:\n1. Wait for the activation notification email from HKBN\n2. Login to My HKBN\n3. Go to 'Message Center' → 'Service Messages'\n4. Open the message titled 'Activate Bowtie Medical Service'\n5. Click the button to get your activation code\n\nWould you like me to guide you through the next steps?"),
                new ChatMessageContent(AuthorRole.User, "Yes, please. What happens after I get the activation code?"),
            ],
            "After receiving your activation code:\n1. Visit the Bowtie designated webpage\n2. Enter the activation code\n3. Fill in your personal information\n4. Create your Bowtie account\n5. Verify your email address\n6. Accept the terms and conditions\n\nThe service will be active for 12 months from the successful activation date. Would you like to know what services you can use immediately after activation?",
            SourceFilenames:
            [
                "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md"
            ]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對計劃價格表示不滿並質疑服務質素",
            [
                new ChatMessageContent(AuthorRole.User, "喂！這個計劃也太貴了吧！你們是不是在搶錢啊！"),
            ],
            "您好，我能理解您對價格感到不滿。請問您是在查詢哪一種計劃呢？讓我先了解您的需求，看看能如何幫您。",
            SourceFilenames: []);

        var indicaidDocuments =
            new List<string>
            {
                "INDICAID「月月測」產品小冊子.md", "RS_sales briefing_INDICAID_20241204.docx.md"
            };
        var bowtieDocuments =
            new List<string>
            {
                "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md",
                "RS_sales briefing_Bowtie_20241204.docx.md"
            };
        var evercareDocuments =
            new List<string>
            {
                "HKBN x Evercare Whatsapp Guide_20241204.md", "RS_sales briefing_Evercare_20241204.docx.md"
            };
        var mobilePlanDocuments =
            new List<string>
            {
                "RS_sales briefing_N mobile_42mbps $78 $98 China data_20241204.docx.md",
                "RS_sales briefing_N mobile_APAC_20241204.docx.md",
                "RS_sales briefing_tri-area_20241204.docx.md",
                "RS_sales briefing_WW plan_20241204.docx.md"
            };

        // Lei's Test Cases
        yield return new ChatEvalQuestion(
            testConfig,
            "General Inquiry",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想咨询一下你们的业务。"),
            ],
            "你好！感谢你对我们业务的关注。我们提供一系列健康科技服务，专注于家庭护理和健康管理，特别适合老年人和有健康挑战的人士。我们的服务包括家庭护理计划、伤口护理、陪护服务和营养咨询等。  我很想了解更多关于你的需求。你是在寻找特定的服务，还是想了解我们如何能帮助你或你的家人？请随时分享你的想法，我在这里帮助你！",
            SourceFilenames: indicaidDocuments.Concat(bowtieDocuments).Concat(evercareDocuments)
                .Concat(mobilePlanDocuments).ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "General inquiry",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想咨询一下你们的业务。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！非常感谢您对我们业务的关注。我们专注于提供高质量的家庭护理服务和快速诊断测试解决方案，以满足现代家庭的需求。\n\n我们的家庭护理服务包括多种计划，例如基础计划每月仅需$399，提供2小时的护理服务，适合老年人或有基本护理需求的人士。我们还提供更高级的计划，包含专业营养咨询和护理服务，帮助家庭更好地照顾他们的亲人。\n\n在快速诊断测试方面，我们的INDICAID\u2122系列产品提供多种健康检测，结果快速且准确，例如HIV/Syphilis快速抗体测试，结果在20分钟内即可得出，准确率高达99.9%。这些测试解决了人们对隐私的关注，让您在家中也能轻松管理健康。\n\n我们注意到，随着城市生活节奏的加快，家庭对专业护理和健康检测的需求日益增加。我们致力于提供灵活、便捷的服务，帮助家庭在繁忙的生活中找到平衡。\n\n我很想了解您对哪些具体服务或产品感兴趣，或者您是否面临任何特定的挑战？这样我们可以更好地为您提供帮助。"),
                new ChatMessageContent(AuthorRole.User, "你们没有移动或者宽带业务吗？"),
            ],
            "你好！感谢你的提问。我们确实提供移动和宽带服务，特别是我们的N Mobile服务。我们的移动计划包括多种5G选项，适合不同的需求，比如本地和全球数据共享。比如，我们的5G移动数据共享计划可以让你在中国大陆、香港和澳门之间共享数据，非常适合经常出行的人士。  此外，我们的移动计划还包括HKBN SAFE，这是一个网络安全服务，可以保护你的设备安全。还有GLOBAL TALK+，让你在国际通话时更加方便。  如果你有特定的需求，比如你需要的流量或通话时间，或者你对某个特定的服务感兴趣，请告诉我，我很乐意帮助你找到最适合你的方案！",
            SourceFilenames: indicaidDocuments.Concat(bowtieDocuments).Concat(evercareDocuments)
                .Concat(mobilePlanDocuments).ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "General inquiry",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想咨询一下你们的业务。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！非常感谢您对我们业务的关注。我们专注于提供高质量的家庭护理服务和快速诊断测试解决方案，以满足现代家庭的需求。\n\n我们的家庭护理服务包括多种计划，例如基础计划每月仅需$399，提供2小时的护理服务，适合老年人或有基本护理需求的人士。我们还提供更高级的计划，包含专业营养咨询和护理服务，帮助家庭更好地照顾他们的亲人。\n\n在快速诊断测试方面，我们的INDICAID\u2122系列产品提供多种健康检测，结果快速且准确，例如HIV/Syphilis快速抗体测试，结果在20分钟内即可得出，准确率高达99.9%。这些测试解决了人们对隐私的关注，让您在家中也能轻松管理健康。\n\n我们注意到，随着城市生活节奏的加快，家庭对专业护理和健康检测的需求日益增加。我们致力于提供灵活、便捷的服务，帮助家庭在繁忙的生活中找到平衡。\n\n我很想了解您对哪些具体服务或产品感兴趣，或者您是否面临任何特定的挑战？这样我们可以更好地为您提供帮助。"),
                new ChatMessageContent(AuthorRole.User, "你们没有移动或者宽带业务吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！感谢你的提问。我们确实提供移动和宽带服务，特别是我们的N Mobile服务。我们的移动计划包括多种5G选项，适合不同的需求，比如本地和全球数据共享。比如，我们的5G移动数据共享计划可以让你在中国大陆、香港和澳门之间共享数据，非常适合经常出行的人士。  此外，我们的移动计划还包括HKBN SAFE，这是一个网络安全服务，可以保护你的设备安全。还有GLOBAL TALK+，让你在国际通话时更加方便。  如果你有特定的需求，比如你需要的流量或通话时间，或者你对某个特定的服务感兴趣，请告诉我，我很乐意帮助你找到最适合你的方案！"),
                new ChatMessageContent(AuthorRole.User, "还有什么其他业务吗？"),
            ],
            "谢谢你的提问！除了移动和宽带服务，我们还提供一系列其他服务，以满足不同客户的需求。  首先，我们与Evercare合作，提供家庭护理服务，包括基本的家庭护理、先进的健康支持和伤口护理。这些服务特别适合需要额外照顾的家庭成员，确保他们在家中也能获得专业的健康支持。  此外，我们的HKBN SAFE服务是一个网络安全解决方案，包含在我们的移动计划中，前六个月免费，之后每月只需$10。这可以帮助保护你的设备免受网络威胁，确保你的个人信息安全。  我们还与Bowtie合作，提供远程医疗和健康管理咨询服务，让你可以方便地获得医疗建议和支持。  如果你对这些服务或其他任何服务感兴趣，或者有具体的需求，请告诉我，我很乐意为你提供更多信息！我们可以一起找到最适合你的解决方案。",
            SourceFilenames: indicaidDocuments.Concat(bowtieDocuments).Concat(evercareDocuments)
                .Concat(mobilePlanDocuments).ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "General inquiry",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想咨询一下你们的业务。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！非常感谢您对我们业务的关注。我们专注于提供高质量的家庭护理服务和快速诊断测试解决方案，以满足现代家庭的需求。\n\n我们的家庭护理服务包括多种计划，例如基础计划每月仅需$399，提供2小时的护理服务，适合老年人或有基本护理需求的人士。我们还提供更高级的计划，包含专业营养咨询和护理服务，帮助家庭更好地照顾他们的亲人。\n\n在快速诊断测试方面，我们的INDICAID\u2122系列产品提供多种健康检测，结果快速且准确，例如HIV/Syphilis快速抗体测试，结果在20分钟内即可得出，准确率高达99.9%。这些测试解决了人们对隐私的关注，让您在家中也能轻松管理健康。\n\n我们注意到，随着城市生活节奏的加快，家庭对专业护理和健康检测的需求日益增加。我们致力于提供灵活、便捷的服务，帮助家庭在繁忙的生活中找到平衡。\n\n我很想了解您对哪些具体服务或产品感兴趣，或者您是否面临任何特定的挑战？这样我们可以更好地为您提供帮助。"),
                new ChatMessageContent(AuthorRole.User, "你们没有移动或者宽带业务吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！感谢你的提问。我们确实提供移动和宽带服务，特别是我们的N Mobile服务。我们的移动计划包括多种5G选项，适合不同的需求，比如本地和全球数据共享。比如，我们的5G移动数据共享计划可以让你在中国大陆、香港和澳门之间共享数据，非常适合经常出行的人士。  此外，我们的移动计划还包括HKBN SAFE，这是一个网络安全服务，可以保护你的设备安全。还有GLOBAL TALK+，让你在国际通话时更加方便。  如果你有特定的需求，比如你需要的流量或通话时间，或者你对某个特定的服务感兴趣，请告诉我，我很乐意帮助你找到最适合你的方案！"),
                new ChatMessageContent(AuthorRole.User, "还有什么其他业务吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "谢谢你的提问！除了移动和宽带服务，我们还提供一系列其他服务，以满足不同客户的需求。  首先，我们与Evercare合作，提供家庭护理服务，包括基本的家庭护理、先进的健康支持和伤口护理。这些服务特别适合需要额外照顾的家庭成员，确保他们在家中也能获得专业的健康支持。  此外，我们的HKBN SAFE服务是一个网络安全解决方案，包含在我们的移动计划中，前六个月免费，之后每月只需$10。这可以帮助保护你的设备免受网络威胁，确保你的个人信息安全。  我们还与Bowtie合作，提供远程医疗和健康管理咨询服务，让你可以方便地获得医疗建议和支持。  如果你对这些服务或其他任何服务感兴趣，或者有具体的需求，请告诉我，我很乐意为你提供更多信息！我们可以一起找到最适合你的解决方案。"),
                new ChatMessageContent(AuthorRole.User, "你们没有家庭互联网服务吗？"),
            ],
            "谢谢你的提问！我们确实提供家庭互联网服务，包括高速宽带选项，适合不同的需求。我们的家庭互联网计划提供稳定的连接和多种速度选择，以满足家庭的上网需求。  如果你有特定的需求，比如你需要的速度或套餐类型，或者你对某个特定的服务感兴趣，请告诉我，我很乐意帮助你找到最适合你的方案！我们可以一起确保你获得最佳的互联网体验。",
            SourceFilenames: mobilePlanDocuments.ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "General inquiry",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想咨询一下你们的业务。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！非常感谢您对我们业务的关注。我们专注于提供高质量的家庭护理服务和快速诊断测试解决方案，以满足现代家庭的需求。\n\n我们的家庭护理服务包括多种计划，例如基础计划每月仅需$399，提供2小时的护理服务，适合老年人或有基本护理需求的人士。我们还提供更高级的计划，包含专业营养咨询和护理服务，帮助家庭更好地照顾他们的亲人。\n\n在快速诊断测试方面，我们的INDICAID\u2122系列产品提供多种健康检测，结果快速且准确，例如HIV/Syphilis快速抗体测试，结果在20分钟内即可得出，准确率高达99.9%。这些测试解决了人们对隐私的关注，让您在家中也能轻松管理健康。\n\n我们注意到，随着城市生活节奏的加快，家庭对专业护理和健康检测的需求日益增加。我们致力于提供灵活、便捷的服务，帮助家庭在繁忙的生活中找到平衡。\n\n我很想了解您对哪些具体服务或产品感兴趣，或者您是否面临任何特定的挑战？这样我们可以更好地为您提供帮助。"),
                new ChatMessageContent(AuthorRole.User, "你们没有移动或者宽带业务吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！感谢你的提问。我们确实提供移动和宽带服务，特别是我们的N Mobile服务。我们的移动计划包括多种5G选项，适合不同的需求，比如本地和全球数据共享。比如，我们的5G移动数据共享计划可以让你在中国大陆、香港和澳门之间共享数据，非常适合经常出行的人士。  此外，我们的移动计划还包括HKBN SAFE，这是一个网络安全服务，可以保护你的设备安全。还有GLOBAL TALK+，让你在国际通话时更加方便。  如果你有特定的需求，比如你需要的流量或通话时间，或者你对某个特定的服务感兴趣，请告诉我，我很乐意帮助你找到最适合你的方案！"),
                new ChatMessageContent(AuthorRole.User, "还有什么其他业务吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "谢谢你的提问！除了移动和宽带服务，我们还提供一系列其他服务，以满足不同客户的需求。  首先，我们与Evercare合作，提供家庭护理服务，包括基本的家庭护理、先进的健康支持和伤口护理。这些服务特别适合需要额外照顾的家庭成员，确保他们在家中也能获得专业的健康支持。  此外，我们的HKBN SAFE服务是一个网络安全解决方案，包含在我们的移动计划中，前六个月免费，之后每月只需$10。这可以帮助保护你的设备免受网络威胁，确保你的个人信息安全。  我们还与Bowtie合作，提供远程医疗和健康管理咨询服务，让你可以方便地获得医疗建议和支持。  如果你对这些服务或其他任何服务感兴趣，或者有具体的需求，请告诉我，我很乐意为你提供更多信息！我们可以一起找到最适合你的解决方案。"),
                new ChatMessageContent(AuthorRole.User, "你们没有家庭互联网服务吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "谢谢你的提问！我们确实提供家庭互联网服务，包括高速宽带选项，适合不同的需求。我们的家庭互联网计划提供稳定的连接和多种速度选择，以满足家庭的上网需求。  如果你有特定的需求，比如你需要的速度或套餐类型，或者你对某个特定的服务感兴趣，请告诉我，我很乐意帮助你找到最适合你的方案！我们可以一起确保你获得最佳的互联网体验。"),
                new ChatMessageContent(AuthorRole.User, "给我介绍下不同的套餐呗"),
            ],
            "谢谢你的耐心等待！我们有几种家庭互联网套餐可供选择，适合不同的需求：  1. **5G 50GB计划**：    - **价格**: $198/月（合同期结束后）    - **速度**: 5G本地数据速度，超出后限速至1Mbps    - **数据**: 50GB本地数据，3GB中国大陆和澳门数据    - **特别优惠**: 包含6个月HKBN SAFE（3个设备），超出后每月$10  2. **5G 30GB计划**：    - **价格**: $278/月（合同期结束后）    - **速度**: 5G本地数据速度，超出后限速至1Mbps    - **数据**: 30GB本地数据    - **特别优惠**: 包含6个月HKBN SAFE（3个设备），超出后每月$10  3. **5G 20GB计划**：    - **价格**: $258/月（合同期结束后）    - **速度**: 5G本地数据速度，超出后限速至1Mbps    - **数据**: 20GB本地数据    - **特别优惠**: 包含6个月HKBN SAFE（3个设备），超出后每月$10  此外，所有计划都包括HKBN SAFE，这是一个额外的安全服务，前6个月免费，之后每月只需$10。对于新号码用户，我们还提供延迟账单的优惠，最多可延迟30天。  如果你有特定的使用需求，比如你通常的上网活动或家庭成员的数量，请告诉我，我可以帮助你找到最适合你的套餐！我们非常重视客户的满意度，期待能为你提供最佳的互联网体验。",
            SourceFilenames: mobilePlanDocuments.ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "General inquiry",
            [
                new ChatMessageContent(AuthorRole.User, "Hi HKBN"),
                new ChatMessageContent(AuthorRole.User, "有咩上網 plan?"),
            ],
            "我們沒有。",
            SourceFilenames: mobilePlanDocuments.ToList());
    }

    private static IEnumerable<ChatEvalQuestion> GetGreetingBehaviorTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Generic/", // Assuming a generic path or empty if not strictly needed
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc"); // Placeholder company ID

        // Test Case 1: Standard Greeting - No Redundancy
        yield return new ChatEvalQuestion(
            testConfig,
            "User greets, assistant greets. User asks a question. Assistant answers without re-greeting.",
            [
                new ChatMessageContent(AuthorRole.User, "Hello"),
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I help you today?"), // Example greeting
                new ChatMessageContent(AuthorRole.User, "I'm interested in your products.")
            ],
            "Great! We have a range of products to suit your needs. To better assist you, could you tell me a bit more about what you're looking for?", // Expected response without greeting
            SourceFilenames: []);

        // Test Case 2: No Initial User Greeting - Assistant Greets
        yield return new ChatEvalQuestion(
            testConfig,
            "User asks a question directly. Assistant answers with a greeting.",
            [
                new ChatMessageContent(AuthorRole.User, "What products do you have?")
            ],
            "Hello! We offer a variety of products. To help me narrow it down, could you tell me what you're looking for?", // Expected response with greeting
            SourceFilenames: []);

        // Test Case 3: Greeting in Another Language (Chinese) - No Redundancy
        yield return new ChatEvalQuestion(
            testConfig,
            "User greets in Chinese, assistant greets in Chinese. User asks a question in Chinese. Assistant answers in Chinese without re-greeting.",
            [
                new ChatMessageContent(AuthorRole.User, "你好"),
                new ChatMessageContent(AuthorRole.Assistant, "你好！請問有什麼可以幫到你？"), // Example greeting in Chinese
                new ChatMessageContent(AuthorRole.User, "我想了解你们的产品。")
            ],
            "好的！我们有多种产品。为了更好地帮助您，您能告诉我您具体在寻找什么吗？", // Expected response in Chinese without re-greeting
            SourceFilenames: []);

        // Test Case 4: Conversation continues after no re-greeting - still no greeting
        yield return new ChatEvalQuestion(
            testConfig,
            "Conversation continues after a non-greeting response. Assistant should still not greet.",
            [
                new ChatMessageContent(AuthorRole.User, "Hello"),
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I help you today?"),
                new ChatMessageContent(AuthorRole.User, "I'm interested in your products."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! We have a range of products to suit your needs. To better assist you, could you tell me a bit more about what you're looking for?"),
                new ChatMessageContent(AuthorRole.User, "Tell me about product X.")
            ],
            "Product X is one of our top sellers. It offers [features and benefits]. Would you like to know more about its pricing or how it compares to product Y?", // Expected response without greeting
            SourceFilenames: []);
    }
}