using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class PostWorkflowPublishedEnrollmentRequestBodyArgs
{
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionId { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonConstructor]
    public PostWorkflowPublishedEnrollmentRequestBodyArgs(
        string entityTypeName,
        string connectionId,
        string workflowId,
        string workflowVersionId,
        string providerName)
    {
        EntityTypeName = entityTypeName;
        ConnectionId = connectionId;
        WorkflowId = workflowId;
        WorkflowVersionId = workflowVersionId;
        ProviderName = providerName;
    }
}