using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class CreateWorkflowWebhookTrigger : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;
    private readonly IIdService _idService;

    public CreateWorkflowWebhookTrigger(
        IWorkflowService workflowService,
        IWorkflowWebhookTriggerService workflowWebhookTriggerService,
        IIdService idService)
    {
        _workflowService = workflowService;
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
        _idService = idService;
    }

    public class CreateWorkflowWebhookTriggerInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameWorkflowId)]
        [Required]
        public string WorkflowId { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameObjectIdExpression)]
        [Required]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("object_type")]
        [Required]
        [AllowedStringValues(
            isIgnoreCase: false,
            "Contact",
            "Contact.Id",
            "Contact.PhoneNumber",
            "Contact.Email")]
        public string ObjectType { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateWorkflowWebhookTriggerInput(
            string sleekflowCompanyId,
            string workflowId,
            string objectIdExpression,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string objectType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            ObjectIdExpression = objectIdExpression;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            ObjectType = objectType;
        }
    }

    public class CreateWorkflowWebhookTriggerOutput
    {
        [JsonProperty("workflow_webhook_trigger")]
        public WorkflowWebhookTrigger WorkflowWebhookTrigger { get; set; }

        [JsonConstructor]
        public CreateWorkflowWebhookTriggerOutput(
            WorkflowWebhookTrigger workflowWebhookTrigger)
        {
            WorkflowWebhookTrigger = workflowWebhookTrigger;
        }
    }

    public async Task<CreateWorkflowWebhookTriggerOutput> F(
        CreateWorkflowWebhookTriggerInput createWorkflowWebhookTriggerInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createWorkflowWebhookTriggerInput.SleekflowStaffId,
            createWorkflowWebhookTriggerInput.SleekflowStaffTeamIds);

        var workflowId = createWorkflowWebhookTriggerInput.WorkflowId;
        var sleekflowCompanyId = createWorkflowWebhookTriggerInput.SleekflowCompanyId;
        var objectIdExpression = createWorkflowWebhookTriggerInput.ObjectIdExpression;
        var objectType = createWorkflowWebhookTriggerInput.ObjectType;

        var (typeName, _) = _idService.DecodeId(workflowId);
        if (typeName != "Workflow")
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult(
                        "The workflow id is invalid.",
                        new[]
                        {
                            "WorkflowId"
                        })
                });
        }

        var existingWorkflowWebhookTrigger =
            await _workflowWebhookTriggerService.GetWorkflowWebhookTriggerOrDefaultAsync(
                workflowId,
                sleekflowCompanyId,
                objectType);

        if (existingWorkflowWebhookTrigger is not null)
        {
            return new CreateWorkflowWebhookTriggerOutput(existingWorkflowWebhookTrigger);
        }

        var (_, versionedWorkflows) = await _workflowService.GetWorkflowAsync(
            workflowId,
            sleekflowCompanyId);

        if (versionedWorkflows.Count == 0)
        {
            throw new SfNotFoundObjectException(workflowId);
        }

        var workflowWebhookTriggerId = _idService.GetId(
            "WorkflowWebhookTrigger",
            workflowId);

        var workflowWebhookTrigger =
            await _workflowWebhookTriggerService.CreateWorkflowWebhookTriggerAsync(
                new WorkflowWebhookTrigger(
                    null,
                    workflowId,
                    RandomStringUtils.Gen(16),
                    objectIdExpression,
                    workflowWebhookTriggerId,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    sleekflowCompanyId,
                    sleekflowStaff,
                    sleekflowStaff,
                    objectType),
                sleekflowCompanyId);

        return new CreateWorkflowWebhookTriggerOutput(workflowWebhookTrigger);
    }
}