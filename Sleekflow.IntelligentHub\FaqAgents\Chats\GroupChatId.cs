namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

/// <summary>
/// Represents a strongly typed GroupChatId, which encodes the companyId and a unique session Guid.
/// Provides parsing and formatting utilities to ensure consistency across the project.
/// </summary>
public sealed class GroupChatId
{
    /// <summary>
    /// The company ID (e.g., sleekflowCompanyId).
    /// </summary>
    public string CompanyId { get; }

    /// <summary>
    /// The object ID (e.g., contactId).
    /// </summary>
    public string ObjectId { get; }

    /// <summary>
    /// The unique session Guid part.
    /// </summary>
    public string SessionId { get; }

    /// <summary>
    /// The canonical string representation (e.g., "companyId:objectId:sessionId").
    /// </summary>
    public override string ToString() => $"{CompanyId}:{ObjectId}:{SessionId}";

    internal GroupChatId(string companyId, string objectId, string sessionId)
    {
        CompanyId = companyId;
        ObjectId = objectId;
        SessionId = sessionId;
    }

    /// <summary>
    /// Creates a new GroupChatId for the given company/chat id.
    /// </summary>
    public static GroupChatId Create(string companyId, string objectId)
    {
        return new GroupChatId(companyId, objectId, Guid.NewGuid().ToString());
    }

    /// <summary>
    /// Parses a groupChatId string (format: "companyId:guid").
    /// Throws if the format is invalid.
    /// </summary>
    public static GroupChatId Parse(string groupChatIdStr)
    {
        if (string.IsNullOrWhiteSpace(groupChatIdStr))
        {
            throw new ArgumentException("groupChatId cannot be null or empty", nameof(groupChatIdStr));
        }

        var parts = groupChatIdStr.Split(':');
        if (parts.Length != 3)
        {
            throw new FormatException($"Invalid groupChatId format: {groupChatIdStr}");
        }

        return new GroupChatId(parts[0], parts[1], parts[2]);
    }
}