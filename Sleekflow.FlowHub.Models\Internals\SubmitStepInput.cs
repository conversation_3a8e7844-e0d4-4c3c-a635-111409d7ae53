using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Internals;

public class SubmitStepInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StepId { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("worker_instance_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string WorkerInstanceId { get; set; }

    [JsonConstructor]
    public SubmitStepInput(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string workerInstanceId)
    {
        StateId = stateId;
        StepId = stepId;
        StackEntries = stackEntries;
        WorkerInstanceId = workerInstanceId;
    }
}