using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Plugins.Models;

[method: JsonConstructor]
public class GenerateKeywordsResponse(List<KeywordRecord> highLevelKeywords, List<KeywordRecord> lowLevelKeywords)
{
    [JsonProperty("high_level_keywords")]
    public List<KeywordRecord> HighLevelKeywords { get; set; } = highLevelKeywords;

    [JsonProperty("low_level_keywords")]
    public List<KeywordRecord> LowLevelKeywords { get; set; } = lowLevelKeywords;
}

[method: JsonConstructor]
public class KeywordRecord(string keyword, string reasoning)
{
    public string Keyword { get; set; } = keyword;

    public string Reasoning { get; set; } = reasoning;
}