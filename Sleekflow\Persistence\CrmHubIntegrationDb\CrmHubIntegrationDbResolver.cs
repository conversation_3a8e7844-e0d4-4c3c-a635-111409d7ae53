using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.CrmHubIntegrationDb;

public interface ICrmHubIntegrationDbResolver : IContainerResolver
{
}

public class CrmHubIntegrationDbResolver : ICrmHubIntegrationDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public CrmHubIntegrationDbResolver(ICrmHubIntegrationDbConfig crmHubIntegrationDbConfig)
    {
        _cosmosClient = new CosmosClient(
            crmHubIntegrationDbConfig.Endpoint,
            crmHubIntegrationDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}