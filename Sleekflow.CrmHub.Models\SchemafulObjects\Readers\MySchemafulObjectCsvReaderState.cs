﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.SchemafulObjects.Readers;

public class MySchemafulObjectCsvReaderState
{
    [JsonProperty("last_byte_position")]
    public long LastBytePosition { get; }

    [JsonProperty("num_of_records")]
    public long NumOfRecords { get; }

    [JsonProperty("headers")]
    public string[]? Headers { get; }

    [JsonProperty("is_completed")]
    public bool IsCompleted { get; set; }

    [JsonConstructor]
    public MySchemafulObjectCsvReaderState(
        long lastBytePosition,
        long numOfRecords,
        string[]? headers,
        bool isCompleted)
    {
        LastBytePosition = lastBytePosition;
        NumOfRecords = numOfRecords;
        Headers = headers;
        IsCompleted = isCompleted;
    }
}