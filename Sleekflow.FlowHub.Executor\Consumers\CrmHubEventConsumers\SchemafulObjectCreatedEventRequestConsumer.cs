using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class SchemafulObjectCreatedEventRequestConsumerDefinition
    : ConsumerDefinition<SchemafulObjectCreatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SchemafulObjectCreatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SchemafulObjectCreatedEventRequestConsumer : IConsumer<SchemafulObjectCreatedEventRequest>
{
    private readonly IServiceBusManager _serviceBusManager;

    public SchemafulObjectCreatedEventRequestConsumer(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<SchemafulObjectCreatedEventRequest> context)
    {
        var schemafulObjectCreatedEventRequest = context.Message;

        // DEVS-7841 changed to use Contact.Id
        // var identityObjectId
        // $"{schemafulObjecUpdatedEventRequest.SchemaId}:{schemafulObjecUpdatedEventRequest.SchemafulObjectId}";
        var identityObjectId = schemafulObjectCreatedEventRequest.SleekflowUserProfileId;

        await _serviceBusManager.PublishAsync(
            new OnTriggerEventRequestedEvent(
                new OnSchemafulObjectCreatedEventBody(
                    schemafulObjectCreatedEventRequest.CreatedAt,
                    schemafulObjectCreatedEventRequest.SchemafulObjectId,
                    schemafulObjectCreatedEventRequest.SchemaId,
                    schemafulObjectCreatedEventRequest.PrimaryPropertyValue,
                    schemafulObjectCreatedEventRequest.SleekflowUserProfileId,
                    schemafulObjectCreatedEventRequest.PropertyValues,
                    schemafulObjectCreatedEventRequest.SleekflowStaffId,
                    schemafulObjectCreatedEventRequest.SleekflowStaffTeamIds),
                identityObjectId,
                "Contact",
                schemafulObjectCreatedEventRequest.SleekflowCompanyId));
    }
}