﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json.Linq;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

public class PatchFlowHubStateAndStepExecutionTtl : IExecutor
{
    private readonly CosmosClient _cosmosClient;

    private string? _stateDatabaseId;
    private string? _stateContainerId;

    private string? _stepExecutionDatabaseId;
    private string? _stepExecutionContainerId;

    private int _stateProcessedCount = 0;
    private int _stepExecutionProcessedCount = 0;

    public PatchFlowHubStateAndStepExecutionTtl(DbConfig dbConfig)
    {
        _cosmosClient = new CosmosClient(
            dbConfig.Endpoint,
            dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
        => "Patch Flow Hub State and Step Execution TTL";

    public async Task PrepareAsync()
    {
        (_stateDatabaseId, _stateContainerId) = await SelectDatabaseContainerAsync("State");
        (_stepExecutionDatabaseId, _stepExecutionContainerId) = await SelectDatabaseContainerAsync("Step Execution");
    }

    private async Task<(string DatabaseId, string ContainerName)> SelectDatabaseContainerAsync(string entityName)
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var databaseId = Prompt.Select(
            $"Select your database for entity {entityName} ",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, databaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var containerId = Prompt.Select(
            $"Select your container for entity {entityName}",
            containerIds);

        return (databaseId, containerId);
    }

    public async Task ExecuteAsync()
    {
        if (!string.IsNullOrEmpty(_stateDatabaseId)
            && !string.IsNullOrEmpty(_stateContainerId)
            && !string.IsNullOrEmpty(_stepExecutionDatabaseId)
            && !string.IsNullOrEmpty(_stepExecutionContainerId))
        {
            await MigrateObjectsAsync();
            Console.WriteLine($"Completed {_stateProcessedCount} for State and {_stepExecutionProcessedCount} for Step Execution");
        }
        else
        {
            Console.WriteLine($"Unable to start {nameof(PatchFlowHubStateAndStepExecutionTtl)}");
        }
    }

    private async Task MigrateObjectsAsync()
    {
        if (string.IsNullOrEmpty(_stateDatabaseId)
            || string.IsNullOrEmpty(_stateContainerId)
            || string.IsNullOrEmpty(_stepExecutionDatabaseId)
            || string.IsNullOrEmpty(_stepExecutionContainerId))
        {
            return;
        }

        var stateRetryPolicy = CosmosUtils.GetDefaultRetryPolicy(_stateContainerId);
        var stepExecutionRetryPolicy = CosmosUtils.GetDefaultRetryPolicy(_stepExecutionContainerId);

        var stateContainer = _cosmosClient.GetContainer(_stateDatabaseId, _stateContainerId);
        var stepExecutionContainer = _cosmosClient.GetContainer(_stepExecutionDatabaseId, _stepExecutionContainerId);

        var statePartitionKeyPaths = await CosmosUtils.GetContainerPartitionKeyPathsAsync(
            _cosmosClient,
            _stateDatabaseId,
            _stateContainerId);

        var stepExecutionPartitionKeyPaths = await CosmosUtils.GetContainerPartitionKeyPathsAsync(
            _cosmosClient,
            _stepExecutionDatabaseId,
            _stepExecutionContainerId);

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(
                stateContainer,
                "SELECT * FROM root c WHERE NOT IS_DEFINED(c.ttl) AND c.state_status IN ('Complete', 'Failed', 'Cancelled', 'Blocked', 'Abandoned') ORDER BY c.id ASC"),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 200
            },
            async (stateDict, token) =>
            {
                var stateId = stateDict["id"] as string;

                var policyResult = await stateRetryPolicy.ExecuteAndCaptureAsync(
                    async () => await MigrateStateAsync(
                        stateDict,
                        stateContainer,
                        statePartitionKeyPaths!,
                        stepExecutionContainer,
                        stepExecutionPartitionKeyPaths!,
                        stepExecutionRetryPolicy,
                        token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception($"Patch failed for state {stateId}.", policyResult.FinalException);
                }

                Interlocked.Increment(ref _stateProcessedCount);

                if (_stateProcessedCount % 1000 == 0)
                {
                    Console.WriteLine($"Processed state count: {_stateProcessedCount}");
                }
            });
    }

    private async Task MigrateStateAsync(
        Dictionary<string, object?> stateDict,
        Container stateContainer,
        IReadOnlyList<string> statePartitionKeyPaths,
        Container stepExecutionContainer,
        IReadOnlyList<string> stepExecutionPartitionKeyPaths,
        IAsyncPolicy stepExecutionRetryPolicy,
        CancellationToken token)
    {
        var stateId = stateDict["id"] as string;
        var stateIdentity = stateDict["identity"] as JObject;
        var sleekflowCompanyId = (stateIdentity?["sleekflow_company_id"] as JValue)?.Value<string>();
        var stateTimestamp = stateDict["_ts"] as long?;

        if (string.IsNullOrWhiteSpace(stateId)
            || string.IsNullOrWhiteSpace(sleekflowCompanyId))
        {
            return;
        }

        var stateDateTime = DateTimeOffset.FromUnixTimeSeconds(stateTimestamp!.Value);
        var stepExpirationSeconds = stateDateTime.AddDays(90).Subtract(DateTimeOffset.UtcNow).TotalSeconds;

        var statePartitionKeyBuilder = new PartitionKeyBuilder();
        foreach (var partitionKeyPath in statePartitionKeyPaths)
        {
            var keyParts = partitionKeyPath.TrimStart('/').Split('/');
            var currentValue = CosmosUtils.GetValueFromDictionary(stateDict, keyParts, 0);
            statePartitionKeyBuilder.Add(currentValue);
        }

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(
                stepExecutionContainer,
                new QueryDefinition(
                        "SELECT * FROM root c WHERE c.sleekflow_company_id = @sleekflow_company_id AND c.state_id = @state_id ORDER BY c.created_at ASC")
                    .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                    .WithParameter("@state_id", stateId)),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10,
                CancellationToken = token
            },
            async (stepExecutionDict, cancellationToken) =>
            {
                var stepId = stepExecutionDict["id"] as string;

                var policyResult = await stepExecutionRetryPolicy.ExecuteAndCaptureAsync(
                    async () =>
                        await MigrateStepExecutionAsync(
                            stepExecutionContainer,
                            stepExecutionPartitionKeyPaths,
                            stepExecutionDict,
                            stepExpirationSeconds,
                            cancellationToken));

                if (policyResult.FinalException != null)
                {
                    throw new Exception($"Patch step execution failed for step {stepId} in state {stateId}.", policyResult.FinalException);
                }

                Interlocked.Increment(ref _stepExecutionProcessedCount);

                if (_stepExecutionProcessedCount % 1000 == 0)
                {
                    Console.WriteLine($"Processed step execution count: {_stepExecutionProcessedCount}");
                }
            });

        var stateExpirationSeconds = stateDateTime.AddDays(365).Subtract(DateTimeOffset.UtcNow).TotalSeconds;
        var stateTtl = stateExpirationSeconds > 0 ? Convert.ToInt32(Math.Ceiling(stateExpirationSeconds)) : 1;

        await stateContainer.PatchItemAsync<object>(
            stateId,
            statePartitionKeyBuilder.Build(),
            new List<PatchOperation>()
            {
                PatchOperation.Set("/ttl", stateTtl)
            },
            new PatchItemRequestOptions()
            {
                EnableContentResponseOnWrite = false
            },
            token);
    }

    private static async Task MigrateStepExecutionAsync(
        Container stepExecutionContainer,
        IReadOnlyList<string> stepExecutionPartitionKeyPaths,
        Dictionary<string, object?> stepExecutionDict,
        double stepExpirationSeconds,
        CancellationToken cancellationToken)
    {
        var stepExecutionTtl = stepExpirationSeconds > 0
            ? Convert.ToInt32(Math.Ceiling(stepExpirationSeconds))
            : 1;

        var stepExecutionId = stepExecutionDict["id"] as string;

        var stepExecutionPartitionKeyBuilder = new PartitionKeyBuilder();

        foreach (var partitionKeyPath in stepExecutionPartitionKeyPaths)
        {
            var keyParts = partitionKeyPath.TrimStart('/').Split('/');
            var currentValue = CosmosUtils.GetValueFromDictionary(stepExecutionDict, keyParts, 0);
            stepExecutionPartitionKeyBuilder.Add(currentValue);
        }

        await stepExecutionContainer.PatchItemAsync<object>(
            stepExecutionId!,
            stepExecutionPartitionKeyBuilder.Build(),
            new List<PatchOperation>()
            {
                PatchOperation.Set("/ttl", stepExecutionTtl)
            },
            new PatchItemRequestOptions()
            {
                EnableContentResponseOnWrite = false
            },
            cancellationToken);
    }
}