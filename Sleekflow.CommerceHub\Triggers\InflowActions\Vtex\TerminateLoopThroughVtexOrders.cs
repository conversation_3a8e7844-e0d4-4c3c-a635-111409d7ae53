﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.States;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.InflowActions.Vtex;

public class TerminateLoopThroughVtexOrders
    : ITrigger<
        TerminateLoopThroughVtexOrders.TerminateLoopThroughVtexOrdersInput,
        TerminateLoopThroughVtexOrders.TerminateLoopThroughVtexOrdersOutput>
{
    private readonly ILoopThroughObjectsService _loopThroughObjectsService;

    public TerminateLoopThroughVtexOrders(ILoopThroughObjectsService loopThroughObjectsService)
    {
        _loopThroughObjectsService = loopThroughObjectsService;
    }

    public class TerminateLoopThroughVtexOrdersInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughVtexOrdersInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class TerminateLoopThroughVtexOrdersOutput
    {
        [JsonProperty("is_terminated")]
        public bool IsTerminated { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughVtexOrdersOutput(bool isTerminated)
        {
            IsTerminated = isTerminated;
        }
    }

    public async Task<TerminateLoopThroughVtexOrdersOutput> F(
        TerminateLoopThroughVtexOrdersInput input)
    {
        var isTerminated = await _loopThroughObjectsService
            .TerminateInProgressLoopThroughExecutionAsync(
                ProviderNames.Vtex,
                input.FlowHubWorkflowId,
                input.FlowHubWorkflowVersionedId,
                input.SleekflowCompanyId);

        return new TerminateLoopThroughVtexOrdersOutput(isTerminated);
    }
}