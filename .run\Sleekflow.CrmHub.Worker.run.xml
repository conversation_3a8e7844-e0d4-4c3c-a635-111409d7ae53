<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.CrmHub.Workers" type="AzureFunctionsHost" factoryName="Azure Functions host">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.CrmHub.Workers/bin/Debug/net8.0/Sleekflow.CrmHub.Workers.dll" />
    <option name="PROGRAM_PARAMETERS" value="host start --pause-on-error --port 7076" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.CrmHub.Workers/bin/Debug/net8.0" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="COSMOS_CRM_HUB_DB_DATABASE_ID" value="crmhubdb" />
      <env name="COSMOS_CRM_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_CRM_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_CRM_HUB_INTEGRATION_DB_DATABASE_ID" value="crmhubintegrationdb" />
      <env name="COSMOS_CRM_HUB_INTEGRATION_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_CRM_HUB_INTEGRATION_DB_KEY" value="****************************************************************************************" />
      <env name="CRM_HUB_INTERNALS_ENDPOINT" value="https://localhost:7070/Internals" />
      <env name="HUBSPOT_INTEGRATOR_INTERNALS_ENDPOINT" value="https://localhost:7074/Internals" />
      <env name="INTERNALS_KEY" value="" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="SALESFORCE_INTEGRATOR_INTERNALS_ENDPOINT" value="https://localhost:7072/Internals" />
      <env name="DYNAMICS365_INTEGRATOR_INTERNALS_ENDPOINT" value="https://localhost:7088/Internals" />
      <env name="GOOGLE_SHEETS_INTEGRATOR_INTERNALS_ENDPOINT" value="https://localhost:7077/Internals" />
      <env name="ZOHO_INTEGRATOR_INTERNALS_ENDPOINT" value="https://localhost:7105/Internals" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.CrmHub.Workers/Sleekflow.CrmHub.Workers.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="0" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="AzureFunctions" />
    <option name="PROJECT_TFM" value="net8.0" />
    <option name="FUNCTION_NAMES" value="" />
    <browser url="http://localhost:7076" />
    <method v="2">
      <option name="BuildFunctionsProject" enabled="true" />
    </method>
  </configuration>
</component>