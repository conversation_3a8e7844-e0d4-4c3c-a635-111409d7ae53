using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.BusinessBalance)]
[Resolver(typeof(IMessagingHubDbResolver))]
public class BusinessBalance : Entity, IHasRecordStatuses, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    private const string PropertyNameCredit = "credit";
    private const string PropertyNameUsed = "used";
    private const string PropertyNameMarkup = "markup";
    private const string PropertyNameMarkupProfile = "markup_profile";
    private const string PropertyNameConversationUsageInsertState = "conversation_usage_insert_state";
    private const string PropertyNameWabaBalances = "waba_balances";
    private const string PropertyNameIsByWabaBillingEnabled = "is_by_waba_billing_enabled";

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty(PropertyNameCredit)]
    public Money Credit { get; set; }

    [JsonProperty(PropertyNameUsed)]
    public Money Used { get; set; }

    [JsonProperty(PropertyNameMarkup)]
    public Money Markup { get; set; }

    [JsonProperty("transaction_handling_fee")]
    public Money? TransactionHandlingFee { get; set; }

    [JsonIgnore]
    public Money Balance => MoneyExtensions.Minus(Credit, AllTimeUsage);

    [JsonIgnore]
    public Money AllTimeUsage => MoneyExtensions.AddAll(
        Used,
        Markup,
        TransactionHandlingFee);

    [JsonProperty(PropertyNameMarkupProfile)]
    public MarkupProfile MarkupProfile { get; set; }

    [JsonProperty(PropertyNameWabaBalances)]
    public List<WabaBalance>? WabaBalances { get; set; }

    [JsonIgnore]
    public Money? UnallocatedCredit => CalculateUnallocatedCredit();

    [JsonProperty(PropertyNameIsByWabaBillingEnabled)]
    public bool? IsByWabaBillingEnabled { get; set; }

    [JsonProperty(PropertyNameConversationUsageInsertState)]
    public ConversationUsageInsertState ConversationUsageInsertState { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public BusinessBalance(
        string id,
        string facebookBusinessId,
        Money credit,
        Money used,
        Money markup,
        Money transactionHandlingFee,
        MarkupProfile markupProfile,
        List<WabaBalance>? wabaBalances,
        bool? isByWabaBillingEnabled,
        ConversationUsageInsertState conversationUsageInsertState,
        List<string> recordStatuses,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.BusinessBalance)
    {
        FacebookBusinessId = facebookBusinessId;
        Credit = credit;
        Used = used;
        Markup = markup;
        WabaBalances = wabaBalances;
        IsByWabaBillingEnabled = isByWabaBillingEnabled ?? false;
        TransactionHandlingFee = transactionHandlingFee;
        MarkupProfile = markupProfile;
        ConversationUsageInsertState = conversationUsageInsertState;
        RecordStatuses = recordStatuses;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public BusinessBalance(
        string id,
        string facebookBusinessId,
        MarkupProfile markupProfile,
        List<WabaBalance>? wabaBalances,
        bool? isByWabaBillingEnabled,
        ConversationUsageInsertState conversationUsageInsertState,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : this(
            id,
            facebookBusinessId,
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, 0),
            markupProfile,
            wabaBalances,
            isByWabaBillingEnabled,
            conversationUsageInsertState,
            new List<string>(),
            createdAt,
            updatedAt)
    {
    }

    // Incremental
    public void CalculateTransactionLog(
        BusinessBalanceTransactionLog businessBalanceTransactionLog)
    {
        Credit = MoneyExtensions.Add(Credit, businessBalanceTransactionLog.Credit);
        Used = MoneyExtensions.Add(Used, businessBalanceTransactionLog.Used);
        Markup = MoneyExtensions.Add(Markup, businessBalanceTransactionLog.Markup);
        if (TransactionHandlingFee is not null && businessBalanceTransactionLog.TransactionHandlingFee is not null)
        {
            TransactionHandlingFee =
                MoneyExtensions.Add(TransactionHandlingFee, businessBalanceTransactionLog.TransactionHandlingFee);
        }

        // record usage for waba if waba level balance found
        if (string.IsNullOrEmpty(businessBalanceTransactionLog.FacebookWabaId))
        {
            return;
        }

        var wabaBalance =
            WabaBalances?.First(x => x.FacebookWabaId == businessBalanceTransactionLog.FacebookWabaId);

        if (wabaBalance == null)
        {
            return;
        }

        wabaBalance.Credit = MoneyExtensions.Add(wabaBalance.Credit, businessBalanceTransactionLog.Credit);
        wabaBalance.Used = MoneyExtensions.Add(wabaBalance.Used, businessBalanceTransactionLog.Used);
        wabaBalance.Markup = MoneyExtensions.Add(wabaBalance.Markup, businessBalanceTransactionLog.Markup);
        if (wabaBalance.TransactionHandlingFee is not null && businessBalanceTransactionLog.TransactionHandlingFee is not null)
        {
            wabaBalance.TransactionHandlingFee =
                MoneyExtensions.Add(
                    wabaBalance.TransactionHandlingFee,
                    businessBalanceTransactionLog.TransactionHandlingFee);
        }

        wabaBalance.UpdatedAt = DateTimeOffset.UtcNow;
    }

    public void CalculateFromCreditTransferTransactionLogs(List<BusinessBalanceTransactionLog> creditTransferTransactionLogs)
    {
        foreach (var creditTransferTransactionLog in creditTransferTransactionLogs)
        {
            string facebookWabaId;
            var creditTransfer = creditTransferTransactionLog.CreditTransferFromTo;
            var creditTransferType = creditTransfer!.CreditTransferType;

            switch (creditTransferType)
            {
                case CreditTransferTypes.NormalTransferFromBusinessToWaba:
                    facebookWabaId = creditTransfer.CreditTransferTo.FacebookWabaId!;

                    var creditedWabaBalance =
                        WabaBalances?.Find(x => x.FacebookWabaId == facebookWabaId);

                    if (creditedWabaBalance is null)
                    {
                        continue;
                    }

                    if (UnallocatedCredit is null || UnallocatedCredit.Amount < creditTransfer.CreditTransferAmount.Amount)
                    {
                        continue;
                    }

                    creditedWabaBalance.Credit = MoneyExtensions.Add(
                        creditedWabaBalance.Credit,
                        creditTransfer.CreditTransferAmount);
                    WabaBalances?.RemoveAll(x => x.FacebookWabaId == facebookWabaId);
                    WabaBalances?.Add(creditedWabaBalance);

                    break;
                case CreditTransferTypes.NormalTransferFromWabaToBusiness:
                case CreditTransferTypes.SystemTransferFromWabaToBusiness:
                    facebookWabaId = creditTransfer.CreditTransferFrom.FacebookWabaId!;

                    var debitedWabaBalance =
                        WabaBalances?.Find(x => x.FacebookWabaId == facebookWabaId);

                    if (debitedWabaBalance is null || debitedWabaBalance.Balance.Amount < creditTransfer.CreditTransferAmount.Amount)
                    {
                        continue;
                    }

                    debitedWabaBalance.Credit = MoneyExtensions.Minus(
                        debitedWabaBalance.Credit,
                        creditTransfer.CreditTransferAmount);
                    WabaBalances?.RemoveAll(x => x.FacebookWabaId == facebookWabaId);
                    WabaBalances?.Add(debitedWabaBalance);

                    break;
            }

            creditTransferTransactionLog.SetCalculationStatusToSuccess();
        }
    }

    private Money? CalculateUnallocatedCredit()
    {
        if (!IsByWabaBillingEnabled.HasValue)
        {
            return null;
        }

        if (Balance.Amount < 0)
        {
            return null;
        }

        var totalWabaBalances = WabaBalances != null
            ? MoneyExtensions.AddAll(WabaBalances.Select(x => x.Balance).ToArray())
            : new Money(Credit.CurrencyIsoCode, 0);

        return MoneyExtensions.Minus(Balance, totalWabaBalances);
    }

}