using System.Security.Cryptography;
using System.Text;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals;

public record ChatEvalQuestion(
    ChatEvalConfig ChatEvalConfig,
    string Scenario,
    ChatMessageContent[]? QuestionContexts = null,
    string? ModelAnswer = null,
    List<string>? SourceFilenames = null,
    ReplyGenerationContext? ReplyGenerationContext = null,
    CompanyAgentConfig? AgentConfig = null,
    bool? ShouldGetContactProperties = null,
    bool? ShouldGetContactMeetings = null,
    bool? ShouldAssignLead = null,
    bool? ShouldScheduleDemo = null,
    Dictionary<string, string>? ExpectedChiliPiperFields = null,
    Dictionary<string, string>? ExpectedHubspotParams = null,
    SfChatEntry[]? SfChatEntriesQuestionContexts = null)
{
    public string GenerateId()
    {
        using var md5 = MD5.Create();
        var fullContextString = string.Join(
            string.Empty,
            QuestionContexts is null
                ? SfChatEntriesQuestionContexts!.Select(entry =>
                    entry.User + entry.Bot + entry.Files?.Select(file => file.Url).Aggregate((a, b) => a + b))
                : QuestionContexts.Select(context => context.Content));
        var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(fullContextString));
        var result = new Guid(hash);
        return result.ToString();
    }
}