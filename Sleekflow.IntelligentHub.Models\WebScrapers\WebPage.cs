﻿using System.ComponentModel;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers;

public interface IWebPage
{
    public string WebPageId { get; set; }

    public string WebPageUri { get; set; }

    public DateTimeOffset? ScrapedTime { get; set; }

    public string LanguageIsoCode { get; set; }

    public string? Title { get; set; }

    public string? Description { get; set; }

    public string? Keywords { get; set; }

    public string? Author { get; set; }
}

public class WebPage : IWebPage
{
    public const string PropertyNameWebPageId = "web_page_id";
    public const string PropertyNameWebPageUri = "web_page_uri";
    public const string PropertyNameScrapedTime = "scraped_time";
    public const string PropertyNameLanguageIsoCode = "language_iso_code";
    public const string PropertyNameTitle = "title";
    public const string PropertyNameDescription = "description";
    public const string PropertyNameKeywords = "keywords";
    public const string PropertyNameAuthor = "author";

    [JsonProperty(PropertyName = PropertyNameWebPageId)]
    [Description("Same with the name of the related Blob file.")]
    public string WebPageId { get; set; }

    [JsonProperty(PropertyName = WebScraperRun.PropertyNameApifyRunId)]
    public string ApifyRunId { get; set; }

    [JsonProperty(PropertyName = PropertyNameWebPageUri)]
    public string WebPageUri { get; set; }

    [JsonProperty(PropertyName = PropertyNameScrapedTime)]
    public DateTimeOffset? ScrapedTime { get; set; }

    [JsonProperty(PropertyName = PropertyNameLanguageIsoCode)]
    public string LanguageIsoCode { get; set; }

    [JsonProperty(PropertyName = PropertyNameTitle)]
    public string? Title { get; set; }

    [JsonProperty(PropertyName = PropertyNameDescription)]
    public string? Description { get; set; }

    [JsonProperty(PropertyName = PropertyNameKeywords)]
    public string? Keywords { get; set; }

    [JsonProperty(PropertyName = PropertyNameAuthor)]
    public string? Author { get; set; }

    [JsonConstructor]
    public WebPage(
        string webPageId,
        string apifyRunId,
        string webPageUri,
        DateTimeOffset? scrapedTime,
        string languageIsoCode,
        string? title,
        string? description,
        string? keywords,
        string? author)
    {
        WebPageId = webPageId;
        ApifyRunId = apifyRunId;
        WebPageUri = webPageUri;
        ScrapedTime = scrapedTime;
        LanguageIsoCode = languageIsoCode;
        Title = title;
        Description = description;
        Keywords = keywords;
        Author = author;
    }
}