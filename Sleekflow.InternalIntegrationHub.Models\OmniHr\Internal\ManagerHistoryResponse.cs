using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class ManagerHistoryResponse
{
    [JsonConstructor]
    public ManagerHistoryResponse(
        int? id,
        string? effectiveDate,
        int? manager,
        ManagerData? managerData,
        int? user,
        UserData? userData,
        int? managerEmploymentStatus)
    {
        Id = id;
        EffectiveDate = effectiveDate;
        Manager = manager;
        ManagerData = managerData;
        User = user;
        UserData = userData;
        ManagerEmploymentStatus = managerEmploymentStatus;
    }

    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public int? Id { get; set; }

    [JsonProperty("effective_date", NullValueHandling = NullValueHandling.Ignore)]
    public string? EffectiveDate { get; set; }

    [JsonProperty("manager", NullValueHandling = NullValueHandling.Ignore)]
    public int? Manager { get; set; }

    [JsonProperty("manager_data", NullValueHandling = NullValueHandling.Ignore)]
    public ManagerData? ManagerData { get; set; }

    [JsonProperty("user", NullValueHandling = NullValueHandling.Ignore)]
    public int? User { get; set; }

    [JsonProperty("user_data", NullValueHandling = NullValueHandling.Ignore)]
    public UserData? UserData { get; set; }

    [JsonProperty("manager_employment_status", NullValueHandling = NullValueHandling.Ignore)]
    public int? ManagerEmploymentStatus { get; set; }
}

public class ManagerData
{
    [JsonConstructor]
    public ManagerData(
        int id,
        int systemId,
        object photo,
        string firstName,
        string lastName,
        string preferredName,
        PrimaryEmail primaryEmail,
        object primaryPhone,
        string locationName,
        string position,
        string department,
        string country,
        string employeeType,
        List<int> roles)
    {
        Id = id;
        SystemId = systemId;
        Photo = photo;
        FirstName = firstName;
        LastName = lastName;
        PreferredName = preferredName;
        PrimaryEmail = primaryEmail;
        PrimaryPhone = primaryPhone;
        LocationName = locationName;
        Position = position;
        Department = department;
        Country = country;
        EmployeeType = employeeType;
        Roles = roles;
    }

    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("system_id")]
    public int SystemId { get; set; }

    [JsonProperty("photo")]
    public object Photo { get; set; }

    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("preferred_name")]
    public string PreferredName { get; set; }

    [JsonProperty("primary_email")]
    public PrimaryEmail PrimaryEmail { get; set; }

    [JsonProperty("primary_phone")]
    public object PrimaryPhone { get; set; }

    [JsonProperty("location_name")]
    public string LocationName { get; set; }

    [JsonProperty("position")]
    public string Position { get; set; }

    [JsonProperty("department")]
    public string Department { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("employee_type")]
    public string EmployeeType { get; set; }

    [JsonProperty("roles")]
    public List<int> Roles { get; set; }
}

public class UserData
{
    [JsonConstructor]
    public UserData(
        int id,
        int systemId,
        object photo,
        string firstName,
        string lastName,
        string preferredName,
        PrimaryEmail primaryEmail,
        object primaryPhone,
        string locationName,
        string position,
        string department,
        string country,
        string employeeType,
        List<int> roles)
    {
        Id = id;
        SystemId = systemId;
        Photo = photo;
        FirstName = firstName;
        LastName = lastName;
        PreferredName = preferredName;
        PrimaryEmail = primaryEmail;
        PrimaryPhone = primaryPhone;
        LocationName = locationName;
        Position = position;
        Department = department;
        Country = country;
        EmployeeType = employeeType;
        Roles = roles;
    }

    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("system_id")]
    public int SystemId { get; set; }

    [JsonProperty("photo")]
    public object Photo { get; set; }

    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("preferred_name")]
    public string PreferredName { get; set; }

    [JsonProperty("primary_email")]
    public PrimaryEmail PrimaryEmail { get; set; }

    [JsonProperty("primary_phone")]
    public object PrimaryPhone { get; set; }

    [JsonProperty("location_name")]
    public string LocationName { get; set; }

    [JsonProperty("position")]
    public string Position { get; set; }

    [JsonProperty("department")]
    public string Department { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("employee_type")]
    public string EmployeeType { get; set; }

    [JsonProperty("roles")]
    public List<int> Roles { get; set; }
}