using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins.Models;

#pragma warning disable SKEXP0050

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

public class StaticPageEntry
{
    [JsonProperty("page")]
    public string Page { get; set; } = string.Empty;

    [JsonProperty("url")]
    public string Url { get; set; } = string.Empty;

    [JsonProperty("description")]
    public string Description { get; set; } = string.Empty;
}

[method: JsonConstructor]
public class StaticSearchPluginResponse : QueryKnowledgeResponse
{
    public StaticSearchPluginResponse(string knowledge, string id)
        : base(knowledge, id, "StaticSearch")
    {
    }
}

public interface IStaticSearchPlugin
{
    [KernelFunction("search_static_pages")]
    [Description(
        "Searches a predefined list of static pages using an LLM to find relevant content based on the user query.")]
    Task<StaticSearchPluginResponse> SearchStaticPagesAsync(
        Kernel kernel,
        [Description("The user query to search for in the static pages.")]
        string query);
}

public class StaticSearchPlugin : IStaticSearchPlugin, IScopedService
{
    private readonly ILogger<StaticSearchPlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public StaticSearchPlugin(
        ILogger<StaticSearchPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("search_static_pages")]
    [Description(
        "Searches a predefined list of static pages using an LLM to find relevant content based on the user query.")]
    public async Task<StaticSearchPluginResponse> SearchStaticPagesAsync(
        Kernel kernel,
        [Description("A natural language sentence to describe the information you are looking for. More comprehensive descriptions yield better results.")]
        string query)
    {
        _logger.LogInformation("Performing static page search for query: {Query}", query);
        var responseId = Guid.NewGuid().ToString();

        var staticSearchConfig = kernel.Data[KernelDataKeys.STATIC_SEARCH_PLUGIN_CONFIG] as StaticSearchConfig;

        if (staticSearchConfig?.Pages == null || !staticSearchConfig.Pages.Any())
        {
            _logger.LogWarning("Static page list is empty or not configured.");
            return new StaticSearchPluginResponse("Static page list is not configured or is empty.", responseId);
        }

        // Prepare data for LLM
        var pageData = JsonConvert.SerializeObject(
            staticSearchConfig.Pages.Select(p => new
            {
                p.Page, p.Url, p.Description
            }));

        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        var searchFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "search_static_pages",
                Template =
                    """
                    Given the following list of pages and their descriptions, find the most relevant page(s) for the user's query.
                    Return the Page, URL, and Description of the most relevant match(es). If multiple pages are relevant, list them.
                    If no page is relevant, indicate that.

                    ====PAGES_DATA====
                    {{$PAGES_DATA}}
                    ====PAGES_DATA====

                    ====USER_QUERY====
                    {{$USER_QUERY}}
                    ====USER_QUERY====

                    Relevant Page(s) Information:
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "PAGES_DATA", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "USER_QUERY", IsRequired = true
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "The most relevant page(s) information or an indication of no match."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                },
            });

        try
        {
            var result = await searchFunction.InvokeAsync<ChatMessageContent>(
                kernel,
                new KernelArguments(promptExecutionSettings)
                {
                    {
                        "PAGES_DATA", pageData
                    },
                    {
                        "USER_QUERY", query
                    }
                });

            var content = result?.Content ?? "No relevant pages found.";
            _logger.LogInformation("Static page search completed for query: {Query}. Result: {Result}", query, content);
            return new StaticSearchPluginResponse($"<CONFIRMED_KNOWLEDGE>{content}</CONFIRMED_KNOWLEDGE>", responseId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during static page search for query: {Query}", query);
            return new StaticSearchPluginResponse(
                $"Failed to perform static page search for query: {query}",
                responseId);
        }
    }
}