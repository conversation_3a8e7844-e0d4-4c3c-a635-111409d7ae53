using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Agents.Actions;
using Sleekflow.Models.Prompts;

namespace Sleekflow.FlowHub.Models.Agents;

public class AgentConfig
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("is_chat_history_enabled_as_context")]
    public bool? IsChatHistoryEnabledAsContext { get; set; }

    [JsonProperty("is_contact_properties_enabled_as_context")]
    public bool? IsContactPropertiesEnabledAsContext { get; set; }

    [JsonProperty("number_of_previous_messages_in_chat_history_enabled_as_context")]
    public int NumberOfPreviousMessagesInChatHistoryAvailableAsContext { get; set; }

    [JsonProperty("channel_type")]
    public string? ChannelType { get; set; }

    [JsonProperty("channel_id")]
    public string? ChannelId { get; set; }

    [JsonProperty("actions")]
    public AgentActions? Actions { get; set; }

    [JsonProperty("prompt_instruction")]
    public PromptInstruction? PromptInstruction { get; set; }

    [JsonConstructor]
    public AgentConfig(
        string? id,
        string? name,
        bool? isChatHistoryEnabledAsContext,
        bool? isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        AgentActions? actions,
        PromptInstruction? promptInstruction)
    {
        Id = id;
        Name = name;
        IsChatHistoryEnabledAsContext = isChatHistoryEnabledAsContext;
        IsContactPropertiesEnabledAsContext = isContactPropertiesEnabledAsContext;
        NumberOfPreviousMessagesInChatHistoryAvailableAsContext =
            numberOfPreviousMessagesInChatHistoryAvailableAsContext;
        ChannelType = channelType;
        ChannelId = channelId;
        Actions = actions;
        PromptInstruction = promptInstruction;
    }

    public AgentConfig()
    {
        Id = null;
        Name = null;
        IsChatHistoryEnabledAsContext = null;
        IsContactPropertiesEnabledAsContext = null;
        NumberOfPreviousMessagesInChatHistoryAvailableAsContext = 0;
        ChannelType = null;
        ChannelId = null;
        Actions = null;
    }
}