using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

public class ExcelDocumentSplitter : IDocumentSplitter
{
    public Task<List<(Stream Stream, int StartPage, int EndPage)>> SplitDocumentIntoChunksAsync(
        Stream stream,
        int numberOfContentPerFile)
    {
        var outputStreams = new List<(Stream Stream, int StartPage, int EndPage)>();

        using var spreadsheetDocument = SpreadsheetDocument.Open(stream, false);
        var workbookPart = spreadsheetDocument.WorkbookPart;
        var worksheetPart = workbookPart!.WorksheetParts.First();
        var sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();

        // first csv row need to be repeated for each chunk for every chunk to be a valid csv
        var firstCsvRow = sheetData.Elements<Row>().First();

        foreach (var chunk in sheetData.Elements<Row>().Skip(1).Chunk(numberOfContentPerFile))
        {
            var newWorksheet = new Worksheet();
            var newSheetData = new SheetData();

            newSheetData.AppendChild(firstCsvRow.CloneNode(true));
            foreach (var row in chunk)
            {
                newSheetData.AppendChild(row.CloneNode(true));
            }

            newWorksheet.Append(newSheetData);

            var currentStream = new MemoryStream();
            using var currentSpreadsheetDocument =
                SpreadsheetDocument.Create(currentStream, SpreadsheetDocumentType.Workbook);
            var newWorkbookPart = currentSpreadsheetDocument
                .AddWorkbookPart();
            newWorkbookPart
                .AddNewPart<WorksheetPart>()
                .Worksheet = newWorksheet;
            if (workbookPart.SharedStringTablePart != null)
            {
                var clonedSharedStringTable =
                    (SharedStringTable) workbookPart.SharedStringTablePart.SharedStringTable.CloneNode(true);
                newWorkbookPart.AddNewPart<SharedStringTablePart>().SharedStringTable = clonedSharedStringTable;
            }

            currentSpreadsheetDocument.Save();
            outputStreams.Add((currentStream, 0, 0));

            // Should not dispose this stream.
            // To be returned to the caller.
            currentStream.Position = 0;
        }

        return Task.FromResult(outputStreams);
    }
}