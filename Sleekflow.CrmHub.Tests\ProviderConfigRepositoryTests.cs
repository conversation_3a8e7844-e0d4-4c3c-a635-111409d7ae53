﻿using MassTransit.Testing;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Serilog;
using Serilog.Enrichers.Span;
using Sleekflow.Constants;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests;

public class ProviderConfigRepositoryTests
{
    private static readonly string PartitionId = $"TEST PARTITION-{Guid.NewGuid()}";

    private readonly ServiceProvider _serviceProvider;

    public ProviderConfigRepositoryTests()
    {
        var loggerConfiguration = new LoggerConfiguration()
            .Enrich.WithSpan()
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .WriteTo.Async(
                wt => wt.Console(
                    outputTemplate:
                    "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));

        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        serviceCollection.AddScoped<IDynamicFiltersRepositoryContext, DynamicFiltersRepositoryContext>();
        serviceCollection.AddScoped<IProviderConfigRepository>(
            sp => new ProviderConfigRepository(
                NullLogger<DynamicFiltersBaseRepository<ProviderConfig>>.Instance,
                sp,
                sp.GetRequiredService<IDynamicFiltersRepositoryContext>()));

        _serviceProvider = serviceCollection.BuildServiceProvider();
    }

    private IProviderConfigRepository GetProviderConfigRepository(IServiceProvider? serviceProvider = null)
    {
        return (serviceProvider ?? _serviceProvider).GetRequiredService<IProviderConfigRepository>();
    }

    private IDynamicFiltersRepositoryContext GetRepositoryFiltersContext(IServiceProvider? serviceProvider = null)
    {
        return (serviceProvider ?? _serviceProvider).GetRequiredService<IDynamicFiltersRepositoryContext>();
    }

    [TearDown]
    public async Task TearDown()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

        repositoryFiltersContext.IsSoftDeleteEnabled = false;
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var providerConfigs = await providerConfigRepository.GetObjectsAsync(
            config =>
                config.SleekflowCompanyId == PartitionId);

        foreach (var providerConfig in providerConfigs)
        {
            await providerConfigRepository.DeleteAsync(
                providerConfig.Id,
                providerConfig.SleekflowCompanyId);
        }
    }

    [OneTimeTearDown]
    public async Task AllTearDown()
    {
        await _serviceProvider.DisposeAsync();
    }

    public class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }

    [Test]
    public async Task RepositoryFiltersContextTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);

        var createCountActive = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(RepositoryFiltersContextTest) + "-ACTIVE",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null),
            PartitionId);
        Assert.That(createCountActive, Is.EqualTo(1));

        var createCountDeleted = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(RepositoryFiltersContextTest) + "-DELETED",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Deleted
                },
                null),
            PartitionId);
        Assert.That(createCountDeleted, Is.EqualTo(1));

        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);
        repositoryFiltersContext.IsSoftDeleteEnabled = false;
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var providerConfigsCount = await providerConfigRepository
            .GetObjectEnumerableAsync(pc => pc.SleekflowCompanyId == PartitionId)
            .Count();
        Assert.That(providerConfigsCount, Is.EqualTo(2));

        repositoryFiltersContext.IsSoftDeleteEnabled = true;
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        providerConfigsCount = await providerConfigRepository
            .GetObjectEnumerableAsync(pc => pc.SleekflowCompanyId == PartitionId)
            .Count();
        Assert.That(providerConfigsCount, Is.EqualTo(1));
    }

    [Test]
    public async Task GetObjectEnumerableAsyncTest()
    {
        {
            await using var scope = _serviceProvider.CreateAsyncScope();

            var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
            var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

            repositoryFiltersContext.SleekflowCompanyId = PartitionId;

            var createCountActive = await providerConfigRepository.CreateAsync(
                new ProviderConfig(
                    nameof(GetObjectEnumerableAsyncTest) + "-ACTIVE",
                    PartitionId,
                    PartitionId,
                    new Dictionary<string, SyncConfig>(),
                    "salesforce-integrator",
                    true,
                    "ZZ",
                    new List<string>
                    {
                        RecordStatuses.Active
                    },
                    null),
                PartitionId);
            Assert.That(createCountActive, Is.EqualTo(1));

            var providerConfigsCountActive = await providerConfigRepository
                .GetObjectEnumerableAsync(config => config.Id == nameof(GetObjectEnumerableAsyncTest) + "-ACTIVE")
                .Count();
            Assert.That(providerConfigsCountActive, Is.EqualTo(1));
        }

        {
            await using var scope = _serviceProvider.CreateAsyncScope();

            var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
            var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

            repositoryFiltersContext.SleekflowCompanyId = PartitionId;

            var createCountDeleted = await providerConfigRepository.CreateAsync(
                new ProviderConfig(
                    nameof(GetObjectEnumerableAsyncTest) + "-DELETED",
                    PartitionId,
                    PartitionId,
                    new Dictionary<string, SyncConfig>(),
                    "salesforce-integrator",
                    true,
                    "ZZ",
                    new List<string>
                    {
                        RecordStatuses.Deleted
                    },
                    null),
                PartitionId);
            Assert.That(createCountDeleted, Is.EqualTo(1));

            var providerConfigsCountDeleted = await providerConfigRepository
                .GetObjectEnumerableAsync(config => config.Id == nameof(GetObjectEnumerableAsyncTest) + "-DELETED")
                .Count();
            Assert.That(providerConfigsCountDeleted, Is.EqualTo(0));

            Assert.ThrowsAsync<SfQueryException>(
                async () =>
                {
                    await providerConfigRepository
                        .GetObjectEnumerableAsync(new QueryDefinition("SELECT * FROM %%CONTAINER_NAME%%"))
                        .Count();
                });
        }

        if (typeof(ProviderConfig).GetInterface(nameof(IHasSleekflowCompanyId)) != null)
        {
            await using var scope = _serviceProvider.CreateAsyncScope();

            var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
            var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

            repositoryFiltersContext.SleekflowCompanyId = PartitionId;

            var createCountActive = await providerConfigRepository.CreateAsync(
                new ProviderConfig(
                    nameof(GetObjectEnumerableAsyncTest) + "-ACTIVE",
                    PartitionId,
                    PartitionId,
                    new Dictionary<string, SyncConfig>(),
                    "salesforce-integrator",
                    true,
                    "ZZ",
                    new List<string>
                    {
                        RecordStatuses.Active
                    },
                    null),
                PartitionId);
            Assert.That(createCountActive, Is.EqualTo(1));

            var provider = await providerConfigRepository
                .GetObjectEnumerableAsync(
                    config =>
                        config.Id == nameof(GetObjectEnumerableAsyncTest) + "-ACTIVE" &&
                        config.SleekflowCompanyId == PartitionId)
                .Count();
            Assert.That(provider, Is.EqualTo(1));

            repositoryFiltersContext.SleekflowCompanyId = "TEST PARTITION1";

            provider = await providerConfigRepository
                .GetObjectEnumerableAsync(
                    config =>
                        config.Id == nameof(GetObjectEnumerableAsyncTest) + "-ACTIVE" &&
                        config.SleekflowCompanyId == PartitionId)
                .Count();
            Assert.That(provider, Is.EqualTo(0));

            Assert.ThrowsAsync<SfQueryException>(
                async () =>
                {
                    await providerConfigRepository
                        .GetObjectEnumerableAsync(
                            new QueryDefinition(
                                    $"SELECT * FROM %%CONTAINER_NAME%% c WHERE ARRAY_CONTAINS(c.record_statuses, @recordStatuses)")
                                .WithParameter("@recordStatuses", RecordStatuses.Active))
                        .Count();
                });

            provider = await providerConfigRepository
                .GetObjectEnumerableAsync(
                    new QueryDefinition(
                            $"SELECT * FROM %%CONTAINER_NAME%% c " +
                            $"WHERE ARRAY_CONTAINS(c.record_statuses, @recordStatuses) " +
                            $"AND c.sleekflow_company_id = @SleekflowCompanyId")
                        .WithParameter("@recordStatuses", RecordStatuses.Active)
                        .WithParameter("@SleekflowCompanyId", PartitionId)).Count();

            Assert.That(provider, Is.EqualTo(1));

            Assert.ThrowsAsync<SfNotFoundObjectException>(
                async () =>
                {
                    await providerConfigRepository.DeleteAsync(
                        nameof(DeleteAsyncTest) + "-ACTIVE",
                        PartitionId);
                });
        }
    }

    [Test]
    public async Task GetObjectsAsyncTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var createCountActive = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(GetObjectsAsyncTest) + "-ACTIVE",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null),
            PartitionId);
        Assert.That(createCountActive, Is.EqualTo(1));

        var providerConfigsCountActive = (await providerConfigRepository
                .GetObjectsAsync(
                    config =>
                        config.Id == nameof(GetObjectsAsyncTest) + "-ACTIVE" &&
                        config.SleekflowCompanyId == PartitionId))
            .Count;
        Assert.That(providerConfigsCountActive, Is.EqualTo(1));

        var createCountDeleted = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(GetObjectsAsyncTest) + "-DELETED",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Deleted
                },
                null),
            PartitionId);
        Assert.That(createCountDeleted, Is.EqualTo(1));

        var providerConfigsCountDeleted = (await providerConfigRepository
                .GetObjectsAsync(
                    config =>
                        config.Id == nameof(GetObjectsAsyncTest) + "-DELETED" &&
                        config.SleekflowCompanyId == PartitionId))
            .Count;
        Assert.That(providerConfigsCountDeleted, Is.EqualTo(0));

        Assert.ThrowsAsync<SfQueryException>(
            async () =>
            {
                await providerConfigRepository
                    .GetObjectEnumerableAsync(new QueryDefinition("SELECT * FROM %%CONTAINER_NAME%%"))
                    .Count();
            });
    }

    [Test]
    public async Task GetContinuationTokenizedObjectsAsyncTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        for (int i = 0; i < 100; i++)
        {
            var createCountActive = await providerConfigRepository.CreateAsync(
                new ProviderConfig(
                    nameof(GetContinuationTokenizedObjectsAsyncTest) + "-" + i + "-ACTIVE",
                    PartitionId,
                    PartitionId,
                    new Dictionary<string, SyncConfig>(),
                    "salesforce-integrator",
                    true,
                    "ZZ",
                    new List<string>
                    {
                        RecordStatuses.Active
                    },
                    null),
                PartitionId);
            Assert.That(createCountActive, Is.EqualTo(1));
        }

        var (providerConfigsActive, nextContinuationTokenActive) = await providerConfigRepository
            .GetContinuationTokenizedObjectsAsync(
                config => config.Id.EndsWith("-ACTIVE") &&
                          config.Id.StartsWith(nameof(GetContinuationTokenizedObjectsAsyncTest)),
                null,
                100);
        Assert.That(providerConfigsActive.Count, Is.EqualTo(100));
        Assert.That(nextContinuationTokenActive, Is.Null);

        var nextContinuationToken = (string?) null;
        var providerConfigs = new List<ProviderConfig>();
        for (int i = 0; i < 100; i += 10)
        {
            var (pc, n) = await providerConfigRepository
                .GetContinuationTokenizedObjectsAsync(
                    config => config.Id.EndsWith("-ACTIVE") &&
                              config.Id.StartsWith(nameof(GetContinuationTokenizedObjectsAsyncTest)),
                    nextContinuationToken,
                    10);

            providerConfigs.AddRange(pc);
            nextContinuationToken = n;

            Assert.That(nextContinuationToken, providerConfigs.Count == 100 ? Is.Null : Is.Not.Null);
        }

        Assert.That(providerConfigs.Count, Is.EqualTo(100));
        Assert.That(nextContinuationToken, Is.Null);

        for (int i = 0; i < 100; i++)
        {
            var createCountDeleted = await providerConfigRepository.CreateAsync(
                new ProviderConfig(
                    nameof(GetContinuationTokenizedObjectsAsyncTest) + "-" + i + "-DELETED",
                    PartitionId,
                    PartitionId,
                    new Dictionary<string, SyncConfig>(),
                    "salesforce-integrator",
                    true,
                    "ZZ",
                    new List<string>
                    {
                        RecordStatuses.Deleted
                    },
                    null),
                PartitionId);
            Assert.That(createCountDeleted, Is.EqualTo(1));
        }

        var (providerConfigsDeleted, nextContinuationTokenDeleted) = await providerConfigRepository
            .GetContinuationTokenizedObjectsAsync(config => config.Id.EndsWith("-DELETED"), null, 100);
        Assert.That(providerConfigsDeleted.Count, Is.EqualTo(0));
        Assert.That(nextContinuationTokenDeleted, Is.Null);
        Assert.ThrowsAsync<SfQueryException>(
            async () =>
            {
                await providerConfigRepository
                    .GetObjectEnumerableAsync(new QueryDefinition("SELECT * FROM %%CONTAINER_NAME%%"))
                    .Count();
            });

        for (var i = 0; i < 100; i++)
        {
            var deleteCount = await providerConfigRepository.DeleteAsync(
                nameof(GetContinuationTokenizedObjectsAsyncTest) + "-" + i + "-ACTIVE",
                PartitionId);
            Assert.That(deleteCount, Is.EqualTo(1));
        }
    }

    [Test]
    public async Task GetAsyncTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var createCountActive = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(GetAsyncTest) + "-ACTIVE",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null),
            PartitionId);
        Assert.That(createCountActive, Is.EqualTo(1));

        var providerConfigActive = await providerConfigRepository.GetAsync(
            nameof(GetAsyncTest) + "-ACTIVE",
            PartitionId);
        Assert.That(providerConfigActive, Is.Not.Null);

        providerConfigActive = await providerConfigRepository.GetAsync(
            nameof(GetAsyncTest) + "-ACTIVE",
            new PartitionKey(PartitionId));
        Assert.That(providerConfigActive, Is.Not.Null);

        var createCountDeleted = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(GetAsyncTest) + "-DELETED",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Deleted
                },
                null),
            PartitionId);
        Assert.That(createCountDeleted, Is.EqualTo(1));

        Assert.ThrowsAsync<SfNotFoundObjectException>(
            async () =>
            {
                await providerConfigRepository
                    .GetAsync(nameof(GetAsyncTest) + "-DELETED", PartitionId);
            });
        Assert.ThrowsAsync<SfNotFoundObjectException>(
            async () =>
            {
                await providerConfigRepository
                    .GetAsync(nameof(GetAsyncTest) + "-DELETED", new PartitionKey(PartitionId));
            });
    }

    [Test]
    public async Task GetOrDefaultAsyncTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);
        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var createCountActive = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(GetOrDefaultAsyncTest) + "-ACTIVE",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null),
            PartitionId);
        Assert.That(createCountActive, Is.EqualTo(1));

        var providerConfigActive = await providerConfigRepository.GetOrDefaultAsync(
            nameof(GetOrDefaultAsyncTest) + "-ACTIVE",
            PartitionId);
        Assert.That(providerConfigActive, Is.Not.Null);

        providerConfigActive = await providerConfigRepository.GetOrDefaultAsync(
            nameof(GetOrDefaultAsyncTest) + "-ACTIVE",
            new PartitionKey(PartitionId));
        Assert.That(providerConfigActive, Is.Not.Null);

        var createCountDeleted = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(GetOrDefaultAsyncTest) + "-DELETED",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Deleted
                },
                null),
            PartitionId);
        Assert.That(createCountDeleted, Is.EqualTo(1));

        var providerConfigDeleted = await providerConfigRepository.GetOrDefaultAsync(
            nameof(GetOrDefaultAsyncTest) + "-DELETED",
            PartitionId);
        Assert.That(providerConfigDeleted, Is.Null);

        providerConfigDeleted = await providerConfigRepository.GetOrDefaultAsync(
            nameof(GetOrDefaultAsyncTest) + "-DELETED",
            new PartitionKey(PartitionId));
        Assert.That(providerConfigDeleted, Is.Null);
    }

    [Test]
    public async Task DeleteAsyncTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var createCountActive = await providerConfigRepository.CreateAsync(
            new ProviderConfig(
                nameof(DeleteAsyncTest) + "-ACTIVE",
                PartitionId,
                PartitionId,
                new Dictionary<string, SyncConfig>(),
                "salesforce-integrator",
                true,
                "ZZ",
                new List<string>
                {
                    RecordStatuses.Active
                },
                null),
            PartitionId);
        Assert.That(createCountActive, Is.EqualTo(1));

        var deleteCount = await providerConfigRepository.DeleteAsync(
            nameof(DeleteAsyncTest) + "-ACTIVE",
            PartitionId);
        Assert.That(deleteCount, Is.EqualTo(1));
        Assert.ThrowsAsync<SfNotFoundObjectException>(
            async () =>
            {
                deleteCount = await providerConfigRepository.DeleteAsync(
                    nameof(DeleteAsyncTest) + "-ACTIVE",
                    PartitionId);
            });
    }

    [Test]
    public async Task ValidateGetAsyncRepositoryFilterContext()
    {
        if (typeof(ProviderConfig).GetInterface(nameof(IHasSleekflowCompanyId)) != null)
        {
            await using var scope = _serviceProvider.CreateAsyncScope();

            var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
            var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

            repositoryFiltersContext.SleekflowCompanyId = PartitionId;

            var createCountActive = await providerConfigRepository.CreateAsync(
                new ProviderConfig(
                    nameof(ValidateGetAsyncRepositoryFilterContext) + "-ACTIVE",
                    PartitionId,
                    PartitionId,
                    new Dictionary<string, SyncConfig>(),
                    "salesforce-integrator",
                    true,
                    "ZZ",
                    new List<string>
                    {
                        RecordStatuses.Active
                    },
                    null),
                PartitionId);
            Assert.That(createCountActive, Is.EqualTo(1));

            var provider = await providerConfigRepository.GetAsync(
                nameof(ValidateGetAsyncRepositoryFilterContext) + "-ACTIVE",
                PartitionId);

            Assert.That(provider, Is.Not.Null);

            repositoryFiltersContext.SleekflowCompanyId = "TEST PARTITION1";

            Assert.ThrowsAsync<SfNotFoundObjectException>(
                async () =>
                {
                    await providerConfigRepository.GetAsync(
                        nameof(ValidateGetAsyncRepositoryFilterContext) + "-ACTIVE",
                        PartitionId);
                });

            repositoryFiltersContext.SleekflowCompanyId = "TEST PARTITION1";

            Assert.ThrowsAsync<SfNotFoundObjectException>(
                async () =>
                {
                    await providerConfigRepository.DeleteAsync(
                        nameof(ValidateGetAsyncRepositoryFilterContext) + "-ACTIVE",
                        PartitionId);
                });
        }
    }

    [Test]
    public async Task IsValidQueryDefinitionTest()
    {
        await using var scope = _serviceProvider.CreateAsyncScope();

        var providerConfigRepository = GetProviderConfigRepository(scope.ServiceProvider);
        var repositoryFiltersContext = GetRepositoryFiltersContext(scope.ServiceProvider);

        repositoryFiltersContext.SleekflowCompanyId = PartitionId;

        var isDynamicFiltersQueryDefinition = providerConfigRepository.IsValidQueryDefinition(
            new QueryDefinition(
                    $"SELECT * " +
                    $"FROM %%CONTAINER_NAME%% c " +
                    $"WHERE ARRAY_CONTAINS(c.record_statuses, @recordStatuses)")
                .WithParameter("@recordStatuses", RecordStatuses.Active),
            IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
            PartitionId);

        Assert.That(isDynamicFiltersQueryDefinition, Is.False);

        isDynamicFiltersQueryDefinition = providerConfigRepository.IsValidQueryDefinition(
            new QueryDefinition(
                    $"SELECT * " +
                    $"FROM %%CONTAINER_NAME%% c " +
                    $"WHERE ARRAY_CONTAINS(c.record_statuses, @recordStatuses)")
                .WithParameter("@recordStatuses", RecordStatuses.Active)
                .WithParameter("@test", PartitionId),
            IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
            PartitionId);

        Assert.That(isDynamicFiltersQueryDefinition, Is.False);

        isDynamicFiltersQueryDefinition = providerConfigRepository.IsValidQueryDefinition(
            new QueryDefinition(
                    $"SELECT * " +
                    $"FROM %%CONTAINER_NAME%% c " +
                    $"WHERE ARRAY_CONTAINS(c.record_statuses, @recordStatuses) AND c.sleekflow_company_id == @sleekflowCompanyId")
                .WithParameter("@recordStatuses", RecordStatuses.Active)
                .WithParameter("@sleekflowCompanyId", PartitionId),
            IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
            PartitionId);

        Assert.That(isDynamicFiltersQueryDefinition, Is.True);
    }
}