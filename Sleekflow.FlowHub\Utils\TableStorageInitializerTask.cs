using Azure;
using Azure.Data.Tables;
using Microsoft.Extensions.Options;
using Sleekflow.FlowHub.Commons.Configs;
using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Utils;

public class TableStorageInitializerTask : IStartupTask
{
    private readonly TableServiceClient _serviceClient;
    private readonly ILogger<TableStorageInitializerTask> _logger;
    private readonly OrleansConfig _orleansConfig;

    private static readonly string[] RequiredTableNames =
    {
        OrleansTableNames.SiloInstances,
        OrleansTableNames.GrainState
    }; // Add other necessary tables

    // Retry Policy Configuration
    private const int MaxRetries = 5; // Max number of retry attempts
    private static readonly TimeSpan InitialDelay = TimeSpan.FromSeconds(2); // Initial delay
    private const double BackoffFactor = 1.5; // Multiplier for delay

    public TableStorageInitializerTask(
        IOptions<OrleansConfig> orleansConfigOptions,
        ILogger<TableStorageInitializerTask> logger)
    {
        _orleansConfig = orleansConfigOptions.Value;
        _logger = logger;
        // Create the service client once
        _serviceClient = new TableServiceClient(_orleansConfig.OrleansStorageConnStr);
    }

    public async Task Execute(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Ensuring Azure Table Storage is initialized for Orleans with retries...");
        var currentDelay = InitialDelay;

        foreach (var tableName in RequiredTableNames)
        {
            var success = false;

            for (int attempt = 1; attempt <= MaxRetries; attempt++)
            {
                try
                {
                    _logger.LogInformation(
                        "[Attempt {Attempt}/{MaxRetries}] Checking table: {TableName}",
                        attempt,
                        MaxRetries,
                        tableName);
                    var tableClient = _serviceClient.GetTableClient(tableName);
                    await tableClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
                    _logger.LogInformation("Table '{TableName}' check completed successfully.", tableName);
                    success = true;

                    break; // Exit retry loop on success
                }
                catch (RequestFailedException rfEx) when (rfEx.Status == 409 && rfEx.ErrorCode == "TableBeingDeleted")
                {
                    // Special case: If the table is being deleted, retrying immediately won't help.
                    // Log a specific warning and wait longer or fail faster depending on requirements.
                    // Here, we'll wait and retry as per the general policy, but log differently.
                    _logger.LogWarning(
                        rfEx,
                        "[Attempt {Attempt}/{MaxRetries}] Table '{TableName}' is currently being deleted. Retrying after delay...",
                        attempt,
                        MaxRetries,
                        tableName);
                    // Fall through to the general delay logic
                }
                catch (Exception ex) when (attempt < MaxRetries)
                {
                    // Log intermediate failures as warnings
                    _logger.LogWarning(
                        ex,
                        "[Attempt {Attempt}/{MaxRetries}] Failed to initialize or verify Azure Table Storage table: {TableName}. Retrying after {Delay}...",
                        attempt,
                        MaxRetries,
                        tableName,
                        currentDelay);
                }
                catch (Exception ex) when (attempt == MaxRetries)
                {
                    // Log final failure as an error before throwing
                    _logger.LogError(
                        ex,
                        "[Attempt {Attempt}/{MaxRetries}] Failed to initialize or verify Azure Table Storage table: {TableName} after all retries. Silo startup will be aborted.",
                        attempt,
                        MaxRetries,
                        tableName);

                    throw; // Rethrow the exception on the last attempt to halt silo startup
                }

                // Wait before the next retry, respecting cancellation token
                try
                {
                    await Task.Delay(currentDelay, cancellationToken);

                    currentDelay =
                        TimeSpan.FromMilliseconds(
                            currentDelay.TotalMilliseconds * BackoffFactor); // Exponential backoff
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning(
                        "Silo startup cancellation requested during table initialization retry delay for table {TableName}. Aborting.",
                        tableName);

                    throw; // Re-throw cancellation exception
                }
            }

            if (!success)
            {
                // This part should technically not be reached if the loop throws on the last attempt,
                // but added for logical completeness.
                var errorMessage = $"Failed to initialize table '{tableName}' after {MaxRetries} attempts.";
                _logger.LogError(errorMessage);

                throw new ApplicationException(errorMessage);
            }

            // Reset delay for the next table
            currentDelay = InitialDelay;
        }

        _logger.LogInformation(
            "Azure Table Storage initialization check completed successfully for all required tables.");
    }
}