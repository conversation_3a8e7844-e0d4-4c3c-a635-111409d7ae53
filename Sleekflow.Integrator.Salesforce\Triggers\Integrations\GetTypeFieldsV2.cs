using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetTypeFieldsV2 : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public GetTypeFieldsV2(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class GetTypeFieldsV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public GetTypeFieldsV2Input(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
        }
    }

    public class GetTypeFieldsV2Output
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetTypeFieldsV2Output(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<GetTypeFieldsV2Output> F(
        GetTypeFieldsV2Input getTypeFieldsV2Input)
    {
        var connection = await _salesforceConnectionService.GetByIdAsync(
            getTypeFieldsV2Input.ConnectionId,
            getTypeFieldsV2Input.SleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getTypeFieldsV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsOutput =
            await _salesforceObjectService.GetFieldsAsync(authentication, getTypeFieldsV2Input.EntityTypeName);

        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        return new GetTypeFieldsV2Output(updatableFields, creatableFields, viewableFields);
    }
}