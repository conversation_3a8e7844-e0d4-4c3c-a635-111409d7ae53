﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderTypeFields : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderTypeFields(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderTypeFieldsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public GetProviderTypeFieldsInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ProviderName = providerName;
        }
    }

    public class GetProviderTypeFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetProviderTypeFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<GetProviderTypeFieldsOutput> F(
        GetProviderTypeFieldsInput getProviderTypeFieldsInput)
    {
        var providerService = _providerSelector.GetProviderService(getProviderTypeFieldsInput.ProviderName);

        var getTypeFieldsOutput = await providerService.GetTypeFieldsAsync(
            getProviderTypeFieldsInput.SleekflowCompanyId,
            getProviderTypeFieldsInput.EntityTypeName);
        if (getTypeFieldsOutput == null)
        {
            throw new SfUserFriendlyException("The integrator does not respond fields");
        }

        return new GetProviderTypeFieldsOutput(
            getTypeFieldsOutput.UpdatableFields,
            getTypeFieldsOutput.CreatableFields,
            getTypeFieldsOutput.ViewableFields);
    }
}