﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class HubspotObjectUpdatedEventRequestConsumerDefinition : ConsumerDefinition<HubspotObjectUpdatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<HubspotObjectUpdatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class HubspotObjectUpdatedEventRequestConsumer : IConsumer<HubspotObjectUpdatedEventRequest>
{
    private readonly IBus _bus;

    public HubspotObjectUpdatedEventRequestConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<HubspotObjectUpdatedEventRequest> context)
    {
        var hubspotObjectUpdatedEventRequest = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnHubspotObjectUpdatedEventBody(
                hubspotObjectUpdatedEventRequest.CreatedAt,
                hubspotObjectUpdatedEventRequest.ConnectionId,
                hubspotObjectUpdatedEventRequest.ObjectType,
                hubspotObjectUpdatedEventRequest.ObjectDict),
            hubspotObjectUpdatedEventRequest.ObjectId,
            hubspotObjectUpdatedEventRequest.ObjectType,
            hubspotObjectUpdatedEventRequest.SleekflowCompanyId));
    }
}