﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class ChangeToneSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("input_message")]
    public string InputMessage { get; set; }

    [JsonProperty("tone_type")]
    public string ToneType { get; set; }

    [JsonProperty( "output_message")]
    public string OutputMessage { get; set; }

    [JsonConstructor]
    public ChangeToneSnapshot(string inputMessage, string toneType, string outputMessage)
    {
        InputMessage = inputMessage;
        ToneType = toneType;
        OutputMessage = outputMessage;
    }
}