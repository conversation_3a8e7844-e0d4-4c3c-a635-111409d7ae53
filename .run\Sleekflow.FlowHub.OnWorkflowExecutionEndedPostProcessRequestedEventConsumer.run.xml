<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer" type="AzureFunctionAppRun" factoryName="Azure - Run Function">
    <option name="PROJECT_FILE_PATH" value="$PROJECT_DIR$/Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer/Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.csproj" />
    <option name="LAUNCH_PROFILE_NAME" value="" />
    <option name="PROJECT_TFM" value="net8.0" />
    <option name="FUNCTION_NAMES" value="" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="0" />
    <option name="PROGRAM_PARAMETERS" value="host start --pause-on-error --port 7097 --dotnet-isolated-debug" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer/bin/Debug/net8.0" />
    <option name="TRACK_ENVS" value="0" />
    <envs>
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="CACHE_PREFIX" value="Sleekflow.FlowHub" />
      <env name="CORE_INTERNALS_ENDPOINT" value="https://localhost:5000/FlowHub/Internals" />
      <env name="CORE_INTERNALS_KEY" value="O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_FLOW_HUB_DB_DATABASE_ID" value="flowHubdb" />
      <env name="COSMOS_FLOW_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_FLOW_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="FILE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s50d9f29d;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="HIGH_TRAFFIC_SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="THROTTLED_SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="SPECIFIC_STEP_REQUEST_RATE_LIMIT_WINDOW_SECONDS" value="60" />
      <env name="STATE_ALL_STEPS_REQUEST_ERROR_MAX_WITHIN_WINDOW" value="500" />
      <env name="STATE_ALL_STEPS_REQUEST_WARNING_MAX_WITHIN_WINDOW" value="100" />
      <env name="STATE_ALL_STEPS_REQUEST_WINDOW_SECONDS" value="60" />
      <env name="SPECIFIC_STEP_REQUEST_MAX_ALLOWED_WITHIN_WINDOW" value="10" />
      <env name="WORKER_FUNCTIONS_KEY" value="PLACEHOLDER" />
      <env name="WORKER_HOSTNAME" value="http://localhost:7097" />
      <env name="WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW" value="1" />
      <env name="WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_WINDOW_SECONDS" value="1800" />
      <env name="WORKFLOW_ENROLLMENT_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW" value="30" />
      <env name="WORKFLOW_ENROLLMENT_RATE_LIMIT_WINDOW_SECONDS" value="60" />
      <env name="WORKFLOW_VERSION_DELETION_CLEANUP_DELAY_DAYS" value="30" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="TRACK_URL" value="1" />
    <method v="2">
      <option name="Build" />
      <option name="RunAzuriteTask" enabled="true" />
    </method>
  </configuration>
</component>