using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Models.Webhook;

public class HubspotCallbackPayload
{
    [JsonProperty("eventId")]
    public long EventId { get; set; }

    [JsonProperty("subscriptionId")]
    public long SubscriptionId { get; set; }

    [JsonProperty("portalId")]
    public long PortalId { get; set; }

    [JsonProperty("appId")]
    public long AppId { get; set; }

    [JsonProperty("occurredAt")]
    public long OccurredAt { get; set; }

    [JsonProperty("subscriptionType")]
    public string SubscriptionType { get; set; }

    [JsonProperty("attemptNumber")]
    public int AttemptNumber { get; set; }

    [JsonProperty("objectId")]
    public long ObjectId { get; set; }

    [JsonProperty("changeFlag")]
    public string ChangeFlag { get; set; }

    [JsonProperty("changeSource")]
    public string ChangeSource { get; set; }

    [JsonConstructor]
    public HubspotCallbackPayload(
        long eventId,
        long subscriptionId,
        long portalId,
        long appId,
        long occurredAt,
        string subscriptionType,
        int attemptNumber,
        long objectId,
        string changeFlag,
        string changeSource)
    {
        EventId = eventId;
        SubscriptionId = subscriptionId;
        PortalId = portalId;
        AppId = appId;
        OccurredAt = occurredAt;
        SubscriptionType = subscriptionType;
        AttemptNumber = attemptNumber;
        ObjectId = objectId;
        ChangeFlag = changeFlag;
        ChangeSource = changeSource;
    }
}