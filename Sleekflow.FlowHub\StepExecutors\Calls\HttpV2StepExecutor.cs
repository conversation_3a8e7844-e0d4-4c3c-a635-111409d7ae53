using System.Net;
using System.Net.Http.Headers;
using System.Text;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Https.Services;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IHttpV2StepExecutor : IStepExecutor
{
}

public class HttpV2StepExecutor
    : GeneralStepExecutor<CallStep<HttpV2StepArgs>>, IHttpV2StepExecutor, IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly HttpClient _httpClient;
    private readonly IWebhookBridgeRedirectService _webhookBridgeRedirectService;
    private readonly IStateAggregator _stateAggregator;
    private readonly ILogger<HttpV2StepExecutor> _logger;

    public HttpV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IHttpClientFactory httpClientFactory,
        IWebhookBridgeRedirectService webhookBridgeRedirectService,
        IStateAggregator stateAggregator,
        ILogger<HttpV2StepExecutor> logger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _webhookBridgeRedirectService = webhookBridgeRedirectService;
        _stateAggregator = stateAggregator;
        _logger = logger;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var urlStr =
                (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.UrlExpr)
                          ?? callStep.Args.UrlExpr);

            // Build query string if provided
            if (callStep.Args.QueryStringExprSet != null && callStep.Args.QueryStringExprSet.Any())
            {
                var queryParams = new List<string>();
                foreach (var param in callStep.Args.QueryStringExprSet)
                {
                    if (param.QueryStringValueExpr != null)
                    {
                        var value = await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
                            state,
                            param.QueryStringValueExpr);
                        if (value != null)
                        {
                            queryParams.Add(
                                $"{Uri.EscapeDataString(param.QueryStringKey)}={Uri.EscapeDataString(value.ToString())}");
                        }
                    }
                }

                if (queryParams.Any())
                {
                    urlStr = $"{urlStr}{(urlStr.Contains("?") ? "&" : "?")}{string.Join("&", queryParams)}";
                }
            }

            HttpMethod httpMethod = callStep.Args.Method switch
            {
                HttpV2StepArgs.HttpMethod.Get => HttpMethod.Get,
                HttpV2StepArgs.HttpMethod.Post => HttpMethod.Post,
                HttpV2StepArgs.HttpMethod.Put => HttpMethod.Put,
                HttpV2StepArgs.HttpMethod.Delete => HttpMethod.Delete,
                _ => HttpMethod.Get
            };

            var reqMsg = new HttpRequestMessage(httpMethod, urlStr);

            var headersDict = new Dictionary<string, object?>();
            if (callStep.Args.HeadersKeyExprSet != null)
            {
                foreach (var header in callStep.Args.HeadersKeyExprSet)
                {
                    if (header.HeaderValueExpr != null)
                    {
                        var headerValue =
                            await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, header.HeaderValueExpr);
                        headersDict[header.HeaderKey] = headerValue;
                    }
                }

                foreach (var (key, value) in headersDict)
                {
                    reqMsg.Headers.Add(key, value as string ?? string.Empty);
                }
            }

            var bodyContent = string.Empty;
            var contentType = string.Empty;
            var bodyType = string.Empty;
            if (httpMethod != HttpMethod.Get && httpMethod != HttpMethod.Delete &&
                !string.IsNullOrEmpty(callStep.Args.BodyExpr))
            {
                bodyContent =
                    (string) (await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, callStep.Args.BodyExpr)
                              ?? callStep.Args.BodyExpr);

                contentType = callStep.Args.ContentType ?? "application/json";
                bodyType = callStep.Args.BodyType ?? "text";

                reqMsg.Content = new StringContent(bodyContent, Encoding.UTF8, contentType);
                if (!string.IsNullOrEmpty(bodyType))
                {
                    reqMsg.Content.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("type", bodyType));
                }
            }

            var requestBodyForLogging = string.Empty;
            if (reqMsg.Content != null)
            {
                requestBodyForLogging = await reqMsg.Content.ReadAsStringAsync();
            }

            _logger.LogInformation(
                "[{StepExecutor}] Sending HTTP request: WorkflowId: {WorkflowId}, StateId: {StateId}, StepId: {StepId}, HttpMethod: {HttpMethod}, Url: {Url}, Headers: {Headers}, RequestBody: {RequestBody}",
                GetType().Name,
                workflow.Id,
                state.Id,
                step.Id,
                httpMethod,
                urlStr,
                reqMsg.Headers?.ToString(),
                requestBodyForLogging);

            string resStr;
            HttpStatusCode httpStatusCode;
            bool isSuccess;
            if (_webhookBridgeRedirectService.ShouldRedirect(state.Identity.SleekflowCompanyId))
            {
                var (success, response, statusCode) = await _webhookBridgeRedirectService.RedirectAsync(
                    urlStr,
                    httpMethod,
                    headersDict,
                    bodyContent,
                    contentType,
                    bodyType);
                resStr = response;
                httpStatusCode = statusCode;
                isSuccess = success;
            }
            else
            {
                var resMsg = await _httpClient.SendAsync(reqMsg);
                resStr = await resMsg.Content.ReadAsStringAsync();
                httpStatusCode = resMsg.StatusCode;
                isSuccess = resMsg.IsSuccessStatusCode;
            }

            _logger.LogInformation(
                "[{StepExecutor}] Received HTTP response. WorkflowId: {WorkflowId}, StateId: {StateId}, StepId: {StepId}, HttpMethod: {HttpMethod}, StatusCode: {StatusCode}, ResponseBody: {ResponseBody}",
                GetType().Name,
                workflow.Id,
                state.Id,
                step.Id,
                httpMethod,
                httpStatusCode,
                resStr);

            if (!isSuccess)
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.InternalError,
                    $"HTTP request failed with status {(int) httpStatusCode}");
            }

            var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                resStr);

            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }
}