﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public sealed class TypedId : IEquatable<TypedId>
{
    [JsonProperty("type")]
    public string Type { get; }

    [JsonProperty("id")]
    public string Id { get; }

    [JsonConstructor]
    public TypedId(
        string type,
        string id)
    {
        Type = type;
        Id = id;
    }

    public bool Equals(TypedId? other)
    {
        if (other is null)
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return Type == other.Type && Id == other.Id;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as TypedId);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Type, Id);
    }
}