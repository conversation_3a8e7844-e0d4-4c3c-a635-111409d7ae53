using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.ShopifyProducts;
using Sleekflow.CommerceHub.Products.Variants.ShopifyProductVariants;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products.ShopifyProducts;

[TriggerGroup(ControllerNames.ShopifyOrders)]
public class CreateOrUpdateShopifyProduct
    : ITrigger<
        CreateOrUpdateShopifyProduct.CreateOrUpdateShopifyProductInput,
        CreateOrUpdateShopifyProduct.CreateOrUpdateShopifyProductOutput>
{
    private readonly IShopifyProductService _shopifyProductService;
    private readonly IShopifyProductVariantService _shopifyProductVariantService;
    private readonly IImageService _imageService;
    private readonly IStoreService _storeService;

    public CreateOrUpdateShopifyProduct(
        IShopifyProductService shopifyProductService,
        IShopifyProductVariantService shopifyProductVariantService,
        IImageService imageService,
        IStoreService storeService)
    {
        _shopifyProductService = shopifyProductService;
        _shopifyProductVariantService = shopifyProductVariantService;
        _imageService = imageService;
        _storeService = storeService;
    }

    public class CreateOrUpdateShopifyProductInput :
        IHasSleekflowCompanyId, IHasMetadata
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("is_view_enabled")]
        public bool IsViewEnabled { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("provider_product")]
        public Dictionary<string, object?> ProviderProduct { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("provider_product_variants")]
        public List<Dictionary<string, object?>> ProviderProductVariants { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("metadata")]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonConstructor]
        public CreateOrUpdateShopifyProductInput(
            string sleekflowCompanyId,
            string storeId,
            bool isViewEnabled,
            List<ImageDto> images,
            Dictionary<string, object?> providerProduct,
            List<Dictionary<string, object?>> providerProductVariants,
            Dictionary<string, object?> metadata)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            IsViewEnabled = isViewEnabled;
            Images = images;
            ProviderProduct = providerProduct;
            ProviderProductVariants = providerProductVariants;
            Metadata = metadata;
        }
    }

    public class CreateOrUpdateShopifyProductOutput
    {
        [JsonProperty("product")]
        public ProductDto Product { get; set; }

        [JsonConstructor]
        public CreateOrUpdateShopifyProductOutput(ProductDto product)
        {
            Product = product;
        }
    }

    public async Task<CreateOrUpdateShopifyProductOutput> F(
        CreateOrUpdateShopifyProductInput createOrUpdateShopifyProductInput)
    {
        var shopifyStore = await _storeService.GetStoreAsync(
            createOrUpdateShopifyProductInput.StoreId,
            createOrUpdateShopifyProductInput.SleekflowCompanyId);

        // shopify stores have only one currency by setting
        var currencyIsoCode = shopifyStore.Currencies[0].CurrencyIsoCode;

        var images = await _imageService.GetImagesAsync(
            createOrUpdateShopifyProductInput.Images,
            createOrUpdateShopifyProductInput.SleekflowCompanyId,
            createOrUpdateShopifyProductInput.StoreId);

        var shopifyProduct = await _shopifyProductService.GetShopifyProductAsync(
            createOrUpdateShopifyProductInput.SleekflowCompanyId,
            createOrUpdateShopifyProductInput.StoreId,
            (string)createOrUpdateShopifyProductInput.ProviderProduct["Id"]!);

        if (shopifyProduct is not null)
        {
            shopifyProduct = await _shopifyProductService.PatchAndGetShopifyProductAsync(
                shopifyProduct.Id,
                shopifyProduct.SleekflowCompanyId,
                currencyIsoCode,
                images,
                createOrUpdateShopifyProductInput.IsViewEnabled,
                createOrUpdateShopifyProductInput.ProviderProduct,
                createOrUpdateShopifyProductInput.ProviderProductVariants,
                createOrUpdateShopifyProductInput.Metadata);
        }
        else
        {
            shopifyProduct = await _shopifyProductService.CreateAndGetShopifyProductAsync(
                createOrUpdateShopifyProductInput.SleekflowCompanyId,
                createOrUpdateShopifyProductInput.StoreId,
                currencyIsoCode,
                images,
                createOrUpdateShopifyProductInput.IsViewEnabled,
                createOrUpdateShopifyProductInput.ProviderProduct,
                createOrUpdateShopifyProductInput.ProviderProductVariants,
                createOrUpdateShopifyProductInput.Metadata);
        }

        List<ProductVariant> shopifyProductVariants = new();
        foreach (var providerProductVariant in createOrUpdateShopifyProductInput.ProviderProductVariants)
        {
            var shopifyProductVariant = await _shopifyProductVariantService.GetShopifyProductVariantAsync(
                createOrUpdateShopifyProductInput.SleekflowCompanyId,
                createOrUpdateShopifyProductInput.StoreId,
                shopifyProduct.Id,
                (string) providerProductVariant["Id"]!);

            if (shopifyProductVariant is not null)
            {
                shopifyProductVariant = await _shopifyProductVariantService.PatchAndGetShopifyProductVariantAsync(
                    shopifyProductVariant.SleekflowCompanyId,
                    shopifyProductVariant.Id,
                    currencyIsoCode,
                    images,
                    providerProductVariant,
                    createOrUpdateShopifyProductInput.Metadata);
            }
            else
            {
                shopifyProductVariant = await _shopifyProductVariantService.CreateAndGetShopifyProductVariantAsync(
                    createOrUpdateShopifyProductInput.SleekflowCompanyId,
                    shopifyProduct.Id,
                    createOrUpdateShopifyProductInput.StoreId,
                    currencyIsoCode,
                    images,
                    providerProductVariant,
                    createOrUpdateShopifyProductInput.Metadata);
            }

            shopifyProductVariants.Add(shopifyProductVariant);
        }

        return new CreateOrUpdateShopifyProductOutput(
            new ProductDto(
                shopifyProduct,
                shopifyProductVariants));
    }
}