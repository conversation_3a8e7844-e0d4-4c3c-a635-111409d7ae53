using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class CreateFileDocument
    : ITrigger<CreateFileDocument.CreateFileDocumentInput,
        CreateFileDocument.CreateFileDocumentOutput>
{
    private readonly IDocumentProcessingService _documentProcessingService;

    public CreateFileDocument(IDocumentProcessingService documentProcessingService)
    {
        _documentProcessingService = documentProcessingService;
    }

    public class CreateFileDocumentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(FileDocument.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [Required]
        [JsonProperty(FileDocument.PropertyNameBlobType)]
        public string BlobType { get; set; }

        [JsonProperty("assign_to_agent_ids")]
        [ValidateArray]
        [Required]
        public List<string> AssignToAgentIds { get; set; }

        [JsonConstructor]
        public CreateFileDocumentInput(
            string sleekflowCompanyId,
            string blobId,
            string blobType,
            List<string> assignToAgentIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
            BlobType = blobType;
            AssignToAgentIds = assignToAgentIds;
        }
    }

    public class CreateFileDocumentOutput
    {
        [JsonProperty("file_document")]
        public FileDocument FileDocument { get; set; }

        [JsonConstructor]
        public CreateFileDocumentOutput(FileDocument fileDocument)
        {
            FileDocument = fileDocument;
        }
    }

    public async Task<CreateFileDocumentOutput> F(
        CreateFileDocumentInput createFileDocumentInput)
    {
        var fileDocument = await _documentProcessingService.CreateDocumentAsync(
            createFileDocumentInput.SleekflowCompanyId,
            createFileDocumentInput.BlobId,
            createFileDocumentInput.BlobType,
            createFileDocumentInput.AssignToAgentIds);

        return new CreateFileDocumentOutput(fileDocument);
    }
}