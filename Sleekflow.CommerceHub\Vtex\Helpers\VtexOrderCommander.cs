﻿using System.Net;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CommerceHub;

namespace Sleekflow.CommerceHub.Vtex.Helpers;

public interface IVtexOrderCommander
{
    /// <summary>
    /// Get Order detail.
    /// <br/><br/>Documentation see https://developers.vtex.com/docs/api-reference/orders-api#get-/api/oms/pvt/orders/-orderId-
    /// </summary>
    Task<VtexOrderDto> GetOrderAsync(
        VtexCredential credential,
        string orderId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get Paginated Orders. VTEX constrains the maximum value to:
    /// <br/> 1. page: 30
    /// <br/> 2. perPage: 100
    /// <br/><br/>Documentation see https://developers.vtex.com/docs/api-reference/orders-api#get-/api/oms/pvt/orders
    /// </summary>
    Task<VtexGetOrdersResponseDto> GetOrdersAsync(
        VtexCredential credential,
        VtexGetOrdersSearchCondition condition,
        int page,
        int perPage,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Changes the status of an order to indicate that it is in handling.
    /// <br/><br/>Documentation see https://developers.vtex.com/docs/api-reference/orders-api#post-/api/oms/pvt/orders/-orderId-/start-handling
    /// </summary>
    Task StartHandlingOrderAsync(
        VtexCredential credential,
        string orderId,
        CancellationToken cancellationToken = default);
}

public class VtexOrderCommander : IVtexOrderCommander, IScopedService
{
    private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

    private readonly HttpClient _httpClient;
    private readonly ILogger<VtexOrderCommander> _logger;

    public VtexOrderCommander(HttpClient httpClient, ILogger<VtexOrderCommander> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<VtexOrderDto> GetOrderAsync(
        VtexCredential credential,
        string orderId,
        CancellationToken cancellationToken = default)
    {
        // build http request
        var request = new HttpRequestMessage(
            HttpMethod.Get,
            $"{credential.Domain}/api/oms/pvt/orders/{orderId}");

        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppKey, credential.AppKey);
        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppToken, credential.AppToken);

        var response = await _httpClient.SendAsync(request, cancellationToken);
        var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                throw new SfVtexInvalidCredentialException();
            }

            if (response.StatusCode == HttpStatusCode.NotFound)
            {
                throw new SfNotFoundObjectException(orderId);
            }

            _logger.LogError(
                "Unable to fetch order detail for order {OrderId} with status code {StatusCode}, body: {Content}. {VtexCredential}",
                orderId,
                response.StatusCode,
                responseBody,
                credential);

            throw new SfVtexHttpRequestException(
                request,
                $"Unable to fetch order detail: {responseBody}");
        }

        var order = JsonConvert.DeserializeObject<VtexOrderDto>(responseBody);
        if (order == null)
        {
            _logger.LogError(
                "Unable to deserialize order detail for order {OrderId} with status code {StatusCode}, body: {Content}. {VtexCredential}",
                orderId,
                response.StatusCode,
                responseBody,
                credential);

            throw new SfVtexHttpRequestException(
                request,
                $"Unable to deserialize order detail: {responseBody}");
        }

        return order;
    }

    public async Task<VtexGetOrdersResponseDto> GetOrdersAsync(
        VtexCredential credential,
        VtexGetOrdersSearchCondition condition,
        int page,
        int perPage,
        CancellationToken cancellationToken = default)
    {
        // search params
        var searchParamString = GenerateGetOrdersSearchParamString(condition, page, perPage);

        // build http request
        var request = new HttpRequestMessage(
            HttpMethod.Get,
            $"{credential.Domain}/api/oms/pvt/orders?{searchParamString}");

        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppKey, credential.AppKey);
        request.Headers.Add(VtexHttpHeaderNames.VtexApiAppToken, credential.AppToken);

        var response = await _httpClient.SendAsync(request, cancellationToken);
        var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                throw new SfVtexInvalidCredentialException();
            }

            _logger.LogError(
                "Unable to fetch orders with status code {StatusCode}, body: {Content}. {VtexCredential}, {Condition}",
                response.StatusCode,
                responseBody,
                credential,
                condition);

            throw new SfVtexHttpRequestException(
                request,
                $"Unable to get orders: {responseBody}");
        }

        var getOrdersResponse = JsonConvert.DeserializeObject<VtexGetOrdersResponseDto>(responseBody);
        if (getOrdersResponse == null)
        {
            _logger.LogError(
                "Unable to deserialize orders response with status code {StatusCode}, body: {Content}. {VtexCredential}, {Condition}",
                response.StatusCode,
                responseBody,
                credential,
                condition);

            throw new SfVtexHttpRequestException(
                request,
                $"Unable to deserialize get orders response: {responseBody}");
        }

        return getOrdersResponse;
    }

    public Task StartHandlingOrderAsync(
        VtexCredential credential,
        string orderId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    private static string GenerateGetOrdersSearchParamString(
        VtexGetOrdersSearchCondition condition,
        int page,
        int perPage)
    {
        // e.g. creationDate:[2025-05-21T02:00:00.000Z TO 2025-05-22T01:59:59.999Z]
        var creationDateString =
            $"creationDate:[{condition.CreatedAtFrom.ToString(DateTimeFormat)} TO {condition.CreatedAtTo.ToString(DateTimeFormat)}]";

        var shouldFilterByStatusCode =
            condition.OrderStatusCode != null && VtexOrderStatusCodes.All.Contains(condition.OrderStatusCode);

        var searchParamString = "orderBy=creationDate,desc" +
               $"&page={page}" +
               $"&per_page={perPage}" +
               $"&f_creationDate={creationDateString}";

        if (shouldFilterByStatusCode)
        {
            searchParamString += $"&f_status={condition.OrderStatusCode}";
        }

        return searchParamString;
    }
}