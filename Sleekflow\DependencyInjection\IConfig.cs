﻿namespace Sleekflow.DependencyInjection;

/// <summary>
/// Marker interface for configuration objects in the Sleekflow architecture.
/// </summary>
/// <remarks>
/// <para>
/// Classes implementing this interface represent configuration settings for various components
/// of the application. They are automatically registered in the dependency injection container
/// and can be resolved by components that require configuration.
/// </para>
/// <para>
/// Configuration objects typically:
/// <list type="bullet">
///   <li>Contain settings loaded from appsettings.json, environment variables, or other sources</li>
///   <li>Are immutable after initialization</li>
///   <li>Are registered as singletons in the dependency injection container</li>
///   <li>Represent a cohesive set of related configuration values for a specific component</li>
/// </list>
/// </para>
/// <para>
/// Example implementation:
/// <code>
/// public class AppConfig : IConfig, IAppConfig
/// {
///     public string ConnectionString { get; set; } = string.Empty;
///     public int Timeout { get; set; }
/// }
/// </code>
/// </para>
/// </remarks>
public interface IConfig
{
}