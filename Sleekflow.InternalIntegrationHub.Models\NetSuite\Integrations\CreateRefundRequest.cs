using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreateRefundRequest
{
    [JsonConstructor]
    public CreateRefundRequest(Customer customer, Collection<ApplyElement> apply)
    {
        Customer = customer;
        Apply = apply;
    }

    [Required]
    [JsonProperty("customer")]
    public Customer Customer { get; set; }

    [Required]
    [JsonProperty("apply")]
    public Collection<ApplyElement> Apply { get; set; }
}