using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Https;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class GetWebpageHead : ITrigger
{
    private readonly ILogger<GetWebpageHead> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public GetWebpageHead(
        ILogger<GetWebpageHead> logger,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    public class GetWebpageHeadInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonConstructor]
        public GetWebpageHeadInput(
            string sleekflowCompanyId,
            string url)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Url = url;
        }
    }

    public class GetWebpageHeadOutput
    {
        [JsonProperty("webpage_head")]
        public WebpageHead WebpageHead { get; set; }

        [JsonConstructor]
        public GetWebpageHeadOutput(WebpageHead webpageHead)
        {
            WebpageHead = webpageHead;
        }
    }

    [Function(nameof(GetWebpageHead))]
    public async Task<GetWebpageHeadOutput> Run(
        [ActivityTrigger]
        GetWebpageHeadInput input)
    {
        _logger.LogInformation(
            "GetWebpageHead processing URL {Url} for company {CompanyId}",
            input.Url,
            input.SleekflowCompanyId);

        var httpClient = _httpClientFactory.CreateClient("default-handler");
        var webpageHead = await GetWebpageHeadForUrlAsync(httpClient, input.Url);

        _logger.LogInformation(
            "GetWebpageHead completed processing URL {Url}, ContentType: {ContentType}",
            input.Url,
            webpageHead.MimeType);

        return new GetWebpageHeadOutput(webpageHead);
    }

    private async Task<WebpageHead> GetWebpageHeadForUrlAsync(HttpClient httpClient, string url)
    {
        var retryPolicy = HttpPolicies.HttpTransientErrorRetryPolicy;

        var response = await retryPolicy.ExecuteAsync(
            async () =>
            {
                _logger.LogDebug("Making HEAD request to {Url}", url);

                var request = new HttpRequestMessage(HttpMethod.Head, url);
                request.Headers.Add(
                    "User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                return await httpClient.SendAsync(request);
            });

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"HEAD request failed for {url}, StatusCode: {response.StatusCode}");
        }

        var mimeType = response.Content.Headers.ContentType?.MediaType ??
                       throw new Exception($"No media type for url {url}");

        _logger.LogDebug(
            "HEAD request successful for {Url}, MimeType: {MimeType}, StatusCode: {StatusCode}",
            url,
            mimeType,
            response.StatusCode);

        return new WebpageHead(mimeType);
    }
}

public class WebpageHead
{
    [JsonProperty("mime_type")]
    public string MimeType { get; set; }

    [JsonConstructor]
    public WebpageHead(string mimeType)
    {
        MimeType = mimeType;
    }
}