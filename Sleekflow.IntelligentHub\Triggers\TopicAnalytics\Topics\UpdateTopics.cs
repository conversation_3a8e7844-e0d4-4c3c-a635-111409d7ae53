using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.TopicAnalytics;
using Sleekflow.IntelligentHub.TopicAnalytics;

namespace Sleekflow.IntelligentHub.Triggers.TopicAnalytics.Topics;

[TriggerGroup(ControllerNames.TopicAnalytics)]
public class UpdateTopics : ITrigger<UpdateTopics.UpdateTopicsInput, UpdateTopics.UpdateTopicsOutput>
{
    private readonly ITopicAnalyticsService _topicAnalyticsService;

    public UpdateTopics(ITopicAnalyticsService topicAnalyticsService)
    {
        _topicAnalyticsService = topicAnalyticsService;
    }

    public class UpdateTopicsInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("topics")]
        public Dictionary<string, TopicAnalyticsTopic?> Topics { get; set; }

        [JsonConstructor]
        public UpdateTopicsInput(string sleekflowCompanyId, Dictionary<string, TopicAnalyticsTopic?> topics)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Topics = topics;
        }
    }

    public class UpdateTopicsOutput
    {
        [JsonProperty("topics")]
        public List<TopicAnalyticsTopic> Topics { get; set; }

        [JsonConstructor]
        public UpdateTopicsOutput(List<TopicAnalyticsTopic> topics)
        {
            Topics = topics;
        }
    }

    public async Task<UpdateTopicsOutput> F(UpdateTopicsInput input)
    {
        var topics = await _topicAnalyticsService.UpdateTopicAnalyticsTopics(input.SleekflowCompanyId, input.Topics);

        return new UpdateTopicsOutput(topics);
    }
}