<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.OPAL/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="containerName" value="opal" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="7002" />
              <option name="hostPort" value="7002" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="sourceFilePath" value="Sleekflow.OPAL/Dockerfile" />
      </settings>
    </deployment>
    <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="false" isSslEnabled="false" />
    <method v="2" />
  </configuration>
</component>