﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.UserMappingConfigs;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("hubspot_user_mapping_config")]
public class HubspotUserMappingConfig : Entity, IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [JsonProperty("user_mappings")]
    public List<UserMapping>? UserMappings { get; set; }

    [JsonConstructor]
    public HubspotUserMappingConfig(
        string id,
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings)
        : base(id, "UserMappingConfig")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        UserMappings = userMappings;
    }
}