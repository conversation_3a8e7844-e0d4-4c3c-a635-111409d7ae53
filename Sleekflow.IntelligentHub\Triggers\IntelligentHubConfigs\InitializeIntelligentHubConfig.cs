using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class InitializeIntelligentHubConfig
    : ITrigger<InitializeIntelligentHubConfig.InitializeIntelligentHubConfigInput,
        InitializeIntelligentHubConfig.InitializeIntelligentHubConfigOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public InitializeIntelligentHubConfig(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class InitializeIntelligentHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IntelligentHubConfig.PropertyNameUsageLimits)]
        [Validations.ValidateObject]
        public Dictionary<string, int> UsageLimits { get; set; }

        [JsonConstructor]
        public InitializeIntelligentHubConfigInput(
            string sleekflowCompanyId,
            Dictionary<string, int> usageLimits)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UsageLimits = usageLimits;
        }
    }

    public class InitializeIntelligentHubConfigOutput
    {
        [JsonProperty("intelligent_hub_config")]
        public IntelligentHubConfig IntelligentHubConfig { get; set; }

        [JsonConstructor]
        public InitializeIntelligentHubConfigOutput(IntelligentHubConfig intelligentHubConfig)
        {
            IntelligentHubConfig = intelligentHubConfig;
        }
    }

    public async Task<InitializeIntelligentHubConfigOutput> F(
        InitializeIntelligentHubConfigInput initializeIntelligentHubConfigInput)
    {
        var intelligentHubConfig = await _intelligentHubConfigService.InitializeIntelligentHubConfigAsync(
            initializeIntelligentHubConfigInput.SleekflowCompanyId,
            initializeIntelligentHubConfigInput.UsageLimits);

        return new InitializeIntelligentHubConfigOutput(intelligentHubConfig);
    }
}