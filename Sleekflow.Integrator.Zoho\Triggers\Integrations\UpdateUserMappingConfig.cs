﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class UpdateUserMappingConfig : ITrigger
{
    private readonly IZohoUserMappingConfigService _zohoUserMappingConfigService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;

    public UpdateUserMappingConfig(
        IZohoUserMappingConfigService zohoUserMappingConfigService,
        IZohoConnectionService zohoConnectionService,
        IZohoAuthenticationService zohoAuthenticationService)
    {
        _zohoUserMappingConfigService = zohoUserMappingConfigService;
        _zohoConnectionService = zohoConnectionService;
        _zohoAuthenticationService = zohoAuthenticationService;
    }

    public class UpdateUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("user_mapping_config_id")]
        [Required]
        public string UserMappingConfigId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [ValidateArray]
        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }

        [JsonConstructor]
        public UpdateUserMappingConfigInput(
            string userMappingConfigId,
            string sleekflowCompanyId,
            List<UserMapping>? userMappings)
        {
            UserMappingConfigId = userMappingConfigId;
            SleekflowCompanyId = sleekflowCompanyId;
            UserMappings = userMappings;
        }
    }

    public class UpdateUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public UpdateUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<UpdateUserMappingConfigOutput> F(
        UpdateUserMappingConfigInput updateUserMappingConfigInput)
    {
        var id = updateUserMappingConfigInput.UserMappingConfigId;
        var sleekflowCompanyId = updateUserMappingConfigInput.SleekflowCompanyId;
        var userMappings = updateUserMappingConfigInput.UserMappings;

        var userMappingConfig = await _zohoUserMappingConfigService.GetByIdAsync(
            id,
            sleekflowCompanyId);

        var connection =
            await _zohoConnectionService.GetByIdAsync(
                userMappingConfig.ConnectionId,
                sleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        userMappingConfig = await _zohoUserMappingConfigService.PatchAndGetAsync(
                id,
                sleekflowCompanyId,
                userMappings);

        return new UpdateUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}