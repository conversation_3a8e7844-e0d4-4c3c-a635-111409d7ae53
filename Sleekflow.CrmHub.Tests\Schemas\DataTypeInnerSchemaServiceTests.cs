﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests.Schemas;

public class DataTypeInnerSchemaServiceTests
{
    private IDataTypeInnerSchemaService _dataTypeInnerSchemaService = null!;

    [SetUp]
    public void SetUp()
    {
        var services = new ServiceCollection();

        var idService = GetIdService();

        services.AddSingleton<IIdService>(idService);
        services.AddSingleton<ISchemaRepository>(GetSchemaRepository());
        services.AddSingleton<ISchemaValidator, SchemaValidator>();

        services.AddSingleton<IOptionService, OptionService>();
        services.AddSingleton<IDataTypeInnerSchemaService, DataTypeInnerSchemaService>();
        services.AddSingleton<IDefaultPropertyHook, DefaultPropertyHook>();
        services.AddSingleton<ISingleChoicePropertyHook, SingleChoicePropertyHook>();
        services.AddSingleton<IMultipleChoicePropertyHook, MultipleChoicePropertyHook>();
        services.AddSingleton<IArrayObjectPropertyHook, ArrayObjectPropertyHook>();

        services.AddSingleton<IPropertyConstructor, PropertyConstructor>();

        var serviceProvider = services.BuildServiceProvider();

        _dataTypeInnerSchemaService = serviceProvider.GetRequiredService<IDataTypeInnerSchemaService>();
    }

    [Test]
    public void CreateInnerSchema_WithCorrectInput()
    {
        var properties = GeneratePropertiesForSchemaCreation();
        var innerSchema = _dataTypeInnerSchemaService.Create(properties);

        Assert.That(innerSchema.Properties.Count, Is.EqualTo(2));
        Assert.That(innerSchema.Properties[1].Id.Length, Is.EqualTo(15));
        Assert.That(innerSchema.Properties[1].Options, Is.Not.Null);
        Assert.That(innerSchema.Properties[1].Options!.Count, Is.EqualTo(3));
        Assert.That(innerSchema.Properties[1].Options![0].Id.Length, Is.EqualTo(15));
    }

    [Test]
    public void CreateInnerSchema_WithNestedArrayObjectType_ShouldThrowException()
    {
        var properties = new List<Property>
        {
            new Property(
                string.Empty,
                "Test Numeric",
                "numeric",
                new NumericDataType(),
                false,
                false,
                true,
                true,
                2,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                string.Empty,
                "Test ArrayObject",
                "array_object",
                new ArrayObjectDataType(new InnerSchema(GeneratePropertiesForSchemaCreation())),
                false,
                false,
                true,
                true,
                2,
                null,
                DateTimeOffset.UtcNow,
                null),
        };

        Assert.Throws<SfCustomObjectNestedInnerSchemaException>(
            () =>
            {
                _dataTypeInnerSchemaService.Create(properties);
            });
    }

    [Test]
    public void UpdateInnerSchema_WithCorrectInput_ShouldUpdateOriginalInnerSchemaCorrectly()
    {
        var originalInnerSchema = _dataTypeInnerSchemaService.Create(GeneratePropertiesForSchemaCreation());
        var receivedInnerSchema = DeepCopy(originalInnerSchema);

        receivedInnerSchema.Properties[0].DisplayName = "Updated Numeric";
        receivedInnerSchema.Properties.RemoveAll(p => p.UniqueName == "multiple_choice");
        receivedInnerSchema.Properties.AddRange(
            new List<Property>
            {
                new Property(
                    string.Empty,
                    "Test Date",
                    "date",
                    new DateDataType(),
                    false,
                    true,
                    true,
                    true,
                    5,
                    new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                    DateTimeOffset.UtcNow,
                    null),
                new Property(
                    string.Empty,
                    "Test SingleChoice",
                    "single_choice",
                    new SingleChoiceDataType(),
                    false,
                    true,
                    true,
                    true,
                    7,
                    new AuditEntity.SleekflowStaff("mocked-staff-id", null),
                    DateTimeOffset.UtcNow,
                    new List<Option>
                    {
                        new Option(string.Empty, "option_0", 0),
                        new Option(string.Empty, "option_1", 1)
                    })
            });

        _dataTypeInnerSchemaService.Update(originalInnerSchema, receivedInnerSchema);

        Assert.That(originalInnerSchema.Properties.Count, Is.EqualTo(3));

        Assert.That(originalInnerSchema.Properties[0].DisplayName, Is.EqualTo("Updated Numeric"));
        Assert.That(originalInnerSchema.Properties[1].Id.Length, Is.EqualTo(15));
        Assert.That(originalInnerSchema.Properties[2].DataType, Is.TypeOf<SingleChoiceDataType>());
        Assert.That(originalInnerSchema.Properties[2].Options!.Count, Is.EqualTo(2));
    }

    private static T DeepCopy<T>(T obj)
    {
        return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(obj))!;
    }

    private List<Property> GeneratePropertiesForSchemaCreation()
    {
        return new List<Property>
        {
            new Property(
                string.Empty,
                "Test Numeric",
                "numeric",
                new NumericDataType(),
                false,
                false,
                true,
                true,
                2,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                string.Empty,
                "Test MultipleChoice",
                "multiple_choice",
                new MultipleChoiceDataType(),
                true,
                true,
                false,
                false,
                9,
                null,
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(string.Empty, "option_0", 0),
                    new Option(string.Empty, "option_1", 1),
                    new Option(string.Empty, "option_2", 2)
                })
        };
    }

    private IdService GetIdService()
    {
        var dbContainerResolver = new DbContainerResolver(new MyCrmHubDbConfig());

        var idService = new IdService(
            NullLogger<IdService>.Instance,
            dbContainerResolver);

        return idService;
    }

    private SchemaRepository GetSchemaRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var schemaRepository = new SchemaRepository(
            NullLogger<SchemaRepository>.Instance,
            serviceProvider);

        return schemaRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig, IDbConfig
    {
        public string Endpoint => "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key => "****************************************************************************************";

        public string DatabaseId => "crmhubdb";
    }
}