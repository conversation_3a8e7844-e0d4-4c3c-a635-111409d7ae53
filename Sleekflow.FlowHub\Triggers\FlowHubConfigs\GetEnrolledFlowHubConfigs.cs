using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class GetEnrolledFlowHubConfigs : ITrigger
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public GetEnrolledFlowHubConfigs(
        IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    public class GetEnrolledFlowHubConfigsInput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 100)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetEnrolledFlowHubConfigsInput(
            string? continuationToken,
            int limit)
        {
            ContinuationToken = continuationToken;
            Limit = limit;
        }
    }

    public class GetEnrolledFlowHubConfigsOutput
    {
        [JsonProperty("flow_hub_configs")]
        public List<FlowHubConfig> FlowHubConfigs { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetEnrolledFlowHubConfigsOutput(
            List<FlowHubConfig> flowHubConfigs,
            string? continuationToken)
        {
            FlowHubConfigs = flowHubConfigs;
            ContinuationToken = continuationToken;
        }
    }

    public async Task<GetEnrolledFlowHubConfigsOutput> F(GetEnrolledFlowHubConfigsInput getEnrolledFlowHubConfigsInput)
    {
        var (flowHubConfigs, nextContinuationToken) =
            await _flowHubConfigService.GetEnrolledFlowHubConfigsAsync(
                getEnrolledFlowHubConfigsInput.ContinuationToken,
                getEnrolledFlowHubConfigsInput.Limit);

        return new GetEnrolledFlowHubConfigsOutput(flowHubConfigs, nextContinuationToken);
    }
}