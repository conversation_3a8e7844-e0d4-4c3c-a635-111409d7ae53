using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Workers.Services;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class ProcessContactEligibilityCheckByBatchV3
{
    private readonly IContactBatchService _contactBatchService;
    private readonly IContactEligibilityService _contactEligibilityService;
    private readonly IContactEnrollmentService _contactEnrollmentService;
    private readonly ILogger<ProcessContactEligibilityCheckByBatchV3> _logger;

    public ProcessContactEligibilityCheckByBatchV3(
        IContactBatchService contactBatchService,
        IContactEligibilityService contactEligibilityService,
        IContactEnrollmentService contactEnrollmentService,
        ILogger<ProcessContactEligibilityCheckByBatchV3> logger)
    {
        _contactBatchService = contactBatchService;
        _contactEligibilityService = contactEligibilityService;
        _contactEnrollmentService = contactEnrollmentService;
        _logger = logger;
    }

    public class ProcessContactEligibilityCheckByBatchV3Input : ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchInput
    {
        [JsonConstructor]
        public ProcessContactEligibilityCheckByBatchV3Input(
            string origin,
            string sleekflowCompanyId,
            DateTimeOffset? lastContactCreatedAt,
            string? lastContactId,
            int? batchSize,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string condition)
            : base(origin, sleekflowCompanyId, lastContactCreatedAt, lastContactId, batchSize, workflowId, workflowVersionedId, workflowName, condition)
        {
        }
    }

    public class ProcessContactEligibilityCheckByBatchV3Output : ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchOutput
    {
        [JsonConstructor]
        public ProcessContactEligibilityCheckByBatchV3Output(
            DateTimeOffset? nextBatchLastContactCreatedAt,
            string? nextBatchLastContactId,
            int contactsInFetchedBatch,
            int contactsEnrolled)
            : base(nextBatchLastContactCreatedAt, nextBatchLastContactId, contactsInFetchedBatch, contactsEnrolled)
        {
        }
    }

    [Function("ProcessContactEligibilityCheckByBatchV3")]
    public async Task<ProcessContactEligibilityCheckByBatchV3Output> RunAsync(
        [ActivityTrigger]
        ProcessContactEligibilityCheckByBatchV3Input input)
    {
        _logger.LogInformation(
            "[V3] Starting ProcessContactsBatchActivity for Company {CompanyId}, Workflow {WorkflowVersionedId}, BatchSize {BatchSize}, LastContactCreatedAt {LastContactCreatedAt}, LastContactId {LastContactId}",
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            input.BatchSize,
            input.LastContactCreatedAt,
            input.LastContactId);
        var contactsBatchResult = await _contactBatchService.GetContactsByBatchAsync(
            input.Origin,
            input.SleekflowCompanyId,
            input.LastContactCreatedAt,
            input.LastContactId,
            input.BatchSize,
            input.WorkflowVersionedId);

        var contactsInFetchedBatch = contactsBatchResult.Contacts?.Count ?? 0;
        _logger.LogInformation("[V3] Fetched {ContactsCount} contacts for batch processing.", contactsInFetchedBatch);
        var contactsEnrolled = 0;
        if (contactsBatchResult.Contacts != null && contactsBatchResult.Contacts.Count > 0)
        {
            _logger.LogInformation("[V3] Begin to ProcessAndEnrollEligible for these {ContactsInFetchedBatch} contact(s)", contactsInFetchedBatch);
            contactsEnrolled = await _contactEligibilityService.ProcessAndEnrollEligibleContactsAsyncV2(
                contactsBatchResult.Contacts,
                input.WorkflowId,
                input.WorkflowVersionedId,
                input.SleekflowCompanyId,
                input.Condition,
                input.WorkflowName,
                _contactEnrollmentService,
                input.Origin);
        }

        _logger.LogInformation(
            "[V3] Processed batch for Company {CompanyId}, Workflow {WorkflowVersionedId}. Fetched: {FetchedCount}, Enrolled: {EnrolledCount}. Next cursor: CreatedAt={NextCreatedAt}, Id={NextId}",
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            contactsInFetchedBatch,
            contactsEnrolled,
            contactsBatchResult.NextBatch?.LastContactCreatedAt,
            contactsBatchResult.NextBatch?.LastContactId);

        return new ProcessContactEligibilityCheckByBatchV3Output(
            contactsBatchResult.NextBatch?.LastContactCreatedAt,
            contactsBatchResult.NextBatch?.LastContactId,
            contactsInFetchedBatch,
            contactsEnrolled);
    }
}