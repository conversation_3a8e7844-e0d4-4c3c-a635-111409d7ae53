﻿using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.UserProfileAuditLogs;

public interface IUserProfileAuditLogRepository : IRepository<UserProfileAuditLog>
{
}

public class UserProfileAuditLogRepository
    : BaseRepository<UserProfileAuditLog>,
        IUserProfileAuditLogRepository,
        ISingletonService
{
    public UserProfileAuditLogRepository(
        ILogger<UserProfileAuditLogRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}