using Newtonsoft.Json;

namespace Sleekflow.KrakenD.Generator.InternalObjects;

internal class ManagedEndpoint : Endpoint
{
    [JsonIgnore]
    public string Group { get; }

    [JsonIgnore]
    public string Subgroup { get; }

    [JsonIgnore]
    public string HostEnvName { get; }

    [JsonIgnore]
    public string KeyEnvName { get; }

    public ManagedEndpoint(
        string group,
        string subgroup,
        string hostEnvName,
        string keyEnvName)
    {
        Group = group;
        Subgroup = subgroup;
        HostEnvName = hostEnvName;
        KeyEnvName = keyEnvName;
        EndpointEndpoint = "/v1/" + group + "/" + subgroup + "/{method}";
        Method = Generator.Method.Post;
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/" + subgroup + "/{method}",
                Method = Generator.Method.Post,
                Host = new object[]
                {
                    "{{ env \"" + hostEnvName + "\" }}"
                },
            }
        };
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaEndpoint = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/pre_endpoint.lua"
                },
                Pre = "pre_endpoint('{{ env \"" + keyEnvName + "\" }}')",
                Live = false,
                AllowOpenLibs = false
            },
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        };
        InputHeaders = new string[]
        {
            "Content-Type",
            "Traceparent",
            "X-Sleekflow-Distributed-Invocation-Context"
        };
    }
}