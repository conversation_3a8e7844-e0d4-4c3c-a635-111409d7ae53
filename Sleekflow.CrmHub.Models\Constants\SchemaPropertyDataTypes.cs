﻿using System.Collections.Immutable;
using Sleekflow.Attributes;

namespace Sleekflow.CrmHub.Models.Constants;

[SwaggerInclude]
public static class SchemaPropertyDataTypes
{
    public const string SingleLineText = "single_line_text";
    public const string Numeric = "numeric";
    public const string Decimal = "decimal";
    public const string SingleChoice = "single_choice";
    public const string MultipleChoice = "multiple_choice";
    public const string Boolean = "boolean";
    public const string Date = "date";
    public const string DateTime = "datetime";
    public const string ArrayObject = "array_object";
    public const string Image = "image";

    public static readonly ImmutableList<string> SupportedDataTypes =
        new List<string>
            {
                SingleLineText,
                Numeric,
                Decimal,
                SingleChoice,
                MultipleChoice,
                Boolean,
                Date,
                DateTime,
                ArrayObject,
                Image
            }
            .ToImmutableList();
}